/*
* Copyright (c) 2019.07，南京华乘电气科技有限公司
* All rights reserved.
*
* detectionmodeview.h
*
* 初始版本：1.0
* 作者：洪澄
* 创建日期：2019年07月24日
* 摘要：DetectionModeView模块接口定义

* 当前版本：1.0
*/

#ifndef DETECTIONMODEVIEW_H
#define DETECTIONMODEVIEW_H

#include <QApplication>
#include "functionPanelView/FunctionPanelView.h"

namespace DetectionModeView
{
    typedef enum _Function_  //功能
    {
        AE = 0,
        UHF,
        HFCT,
        INFRARED,
        CURRENT_DETECTION,
        AUDIO,
        TEV
    }Function;
}

class DetectionModePanelView : public FunctionPanelView
{
    Q_OBJECT
public:
    /*************************************************
    函数名： DetectionModePanelView(const FunctionButtonPanel::Config *pConfig, const QString &strBackgroundIcon, QWidget *parent = 0)
    输入参数： pConfig：子功能按钮配置
              strBackgroundIcon：背景图片
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit DetectionModePanelView(const FunctionButtonPanel::Config *pConfig, const QString &strBackgroundIcon, QWidget *parent = 0);

    /*************************************************
    函数名： ~DetectionModePanelView()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~DetectionModePanelView();

protected:
    /*************************************************
    函数名： onButtonPressed(quint8 ucID, const FunctionButtonPanel::Config *pConfig)
    输入参数： ucID：按钮ID
              pConfig：按钮配置
    输出参数： NULL
    返回值： NULL
    功能： 响应按钮按下动作
    *************************************************************/
    void onButtonPressed(quint8 ucID, const FunctionButtonPanel::Config *pConfig);

};

#endif // DETECTIONMODEVIEW_H
