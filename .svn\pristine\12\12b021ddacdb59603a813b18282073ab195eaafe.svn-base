﻿#include <QMetaType>
#include <QThread>
#include <QTimer>
#include <QCoreApplication>
#include <QFile>
#include <QSettings>
#include <QMutex>
#include <QMutexLocker>
#include <QtConcurrentRun>
#include "systemsettings.h"
#include "infraredservice.h"
#include "infraredconfig.h"
#include "config/ConfigInstance.h"
#include "Module.h"
#include "config/ConfigManager.h"
#include "appconfig.h"
#include "systemsetting/SystemSet.h"
#include "datadefine.h"
#ifdef Q_PROCESSOR_ARM
#include <unistd.h>
#endif

#include "log/log.h"
#include "global_log.h"

//static QMutex g_mtInfraredServiceObj;
const int g_iInvalidTimerId = -1;

/****************************
功能： 模块单例
*****************************/
InfraredService* InfraredService::instance()
{
    //QMutexLocker stLocker(&g_mtInfraredServiceObj);
    static InfraredService service;
    return &service;
}

/*************************************************
函数名： InfraredService()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
InfraredService::InfraredService()
    :m_pThread(NULL)
    ,m_mutexAffair(QMutex::Recursive)
    ,m_pAffair(NULL)
    ,m_bIsStopService(true)
    ,m_bIsReadData(false)
    ,m_iPingFailTimes(0)
    ,m_iSampleTimerId(g_iInvalidTimerId)
    ,m_iRet(HC_FAILURE)
    ,m_bInited(false)
    ,m_bIniting(false)
    ,m_eInfraredType(Infrared::FILIAR)
{
    qRegisterMetaType<Infrared::InfraredData>("Infrared::InfraredData");
    qRegisterMetaType<MultiServiceNS::USERID>("MultiServiceNS::USERID");
    qRegisterMetaType<QSharedPointer<Infrared::InfraredData>>("QSharedPointer<Infrared::InfraredData>");
    qRegisterMetaType<Infrared::InfraredType>("Infrared::InfraredType");

    m_pAffair = new HCAffair(Infrared::AFFAIR_COUNT, this);
    connect(m_pAffair, SIGNAL(sigAffair(quint16, void*, quint16)), this, SLOT(onAffair(quint16, void*, quint16)));
    connect(this, SIGNAL(sigStartTimer()), this, SLOT(onStartTimer()));
    connect(this, SIGNAL(sigStopTimer()), this, SLOT(onStopTimer()));

    //setIPtoIR();
    myprocess = new QProcess();
    connect(myprocess, SIGNAL(readyReadStandardOutput()), this, SLOT(onOutLog()));

    m_pThread = new QThread(this);
    moveToThread(m_pThread);

    if(!m_pThread->isRunning())
    {
        m_pThread->start();
    }
}

/*************************************************
函数名： ~InfraredService()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
InfraredService::~InfraredService()
{
    disconnect(m_pAffair, SIGNAL(sigAffair(quint16,void*,quint16)), this, SLOT(onAffair(quint16,void*,quint16)));
    disconnect(this, SIGNAL(sigStartTimer()), this, SLOT(onStartTimer()));
    disconnect(this, SIGNAL(sigStopTimer()), this, SLOT(onStopTimer()));

    if (!m_bIsStopService)
    {
        stop();
    }

    if(m_pThread->isRunning())
    {
        m_pThread->quit();
        m_pThread->wait(3000);
    }
}

/*************************************************
功能： 启动采样业务的额外处理   （一般指在子类中的额外操作）
输入参数：
         userId -- 用户ID
返回：
      额外处理是否成功
*************************************************/
bool InfraredService::startSampleExt( MultiServiceNS::USERID userId )
{
    Q_UNUSED(userId);
    QMutexLocker qMutexLocker(&m_mutexAffair);  //获取事务操作权限
    if(g_iInvalidTimerId == m_iSampleTimerId)
    {
        startReadData();
        emit sigStartTimer();
    }

    return true;
}

/*************************************************
功能： 停止采样业务的额外处理   （一般指在子类中的额外操作）
输入参数：
         userId -- 用户ID
返回：
      额外处理是否成功
*************************************************/
bool InfraredService::stopSampleExt( MultiServiceNS::USERID userId )
{
    if(m_bIsStopService)
    {
        return true;
    }

    //判断是否还有监听用户
    QVector<MultiServiceNS::USERID> sampleUser = sampleUsers(MultiServiceNS::SPECTRUM_INFRARED);
    if(sampleUser.contains(userId))
    {
        // 当前发送队列，有且只有该用户，停止定时器
        if(1 == sampleUser.size())
        {
            QMutexLocker qMutexLocker(&m_mutexAffair);  //获取事务操作权限
            stopReadData();
            emit sigStopTimer();
        }
    }

    return true;
}

/*************************************************
函数名： startReadData()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 开始读取数据
*************************************************************/
void InfraredService::startReadData()
{
    m_bIsReadData = true;
}

/*************************************************
函数名： stopReadData()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 停止读取数据
*************************************************************/
void InfraredService::stopReadData()
{
    m_bIsReadData = false;
}

/*************************************************
函数名： readParameters(Params *pstParams)
输入参数： pstParams：红外参数
输出参数： NULL
返回值： 操作结果
功能： 读取红外参数
*************************************************************/
bool InfraredService::readParameters(Params *pstParams)
{
    bool bRet = false;
#ifdef Q_PROCESSOR_ARM
    for (int j = 0; j < MAX_READ_PARAMS_FAIL_TIMES; ++j)
    {
        if (HC_SUCCESS == get_gigecamera_params(pstParams))
        {
            bRet = true;
            break;
        }
    }
#else
    bRet = true;
#endif

    if (bRet)
    {
        // 从配置文件读取参数
        readParamFromFile(pstParams->stObj);
    }

    return bRet;
}

/*************************************************
函数名： resetValidValue()
输入参数： params:红外设置的参数
输出参数： NULL
返回值： NULL
功能： 校验红外设置的参数，如辐射率或光学传输率不在合理范围，则置为最小值
*************************************************************/
void InfraredService::resetValidValue(ObjParam &params)
{
    if(params.dblEmissivity < 0.01)
    {
        params.dblEmissivity = 0.01;
    }
    if(params.dblExtOptTransm < 0.01)
    {
        params.dblExtOptTransm = 0.01;
    }

    return;
}

/*************************************************
函数名： readParamFromFile(ObjParam *pstParams)
输入参数： pstParams：环境参数
输出参数： NULL
返回值： 结果状态
功能： 读取红外环境参数
*************************************************************/
bool InfraredService::readParamFromFile(ObjParam &params)
{
    if ( !QFile::exists( IRConfig::IR_SETTING_FILE) )
    {
        logError("IR_SETTING_FILE not exists.");
        return false;
    }

    QSettings set(IRConfig::IR_SETTING_FILE, QSettings::IniFormat);
    set.beginGroup(IRConfig::Group_Param);
    params.dblEmissivity = set.value(IRConfig::Key_Param_Emiss).toDouble();
    params.dblObjDistance = set.value(IRConfig::Key_Param_Dist).toDouble();
    params.dblAtmTemp = set.value(IRConfig::Key_Param_Atm).toDouble();
    params.dblAmbTemp = set.value(IRConfig::Key_Param_Amb).toDouble();
    params.dblRelHum = set.value(IRConfig::Key_Param_Hum).toDouble();
    params.dblExtOptTemp = set.value(IRConfig::Key_Param_ExtOptTemp).toDouble();
    params.dblExtOptTransm = set.value(IRConfig::Key_Param_ExtOptTransm).toDouble();
    set.endGroup();

    resetValidValue(params);

    return true;
}

/*************************************************
函数名： onAffair(quint16 usAffair, void *pInfo, quint16 usID)
输入参数： usAffair：事务
          usID：当前事务的序号
输出参数： pInfo：存放结果数据指针
返回值： NULL
功能： 处理异步事务
*************************************************************/
void InfraredService::onAffair(quint16 usAffair, void *pInfo, quint16 usID)
{
    QMutexLocker qMutexLocker(&m_mutexAffair);  //获取事务操作权限
    int iResult = HC_SUCCESS;
    switch (usAffair)
    {
    case Infrared::INIT:
    {
        log_debug("init infrared.");
        m_bIniting = true;
        bool bRet = infraredInit();
        log_debug("init infrared, ret: %d.", bRet);
        m_bIniting = false;
        emit sigInfraredInitResult(bRet);
        if(!bRet)
        {
            iResult = HC_FAILURE;
        }

        break;
    }
    case Infrared::STOP:
    {
        log_debug("deinit infrared.");
        m_bInited = false;
#ifdef Q_PROCESSOR_ARM
        stop_camera(&m_stGigedata);
        gige_camera_exit(&m_stGigedata);
        if(HC_SUCCESS == poe_power_ctrl(0))
        {
            iResult = HC_SUCCESS;
        }
        else
        {
            iResult = HC_FAILURE;
        }
#endif
        break;
    }
    default:
        break;
    }

    m_iRet = iResult;
    if (NULL != pInfo)
    {
        *((int *)pInfo) = iResult;
    }

    m_pAffair->commit(usAffair, usID);

    return;
}

/*************************************************
函数名： onStartTimer()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 启动采集定时器
*************************************************************/
void InfraredService::onStartTimer()
{
    if(g_iInvalidTimerId == m_iSampleTimerId)
    {
        m_iSampleTimerId = startTimer(TIME_50MS);
    }

    return;
}

/*************************************************
函数名： onStopTimer()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 停止采集定时器
*************************************************************/
void InfraredService::onStopTimer()
{
    if(g_iInvalidTimerId != m_iSampleTimerId)
    {
        killTimer(m_iSampleTimerId);
        m_iSampleTimerId = g_iInvalidTimerId;
    }

    return;
}

/*************************************************
函数名： onOutLog()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 获取指令输出日志
*************************************************************/
void InfraredService::onOutLog()
{
    QString strResult;
    strResult = myprocess->readAllStandardOutput();
    if(!strResult.isEmpty())
    {
        if(strResult.contains("ttl",Qt::CaseSensitive))
        {
            m_eInfraredType = Infrared::GUIDE;
        }
        if(strResult.contains("100% packet loss",Qt::CaseSensitive))
        {
            m_eInfraredType = Infrared::FILIAR;
        }

        emit sigInfraredTypeChanged(m_eInfraredType);
    }
}

/*************************************************
函数名： infraredInit()
输入参数： NULL
输出参数： NULL
返回值： 操作结果
功能： 红外初始化
*************************************************************/
bool InfraredService::infraredInit()
{
    if(m_bInited)
    {
        logWarning("infrared has inited.");
        return true;
    }

    bool bRet = false;
#ifdef Q_PROCESSOR_ARM
    if(HC_SUCCESS != poe_power_ctrl(1))
    {
        logError("poe_power_ctrl return failed.");
        m_bInited = false;
        return bRet;
    }

    memset(&m_stGigedata, 0, sizeof(GigeData));
    for(int j = 0; j < MAX_INIT_FAIL_TIMES; ++j)
    {
        if(m_bIsStopService)
        {
            m_bInited = false;
            break;
        }

        if(HC_SUCCESS == gige_camera_init(&m_stGigedata))
        {
            m_bInited = true;
            bRet = true;
            break;
        }
        else
        {
            logError("gige_camera_init return failed.");
            Module::mSleep(1000);
            m_bInited = false;
        }
    }
#endif

    return bRet;
}

/*************************************************
函数名： restartCamara()
输入参数： NULL
输出参数： NULL
返回值： 操作结果
功能： 重启红外摄像机
*************************************************************/
bool InfraredService::restartCamera()
{
    bool bRet = true;
#ifdef Q_PROCESSOR_ARM
    stop_camera(&m_stGigedata);
    gige_camera_exit(&m_stGigedata);

    memset(&m_stGigedata, 0, sizeof(GigeData));
    INT32 iSuccess = gige_camera_init(&m_stGigedata);
    bRet = (HC_SUCCESS == iSuccess ? true : false);
#endif

    return bRet;
}

/*************************************************
函数名： setIPtoIR()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 设置与红外连接的网口的ip
*************************************************************/
void InfraredService::setIPtoIR()
{
    //设置的是T95设备的eth0网卡信息
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    QString strEth0 = pConfig->value(APPConfig::KEY_INFRARED_CAMERA_IP);
    log_debug("infrared camera ip: %s.", strEth0.toLatin1().data());
    if(SystemSet::TEXT_INFRARED_CAMERA_IP2 != strEth0 && SystemSet::TEXT_INFRARED_CAMERA_IP1 != strEth0)
    {
        //都不是两个网段时，默认设置为TEXT_INFRARED_CAMERA_IP1
        logError("wrong eth0 ip info.");
        strEth0 = SystemSet::TEXT_INFRARED_CAMERA_IP1;
        pConfig->setValue(strEth0, APPConfig::KEY_INFRARED_CAMERA_IP);
    }
    pConfig->endGroup();

    QString strBroad = (strEth0 == SystemSet::TEXT_INFRARED_CAMERA_IP2) ? SystemSet::TEXT_INFRARED_CAMERA_IP2_GATE : SystemSet::TEXT_INFRARED_CAMERA_IP1_GATE;

    //----for test by wj----
    //strEth0 = SystemSet::TEXT_INFRARED_CAMERA_IP2;
    //strBroad = SystemSet::TEXT_INFRARED_CAMERA_IP2_GATE;

    QString strCmd = QString("ifconfig eth0 %1 broadcast %2").arg(strEth0).arg(strBroad);
    logDebug(strCmd);

#ifdef Q_PROCESSOR_ARM
    system(strCmd.toStdString().c_str());
#endif

    return;
}

/*************************************************
函数名： restoreIPtoDev()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 恢复设备的网段信息
*************************************************************/
void InfraredService::restoreIPtoDev()
{
    QString strCmd = QString("ifconfig eth0 %1 broadcast %2")
            .arg(SystemSet::TEXT_INFRARED_CAMERA_IP1).arg(SystemSet::TEXT_INFRARED_CAMERA_IP1_GATE);
    logDebug(strCmd);

#ifdef Q_PROCESSOR_ARM
    system(strCmd.toStdString().c_str());
#endif

    return;
}

/*************************************************
函数名： timerEvent(QTimerEvent *event)
输入参数： event：定时器事件
输出参数： NULL
返回值： NULL
功能： 定时器事件处理
*************************************************************/
void InfraredService::timerEvent(QTimerEvent *event)
{
    QMutexLocker qMutexLocker(&m_mutexAffair);  //获取事务操作权限
    if(event->timerId() == m_iSampleTimerId)
    {
        if(!m_bIsStopService && m_bIsReadData)
        {
#ifdef Q_PROCESSOR_ARM
            if (1 == gige_camera_running(&m_stGigedata))
            {
                QSharedPointer<Infrared::InfraredData> qspData(new Infrared::InfraredData());
                memset(qspData->aucData, 0, FRAME_BUF_SIZE);
                memset(&qspData->stFrameInfo, 0, sizeof(FrameInfo));
                int iSuccess = get_camera_frame(&m_stGigedata, qspData->aucData, &qspData->stFrameInfo);
                QVector<MultiServiceNS::USERID> sampleUser = sampleUsers(MultiServiceNS::SPECTRUM_INFRARED);
                if (HC_SUCCESS == iSuccess)
                {
                    m_iPingFailTimes = 0;
                    if (m_bIsReadData)
                    {
                        for(int i = 0, iSize = sampleUser.size(); i < iSize; ++i)
                        {
                            emit sigData(qspData, sampleUser[i]);
                        }
                    }
                }
                else if (!m_bIsStopService)
                {
                    //断开重连处理
                    logDebug("camera disconnected, reconnecting...");
                    ++m_iPingFailTimes;
                    if (m_iPingFailTimes >= MAX_PING_FAIL_TIMES)
                    {
                        if(!restartCamera())
                        {
                            logError("reconnect infrared camera fail.");
                            for(int i = 0, iSize = sampleUser.size(); i < iSize; ++i)
                            {
                                emit sigReadDataFail(sampleUser[i]);
                            }
                        }
                        else
                        {
                            logInfo("reconnect infrared camera success.");
                        }
                        m_iPingFailTimes = 1;
                    }
                }
            }
            else if (!m_bIsStopService)
            {
                //断开重连处理
                logDebug("camera disconnected, reconnecting...");
                ++m_iPingFailTimes;
                if (m_iPingFailTimes >= MAX_PING_FAIL_TIMES)
                {
                    if(!restartCamera())
                    {
                        logError("reconnect infrared camera fail.");
                        QVector<MultiServiceNS::USERID> sampleUser = sampleUsers(MultiServiceNS::SPECTRUM_INFRARED);
                        for(int i = 0, iSize = sampleUser.size(); i < iSize; ++i)
                        {
                            emit sigReadDataFail(sampleUser[i]);
                        }
                    }
                    else
                    {
                        logInfo("reconnect infrared camera success.");
                    }
                    m_iPingFailTimes = 0;
                }
            }
#endif
        }
    }
    else
    {
        killTimer(event->timerId());
    }

    return;
}


/***************************************************
 * 功能，初始化模块
 * *************************************************/
void InfraredService::initModule()
{
    QtConcurrent::run(this, &InfraredService::pfnInitModule);
    return;
}

/***************************************************
 * 功能，反初始化模块
 * *************************************************/
void InfraredService::deinitModule()
{
    QtConcurrent::run(this, &InfraredService::pfnDeinitModule);
    return;
}

/***************************************************
 * 功能，初始化模块
 * *************************************************/
void InfraredService::pfnInitModule()
{
    setIPtoIR();
    start();
    m_bIsStopService = false;
    m_pAffair->request(Infrared::INIT, NULL);

    return;
}

/***************************************************
 * 功能，反初始化模块
 * *************************************************/
void InfraredService::pfnDeinitModule()
{    
    restoreIPtoDev();
    emit sigStopTimer();
    stop();
    m_bIsStopService = true;
    m_pAffair->request(Infrared::STOP, NULL);

    return;
}

/***************************************************
 * 功能，是否已初始化
 * *************************************************/
bool InfraredService::isInited()
{
    return m_bInited;
}

/***************************************************
 * 功能，是否初始化中
 * *************************************************/
bool InfraredService::isIniting()
{
    return m_bIniting;
}

/***************************************************
 * 功能，初始化红外模组接入类型
 * *************************************************/
Infrared::InfraredType InfraredService::initInfraredType()
{
    //pfnFlierInfraredType();
    pfnInfraredType();
    qDebug() << "Make failed:m_eInfraredTypem_eInfraredTypem_eInfraredType-----"<<m_eInfraredType;
    return m_eInfraredType;
}


/***************************************************
 * 功能，获取红外模组类型
 * *************************************************/
Infrared::InfraredType InfraredService::getInfraredType()
{
    qDebug() << "Make failed:current m_eInfraredType-----"<<m_eInfraredType;
    return m_eInfraredType;
}

/***************************************************
 * 功能，执行获取红外接入装置命令
 * *************************************************/
void InfraredService::pfnInfraredType()
{
    myprocess->setProcessChannelMode(QProcess::MergedChannels);
    myprocess->start("ping -c 1 -w 1 192.168.2.11");
    if (!myprocess->waitForFinished())
	{
        qDebug() << "Make failed:" << myprocess->errorString();
	}
    myprocess->close();

}

/***************************************************
 * 功能，执行获取红外接入装置命令
 * *************************************************/
void InfraredService::pfnFlierInfraredType()
{
    myprocess->setProcessChannelMode(QProcess::MergedChannels);
    myprocess->start("ping -c 1 -w 1 192.168.2.11");
    if (!myprocess->waitForFinished())
    {
        qDebug() << "Make failed:" << myprocess->errorString();
    }
    myprocess->close();

}

