/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* PDATestDataView.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年6月21日
* 摘要：测试项列表

* 当前版本：1.0
*/

#ifndef PDATESTDATAVIEW_H
#define PDATESTDATAVIEW_H

#include <QMutex>
#include "PDAUi/PDAUiView/pdalistview.h"
#include "PDAUi/PDAUiBean/pdalistchart.h"
#include "pda/pda.h"
#include "pda/pdaservice.h"

class PDATestDataView : public PDAListView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent:父控件指针
    *************************************************************/
    explicit PDATestDataView(QWidget *parent = 0);

protected:
    /*************************************************
    功能： 定时器处理函数
    输入参数：
            e -- 定时事件
    *************************************************************/
    void timerEvent( QTimerEvent* e );

    /****************************
    功能： 处理showEvent事件
    输入参数:
           event -- showEvent
    *****************************/
    void showEvent(QShowEvent *event);

protected slots:
    /*************************************************
    功能： 槽，响应条目被点击后的事件
    输入参数：
            strItem -- 条目名称
    *************************************************************/
    void onItemClicked( int id );

private slots:
    /*************************************************
    功能： 槽函数， 刷新界面
    输入参数:NULL
    *************************************************************/
    void onTaskChanged();

    /*************************************************
    功能： 槽函数， 测试项列表自动跳转界面延时显示
    （为在跳转前后能够短暂回到测试项列表，避免突兀的在多个测试界面之间跳转）
    *************************************************************/
    void onAutoSwitchViewDelay();

    /*************************************************
    功能： 槽函数， 响应自动跳转状态发生变化的信号
    *************************************************************/
    void onAutoSwitched();

    /*************************************************
    功能： 槽函数， 响应检测页面关闭信号
    *************************************************************/
    void onTestViewClosed();

private:
    /*************************************************
    功能： 刷新列表
    输入参数:测点
    *************************************************************/
    void refreshList( const ItemTestPoint& testPoint );

    /*************************************************
    功能： 获取测试项的相关信息
    输入参数:testPoint -- 测点
    返回值：条目显示需要的测试项信息
    *************************************************************/
    QList<PDAListChart::ListItemInfo> testDataInfo( const ItemTestPoint& testPoint );

    /*************************************************
    功能： 统计测试完成状态，界面显示相应提示
    *************************************************************/
    void checkTestState( void );

private:
    PDATask *m_pCurrentTask;//pda 当前task对象

    qint32 m_iAutoSwitchTimerId;    // 是否自动跳转的定时器id
    int m_iTestDataPos;             // 首个未经测试的测试项位置

    bool m_bClicked;
    QMutex m_mtClick;
    QMutex m_mt4ItemInfo;   //加锁处理，避免界面崩溃
};

#endif // PDATESTDATAVIEW_H
