/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* AEAmpView.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月9日
* 修改日期：2016年12月30日
*       新版本重构
* 摘要：AE幅值图谱定义

* 当前版本：1.0
*/

#ifndef AEWAVEVIEW_H
#define AEWAVEVIEW_H

#include "aewaveview/aewaveviewbase.h"
#include "ae/AE.h"
#include "ae/AEWaveService.h"
#include "AeWave.h"
#include "buttonBar/LabelButtonBar.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "recordplay/RecordPlayService.h"
#include "ae/AEView.h"

class AEWaveView : public AEWaveViewBase
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        parent:父控件指针
    *************************************************************/
    explicit AEWaveView(const QString& strTitle, QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~AEWaveView();

    /*************************************************
    功能： 启动采样
    *************************************************************/
    void startSample();

    /*************************************************
    功能： 停止采样
    *************************************************************/
    void stopSample();

protected:
    /*************************************************
    功能： 响应S键事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onSKeyPressed();

	/******************************************
     * 功能：AE录音到最大录音时间，处理逻辑
     * ****************************************/
    void stopAERecordNextToDo();

    /****************************************************
     * 功能：保存操作
     * **************************************************/
    void pressSaveData();
	
protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

    /*************************************************
    功能： 槽，响应读取的数据
    输入参数：
            data -- 波形数据
            id -- 用户id
    *************************************************************/
    void onDataRead(QVector< AE::WaveData > datas,MultiServiceNS::USERID userId);

    /*************************************************
    功能： 槽，响应通道变化
    输入参数：
            eChannel -- 变化后的通道
    *************************************************************/
    void onChannelChanged( AE::ChannelType eChannel );

    /*************************************************
    功能： 槽，响应同步状态变化
    输入参数：
            eSyncState -- 同步状态
    *************************************************************/
    void onSyncStateChanged( Module::SyncState eSyncState );

    /*************************************************
    功能： 槽，响应录音界面关闭后的处理（重新开始监听超声波）
    *************************************************************/
    void onStartListenAE( );

    /*************************************************
    功能： 槽，响应读取数据失败
    *************************************************************/
    void onDataReadFailed();

    /*************************************************
    功能： 槽，响应录音完成
    输入参数：
            strRecordFilePath -- 录音文件保存的路径
            uint -- 录音时长
    *************************************************************/
    void onRecorded(const QString& strRecordFilePath, const uint uiRecordingTime);

	/*************************************************
    功能： 槽，录音时长变化
    输入参数：
          	uiRecordingTime 录音时长 单位s
    *************************************************************/
    void onTimerRefresh(uint uiRecordingTime);

    /*************************************************
    功能： 槽，响应系统音量变化
    输入参数：
            qui8Volume -- 系统音量
    *************************************************************/
    void onSysVolume(quint8 qui8Volume);
	
private:
    /*************************************************
    功能： 初始化数据
    *************************************************************/
    void initDatas();

    /*************************************************
    功能： 设置按钮栏数据
    *************************************************************/
    void setButtonBarDatas();

    /*************************************************
    功能： 设置表格数据
    *************************************************************/
    void setChartDatas();

	/*************************************************
    功能： 载入数据
    *************************************************************/
    void loadRecordData();

    /*************************************************
    功能： 删除数据
    *************************************************************/
    void deleteRecordData();

	/*************************************************
    功能： 保存录音文件
    输入参数：
            strRecordFilePath -- 录音文件保存的路径
            uint -- 录音时长
    *************************************************************/
    void saveRecorded(const QString& strRecordFilePath, const quint32 uiRecordingTime);
	
    /*************************************************
    功能： 载入数据
    *************************************************************/
    void loadData();

    /*************************************************
    功能： 删除数据
    *************************************************************/
    void deleteData();

    /*************************************************
    功能： 录音
    *************************************************************/
    void recorder();

    /*************************************************
    功能： 恢复默认
    *************************************************************/
    void restoreDefault();

    /*************************************************
    功能： 保存数据
    输入参数：
            stationName -- 变电站名
            deviceName -- 设备名
    返回：
        保存后的文件名
    *************************************************************/
    QString saveDataToFile();

    /*************************************************
    功能： 保存设置
    *************************************************************/
    bool saveConfig();

    /*************************************************
    功能： 触发值按钮的触发值列表根据增益、量纲动态变化
    输入参数：
            eGain -- 增益
    *************************************************************/
    void resetTriggerList( AE::GainType eGain );

    /*************************************************
    功能： 幅值范围按钮的列表根据增益动态变化
    输入参数：
            eGain -- 增益
    *************************************************************/
    void resetAmpRange( AE::GainType eGain );


    /*************************************************
    功能： 设置工作模式
    *************************************************************/
    void setWorkMode();

    /*************************************************
    功能： 设置所有工作参数
    *************************************************************/
    void setAllWorkingSet();

    /*************************************************
    功能： 判断是否满足触发条件（单次）
    输入参数：
            data -- 采集的数据
    *************************************************************/
    bool isTriggered( const AE::WaveData& data );

    /*************************************************
    功能： 初始化图谱状态标识
    *************************************************************/
    void initChartState( void );

    /*****************************************
     * 功能：初始化按钮栏信息
     * **********************************************/
    void initBtnBarInfo();

private:
    AEWave* m_pChart;//图谱
    ConfigInstance* m_pConfig;//配置模块

    AE::GainType m_eGain;//增益
    AEFilter m_eFilter; //带宽
    AE::SampleMode m_eSampleMode;//采样模式
    AE::SampleTime m_eSampleTime;//采集时间
    AE::AmpRange m_eAmpRange;//幅值范围
    AE::TriggerValue m_eTriggerValue;//触发值
    AE::ChannelType m_eChannel;//通道
    Module::SyncSource m_eSyncSource;//同步源
    Module::SyncState m_eSyncState;//同步状态
    bool m_bTriggered;//是否已触发（单次模式）
    QVector< AE::WaveData > m_data;//数据

    ButtonInfo::Info *m_pMoreBtnInfo;
    quint8 m_qui8MoreBtnCnt;
};

#endif // AEWAVEVIEW_H
