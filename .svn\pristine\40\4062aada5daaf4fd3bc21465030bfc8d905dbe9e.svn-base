#include "usbmodemanager.h"
#include <QMutex>
#include <QMutexLocker>
#include <QDir>
#include <QFile>
#include "global_log.h"

#ifdef Q_PROCESSOR_ARM
#include <unistd.h>
#endif

//static QMutex g_mtUsbModeObj;

UsbModeManager::UsbModeManager()
{
}

UsbModeManager::~UsbModeManager()
{
}

UsbModeManager* UsbModeManager::instance()
{
    //QMutexLocker stLocker(&g_mtUsbModeObj);
    static UsbModeManager objManager;
    return &objManager;
}

void UsbModeManager::createPackageFolder()
{
    QFileInfo objFileInfo(PACKAGE_FOLDER);
    if(!(objFileInfo.exists()))
    {
        QDir objDir;
        if(objDir.mkpath(PACKAGE_FOLDER))
        {

            log_debug("create %s success...", PACKAGE_FOLDER);
#ifdef Q_PROCESSOR_ARM
            //set file folder permission
            //QString qstrCmd = QString("chmod 744 -R %1").arg(PACKAGE_FOLDER);
            QString qstrCmd = QString("chattr +a -R %1").arg(PACKAGE_FOLDER);
            system(qstrCmd.toStdString().c_str());
#endif
        }
        else
        {
            log_debug("create %s fail...", PACKAGE_FOLDER);
        }
    }
    else
    {
        log_debug("%s is exist...", PACKAGE_FOLDER);
    }

    return;
}


