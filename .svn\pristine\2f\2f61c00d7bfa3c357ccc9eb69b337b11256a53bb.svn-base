﻿#include "infraredpdaview.h"
#include <QFutureWatcher>
#include <QtConcurrentRun>
#include "infrareddefine.h"
#include "window/Window.h"
#include "dataSave/DataFileInfos.h"
#include "deleteDataView/RotateDeleteDataView.h"
#include "infrared/InfraredDataSave.h"
#include "infrared/rotatewidget.h"
#include "infraredplaybackview.h"
#include "config/ConfigManager.h"
#include "systemsetting/SystemSet.h"
#include "statusbar/StatusBar.h"
#include "appconfig.h"
#include "pda/pdaservice.h"
#include "pda/pdatask.h"
#include "appfontmanager/appfontmanager.h"
#include "messageBox/waitmsgbox.h"
#include "log/log.h"


const int INVALID_USER = -1;
// 采集页面按钮菜单
const InfraredButtonInfo s_InfraredButtonInfos[] =
{
    { BUTTON_INFRARED_STATUS,           &g_InfraredRunButtonInfo, },
    { BUTTON_INFRARED_COLOR_TYPE,       &g_InfraredColorButtonInfoBasic, },
    { BUTTON_INFRARED_ANALYSE_SHAPE,    &g_InfraredAnalyseShapeBasic, },
    { BUTTON_INFRARED_DELETE_ALL_SHAPE, &g_InfraredDelAllButtonInfo, },
    { BUTTON_INFRARED_SET_PARAM,        &g_InfraredSetParamButtonInfo, },
    { BUTTON_INFRARED_ADD,              &g_InfraredAdd, },
    { BUTTON_INFRARED_SAVA_DATA,        &g_SaveDataButtonInfo, },
    { BUTTON_INFRARED_EXIT,             &g_InfraredExit, },
    //{ BUTTON_INFRARED_LOAD_DATA,        &g_LoadDataButtonInfo, },
    //{ BUTTON_INFRARED_DELETE_DATA,      &g_DeleteDataButtonInfo, }
};


InfraredSaveInfo pfnSaveDataFun(InfraredViewBase *pUserInfo,
                                PDATask *pTask,
                                const InfraredDataInfo &stInfraredDataInfo,
                                const DataMapHead &stHeadInfo)
{
    InfraredSaveInfo stSaveInfo;

    if(pUserInfo && pTask)
    {
        pTask->saveInfraredTestData(stInfraredDataInfo, stHeadInfo, stSaveInfo.qstrDataFilePath, stSaveInfo.qstrPicturePath);
    }
    else
    {
        logError("pUserInfo == NULL || pTask == NULL");
    }

    return stSaveInfo;
}

/*************************************************
函数名： InfraredView(QWidget *parent)
输入参数： parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
InfraredPDAView::InfraredPDAView(const QString &strTitle, const ItemTestData &stTestDataInfo, QWidget *parent)
    : InfraredViewBase(parent)
    , m_pService(NULL)
    , m_bIsReadData(false)
    , m_bIsReadDataBeforePlayback(false)
    , m_bInitFlag(true)
    , m_bSave(false)
    , m_strTitle(strTitle)
{
    m_qstrTestDataFilePath = stTestDataInfo.strFilePath;
    m_stTestData = stTestDataInfo;
    m_bPlaybacking = false;

    InfraredControlButtonBar* pButtonBar = createButtonBar(s_InfraredButtonInfos, sizeof(s_InfraredButtonInfos) / sizeof(InfraredButtonInfo));
    setButtonBar(pButtonBar);

    disableButtons();
    // 载入数据可用
    //m_pButtonBar->buttons(BUTTON_INFRARED_LOAD_DATA)->setEnabled(true);
    m_pButtonBar->buttons(BUTTON_INFRARED_STATUS)->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_RECOVER));
    //m_pButtonBar->buttons(BUTTON_INFRARED_DELETE_DATA)->setEnabled(true);

    //connect(m_pInfraredImagingView, SIGNAL(sigCreateAnalysisShape()), this, SLOT(onStopSample()));
    connect(m_pInfraredImagingView, SIGNAL(sigCreateAnalysisShape()), this, SLOT(onCreateShape()));
    connect(m_pInfraredImagingView, SIGNAL(sigRestartSample()), this, SLOT(onRestartSample()));

    m_pService = InfraredService::instance();
    connect(m_pService, SIGNAL(sigData(QSharedPointer<Infrared::InfraredData>, MultiServiceNS::USERID)), this, SLOT(onData(QSharedPointer<Infrared::InfraredData>, MultiServiceNS::USERID)));
    connect(m_pService, SIGNAL(sigReadDataFail(MultiServiceNS::USERID)), this, SLOT(onReadDataFail()));
    connect(m_pService, SIGNAL(sigInfraredInitResult(bool)), this, SLOT(onInfraredInitResult(bool)));
    connect(this, SIGNAL(sigExitInfrared()), this, SLOT(onExitInfrared()));

    m_iUserId = INVALID_USER;
    MultiServiceNS::SampleUser user;
    user.eSampleType = MultiServiceNS::SPECTRUM_INFRARED;
    m_iUserId = m_pService->addUser(user);

    //显示上次的测试数据
    if(m_stTestData.bTested)
    {
        /*if(m_pBtnBars)
        {
            m_pBtnBars->hide();
        }*/

        playBackTestedData(m_qstrTestDataFilePath);
        QTimer::singleShot(START_SAMPLE_TIME_INTERVAL_2000MS, this, SLOT(onRecoverSampleView()));
    }
    else
    {
        recoverStartSample();
    }
}

/*************************************************
函数名： ~InfraredView()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
InfraredPDAView::~InfraredPDAView()
{
}

/************************************************
 * 函数名   : onRecoverSampleView
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :槽函数，起动采样
 ************************************************/
void InfraredPDAView::onRecoverSampleView()
{
    if(m_pButtonBar)
    {
        m_pButtonBar->setFocus();
        m_pButtonBar->activeFirst();

        bool bAddEnable = m_stTestData.bIsBgn ? false : true;
        m_pButtonBar->buttons(BUTTON_INFRARED_ADD)->setEnabled(bAddEnable);
        m_pButtonBar->buttons(BUTTON_INFRARED_EXIT)->setEnabled(true);

        // 清空回放显示的标题
        if (!m_qstrTestDataFilePath.isEmpty())
        {
            m_qstrTestDataFilePath.clear();
            m_pInfraredImagingView->showFileName(m_qstrTestDataFilePath);
        }
        m_pInfraredImagingView->clearData();

        recoverStartSample();
    }
}

/************************************************
 * 函数名   : onAddTestData
 * 输入参数 :  struct_AddingTestData&
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :槽函数，将新增测试项界面的信息写入pdaTask中
 ************************************************/
void InfraredPDAView::onAddTestData(struct_AddingTestData& stAddTestData)
{
    for(int i = 0, iSize = stAddTestData.evType.size(); i < iSize; ++i)
    {
        ItemTestData stTestData;
        stTestData.bIsBgn = m_stTestData.bIsBgn;
        stTestData.eDataType = stAddTestData.evType.at(i);
        stTestData.eDataUnit = m_stTestData.eDataUnit;

        PDAService::instance()->currentTask()->addTestData(stTestData);
    }
    return;
}

/************************************************
 * 函数名   : playBackTestedData
 * 输入参数 : qstrFile---数据文件存放路径
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 回放数据文件里的测试数据
 ************************************************/
void InfraredPDAView::playBackTestedData(const QString &qstrFile)
{
    InfraredDataInfo stData;
    InfraredDataSave dataSave;

    m_bPlaybacking = true;
    if (HC_SUCCESS == dataSave.getDataFromFile(qstrFile, &stData))
    {
        FrameInfo info;
        info.size_x = stData.uiWidth;
        info.size_y = stData.uiHeight;
        info.frame_sz = stData.uiWidth * stData.uiHeight * stData.ucPixelCount;

        Params stParams;
        stParams.stObj.dblEmissivity   = stData.RadiationRate;
        stParams.stObj.dblObjDistance  = stData.TestDistance;
        stParams.stObj.dblAtmTemp      = stData.AtmosphericTemperature;
        stParams.stObj.dblAmbTemp      = stData.ReflectedTemperature;
        stParams.stObj.dblExtOptTemp   = stData.ExternalOpticalTemperatue;
        stParams.stObj.dblExtOptTransm = stData.ExternalOpticalRate;
        stParams.stObj.dblRelHum       = stData.RelativeHumidity;

        stParams.stPlanck.iR   = stData.TauPlanckConstiR;
        stParams.stPlanck.dblB = stData.TauPlanckConstdblB;
        stParams.stPlanck.dblF = stData.TauPlanckConstdblF;
        stParams.stPlanck.dblO = stData.TauPlanckConstdblO;

        stParams.stSpec.dblX      = stData.SpectralResponsedblX;
        stParams.stSpec.dblAlpha1 = stData.SpectralResponsedblAlpha1;
        stParams.stSpec.dblAlpha2 = stData.SpectralResponsedblAlpha2;
        stParams.stSpec.dblBeta1  = stData.SpectralResponsedblBeta1;
        stParams.stSpec.dblBeta2  = stData.SpectralResponsedblBeta2;

        TemperatureInfo stTmpInfo;
        stTmpInfo.max = stData.fMaxTemperature;
        stTmpInfo.min = stData.fMinTemperature;
        stTmpInfo.avg = stData.fAvgTemperature;

        ColorType cType = (ColorType)(stData.iColorType);
        if (cType < COLOR_TYPE_IRON || cType >= COLOR_TYPE_COUNT)
        {
            cType = COLOR_TYPE_IRON;
        }
        m_eCurrentPalette = cType;

        setPlaybackViewTag(true);

        if(m_pInfraredImagingView)
        {
            m_pInfraredImagingView->setReferenceTemp(stData.fRefTemperature);
            m_pInfraredImagingView->setColorType(m_eCurrentPalette);
            m_pInfraredImagingView->playBack((unsigned short *)(stData.aucInfraredData), info, stParams);
            m_pInfraredImagingView->setTempInfo(stTmpInfo);
            QString strTitle = qstrFile.mid(qstrFile.lastIndexOf('/') + 1);
            m_pInfraredImagingView->showFileName(strTitle);
            m_pInfraredImagingView->setEnabled(false); // 避免没有数据时点击屏幕会导致分析图形按钮使能
        }
    }
}

/************************************************
 * 函数名   : recoverStartSample
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :起动采样
 ************************************************/
void InfraredPDAView::recoverStartSample()
{
    m_bPlaybacking = false;
    startSample();
    return;
}

/*************************************
 * 功能：开始采集数据
 * *************************************/
void InfraredPDAView::startSample()
{
    setPlaybackViewTag(false);

    if(m_pService->isInited())
    {
        onInitSuccess();
    }
    else if(!m_pService->isInited() && !m_pService->isIniting())
    {
        m_pService->initModule();
    }

    return;
}

/*************************************
 * 功能：停止采集数据
 * *************************************/
void InfraredPDAView::stopSample()
{
    onExitInfrared();
    return;
}

/************************************************
 * 函数名   : setTestPointInfo
 * 输入参数 : stTestPointInfo---巡检测点相关信息
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 设置显示测点信息
 ************************************************/
void InfraredPDAView::setTestPointInfo(const View::PatrolTestPointInfo &stTestPointInfo)
{
    m_stTestPointInfo = stTestPointInfo;
    setStationInfo(m_stTestPointInfo.strStationName, m_stTestPointInfo.strDeviceName, m_stTestPointInfo.strTestPointName);
    showStationInfo(true);
    return;
}

/*************************************************
函数名： onPlaybackFinished()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应回放结束信号
*************************************************************/
void InfraredPDAView::onPlaybackFinished()
{
    if (m_bIsReadDataBeforePlayback)
    {
        resume();
    }
    return;
}

/*************************************************
函数名： onData(Infrared::InfraredData stData)
输入参数： data：红外数据
输出参数： NULL
返回值： NULL
功能： 响应红外数据信号
*************************************************************/
void InfraredPDAView::onData(QSharedPointer<Infrared::InfraredData> qspInfraredData, MultiServiceNS::USERID userId)
{
    if ( m_bSave )
    {
        return;
    }

    if( userId == m_iUserId )
    {
        // 有数据来了恢复使能
        if (!m_pInfraredImagingView->isEnabled())
        {
            m_pInfraredImagingView->setEnabled(true);
        }
        m_pInfraredImagingView->setData(qspInfraredData->aucData, qspInfraredData->stFrameInfo);
    }
}

/*************************************************
函数名： onReadDataFail()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应读取数据失败信号
*************************************************************/
void InfraredPDAView::onReadDataFail()
{
    //suspend();        // commanded by wujun 2018.6.24

    //    MsgBox *pMsgBox = new MsgBox(MsgBox::INFORMATION);
    //    pMsgBox->setInfo("", trUtf8("Fail to get infrared data."));
    //    InfraredViewBase::rotateMsgBox(pMsgBox);
}

/*************************************************
函数名： onInfraredInitResult(bool isSuccess)
输入参数： isSuccess：红外初始化结果
输出参数： NULL
返回值： NULL
功能： 响应红外初始化结果信号
*************************************************************/
void InfraredPDAView::onInfraredInitResult(bool isSuccess)
{
    logInfo(QString("infrared init result: %1.").arg(isSuccess).toLatin1().data());
    m_bInitFlag = false;
    if(isSuccess)
    {
        onInitSuccess();
    }
    else
    {
        onInitFail();
    }
}

/*************************************************
函数名： showEvent(QShowEvent *e)
输入参数： e：显示事件
输出参数： NULL
返回值： NULL
功能： 显示事件处理
*************************************************************/
void InfraredPDAView::showEvent(QShowEvent *e)
{
    StatusBar::instance()->hide();
    InfraredViewBase::showEvent(e);
    m_pButtonBar->activeFirst();

    if(!m_bPlaybacking)
    {
        if(m_pService->isInited())
        {
            onInitSuccess();
        }
        else if(!m_pService->isInited() && !m_pService->isIniting())
        {
            m_pService->initModule();
        }

        QTimer::singleShot(1000, this, SLOT(showInitDialog()));
    }

    bool bAddEnable = m_stTestData.bIsBgn ? false : true;
    if(m_pButtonBar)
    {
        if(!m_stTestData.bTested)
        {
            m_pButtonBar->buttons(BUTTON_INFRARED_ADD)->setEnabled(bAddEnable);
            m_pButtonBar->buttons(BUTTON_INFRARED_EXIT)->setEnabled(true);
        }
    }
}

/*************************************************
函数名： keyPressEvent(QKeyEvent *e)
输入参数： e：按键事件
输出参数： NULL
返回值： NULL
功能： 按键事件处理
*************************************************************/
void InfraredPDAView::keyPressEvent(QKeyEvent *e)
{
    if (Qt::Key_Escape == e->key())
    {
        if(m_bPdaWaiting)
        {
            logInfo("saving data...");
            return;
        }

        //emit sigExitInfrared();
        onExitInfrared();
        close();
    }
    else if (Qt::Key_F1 == e->key())       // S键
    {
        onPressSKey();
    }
    else
    {
        InfraredViewBase::keyPressEvent(e);
    }

    return;
}

/*************************************************
功能： 处理窗口关闭事件
*************************************************************/
void InfraredPDAView::closeEvent(QCloseEvent* event)
{
    InfraredViewBase::closeEvent(event);
    StatusBar::instance()->show();
    return;
}

/*************************************************
函数名： onButtonPressed(UINT8 ucID)
输入参数： ucID：按钮ID
输出参数： NULL
返回值： NULL
功能： 按钮响应处理
*************************************************************/
void InfraredPDAView::onButtonPressed(UINT8 ucID)
{
    if (m_pButtonBar->buttons(ucID))
    {
        if (!m_pButtonBar->buttons(ucID)->isEnabled())
        {
            logError("current press button is not enable.");
            return;
        }
    }

    switch (ucID)
    {
    case BUTTON_INFRARED_STATUS:
    {
        readDataCtrl();
    }
        break;

    case BUTTON_INFRARED_SAVA_DATA:     // 保存数据
    {
        saveData(true);
    }
        break;

    case BUTTON_INFRARED_LOAD_DATA:
    {
        loadData();
    }
        break;

    case BUTTON_INFRARED_DELETE_DATA:
    {
        deleteData();
    }
        break;

    case BUTTON_INFRARED_ADD://新增测试项
    {
        addTestData();
        break;
    }

    case BUTTON_INFRARED_EXIT://退出
    {
        if(m_bPdaWaiting)
        {
            logInfo("saving data...");
        }
        else
        {
            //emit sigExitInfrared();
            onExitInfrared();
            close();
        }

        break;
    }

    default:
        InfraredViewBase::onButtonPressed(ucID);
        break;
    }
    return;
}

/*************************************************
函数名： loadData()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 载入数据
*************************************************************/
void InfraredPDAView::loadData()
{
    QString filePath = DATA_STORAGE_PATH + "/" + INFRARED_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if (!infoList.isEmpty())
    {
        m_bIsReadDataBeforePlayback = m_bIsReadData;
        suspend();

        RotatePlayBackView *pView = new RotatePlayBackView(filePath, INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_PLAYBACK), INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_LOAD_DATA), false, false, false, false);
        InfraredPlaybackView *pPlayBackView = new InfraredPlaybackView();

        connect(pPlayBackView, SIGNAL(sigPlayNextFile()),   pView, SLOT(onPlayNextFile()));
        connect(pPlayBackView, SIGNAL(sigPlayLastFile()),   pView, SLOT(onPlayLastFile()));
        connect(pPlayBackView, SIGNAL(sigExit()), pView, SLOT(onExitPlayBack()));
        connect(pPlayBackView, SIGNAL(destroyed(QObject *)), this, SLOT(onPlaybackFinished()));

        pView->addPlayback(INFRARED_FILE_NAME_SUFFIX, pPlayBackView);
        //pView->exec();
        pView->show();
    }
    else
    {
        MsgBox *pMsgBox = new MsgBox(MsgBox::WARNING);
        pMsgBox->setInfo("", QObject::trUtf8("No file!"));
        rotateMsgBox(pMsgBox);
    }
    return;
}

/*************************************************
函数名： suspend()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 暂停
*************************************************************/
void InfraredPDAView::suspend()
{
    if (m_bIsReadData)
    {
        onStopSample();

        m_pInfraredImagingView->suspend();

        if (m_pButtonBar->buttons(BUTTON_INFRARED_STATUS))
        {
            m_pButtonBar->buttons(BUTTON_INFRARED_STATUS)->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_RECOVER));
        }
        //m_pButtonBar->buttons(BUTTON_INFRARED_ANALYSE_SHAPE)->setEnabled(m_bIsReadData);
    }
    return;
}

void InfraredPDAView::onCreateShape()
{
    if (m_bIsReadData)
    {
        onStopSample();

        if (m_pButtonBar->buttons(BUTTON_INFRARED_STATUS))
        {
            m_pButtonBar->buttons(BUTTON_INFRARED_STATUS)->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_RECOVER));
        }
    }
    return;
}

/*************************************************
函数名： resume()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 恢复
*************************************************************/
void InfraredPDAView::resume()
{
    m_bIsReadData = true;
    m_pService->startReadData();
    m_pInfraredImagingView->resume();
    m_pButtonBar->buttons(BUTTON_INFRARED_STATUS)->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_FROZEN));
    return;
}

/*************************************************
函数名： onStopSample()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 停止采集
*************************************************************/
void InfraredPDAView::onStopSample()
{
    m_pService->stopReadData();
    m_bIsReadData = false;
    return;
}

/*************************************************
函数名： onRestartSample()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 重新开始采集
*************************************************************/
void InfraredPDAView::onRestartSample()
{
    m_pService->startReadData();
    m_bIsReadData = true;

    // add by wujun
    m_pButtonBar->buttons(BUTTON_INFRARED_STATUS)->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_FROZEN));
    return;
}

/*************************************************
函数名： onPressSKey()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 处理S键
*************************************************************/
void InfraredPDAView::onPressSKey()
{
    saveData(true);
    return;
}

/*************************************************
函数名： readDataCtrl()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 控制红外数据读取
*************************************************************/
void InfraredPDAView::readDataCtrl()
{
    if (m_bIsReadData)
    {
        suspend();
    }
    else
    {
        resume();
    }
    return;
}

/*************************************************
函数名： saveData()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 保存红外数据
*************************************************************/
void InfraredPDAView::saveData(bool bSaveJpeg)
{
    if(m_bPdaWaiting)
    {
        logInfo("saving data...");
        return;
    }

    m_bPdaWaiting = true;

    m_bSave = true;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    SystemSet::TemperatureUnitOption eUnit = (SystemSet::TemperatureUnitOption) pConfig->value( APPConfig::KEY_TEMPERATURE_UNIT ).toInt();

    if (m_pInfraredImagingView->isDataEmpty())
    {
        emit sigSaveFinished();

        m_bPdaWaiting = false;
        MsgBox *pMsgBox = new MsgBox(MsgBox::INFORMATION);
        pMsgBox->setInfo("", QObject::trUtf8("Empty data, save failure!"));
        rotateMsgBox(pMsgBox);
    }
    else
    {
        suspend();

        FrameInfo stFrameInfo = {0, 0, 0};
        InfraredDataInfo stInfraredDataInfo;
        Params stParams;

        // get data and params
        m_pInfraredImagingView->data((unsigned short *)stInfraredDataInfo.aucInfraredData,
                                     INFRARED_DATA_LEN, stFrameInfo, stParams);

        // 当前温度信息(max,min,avg,...)
        TemperatureInfo tmpInfo = m_pInfraredImagingView->temperatureInfomation();

        // TODO: get info and save as FFF
        stInfraredDataInfo.strSubstationName           = m_stTestPointInfo.strStationName;
        stInfraredDataInfo.strTestedDevName            = m_stTestPointInfo.strDeviceName;
        stInfraredDataInfo.dateTime                    = QDateTime::currentDateTime();
        stInfraredDataInfo.uiWidth                     = stFrameInfo.size_x;
        stInfraredDataInfo.uiHeight                    = stFrameInfo.size_y;
        stInfraredDataInfo.ucPixelCount                = sizeof(unsigned short);
        stInfraredDataInfo.ucPixelConvertToTemperature = 1;
        stInfraredDataInfo.fFactor                     = 0.04;      //todo
        stInfraredDataInfo.fOffset                     = 273.15;    //todo
        if(eUnit == SystemSet::TEMPERATURE_UNIT_CENTIGRADE)//摄氏度
        {
            stInfraredDataInfo.fMaxTemperature = tmpInfo.max;
            stInfraredDataInfo.fMinTemperature = tmpInfo.min;
            stInfraredDataInfo.fAvgTemperature = tmpInfo.avg;
            stInfraredDataInfo.fRefTemperature = m_pInfraredImagingView->getReferenceTemp();
        }
        else        //华氏度
        {
            //double dValue = Module::Centigrade2Fahrenheit(tmpInfo.max);
            stInfraredDataInfo.fMaxTemperature = tmpInfo.max;

            //dValue = Module::Centigrade2Fahrenheit(tmpInfo.min);
            stInfraredDataInfo.fMinTemperature = tmpInfo.min;

            //dValue = Module::Centigrade2Fahrenheit(tmpInfo.avg);
            stInfraredDataInfo.fAvgTemperature = tmpInfo.avg;

            //dValue = Module::Centigrade2Fahrenheit(m_pInfraredImagingView->getReferenceTemp());
            stInfraredDataInfo.fRefTemperature = m_pInfraredImagingView->getReferenceTemp(); //参考温度需要界面输入
        }

#ifdef _MALAYSIA_VERSION_DEFINED_
        stInfraredDataInfo.fDifTemperature = Module::dealFloatPrecision(stInfraredDataInfo.fMaxTemperature - stInfraredDataInfo.fMinTemperature, m_iPrecious);
#else
        stInfraredDataInfo.fDifTemperature = Module::dealFloatPrecision(stInfraredDataInfo.fMaxTemperature - stInfraredDataInfo.fRefTemperature, m_iPrecious);
#endif
        stInfraredDataInfo.RadiationRate             = stParams.stObj.dblEmissivity;

        //大气温度、测试距离和反射温度 辐射率只保留小数点后一位 解决上位机显示问题
        stParams.stObj.dblObjDistance = Module::dealDoublePrecision(stParams.stObj.dblObjDistance, m_iPrecious);
        stParams.stObj.dblAtmTemp = Module::dealDoublePrecision(stParams.stObj.dblAtmTemp, m_iPrecious);
        stParams.stObj.dblAmbTemp = Module::dealDoublePrecision(stParams.stObj.dblAmbTemp, m_iPrecious);
        stInfraredDataInfo.RadiationRate = Module::dealDoublePrecision(stInfraredDataInfo.RadiationRate, 2);

        stInfraredDataInfo.TestDistance              = stParams.stObj.dblObjDistance;
        if(eUnit == SystemSet::TEMPERATURE_UNIT_CENTIGRADE)//摄氏度
        {
            stInfraredDataInfo.AtmosphericTemperature    = stParams.stObj.dblAtmTemp;
        }
        else//华氏度
        {
            //double dValue = Module::Centigrade2Fahrenheit(stParams.stObj.dblAtmTemp);
            stInfraredDataInfo.AtmosphericTemperature    = stParams.stObj.dblAtmTemp;
        }
        if(eUnit == SystemSet::TEMPERATURE_UNIT_CENTIGRADE)//摄氏度
        {
            stInfraredDataInfo.ReflectedTemperature    = stParams.stObj.dblAmbTemp;
            stInfraredDataInfo.ExternalOpticalTemperatue = stParams.stObj.dblExtOptTemp;
        }
        else//华氏度
        {
            //double dValue = Module::Centigrade2Fahrenheit(stParams.stObj.dblAmbTemp);
            stInfraredDataInfo.ReflectedTemperature    = stParams.stObj.dblAmbTemp;

            //dValue = Module::Centigrade2Fahrenheit(stParams.stObj.dblExtOptTemp);
            stInfraredDataInfo.ExternalOpticalTemperatue    = stParams.stObj.dblExtOptTemp;
        }

        stInfraredDataInfo.ExternalOpticalRate       = stParams.stObj.dblExtOptTransm;
        stInfraredDataInfo.RelativeHumidity          = stParams.stObj.dblRelHum;

        stInfraredDataInfo.TauPlanckConstiR   = stParams.stPlanck.iR;//todo
        stInfraredDataInfo.TauPlanckConstdblB = stParams.stPlanck.dblB;
        stInfraredDataInfo.TauPlanckConstdblF = stParams.stPlanck.dblF;
        stInfraredDataInfo.TauPlanckConstdblO = stParams.stPlanck.dblO;

        stInfraredDataInfo.SpectralResponsedblX      = stParams.stSpec.dblX;
        stInfraredDataInfo.SpectralResponsedblAlpha1 = stParams.stSpec.dblAlpha1;
        stInfraredDataInfo.SpectralResponsedblAlpha2 = stParams.stSpec.dblAlpha2;
        stInfraredDataInfo.SpectralResponsedblBeta1  = stParams.stSpec.dblBeta1;
        stInfraredDataInfo.SpectralResponsedblBeta2  = stParams.stSpec.dblBeta2;

        stInfraredDataInfo.iColorType = (quint8)m_pInfraredImagingView->getColorType();

        bool bSaveFailed = true;
        do
        {
            PDATask *pTask = PDAService::instance()->currentTask();
            if(!pTask)
            {
                logWarning("pTask == NULL");
                break;
            }

            TaskInfo stTaskInfo = pTask->taskInfo();
            DataMapHead stHeadInfo;
            stHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_UNUSED;
            stHeadInfo.generationDateTime = stInfraredDataInfo.dateTime;
            stHeadInfo.strSubstationName = m_stTestPointInfo.strStationName;
            stHeadInfo.strSubstationNumber = m_stTestPointInfo.strStationID;
            stHeadInfo.eWeather = static_cast<DataFileNS::Weather>(stTaskInfo.stWeatherInfo.getWeatherDataSave());
            stHeadInfo.fTemperature = stTaskInfo.stWeatherInfo.dTemperature;
            stHeadInfo.ucHumidity = stTaskInfo.stWeatherInfo.dHumidity;
            stHeadInfo.strDeviceName = m_stTestPointInfo.strDeviceName;
            stHeadInfo.strDeviceNumber = m_stTestPointInfo.strDeviceID;
            stHeadInfo.strTestPointName = m_stTestPointInfo.strTestPointName;
            stHeadInfo.strTestPointNumber = m_stTestPointInfo.strTestPointID;
            stHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_UNRECORD;

            showWaitingDialog(QObject::trUtf8("Saving data ..."));

            QFuture<InfraredSaveInfo> future = QtConcurrent::run(pfnSaveDataFun, this, pTask, stInfraredDataInfo, stHeadInfo);
            QFutureWatcher<InfraredSaveInfo> watcher;
            watcher.setFuture(future);

            QEventLoop loop;
            connect(&watcher, SIGNAL(resultReadyAt(int)), &loop, SLOT(quit()));
            loop.exec();

            InfraredSaveInfo stIRSaveInfo = future.result();

            if(bSaveJpeg && !(stIRSaveInfo.qstrPicturePath.isEmpty()))
            {
                m_pInfraredImagingView->saveAsJpeg(stIRSaveInfo.qstrPicturePath);
                //emit sigSavePicture(stIRSaveInfo.qstrPicturePath);
            }

            emit sigSaveFinished();

            if(stIRSaveInfo.qstrDataFilePath.isEmpty())
            {
                m_bPdaWaiting = false;
                MsgBox *pMsgBox = new MsgBox(MsgBox::INFORMATION);
                pMsgBox->setInfo("", QObject::trUtf8("Save failure!"));
                rotateMsgBox(pMsgBox);
            }
            else
            {
                bSaveFailed = false;
                onExitInfrared();
                delayToClose();
            }

        }while(0);

        if(bSaveFailed)
        {
            resume();
        }
    }

    m_bSave = false;

    return;
}

/*************************************************
函数名： deleteData()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 删除数据
*************************************************************/
void InfraredPDAView::deleteData()
{
    QStringList nameFilters;
    nameFilters << INFRARED_FILE_NAME_SUFFIX;

    QString filePath = DATA_STORAGE_PATH + "/" + INFRARED_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if (!infoList.isEmpty())
    {
        RotateDeleteDataView *pView = new RotateDeleteDataView(filePath, INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_DELETE_DATA), nameFilters);
        QStringList lstSuffix;
        lstSuffix.append(JPG_FILE_NAME_SUFFIX);
        pView->setRelatedSuffix(INFRARED_FILE_NAME_SUFFIX, lstSuffix);
        QWidget *pFocusWidget = this->focusWidget();
        if ( pFocusWidget )
        {
            pFocusWidget->clearFocus();
        }
        pView->setFocus();
        pView->exec();
        if ( pFocusWidget )
        {
            pFocusWidget->setFocus();
        }
    }
    else
    {
        MsgBox *pMsgBox = new MsgBox(MsgBox::WARNING);
        pMsgBox->setInfo("", QObject::trUtf8("No file!"));
        rotateMsgBox(pMsgBox);
    }
    return;
}

/*************************************************
函数名： disableButtons()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 禁用部分按钮
*************************************************************/
void InfraredPDAView::disableButtons()
{
    QList<InfraredControlButton *> buttons = m_pButtonBar->buttonList();
    for (int i = 0, iSize = buttons.size(); i < iSize; ++i)
    {
        if (buttons[i]->id() != BUTTON_INFRARED_LOAD_DATA)
        {
            buttons[i]->setEnabled(false);
        }
    }
    return;
}

/*************************************************
函数名： enableButtons()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 使能部分按钮
*************************************************************/
void InfraredPDAView::enableButtons()
{
    QList<InfraredControlButton *> buttons = m_pButtonBar->buttonList();
    for (int i = 0, iSize = buttons.size(); i < iSize; ++i)
    {
        if ( buttons[i]->id() != BUTTON_INFRARED_LOAD_DATA )
        {
            buttons[i]->setEnabled(true);
        }
    }

    // disable some button when init...
    m_pButtonBar->buttons(BUTTON_INFRARED_DELETE_ALL_SHAPE)->setEnabled(false);
    return;
}

/*************************************************
函数名： showInitDialog()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 显示初始化对话框
*************************************************************/
void InfraredPDAView::showInitDialog()
{
    if (m_bInitFlag && m_pService->isIniting())
    {
        QString strInfo = QObject::trUtf8("Connecting to infrared device ...");
        MsgBox *pInitMsgBox = new MsgBox(MsgBox::INFORMATION);
        pInitMsgBox->setInfo("", strInfo, MsgBox::OK);

        RotateDialog *pInitingDialog = new RotateDialog(pInitMsgBox);
        pInitingDialog->setWindowModality(Qt::ApplicationModal);
        pInitingDialog->setAttribute(Qt::WA_DeleteOnClose);
        pInitingDialog->setFocusPolicy(Qt::StrongFocus);
        pInitingDialog->show();

        connect(m_pService, SIGNAL(sigInfraredInitResult(bool)), pInitingDialog, SLOT(accept()));
    }
    return;
}

/*************************************************
函数名： onInitSuccess()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 处理初始化成功结果
*************************************************************/
void InfraredPDAView::onInitSuccess()
{
    m_pService->startSample(m_iUserId); //初始化成功后自动开始采集

    Params stParams;
    memset(&stParams, 0, sizeof(Params));

    if (m_pService->readParameters(&stParams))
    {
        m_pInfraredImagingView->setGigeCameraParams(&stParams);
        enableButtons();
        resume();
    }
    else
    {
        MsgBox *pMsgBox = new MsgBox(MsgBox::INFORMATION);
        pMsgBox->setInfo("", QObject::trUtf8("Init params failed, re-enter!"), MsgBox::OK);
        if(MsgBox::OK == rotateMsgBox(pMsgBox))
        {
            onExitInfrared();
            close();
        }
    }

    return;
}

/*************************************************
函数名： onInitFail()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 处理初始化失败结果
*************************************************************/
void InfraredPDAView::onInitFail()
{
    MsgBox *pMsgBox = new MsgBox(MsgBox::WARNING);
    pMsgBox->setInfo("", QObject::trUtf8("Initialization failed!"), MsgBox::OK);
    rotateMsgBox(pMsgBox);
    return;
}

/*************************************************
功能： 响应退出红外检测信号
*************************************************************/
void InfraredPDAView::onExitInfrared()
{
    onStopSample();

    m_pService->stopSample(m_iUserId);

    if(m_iUserId != INVALID_USER)
    {
        m_pService->deleteUser(m_iUserId);
    }

    return;
}

void InfraredPDAView::addTestData()
{
    PDAServiceNS::TestPointType eType = PDAService::instance()->currentTask()->currentTestType()->eTestPointType;
    AddTestDataDialog *pDialog = new AddTestDataDialog(eType);
    connect(pDialog, SIGNAL(sigAddingTestDataChanged(struct_AddingTestData&)), this, SLOT(onAddTestData(struct_AddingTestData&)));
    //pDialog->show();

    RotateDialog *pRotateDialog = new RotateDialog(pDialog);
    pRotateDialog->setWindowModality(Qt::ApplicationModal);
    pRotateDialog->setAttribute(Qt::WA_DeleteOnClose);
    pRotateDialog->setFocusPolicy(Qt::StrongFocus);
    pRotateDialog->show();

    return;
}

//void InfraredPDAView::timerEvent(QTimerEvent* event)
//{
//    if(event->timerId() == m_iSaveTimerId)
//    {
//        onButtonPressed(BUTTON_INFRARED_SAVA_DATA);
//    }
//    return;
//}
