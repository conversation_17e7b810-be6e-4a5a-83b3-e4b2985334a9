/*
* Copyright (c) 2017.03，南京华乘电气科技有限公司
* All rights reserved.
*
* tevviewdefine.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年03月13日
* 摘要：TEVView模块宏定义、常量、结构、枚举等

* 当前版本：1.0
*/

#ifndef RECORDPLAYVIEWDEFINE_H
#define RECORDPLAYVIEWDEFINE_H

#include <QtGlobal>
#define RECORD_PLAY_TRANSLATE(str) qApp->translate(RecordPlayViewString::CONTEXT, (str))

namespace RecordPlayViewString
{
    const char* const CONTEXT = "RecordPlayView";  //translate上下文

    const char* const TEXT_PLAY = QT_TRANSLATE_NOOP_UTF8("RecordPlayView", "Play");
    const char* const TEXT_LAST = QT_TRANSLATE_NOOP_UTF8("RecordPlayView", "Previous");
    const char* const TEXT_NEXT = QT_TRANSLATE_NOOP_UTF8("RecordPlayView", "Next");
    const char* const TEXT_VOLUME = QT_TRANSLATE_NOOP_UTF8("RecordPlayView", "Volume");


    const char* const TEXT_RECORD = QT_TRANSLATE_NOOP("RecordPlayView", "Record");
    const char* const TEXT_CONTINUE = QT_TRANSLATE_NOOP("RecordPlayView", "Continue");
    const char* const TEXT_PAUSE = QT_TRANSLATE_NOOP("RecordPlayView", "Pause");
    const char* const TEXT_STOP = QT_TRANSLATE_NOOP("RecordPlayView", "Stop");


    const char* const TEXT_RECORD_TIMELEN = QT_TRANSLATE_NOOP_UTF8("RecordPlayView", "Record Duration");

    const char* const TEXT_RECORD_TIMELEN_UNIT = "min";
}

#endif // RECORDPLAYVIEWDEFINE_H
