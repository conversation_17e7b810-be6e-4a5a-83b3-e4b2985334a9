#include "aeflypdaview.h"
#include "ae/AEView.h"
#include "ae/AEViewConfig.h"
#include "controlButton/ControlButtonInfo.h"
#include "buttonBar/PushButtonBar.h"
#include "controlButton/CmdButton.h"
#include "controlButton/RadioButton.h"
#include "controlButton/SliderButton.h"
#include "window/Window.h"
#include "messageBox/msgbox.h"
#include "ae/AEConfig.h"
#include "ae/dataSave/AEFlightDataSave.h"
#include "appconfig.h"
#include "pda/pdaservice.h"
#include "pda/pdatask.h"
#include "peripheral/peripheralservice.h"
#include "recordplay/RecordPlayService.h"
#include "functionconfigmanager/funccfgsrvmanager.h"
#include "View.h"

const int INVALID_USER = -1;
const UINT8 MINUTE_PER_HOUR = 60;
const UINT8 SECOND_PER_MINUTE = 60;

typedef enum _AEFlyButton
{
    BUTTON_AE_SAMPLE = 0,//采样
    BUTTON_AE_GAIN,//增益
    BUTTON_AE_TRIGGER_VALUE, //触发值
    BUTTON_AE_OPEN_DOOR_TIME,//AE开门时间
    BUTTON_AE_SAVE_DATA,//保存数据
    BUTTON_AE_ADD_TEST_DATA, //新增测试项
    BUTTON_AE_RECORD,//录音
    BUTTON_MORE_CONFIG,//更多配置

    //more
    BUTTON_AE_TIME_INTERVAL,//时间间隔
    BUTTON_AE_CLOSE_DOOR_TIME,//AE关门时间
    BUTTON_AE_VOLUME,//设置音量
    BUTTON_AE_CHANNEL,//通道
    BUTTON_AE_FILTER,//带宽模式
    BUTTON_AE_RESTORE_DEFAULT,//恢复默认参数
}AEFlyButton;

//增益
const ButtonInfo::RadioValueConfig s_AEGainCfg =
{
    AE::TEXT_GAIN_OPTIONS, sizeof(AE::TEXT_GAIN_OPTIONS)/sizeof(char*)
};

//触发值
const ButtonInfo::RadioValueConfig s_AETriggerCfg =
{
    NULL, AE::TRIGGER_LEVEL_COUNT
};

//通道
const ButtonInfo::RadioValueConfig s_AEChannel =
{
    AE::TEXT_CHANNELS, sizeof(AE::TEXT_CHANNELS)/sizeof(char*)
};

//开门时间
const ButtonInfo::GroupSliderValueConfig s_OpenDoorTimeButtonInfo =
{
    AE::OPEN_DOOR_TIME, AE::OPEN_DOOR_TIME_COUNT
};

//关门时间
const ButtonInfo::GroupSliderValueConfig s_AECloseDoorButtonInfo =
{
    AE::CLOSE_DOOR_TIME, AE::CLOSE_DOOR_TIME_COUNT
};

//时间间隔
const ButtonInfo::GroupSliderValueConfig s_TimeIntervalButtonInfo =
{
    AE::TIME_INTERVAL_VALUE, AE::TIME_INTERVAL_COUNT
};

//带宽模式
const ButtonInfo::RadioValueConfig s_AEFilterCfg =
{
    AE::TEXT_FILTER_OPTIONS, sizeof(AE::TEXT_FILTER_OPTIONS)/sizeof(char*)
};

//控制按钮定义
const ButtonInfo::Info s_AEButtonInfo[] =
{
    { BUTTON_AE_SAMPLE, { ButtonInfo::COMMAND, AE::TEXT_SAMPLE, NULL, ":/images/sampleControl/sample.png", NULL } },//采样
    { BUTTON_AE_GAIN, { ButtonInfo::RADIO, AE::TEXT_GAIN, NULL, ":/images/sampleControl/gain.png", &s_AEGainCfg } },//增益
    { BUTTON_AE_TRIGGER_VALUE, { ButtonInfo::RADIO, AE::TEXT_TRIGGER_VALUE, NULL, ":/images/sampleControl/triggerValue.png", &s_AETriggerCfg } },//触发值
    { BUTTON_AE_OPEN_DOOR_TIME, { ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_OPEN_DOOR_TIME, AE::TEXT_US, ":/images/sampleControl/openDoorTime.png", &s_OpenDoorTimeButtonInfo } },//触发值
    { BUTTON_AE_SAVE_DATA, { ButtonInfo::COMMAND, AE::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL } },//保存数据
    { BUTTON_AE_ADD_TEST_DATA, { ButtonInfo::COMMAND, AE::TEXT_ADD, NULL, ":/images/sampleControl/icon.png", NULL } },//新增测试项
    { BUTTON_AE_RECORD, { ButtonInfo::COMMAND, AE::TEXT_START_RECORD, NULL, ":/images/sampleControl/icon.png", NULL } },//录音
    //{ BUTTON_AE_CLOSE_DOOR_TIME, { ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_CLOSE_DOOR_TIME, AE::TEXT_MS, ":/images/sampleControl/closeDoorTime.png", &s_AECloseDoorButtonInfo } },//关门时间
    { BUTTON_MORE_CONFIG, { ButtonInfo::COMMAND, AE::TEXT_MORE_CONFIG, NULL, ":/images/sampleControl/moreconfig.png", NULL } },//更多配置
};

//"更多.."按钮定义
const ButtonInfo::Info g_AETimeIntervalBtn = {BUTTON_AE_TIME_INTERVAL, {ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_TIME_INTERVAL, AE::TEXT_MS, ":/images/sampleControl/timeInterval.png", &s_TimeIntervalButtonInfo}};//时间间隔
const ButtonInfo::Info g_AECloseDoorBtn = {BUTTON_AE_CLOSE_DOOR_TIME, {ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_CLOSE_DOOR_TIME, AE::TEXT_MS, ":/images/sampleControl/closeDoorTime.png", &s_AECloseDoorButtonInfo}};//关门时间
const ButtonInfo::Info g_AEVolumeBtn = {BUTTON_AE_VOLUME, {ButtonInfo::FIXED_STEP_SLIDER, AE::TEXT_AE_VOLUME, NULL, "", &AE::g_VolumeCfg}};//音量
const ButtonInfo::Info g_AEChannelBtn = {BUTTON_AE_CHANNEL, {ButtonInfo::RADIO, AE::TEXT_CHANNEL, NULL, "", &s_AEChannel}};//通道
const ButtonInfo::Info g_AEFilterBtn = {BUTTON_AE_FILTER, {ButtonInfo::RADIO, AE::TEXT_FILTER, NULL, "", &s_AEFilterCfg}};//带宽模式;
const ButtonInfo::Info g_AERestoreDataBtn = {BUTTON_AE_RESTORE_DEFAULT, {ButtonInfo::COMMAND, AE::TEXT_RESTORE_DEFAULT, NULL, ":/images/sampleControl/restoreDefault.png", NULL}};//恢复默认


/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
AEFlyPDAView::AEFlyPDAView(const QString &strTitle, const ItemTestData &stTestDataInfo, QWidget *parent) :
    AEFlyViewBase( strTitle,parent)
{
    //PeripheralService::instance()->openAEPower();

    m_qsTestDataFilePath = stTestDataInfo.strFilePath;
    m_qstrAttachPath = stTestDataInfo.strAttachPath;
    m_stTestData = stTestDataInfo;

    //temply mask ae fly playback data
    //m_isTested = false;

    initBtnBarInfo();

    //初始化使用的数据
    initDatas();

    //新建图谱
    ChartWidget *pChart = createChart(parent);
    setChart( pChart );

    //触发值列表根据增益、量纲动态变化
    resetTriggerList( m_eGain );

    //设置数据
    setButtonBarDatas();

    setChartDatas();

    //设置工作参数
    setAllWorkingSet();

    //显示上次的测试数据
    if(m_stTestData.bTested)
    {
        buttonBar()->hide();
        playBackTestedData(m_qsTestDataFilePath);
        QTimer::singleShot(START_SAMPLE_TIME_INTERVAL_2000MS, this, SLOT(onRecoverSampleView()));
    }

    //设置音量
    RecordPlayService::instance()->setVolume(m_ucVolume);

    connect(RecordPlayService::instance(), SIGNAL(sigSysVolume(quint8)), this, SLOT(onSysVolume(quint8)));
}

/*************************************************
功能： 析构
*************************************************************/
AEFlyPDAView::~AEFlyPDAView( )
{
    saveConfig();//存储到配置文件中
    stopSample();

    if(m_pMoreBtnInfo)
    {
        delete [] m_pMoreBtnInfo;
        m_pMoreBtnInfo = NULL;
    }
}

/*****************************************
 * 功能：初始化按钮栏信息
 * **********************************************/
void AEFlyPDAView::initBtnBarInfo()
{
    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar(AE::CONTEXT, s_AEButtonInfo, sizeof(s_AEButtonInfo) / sizeof(ButtonInfo::Info));
    m_pSampleBtn = buttonBar()->button(BUTTON_AE_SAMPLE);

    m_pMoreBtnInfo = NULL;
    m_qui8MoreBtnCnt = 0;
    m_bChannelEnable = false;

    QVector<ButtonInfo::Info> qvtMoreBtnInfos;
    qvtMoreBtnInfos.clear();

    qvtMoreBtnInfos.push_back(g_AETimeIntervalBtn);
    qvtMoreBtnInfos.push_back(g_AECloseDoorBtn);
    qvtMoreBtnInfos.push_back(g_AEVolumeBtn);

    FuncConfigManagerNS::FunctionInfo stAEWirelessInfo;
    stAEWirelessInfo.iFuncID = FuncConfigManagerNS::AE_WIRELESS;
    stAEWirelessInfo.iParentID = FuncConfigManagerNS::AE;

    FuncConfigManagerNS::ConfigInfo stConfigInfo;
    FuncCfgSrvManager::instance()->getConfigInfo(stConfigInfo);
    int iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAEWirelessInfo);
    if(0 <= iIndex && iIndex < stConfigInfo.qvtFuncInfos.size())
    {
        if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
        {
            qvtMoreBtnInfos.push_back(g_AEChannelBtn);
            m_bChannelEnable = true;
        }
    }

#ifdef _R3_DEFINED_
    qvtMoreBtnInfos.push_back(g_AEFilterBtn);
#endif
    qvtMoreBtnInfos.push_back(g_AERestoreDataBtn);

    m_qui8MoreBtnCnt = static_cast<quint8>(qvtMoreBtnInfos.size());
    m_pMoreBtnInfo = new ButtonInfo::Info[m_qui8MoreBtnCnt];
    for (int i = 0; i < m_qui8MoreBtnCnt; ++i)
    {
        m_pMoreBtnInfo[i] = qvtMoreBtnInfos[i];
    }

    //创建更多设置栏
    createMoreConfigButtonBar(AE::CONTEXT, m_pMoreBtnInfo, m_qui8MoreBtnCnt);
}

/************************************************
 * 函数名   : onRecoverSampleView
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :槽函数，起动采样
 ************************************************/
void AEFlyPDAView::onRecoverSampleView()
{
    buttonBar()->show();
    buttonBar()->setFocus();
    m_pSampleBtn->setEnabled(true);
    m_pSampleBtn->setActive(true);
    //恢复当前的参数设置
    initParameters();
    setButtonBarDatas();
    setChartDatas();
    setAllWorkingSet();
    if(AEFlyService* pService = getAEFlyService())
    {
        pService->setChannel( m_eChannel );
    }
    m_pChart->clear();
}

/*************************************************
功能： 槽，响应系统音量变化
输入参数：
        qui8Volume -- 系统音量
*************************************************************/
void AEFlyPDAView::onSysVolume(quint8 qui8Volume)
{
    if(m_ucVolume != qui8Volume)
    {
        m_ucVolume = qui8Volume;
        ((PopupButton*)(buttonBar()->button(BUTTON_AE_VOLUME)))->setValue(m_ucVolume);
    }
}

/************************************************
 * 函数名   : playBackTestedData
 * 输入参数 : qsFile---数据文件存放路径
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 回放数据文件里的测试数据
 ************************************************/
void AEFlyPDAView::playBackTestedData(const QString &qsFile)
{
    AEFlightDataSave  fDataSave;
    AEFlightDataInfo  stPlayBackDataInfo; // 存放回放数据的结构体
    int value = fDataSave.getDataFromFile( qsFile,&stPlayBackDataInfo );

    if( HC_FAILURE == value )
    {
        logError(QString("playback file: %1 failed.").arg(qsFile).toLatin1().data());
        return;
    }
    m_pChart->setGain( stPlayBackDataInfo.gain() );
    m_pChart->setTrigger( stPlayBackDataInfo.fTrigThrhd );
    m_pChart->setTimeInterval( stPlayBackDataInfo.usMaxTimeInterval );
    m_pChart->setChannel(m_eChannel);
    m_pChart->setChannelValid( true );

    m_pChart->setRunningIconValid( false );
    m_pChart->setPulseCountingValid( false );

    //因为该图谱为累积显示，故新添加数据应当清除之前显示的部分
    m_pChart->clear();

    /*载入数据*/
    for(int i = 0; i < AEPULSEMAXNUM; ++i)
    {
        m_pChart->addSamples(stPlayBackDataInfo.stAEFlightData[i].fInterval * 1000.0 + 0.5f, stPlayBackDataInfo.stAEFlightData[i].fPeakValue);
    }
}

/************************************************
 * 函数名   : setTestPointInfo
 * 输入参数 : stTestPointInfo---巡检测点相关信息
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 设置显示测点信息
 ************************************************/
void AEFlyPDAView::setTestPointInfo(const View::PatrolTestPointInfo &stTestPointInfo)
{
    m_stTestPointInfo = stTestPointInfo;
    m_qsStation = stTestPointInfo.strStationName;
    m_qsDevice = stTestPointInfo.strDeviceName;

    QString qstrInfo = VIEW_TRANSLATE(View::TEXT_STATION_NAME) + m_qsStation;
    m_pStationLabel->setText(getRightEllipsisInfo(m_pStationLabel->font(), width(), qstrInfo));

    qstrInfo = VIEW_TRANSLATE(View::TEXT_DEVICE) + m_qsDevice;
    m_pDeviceLabel->setText(getRightEllipsisInfo(m_pDeviceLabel->font(), width(), qstrInfo));

    qstrInfo = VIEW_TRANSLATE(View::TEXT_TESTPOINT) + stTestPointInfo.strTestPointName;
    m_pTestPointLabel->setText(getRightEllipsisInfo(m_pTestPointLabel->font(), width(), qstrInfo));
}

/*************************************************
功能： 初始化参数
*************************************************************/
void AEFlyPDAView::initParameters()
{
    int iGroup = AE::GROUP_AE_FLY;
    m_pConfig->beginGroup( Module::GROUP_AE );

    m_eGain = (AE::GainType)m_pConfig->value( AE::KEY_GAIN ).toUInt();
    m_eFilter = (AEFilter) m_pConfig->value(AE::KEY_FILTER).toUInt();
    m_eTriggerValue = (AE::TriggerValue)m_pConfig->value( AE::KEY_TRIGGER_VALUE, iGroup ).toUInt();
    m_iTriggerValue = AE::getThreshold( AE::UNIT_DEFAULT,m_eGain,m_eTriggerValue );

    m_usOpenDoorTime = m_pConfig->value( AE::KEY_OPEN_DOOR_TIME, iGroup ).toUInt();
    m_usCloseDoorTime = m_pConfig->value( AE::KEY_CLOSE_DOOR_TIME, iGroup ).toUInt();
    m_iTimeInterval = m_pConfig->value( AE::KEY_TIME_INTERVAL, iGroup ).toUInt();
    if(m_eChannel != AE::SURFACE_MOUNT)
    {
        m_eChannel = m_bChannelEnable ? ((AE::ChannelType)m_pConfig->value(AE::KEY_CHANNEL_TYPE).toUInt()) : AE::CHANNEL_DEFAULT;
    }
    m_pConfig->endGroup();

    m_pConfig->beginGroup( Module::GROUP_APP );
    m_ucVolume = m_pConfig->value(APPConfig::KEY_SYSTEM_VOLUME).toUInt();
    m_pConfig->endGroup();

    m_usPulseCnt = 0;
}

ChartWidget *AEFlyPDAView::createChart(QWidget *parent)
{
    Q_UNUSED(parent)
    dbg_info("");
    ChartWidget *pWidget = new ChartWidget;
    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->setContentsMargins(0, 0, 0, 0);

    //站名
    m_pStationLabel = new QLabel;
    //设备名
    m_pDeviceLabel = new QLabel;
    //测点名
    m_pTestPointLabel = new QLabel;

    //设置style
    QFont font = m_pStationLabel->font();
    font.setPointSize(20);
    m_pStationLabel->setFont(font);
    m_pDeviceLabel->setFont(font);
    m_pTestPointLabel->setFont(font);

    //图谱
    m_pChart = new AEFlyChart;

    vLayout->addWidget(m_pStationLabel);
    vLayout->addWidget(m_pDeviceLabel);
    vLayout->addWidget(m_pTestPointLabel);
    vLayout->addWidget(m_pChart);
    pWidget->setLayout(vLayout);
    pWidget->setFixedWidth(Window::WIDTH);

    return pWidget;
}

/*************************************************
功能： 初始化图谱
*************************************************************/
void AEFlyPDAView::initChart()
{
    /*暂停图谱刷新*/
    m_pChart->setRunningMode(isSampling());
    /*清空计数值*/
    m_usPulseCnt = 0;
    m_pChart->setPulseCounting(m_usPulseCnt);
    /*清空图谱*/
    m_pChart->clear();
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void AEFlyPDAView::onButtonValueChanged( int id, int iValue )
{
    bool bIsConfigChanged = true;
    switch( id )
    {
    case BUTTON_AE_GAIN://增益
    {
        if( m_eGain != (AE::GainType)iValue )
        {
            m_eGain = (AE::GainType)iValue;
            if(AEFlyService* pService = getAEFlyService())
            {
                pService->setGain( m_eGain );
            }
            m_pChart->setGain( m_eGain );

            resetTriggerList( m_eGain );//增益变化时，重置触发值列表
            resetTriggerValue();
        }
        else
        {
            // not changed
        }

    }
        break;
    case BUTTON_AE_TRIGGER_VALUE://触发值
    {
        if( m_eTriggerValue != (AE::TriggerValue)iValue )
        {
            m_eTriggerValue = (AE::TriggerValue)iValue;
            resetTriggerValue();
        }
    }
        break;
    case BUTTON_AE_VOLUME://音量
    {
        if((int)m_ucVolume != iValue)
        {
            m_ucVolume = (quint8)iValue;
            RecordPlayService::instance()->setVolume(m_ucVolume);

            ConfigInstance *pConfig = ConfigManager::instance()->config();
            pConfig->beginGroup(Module::GROUP_APP);
            pConfig->setValue(iValue, APPConfig::KEY_SYSTEM_VOLUME);
            pConfig->endGroup();
        }
    }
        break;
    case BUTTON_AE_OPEN_DOOR_TIME://开门时间
    {
        if( m_usOpenDoorTime != iValue )
        {
            m_usOpenDoorTime = iValue;
            if(AEFlyService* pService = getAEFlyService())
            {
                pService->setOpenDoorTime( m_usOpenDoorTime );
            }
        }
    }
        break;
    case BUTTON_AE_CLOSE_DOOR_TIME://关门时间
    {
        if( m_usCloseDoorTime != iValue )
        {
            m_usCloseDoorTime = iValue;
            if(AEFlyService* pService = getAEFlyService())
            {
                pService->setCloseDoorTime( AE::MODE_FLY,m_usCloseDoorTime );
            }
        }
    }
        break;
    case BUTTON_AE_TIME_INTERVAL://时间间隔
    {
        if( m_iTimeInterval != iValue )
        {
            m_iTimeInterval = iValue;
            m_pChart->setTimeInterval( m_iTimeInterval );
            if(AEFlyService* pService = getAEFlyService())
            {
                pService->setTimeInterval( m_iTimeInterval );
            }
        }
    }
        break;
    case BUTTON_AE_CHANNEL://通道
    {
        stopSampleService();

        if(AEFlyService* pService = getAEFlyService())
        {
            if( iValue == 1)
            {
                pService->setChannel( AE::WIRELESS );
                m_eChannel = AE::WIRELESS;
                m_pChart->setChannel( m_eChannel );
            }
            else
            {
                pService->setChannel( AE::AIR_SOUND );
                m_eChannel = AE::AIR_SOUND;
                m_pChart->setChannel( m_eChannel );
            }
        }

        startSampleService();
    }
        break;
    case BUTTON_AE_FILTER:
    {
        m_eFilter = (AEFilter)iValue;
        if(AEFlyService* pService = getAEFlyService())
        {
            pService->setFilter(m_eFilter);
        }
    }
        break;
    default:
    {
        bIsConfigChanged = false;
    }
        return;
    }
    if( bIsConfigChanged )
    {
        stopSample();
        initChart();
    }
}

/*************************************************
功能： 弹出新增测试项窗口
*************************************************************/
void AEFlyPDAView::addTestData()
{
    PDAServiceNS::TestPointType eType = PDAService::instance()->currentTask()->currentTestType()->eTestPointType;
    AddTestDataDialog *pDialog = new AddTestDataDialog(eType);
    pDialog->show();
    connect(pDialog, SIGNAL(sigAddingTestDataChanged(struct_AddingTestData&)), this, SLOT(onAddTestData(struct_AddingTestData&)));
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void AEFlyPDAView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case BUTTON_MORE_CONFIG://更多设置
    {
        showMoreConfigButtonBar();
    }
        break;
    case BUTTON_AE_ADD_TEST_DATA://新增测试项
    {
        addTestData();
    }
        break;
    case BUTTON_AE_SAMPLE://采样
    {
        if(!isSampling()) //图谱刷新暂停后，点击采样，处理；否则不处理
        {
            initChart();
            startSample();
        }
        else
        {
            stopSample();
        }
    }
        break;
    case BUTTON_AE_SAVE_DATA://保存数据
    {
        saveTestData();
    }
        break;
    case BUTTON_AE_RESTORE_DEFAULT://恢复默认参数
    {
        restoreDefault();
    }
        break;
    case BUTTON_AE_RECORD://录音
    {
        if(m_bPdaWaiting)
        {
            logInfo("saving data...");
            return;
        }

        if(m_bRecording)
        {
            //本次为停止录音
            if(stopAERecord())
            {
                this->buttonBar()->button(BUTTON_AE_RECORD)->setTitle(AE_TRANSLATE(AE::TEXT_START_RECORD));
                saveTestData();
            }
        }
        else
        {
            //本次为开始录音
            if(startAERecord())
            {
                //this->buttonBar()->button(BUTTON_AE_RECORD)->setTitle(AE_TRANSLATE(AE::TEXT_STOP_RECORD));
                m_uiTotalRecordingTime = 0;
            }
        }
    }
        break;
    default:
        break;
    }
}

/*************************************************
功能： 保存数据
输入参数：
        stationName -- 变电站名
        deviceName -- 设备名
返回：
        保存结果
*************************************************************/
bool AEFlyPDAView::savePDAData(const QString &stationName, const QString& deviceName )
{
    Q_UNUSED(stationName);
    Q_UNUSED(deviceName);
    AEFlightDataInfo sSaveDataInfo;

    PDAService *pPDAService = PDAService::instance();
    PDATask *pTask = pPDAService->currentTask();
    APP_CHECK_RETURN_VAL(pTask, false);

    TaskInfo stTaskInfo = pTask->taskInfo();

    sSaveDataInfo.stHeadInfo.generationDateTime = QDateTime::currentDateTime();
    sSaveDataInfo.stHeadInfo.strSubstationName = m_stTestPointInfo.strStationName;
    sSaveDataInfo.stHeadInfo.strSubstationNumber = m_stTestPointInfo.strStationID;
    sSaveDataInfo.stHeadInfo.eWeather = static_cast<DataFileNS::Weather>(stTaskInfo.stWeatherInfo.getWeatherDataSave());
    sSaveDataInfo.stHeadInfo.fTemperature = stTaskInfo.stWeatherInfo.dTemperature;
    sSaveDataInfo.stHeadInfo.ucHumidity = stTaskInfo.stWeatherInfo.dHumidity;
    sSaveDataInfo.stHeadInfo.strDeviceName = m_stTestPointInfo.strDeviceName;
    sSaveDataInfo.stHeadInfo.strDeviceNumber = m_stTestPointInfo.strDeviceID;
    sSaveDataInfo.stHeadInfo.strTestPointName = m_stTestPointInfo.strTestPointName;
    sSaveDataInfo.stHeadInfo.strTestPointNumber = m_stTestPointInfo.strTestPointID;
    sSaveDataInfo.stHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_UNRECORD;
    sSaveDataInfo.stHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;
    sSaveDataInfo.setUnit( AE::UNIT_DEFAULT );
    sSaveDataInfo.ePulseUnit = AEMapNS::PULSE_MIL_SECOND;
    sSaveDataInfo.stHeadInfo.ucTestChannelSign = /*m_eChannel*/(m_eChannel == AE::WIRELESS) ? 1 : 0;
    sSaveDataInfo.eTransformerType = AE::transformChannelToDataFile(m_eChannel);

    sSaveDataInfo.eGainType =  DataFileNS::GAIN_TYPE_AMPLIFY_RATIO;
    sSaveDataInfo.sGainValue =  AE::g_ausGainValues[m_eGain];

    sSaveDataInfo.fTrigThrhd = m_iTriggerValue;
    sSaveDataInfo.usMaxTimeInterval = m_iTimeInterval;
    sSaveDataInfo.usGatingTime = m_usOpenDoorTime;
    sSaveDataInfo.usShutTime = m_usCloseDoorTime;
    sSaveDataInfo.fSyncFreq = -1;
    sSaveDataInfo.ucSyncState = 0;
    QList<AEAbstractChart::AbstractData> vChartDatas = m_pChart->samples();
    sSaveDataInfo.iDataPointNum = vChartDatas.size();
    AEFlightData sRawData[AEPULSEMAXNUM];
    memset( &sRawData,0,AEPULSEMAXNUM*sizeof(AEFlightData) );

    for(int i = 0, iSize = vChartDatas.size(); i < iSize; ++i)
    {
        sRawData[i].fInterval = vChartDatas.at( i ).fXscale * m_iTimeInterval;
        sRawData[i].fPeakValue = vChartDatas.at( i ).fYscale * AE::Y_RANGE_VALUES[m_eGain];
        if(sRawData[i].fPeakValue >= AE::Y_RANGE_VALUES[m_eGain])
        {
            sRawData[i].fPeakValue = AE::Y_RANGE_VALUES[m_eGain];
        }
    }
    memcpy(sSaveDataInfo.stAEFlightData, sRawData, sizeof(sRawData));

    //存放图谱数据点颜色
    QList<QColor> lDataColors = m_pChart->sampleColors();
    quint8 ucaDataColor[AEPULSEMAXNUM * 3];
    memset( &ucaDataColor,0,AEPULSEMAXNUM*3*sizeof(quint8) );

    for(int i = 0, iSize = vChartDatas.size(); i < iSize; ++i)
    {
        ucaDataColor[3 * i] = lDataColors.at(i).red();
        ucaDataColor[3 * i + 1] = lDataColors.at(i).green();
        ucaDataColor[3 * i + 2] = lDataColors.at(i).blue();
    }
    memcpy(sSaveDataInfo.ucaAEFlyDataColor, ucaDataColor, vChartDatas.size() * 3 * sizeof(quint8));

    //存放图谱数据放电次数
    QList<int> lColorIndex = m_pChart->sampleColorIndex();
    quint16 usaColorIndex[AEPULSEMAXNUM];
    memset( &usaColorIndex,0,AEPULSEMAXNUM*sizeof(quint16) );

    for(int i = 0, iSize = lColorIndex.size(); i < iSize; ++i)
    {
        usaColorIndex[i] = lColorIndex.at(i);
    }
    memcpy(sSaveDataInfo.usaAEFlyColorIndex, usaColorIndex, lColorIndex.size() * sizeof(quint16));

    return pTask->saveAEFlyData(sSaveDataInfo, m_qstrAttachPath);
}

/*************************************************
函数名： saveTestData
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 测试数据传给pdaservice保存
*************************************************************/
void AEFlyPDAView::saveTestData()
{
    if(m_bPdaWaiting)
    {
        logInfo("saving data...");
        return;
    }
    m_bPdaWaiting = true;

    //停止采集
    stopSample();

    if(m_bRecording)
    {
        //停止录音
        if(stopAERecord())
        {
            this->buttonBar()->button(BUTTON_AE_RECORD)->setTitle(AE_TRANSLATE(AE::TEXT_START_RECORD));
        }
        else
        {
            //录音未停止，不保存数据
            logWarning("waiting for stop ae record.");
            m_bPdaWaiting = false;
            startSample();
            return;
        }
    }

    if(savePDAData( m_qsStation, m_qsDevice ))
    {
        delayToClose();
    }
    else
    {
        m_bPdaWaiting = false;
        startSample();

        PushButtonBar* pBtnBar = buttonBar();
        QPoint centerPoint;
        centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
        centerPoint.setY(pBtnBar->pos().y()+pBtnBar->height()/2);

        MsgBox::information("", QObject::trUtf8("Save failure!"), centerPoint);
    }
}

/*************************************************
功能： 开始采集
*************************************************************/
void AEFlyPDAView::startSample()
{
    if(!isSampling())
    {
        startSampleService();

        //回放时不显示脉冲计数 运行图标等，采样时显示这些
        m_pChart->setChannelValid(true);
        m_pChart->setRunningIconValid(true);
        m_pChart->setPulseCountingValid(true);
        m_pChart->setRunningMode(true);

        m_pSampleBtn->setTitle(QObject::trUtf8("Stop"));

        if(!(buttonBar()->button(BUTTON_AE_SAVE_DATA)->isEnabled()))
        {
            //开启采样之后，使能保存按钮
            buttonBar()->button(BUTTON_AE_SAVE_DATA)->setEnabled(true);
        }
    }
}

/*************************************************
功能： 停止采集
*************************************************************/
void AEFlyPDAView::stopSample()
{
    if(isSampling())
    {
        stopSampleService();
        m_pChart->setRunningMode(false);
        m_pSampleBtn->setTitle(QObject::trUtf8("Sample"));
    }
}

/*************************************************
功能： 重新设置触发值，包括提供给service和chart两部分
*************************************************************/
void AEFlyPDAView::resetTriggerValue()
{
    if(AEFlyService* pService = getAEFlyService())
    {
        pService->setTriggerValue( m_eTriggerValue );
    }
    m_iTriggerValue = AE::getThreshold( AE::UNIT_DEFAULT,m_eGain,m_eTriggerValue );
    m_pChart->setTrigger( m_iTriggerValue );
}

/*************************************************
功能： 槽，响应读取的数据
输入参数：
        data -- 飞行数据
*************************************************************/
void AEFlyPDAView::onDataRead(QVector<AE::FlyData> datas,MultiServiceNS::USERID userId)
{
    // 因为停止采样的操作是异步的，故有可能下发采集命令后仍有数据上传
    if((getUserId() != INVALID_USER) && (getUserId() == userId) && isSampling())
    {
        for(int i = 0, iSize = datas.size(); i < iSize; ++i)
        {
            UINT32 uiPulseInterval = datas.at(i).uiPulseInterval;  // 脉冲间隔时间 us
            float fPeakValue = datas.at(i).fPeakValue;
            if( fPeakValue  > m_iTriggerValue )
            {
                // 脉冲计数值超出累积上限，清零计数，暂停采集
                if( m_usPulseCnt >= PULSE_COUNT_MAX )
                {
                    stopSample();

                    m_usPulseCnt = PULSE_COUNT_MAX;
                    m_pChart->setPulseCounting(m_usPulseCnt);
                    break;
                }
                else
                {
                    if( m_pChart->addSamples( uiPulseInterval,fPeakValue ) )
                    {
                        ++m_usPulseCnt;
                        m_pChart->setPulseCounting(m_usPulseCnt);
                    }
                    m_pChart->setRunningMode(isSampling());
                }
            }

        }
    }
    else
    {
        //illegal
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
    }
}
/*************************************************
功能： 槽，响应通道变化
输入参数：
        eChannel -- 变化后的通道
*************************************************************/
void AEFlyPDAView::onChannelChanged( AE::ChannelType eChannel )
{
    if( m_eChannel != eChannel )
    {
        m_eChannel = eChannel;
        m_pChart->setChannel( eChannel );
    }
}

/*************************************************
功能： 初始化数据
*************************************************************/
void AEFlyPDAView::initDatas()
{
    m_pChart = NULL;
    m_pConfig = ConfigManager::instance()->config();

    MultiServiceNS::SampleUser user;
    user.eSampleType = MultiServiceNS::SPECTRUM_AE_FLY;
    addUser( user );

    if(!(m_stTestData.bTested))
    {
        initParameters();
    }
    else
    {
        initParametersByDataFile(m_qsTestDataFilePath);
    }

    if (AEFlyService* pService = getAEFlyService())
    {
        pService->setTimeInterval(m_iTimeInterval);
    }
}

/************************************************
 * 函数名   : initParametersByDataFile
 * 输入参数 : qsFile---数据文件存放路径
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :根据数据文件里的数据初始化采样参数
 ************************************************/
void AEFlyPDAView::initParametersByDataFile(const QString &qsFile)
{
    AEFlightDataSave  fDataSave;
    AEFlightDataInfo  stPlayBackDataInfo;
    int value = fDataSave.getDataFromFile( qsFile,&stPlayBackDataInfo );

    if( HC_FAILURE == value )
    {
        initParameters();
        logError(QString("get param from file: %1 failed.").arg(qsFile).toLatin1().data());
        return;
    }

    /*
        数据文件里存在的采样参数，从数据文件读
        数据文件不存在的采样参数，则仍然从配置文件读
    */
    m_eGain = AE::gainValEnum2Type(stPlayBackDataInfo.eGainValue);

    int iGroup = AE::GROUP_AE_FLY;
    m_pConfig->beginGroup( Module::GROUP_AE );
    m_eTriggerValue = (AE::TriggerValue)m_pConfig->value( AE::KEY_TRIGGER_VALUE, iGroup ).toUInt();
    m_pConfig->endGroup();

    m_pConfig->beginGroup( Module::GROUP_APP );
    m_ucVolume = m_pConfig->value(APPConfig::KEY_SYSTEM_VOLUME).toUInt();
    m_pConfig->endGroup();

    m_iTriggerValue = AE::getThreshold( AE::UNIT_DEFAULT,m_eGain,m_eTriggerValue );
    m_iTimeInterval = stPlayBackDataInfo.usMaxTimeInterval;
    m_usOpenDoorTime = stPlayBackDataInfo.usGatingTime;
    m_usCloseDoorTime = stPlayBackDataInfo.usShutTime;
    //m_eChannel = (AE::ChannelType) stPlayBackDataInfo.stHeadInfo.ucTestChannelSign;
    m_eChannel = AE::transformChannelFromDataFile((AEMapNS::AETransformerType)stPlayBackDataInfo.eTransformerType);
    m_usPulseCnt = 0;
    return;
}

/*************************************************
功能： 设置按钮栏数据
*************************************************************/
void AEFlyPDAView::setButtonBarDatas()
{
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_OPEN_DOOR_TIME)))->setValue( m_usOpenDoorTime );
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_GAIN)))->setValue( m_eGain );
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_CLOSE_DOOR_TIME)))->setValue( m_usCloseDoorTime );
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_TIME_INTERVAL)))->setValue( m_iTimeInterval );
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_TRIGGER_VALUE)))->setValue( m_eTriggerValue );

    if(((PopupButton*)(buttonBar()->button(BUTTON_AE_FILTER))))
    {
        ((PopupButton*)(buttonBar()->button(BUTTON_AE_FILTER)))->setValue(m_eFilter);
    }

    int iChannelVal = (AE::WIRELESS == m_eChannel) ? (AE::WIRELESS - 1) : AE::AIR_SOUND;
    if(((PopupButton*)(buttonBar()->button(BUTTON_AE_CHANNEL))))
    {
        ((PopupButton*)(buttonBar()->button(BUTTON_AE_CHANNEL)))->setValue(iChannelVal);
    }

    ((PopupButton*)(buttonBar()->button(BUTTON_AE_VOLUME)))->setValue(m_ucVolume);

    return;
}

/*************************************************
功能： 设置表格数据
*************************************************************/
void AEFlyPDAView::setChartDatas()
{
    m_pChart->setGain( m_eGain );
    m_pChart->setTimeInterval( m_iTimeInterval );
    m_pChart->setChannel( m_eChannel );
    m_pChart->setPulseCounting( m_usPulseCnt );
    m_pChart->setRunningMode( isSampling() );
    m_pChart->setTrigger( m_iTriggerValue );
}

/*************************************************
功能： 恢复默认
*************************************************************/
void AEFlyPDAView::restoreDefault()
{
    if(MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to restore default settings?")))
    {
        stopSample();
        m_pChart->clear();

        int iGroup = AE::GROUP_AE_FLY;
        m_pConfig->beginGroup( Module::GROUP_AE );
        QVector<Config::GroupKey> totalKeys;
        totalKeys << Config::GroupKey( AE::KEY_GAIN );
        totalKeys << Config::GroupKey( AE::KEY_TRIGGER_VALUE, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_OPEN_DOOR_TIME, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_CLOSE_DOOR_TIME, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_TIME_INTERVAL, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_CHANNEL_TYPE );

        m_pConfig->restoreDefault( totalKeys );
        m_pConfig->endGroup();

        m_pConfig->beginGroup(Module::GROUP_APP);
        QVector<Config::GroupKey> qvtAppDefaultKeys;
        qvtAppDefaultKeys << Config::GroupKey(APPConfig::KEY_SYSTEM_VOLUME);
        m_pConfig->restoreDefault(qvtAppDefaultKeys);
        m_pConfig->endGroup();

        //重新初始化数据
        initParameters();
        if(AEFlyService* pService = getAEFlyService())
        {
            pService->setTimeInterval( m_iTimeInterval );
        }

        resetTriggerList( m_eGain );
        //设置数据
        setButtonBarDatas();
        setChartDatas();

        //设置工作参数
        setAllWorkingSet();

        //设置音量
        RecordPlayService::instance()->setVolume(m_ucVolume);
    }
}

/*************************************************
功能： 保存设置
*************************************************************/
bool AEFlyPDAView::saveConfig()
{
    int iGroup = AE::GROUP_AE_FLY;
    m_pConfig->beginGroup( Module::GROUP_AE );

    m_pConfig->setValue( m_eGain, AE::KEY_GAIN );
    m_pConfig->setValue( m_eFilter, AE::KEY_FILTER );
    m_pConfig->setValue( m_eTriggerValue, AE::KEY_TRIGGER_VALUE, iGroup );
    m_pConfig->setValue( m_usOpenDoorTime, AE::KEY_OPEN_DOOR_TIME, iGroup );
    m_pConfig->setValue( m_usCloseDoorTime, AE::KEY_CLOSE_DOOR_TIME, iGroup );
    m_pConfig->setValue( m_iTimeInterval, AE::KEY_TIME_INTERVAL, iGroup );
    if(m_eChannel != AE::SURFACE_MOUNT)
    {
        m_pConfig->setValue( m_eChannel, AE::KEY_CHANNEL_TYPE );
    }
    m_pConfig->endGroup();

    return true;
}

/*************************************************
功能： 触发值按钮的触发值列表根据增益、量纲动态变化
输入参数：
        eGain -- 增益
*************************************************************/
void AEFlyPDAView::resetTriggerList( AE::GainType eGain )
{
    QStringList listTrigger = AE::getTriggerList( AE::UNIT_DEFAULT, eGain );

    RadioButton* pButTrigger = (RadioButton*)( this->buttonBar()->button(BUTTON_AE_TRIGGER_VALUE) );
    if( NULL != pButTrigger )
    {
        pButTrigger->setOptionList( listTrigger );
    }
    else
    {
        qWarning() << "AEFlyPDAView::resetTriggerList: error, button is NULL!";
    }
}

/*************************************************
功能： 设置所有工作参数
*************************************************************/
void AEFlyPDAView::setAllWorkingSet()
{
    if (AEFlyService* pService = getAEFlyService())
    {
        pService->transaction();
        pService->setWorkMode( AE::MODE_FLY );
        pService->setGain( m_eGain );
        pService->setFilter(m_eFilter);
        pService->setTriggerValue( m_eTriggerValue );
        pService->setCloseDoorTime( AE::MODE_FLY,m_usCloseDoorTime );
        pService->setOpenDoorTime( m_usOpenDoorTime );
        pService->setUnit( AE::UNIT_DEFAULT );
        pService->setSyncSource( Module::SYNC_SOURCE_DEFAULT );
        pService->commit();

        pService->setChannel(m_eChannel);
        pService->setTimeInterval(m_iTimeInterval);
    }
    else
    {
        qWarning() << "AEFlyPDAView::setWorkingSet: error, service handle is NULL!";
    }
}

/*************************************************
功能： 响应S键事件
输入参数：
        id -- 按钮ID
*************************************************************/
void AEFlyPDAView::onSKeyPressed()
{
    saveTestData();
}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void AEFlyPDAView::keyPressEvent(QKeyEvent *event)
{
    if( event->key() == Qt::Key_Escape )
    {
        if(isSampling())
        {
            stopSample();
        }
        else
        {
            SampleChartView::keyPressEvent( event );
        }
    }
    else
    {
        SampleChartView::keyPressEvent( event );
    }
}

void AEFlyPDAView::showEvent(QShowEvent *event)
{
    Q_UNUSED(event);

    if(m_stTestData.bTested)
    {
        //回放数据时不允许点击保存按钮，开始采样之后才使能
        buttonBar()->button(BUTTON_AE_SAVE_DATA)->setEnabled(false);
        buttonBar()->button(BUTTON_AE_SAVE_DATA)->setActive(false);
        m_pSampleBtn->setEnabled(false);
        m_pSampleBtn->setActive(false);
    }

    if(m_stTestData.bIsBgn)
    {
        //背景测试中不允许新增
        buttonBar()->button(BUTTON_AE_ADD_TEST_DATA)->setEnabled(false);
    }

    return;
}

/************************************************
 * 函数名   : onAddTestData
 * 输入参数 :  struct_AddingTestData&
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :槽函数，将新增测试项界面的信息写入pdaTask中
 ************************************************/
void AEFlyPDAView::onAddTestData( struct_AddingTestData &stAddingTestData)
{
    for(int i = 0, iSize = stAddingTestData.evType.size(); i < iSize; ++i)
    {
        ItemTestData stTestData;
        stTestData.bIsBgn = m_stTestData.bIsBgn;
        stTestData.eDataType = stAddingTestData.evType.at(i);
        stTestData.eDataUnit = m_stTestData.eDataUnit;

        PDAService::instance()->currentTask()->addTestData(stTestData);
    }
    return;
}

/******************************************
 * 功能：AE录音到最大录音时间，处理逻辑
 * ****************************************/
void AEFlyPDAView::stopAERecordToDo()
{
    if(stopAERecord())
    {
        this->buttonBar()->button(BUTTON_AE_RECORD)->setTitle(AE_TRANSLATE(AE::TEXT_START_RECORD));
        saveTestData();
    }
    else
    {
        logError("stop record failed.");
    }

    return;
}

/*************************************************
功能： 槽，响应录音时长变化
输入参数：
        id -- 按钮ID
*************************************************************/
void AEFlyPDAView::onTimerRefresh(uint uiRecordingTime)
{
	char cRecordTime[10] = {0};
	
    UINT32 uiHour = uiRecordingTime / (MINUTE_PER_HOUR * SECOND_PER_MINUTE);
    UINT32 uiMin = (uiRecordingTime % (MINUTE_PER_HOUR * SECOND_PER_MINUTE)) / SECOND_PER_MINUTE;
    UINT32 uiSec = (uiRecordingTime % (MINUTE_PER_HOUR * SECOND_PER_MINUTE)) % SECOND_PER_MINUTE;
	
    sprintf(cRecordTime, "%02d:%02d", uiMin, uiSec); //显示样式：xx:xx
	logInfo(QString("cRecordTime %1").arg(cRecordTime));
	
	this->buttonBar()->button(BUTTON_AE_RECORD)->setTitle(AE_TRANSLATE(cRecordTime));
}

