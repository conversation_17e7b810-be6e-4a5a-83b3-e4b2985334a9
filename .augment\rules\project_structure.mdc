---
description:
globs:
alwaysApply: false
---
# 项目结构指南

## 目录结构
```
T95/
├── Z200/                # 主应用程序
│   ├── app/             # 应用程序入口
│   ├── core/            # 核心功能模块
│   ├── module/          # 功能模块
│   ├── service/         # 服务模块
│   ├── view/            # 视图模块
│   ├── widget/          # 自定义控件
│   ├── mobileAccess/    # 移动端访问模块
│   ├── config/          # 配置文件
│   ├── lib/             # 库文件
│   └── z200.pro         # 项目文件
├── phasechart/          # 相位图表模块
│   ├── src/             # 源代码
│   ├── demos/           # 示例程序
│   └── PhaseChart.pro   # 项目文件
├── dataspecification/   # 数据规范模块
│   ├── common/          # 通用组件
│   ├── tev/             # TEV数据处理
│   ├── ae/              # AE数据处理
│   ├── infrared/        # 红外数据处理
│   ├── prps/            # PRPS数据处理
│   ├── mechanical/      # 机械数据处理
│   ├── currentamplitude/# 电流幅值数据处理
│   └── include/         # 头文件
├── datafile/            # 数据文件
├── doc/                 # 文档
└── upgrade/             # 升级相关文件
```

## 文件命名约定
- 头文件: `*.h`
- 源文件: `*.cpp`
- Qt项目文件: `*.pro`
- 用户界面文件: `*.ui`
- Qt语言文件: `*.ts`

## 模块组织
- Z200是主应用程序，包含UI和业务逻辑
- phasechart提供相位图表相关功能
- dataspecification提供数据规范和处理功能

## 构建系统
- 使用qmake管理项目构建
- 主要模块都有自己的.pro文件
- 使用SUBDIRS模板组织子项目

## 国际化
- 使用Qt的翻译系统
- 语言文件保存在主目录下（如Z200/*.ts）
- 支持多种语言，包括中文、英文等
