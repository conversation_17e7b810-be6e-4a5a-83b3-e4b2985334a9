#include "pdalistitem.h"
#include <QHBoxLayout>
#include <QEvent>
#include <QDebug>
#include <QToolTip>
#include "label/newtooltip.h"
#include "log/log.h"

/**************************************************
 *          勾选按键标签是否勾选的样式
 * ***********************************************/
const QString LABEL_TICKED = "border:none;border-image: url(:/images/tick_PDA.png)";
const QString LABEL_NOT_TICKED = "border:none;";

const QString CHECKBOX_TICKED = "border:none;border-image: url(:/images/Checkbox-ticked.png)";
const QString CHECKBOX_NOT_TICKED = "border:none;border-image: url(:/images/Checkbox_unTicked.png)";
/**************************************************
 *          伸缩因子，布局使用
 * ***********************************************/
//按键伸缩因子
typedef enum _LabelStretch
{
    AUTO_WRAP_LABEL = 8,//自动换行标签伸缩因子
    TICK_STRETCH = 1,//勾号伸缩因子
}LabelStretch;

/**************************************************
 *          选中样式
 * ***********************************************/
const QString ITEM_NORMAL_STYLE = "PDAListItem{border-radius: 5px; border-width: 1px; border-color:gray;border-style: solid;"
        "background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,stop: 0 #E0E0E0, stop: 1 #FFFFFF);}";
const QString ITEM_SELECTED_STYLE = "PDAListItem{background-color: rgb(205, 232, 255); border-radius: 5px; border-width: 1px; border-color:blue;border-style: solid}";
const QString ITEM_FOCUS_SELECTED_STYLE = "PDAListItem{background-color: rgb(205, 232, 255); border-radius: 5px; border-width: 1px; border-color:yellow;border-style: solid}";
const QString ITEM_FOCUS_NORMAL_STYLE = "PDAListItem{border-radius: 5px; border-width: 1px; border-color:yellow;border-style: solid;"
        "background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,stop: 0 #E0E0E0, stop: 1 #FFFFFF);}";

#define TICK_WIDTH_RATIO 0.5
#define TICK_HEIGHT_RATIO 0.3
#define MARGIN_SIZE 5
#define ITEM_MINIUM_HEIGHT 80 //item最小高度
#define FIRST_ITEM_POSITION_Y 102 //第一个item的y坐标

/*************************************************
输入参数:
       strText -- 显示的文本
       parent -- 父窗体指针
       bIsTicked -- 是否勾选
功能： 构造函数
*************************************************************/
PDAListItem::PDAListItem(const QString &strText,
                         bool bIsTicked,
                         ItemMode eMode,
                         QWidget *parent) :
    QFrame(parent),
    m_strText( strText ),
    m_bIsSelected( false ),
    m_eMode( eMode )
{
    setFrameStyle(QFrame::Panel | QFrame::Raised);
    setStyleSheet( ITEM_NORMAL_STYLE );
    m_stTime = QTime::currentTime();

    m_pLabelAutoWrap = new AutoWrapLabel( this,m_strText );
    m_pLabelAutoWrap->installEventFilter( this );
    m_pLabelTick = new QLabel( this );
    m_pLabelTick->installEventFilter( this );
    setTicked( bIsTicked );

    QHBoxLayout *hLayout = new QHBoxLayout;
    hLayout->setSpacing( 0 );
    hLayout->setMargin( MARGIN_SIZE );
    hLayout->addWidget( m_pLabelAutoWrap,AUTO_WRAP_LABEL );
    hLayout->addWidget( m_pLabelTick,TICK_STRETCH );
    setLayout( hLayout );
}

/*************************************************
输入参数:strText -- 文本
功能： 添加显示的文本
*************************************************************/
void PDAListItem::setText( const QString& strText )
{
    m_pLabelAutoWrap->setText( strText );
    m_strText = strText;
}

/*************************************************
返回值:strText -- 文本
功能： 获取显示的文本
*************************************************************/
const QString& PDAListItem::text( void )
{
    return m_strText;
}

/*************************************************
输入参数:strText -- 文本
功能： 添加内部的文本
*************************************************************/
void PDAListItem::setInnerText( const QString& strInnerText )
{
    m_strInnerText = strInnerText;
    return;
}

/*************************************************
返回值:strText -- 文本
功能： 获取内部的文本
*************************************************************/
const QString& PDAListItem::InnerText( void )
{
    return m_strInnerText;
}

/*************************************************
输入参数:QFont -- 字体
功能： 设置字体
*************************************************************/
void PDAListItem::setFont(QFont& font)
{
    m_pLabelAutoWrap->setTextFont(font);
    return;
}

/*************************************************
输入参数:true -- 选中
       false -- 未选中
功能： 设置是否选中
*************************************************************/
void PDAListItem::setSelected(bool bSelected, bool bShowTip)
{
    if( bSelected != m_bIsSelected )
    {
        QVector<QString> qvtMultiRowText = m_pLabelAutoWrap->getMultiRowText();
        QString qstrToolTip;
        for (int i = 0, iSize = qvtMultiRowText.size(); i < iSize; ++i)
        {
            if (0 == i)
            {
                qstrToolTip = qvtMultiRowText[i];
            }
            else
            {
                qstrToolTip += "\n" + qvtMultiRowText[i];
            }
        }

        m_bIsSelected = bSelected;
        if( m_bIsSelected )
        {
            setStyleSheet( ITEM_SELECTED_STYLE );
            setFrameStyle( QFrame::Panel | QFrame::Sunken );

            if(bShowTip)
            {
                if (m_pLabelAutoWrap->isIncompleteDisplay())
                {
                    QFont font = m_pLabelAutoWrap->font();
                    font.setPixelSize(25);
                    NewToolTip::setFont(font);
                    // 如果是第一行按钮，Tooltip显示在下方
                    if (FIRST_ITEM_POSITION_Y == mapToGlobal(QPoint(0, 0)).y())
                    {
                        NewToolTip::showTextBelowWithNoTextProcessed(mapToGlobal(QPoint(0, ITEM_MINIUM_HEIGHT)), qstrToolTip, this);
                    }
                    else
                    {
                        NewToolTip::showTextAboveWithNoTextProcessed(mapToGlobal(QPoint(0, 0)), qstrToolTip, this);
                    }
                }
            }
        }
        else
        {
            setStyleSheet( ITEM_NORMAL_STYLE );
            setFrameStyle( QFrame::Panel | QFrame::Raised );

            if (m_pLabelAutoWrap->isIncompleteDisplay() && NewToolTip::text() == qstrToolTip)
            {
                NewToolTip::hideText();
            }
        }
    }

    return;
}

/*************************************************
输入参数:true -- 选中
       false -- 未选中
功能： 设置是否选中
*************************************************************/
void PDAListItem::setFocusState(bool bFocused, bool bShowTip)
{
    QVector<QString> qvtMultiRowText = m_pLabelAutoWrap->getMultiRowText();
    QString qstrToolTip;
    for (int i = 0, iSize = qvtMultiRowText.size(); i < iSize; ++i)
    {
        if (0 == i)
        {
            qstrToolTip = qvtMultiRowText[i];
        }
        else
        {
            qstrToolTip += "\n" + qvtMultiRowText[i];
        }
    }

    if(bFocused)
    {
        if(m_bIsSelected)
        {
            setStyleSheet(ITEM_FOCUS_SELECTED_STYLE);
            setFrameStyle(QFrame::Panel | QFrame::Sunken);
        }
        else
        {
            setStyleSheet(ITEM_FOCUS_NORMAL_STYLE);
            setFrameStyle(QFrame::Panel | QFrame::Raised);
        }

        if(bShowTip)
        {
            if (m_pLabelAutoWrap->isIncompleteDisplay())
            {
                QFont font = m_pLabelAutoWrap->font();
                font.setPixelSize(25);
                NewToolTip::setFont(font);
                // 如果是第一行按钮，Tooltip显示在下方
                if (FIRST_ITEM_POSITION_Y == mapToGlobal(QPoint(0, 0)).y())
                {
                    NewToolTip::showTextBelowWithNoTextProcessed(mapToGlobal(QPoint(0, ITEM_MINIUM_HEIGHT)), qstrToolTip, this);
                }
                else
                {
                    NewToolTip::showTextAboveWithNoTextProcessed(mapToGlobal(QPoint(0, 0)), qstrToolTip, this);
                }
            }
        }
    }
    else
    {
        if(m_bIsSelected)
        {
            setStyleSheet(ITEM_SELECTED_STYLE);
            setFrameStyle(QFrame::Panel | QFrame::Sunken);
        }
        else
        {
            setStyleSheet(ITEM_NORMAL_STYLE);
            setFrameStyle(QFrame::Panel | QFrame::Raised);
        }

        if (m_pLabelAutoWrap->isIncompleteDisplay() && NewToolTip::text() == qstrToolTip)
        {
            NewToolTip::hideText();
        }
    }

    return;
}

/*************************************************
返回值:true -- 选中
       false -- 未选中
功能： 是否选中
*************************************************************/
bool PDAListItem::isSelected( void )
{
    return m_bIsSelected;
}

/*************************************************
输入参数:true -- 勾选
       false -- 未勾选
功能： 设置是否勾选
*************************************************************/
void PDAListItem::setTicked( bool bTicked )
{
    //if( m_bIsTicked != bTicked )
    {
        m_bIsTicked = bTicked;
        if( m_bIsTicked )
        {
            if( m_eMode == LABEL_MODE )
            {
                m_pLabelTick->setStyleSheet( LABEL_TICKED );
            }
            else if( m_eMode == CHECK_BOX )
            {
                m_pLabelTick->setStyleSheet( CHECKBOX_TICKED );
            }
        }
        else
        {
            if( m_eMode == LABEL_MODE )
            {
                m_pLabelTick->setStyleSheet( LABEL_NOT_TICKED );
            }
            else if( m_eMode == CHECK_BOX )
            {
                m_pLabelTick->setStyleSheet( CHECKBOX_NOT_TICKED );
            }
        }
    }

    return;
}

/*************************************************
返回值:true -- 勾选
       false -- 未勾选
功能： 是否勾选
*************************************************************/
bool PDAListItem::isTicked( void )
{
    return m_bIsTicked;
}

/*************************************************
函数名： eventFilter
功能： 事件过滤器，获取当前点击的子控件索引，方便View设置选中的stylesheet
*************************************************************/
bool PDAListItem::eventFilter(QObject *pObj, QEvent *pEvent)
{
    if( pEvent->type() == QEvent::MouseButtonPress )
    {
        if( ( m_eMode == CHECK_BOX )
                && ( pObj == m_pLabelTick ) )
        {
            setTicked( !m_bIsTicked );
        }
        else if( pObj == m_pLabelAutoWrap )
        {
            m_stTime = QTime::currentTime();
            emit sigPressedDown();
        }
        return true;
    }
    else if(pEvent->type() == QEvent::MouseButtonRelease)
    {
        if(pObj == m_pLabelAutoWrap)
        {
            if(m_stTime.elapsed() > 1500)
            {
                //long press
                emit sigLongPressed();
            }
            else
            {
                //short press
                emit sigPressed();
                qDebug() << "PDAListItem::eventFilter 1: " << m_pLabelAutoWrap->geometry();
                qDebug() << "PDAListItem::eventFilter 2: " << m_pLabelTick->geometry();
            }
        }
        return true;
    }
    else
    {
        return QWidget::eventFilter(pObj, pEvent);
    }
}
