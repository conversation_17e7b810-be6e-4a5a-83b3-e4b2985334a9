#include "tevprpsplaybackview.h"
#include "datadefine.h"
#include "tev/dataSave/prps/tevprpsandprpddatasave.h"
#include "window/Window.h"
#include "playbackView/PlayBackBase.h"

//data save
#include "prps/prpddatamap.h"
#include "datafile/prps/prpsmapdefine.h"
#include "datafile/mapdatafactory.h"
#include "appconfig.h"

/****************************
功能： 构造函数
输入参数:
    parent -- 父窗体
*****************************/
TEVPRPSPlayBackView::TEVPRPSPlayBackView(QWidget *parent) :
    PlayBackBase(parent)
{
    QPalette p;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    SystemSet::PRPSBGColor eColor = (SystemSet::PRPSBGColor) pConfig->value( APPConfig::KEY_PRPS_BG_COLOR ).toInt();
    pConfig->endGroup();
    if(eColor == SystemSet::PRPS_BG_COLOR_GRAY)
    {
        p.setColor( QPalette::Background,QColor(128, 128, 128) );
    }
    else
    {
        p.setColor( QPalette::Background,Qt::white );
    }
    setPalette( p );
    setAutoFillBackground( true );

    m_pChart = new UhfPrpsUnionView(TEV::PRPS_PERIOD_CNT,TEV::PRPS_PERIOD_CNT, TEV::PRPS_PHASE_CNT, TEV::CHART_MAX_VALUE,TEV::CHART_MIN_VALUE);
    m_pChart->setFixedSize(Window::WIDTH, CHART_HEIGHT);
    m_pChart->setPrpdContentsMargins(0, PRPD_MARGIN, 0, PRPD_MARGIN);
    m_pChart->setOriginXRatio(25);
    m_pChart->setPhaseAxisOffset(35);
    setCenterWidget( m_pChart );
}

/****************************
功能： 回放文件
输入参数:
    strFileName -- 文件名
*****************************/
void TEVPRPSPlayBackView::playbackFile( const QString& strFileName )
{
    qDebug()<<"TEVPRPSPlayBackView::playbackFile, strFileName:"<<strFileName;

    TEVPRPSAndPRPDDataSave  fDataSave;
    TEVPRPSPRPDDataInfo sPlayBackDataInfo;
    int value = fDataSave.getData( strFileName,&sPlayBackDataInfo );

    if( HC_FAILURE == value )
    {
        qWarning() << "TEVPRPSPlayBackView::playbackFile :" << strFileName << " get data  failed!";
        return;
    }
    displayMap(sPlayBackDataInfo);
}

/****************************
功能： 在回放中显示
输入参数: void
*****************************/
void TEVPRPSPlayBackView::displayMap(TEVPRPSPRPDDataInfo &PlayBackDataInfo)
{

    m_pChart->clearData();

//    if(PlayBackDataInfo.ucFreq == PrpsGlobal::FREQ_50)
//    {
//        m_pChart->setDisplayedPeriodCnt(PrpsGlobal::FREQ_50);
//    }
//    else
//    {
//        m_pChart->setDisplayedPeriodCnt(PrpsGlobal::FREQ_60);
//    }

    /*设置频率*/
    m_pChart->setPowerFreq(PrpsGlobal::FREQ_50);

    m_pChart->clearSyncText();
    //m_pChart->setSync((PrpsGlobal::SyncSource) (PlayBackDataInfo.stPRPSInfo.eSyncSource -1), (PrpsGlobal::SyncState) PlayBackDataInfo.stPRPSInfo.ucSyncState );

    //是否无信号状态没有保存到数据文件，回放时，先简单默认不显示无信号
    m_pChart->setConnected(true);

    setStationName(PlayBackDataInfo.stPRPSHeadInfo.strSubstationName);
    setDeviceName(PlayBackDataInfo.stPRPSHeadInfo.strDeviceName);

    QVector<qint16> prpdData;
    prpdData.clear();
    double dPRRepeatyData;

    //NOTE 如果是累计模式，iPeriodCnt的计算不正确
    int iPeriodCnt = PlayBackDataInfo.stPRPDInfo.iPowerFreCycleCount;
    double dTimePerPeriod = (1000.0 / (double) PlayBackDataInfo.ucFreq) / 1000.0;//每周期时间 单位s

    dbg_info("iPeriodCnt is %d\n", iPeriodCnt);

    for(int i = 0; i < PlayBackDataInfo.stPRPDInfo.iQuantificationAmp; i ++)
    {
        for(int j = 0; j < PlayBackDataInfo.stPRPDInfo.iPhaseIntervalCount; j ++)
        {
            dPRRepeatyData = PlayBackDataInfo.vecPRRepeatyData.at(j*PlayBackDataInfo.stPRPDInfo.iQuantificationAmp + i);
            dPRRepeatyData = dPRRepeatyData *iPeriodCnt * dTimePerPeriod;
            prpdData.append((qint16)(dPRRepeatyData+0.5f));
            if(dPRRepeatyData > 0)
            {
                //dbg_info("%dth is %f\n", j*PlayBackDataInfo.stPRPDInfo.iQuantificationAmp + i, dPRRepeatyData);
            }
        }
    }

    dbg_info("PlayBackDataInfo.stPRPDInfo.iPowerFreCycleCount is %d\n", PlayBackDataInfo.stPRPDInfo.iPowerFreCycleCount);
    m_pChart->setMaxSpectrum(PlayBackDataInfo.stPRPSInfo.fMax);
    m_pChart->addPlayBackData( PlayBackDataInfo.vecPRPSData, prpdData, PlayBackDataInfo.stPRPDInfo.iPowerFreCycleCount );
}

