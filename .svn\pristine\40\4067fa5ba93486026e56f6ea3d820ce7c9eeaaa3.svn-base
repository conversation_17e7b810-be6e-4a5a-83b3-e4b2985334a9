#ifndef ADDTESTDATADIALOG_H
#define ADDTESTDATADIALOG_H

#include <QDialog>
#include <QListWidget>
#include <QLabel>
#include <QPushButton>
#include <QKeyEvent>
#include "pda/pda.h"
#include "PDAUi/PDAUiView/PDAViewConfig.h"
#include "PDAUi/PDAUiView/pdasifttaskview/listwidget.h"


typedef struct _struct_AddingTestData
{
    //QVector<bool> bvIsBgn;
    QVector<PDAServiceNS::TestDataType> evType;
}struct_AddingTestData;

class AddTestDataDialog : public QDialog
{
    Q_OBJECT
public:
    AddTestDataDialog(PDAServiceNS::TestPointType eType, QWidget *parent = 0);

    typedef struct _AddingInfo
    {
        PDAServiceNS::TestDataType eNum;
        const char*  strTestDataName;            //菜单名称;
        quint8 ucDefaultIndex;
    }AddingInfo;

    /****************************
    函数名： keyPressEvent
    输入参数:
            e -- keyPress事件
    输出参数：NULL
    返回值：NULL
    功能： 处理keyPress事件
    *****************************/
    void keyPressEvent(QKeyEvent* event);

    /*************************************************
    功能： 处理show事件
    *************************************************************/
    void showEvent(QShowEvent* event);

    /*************************************************
    函数名： eventFilter
    功能： 事件过滤器
    *************************************************************/
    bool eventFilter(QObject* pObj, QEvent* pEvent);

signals:
    void sigAddingTestDataChanged( struct_AddingTestData&);

private slots:
    /*************************************************
    功能： 响应确认按钮点击
    *************************************************************/
    void onBtnOKPressed();

    /*************************************************
    功能： 响应取消按钮点击
    *************************************************************/
    void onBtnCancelPressed();

private:

    ListWidget *mpTestDataListWidget;
    QPushButton *mpBtnOk;   // ok键
    QPushButton *mpBtnCancel;// 取消键
    QMap<quint8, PDAServiceNS::TestDataType> m_MatchingMap;
    struct_AddingTestData mstAddingTestData ;
    QVector<QWidget*>  m_vAllin;
    quint32 m_iCurrentIndex;

};

#endif // ADDTESTDATADIALOG_H
