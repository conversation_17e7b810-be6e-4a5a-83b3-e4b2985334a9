#include "isampleappcomm.h"
#include <QTimerEvent>
#include "comm/abstractcomm.h"
#include "systemsetting/systemsetservice.h"
#include "protocol/linklayerprotocolbase.h"
#include "protocol/linklayerprotocollr.h"
#include "protocol/linklayerprotocolstandard.h"
#include "business/localbusinessbase.h"
#include "business/localbusinesslr.h"
#include "business/localbusinessstandard.h"


ISampleAppComm::ISampleAppComm(QObject *parent) : QObject(parent)
{
    m_uiHeartbeatIntvl = 0;
    m_iGetHeartbeatId = -1;
}

ISampleAppComm::~ISampleAppComm()
{

}

/****************************
功能： 进行协议层的初始化，各个协议层的挂接
入参： NULL
出参： NULL
返回值：NULL
*****************************/
void ISampleAppComm::initProtocol()
{
    // 创建通讯组件与服务组件
    m_pClient = createComm();

    SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
    if(SystemSet::ACCESS_PROTO_SDLR == eProtocol)
    {
        m_pProtocol = new LinkLayerProtocolLR(m_pClient, this);
        m_pLocalBusiness = new LocalBusinessLR(this);
    }
    else
    {
        //其他杭州华云、北京融智通、上海艾飞、西安创亦均按标准协议
        m_pProtocol = new LinkLayerProtocolStandard(m_pClient, this);
        m_pLocalBusiness = new LocalBusinessStandard(this);
    }

    // 注册信号所用的类型
    qRegisterMetaType<Protocol::ProgressState>("Protocol::ProgressState");
    qRegisterMetaType<Protocol::ProtocolParam>("Protocol::ProtocolParam");
    qRegisterMetaType<Protocol::CommFrameInfo>("Protocol::CommFrameInfo");
    qRegisterMetaType<QVector<UINT8> >("QVector<UINT8>");

    // 建立从下到上的数据上报通路
    // 通讯组件可读信号->链路层协议解析->本地业务处理->继续上报到使用该信息的地方
    connect(m_pClient, SIGNAL(sigReadyRead(qint64)), m_pProtocol, SLOT(onReadyRead(qint64)));
    connect(m_pProtocol, SIGNAL(sigDataRead(QByteArray, Protocol::ProtocolParam)), m_pLocalBusiness, SLOT(onParseData(QByteArray, Protocol::ProtocolParam)));
    connect(m_pProtocol, SIGNAL(sigDataRead_EX(Protocol::CommFrameInfo)), m_pLocalBusiness, SLOT(onParseData_EX(Protocol::CommFrameInfo)), Qt::QueuedConnection);
    connect(m_pLocalBusiness, SIGNAL(sigFigureType(QVector<UINT8>)), this, SIGNAL(sigFigureType(QVector<UINT8>)));
    connect(m_pLocalBusiness, SIGNAL(sigTaskContent(QByteArray)), this, SIGNAL(sigTaskContent(QByteArray)));
    connect(m_pProtocol, SIGNAL(sigUploadResult(bool)), this, SIGNAL(sigUploadResult(bool)));

    // 数据发送的挂接
    connect(m_pLocalBusiness, SIGNAL(sigDataToRemote(QByteArray,Protocol::ProtocolParam)), m_pProtocol, SLOT(onSendData(QByteArray,Protocol::ProtocolParam)));
    connect(m_pLocalBusiness, SIGNAL(sigDataToRemote_EX(Protocol::CommFrameInfo)), m_pProtocol, SLOT(onSendData_EX(Protocol::CommFrameInfo)), Qt::QueuedConnection);
    connect(this, SIGNAL(sigStartHBTimer()), this, SLOT(onStartHBTimer()));

    m_bConnected = false;
    m_uiHeartbeatIntvl = (2 * 60000);

    m_pLocalBusiness->setHBTimerDelay(m_uiHeartbeatIntvl * 4);      //超时连接为3次，判断超时是第4次

    emit sigStartHBTimer();
}


/****************************
功能： 打开
*****************************/
void ISampleAppComm::open( void )
{
    m_pClient->open();
}

/****************************
功能： 关闭
*****************************/
void ISampleAppComm::close( void )
{
    m_pClient->close();
}

/****************************
功能： 发送和app通信的业务数据
入参： baData -- 需要发送的业务数据
出参： NULL
返回值：void
*****************************/
void ISampleAppComm::sendData( const QByteArray &baData )
{
    Q_UNUSED(baData);
    //m_pLocalBusiness->sendDatFileData(baData);
}

/****************************
函数名：
输入参数： xmlData -- 上传报文数据内容
         zipFileContent -- 数据压缩文件内容
         zipFileName -- 压缩文件名
输出参数： NULL
返回值： NULL
功能： 发送上传数据报文
*****************************/
void ISampleAppComm::sendUploadData( const QByteArray& xmlData, const QByteArray& zipFileContent, const QString &zipFileName )
{
    SystemSet::CustomAccessMode eMode = SystemSetService::instance()->getCustomAccessMode();
    if(SystemSet::ACCESS_USB_MODE != eMode)
    {
        //需要网络连接
        m_pLocalBusiness->sendUploadData(xmlData, zipFileContent, zipFileName);
    }
    else
    {
        Q_UNUSED(xmlData);
        Q_UNUSED(zipFileContent);
        Q_UNUSED(zipFileName);
    }

    return;
}

/****************************
功能： 判断连接是否存在
入参： NULL
出参： NULL
返回值：连接存在则为true，否则为false
*****************************/
bool ISampleAppComm::isConnected()
{
    return m_bConnected;
}

/****************************
功能： 定时器处理函数
入参： pEvent -- 定时事件
*****************************/
void ISampleAppComm::timerEvent( QTimerEvent* pEvent )
{
    int iId = pEvent->timerId();
    if( iId == m_iGetHeartbeatId )
    {
        if( isConnected() )
        {
            if(!(m_pProtocol->isSendingMultiFrame()))
            {
                m_pLocalBusiness->sendHeartbeat();
            }
        }
    }
    else
    {
        qWarning("HSTCommunication timerId is wrong");
    }
}

//槽，启动心跳定时器
void ISampleAppComm::onStartHBTimer()
{
    if( -1 != m_iGetHeartbeatId )
    {
        killTimer( m_iGetHeartbeatId );
    }

    //先发一次心跳
    if(isConnected())
    {
        m_pLocalBusiness->sendHeartbeat();
    }
    m_iGetHeartbeatId = startTimer(m_uiHeartbeatIntvl);
    return;
}

/*************************************************
功能： 设置发送心跳包的间隔
输入参数:
    iIntvl -- 间隔时间 单位：ms
输出参数：NULL
返回值： NULL
*************************************************/
void ISampleAppComm::setHeartbeatIntvl( int iIntvl )
{
    m_uiHeartbeatIntvl = iIntvl;
    emit sigStartHBTimer();
}
