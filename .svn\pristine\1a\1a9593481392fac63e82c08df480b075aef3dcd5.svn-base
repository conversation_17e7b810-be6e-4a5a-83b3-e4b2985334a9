﻿/*
* Copyright (c) 2017.2，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：CAView.cpp
*
* 初始版本：1.0
* 作者：赵勇军
* 修改日期：2017年5月18日
* 摘要：外设匹配二级菜单申明
* 当前版本：1.0
*/

#include <QDebug>
#include <QApplication>
#include "pmview.h"
#include "peripheralmatch/conditionermatchview.h"
#include "camatchview.h"
#include "datadefine.h"
#include "functionconfigmanager/funccfgsrvmanager.h"
#include "Function.h"
#ifdef Q_PROCESSOR_ARM
#include "UHFHFCTAETEVApi.h"
#endif

//功能按钮配置

const QString s_UHFConditionerIconPath[] =
{
    ":/images/FunctionImage/uhfConditioner.png"
};

const QString s_HFCTConditionerIconPath[] =
{
    ":/images/FunctionImage/hfctConditioner.png"
};

const QString s_ExternalSyncerIconPath[] =
{
    ":/images/FunctionImage/ExternalSyncer.png"
};

const QString s_AESensorIconPath[] =
{
    ":/images/FunctionImage/aeSensor.png"
};

const QString s_CAConditionerIconPath[] =
{
    ":/images/sampleControl/Current_CA_2.png"
};

const QString s_CurrentDetectionSensorIconPath[] =
{
    ":/images/functionbutton/current_detection.png"
};

void getPMSubFuncConfig(FunctionButtonPanel::Config*& pstSubFuncConfig, quint8 &qui8SubFuncCount)
{
    QVector<FunctionButtonPanel::Config> qvtOpitionalFunctionConfigs;
    qvtOpitionalFunctionConfigs.clear();

    FuncConfigManagerNS::ConfigInfo stConfigInfo;
    FuncCfgSrvManager::instance()->getConfigInfo(stConfigInfo);
    const QVector<FuncConfigManagerNS::FunctionInfo>& qvtFuncInfos = stConfigInfo.qvtFuncInfos;
    for (int i = 0, iSize = qvtFuncInfos.size(); i < iSize; ++i)
    {
        if(!(qvtFuncInfos[i].bEnable))
        {
            continue;
        }

        Function::Code eFuncCode = View::functionID2Enum(static_cast<FuncConfigManagerNS::FunctionID>(qvtFuncInfos[i].iFuncID));
        switch (eFuncCode)
        {
        case Function::UHF:
        {
            qvtOpitionalFunctionConfigs.push_back({PMView::PM_UHF, QT_TRANSLATE_NOOP("PMView", "UHF"), s_UHFConditionerIconPath, sizeof(s_UHFConditionerIconPath) / sizeof(QString), NULL, 0, 0});
            break;
        }
        case Function::HFCT:
        {
            qvtOpitionalFunctionConfigs.push_back({PMView::PM_HFCT, QT_TRANSLATE_NOOP("PMView", "HFCT"), s_HFCTConditionerIconPath, sizeof(s_HFCTConditionerIconPath) / sizeof(QString), NULL, 0, 0});
            break;
        }
        case Function::CA:
        {
            qvtOpitionalFunctionConfigs.push_back({PMView::PM_CA_CONDITIONER, QT_TRANSLATE_NOOP("PMView", "CA Diag."), s_CAConditionerIconPath, sizeof(s_CAConditionerIconPath) / sizeof(QString), NULL, 0, 0});
            break;
        }
        case Function::AE_WIRELESS_FUNC:
        {
            qvtOpitionalFunctionConfigs.push_back({PMView::PM_AE, QT_TRANSLATE_NOOP("PMView", "AE"), s_AESensorIconPath, sizeof(s_AESensorIconPath) / sizeof(QString), NULL, 0, 0});
            break;
        }
        case Function::CURRENT_DETECTION:
        {
            qvtOpitionalFunctionConfigs.push_back({ PMView::PM_CURRENT_DETECTION, QT_TRANSLATE_NOOP("PMView", "Current"), s_CurrentDetectionSensorIconPath, sizeof(s_CurrentDetectionSensorIconPath) / sizeof(QString), NULL, 0, 0 });
            break;
        }
        default:
        {
            break;
        }

        }
    }

    qvtOpitionalFunctionConfigs.push_back({PMView::PM_SYNCHRONIZER, QT_TRANSLATE_NOOP("PMView", "Power Sync."), s_ExternalSyncerIconPath, sizeof(s_ExternalSyncerIconPath) / sizeof(QString), NULL, 0, 0});

    qui8SubFuncCount = static_cast<quint8>(qvtOpitionalFunctionConfigs.size());
    pstSubFuncConfig = new FunctionButtonPanel::Config[qui8SubFuncCount];
    for (int i = 0; i < qui8SubFuncCount; ++i)
    {
        pstSubFuncConfig[i] = qvtOpitionalFunctionConfigs[i];
    }

    return;
}


/************************************************
 * 功能     : 构造函数
 * 输入参数 :
 *      pConfig -- 配置
 *      strBackgroundIcon -- 背景图片
 *      parent -- 父窗体
 * 输出参数 : NULL
 * 返回值   : NULL
 ************************************************/
PMViewPanel::PMViewPanel( const FunctionButtonPanel::Config* pConfig,
                          const QString& strBackgroundIcon,
                          QWidget *parent )
    :FunctionPanelView( "PMView",pConfig, strBackgroundIcon, parent )
{

}


/************************************************
 * 功能     : 析构函数
 ************************************************/
PMViewPanel::~PMViewPanel()
{
    qDebug()<<"PMViewPanel::~PMViewPanel";
}

/************************************************
 * 功能     : 响应按钮按下动作
 * 注：如有子功能表，则默认打开子功能表
 * 输入参数：
 *      ucID -- 按钮ID
 *      pConfig -- 按钮配置
 ************************************************/
void PMViewPanel::onButtonPressed( quint8 ucID, const FunctionButtonPanel::Config* pConfig )
{
    Q_UNUSED( pConfig );
    switch ( ucID )
    {
#ifdef Q_PROCESSOR_ARM
    case PMView::PM_UHF:
    {
        ConditionereMatchView *pView = new ConditionereMatchView(UHF_TERMINAL);
        pView->show();
        break;
    }
    case PMView::PM_HFCT:
    {
        ConditionereMatchView *pView = new ConditionereMatchView(HFCT_TERMINAL);
        pView->show();
        break;
    }
    case PMView::PM_SYNCHRONIZER:
    {
        ConditionereMatchView *pView = new ConditionereMatchView(SYNCHRONIZER);
        pView->show();
        break;
    }
    case PMView::PM_AE:
    {
        ConditionereMatchView *pView = new ConditionereMatchView(AE_TERMINAL);
        pView->show();
        break;
    }
#endif
    case PMView::PM_CA_CONDITIONER:
    {
        CAMatchView *pView = new CAMatchView();
        pView->show();
        break;
    }
    case PMView::PM_CURRENT_DETECTION:
    {
        ConditionereMatchView *pView = new ConditionereMatchView(CABLE_CURRENT_TERMINAL);
        pView->show();
        break;
    }
    default:
    {
        logError("invalid function id.");
    }
        break;
    }

    return;
}
