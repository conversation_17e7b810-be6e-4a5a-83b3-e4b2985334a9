﻿#include <QtConcurrentRun>
#include "dataSave/DataFileInfos.h"
#include "deleteDataView/RotateDeleteDataView.h"
#include "infrared/guideinfrareddatasave.h"
#include "infrared/rotatewidget.h"
#include "infrared/rotatedialog.h"
#include "playbackView/RotatePlayBackView.h"
#include "guideinfraredplaybackview.h"
#include "guideinfraredpdaview.h"
#include "config/ConfigManager.h"
#include "systemsetting/SystemSet.h"
#include "statusbar/StatusBar.h"
#include "appconfig.h"
#include "log/log.h"
#include "fileoper/fileoperutil.h"
#include "infrared/guide/guideclientmanager.h"
#include "pda/pdatask.h"
#include "pda/pdaservice.h"
#include "infrared/InfraredDataSave.h"
#include "infrared/infraredimagingview.h"

namespace
{
    QString pfnSaveDataFun(GuideInfraredViewBase* pMain, PDATask* pTask,const GuideInfraredDataInfo& stGuideInfraredDataInfo, const DataMapHead& stDataMapHead)
    {
        QString qstrDataFilePath;
        QString qstrPicturePath;
        if(pMain && pTask)
        {
            pTask->saveGuideInfraredTestData(stGuideInfraredDataInfo, stDataMapHead, qstrDataFilePath, qstrPicturePath);
        }
        else
        {
            logError("pUserInfo == NULL || pTask == NULL");
        }

        return qstrDataFilePath;
    }

    // 采集页面按钮菜单
    const InfraredButtonInfo s_InfraredButtonInfos[] =
    {
        { GuideInfrared::BUTTON_STATUS,           &g_InfraredRunButtonInfo, },
    #ifdef DISPLAY_MODE
        { GuideInfrared::BUTTON_DISPLAY_MODE,           &g_DisplayModeButtonInfo, },
    #endif
        { GuideInfrared::BUTTON_COLOR_TYPE,       &g_InfraredColorButtonInfoBasic, },
        { GuideInfrared::BUTTON_ANALYSE_SHAPE,    &g_InfraredAnalyseShapeBasic, },
        { GuideInfrared::BUTTON_SET_PARAM,        &g_InfraredSetParamButtonInfo, },
        { GuideInfrared::BUTTON_DELETE_ALL_SHAPE, &g_InfraredDelAllButtonInfo, },
        { GuideInfrared::BUTTON_SAVA_DATA,        &g_SaveDataButtonInfo, },
        { GuideInfrared::BUTTON_ADD,        &g_InfraredAdd, },
        { GuideInfrared::BUTTON_AUTO_FOCUS,      &g_AutoFocusButtonInfo, },
        { GuideInfrared::BUTTON_CLOSE_FOCUS_FINE_TUNING,      &g_CloseFocusFineTuningButtonInfo, },
        { GuideInfrared::BUTTON_FAR_FOCUS_FINE_TUNING,      &g_FarFocusFineTuningButtonInfo, },
//        { BUTTON_ELECTRONIC_ZOOM,      &g_ElectronicZoomButtonInfo, },
    #ifdef DISPLAY_MODE
        { GuideInfrared::BUTTON_LASER_CONTROL,      &g_LaserControlButtonInfo, },
        { GuideInfrared::BUTTON_LED_FILL_LIGHT,      &g_LEDFillLightButtonInfo, },
        { GuideInfrared::BUTTON_AUXILIARY_LIGHTING,      &g_AuxiliaryLightingButtonInfo, },
    #endif
        { GuideInfrared::BUTTON_EXIT,             &g_InfraredExit, },
    };
    const int INVALID_USER = -1;
    const int g_iStartSampleTimeInterval2000MS = 2000;
}
/*************************************************
函数名： GuideInfraredPDAView(QWidget *parent)
输入参数： parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
GuideInfraredPDAView::GuideInfraredPDAView(const QString& qstrTitle, const ItemTestData& stTestDataInfo, QWidget *parent)
    : GuideInfraredViewBase(parent),
      m_bIsReadData(false),
      m_bIsReadDataBeforePlayback(false),
      m_bInitFlag(true),
      m_bSave(false),
      m_bLedFillLight(false),
      m_qstrTitle(qstrTitle),
      m_bSaveImage(true),
      m_pPlaybackInfraredImagingView(NULL)
{
    m_qstrTestDataFilePath = stTestDataInfo.strFilePath;
    m_stTestData = stTestDataInfo;
    m_bPlaybacking = false;

    InfraredControlButtonBar* pButtonBar = createButtonBar(s_InfraredButtonInfos, sizeof(s_InfraredButtonInfos) / sizeof(InfraredButtonInfo));
#ifdef DISPLAY_MODE
    pButtonBar->buttons(GuideInfrared::BUTTON_LASER_CONTROL)->setPopupMode(PopupWidget::SWITCH_MODE);
    pButtonBar->buttons(GuideInfrared::BUTTON_LASER_CONTROL)->setValue(GuideClientManager::instance()->getLaserControlState());
    pButtonBar->buttons(GuideInfrared::BUTTON_LED_FILL_LIGHT)->setPopupMode(PopupWidget::SWITCH_MODE);
    pButtonBar->buttons(GuideInfrared::BUTTON_LED_FILL_LIGHT)->setValue(m_bLedFillLight);
    pButtonBar->buttons(GuideInfrared::BUTTON_AUXILIARY_LIGHTING)->setPopupMode(PopupWidget::SWITCH_MODE);
    pButtonBar->buttons(GuideInfrared::BUTTON_AUXILIARY_LIGHTING)->setValue(GuideClientManager::instance()->getAuxiliaryLightingState());
    pButtonBar->buttons(GuideInfrared::BUTTON_DISPLAY_MODE)->setPopupMode(PopupWidget::SWITCH_MODE);
    pButtonBar->buttons(GuideInfrared::BUTTON_DISPLAY_MODE)->setValue(m_eGuideInfraredDisplayMode);
#endif
    setButtonBar(pButtonBar);

    disableButtons();
    m_pButtonBar->buttons(GuideInfrared::BUTTON_STATUS)->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_RECOVER));

    m_pGuideInfraredImagingView->setDisplayMode(m_eGuideInfraredDisplayMode);
    //connect(m_pGuideInfraredImagingView, SIGNAL(sigCreateAnalysisShape()), this, SLOT(onStopSample()));
    connect(m_pGuideInfraredImagingView, SIGNAL(sigCreateAnalysisShape()), this, SLOT(onCreateShape()));
    connect(m_pGuideInfraredImagingView, SIGNAL(sigRestartSample()), this, SLOT(onRestartSample()));

    connect(GuideClientManager::instance(), SIGNAL(sigRgbData(QSharedPointer<Guide::GuideRGBDataInfo>)), this, SLOT(onRgbData(QSharedPointer<Guide::GuideRGBDataInfo>)));
    connect(GuideClientManager::instance(), SIGNAL(sigY16Data(QSharedPointer<Guide::GuideY16DataInfo>)), this, SLOT(onY16DataInfo(QSharedPointer<Guide::GuideY16DataInfo>)));

    connect(GuideClientManager::instance(), SIGNAL(sigConnectInfraredDevFinished(bool)), this, SLOT(onInfraredInitResult(bool)));
    connect(GuideClientManager::instance(), SIGNAL(sigConnectInfraredDevState(bool)), this, SLOT(onInfraredDevState(bool)));

    connect(&m_saveDataFutureWatcher, SIGNAL(finished()), this, SLOT(onSaveDataFinished()));

    // 显示上次的测试数据
    if(m_stTestData.bTested)
    {
        playBackTestedData(m_qstrTestDataFilePath);
        QTimer::singleShot(g_iStartSampleTimeInterval2000MS, this, SLOT(onRecoverSampleView()));
    }
}

/*************************************************
功能： 析构函数
*************************************************************/
GuideInfraredPDAView::~GuideInfraredPDAView()
{
    m_bIsReadData = false;
    disconnect(GuideClientManager::instance(), 0, this, 0);
}

/************************************************
 * 输入参数 : stTestPointInfo---巡检测点相关信息
 * 功能     : 设置显示测点信息
 ************************************************/
void GuideInfraredPDAView::setTestPointInfo(const View::PatrolTestPointInfo& stTestPointInfo)
{
    m_stTestPointInfo = stTestPointInfo;
    setStationInfo(m_stTestPointInfo.strStationName, m_stTestPointInfo.strDeviceName, m_stTestPointInfo.strTestPointName);
    showStationInfo(true);

    // 如果在回放，给回放界面上也显示
    if (m_pPlaybackInfraredImagingView)
    {
        m_pPlaybackInfraredImagingView->showStationInfo(true);
        m_pPlaybackInfraredImagingView->setStationInfo(m_stTestPointInfo.strStationName, m_stTestPointInfo.strDeviceName, m_stTestPointInfo.strTestPointName);
    }
}

/*************************************************
功能： 响应回放结束信号
*************************************************************/
void GuideInfraredPDAView::onPlaybackFinished()
{
    if (m_bIsReadDataBeforePlayback)
    {
        resume();
    }
}

/*************************************************
输入参数： data：红外数据
功能： 响应红外数据信号
*************************************************************/
void GuideInfraredPDAView::onRgbData(QSharedPointer<Guide::GuideRGBDataInfo> qspInfraredData)
{
    if (m_bSave || !m_bIsReadData)
    {
        return;
    }

    if (m_pGuideInfraredImagingView)
    {
        m_pGuideInfraredImagingView->setInfraredRgbData(qspInfraredData);
    }
}

void GuideInfraredPDAView::onY16DataInfo(QSharedPointer<Guide::GuideY16DataInfo> qspY16DataInfo)
{
    if (m_bSave || !m_bIsReadData)
    {
        return;
    }

    if (m_pInfraredMsgBox)
    {
        // 如果断开连接又连上了把对话框关掉
        m_pInfraredMsgBox->accept();
        m_pInfraredMsgBox = NULL;
    }

    if (m_pGuideInfraredImagingView)
    {
        m_pGuideInfraredImagingView->setY16DataInfo(qspY16DataInfo);
    }
}

/*************************************************
输入参数： isSuccess：红外初始化结果
功能： 响应红外初始化结果信号
*************************************************************/
void GuideInfraredPDAView::onInfraredInitResult(bool bSuccess)
{
    logWarning(QString("infrared init result: %1.").arg(bSuccess).toLatin1().data());
    if (m_bInitFlag)
    {
        m_bInitFlag = false;
        emit sigInfraredInitFinished();
        if (bSuccess)
        {
            onInitSuccess();
        }
        else
        {
            onInitFail();
        }
    }
}

/*************************************************************
 * 功能：红外设备状态改变槽函数
 * 输入参数：
 *         bConnectState：连接状态
 * ************************************************************/
void GuideInfraredPDAView::onInfraredDevState(bool bConnectState)
{
    if (!bConnectState)
    {
        // 如果断开连接
        showMsgBox(QObject::tr("Device disconnected!"), MsgBox::WARNING);
    }
}

/*************************************************
输入参数： e：显示事件
功能： 显示事件处理
*************************************************************/
void GuideInfraredPDAView::showEvent(QShowEvent *e)
{
    GuideInfraredViewBase::showEvent(e);
    StatusBar::instance()->hide();

    if(!m_bPlaybacking)
    {
        onRecoverSampleView();
    }
}

/*************************************************
输入参数： e：按键事件
功能： 按键事件处理
*************************************************************/
void GuideInfraredPDAView::keyPressEvent(QKeyEvent *e)
{
    if (m_bSave || m_bPdaWaiting)
    {
        // 正在保存或即将要关闭界面，禁止响应所有按键操作
        return;
    }

    if (Qt::Key_Escape == e->key())
    {
        if (m_bSave)
        {
            logInfo("saving data...");
            return;
        }

        m_bExit = true;
//        if (GuideClientManager::instance()->isWaitForClose())
//        {
//            MsgBox* pMsgBox = new MsgBox(MsgBox::INFORMATION);
//            pMsgBox->setInfo("", QObject::trUtf8("Service exiting, please wait..."), MsgBox::NOBUTTON);
//            QTimer::singleShot(10000, pMsgBox, SLOT(accept()));
//            rotateMsgBox(pMsgBox);
//        }
        close();
    }
    else if (Qt::Key_F1 == e->key())       // S键
    {
        onPressSKey();
    }
    else
    {
        GuideInfraredViewBase::keyPressEvent(e);
    }
}

/*************************************************
功能： 处理窗口关闭事件
*************************************************************/
void GuideInfraredPDAView::closeEvent(QCloseEvent* event)
{
    GuideInfraredViewBase::closeEvent(event);
    StatusBar::instance()->show();
}

/*************************************************
输入参数： ucID：按钮ID
功能： 按钮响应处理
*************************************************************/
void GuideInfraredPDAView::onButtonPressed(UINT8 ucID)
{
    if (m_bSave || m_bPdaWaiting)
    {
        // 正在保存或即将要关闭界面，禁止响应所有按键操作
        return;
    }

    if (!m_pButtonBar)
    {
        return;
    }

    if (InfraredControlButton* pButton = m_pButtonBar->buttons(ucID))
    {
        if (!pButton->isEnabled())
        {
            logError("current press button is not enable.");
            return;
        }
    }
    else
    {
        return;
    }

    switch (ucID)
    {
    case GuideInfrared::BUTTON_STATUS:
    {
        readDataCtrl();
    }
        break;
    case GuideInfrared::BUTTON_SAVA_DATA:     // 保存数据
    {
        saveData(true);
    }
        break;
    case GuideInfrared::BUTTON_LOAD_DATA:
    {
        loadData();
    }
        break;
    case GuideInfrared::BUTTON_DELETE_DATA:
    {
        deleteData();
    }
        break;
    case GuideInfrared::BUTTON_AUTO_FOCUS:
    {
        if (!GuideClientManager::instance()->ctrlFocus(Guide::FOCUS_AUTO))
        {
            showMsgBox(QObject::tr("Autofocus failed!"), MsgBox::WARNING);
        }
    }
        break;
    case GuideInfrared::BUTTON_CLOSE_FOCUS_FINE_TUNING:
    {
        if (!GuideClientManager::instance()->ctrlFocus(Guide::FOCUS_NEAR_FINE))
        {
            showMsgBox(QObject::tr("Close focus fine-tuning failed!"), MsgBox::WARNING);
        }
    }
        break;
    case GuideInfrared::BUTTON_FAR_FOCUS_FINE_TUNING:
    {
        if (!GuideClientManager::instance()->ctrlFocus(Guide::FOCUS_FAR_FINE))
        {
            showMsgBox(QObject::tr("Far focus fine-tuning failed!"), MsgBox::WARNING);
        }
    }
        break;
    case GuideInfrared::BUTTON_ADD:
    {
        addTestData();
    }
        break;
    case GuideInfrared::BUTTON_EXIT://退出
    {
        if(m_bPdaWaiting)
        {
            logInfo("saving data...");
        }
        else
        {
            close();
        }
    }
        break;
    default:
        GuideInfraredViewBase::onButtonPressed(ucID);
        break;
    }
}

/*************************************************
功能： 载入数据
*************************************************************/
void GuideInfraredPDAView::loadData()
{
    QString filePath = DATA_STORAGE_PATH + "/" + INFRARED_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if (!infoList.isEmpty())
    {
        m_bIsReadDataBeforePlayback = m_bIsReadData;
        suspend();

        RotatePlayBackView *pView = new RotatePlayBackView(filePath, INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_PLAYBACK), INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_LOAD_DATA), false, false, false, false);
        GuideInfraredPlaybackView *pPlayBackView = new GuideInfraredPlaybackView();

        connect(pPlayBackView, SIGNAL(sigPlayNextFile()),   pView, SLOT(onPlayNextFile()));
        connect(pPlayBackView, SIGNAL(sigPlayLastFile()),   pView, SLOT(onPlayLastFile()));
        connect(pPlayBackView, SIGNAL(sigExit()), pView, SLOT(onExitPlayBack()));
        connect(pPlayBackView, SIGNAL(destroyed(QObject *)), this, SLOT(onPlaybackFinished()));

        pView->addPlayback(INFRARED_FILE_NAME_SUFFIX, pPlayBackView);
        //pView->exec();
        pView->show();      // for test, wujun
    }
    else
    {
        MsgBox *pMsgBox = new MsgBox(MsgBox::WARNING);
        pMsgBox->setInfo("", QObject::trUtf8("No file!"));
        rotateMsgBox(pMsgBox);
    }
}

/*************************************************
功能： 暂停
*************************************************************/
void GuideInfraredPDAView::suspend()
{
    if (m_pGuideInfraredImagingView)
    {
        m_pGuideInfraredImagingView->suspend();
    }
    // 停止红外数据采集
    onStopSample();
}

/*************************************************
功能： 响应创建分析图形信号
*************************************************************/
void GuideInfraredPDAView::onCreateShape()
{
    // 停止红外数据采集
    onStopSample();
}

/*************************************************
功能： 恢复
*************************************************************/
void GuideInfraredPDAView::resume()
{
    m_bIsReadData = true;

    if (m_pGuideInfraredImagingView)
    {
        m_pGuideInfraredImagingView->resume();
    }

    if (m_pButtonBar)
    {
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_STATUS))
        {
            pButton->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_FROZEN));
        }
    }

    GuideClientManager::instance()->ctrlLiveVideo(Guide::CTRL_RESUME);
}

/*************************************************
功能： 停止采集
*************************************************************/
void GuideInfraredPDAView::onStopSample()
{
    if (m_bIsReadData)
    {
        m_bIsReadData = false;

        if (m_pButtonBar)
        {
            if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_STATUS))
            {
                pButton->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_RECOVER));
            }
        }

        GuideClientManager::instance()->ctrlLiveVideo(Guide::CTRL_FREEZE);
    }
}

/*************************************************
功能： 重新开始采集
*************************************************************/
void GuideInfraredPDAView::onRestartSample()
{
    m_bIsReadData = true;

    if (m_pButtonBar)
    {
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_STATUS))
        {
            pButton->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_FROZEN));
        }
    }

    GuideClientManager::instance()->ctrlLiveVideo(Guide::CTRL_RESUME);
}

/*************************************************
功能： 处理S键
*************************************************************/
void GuideInfraredPDAView::onPressSKey()
{
    saveData(true);
}

/*************************************************
功能： 控制红外数据读取
*************************************************************/
void GuideInfraredPDAView::readDataCtrl()
{
    if (m_bIsReadData)
    {
        suspend();
    }
    else
    {
        resume();
    }
}

/*************************************************
功能： 保存红外数据
*************************************************************/
void GuideInfraredPDAView::saveData(bool bSaveJpeg)
{
    if(m_bSave)
    {
        logInfo("saving data...");
        return;
    }

    if (NULL == m_pGuideInfraredImagingView)
    {
        return;
    }

    PDATask* pTask = PDAService::instance()->currentTask();
    if(!pTask)
    {
        logWarning("pTask == NULL");
        return;
    }

    m_bSave = true;
    m_bSaveImage = bSaveJpeg;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    SystemSet::TemperatureUnitOption eUnit = (SystemSet::TemperatureUnitOption) pConfig->value( APPConfig::KEY_TEMPERATURE_UNIT ).toInt();

    if (m_pGuideInfraredImagingView->isDataEmpty())
    {
        MsgBox *pMsgBox = new MsgBox(MsgBox::INFORMATION);
        pMsgBox->setInfo("", QObject::trUtf8("Empty data, save failure!"));
        rotateMsgBox(pMsgBox);
        m_bSave = false;
        return;
    }

    suspend();

    showWaitingDialog(QObject::trUtf8("Saving data ..."));
    logError(QString("GuideInfraredPDAView::saveData start"));
#ifdef DISPLAY_MODE
    m_stSavePicturesInfo = Guide::SavePicturesInfo();
    // 单独红外模式可见光照片需要调用拍照借口获取
    GuideClientManager::instance()->ctrlSavePictures(static_cast<Guide::SwitchValue>(m_bLedFillLight), m_stSavePicturesInfo);
#endif

    FrameInfo stFrameInfo;
    memset(&stFrameInfo, 0, sizeof(FrameInfo));
    m_pGuideInfraredImagingView->getFrameInfo(stFrameInfo);
    Params stParams;
    memset(&stParams, 0, sizeof(Params));

    m_stSaveGuideInfraredDataInfo = GuideInfraredDataInfo();

    // 数码相机模式不保存红外数据
    if (GuideInfrared::DISPLAY_DIGITAL_CAMERA != m_eGuideInfraredDisplayMode)
    {
        if (stFrameInfo.frame_sz > 0)
        {
            m_stSaveGuideInfraredDataInfo.iRawDataLen = stFrameInfo.frame_sz * sizeof(INT16);
            m_stSaveGuideInfraredDataInfo.pucRawData = new quint8[m_stSaveGuideInfraredDataInfo.iRawDataLen];

#ifdef SAVE_GUIDE_DATA
            QByteArray qbaMeasureParamData;
            m_pGuideInfraredImagingView->getData((INT16*)m_stSaveGuideInfraredDataInfo.pucRawData,
                                                 qbaMeasureParamData);

            m_stSaveGuideInfraredDataInfo.iMeasureParamDataLength = qbaMeasureParamData.size();
            if (m_stSaveGuideInfraredDataInfo.iMeasureParamDataLength > 0)
            {
                m_stSaveGuideInfraredDataInfo.pMeasureParamData = new char[m_stSaveGuideInfraredDataInfo.iMeasureParamDataLength];
                memcpy(m_stSaveGuideInfraredDataInfo.pMeasureParamData, qbaMeasureParamData.data(), m_stSaveGuideInfraredDataInfo.iMeasureParamDataLength);
            }

//                stGuideInfraredDataInfo.iTempDataLen = stFrameInfo.frame_sz;
//                stGuideInfraredDataInfo.pTemperatureData = new float[stFrameInfo.frame_sz];
//                m_pGuideInfraredImagingView->getTemperatureData(stGuideInfraredDataInfo.pTemperatureData);
#else
            m_pGuideInfraredImagingView->getFlirRawData((INT16*)m_stSaveGuideInfraredDataInfo.pucRawData, stParams);
#endif

        }
    }

    Guide::TemperatureParameterInfo stTemperatureParameterInfo;
    m_pGuideInfraredImagingView->getTemperatureParameterInfo(stTemperatureParameterInfo);

    // 当前温度信息(max,min,avg,...)
    TemperatureInfo tmpInfo = m_pGuideInfraredImagingView->temperatureInfomation();

    // TODO: get info and save as FFF
    m_stSaveGuideInfraredDataInfo.strSubstationName           = m_stTestPointInfo.strStationName;
    m_stSaveGuideInfraredDataInfo.strTestedDevName            = m_stTestPointInfo.strDeviceName;
    m_stSaveGuideInfraredDataInfo.dateTime                    = QDateTime::currentDateTime();
    m_stSaveGuideInfraredDataInfo.uiWidth                     = stFrameInfo.size_x;
    m_stSaveGuideInfraredDataInfo.uiHeight                    = stFrameInfo.size_y;
    m_stSaveGuideInfraredDataInfo.ucPixelCount                = sizeof(unsigned short);
    m_stSaveGuideInfraredDataInfo.ucPixelConvertToTemperature = 1;
    m_stSaveGuideInfraredDataInfo.fFactor                     = 0.04;      //todo
    m_stSaveGuideInfraredDataInfo.fOffset                     = 273.15;    //todo
    if(eUnit == SystemSet::TEMPERATURE_UNIT_CENTIGRADE)//摄氏度
    {
        m_stSaveGuideInfraredDataInfo.fMaxTemperature = tmpInfo.max;
        m_stSaveGuideInfraredDataInfo.fMinTemperature = tmpInfo.min;
        m_stSaveGuideInfraredDataInfo.fAvgTemperature = tmpInfo.avg;
        m_stSaveGuideInfraredDataInfo.fRefTemperature = stTemperatureParameterInfo.dBGTemperature;   //参考温度需要界面输入
    }
    else        //华氏度
    {
        //double dValue = Module::Centigrade2Fahrenheit(tmpInfo.max);
        m_stSaveGuideInfraredDataInfo.fMaxTemperature = tmpInfo.max;

        //dValue = Module::Centigrade2Fahrenheit(tmpInfo.min);
        m_stSaveGuideInfraredDataInfo.fMinTemperature = tmpInfo.min;

        //dValue = Module::Centigrade2Fahrenheit(tmpInfo.avg);
        m_stSaveGuideInfraredDataInfo.fAvgTemperature = tmpInfo.avg;

        //dValue = Module::Centigrade2Fahrenheit(stInfraredDataInfo.fRefTemperature);
        m_stSaveGuideInfraredDataInfo.fRefTemperature = stTemperatureParameterInfo.dBGTemperature;     //参考温度需要界面输入
    }

#ifdef _MALAYSIA_VERSION_DEFINED_
    //马来西亚客户定制化需求
    stInfraredDataInfo.fDifTemperature = Module::dealFloatPrecision(stInfraredDataInfo.fMaxTemperature - stInfraredDataInfo.fMinTemperature, m_iPrecious);
#else
    m_stSaveGuideInfraredDataInfo.fDifTemperature = Module::dealFloatPrecision(m_stSaveGuideInfraredDataInfo.fMaxTemperature - m_stSaveGuideInfraredDataInfo.fRefTemperature, m_iPrecious);
#endif
    m_stSaveGuideInfraredDataInfo.RadiationRate             = stTemperatureParameterInfo.dEmissivity;

    //大气温度、测试距离和反射温度 辐射率只保留小数点后一位 解决上位机显示问题
    stTemperatureParameterInfo.dDistance = Module::dealDoublePrecision(stTemperatureParameterInfo.dDistance, m_iPrecious);
    m_stSaveGuideInfraredDataInfo.RadiationRate = Module::dealDoublePrecision(m_stSaveGuideInfraredDataInfo.RadiationRate, 2);

    m_stSaveGuideInfraredDataInfo.AtmosphericTemperature = Module::dealDoublePrecision(stParams.stObj.dblAtmTemp, m_iPrecious);
    m_stSaveGuideInfraredDataInfo.ReflectedTemperature = Module::dealDoublePrecision(stParams.stObj.dblAmbTemp, m_iPrecious);
    m_stSaveGuideInfraredDataInfo.ExternalOpticalTemperatue = Module::dealDoublePrecision(stParams.stObj.dblExtOptTemp, m_iPrecious);
    m_stSaveGuideInfraredDataInfo.ExternalOpticalRate = stParams.stObj.dblExtOptTransm;

    m_stSaveGuideInfraredDataInfo.TestDistance              = stTemperatureParameterInfo.dDistance;
    m_stSaveGuideInfraredDataInfo.RelativeHumidity = stTemperatureParameterInfo.dHumidity;

    m_stSaveGuideInfraredDataInfo.TauPlanckConstiR = stParams.stPlanck.iR;
    m_stSaveGuideInfraredDataInfo.TauPlanckConstdblB = stParams.stPlanck.dblB;
    m_stSaveGuideInfraredDataInfo.TauPlanckConstdblF = stParams.stPlanck.dblF;
    m_stSaveGuideInfraredDataInfo.TauPlanckConstdblO = stParams.stPlanck.dblO;

    m_stSaveGuideInfraredDataInfo.SpectralResponsedblX = stParams.stSpec.dblX;
    m_stSaveGuideInfraredDataInfo.SpectralResponsedblAlpha1 = stParams.stSpec.dblAlpha1;
    m_stSaveGuideInfraredDataInfo.SpectralResponsedblAlpha2 = stParams.stSpec.dblAlpha2;
    m_stSaveGuideInfraredDataInfo.SpectralResponsedblBeta1 = stParams.stSpec.dblBeta1;
    m_stSaveGuideInfraredDataInfo.SpectralResponsedblBeta2 = stParams.stSpec.dblBeta2;
    m_stSaveGuideInfraredDataInfo.iColorType = (quint8)GuideColorType2ColorType(m_pGuideInfraredImagingView->getColorType());

    DataMapHead stDataMapHead;
    stDataMapHead.eDataPrimitiveType = DataFileNS::DATA_TYPE_UNUSED;
    stDataMapHead.generationDateTime = m_stSaveGuideInfraredDataInfo.dateTime;
    stDataMapHead.strSubstationName = m_stTestPointInfo.strStationName;
    stDataMapHead.strSubstationNumber = m_stTestPointInfo.strStationID;
    stDataMapHead.strDeviceName = m_stTestPointInfo.strDeviceName;
    stDataMapHead.strDeviceNumber = m_stTestPointInfo.strDeviceID;
    stDataMapHead.strTestPointName = m_stTestPointInfo.strTestPointName;
    stDataMapHead.strTestPointNumber = m_stTestPointInfo.strTestPointID;
    stDataMapHead.eTestLocation = DataFileNS::TEST_LOCATION_UNRECORD;

    QFuture<QString> future = QtConcurrent::run(pfnSaveDataFun, this, pTask, m_stSaveGuideInfraredDataInfo, stDataMapHead);
    m_saveDataFutureWatcher.setFuture(future);
}

/*************************************************
功能： 删除数据
*************************************************************/
void GuideInfraredPDAView::deleteData()
{
    QStringList nameFilters;
    nameFilters << INFRARED_FILE_NAME_SUFFIX;

    QString filePath = DATA_STORAGE_PATH + "/" + INFRARED_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if (!infoList.isEmpty())
    {
        RotateDeleteDataView *pView = new RotateDeleteDataView(filePath, INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_DELETE_DATA), nameFilters);
        QStringList lstSuffix;
        lstSuffix << JPG_FILE_NAME_SUFFIX;
        pView->setRelatedSuffix(INFRARED_FILE_NAME_SUFFIX, lstSuffix);
        QWidget *pFocusWidget = this->focusWidget();
        if ( pFocusWidget )
        {
            pFocusWidget->clearFocus();
        }
        pView->setFocus();
        pView->exec();
        if ( pFocusWidget )
        {
            pFocusWidget->setFocus();
        }
    }
    else
    {
        MsgBox *pMsgBox = new MsgBox(MsgBox::WARNING);
        pMsgBox->setInfo("", QObject::trUtf8("No file!"));
        rotateMsgBox(pMsgBox);
    }
}

/*************************************************
功能： 禁用部分按钮
*************************************************************/
void GuideInfraredPDAView::disableButtons()
{
    if (!m_pButtonBar)
    {
        return;
    }

    QList<InfraredControlButton *> buttons = m_pButtonBar->buttonList();
    for (int i = 0, iSize = buttons.size(); i < iSize; ++i)
    {
        if (buttons[i])
        {
            if (buttons[i]->id() != GuideInfrared::BUTTON_LOAD_DATA)
            {
                buttons[i]->setEnabled(false);
            }
        }
    }
}

/*************************************************
功能： 使能部分按钮
*************************************************************/
void GuideInfraredPDAView::enableButtons()
{
    if (!m_pButtonBar)
    {
        return;
    }

    QList<InfraredControlButton *> buttons = m_pButtonBar->buttonList();
    for (int i = 0, iSize = buttons.size(); i < iSize; ++i)
    {
        if (buttons[i])
        {
            if (buttons[i]->id() != GuideInfrared::BUTTON_LOAD_DATA)
            {
                buttons[i]->setEnabled(true);
            }
        }
    }

    if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_DELETE_ALL_SHAPE))
    {
        pButton->setEnabled(false);
    }
}

/*************************************************************
 * 功能：更新按钮状态
 * ************************************************************/
void GuideInfraredPDAView::updateButtonState()
{
    if (NULL == m_pButtonBar)
    {
        return;
    }

    bool bInfraredEnable = m_eGuideInfraredDisplayMode != GuideInfrared::DISPLAY_DIGITAL_CAMERA;

    if (bInfraredEnable)
    {
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_ANALYSE_SHAPE))
        {
            pButton->setEnabled(true);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_SET_PARAM))
        {
            pButton->setEnabled(true);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_CLOSE_FOCUS_FINE_TUNING))
        {
            pButton->setEnabled(true);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_FAR_FOCUS_FINE_TUNING))
        {
            pButton->setEnabled(true);
        }
        //m_pButtonBar->buttons(GuideInfrared::BUTTON_DELETE_ALL_SHAPE)->setEnabled(true);
    }
    else
    {
        // 禁用的时候要删除所有分析图形
        if (m_pGuideInfraredImagingView)
        {
            m_pGuideInfraredImagingView->deleteAll();
        }

        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_ANALYSE_SHAPE))
        {
            pButton->setEnabled(false);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_DELETE_ALL_SHAPE))
        {
            pButton->setEnabled(false);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_SET_PARAM))
        {
            pButton->setEnabled(false);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_CLOSE_FOCUS_FINE_TUNING))
        {
            pButton->setEnabled(false);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_FAR_FOCUS_FINE_TUNING))
        {
            pButton->setEnabled(false);
        }
    }
}

/*************************************************
功能： 显示初始化对话框
*************************************************************/
void GuideInfraredPDAView::showInitDialog()
{
    if (m_bInitFlag)
    {
        QString strInfo = QObject::trUtf8("Connecting to infrared device ...");
        MsgBox *pInitMsgBox = new MsgBox(MsgBox::INFORMATION);
        pInitMsgBox->setInfo("", strInfo, MsgBox::OK);

        RotateDialog *pInitingDialog = new RotateDialog(pInitMsgBox);
        pInitingDialog->setWindowModality(Qt::ApplicationModal);
        pInitingDialog->setAttribute(Qt::WA_DeleteOnClose);
        pInitingDialog->setFocusPolicy(Qt::StrongFocus);
        pInitingDialog->show();

        connect(this, SIGNAL(sigInfraredInitFinished()), pInitingDialog, SLOT(accept()));
    }
}

/************************************************
 * 功能:槽函数，起动采样
 ************************************************/
void GuideInfraredPDAView::onRecoverSampleView()
{
    bool bPrePlaybackingState = m_bPlaybacking;
    m_bPlaybacking = false;
    setPlaybackViewTag(false);

    // 清空回放显示的标题，并隐藏回放界面
    if (!m_qstrTestDataFilePath.isEmpty())
    {
        m_qstrTestDataFilePath.clear();
        if (m_pPlaybackInfraredImagingView)
        {
            m_pPlaybackInfraredImagingView->showFileName(m_qstrTestDataFilePath);
        }
        if (m_pPlaybackView)
        {
            m_pPlaybackView->hide();
        }
    }

    if(m_pButtonBar)
    {
        m_pButtonBar->setFocus();
        m_pButtonBar->activeFirst();

        bool bAddEnable = m_stTestData.bIsBgn ? false : true;
        m_pButtonBar->buttons(GuideInfrared::BUTTON_ADD)->setEnabled(bAddEnable);
        m_pButtonBar->buttons(GuideInfrared::BUTTON_EXIT)->setEnabled(true);
    }

    if (!GuideClientManager::instance()->isConnected())
    {
        Guide::GuideDevInfo stGuideDevInfo;
        GuideClientManager::instance()->connectInfraredDev(stGuideDevInfo);

        // 如果是回放直接显示不需要等待1s
        if (bPrePlaybackingState)
        {
            showInitDialog();
        }
        else
        {
            //显示初始化对话框，通过信号的方式，直接调用有焦点问题
            //正常情况下不显示
            QTimer::singleShot(1000, this, SLOT(showInitDialog()));
        }
    }
    else
    {
        onInitSuccess();
    }
}

/************************************************
 * 功能：将新增测试项界面的信息写入pdaTask中
 * 输入参数：
 *         stAddTestData：新增测试项数据
 ************************************************/
void GuideInfraredPDAView::onAddTestData(struct_AddingTestData& stAddTestData)
{
    for(int i = 0, iSize = stAddTestData.evType.size(); i < iSize; ++i)
    {
        ItemTestData stTestData;
        stTestData.bIsBgn = m_stTestData.bIsBgn;
        stTestData.eDataType = stAddTestData.evType.at(i);
        stTestData.eDataUnit = m_stTestData.eDataUnit;

        PDAService::instance()->currentTask()->addTestData(stTestData);
    }
}

void GuideInfraredPDAView::onSaveDataFinished()
{
    QString qstrFilePath = m_saveDataFutureWatcher.result();
    if (m_bSaveImage && !qstrFilePath.isEmpty())
    {
        int nPos = qstrFilePath.lastIndexOf('.');
        QString qstrCommonFileName = qstrFilePath.left(nPos);
        if (GuideInfrared::DISPLAY_PICTURE_IN_PICTURE == m_eGuideInfraredDisplayMode)
        {
            // 画中画模式保存画中画截图和可见光图片
            QString qstrPicInPicFileFullName = qstrCommonFileName + "_pip" + JPG_FILE_NAME_SUFFIX;
            QPixmap pix = QPixmap::grabWidget(m_pGuideInfraredImagingView, QRect(0, 0, IMAGE_SCALED_WIDTH, IMAGE_SCALED_HEIGHT));
            if (!pix.save(qstrPicInPicFileFullName, "JPG"))
            {
                qDebug() << "----fail to save " << qstrPicInPicFileFullName;
            }
        }
        else if (GuideInfrared::DISPLAY_INFRARED == m_eGuideInfraredDisplayMode)
        {
            // 红外模式只存储红外+可见光图片
            QString strJpegFileFullName = qstrCommonFileName + "_infrared" + JPG_FILE_NAME_SUFFIX;
            m_pGuideInfraredImagingView->saveInfraredPictureAsJpeg(strJpegFileFullName);
        }

#ifdef DISPLAY_MODE
        // 都存储拍照的高清数据
        if (!m_stSavePicturesInfo.qbaVisiblePictureData.isEmpty())
        {
            QString strVisibleLightFileFullName = qstrCommonFileName + "_vl" + JPG_FILE_NAME_SUFFIX;
            QImage visibleLightImage = QImage::fromData(m_stSavePicturesInfo.qbaVisiblePictureData, "JPG");
            visibleLightImage.save(strVisibleLightFileFullName, "JPG");
        }
#endif
    }

    if(m_stSaveGuideInfraredDataInfo.pucRawData)
    {
        delete [] m_stSaveGuideInfraredDataInfo.pucRawData;
        m_stSaveGuideInfraredDataInfo.pucRawData = NULL;
    }

    if(m_stSaveGuideInfraredDataInfo.pucIRphotoData)
    {
        delete [] m_stSaveGuideInfraredDataInfo.pucIRphotoData;
        m_stSaveGuideInfraredDataInfo.pucIRphotoData = NULL;
    }

    if(qstrFilePath.isEmpty())
    {
        MsgBox* pMsgBox = new MsgBox(MsgBox::INFORMATION);
        pMsgBox->setInfo("", QObject::trUtf8("Save failure!"));
        rotateMsgBox(pMsgBox);
        resume();
    }
    else
    {
        delayToClose();
    }

    emit sigSaveFinished();

    m_bSave = false;
}

/*************************************************
功能： 处理初始化成功结果
*************************************************************/
void GuideInfraredPDAView::onInitSuccess()
{
    // 回放的时候不需要
    if (m_bPlaybacking)
    {
        return;
    }

    Guide::TemperatureParameterInfo stTemperatureParameterInfo;
    if (GuideClientManager::instance()->getTemperatureParamInfo(stTemperatureParameterInfo))
    {
        if (m_pGuideInfraredImagingView)
        {
            m_pGuideInfraredImagingView->setTemperatureParameterInfo(stTemperatureParameterInfo);
        }

        enableButtons();
        resume();
    }
    else
    {
        MsgBox *pMsgBox = new MsgBox(MsgBox::INFORMATION);
        pMsgBox->setInfo("", QObject::trUtf8("Failed to initialize parameters, do you want to exit the page?"), MsgBox::OK | MsgBox::CANCEL);
        if(MsgBox::OK == rotateMsgBox(pMsgBox))
        {
            //onExitInfrared();
            close();
        }
    }
}

/*************************************************
功能： 处理初始化失败结果
*************************************************************/
void GuideInfraredPDAView::onInitFail()
{
    showMsgBox(QObject::trUtf8("Initialization failed!"), MsgBox::WARNING);
}

/************************************************
 * 功能: 回放数据文件里的测试数据
 * 输入参数:
 *         qstrFile---数据文件存放路径
 ************************************************/
void GuideInfraredPDAView::playBackTestedData(const QString& qstrFile)
{
    m_bPlaybacking = true;

    InfraredDataInfo stData;
    InfraredDataSave dataSave;

    qDebug() << "GuideInfraredPDAView::playBackTestedData: " << qstrFile;

    if (HC_SUCCESS == dataSave.getDataFromFile(qstrFile, &stData))
    {
        if (NULL == m_pPlaybackInfraredImagingView)
        {
            m_pPlaybackInfraredImagingView = new InfraredImagingView();
            QSize guideInfraredImagingViewSize = m_pGuideInfraredImagingView->sizeHint();

            m_pPlaybackScene = new QGraphicsScene(this);
            m_pPlaybackScene->setSceneRect(0, 0, guideInfraredImagingViewSize.width(), guideInfraredImagingViewSize.height());
            m_pPlaybackScene->addWidget(m_pPlaybackInfraredImagingView);
            m_pPlaybackInfraredImagingView->resize(guideInfraredImagingViewSize);

            m_pPlaybackView = new QGraphicsView(m_pPlaybackScene, this);
            m_pPlaybackView->setContentsMargins(0, 0, 0, 0);
            m_pPlaybackView->setFrameStyle(QFrame::NoFrame);
            m_pPlaybackView->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
            m_pPlaybackView->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
            m_pPlaybackView->setResizeAnchor(QGraphicsView::AnchorViewCenter);
            m_pPlaybackView->setFocusPolicy(Qt::NoFocus);
            m_pPlaybackView->rotate(VIEW_ROTATE_ANGLE);
            m_pPlaybackView->setFixedSize(guideInfraredImagingViewSize.height(), guideInfraredImagingViewSize.width());
        }

        m_pPlaybackInfraredImagingView->deleteAll(true);

        FrameInfo info;
        info.size_x = stData.uiWidth;
        info.size_y = stData.uiHeight;
        info.frame_sz = stData.uiWidth * stData.uiHeight * stData.ucPixelCount;

        Params stParams;
        stParams.stObj.dblEmissivity   = stData.RadiationRate;
        stParams.stObj.dblObjDistance  = stData.TestDistance;
        stParams.stObj.dblAtmTemp      = stData.AtmosphericTemperature;
        stParams.stObj.dblAmbTemp      = stData.ReflectedTemperature;
        stParams.stObj.dblExtOptTemp   = stData.ExternalOpticalTemperatue;
        stParams.stObj.dblExtOptTransm = stData.ExternalOpticalRate;
        stParams.stObj.dblRelHum       = stData.RelativeHumidity;

        stParams.stPlanck.iR   = stData.TauPlanckConstiR;
        stParams.stPlanck.dblB = stData.TauPlanckConstdblB;
        stParams.stPlanck.dblF = stData.TauPlanckConstdblF;
        stParams.stPlanck.dblO = stData.TauPlanckConstdblO;

        stParams.stSpec.dblX      = stData.SpectralResponsedblX;
        stParams.stSpec.dblAlpha1 = stData.SpectralResponsedblAlpha1;
        stParams.stSpec.dblAlpha2 = stData.SpectralResponsedblAlpha2;
        stParams.stSpec.dblBeta1  = stData.SpectralResponsedblBeta1;
        stParams.stSpec.dblBeta2  = stData.SpectralResponsedblBeta2;

        TemperatureInfo stTmpInfo;
        stTmpInfo.max = stData.fMaxTemperature;
        stTmpInfo.min = stData.fMinTemperature;
        stTmpInfo.avg = stData.fAvgTemperature;

        m_pPlaybackInfraredImagingView->setColorType(static_cast<ColorType>(stData.iColorType));
        m_pPlaybackInfraredImagingView->setReferenceTemp(stData.fRefTemperature);
        m_pPlaybackInfraredImagingView->playBack((unsigned short *)(stData.aucInfraredData), info, stParams);
        m_pPlaybackInfraredImagingView->setTempInfo(stTmpInfo);
        QString strTitle = qstrFile.mid(qstrFile.lastIndexOf('/') + 1);
        m_pPlaybackInfraredImagingView->showFileName(strTitle);
    }

    m_pPlaybackView->raise();
}

void GuideInfraredPDAView::playBackTestedDataEx(const QString &qstrFile)
{
    qDebug() << "GuideInfraredPDAView::playBackTestedData1";
    GuideInfraredDataInfo stData;
    GuideInfraredDataSave dataSave;
    m_bPlaybacking = true;

    if (HC_SUCCESS == dataSave.getDataFromFile(qstrFile, &stData))
    {
        qDebug() << "GuideInfraredPDAView::playBackTestedData2";
        GuideInfraredDataManage::GuidePlaybackData stGuidePlaybackData;
        stGuidePlaybackData.iImageWidth = stData.uiWidth;
        stGuidePlaybackData.iImageHeight = stData.uiHeight;

        stGuidePlaybackData.stTemperatureParameterInfo.dEmissivity = stData.RadiationRate;
        stGuidePlaybackData.stTemperatureParameterInfo.dDistance = stData.TestDistance;
        stGuidePlaybackData.stTemperatureParameterInfo.dBGTemperature = stData.fRefTemperature;
        stGuidePlaybackData.stTemperatureParameterInfo.dHumidity = stData.RelativeHumidity;

        TemperatureInfo stTmpInfo;
        stTmpInfo.max = stData.fMaxTemperature;
        stTmpInfo.min = stData.fMinTemperature;
        stTmpInfo.avg = stData.fAvgTemperature;

        Guide::PaletteVal ePaletteVal = ColorType2GuideColorType(static_cast<ColorType>(stData.iColorType));
        m_pGuideInfraredImagingView->setColorType(ePaletteVal, false);

        int nPos = qstrFile.lastIndexOf('.');
        GuideInfrared::GuideInfraredDisplayMode eGuideInfraredDisplayMode = GuideInfrared::DISPLAY_INFRARED;

        // 如果没有红外数据则是数码相机模式
        if (!stData.pucRawData)
        {
            eGuideInfraredDisplayMode = GuideInfrared::DISPLAY_DIGITAL_CAMERA;
        }
        else
        {
            stGuidePlaybackData.pY16Data = (INT16*)(stData.pucRawData);
            stGuidePlaybackData.iY16DataSize = stData.iRawDataLen;

            // 如果有红外照片则是红外模式，没有就是画中画模式
            QString qstrInfraredFileFullName = qstrFile.left(nPos) + "_infrared" + JPG_FILE_NAME_SUFFIX;
            qDebug() << "GuideInfraredPDAView::playBackTestedData: " << qstrInfraredFileFullName;
            if (!FileOperUtil::checkFileOrDirExist(qstrInfraredFileFullName))
            {
                eGuideInfraredDisplayMode = GuideInfrared::DISPLAY_PICTURE_IN_PICTURE;
            }
        }

#ifdef DISPLAY_MODE
        // 读取可见光照片数据
        QByteArray qbaVisibleData;
        QString strVisibleLightFileFullName = qstrFile.left(nPos) + "_vl" + JPG_FILE_NAME_SUFFIX;
        FileOperUtil::readFile(strVisibleLightFileFullName, qbaVisibleData);

        if (!qbaVisibleData.isEmpty())
        {
            stGuidePlaybackData.qbaVisibleData = qbaVisibleData;
        }
#endif

        m_pGuideInfraredImagingView->setPlaybackData(stGuidePlaybackData);
        m_pGuideInfraredImagingView->setDisplayMode(eGuideInfraredDisplayMode);
        m_pGuideInfraredImagingView->setTempInfo(stTmpInfo);
        QString qstrTitle = qstrFile.mid(qstrFile.lastIndexOf('/') + 1);
        m_pGuideInfraredImagingView->showFileName(qstrTitle);

        if (GuideInfrared::DISPLAY_INFRARED == eGuideInfraredDisplayMode)
        {
            m_pGuideInfraredImagingView->playbackInfraredPhoto();
        }

        if (stData.pucRawData)
        {
            delete[] stData.pucRawData;
        }
        if (stData.pucVisLightData)
        {
            delete[] stData.pucVisLightData;
        }
        if (stData.pucIRphotoData)
        {
            delete[] stData.pucIRphotoData;
        }
    }
    qDebug() << "GuideInfraredPDAView::playBackTestedData3";
}

/*************************************************
功能： 弹出新增测试项窗口
*************************************************************/
void GuideInfraredPDAView::addTestData()
{
    PDAServiceNS::TestPointType eType = PDAService::instance()->currentTask()->currentTestType()->eTestPointType;
    AddTestDataDialog *pDialog = new AddTestDataDialog(eType);
    connect(pDialog, SIGNAL(sigAddingTestDataChanged(struct_AddingTestData&)), this, SLOT(onAddTestData(struct_AddingTestData&)));

    RotateDialog *pRotateDialog = new RotateDialog(pDialog);
    pRotateDialog->setWindowModality(Qt::ApplicationModal);
    pRotateDialog->setAttribute(Qt::WA_DeleteOnClose);
    pRotateDialog->setFocusPolicy(Qt::StrongFocus);
    pRotateDialog->show();
}

/*************************************************
函数名： onButtonValueChanged(int id, int iValue)
输入参数： id：按钮ID
          iValue：按钮值
功能： 响应按钮值变化事件
*************************************************************/
void GuideInfraredPDAView::onButtonValueChanged(int id, int iValue)
{
    if (m_bSave || m_bPdaWaiting)
    {
        // 正在保存或即将要关闭界面，禁止响应所有按键操作
        return;
    }

    if (!m_pButtonBar)
    {
        return;
    }

    if (InfraredControlButton* pButton = m_pButtonBar->buttons(id))
    {
        if (!pButton->isEnabled())
        {
            logError("current press button is not enable.");
            return;
        }
    }
    else
    {
        return;
    }

    switch (id)
    {
    case GuideInfrared::BUTTON_LASER_CONTROL:
    {
        ctrlLaser(static_cast<Guide::SwitchValue>(iValue));
    }
        break;
    case GuideInfrared::BUTTON_LED_FILL_LIGHT:
    {
        m_bLedFillLight = 1 == iValue;
    }
        break;
    case GuideInfrared::BUTTON_DISPLAY_MODE:
    {
        setDisplayMode(static_cast<GuideInfrared::GuideInfraredDisplayMode>(iValue));
    }
        break;
    case GuideInfrared::BUTTON_AUXILIARY_LIGHTING:
    {
        ctrlAuxiliaryLighting(static_cast<Guide::SwitchValue>(iValue));
    }
        break;
    default:
        GuideInfraredViewBase::onButtonValueChanged(id, iValue);
        break;
    }
}

/***********************************************************
 * 功能：激光控制
 * 输入参数：
 *      eSwitchValue：开或关
 * ********************************************************/
void GuideInfraredPDAView::ctrlLaser(Guide::SwitchValue eSwitchValue)
{
    if (!GuideClientManager::instance()->ctrlLaser(eSwitchValue))
    {
        if (Guide::SW_OFF == eSwitchValue)
        {
            showMsgBox(QObject::tr("Failed to turn off laser control!"), MsgBox::WARNING);
        }
        else
        {
            showMsgBox(QObject::tr("Failed to turn on laser control!"), MsgBox::WARNING);
        }

        // 恢复按钮状态
        if (m_pButtonBar)
        {
            if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_LASER_CONTROL))
            {
                Guide::SwitchValue eChangeValue = Guide::SW_OFF == eSwitchValue ? Guide::SW_ON : Guide::SW_OFF;
                pButton->setValue(eChangeValue);
            }
        }
    }
}

/***********************************************************
 * 功能：辅助照明控制
 * 输入参数：
 *      eSwitchValue：开或关
 * ********************************************************/
void GuideInfraredPDAView::ctrlAuxiliaryLighting(Guide::SwitchValue eSwitchValue)
{
    if (!GuideClientManager::instance()->ctrlAuxiliaryLighting(eSwitchValue))
    {
        if (Guide::SW_OFF == eSwitchValue)
        {
            showMsgBox(QObject::tr("Failed to turn off auxiliary lighting!"), MsgBox::WARNING);
        }
        else
        {
            showMsgBox(QObject::tr("Failed to turn on auxiliary lighting!"), MsgBox::WARNING);
        }

        // 恢复按钮状态
        if (m_pButtonBar)
        {
            if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_AUXILIARY_LIGHTING))
            {
                Guide::SwitchValue eChangeValue = Guide::SW_OFF == eSwitchValue ? Guide::SW_ON : Guide::SW_OFF;
                pButton->setValue(eChangeValue);
            }
        }
    }
}
