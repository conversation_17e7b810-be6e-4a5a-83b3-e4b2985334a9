/*
* Copyright (c) 2016.09，南京华乘电气科技有限公司
* All rights reserved.
*
* qrcodeview.cpp
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年8月15日
* 摘要：二维码扫描界面和功能实现

* 当前版本：1.0
*/


#include "qrcodeview.h"



/*************************************************
函数名： QrCodeView(const QString &qsTitle, QWidget *parent = NULL)
输入参数:qsTitle---标题
        parent---父控件
输出参数：NULL
返回值： NULL
功能： 构造函数
*************************************************************/
QrCodeView::QrCodeView(const QString &qsTitle, QWidget *parent):SampleChartView(qsTitle, parent)
{

}

/*************************************************
函数名： ~QrCodeView()
输入参数: NULL
输出参数：NULL
返回值： NULL
功能： 析构函数
*************************************************************/
QrCodeView::~QrCodeView()
{

}



/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void QrCodeView::onCommandButtonPressed( int id )
{
    Q_UNUSED(id);
    return;
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void QrCodeView::onButtonValueChanged( int id, int iValue )
{
    Q_UNUSED(id);
    Q_UNUSED(iValue);
    return;
}

