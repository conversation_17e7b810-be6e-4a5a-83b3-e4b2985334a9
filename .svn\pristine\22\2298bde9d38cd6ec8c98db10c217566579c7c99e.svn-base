#include <QTimer>
#include "pdapointlistview.h"
#include "window/Window.h"
#include "PDAUi/PDAUiView/PDAViewConfig.h"
#include "pdatestdataview.h"
#include "pdacurrenttestdataview.h"
#include "peripheral/peripheralservice.h"
#include "recordplay/RecordPlayService.h"
#include "systemsetting/systemsetservice.h"
#include "infrared/infraredservice.h"
#include "infrared/guide/guideclientmanager.h"
#include "log/log.h"
#include "global_log.h"


#define TIMER_ID_INIT -1
#define TIMER_OUT 1000
#define AUTO_SWITCH_VIEW_DELAY 1000

/*************************************************
功能： 构造函数
输入参数:
    parent:父控件指针
*************************************************************/
PDAPointListView::PDAPointListView(QWidget *parent)
    : PDAListView(QObject::trUtf8("Test Point List"), parent)
    , m_iAutoSwitchTimerId(TIMER_ID_INIT)
    , m_iTestPointPos(0)

{
    setFixedSize( Window::WIDTH, Window::HEIGHT );

    m_PDAService = PDAService::instance();
    m_pCurrentTask = m_PDAService->currentTask();
    connect( m_pCurrentTask,SIGNAL(sigTaskChanged()),this,SLOT(onTaskChanged()) );
    connect( m_pCurrentTask,SIGNAL(sigTestPointChanged(int)),this,SLOT(onTestPointChanged(int)));
    connect( m_pCurrentTask,SIGNAL(sigDeviceChanged(int)),this,SLOT(close()) );

    ItemDevice* pDevDataTmp = m_pCurrentTask->currentDevice();
    if(pDevDataTmp)
    {
        m_eTestPointType = pDevDataTmp->testPoints.at(0).eType;
    }

    //添加列表条目
    addItems();

    disconnect( titleBar(),SIGNAL(sigClicked()),this,SLOT(close()) );
    connect( titleBar(),SIGNAL(sigClicked()),this,SLOT(onCancelTest()) );

    openPower();
}

/*************************************************
功能： 析构
*************************************************************/
PDAPointListView::~PDAPointListView( )
{
    closePower();
}

void PDAPointListView::openPower()
{
    switch (m_eTestPointType)
    {
    case PDAServiceNS::AE_TYPE://AE测点
    {
        PeripheralService::instance()->openAEPower();
        logInfo("open ae power.");
        //RecordPlayService::instance()->start();
        RecordPlayService::instance()->startListenAE();
        break;
    }
    case PDAServiceNS::TEV_TYPE://TEV测点
    {
        PeripheralService::instance()->openTEVPower();
        logInfo("open tev power.");
        break;
    }
    case PDAServiceNS::INFRARED_TYPE://Infrared测点
    {
        Infrared::InfraredType eInfraredType = InfraredService::instance()->initInfraredType();
        if(Infrared::GUIDE == eInfraredType)
        {
            Guide::GuideDevInfo stGuideDevInfo;
            GuideClientManager::instance()->connectInfraredDev(stGuideDevInfo);
        }
        else
        {
            InfraredService::instance()->initModule();
        }
        break;
    }
    default:
        break;
    }

    return;
}

void PDAPointListView::closePower()
{
    switch (m_eTestPointType)
    {
    case PDAServiceNS::AE_TYPE://AE测点
    {
        RecordPlayService::instance()->stopListenAE();
        //RecordPlayService::instance()->stop();
        PeripheralService::instance()->closeAEPower();
        logInfo("close ae power.");
        break;
    }
    case PDAServiceNS::TEV_TYPE://TEV测点
    {
        PeripheralService::instance()->closeTEVPower();
        logInfo("close tev power.");
        break;
    }
    case PDAServiceNS::INFRARED_TYPE://Infrared测点
    {
        Infrared::InfraredType eInfraredType = InfraredService::instance()->getInfraredType();
        if(Infrared::GUIDE == eInfraredType)
        {
            // 退出界面将激光功能关闭
            GuideClientManager::instance()->ctrlLaser(Guide::SW_OFF);

            // 退出界面将辅助照明关闭
            GuideClientManager::instance()->ctrlAuxiliaryLighting(Guide::SW_OFF);

            GuideClientManager::instance()->disconnectInfraredDev();
        }
        else
        {
            InfraredService::instance()->deinitModule();
        }
        break;
    }
    default:
        break;
    }

    return;
}

/*************************************************
功能： 槽函数， 为了解决手动选中某测点实际未测试的场景，在退出时将下一个待测测点位置清空
*************************************************************/
void PDAPointListView::onCancelTest( void )
{
    if( m_pCurrentTask )
    {
        m_pCurrentTask->initTestPos();
    }
    close();
    return;
}

/*************************************************
功能： 定时器处理函数
输入参数：
        e -- 定时事件
*************************************************************/
void PDAPointListView::timerEvent(QTimerEvent* e)
{
    if(e->timerId() == m_iAutoSwitchTimerId)
    {
        killTimer(m_iAutoSwitchTimerId);
        m_iAutoSwitchTimerId = TIMER_ID_INIT;       // 关闭定时器

        if(!m_pCurrentTask)
        {
            logError("current task pointer is NULL.");
            return;
        }

        ItemDevice* pCurrentDevice = m_pCurrentTask->currentDevice();
        if (NULL == pCurrentDevice)
        {
            logError("current device pointer is NULL.");
            return;
        }

        int iPointSize = pCurrentDevice->testPoints.size();
        bool bFirstPointTested = false;
        if((m_iTestPointPos >= 0) && (m_iTestPointPos < iPointSize))
        {
            if(SystemSet::PATROL_RFID_SCAN_SWITCH == SystemSetService::instance()->getPatrolSwitchMode())
            {
                bFirstPointTested  = pCurrentDevice->testPoints[m_iTestPointPos].isTestFinished();
                if(bFirstPointTested)
                {
                    logWarning("current testPoint is tested.");
                    MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("Current patrol position test completed."));
                }
                else
                {
                    if(m_pCurrentTask->getCurPatrolPosition() == pCurrentDevice->testPoints[m_iTestPointPos].ePatrolPosition)
                    {
                        switchTestView(m_iTestPointPos);
                    }
                    else
                    {
                        logWarning("current testPoint is tested.");
                        MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("Current patrol position test completed."));
                    }
                }
            }
            else
            {
                bFirstPointTested  = pCurrentDevice->testPoints[m_iTestPointPos].isTestFinished();
                if(bFirstPointTested)
                {
                    logWarning("current testPoint is tested.");
                }
                else
                {
                    switchTestView(m_iTestPointPos);
                }
            }
        }
    }
    else
    {
        logWarning("wrong timer id.");
        killTimer(e->timerId());
    }

    return;
}

/****************************
功能： 处理showEvent事件
输入参数:
       event -- showEvent
*****************************/
void PDAPointListView::showEvent(QShowEvent *event)
{
    PDAListView::showEvent(event);
    m_iAutoSwitchTimerId = startTimer( TIMER_OUT );     // 打开自动跳转定时器 间隔1s
}

/*************************************************
功能： 槽函数， 自动跳转测点位置发生变化时触发
入参：测点位置
*************************************************************/
void PDAPointListView::onTestPointChanged(int testPointPos)
{
    m_iTestPointPos = testPointPos;
    if( ( m_iTestPointPos >= 0 ) &&
            ( m_iTestPointPos < m_pChart->allItems().size() ) )
    {
        m_pChart->setCurrentItemSelected( m_iTestPointPos );
        //QTimer::singleShot( AUTO_SWITCH_VIEW_DELAY,this,SLOT(onAutoSwitchViewDelay() ) );
        onAutoSwitchViewDelay();
    }
    else
    {
        logWarning("test point is out of size.");
    }
    return;
}

/*************************************************
功能： 添加列表条目
输入参数:NULL
*************************************************************/
void PDAPointListView::addItems()
{
    QMutexLocker stLocker(&m_mt4ItemInfo);
    QList<PDAListChart::ListItemInfo> qlTestPointListItemInfo;
    qlTestPointListItemInfo.clear();

    appendListItems(qlTestPointListItemInfo);
    m_pChart->addItems(qlTestPointListItemInfo);

    m_iTestPointPos = m_pCurrentTask->getNeedTestPointPos();
    m_pChart->setCurrentItemSelected(m_iTestPointPos);  // 选中当前未经测试的测点条目；

    return;
}

/*************************************************
功能： 槽函数， 刷新界面
输入参数:NULL
*************************************************************/
void PDAPointListView::onTaskChanged()
{
    QMutexLocker stLocker(&m_mt4ItemInfo);
    QList<PDAListChart::ListItemInfo> qlTestPointListItemInfo;
    appendListItems(qlTestPointListItemInfo);

    int iItemSize = qlTestPointListItemInfo.size();
    if( m_pChart->allItems().size() == iItemSize )
    {
        for(int i = 0; i < iItemSize; ++i)
        {
            m_pChart->setItemInfo( qlTestPointListItemInfo.at( i ), i );
        }
    }
    else
    {
        logWarning("view item size is not equal cache size.");
    }

    m_iTestPointPos = m_pCurrentTask->getNeedTestPointPos();
    m_pChart->setCurrentItemSelected(m_iTestPointPos);

    return;
}

/*************************************************
功能： 槽函数， 测点列表自动跳转界面延时显示（为在跳转前后能够短暂回到测点列表，避免突兀的在多个测试界面之间跳转）
*************************************************************/
void PDAPointListView::onAutoSwitchViewDelay( void )
{
    switchTestView(m_iTestPointPos);
    return;
}

/*************************************************
功能： 返回测点所属的测点类型
返回值：测点类型
*************************************************************/
PDAServiceNS::TestPointType PDAPointListView::testPointType()
{
    ItemTestType* pTestType = m_pCurrentTask->currentTestType();
    PDAServiceNS::TestPointType eTestPointType = pTestType->eTestPointType;
    return eTestPointType;
}

/*************************************************
功能： 读取设备下测点信息，添加到list
输入参数：NULL
输出参数：qlTestPointListItemInfo---保存所有设测点信息的list
*************************************************************/
void PDAPointListView::appendListItems(QList<PDAListChart::ListItemInfo> &qlTestPointListItemInfo)
{
    if(!m_pCurrentTask)
    {
        logError("current task is NULL.");
        return;
    }

    PDAServiceNS::PatrolPosition ePos = m_pCurrentTask->getCurPatrolPosition();

    ItemDevice* pDevData = m_pCurrentTask->currentDevice();
    if(!pDevData)
    {
        logError("current device is NULL.");
        return;
    }

    qint32 uiTestedItemsCnt = 0;
    int iTestPointCnt = pDevData->testPoints.size();
    for(int i = 0; i < iTestPointCnt; ++i)
    {
        PDAListChart::ListItemInfo stItemInfo;

        //测点还是显示全部的，只不过跳转时候做限制比较合理
        /*if(PDAServiceNS::PATROL_POS_NONE != ePos)
        {
            if(ePos != pDevData->testPoints.at(i).ePatrolPosition)
            {
                continue;
            }
        }*/

        if(pDevData)
        {
            stItemInfo.m_strItemName = pDevData->testPoints.at(i).strItemName;
            stItemInfo.m_iTotalCount = pDevData->testPoints.at(i).testDatas.size();
        }
        else
        {
            logError("current device is NULL.");
            break;
        }

        uiTestedItemsCnt = 0;
        for(int j = 0; j < stItemInfo.m_iTotalCount; ++j)
        {
            if(pDevData && pDevData->testPoints.at(i).testDatas.at(j).bTested == true)
            {
                ++uiTestedItemsCnt;
            }
        }

        //已测的测试项个数
        stItemInfo.m_iTestedCount = uiTestedItemsCnt;
        //新增业务 ，测点下的测试项均已完成，则默认勾号标记
        if((stItemInfo.m_iTestedCount == stItemInfo.m_iTotalCount) && (stItemInfo.m_iTotalCount != 0))
        {
            stItemInfo.m_bIsTicked = true;
        }
        else
        {
            stItemInfo.m_bIsTicked = false;
        }

        qlTestPointListItemInfo.append(stItemInfo);
    }

    return;
}

/*************************************************
功能： 进入测试界面
输入参数：
        id -- 条目序号
*************************************************************/
void PDAPointListView::switchTestView( int id )
{    
    ItemDevice* pDevData = m_pCurrentTask->currentDevice();
    if(!pDevData)
    {
        logError("current device pointer is NULL.");
        return;
    }

    if((id >= 0) && (id < pDevData->testPoints.size()))
    {
        m_pCurrentTask->setCurrentTestPoint(pDevData->testPoints.at(id));

        // 电流类型的显示电流类型的测试项数据列表
        PDAServiceNS::TestPointType eCurrentTestPointType = pDevData->testPoints.at(id).eType;
        if (PDAServiceNS::CABLEGROUND_TYPE == eCurrentTestPointType
                || PDAServiceNS::COREGOUND_TYPE == eCurrentTestPointType)
        {
            PDACurrentTestDataView* pView = new PDACurrentTestDataView();
            pView->show();
        }
        else
        {
            PDATestDataView *pView = new PDATestDataView();
            pView->show();
        }
    }
    else
    {
        logError(QString("curernt test point id: %1, error.").arg(id));
    }
    return;
}

/*************************************************
功能： 处理条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void PDAPointListView::onItemClicked( int id )
{
    if( TIMER_ID_INIT != m_iAutoSwitchTimerId )
    {
        killTimer( m_iAutoSwitchTimerId );
        m_iAutoSwitchTimerId = TIMER_ID_INIT;
    }

    switchTestView( id );

    return;
}


