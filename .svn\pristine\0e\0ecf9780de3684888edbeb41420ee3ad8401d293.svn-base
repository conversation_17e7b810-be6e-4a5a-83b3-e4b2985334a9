/*
* Copyright (c) 2017.12，南京华乘电气科技有限公司
* All rights reserved.
*
* MobileAccessService.h
*
* 初始版本：1.0
* 作者：陈康
* 创建日期：2017年12月06日
* 摘要：移动终端接入的对外接口类
       1.提供给View的访问接口和通知信号；
       2.对通信、本地采集、数据保存等模块进行管理，对操作进行关联

* 当前版本：1.0
*/
#ifndef MOBILEACCESSSERVICE_H
#define MOBILEACCESSSERVICE_H

#include <QObject>
#include <QVector>
#include "mobileaccess_global.h"
#include "datadefine.h"
#include "appcomm/appcommservice.h"

class SpectrumManager;
class SubTask;
class MOBILEACCESSSHARED_EXPORT MobileAccessService : public QObject
{
    Q_OBJECT
public:
    /****************************
    功能： 本地通讯模块单例
    *****************************/
    static MobileAccessService* instance();

    /****************************
    功能： 设置连接的ip和端口号
    *****************************/
    void setConnectIPPort();

    /****************************
    功能： 判断与app是否有指定的连接
    *****************************/
    bool isConnected(CommMode eMode);

    /****************************
    功能： 判断与app是否连接
    *****************************/
    bool isAppConnectExist();

    /****************************
    功能： 判断与app的连接是否更改
    *****************************/
    bool isHostChanged();

    /****************************
    功能： 与app进行连接
    *****************************/
    void connectHost(CommMode eMode);

    /****************************
    功能： 与app断开连接
    *****************************/
    void disconnectHost(CommMode eMode);

    /****************************
    功能： 登录平台（江苏电科院）
    *****************************/
    bool loginPlatform(QString strUserName, QString strPassWard);

    /****************************
    功能： 是否处于登录状态（江苏电科院）
    *****************************/
    bool isLogined();

    /****************************
    功能： 平台主任务下载（江苏电科院）
    *****************************/
    void downLoadMainTask();

    /****************************
    功能： 主任务上传（含江苏电科院）
    *****************************/
    void uploadMainTask( const QString& strMainId );

    /****************************
    功能： 多个子任务上传
    *****************************/
    void uploadSubTasks( const QString& strMainId, const QStringList& strSubIdList );

    void uploadGaps( SubTask *pSubTask, const QStringList& strGapIds );

    /****************************
    功能： 多个测点上传
    *****************************/
    void uploadTestPoints(SubTask *pSubTask, const QString& strGapId,
                           const QStringList& strPointIdList );

    /*************************************************
     * 功能：上传JSON任务
     * 输入参数：
     *      qstrMainTaskId：主任务ID
     *      qstrlstSubTaskIds：子任务ID集合
     * ***********************************************/
    void uploadJSONTask(const QString &qstrMainTaskId, const QStringList &qstrlstSubTaskIds);

    /*************************************************
     * 功能：设置跳转模式
     * 输入参数：
     *      iMode：跳转模式，0：自动跳转，1：手动跳转
     * ***********************************************/
    void setSwitchMode(int iMode);

    /*************************************************
     * 功能：获取跳转模式
     * 返回值：
     *      int：跳转模式，0：自动跳转，1：手动跳转
     * ***********************************************/
    int getSwitchMode();

    /*************************************************
     * 功能：设置通信模式
     * 输入参数：
     *      eMode：通信模式
     * ***********************************************/
    void setCurrentCommMode(CommMode eMode);

    /*************************************************
     * 功能：获取通信模式
     * 返回值：
     *      CommMode：通信模式
     * ***********************************************/
    CommMode getCurrentCommMode();

    /*************************************************
     * 功能：提示后台服务状态信息
     * 返回值：
     * ***********************************************/
    void sendServiceStateInfo();

signals:
    /*************************************************
    功能：连接状态改变信号
    参数：bConnected -- 是否有连接
    *************************************************************/
    void sigWifiStatusChanged(bool bConnected);

    void sigBluetoothStatusChanged(bool bConnected);

    void sigUdpStatusChanged(bool bConnected);

    /*************************************************
    功能：显示上传进度
    参数：iPercent -- 上传进度百分比 -1代表失败 100代表完成
    *************************************************************/
    void sigUploadProgress(int iPercent);

    /*************************************************
    功能：上传完毕
    *************************************************************/
    void sigUploadFinished();

    /*************************************************
    功能：完成压缩
    *************************************************************/
    void sigCompressDone();

    /*************************************************
    功能：后台服务未初始化
    *************************************************************/
    void sigPDAServiceUninit();

    /*************************************************
    功能：上传结果
    *************************************************************/
    void sigUploadResult(bool bResult);

    /*************************************************
    功能：下载结果
    *************************************************************/
    void sigDownloadResult(bool bResult);
	
private slots:
    /*************************************************
    功能：连接状态发生变化的处理
    参数：isConnected -- 是否有连接
    *************************************************************/
    void onConnectionStatusChanged(bool isConnected, CommMode eMode);

    /*************************************************
    功能：数据保存完毕进行发送的处理
    参数：strFilePath -- 数据保存的文件路径
    *************************************************************/
    void onSendDataFile(QString strFilePath, CommMode eMode);

    /*************************************************
    功能：对请求的图谱进行处理
    参数：maps -- 采集的图谱类型
    *************************************************************/
    void onGetFigureData(QVector<UINT8> maps, CommMode eMode);

    /*************************************************
    功能：下发任务报文的处理
    参数：fileContent -- 任务文件的内容
    *************************************************************/
    void onGetTask(QByteArray fileContent, CommMode eMode);

    //子任务完成采集后的处理
    void onSubTaskFinished(SubTask* pSubTask, CommMode eMode);

    /*************************************************
    功能：显示上传进度
    参数：iPercent -- 上传进度百分比 -1代表失败 100代表完成
    *************************************************************/
    void onUploadProgress(int iPercent);

private:
    /*************************************************
    函数名： AbstractComm(QObject *parent = 0)
    输入参数： parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit MobileAccessService( QObject *parent = 0 );

    /*************************************************
    函数名：
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能：读取云配置信息(ip,端口)
    *************************************************************/
    void readServerSettings(QString &strServerIP, QString &strServerPort);

private:
    AppCommService *m_pCommService;
    SpectrumManager *m_pMapManager;
    CommMode m_eCurrentMode;

    QString m_qsConnectedHostPort;
    QString m_qsConnectedHostIP;
};

#endif // MOBILEACCESSSERVICE_H
