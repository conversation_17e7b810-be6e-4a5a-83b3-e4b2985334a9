/*
* Copyright (c) 2018.02，南京华乘电气科技有限公司
* All rights reserved.
*
* UdpAppComm.h
*
* 初始版本：1.0
* 作者：
* 创建日期：2018年02月11日
* 摘要：实现和定制接入终端app进行UDP通信

* 当前版本：1.0
*/
#ifndef UDPAPPCOMM_H
#define UDPAPPCOMM_H

#include "isampleappcomm.h"
#include "comm/udpbean.h"

class UdpAppComm : public ISampleAppComm
{
    Q_OBJECT
public:
    explicit UdpAppComm(QObject *parent = 0);
    ~UdpAppComm();

    /****************************
    功能： 设置通讯参数
    入参： vParams -- 通信端口的参数集合
    出参： NULL
    返回值：void
    *****************************/
    void setCommParam( const QVector<QString> &vParams );

protected:
    /****************************
    功能： 创建底层通信组件
    入参： NULL
    出参： NULL
    返回值：底层组件指针
    *****************************/
    AbstractComm* createComm();

private slots:

private:
    UdpBean * m_pUdpObj;
};

#endif // UDPAPPCOMM_H
