﻿/*
* Copyright (c) 2017.08，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: phasedataitemcolormap.h
*
* 初始版本: 1.0
* 作者: 张涛
* 创建日期: 2017年11月13日
* 摘要: 该文件定义了周期图谱数据的颜色映射器, 主要用来获取数据对应的显示颜色

* 当前版本: 1.0
*/

#ifndef PHASEDATAITEMCOLORMAP_H
#define PHASEDATAITEMCOLORMAP_H

#include <QColor>
#include "phasedef.h"
#include <QDebug>
#include "qmath.h"

namespace
{
    static const double jetColorBar[100][3] =
    {
        {     0,      0, 0.5200},
        {     0,      0, 0.5600},
        {     0,      0, 0.6000},
        {     0,      0, 0.6400},
        {     0,      0, 0.6800},
        {     0,      0, 0.7200},
        {     0,      0, 0.7600},
        {     0,      0, 0.8000},
        {     0,      0, 0.8400},
        {     0,      0, 0.8800},
        {     0,      0, 0.9200},
        {     0,      0, 0.9600},
        {     0,      0, 1.0000},
        {     0, 0.0400, 1.0000},
        {     0, 0.0800, 1.0000},
        {     0, 0.1200, 1.0000},
        {     0, 0.1600, 1.0000},
        {     0, 0.2000, 1.0000},
        {     0, 0.2400, 1.0000},
        {     0, 0.2800, 1.0000},
        {     0, 0.3200, 1.0000},
        {     0, 0.3600, 1.0000},
        {     0, 0.4000, 1.0000},
        {     0, 0.4400, 1.0000},
        {     0, 0.4800, 1.0000},
        {     0, 0.5200, 1.0000},
        {     0, 0.5600, 1.0000},
        {     0, 0.6000, 1.0000},
        {     0, 0.6400, 1.0000},
        {     0, 0.6800, 1.0000},
        {     0, 0.7200, 1.0000},
        {     0, 0.7600, 1.0000},
        {     0, 0.8000, 1.0000},
        {     0, 0.8400, 1.0000},
        {     0, 0.8800, 1.0000},
        {     0, 0.9200, 1.0000},
        {     0, 0.9600, 1.0000},
        {     0, 1.0000, 1.0000},
        {0.0400, 1.0000, 0.9600},
        {0.0800, 1.0000, 0.9200},
        {0.1200, 1.0000, 0.8800},
        {0.1600, 1.0000, 0.8400},
        {0.2000, 1.0000, 0.8000},
        {0.2400, 1.0000, 0.7600},
        {0.2800, 1.0000, 0.7200},
        {0.3200, 1.0000, 0.6800},
        {0.3600, 1.0000, 0.6400},
        {0.4000, 1.0000, 0.6000},
        {0.4400, 1.0000, 0.5600},
        {0.4800, 1.0000, 0.5200},
        {0.5200, 1.0000, 0.4800},
        {0.5600, 1.0000, 0.4400},
        {0.6000, 1.0000, 0.4000},
        {0.6400, 1.0000, 0.3600},
        {0.6800, 1.0000, 0.3200},
        {0.7200, 1.0000, 0.2800},
        {0.7600, 1.0000, 0.2400},
        {0.8000, 1.0000, 0.2000},
        {0.8400, 1.0000, 0.1600},
        {0.8800, 1.0000, 0.1200},
        {0.9200, 1.0000, 0.0800},
        {0.9600, 1.0000, 0.0400},
        {1.0000, 1.0000,      0},
        {1.0000, 0.9600,      0},
        {1.0000, 0.9200,      0},
        {1.0000, 0.8800,      0},
        {1.0000, 0.8400,      0},
        {1.0000, 0.8000,      0},
        {1.0000, 0.7600,      0},
        {1.0000, 0.7200,      0},
        {1.0000, 0.6800,      0},
        {1.0000, 0.6400,      0},
        {1.0000, 0.6000,      0},
        {1.0000, 0.5600,      0},
        {1.0000, 0.5200,      0},
        {1.0000, 0.4800,      0},
        {1.0000, 0.4400,      0},
        {1.0000, 0.4000,      0},
        {1.0000, 0.3600,      0},
        {1.0000, 0.3200,      0},
        {1.0000, 0.2800,      0},
        {1.0000, 0.2400,      0},
        {1.0000, 0.2000,      0},
        {1.0000, 0.1600,      0},
        {1.0000, 0.1200,      0},
        {1.0000, 0.0800,      0},
        {1.0000, 0.0400,      0},
        {1.0000,      0,      0},
        {0.9600,      0,      0},
        {0.9200,      0,      0},
        {0.8800,      0,      0},
        {0.8400,      0,      0},
        {0.8000,      0,      0},
        {0.7600,      0,      0},
        {0.7200,      0,      0},
        {0.6800,      0,      0},
        {0.6400,      0,      0},
        {0.6000,      0,      0},
        {0.5600,      0,      0},
        {0.5200,      0,      0}
    };

    static const double parulaColorBar[100][3] =
    {
        { 0.2081, 0.1663, 0.5292},
        { 0.2106, 0.1812, 0.5599},
        { 0.2121, 0.1963, 0.5910},
        { 0.2124, 0.2116, 0.6224},
        { 0.2107, 0.2272, 0.6543},
        { 0.2066, 0.2432, 0.6863},
        { 0.1989, 0.2597, 0.7186},
        { 0.1865, 0.2766, 0.7511},
        { 0.1675, 0.2946, 0.7840},
        { 0.1403, 0.3147, 0.8168},
        { 0.1025, 0.3375, 0.8469},
        { 0.0591, 0.3598, 0.8683},
        { 0.0224, 0.3786, 0.8793},
        { 0.0073, 0.3937, 0.8830},
        { 0.0055, 0.4069, 0.8831},
        { 0.0106, 0.4187, 0.8809},
        { 0.0192, 0.4297, 0.8776},
        { 0.0297, 0.4402, 0.8733},
        { 0.0410, 0.4502, 0.8685},
        { 0.0512, 0.4600, 0.8633},
        { 0.0597, 0.4696, 0.8578},
        { 0.0668, 0.4791, 0.8523},
        { 0.0723, 0.4887, 0.8467},
        { 0.0764, 0.4983, 0.8413},
        { 0.0788, 0.5082, 0.8362},
        { 0.0794, 0.5185, 0.8318},
        { 0.0778, 0.5294, 0.8280},
        { 0.0734, 0.5410, 0.8257},
        { 0.0664, 0.5533, 0.8243},
        { 0.0574, 0.5662, 0.8235},
        { 0.0473, 0.5790, 0.8227},
        { 0.0377, 0.5915, 0.8210},
        { 0.0308, 0.6030, 0.8180},
        { 0.0265, 0.6137, 0.8135},
        { 0.0245, 0.6235, 0.8077},
        { 0.0237, 0.6324, 0.8005},
        { 0.0232, 0.6407, 0.7925},
        { 0.0228, 0.6483, 0.7835},
        { 0.0231, 0.6555, 0.7740},
        { 0.0254, 0.6623, 0.7637},
        { 0.0307, 0.6689, 0.7531},
        { 0.0400, 0.6752, 0.7419},
        { 0.0527, 0.6812, 0.7305},
        { 0.0677, 0.6871, 0.7185},
        { 0.0843, 0.6928, 0.7062},
        { 0.1024, 0.6984, 0.6934},
        { 0.1218, 0.7038, 0.6802},
        { 0.1423, 0.7090, 0.6666},
        { 0.1640, 0.7141, 0.6527},
        { 0.1868, 0.7190, 0.6383},
        { 0.2108, 0.7237, 0.6235},
        { 0.2360, 0.7281, 0.6086},
        { 0.2625, 0.7323, 0.5933},
        { 0.2901, 0.7361, 0.5778},
        { 0.3187, 0.7395, 0.5625},
        { 0.3482, 0.7424, 0.5473},
        { 0.3781, 0.7448, 0.5325},
        { 0.4082, 0.7466, 0.5185},
        { 0.4379, 0.7479, 0.5052},
        { 0.4669, 0.7487, 0.4925},
        { 0.4951, 0.7491, 0.4806},
        { 0.5224, 0.7492, 0.4693},
        { 0.5488, 0.7489, 0.4584},
        { 0.5745, 0.7484, 0.4479},
        { 0.5994, 0.7476, 0.4380},
        { 0.6236, 0.7467, 0.4282},
        { 0.6473, 0.7456, 0.4188},
        { 0.6704, 0.7443, 0.4096},
        { 0.6931, 0.7429, 0.4006},
        { 0.7153, 0.7414, 0.3917},
        { 0.7371, 0.7397, 0.3830},
        { 0.7586, 0.7379, 0.3743},
        { 0.7798, 0.7361, 0.3658},
        { 0.8007, 0.7343, 0.3572},
        { 0.8214, 0.7325, 0.3485},
        { 0.8419, 0.7307, 0.3398},
        { 0.8623, 0.7289, 0.3309},
        { 0.8824, 0.7274, 0.3217},
        { 0.9025, 0.7262, 0.3121},
        { 0.9224, 0.7256, 0.3018},
        { 0.9422, 0.7259, 0.2904},
        { 0.9613, 0.7281, 0.2774},
        { 0.9785, 0.7332, 0.2620},
        { 0.9914, 0.7422, 0.2451},
        { 0.9978, 0.7542, 0.2289},
        { 0.9990, 0.7672, 0.2144},
        { 0.9970, 0.7804, 0.2018},
        { 0.9931, 0.7936, 0.1901},
        { 0.9880, 0.8066, 0.1794},
        { 0.9822, 0.8196, 0.1691},
        { 0.9763, 0.8328, 0.1590},
        { 0.9705, 0.8462, 0.1489},
        { 0.9655, 0.8601, 0.1386},
        { 0.9616, 0.8748, 0.1278},
        { 0.9592, 0.8903, 0.1166},
        { 0.9587, 0.9068, 0.1050},
        { 0.9602, 0.9244, 0.0931},
        { 0.9638, 0.9432, 0.0809},
        { 0.9696, 0.9628, 0.0681},
        { 0.9763, 0.9831, 0.0538}

    };

    static const double hsvColorBar[100][3] =
    {
        { 1.0000,      0,      0 },
        { 1.0000, 0.0600,      0 },
        { 1.0000, 0.1200,      0 },
        { 1.0000, 0.1800,      0 },
        { 1.0000, 0.2400,      0 },
        { 1.0000, 0.3000,      0 },
        { 1.0000, 0.3600,      0 },
        { 1.0000, 0.4200,      0 },
        { 1.0000, 0.4800,      0 },
        { 1.0000, 0.5400,      0 },
        { 1.0000, 0.6000,      0 },
        { 1.0000, 0.6600,      0 },
        { 1.0000, 0.7200,      0 },
        { 1.0000, 0.7800,      0 },
        { 1.0000, 0.8400,      0 },
        { 1.0000, 0.9000,      0 },
        { 1.0000, 0.9600,      0 },
        { 0.9800, 1.0000,      0 },
        { 0.9200, 1.0000,      0 },
        { 0.8600, 1.0000,      0 },
        { 0.8000, 1.0000,      0 },
        { 0.7400, 1.0000,      0 },
        { 0.6800, 1.0000,      0 },
        { 0.6200, 1.0000,      0 },
        { 0.5600, 1.0000,      0 },
        { 0.5000, 1.0000,      0 },
        { 0.4400, 1.0000,      0 },
        { 0.3800, 1.0000,      0 },
        { 0.3200, 1.0000,      0 },
        { 0.2600, 1.0000,      0 },
        { 0.2000, 1.0000,      0 },
        { 0.1400, 1.0000,      0 },
        { 0.0800, 1.0000,      0 },
        { 0.0200, 1.0000,      0 },
        {      0, 1.0000, 0.0400 },
        {      0, 1.0000, 0.1000 },
        {      0, 1.0000, 0.1600 },
        {      0, 1.0000, 0.2200 },
        {      0, 1.0000, 0.2800 },
        {      0, 1.0000, 0.3400 },
        {      0, 1.0000, 0.4000 },
        {      0, 1.0000, 0.4600 },
        {      0, 1.0000, 0.5200 },
        {      0, 1.0000, 0.5800 },
        {      0, 1.0000, 0.6400 },
        {      0, 1.0000, 0.7000 },
        {      0, 1.0000, 0.7600 },
        {      0, 1.0000, 0.8200 },
        {      0, 1.0000, 0.8800 },
        {      0, 1.0000, 0.9400 },
        {      0, 1.0000, 1.0000 },
        {      0, 0.9400, 1.0000 },
        {      0, 0.8800, 1.0000 },
        {      0, 0.8200, 1.0000 },
        {      0, 0.7600, 1.0000 },
        {      0, 0.7000, 1.0000 },
        {      0, 0.6400, 1.0000 },
        {      0, 0.5800, 1.0000 },
        {      0, 0.5200, 1.0000 },
        {      0, 0.4600, 1.0000 },
        {      0, 0.4000, 1.0000 },
        {      0, 0.3400, 1.0000 },
        {      0, 0.2800, 1.0000 },
        {      0, 0.2200, 1.0000 },
        {      0, 0.1600, 1.0000 },
        {      0, 0.1000, 1.0000 },
        {      0, 0.0400, 1.0000 },
        { 0.0200,      0, 1.0000 },
        { 0.0800,      0, 1.0000 },
        { 0.1400,      0, 1.0000 },
        { 0.2000,      0, 1.0000 },
        { 0.2600,      0, 1.0000 },
        { 0.3200,      0, 1.0000 },
        { 0.3800,      0, 1.0000 },
        { 0.4400,      0, 1.0000 },
        { 0.5000,      0, 1.0000 },
        { 0.5600,      0, 1.0000 },
        { 0.6200,      0, 1.0000 },
        { 0.6800,      0, 1.0000 },
        { 0.7400,      0, 1.0000 },
        { 0.8000,      0, 1.0000 },
        { 0.8600,      0, 1.0000 },
        { 0.9200,      0, 1.0000 },
        { 0.9800,      0, 1.0000 },
        { 1.0000,      0, 0.9600 },
        { 1.0000,      0, 0.9000 },
        { 1.0000,      0, 0.8400 },
        { 1.0000,      0, 0.7800 },
        { 1.0000,      0, 0.7200 },
        { 1.0000,      0, 0.6600 },
        { 1.0000,      0, 0.6000 },
        { 1.0000,      0, 0.5400 },
        { 1.0000,      0, 0.4800 },
        { 1.0000,      0, 0.4200 },
        { 1.0000,      0, 0.3600 },
        { 1.0000,      0, 0.3000 },
        { 1.0000,      0, 0.2400 },
        { 1.0000,      0, 0.1800 },
        { 1.0000,      0, 0.1200 },
        { 1.0000,      0, 0.0600 }

    };
}

class PhaseDataItemColorMap
{
public:
    explicit PhaseDataItemColorMap(Phase::DataItemColorStyle eStyle = Phase::Blaze);

    // 颜色风格
    Phase::DataItemColorStyle style() const
    {
        return m_eStyle;
    }

    void setStyle(Phase::DataItemColorStyle eStyle)
    {
        m_eStyle = eStyle;
    }

    // 根据百分比计算颜色, 输入为0~1
    inline QColor colorForPercent(float fPercent) const
    {
        return QColor::fromHsl(int(m_arrColorCeofficient[m_eStyle][0] + m_arrColorCeofficient[m_eStyle][1] * fPercent),
                int(m_arrColorCeofficient[m_eStyle][2] + m_arrColorCeofficient[m_eStyle][3] * fPercent),
                int(m_arrColorCeofficient[m_eStyle][4] + m_arrColorCeofficient[m_eStyle][5] * fPercent));
    }

    inline QColor colorForPercentEx(float fPercent) const
    {
        int iIndex = qCeil(fPercent * 100) - 1;
        if (iIndex < 0)
        {
            return QColor();
        }
        return QColor(jetColorBar[iIndex][0] * 255, jetColorBar[iIndex][1] * 255, jetColorBar[iIndex][2] * 255);
    }

private:
    Phase::DataItemColorStyle m_eStyle;

    enum StyleColorCoefficientNum
    {
        CoefficientNum = 6
    };

    // 系数表
    static const int m_arrColorCeofficient[Phase::StyleCount][CoefficientNum];
};

#endif // PHASEDATAITEMCOLORMAP_H
