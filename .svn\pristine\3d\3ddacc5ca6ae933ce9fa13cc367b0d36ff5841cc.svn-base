﻿#include <QHBoxLayout>
#include <QDebug>
#include "validator/doublevalidator.h"
#include "validator/intvalidator.h"
#include "infraredparameteritem.h"
#include "appfontmanager/appfontmanager.h"
#include "log/log.h"

const double LIMIT_VALUE_DEFAULT = -1.0;
const int FONT_SIZE = 20;
const int EDIT_SIZE = 14;
const int CIRCLE_BTN_SIZE = 40;
const QString PLUS_BUTTON_STYLE = "QPushButton{border:none;border-image: url(:/images/plus.png)}";
const QString MINUS_BUTTON_STYLE = "QPushButton{border:none;border-image: url(:/images/minus.png)}";
const QString LABEL_STYLE = "QLabel{border:none;}";

const int ITEM_SPACING = 10;
const int TITLE_FACTOR = 3;
const int BUTTON_FACTOR = 1;
const int EDIT_FACTOR = 3;

#define TEXT_WIDTH_RATIO    0.7
#define TEXT_HEIGHT_RATIO   0.7

/*************************************************
函数名： InfraredParameterItem(const QString &title,
                              const double dStep,
                              const QString &strValue,
                              QWidget *parent)
输入参数： title：参数条目名称
          dStep：参数条目步进
          strValue：参数条目初始值
          parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
InfraredParameterItem::InfraredParameterItem(const QString &title,
                                             const double dStep,
                                             const QString &strValue,
                                             QWidget *parent)
    :QFrame(parent)
    ,m_strValue(strValue)
    ,m_dStep(dStep)
    ,m_pTitleLabel(NULL)
    ,m_pValueEdit(NULL)
    ,m_pDecreaseButton(NULL)
    ,m_pPlusButton(NULL)
    ,m_dMaxValue(LIMIT_VALUE_DEFAULT)
    ,m_dMinValue(LIMIT_VALUE_DEFAULT)
    ,m_isPlusBtnPressed(false)
    ,m_isDecreaseBtnPressed(false)
    ,m_iTimer(-1)
    ,m_pValidator(NULL)
{
    setAttribute(Qt::WA_TranslucentBackground, true);
    setWindowFlags(Qt::FramelessWindowHint);
    setStyleSheet("border:1px solid red;");

    // “-”按钮
    m_pDecreaseButton = new QPushButton(this);
    m_pDecreaseButton->setFixedSize(CIRCLE_BTN_SIZE, CIRCLE_BTN_SIZE);
    m_pDecreaseButton->setStyleSheet(MINUS_BUTTON_STYLE);
    connect(m_pDecreaseButton, SIGNAL(pressed()), this, SLOT(onDecreaseButtonPressed()));
    connect(m_pDecreaseButton, SIGNAL(released()), this, SLOT(onDecreaseButtonReleased()));

    // “+”按钮
    m_pPlusButton = new QPushButton(this);
    m_pPlusButton->setFixedSize(CIRCLE_BTN_SIZE, CIRCLE_BTN_SIZE);
    m_pPlusButton->setStyleSheet(PLUS_BUTTON_STYLE);
    connect(m_pPlusButton, SIGNAL(pressed()), this, SLOT(onPlusButtonPressed()));
    connect(m_pPlusButton, SIGNAL(released()), this, SLOT(onPlusButtonReleased()));

    m_pTitleLabel = new QLabel(this);
    m_pTitleLabel->setStyleSheet(LABEL_STYLE);
    QFont qFont = AppFontManager::instance()->getAppCurFont();
    qFont.setPointSize(FONT_SIZE);
    m_pTitleLabel->setFont(qFont);
    m_pTitleLabel->setText(title);

    m_pValueEdit = new QLineEdit(this);
    qFont.setPointSize(EDIT_SIZE);
    m_pValueEdit->setFont(qFont);

    QHBoxLayout *layout = new QHBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setSpacing(ITEM_SPACING);

    QSizePolicy sizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);
    m_pDecreaseButton->setSizePolicy(sizePolicy);
    m_pValueEdit->setSizePolicy(sizePolicy);
    m_pPlusButton->setSizePolicy(sizePolicy);

    layout->addWidget(m_pTitleLabel, TITLE_FACTOR);
    layout->addWidget(m_pDecreaseButton, BUTTON_FACTOR);
    layout->addWidget(m_pValueEdit, EDIT_FACTOR);
    layout->addWidget(m_pPlusButton, BUTTON_FACTOR);

    setLayout(layout);
}

/*************************************************
函数名： text()
输入参数： NULL
输出参数： NULL
返回值： 当前显示内容
功能： 获取当前显示内容
*************************************************************/
const QString& InfraredParameterItem::text()
{
    return m_strValue;
}

/*************************************************
函数名： title()
输入参数： NULL
输出参数： NULL
返回值： 参数条目名称
功能： 获取参数条目名称
*************************************************************/
QString InfraredParameterItem::title()
{
    return m_pTitleLabel->text();
}

/*************************************************
函数名： maxValue()
输入参数： NULL
输出参数： NULL
返回值： 参数条目最大值
功能： 获取参数条目最大值
*************************************************************/
double InfraredParameterItem::maxValue()
{
    return m_dMaxValue;
}

/*************************************************
函数名： minValue()
输入参数： NULL
输出参数： NULL
返回值： 参数条目最小值
功能： 获取参数条目最小值
*************************************************************/
double InfraredParameterItem::minValue()
{
    return m_dMinValue;
}

/*************************************************
函数名： setText(const QString &str)
输入参数： str：当前显示内容
输出参数： NULL
返回值： NULL
功能： 设置当前显示内容
*************************************************************/
void InfraredParameterItem::setText(const QString &str)
{
    m_pValueEdit->setText(str);

    if (isValid() == 0)
    {
        refreshDefaultValue();
    }
    else
    {
        restoreDefaultValue();
    }
}

/*************************************************
函数名： setRange(double dMin, double dMax)
输入参数： dMin：最小值
          dMax：最大值
输出参数： NULL
返回值： NULL
功能： 设置参数条目合法值范围
*************************************************************/
void InfraredParameterItem::setRange(double dMin, double dMax, uint nDecimal)
{
    if (dMin < dMax)
    {
        m_dMinValue = dMin;
        m_dMaxValue = dMax;
        if( 0 == nDecimal )
        {
            // 添加校验器
            if(!m_pValidator)
            {
                m_pValidator = new IntValidator(this);
            }
            ((QIntValidator* )m_pValidator)->setRange((int)dMin, (int)dMax);
            m_pValueEdit->setValidator(m_pValidator);
        }
        else
        {
            // 添加校验器
            if(!m_pValidator)
            {
                m_pValidator = new DoubleValidator(this);
            }
            ((QDoubleValidator* )m_pValidator)->setRange(dMin, dMax, nDecimal);
            m_pValueEdit->setValidator(m_pValidator);
        }
    }

    return;
}

/*************************************************
函数名： isValid()
输入参数： NULL
输出参数： NULL
返回值： 参数条目值是否有效
功能： 判断参数条目值是否有效
*************************************************************/
int InfraredParameterItem::isValid()
{
    int nRes = -1;
    QString context = m_pValueEdit->text();
    if (context.isEmpty() || context.contains(" "))
    {
        return nRes;
    }

    if( context == "-" )
    {
        return -2;
    }

    double dValue = m_pValueEdit->text().toDouble();

    if (!qFuzzyCompare(m_dMinValue,LIMIT_VALUE_DEFAULT) && !qFuzzyCompare(m_dMaxValue,LIMIT_VALUE_DEFAULT))
    {
        if (dValue < m_dMinValue || dValue > m_dMaxValue)
        {
            nRes = 1;
        }
        else
        {
            nRes = 0;
        }
    }
    else
    {
        nRes = 1;
    }

    return nRes;
}

/*************************************************
函数名： restoreDefaultValue()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 恢复默认值
*************************************************************/
void InfraredParameterItem::restoreDefaultValue()
{
    m_pValueEdit->setText(m_strValue);
}

/*************************************************
函数名： refreshDefaultValue()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 更新默认值
*************************************************************/
void InfraredParameterItem::refreshDefaultValue()
{
    m_strValue = m_pValueEdit->text();
}

/*************************************************
函数名： resizeEvent(QResizeEvent *e)
输入参数： e：尺寸改变事件
输出参数： NULL
返回值： NULL
功能： 尺寸改变事件处理
*************************************************************/
void InfraredParameterItem::resizeEvent(QResizeEvent *e)
{
    Q_UNUSED(e);
    autoScaleText(m_pValueEdit, m_pValueEdit->text());
}

/*************************************************
函数名： timerEvent(QTimerEvent *event)
输入参数： event：定时器事件
输出参数： NULL
返回值： NULL
功能： 定时器事件处理
*************************************************************/
void InfraredParameterItem::timerEvent(QTimerEvent *event)
{
    if (event->timerId() == m_iTimer)
    {
        if (m_isPlusBtnPressed)
        {
            onPlusButtonClicked();
        }

        if (m_isDecreaseBtnPressed)
        {
            onDecreaseButtonClicked();
        }
    }
}

/*************************************************
函数名： onPlusButtonClicked()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 增加按钮响应槽函数
*************************************************************/
void InfraredParameterItem::onPlusButtonClicked()
{
    double dValue = m_pValueEdit->text().toDouble();
    dValue += m_dStep;
    if (dValue < maxValue() || qFuzzyCompare(dValue, maxValue()))
    {
        m_pValueEdit->setText(QString::number(dValue));
    }
}

/*************************************************
函数名： onDecreaseButtonClicked()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 减少按钮响应槽函数
*************************************************************/
void InfraredParameterItem::onDecreaseButtonClicked()
{
    double dValue = m_pValueEdit->text().toDouble();
    dValue -= m_dStep;
    if (dValue > minValue() || qFuzzyCompare(dValue, minValue()))
    {
        m_pValueEdit->setText(QString::number(dValue));
    }
}

/*************************************************
函数名： onDecreaseButtonPressed()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 减少按钮按下响应槽函数
*************************************************************/
void InfraredParameterItem::onDecreaseButtonPressed()
{
    m_isDecreaseBtnPressed = true;
    m_isPlusBtnPressed = false;

    if (-1 == m_iTimer)
    {
        m_iTimer = startTimer(TIME_100MS);
    }
}

/*************************************************
函数名： onDecreaseButtonReleased()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 减少按钮松开响应槽函数
*************************************************************/
void InfraredParameterItem::onDecreaseButtonReleased()
{
    m_isDecreaseBtnPressed = false;
    m_isPlusBtnPressed = false;

    if (m_iTimer != -1)
    {
        killTimer(m_iTimer);
        m_iTimer = -1;
    }

    onDecreaseButtonClicked();
}

/*************************************************
函数名： onPlusButtonPressed()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 增加按钮按下响应槽函数
*************************************************************/
void InfraredParameterItem::onPlusButtonPressed()
{
    m_isDecreaseBtnPressed = false;
    m_isPlusBtnPressed = true;

    if (-1 == m_iTimer)
    {
        m_iTimer = startTimer(TIME_100MS);
    }
}

/*************************************************
函数名： onPlusButtonReleased()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 增加按钮松开响应槽函数
*************************************************************/
void InfraredParameterItem::onPlusButtonReleased()
{
    m_isDecreaseBtnPressed = false;
    m_isPlusBtnPressed = false;

    if (m_iTimer != -1)
    {
        killTimer(m_iTimer);
        m_iTimer = -1;
    }

    onPlusButtonClicked();
}

/*************************************************
函数名： autoScaleText(QWidget *pWidget, const QString &str)
输入参数： pWidget：控件指针
          str：文本内容
输出参数： NULL
返回值： NULL
功能： 文字大小根据文本内容自适应
*************************************************************/
void InfraredParameterItem::autoScaleText(QWidget *pWidget, const QString &str)
{
    int iWidth = pWidget->fontMetrics().width(str);
    int iHeight = pWidget->fontMetrics().height();

    float fWidthRatio = pWidget->width() * TEXT_WIDTH_RATIO / iWidth;
    float fHeightRatio = pWidget->height() * TEXT_HEIGHT_RATIO / iHeight;

    float fRatio = fWidthRatio > fHeightRatio ? fHeightRatio : fWidthRatio;
    QFont font = AppFontManager::instance()->getAppCurFont();
    font.setPointSizeF(EDIT_SIZE * fRatio);
    pWidget->setFont(font);
    return;
}
