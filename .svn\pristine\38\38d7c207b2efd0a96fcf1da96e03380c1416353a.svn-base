﻿/*
* Copyright (c) 2017.08，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: phasechartmap.h
*
* 初始版本: 1.0
* 作者: 张涛
* 创建日期: 2017年11月13日
* 摘要: 该文件定义了周期图谱坐标映射类的基类, 输入图谱的坐标信息, 输出坐标系各图形元素, 用以计算坐标

* 当前版本: 1.0
*/

#ifndef PHASECHARTMAP_H
#define PHASECHARTMAP_H

#include <QRect>
#include <QFont>
#include "phasedef.h"

class PhaseChartMap
{
public:
    enum MapType
    {
        PrpdMapper = 0,
        PrpsMapper2D
    };

    PhaseChartMap();
    virtual ~PhaseChartMap();

    virtual MapType type() const = 0;

    // 边界矩形
    QRect boundingRect() const
    {
        return m_boundingRect;
    }

    void setBoundingRect(const QRect &r)
    {
        PHASE_ASSERT(!r.isNull());

        m_boundingRect = r;
    }

    // 坐标系矩形
    QRect coordinateRect() const
    {
        return m_coorGeometry;
    }

    void setCoordinateRect(const QRect & rect);

    // 文字字体
    QFont textFont() const
    {
        return m_textFont;
    }

    void setTextFont(const QFont &font)
    {
        m_textFont = font;
    }

    // 根据参数刷新缓存的映射信息
    virtual void updateFactor() = 0;

private:
    Q_DISABLE_COPY(PhaseChartMap)

    QRect m_boundingRect;
    QRect m_coorGeometry;
    QFont m_textFont;
};

#endif // PHASECHARTMAP_H
