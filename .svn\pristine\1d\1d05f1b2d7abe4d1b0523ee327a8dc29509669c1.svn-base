﻿/*
* Copyright (c) 2016.1，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：pdaloginview.h
*
* 初始版本：1.0
* 作者：张浪
* 创建日期：2018年11月29日
* 摘要：该文件定义了接入APP功能登录界面
* 当前版本：1.0
*/

#ifndef PDALOGINVIEW_H
#define PDALOGINVIEW_H

#include <QWidget>
#include <QMap>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include "titlebar/TitleBar.h"


class PDALoginView : public QWidget
{
    Q_OBJECT
public:
    /****************************
    函数名： PDALoginView
    输入参数:
            parent:父窗口指针
    输出参数：NULL
    返回值：NULL
    功能： 构造函数
    *****************************/
    explicit PDALoginView(QWidget *parent = 0);
    ~PDALoginView();

signals:
    /****************************
    函数名： sigLogined
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能： 释放登录成功信号
    *****************************/
    void sigLogined();

private slots:
    /****************************
    函数名： onLoginClicked
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能： 点击登录按钮处理，请求登录
    *****************************/
    void onLoginClicked();

    /****************************
    函数名： onLoginClicked
    输入参数: qstrText
    输出参数：NULL
    返回值：NULL
    功能： 处理密码框内容改变事件
    *****************************/
    void onPwdTextEdited(const QString &qstrText);

protected:
    /****************************
    函数名： showEvent
    输入参数:
            event -- showEvent
    输出参数：NULL
    返回值：NULL
    功能： 处理showEvent事件
    *****************************/
    void showEvent(QShowEvent *event);

    /****************************
    函数名： keyPressEvent
    输入参数:
            event -- keyPress事件
    输出参数：NULL
    返回值：NULL
    功能： 处理keyPress事件
    *****************************/
    void keyPressEvent(QKeyEvent *event);

    /*************************************************
    功能： 窗口关闭事件
    输入参数:
        event -- 事件
    *************************************************************/
    void closeEvent(QCloseEvent* event);

private:
    /****************************
    函数名： createUI
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能： 创建界面元素
    *****************************/
    void createUI();

    /****************************
    函数名： login
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能： 初始化点击登陆后的界面
    *****************************/
    void login();

    /****************************
    函数名： saveUserInfo
    输入参数: qsUserName---上次登录成功的用户名
            qsPWD ---上次登录成功的密码
    输出参数：NULL
    返回值：NULL
    功能： 把有效的用户名保存在配置文件
    *****************************/
    void saveUserInfo(const QString &qsUserName, const QString &qsPWD);


    /****************************
    函数名： getLoginedUserInfo
    输入参数: NULL
    输出参数：qsUser---上次登录成功的用户名
            qsPwd---上次登录成功的密码
    返回值：NULL
    功能： 从配置文件读取已登录用户名/密码
    *****************************/
    void getLoginedUserInfo(QString &qsUser, QString &qsPwd, QString &qsPwdTime);

private:
    QString         m_qsUser;
    QString         m_qsPwd;
    bool            m_bRequestLogin;
    bool            m_bSendingReq;

    QMap<int, QWidget*>     m_map4Widget;
    TitleBar*               m_pTitleBar;  // 标题栏
    QLineEdit*              m_pUsrId;
    QLineEdit*              m_pPwd;
    QPushButton*            m_pBtnLogin;
    int                     m_iCurIdx;

};

#endif // PDALOGINVIEW_H
