#include "aewavetask.h"
#include "ae/AEWaveService.h"
#include "config/ConfigManager.h"
#include "ae/AEConfig.h"
#include "appconfig.h"
#include "ae/aewavedatamap.h"
#include "peripheral/peripheralservice.h"

AEWaveTask::AEWaveTask(QObject *parent) : AbstractSpectrumTask(parent)
{
    m_pAEService = AEWaveService::instance();
}

AEWaveTask::~AEWaveTask()
{
    m_pAEService->deleteUser( m_userId );
}

void AEWaveTask::startSample()
{
    PeripheralService *pPeripherService = PeripheralService::instance();
    pPeripherService->openAEPower();

    //注册用户
    MultiServiceNS::SampleUser userInfo;
    userInfo.eSampleType = MultiServiceNS::SPECTRUM_AE_WAVE;
    m_userId = m_pAEService->addUser( userInfo );
    //设置参数
    setAEWaveParameters();
    connect( m_pAEService, SIGNAL(sigData(QVector<AE::WaveData>,MultiServiceNS::USERID)),
             this, SLOT(onDataRead(QVector<AE::WaveData>,MultiServiceNS::USERID)) );
    connect( m_pAEService, SIGNAL(sigReadAEWaveDataFailed(MultiServiceNS::USERID)),
             this, SLOT(onReadDataFailed(MultiServiceNS::USERID)) );
    m_pAEService->startSample( m_userId );
    //m_pAEService->startExclusiveSample( m_userId );
}

void AEWaveTask::onDataRead(QVector<AE::WaveData> vecData, MultiServiceNS::USERID ucID )
{
    if( ucID == m_userId )
    {
        //设置数据文件里图谱数据
        m_vecData = vecData;

        fulfillTask();
    }
}

void AEWaveTask::onReadDataFailed(MultiServiceNS::USERID  ucID)
{
    if( ucID == m_userId )
    {
        fulfillTask();
    }
}

void AEWaveTask::saveMapData()
{
    //创建图谱保存对象
    AEWaveDataMap * pMap = new AEWaveDataMap;

    //设置头部信息
    pMap->setSpectrumProperty(DataFileNS::PROPERTY_TEST);

    //设置ext信息
    fillAEWaveInfo( pMap );
    //设置数据内容
    fillAEWaveData( pMap );

    emit sigSampleFinished( pMap );
}

void AEWaveTask::fulfillTask()
{
    //停止采样
    //m_pAEService->stopSample( m_userId );
    m_pAEService->stopExclusiveSample(m_userId);

    saveMapData();

    PeripheralService *pPeripherService = PeripheralService::instance();
    pPeripherService->closeAEPower();
}

void AEWaveTask::fillAEWaveInfo(AEWaveDataMap *pMap )
{
    pMap->setDataPrimitiveType( DataFileNS::DATA_TYPE_FLOAT );
    pMap->setTestChannelSign( AE::CHANNEL_DEFAULT );

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    UINT32 uiFreq = (Frequency) pConfig->value( APPConfig::KEY_SYS_FREQ ).toUInt();
    pConfig->endGroup();

    AEMapNS::AEWaveBinaryMapInfo mapInfo;
    mapInfo.eAmpUnit = DataFileNS::AMP_UNIT_mV;//m_eUnit TODO
    mapInfo.fAmpLowerLimit = 0;
    mapInfo.fAmpUpperLimit = AE::AE_AMPLITUDE_SCOPE[m_eGain][m_eAmpRange];
    mapInfo.eSonacType = AEMapNS::AE_TRANSFORMER_AIR;
    mapInfo.iDataPointNum = m_vecData.size();
    mapInfo.lSampleRate = (AEWAVENUM * uiFreq)/ AE::AE_SAMPLE_TIME[m_eSampleTime];//TODO 1024

    memset(mapInfo.ucaDischargeTypeProb, 0, sizeof(mapInfo.ucaDischargeTypeProb));
    pMap->setBinaryInfo( &mapInfo );
}

void AEWaveTask::fillAEWaveData( AEWaveDataMap *pMap )
{
    int dataCnt  = m_vecData.size();

    if(dataCnt > 0)
    {
        float * pfData = new float[dataCnt];
        memset(pfData, 0, dataCnt * sizeof(float));
        for(int i = 0 ; i < dataCnt;  i ++)
        {
            pfData[i] = m_vecData.at(i).fWaveValue;
        }
        pMap->setData( pfData, dataCnt );
    }
}

void AEWaveTask::setAEWaveParameters()
{
    int iGroup = AE::GROUP_AE_WAVE;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_AE );
    m_eGain = (AE::GainType)pConfig->value( AE::KEY_GAIN ).toUInt();
    m_eSyncSource = (Module::SyncSource)pConfig->value( AE::KEY_SYNC_SOURCE ).toUInt();
    m_eTriggerValue = (AE::TriggerValue)pConfig->value( AE::KEY_TRIGGER_VALUE, iGroup ).toUInt();
    m_eSampleTime = (AE::SampleTime)pConfig->value( AE::KEY_SAMPLE_TIME, iGroup ).toUInt();
    m_eAmpRange = (AE::AmpRange)pConfig->value( AE::KEY_AMPLITUDE_SCOPE, iGroup ).toUInt();
    pConfig->endGroup();

    //eTriggerValue = AE::TRIGGER_LEVEL_0;

    m_pAEService->transaction();
    m_pAEService->setWorkMode( AE::MODE_WAVE );
    m_pAEService->setGain( m_eGain );
    m_pAEService->setTriggerValue( m_eTriggerValue );
    m_pAEService->setUnit( AE::UNIT_DEFAULT );
    m_pAEService->setSampleTime( m_eSampleTime );
    m_pAEService->setSyncSource( m_eSyncSource );
    m_pAEService->commit();
}
