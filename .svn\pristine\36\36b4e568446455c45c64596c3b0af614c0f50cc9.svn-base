/*
* Copyright (c) 2016.05，南京华乘电气科技有限公司
* All rights reserved.
*
* RecordView.cpp
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2016年5月24日
* 修改日期：2016年12月12日
* 摘要：音频录制文件界面定义

* 当前版本：1.0
*/
#include "RecordView.h"
#include <QDateTime>
#include <QHBoxLayout>
#include "recordplay/RecordPlay.h"
#include "window/Window.h"
#include "messageBox/msgbox.h"
#include "datadefine.h"
#include "View.h"
#include "dataSave/DataSave.h"
#include "recordplayviewdefine.h"
#include "config/ConfigInstance.h"
#include "appconfig.h"
#include "systemsetting/SystemSet.h"
#include "systemsetting/shutdownpower.h"
#include "controlButton/PopupButton.h"
#include "systemsetting/systemsetservice.h"
#include "log/log.h"


#define MIN_RECORD_TIME     3           //至少录制3秒
#define PER_MIN_SECOND     60           //60秒

const QString RECORD_PIXMAP_PATH = ":/images/record.png";
const UINT8 MINUTE_PER_HOUR = 60;
const UINT8 SECOND_PER_MINUTE = 60;


//注：由于考虑到实际使用中，不会使用到录音暂停和恢复功能，于是底层库不提供暂停和恢复接口，应用层只需要开始和停止录音
typedef enum _RecordButton
{
    BUTTON_RECORD = 0, //录音
    //BUTTON_STOP_RECORD, //停止录音
    BUTTON_RECORD_TIMELEN, //录音时长
}RecordButton;

//音量
const ButtonInfo::SliderValueConfig s_TimeDurationCfg =
{
    SystemSet::DURA_MIN, SystemSet::DURA_MAX, SystemSet::DURA_STEP
};

//控制按钮定义
const ButtonInfo::Info s_RecordPlayButtonInfo[] =
{
    { BUTTON_RECORD, { ButtonInfo::COMMAND, RecordPlayViewString::TEXT_RECORD, NULL, ":/images/sampleControl/record.png", NULL } },//录音、暂停、
    //{ BUTTON_STOP_RECORD, { ButtonInfo::COMMAND, RecordPlayViewString::TEXT_STOP, NULL, ":/images/sampleControl/sample.png", NULL } },//停止
    { BUTTON_RECORD_TIMELEN, { ButtonInfo::FIXED_STEP_SLIDER, RecordPlayViewString::TEXT_RECORD_TIMELEN, RecordPlayViewString::TEXT_RECORD_TIMELEN_UNIT, ":/images/sampleControl/timeInterval.png", &s_TimeDurationCfg } },//录音时长
};

/*************************************************
功能： 构造函数
输入参数: strTitle: 标题
        eType: 录音类型
        parent: 父控件指针
*************************************************************/
RecordView::RecordView(const QString& strTitle, RecordPlay::Type eType, RecordPlay::AEType eTypeAE, QWidget *parent)
    : SampleChartView(strTitle, parent)
    , m_eType(eType)
    , m_eAEType(eTypeAE)
{
    resize( Window::WIDTH, Window::HEIGHT );
    m_bStopEnable = false;

    ChartWidget* pChart = createChart();
    setChart( pChart );

    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar( RecordPlayViewString::CONTEXT, s_RecordPlayButtonInfo, sizeof(s_RecordPlayButtonInfo) / sizeof(ButtonInfo::Info) );

    m_pRecordButton = qobject_cast<ControlButton*>(pButtonBar->button(BUTTON_RECORD));//录音播放按钮
    //m_pStopButton = qobject_cast<ControlButton*>(pButtonBar->button(BUTTON_STOP_RECORD));//停止按钮

    //数据初始化
    initDatas();

    ShutDownPower::getInstance()->stop();   // 停止定时关机

    //RecordPlayService::instance()->start();

    //设置音量
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    UINT8 ucVolume = pConfig->value(APPConfig::KEY_SYSTEM_VOLUME).toUInt();
    m_qui32RecordDuration = pConfig->value(APPConfig::KEY_RECORD_DURATION).toUInt();
    pConfig->endGroup();

    RecordPlayService::instance()->setVolume(ucVolume);
    ((PopupButton*)(buttonBar()->button(BUTTON_RECORD_TIMELEN)))->setValue(m_qui32RecordDuration);

    connect( RecordPlayService::instance(), SIGNAL(sigRecordResult(int)), this, SLOT(onRecordResult(int)) );//录音结果

    //m_pStopButton->setEnabled(false);
}

/*************************************************
功能： 构造函数
输入参数: strTitle: 标题
		eType: 录音类型
		parent: 父控件指针
*************************************************************/
RecordView::RecordView(const QString& strTitle, RecordPlay::Type eType, QWidget *parent)
	: SampleChartView(strTitle, parent)
	, m_eType(eType)
{
	resize( Window::WIDTH, Window::HEIGHT );
	m_bStopEnable = false;

	ChartWidget* pChart = createChart();
	setChart( pChart );

	//创建按钮栏
	PushButtonBar* pButtonBar = createButtonBar( RecordPlayViewString::CONTEXT, s_RecordPlayButtonInfo, sizeof(s_RecordPlayButtonInfo) / sizeof(ButtonInfo::Info) );

	m_pRecordButton = qobject_cast<ControlButton*>(pButtonBar->button(BUTTON_RECORD));//录音播放按钮
	//m_pStopButton = qobject_cast<ControlButton*>(pButtonBar->button(BUTTON_STOP_RECORD));//停止按钮

	//数据初始化
	initDatas();

	ShutDownPower::getInstance()->stop();	// 停止定时关机

	//RecordPlayService::instance()->start();

	//设置音量
	ConfigInstance *pConfig = ConfigManager::instance()->config();
	pConfig->beginGroup(Module::GROUP_APP);
	UINT8 ucVolume = pConfig->value(APPConfig::KEY_SYSTEM_VOLUME).toUInt();
	m_qui32RecordDuration = pConfig->value(APPConfig::KEY_RECORD_DURATION).toUInt();
	pConfig->endGroup();

	RecordPlayService::instance()->setVolume(ucVolume);
	((PopupButton*)(buttonBar()->button(BUTTON_RECORD_TIMELEN)))->setValue(m_qui32RecordDuration);

	connect( RecordPlayService::instance(), SIGNAL(sigRecordResult(int)), this, SLOT(onRecordResult(int)) );//录音结果

	//m_pStopButton->setEnabled(false);
}


/*************************************************
功能： 析构函数
*************************************************************/
RecordView::~RecordView()
{
    if((RecordPlay::RECORD_STATE_RECORDING == m_eRecordState)/* || (RecordPlay::RECORD_STATE_RECORDING_PAUSED == m_eRecordState)*/)
    {
        cancelRecord();
    }

    ShutDownPower::getInstance()->start();   // 开始定时关机
    //RecordPlayService::instance()->stop();

    emit sigClosed();
}

/*************************************************
功能： 取消音频录制，删除音频文件
*************************************************************/
void RecordView::cancelRecord()
{
    //先停止录音，释放文件操作权限
    RecordPlayService::instance()->stopRecord();

    //删除文件
    QFile file(m_strRecordFileName);
    logInfo(QString("cancel record file name: %1.").arg(m_strRecordFileName));

    if(file.exists())
    {
        if(!(file.remove()))
        {
            logError("remove record file failed.");
        }
    }

    return;
}

/*************************************************
功能： 创建图表内容
返回：
      图表
*************************************************************/
ChartWidget* RecordView::createChart()
{
    //窗口主控件
    ChartWidget *pWidget = new ChartWidget;

    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->setContentsMargins(0, 0, 0, 0);
    vLayout->addStretch();

    //录音图标
    QLabel *m_pRecordIcon = new QLabel;
    m_pRecordIcon->setScaledContents( true );
    m_pRecordIcon->setFixedWidth( Window::WIDTH * 0.7 );
    m_pRecordIcon->setFixedHeight( Window::HEIGHT * 0.5 );
    QPixmap recordPixmap(RECORD_PIXMAP_PATH);
    m_pRecordIcon->setPixmap(recordPixmap);

    //放置录音图标的水平布局
    QHBoxLayout *hLayout = new QHBoxLayout();
    hLayout->addStretch();
    hLayout->addWidget( m_pRecordIcon );
    hLayout->addStretch();
    hLayout->setContentsMargins(0, 0, 0, 0);
    hLayout->setSpacing( 0 );

    vLayout->addSpacing( 50 );
    vLayout->addLayout( hLayout,7 );

    //音频文件label
    m_plabelFileName = new QLabel(this);
    m_plabelFileName->setAlignment(Qt::AlignHCenter|Qt::AlignBottom);
    m_plabelFileName->setText("");
    vLayout->addWidget(m_plabelFileName, 1);

    //已录制时间label
    m_plabelElapsedTime = new QLabel(this);
    m_plabelElapsedTime->setAlignment(Qt::AlignHCenter|Qt::AlignBottom);
    m_plabelElapsedTime->setText("0:0:0");
    QFont font = m_plabelElapsedTime->font();
    font.setPointSize(32);
    m_plabelElapsedTime->setFont(font);
    vLayout->addWidget(m_plabelElapsedTime, 1);
    pWidget->setLayout(vLayout);

    return pWidget;
}

/*************************************************
功能： 数据初始化
*************************************************************/
void RecordView::initDatas(void)
{
    m_eRecordState = RecordPlay::RECORD_STATE_NONE;
    m_iTimerIDRefresh = -1;
    m_uiElapsedTime = 0;
    m_uiTotalRecordingTime = 0;
    m_qui32RecordDuration = 0;
    return;
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void RecordView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case BUTTON_RECORD:
    {
        if(RecordPlay::RECORD_STATE_NONE == m_eRecordState)
        {
            startRecord();
        }
        /*else if( RecordPlay::RECORD_STATE_RECORDING_PAUSED == m_eRecordState )
        {
            resumeRecord();

        }
        else if( RecordPlay::RECORD_STATE_RECORDING == m_eRecordState )
        {
            pauseRecord();
        }*/
        else if(RecordPlay::RECORD_STATE_RECORDING == m_eRecordState)
        {
            if(m_bStopEnable)
            {
                stopRecord();
            }
        }
        else
        {
            logWarning(QString("invalid state: %1.").arg(m_eRecordState));
        }

        break;
    }
        /*case BUTTON_STOP_RECORD:
    {
        if(m_bStopEnable)
        {
            if( ( RecordPlay::RECORD_STATE_RECORDING == m_eRecordState ) || ( RecordPlay::RECORD_STATE_RECORDING_PAUSED == m_eRecordState ) )
            {
                stopRecord();
            }
        }
        else
        {
            logWarning("can not stop record temporarily.");
        }
        break;
    }*/
    default:
        break;
    }
    return;
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void RecordView::onButtonValueChanged( int id, int iValue )
{
    switch(id)
    {
    case BUTTON_RECORD_TIMELEN://音量
    {
        quint32 quiValue = (quint32)iValue;
        if(quiValue != m_qui32RecordDuration)
        {
            m_qui32RecordDuration = quiValue;

            ConfigInstance *pConfig = ConfigManager::instance()->config();
            pConfig->beginGroup(Module::GROUP_APP);
            pConfig->setValue(m_qui32RecordDuration, APPConfig::KEY_RECORD_DURATION);
            pConfig->endGroup();
            pConfig->save();
        }
        break;
    }
    default:
        break;
    }
    return;
}

/*************************************************
功能： 启动录音
*************************************************************/
void RecordView::startRecord()
{
    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        MsgBox::informationWithoutAutoAccept("", trUtf8("Fail to start recording, the disk is occupied."));
        return;
    }

    //获取文件名
    m_strRecordFileName = RecordPlay::getRecordFileName(m_eType, m_eAEType);

    //QFileInfo fileInfo(m_strRecordFileName);
    m_plabelFileName->setText(""/*fileInfo.fileName()*/);   //不显示不匹配的文件名
    QFont font = m_plabelFileName->font();
    font = getRewidthFont(font, m_strRecordFileName, Window::WIDTH);
    m_plabelFileName->setFont(font);

    //开始录音
    RecordPlayService::instance()->setRecordType(m_eType, m_eAEType);
    RecordPlayService::instance()->startRecord(m_strRecordFileName);

    m_eRecordState = RecordPlay::RECORD_STATE_RECORDING;

    //刷新时间
    m_uiTotalRecordingTime = 0;
    updateElapsedTimeLabel();

    //开始刷新时间
    startRefresh();

    //m_pRecordButton->setTitle(RECORD_PLAY_TRANSLATE(RecordPlayViewString::TEXT_PAUSE));//按钮变为暂停
    m_pRecordButton->setTitle(RECORD_PLAY_TRANSLATE(RecordPlayViewString::TEXT_STOP));//按钮变为停止
    m_bStopEnable = false;

    //m_pStopButton->setEnabled(false);
    return;
}

/*************************************************
功能： 启动刷新
*************************************************************/
void RecordView::startRefresh()
{
    if(-1 == m_iTimerIDRefresh)
    {
        m_iTimerIDRefresh = startTimer(REFRESH_TIME_INTERVAL_1S);
    }
    return;
}

/*************************************************
功能： 停止刷新
*************************************************************/
void RecordView::stopRefresh()
{
    if(m_iTimerIDRefresh != -1)
    {
        killTimer(m_iTimerIDRefresh);
        m_iTimerIDRefresh = -1;
    }
    return;
}

/*************************************************
功能： 更新已录音时间
*************************************************************/
void RecordView::updateElapsedTimeLabel(void)
{
    UINT32 uiHour = m_uiTotalRecordingTime / (MINUTE_PER_HOUR * SECOND_PER_MINUTE);
    UINT32 uiMin = (m_uiTotalRecordingTime % (MINUTE_PER_HOUR * SECOND_PER_MINUTE)) / SECOND_PER_MINUTE;
    UINT32 uiSec = (m_uiTotalRecordingTime % (MINUTE_PER_HOUR * SECOND_PER_MINUTE)) % SECOND_PER_MINUTE;

    QString qsTime = "";
    qsTime.sprintf("%02d:%02d:%02d", uiHour, uiMin, uiSec);
    m_plabelElapsedTime->setText(qsTime);
    return;
}

/*************************************************
功能： 恢复录音
*************************************************************/
void RecordView::resumeRecord()
{
    m_eRecordState = RecordPlay::RECORD_STATE_RECORDING;
    startRefresh();
    m_pRecordButton->setTitle(RECORD_PLAY_TRANSLATE(RecordPlayViewString::TEXT_PAUSE));
    RecordPlayService::instance()->resumeRecord();
    return;
}

/*************************************************
功能： 暂停录音
*************************************************************/
void RecordView::pauseRecord()
{
    m_eRecordState = RecordPlay::RECORD_STATE_RECORDING_PAUSED;
    stopRefresh();
    m_pRecordButton->setTitle(RECORD_PLAY_TRANSLATE(RecordPlayViewString::TEXT_CONTINUE));
    RecordPlayService::instance()->pauseRecord();
    return;
}

/*************************************************
功能： 停止录音
*************************************************************/
void RecordView::stopRecord()
{
    RecordPlayService::instance()->stopRecord();
    m_pRecordButton->setTitle(RECORD_PLAY_TRANSLATE(RecordPlayViewString::TEXT_RECORD));
    stopRefresh();

    emit sigRecorded(m_strRecordFileName, m_uiTotalRecordingTime);
    logInfo(QString("record file (%1) completed.").arg(m_strRecordFileName));

    m_eRecordState = RecordPlay::RECORD_STATE_NONE;
    m_uiTotalRecordingTime = 0;
    updateElapsedTimeLabel();
    m_plabelFileName->setText("");

    return;
}

/*************************************************
功能： 定时事件处理
输入参数: event:定时器事件
*************************************************************/
void RecordView::timerEvent(QTimerEvent *event)
{
    if(event->timerId() == m_iTimerIDRefresh)
    {
        ++m_uiTotalRecordingTime;
        if(m_uiTotalRecordingTime > MIN_RECORD_TIME)
        {
            m_bStopEnable = true;
            //m_pStopButton->setEnabled(true);
        }

        updateElapsedTimeLabel();//刷新时间

        if(m_uiTotalRecordingTime >= (PER_MIN_SECOND * m_qui32RecordDuration))
        {
            stopRecord();
        }
    }
    else
    {
        logWarning("not record refresh timer id.");
    }
    return;
}

/*************************************************
功能： 录音结果槽函数
输入参数：
        iResult -- 录音结果
*************************************************************/
void RecordView::onRecordResult( int iResult )
{
    if((RecordPlay::RECORD_STATE_RECORDING == m_eRecordState)
            || (RecordPlay::RECORD_STATE_NONE == m_eRecordState))
    {
        logInfo(QString("record file (%1) result: %2.").arg(m_strRecordFileName).arg(iResult));
        if(iResult < 0)
        {
            stopRecord();
            MsgBox::warning("", trUtf8("Unrecorded!"));
        }
        else
        {
            //m_pRecordButton->setTitle(RECORD_PLAY_TRANSLATE(RecordPlayViewString::TEXT_RECORD));
            //emit sigRecorded(m_strRecordFileName, m_uiTotalRecordingTime);
            //logInfo(QString("record file (%1) completed.").arg(m_strRecordFileName));
            //MsgBox::information("", trUtf8("Recorded!"));
        }
    }
    return;
}

