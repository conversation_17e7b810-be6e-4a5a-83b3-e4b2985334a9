/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* HFCTAmpService.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年12月14日
* 摘要：为了适配接入定制接入终端，后产生的多用户的使用场景，进行的修改

* 当前版本：1.0
*/

#ifndef HFCTAMPSERVICE_H
#define HFCTAMPSERVICE_H

#include "HFCTService.h"

class MODULESHARED_EXPORT HFCTAmpService : public HFCTService
{
    Q_OBJECT
public:
    /****************************
    功能： 模块单例
    *****************************/
    static HFCTAmpService* instance();
private:
    /****************************
    功能： 构造函数
    *****************************/
    HFCTAmpService();

    /****************************
    功能： 析构函数
    *****************************/
    ~HFCTAmpService();

    /****************************
    功能： disable 拷贝
    *****************************/
    HFCTAmpService( const HFCTAmpService& other );

    /****************************
    功能： disable 赋值
    *****************************/
    HFCTAmpService & operator = (const HFCTAmpService &);

};

#endif // HFCTAMPSERVICE_H
