#include "settingswitch.h"
#include "Widget.h"
#include <QHBoxLayout>
#include <QPainter>
#include <QBitmap>
#include <QDebug>

#define WIDTH_RATIO 0.8
#define HEIGHT_RATION 0.5
#define MARGIN_SIZE 10
#define SWITCH_WIDTH 100
#define SWITCH_HEIGHT 60

#define TEXT_WIDTH  280
#define TEXT_HEIGHT 60

/*************************************************
输入参数:parent -- 父窗体指针
功能： 构造函数
*************************************************************/
SettingSwitch::SettingSwitch(bool bWifiOn, const QString &title, QWidget *parent)
    : Setting<PERSON>rame(parent)
{
    m_pSwitch = new SwitchMenu(this);
    m_pSwitch->setIconSize(SWITCH_WIDTH, SWITCH_HEIGHT);
    m_pSwitch->initialize(":images/back.png", ":images/front.png");
    m_pSwitch->setSwitchState(bWifiOn);
    m_pSwitch->setFocusPolicy(Qt::NoFocus);

    m_pTitle = new QLabel(title, this);
    m_pTitle->setFixedSize(TEXT_WIDTH, TEXT_HEIGHT);

    QHBoxLayout *hLayout = new QHBoxLayout;
    hLayout->setMargin(MARGIN_SIZE);
    hLayout->setSpacing(0);
    hLayout->addWidget(m_pTitle, Qt::AlignLeft);
    hLayout->addWidget(m_pSwitch, Qt::AlignRight);
    setLayout(hLayout);

    connect(m_pSwitch, SIGNAL(sigSwitchStatus(bool)), this, SIGNAL(sigSwitchStatusChanged(bool)));
}

/*************************************************
功能： 点击开关
*************************************************************/
void SettingSwitch::clickSwitch(void)
{
    m_pSwitch->clickSwitchMenu();
    return;
}

/************************************************
 * 输入参数 : bOn  true -- 打开
 *                false -- 关闭
 * 功能     : 按下状态切换开关
 ************************************************/
void SettingSwitch::setSwitchState(bool bOn, bool bSendSignals)
{
    m_pSwitch->setSwitchState(bOn, bSendSignals);
    return;
}
/*************************************************
功能： resize事件，用来重定义图片和文字的大小
*************************************************************/
void SettingSwitch::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    QFont fontHeight = getReheightFont(m_pTitle->font(), height() * HEIGHT_RATION);

    QFontMetrics fm(fontHeight);
    QFont font = fontHeight;
    if(m_pTitle->width() * WIDTH_RATIO < fm.width(m_pTitle->text()))
    {
        font = getRewidthFont(m_pTitle->font(), m_pTitle->text(), m_pTitle->width() * WIDTH_RATIO);
    }
    m_pTitle->setFont(font);

    return;
}
