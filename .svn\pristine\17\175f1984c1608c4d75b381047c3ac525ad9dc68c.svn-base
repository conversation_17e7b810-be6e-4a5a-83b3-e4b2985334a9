/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* SystemSettingGrounp.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月25日
* 摘要：网络设置切换开关以及编辑输入框的集合的定义

* 当前版本：1.0
*/

#ifndef SYSTEMSETTINGGROUNP_H
#define SYSTEMSETTINGGROUNP_H

#include <QFrame>
#include <QPushButton>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QVector>
#include <QHash>
#include <QKeyEvent>
#include "lineeditgrounp.h"
#include "SwitchMenu.h"
#include "buttonlabel/buttonlabel.h"
#include "datadefine.h"

class SystemSettingGrounp : public QFrame
{
    Q_OBJECT
public:
    /*************************************************
    函数名： SystemSettingGrounp(const QString& qsGrounpName,QWidget *parent)
    输入参数:qsGrounpName：group的标签名
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit SystemSettingGrounp(const QString& qsGrounpName,QWidget *parent = 0);

    /************************************************
     * 函数名   : addLineEditItem
     * 输入参数 : editNameIndex：输入框标签名；lineEdit：输入框的指针
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 添加输入编辑框
     ************************************************/
    void addLineEditItem(LineEditGrounp::NAME_INDEX editNameIndex, QLineEdit *lineEdit );

    /************************************************
     * 函数名   : getTextFromString
     * 输入参数 : editNameIndex：编辑框标签名
     * 输出参数 : NULL
     * 返回值   : QString
     * 功能     : 返回输入框内的文本信息
     ************************************************/
    const QString getTextFromString( LineEditGrounp::NAME_INDEX editNameIndex );

    /************************************************
     * 函数名   : activePreviousWidget
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 将焦点往前移动
     ************************************************/
    virtual void activePreviousWidget( void );

    /************************************************
     * 函数名   : activeNextWidget
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 将焦点往后移动
     ************************************************/
     virtual void activeNextWidget( void );

    /************************************************
     * 函数名   : activeEnterPress
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 模拟有enter操作的控件，被点击的效果
     ************************************************/
    virtual void activeEnterPress( void );

    /************************************************
     * 功能     : 清除所有的焦点状态
     ************************************************/
    virtual void clearAllFocus();

    /************************************************
     * 功能     : 设置当前控件的焦点状态
     ************************************************/
    virtual void setCurFocus();

    /************************************************
     * 功能     : 获取控件激活状态
     ************************************************/
    bool getActiveState();

    /************************************************
     * 功能     : 设置控件激活状态
     ************************************************/
    void setActiveState(bool bActived);

signals:
    void sigOk( const QStringList& strList );   // signal 按下ok键发出的

public slots:
    /************************************************
     * 函数名   : isDisableLineEdit
     * 输入参数 : bisDisable：切换开关的状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : slot 和切换开关进行绑定的槽函数
     ************************************************/
    void isDisableLineEdit(bool bisDisable);

    /************************************************
     * 函数名   : onOk
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 和ok按键绑定的槽函数
     ************************************************/
    void onOk();

    /************************************************
     * 函数名   : onCancel
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 和cancel按键绑定的槽函数
     ************************************************/
    virtual void onCancel();
private:
    /************************************************
     * 函数名   : createMainView
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建主界面
     ************************************************/
    void createMainView( void );

protected:
    LineEditGrounp  *m_pLineEditGrounp; // 输入编辑框的集合
    QVector<QWidget*>   m_vAllin;       // 各子窗体的结合，用来完成焦点的切换
    buttonLabel *m_pOkLabel;
    //buttonLabel *m_pCancelLabel;
    SwitchMenu  *m_pSwitchMenu;     // 切换开关
    bool m_bActived;

private:
    //QLabel  *m_pTitle;              // 表示group类型的标签
    int m_iCurFocusIndex;
    QHash<LineEditGrounp::NAME_INDEX,QLineEdit* > m_hLineEdit;// 存放各输入编辑框的hash
};

#endif // SYSTEMSETTINGGROUNP_H
