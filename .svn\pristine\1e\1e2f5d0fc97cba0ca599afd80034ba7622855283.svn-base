/*
* Copyright (c) 2017.11，南京华乘电气科技有限公司
* All rights reserved.
*
* infraredservice.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年11月10日
* 摘要：红外服务

* 当前版本： 1.1
* 作者： wujun
* 修改日期： 2017.12.20
* 修改内容： 从配置文件获取IP
*/

#ifndef INFRAREDSERVICE_H
#define INFRAREDSERVICE_H

#include <QThread>
#include <QMutex>
#include <QTimerEvent>
#include "gigecamera.h"
#include "multiservice/multiuserservice.h"
#include "module_global.h"
#include <QDateTime>
#include <QDebug>
#include <QProcess>
namespace Infrared
{
typedef struct _InfraredData  //红外数据
{
    UINT8 aucData[FRAME_BUF_SIZE];  //数据
    FrameInfo stFrameInfo;          //数据信息
}InfraredData;

typedef enum _InfraredAffair  //红外事务
{
    INIT = 0,
    STOP,
    AFFAIR_COUNT
}InfraredAffair;

typedef enum _InfraredType  //红外类型
{
    INVALID = 0,
    GUIDE,
    FILIAR
}InfraredType;
}

class MODULESHARED_EXPORT InfraredService : public MultiUserService
{
    Q_OBJECT
public:
    /****************************
    功能： 模块单例
    *****************************/
    static InfraredService* instance();

    /*************************************************
    函数名： ~InfraredService()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~InfraredService();

    /*************************************************
    函数名： startReadData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 开始读取数据
    *************************************************************/
    void startReadData();

    /*************************************************
    函数名： stopReadData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 停止读取数据
    *************************************************************/
    void stopReadData();

    /*************************************************
    函数名： readParameters(Params *pstParams)
    输入参数： pstParams：红外参数
    输出参数： NULL
    返回值： 操作结果
    功能： 读取红外参数
    *************************************************************/
    bool readParameters(Params *pstParams);

    /*************************************************
    函数名： readParamFromFile(ObjParam *pstParams)
    输入参数： pstParams：环境参数
    输出参数： NULL
    返回值： 结果状态
    功能： 读取红外环境参数
    *************************************************************/
    bool readParamFromFile( ObjParam &params );

    /*************************************************
    函数名： setIPtoIR()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置与红外连接的网口的ip
    *************************************************************/
    void setIPtoIR();

    /*************************************************
    函数名： restoreIPtoDev()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 恢复设备的网段信息
    *************************************************************/
    void restoreIPtoDev();

    /*************************************************
    函数名： start()
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果
    功能： 启动业务
    *************************************************************/
    bool start() { return true; }

    /*************************************************
    函数名： stop()
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果
    功能： 终止业务
    *************************************************************/
    bool stop() { return true; }

    /***************************************************
     * 功能，初始化模块
     * *************************************************/
    void initModule();

    /***************************************************
     * 功能，反初始化模块
     * *************************************************/
    void deinitModule();

    /***************************************************
     * 功能，是否已初始化
     * *************************************************/
    bool isInited();

    /***************************************************
     * 功能，是否初始化中
     * *************************************************/
    bool isIniting();

    /***************************************************
     * 功能，获取红外模组类型
     * *************************************************/
    Infrared::InfraredType getInfraredType();

    /***************************************************
     * 功能，初始化红外模组接入类型
     * *************************************************/
    Infrared::InfraredType initInfraredType();


signals:
    /*************************************************
    传递参数： data：红外数据
    说明： 红外数据信号
    *************************************************************/
    void sigData(QSharedPointer<Infrared::InfraredData> data,MultiServiceNS::USERID userId);

    /*************************************************
    传递参数： NULL
    说明： 读取数据失败信号
    *************************************************************/
    void sigReadDataFail(MultiServiceNS::USERID userId);

    /*************************************************
    传递参数： bRet：初始化结果
    说明： 红外初始化结果信号
    *************************************************************/
    void sigInfraredInitResult(bool bRet);

    /*************************************************
    传递参数： NULL
    说明： 创建采集定时器信号
    *************************************************************/
    void sigStartTimer();

    /*************************************************
    传递参数： NULL
    说明： 停止采集定时器信号
    *************************************************************/
    void sigStopTimer();

    /*************************************************
    传递参数： NULL
    说明： 红外设备切换
    *************************************************************/
    void sigInfraredTypeChanged(Infrared::InfraredType eInfraredType);

private slots:
    /*************************************************
    函数名： onAffair(quint16 usAffair, void *pInfo, quint16 usID)
    输入参数： usAffair：事务
              usID：当前事务的序号
    输出参数： pInfo：存放结果数据指针
    返回值： NULL
    功能： 处理异步事务
    *************************************************************/
    void onAffair(quint16 usAffair, void *pInfo, quint16 usID);

    /*************************************************
    函数名： onStartTimer()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 启动采集定时器
    *************************************************************/
    void onStartTimer();

    /*************************************************
    函数名： onStopTimer()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 停止采集定时器
    *************************************************************/
    void onStopTimer();

    /*************************************************
    函数名： onOutLog()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 获取指令输出日志
    *************************************************************/
    void onOutLog();


protected:
    /*************************************************
    函数名： timerEvent(QTimerEvent *event)
    输入参数： event：定时器事件
    输出参数： NULL
    返回值： NULL
    功能： 定时器事件处理
    *************************************************************/
    void timerEvent(QTimerEvent *event);

    /*************************************************
    功能： 启动采样业务的额外处理   （一般指在子类中的额外操作）
    输入参数：
             userId -- 用户ID
    返回：
          额外处理是否成功
    *************************************************/
    virtual bool startSampleExt( MultiServiceNS::USERID userId );

    /*************************************************
    功能： 停止采样业务的额外处理   （一般指在子类中的额外操作）
    输入参数：
             userId -- 用户ID
    返回：
          额外处理是否成功
    *************************************************/
    virtual bool stopSampleExt( MultiServiceNS::USERID userId );

private:
    /*************************************************
    函数名： InfraredService()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    InfraredService();

    /*************************************************
    函数名： infraredInit()
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果
    功能： 红外初始化
    *************************************************************/
    bool infraredInit();

    /*************************************************
    函数名： restartCamara()
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果
    功能： 重启红外摄像机
    *************************************************************/
    bool restartCamera();

    /*************************************************
    函数名： resetValidValue()
    输入参数： params:红外设置的参数
    输出参数： NULL
    返回值： NULL
    功能： 校验红外设置的参数，如辐射率或光学传输率不在合理范围，则置为最小值
    *************************************************************/
    void resetValidValue(ObjParam &params);

    /***************************************************
     * 功能，初始化模块
     * *************************************************/
    void pfnInitModule();

    /***************************************************
     * 功能，反初始化模块
     * *************************************************/
    void pfnDeinitModule();

    /***************************************************
     * 功能，执行获取红外接入装置命令
     * *************************************************/
    void pfnInfraredType();

    /***************************************************
     * 功能，执行获取红外接入装置命令
     * *************************************************/
    void pfnFlierInfraredType();

private:
    enum
    {
        MAX_INIT_FAIL_TIMES = 10,
        MAX_READ_PARAMS_FAIL_TIMES = 3,
        //MAX_READ_DATA_FAIL_TIMES = 60,
        MAX_READ_DATA_FAIL_TIMES = 20,
        MAX_PING_FAIL_TIMES = 4,
        TIME_50MS = 50,
        TIME_3S = 3000
    };

    QThread *m_pThread;
    QMutex m_mutexAffair;  //事务互斥锁
    HCAffair *m_pAffair;  //事务模块

    volatile bool m_bIsStopService;
    volatile bool m_bIsReadData;
    GigeData m_stGigedata;
    //int m_iReadDataFailTimes;
    int m_iPingFailTimes;
    int m_iSampleTimerId;

    ObjParam m_objParam;    //环境参数
    int m_iRet;     //摄像头初始化和关闭操作的返回结果
    bool m_bInited; //已初始化标志
    bool m_bIniting; //初始化中
    QProcess *myprocess;
    Infrared::InfraredType m_eInfraredType;
};

#endif // INFRAREDSERVICE_H
