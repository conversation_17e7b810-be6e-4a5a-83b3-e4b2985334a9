﻿/*
 * Copyright (c) 2015.11，南京华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：UHFHFCTAETEVApi.h
 *
 *
 * 初始版本：1.0
 * 作者：吴昌盛
 * 创建日期：2016年1月13日
 * 摘要：UHF HFCT AE TEV通讯的API接口
 *
 * 说明:接收器接UHF、HFCT、同步器，接收器通过Zigbee无线与ARM板通讯，ARM通过串口与Zigbee连接；
 *      AE、TEV直接串口至ARM。
 *      2019年7月4日
 *      新增电缆电流检测调理器外设支持,数据链路如下：arm<--uart-->zigbee <--cc2510--> cable current terminal device
 *
 *      用户连接无线调理器读取采集数据控制流程：
 *        1. start_search_wireless (eDeviceType) --扫描指定类型的无线调理器
 *        2. get_wireless_addrlist (eDeviceType) --获取调理器地址列表
 *        3. set_wireless_addrlist  (eDeviceType) --设置调理器地址
 *
 *        <4...> 设置调理器参数 <可选>
 *        5. get_xxx_data  读取采集数据
 */

#ifndef _UHFHFCTAETEVAPI_H_
#define _UHFHFCTAETEVAPI_H_

#include "DriverDataDefine.h"

#ifdef __cplusplus    
extern "C"
{
#endif

// #define   WIRELESS_AE_TEST   //无线AE测试开关
#define   AETOCONDITIONER         							//AE连接到调理器定义
//#define   UHFHFCTAETEV_DEBUG      					//调试定义 包含打印收发报文
//#define   AE_FILTER_ENABLE      //开启AE带宽设置功能，打开时为R3版本，否则为R2版本

#ifdef      UHFHFCTAETEV_DEBUG
#define     UHFHFCTAETEV_dbg(fmt,args...) 	do{printf("[UHFHFCTAETEV %s:] "fmt,__FUNCTION__,##args);}while(0);
#else
#define     UHFHFCTAETEV_dbg(fmt,args...)
#endif

#define     CONDITIONERROGRM        "/home/<USER>/T90S.bin"       //调理器程序
#define     ZIGBEEPROGRM            "/home/<USER>/app_crc.bin"    //Zigbee程序

#define     CALIBRATING             (2)       					//用于TEV校准返回值 表示校准中......

#define     SPECTTRUMNUM            (60)       					//UHF HFCT频谱字节数
#define     AEWAVENUM               (250)      					//超声波波形字节数
#define     AEPULSEMAXNUM           (1000)     					//AE单帧脉冲计数最大值
#define     ADDRBYTENUM             (8)        					//设备地址字节数
#define     VERSIONBYTENUM          (18)       					//版本字节数
#define     MAXWIRELESSDEVICENUM    (10)       					//单种类型无线设备搜索上限个数

#define     TEV_PRPS_COUNT          (72)                        //TEV PRPS数据点数

//无线调理器默认参数定义
#define AE_WIRELESS_PULSE_NUM       (1)//无线调理器每次只上送一个点
#define AE_WIRELESS_SAMPLE_RATE     (40960)
#define AE_WIRELESS_AD_RANGE        (256)//设备类型
#define AE_WIRELESS_AD_SAMPLE_BIT   (8)
#define AE_WIRELESS_AD_SAMPLE_MODE  (0)//代表标准二进制

typedef enum
{
    UHF_TERMINAL = 0, //UHF终端
    HFCT_TERMINAL, //HFCT终端
    SYNCHRONIZER, //同步器
    RECEIVER, //接收器
    EXTERNAL_DEVICE, //外部设备
    AE_TERMINAL, //超声波
    TEV_TERMINAL, //地电波
    CABLE_CURRENT_TERMINAL, // 电缆电流测量终端
    NODEVICEType = 0xff, //初始值
} WLDeviceType;

//UHF滤波方式
typedef enum
{
    ALL_PASS_FILTER = 0, //全通滤波
    LOW_PASS_FILTER, //低通滤波
    HIGH_PASS_FILTER, //高通滤波
} UHFFilterControl;

//UHF HFCT增益衰减
typedef enum
{
    GAIN_20 = 0, //20dB增益  前置增益开
    GAIN_0, //0dB增益   前置增益关
    ATTENUATE_0, //0dB衰减
    ATTENUATE_20, //-20dB衰减
    ATTENUATE_40, //-40dB衰减
    ATTENUATE_60, //-60dB衰减
} UHFHFCTGain;

//同步源
typedef enum
{
    INTER_SYNC = 0, //内同步
    WIRELESS_SYNC, //电源同步
    LIGHT_SYNC, //光同步
    OUTER_SYNC //外同步
} SyncSource;

//频谱状态
typedef enum
{
    SPECTRUM_INSIDE_RANGE = 0, //频谱范围内
    SPECTRUM_UNDER_LOW, //频谱小于范围
    SPECTRUM_ABOVE_HIGH, //频谱大于范围
} SpectrumState;

//同步状态
typedef enum
{
    Not_Sync = 0, //未同步
    Synced, //已同步
} SyncState;

//地址状态
typedef enum
{
    Addr_idle = 0, //空闲
    Addr_busy, //已占用
} AddrState;

//工作模式
typedef enum
{
    Amplitude = 0, //幅值模式
    PRPD, //PRPD
    Pulse, //脉冲模式
    Waveform, //波形模式
    AllViews,//一次上送所有图谱数据
    NoneMode,
} WorkMode;

//单位
typedef enum
{
    dB = 1, //dB
    mV = 3, //mV
} AE_Unit;

//超声波连接状态
typedef enum
{
    AE_NotConnect = 0, //未连接
    AE_Connected, //已连接
} AE_ConnectState;

//电网频率
typedef enum
{
    FREQ_50 = 50, //50Hz
    FREQ_60 = 60, //60Hz
    FREQ_UNKNOW = 1, //未知
} Frequency;

//AE通道
typedef enum
{
    AirSound = 0, //空声
    SurfaceMount, //表贴
    AE_Wireless, //无线通道  保留接口，暂不用
} AE_CHANNEL;

//AE命令
typedef enum
{
    AE_OPEN_POWER = 0, //打开电源
    AE_START_SAMPLE, //开始采集
    AE_GET_DATA, //获取数据
    AE_STOP_SAMPLE, //停止采集
    AE_CLOSE_POWER, //关闭电源
} AE_CMD; //除电源外，只针对PRPD和脉冲模式两种模式有效，其它两种模式为00

//TEV命令
typedef enum
{
    TEV_OPEN_POWER = 0, //打开电源
    TEV_GET_DATA = 2, //获取数据
    TEV_CLOSE_POWER = 4, //关闭电源
} TEV_CMD; //只针对PRPD和脉冲模式两种模式有效，其它两种模式为00

//AE校准命令
typedef enum
{
    AEDEFAULTPRAM = 0, //恢复默认值
    AE60dBDrift = 1, //60dB档位  零漂校准
    AE60dBCoeff, //60dB档位  系数校准
    AE80dBDrift, //80dB档位  零漂校准
    AE80dBCoeff, //80dB档位  系数校准
    AE100dBDrift, //100dB档位 零漂校准
    AE100dBCoeff, //100dB档位 系数校准
    AE60dBData, //60dB档位  AE值读取
    AE80dBData, //80dB档位  AE值读取
    AE100dBData, //100dB档位 AE值读取
} CalibrateAECmd;

//TEV校准命令

typedef enum _TevCaliCommand
{
    TEV_RESET_CALI_PARAM = 0,//恢复默认参数

    TEV_CALI_GAIN1,//40dB档位校准
    TEV_CALI_GAIN5,//20dB档位校准
    TEV_CALI_GAIN50,//0dB系数校准

    TEV_SET_GAIN1_CALI_COEFF, //设置tev采集档位校准系数K，B
    TEV_SET_GAIN5_CALI_COEFF,
    TEV_SET_GAIN50_CALI_COEFF,

    TEV_TEST_GET_SAMPLE_DATA,//读取TEV测量值，用于测试
}TevCaliCommand;

/* 无线设备地址 */
typedef struct
{
    UINT8 caAddr[ADDRBYTENUM]; //设备地址
    AddrState eAddrState; //设备空闲状态
} WLDeviceAddr;

/* 无线设备版本 */
typedef struct
{
    UINT8 caVersion[VERSIONBYTENUM]; //版本
    UINT8 ucVerLen; //版本字节数
} WLDeviceVer;

/* UHF HFCT 数据类 */
typedef struct
{
    UHFFilterControl eBandWidth; //带宽  UHF独有
    SpectrumState eSpectrumState; //频谱状态 HFCT独有
    SyncSource eSyncSource; //同步源
    SyncState eSyncState; //同步状态
    INT8 cMaxSpectrum; //转换后的最大频谱
    INT8 cGain; //增益 HFCT增益数据为0~60对应菜单0~-60
    INT8 caSpectrum[SPECTTRUMNUM]; //转换后的频谱值
} UHFHFCTData;

/** 电缆电流测量终端数据类 */

typedef struct
{
    float fGroundCurrent;  //接地电流
    float fLoadCurrent;  //负载电流
    UINT8 ucResv[16]; //保留字节
}CableCurrentData;

/*********AE带宽枚举**********/
typedef enum _AEFilter
{
    FILTER_20_80_kHZ = 0,
    FILTER_80_300_kHZ = 1,
    
    FILTER_MIN = FILTER_20_80_kHZ,
    FILTER_MAX = FILTER_80_300_kHZ,
    FILTER_DEFAULT = FILTER_20_80_kHZ
}AEFilter;

/* AE 工作参数的类 对上使用 对下使用的在UHFHFCTAETAE.h */
typedef struct
{
    WorkMode eWorkMode; //工作模式
    AE_Unit eAE_Unit; //单位 dB mV
    float fGain; //增益
    SyncSource eSyncSource; //同步源  默认电源同步
    UINT16 usFComponent; //FFT所需的频率分量，有效值 （10,20,30… 500） 步长10Hz
    UINT8 ucNgrid; //波形图谱检测时显示的电网周期数据（1-10）
    float fAmplitudeThd; //幅值阈值
    float fPrpdThd; //PRPD阈值
    float fPulseThd; //脉冲阈值
    float fWaveThd; //波形阈值
    UINT16 usPrpdBlocking; //PRPD的关门时间
    UINT16 usPulseBlocking; //脉冲模式的关门时间
    UINT16 usPulseGating; //脉冲模式的开门时间
#ifdef AE_FILTER_ENABLE
    AEFilter eAEFilter;//AE带宽模式
#endif
} AEWorkSetForUp;

/* AE 数据 */
typedef struct
{
    SyncState eSyncState; //同步状态
    SyncSource eSyncSource; //同步源
    AE_CHANNEL eAE_CHANNEL; //传感器类型
    float fGain; //增益
    UINT32 uiSamplingRate; //采样率
    UINT32 uiADRange; //AD量程
    UINT8 ucADSampling; //AD采样位数，例如13代表13位AD采样精度
    UINT8 ucADSampDataFormat; //AD采样数据格式（0：标准二进制，1：二进制补码）
    WorkMode eWorkMode; //工作模式
    AE_Unit eAE_Unit; //单位 dB mV
    UINT16 usPulseNum; //脉冲计数 单帧的脉冲数只有PRPD、脉冲模式有效
    union //数据
    {
        struct
        {
            float fPeakValue; //峰值
            float fRMS; //有效值
            float fFirstFreqComValue; //第一频率分量值
            float fScendFreqComValue; //第二频率分量值
        } AmpData;
        struct
        {
            float fPhaseValue; //相对相位值(0~360)
            float fPeakValue; //峰值
        } PRPDData[AEPULSEMAXNUM];
        struct
        {
            UINT32 uiPulseInterval; //脉冲间隔时间 us
            float fPeakValue; //峰值
        } PulseData[AEPULSEMAXNUM];
        struct
        {
            float faWaveValue[AEWAVENUM]; //250点波形采集值
        } WaveData;
    } Data;
} __attribute__((packed))
AEReadData;


/*  一次上送四种AE数据的结构定义  */
#pragma pack(1)

#define MAX_PHASE_PULSE_NUM_PER_MSG   (50)
#define MAX_WAVE_POINT_PER_MSG        (1024)

//幅值数据定义
typedef struct _AEAmpData 
{
    float fPeakValue; //峰值
    float fRMS; //有效值
    float fH1; //第一频率分量值
    float fH2; //第二频率分量值
}AEAmpData;

//相位数据定义
typedef struct _AEPhaseData
{
    float fPhaseValue; //相对相位值(0~360)
    float fPeakValue; //峰值
}AEPhaseData;

//脉冲数据定义
typedef struct _AEPulseData 
{
    UINT32 uiPulseInterval; //脉冲间隔时间 us
    float fPeakValue; //峰值
}AEPulseData;

//一次采集上送四种图谱的AE结构体定义
typedef struct _AEAllView
{
    SyncState eSyncState; //同步状态
    SyncSource eSyncSource; //同步源
    AE_CHANNEL eAE_CHANNEL; //传感器类型
    float fGain; //增益
    UINT32 uiSamplingRate; //采样率
    UINT32 uiADRange; //AD量程
    UINT8 ucADSampling; //AD采样位数，例如13代表13位AD采样精度
    UINT8 ucADSampDataFormat; //AD采样数据格式（0：标准二进制，1：二进制补码）
    WorkMode eWorkMode; //工作模式
    AE_Unit eAE_Unit; //单位 dB mV
    UINT16 usPhasePointNum;//数据中的相位图谱点数
    UINT16 usPulsePointNum;//数据中的脉冲图谱点数
    UINT16 usWavePointNum;//数据中的波形图谱点数

    AEAmpData stAeAmp;//幅值数据
    AEPhaseData staPhaseData[MAX_PHASE_PULSE_NUM_PER_MSG];//相位数据
    AEPulseData staPulseData[MAX_PHASE_PULSE_NUM_PER_MSG];//脉冲数据
    float faWaveData[MAX_WAVE_POINT_PER_MSG];//波形数据
}AEAllView;

#pragma pack()

/************************************************
 * 功能     ：一次获取AE四种图谱数据
 *
 * 描述     :使用方法:
 *           1.调用set_ae_settings设置参数,需要填写所有项,工作模式需要填写成AllViews
 *           2.调用get_ae_data接口,下发开始采样命令
 *           2.调用get_all_ae_views获取数据并解析
 *              幅值数据 -- 固定长度
 *              相位数据 -- 有效点数为usPhasePointNum,其他为空
 *              脉冲数据 -- 有效点数为usPulsePointNum,其他为空
 *              波形数据 -- 有效点数为usWavePointNum,其他为空
 *           3.如果需要关闭采集,则调用原有的get_ae_data接口即可
 *
 * 输入参数 ：
 *           eCaliCmd -- TEV校准命令
 *           eAE_CHANNEL -- AE通道
 * 输出参数 :
 *           pstCaliInfo -- 底层返回的校准信息,使用方法见描述
 * 返回值  : 0 -- success; else -- fail
 ************************************************/
INT32 get_all_ae_views(AEAllView *pstAEViewData,  AE_CHANNEL eAE_CHANNEL);

//无线AE调理器的数据格式定义
typedef enum _WirelessAEDataDefine
{
    POS_WIRELESSAE_CMD = 0,
    POS_WIRELESSAE_STATUS,
    POS_WIRELESSAE_PARAM,
    POS_WIRELESSAE_WORK_MODE,
    POS_WIRELESSAE_DATA,

    WIRELESSAE_WAVE_DATA_LEN = 250,//波形长250个字节
    WIRELESSAE_COEFFICIENT = 11,//经验值,继承自T90的处理
}WirelessAEDataDefine;

typedef struct
{
    UINT8 ucWorkMode; //工作模式  0幅值检测  1脉冲模式
    UINT8 ucTime; //脉冲计数时间 1，2  单位秒
    UINT8 ucBackGround; //TEV黄色报警
    UINT8 ucAlarm; //TEV红色报警
}__attribute__((packed))
TEVWorkSettings;



//TEV通道枚举
typedef enum _TevChannel
{
    TEV_CHANNEL_0dB = 0,//0~19dB
    TEV_CHANNEL_20dB,//20~39dB
    TEV_CHANNEL_40dB,//40~60dB
    TEV_CHANNEL_COUNT,
}TevChannel;

//TEV系数
typedef enum _CaliStatus
{
    TEV_CALI_UNDO = 0,
    TEV_CALI_DOING,
    TEV_CALI_SUCCEED,
    TEV_CALI_FAILED,
}CaliStatus;

typedef struct _CalibrateParam
{
    float fK;   //系数
    float fB;   //零漂
    CaliStatus eCaliSta;//校准状态
}CalibrateParam;

typedef struct _TEVCalibrateParam
{
    CalibrateParam CaliParam[TEV_CHANNEL_COUNT];
    UINT16 ausDacRef[TEV_CHANNEL_COUNT][20]; //tev 三个采集通道DAC码值参考数组
}TEVCalibrateParam;

typedef struct _AutoCali
{
    CalibrateParam stParam;
    UINT16 ausDacRef[20]; //校准档位对应DAC码值参考数组
}AutoCali;


typedef enum _TEVWorkMode
{
    MODE_AMP = 0,
    MODE_PULSE,
    MODE_PRPS,

    MODE_COUNT,
}TEVWorkMode;

#pragma pack(1)
typedef struct
{
    UINT8 ucWorkMode; //工作模式 0幅值检测  1脉冲模式  2 PRPS模式
    union //数据
    {
        struct
        {
            INT8 cAmpValue; //幅值 当前测量值 -1~61 -1表示<0dB 61表示>60dB
            UINT16 usTevMv;//tev当前档位db值对应的mv值
        } AmpData;
        struct
        {
            UINT32 uiPulseNum; //脉冲数
            UINT32 uiPerPulseNUm; //每周期脉冲数
            UINT32 uiPDSeverity; //放电严重程度
            INT8 cAmpValue; //幅值测量值  -1~61 -1表示<0dB 61表示>60dB
        } PulseData;
        struct
        {
            INT8 cMaxSpectrum; //转换后的最大频谱
            SpectrumState eSpectrumState;//频谱增益状态
            INT8 cSpectrum[TEV_PRPS_COUNT]; //转换后的频谱值
        } PrpsData;
    }Data;
}
TEVReadData;
#pragma pack()

//AE增益等级定义
typedef enum _AEGainLevel
{
    AE_GAIN_LEVEL_60 = 0,
    AE_GAIN_LEVEL_80,
    AE_GAIN_LEVEL_100,
    AE_GAIN_LEVEL_INVALID = 0xFF,
}AEGainLevel;

#pragma pack(1)
typedef  struct  _TevSample
{
    UINT16 ausDacVal[TEV_CHANNEL_COUNT]; //tev三个采集通道测量的当前输入信号对应DAC码值
    UINT8  ucDB; //当前输入信号对应TEV测量dB值
    UINT16 ausDacRef[TEV_CHANNEL_COUNT][20]; //tev 三个采集通道DAC码值参考数组
}TevSample;
#pragma pack()

#pragma pack(1)
typedef struct _R3CaliInfo
{
    float fSampleValue;//采集值,适用于校准步骤一和二
    float fK;//校准系数k,适用于校准步骤三
    float fB;//校准系数b,适用于校准步骤三
    INT8 cStatus;//校准成功与否,适用于校准步骤三
}R3CaliInfo;
#pragma pack()

//TEV校准命令枚举 2018.3.16 R3硬件版本
typedef enum _R3TevCaliCommand
{
    TEV_RETURN_TO_DEFAULT = 0,//恢复默认参数
    
    TEV_CALI_0dB_STEP1,  //0dB档位校准步骤一
    TEV_CALI_0dB_STEP2,  //0dB档位校准步骤二
    TEV_CALI_0dB_STEP3,  //0dB系数校准步骤三
    
    TEV_CALI_20dB_STEP1,  //20dB档位校准步骤一
    TEV_CALI_20dB_STEP2,  //20dB档位校准步骤二
    TEV_CALI_20dB_STEP3,  //20dB系数校准步骤三
    
    TEV_CALI_40dB_STEP1,  //40dB档位校准步骤一
    TEV_CALI_40dB_STEP2,  //40dB档位校准步骤二
    TEV_CALI_40dB_STEP3,  //40dB系数校准步骤三
    
    TEV_READ,//读TEV

}R3TevCaliCommand;

//UHFHFCTAETEVApi版本
const INT8 * UHFHFCTAETEVApi_version(void);

//stm32版本
const INT8 * stm32_version(void);

//设备序列号  返回的8字节数组的首地址  数组在库里已经定义
const UINT8 * device_id_num(void);

//初始化
INT32 init_uhfhfctaetev(void);

//退出
void exit_uhfhfctaetev(void);

//设置电网频率
INT32 set_frequency(Frequency eFrequency);

//获取电网频率 返回50、60、-1
Frequency get_frequency(void);

//升级Stm32程序  pucPercentage进度百分比  50表示50%
INT32 update_conditioner(UINT8 *pucPercentage);

//升级zigbee程序  pucPercentage进度百分比  50表示50%
INT32 update_zigbee(UINT8 *pucPercentage);

//获取UHF HFCT数据  UHF、HFCT
INT32 get_uhfhfct_data(UHFHFCTData *pstUHFHFCTData, WLDeviceType eDeviceType);

//设置UHF滤波  
INT32 set_uhf_filter(UHFFilterControl eFilterControl);

//设置增益 UHF:0dB增益、20dB增益  HFCT:0dB衰减、-20dB衰减、-40dB衰减、-60dB衰减
INT32 set_uhfhfct_gain(UHFHFCTGain eUHFHFCTGain, WLDeviceType eDeviceType);

//设置UHF、HFCT、AE同步
INT32 set_wireless_syncsource(SyncSource eUHFHFCTSync, WLDeviceType eDeviceType);

//设置设备地址  UHF、HFCT、同步器、超声波(保留超声波接口、但一般没有超声波)
INT32 set_wireless_addrlist(WLDeviceAddr *pstAddr, WLDeviceType eDeviceType);

/************************************************
 * 功能       ： 开始搜索无线设备
 * 描述       ： 开始搜索后，等待10秒，调get_wireless_addrlist获取无线设备地址
 *        针对设备：UHF、HFCT、同步器、超声波(保留超声波接口、但一般没有超声波)
 * 入参       ：
 *          eDeviceType 设备类型
 * 返回值   ：
 *          0    ：成功
 *          else ：失败
 ************************************************/
INT32 start_search_wireless(WLDeviceType eDeviceType);

/************************************************
 * 功能       ： 获取无线设备地址
 * 描述       ： 调start_search_wireless开始搜索后，等待10秒，获取无线设备地址
 *        针对设备：UHF、HFCT、同步器、超声波(保留超声波接口、但一般没有超声波)
 * 入参       ：
 *          eDeviceType 设备类型
 * 出参       ：
 *          pstAddr 地址
 *          pusAddrNum 地址个数
 * 返回值   ：
 *          0    ：成功
 *          else ：失败
 ************************************************/
INT32 get_wireless_addrlist(WLDeviceAddr *pstAddr, UINT16 *pusAddrNum, WLDeviceType eDeviceType);

//获取版本  接收器、外部设备(UHF、HFCT这些接在接收器上的设备)  WLDeviceAddr *pstAddr为设备地址,接收器时该参数为NULL
INT32 get_wireless_version(WLDeviceVer *pstWLDeviceVer, WLDeviceType eDeviceType, WLDeviceAddr *pstAddr);

//校准AE  fValue为AE值时单位mV  设置默认参数时出参无意义
INT32 Calibrate_ae(float *fValue, CalibrateAECmd eCalibrateAECmd);

/************************************************
 * 函数名   ：get_tev_calibrate_param

 * 输入参数 ：TEVCalibrateParam *pstTEVCalibrateParam

 * 输出参数 ：

 * 返回值   ：0:succeed; negtive:failed

 * 功能     ：获取TEV系数

 * 描述     :
 ************************************************/
INT32 get_tev_calibrate_param(TEVCalibrateParam *pstTEVCalibrateParam);

/************************************************
 * 函数名   ：Calibrate_tev

 * 输入参数 ：eCmd - TEV 校准子命令
 *                   TEV_RESET_CALI_PARAM -恢复默认参数
 *
                         TEV_CALI_GAIN1, - 40dB档位校准


                         TEV_CALI_GAIN5, - 20dB档位校准

                         TEV_CALI_GAIN50, - 0dB系数校准

 * 输出参数 ：pstCali - 校准命令返回参数信息
 *           pstCali -> usaDacRef --校准档位 DAC 码值参考数组
 *           pstCali -> stParam.fK   --当前档位计算校准系数 K （仅当校准成功时有效，否则返回默认系数）
 *           pstCali -> stParam.fB   --当前档位计算校准系数 B （仅当校准成功时有效，否则返回默认系数）
 *           pstCali -> stParam.eCaliSta   --当前校准状态

 * 返回值   ：0:succeed; negtive:failed

 * 功能     ：校准TEV

 * 描述     :
 * 1.校准算法更新：
 *   每个档位一个校准点，使用二分法采集10次校准点对应的DAC码值取均值，作为该校准点对应的最终码值，
 *   并根据如下公式（DAC[i]=DAC[i-1]*1.112)更新当前校准档位的码值参考数组
 *   档位    校准点    校准电压mVpp
 *   0~19dB    18dB       321
 *   20~39dB   25dB       718
 *   40~59dB   43dB       5700
 *
 ************************************************/
INT32 Calibrate_tev(TevCaliCommand eCmd,AutoCali *pstCali);

/************************************************
 * 功能     ：设置TEV档位校准系数
 *
 * 描述     : 用于手动微调TEV档位校准系数K,B

 * 输入参数 ： eCmd - TEV 校准子命令
 *              TEV_SET_GAIN1_CALI_COEFF -- 40dB档位校准系数设置
 *              TEV_SET_GAIN5_CALI_COEFF -- 20dB档位校准系数设置
 *              TEV_SET_GAIN50_CALI_COEFF -- 0dB档位校准系数设置
 *
 *
 *
 * 输出参数 : 无
 *
 ************************************************/
INT32 set_tev_cali_coeff(TevCaliCommand eCmd,float fK,float fB);

//获取AE数据  AE_CMD只针对PRPD和脉冲模式两种模式有效 其它模式设置成获取数据
INT32 get_ae_data(AEReadData *pstAEReadData, AE_CHANNEL eAE_CHANNEL, AE_CMD eAE_CMD);

//设置AE工作参数  空声及表贴是都一个处理器所以只需设置一次
INT32 set_ae_settings(AEWorkSetForUp *pstAEWorkSetForUp, AE_CHANNEL eAE_CHANNEL);

//获取AE连接状态  无线AE时使用，保留暂不使用
INT32 get_ae_connectstate(AE_ConnectState *peAE_ConnectState);

//获取TEV数据  TEV
INT32 get_tev_data(TEVReadData *pstTEVReadData, TEV_CMD eTEV_CMD);

//设置TEV工作参数
INT32 set_tev_settings(TEVWorkSettings *pstTEVWorkSettings);

//读取Stm32是否在App 在App返回成功  不在返回失败
INT32 get_stm32_inapp(void);

//Stm32跳转至App
INT32 stm32_goto_app(void);

//获取电量 返回值0~100表示相对满电压的百分比  负数出错
INT32 get_bat_power(void);

//获取TEV当前档位对应的电压值
INT32 get_tev_voltage_value(INT32 *iVol1, INT32 *iVol2, INT32 *iVol3);

//测试AE无线数据读取
//usInterval -- 读取时间间隔,单位ms
//eWorkMode -- 工作模式
void get_wireless_ae_data_test( UINT16 usInterval, WorkMode eWorkMode );

//获取电流检测数据
INT32 get_cable_current_data(CableCurrentData *pstCableData);

/************************************************
 * 功能     ：读取TEV测量值
 *
 * 描述     : 用于测试TEV采集值信息

 * 输入参数 ：TevSample 结构体指针
 *

 * 输出参数 : TevSample 结构体指针
 *
 ************************************************/
INT32 test_tev_sample(TevSample *stTev);

#ifdef __cplusplus
}
#endif

#endif
