﻿#ifndef DATEVIEW_H
#define DATEVIEW_H

#include <QWidget>
#include <QCheckBox>
#include <QCalendarWidget>
#include <QLabel>
#include <QPushButton>
#include "datadefine.h"

class DateView : public QWidget
{
    Q_OBJECT

public:
    DateView(QWidget *parent = NULL);

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~DateView();
private slots:
    /*************************************************
    函数名： onOKButtonClicked
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 确认按钮点击槽函数
    *************************************************************/
    void onOKButtonClicked();

    /*************************************************
    函数名： onCancelButtonClicked
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 取消按钮点击槽函数
    *************************************************************/
    void onCancelButtonClicked();

    /*************************************************
    函数名： onSelectedDateChanged
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能：日期框内选中值发生改变时绑定的槽
    *************************************************************/
    void onSelectedDateChanged();

    /*************************************************
    函数名： onPreviousMonth
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能：显示上一个月
    *************************************************************/
    void onPreviousMonth();

    /*************************************************
    函数名： onNextMonth
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能：显示下一个月
    *************************************************************/
    void onNextMonth();

    /*************************************************
    函数名： onLeftBtnPressed
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 处理按下向左的箭头
    *************************************************************/
    void onLeftBtnPressed(void);


    /*************************************************
    函数名： onRightBtnPressed
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能：处理按下向右的箭头
    *************************************************************/
    void onRightBtnPressed(void);


    /*************************************************
    函数名： onLeftBtnReleased
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 处理松开向左的箭头
    *************************************************************/
    void onLeftBtnReleased(void);


    /*************************************************
    函数名： onRightBtnReleased
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 处理松开向右的箭头
    *************************************************************/
    void onRightBtnReleased(void);


protected:
    void keyPressEvent(QKeyEvent *pEvent);

    /************************************************
     * 函数名   : eventFilter
     * 输入参数 : pObj: 面板中的控件;pEvent: 事件
     * 输出参数 : NULL
     * 返回值   : 事件处理结果
     * 功能     : 事件过滤器,处理鼠标点击事件及物理键盘按钮事件
     ************************************************/
    bool eventFilter(QObject *pObj, QEvent *pEvent);

    /*************************************************
    函数名： timerEvent
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 定时函数
    *************************************************************/
    void timerEvent(QTimerEvent *pEvent);

    /************************************************
     * 函数名   : resizeEvent
     * 输入参数 :
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : resize事件
     ************************************************/
    void resizeEvent(QResizeEvent* pEvent);

    /*************************************************
    功能： 显示事件处理
    *************************************************************/
    void showEvent(QShowEvent *event);

    /*************************************************
    功能： 处理窗口关闭事件
    *************************************************************/
    void closeEvent(QCloseEvent* event);

private:
    /*************************************************
    函数名： showDate
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能：根据QDate显示出日期，以年.月.日的结构组织
    *************************************************************/
    void showDate( void );

private:
    enum {
        INVALID_TIMER = -1, //无效定时器id
        AUTO_CHANGE_DATE_TIMER_200MS = 200 //自动更新日期时间定时器时间间隔
    };

    QCheckBox *m_pFirstFridayCheckBox;
    QCheckBox *m_pMayFirstCheckBox;
    QCalendarWidget *m_pCalendar;
    QDate m_SlectedDate;
    QLabel *m_pCurrentDateLabel; // 显示当前日期的标签

    int m_iAutoChangeDateTimerId; //自动更新日期时间定时器id

    bool m_isPressRightBtn; //是否按下右方向箭头标志
    bool m_isPressLeftBtn; //是否按下左方向箭头标志

    QPushButton *m_pOkButton;
    QPushButton *m_pCancelButton;
};

#endif // DATEVIEW_H
