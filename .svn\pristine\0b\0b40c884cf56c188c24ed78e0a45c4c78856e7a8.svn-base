#include "pdafiletool.h"
#include <QFileInfo>
#include "dataSave/DataFileInfos.h"
#include "log/log.h"

PdaFileTool::PdaFileTool(QObject* parent) : QObject(parent)
{
}

PdaFileTool::~PdaFileTool()
{
}

ChartType PdaFileTool::getChartType(const QString& qstrFilePath)
{
    QFileInfo qfInfo(qstrFilePath);
    QString qstrSuffix = QString(".") + qfInfo.suffix();
    ChartType enChartType;
    if(AE_WAVE_FILE_NAME_SUFFIX == qstrSuffix)
    {
        enChartType = Type_AeWaveView;
    }
    else if(AE_AMP_FILE_NAME_SUFFIX == qstrSuffix)
    {
        enChartType = Type_AeAmpView;
    }
    else if(AE_PHASE_FILE_NAME_SUFFIX == qstrSuffix)
    {
        enChartType = Type_AePhaseView;
    }
    else if(UHF_PRPS_PRPD_FILE_NAME_SUFFIX == qstrSuffix || HFCT_PRPS_PRPD_FILE_NAME_SUFFIX == qstrSuffix)
    {
        enChartType = Type_Prps3D;
    }
    else if(TEV_PRPS_PRPD_FILE_NAME_SUFFIX == qstrSuffix)
    {
        enChartType = Type_Tev;
    }
    else if(INFRARED_FILE_NAME_SUFFIX == qstrSuffix)
    {
        enChartType = Type_Infrared;
    }
    else
    {
        enChartType = Type_Unknown;
    }

    return enChartType;
}

QString PdaFileTool::findStrInfoByType(const QString& qstrType, ChartType eType)
{
    QString qstrRet = "";
    switch (eType)
    {
    case Type_Prps3D:
    {
        qstrRet = findPrpsInfo(qstrType);
        break;
    }
    default:
    {
        break;
    }

    }
    return qstrRet;
}

QString PdaFileTool::findPrpsInfo(const QString& qstrType)
{
    PrpsDiagnoseType eType = (PrpsDiagnoseType)(qstrType.toUInt());
    QString qstrResult = "";
    switch (eType)
    {
    case PRPS_NORMAL:
        qstrResult = QObject::trUtf8("Normal");
        break;
    case PRPS_CORONA:
        qstrResult = QObject::trUtf8("Corona");
        break;
    case PRPS_FLOATING_ELECTRODE:
        qstrResult = QObject::trUtf8("Floating Electrode");
        break;
    case PRPS_VOID:
        qstrResult = QObject::trUtf8("Void");
        break;
    case PRPS_SURFACE:
        qstrResult = QObject::trUtf8("Surface");
        break;
    case PRPS_PARTICLE:
        qstrResult = QObject::trUtf8("Particle");
        break;
    case PRPS_NOISE:
        qstrResult = QObject::trUtf8("Noise");
        break;
    case PRPS_CALIBRATION:
        qstrResult = QObject::trUtf8("Calibration");
        break;
    case PRPS_UNKNOWN:
        qstrResult = QObject::trUtf8("Unknown");
        break;
    case PRPS_INSUFFICIENT_DATA:
        qstrResult = QObject::trUtf8("Insufficient Data");
        break;
    case PRPS_DRILL_NOISE:
        qstrResult = QObject::trUtf8("Drill Noise");
        break;
    case PRPS_ENERGYSAVINGLAMPS_NOISE:
        qstrResult = QObject::trUtf8("Energy Saving Lamps Noise");
        break;
    case PRPS_FAN_NOISE:
        qstrResult = QObject::trUtf8("Fan Noise");
        break;
    case PRPS_IGNITION_NOISE:
        qstrResult = QObject::trUtf8("Ignition Noise");
        break;
    case PRPS_INTERPHONE_NOISE:
        qstrResult = QObject::trUtf8("Interphone Noise");
        break;
    case PRPS_MICROWAVESULFUR_NOISE:
        qstrResult = QObject::trUtf8("Microwave Sulfer Noise");
        break;
    case PRPS_MOTOR_NOISE:
        qstrResult = QObject::trUtf8("Motor Noise");
        break;
    case PRPS_RADAR_NOISE:
        qstrResult = QObject::trUtf8("Radar Noise");
        break;
    case PRPS_SPARKLEAKDETECTOR_NOISE:
        qstrResult = QObject::trUtf8("Spark Leak Detector Noise");
        break;
    case PRPS_MOBILE_NOISE:
        qstrResult = QObject::trUtf8("Mobile Noise");
        break;
    case PRPS_PD:
        qstrResult = QObject::trUtf8("PD");
        break;
    case PRPS_NOTPD:
        qstrResult = QObject::trUtf8("Not PD");
        break;
    case PRPS_INSULATION:
        qstrResult = QObject::trUtf8("Insulation");
        break;
    case PRPS_MECHANICAL_VIBRATION:
        qstrResult = QObject::trUtf8("Mechanical Vibration");
        break;
    case PRPS_LIGHT_NOISE:
        qstrResult = QObject::trUtf8("Light Noise");
        break;
    default:
        qstrResult = QObject::trUtf8("Unknown");
        break;
    }
    return qstrResult;
}

