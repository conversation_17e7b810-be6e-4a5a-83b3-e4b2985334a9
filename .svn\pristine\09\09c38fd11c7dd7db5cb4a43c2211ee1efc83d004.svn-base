/*
 * Copyright (c) 2019.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：mechanicalcoilspectrum.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/11/04
 * 摘要：mechanicalcoil图谱
 * 当前版本：1.0
*/

#pragma once

#include "mechanicalspectrum.h"
#include "mechanicalspectrumdefine.h"

namespace DataSpecificationNS
{
    class MechanicalCoilSpectrumPrivate;
    class DATASPECIFICATIONSHARED_EXPORT MechanicalCoilSpectrum : public MechanicalSpectrum
    {
    public:
        MechanicalCoilSpectrum();
        ~MechanicalCoilSpectrum();


        /************************************************
         * 函数名   : spectrumName
         * 输入参数 : NULL
         * 输出参数 : NULL
         * 返回值   : QString
         * 功能     : 图谱名称
         ************************************************/
        virtual QString spectrumName() const;

        /************************************************
         * 函数名   : setMechanicalCoilExtInformation
         * 输入参数 :
           const MechanicalCoilExtInformation& stMechanicalCoilExtInformation: MechanicalCoilExtInformation
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置MechanicalCoilExtInformation
         ************************************************/
        void setMechanicalCoilExtInformation(const MechanicalCoilExtInformation& stMechanicalCoilExtInformation);

        /************************************************
         * 函数名   : getMechanicalCoilExtInformation
         * 输入参数 : NULL
         * 输出参数 :
           MechanicalCoilExtInformation& stMechanicalCoilExtInformation: MechanicalCoilExtInformation
         * 返回值   : void
         * 功能     : 获取MechanicalCoilExtInformation
         ************************************************/
        void getMechanicalCoilExtInformation(MechanicalCoilExtInformation& stMechanicalCoilExtInformation);

    protected:

        /************************************************
         * 函数名   : saveBinarySpectrumExtInfo
         * 输入参数 :
           QDataStream& out: 输出流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存二进制可扩展信息段
         ************************************************/
        virtual bool saveBinarySpectrumExtInfo(QDataStream& out);

        /************************************************
         * 函数名   : saveXMLSpectrumExtInfo
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
           QDomElement& element: dom元素
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存XML可扩展信息段
         ************************************************/
        virtual bool saveXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj, QDomElement& element);

        /************************************************
         * 函数名   : saveJSONSpectrumExtInfo
         * 输入参数 :
           rapidjson::Document::AllocatorType& alloc:
           rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存JSON可扩展信息段
         ************************************************/
        virtual bool saveJSONSpectrumExtInfo(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : parseBinarySpectrumExtInfo
         * 输入参数 :
           QDataStream& in: 输入流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析二进制可扩展信息段
         ************************************************/
        virtual bool parseBinarySpectrumExtInfo(QDataStream& in);

        /************************************************
         * 函数名   : parseXMLSpectrumExtInfo
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析XML可扩展信息段
         ************************************************/
        virtual bool parseXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj);

        /************************************************
         * 函数名   : parseJSONSpectrumExtInfo
         * 输入参数 :
           const rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析JSON可扩展信息段
         ************************************************/
        virtual bool parseJSONSpectrumExtInfo(const rapidjson::Value& jsonValue);

    private:
        MechanicalCoilSpectrumPrivate* m_pMechanicalCoilSpectrumPrivate{ Q_NULLPTR };
    };
}
