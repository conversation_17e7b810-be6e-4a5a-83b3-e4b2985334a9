#ifndef CAPRPSUNIONVIEW_H
#define CAPRPSUNIONVIEW_H

/*
* Copyright (c) 2015.11，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: CAPrpsUnionView.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年4月7日
* 摘要：该文件主要定义CA prps和prpd整合的view

* 当前版本：1.0
*/

#include "prpsunionview.h"
#include "Widget.h"

class WIDGET_EXPORT CAPrpsUnionView : public PrpsUnionView
{
    Q_OBJECT
public:
    /************************************************
     * 功能: 构造函数
     * 入参：parent -- 父控件指针
     *      iPeriodCount -- 周期个数
     *      iPhaseCount -- 每周期相位个数
     *      dMax -- 量程最大值
     *      dMin -- 量程最小值
     ************************************************/
    explicit CAPrpsUnionView( qint32 iDisplayedPeriodCount,qint32 iPeriodCount, qint32 iPhaseCount,
                               double dMax,double dMin,QWidget *parent = 0 );

    /****************************
    输入参数:cMaxSpectrum:最大频谱值
    功能： 设置最大频谱值
    业务逻辑：
        最大值 > 70        MAX > 70
        最大值 （ 0~70 ）   MAX = value
        最大值 < 0         MAX < 0
    *****************************/
    void setMaxSpectrum( double dMaxSpectrum );
};

#endif // CAPRPSUNIONVIEW_H
