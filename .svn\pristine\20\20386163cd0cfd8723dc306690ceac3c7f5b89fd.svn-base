﻿#include <stdio.h>
#include <string.h>
#include "AfDes_CBC.h"

#define MIN_SIZE 8

bool RunPad(int ePadMode, const char* pcDataIn, quint32 uiDataLen, char* pcDataPad)
{
    if( ( NULL == pcDataIn ) || ( NULL == pcDataPad ) )
        return false;

    memcpy( pcDataPad, pcDataIn, uiDataLen );
    quint32 uiResLen = uiDataLen & 0x7;

    switch( ePadMode )
    {
    case 1:
        memset(pcDataPad + uiDataLen, 0x00, MIN_SIZE - uiResLen);
        break;
    case 2:
        memset(pcDataPad + uiDataLen, 0x80, 1);
        memset(pcDataPad + uiDataLen, 0x00, 7 - uiResLen);
        break;
    case 7:
        memset(pcDataPad + uiDataLen, 8 - uiResLen, 8 - uiResLen);
        break;
    default:
        break;
    }

    return true;
}

void AfDes_CBC::SetIV(const void* iv)
{
    memcpy(m_iv, iv, 8);
}

// 单组加密/解密,8字节
void AfDes_CBC::Encrypt(const void* plain, void* cipher)
{
    // 加密
    des_cblock b;
    memcpy(b, plain, 8);
    des_cbc_encrypt(&b, &b, 8, m_ks, &m_iv, DES_ENCRYPT);

    // 拷贝结果
    memcpy(cipher, b, 8);
    memcpy(m_iv, b, 8); // 输出结果就是下一次的IV
}

void AfDes_CBC::Decrypt(void* plain, const void* cipher)
{
    // 解密
    des_cblock b; // 临时缓冲区
    memcpy(b, cipher, 8);
    des_cbc_encrypt(&b, &b, 8, m_ks, &m_iv, DES_DECRYPT);

    // 拷贝结果
    memcpy(plain, b, 8);
    memcpy(m_iv, cipher, 8); // 输出结果就是下一次的IV
}

/************************************************
 * 函数名    :enCrypt
 * 输入参数  ：
 *      data -- 需要加密的数据
 * 输出参数  ：NULL
 * 返回值   ：
 *      加密后的数据
 * 功能     ：加密
 ************************************************/
QByteArray AfDes_CBC::enCryptDes(const QByteArray& data)
{
    //先按8位补齐数据（注：如果已经对其，则补充8位）
    quint32 uiResLen = data.count() & 0x7;
    quint32 uiPadLen = data.count() + MIN_SIZE - uiResLen;//按8位补齐后的长度

    //初始化输出数据
    QByteArray dataOut;
    dataOut.resize(uiPadLen);

    memset(dataOut.data(), 0, dataOut.count());
    memcpy(dataOut.data(), data.data(), data.count());

    //填充补齐
    QByteArray dataPad;
    dataPad.resize(uiPadLen);
    RunPad(m_padding_mode, data.data(), data.count(), dataPad.data());

    char* pOutput = dataOut.data();
    const char* pInput = dataPad.constData();
    for(unsigned int i = 0, dwSize = uiPadLen / MIN_SIZE; i < dwSize; ++i)
    {
        Encrypt(pInput + i * MIN_SIZE, pOutput + i * MIN_SIZE);
    }

    return dataOut;
}
