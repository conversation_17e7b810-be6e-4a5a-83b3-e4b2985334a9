﻿/*
* Copyright (c) 2017.08，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: phasechartitem.h
*
* 初始版本: 1.0
* 作者: 张涛
* 创建日期: 2017年11月13日
* 摘要: 该文件定义了周期图谱item基类

* 当前版本: 1.0
*/

#ifndef PHASECHART_H
#define PHASECHART_H

#include <QString>
#include <QRect>
#include <QFont>
#include "phasedef.h"

class PhasePaintData;
class PhaseChartMap;
class PhaseChartDraw;
class PhaseValueHelper;

class PhaseChart
{
public:
    PhaseChart();
    virtual ~PhaseChart();

    /************************************************
     * 函数名: boundingRect
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 边界矩形
     * 功能: 获取边界矩形
     ************************************************/
    QRect boundingRect() const
    {
        return m_boundingRect;
    }

    /************************************************
     * 函数名: setBoundingRect
     * 输入参数:
     *          bdRect: 边界矩形
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置边界矩形
     ************************************************/
    void setBoundingRect(const QRect &bdRect);

    /************************************************
     * 函数名: coordinateRect
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 坐标系矩形
     * 功能: 获取坐标系矩形
     ************************************************/
    QRect coordinateRect() const
    {
        return m_coordinateGeometry;
    }

    /************************************************
     * 函数名: setCoordinateRect
     * 输入参数:
     *          rect: 坐标系矩形
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置坐标系矩形
     ************************************************/
    void setCoordinateRect(const QRect &rect);

    /************************************************
     * 函数名: phaseOffset
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 当前相位偏移度数
     * 功能: 返回当前相位偏移度数
     ************************************************/
    int phaseOffset() const;

    /************************************************
     * 函数名: setPhaseOffset
     * 输入参数:
     *        iAngle: 度数
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置当前相位偏移度数
     ************************************************/
    void setPhaseOffset(int iAngle);

    /************************************************
     * 函数名: threshold
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 阈值(0~1之间)
     * 功能: 返回阈值
     ************************************************/
    float threshold() const;

    /************************************************
     * 函数名: threshold
     * 输入参数:
     *        threshold: 阈值, 范围在0~1之间
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置阈值
     ************************************************/
    void setThreshold(float fThreshold);

    /************************************************
     * 函数名: setTextFont
     * 输入参数:font: 字体
     * 输出参数: NULL
     * 功能: 设置绘制坐标轴文字信息所用的字体
     ************************************************/
    void setTextFont(const QFont& font);

    /************************************************
     * 函数名: textFont
     * 输入参数:void
     * 输出参数: NULL
     * 返回值: 字体
     * 功能: 返回绘制坐标轴文字信息所用的字体
     ************************************************/
    inline QFont textFont() const
    {
        return m_textFont;
    }

    /************************************************
     * 函数名: valueHelper
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 数据操作辅助对象
     * 功能: 返回数据操作辅助对象
     ************************************************/
    PhaseValueHelper* valueHelper() const
    {
        return m_pValueHelper;
    }

    /************************************************
     * 函数名: setValueHelper
     * 输入参数:
     *          pHelper: 数据操作辅助对象
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置数据操作辅助对象
     ************************************************/
    void setValueHelper(PhaseValueHelper *pHelper);

    void setVisible(bool bVisible);

    bool isVisible() const;

    virtual void paint(QPainter *pPainter, const QPalette &palette);

protected:
    // 更新绘制数据
    virtual void updatePaintContents(int iStartIndex) = 0;

    // 更新参数
    virtual void updateParams() = 0;

    // 获取绘制数据
    const PhasePaintData* paintData() const
    {
        return m_pPaintData;
    }

    // 设置绘制数据
    void setPaintData(const PhasePaintData *pData);

    // 获取/设置绘制对象
    PhaseChartDraw* chartDraw() const;
    void setChartDraw(PhaseChartDraw *pDraw);

    // 获取/设置坐标映射器
    PhaseChartMap* chartMap() const;
    void setChartMap(PhaseChartMap *pMap);

private:
    bool m_bVisible;
    QRect m_boundingRect;
    QRect m_coordinateGeometry;      // 图谱的边界矩形
    QFont m_textFont;
    PhaseChartMap *m_pChartMap;
    PhaseChartDraw *m_pChartDraw;
    const PhasePaintData *m_pPaintData;
    PhaseValueHelper *m_pValueHelper;

    friend class PhaseChartView;
};

#endif // PHASECHART_H
