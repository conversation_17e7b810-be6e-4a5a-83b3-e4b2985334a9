#include "stm32service.h"
#include "datadefine.h"
#include "systemsettings.h"
#include <QTimerEvent>
#include <QDebug>
#include <QEventLoop>
#include <QTimer>
#include <QDateTime>
#include "global_log.h"

#define SET_AE_POWER_CNT 2 // 定义开关电源允许失败的次数上限
#define MAX_IDEL_COUNT 100 //采集器最大带电不工作间隔次数，超过这个次数，自动关电
#define POWER_SCAN_INTVL 1000 //采集器关电的扫描间隔
#define AE_PHASEFLY_MIN_INTVL 200
//AE设置相位模式到开始采集的最小时间间隔
#define AE_PHASE_START_MIN_INTVL 45
#define STOP_SAMPLE_SET_CNT 3
Stm32Service::Stm32Service(QObject *parent) : QObject(parent)
{
    m_eSampleType = SAMPLE_DEFAULT;
    m_eAE_CHANNEL = AirSound;
    m_iAEPowerRef = 0;
    m_eAeWorkMode = NoneMode;
    m_eAeExclusiveMode = NoneMode;

    m_iTEVPowerRef = 0;
    m_eTevMode = TEV_NONE_MODE;
    m_eTevExclusiveMode = TEV_NONE_MODE;
    m_stTEVWorkSet.ucWorkMode = TEV_NONE_MODE;
}

Stm32Service::~Stm32Service()
{

}

/****************************
 功能：设置当前传感器类型
 输入参数：echannel---传感器类型
 *****************************/
void Stm32Service::setAEChannel( AE_CHANNEL eChannel)
{
    m_eAE_CHANNEL = eChannel;
    m_eAeWorkMode = NoneMode;
}

/****************************
 功能： 用eventloop延时指定时间
 输入参数：uiTime---延迟的时间，单位ms
 *****************************/
void Stm32Service::timeDeley(UINT32 uiTime)
{
    QEventLoop eventloop;
    QTimer::singleShot(uiTime, &eventloop, SLOT(quit()));
    eventloop.exec();
}

/****************************
 功能： AE相位和飞行的采集需要显式关闭
 *****************************/
void Stm32Service::stopSampleAeData( void )
{
    int iResult = -1;
    for( int i = 0;i < STOP_SAMPLE_SET_CNT;++i )
    {
#ifdef Q_OS_LINUX
        iResult = get_ae_data( NULL,m_eAE_CHANNEL,AE_STOP_SAMPLE );
#endif
        if(iResult == 0)
        {
            break;
        }
    }
}

/*************************************************
功能： 获取AE数据
输入参数：
        eWorkMode -- 工作模式
输出参数：
        pstAEReadData -- 读取的数据
返回：
        是否读取成功
*************************************************/
int Stm32Service::readAEData( AEReadData *pstAEReadData, WorkMode eWorkMode )
{
    int iResult = 0;

    //在独占模式下，如果读取的模式和独占模式不一致，则不进行读取
    if( (m_eAeExclusiveMode != NoneMode) && (eWorkMode != m_eAeExclusiveMode) )
    {
        log_error("can't sample in exclusive mode: %d", eWorkMode);
        return -1;
    }

    m_stmMutex.lock();
    if( (m_eAeWorkMode == eWorkMode) && (m_eSampleType == SAMPLE_AE) )//模式未发生变化，只需要继续采集即可
    {
        /*
        if( ( PRPD == m_eAeWorkMode ) && ( !m_bPhaseReadable ) )
        {
            qint64 intvl = QDateTime::currentMSecsSinceEpoch() - m_aePhaseFlySampleTime;
            if( intvl > AE_PHASE_START_MIN_INTVL )
            {
                //采集当前数据
                get_ae_data( NULL,m_eAE_CHANNEL,AE_START_SAMPLE );
                iResult = getAEData( pstAEReadData );
                m_bPhaseReadable = true;
            }
        }
        else
        {
            iResult = getAEData( pstAEReadData );
        }
        */
        iResult = getAEData( pstAEReadData );
    }
    else//模式变化，则需要切换模式后再进行读取
    {
        //保证AE相位、飞行有足够的采集间隔
        if( isLastAESampleEnough( m_eAeWorkMode ) )
        {
            bool bAeSetSuccess = false;
            switch( eWorkMode )
            {
            case Amplitude: //幅值模式
            {
                iResult = set_ae_settings( &m_stAEAmpWorkSet, m_eAE_CHANNEL );

                if( 0 == iResult )
                {
                    bAeSetSuccess = true;
                    //采集当前数据
                    iResult = getAEData( pstAEReadData );
                }
            }
                break;
            case PRPD: //PRPD
            {
                //设置参数
                iResult = get_ae_data( NULL,m_eAE_CHANNEL,AE_STOP_SAMPLE );
                iResult = set_ae_settings( &m_stAEPhaseWorkSet, m_eAE_CHANNEL );

                //下次采集
                //m_bPhaseReadable = false;

                if( 0 == iResult )
                {
                    bAeSetSuccess = true;
                    get_ae_data( NULL,m_eAE_CHANNEL,AE_START_SAMPLE );
                    iResult = getAEData( pstAEReadData );

                    m_aePhaseFlySampleTime = QDateTime::currentMSecsSinceEpoch();
                }
            }
                break;
            case Pulse: //脉冲模式
            {
                iResult = get_ae_data( NULL,m_eAE_CHANNEL,AE_STOP_SAMPLE );
                iResult = set_ae_settings( &m_stAEFlyWorkSet, m_eAE_CHANNEL );

                if( 0 == iResult )
                {
                    bAeSetSuccess = true;
                    //开始采集
                    get_ae_data( NULL,m_eAE_CHANNEL,AE_START_SAMPLE );

                    //采集当前数据
                    iResult = getAEData( pstAEReadData );
                    m_aePhaseFlySampleTime = QDateTime::currentMSecsSinceEpoch();
                }
            }
                break;
            case Waveform:
            {
                iResult = set_ae_settings( &m_stAEWaveWorkSet, m_eAE_CHANNEL );

                if( 0 == iResult )
                {
                    bAeSetSuccess = true;
                    //采集当前数据
                    iResult = getAEData( pstAEReadData );
                }
            }
                break;
            case AllViews:
            case NoneMode:
                break;
            }
            //如果设置成功，则更改模式
            if( bAeSetSuccess )
            {
                m_eAeWorkMode = eWorkMode;
                m_eSampleType = SAMPLE_AE;
            }
            else//设置失败，重置模式
            {
                m_eAeWorkMode = NoneMode;
            }
        }
        else//如果AE相位、飞行实际采集的间隔不够，则不进行其它类型数据的采集
        {
            //don't change mode
            iResult = -1;
            log_debug("wait pre sample...");
        }
    }
    m_stmMutex.unlock();

    return iResult;
}

/****************************
功能： 清除工作模式，之后再启动采样时，会先stop sample,再start sample

输入参数：
       NULL
输出参数：
       NULL
返回：
       NULL
 *****************************/
void Stm32Service::clearWorkMode()
{
    m_eAeWorkMode = NoneMode;
}

/*************************************************
功能： 获取AE全图谱数据
输入参数：
        NULL
输出参数：
        pstAEViewData -- 读取的数据
返回：
        是否读取成功 0为成功，-1为未成功
*************************************************/
int Stm32Service::readAllAEData( AEAllView *pstAEViewData )
{
    int iResult = 0;
    m_stmMutex.lock();
    bool bAeSetSuccess = true;
    if( (m_eAeWorkMode == AllViews) && (m_eSampleType == SAMPLE_AE) )//模式未发生变化，只需要继续采集即可
    {
        iResult = get_all_ae_views( pstAEViewData, m_eAE_CHANNEL );
        //log_debug("read ae all data, channel: %d, ret: %d.", m_eAE_CHANNEL, iResult);
    }
    else
    {
        iResult = get_ae_data( NULL,m_eAE_CHANNEL,AE_STOP_SAMPLE );
        if( AE_Wireless == m_eAE_CHANNEL )
        {
            //NOTE:特殊处理--无线AE采集四图谱的时候,把工作模式填成波形
            AEWorkSetForUp tmpWorkSet;
            memcpy( &tmpWorkSet, &m_stAEAllWorkSet, sizeof( m_stAEAllWorkSet ) );
            tmpWorkSet.eWorkMode = Waveform;
            tmpWorkSet.ucNgrid = 5;
            iResult = set_ae_settings( &tmpWorkSet, m_eAE_CHANNEL );
        }
        else
        {
            iResult = set_ae_settings( &m_stAEAllWorkSet, m_eAE_CHANNEL );
        }

        if( 0 == iResult )
        {
            bAeSetSuccess = true;
            //开始采集
            get_ae_data( NULL,m_eAE_CHANNEL,AE_START_SAMPLE );

            //采集当前数据
            iResult = get_all_ae_views( pstAEViewData, m_eAE_CHANNEL );
        }
        else
        {
            bAeSetSuccess = false;
        }
    }

    //如果设置成功，则更改模式
    if( bAeSetSuccess )
    {
        m_eAeWorkMode = AllViews;
        m_eSampleType = SAMPLE_AE;
    }
    else//设置失败，重置模式
    {
        m_eAeWorkMode = NoneMode;
    }

    m_stmMutex.unlock();

    return iResult;
}

/****************************
 功能： 获取AE数据
 输出参数：pstAEReadData -- 存储读取的数据
 *****************************/
int Stm32Service::getAEData( AEReadData *pstAEReadData )
{
    int iResult = 0;
#ifdef Q_OS_LINUX
    iResult = get_ae_data( pstAEReadData, m_eAE_CHANNEL, AE_GET_DATA);
#endif
    if( iResult == 0 )
    {
        m_eAE_CHANNEL = pstAEReadData->eAE_CHANNEL;
    }
    return iResult;
}

/*************************************************
功能： 设置AE的配置参数
输入参数：
        pstAEWorkSetForUp -- 当前参数配置
输出参数：NULL
返回：
        是否设置成功
*************************************************/
int Stm32Service::setAESettings( AEWorkSetForUp *pstAEWorkSetForUp )
{
    int iResult = 0;

    m_stmMutex.lock();

    switch( pstAEWorkSetForUp->eWorkMode )
    {
    case Amplitude: //幅值模式
        memcpy( &m_stAEAmpWorkSet, pstAEWorkSetForUp, sizeof(AEWorkSetForUp) );
        break;
    case PRPD: //PRPD
        memcpy( &m_stAEPhaseWorkSet, pstAEWorkSetForUp, sizeof(AEWorkSetForUp) );
        break;
    case Pulse: //脉冲模式
        memcpy( &m_stAEFlyWorkSet, pstAEWorkSetForUp, sizeof(AEWorkSetForUp) );
        break;
    case Waveform: //波形模式
        memcpy( &m_stAEWaveWorkSet, pstAEWorkSetForUp, sizeof(AEWorkSetForUp) );
        break;
    case AllViews:
        memcpy( &m_stAEAllWorkSet, pstAEWorkSetForUp, sizeof(AEWorkSetForUp) );
        break;
    case NoneMode:
        break;
    }

    /*
    if( m_eAeWorkMode == pstAEWorkSetForUp->eWorkMode )
    {
        iResult = set_ae_settings( &m_stAEFlyWorkSet, m_eAE_CHANNEL );
    }
    */
	//参数设置后，当前采集模式重置，以便下次读取时重新设置
    m_eAeWorkMode = NoneMode;

    m_stmMutex.unlock();

    return iResult;
}

/*************************************************
功能： 获取TEV数据
输入参数：
        eWorkMode -- 工作模式 0幅值检测  1脉冲模式
输出参数：
        pstTEVReadData -- 读取的数据
返回：
        是否读取成功
*************************************************/
int Stm32Service::readTEVData( TEVReadData *pstTEVReadData, UINT8 ucWorkMode )
{
    int iResult = 0;
    m_stmMutex.lock();
    switch( m_eSampleType )
    {
    case SAMPLE_TEV:
    {
        if( m_stTEVWorkSet.ucWorkMode == ucWorkMode )
        {
            //模式未发生变化，只需要继续采集即可
#ifdef Q_OS_LINUX
            iResult = get_tev_data( pstTEVReadData, TEV_GET_DATA );
#endif
        }
        else
        {
            UINT8 ucTempWorkMode = m_stTEVWorkSet.ucWorkMode;
            //设置本次参数
            m_stTEVWorkSet.ucWorkMode = ucWorkMode;
#ifdef Q_OS_LINUX
            iResult = set_tev_settings( &m_stTEVWorkSet );
            if( 0 == iResult )
            {
                //采集当前数据
                iResult = get_tev_data( pstTEVReadData, TEV_GET_DATA );
            }
            else
            {
                m_stTEVWorkSet.ucWorkMode = ucTempWorkMode;
            }

#endif
        }
    }
        break;
    case SAMPLE_AE:
    case SAMPLE_NONE:
    {
        //设置本次参数
        m_stTEVWorkSet.ucWorkMode = ucWorkMode;
#ifdef Q_OS_LINUX
        //设置本次参数
        iResult = set_tev_settings( &m_stTEVWorkSet );
        //采集当前数据
        iResult = get_tev_data( pstTEVReadData, TEV_GET_DATA );
#endif

        m_eSampleType = SAMPLE_TEV;
    }
        break;
    default:
    {
        //unreachable branch
    }
    }
    m_stmMutex.unlock();

    return iResult;
}

/*************************************************
功能： 设置TEV的配置参数
输入参数：
        pstTEVWorkSettings -- 当前参数配置
输出参数：NULL
返回：
        是否设置成功
*************************************************/
int Stm32Service::setTEVSettings( TEVWorkSettings *pstTEVWorkSettings )
{
    int iResult = 0;
    //参数一致，不需要再进行设置
    if( memcmp( &m_stTEVWorkSet, pstTEVWorkSettings, sizeof(TEVWorkSettings) ) == 0 )
    {
        iResult = 0;
    }
    else
    {
#ifdef Q_OS_LINUX
        m_stmMutex.lock();
        //iResult = set_tev_settings( pstTEVWorkSettings );
        m_eSampleType = SAMPLE_NONE;
        m_stmMutex.unlock();
#endif
        memcpy( &m_stTEVWorkSet, pstTEVWorkSettings, sizeof(TEVWorkSettings) );
        m_stTEVWorkSet.ucWorkMode = TEV_NONE_MODE;
    }

    return iResult;
}

/****************************
 功能： 打开AE电源
 *****************************/
int Stm32Service::openAEPower( void )
{
    int iResult = -1;
    if( !isAEPowerOn() )//电源未打开，进行上电操作
    {
#ifdef Q_PROCESSOR_ARM
        //打开模拟板5V电源
        //analog_board_power_ctrl(1);
#endif
        int cnt = 0;
        while( iResult != 0 && cnt <= SET_AE_POWER_CNT )
        {
#ifdef Q_OS_LINUX
            iResult = get_ae_data( NULL,AirSound,AE_OPEN_POWER );
#endif
            cnt++;
            if(iResult == 0)
            {
                m_iAEPowerRef = 1;
            }
        }
    }
    else//电源已打开，增加引用计数，不进行上电操作
    {
        m_iAEPowerRef++;
        iResult = 0;
    }
    return iResult;
}

/****************************
 功能： 关闭AE电源
 *****************************/
int Stm32Service::closeAEPower( void )
{
    int iResult = -1;
    if( isAEPowerOn() )
    { 
        if( m_iAEPowerRef == 1 )//仅引用计数为1时，需要实际进行电源关闭操作
        {
            int cnt = 0;
            while( iResult != 0 && cnt <= SET_AE_POWER_CNT )
            {
        #ifdef Q_OS_LINUX
                iResult = get_ae_data( NULL,AirSound,AE_CLOSE_POWER );
        #endif
                cnt++;
                if(iResult == 0)
                {
                    m_iAEPowerRef--;
                }
            }

#ifdef Q_PROCESSOR_ARM
            //关闭模拟板5V电源
            //analog_board_power_ctrl(0);
#endif
        }
        else//引用计数大于1时，仅修改引用计数，不进行电源关闭操作
        {
            m_iAEPowerRef--;
            iResult = 0;
        }
    }
    else
    {
        iResult = 0;
    }

    return iResult;
}

/****************************
 功能： 打开TEV电源
 *****************************/
int Stm32Service::openTEVPower( void )
{
    int iResult = -1;
    if( !isTEVPowerOn() )//电源未打开，进行上电操作
    {
#ifdef Q_OS_LINUX
        //打开模拟板5V电源
        //analog_board_power_ctrl(1);
        iResult = get_tev_data( NULL, TEV_OPEN_POWER );
#endif
        if(iResult == 0)
        {
            m_iTEVPowerRef = 1;
        }
    }
    else//电源已打开，增加引用计数，不进行上电操作
    {
        m_iTEVPowerRef++;
        iResult = 0;
    }
    return iResult;
}

/****************************
 功能： 关闭TEV电源
 *****************************/
int Stm32Service::closeTEVPower( void )
{
    int iResult = -1;
    if( isTEVPowerOn() )
    {
        if( m_iTEVPowerRef == 1 )//仅引用计数为1时，需要实际进行电源关闭操作
        {
#ifdef Q_OS_LINUX
            iResult = get_tev_data( NULL, TEV_CLOSE_POWER );
            //关闭模拟板5V电源
            //analog_board_power_ctrl(0);
#endif
            if(iResult == 0)
            {
                m_iTEVPowerRef--;
            }
        }
        else//引用计数大于1时，仅修改引用计数，不进行电源关闭操作
        {
            m_iTEVPowerRef--;
            iResult = 0;
        }
    }
    else
    {
        iResult = 0;
    }

    return iResult;
}

bool Stm32Service::isLastAESampleEnough(WorkMode eAeWorkMode)
{
    bool bRet = false;
    if( (eAeWorkMode == PRPD) || (eAeWorkMode == Pulse) )
    {
        qint64 intvl = QDateTime::currentMSecsSinceEpoch() - m_aePhaseFlySampleTime;
        log_debug("waste time: %d", (int)intvl);
        if( intvl > AE_PHASEFLY_MIN_INTVL )
        {
            bRet = true;
        }
        else
        {
            bRet = false;
        }
    }
    else
    {
        bRet = true;
    }
    return bRet;
}

/****************************
 功能： 设置AE独占采集的模式
 返回值：设置成功返回true，设置有冲突返回false
 *****************************/
bool Stm32Service::setExclusiveAEMode( WorkMode eMode )
{
    bool bRet = false;
    if( NoneMode == m_eAeExclusiveMode )//没有被其它模式所独占
    {
        m_eAeExclusiveMode = eMode;
        //如果独占的模式和当前采集模式不一致，则重置当前采集模式
        if( m_eAeExclusiveMode != m_eAeWorkMode )
        {
            m_eAeWorkMode = NoneMode;
        }
        bRet = true;
    }
    else//已被独占，则无法设置独占
    {
        bRet = false;
    }
    return bRet;
}

/****************************
 功能： 设置TEV独占采集的模式
 返回值：设置成功返回true，设置有冲突返回false
 *****************************/
bool Stm32Service::setExclusiveTEVMode( UINT8 ucWorkMode )
{
    bool bRet = false;
    if( TEV_NONE_MODE == m_eTevExclusiveMode )//没有被其它模式所独占
    {
        m_eTevExclusiveMode = (TEVMode)ucWorkMode;
        bRet = true;
    }
    else//已被独占，则无法设置独占
    {
        bRet = false;
    }
    return bRet;
}
