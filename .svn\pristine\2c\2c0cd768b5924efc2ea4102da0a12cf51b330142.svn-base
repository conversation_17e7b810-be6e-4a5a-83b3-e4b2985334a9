#include "archivemanager.h"
#include <QStringList>
#include "datadefine.h"
#include "fileoper/fileoperutil.h"
#include "appcomm/qjason/qjson.h"
#include "log/log.h"


bool assetInfoSort(const DistributeNetAccessNS::RealAssetInfo &stInfo1, const DistributeNetAccessNS::RealAssetInfo &stInfo2)
{
    return (stInfo1.iSN <= stInfo2.iSN);
}

/**************************************************************
 * 功能：构造函数
 * 输入参数：
 *      parent：父指针
 * ************************************************************/
ArchiveManager::ArchiveManager(QObject *parent)
    : QObject(parent)
{
    init();
}

/**************************************************************
 * 功能：析构函数
 * ************************************************************/
ArchiveManager::~ArchiveManager()
{
    deinit();
}

/**************************************************************
 * 功能：初始化
 * ************************************************************/
void ArchiveManager::init()
{
    //scanAllArchiveInfos();
    return;
}

/**************************************************************
 * 功能：反初始化
 * ************************************************************/
void ArchiveManager::deinit()
{
    return;
}

/**************************************************************
 * 功能：模块服务单例类
 * ************************************************************/
ArchiveManager* ArchiveManager::instance()
{
    static ArchiveManager objManager;
    return (&objManager);
}

/**************************************************************
 * 功能：创建档案路径
 * ************************************************************/
void ArchiveManager::createArchivePath()
{
    FileOperUtil::createDirectory(DistributeNetAccessNS::ARCHIVE_PATH);
    return;
}

/**************************************************************
 * 功能：解析所有档案信息
 * ************************************************************/
void ArchiveManager::scanAllArchiveInfos()
{
    QStringList qstrlstArchiveFiles;
    qstrlstArchiveFiles.clear();

    QStringList qstrlstTypes;
    qstrlstTypes.clear();

    // 获取指定文件夹所有档案信息文件
    qstrlstArchiveFiles = FileOperUtil::getFilePathsUnderDirPath(DistributeNetAccessNS::ARCHIVE_PATH, qstrlstTypes);

    QVector<DistributeNetAccessNS::ArchiveInfo> qvtArchiveInfos;
    qvtArchiveInfos.clear();

    // 逐个读取json文件中的内容
    for(int i = 0, iSize = qstrlstArchiveFiles.size(); i < iSize; ++i)
    {
        if(QString(DistributeNetAccessNS::FILE_JSON_SUFFIX) == FileOperUtil::getFileSuffix(qstrlstArchiveFiles.at(i)))
        {
            QByteArray qbaData;
            if(FileOperUtil::readFile(qstrlstArchiveFiles.at(i), qbaData))
            {
                parseArchiveInfos(qstrlstArchiveFiles.at(i), qbaData, qvtArchiveInfos);
            }
        }
    }

    if(m_qmt4ArchiveInfos.tryLock(DistributeNetAccessNS::g_iLockWaitTime))
    {
        m_qvtArchiveInfos = qvtArchiveInfos;
        m_qmt4ArchiveInfos.unlock();
    }

    return;
}

/**************************************************************
 * 功能：获取档案信息集合
 * 输出参数：
 *      qvtArchiveInfos：档案信息集合
 * ************************************************************/
void ArchiveManager::getArchiveInfos(QVector<DistributeNetAccessNS::ArchiveInfo> &qvtArchiveInfos)
{
    if(m_qmt4ArchiveInfos.tryLock(DistributeNetAccessNS::g_iLockWaitTime))
    {
        qvtArchiveInfos = m_qvtArchiveInfos;
        m_qmt4ArchiveInfos.unlock();
    }

    return;
}

/**************************************************************
 * 功能：解析档案信息
 * 输入参数：
 *      qstrFilePath：文件路径
 *      qbaJsonData：档案信息数据，json格式
 * 输出参数：
 *      qvtArchiveInfos：档案信息集合
 * ************************************************************/
void ArchiveManager::parseArchiveInfos(const QString &qstrFilePath, const QByteArray &qbaJsonData, QVector<DistributeNetAccessNS::ArchiveInfo> &qvtArchiveInfos)
{
    if(qbaJsonData.isEmpty())
    {
        return;
    }

    QJson objData(qbaJsonData);
    QJson objArchiveDatas(objData.value(DistributeNetAccessNS::TEST_ARCHIVES));
    if(objArchiveDatas.isArray())
    {
        for(int i = 0, iCount = objArchiveDatas.count(); i < iCount; ++i)
        {
            // 解析档案信息
            DistributeNetAccessNS::ArchiveInfo stArchiveInfo;
            stArchiveInfo.qstrFilePath = qstrFilePath;
            stArchiveInfo.eType = static_cast<DistributeNetAccessNS::ArchiveType>(objArchiveDatas.at(i).value(DistributeNetAccessNS::ARCHIVE_TYPE).toNumber());
            stArchiveInfo.qstrId = objArchiveDatas.at(i).value(DistributeNetAccessNS::ARCHIVE_ID).toString();
            stArchiveInfo.qstrName = objArchiveDatas.at(i).value(DistributeNetAccessNS::ARCHIVE_NAME).toString();

            // 主设备类型集合
            QJson objAssetsDatas(objArchiveDatas.at(i).value(DistributeNetAccessNS::MAIN_ASSETS));
            if(objAssetsDatas.isArray())
            {
                for(int j = 0, jCount = objAssetsDatas.count(); j < jCount; ++j)
                {
                    DistributeNetAccessNS::MainAssetInfo stMainAssetInfo;
                    stMainAssetInfo.iType = static_cast<int>(objAssetsDatas.at(j).value(DistributeNetAccessNS::MAIN_ASSET_TYPE).toNumber());
                    stMainAssetInfo.qstrId = objAssetsDatas.at(j).value(DistributeNetAccessNS::MAIN_ASSET_ID).toString();
                    stMainAssetInfo.qstrName = objAssetsDatas.at(j).value(DistributeNetAccessNS::MAIN_ASSET_NAME).toString();

                    // 子设备信息集合
                    QJson objSubAssetsDatas(objAssetsDatas.at(j).value(DistributeNetAccessNS::SUB_ASSETS));
                    if(objSubAssetsDatas.isArray())
                    {
                        for(int k = 0, kCount = objSubAssetsDatas.count(); k < kCount; ++k)
                        {
                            DistributeNetAccessNS::RealAssetInfo stRealAssetInfo;
                            stRealAssetInfo.eType = static_cast<DistributeNetAccessNS::AssetType>(objSubAssetsDatas.at(k).value(DistributeNetAccessNS::SUB_ASSET_TYPE).toNumber());
                            stRealAssetInfo.qstrId = objSubAssetsDatas.at(k).value(DistributeNetAccessNS::SUB_ASSET_ID).toString();
                            stRealAssetInfo.qstrName = objSubAssetsDatas.at(k).value(DistributeNetAccessNS::SUB_ASSET_NAME).toString();
                            stRealAssetInfo.iSN = static_cast<int>(objSubAssetsDatas.at(k).value(DistributeNetAccessNS::SUB_ASSET_SN).toNumber());

                            stMainAssetInfo.qvtRealAssetInfos.append(stRealAssetInfo);
                        }

                        // 排序
                        /*qSort(stMainAssetInfo.qvtRealAssetInfos.begin()
                              , stMainAssetInfo.qvtRealAssetInfos.end()
                              , [](const DistributeNetAccessNS::RealAssetInfo &stInfo1, const DistributeNetAccessNS::RealAssetInfo &stInfo2)
                        {
                            return (stInfo1.iSN <= stInfo2.iSN);
                        });*/

                        qSort(stMainAssetInfo.qvtRealAssetInfos.begin()
                              , stMainAssetInfo.qvtRealAssetInfos.end()
                              , assetInfoSort);
                    }
                    stArchiveInfo.qvtMainAssetInfos.append(stMainAssetInfo);
                }
            }

            if(!(qvtArchiveInfos.contains(stArchiveInfo)))
            {
                qvtArchiveInfos.append(stArchiveInfo);
            }
            else
            {
                logWarning(QString("archive id (%1) already exists.").arg(stArchiveInfo.qstrId));
            }
        }
    }

    return;
}

/**************************************************************
 * 功能：根据档案id获取档案信息
 * 输入参数：
 *      qstrId：档案id
 * 输出参数：
 *      stArchiveInfo：档案信息
 * 返回值
 *      bool：操作结果，true -- 成功，false -- 失败
 * ************************************************************/
bool ArchiveManager::getArchiveInfo(const QString &qstrId, DistributeNetAccessNS::ArchiveInfo &stArchiveInfo)
{
    bool bRet = false;

    if(qstrId.isEmpty())
    {
        return bRet;
    }

    QVector<DistributeNetAccessNS::ArchiveInfo> qvtArchiveInfos;
    qvtArchiveInfos.clear();

    getArchiveInfos(qvtArchiveInfos);

    for(auto iter = qvtArchiveInfos.begin(); iter != qvtArchiveInfos.end(); ++iter)
    {
        if(qstrId == iter->qstrId)
        {
            stArchiveInfo = *iter;
            bRet = true;
            break;
        }
    }

    return bRet;
}

/**************************************************************
 * 功能：根据档案id删除档案信息
 * 输入参数：
 *      qstrId：档案id
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ************************************************************/
bool ArchiveManager::deleteArchiveInfo(const QString &qstrId)
{
    bool bRet = false;

    // 目前这种删除方式是一个文件中只有一个档案信息
    DistributeNetAccessNS::ArchiveInfo stArchiveInfo;
    if(getArchiveInfo(qstrId, stArchiveInfo))
    {
        if(FileOperUtil::deleteFile(stArchiveInfo.qstrFilePath))
        {
            if(m_qmt4ArchiveInfos.tryLock(DistributeNetAccessNS::g_iLockWaitTime))
            {
                int iIndex = m_qvtArchiveInfos.indexOf(stArchiveInfo);
                if(0 <= iIndex && iIndex < m_qvtArchiveInfos.size())
                {
                    m_qvtArchiveInfos.remove(iIndex);
                    bRet = true;
                }

                m_qmt4ArchiveInfos.unlock();
            }
        }
    }

    return bRet;
}

