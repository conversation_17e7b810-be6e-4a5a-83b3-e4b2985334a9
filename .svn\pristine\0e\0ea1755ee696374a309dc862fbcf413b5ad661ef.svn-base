/*****< ndcsapi.h >************************************************************/
/*      Copyright 2012 - 2014 Stonestreet One.                                */
/*      All Rights Reserved.                                                  */
/*                                                                            */
/*  NDCSAPI - Stonestreet One Bluetooth Next DST Change Service (GATT based)  */
/*            API Type Definitions, Constants, and Prototypes.                */
/*                                                                            */
/*  Author:  Ajay Parashar                                                    */
/*                                                                            */
/*** MODIFICATION HISTORY *****************************************************/
/*                                                                            */
/*   mm/dd/yy  F. Lastname    Description of Modification                     */
/*   --------  -----------    ------------------------------------------------*/
/*   06/25/12  A. Parashar    Initial creation.                               */
/******************************************************************************/
#ifndef __NDCSAPIH__
#define __NDCSAPIH__

#include "SS1BTPS.h"         /* Bluetooth Stack API Prototypes/Constants.     */
#include "SS1BTGAT.h"        /* Bluetooth Stack GATT API Prototypes/Constants.*/
#include "NDCSType.h"        /* Next DST Change Service Types/Constants.      */

   /* Error Return Codes.                                               */

   /* Error Codes that are smaller than these (less than -1000) are     */
   /* related to the Bluetooth Protocol Stack itself (see BTERRORS.H).  */
#define NDCS_ERROR_INVALID_PARAMETER                     (-1000)
#define NDCS_ERROR_INVALID_BLUETOOTH_STACK_ID            (-1001)
#define NDCS_ERROR_INSUFFICIENT_RESOURCES                (-1002)
#define NDCS_ERROR_SERVICE_ALREADY_REGISTERED            (-1003)
#define NDCS_ERROR_INVALID_INSTANCE_ID                   (-1004)
#define NDCS_ERROR_MALFORMATTED_DATA                     (-1005)
#define NDCS_ERROR_UNKNOWN_ERROR                         (-1006)

   /* The followng defines the format of a _tagNDCS_Date_Time_Data_t    */
   /* This is used to represent Date-Time                               */
typedef __PACKED_STRUCT_BEGIN__ struct _tagNDCS_Date_Time_Data_t
{
  Word_t Year;
  Byte_t Month;
  Byte_t Day;
  Byte_t Hours;
  Byte_t Minutes;
  Byte_t Seconds;
} __PACKED_STRUCT_END__ NDCS_Date_Time_Data_t;

#define NDCS_DATE_TIME_DATA_SIZE                         (sizeof(NDCS_Date_Time_Data_t))

   /* The structure defines the format of _tagNDCS_Time_With_Dst_Data_t */
   /* This is used to represent Time with Dst structure The first member*/
   /* specifies Date-Time, and the second member specifies the DST      */
   /* Offset.                                                           */
typedef __PACKED_STRUCT_BEGIN__ struct _tagNDCS_Time_With_Dst_Data_t
{
   NDCS_Date_Time_Data_t Date_Time;
   Byte_t                Dst_Offset;
}__PACKED_STRUCT_END__ NDCS_Time_With_Dst_Data_t;

#define NDCS_TIME_WITH_DST_DATA_SIZE                     (sizeof(NDCS_Time_With_Dst_Data_t))

   /* The following enumeration covers all the events generated by the  */
   /* NDCS Service.These are used to determine the type of each event   */
   /* generated, and to ensure the proper union element is accessed for */
   /* the NDCS_Event_Data_t structure.                                  */
typedef enum
{
   etNDCS_Server_Read_Current_Time_Request
} NDCS_Event_Type_t;

   /* The following NDCS Service Event is dispatched to a NDCS Server   */
   /* when a NDCS Client sends request to read current time data.  The  */
   /* ConnectionID, ConnectionType, and RemoteDevice specifiy the Client*/
   /* that is making the Request.                                       */
typedef struct _tagNDCS_Read_Time_With_DST_Request_Data_t
{
   unsigned int           InstanceID;
   unsigned int           ConnectionID;
   unsigned int           TransactionID;
   GATT_Connection_Type_t ConnectionType;
   BD_ADDR_t              RemoteDevice;
} NDCS_Read_Time_With_DST_Request_Data_t;

#define NDCS_READ_TIME_WITH_DST_REQUEST_DATA_SIZE       (sizeof(NDCS_Read_Time_With_DST_Request_Data_t))

   /* The following structure represents the container structure for    */
   /* holding all NDCS Service Event Data.  This structure is received  */
   /* for each event generated. The Event_Data_Type member is used to   */
   /* determine the appropriate union member element to access the      */
   /* contained data.  The Event_Data_Size member contains the total    */
   /* size of the data contained in this event.                         */
typedef struct _tagNDCS_Event_Data_t
{
   NDCS_Event_Type_t Event_Data_Type;
   Word_t           Event_Data_Size;
   union
   {
      NDCS_Read_Time_With_DST_Request_Data_t   *NDCS_Read_Time_With_DST_Request_Data;
   } Event_Data;
} NDCS_Event_Data_t;

#define NDCS_EVENT_DATA_SIZE                     (sizeof(NDCS_Event_Data_t))


   /* The following structure contains the Handles that will need to be */
   /* cached by a NDCS client in order to only do service discovery     */
   /* once.                                                             */
typedef struct _tagNDCS_Client_Information_t
{
   Word_t Time_With_Dst;
} NDCS_Client_Information_t;

#define NDCS_CLIENT_INFORMATION_DATA_SIZE                (sizeof( NDCS_Client_Information_t))

   /* The following structure contains the Handles that will need to be */
   /* cached by a NDCS Server in order to only do service discovery     */
   /* once.                                                             */
typedef struct _tagNDCS_Server_Information_t
{
   Word_t Time_With_Dst;
} NDCS_Server_Information_t;

#define NDCS_SERVER_INFORMATION_DATA_SIZE                (sizeof( NDCS_Server_Information_t))


   /* The following declared type represents the Prototype Function for */
   /* a NDCS Service Event Receive Data Callback.  This function will be*/
   /* called whenever an NDCS Service Event occurs that is associated   */
   /* with the specified Bluetooth Stack ID.  This function passes to   */
   /* the caller the Bluetooth Stack ID, the NDCS Event Data that       */
   /* occurred and the NDCS Service Event Callback Parameter that was   */
   /* specified when this Callback was installed.  The caller is free to*/
   /* use the contents of the NDCS Service Event Data ONLY in the       */
   /* context of this callback.  If the caller requires the Data for a  */
   /* longer period of time, then the callback function MUST copy the   */
   /* data into another Data Buffer This function is guaranteed NOT to  */
   /* be invoked more than once simultaneously for the specified        */
   /* installed callback (i.e.  this function DOES NOT have be          */
   /* re-entrant).  It needs to be noted however, that if the same      */
   /* Callback is installed more than once, then the callbacks will be  */
   /* called serially.  Because of this, the processing in this function*/
   /* should be as efficient as possible.  It should also be noted that */
   /* this function is called in the Thread Context of a Thread that the*/
   /* User does NOT own.  Therefore, processing in this function should */
   /* be as efficient as possible (this argument holds anyway because   */
   /* another NDCS Service Event will not be processed while this       */
   /* function call is outstanding).                                    */
   /* ** NOTE ** This function MUST NOT Block and wait for events that  */
   /*            can only be satisfied by Receiving NDCS Service Event  */
   /*            Packets.  A Deadlock WILL occur because NO NDCS Event  */
   /*            Callbacks will be issued while this function is        */
   /*            currently outstanding.                                 */
typedef void (BTPSAPI *NDCS_Event_Callback_t)(unsigned int BluetoothStackID, NDCS_Event_Data_t *NDCS_Event_Data, unsigned long CallbackParameter);

   /* NDCS Server API.                                                  */

   /* The following function is responsible for opening a NDCS Server.  */
   /* The first parameter is the Bluetooth Stack ID on which to open the*/
   /* server.  The final parameter is a pointer to store the GATT       */
   /* Service ID of the registered NDCS service.  This can be used to   */
   /* include the service registered by this call.  This function       */
   /* returns the positive, non-zero, Instance ID or a negative error   */
   /* code.                                                             */
   /* * NOTE * Only 1 NDCS Server may be open at a time, per Bluetooth  */
   /*          Stack ID.                                                */
   /* * NOTE * All Client Requests will be dispatch to the EventCallback*/
   /*          function that is specified by the second parameter to    */
   /*          this function.                                           */
BTPSAPI_DECLARATION int BTPSAPI NDCS_Initialize_Service(unsigned int BluetoothStackID, NDCS_Event_Callback_t EventCallback, unsigned long CallbackParameter, unsigned int *ServiceID);

#ifdef INCLUDE_BLUETOOTH_API_PROTOTYPES
   typedef int (BTPSAPI *PFN_NDCS_Initialize_Service_t)(unsigned int BluetoothStackID, NDCS_Event_Callback_t EventCallback, unsigned long CallbackParameter, unsigned int *ServiceID);
#endif

   /* The following function is responsible for opening a NDCS Server.  */
   /* The first parameter is the Bluetooth Stack ID on which to open the*/
   /* server.  The second parameter is the Callback function to call    */
   /* when an event occurs on this Server Port.  The third parameter is */
   /* a user-defined callback parameter that will be passed to the      */
   /* callback function with each event.  The fourth parameter is a     */
   /* pointer to store the GATT Service ID of the registered NDCS       */
   /* service.  This can be used to include the service registered by   */
   /* this call.  The final parameter is a pointer, that on input can be*/
   /* used to control the location of the service in the GATT database, */
   /* and on ouput to store the service handle range.  This function    */
   /* returns the positive, non-zero, Instance ID or a negative error   */
   /* code.                                                             */
   /* * NOTE * Only 1 NDCS Server may be open at a time, per Bluetooth  */
   /*          Stack ID.                                                */
   /* * NOTE * All Client Requests will be dispatch to the EventCallback*/
   /*          function that is specified by the second parameter to    */
   /*          this function.                                           */
BTPSAPI_DECLARATION int BTPSAPI NDCS_Initialize_Service_Handle_Range(unsigned int BluetoothStackID, NDCS_Event_Callback_t EventCallback, unsigned long CallbackParameter, unsigned int *ServiceID, GATT_Attribute_Handle_Group_t *ServiceHandleRange);

#ifdef INCLUDE_BLUETOOTH_API_PROTOTYPES
   typedef int (BTPSAPI *PFN_NDCS_Initialize_Service_Handle_Range_t)(unsigned int BluetoothStackID, NDCS_Event_Callback_t EventCallback, unsigned long CallbackParameter, unsigned int *ServiceID, GATT_Attribute_Handle_Group_t *ServiceHandleRange);
#endif

   /* The following function is responsible for closing a previously    */
   /* opened NDCS Server.  The first parameter is the Bluetooth Stack ID*/
   /* on which to close the server.  The second parameter is the        */
   /* InstanceID that was returned from a successful call to            */
   /* NDCS_Initialize_Service().  This function returns a zero if       */
   /* successful or a negative return error code if an error occurs.    */
BTPSAPI_DECLARATION int BTPSAPI NDCS_Cleanup_Service(unsigned int BluetoothStackID, unsigned int InstanceID);

#ifdef INCLUDE_BLUETOOTH_API_PROTOTYPES
   typedef int (BTPSAPI *PFN_NDCS_Cleanup_Service_t)(unsigned int BluetoothStackID, unsigned int InstanceID);
#endif

   /* The following function is responsible for querying the number of  */
   /* attributes that are contained in the NDCS Service that is         */
   /* registered with a call to NDCS_Initialize_Service().  This        */
   /* function returns the non-zero number of attributes that are       */
   /* contained in a NDCS Server or zero on failure.                    */
BTPSAPI_DECLARATION unsigned int BTPSAPI NDCS_Query_Number_Attributes(void);

#ifdef INCLUDE_BLUETOOTH_API_PROTOTYPES
   typedef unsigned int (BTPSAPI *PFN_NDCS_Query_Number_Attributes_t)(void);
#endif

   /* The following function is responsible for sending the Next DST    */
   /* Change Time read request response.  The first parameter is        */
   /* Bluetooth Stack ID of the Bluetooth Device.  The second parameter */
   /* is the Transaction ID.  The final parameter is the value of       */
   /* NDCS_Time_With_Dst_Data_t structure.  This function returns a zero*/
   /* if successful or a negative return error code if an error occurs. */
BTPSAPI_DECLARATION int BTPSAPI NDCS_Time_With_DST_Read_Request_Response(unsigned int BluetoothStackID, unsigned int TransactionID, NDCS_Time_With_Dst_Data_t *Next_Dst_Change_Time);

#ifdef INCLUDE_BLUETOOTH_API_PROTOTYPES
   typedef int (BTPSAPI *PFN_NDCS_Time_With_DST_Read_Request_Response_t)(unsigned int BluetoothStackID, unsigned int TransactionID, NDCS_Time_With_Dst_Data_t *Next_Dst_Change_Time);
#endif

   /* The following function is responsible for responding to a Next DST*/
   /* Change Time read request with an error response.  The first       */
   /* parameter is Bluetooth Stack ID of the Bluetooth Device.  The     */
   /* second parameter is the Transaction ID.  The final parameter is   */
   /* the GATT error code to respond to the request with.  This function*/
   /* returns a zero if successful or a negative return error code if an*/
   /* error occurs.                                                     */
BTPSAPI_DECLARATION int BTPSAPI NDCS_Time_With_DST_Read_Request_Error_Response(unsigned int BluetoothStackID, unsigned int TransactionID, Byte_t ErrorCode);

#ifdef INCLUDE_BLUETOOTH_API_PROTOTYPES
   typedef int (BTPSAPI *PFN_NDCS_Time_With_DST_Read_Request_Error_Response_t)(unsigned int BluetoothStackID, unsigned int TransactionID, Byte_t ErrorCode);
#endif

   /* NDCS Client API.                                                  */

   /* The following function is responsible for parsing a value received*/
   /* from a remote NDCS Server interpreting it as a Next DST Changetime*/
   /* characteristic.  The first parameter is the length of the value   */
   /* returned by the remote NDCS Server.  The second parameter is a    */
   /* pointer to the data returned by the remote NDCS Server.  The final*/
   /* parameter is a pointer to store the parsed Current Time           */
   /* Measurement value.  This function returns a zero if successful or */
   /* a negative return error code if an error occurs.                  */
BTPSAPI_DECLARATION int BTPSAPI NDCS_Decode_Time_With_Dst(unsigned int ValueLength, Byte_t *Value, NDCS_Time_With_Dst_Data_t *Next_Dst_Change_Time);

#ifdef INCLUDE_BLUETOOTH_API_PROTOTYPES
   typedef int (BTPSAPI *PFN_NDCS_Decode_Time_With_Dst_t)(unsigned int ValueLength, Byte_t *Value, NDCS_Time_With_Dst_Data_t *Next_Dst_Change_Time);
#endif

#endif
