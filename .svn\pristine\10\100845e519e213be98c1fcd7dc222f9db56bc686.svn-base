#include "loadingdialog.h"
#include <QBoxLayout>
#include <QPainter>
#include <QBitmap>


const QString TEXT_STYLE = "QLabel{font-size: 25px;}";
const int LOADINGDIALOG_WIDTH = 400;
const int LOADINGDIALOG_HEIGHT = 240;          // 等待框的高宽

LoadingDialog::LoadingDialog(const QString& strText, QWidget* parent)
    : QDialog(parent, Qt::FramelessWindowHint | Qt::Dialog)
{
    setFixedSize(LOADINGDIALOG_WIDTH, LOADINGDIALOG_HEIGHT);

    m_pGifMovie = new QMovie(this);
    m_pGifMovie->setFileName(":/gif/loading.gif");

    m_pGifLabel = new QLabel(this);
    m_pGifLabel->setAlignment(Qt::AlignCenter);
    m_pGifLabel->setMovie(m_pGifMovie);
    m_pGifMovie->start();

    QBoxLayout *boxLayout = new QBoxLayout(QBoxLayout::TopToBottom);

    m_pTextLabel = new QLabel(strText, this);
    m_pTextLabel->setAlignment(Qt::AlignLeft);
    m_pTextLabel->setWordWrap(true);
    m_pTextLabel->setContentsMargins(5, 2, 5, 2);
    m_pTextLabel->setStyleSheet(TEXT_STYLE);

    boxLayout->addWidget(m_pGifLabel, Qt::AlignCenter);
    boxLayout->addWidget(m_pTextLabel, Qt::AlignBottom);

    setLayout(boxLayout);
}

LoadingDialog::~LoadingDialog()
{

}

/*******************************************************
 * 功能：重绘事件
 * 输入参数：
 *      pEvent：事件
 * *****************************************************/
void LoadingDialog::paintEvent(QPaintEvent *pEvent)
{
    Q_UNUSED(pEvent);

    QBitmap bitmap(this->size());
    QPainter painter(&bitmap);
    painter.setPen(Qt::NoPen);
    painter.fillRect(bitmap.rect(), Qt::white);
    //painter.setBrush(Qt::white);
    painter.setBrush(QColor(0, 0, 0));
    //painter.setRenderHint(QPainter::Antialiasing);
    painter.drawRoundedRect(rect(), 10, 10);
    setMask(bitmap);           // 绘制圆角外框

    QPainter painterBG(this);
    painterBG.setPen(Qt::NoPen);
    QLinearGradient linearGradient(QPoint(0, 0), QPoint(width(), height()));
    linearGradient.setColorAt(0, QColor(130, 181, 238));        //245, 245, 245   130, 181, 238
    linearGradient.setColorAt(1, QColor(189, 217, 247));        //245, 245, 245   189, 217, 247
    painterBG.setBrush(QBrush(linearGradient));
    painterBG.drawRect(rect());

    return;
}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void LoadingDialog::keyPressEvent(QKeyEvent* event)
{
    //拦截按键事件
    Q_UNUSED(event);
    return;
}
