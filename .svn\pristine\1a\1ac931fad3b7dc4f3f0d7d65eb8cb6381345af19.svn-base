#include "RecordPlay.h"

namespace RecordPlay
{
    Logger* m_pLog = NULL;
}
/************************************************
 * 功能：设置日志模块
 * 输入参数：
 *      pLogger -- 日志模块指针
 ************************************************/
void RecordPlay::setLogger( Logger* pLogger )
{
    m_pLog = pLogger;
}

/************************************************
 * 功能：获取日志模块句柄
 * 输入参数：
 *      eLevel -- 等级
 * 返回：
 *      日志模块
 ************************************************/
LogInstance RecordPlay::logger( Logging::LogLevel eLevel )
{
    return (NULL==m_pLog) ? LogInstance( Logging::TRACE, NULL ) : m_pLog->log( eLevel );
}
