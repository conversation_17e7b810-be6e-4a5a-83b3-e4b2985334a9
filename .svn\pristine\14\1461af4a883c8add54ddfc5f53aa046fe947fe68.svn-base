/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* uhfprpspdaview.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年4月21日
*       新版本重构
* 摘要：UHF prps view接口和成员申明

* 当前版本：1.0
*/

#ifndef TEVPRPSPDAVIEW_H
#define TEVPRPSPDAVIEW_H

#include <QWidget>
#include "tevprpsviewbase.h"
#include "tev/tevdefine.h"
#include "buttonBar/LabelButtonBar.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "prps/prpsview/uhfprpsunionview.h"
#include "messageBox/msgbox.h"
#include "pda/pda.h"
#include "loadingView/textloadingview.h"
#include "PDAUi/PDAUiBean/addtestdatadialog.h"

//data save
#include "tev/dataSave/prps/tevprpsandprpddatasave.h"

class TEVPRPSPDAView : public TEVPRPSViewBase
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        parent:父控件指针
    *************************************************************/
    TEVPRPSPDAView(const QString &strTitle, const ItemTestData &stTestDataInfo, QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~TEVPRPSPDAView( );

    /************************************************
     * 函数名   : setTestPointInfo
     * 输入参数 : qsStation---站名
     *          qsDevice---设备名
     *          qsTestPoint---测点名
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置显示测点信息
     ************************************************/
    void setTestPointInfo(const QString &qsStation, const QString &qsDevice,const QString &qsTestPoint);

protected:
    /*************************************************
    功能： 响应S键事件
    *************************************************************/
    void onSKeyPressed();

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

    /************************************************
     * 函数名   : onStartTest
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :槽函数，起动采样
     ************************************************/
    void onStartTest();

private:
    /************************************************
     * 函数名   : setButtonBarDatas
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置buttonbar显示的参数
     ************************************************/
    void setButtonBarDatas();

    /************************************************
     * 函数名   : createButtonbar
     * 输入参数 : parent---父控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建buttonbar
     ************************************************/
    PushButtonBar* createButtonbar(QWidget *parent);

    /************************************************
     * 函数名   : createMoreButtonBar
     * 输入参数 : pButtonBar---挂接用的控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建更多...
     ************************************************/
    LabelButtonBar *createMoreButtonBar(PushButtonBar *pButtonBar);


    /************************************************
     * 函数名   : switchSample
     * 输入参数 : eState: 采样状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 起动、暂停采样切换
     ************************************************/
    void switchSample(State eState);


    /************************************************
     * 函数名   : cloudDiagnosis
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 云诊断采样数据
     ************************************************/
    void cloudDiagnosis();

    /************************************************
     * 函数名   : restoreDefault
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 恢复默认参数
     ************************************************/
    void restoreDefault();

    /************************************************
     * 函数名   : setSampleBtnText
     * 输入参数 : eState: 采样状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 更新采样状态设置采样按钮显示的文本
     ************************************************/
    void setSampleBtnText(State eState);


    /************************************************
     * 函数名   : setConfigData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :
     ************************************************/
    void setConfigData();


    /*************************************************
    功能： 保存设置
    *************************************************************/
    bool saveConfig(void);


    /************************************************
     * 函数名   : initData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 初始化数据成员
     ************************************************/
    void initData();

    /*************************************************
    函数名： saveTestData
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 测试数据传给pdaservice保存
    *************************************************************/
    void saveTestData();

    /************************************************
     * 函数名   : savePDAData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 保存采样数据到xml文件
     ************************************************/
    bool savePDAData(const QString &stationName, const QString& deviceName);

    /************************************************
     * 函数名   : saveCloudDiagnosisData
     * 输入参数 : stationName---站名
     *          deviceName---设备名
     * 输出参数 : qsFilePath---数据文件路径
     * 返回值   : NULL
     * 功能     : 保存云诊断数据到xml文件
     ************************************************/
    bool saveCloudDiagnosisData(QString &qsFilePath , const QString &stationName = "", const QString& deviceName = "");

    /************************************************
     * 函数名   : composeSavedData
     * 输入参数 : stationName---站名
     *          deviceName---设备名
     * 输出参数 : stSavedData---需要保存的数据
     * 返回值   : NULL
     * 功能     : 组织需要保存的数据
     ************************************************/
    void composeSavedData(const QString &stationName, const QString& deviceName, TEVPRPSPRPDDataInfo &stSavedData);

    /************************************************
     * 函数名   : createChart
     * 输入参数 : parent---父控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建图谱
     ************************************************/
    ChartWidget* createChart(QWidget *parent);

    /************************************************
     * 函数名   : showMoreConfigBar
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 显示更多...
     ************************************************/
    void showMoreConfigBar();

    /************************************************
     * 函数名   : setChartParameters
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 给图谱设置参数
     ************************************************/
    void setChartParameters();

    /************************************************
     * 函数名   : setAllWorkSets
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置所有的工作参数
     ************************************************/
    void setAllWorkSets();

    /*************************************************
    功能： 弹出新增测试项窗口
    *************************************************************/
    void addTestData();

    /************************************************
     * 函数名   : initParametersByDataFile
     * 输入参数 : qsFile---数据文件存放路径
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :根据数据文件里的数据初始化采样参数
     ************************************************/
    void initParametersByDataFile(const QString &qsFile);

    /************************************************
     * 函数名   : playBackTestedData
     * 输入参数 : qsFile---数据文件存放路径
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 回放数据文件里的测试数据
     ************************************************/
    void playBackTestedData(const QString &qsFile);

    void setPRPSData(TEVPRPSPRPDDataInfo &stSavedData);

    void setPRPDData(TEVPRPSPRPDDataInfo &stSavedData);

    void displayMap(TEVPRPSPRPDDataInfo &PlayBackDataInfo);


    void setMapHead(const QString &stationName, const QString& deviceName, TEVPRPSPRPDDataInfo &stSavedData);


    void setMapInfo(TEVPRPSPRPDDataInfo &stSavedData);

protected:
    /*************************************************
    功能： 键盘事件
    输入参数:
        event -- 事件
    *************************************************************/
    void keyPressEvent( QKeyEvent* event );

private slots:
    void startColudDiagnosis();

    void onTitleBarClicked( void );

    /************************************************
     * 函数名   : onDataRead
     * 输入参数 : stData: 采样数据
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，处理收到的采样数据
     ************************************************/
    void onDataRead(UHF::PRPSData stData, MultiServiceNS::USERID Id);

    /************************************************
     * 函数名   : onSignalChanged
     * 输入参数 : eState: 信号状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，信号状态的改变
     ************************************************/
    void onSignalChanged(Module::SignalState eState);

    /************************************************
     * 函数名   : onSyncStateChanged
     * 输入参数 : eState: 同步状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，同步状态的改变
     ************************************************/
    void onSyncStateChanged(Module::SyncState eState);

    /*************************************************
    参数： NULL
    功能： 云诊断的结果
    *************************************************/
    void onCloudDiagnosis(QString strResult, QString strCode, bool bRet);

    /************************************************
     * 函数名   : onAddTestData
     * 输入参数 :  struct_AddingTestData&
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :槽函数，将新增测试项界面的信息写入pdaTask中
     ************************************************/
    void onAddTestData(struct_AddingTestData&);

    void setPRPSMapHead(const QString &stationName, const QString& deviceName, TEVPRPSPRPDDataInfo &stSavedData);

    void setPRPDMapHead(const QString &stationName, const QString& deviceName, TEVPRPSPRPDDataInfo &stSavedData);

    void setPRPSMapInfo(TEVPRPSPRPDDataInfo &stSavedData);

    void setPRPDMapInfo(TEVPRPSPRPDDataInfo &stSavedData);

private:

    QString m_qsStation;
    QString m_qsDevice;

    //站名label
    QLabel *m_pStationLabel;
    //设备名label
    QLabel *m_pDeviceLabel;
    //测点名label
    QLabel *m_pTestPointLabel;

    UHFPRPSService* m_pService;//服务模块
    ConfigInstance* m_pConfig;//配置模块
    LabelButtonBar* m_pMoreConfigView;//更多设置视图
    UhfPrpsUnionView *m_pChart;//图谱对象

    Module::SignalState m_eSignalState;// 信号状态
    Module::SyncSource m_eSyncSource;//同步方式
    Module::SyncState m_eSyncState;//同步状态
    UHF::ForwardGain m_eForwardGain;//前置增益
    UHF::BandWidth m_eBandWidth;//带宽

    int m_iPhaseAlias; //相移角度
    bool m_isSuspend;   //采样是否暂停标志
    UHF::PRPSData m_stData; //采样数据
    UINT16 m_usMaxSpectrumValue;// 最大值

    QVector<double> m_vMaxValueVector;  //存放最大采样数据的容器
    UINT8 m_ucSysFreq;//系统频率
    bool m_isAccumulation; //是否累积标志
    ControlButton *m_pSampleBtn; //采样按钮
    bool m_bIsStartColudDiagnosis;//是否启动云诊断标志
    State m_eState;

    bool m_isTested;    //是否已测标志
    QString m_qsTestDataFilePath;//测试数据文件路径

    enum {
        START_SAMPLE_TIME_INTERVAL_1000MS = 1000
    };

    TextLoadingView *m_pLoadingWidget;
};

#endif // TEVPRPSPDAVIEW_H
