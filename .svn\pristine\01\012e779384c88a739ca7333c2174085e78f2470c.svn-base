﻿#include "prpsdatamap.h"
#include "datamapconfig.h"
#include "common/datamaputils.h"

PRPSDataMap::PRPSDataMap( )
{
    m_sMapHead.eCode = DataFileNS::SPECTRUM_CODE_UHF_PRPS;
}

PRPSDataMap::~PRPSDataMap()
{

}

QString PRPSDataMap::mapRootTag()
{
    return XML_FILE_NODE_PRPS;
}

/*************************************************
功能： 生成map数据部分的xml文本
输入参数：
        baData -- 保存图谱数据的xml文本内容
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool PRPSDataMap::saveMapDataXML(XMLDocument *pDoc, QDomElement &element,bool bCrypt)
{
    qDebug()<<"PRPSDataMap::saveMapDataXML";
    pDoc->beginElement(element);
    pDoc->beginElement(XML_FILE_NODE_DATA);

    int iPerDataSize = perDataSize();
    //data
    QByteArray baDatas;
    baDatas = QByteArray::fromRawData( (char*)m_pPrpsData, m_stPRPSExt.iPowerFreCycleCount * m_stPRPSExt.iPhaseIntervalCount * iPerDataSize );//zyj
    QString strData = baDatas.toBase64();
    pDoc->setValue( XML_FILE_NODE_DATA, strData );

    //color
    QByteArray baColor;
    baColor = QByteArray::fromRawData( (char*)m_pPRPSColor, 3*m_stPRPSExt.iPowerFreCycleCount * m_stPRPSExt.iPhaseIntervalCount * sizeof(quint8) );//zyj
    QString strColor = baColor.toBase64();
    pDoc->setValue( XML_FILE_NODE_DATA_COLOR, strColor );

    bool isSuccess = pDoc->save(bCrypt);
    return isSuccess;
}

/*************************************************
功能： 生成map扩展部分的xml文本
输入参数：
        pDoc -- 保存图谱数据的xml文本内容
        element---
        bCrypt---
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool PRPSDataMap::saveMapExtXML(XMLDocument *pDoc, QDomElement &element, bool bCrypt)
{
    pDoc->beginElement(element);

    pDoc->beginElement(XML_FILE_NODE_EXTINFORMATION);
    pDoc->setValue( PRPSInfoNode::TEXT_AMP_UNIT, QString::number(m_stPRPSExt.eAmpUnit) );
    pDoc->setValue( PRPSInfoNode::TEXT_AMP_MIN, QString::number(m_stPRPSExt.fAmpLowerLimit,'f',1));
    pDoc->setValue( PRPSInfoNode::TEXT_AMP_MAX, QString::number(m_stPRPSExt.fAmpUpperLimit,'f',1) );
    pDoc->setValue( PRPSInfoNode::TEXT_BAND_WIDTH, QString::number(m_stPRPSExt.eBandWidth) );
    //pDoc->setValue( PRPSInfoNode::TEXT_RANGE_MAX, QString::number(m_stPRPSExt.iRangeMax) );
    //qDebug()<<"PRPSDataMap::saveMapExtXML, m_stPRPSExt.iRangeMax:"<<m_stPRPSExt.iRangeMax;
    pDoc->setValue( PRPSInfoNode::TEXT_FRQ_MIN, QString::number(m_stPRPSExt.fFrequencyMin,'f',1) );
    pDoc->setValue( PRPSInfoNode::TEXT_FRQ_MAX, QString::number(m_stPRPSExt.fFrequencyMax,'f',1) );
    pDoc->setValue( PRPSInfoNode::TEXT_SINGLE_CYCLE_INTERVAL, QString::number(m_stPRPSExt.iSingleCycleInterval) );
    pDoc->setValue( PRPSInfoNode::TEXT_PHASE_INTVL_COUNT, QString::number(m_stPRPSExt.iPhaseIntervalCount) );
    pDoc->setValue( PRPSInfoNode::TEXT_QUANTY_AMP, QString::number(m_stPRPSExt.iQuantificationAmp) );
    pDoc->setValue( PRPSInfoNode::TEXT_PFRECYCLE_COUNT, QString::number(m_stPRPSExt.iPowerFreCycleCount) );
    pDoc->setValue( PRPSInfoNode::TEXT_RECORD_PRPS_CYCLE_COUNT, QString::number(m_stPRPSExt.uiRecordPRPSCycleCount) );


    QString strPR;
    //放电概率
    for(int i = 0; i < 8; ++i)
    {

        QString strPRTemp = QString::number(m_stPRPSExt.ucaDischargeTypeProb[i]);
        strPR = strPR + strPRTemp;
        strPR = strPR + ",";
    }

    strPR.remove(strPR.size()-1, 1);
    pDoc->setValue( PRPSInfoNode::TEXT_PD_TYPE_PR, strPR );

    pDoc->setValue( PRPSInfoNode::TEXT_EFFECIVE_SIGN, QString::number(m_stPRPSExt.eDataSign) );
    pDoc->setValue( PRPSInfoNode::TEXT_GAIN, QString::number(m_stPRPSExt.sGain) );
    pDoc->setValue( PRPSInfoNode::TEXT_GAIN_TYPE, QString::number(m_stPRPSExt.eGainType) );
    pDoc->setValue( PRPSInfoNode::TEXT_SYNC_SOURCE, QString::number(m_stPRPSExt.eSyncSource) );
    pDoc->setValue( PRPSInfoNode::TEXT_SYNC_STATE, QString::number(m_stPRPSExt.ucSyncState) );
    pDoc->setValue( PRPSInfoNode::TEXT_SYNC_FREQ, QString::number(m_stPRPSExt.fSyncFreq) );
    pDoc->setValue( PRPSInfoNode::TEXT_ANALYSIS_THRESHOLD, QString::number(m_stPRPSExt.fAnalysisThreshold, 'f', 2) );
    pDoc->setValue( PRPSInfoNode::TEXT_MAX_VALUE, QString::number(m_stPRPSExt.fMax, 'f', 2) );
    bool isSuccess = pDoc->save(bCrypt);
    return isSuccess;
}

/*************************************************
功能： 生成map扩展部分的qbytearray文本，方便存储为二进制
*************************************************************/
void PRPSDataMap::saveMapExtBinary( QByteArray &baPackage )
{
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.setFloatingPointPrecision(QDataStream::SinglePrecision);
    out.device()->seek(out.device()->size());
    out << (quint8)m_stPRPSExt.eAmpUnit;

    out << m_stPRPSExt.fAmpLowerLimit;
    out << m_stPRPSExt.fAmpUpperLimit;

    out << (quint8)m_stPRPSExt.eBandWidth;
    qDebug()<<"PRPSDataMap::saveMapExtBinary, m_stPRPSExt.eBandWidth:"<<m_stPRPSExt.eBandWidth;
    out << m_stPRPSExt.fFrequencyMin;
    out << m_stPRPSExt.fFrequencyMax;

    out << (qint32)m_stPRPSExt.iPhaseIntervalCount;
    out << (qint32)m_stPRPSExt.iQuantificationAmp;
    out << (qint32)m_stPRPSExt.iPowerFreCycleCount;

    for(uint i = 0; i < sizeof(m_stPRPSExt.ucaDischargeTypeProb) / sizeof(quint8); i ++)
    {
        out << (quint8)m_stPRPSExt.ucaDischargeTypeProb[i];
    }

    DataFormat::addBinaryInfo(m_stPRPSExt.strBGFileName, DataFileNS::TYPE_ASCII, out, DataFileNS::BG_FILE_NAME_LEN);

    quint8 ucaReserveData [DataFileNS::PRPSPRPD_RESERVE];
    memset(ucaReserveData, 0, DataFileNS::PRPSPRPD_RESERVE);
    for(int i = 0; i < DataFileNS::PRPSPRPD_RESERVE; i ++)
    {
         out << (quint8)ucaReserveData[i];
    }
}

/*************************************************
功能： 生成map数据部分的qbytearray文本，方便存储为二进制
*************************************************************/
void PRPSDataMap::saveMapDataBinary( QByteArray &baPackage )
{
    int iDataCnt = m_stPRPSExt.iPhaseIntervalCount * m_stPRPSExt.iPowerFreCycleCount;
    if( iDataCnt <= 0)
    {
        return;
    }
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.setFloatingPointPrecision(QDataStream::DoublePrecision);
    out.device()->seek(out.device()->size());

    qDebug() << " PRPSDataMap::saveMapDataBinary, iDataCnt : " << iDataCnt;
    double * daData = NULL;
    if(NULL != m_pPrpsData)
    {
        qDebug() << " PRPSDataMap::saveMapDataBinary, NULL != m_pPrpsData ";
        daData = (double *)m_pPrpsData;
        for(int i = 0; i < iDataCnt; i ++)
        {
            double iData = daData[i];
            out << (double)iData;
        }
    }
}

/*************************************************
功能： 解析map的扩展字段
输入参数：
        baData -- 图谱数据的xml文本内容
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool PRPSDataMap::parseMapExtXML(XMLDocument *pDoc, const QString &strRootTag )
{
    bool bRet = true;

    pDoc->beginElement( strRootTag );
    pDoc->beginElement( XML_FILE_NODE_EXTINFORMATION );
    m_stPRPSExt.eAmpUnit = (DataFileNS::AmpUnit)pDoc->value(PRPSInfoNode::TEXT_AMP_UNIT).toInt();
    m_stPRPSExt.fAmpLowerLimit = pDoc->value(PRPSInfoNode::TEXT_AMP_MIN).toFloat();
    m_stPRPSExt.fAmpUpperLimit = pDoc->value(PRPSInfoNode::TEXT_AMP_MAX).toFloat();
    m_stPRPSExt.eBandWidth = (DataFileNS::MapBandWidth)pDoc->value(PRPSInfoNode::TEXT_BAND_WIDTH).toInt();
    //m_stPRPSExt.iRangeMax = pDoc->value(PRPSInfoNode::TEXT_RANGE_MAX).toInt();
    //qDebug()<<"PRPSDataMap::parseMapExtXML, m_stPRPSExt.iRangeMax:"<<m_stPRPSExt.iRangeMax;
    m_stPRPSExt.fFrequencyMin = pDoc->value(PRPSInfoNode::TEXT_FRQ_MIN).toFloat();
    m_stPRPSExt.fFrequencyMax = pDoc->value(PRPSInfoNode::TEXT_FRQ_MAX).toFloat();
    m_stPRPSExt.iSingleCycleInterval = pDoc->value(PRPSInfoNode::TEXT_SINGLE_CYCLE_INTERVAL).toInt();
    m_stPRPSExt.iPhaseIntervalCount = pDoc->value(PRPSInfoNode::TEXT_PHASE_INTVL_COUNT).toInt();
    m_stPRPSExt.iQuantificationAmp = pDoc->value(PRPSInfoNode::TEXT_QUANTY_AMP).toInt();
    m_stPRPSExt.iPowerFreCycleCount = pDoc->value(PRPSInfoNode::TEXT_PFRECYCLE_COUNT).toInt();
    m_stPRPSExt.uiRecordPRPSCycleCount = pDoc->value(PRPSInfoNode::TEXT_RECORD_PRPS_CYCLE_COUNT).toUInt();


    QByteArray baDischargeTypeProb = QByteArray::fromHex( pDoc->value(PRPSInfoNode::TEXT_PD_TYPE_PR).toLatin1() );
    memcpy( m_stPRPSExt.ucaDischargeTypeProb, baDischargeTypeProb.data(), baDischargeTypeProb.size() );

    m_stPRPSExt.eDataSign = (PRPSMapNS::MapDataSign)( pDoc->value(PRPSInfoNode::TEXT_EFFECIVE_SIGN).toInt() );
    m_stPRPSExt.eGainType = (DataFileNS::GainType)pDoc->value(PRPSInfoNode::TEXT_GAIN_TYPE).toInt();
    m_stPRPSExt.sGain = pDoc->value(PRPSInfoNode:: TEXT_GAIN).toShort();
    m_stPRPSExt.eSyncSource = (DataFileNS::SyncSource)pDoc->value(PRPSInfoNode::TEXT_SYNC_SOURCE).toUInt();
    m_stPRPSExt.ucSyncState = pDoc->value(PRPSInfoNode::TEXT_SYNC_STATE).toUInt();
    m_stPRPSExt.fSyncFreq = pDoc->value(PRPSInfoNode::TEXT_SYNC_FREQ).toFloat();
    m_stPRPSExt.fAnalysisThreshold = pDoc->value(PRPSInfoNode::TEXT_ANALYSIS_THRESHOLD).toFloat();
    m_stPRPSExt.fMax = pDoc->value(PRPSInfoNode::TEXT_MAX_VALUE).toFloat();

    pDoc->endElement();
    pDoc->endElement();
    return bRet;
}

/*************************************************
功能： 解析map除头以外的所有字段
输入参数：
        bytes -- 图谱数据的二进制文件内容
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool PRPSDataMap::parseMapDataBinary(const QByteArray &bytes)
{
    QDataStream in(bytes);
    in.setByteOrder(QDataStream::LittleEndian);

    quint8 ampUnit = 0, bandWidth = 0;

    DataFormat::getUInt8(in, ampUnit);                      //幅值单位
    DataFormat::getFloat(in, m_stPRPSExt.fAmpLowerLimit);   //幅值下限
    DataFormat::getFloat(in, m_stPRPSExt.fAmpUpperLimit);   //幅值上限
    DataFormat::getUInt8(in, bandWidth);                    //频带
    DataFormat::getFloat(in, m_stPRPSExt.fFrequencyMin);
    DataFormat::getFloat(in, m_stPRPSExt.fFrequencyMax);
    DataFormat::getInt32(in, m_stPRPSExt.iPhaseIntervalCount);
    DataFormat::getInt32(in, m_stPRPSExt.iQuantificationAmp);
    DataFormat::getInt32(in, m_stPRPSExt.iPowerFreCycleCount);
    DataFormat::getUint8Array(in, m_stPRPSExt.ucaDischargeTypeProb, 8); //放电类型概率
    //背景文件名称
    DataFormat::getAscii(in, DataFileNS::BG_FILE_NAME_LEN, m_stPRPSExt.strBGFileName);
    m_stPRPSExt.eAmpUnit = DataFileNS::AmpUnit(ampUnit);
    m_stPRPSExt.eBandWidth = DataFileNS::MapBandWidth(bandWidth);

    // 跳过预留字节
    if(in.status() != QDataStream::Ok || DataFileNS::PRPSPRPD_RESERVE != in.skipRawData(DataFileNS::PRPSPRPD_RESERVE))
    {
        return false;
    }

    // 图谱数据
    if ( m_pPrpsData )
    {
        DataMapUtils::deleteDataBuf( (void**)&m_pPrpsData );
    }
    unsigned long iBufSize = 0;
    m_pPrpsData = DataMapUtils::createDataBuf( m_stPRPSExt.iPowerFreCycleCount * m_stPRPSExt.iPhaseIntervalCount,
                                                 m_sMapHead.eDataPrimitiveType, iBufSize );
    in.readRawData(reinterpret_cast<char *>(m_pPrpsData), iBufSize);

//    //----for test----
//    int nCount = m_stPRPSExt.iPowerFreCycleCount * m_stPRPSExt.iPhaseIntervalCount;
//    QVector<float> datas;
//    datas.resize(nCount);
//    memcpy(datas.data(), m_pPrpsData, iBufSize);
//    qDebug() << "----prps data" << datas;
//    //----

    return (in.status() == QDataStream::Ok);
}


/*************************************************
功能： 解析map的data字段
输出参数：
        baData -- 图谱数据的xml文本内容
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool PRPSDataMap::parseMapDataXML(XMLDocument *pDoc, const QString &strRootTag )
{
    Q_UNUSED(strRootTag);
    bool bRet = true;
    pDoc->beginElement( mapRootTag() );
    pDoc->beginElement( XML_FILE_NODE_DATA );
    qDebug()<<"PRPSDataMap::parseMapDataXML, mapRootTag():"<<mapRootTag();
    QByteArray baData = QByteArray::fromBase64(pDoc->value(XML_FILE_NODE_DATA).toLatin1());

    DataMapUtils::deleteDataBuf( (void**)&m_pPrpsData );
    unsigned long iBufSize = 0;
    m_pPrpsData = DataMapUtils::createDataBuf( m_stPRPSExt.iPowerFreCycleCount * m_stPRPSExt.iPhaseIntervalCount,//zyj
                                                 m_sMapHead.eDataPrimitiveType, iBufSize );
    //memcpy( m_pPrpsData, (double*)baData.data(), baData.count() );

    memcpy(m_pPrpsData, baData.data(), iBufSize);
    pDoc->endElement();
    pDoc->endElement();
    return bRet;
}
