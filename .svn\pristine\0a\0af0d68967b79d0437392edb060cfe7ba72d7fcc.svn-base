#pragma GCC diagnostic error "-std=c++11"
#include "Desktop.h"
#include <QVBoxLayout>
#include <QBitmap>
#include <QDebug>
#include <UHFHFCTAETEVApi.h>
#include "datadefine.h"
#include "Function.h"
#include "systemsettings.h"
#ifdef Q_OS_LINUX
#include <wifi.h>
#endif
#include "systemsetting/systemsetservice.h"
#include "statemonior/StateMonitor.h"
#include "activemanager/activemanager.h"
#include "window/Window.h"
#include "Widget.h"
#include "ae/AEView.h"
#include "ae/AEConfig.h"
#include "ae/aemainview.h"
#include "uhf/UHFView.h"
#include "uhf/UHFConfig.h"
#include "hfct/HFCTView.h"
#include "hfct/HFCTConfig.h"
#include "tev/tevconfig.h"
#include "ca/CAView.h"
#include "tev/tevview.h"
#include "infrared/infraredview.h"
#include "guideinfrared/guideinfraredview.h"
#include "systemsetview/systemsetwidget/systemsetview.h"
#include "peripheralmatch/pmview.h"
#include "appconfig.h"
#include "functionsconfig.h"
#include "peripheralmatch/pmservice.h"
#include "rfidview.h"
#include "PDAUi/PDAUiView/pdataskview.h"
#include "update/localcomm.h"
#include "systemsetview/settingview.h"
#include "environment/environmentinfo.h"
//#include "functionsconfig/functionsconfigview.h"
#include "gps/gpsview.h"
#include "qrcode/qrcodeview.h"
#include "recordplayview.h"
#include "mobileaccessservice.h"
#include "customaccessUi/connectview/connectview.h"
#include "customaccessUi/maintaskview.h"
#include "functionconfigmanager/funccfgsrvmanager.h"
#include "selfinspectionmanager/selfinspectionmanager.h"
#include "iniconfig/iniconfig.h"
#include "currentdetection/currentdetectionview.h"
#include "detectionmode/detectionmodeview.h"
#include "customaccessUi/taskmodeview/taskmodeview.h"
#include "pagecontrol/pagecontrolwidget.h"
#include "View.h"
#include "uhf/UHFViewConfig.h"
#include "distributenetaccess/distributenettaskview.h"
#include "videofilelist/videofilelist.h"
#include "distributenetaccess/distributetaskmanager/distributetaskmanager.h"


//局部宏定义
#define BACKGROUND_PNG          ":/images/main_desktop_1.png" //背景图片
#define STATUSBAR_HEIGHT        45
#define BUTTON_COUNT_PER_PAGE        12 // 主菜单每页按钮个数
#define DESKTOP_ICON_LESS_THAN_9

#ifdef DESKTOP_ICON_LESS_THAN_9
typedef enum _DesktopInfo
{
    FUNCTION_COLUMN_COUNT = 3,//功能按钮列数
    DATE_PIXEL = 30,//日期像素
    TIME_PIXEL = 60,//时间像素
    BUTTON_HEIGHT = 140,//按钮高度
    BUTTON_WIDTH = 120,//按钮宽度
}DesktopInfo;
#else
typedef enum _DesktopInfo
{
    FUNCTION_COLUMN_COUNT = 4,//功能按钮列数
    DATE_PIXEL = 30,//日期像素
    TIME_PIXEL = 70,//时间像素
    BUTTON_HEIGHT = 140,//按钮高度
    BUTTON_WIDTH = 110,//按钮宽度
}DesktopInfo;
#endif

// 北京融智通
typedef enum _BJRZTDesktopInfo
{
    BJRZT_FUNCTION_COLUMN_COUNT = 2,//功能按钮列数
    BJRZT_BUTTON_HEIGHT = 250,//按钮高度
    BJRZT_BUTTON_WIDTH = 190,//按钮宽度
    BJRZT_BUTTON_ICON_SIZE = 180,//按钮图标大小
    BJRZT_BUTTON_TITLE_WIDTH = 40,//按钮标题高度
}BJRZTDesktopInfo;


//功能按钮配置
const QString s_AEIconPath[] =
{
    ":/images/functionbutton/ae.png"
};

const QString s_TEVIconPath[] =
{
    ":/images/functionbutton/tev.png"
};

const QString s_UHFIconPath[] =
{
    ":/images/functionbutton/uhf.png"
};

const QString s_HFCTIconPath[] =
{
    ":/images/functionbutton/hfct.png"
};

const QString s_InfaredIconPath[] =
{
    ":/images/functionbutton/infrared.png"
};

const QString s_CAIconPath[] =
{
    ":/images/functionbutton/ca.png"
};

const QString s_CurrentDetectionIconPath[] =
{
    ":/images/functionbutton/current_detection.png"
};

const QString s_RFIDIconPath[] =
{
    ":/images/functionbutton/rfid.png"
};

const QString s_QRcodeIconPath[] =
{
    ":/images/functionbutton/qrcode.png"
};

const QString s_PatrolIconPath[] =
{
    ":/images/functionbutton/patrol.png"
};

const QString s_MatchingIconPath[] =
{
    ":/images/functionbutton/peripheral_matching.png"
};

const QString s_CustomAccessIconPath[] =
{
    ":/images/functionbutton/access.png"
};

const QString s_SettingIconPath[] =
{
    ":/images/functionbutton/setting.png"
};

const QString s_EnvironmentIconPath[] =
{
    ":/images/functionbutton/fine.png"
};

const QString s_GPSIconPath[] =
{
    ":/images/functionbutton/gps.png"
};

const QString s_AudioIconPath[] =
{
    ":/images/functionbutton/record_play.png"
};
const QString s_TaskModeIconPath[] =
{
    ":/images/functionbutton/taskmode.png"
};
const QString s_DetectionModeIconPath[] =
{
    ":/images/functionbutton/detectionmode.png"
};

const QString s_DemoVideoIconPath[] =
{
    ":/images/functionbutton/demo_video.png"
};


Desktop* Desktop::m_pInstance = NULL;
QMutex Desktop::m_qmt4Instance;

/************************************************
 * 功能     : 构造函数
 * 输入参数 : stFuncView: 功能视图信息; parent: 父控件
 * 输出参数 : NULL
 * 返回值   : NULL
 ************************************************/
Desktop::Desktop(SplashScreen *pScreen, QWidget *parent)
    : QWidget(parent)
    , m_pConnectApp(NULL),
      m_pInactivatedFunctionPanel(NULL),
      m_pstInactivatedFunctionConfig(NULL)
{
    setWindowFlags(Qt::FramelessWindowHint);//无边框

    setBackground(this, BACKGROUND_PNG);

    m_bCustomAccessEnable = false;
    m_bClickEnable = false;
    m_bActiveScreenShow = false;

    SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
    if(SystemSet::ACCESS_PROTO_BJRZT == eProtocol)
    {
        setBJRZTAppFunctionsConfig();
        //功能按钮
        m_pFunctionPanel = new FunctionButtonPanel("Desktop", &m_stfunctionConfig, false, 4); // 主菜单不需要滚动
        m_pFunctionPanel->setButtonSize(QSize(BJRZT_BUTTON_WIDTH, BJRZT_BUTTON_HEIGHT));
        m_pFunctionPanel->setTitleHeight(BJRZT_BUTTON_TITLE_WIDTH);
        m_pFunctionPanel->setIconSize(QSize(BJRZT_BUTTON_ICON_SIZE, BJRZT_BUTTON_ICON_SIZE));
    }
    else
    {
        setAppFunctionsConfig();
        //功能按钮
        m_pFunctionPanel = new FunctionButtonPanel("Desktop", &m_stfunctionConfig, false, BUTTON_COUNT_PER_PAGE); // 主菜单需要滚动
        m_pFunctionPanel->setButtonSize(QSize(BUTTON_WIDTH, BUTTON_HEIGHT));
    }

    PageControlWidget* pPageControlWidget = new PageControlWidget(this);
    pPageControlWidget->setPageScrollArea(m_pFunctionPanel);

    m_pSplash = pScreen;

    //日期时间
    m_pDateTimeWidget = new DateTimeWidget();
    m_pDateTimeWidget->setPixcel(DATE_PIXEL, TIME_PIXEL);
    setDateTimeFormat();

    //connect(m_pFunctionPanel, SIGNAL(sigButtonPressed(quint8,const FunctionButtonPanel::Config*)), this, SLOT(onButtonPressed(quint8, const FunctionButtonPanel::Config*)));
    //connect( StateMonitor::instance(), SIGNAL(sigNeedUpdateFirmware()), this, SLOT(onNeedUpdateFirmware()) );//需要进行固件更新提示
    //connect(FuncCfgSrvManager::instance(), SIGNAL(sigFuncCfgChanged()), this, SLOT(onFuncCfgChanged()) );//功能配置修改后重启提示

    QFrame* pFrame = new QFrame();
    pFrame->setFixedSize(Window::WIDTH, STATUSBAR_HEIGHT);
    pFrame->setWindowOpacity(1.0);

    QVBoxLayout *playout = new QVBoxLayout( this );
    playout->setMargin(0);
    playout->addWidget(pFrame);
    playout->addWidget(m_pDateTimeWidget);
    playout->addWidget(pPageControlWidget, Qt::AlignTop);
    setLayout(playout);

    resize(Window::WIDTH, Window::HEIGHT);

    restoreDefault();

    autoConnectConnectedConditioners();

    setAEChannel();

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );

    pConfig->setValue(SystemSet::USB_STATE_NET, APPConfig::KEY_USB_STATE_SWITCH);
    pConfig->setValue(SystemSet::HOT_CLOSED, APPConfig::KEY_HOT_SWITCH);
    pConfig->setValue(SystemSet::FOUR_G_CLOSED, APPConfig::KEY_FOUR_G_SWITCH);

    pConfig->endGroup();

    //设备自检
    connect(SelfInspectionManager::instance(), SIGNAL(sigSelfInspResult(SelfState)), this, SLOT(onSelfInspectFinished(SelfState)));
    SelfInspectionManager::instance()->startSelfInspection(false);//临时不调用自检，后续硬件黑屏问题解决后需修改为真正的自检调用

    // 巡检服务未初始化的提示
    connect(MobileAccessService::instance(), SIGNAL(sigPDAServiceUninit()), this, SLOT(onPDAServiceUninit()));
}

Desktop::~Desktop()
{
    disconnect(SystemSetService::instance(), SIGNAL(sigDeviceActived()), this, SLOT(onDeviceActived()));
    if(m_pstFunctionConfig)
    {
        delete [] m_pstFunctionConfig;
        m_pstFunctionConfig = NULL;
    }
}

/************************************************
 * 功能：处理界面显示事件
 * 输入参数：
 *      pEvent：显示事件
 * **************************************************/
void Desktop::showEvent(QShowEvent *pEvent)
{
    Q_UNUSED(pEvent);

    if(!m_bActiveScreenShow)
    {
        showStatusBar();
    }

    checkCalibrationTime();

    //接入终端启用时
    if(m_bCustomAccessEnable)
    {
        SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
        if(SystemSet::ACCESS_PROTO_SDLR == eProtocol
                || SystemSet::ACCESS_PROTO_SHAF == eProtocol
                || SystemSet::ACCESS_PROTO_XACY == eProtocol
                || SystemSet::ACCESS_PROTO_TJXJ == eProtocol
                 || SystemSet::ACCESS_PROTO_JSDKY == eProtocol)
        {
            ConfigInstance* pConfig = ConfigManager::instance()->config();
            if(pConfig)
            {
                pConfig->beginGroup(Module::GROUP_APP);
                pConfig->setDefaultValue(static_cast<int>(SystemSet::ACCESS_BLUETOOTH_MODE), APPConfig::KEY_CUSTOM_ACCESS_MODE);
                pConfig->endGroup();
            }
        }

        if(SystemSet::ACCESS_PROTO_ZJHY == eProtocol || SystemSet::ACCESS_PROTO_BJRZT == eProtocol)
        {
            SystemSet::CustomAccessMode eMode = SystemSetService::instance()->getCustomAccessMode();
            if(SystemSet::ACCESS_USB_MODE == eMode)
            {
                //本地USB直连模式，需要将设备的状态设置为可读可写模式
                SystemSetService::instance()->setUSBState(SystemSet::USB_STATE_STORGE, true);
                SystemSetService::instance()->setUSBMode(SystemSet::APP_USB_GADGET_MASS_STORAGE_RW);
            }
            else
            {
                //SystemSetService::instance()->setUSBState(SystemSet::USB_STATE_NET, true); //no need to reset
            }
        }
    }

    QTimer::singleShot(1000, this, SLOT(onEnableToClick()));        //释放可以响应点击事件
    QTimer::singleShot(500, this, SLOT(onCloseSplashScreen()));
    QTimer::singleShot(3000, this, SLOT(onCheckActiveInfo()));

    return;
}

/*************************************************
功能： 响应按钮按下动作
输入参数：
        event -- 按键事件
*************************************************************/
//void Desktop::keyPressEvent( QKeyEvent *event )
//{
//    switch( event->key() )
//    {
//    case View::KEY_S_DOWN://S+DOWN
//    {
//        SystemSetService::instance()->initLCD();
//        break;
//    }
//    default:
//        QWidget::keyPressEvent(event);
//        break;
//    }

//    return;
//}

/****************************
功能： 模块单例
*****************************/
Desktop* Desktop::instance(SplashScreen *pSplash)
{
    //加锁双重校验，线程安全
    if(!m_pInstance)
    {
        if(m_qmt4Instance.tryLock(5000))
        {
            if(!m_pInstance)
            {
                m_pInstance = new (std::nothrow) Desktop(pSplash);
            }

            m_qmt4Instance.unlock();
        }
    }

    return m_pInstance;
}

void Desktop::setDateTimeFormat()
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    SystemSet::DateFormat eDateFormat = (SystemSet::DateFormat)pConfig->value( APPConfig::KEY_DATE_FORMAT ).toInt();

    SystemSet::TimeFormat eTimeFormat = (SystemSet::TimeFormat) pConfig->value( APPConfig::KEY_TIME_FORMAT ).toInt();

    bool isDST = (bool) pConfig->value( APPConfig::KEY_USE_DST ).toInt();
    pConfig->endGroup();

    setDateFormat(eDateFormat);
    setTimeFormat(eTimeFormat);
    setIsDSTWork(isDST);
}

void Desktop::setDateFormat(SystemSet::DateFormat eNewFormat)
{
    m_pDateTimeWidget->setDateFormat(eNewFormat);
}

void Desktop::setTimeFormat(SystemSet::TimeFormat eNewFormat)
{
    m_pDateTimeWidget->setTimeFormat(eNewFormat);
}

void Desktop::setIsDSTWork(bool isDSTWork)
{
    m_pDateTimeWidget->setIsDSTWork(isDSTWork);
}

/************************************************
 * 功能     : 槽函数，需要进行固件更新的提示
 ************************************************/
void Desktop::onNeedUpdateFirmware()
{
    MsgBox::warning("", trUtf8("Error, charge the battery and upgrade firmware later."));
    return;
}

/*************************************************
功能： 从配置文件读取各功能选项开关值,赋给数组
*************************************************************/
void Desktop::setAppFunctionsConfig()
{
    QVector<FunctionButtonPanel::Config> qvtOpitionalFunctionConfigs;
    qvtOpitionalFunctionConfigs.clear();

    FuncConfigManagerNS::ConfigInfo stConfigInfo;
    FuncCfgSrvManager::instance()->getConfigInfo(stConfigInfo);
    const QVector<FuncConfigManagerNS::FunctionInfo>& qvtFuncInfos = stConfigInfo.qvtFuncInfos;

    for (int i = 0, iSize = qvtFuncInfos.size(); i < iSize; ++i)
    {
        if (!(qvtFuncInfos[i].bEnable))
        {
            continue;
        }

        Function::Code enumFunctionCode = View::functionID2Enum(static_cast<FuncConfigManagerNS::FunctionID>(qvtFuncInfos[i].iFuncID));
        switch (enumFunctionCode)
        {
        case Function::TEV:
        {
            FunctionButtonPanel::Config* pTevSubFuncCfg = NULL;
            quint8 qui8TevSubFuncCnt = 0;
            getTevSubFuncConfig(pTevSubFuncCfg, qui8TevSubFuncCnt);
            qvtOpitionalFunctionConfigs.push_back({Function::TEV, QT_TRANSLATE_NOOP("Desktop", "TEV"), s_TEVIconPath, sizeof(s_TEVIconPath) / sizeof(QString), pTevSubFuncCfg, qui8TevSubFuncCnt, 2});
            break;
        }
        case Function::AE:
        {
            FunctionButtonPanel::Config* pAESubFuncCfg = NULL;
            quint8 qui8AESubFuncCnt = 0;
            getAESubFuncConfig(pAESubFuncCfg, qui8AESubFuncCnt);
            qvtOpitionalFunctionConfigs.push_back({Function::AE, QT_TRANSLATE_NOOP("Desktop", "AE"), s_AEIconPath, sizeof(s_AEIconPath) / sizeof(QString) , pAESubFuncCfg, qui8AESubFuncCnt, 2});
            break;
        }
        case Function::UHF:
        {
            FunctionButtonPanel::Config* pUHFSubFuncCfg = NULL;
            quint8 qui8UHFSubFuncCnt = 0;
            getUHFSubFuncConfig(pUHFSubFuncCfg, qui8UHFSubFuncCnt);
            qvtOpitionalFunctionConfigs.push_back({Function::UHF, QT_TRANSLATE_NOOP("Desktop", "UHF"), s_UHFIconPath, sizeof(s_UHFIconPath) / sizeof(QString), pUHFSubFuncCfg, qui8UHFSubFuncCnt, 2});
            break;
        }
        case Function::HFCT:
        {
            FunctionButtonPanel::Config* pHFCTSubFuncCfg = NULL;
            quint8 qui8HFCTSubFuncCnt = 0;
            getHFCTSubFuncConfig(pHFCTSubFuncCfg, qui8HFCTSubFuncCnt);
            qvtOpitionalFunctionConfigs.push_back({Function::HFCT, QT_TRANSLATE_NOOP("Desktop", "HFCT"), s_HFCTIconPath, sizeof(s_HFCTIconPath) / sizeof(QString), pHFCTSubFuncCfg, qui8HFCTSubFuncCnt, 2});
            break;
        }
        case Function::CA:
        {
            FunctionButtonPanel::Config* pCASubFuncCfg = NULL;
            quint8 qui8CASubFuncCnt = 0;
            getCASubFuncConfig(pCASubFuncCfg, qui8CASubFuncCnt);
            qvtOpitionalFunctionConfigs.push_back({Function::CA, QT_TRANSLATE_NOOP("Desktop", "CA Diag."), s_CAIconPath, sizeof(s_CAIconPath) / sizeof(QString), pCASubFuncCfg, qui8CASubFuncCnt, 2});
            break;
        }
        case Function::INFRARED:
        {
            qvtOpitionalFunctionConfigs.push_back({Function::INFRARED, QT_TRANSLATE_NOOP("Desktop", "Infrared"), s_InfaredIconPath, sizeof(s_InfaredIconPath) / sizeof(QString), NULL, 0, 0});
            break;
        }
        case Function::CURRENT_DETECTION:
        {
            qvtOpitionalFunctionConfigs.push_back({Function::CURRENT_DETECTION, QT_TRANSLATE_NOOP("Desktop", "Current"), s_CurrentDetectionIconPath, sizeof(s_CurrentDetectionIconPath) / sizeof(QString), NULL, 0, 0});
            break;
        }
        case Function::PATROL:
        {
            qvtOpitionalFunctionConfigs.push_back({Function::PATROL, QT_TRANSLATE_NOOP("Desktop", "Patrol"), s_PatrolIconPath, sizeof(s_PatrolIconPath) / sizeof(QString), NULL, 0, 0});
            break;
        }
        case Function::CONNECT_TERMINAL_APP:
        {
            qvtOpitionalFunctionConfigs.push_back({Function::CONNECT_TERMINAL_APP, QT_TRANSLATE_NOOP("Desktop", "C-APP"), s_CustomAccessIconPath, sizeof(s_CustomAccessIconPath) / sizeof(QString), NULL, 0, 0});
            m_bCustomAccessEnable = true;
            break;
        }
        default:
            break;
        }
    }

    qvtOpitionalFunctionConfigs.push_back({Function::RFID, QT_TRANSLATE_NOOP("Desktop", "RFID"), s_RFIDIconPath, sizeof(s_RFIDIconPath) / sizeof(QString), g_RFIDFunctionConfig, sizeof(g_RFIDFunctionConfig) / sizeof(FunctionButtonPanel::Config), 2});

    SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
    if(SystemSet::ACCESS_PROTO_ZJHY == eProtocol)
    {
        qvtOpitionalFunctionConfigs.push_back({Function::DEMO_VIDEO, QT_TRANSLATE_NOOP("Desktop", "Demo Video"), s_DemoVideoIconPath, sizeof(s_DemoVideoIconPath) / sizeof(QString), NULL, 0, 0});
    }

    FunctionButtonPanel::Config* pPMSubFuncCfg = NULL;
    quint8 qui8PMSubFuncCnt = 0;
    getPMSubFuncConfig(pPMSubFuncCfg, qui8PMSubFuncCnt);
    qvtOpitionalFunctionConfigs.push_back({Function::PERIPHERAL_MATCHING, QT_TRANSLATE_NOOP("Desktop", "Pairing"), s_MatchingIconPath,sizeof(s_MatchingIconPath) / sizeof(QString), pPMSubFuncCfg, qui8PMSubFuncCnt, 2});

    qvtOpitionalFunctionConfigs.push_back({Function::AUDIO, QT_TRANSLATE_NOOP("Desktop", "Audio"), s_AudioIconPath, sizeof(s_AudioIconPath) / sizeof(QString), g_AudioFunctionConfig, sizeof(g_AudioFunctionConfig) / sizeof(FunctionButtonPanel::Config), 2});
    qvtOpitionalFunctionConfigs.push_back({Function::SETTING, QT_TRANSLATE_NOOP("Desktop", "Settings"), s_SettingIconPath, sizeof(s_SettingIconPath) / sizeof(QString) , g_SettingFunctionConfig, sizeof(g_SettingFunctionConfig) / sizeof(FunctionButtonPanel::Config), 2});

    //动态分配数组
    int iFuncCount = qvtOpitionalFunctionConfigs.size();
    logInfo(QString("function count: %1.").arg(iFuncCount));

    m_pstFunctionConfig = new FunctionButtonPanel::Config[iFuncCount];
    m_stfunctionConfig = {0, NULL, NULL, 0, m_pstFunctionConfig, static_cast<quint8>(iFuncCount), FUNCTION_COLUMN_COUNT};

    //给数组元素赋值
    for(int i = 0; i < iFuncCount; ++i)
    {
        m_pstFunctionConfig[i] = qvtOpitionalFunctionConfigs[i];
    }

    return;
}

/*************************************************
功能： 从配置文件读取各功能选项开关值,赋给数组（北京融智通）
*************************************************************/
void Desktop::setBJRZTAppFunctionsConfig()
{
    QVector<FunctionButtonPanel::Config> qvtOpitionalFunctionConfigs;

    qvtOpitionalFunctionConfigs.push_back({ Function::TASK_MODE, QT_TRANSLATE_NOOP("Desktop", "Task Mode"), s_TaskModeIconPath, sizeof(s_TaskModeIconPath) / sizeof(QString), NULL, 0, 0 });

    // 检测模式子功能
    QVector<FunctionButtonPanel::Config> qvtDetectionModeFunctionConfigs;

    // TEV
    FunctionButtonPanel::Config* pTEVSubFuncCfg = NULL;
    quint8 qui8TEVSubFuncCnt = 0;
    getTevSubFuncConfig(pTEVSubFuncCfg, qui8TEVSubFuncCnt);
    qvtDetectionModeFunctionConfigs.push_back({ DetectionModeView::TEV, QT_TRANSLATE_NOOP("DetectionModeView", "TEV"), s_TEVIconPath, sizeof(s_TEVIconPath) / sizeof(QString) , pTEVSubFuncCfg, qui8TEVSubFuncCnt, 2 });

    FunctionButtonPanel::Config* pAESubFuncCfg = NULL;
    quint8 qui8AESubFuncCnt = 0;
    getAESubFuncConfig(pAESubFuncCfg, qui8AESubFuncCnt);
    qvtDetectionModeFunctionConfigs.push_back({ DetectionModeView::AE, QT_TRANSLATE_NOOP("DetectionModeView", "AE"), s_AEIconPath, sizeof(s_AEIconPath) / sizeof(QString) , pAESubFuncCfg, qui8AESubFuncCnt, 2 });

    FunctionButtonPanel::Config* pUHFSubFuncCfg = NULL;
    quint8 qui8UHFSubFuncCnt = 0;
    getUHFSubFuncConfig(pUHFSubFuncCfg, qui8UHFSubFuncCnt);
    qvtDetectionModeFunctionConfigs.push_back({ DetectionModeView::UHF, QT_TRANSLATE_NOOP("DetectionModeView", "UHF"), s_UHFIconPath, sizeof(s_UHFIconPath) / sizeof(QString), pUHFSubFuncCfg, qui8UHFSubFuncCnt, 2 });

    FunctionButtonPanel::Config* pHFCTSubFuncCfg = NULL;
    quint8 qui8HFCTSubFuncCnt = 0;
    getHFCTSubFuncConfig(pHFCTSubFuncCfg, qui8HFCTSubFuncCnt);
    qvtDetectionModeFunctionConfigs.push_back({ DetectionModeView::HFCT, QT_TRANSLATE_NOOP("DetectionModeView", "HFCT"), s_HFCTIconPath, sizeof(s_HFCTIconPath) / sizeof(QString), pHFCTSubFuncCfg, qui8HFCTSubFuncCnt, 2 });

    qvtDetectionModeFunctionConfigs.push_back({ DetectionModeView::INFRARED, QT_TRANSLATE_NOOP("DetectionModeView", "Infrared"), s_InfaredIconPath, sizeof(s_InfaredIconPath) / sizeof(QString), NULL, 0, 0 });

    qvtDetectionModeFunctionConfigs.push_back({ DetectionModeView::CURRENT_DETECTION,   QT_TRANSLATE_NOOP("DetectionModeView", "Current Detection"), s_CurrentDetectionIconPath,sizeof(s_CurrentDetectionIconPath) / sizeof(QString), NULL, 0, 0 });

    //qvtDetectionModeFunctionConfigs.push_back({ DetectionModeView::AUDIO, QT_TRANSLATE_NOOP("DetectionModeView", "Audio"), s_AudioIconPath, sizeof(s_AudioIconPath) / sizeof(QString), g_AudioFunctionConfig, sizeof(g_AudioFunctionConfig) / sizeof(FunctionButtonPanel::Config), 2 });


    FunctionButtonPanel::Config* pDetectionModeSubFuncCfg = new FunctionButtonPanel::Config[qvtDetectionModeFunctionConfigs.size()];
    for (int i = 0; i < qvtDetectionModeFunctionConfigs.size(); ++i)
    {
        pDetectionModeSubFuncCfg[i] = qvtDetectionModeFunctionConfigs[i];
    }
    qvtOpitionalFunctionConfigs.push_back({ Function::DETECTION_MODE, QT_TRANSLATE_NOOP("Desktop", "Detection Mode"), s_DetectionModeIconPath,sizeof(s_DetectionModeIconPath) / sizeof(QString), pDetectionModeSubFuncCfg, qvtDetectionModeFunctionConfigs.size(), 2 });

    FunctionButtonPanel::Config* pPMSubFuncCfg = NULL;
    quint8 qui8PMSubFuncCnt = 0;
    getPMSubFuncConfig(pPMSubFuncCfg, qui8PMSubFuncCnt);
    qvtOpitionalFunctionConfigs.push_back({ Function::PERIPHERAL_MATCHING, QT_TRANSLATE_NOOP("Desktop", "Pairing"), s_MatchingIconPath,sizeof(s_MatchingIconPath) / sizeof(QString), pPMSubFuncCfg, qui8PMSubFuncCnt, 2 });
    qvtOpitionalFunctionConfigs.push_back({ Function::SETTING, QT_TRANSLATE_NOOP("Desktop", "Settings"), s_SettingIconPath, sizeof(s_SettingIconPath) / sizeof(QString) , g_SettingFunctionConfig, sizeof(g_SettingFunctionConfig) / sizeof(FunctionButtonPanel::Config), 2 });

    int iFuncCount = qvtOpitionalFunctionConfigs.size();
    m_pstFunctionConfig = new FunctionButtonPanel::Config[iFuncCount];
    m_stfunctionConfig = { 0, NULL, NULL, 0, m_pstFunctionConfig, static_cast<quint8>(iFuncCount), BJRZT_FUNCTION_COLUMN_COUNT };

    //给数组元素赋值
    for (int i = 0; i < iFuncCount; ++i)
    {
        m_pstFunctionConfig[i] = qvtOpitionalFunctionConfigs[i];
    }
}

/*************************************************
功能： 显示自检失败对话框
*************************************************************/
void Desktop::showSelfInspectionErrorDlg()
{
    MsgBox::warning("", trUtf8("Self-Check failed, please go to Self-Check page for details."));
    return;
}

/*************************************************
功能： 显示校准信息对话框
*************************************************************/
void Desktop::onShowCalibrationInfoDlg()
{
    CalibrateInfo stCalibInfo;
    if(IniConfig::readCalibrateInfo(stCalibInfo))
    {
        if(!(stCalibInfo.bNoRemind))
        {
            MsgBox* pBox = new MsgBox(MsgBox::WARNING, QPoint(Window::WIDTH / 2, Window::HEIGHT / 2));
            pBox->setInfo("", trUtf8("The calibration date has expired, please apply for calibration service."), (MsgBox::Buttons)(MsgBox::OK | MsgBox::CANCEL));
            pBox->button(MsgBox::CANCEL)->setText(QObject::tr("Don't remind"));
            //pBox->setDelayAcceptTime();
            if (MsgBox::CANCEL == pBox->exec())
            {
                //TODO: 保存到配置文件中
                stCalibInfo.bNoRemind = true;
                IniConfig::writeCalibrateInfo(stCalibInfo);
            }
        }
        else
        {
            logInfo("don't remind this tips.");
        }
    }

    return;
}

/************************************************
 * 函数名   : conditionerID2Addr(const QString& strID)
 * 输入参数 : strID---调理器ID
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 调理器ID转换为调理器地址
 ************************************************/
QByteArray Desktop::conditionerID2Addr(const QString& strID)
{
    QByteArray reverse = QByteArray::fromHex(strID.toLatin1());

    QByteArray byteAddr(reverse.size(), char(0));

    int len = reverse.size();
    for(int i = 0; i < len; ++i)
    {
        byteAddr[i] = reverse[len - 1 - i];
    }

    return byteAddr;
}

/************************************************
 * 函数名   : autoConnectMatchedDevice
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 自动连接上次已连接的调理器
 ************************************************/
void Desktop::autoConnectConnectedConditioners()
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    QString qsConnectedUHFConditioner = pConfig->value(APPConfig::KEY_UHF_CONDITIONER_ID);
    QString qsConnectedHFCTConditioner = pConfig->value(APPConfig::KEY_HFCT_CONDITIONER_ID);
    QString qsConnectedSynchronizerConditioner = pConfig->value(APPConfig::KEY_SYNCHRONIZER_CONDITIONER_ID);
    QString qsConnectedAEConditioner = pConfig->value(APPConfig::KEY_AE_CONDITIONER_ID);
    QString qsConnectedCurrentDetectionConditioner = pConfig->value(APPConfig::KEY_CURRENT_DETECTION_CONDITIONER_ID);
    pConfig->endGroup();

    qDebug()<<"qsConnectedUHFConditioner:"<<qsConnectedUHFConditioner;
    qDebug()<<"qsConnectedHFCTConditioner:"<<qsConnectedHFCTConditioner;
    qDebug()<<"qsConnectedSynchronizerConditioner:"<<qsConnectedSynchronizerConditioner;

    PMService *pPMService = new PMService;
    //uhf conditioner
    if(!qsConnectedUHFConditioner.isEmpty())
    {
        qDebug()<<"KEY_UHF_DEV_NAME,"<<qsConnectedUHFConditioner;
        WLDeviceAddr stMatchedUHFDevAddr = {{0}, Addr_idle};
        memset(stMatchedUHFDevAddr.caAddr, 0, ADDRBYTENUM * sizeof(UINT8));

        QByteArray UHFAddr = conditionerID2Addr(qsConnectedUHFConditioner);
        memcpy(stMatchedUHFDevAddr.caAddr, UHFAddr.data(), UHFAddr.size());
#ifdef Q_PROCESSOR_ARM
        pPMService->connectConditioner(&stMatchedUHFDevAddr, UHF_TERMINAL);
#endif
    }

    //hfct conditioner
    if(!qsConnectedHFCTConditioner.isEmpty())
    {
        qDebug()<<"KEY_HFCT_DEV_NAME,"<<qsConnectedHFCTConditioner;
        WLDeviceAddr stMatchedHFCTDevAddr = {{0}, Addr_idle};
        memset(stMatchedHFCTDevAddr.caAddr, 0, ADDRBYTENUM * sizeof(UINT8));

        QByteArray HFCTaddr = conditionerID2Addr(qsConnectedHFCTConditioner);
        memcpy(stMatchedHFCTDevAddr.caAddr, HFCTaddr.data(), HFCTaddr.size());
#ifdef Q_PROCESSOR_ARM
        pPMService->connectConditioner(&stMatchedHFCTDevAddr, HFCT_TERMINAL);
#endif
    }

    //external synchronizer
    if(!qsConnectedSynchronizerConditioner.isEmpty())
    {
        qDebug()<<"KEY_SYNCHRONIZER_NAME,"<<qsConnectedSynchronizerConditioner;
        WLDeviceAddr stMatchedSynchronizerDevAddr = {{0}, Addr_idle};
        memset(stMatchedSynchronizerDevAddr.caAddr, 0, ADDRBYTENUM * sizeof(UINT8));

        QByteArray SynchronizerAddr = conditionerID2Addr(qsConnectedSynchronizerConditioner);
        memcpy(stMatchedSynchronizerDevAddr.caAddr, SynchronizerAddr.data(), SynchronizerAddr.size());
#ifdef Q_PROCESSOR_ARM
        pPMService->connectConditioner(&stMatchedSynchronizerDevAddr, SYNCHRONIZER);
#endif
    }

    //AE conditioner
    if(!qsConnectedAEConditioner.isEmpty())
    {
        WLDeviceAddr stMatchedAEDevAddr = {{0}, Addr_idle};
        memset(stMatchedAEDevAddr.caAddr, 0, ADDRBYTENUM * sizeof(UINT8));

        QByteArray AEAddr = conditionerID2Addr(qsConnectedAEConditioner);
        memcpy(stMatchedAEDevAddr.caAddr, AEAddr.data(), AEAddr.size());
#ifdef Q_PROCESSOR_ARM
        pPMService->connectConditioner(&stMatchedAEDevAddr, AE_TERMINAL);
#endif
    }

    //电流检测 conditioner
    if (!qsConnectedCurrentDetectionConditioner.isEmpty())
    {
        qDebug() << "KEY_CURRENT_DETECTION_CONDITIONER_NAME," << qsConnectedCurrentDetectionConditioner;
        WLDeviceAddr stMatchedCurrentDetectionDevAddr = { {0}, Addr_idle };
        memset(stMatchedCurrentDetectionDevAddr.caAddr, 0, ADDRBYTENUM * sizeof(UINT8));

        QByteArray baCurrentDetectionAddr = conditionerID2Addr(qsConnectedCurrentDetectionConditioner);
        memcpy(stMatchedCurrentDetectionDevAddr.caAddr, baCurrentDetectionAddr.data(), baCurrentDetectionAddr.size());
#ifdef Q_PROCESSOR_ARM
        pPMService->connectConditioner(&stMatchedCurrentDetectionDevAddr, CABLE_CURRENT_TERMINAL);
#endif
    }
    APP_CHECK_FREE(pPMService);

    return;
}

/************************************************
 * 功能     : 部分参数上电需要恢复默认值
 ************************************************/
void Desktop::restoreDefault( void )
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
	
    /*pConfig->beginGroup(Module::GROUP_AE);  // 恢复AE模块下需上电恢复默认项

    QVector<Config::GroupKey> vecAEGroupKeys;

    vecAEGroupKeys << Config::GroupKey( AE::KEY_GAIN )
                   << Config::GroupKey( AE::KEY_SAMPLE_MODE ,AE::GROUP_AE_AMPLITUDE )
                   << Config::GroupKey( AE::KEY_TRIGGER_VALUE ,AE::GROUP_AE_PHASE )
                   << Config::GroupKey( AE::KEY_CLOSE_DOOR_TIME ,AE::GROUP_AE_PHASE )
                   << Config::GroupKey( AE::KEY_PHASE_SHIFT ,AE::GROUP_AE_PHASE )
                   << Config::GroupKey( AE::KEY_TRIGGER_VALUE ,AE::GROUP_AE_FLY )
                   << Config::GroupKey( AE::KEY_OPEN_DOOR_TIME ,AE::GROUP_AE_FLY )
                   << Config::GroupKey( AE::KEY_CLOSE_DOOR_TIME ,AE::GROUP_AE_FLY )
                   << Config::GroupKey( AE::KEY_TIME_INTERVAL ,AE::GROUP_AE_FLY )
                   << Config::GroupKey( AE::KEY_SAMPLE_MODE ,AE::GROUP_AE_WAVE )
                   << Config::GroupKey( AE::KEY_TRIGGER_VALUE ,AE::GROUP_AE_WAVE )
                   << Config::GroupKey( AE::KEY_SAMPLE_TIME ,AE::GROUP_AE_WAVE )
                   << Config::GroupKey( AE::KEY_AMPLITUDE_SCOPE ,AE::GROUP_AE_WAVE );

    pConfig->restoreDefault( vecAEGroupKeys );
    pConfig->endGroup();*/

    pConfig->beginGroup( Module::GROUP_TEV );  // 恢复TEV模块下需上电恢复默认项

    QVector<Config::GroupKey> vecTEVGroupKeys;

    vecTEVGroupKeys << Config::GroupKey( TEV::KEY_SAMPLE_MODE ,TEV::GROUP_TEV_AMP );
    vecTEVGroupKeys << Config::GroupKey( TEV::KEY_SAMPLE_MODE ,TEV::GROUP_TEV_PULSE );

    pConfig->restoreDefault( vecTEVGroupKeys );
    pConfig->endGroup();

    pConfig->beginGroup( Module::GROUP_UHF );  // 恢复UHF模块下需上电恢复默认项

    QVector<Config::GroupKey> vecUHFGroupKeys;

    vecUHFGroupKeys << Config::GroupKey( UHF::KEY_SAMPLE_MODE ,UHF::GROUP_UHF_AMPLITUDE )
                    << Config::GroupKey( UHF::KEY_SAMPLE_MODE ,UHF::GROUP_UHF_SPECTRUM )
                    << Config::GroupKey( UHF::KEY_VERTIVAL_SCALAR ,UHF::GROUP_UHF_SPECTRUM )
                    << Config::GroupKey( UHF::KEY_PHASEALIAS );

    pConfig->restoreDefault( vecUHFGroupKeys );
    pConfig->endGroup();

    pConfig->beginGroup( Module::GROUP_HFCT );  // 恢复HFCT模块下需上电恢复默认项

    QVector<Config::GroupKey> vecHFCTGroupKeys;

    vecHFCTGroupKeys << Config::GroupKey( HFCT::KEY_SAMPLE_MODE ,HFCT::GROUP_HFCT_AMPLITUDE )
                     << Config::GroupKey( HFCT::KEY_SAMPLE_MODE ,HFCT::GROUP_HFCT_SPECTRUM )
                     << Config::GroupKey( HFCT::KEY_GAIN )
                     << Config::GroupKey( HFCT::KEY_PHASEALIAS );

    pConfig->restoreDefault( vecHFCTGroupKeys );
    pConfig->endGroup();

    return;
}

/************************************************
 * 功能     : 槽，响应按钮点击事件
 * 输入参数 :
 *      ucID -- 按钮ID
 *      pConfig -- 对应配置
 ************************************************/
void Desktop::onButtonPressed( quint8 ucID, const FunctionButtonPanel::Config* pConfig )
{
    if(!m_bClickEnable)
    {
        //logWarning("desktop view is disable to click, waiting.");
        return;
    }

    switch(ucID)
    {
    case Function::AE://AE超声波
    {
        AEMainView* pView = new AEMainView();
        pView->resize(Window::WIDTH, Window::HEIGHT);
        pView->show();

//        AEViewPanel* pPanel = new AEViewPanel(pConfig, BACKGROUND_PNG);
//        pPanel->resize(Window::WIDTH, Window::HEIGHT);
//        pPanel->show();
        break;
    }
    case Function::UHF://UHF特高频
    {
        UHFViewPanel* pPanel = new UHFViewPanel(pConfig, BACKGROUND_PNG);
        pPanel->resize(Window::WIDTH, Window::HEIGHT);
        pPanel->show();
        break;
    }
    case Function::HFCT://HFCT高频电流
    {
        HFCTViewPanel* pPanel = new HFCTViewPanel(pConfig, BACKGROUND_PNG);
        pPanel->resize(Window::WIDTH, Window::HEIGHT);
        pPanel->show();
        break;
    }
    case Function::TEV://TEV暂态地
    {
        TEVPanelView* pPanel = new TEVPanelView(pConfig, BACKGROUND_PNG);
        pPanel->resize(Window::WIDTH, Window::HEIGHT);
        pPanel->show();
        break;
    }
    case Function::CA://CA电流分析
    {
        CAViewPanel* pPanel = new CAViewPanel(pConfig, BACKGROUND_PNG);
        pPanel->resize(Window::WIDTH, Window::HEIGHT);
        pPanel->show();
        break;
    }
    case Function::INFRARED://Infrared红外
    {
        Infrared::InfraredType eInfraredType =InfraredService::instance()->initInfraredType();
        QWidget* pView;
        if(Infrared::GUIDE == eInfraredType)
        {
            pView = new GuideInfraredView();
        }
        else
        {
            pView = new InfraredView();
        }
        pView->resize(Window::WIDTH, Window::HEIGHT);
        pView->show();
        break;
    }
    case Function::PERIPHERAL_MATCHING://外设匹配
    {
        PMViewPanel* pPanel = new PMViewPanel(pConfig, BACKGROUND_PNG);
        pPanel->resize(Window::WIDTH, Window::HEIGHT);
        pPanel->show();
        break;
    }
    case Function::SETTING://系统设置
    {
        SettingView* pView = new SettingView(pConfig, BACKGROUND_PNG);
        pView->setFixedSize( Window::WIDTH, Window::HEIGHT );
        connect(pView, SIGNAL(sigLanguageChanged()), this, SLOT(onRetranslateView()));
        pView->show();
    }
        break;
    case Function::RFID://RFID
    {
        RFIDViewPanel* pPanel = new RFIDViewPanel(pConfig, BACKGROUND_PNG);
        pPanel->resize(Window::WIDTH, Window::HEIGHT);
        pPanel->show();
    }
        break;
    case Function::PATROL://智能巡检
    {
        if(!(PDAService::instance()->getOpenState()))
        {
            PDATaskView* pView = new PDATaskView();
            pView->setFixedSize( Window::WIDTH, Window::HEIGHT );
            pView->show();
        }
        break;
    }
    case Function::CURRENT_DETECTION://电流检测
    {
        CurrentDetectionView* pCurrentDetectionView = new CurrentDetectionView();
        pCurrentDetectionView->resize(Window::WIDTH, Window::HEIGHT);
        pCurrentDetectionView->show();
        break;
    }
    case Function::AUDIO://音频
    {
        RecordPlayPanelView* pPanel = new RecordPlayPanelView(pConfig, BACKGROUND_PNG);
        pPanel->resize(Window::WIDTH, Window::HEIGHT);
        pPanel->show();
        break;
    }
    case Function::QRCODE://二维码
    {
        QrCodeView *pView= new QrCodeView(trUtf8("QR"));
        pView->setFixedSize(Window::WIDTH, Window::HEIGHT);
        pView->show();
        break;
    }
    case Function::ENVIRONMENT_INFO://环境信息
    {
        EnvironmentInfoView *pEnvironmentInfoView= new EnvironmentInfoView(trUtf8("Environment"));
        pEnvironmentInfoView->setAttribute(Qt::WA_DeleteOnClose);
        pEnvironmentInfoView->show();
        break;
    }
    case Function::GPS://GPS
    {
        GPSView* pView = new GPSView(trUtf8("GPS"));
        pView->setFixedSize(Window::WIDTH, Window::HEIGHT);
        pView->show();
        break;
    }
    case Function::CONNECT_TERMINAL_APP://接入终端
    {
        showCustommAccessView();
        break;
    }
    case Function::DETECTION_MODE:
    {
        DetectionModePanelView* pPanel = new DetectionModePanelView(pConfig, BACKGROUND_PNG);
        pPanel->resize(Window::WIDTH, Window::HEIGHT);
        pPanel->show();
        break;
    }
    case Function::TASK_MODE:
    {
        TaskModeView* pView = new TaskModeView();
        //pView->resize(Window::WIDTH, Window::HEIGHT);
        pView->show();
        break;
    }
    case Function::DEMO_VIDEO:
    {
        VideoFileList* pView = new VideoFileList(qApp->translate("Desktop", pConfig->pchTitle));
        pView->setAttribute(Qt::WA_DeleteOnClose);
        pView->resize(Window::WIDTH, Window::HEIGHT);
        pView->addVideoFiles(DistributeTaskManager::instance()->getVideoFolderPath());
        pView->show();
        break;
    }
    default:
    {
        logError("invalid app id.");
        break;
    }
    }

    return;
}

/****************************
函数名： onConnectStatusChanged
输入参数:isConnected -- 是否已连接状态
输出参数：NULL
返回值：NULL
功能： 槽函数，当连接断开时，弹出提示
*****************************/
void Desktop::onConnectStatusChanged(bool isConnected)
{
    if(!isConnected)
    {
        MsgBox::warning("", QObject::trUtf8("Disconnected!"));
    }

    return;
}

void Desktop::onRetranslateView()
{
    m_pFunctionPanel->retranslateUI("Desktop");
    StatusBar::instance()->retranslateUI();
    m_pDateTimeWidget->retranslateUI();
    return;
}

void Desktop::setAEChannel()
{
    FuncConfigManagerNS::FunctionInfo stAEWirelessInfo;
    stAEWirelessInfo.iFuncID = FuncConfigManagerNS::AE_WIRELESS;
    stAEWirelessInfo.iParentID = FuncConfigManagerNS::AE;
    int iAEChannelType = AE::AIR_SOUND;

    FuncConfigManagerNS::ConfigInfo stConfigInfo;
    FuncCfgSrvManager::instance()->getConfigInfo(stConfigInfo);
    int iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAEWirelessInfo);
    if(0 <= iIndex && iIndex < stConfigInfo.qvtFuncInfos.size())
    {
        if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
        {
            AE_ConnectState eState = AE_NotConnect;
#ifdef Q_PROCESSOR_ARM
            get_ae_connectstate(&eState);
#endif
            iAEChannelType = (AE_Connected == eState) ? AE::WIRELESS : AE::AIR_SOUND;
        }
        else
        {
            iAEChannelType = AE::AIR_SOUND;
        }
    }
    else
    {
        iAEChannelType = AE::AIR_SOUND;
    }

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_AE);
    pConfig->setValue(iAEChannelType, AE::KEY_CHANNEL_TYPE);
    pConfig->endGroup();

    return;
}

/****************************************************
 * 功能：自检结束槽函数
 * 输入参数：
 *      eState：自检结果
 * ******************************************************/
void Desktop::onSelfInspectFinished(SelfState eState)
{
    disconnect(SelfInspectionManager::instance(), SIGNAL(sigSelfInspResult(SelfState)), this, SLOT(onSelfInspectFinished(SelfState)));

    this->show();       //自检结束显示界面

    //自检失败显示对话框
    if(SelfState::SELF_FAIL == eState)
    {
        showSelfInspectionErrorDlg();
    }

    return;
}

/***********************************************
 * 功能：当功能配置修改后弹出重启提示
 * **********************************************/
void Desktop::onFuncCfgChanged()
{
    MsgBox::Button eReply = MsgBox::question("", trUtf8("Functional configuration has been modified, confirm to take effect after auto shutdown and restart manually?"));
    if(MsgBox::OK == eReply)
    {
        SystemSetService::instance()->shutDownSystem();
    }

    return;
}

/***********************************************
 * 功能：当已经到截止时间系统弹窗给出警告信息,系统断电
 * **********************************************/
void Desktop::onActiveTimeout()
{
    switchFunctionButtonPanel(false);
    //MsgBox::information_EX("", trUtf8("The activation date has expired."), 10000);
    MsgBox::information_EX("", trUtf8("Device is not in the activation date."), 10000);
    return;
}

/********************************************
 * 功能：响应splash界面关闭槽
 * *********************************************/
void Desktop::onCloseSplashScreen()
{
    connect(SystemSetService::instance(), SIGNAL(sigDeviceActived()), this, SLOT(onDeviceActived()));
    connect(m_pFunctionPanel, SIGNAL(sigButtonPressed(quint8,const FunctionButtonPanel::Config*)), this, SLOT(onButtonPressed(quint8, const FunctionButtonPanel::Config*)));
    connect(StateMonitor::instance(), SIGNAL(sigNeedUpdateFirmware()), this, SLOT(onNeedUpdateFirmware()));//需要进行固件更新提示
    connect(FuncCfgSrvManager::instance(), SIGNAL(sigFuncCfgChanged()), this, SLOT(onFuncCfgChanged()));//功能配置修改后重启提示
    connect(StateMonitor::instance(), SIGNAL(sigActiveTimeout()), this, SLOT(onActiveTimeout()));//租赁时间已到，设备断电

    if(m_pSplash && !(m_pSplash->isHidden()))
    {
        m_pSplash->close();
    }

    //bool bShow = (ACTIVE_OVERDUE == ActiveManager::instance()->getActiveStateByIniFile()) ? false : true;
    //showOrHideFuncBtn(bShow);
    if(ACTIVE_OVERDUE == ActiveManager::instance()->getActiveStateByIniFile())
    {
        switchFunctionButtonPanel(false);
    }

    QTimer::singleShot(1500, this, SIGNAL(sigSplashViewClosed()));//关闭激活页面

    return;
}

/********************************************
 * 功能：响应可以开始点击操作的槽
 * *********************************************/
void Desktop::onEnableToClick()
{
    m_bClickEnable = true;
    return;
}

/************************************************
 * 功能：校验校准日期
 * *************************************************/
void Desktop::checkCalibrationTime()
{
    //检查校准日期
    CalibrateInfo stCalibInfo;
    if (IniConfig::readCalibrateInfo(stCalibInfo))
    {
        QDate dtCalibrationDate = QDate::fromString(stCalibInfo.qstrCalibrationDate, CALIBRATION_DATE_FORMAT);
        dtCalibrationDate = dtCalibrationDate.addMonths(stCalibInfo.iIntervalMonths);

        if (dtCalibrationDate <= QDate::currentDate())
        {
            QTimer::singleShot(1000, this, SLOT(onShowCalibrationInfoDlg()));
        }
    }
    else
    {
        //QTimer::singleShot(1000, this, SLOT(onShowCalibrationInfoDlg()));
        logWarning("device calibration time is not exist.");
    }

    return;
}

void Desktop::setActiveScreenShow(bool bShow)
{
    m_bActiveScreenShow = bShow;
    return;
}

void Desktop::showStatusBar()
{
    //状态栏
    StatusBar::instance()->move(0, 0);
    StatusBar::instance()->show();
    return;
}

/************************************************
 * 功能：槽函数，响应设备被激活成功的操作
 * *********************************************/
void Desktop::onDeviceActived()
{
    switchFunctionButtonPanel(true);
    MsgBox::information("", trUtf8("The device has been activated successfully."));
    return;
}

/************************************************
 * 功能：槽函数，响应开始校验激活期限操作
 * *********************************************/
void Desktop::onCheckActiveInfo()
{
    StateMonitor::instance()->activeTimeout();
    StateMonitor::instance()->startActiveCheckTimer();
    return;
}

/************************************************
 * 功能：槽函数，响应巡检服务未初始化信号
 * *********************************************/
void Desktop::onPDAServiceUninit()
{
    MsgBox::information("", trUtf8("Please enter the patrol function to download tasks."));
}

/************************************************
 * 功能：切换功能按钮栏
 * 输入参数：
 *      bActivated：是否激活
 ************************************************/
void Desktop::switchFunctionButtonPanel(bool bActivated)
{
    if (!bActivated)
    {
        if (NULL == m_pInactivatedFunctionPanel)
        {
            QVector<FunctionButtonPanel::Config> qvtFunctionConfigs;
            qvtFunctionConfigs.push_back({ Function::SETTING, QT_TRANSLATE_NOOP("Desktop", "Settings"), s_SettingIconPath, sizeof(s_SettingIconPath) / sizeof(QString) , g_SettingFunctionConfig, sizeof(g_SettingFunctionConfig) / sizeof(FunctionButtonPanel::Config), 2 });

            int iFuncCount = qvtFunctionConfigs.size();
            m_pstInactivatedFunctionConfig = new FunctionButtonPanel::Config[iFuncCount];
            m_stInactivatedFunctionConfig = { 0, NULL, NULL, 0, m_pstInactivatedFunctionConfig, static_cast<quint8>(iFuncCount), BJRZT_FUNCTION_COLUMN_COUNT };

            // 给数组元素赋值
            for (int i = 0; i < iFuncCount; ++i)
            {
                m_pstInactivatedFunctionConfig[i] = qvtFunctionConfigs[i];
            }

            // 功能按钮
            m_pInactivatedFunctionPanel = new FunctionButtonPanel("Desktop", &m_stInactivatedFunctionConfig, false, 4);
            m_pInactivatedFunctionPanel->setButtonSize(QSize(BJRZT_BUTTON_WIDTH, BJRZT_BUTTON_HEIGHT));
            m_pInactivatedFunctionPanel->setTitleHeight(BJRZT_BUTTON_TITLE_WIDTH);
            m_pInactivatedFunctionPanel->setIconSize(QSize(BJRZT_BUTTON_ICON_SIZE, BJRZT_BUTTON_ICON_SIZE));
            m_pInactivatedFunctionPanel->setWindowFlags(Qt::FramelessWindowHint);

            connect(m_pInactivatedFunctionPanel, SIGNAL(sigButtonPressed(quint8,const FunctionButtonPanel::Config*)), this, SLOT(onButtonPressed(quint8, const FunctionButtonPanel::Config*)));
        }

        QPoint pos = m_pFunctionPanel->mapToGlobal(m_pFunctionPanel->pos());
        m_pInactivatedFunctionPanel->setGeometry(m_pFunctionPanel->geometry());
        m_pInactivatedFunctionPanel->move(pos);
        m_pInactivatedFunctionPanel->setVisible(true);
        m_pInactivatedFunctionPanel->raise();

        m_pFunctionPanel->setVisible(false);
    }
    else
    {
        if (m_pInactivatedFunctionPanel)
        {
            m_pInactivatedFunctionPanel->setVisible(false);
        }

        m_pFunctionPanel->setVisible(true);
    }
}

/************************************************
 * 功能：根据配置显示不同的接入模式界面
 ************************************************/
void Desktop::showCustommAccessView()
{
    SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
    if(SystemSet::ACCESS_PROTO_ZJHY == eProtocol)
    {
        DistributeNetTaskView* pView = new DistributeNetTaskView();
        pView->show();
    }
    else if(SystemSet::ACCESS_PROTO_BJRZT == eProtocol)
    {
        TaskModeView* pView = new TaskModeView();
        pView->show();
    }
    else
    {
        MainTaskView *pView = new MainTaskView();
        pView->show();
    }

    return;
}




