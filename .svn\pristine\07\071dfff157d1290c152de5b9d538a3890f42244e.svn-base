#include "TEVPulseDataMapSave.h"
#include "model/HCStatus.h"
#include "datadefine.h"
#include "appconfig.h"
#include "datafile/mapdatafactory.h"
#include "systemsetting/systemsetservice.h"

/************************************************
 * 函数名   : TEVPulseDataMapSave
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 构造函数
 ************************************************/
TEVPulseDataMapSave::TEVPulseDataMapSave()
{
    registerMaps();
    m_pTEVPulseDataInfo = NULL;
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString TEVPulseDataMapSave::saveData(void *pData)
{
    if(NULL == pData)
    {
        return "";
    }
    m_pTEVPulseDataInfo = (TEVPulseDataInfo*)pData;
    m_strAbsolutePath = getAbsolutePath(m_pTEVPulseDataInfo->stHeadInfo.generationDateTime);

    return saveData( pData, m_strAbsolutePath );
}

void TEVPulseDataMapSave::registerMaps()
{
    MapDataFactory::registerClass<TEVPulseDataMap>(XML_FILE_NODE_TEV_PULSE);//图谱根节点tag名
}

void TEVPulseDataMapSave::addTEVPulseMap(DataFile *pFile)
{
    TEVPulseDataMap *pMap = new TEVPulseDataMap;
    setMapHead(pMap);
    setMapInfo(pMap);
    setMapData(pMap);
    pFile->addMap(pMap);
}

void TEVPulseDataMapSave::setMapHead(DataMap *pMap)
{
    pMap->setCode(DataFileNS::SPECTRUM_CODE_TEV_PULSE);

    pMap->setGenerationTime(m_pTEVPulseDataInfo->stHeadInfo.generationDateTime);
    pMap->setSpectrumProperty(m_pTEVPulseDataInfo->stHeadInfo.eMapProperty);
    pMap->setDeviceName(m_pTEVPulseDataInfo->stHeadInfo.strDeviceName);
    pMap->setDeviceNumber(m_pTEVPulseDataInfo->stHeadInfo.strDeviceNumber);
    pMap->setTestPointName(m_pTEVPulseDataInfo->stHeadInfo.strTestPointName);
    pMap->setTestPointNumber(m_pTEVPulseDataInfo->stHeadInfo.strTestPointNumber);
    pMap->setTestChannelSign(m_pTEVPulseDataInfo->stHeadInfo.ucTestChannelSign + 1);//从1开始
    pMap->setDataPrimitiveType(m_pTEVPulseDataInfo->stHeadInfo.eDataPrimitiveType);
    pMap->setPDDefectLevel(m_pTEVPulseDataInfo->stHeadInfo.ePDDefectLevel);
    pMap->setPDSignalTypeInfos(m_pTEVPulseDataInfo->stHeadInfo.qstrPDSignalTypeInfos);
    pMap->setRemark(m_pTEVPulseDataInfo->stHeadInfo.qstrRemark);

    return;
}

void TEVPulseDataMapSave::setMapInfo(TEVPulseDataMap *pMap)
{
    //step4 set map informations
    TEVMapNS::TEVPulseMapInfo stInfo;
    stInfo.eAmpUnit = m_pTEVPulseDataInfo->eDataUnit;
    stInfo.fAmpLowerLimit = m_pTEVPulseDataInfo->fAmpMin;
    stInfo.fAmpUpperLimit =m_pTEVPulseDataInfo->fAmpMax;
    stInfo.fWarningValue = m_pTEVPulseDataInfo->fWarningValue;
    stInfo.fAlarmValue = m_pTEVPulseDataInfo->fAlarmingValue;
    stInfo.uiPulseTimeLength = m_pTEVPulseDataInfo->uiPulseTimeLength;

    pMap->setInfo(&stInfo);
}

void TEVPulseDataMapSave::setMapData(TEVPulseDataMap *pMap)
{
    TEVMapNS::TEVPulseData stData;
    stData.fTEVAmp = m_pTEVPulseDataInfo->fTEVAmp;
    stData.uiPulseCountPerPeriod = m_pTEVPulseDataInfo->uiPulseCountPerPeriod;
    stData.iPulseCount = m_pTEVPulseDataInfo->iPulseCount;
    stData.fSeverity = m_pTEVPulseDataInfo->fSeverity;

    pMap->setData(&stData);
}

/*************************************************
功能： 保存数据
返回：
    保存结果
*************************************************************/
QString TEVPulseDataMapSave::saveData(void *pData, const QString &qsSavedPath)
{
    if(!pData)
    {
        return "";
    }

    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return "";
    }

    m_pTEVPulseDataInfo = (TEVPulseDataInfo*) pData;
    // step1 new data file
    DataFile *pFile = new DataFile;

    // step2 set file heads info
    setFileHeads(pFile, m_pTEVPulseDataInfo->stHeadInfo);

    // step3 add map
    addTEVPulseMap(pFile);

    //step4 save data to file
    QString strDataFile;
    if(!(pFile->save(qsSavedPath, TEV_PULSE_FILE_NAME_SUFFIX, strDataFile)))
    {
        QFile file(strDataFile);
        file.remove();
        strDataFile = "";
    }
    delete pFile;
    pFile = NULL;
    logDebug(strDataFile);
    //添加文件备注信息
    RemarkInfoManager::instance()->saveRemarkInfo(strDataFile, m_pTEVPulseDataInfo->stHeadInfo.qstrRemark);
    return strDataFile;
}

/************************************************
 * 函数名   : getDataMap
 * 输入参数 : baData: xml格式数据流;
 * 输出参数 : pDatas: 数据
 * 返回值   : void
 * 功能     : 将xml格式数据提取到指定格式的结构体变量中
 ************************************************/
void TEVPulseDataMapSave::getDataMap(const QByteArray& baData, void *pData)
{
    Q_UNUSED(baData);
    Q_UNUSED(pData);
}

INT32 TEVPulseDataMapSave::getDataFromFile(const QString &qsFile, void* stData)
{
    DataFile *psDataFile = new DataFile;

    //step1 open data file
    bool isSuccess = psDataFile->open(qsFile);
    if(isSuccess == false)
    {
        delete psDataFile;
        return HC_FAILURE;
    }
    m_pTEVPulseDataInfo = (TEVPulseDataInfo*)stData;
    //获取文件头信息
    m_pTEVPulseDataInfo->stHeadInfo.strSubstationName = psDataFile->stationName();

    TEVPulseDataMap *pMap = dynamic_cast <TEVPulseDataMap*>(psDataFile->dataMap(DataFileNS::SPECTRUM_CODE_TEV_PULSE));
    if( NULL == pMap )
    {
        delete psDataFile;
        return HC_FAILURE;
    }
    pMap->getDataType( m_pTEVPulseDataInfo->stHeadInfo.eDataPrimitiveType );
    pMap->getDeviceName( m_pTEVPulseDataInfo->stHeadInfo.strDeviceName );
    pMap->getDeviceNumber( m_pTEVPulseDataInfo->stHeadInfo.strDeviceNumber );
    pMap->getTestPointName( m_pTEVPulseDataInfo->stHeadInfo.strTestPointName );
    pMap->getTestPointNumber( m_pTEVPulseDataInfo->stHeadInfo.strTestPointNumber );
    QString strDateTime;
    pMap->getMapGenerationTime( strDateTime );
    m_pTEVPulseDataInfo->stHeadInfo.generationDateTime = QDateTime::fromString( strDateTime, "yyyy-mm-dd hh:mm:ss" );
    pMap->getMapProperty( m_pTEVPulseDataInfo->stHeadInfo.eMapProperty );
    pMap->getTestChannelSign( m_pTEVPulseDataInfo->stHeadInfo.ucTestChannelSign );
    m_pTEVPulseDataInfo->stHeadInfo.ucTestChannelSign--;
    pMap->getPDDefectLevel(m_pTEVPulseDataInfo->stHeadInfo.ePDDefectLevel);
    pMap->getPDSignalTypeInfos(m_pTEVPulseDataInfo->stHeadInfo.qstrPDSignalTypeInfos);

    //设置图谱信息
    TEVMapNS::TEVPulseMapInfo stMapInfo;
    pMap->getInfo(&stMapInfo);
    m_pTEVPulseDataInfo->eDataUnit = stMapInfo.eAmpUnit;
    m_pTEVPulseDataInfo->fAmpMin = stMapInfo.fAmpLowerLimit;
    m_pTEVPulseDataInfo->fAmpMax = stMapInfo.fAmpUpperLimit;
    m_pTEVPulseDataInfo->fWarningValue = stMapInfo.fWarningValue;
    m_pTEVPulseDataInfo->fAlarmingValue = stMapInfo.fAlarmValue;

    //设置图谱数据
    TEVMapNS::TEVPulseData stMapData;
    pMap->getData(&stMapData);
    m_pTEVPulseDataInfo->fTEVAmp = stMapData.fTEVAmp;
    m_pTEVPulseDataInfo->uiPulseCountPerPeriod = stMapData.uiPulseCountPerPeriod;
    m_pTEVPulseDataInfo->iPulseCount = stMapData.iPulseCount;
    m_pTEVPulseDataInfo->fSeverity = stMapData.fSeverity;

    delete psDataFile;

    return HC_SUCCESS;
}

/************************************************
 * 函数名   : getDataByteArray
 * 输入参数 : void;
 * 输出参数 : NULL
 * 返回值   : xml格式数据流
 * 功能     : 获取xml格式数据流
 ************************************************/
QByteArray TEVPulseDataMapSave::getDataByteArray(void)
{
    return m_baData;
}

/************************************************
 * 函数名   : getStringFromData
 * 输入参数 : pDatas: 数据; uiCounts: 数据个数
 * 输出参数 : NULL
 * 返回值   : 转换后的字符串
 * 功能     : 将数据转成base64的字符串
 ************************************************/
QString TEVPulseDataMapSave::getStringFromData( void *pDatas, UINT32 uiCounts)
{
    Q_UNUSED(pDatas);
    Q_UNUSED(uiCounts);
    return NULL;
}

/************************************************
 * 函数名   : saveExtInformation
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储可扩展信息
 ************************************************/
void TEVPulseDataMapSave::saveExtInformation(XMLDocument& doc)
{
    Q_UNUSED(doc);
}

/************************************************
 * 函数名   : saveRawData
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储数据部分
 ************************************************/
void TEVPulseDataMapSave::saveRawData(XMLDocument& doc)
{
    Q_UNUSED(doc);
}

/************************************************
 * 函数名   : parseData
 * 输入参数 : baData: 数据
 * 输出参数 : pData: 解析到的数据
 * 返回值   : void
 * 功能     : 解析数据
 ************************************************/
void TEVPulseDataMapSave::parseData(const QByteArray& baData, void *pData, const QString &strFileName)
{
    Q_UNUSED(baData);
    Q_UNUSED(pData);
    Q_UNUSED(strFileName);
}

/************************************************
 * 函数名   : getDataTypeFolder
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件夹名
 * 功能     : 获取图谱类型文件存储最上层文件夹名
 ************************************************/
QString TEVPulseDataMapSave::getDataTypeFolder(void)
{
    return TEV_PULSE_FOLDER;
}

/************************************************
 * 函数名   : getFileNameSuffix
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件后缀名
 * 功能     : 获取数据文件后缀名
 ************************************************/
QString TEVPulseDataMapSave::getFileNameSuffix(void)
{
    return TEV_PULSE_FILE_NAME_SUFFIX;
}
