#include "vpnlineeditgroup.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QDebug>
#include "global_log.h"


// 标签样式
const QString LABEL_STYLE ="QLabel{color: rgb(0, 0, 0); font-size: 23px;}";
const int LABEL_WIDTH = 110;
const int LABEL_HEIGHT = 60;

/*************************************************
函数名： VpnLineEditGroup(QWidget *parent)
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
VpnLineEditGroup::VpnLineEditGroup(QWidget *parent)
    : QWidget(parent)
{
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setMargin(0);
    layout->setSpacing(20);
    setLayout(layout);        // 添加整体布局
}

/************************************************
 * 函数名   : addLineEditItem
 * 输入参数 : editNameIndex：标签名序号；lineEdit：输入框
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 往子窗口添加输入框
 ************************************************/
void VpnLineEditGroup::addLineEditItem(VpnLineEditGroup::NAME_INDEX editNameIndex, QLineEdit *lineEdit)
{
    QVBoxLayout *layout = (QVBoxLayout *)(this->layout());
    QString qsEditName = getEditName(editNameIndex);
    if(lineEdit && !(qsEditName.isEmpty()))
    {
        QLabel *pLabel = new QLabel(qsEditName + ":", this);
        pLabel->setFrameStyle(QFrame::Panel | QFrame::Raised);
        pLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        pLabel->setFixedSize(LABEL_WIDTH, LABEL_HEIGHT);
        lineEdit->setFixedHeight(LABEL_HEIGHT);
        pLabel->setStyleSheet(LABEL_STYLE);       // 设置凸起样式
        QHBoxLayout *hLayout = new QHBoxLayout;
        hLayout->addWidget(pLabel, 1);
        hLayout->addWidget(lineEdit, 3);
        layout->addLayout(hLayout);   // 完成布局
        m_vLineEditVector.append(lineEdit);
    }

    return;
}

/************************************************
 * 函数名   : getLineEdit
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 获得所有输入框的指针
 ************************************************/
QVector<QLineEdit *> VpnLineEditGroup::getLineEdit(void)
{
    return m_vLineEditVector;
}

/************************************************
 * 函数名   : getEditName
 * 功能     : 根据序号返回lineEdit的名字
 ************************************************/
QString VpnLineEditGroup::getEditName(VpnLineEditGroup::NAME_INDEX editNameIndex)
{
    QString editName = "";
    switch(editNameIndex)
    {
    case VPN_DOMIAN:
    {
        editName = QObject::trUtf8("Domain");
        break;
    }
    case VPN_USER:
    {
        editName = QObject::trUtf8("User");
        break;
    }
    case VPN_PWD:
    {
        editName = QObject::trUtf8("PWD");
        break;
    }
    default:
    {
        qWarning("editNameIndex wrong!");
        break;
    }
    }

    return editName;
}
