#include "AERecordDataSave.h"
#include "datadefine.h"
#ifdef Q_PROCESSOR_ARM
#include <unistd.h>
#endif
#include "datafile/datafile.h"
#include "datafile/ae/aephasedatamap.h"
#include "ae/AEConfig.h"
#include "datafile/mapdatafactory.h"
#include "model/HCStatus.h"
#include "systemsetting/systemsetservice.h"
#include "log/log.h"
/************************************************
 * 函数名   : AERecordDataSave
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 构造函数
 ************************************************/
AERecordDataSave::AERecordDataSave()
{
    m_pAERecordDataInfo = NULL;
    MapDataFactory::registerClass<AERecordDataMap>(XML_FILE_NODE_AE_RECORD);
}
/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString AERecordDataSave::saveData(void *pData)
{
    if(NULL == pData)
    {
        return NULL;
    }
    m_pAERecordDataInfo = (AERecordDataInfo*)pData;
    m_strAbsolutePath = getAbsolutePath(m_pAERecordDataInfo->stHeadInfo.generationDateTime);

    return saveData( pData, m_strAbsolutePath );
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString AERecordDataSave::saveData(void *pData, const QString &qsSavedPath)
{
    QString strSavePath("");
    if(NULL == pData)
    {
        return strSavePath;
    }

    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return strSavePath;
    }

    m_pAERecordDataInfo = (AERecordDataInfo*)pData;

    QDir dir(qsSavedPath);
    if(!dir.exists())
    {
        dir.mkpath(qsSavedPath);
    }

    DataFile *pFile = new DataFile;

    setFileHeads(pFile, m_pAERecordDataInfo->stHeadInfo );

    AERecordDataMap *pMap = new AERecordDataMap;
    setMapHead(pMap);
    setMapInfo(pMap);
    setMapData(pMap);
    pFile->addMap(pMap);

    if(!(pFile->save(qsSavedPath, AE_AUDIO_FILE_NAME_SUFFIX, strSavePath)))
    {
        QFile file(strSavePath);
        file.remove();
        strSavePath = "";
    }

    delete pFile;
    pFile = NULL;
    logDebug(strSavePath);

    return strSavePath;
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString AERecordDataSave::saveData(void *pData, const QString &qsSavedPath, const QString &qstrFileName)
{
    QString qstrFilePath = "";
    do
    {
        if(!pData)
        {
            break;
        }

		if(!(SystemSetService::instance()->storageOperEnable()))
	    {
	        logWarning("unable to operate storage space");
	        return qstrFilePath;
	    }
		
        m_pAERecordDataInfo = (AERecordDataInfo*)pData;

        QDir dir(qsSavedPath);
        if(!dir.exists())
        {
            dir.mkpath(qsSavedPath);
        }

        DataFile *pFile = new DataFile;

        setFileHeads(pFile, m_pAERecordDataInfo->stHeadInfo );

        AERecordDataMap *pMap = new AERecordDataMap;
        setMapHead(pMap);
        setMapInfo(pMap);
        setMapData(pMap);
        pFile->addMap(pMap);

        const QString qstrFilePathTemp = qsSavedPath + "/" + qstrFileName + AE_AUDIO_FILE_NAME_SUFFIX;
        if(pFile->save(qsSavedPath, AE_AUDIO_FILE_NAME_SUFFIX, qstrFilePathTemp))
        {
            qstrFilePath = qstrFilePathTemp;
        }
        else
        {
            QFile file(qstrFilePathTemp);
            file.remove();
        }

        delete pFile;
        pFile = NULL;

    }while(0);

    return qstrFilePath;
}

void AERecordDataSave::setMapHead(DataMap *pMap)
{
    pMap->setCode(DataFileNS::SPECTRUM_CODE_RECORD_AE);
    pMap->setGenerationTime(m_pAERecordDataInfo->stHeadInfo.generationDateTime);
    pMap->setSpectrumProperty(m_pAERecordDataInfo->stHeadInfo.eMapProperty);
    pMap->setDeviceName(m_pAERecordDataInfo->stHeadInfo.strDeviceName);
    pMap->setDeviceNumber(m_pAERecordDataInfo->stHeadInfo.strDeviceNumber);
    pMap->setTestPointName(m_pAERecordDataInfo->stHeadInfo.strTestPointName);
    pMap->setTestLocation(m_pAERecordDataInfo->stHeadInfo.eTestLocation);
    pMap->setTestPointNumber(m_pAERecordDataInfo->stHeadInfo.strTestPointNumber);
    pMap->setTestChannelSign(m_pAERecordDataInfo->stHeadInfo.ucTestChannelSign);
    pMap->setDataPrimitiveType(m_pAERecordDataInfo->stHeadInfo.eDataPrimitiveType);
}

void AERecordDataSave::setMapInfo(AERecordDataMap *pMap)
{
    //step4 set map informations
    AEMapNS::AERecordMapInfo stInfo;

    stInfo.uiDuration = m_pAERecordDataInfo->uiDuration;
    stInfo.eAmpUnit = m_pAERecordDataInfo->eAmpUnit;
    stInfo.eGainType = m_pAERecordDataInfo->eGainType;
    stInfo.sGain = m_pAERecordDataInfo->sGain;
    stInfo.eSyncSource = m_pAERecordDataInfo->eSyncSource;
    stInfo.fSyncFreq = m_pAERecordDataInfo->fSyncFreq;
    stInfo.eTransformerType = m_pAERecordDataInfo->eTransformerType;
    stInfo.uiPhaseShutTime = m_pAERecordDataInfo->uiPhaseShutTime;
    stInfo.fPhaseTriggerThreshold = m_pAERecordDataInfo->fPhaseTriggerThreshold;
    stInfo.uiPulseOpenTime = m_pAERecordDataInfo->uiPulseOpenTime;
    stInfo.uiPulseShutTime = m_pAERecordDataInfo->uiPulseShutTime;
    stInfo.fPulseTriggerThreshold = m_pAERecordDataInfo->fPulseTriggerThreshold;
    stInfo.uiSamplePeriod = m_pAERecordDataInfo->uiSamplePeriod;

    pMap->setInfo(&stInfo);
}

void AERecordDataSave::setMapData(AERecordDataMap *pMap)
{
    AEMapNS::AERecordData pData;
    pData.strFilePath = m_pAERecordDataInfo->strFilePath;
    pMap->setData(&pData,8);
}

void AERecordDataSave::addAERecordMap(DataFile *pFile)
{
    AERecordDataMap *pMap = new AERecordDataMap;
    setMapHead(pMap);
    setMapInfo(pMap);
    setMapData(pMap);
    pFile->addMap(pMap);
}
#if 0
/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString AERecordDataSave::saveData(void *pData, const QString &qsSavedPath)
{

    if(NULL == pData)
    {
        Module::logger() << "AERecordDataSave::saveData: << NULL data input!";
        return NULL;
    }
    m_pAERecordDataInfo = (AERecordDataInfo*)pData;

    QDir dir(qsSavedPath);
    if(!dir.exists())
    {
        dir.mkpath(qsSavedPath);
    }

    const QString& strFileName = getFileName(m_pAERecordDataInfo->dateTime);
    m_strAbsolutePath = qsSavedPath + "/" + strFileName;

    XMLDocument doc("AERecordlitude");

    organizeData(doc);

    bool isSuccess = saveToDataFile(AE_Record_WITH_MAP, doc.getByteArray());
    if(isSuccess == false)
    {
        m_strAbsolutePath.clear();
    }

}
#endif
/************************************************
 * 函数名   : getStringFromData
 * 输入参数 : pDatas: 数据; uiCounts: 数据个数
 * 输出参数 : NULL
 * 返回值   : 转换后的字符串
 * 功能     : 将数据转成base64的字符串
 ************************************************/
QString AERecordDataSave::getStringFromData( void *pDatas, UINT32 uiCounts)
{
    Q_UNUSED(pDatas);
    Q_UNUSED(uiCounts);
    return NULL;
}

/************************************************
 * 函数名   : saveExtInformation
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储可扩展信息
 ************************************************/
void AERecordDataSave::saveExtInformation(XMLDocument& doc)
{
    Q_UNUSED(doc);
}

/************************************************
 * 函数名   : saveRawData
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储数据部分
 ************************************************/
void AERecordDataSave::saveRawData(XMLDocument& doc)
{
    Q_UNUSED(doc);
}

/************************************************
 * 函数名   : organizeData
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 组织数据文件信息
 ************************************************/
void AERecordDataSave::organizeData(XMLDocument& doc)
{
    Q_UNUSED(doc);
}

/************************************************
  * 函数名   : parseData
  * 输入参数 : baData: 数据
  * 输出参数 : pData: 解析到的数据
  * 返回值   : void
  * 功能     : 解析数据
  ************************************************/
void AERecordDataSave::parseData(const QByteArray& baData, void *pData, const QString &strFileName)
{
    Q_UNUSED(baData);
    Q_UNUSED(pData);
    Q_UNUSED(strFileName);
}

/************************************************
 * 函数名   : getDataTypeFolder
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件夹名
 * 功能     : 获取图谱类型文件存储最上层文件夹名
 ************************************************/
QString AERecordDataSave::getDataTypeFolder(void)
{
    return AE_RECORD_FOLDER;
}

/************************************************
 * 函数名   : getDataFromFile
 * 输入参数 : strFileName: 文件名; pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 获取结果
 * 功能     : 获取指定数据文件中的数据
 ************************************************/
INT32 AERecordDataSave::getDataFromFile(const QString& strFileName, void *pData)
{
    DataFile *psDataFile = new DataFile;

    //step1 open data file
    bool isSuccess = psDataFile->open(strFileName);
    if(isSuccess == false)
    {
        delete psDataFile;
        return HC_FAILURE;
    }

    m_pAERecordDataInfo = (AERecordDataInfo*)pData;
    //step2 get displayed file head
    m_pAERecordDataInfo->stHeadInfo.strSubstationName = psDataFile->stationName();

    AERecordDataMap *pMap = dynamic_cast <AERecordDataMap*>(psDataFile->dataMap(DataFileNS::SPECTRUM_CODE_RECORD_AE));
    if( NULL == pMap )
    {
        delete psDataFile;
        return HC_FAILURE;
    }
    //step3 get displayed map head
    pMap->getDataType( m_pAERecordDataInfo->stHeadInfo.eDataPrimitiveType );
    pMap->getDeviceName( m_pAERecordDataInfo->stHeadInfo.strDeviceName );
    pMap->getDeviceNumber( m_pAERecordDataInfo->stHeadInfo.strDeviceNumber );
    pMap->getTestPointName( m_pAERecordDataInfo->stHeadInfo.strTestPointName );
    pMap->getTestPointNumber( m_pAERecordDataInfo->stHeadInfo.strTestPointNumber );
    QString strDateTime;
    pMap->getMapGenerationTime( strDateTime );
    m_pAERecordDataInfo->stHeadInfo.generationDateTime = QDateTime::fromString( strDateTime, "yyyy-mm-dd hh:mm:ss" );
    pMap->getMapProperty( m_pAERecordDataInfo->stHeadInfo.eMapProperty );
    pMap->getTestChannelSign( m_pAERecordDataInfo->stHeadInfo.ucTestChannelSign );
    //    m_pAERecordDataInfo->stHeadInfo.ucTestChannelSign--;

    //step4 get displayed map information
    AEMapNS::AERecordMapInfo stMapInfo;
    pMap->getInfo(&stMapInfo);
    m_pAERecordDataInfo->uiDuration = stMapInfo.uiDuration;
    m_pAERecordDataInfo->eAmpUnit = stMapInfo.eAmpUnit;
    m_pAERecordDataInfo->eGainType = stMapInfo.eGainType;
    m_pAERecordDataInfo->sGain = stMapInfo.sGain;
    m_pAERecordDataInfo->eSyncSource = stMapInfo.eSyncSource;
    m_pAERecordDataInfo->fSyncFreq = stMapInfo.fSyncFreq;
    m_pAERecordDataInfo->eTransformerType = stMapInfo.eTransformerType;
    m_pAERecordDataInfo->uiPhaseShutTime = stMapInfo.uiPhaseShutTime;
    m_pAERecordDataInfo->fPhaseTriggerThreshold = stMapInfo.fPhaseTriggerThreshold;
    m_pAERecordDataInfo->uiPulseOpenTime = stMapInfo.uiPulseOpenTime;
    m_pAERecordDataInfo->uiPulseShutTime = stMapInfo.uiPulseShutTime;
    m_pAERecordDataInfo->fPulseTriggerThreshold = stMapInfo.fPulseTriggerThreshold;
    m_pAERecordDataInfo->uiSamplePeriod = stMapInfo.uiSamplePeriod;

    AEMapNS::AERecordData  m_pAeData;
    pMap->getData(&m_pAeData);

    m_pAERecordDataInfo->strFilePath = m_pAeData.strFilePath;

    delete psDataFile;
    return HC_SUCCESS;
}
/************************************************
 * 函数名   : getFileNameSuffix
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件后缀名
 * 功能     : 获取数据文件后缀名
 ************************************************/
QString AERecordDataSave::getFileNameSuffix(void)
{
    return AE_AUDIO_FILE_NAME_SUFFIX;
}



