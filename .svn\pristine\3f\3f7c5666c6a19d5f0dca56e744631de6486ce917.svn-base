/*
 * Copyright (c) 2019.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：prpsprpdspectrum.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/11/08
 * 摘要：prpsprpd图谱数据定义
 * 当前版本：1.0
 */

#pragma once

#include <QString>
#include <vector>
#include "dataspecification_def.h"

namespace DataSpecificationNS
{
    struct PRPSExtInformation
    {
        AmpUnit eAmpUnit; // 幅值单位
        float fAmpLowerLimit; // 幅值下限
        float fAmpUpperLimit; // 幅值上限
        FrequencyBand eFrequencyBand; // 频带
        float fFrequencyLowerLimit; // 下限频率
        float fFequencyUpperLimit; // 上限频率
        qint32 iPhaseWindowCount; // 相位窗数m
        qint32 iQuantizedAmplitude; // 量化幅值n
        qint32 iPowerFreqCycleCount; // 工频周期数p
        quint8 aucPDTypeProbability[8]; // 放电类型概率
        QString qstrBGFileName; // 背景文件名称
        DataJudgmentFlag eDataJudgmentFlag; // 数据判断标志
        qint16 sGain; // 增益
        SyncSource eSyncSource; // 同步源
        quint8 ucSyncState; // 同步状态,失败：0x00 成功：0x01
        float fSyncFrequency; // 同步频率，如果没有同步频率，则存-1
        DischargeSeverity eDischargeSeverity; // 报警等级
        quint8 ucDenoisingThreshold; // 去噪阈值

        PRPSExtInformation()
        {
            eAmpUnit = AMP_UNIT_DEFAULT;
            fAmpLowerLimit = 0;
            fAmpUpperLimit = 0;
            eFrequencyBand = BAND_UNRECORD;
            fFrequencyLowerLimit = 0;
            fFequencyUpperLimit = 0;
            iPhaseWindowCount = 60;
            iQuantizedAmplitude = 50;
            iPowerFreqCycleCount = 50;
            memset(aucPDTypeProbability, 0, sizeof(aucPDTypeProbability));
            eDataJudgmentFlag = DATA_DEFAULT;
            sGain = 60;
            eSyncSource = SYNC_SOURCE_DEFAULT;
            ucSyncState = 0;
            fSyncFrequency = -1;
            eDischargeSeverity = DISCHARGE_SEVERITY_LEVEL_0;
            ucDenoisingThreshold = 0;
        }

        PRPSExtInformation& operator=(const PRPSExtInformation& stPRPSExtInformation)
        {
            this->eAmpUnit = stPRPSExtInformation.eAmpUnit;
            this->fAmpLowerLimit = stPRPSExtInformation.fAmpLowerLimit;
            this->fAmpUpperLimit = stPRPSExtInformation.fAmpUpperLimit;
            this->eFrequencyBand = stPRPSExtInformation.eFrequencyBand;
            this->fFrequencyLowerLimit = stPRPSExtInformation.fFrequencyLowerLimit;
            this->fFequencyUpperLimit = stPRPSExtInformation.fFequencyUpperLimit;
            this->iPhaseWindowCount = stPRPSExtInformation.iPhaseWindowCount;
            this->iQuantizedAmplitude = stPRPSExtInformation.iQuantizedAmplitude;
            this->iPowerFreqCycleCount = stPRPSExtInformation.iPowerFreqCycleCount;
            memcpy(this->aucPDTypeProbability, stPRPSExtInformation.aucPDTypeProbability, sizeof(stPRPSExtInformation.aucPDTypeProbability));
            this->qstrBGFileName = stPRPSExtInformation.qstrBGFileName;
            this->eDataJudgmentFlag = stPRPSExtInformation.eDataJudgmentFlag;
            this->sGain = stPRPSExtInformation.sGain;
            this->eSyncSource = stPRPSExtInformation.eSyncSource;
            this->ucSyncState = stPRPSExtInformation.ucSyncState;
            this->fSyncFrequency = stPRPSExtInformation.fSyncFrequency;
            this->eDischargeSeverity = stPRPSExtInformation.eDischargeSeverity;
            this->ucDenoisingThreshold = stPRPSExtInformation.ucDenoisingThreshold;
            return *this;
        }

        bool operator==(const PRPSExtInformation& stPRPSExtInformation) const
        {
            if (!(this->eAmpUnit == stPRPSExtInformation.eAmpUnit)) return false;
            if (!qFuzzyCompare(this->fAmpLowerLimit, stPRPSExtInformation.fAmpLowerLimit)) return false;
            if (!qFuzzyCompare(this->fAmpUpperLimit, stPRPSExtInformation.fAmpUpperLimit)) return false;
            if (!(this->eFrequencyBand == stPRPSExtInformation.eFrequencyBand)) return false;
            if (!qFuzzyCompare(this->fFrequencyLowerLimit, stPRPSExtInformation.fFrequencyLowerLimit)) return false;
            if (!qFuzzyCompare(this->fFequencyUpperLimit, stPRPSExtInformation.fFequencyUpperLimit)) return false;
            if (!(this->iPhaseWindowCount == stPRPSExtInformation.iPhaseWindowCount)) return false;
            if (!(this->iQuantizedAmplitude == stPRPSExtInformation.iQuantizedAmplitude)) return false;
            if (!(this->iPowerFreqCycleCount == stPRPSExtInformation.iPowerFreqCycleCount)) return false;
            if (!(this->aucPDTypeProbability == stPRPSExtInformation.aucPDTypeProbability)) return false;
            if (!(this->qstrBGFileName == stPRPSExtInformation.qstrBGFileName)) return false;
            if (!(this->eDataJudgmentFlag == stPRPSExtInformation.eDataJudgmentFlag)) return false;
            if (!(this->sGain == stPRPSExtInformation.sGain)) return false;
            if (!(this->eSyncSource == stPRPSExtInformation.eSyncSource)) return false;
            if (!(this->ucSyncState == stPRPSExtInformation.ucSyncState)) return false;
            if (!qFuzzyCompare(this->fSyncFrequency, stPRPSExtInformation.fSyncFrequency)) return false;
            if (!(this->eDischargeSeverity == stPRPSExtInformation.eDischargeSeverity)) return false;
            if (!(this->ucDenoisingThreshold == stPRPSExtInformation.ucDenoisingThreshold)) return false;
            return true;
        }
    };

    struct PRPSData
    {
        QByteArray qbaPDSpectrumData;

        PRPSData()
        {

        }

        PRPSData& operator=(const PRPSData& stPRPSData)
        {
            this->qbaPDSpectrumData = stPRPSData.qbaPDSpectrumData;
            return *this;
        }

        bool operator==(const PRPSData& stPRPSData) const
        {
            if (!(this->qbaPDSpectrumData == stPRPSData.qbaPDSpectrumData)) return false;
            return true;
        }
    };


    struct PRPDExtInformation
    {
        AmpUnit eAmpUnit; // 幅值单位
        float fAmpLowerLimit; // 幅值下限
        float fAmpUpperLimit; // 幅值上限
        FrequencyBand eFrequencyBand; // 频带
        float fFrequencyLowerLimit; // 下限频率
        float fFequencyUpperLimit; // 上限频率
        qint32 iPhaseWindowCount; // 相位窗数m
        qint32 iQuantizedAmplitude; // 量化幅值n
        qint32 iPowerFreqCycleCount; // 工频周期数p
        quint8 aucPDTypeProbability[8]; // 放电类型概率
        QString qstrBGFileName; // 背景文件名称
        DataJudgmentFlag eDataJudgmentFlag; // 数据判断标志
        qint16 sGain; // 增益
        SyncSource eSyncSource; // 同步源
        quint8 ucSyncState; // 同步状态,失败：0x00 成功：0x01
        float fSyncFrequency; // 同步频率，如果没有同步频率，则存-1
        DischargeSeverity eDischargeSeverity; // 报警等级
        quint8 ucDenoisingThreshold; // 去噪阈值

        PRPDExtInformation()
        {
            eAmpUnit = AMP_UNIT_DEFAULT;
            fAmpLowerLimit = 0;
            fAmpUpperLimit = 0;
            eFrequencyBand = BAND_UNRECORD;
            fFrequencyLowerLimit = 0;
            fFequencyUpperLimit = 0;
            iPhaseWindowCount = 60;
            iQuantizedAmplitude = 50;
            iPowerFreqCycleCount = 50;
            memset(aucPDTypeProbability, 0, sizeof(aucPDTypeProbability));
            eDataJudgmentFlag = DATA_DEFAULT;
            sGain = 60;
            eSyncSource = SYNC_SOURCE_DEFAULT;
            ucSyncState = 0;
            fSyncFrequency = -1;
            eDischargeSeverity = DISCHARGE_SEVERITY_LEVEL_0;
            ucDenoisingThreshold = 0;
        }

        PRPDExtInformation& operator=(const PRPDExtInformation& stPRPDExtInformation)
        {
            this->eAmpUnit = stPRPDExtInformation.eAmpUnit;
            this->fAmpLowerLimit = stPRPDExtInformation.fAmpLowerLimit;
            this->fAmpUpperLimit = stPRPDExtInformation.fAmpUpperLimit;
            this->eFrequencyBand = stPRPDExtInformation.eFrequencyBand;
            this->fFrequencyLowerLimit = stPRPDExtInformation.fFrequencyLowerLimit;
            this->fFequencyUpperLimit = stPRPDExtInformation.fFequencyUpperLimit;
            this->iPhaseWindowCount = stPRPDExtInformation.iPhaseWindowCount;
            this->iQuantizedAmplitude = stPRPDExtInformation.iQuantizedAmplitude;
            this->iPowerFreqCycleCount = stPRPDExtInformation.iPowerFreqCycleCount;
            memcpy(this->aucPDTypeProbability, stPRPDExtInformation.aucPDTypeProbability, sizeof(stPRPDExtInformation.aucPDTypeProbability));
            this->qstrBGFileName = stPRPDExtInformation.qstrBGFileName;
            this->eDataJudgmentFlag = stPRPDExtInformation.eDataJudgmentFlag;
            this->sGain = stPRPDExtInformation.sGain;
            this->eSyncSource = stPRPDExtInformation.eSyncSource;
            this->ucSyncState = stPRPDExtInformation.ucSyncState;
            this->fSyncFrequency = stPRPDExtInformation.fSyncFrequency;
            this->eDischargeSeverity = stPRPDExtInformation.eDischargeSeverity;
            this->ucDenoisingThreshold = stPRPDExtInformation.ucDenoisingThreshold;
            return *this;
        }

        bool operator==(const PRPDExtInformation& stPRPDExtInformation) const
        {
            if (!(this->eAmpUnit == stPRPDExtInformation.eAmpUnit)) return false;
            if (!qFuzzyCompare(this->fAmpLowerLimit, stPRPDExtInformation.fAmpLowerLimit)) return false;
            if (!qFuzzyCompare(this->fAmpUpperLimit, stPRPDExtInformation.fAmpUpperLimit)) return false;
            if (!(this->eFrequencyBand == stPRPDExtInformation.eFrequencyBand)) return false;
            if (!qFuzzyCompare(this->fFrequencyLowerLimit, stPRPDExtInformation.fFrequencyLowerLimit)) return false;
            if (!qFuzzyCompare(this->fFequencyUpperLimit, stPRPDExtInformation.fFequencyUpperLimit)) return false;
            if (!(this->iPhaseWindowCount == stPRPDExtInformation.iPhaseWindowCount)) return false;
            if (!(this->iQuantizedAmplitude == stPRPDExtInformation.iQuantizedAmplitude)) return false;
            if (!(this->iPowerFreqCycleCount == stPRPDExtInformation.iPowerFreqCycleCount)) return false;
            if (!(this->aucPDTypeProbability == stPRPDExtInformation.aucPDTypeProbability)) return false;
            if (!(this->qstrBGFileName == stPRPDExtInformation.qstrBGFileName)) return false;
            if (!(this->eDataJudgmentFlag == stPRPDExtInformation.eDataJudgmentFlag)) return false;
            if (!(this->sGain == stPRPDExtInformation.sGain)) return false;
            if (!(this->eSyncSource == stPRPDExtInformation.eSyncSource)) return false;
            if (!(this->ucSyncState == stPRPDExtInformation.ucSyncState)) return false;
            if (!qFuzzyCompare(this->fSyncFrequency, stPRPDExtInformation.fSyncFrequency)) return false;
            if (!(this->eDischargeSeverity == stPRPDExtInformation.eDischargeSeverity)) return false;
            if (!(this->ucDenoisingThreshold == stPRPDExtInformation.ucDenoisingThreshold)) return false;
            return true;
        }
    };

    struct PRPDData
    {
        QByteArray qbaPDSpectrumData;

        PRPDData()
        {

        }

        PRPDData& operator=(const PRPDData& stPRPDData)
        {
            this->qbaPDSpectrumData = stPRPDData.qbaPDSpectrumData;
            return *this;
        }

        bool operator==(const PRPDData& stPRPDData) const
        {
            if (!(this->qbaPDSpectrumData == stPRPDData.qbaPDSpectrumData)) return false;
            return true;
        }
    };
}
