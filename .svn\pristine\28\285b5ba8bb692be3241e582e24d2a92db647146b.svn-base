#include "UHFIntervalStrategy.h"
#include "model/HCStatus.h"
#include "../peripheral/peripheralservice.h"
#include "log/log.h"


const int MAX_VALUE_INIT = -128;

/****************************
功能： 构造函数
*****************************/
UHFIntervalStrategy::UHFIntervalStrategy(QObject *parent) :
    UHFSampleStrategy(parent),m_iReadCnt( 0 )
  ,m_bGetDataResult(false),m_eSignalState(Module::SIGNAL_STATE_NONE),
    m_bSignalInit(false)
{
    m_iReadFailCnt = 0;
    memset(&m_dataRead,0x0,sizeof(UHFHFCTData));
    memset(&m_sMaxRead,0x0,sizeof(UHFHFCTData));
}

/*************************************************
功能： 更新最大值
入参：sReadData -- 读取到的数据
*************************************************/
void UHFIntervalStrategy::updateMaxValue( const UHFHFCTData& sReadData )
{
    if( sReadData.cMaxSpectrum > m_sMaxRead.cMaxSpectrum )  // 更新最大值
    {
        m_sMaxRead = sReadData;
    }
}

/*************************************************
功能： 更新信号状态
入参：eSignalState -- 信号状态
*************************************************/
void UHFIntervalStrategy::updateSignalState( Module::SignalState eSignalState )
{
    if( !m_bSignalInit )
    {
        m_eSignalState = eSignalState;
        emit sigSignalChanged( m_eSignalState );
        m_bSignalInit = true;
    }
    else
    {
        if( eSignalState != m_eSignalState ) // 监测信号状态是否发生改变
        {
            m_eSignalState = eSignalState;
            emit sigSignalChanged( m_eSignalState );
        }
        //m_eSignalState = eSignalState;
        //emit sigSignalChanged(m_eSignalState);
    }
    return;
}

/*************************************************
功能： 获取有效数据
返回值：UHF 幅值数据
*************************************************/
UHF::IntervalData UHFIntervalStrategy::getValidData( void )
{
    UHF::IntervalData data;
    data.cMaxSpectrum = m_sMaxRead.cMaxSpectrum;
    for( int i = 0;i < UHF::SPECTRUM_DATA_SIZE;++i )
    {
        data.vSpectrum[i] = m_sMaxRead.caSpectrum[i];
    }
    return data;
}

/*************************************************
功能： 定时事件处理
输入参数:
    event -- 事件
*************************************************/
void UHFIntervalStrategy::timerEvent(QTimerEvent *e)
{
    if( e->timerId() == timerId() )
    {
        //先停止采样
        //stopSampleTimer();

        ++m_iReadCnt;

        Module::SampleState stSampleState;
        memset(&stSampleState, 0x0, sizeof(stSampleState));

        if(HC_SUCCESS == PeripheralService::instance()->readUhfData(&m_dataRead))
        {
            updateMaxValue(m_dataRead);  // 更新最大值
            m_bGetDataResult = true;
        }
        else
        {
            // read data fail
            ++m_iReadFailCnt;
        }

        //累积读取5次，若均未取到数据认为无信号
        if(m_iReadCnt > READ_DATA_CNT)
        {
            m_iReadCnt = 0;

            if(m_bGetDataResult) // 5次中至少有一次取得数据
            {
                m_iReadFailCnt = 0;
                emit sigData(getValidData()); // 获取有效数据

                stSampleState.eSignalState = Module::SIGNAL_STATE_EXIST;
                stSampleState.eSyncSource = static_cast<Module::SyncSource>(m_dataRead.eSyncSource);
                stSampleState.eSyncState = static_cast<Module::SyncState>(m_dataRead.eSyncState);
                stSampleState.iGain = static_cast<int>(m_dataRead.cGain);
                stSampleState.iBandWidth = static_cast<int>(m_dataRead.eBandWidth);
                stSampleState.iSampleInterval = m_iSampleInterval;

                updateState(stSampleState);

                m_bGetDataResult = false;  // 清除读取数据结果标志位
                memset(&m_sMaxRead,0x0,sizeof(UHFHFCTData)); // 读取到的五组内最大数据备份清零
            }
        }

        if(m_iReadFailCnt >= READ_DATA_FAIL_CNT)
        {
            m_iReadFailCnt = 0;

            stSampleState.eSignalState = Module::SIGNAL_STATE_NONE;
            stSampleState.eSyncSource = m_eSyncSource;
            stSampleState.eSyncState = Module::Not_Sync;
            stSampleState.iGain = static_cast<int>(m_dataRead.cGain);
            stSampleState.iBandWidth = static_cast<int>(m_dataRead.eBandWidth);
            stSampleState.iSampleInterval = m_iSampleInterval;

            updateState(stSampleState);
        }

        //重新开始采样
        //startSampleTimer();
    }
    else
    {
        killTimer(e->timerId());
    }

    return;
}
