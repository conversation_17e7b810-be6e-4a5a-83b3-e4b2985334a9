/*
* Copyright (c) 2017.11，南京华乘电气科技有限公司
* All rights reserved.
*
* rotateinfraredparameterdialog.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年11月27日
* 摘要：横屏参数设置对话框

* 当前版本：1.0
*/

#ifndef ROTATEINFRAREDPARAMETERDIALOG_H
#define ROTATEINFRAREDPARAMETERDIALOG_H

#include <QDialog>

class RotateInfraredParameterDialog : public QDialog
{
    Q_OBJECT
public:
    /*************************************************
    函数名： RotateInfraredParameterDialog(QDialog *pDialog, int angle = 90, QWidget *parent = 0)
    输入参数： pDialog：对话框指针
              angle：旋转角度
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    RotateInfraredParameterDialog(QDialog *pDialog, int angle = 90, QWidget *parent = 0);


protected:
    /*************************************************
    函数名： showEvent(QShowEvent *e)
    输入参数： e：显示事件
    输出参数： NULL
    返回值： NULL
    功能： 显示事件处理
    *************************************************************/
    void showEvent(QShowEvent *e);


private slots:
    /*************************************************
    函数名： onFinished()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 对话框关闭响应槽函数
    *************************************************************/
    void onFinished();

};

#endif // ROTATEINFRAREDPARAMETERDIALOG_H
