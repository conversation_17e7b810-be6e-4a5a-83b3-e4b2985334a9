/*
* Copyright (c) 2017.5，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：timezonemanager.h
*
* 初始版本：1.0
* 作者：张浪
* 修改日期：2018年9月22日
* 摘要：时区管理
* 当前版本：1.0
*/

#ifndef TIMEZONEMANAGER_H
#define TIMEZONEMANAGER_H

#include <QObject>
#include "timezonedefine.h"


class TimezoneManager : public QObject
{
    Q_OBJECT
private:
    explicit TimezoneManager(QObject *parent = NULL);
    ~TimezoneManager();

public:
    //获取时区管理类实例
    static TimezoneManager* instance();

    //初始化时区信息
    void initTimezoneInfo();

    //获取上一次本地时区
    double getLastLocalTimezoneVal();

    //获取本地时区
    double getLocalTimezoneVal();

    //获取本地时区信息
    QString getLocalTimezoneInfo();

    //设置本地时区
    void setLocalTimezoneVal(const QString& qstrTimeZoneInfo, bool bNotified = true);

    //设置本地时区
    void setLocalTimezoneVal(double dTimezoneVal);

    //UTC时间，单位s，格式化为本地增加时区的时间，因为设备arm层没有设置对应的时区，应用层来做兼容
    quint32 formatUTCTimeToLocalValSec(quint32 dwTimeVal);

    //UTC时间，单位ms，格式化为本地增加时区的时间，因为设备arm层没有设置对应的时区，应用层来做兼容
    qint64 formatUTCTimeToLocalValMsec(qint64 lTimeVal);

    //UTC时间，单位s，本地实际格式化为去除时区之后的UTC时间，因为设备arm层没有设置对应的时区，应用层来做兼容
    quint32 formatLocalTimeToUTCValSec(quint32 dwTimeVal);

    //UTC时间，单位ms，本地实际格式化为去除时区之后的UTC时间，因为设备arm层没有设置对应的时区，应用层来做兼容
    qint64 formatLocalTimeToUTCValMsec(qint64 lTimeVal);

    //获取当前时间的utc时间，单位秒（s）
    qint64 getCurUtcSecTime();

    //获取当前时间的utc时间，单位秒（ms）
    qint64 getCurUtcMsecTime();

    //根据时间s值获取格式化的时间string信息，格式为yyyy-MM-dd hh:mm:ss
    QString getFormatTimeStr(quint32 dwTime);

signals:
    //通知时区被修改
    void sigTimezoneChanged();

private:
    double m_dTZHourVal;            //时区时间，正表示东经，负表示西经，如8.00表示东八区
    double m_dTZLastHourVal;        //上一次的时区时间（用于记录时区变化修改系统时间），正表示东经，负表示西经，如8.00表示东八区
    QString m_qstrTimezoneInfo;
};

#endif // TIMEZONEMANAGER_H
