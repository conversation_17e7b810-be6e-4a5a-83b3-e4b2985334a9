/*
 * Copyright (c) 2020.10，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：pagescrollarea.h
 *
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2020/10/23
 * 摘要：页面滚动区域
 * 当前版本：1.0
 */

#ifndef PAGESCROLLAREA_H
#define PAGESCROLLAREA_H

#include <QScrollArea>
#include <QPointer>

class QHBoxLayout;
class PageScrollArea : public QScrollArea
{
    Q_OBJECT

public:
    explicit PageScrollArea(QWidget* pParent = NULL);

    void addPage(QWidget* pPage);

    void setCurrentPage(const int iCurrentPage);

    int pageCount();

signals:
    void sigCurrentPageChagned(const int iCurrentPage, const int iPreviousPage);

protected:
    virtual void mousePressEvent(QMouseEvent* pEvent);

    virtual void mouseMoveEvent(QMouseEvent* pEvent);

    virtual void mouseReleaseEvent(QMouseEvent* pEvent);

    virtual void resizeEvent(QResizeEvent* pEvent);

private:
    int m_iTotalPage;
    int m_iCurrentPage;
    bool m_bPressed;
    QPoint m_lastPoint;
    int m_iMoveDistance;
    QHBoxLayout* m_pMainLayout;
    QVector<QPointer<QWidget> > m_qvtPages;
};

#endif // PAGESCROLLAREA_H
