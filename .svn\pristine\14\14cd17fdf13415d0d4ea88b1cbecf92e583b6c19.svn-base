#include "prpsdatamodel.h"
#include <QVector>
#include <QDebug>
const int MAX_CACHE_SIZE = 5; // 缓存的最大组数 即5 * iPhase * iPeriod
/************************************************
 * 输入参数:
 *          iPeriodCount: 周期数
 *          iPhaseCount: 每周期数据点个数
 *          parent: 父对象指针
 * 功能: 构造函数
 ************************************************/
PrpsDataModel::PrpsDataModel(qint32 iPeriodCount, qint32 iPhaseCount,QObject *parent) :
    QObject(parent),
    m_iPeriodCount( iPeriodCount ),
    m_iPhaseCount( iPhaseCount ),
    m_iBufferSize( MAX_CACHE_SIZE * iPhaseCount * iPeriodCount ),
    m_iCacheCurrentPos( 0 ),
    m_bPrpsAccumulateEnabled( false ),
    m_strUnitSuffix( "dB" )
{
    m_vCurrentData = QVector<double>( m_iPeriodCount * m_iPhaseCount,0 );
}

/************************************************
 * 输入参数:
 *        bEnabled: true -- 使能
 *                  false -- 关闭
 * 功能: 设置录制使能开关
 ************************************************/
void PrpsDataModel::setPrpsAccumulateEnabled( bool bEnabled )
{
    m_bPrpsAccumulateEnabled = bEnabled;
    updateCacheSize( m_bPrpsAccumulateEnabled );    // 根据录制开关重定义缓存默认大小
    clearData();                                    // 内置业务逻辑，当切换是否进行prps累积时，清除当前数据
}

/************************************************
 * 返回值:
 *       true -- 使能
 *      false -- 关闭
 * 功能: 获得PRPS累积使能开关
 ************************************************/
bool PrpsDataModel::isPrpsAccumulateEnabled( void )
{
    return m_bPrpsAccumulateEnabled;
}

/************************************************
 * 功能: 根据录制开关是否打开，更新缓存的默认大小
 * 入参：bRecordEnabled -- 录制开关
 ************************************************/
void PrpsDataModel::updateCacheSize( bool bRecordEnabled )
{
    if( !bRecordEnabled )
    {
        setBufferSize( MAX_CACHE_SIZE * m_iPhaseCount * m_iPeriodCount );
    }
    else
    {
        // by set
    }
}

/************************************************
 * 输入参数:
 *          iSize: 缓存大小
 * 功能: 设置缓存大小
 ************************************************/
void PrpsDataModel::setBufferSize( qint32 iSize )
{
    m_iBufferSize = iSize;
}

/************************************************
 * 返回值: 数据的单位名称
 * 功能: 单位名称
 ************************************************/
QString PrpsDataModel::suffix( void ) const
{
    return m_strUnitSuffix;
}

/************************************************
 * 输入参数:
 *        strSuffix: 单位名称(一般为mV或dB)
 * 功能: 设置单位名称
 ************************************************/
void PrpsDataModel::setSuffix( const QString& strSuffix )
{
    m_strUnitSuffix = strSuffix;
    emit sigSuffixChanged( m_strUnitSuffix );
}

/************************************************
 * 返回值: 当前数据
 * 功能: 返回当前数据
 ************************************************/
const QVector<double> &PrpsDataModel::currentData() const
{
    return m_vCurrentData;
}

/************************************************
 * 返回值: 全部数据
 * 功能: 返回全部数据
 ************************************************/
const QVector<double> &PrpsDataModel::allData() const
{
    return m_vDataCache;
}

/************************************************
 * 入参: data -- 添加进来的数据
 * 功能: 添加的数据
 ************************************************/
void PrpsDataModel::setData( const QVector<double> &data )
{
    qint32 iCacheSizeAll = m_vDataCache.size() + data.size();
    if( iCacheSizeAll > m_iBufferSize )
    {
        m_vDataCache += data;       // 将数据添加进缓存
        m_vDataCache = m_vDataCache.mid( iCacheSizeAll - m_iBufferSize );   //若超出缓存上限则将最前面的数据移除
    }
    else
    {
        m_vDataCache += data;       // 将数据添加进缓存
    }

}

/************************************************
 * 输入参数:
 *        iPeriod: 指定推进周期数
 * 返回值: 实际推进周期数
 * 功能: 实现数据更新,即推进给定周期数,如果数据不足,则返回实际推进的周期数
 ************************************************/
int PrpsDataModel::advance( int iPeriod )
{
    int iActualAdvance = iPeriod + m_iCacheCurrentPos;              // 当前推进的总周期数

    if( isAdvanceStepValid( iActualAdvance ) )                      // 推进的周期数是否合理
    {
        if( !isCacheEnough( iActualAdvance,m_vDataCache ) )         // 缓存是否足够
        {
            iActualAdvance = m_vDataCache.size() / m_iPhaseCount;   // 缓存不够的情况下，推进当前可供提供的缓存
        }
        else
        {
            // cache size is enough
        }
    }
    else
    {
        iActualAdvance = 0;
    }
    if( m_bPrpsAccumulateEnabled )
    {
        // 根据推进的间隔
        if( iActualAdvance < m_iPeriodCount )
        {
            m_vCurrentData = QVector<double>( ( m_iPeriodCount - iActualAdvance ) * m_iPhaseCount,0 ) +
                    m_vDataCache.mid( 0,iActualAdvance * m_iPhaseCount );
        }
        else
        {
            m_vCurrentData = m_vDataCache.mid( ( iActualAdvance - m_iPeriodCount ) * m_iPhaseCount,
                                               m_iPeriodCount * m_iPhaseCount );
        }
        m_iCacheCurrentPos = iActualAdvance;
    }
    else
    {
        qint32 iMaxAdvancePeriod = maxAdvancePeriodSingle();
        if( iActualAdvance > iMaxAdvancePeriod )
        {
            /*
             * 隐藏业务逻辑，如果累积大量数据，计算需要推动的周期数超过设置周期（50），
             * 则按照最大周期数进行推进，否则部分数据无法正确显示
            */
            iActualAdvance = iMaxAdvancePeriod;
        }
        m_vCurrentData = m_vCurrentData.mid( iActualAdvance * m_iPhaseCount ) +
                         m_vDataCache.mid( 0,iActualAdvance * m_iPhaseCount );
        m_vDataCache.remove( 0,iActualAdvance * m_iPhaseCount );
        m_iCacheCurrentPos = 0;
    }

    emit sigAdvanced( iActualAdvance );
    return iActualAdvance;
}

/************************************************
 * 功能: 单次最大推进周期数
 * 返回值：最大推进周期数
 ************************************************/
qint32 PrpsDataModel::maxAdvancePeriodSingle( void )
{
    return m_iPeriodCount;
}

/************************************************
 * 功能: 重置
 *      清空当前显示数据，以及当前数据在缓存中的相对位置等信息
 ************************************************/
void PrpsDataModel::reset( void )
{
    m_iCacheCurrentPos = 0;
    m_vCurrentData = QVector<double>( m_iPeriodCount * m_iPhaseCount,0 );
}

/************************************************
 * 功能: 判断推进的步数书否合法
 * 入参：iStep -- 推进的步数
 * 返回值：true -- 合法
 *       false -- 非法
 ************************************************/
bool PrpsDataModel::isAdvanceStepValid( qint32 iStep )
{
    bool bValid = false;
    if( iStep < 0 )
    {
        bValid = false;
    }
    else
    {
        bValid = true;
    }
    return bValid;
}

/************************************************
 * 功能: 判断缓存是否足够
 * 入参：iPerod -- 推动的周期数
 *      cache -- 缓存
 * 返回值：true -- 缓存足够
 *       false -- 缓存不足
 ************************************************/
bool PrpsDataModel::isCacheEnough( qint32 iPeriod,const QVector<double>& cache )
{
    bool bIsEnough = false;
    if( ( cache.size() / m_iPhaseCount ) > iPeriod )
    {
        bIsEnough = true;
    }
    else
    {
        // not enough
    }
    return bIsEnough;
}

/************************************************
 * 功能: 清除数据
 ************************************************/
void PrpsDataModel::clearData()
{
    m_vDataCache.clear();

    m_vCurrentData = QVector<double>( m_iPeriodCount * m_iPhaseCount,0 );

    emit sigDataCleared();
}

/************************************************
 * 返回值: 缓存的大小
 * 功能: 获取缓存中的数据size
 ************************************************/
qint32 PrpsDataModel::bufferSize( void )
{
    return m_vDataCache.size();
}
