/*
* Copyright (c) 2016.02，南京华乘电气科技有限公司
* All rights reserved.
*
* PlayBackViewEx.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月17日
* 摘要：定义回放显示界面

* 当前版本：1.0
*/
#include <QApplication>
#include <QDesktopWidget>
#include <QVBoxLayout>
#include <QDebug>
#include <QStringList>
#include "PlayBackViewEx.h"
#include "PngPlayBack.h"
#include "datadefine.h"
#include "dataSave/DataFileInfos.h"

#define PLAYBACK_WIDTH 480//宽度
#define PLAYBACK_HEIGHT 854//宽度
#define PLAYBACK_TITLE_HEIGHT 100

/****************************
功能： 构造函数
输入参数:
    path -- 路径
    title -- 回放成功时标题
    qstrTmpTitle -- 中间过程的标题
    bEnablePngFile -- 是否使能Png回放（默认支持）
    parent -- 父窗体
*****************************/
PlayBackViewEx::PlayBackViewEx( const QString &path, const QString &title, const QString& qstrTmpTitle, bool isShownTitleAtPlayBackView, bool bShownTitle, bool bShownFileRoute, QWidget *parent )
    : QDialog(parent),
      m_qsRootPath( path ),
      m_isShownTitleAtPlayBackView(isShownTitleAtPlayBackView),
      m_pPlayBackView(NULL)
{
    setWindowFlags(Qt::FramelessWindowHint);
    setFocusPolicy( Qt::StrongFocus );
#ifdef Q_WS_QWS
    setFixedWidth( qApp->desktop()->width() );
    setFixedHeight( qApp->desktop()->height() );
#else
    setFixedWidth( PLAYBACK_WIDTH );
    setFixedHeight( PLAYBACK_HEIGHT );
#endif

    m_pFileListView = new FileListViewEx( path, this );

    m_qstrTitle = title;
    m_qstrTmpTitle = qstrTmpTitle;

    m_pTitleBar = new TitleBar( m_qstrTmpTitle, this );// 初始化标题栏
    m_pTitleBar->setFixedHeight(PLAYBACK_TITLE_HEIGHT);

    m_pPlayChart = new PlayBackChart( this, bShownFileRoute, bShownTitle);// 初始化回放界面

    m_pStackLayout = new QStackedLayout;   // 将文件选择页面和图谱显示界面加入栈布局器
    m_pStackLayout->addWidget( m_pFileListView );
    m_pStackLayout->addWidget( m_pPlayChart );
    m_pStackLayout->setMargin( 0 );
    m_pStackLayout->setSpacing( 0 );
    m_pStackLayout->setCurrentWidget( m_pFileListView );

    QVBoxLayout *vLayout = new QVBoxLayout( this );

    vLayout->addWidget( m_pTitleBar,1 );
    vLayout->addLayout( m_pStackLayout,7 );
    vLayout->setMargin( 0 );
    vLayout->setSpacing( 0 );
    setLayout( vLayout );

    setAttribute( Qt::WA_DeleteOnClose );

    connect( m_pFileListView, SIGNAL(sigFileSelected(int,QStringList)), this, SLOT(onFileChoosed(int,QStringList)) );
    connect( m_pTitleBar,SIGNAL(sigClicked()),this,SLOT(onReturnPreviousWidget()) );

    m_iIndex = -1;
}

/*************************************************
功能： 添加类型及对应的回放图表
输入参数:
    strSuffix -- 文件类型（如*.t01)
    pPlayBack:回放图表
*************************************************************/
void PlayBackViewEx::addPlayback(const QStringList& fileList, PlayBackBase* pPlayBack)
{
    if( NULL != pPlayBack )
    {
        m_pPlayBackView = pPlayBack;
        m_pFileListView->setFileList(fileList);
        m_pPlayChart->addChart(pPlayBack);

        connect(pPlayBack, SIGNAL(sigPressDown()), this, SLOT(onDownPress()));
        connect(pPlayBack, SIGNAL(sigPressUp()), this, SLOT(onUpPress()));
        connect(pPlayBack, SIGNAL(sigDeleteCurrentFile()), this, SLOT(onDeleteCurrentFile()));
    }
    else
    {
        qWarning() << "PlayBackViewEx::addPlayback: invalid NULL playback set!";
    }
}

/*************************************************
函数名： onPathChanged
输入参数:
    qsPath:文件列表选择的路径
输出参数：NULL
返回值： NULL
功能： 切换回放的数据源
*************************************************************/
void PlayBackViewEx::closeEvent(QCloseEvent *e)
{
    Q_UNUSED( e );
    emit sigClosed();
}

/****************************
函数名： setFileRoute;
输入参数:
    fileRoute -- 除根目录外的路径
输出参数：NULL
返回值：NULL
功能： 设置设备名
*****************************/
void PlayBackViewEx::setFileRoute( const QString& fileRoute )
{
    int i = fileRoute.count("/");       // 计算根目录下“/”的个数，用来显示路径时滤出根路径部分
    QString string = fileRoute.section( "/",i );// 通过上条语句得到的“/”个数，取后面所有字符串
    m_pPlayChart->setFileRoute( string );// 设置显示路径
}

/****************************
函数名： handleEsc;
输入参数:NULL
输出参数：NULL
返回值：NULL
功能： 按下esc键或点击标题栏的处理
*****************************/
void PlayBackViewEx::handleEsc( void )
{
    if( m_pFileListView == m_pStackLayout->currentWidget() ) // 若当前页面是文件选择页面，esc则关闭
    {
        close();
    }
    else
    {
        m_pTitleBar->setHidden(false);
        m_pTitleBar->setTitle(m_qstrTmpTitle);
        m_pStackLayout->setCurrentWidget( m_pFileListView ); // 若当前页面是回放页面，esc则回到文件选择页面
        m_pFileListView->setFocus();  // 解决测试缺陷，回放数据后点击标题栏，无法再通过上下键切换图谱
    }
}

/****************************
功能： 绑定标题栏，标题栏点击时，类似esc的处理
*****************************/
void PlayBackViewEx::onReturnPreviousWidget( )
{
    handleEsc();
}

/*************************************************
函数名： keyPressEvent
输入参数:
    event -- 事件
输出参数：NULL
返回值： NULL
功能： 键盘事件
*************************************************************/
void PlayBackViewEx::keyPressEvent( QKeyEvent* event )
{
    if( event->key() == Qt::Key_Escape )
    {
        handleEsc();
    }
    else if( ( event->key() == Qt::Key_Enter ) || ( event->key() == Qt::Key_Return ) )
    {
        if( m_pPlayChart == m_pStackLayout->currentWidget() )
        {
            emit sigEnterPressed();
            close();                                                    // 若当前页面是回放页面，enter则关闭
        }
    }
    else if( event->key() == Qt::Key_Up )
    {
        if( m_pPlayChart == m_pStackLayout->currentWidget() )
        {
            moveBackward();                                                                // 若当前页面是回放页面，up则上翻页
        }
    }
    else if( event->key() == Qt::Key_Down )
    {
        if( m_pPlayChart == m_pStackLayout->currentWidget() )
        {
            moveForward();                                                                // 若当前页面是回放页面，down则下翻页
        }
    }
    else
    {
        QWidget::keyPressEvent( event );
    }
}

/****************************
功能： 槽函数，响应回放文件选中
*****************************/
void PlayBackViewEx::onFileChoosed(int Id , const QStringList &qStringList)
{
    m_qFileList = qStringList;
    m_iIndex = Id;

    if( Id >= 0 && Id < m_qFileList.size() )
    {
        fileChoosed( m_qFileList[Id] );
    }
}

void PlayBackViewEx::onPlayNextFile()
{
    //dbg_info("");
    moveForward();
}

void PlayBackViewEx::onPlayLastFile()
{
    //dbg_info("");
    moveBackward();
}

/****************************
功能： 下翻页
*****************************/
void PlayBackViewEx::moveForward( void )
{
    m_iIndex = ( m_iIndex + 1 )%m_qFileList.count();

    fileChoosed( m_qFileList.at( m_iIndex ) );
}

/****************************
功能： 上翻页
*****************************/
void PlayBackViewEx::moveBackward( void )
{
    m_iIndex = ( m_iIndex + m_qFileList.count() - 1 )%m_qFileList.count();

    fileChoosed( m_qFileList.at( m_iIndex ) );
}

/****************************
入参：strFileName -- 选中文件名
功能： 处理选中文件
*****************************/
void PlayBackViewEx::fileChoosed( const QString& strFileName )
{
    // 如果是同一个文件，不需要重复回放
    if (strFileName == m_strFileName)
    {
        return;
    }

    m_pTitleBar->setTitle(m_qstrTitle);

    m_strFileName = strFileName;
    if(m_isShownTitleAtPlayBackView == false)
    {
        m_pTitleBar->setHidden(true);
    }
    setFileRoute( strFileName );         // 设置文件路径选择

    if (NULL != m_pPlayBackView)
    {
        m_pStackLayout->setCurrentWidget( m_pPlayChart );  // 设置当前页面为回放界面
        m_pPlayBackView->loadData(strFileName);
        m_pPlayBackView->playbackFile(strFileName);
    }
}

/****************************
函数名： setStationName;
输入参数:
    stationName -- 站名
输出参数：NULL
返回值：NULL
功能： 设置站点名
*****************************/
void PlayBackViewEx::setStationName( const QString& stationName )
{
    m_pPlayChart->setStationName( stationName );         // 设置站名
}

/****************************
函数名： setStationName;
输入参数:
    deviceName -- 设备名
输出参数：NULL
返回值：NULL
功能： 设置设备名
*****************************/
void PlayBackViewEx::setDeviceName( const QString& deviceName )
{
    m_pPlayChart->setDeviceName( deviceName );      // 设置设备名称
}

/****************************
 接收上按键按下信号
 ****************************/
void PlayBackViewEx::onUpPress()
{
    moveBackward();
    return;
}

/****************************
 接收下按键按下信号
 ****************************/
void PlayBackViewEx::onDownPress()
{
    moveForward();
    return;
}

/****************************
 接收删除文件信号
 ****************************/
void PlayBackViewEx::onDeleteCurrentFile()
{
    if (m_strFileName.isEmpty())
    {
        return;
    }

//    QFile file(m_strFileName);
//    if(file.exists())
//    {
//        file.remove();
//    }

    m_qFileList.removeAt(m_iIndex);
    emit sigDeleteCurrentFile(m_strFileName);
    m_strFileName.clear();

    // 如果文件列表为空则退出界面，否则回放上一条
    if (m_qFileList.isEmpty())
    {
        close();
    }
    else
    {
        moveBackward();
    }
}

