/*
 * Copyright (c) 2021.10，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：currentampspectrumdefine.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2021/10/22
 * 摘要：电流幅值图谱数据定义
 * 当前版本：1.0
 */

#pragma once

#include <QString>
#include <QHash>
#include "dataspecification_def.h"

namespace DataSpecificationNS
{
    struct CurrentAmpExtInformation
    {
        AmpUnit eAmpUnit;               //幅值单位
        quint8 ucCurrentDataCount;

        CurrentAmpExtInformation()
        {
            eAmpUnit = AMP_UNIT_DEFAULT;
            ucCurrentDataCount = 0;
        }

        CurrentAmpExtInformation& operator=(const CurrentAmpExtInformation& stCurrentAmpExtInformation)
        {
            this->eAmpUnit = stCurrentAmpExtInformation.eAmpUnit;
            this->ucCurrentDataCount = stCurrentAmpExtInformation.ucCurrentDataCount;
            return *this;
        }

        bool operator==(const CurrentAmpExtInformation& stCurrentAmpExtInformation) const
        {
            if (!(this->eAmpUnit == stCurrentAmpExtInformation.eAmpUnit)) return false;
            if (!(this->ucCurrentDataCount == stCurrentAmpExtInformation.ucCurrentDataCount)) return false;
            return true;
        }
    };

    struct CurrentAmpData
    {
        QHash<QString, float> currentDatas;

        CurrentAmpData()
        {
        }

        CurrentAmpData& operator=(const CurrentAmpData& stCurrentAmpData)
        {
            this->currentDatas = stCurrentAmpData.currentDatas;
            return *this;
        }

        bool operator==(const CurrentAmpData& stCurrentAmpData) const
        {
            if (!(this->currentDatas == stCurrentAmpData.currentDatas)) return false;
            return true;
        }
    };
}
