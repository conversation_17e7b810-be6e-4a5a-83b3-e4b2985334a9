#include "connectorview.h"
#include <QtConcurrentRun>
#include "datadefine.h"
#include "mobileaccessservice.h"
#include "testypeview.h"
#include "messageBox/msgbox.h"
#include "window/Window.h"
#include "Module.h"
#include "customaccesstask/taskmanager.h"
#include "peripheral/peripheralservice.h"
#include "recordplay/RecordPlayService.h"
#include "infrared/infraredservice.h"
#include "current/currentservice.h"
#include "customaccessUi/taskmodeview/taskmodeconfig.h"
#include "global_log.h"


#define SINGLE_TASK     1
#define DEFAULT_DELAY   300
#define TIMER_ID_INIT   -1
#define TIMER_OUT       1000
#define AUTO_SWITCH_VIEW_DELAY  1000




typedef enum _TaskButton_
{
    BUTTON_OPEN = 0, // 打开

}TaskButton;



//控制按钮定义
const ButtonInfo::Info g_OpenBtn = {BUTTON_OPEN, {ButtonInfo::COMMAND, TaskModeConfig::TEXT_OPEN, NULL, "", NULL}};


/*************************************************
功能： 构造函数
输入参数:
    qstrTitle:标题
    parent:父控件指针
*************************************************************/
ConnectorView::ConnectorView(const QString qstrTitle, QWidget *parent)
    : PDAListView(qstrTitle, parent)
    , m_iCurrentConnectorIndex(-1)
    , m_iAutoSwitchTimerId(TIMER_ID_INIT)
    , m_bAutoSwitch(true)
    , m_bClickEnable(false)
    , m_bSwitchNext(false)
    , m_bGettingCurrentInfo(false)
    , m_iCurrentTestIndex(0)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);

    m_pButtonBar = NULL;
    m_eAccessMode = SystemSetService::instance()->getCustomAccessMode();

    //添加任务信息
    initTaskItem();

    initBtnBarInfo();

    //connect(TaskManager::instance(), SIGNAL(sigCurJSONAssetTested(QString, int)), this, SLOT(onTaskChanged(QString, int)));
    connect(CurrentService::instance(), SIGNAL(sigReadFinished(bool, const Module::CabelCurrentInfo&)), this, SLOT(onReadCurrentFinished(bool, const Module::CabelCurrentInfo&)));
}

/*************************************************
功能： 析构函数
*************************************************************/
ConnectorView::~ConnectorView()
{
    //disconnect(TaskManager::instance(), SIGNAL(sigCurJSONAssetTested(QString, int)), this, SLOT(onTaskChanged(QString, int)));

    if(m_pBtnInfo)
    {
        delete [] m_pBtnInfo;
        m_pBtnInfo = NULL;
    }
}

/**************************************
 * 功能：处理显示事件
 * 输入参数：
 *      pEvent：显示事件
 * ************************************/
void ConnectorView::showEvent(QShowEvent *pEvent)
{
    Q_UNUSED(pEvent);
    QTimer::singleShot(TaskModeConfig::g_iClickedDelay, this, SLOT(onEnableClickEvent()));

    //setAutoSwitchTest();
}

/*************************************************
功能： 窗口关闭事件
输入参数:
    pEvent -- 事件
*************************************************************/
void ConnectorView::closeEvent(QCloseEvent* pEvent)
{
    killAutoSwitchTimer();

    if(m_bSwitchNext)
    {
        emit sigSwitchNext();
    }

    PDAListView::closeEvent(pEvent);
}

/*************************************************
功能： 定时器函数
输入参数:
    pEvent:定时事件
*************************************************************/
void ConnectorView::timerEvent(QTimerEvent *pEvent)
{
    m_bClickEnable = false;

    if(pEvent->timerId() == m_iAutoSwitchTimerId)
    {
        killAutoSwitchTimer();

        if(TaskManager::instance()->isJSONSubInfoFinished(TaskManager::instance()->getJSONSubTaskId()))
        {
            if(this->isActiveWindow())
            {
                if(MsgBox::OK == MsgBox::delayQuestion("", QObject::trUtf8("All items have been tested and will automatically jump.")))
                {
                    m_bSwitchNext = true;
                    close();
                }
                else
                {
                    m_bSwitchNext = false;
                }
            }
        }
        else
        {
            QString qstrId = TaskManager::instance()->getUnfinishedJSONAssetId();
            if(qstrId.isEmpty())
            {
                TaskManager::instance()->setJSONSubInfoFinished(TaskManager::instance()->getJSONSubTaskId());
                logError("get unfinished asset task failed.");
            }
            else
            {
                if(m_pChart)
                {
                    m_iCurrentTestIndex = m_pChart->indexOfItem(qstrId);
                    m_pChart->setCurrentItemSelected(static_cast<quint32>(m_iCurrentTestIndex));
                    clickItemToDo(m_iCurrentTestIndex);
                }
            }
        }
    }

    m_bClickEnable = true;

    return;
}

/********************************************
 * 功能：初始化主任务列表
 * ******************************************/
void ConnectorView::initTaskItem()
{
    QVector<qint32> tickIndexes = m_pChart->itemsIndexTicked();
    m_pChart->deleteAllItem();

    //添加任务信息
    QList<PDAListChart::ListItemInfo> itemInfos;
    bool bTicked = false;
    int iTested = 0;

    // 插入负荷电流项
    iTested = TaskManager::instance()->isJSONTaskLoadCurrentTested(TaskManager::instance()->getJSONMainTaskId());
    itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_LOAD_CURRENT), -1, iTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::TYPE_LOAD_CURRENT));
    if (iTested)
    {
        float fLoadCurrentVal = TaskManager::instance()->getCurJSONTaskLoadCurrentVal();
        itemInfos[0].m_strContent = (QString("%1A").arg(static_cast<double>(fLoadCurrentVal), 0, 'f', 1));
    }

    if(m_qmt4AssetInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        m_qvtAssetInfos = TaskManager::instance()->getCurJSONTaskAssetInfos();

        for(int i = 0, iSize = m_qvtAssetInfos.size(); i < iSize; ++i)
        {
            bTicked = tickIndexes.contains(i);
            iTested = TaskManager::instance()->getJSONAssetTestVal(m_qvtAssetInfos[i].qstrId);

            itemInfos << PDAListChart::ListItemInfo(m_qvtAssetInfos[i].qstrName, -1, iTested, bTicked, PDAListItem::LABEL_MODE, m_qvtAssetInfos[i].qstrId);
            /*if(SystemSet::ACCESS_USB_MODE == m_eAccessMode)
            {
                itemInfos << PDAListChart::ListItemInfo(m_qvtAssetInfos[i].qstrName, -1, iTested, bTicked, PDAListItem::LABEL_MODE, m_qvtAssetInfos[i].qstrId);
            }
            else
            {
                itemInfos << PDAListChart::ListItemInfo(m_qvtAssetInfos[i].qstrName, -1, iTested, bTicked, PDAListItem::CHECK_BOX, m_qvtAssetInfos[i].qstrId);
            }*/
        }

        m_qmt4AssetInfos.unlock();
    }

    m_pChart->addItems(itemInfos);
    m_pChart->setCurrentItemSelected(m_iCurrentTestIndex);
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void ConnectorView::onCommandButtonPressed(int id)
{
    if(!m_bClickEnable)
    {
        logWarning("unable to click.");
        return;
    }

    killAutoSwitchTimer();

    switch(id)
    {
    case BUTTON_OPEN:
    {
        int iSelectItems = m_pChart->itemsIndexSelected().size();
        if(iSelectItems == SINGLE_TASK)                        // 是否选中单个任务，仅允许打开单个
        {
            int iSelectedIndex = m_pChart->itemIndexSelected();
            if(iSelectedIndex != PDAListChart::ERROR_SELECTED_INDEX)  // 是否有条目被选中
            {
                clickItemToDo(iSelectedIndex);
            }
            else
            {
                logWarning("no item select");
            }
        }
        else if(iSelectItems > SINGLE_TASK)
        {
            MsgBox::warning("", QObject::trUtf8("Unable to open multiple items."));
        }
        else
        {
            MsgBox::warning("", QObject::trUtf8("No item has been chosen."));
        }

        break;
    }
    default:
        break;
    }

    return;
}

/*************************************************
功能： 处理条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void ConnectorView::onItemClicked(int id)
{
    if(!m_bClickEnable)
    {
        logWarning("unable to click.");
        return;
    }

    killAutoSwitchTimer();

    m_iCurrentTestIndex = id;
    clickItemToDo(id);
}

/*************************************************
功能： 槽，响应******
*************************************************************/
void ConnectorView::onTaskChanged(QString qstrId, int iTestVal)
{
    if(m_pChart)
    {
        m_pChart->setCurItemTested(qstrId, iTestVal);
    }

    return;
}

/*************************************************
功能： 槽，响应读取电流结束
*************************************************************/
void ConnectorView::onReadCurrentFinished(bool bSuccess, const Module::CabelCurrentInfo& stCurrentInfo)
{
    if (!m_bGettingCurrentInfo)
    {
        return;
    }

    m_bGettingCurrentInfo = false;
    m_bClickEnable = true;
    m_pChart->setEnabled(true);
    if (!bSuccess)
    {
        MsgBox::warning("", trUtf8("Read load current failed!"));
        return;
    }

    TaskManager::instance()->setCurJSONTaskLoadCurrentVal(stCurrentInfo.fLoadCurrentVal);

    QList<PDAListChart::ListItemInfo> itemInfos = m_pChart->allItems();
    if (itemInfos.size() > 0)
    {
        itemInfos[0].m_strContent = (QString("%1A").arg(static_cast<double>(stCurrentInfo.fLoadCurrentVal), 0, 'f', 1));
        m_pChart->setItemInfo(itemInfos[0], 0);
        m_pChart->setCurItemTested(itemInfos[0].m_strId, true);
    }
}

/*************************************************
功能： 槽，响应测试类型视图关闭
*************************************************************/
void ConnectorView::onTestTypeViewClosed()
{
    // 更新当前项的状态
    PDAListChart::ListItemInfo stListItemInfo = m_pChart->currentItemInfo();
    if (!stListItemInfo.m_strId.isEmpty())
    {
        int iTestVal = TaskManager::instance()->getJSONAssetTestVal(stListItemInfo.m_strId);
        m_pChart->setCurItemTested(stListItemInfo.m_strId, iTestVal);
    }
}

/*****************************************
 * 功能：初始化按钮栏信息
 * **********************************************/
void ConnectorView::initBtnBarInfo()
{
    m_pBtnInfo = NULL;
    m_qui8BtnCnt = 0;

    m_pButtonBar = NULL;

    QVector<ButtonInfo::Info> qvtBtnInfos;
    qvtBtnInfos.clear();

    qvtBtnInfos.push_back(g_OpenBtn);

    m_qui8BtnCnt = static_cast<quint8>(qvtBtnInfos.size());
    m_pBtnInfo = new ButtonInfo::Info[m_qui8BtnCnt];
    for (int i = 0; i < m_qui8BtnCnt; ++i)
    {
        m_pBtnInfo[i] = qvtBtnInfos[i];
    }

    //创建按钮栏
    m_pButtonBar = createButtonBar(TaskModeConfig::CONTEXT, m_pBtnInfo, m_qui8BtnCnt);
}

/********************************************
 * 功能：槽，使能可以点击事件
 * ******************************************/
void ConnectorView::onEnableClickEvent()
{
    m_bClickEnable = true;
    return;
}

/*****************************************
 * 功能：关闭自动跳转定时器
 * **********************************************/
void ConnectorView::killAutoSwitchTimer()
{
    if(TIMER_ID_INIT != m_iAutoSwitchTimerId)
    {
        killTimer(m_iAutoSwitchTimerId);
        m_iAutoSwitchTimerId = TIMER_ID_INIT;
    }

    return;
}

/*****************************************
 * 功能：自动跳转检测任务
 * **********************************************/
void ConnectorView::setAutoSwitchTest()
{
    int iMode = MobileAccessService::instance()->getSwitchMode();
    if(SystemSet::ACCESS_AUTO_SWITCH == static_cast<SystemSet::AccessSwitchMode>(iMode))
    {
        if(TIMER_ID_INIT == m_iAutoSwitchTimerId)
        {
            m_iAutoSwitchTimerId = startTimer(AUTO_SWITCH_VIEW_DELAY);
        }
    }

    return;
}

/*****************************************
 * 功能：条目被点击后的操作，弹出相位选择的页面
 * **********************************************/
void ConnectorView::clickItemToDo(int iIndex)
{
    // 第一个是负荷电流
    if (0 == iIndex)
    {
        m_pChart->setEnabled(false);
        m_bClickEnable = false;
        m_bGettingCurrentInfo = true;
        CurrentService::instance()->getCurrentInfoSync();
    }
    else
    {
        iIndex -= 1;
        if(m_qmt4AssetInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
        {
            if(0 <= iIndex && iIndex < m_qvtAssetInfos.size())
            {
                m_iCurrentConnectorIndex = iIndex;
                TaskManager::instance()->setJSONAssetId(m_qvtAssetInfos[iIndex].qstrId);

                // 显示测试类型
                TestTypeView* pTestTypeView = new TestTypeView();
                connect(pTestTypeView, SIGNAL(sigClosed()), this, SLOT(onTestTypeViewClosed()));
                pTestTypeView->show();
            }

            m_qmt4AssetInfos.unlock();
        }

    }
}
