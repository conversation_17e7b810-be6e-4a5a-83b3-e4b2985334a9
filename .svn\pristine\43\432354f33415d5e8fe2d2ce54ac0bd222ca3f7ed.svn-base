
#include <QTimerEvent>
#include "currentdetectionservice.h"
#include "peripheral/peripheralservice.h"

const int INVALID_TIMER = -1;
const int TIME_INTERVAL = 1000;
const int NO_SIGNAL_THREADSHOLD_CHECK = 5;

CurrentDetectionService* CurrentDetectionService::instance()
{
    static CurrentDetectionService service;
    return &service;
}

CurrentDetectionService::CurrentDetectionService()
    : m_pThread(NULL),
    m_mutexAffair(QMutex::Recursive),
    m_iTimer(INVALID_TIMER),
    m_bIsSampling(false),
    m_eSignalState(Module::SIGNAL_STATE_INVALID),
    m_uiReadDataFailCnt(0)
{
    qRegisterMetaType<Module::SignalState>("Module::SignalState");
    qRegisterMetaType<CurrentDetection::CurrentDetectionData>("CurrentDetection::CurrentDetectionData");
    qRegisterMetaType<MultiServiceNS::USERID>("MultiServiceNS::USERID");

    m_pThread = new QThread(this);
    moveToThread(m_pThread);

    connect(this, SIGNAL(sigStartSample()), this, SLOT(onStartSample()), Qt::QueuedConnection);
    connect(this, SIGNAL(sigStopSample()), this, SLOT(onStopSample()), Qt::QueuedConnection);
    emit sigStartSample();
}

CurrentDetectionService::~CurrentDetectionService()
{
    stop();

    if (-1 != m_iTimer)
    {
        killTimer(m_iTimer);
    }
}

/*************************************************
函数名： start()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 启动业务
*************************************************************/
bool CurrentDetectionService::start()
{
    QMutexLocker qMutexLocker(&m_mutexAffair);  //获取事务操作权限
    if (!m_pThread->isRunning())
    {
        m_pThread->start();
    }
    return true;
}

/*************************************************
功能： 启动业务
输入参数:void
输出参数：NULL
返回值： NULL
*************************************************/
bool CurrentDetectionService::isStart(void)
{
    bool isStart = false;
    QMutexLocker qMutexLocker(&m_mutexAffair);  //获取事务操作权限
    isStart = m_pThread->isRunning();
    return isStart;
}

/*************************************************
函数名： stop()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 终止业务
*************************************************************/
bool CurrentDetectionService::stop()
{
    m_eSignalState = Module::SIGNAL_STATE_INVALID;
    m_uiReadDataFailCnt = 0;
    return true;
}

/*************************************************
功能： 启动采样业务的额外处理   （一般指在子类中的额外操作）
输入参数：
        userId -- 用户ID
返回：
      额外处理是否成功
*************************************************/
bool CurrentDetectionService::startSampleExt(MultiServiceNS::USERID userId)
{
    if (m_bIsSampling)
    {
        return true;
    }
    //暂不考虑，定时间隔不同的问题
    Q_UNUSED(userId)
    QMutexLocker qMutexLocker(&m_mutexAffair);  //获取事务操作权限
    if (!m_pThread->isRunning())
    {
        m_pThread->start();
    }

    emit sigSignalChanged(m_eSignalState);

    emit sigStartSample();

    m_bIsSampling = true;

    return true;
}

/*************************************************
功能： 停止采样业务的额外处理   （一般指在子类中的额外操作）
输入参数：
        userId -- 用户ID
返回：
      额外处理是否成功
*************************************************/
bool CurrentDetectionService::stopSampleExt(MultiServiceNS::USERID userId)
{
    QMutexLocker qMutexLocker(&m_mutexAffair);  //获取事务操作权限
    QVector<MultiServiceNS::USERID> sampleUser = sampleUsers(MultiServiceNS::SPECTRUM_CURRENTDETECTION);

    if (sampleUser.contains(userId))
    {
        // 当前发送队列，有且只有该用户，停止定时器
        if (sampleUser.size() == 1)
        {
            m_bIsSampling = false;
            emit sigStopSample();
        }
    }
    else
    {
        qWarning("CurrentDetectionService::stopSampleExt id is not pulseType");
    }

    return true;
}

/*************************************************
函数名： timerEvent(QTimerEvent *event)
输入参数： event：定时器事件
输出参数： NULL
返回值： NULL
功能： 定时器事件处理
*************************************************************/
void CurrentDetectionService::timerEvent(QTimerEvent *event)
{
    if (event->timerId() == m_iTimer && m_bIsSampling)
    {
        singleSample();
    }
}

/*************************************************
函数名： singleSample()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 单次采集
*************************************************************/
void CurrentDetectionService::singleSample()
{
#ifdef Q_PROCESSOR_ARM
    QMutexLocker qMutexLocker(&m_mutexAffair);  //获取事务操作权限
    CableCurrentData stCableCurrentData;
    memset(&stCableCurrentData, 0, sizeof(CableCurrentData));

    if (HC_SUCCESS == PeripheralService::instance()->readCurrentDetectionData(&stCableCurrentData))
    {
        m_uiReadDataFailCnt = 0;

        CurrentDetection::CurrentDetectionData data;
        data.fGroundingCurrentValue = stCableCurrentData.fGroundCurrent;  //当前接地电流数据
        data.fLoadCurrentValue = stCableCurrentData.fLoadCurrent;  //当前负荷电流数据

        logDebug(QString("Current Detection fGroundCurrent: %1, fLoadCurrent: %2.").arg(stCableCurrentData.fGroundCurrent).arg(stCableCurrentData.fLoadCurrent));

        QVector<MultiServiceNS::USERID> sampleUser = sampleUsers(MultiServiceNS::SPECTRUM_CURRENTDETECTION);
        for (int i = 0; i < sampleUser.size(); ++i)
        {
            emit sigData(data, sampleUser[i]);
        }

        updateSignalState(Module::SIGNAL_STATE_EXIST); //更新信号状态的变化
    }
    else
    {
        // read data fail
        ++m_uiReadDataFailCnt;
    }

    //累积读取5次，若均未取到数据认为无信号
    if (m_uiReadDataFailCnt >= NO_SIGNAL_THREADSHOLD_CHECK)
    {
        updateSignalState(Module::SIGNAL_STATE_NONE); //更新信号状态的变化
    }
#endif
}

/*************************************************
函数名： singleSample()
输入参数： eSignalState -- 信号状态
输出参数： NULL
返回值： NULL
功能： 更新信号状态
*************************************************/
void CurrentDetectionService::updateSignalState(const Module::SignalState eSignalState)
{
    if (eSignalState == m_eSignalState)
    {
        return;
    }

    m_eSignalState = eSignalState;
    emit sigSignalChanged(m_eSignalState);
}

void CurrentDetectionService::onStartSample()
{
    if (INVALID_TIMER == m_iTimer)
    {
        m_iTimer = startTimer(TIME_INTERVAL);
    }
}

void CurrentDetectionService::onStopSample()
{
    if (INVALID_TIMER != m_iTimer)
    {
        killTimer(m_iTimer);
        m_iTimer = INVALID_TIMER;
    }
}
