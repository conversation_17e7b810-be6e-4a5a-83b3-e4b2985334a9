﻿/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* ClockWindow.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月17日
* 摘要：时间设置窗口定义

* 当前版本：1.0
*/

#ifndef CLOCKWINDOW_H
#define CLOCKWINDOW_H

#include <QTimeEdit>
#include <QWidget>
#include <QVBoxLayout>
#include <QTimer>
#include <QTimerEvent>
#include <QPushButton>
#include "datadefine.h"
#include "timeLabel.h"
#include "Clock.h"
#include "ClockaTimeEdit.h"

class ClockWindow : public QWidget
{
    Q_OBJECT

public:
    /*************************************************
    函数名： ClockWindow
    输入参数:
            parent: 父控件
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    ClockWindow(QWidget *parent = NULL);

protected:
    /*************************************************
    函数名： timerEvent
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 定时函数
    *************************************************************/
    void timerEvent( QTimerEvent* );

    /************************************************
     * 函数名   : eventFilter
     * 输入参数 : pObj: 面板中的控件;pEvent: 事件
     * 输出参数 : NULL
     * 返回值   : 事件处理结果
     * 功能     : 事件过滤器,处理鼠标点击事件及物理键盘按钮事件
     ************************************************/
    bool eventFilter(QObject *pObj,QEvent *pEvent);

    /************************************************
     * 函数名   : resizeEvent
     * 输入参数 :
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : resize事件
     ************************************************/
    void resizeEvent(QResizeEvent* pEvent);

    /*************************************************
    功能： 显示事件处理
    *************************************************************/
    void showEvent(QShowEvent *event);

    /*************************************************
    功能： 处理窗口关闭事件
    *************************************************************/
    void closeEvent(QCloseEvent* event);

private slots:

    /*************************************************
    函数名： onTimeChanged
    输入参数:
            time: 当前时间
    输出参数： NULL
    返回值： NULL
    功能： 时间修改槽函数
    *************************************************************/
    void onTimeChanged(QTime time);

    /*************************************************
    函数名： onStopTimer
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 停止定时器
    *************************************************************/
    void onStopTimer();

    /*************************************************
    函数名： onOKButtonClicked
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 确认按钮点击槽函数
    *************************************************************/
    void onOKButtonClicked();

    /*************************************************
    函数名： onCancelButtonClicked
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 取消按钮点击槽函数
    *************************************************************/
    void onCancelButtonClicked();


private:
    enum
    {
        INVALID_TIMER_ID = -1, //无效的定时器id
        AUTO_CHANGE_TIME_TIMER_200_MS = 200 //自动更新时间的定时器时间间隔
    };

    Clock *m_pClock;    //时钟控件

    int m_iRefreshId;
    timeLabel   *m_pTimeLabel;
    QPushButton *m_pPlus;
    QPushButton *m_pMinus;
    QTime m_qTime;

    bool m_isPressPlusBtn; //+按钮是否按下标志
    bool m_isPressMinusBtn; //-按钮是否按下标志
    QPushButton *m_pOkButton;
    QPushButton *m_pCancelButton;
    int m_iAutoChangeTimeTimerId; //长按 + - 按钮时， 自动更新时间的定时器id
};

#endif // CLOCKWINDOW_H
