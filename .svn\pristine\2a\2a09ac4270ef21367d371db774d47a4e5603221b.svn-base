#ifndef AEPDAMAINVIEW_H
#define AEPDAMAINVIEW_H

#include <QWidget>
#include <QStackedLayout>
#include "pda/pda.h"
#include "PDAUi/PDAUiView/PDAViewConfig.h"

class AEPDAMainView : public QWidget
{
    Q_OBJECT
public:
    explicit AEPDAMainView(const ItemTestData& stTestDataInfo, const PDAView::PatrolTestPointInfo& stPatrolTestPointInfo, QWidget* parent = NULL);
    virtual ~AEPDAMainView();

signals:
    /*************************************************
    功能： 信号，窗口关闭
    *************************************************************/
    void sigClosed();

protected:
    void keyPressEvent(QKeyEvent *pEvent);

    bool eventFilter(QObject *pObj, QEvent *pEvent);

    /*************************************************
    功能： 定时器处理函数
    输入参数：
         pEvent -- 定时事件
    *************************************************************/
    void timerEvent(QTimerEvent* pEvent);

    /*************************************************
    功能： 窗口关闭事件
    输入参数:
        pEvent -- 事件
    *************************************************************/
    void closeEvent(QCloseEvent* pEvent);

private:
    void showAEView(const ItemTestData& stTestDataInfo, const PDAView::PatrolTestPointInfo& stPatrolTestPointInfo);

    /*************************************************
    功能： 延迟关闭界面，用于查看检测数据
    *************************************************************/
    void delayToClose();

    /*************************************************
    功能： 关闭自动跳转定时器
    *************************************************************/
    void killAutoSwitchTimer();

private slots:
    void onTested();

private:
    QStackedLayout* m_pStackedLayout;
    int m_iAutoSwitchTimerId;
};

#endif // AEPDAMAINVIEW_H
