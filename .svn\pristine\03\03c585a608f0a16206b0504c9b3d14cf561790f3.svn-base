/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* LineEditGrounp.cpp
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月25日
* 摘要：网络设置下输入框集合的定义

* 当前版本：1.0
*/

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QDebug>
#include "ppplineeditgroup.h"

// 标签样式
//const QString LABEL_STYLE ="QLabel{color:rgb(0,0,0);font-size:23px;font-family:msyh}";
const QString LABEL_STYLE ="QLabel{color:rgb(0,0,0);font-size:23px;}";
const int LABEL_WIDTH = 110;
const int LABEL_HEIGHT = 60;
/*************************************************
函数名： PPPLineEditGrounp(QWidget *parent)
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
PPPLineEditGrounp::PPPLineEditGrounp(QWidget *parent) :
    QWidget(parent)
{
    QVBoxLayout *layout = new QVBoxLayout( this );
    layout->setMargin( 0 );
    layout->setSpacing( 20 );
    setLayout( layout );        // 添加整体布局
}

/************************************************
 * 函数名   : addLineEditItem
 * 输入参数 : editNameIndex：标签名序号；lineEdit：输入框
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 往子窗口添加输入框
 ************************************************/
void PPPLineEditGrounp::addLineEditItem(PPPLineEditGrounp::NAME_INDEX editNameIndex, QLineEdit *lineEdit)
{
    QVBoxLayout *layout = ( QVBoxLayout* )this->layout();
    QString qsEditName = getEditName(editNameIndex);
    if( NULL != lineEdit && !qsEditName.isNull() )
    {
        QLabel  *pLabel = new QLabel( qsEditName + ":",this );
        pLabel->setFrameStyle( QFrame::Panel|QFrame::Raised );
        pLabel->setAlignment( Qt::AlignLeft|Qt::AlignVCenter );
        pLabel->setFixedSize( LABEL_WIDTH,LABEL_HEIGHT );
        lineEdit->setFixedHeight( LABEL_HEIGHT );
        pLabel->setStyleSheet( LABEL_STYLE );       // 设置凸起样式
        QHBoxLayout *hLayout = new QHBoxLayout;
        hLayout->addWidget( pLabel,1 );
        hLayout->addWidget( lineEdit,3 );
        layout->addLayout( hLayout );   // 完成布局
        m_vLineEditVector.append( lineEdit );
    }
}

/************************************************
 * 函数名   : getLineEdit
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 获得所有输入框的指针
 ************************************************/
QVector<QLineEdit*> PPPLineEditGrounp::getLineEdit( void )
{
    return m_vLineEditVector;
}

/************************************************
 * 函数名   : getEditName
 * 功能     : 根据序号返回lineEdit的名字
 ************************************************/
QString PPPLineEditGrounp::getEditName(PPPLineEditGrounp::NAME_INDEX editNameIndex )
{
    QString editName = QString::null;
    switch( editNameIndex )
    {
        case PPP_USER:
        {
            editName = QObject::trUtf8("User");
        }
            break;
        case PPP_PWD:
        {
            editName = QObject::trUtf8("PWD");
        }
            break;
        case PPP_APN:
        {
            editName = QObject::trUtf8("APN");
        }
        break;
        default:
        {
            qWarning("editNameIndex wrong!");
        }
            break;
    }
    return editName;
}
