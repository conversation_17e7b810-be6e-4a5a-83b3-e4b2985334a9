#include "aephasespectrum.h"
#include "private/aespectrumprivatedefine.h"
#include "common/dataspecificationutils.h"
#include "common/binaryprocesshelper.h"
#include "common/xmldocument.h"
#include "private/spectrumdatafilecommondefine.h"
#include "common/xmlprocesshelper.h"
#include <QDebug>

namespace DataSpecificationNS
{
    class AEPhaseSpectrumPrivate
    {
    public:
        AEPhaseExtInformationPrivate m_stAEPhaseExtInformationPrivate;
        AEPhaseDataPrivate m_stAEPhaseDataPrivate;
    };

    AEPhaseSpectrum::AEPhaseSpectrum()
        : Spectrum(),
          m_pAEPhaseSpectrumPrivate(new AEPhaseSpectrumPrivate)
    {
        setSpectrumTypeCode(SPECTRUM_CODE_AE_PHASE);
    }

    AEPhaseSpectrum::~AEPhaseSpectrum()
    {
    }

    /************************************************
     * 函数名   : setDataSpecificationVersion
     * 输入参数 :
       const DataSpecificationVersion eDataSpecificationVersion: 数据规范版本号
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置数据规范版本号
     ************************************************/
    void AEPhaseSpectrum::setDataSpecificationVersion(const DataSpecificationVersion eDataSpecificationVersion)
    {
        Spectrum::setDataSpecificationVersion(eDataSpecificationVersion);
        if (V_1_1_0_0 == eDataSpecificationVersion)
        {
            m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.skipTriggerThreshold();
            m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.skipOpenTime();
            m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.skipShutTime();
            m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.skipMaxIntervalTime();
            m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.skipGain();
            m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.skipSyncSource();
            m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.skipSyncState();
            m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.skipSyncFrequency();
            m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.skipDischargeSeverity();
        }
    }

    /************************************************
     * 函数名   : spectrumName
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : QString
     * 功能     : 图谱名称
     ************************************************/
    QString AEPhaseSpectrum::spectrumName() const
    {
        return STR_FILE_NODE_AE_PHASE;
    }

    /************************************************
     * 函数名   : setAEPhaseExtInformation
     * 输入参数 :
       const AEPhaseExtInformation& stAEPhaseExtInformation: AE相位ExtInformation
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置AE相位ExtInformation
     ************************************************/
    void AEPhaseSpectrum::setAEPhaseExtInformation(const AEPhaseExtInformation& stAEPhaseExtInformation)
    {
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAmpUnit = stAEPhaseExtInformation.eAmpUnit;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpLowerLimit = stAEPhaseExtInformation.fAmpLowerLimit;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpUpperLimit = stAEPhaseExtInformation.fAmpUpperLimit;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAESensorType = stAEPhaseExtInformation.eAESensorType;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint = stAEPhaseExtInformation.iDataPoint;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability.reserve(8);

        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability.clear();
        for (const quint8& ucDischargeTypeProb : stAEPhaseExtInformation.aucPDTypeProbability)
        {
            m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability.push_back(ucDischargeTypeProb);
        }

        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fTriggerThreshold = stAEPhaseExtInformation.fTriggerThreshold;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sOpenTime = stAEPhaseExtInformation.sOpenTime;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sShutTime = stAEPhaseExtInformation.sShutTime;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sMaxIntervalTime = stAEPhaseExtInformation.sMaxIntervalTime;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sGain = stAEPhaseExtInformation.sGain;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncSource = stAEPhaseExtInformation.eSyncSource;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncState = stAEPhaseExtInformation.ucSyncState;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fSyncFrequency = stAEPhaseExtInformation.fSyncFrequency;
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucDischargeSeverity = stAEPhaseExtInformation.eDischargeSeverity;
    }

    /************************************************
     * 函数名   : setAEPhaseData
     * 输入参数 :
       const AEPhaseData& stAEPhaseData: AE相位Data
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置AE相位Data
     ************************************************/
    void AEPhaseSpectrum::setAEPhaseData(const AEPhaseData& stAEPhaseData)
    {
        m_pAEPhaseSpectrumPrivate->m_stAEPhaseDataPrivate.qbaAEPhaseData = stAEPhaseData.qbaAEPhaseData;
    }

    /************************************************
     * 函数名   : getAEPhaseExtInformation
     * 输入参数 : NULL
     * 输出参数 :
       AEPhaseExtInformation& stAEPhaseExtInformation: AE相位ExtInformation
     * 返回值   : void
     * 功能     : 获取AE相位ExtInformation
     ************************************************/
    void AEPhaseSpectrum::getAEPhaseExtInformation(AEPhaseExtInformation &stAEPhaseExtInformation)
    {
        stAEPhaseExtInformation.eAmpUnit = static_cast<AmpUnit>(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAmpUnit);
        stAEPhaseExtInformation.fAmpLowerLimit = m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpLowerLimit;
        stAEPhaseExtInformation.fAmpUpperLimit = m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpUpperLimit;
        stAEPhaseExtInformation.eAESensorType = static_cast<AESensorType>(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAESensorType);
        stAEPhaseExtInformation.iDataPoint = m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint;

        std::copy(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability.begin(), m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability.end(), stAEPhaseExtInformation.aucPDTypeProbability);

        stAEPhaseExtInformation.fTriggerThreshold = m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fTriggerThreshold;
        stAEPhaseExtInformation.sOpenTime = m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sOpenTime;
        stAEPhaseExtInformation.sShutTime = m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sShutTime;
        stAEPhaseExtInformation.sMaxIntervalTime = m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sMaxIntervalTime;
        stAEPhaseExtInformation.sGain = m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sGain;
        stAEPhaseExtInformation.eSyncSource = static_cast<SyncSource>(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncSource);
        stAEPhaseExtInformation.ucSyncState = m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncState;
        stAEPhaseExtInformation.fSyncFrequency = m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fSyncFrequency;
        stAEPhaseExtInformation.eDischargeSeverity = static_cast<DischargeSeverity>(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucDischargeSeverity);
    }

    /************************************************
     * 函数名   : setAEPhaseData
     * 输入参数 : NULL
     * 输出参数 :
       AEPhaseData& stAEPhaseData: AE相位Data
     * 返回值   : void
     * 功能     : 获取AE相位Data
     ************************************************/
    void AEPhaseSpectrum::getAEPhaseData(AEPhaseData& stAEPhaseData)
    {
        stAEPhaseData.qbaAEPhaseData = m_pAEPhaseSpectrumPrivate->m_stAEPhaseDataPrivate.qbaAEPhaseData;
    }

    /************************************************
     * 函数名   : saveBinarySpectrumExtInfo
     * 输入参数 :
       QDataStream& out: 输出流
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存二进制可扩展信息段
     ************************************************/
    bool AEPhaseSpectrum::saveBinarySpectrumExtInfo(QDataStream& out)
    {
        out.device()->seek(SPECTRUM_HEADER_LENGTH);

        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpUnit) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAmpUnit;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpLowerLimit) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpLowerLimit;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpUpperLimit) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpUpperLimit;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAESensorType) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAESensorType;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipDataPoint) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint;

        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipPDTypeProbability)
        {
            for (const quint8& uPDType : m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability)
            {
                out << uPDType;
            }
        }

        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipTriggerThreshold) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fTriggerThreshold;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipOpenTime) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sOpenTime;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipShutTime) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sShutTime;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipMaxIntervalTime) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sMaxIntervalTime;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipGain) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sGain;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncSource) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncSource;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncState) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncState;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncFrequency) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fSyncFrequency;
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipDischargeSeverity) out << m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucDischargeSeverity;

        DataSpecificationUtils::saveBinaryReservedData(SPECTRUM_BASE_LENGTH - out.device()->size(), out);

        return true;
    }

    /************************************************
     * 函数名   : saveXMLSpectrumExtInfo
     * 输入参数 :
       XMLDocument& xmlDocumentObj: xml文档对象
       QDomElement& element: dom元素
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存XML可扩展信息段
     ************************************************/
    bool AEPhaseSpectrum::saveXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj, QDomElement& element)
    {
        xmlDocumentObj.beginElement(element);
        xmlDocumentObj.beginElement(STR_SPECTRUM_NODE_EXTINFORMATION);

        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpUnit) xmlDocumentObj.setValue(TEXT_AMP_UNIT, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAmpUnit));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpLowerLimit) xmlDocumentObj.setValue(TEXT_AMP_MIN, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpLowerLimit, 'f', 1));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpUpperLimit) xmlDocumentObj.setValue(TEXT_AMP_MAX, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpUpperLimit, 'f', 1));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAESensorType) xmlDocumentObj.setValue(TEXT_SENSOR_TYPE, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAESensorType));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipDataPoint) xmlDocumentObj.setValue(TEXT_DATA_POINT, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipPDTypeProbability) xmlDocumentObj.setValue(TEXT_PD_TYPE_PR, DataSpecificationUtils::dischargeTypeProbabilityVector2QString(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipTriggerThreshold) xmlDocumentObj.setValue(TEXT_TRIGGER_THRESHOLD, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fTriggerThreshold, 'f', 1));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipOpenTime) xmlDocumentObj.setValue(TEXT_OPEN_TIME, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sOpenTime));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipShutTime) xmlDocumentObj.setValue(TEXT_SHUT_TIME, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sShutTime));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipMaxIntervalTime) xmlDocumentObj.setValue(TEXT_MAX_TIME_INTERVAL, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sMaxIntervalTime));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipGain) xmlDocumentObj.setValue(TEXT_GAIN, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sGain));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncSource) xmlDocumentObj.setValue(TEXT_SYNC_SOURCE, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncSource));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncState) xmlDocumentObj.setValue(TEXT_SYNC_STATE, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncState));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncFrequency) xmlDocumentObj.setValue(TEXT_SYNC_FREQ, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fSyncFrequency, 'f', 1));
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipDischargeSeverity) xmlDocumentObj.setValue(TEXT_DISCHARGE_SEVERITY, QString::number(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucDischargeSeverity));

        return true;
    }

    /************************************************
     * 函数名   : saveJSONSpectrumExtInfo
     * 输入参数 :
       rapidjson::Document::AllocatorType& alloc:
       rapidjson::Value& jsonValue: json数据
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存JSON可扩展信息段
     ************************************************/
    bool AEPhaseSpectrum::saveJSONSpectrumExtInfo(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue)
    {
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpUnit) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAmpUnit, TEXT_AMP_UNIT, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpLowerLimit) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpLowerLimit, TEXT_AMP_MIN, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpUpperLimit) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpUpperLimit, TEXT_AMP_MAX, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAESensorType) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAESensorType, TEXT_SENSOR_TYPE, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipDataPoint) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint, TEXT_DATA_POINT, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipPDTypeProbability) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability, TEXT_PD_TYPE_PR, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipTriggerThreshold) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fTriggerThreshold, TEXT_TRIGGER_THRESHOLD, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipOpenTime) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sOpenTime, TEXT_OPEN_TIME, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipShutTime) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sShutTime, TEXT_SHUT_TIME, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipMaxIntervalTime) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sMaxIntervalTime, TEXT_MAX_TIME_INTERVAL, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipGain) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sGain, TEXT_GAIN, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncSource) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncSource, TEXT_SYNC_SOURCE, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncState) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncState, TEXT_SYNC_STATE, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncFrequency) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fSyncFrequency, TEXT_SYNC_FREQ, alloc, jsonValue);
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipDischargeSeverity) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucDischargeSeverity, TEXT_DISCHARGE_SEVERITY, alloc, jsonValue);

        return true;
    }

    /************************************************
     * 函数名   : saveBinarySpectrumData
     * 输入参数 :
       QDataStream& out: 输出流
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存二进制图谱数据段
     ************************************************/
    bool AEPhaseSpectrum::saveBinarySpectrumData(QDataStream& out)
    {
        if (m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint <= 0)
        {
            return false;
        }
        out.device()->seek(SPECTRUM_BASE_LENGTH);

        DataSpecificationUtils::saveBinaryPointerData(out, storageDataType(), m_pAEPhaseSpectrumPrivate->m_stAEPhaseDataPrivate.qbaAEPhaseData.data(), 2 * m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint);

        return true;
    }

    /************************************************
     * 函数名   : saveXMLSpectrumData
     * 输入参数 :
       XMLDocument& xmlDocumentObj: xml文档对象
       QDomElement& element: dom元素
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存XML图谱数据段
     ************************************************/
    bool AEPhaseSpectrum::saveXMLSpectrumData(XMLDocument& xmlDocumentObj, QDomElement& element)
    {
        xmlDocumentObj.beginElement(element);
        xmlDocumentObj.beginElement(STR_SPECTRUM_NODE_DATA);

        QString strData = m_pAEPhaseSpectrumPrivate->m_stAEPhaseDataPrivate.qbaAEPhaseData.toBase64();
        xmlDocumentObj.setValue(STR_SPECTRUM_NODE_DATA, strData);

        return true;
    }

    /************************************************
     * 函数名   : saveJSONSpectrumData
     * 输入参数 :
       rapidjson::Document::AllocatorType& alloc:
       rapidjson::Value& jsonValue: json数据
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存JSON图谱数据段
     ************************************************/
    bool AEPhaseSpectrum::saveJSONSpectrumData(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue)
    {
        if (!m_pAEPhaseSpectrumPrivate->m_stAEPhaseDataPrivate.bSkipAEPhaseSpectrumData) saveJSONField(m_pAEPhaseSpectrumPrivate->m_stAEPhaseDataPrivate.qbaAEPhaseData, STR_SPECTRUM_NODE_DATA, alloc, jsonValue);
        return true;
    }

    /************************************************
     * 函数名   : parseBinarySpectrumExtInfo
     * 输入参数 :
       QDataStream& in: 输入流
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析二进制可扩展信息段
     ************************************************/
    bool AEPhaseSpectrum::parseBinarySpectrumExtInfo(QDataStream& in)
    {
        if (!in.device()->seek(SPECTRUM_HEADER_LENGTH))
        {
            qDebug() << "AEPhaseSpectrum::parseBinarySpectrumExtInfo failed";
            return false;
        }

        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpUnit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAmpUnit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAmpUnit);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpLowerLimit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpLowerLimit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAmpLowerLimit);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAmpUpperLimit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpUpperLimit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAmpUpperLimit);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipAESensorType, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAESensorType, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAESensorType);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipDataPoint, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasDataPoint);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipPDTypeProbability, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability, 8, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasPDTypeProbability);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipTriggerThreshold, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fTriggerThreshold, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasTriggerThreshold);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipOpenTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sOpenTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasOpenTime);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipShutTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sShutTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasShutTime);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipMaxIntervalTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sMaxIntervalTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasMaxIntervalTime);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipGain, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sGain, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasGain);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncSource, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncSource, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasSyncSource);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncState, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncState, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasSyncState);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipSyncFrequency, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fSyncFrequency, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasSyncFrequency);
        parseBinaryField(in, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bSkipDischargeSeverity, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucDischargeSeverity, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasDischargeSeverity);

        return in.status() == QDataStream::Ok;
    }

    /************************************************
     * 函数名   : parseXMLSpectrumExtInfo
     * 输入参数 :
       XMLDocument& xmlDocumentObj: xml文档对象
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析XML可扩展信息段
     ************************************************/
    bool AEPhaseSpectrum::parseXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj)
    {
        xmlDocumentObj.beginElement(spectrumName());
        xmlDocumentObj.beginElement(STR_SPECTRUM_NODE_EXTINFORMATION);

        parseXMLField(xmlDocumentObj, TEXT_AMP_UNIT, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAmpUnit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAmpUnit);
        parseXMLField(xmlDocumentObj, TEXT_AMP_MIN, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpLowerLimit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAmpLowerLimit);
        parseXMLField(xmlDocumentObj, TEXT_AMP_MAX, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpUpperLimit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAmpUpperLimit);
        parseXMLField(xmlDocumentObj, TEXT_SENSOR_TYPE, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAESensorType, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAESensorType);
        parseXMLField(xmlDocumentObj, TEXT_DATA_POINT, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasDataPoint);
        parseXMLField(xmlDocumentObj, TEXT_PD_TYPE_PR, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasPDTypeProbability);
        parseXMLField(xmlDocumentObj, TEXT_TRIGGER_THRESHOLD, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fTriggerThreshold, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasTriggerThreshold);
        parseXMLField(xmlDocumentObj, TEXT_OPEN_TIME, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sOpenTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasOpenTime);
        parseXMLField(xmlDocumentObj, TEXT_SHUT_TIME, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sShutTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasShutTime);
        parseXMLField(xmlDocumentObj, TEXT_MAX_TIME_INTERVAL, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sMaxIntervalTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasMaxIntervalTime);
        parseXMLField(xmlDocumentObj, TEXT_GAIN, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sGain, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasGain);
        parseXMLField(xmlDocumentObj, TEXT_SYNC_SOURCE, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncSource, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasSyncSource);
        parseXMLField(xmlDocumentObj, TEXT_SYNC_STATE, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncState, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasSyncState);
        parseXMLField(xmlDocumentObj, TEXT_SYNC_FREQ, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fSyncFrequency, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasSyncFrequency);
        parseXMLField(xmlDocumentObj, TEXT_DISCHARGE_SEVERITY, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucDischargeSeverity, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasDischargeSeverity);

        xmlDocumentObj.endElement();
        xmlDocumentObj.endElement();

        return true;
    }

    /************************************************
     * 函数名   : parseJSONSpectrumExtInfo
     * 输入参数 :
       const rapidjson::Value& jsonValue: json数据
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析JSON可扩展信息段
     ************************************************/
    bool AEPhaseSpectrum::parseJSONSpectrumExtInfo(const rapidjson::Value& jsonValue)
    {
        parseJSONField(jsonValue, TEXT_AMP_UNIT, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAmpUnit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAmpUnit);
        parseJSONField(jsonValue, TEXT_AMP_MIN, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpLowerLimit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAmpLowerLimit);
        parseJSONField(jsonValue, TEXT_AMP_MAX, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fAmpUpperLimit, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAmpUpperLimit);
        parseJSONField(jsonValue, TEXT_SENSOR_TYPE, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucAESensorType, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasAESensorType);
        parseJSONField(jsonValue, TEXT_DATA_POINT, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasDataPoint);
        parseJSONField(jsonValue, TEXT_PD_TYPE_PR, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.vecPDTypeProbability, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasPDTypeProbability);
        parseJSONField(jsonValue, TEXT_TRIGGER_THRESHOLD, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fTriggerThreshold, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasTriggerThreshold);
        parseJSONField(jsonValue, TEXT_OPEN_TIME, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sOpenTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasOpenTime);
        parseJSONField(jsonValue, TEXT_SHUT_TIME, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sShutTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasShutTime);
        parseJSONField(jsonValue, TEXT_MAX_TIME_INTERVAL, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sMaxIntervalTime, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasMaxIntervalTime);
        parseJSONField(jsonValue, TEXT_GAIN, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.sGain, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasGain);
        parseJSONField(jsonValue, TEXT_SYNC_SOURCE, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncSource, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasSyncSource);
        parseJSONField(jsonValue, TEXT_SYNC_STATE, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucSyncState, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasSyncState);
        parseJSONField(jsonValue, TEXT_SYNC_FREQ, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.fSyncFrequency, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasSyncFrequency);
        parseJSONField(jsonValue, TEXT_DISCHARGE_SEVERITY, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.ucDischargeSeverity, m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.bHasDischargeSeverity);

        return true;
    }

    /************************************************
     * 函数名   : parseBinarySpectrumData
     * 输入参数 :
       QDataStream& in: 输入流
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析二进制图谱数据段
     ************************************************/
    bool AEPhaseSpectrum::parseBinarySpectrumData(QDataStream& in)
    {
        if (!in.device()->seek(SPECTRUM_BASE_LENGTH))
        {
            qDebug() << "AEPhaseSpectrum::parseBinarySpectrumData failed";
            return false;
        }

        DataSpecificationUtils::parseBinaryPointerData(in, storageDataType(), m_pAEPhaseSpectrumPrivate->m_stAEPhaseDataPrivate.qbaAEPhaseData, 2 * m_pAEPhaseSpectrumPrivate->m_stAEPhaseExtInformationPrivate.iDataPoint);

        return in.status() == QDataStream::Ok;
    }

    /************************************************
     * 函数名   : parseXMLSpectrumData
     * 输入参数 :
       XMLDocument& xmlDocumentObj: xml文档对象
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析XML图谱数据段
     ************************************************/
    bool AEPhaseSpectrum::parseXMLSpectrumData(XMLDocument& xmlDocumentObj)
    {
        xmlDocumentObj.beginElement(spectrumName());
        xmlDocumentObj.beginElement(STR_SPECTRUM_NODE_DATA);

        m_pAEPhaseSpectrumPrivate->m_stAEPhaseDataPrivate.qbaAEPhaseData = QByteArray::fromBase64(xmlDocumentObj.value(STR_SPECTRUM_NODE_DATA).toLatin1());

        xmlDocumentObj.endElement();
        xmlDocumentObj.endElement();

        return true;
    }

    /************************************************
     * 函数名   : parseJSONSpectrumData
     * 输入参数 :
       const rapidjson::Value& jsonValue: json数据
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析JSON图谱数据段
     ************************************************/
    bool AEPhaseSpectrum::parseJSONSpectrumData(const rapidjson::Value& jsonValue)
    {
        parseJSONField(jsonValue, STR_SPECTRUM_NODE_DATA, m_pAEPhaseSpectrumPrivate->m_stAEPhaseDataPrivate.qbaAEPhaseData, m_pAEPhaseSpectrumPrivate->m_stAEPhaseDataPrivate.bHasAEPhaseSpectrumData);

        return true;
    }

}
