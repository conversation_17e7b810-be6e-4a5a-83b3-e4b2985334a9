#include "infraredtask.h"
#include "infrared/infraredservice.h"
#include "infrared/infrareddatamap.h"
#include "gigecamera.h"
#include "bmp.h"
#include "imageconversion.h"
#include "Module.h"
static qint8 s_acBmpBuff[0x100000];
InfraredTask::InfraredTask(QObject *parent) : AbstractSpectrumTask(parent)
{
    m_pService = InfraredService::instance();
}

InfraredTask::~InfraredTask()
{

}

/*************************************************
功能： 开始进行对应图谱的采集 异步接口
输入参数: NULL
输出参数：NULL
返回值： NULL
*************************************************/
void InfraredTask::startSample()
{
    //注册用户
    MultiServiceNS::SampleUser userInfo;
    userInfo.eSampleType = MultiServiceNS::SPECTRUM_INFRARED;
    m_userId = m_pService->addUser( userInfo );
    connect( m_pService, SIGNAL(sigData(QSharedPointer<Infrared::InfraredData>,MultiServiceNS::USERID)),
             this, SLOT(onDataRead(QSharedPointer<Infrared::InfraredData>,MultiServiceNS::USERID)) );
    connect( m_pService, SIGNAL(sigReadDataFail(MultiServiceNS::USERID)),
             this, SLOT(onReadDataFail()) );
    connect( m_pService, SIGNAL(sigInfraredInitResult(bool)),
             this, SLOT(onCameraInitFail(bool)) );
    m_pService->start(  );
    m_pService->startSample( m_userId );
}

//#ifdef Q_PROCESSOR_ARM      // ubuntu下需要注释
/*************************************************
函数名： onData(Infrared::InfraredData stData)
输入参数： data：红外数据
         id -- 用户
输出参数： NULL
返回值： NULL
功能： 响应红外数据信号
*************************************************************/
void InfraredTask::onDataRead(QSharedPointer<Infrared::InfraredData> qspInfraredData, MultiServiceNS::USERID id)
{

    if( m_userId == id )
    {
        m_stData = *qspInfraredData;
        memcpy(m_stData.aucData, qspInfraredData->aucData, FRAME_BUF_SIZE);
        memcpy(&m_stData.stFrameInfo, &qspInfraredData->stFrameInfo, sizeof(FrameInfo));
#ifdef Q_PROCESSOR_ARM
        get_gigecamera_params(&m_stParams);
#endif
        fulfillTask();
    }
}

//#endif

void InfraredTask::onReadDataFail(  )
{
    //if( m_userId == id )
    {
        m_stData.stFrameInfo.size_x = 0;
        m_stData.stFrameInfo.size_y = 0;
        m_stData.stFrameInfo.frame_sz = 0;
#ifdef Q_PROCESSOR_ARM
        get_gigecamera_params(&m_stParams);
#endif
        fulfillTask();
    }
}

void InfraredTask::onCameraInitFail( bool bRet )
{
    if( !bRet )
    {
        m_stData.stFrameInfo.size_x = 0;
        m_stData.stFrameInfo.size_y = 0;
        m_stData.stFrameInfo.frame_sz = 0;
#ifdef Q_PROCESSOR_ARM
        get_gigecamera_params(&m_stParams);
#endif
        fulfillTask();
    }
    else
    {
        m_pService->startReadData();
    }
}

void InfraredTask::fulfillTask()
{
    //停止采样
    m_pService->stopSample( m_userId );

    saveMapData();
}

/*************************************************
功能： 保存图谱数据到保存结构体中
输入参数: NULL
输出参数：NULL
返回值： NULL
*************************************************/
void InfraredTask::saveMapData()
{
    //创建图谱保存对象
    InfraredDataMap * pMap = new InfraredDataMap;

    //设置头部信息
    pMap->setSpectrumProperty(DataFileNS::PROPERTY_TEST);

    //设置ext信息
    fillInfraredInfo( pMap );
    //设置数据内容
    fillInfraredData( pMap );

    emit sigSampleFinished( pMap );
}

/*************************************************
功能： 保存图谱信息部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void InfraredTask::fillInfraredInfo( InfraredDataMap * pMap )
{
    pMap->setDataPrimitiveType( DataFileNS::DATA_TYPE_FLOAT );
    pMap->setTestChannelSign( 1 );

    InfraredMapNS::InfraredMapInfo mapInfo;
    //mapInfo.eTempUnit = DataFileNS::AMP_UNIT_Degree;           //温度单位 AMP_UNIT_Degree
    //mapInfo.iLatticeWidth = m_stData.stFrameInfo.size_x; //点阵宽度
    //mapInfo.iLatticeHeight = m_stData.stFrameInfo.size_y; //点阵高度
    mapInfo.iWidth = m_stData.stFrameInfo.size_x; //点阵宽度
    mapInfo.iHeight = m_stData.stFrameInfo.size_y; //点阵高度
    mapInfo.iVisibleLightLenL1 = 0; //可见光照片数据长度L1
    mapInfo.iInfraredDataLenL2 = 0; //红外照片数据长度L2

    createInfraredData();
    mapInfo.iInfraredDataLenL2 = m_iInfraredDataLen;

    mapInfo.fRadiance = (float)m_stParams.stObj.dblEmissivity; //辐射率
    mapInfo.fMeasureDistance = (float)m_stParams.stObj.dblObjDistance; //测试距离
    mapInfo.fAtomTemperature = (float)m_stParams.stObj.dblAtmTemp; //大气温度
    mapInfo.cRelatedHumidity = (qint8)(m_stParams.stObj.dblRelHum * 100); //相对湿度
    mapInfo.fReflexTemperature = (float)m_stParams.stObj.dblAmbTemp; //反射温度

    pMap->setInfo( &mapInfo );
}

bool InfraredTask::createInfraredData()
{
    //memset( m_acGigeCameraBuff, 0, TEMPERATURE_BUFF_SIZE );
    m_iInfraredDataLen = 0;

    memset( s_acBmpBuff, 0, 0x100000 );
    int iBmpLen = create_ir_bmp( (char*)s_acBmpBuff, m_stData.stFrameInfo.size_x,
                                 m_stData.stFrameInfo.size_y, (unsigned char*)m_stData.aucData );

    //TEST
    QString bmpFile = "/home/<USER>/test.bmp";
    QFile fileBMP( bmpFile );
    if(fileBMP.open(QFile::ReadWrite))
    {
        fileBMP.resize(0);

        fileBMP.write( (char*)s_acBmpBuff, iBmpLen );
        fileBMP.flush();
        fileBMP.close();
    }
    else
    {
        qDebug() << "!!!!!!!!!!!!!!open file failed";
    }
    //end test

    memset( m_acGigeCameraBuff, 0, TEMPERATURE_BUFF_SIZE );
    unsigned int iJpgLen = 0;
#ifdef Q_PROCESSOR_ARM
    convert_bmp_image((char *)m_acGigeCameraBuff , &iJpgLen, (unsigned char*)s_acBmpBuff, iBmpLen, "JPG");

    //TEST
    QString jpgFilePath = "/home/<USER>/test.jpg";
    QFile fileJpg( jpgFilePath );
    if(fileJpg.open(QFile::ReadWrite))
    {
        fileJpg.resize(0);

        fileJpg.write( (char*)m_acGigeCameraBuff, iJpgLen );
        fileJpg.flush();
        fileJpg.close();
    }
    else
    {
        qDebug() << "!!!!!!!!!!!!!!open file failed";
    }
    //end test
#endif

    m_iInfraredDataLen = iJpgLen;
    return true;
}

/*************************************************
功能： 保存图谱数据部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void InfraredTask::fillInfraredData( InfraredDataMap *pMap )
{
    InfraredMapNS::InfraredMapData tmpData;
    tmpData.iTempDataLen = m_stData.stFrameInfo.size_x * m_stData.stFrameInfo.size_y;
    UINT16 *p = (UINT16*) m_stData.aucData;
    float * pfData = new float[tmpData.iTempDataLen];
    memset( pfData, 0, sizeof(float) * tmpData.iTempDataLen );

    for( int i = 0; i < tmpData.iTempDataLen; i++ )
    {
#ifdef Q_PROCESSOR_ARM
        pfData[i] = tau_to_temp( p[i], &m_stParams );
        pfData[i] = Module::dealFloatPrecision(pfData[i], 1); // 保留一位小数
#endif
    }
    tmpData.pTemperatureData = pfData;

    tmpData.pucRawData = (quint8 *)m_acGigeCameraBuff;

    pMap->setData( &tmpData );

    delete[] pfData;
}
