﻿#include "downloadcontroller.h"
#include "time.h"
#include "appserverutils.h"
#include "../appserverdefine.h"
#include "pda/cloud/qjason/qjson.h"
#include "pda/pda.h"
#include "globalerrprocess.h"
#include "pda/taskfileio.h"
#include "log/log.h"


using namespace errorProcess;
using namespace AppServerNS;

DownloadController::DownloadController(QObject *parent) : HttpRequestHandler(parent)
{

}

DownloadController::~DownloadController()
{

}

void DownloadController::service( HttpRequest& request, HttpResponse& response )
{
    int responseCode = REPLY_SUCCESS_CODE;

    /*
    //解析request判断有效性
    long requestTime = request.getParameter( TIME_STAMP_KEY ).toLong();
    if( !AppServerUtils::isTimestampValid( requestTime ) )
    {
        responseCode = SIGNATURE_INVALID_ERR;
    }
    */


    //读取文件内容
    QByteArray fileContent;//文件块内容
    fileContent.clear();

    int totalSize = 1;//文件总块数
    int index = 1;//不传则默认为1
    if( REPLY_SUCCESS_CODE == responseCode )
    {
        //获取文件路径和分块索引
        QString filePath = HttpRequest::urlDecode( request.getParameter( FILE_PATH_KEY ) );
        QByteArray indexStr = request.getParameter( FILE_BLOCK_INDEX_KEY );
        if( !indexStr.isEmpty() )
        {
            index = indexStr.toInt();
        }

        logInfo(QString("download file (%1), index: %2.").arg(filePath).arg(index));
        //modify by Mountains
        if( QFileInfo(filePath).suffix() == QString("t13") )
        {
            //上传删除remote节点的临时文件
            QString strTmpFile = "";
            TaskFileIO tmpFileIO;
            tmpFileIO.createTmpAPPTaskFilePath(filePath, strTmpFile);

            //获取文件内容
            if( !AppServerUtils::getFileBlockContent( strTmpFile, index, fileContent, totalSize ) )
            {
                responseCode = FILE_OP_ERROR;
            }

            //上传后删除临时文件
            QFile file(strTmpFile);
            if(file.exists())
            {
                file.setPermissions(QFile::WriteOther);
                file.remove();
            }
        }
        else
        {
            //获取文件内容
            if( !AppServerUtils::getFileBlockContent( filePath, index, fileContent, totalSize ) )
            {
                responseCode = FILE_OP_ERROR;
            }
        }
    }

    //生成响应头 注意：参数在应答报头中，文件内容在应答主体中
    response.setHeader( REPLY_CODE_KEY, responseCode );
    response.setHeader( REPLY_MSG_KEY, AppServerUtils::stateMsgByCode( responseCode ).toUtf8() );
    if( REPLY_SUCCESS_CODE == responseCode )
    {
        QJson resultJson;
        resultJson.add( BLOCK_INDEX_KEY, QString::number( index ).toLatin1() );
        resultJson.add( TOTAL_BLOCKS_KEY, QString::number( totalSize ).toLatin1() );
        resultJson.add( BLOCK_MD5_KEY, AppServerUtils::calcMd5Value( fileContent ) );
        response.setHeader( REPLY_RESULT_KEY, resultJson.unformattedData() );
        response.write( fileContent, true );
    }

    return;
}
