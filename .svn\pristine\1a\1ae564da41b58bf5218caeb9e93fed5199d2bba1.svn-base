#include "tevpulsepdaview.h"
#include "window/Window.h"
#include "deleteDataView/DeleteDataView.h"
#include "tevpulseplaybackview.h"
#include "playbackView/PlayBackView.h"
#include "messageBox/msgbox.h"
#include "dataSave/DataFileInfos.h"
#include "controlButton/SliderButton.h"
#include "pda/pdaservice.h"
#include "tev/tevconfig.h"
#include "tevviewdefine.h"
#include "tev/dataSave/TEVPulseDataMapSave.h"
#include "diagnosismgr/diagnosismanager.h"
#include "log/log.h"


typedef enum _TEVPulseButton
{
    BUTTON_TEV_SAVE_DATA = 0,
    BUTTON_TEV_RED_ALERT,
    BUTTON_ADD_TEST_DATA,
    BUTTON_TEV_MORE,

    BUTTON_TEV_PULSE_LEN,
    BUTTON_TEV_YELLOW_ALERT,
    BUTTON_TEV_RESTORE_DEFAULT
}TEVPulseButton;

//脉冲时长
const ButtonInfo::RadioValueConfig s_TEVPulseLen =
{
    TEV::TEXT_PULSE_LEN_OPTIONS, sizeof(TEV::TEXT_PULSE_LEN_OPTIONS) / sizeof(char*)
};

//黄色报警
const ButtonInfo::SliderValueConfig s_TEVYellowAlertCfg =
{
    TEV::YELLOW_ALERT_MIN, TEV::YELLOW_ALERT_MAX, TEV::ALERT_STEP
};

//红色报警
const ButtonInfo::SliderValueConfig s_TEVRedAlertCfg =
{
    TEV::RED_ALERT_MIN, TEV::RED_ALERT_MAX, TEV::ALERT_STEP
};

//控制按钮定义
const ButtonInfo::Info s_TEVButtonInfo[] =
{
    {BUTTON_TEV_SAVE_DATA, {ButtonInfo::COMMAND, TEV::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL}},
    {BUTTON_TEV_RED_ALERT, {ButtonInfo::FIXED_STEP_SLIDER, TEV::TEXT_RED_ALERT, TEV::TEXT_DB, ":/images/sampleControl/alert.png", &s_TEVRedAlertCfg}},
    {BUTTON_ADD_TEST_DATA, { ButtonInfo::COMMAND, TEV::TEXT_ADD, NULL, ":/images/sampleControl/noiseClean.png", NULL}},
    {BUTTON_TEV_MORE, {ButtonInfo::COMMAND, TEV::TEXT_MORE, NULL, ":/images/sampleControl/moreconfig.png", NULL}}
};

//"更多.."按钮定义
const ButtonInfo::Info s_TEVButtonInfoMore[] =
{
    {BUTTON_TEV_PULSE_LEN, {ButtonInfo::RADIO, TEV::TEXT_PULSE_LEN, NULL, ":/images/sampleControl/pulseCountTimeLength.png", &s_TEVPulseLen}},
    {BUTTON_TEV_YELLOW_ALERT, {ButtonInfo::FIXED_STEP_SLIDER, TEV::TEXT_YELLOW_ALERT, TEV::TEXT_DB, ":/images/sampleControl/alert.png", &s_TEVYellowAlertCfg}},
    {BUTTON_TEV_RESTORE_DEFAULT, {ButtonInfo::COMMAND, TEV::TEXT_RESTORE_DEFAULT, NULL, ":/images/sampleControl/restoreDefault.png", NULL}}
};

const int TEV_CHART_HEIGHT = 510;
const int INVALID_USER = -1;

/*************************************************
函数名： TEVPulseView(const QString &strTitle, const ItemTestData &stTestDataInfo, QWidget *parent)
输入参数： strTitle：标题
          parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
TEVPulsePDAView::TEVPulsePDAView(const QString &strTitle, const ItemTestData &stTestDataInfo, QWidget *parent)
    : TEVPulseViewBase(strTitle, parent)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);

    m_qsTestDataFilePath = stTestDataInfo.strFilePath;
    m_stTestData = stTestDataInfo;

    m_ucYellowAlert = 0;
    m_ucRedAlert = 0;
    m_ucPulseLen = 0;

    m_pConfig = ConfigManager::instance()->config();

    //新建图谱
    ChartWidget *pChartWidget = createChart(parent);
    setChart(pChartWidget);

    //创建按钮栏
    PushButtonBar *pBtnBar = createButtonBar(TEV::CONTEXT, s_TEVButtonInfo, sizeof(s_TEVButtonInfo) / sizeof(ButtonInfo::Info));

    // 创建更多设置菜单栏
    createMoreConfigButtonBar(TEV::CONTEXT, s_TEVButtonInfoMore, sizeof(s_TEVButtonInfoMore) / sizeof(ButtonInfo::Info));

    //初始化数据
    initDatas();

    //重置报警值范围
    resetAlarmScope();

    //设置按钮数据
    setButtonDatas();

    //设置图谱数据
    setChartDatas();

    //设置工作参数
    setWorksets();

    //显示上次的测试数据
    if(m_stTestData.bTested)
    {
        buttonBar()->hide();
        readTestedData(m_qsTestDataFilePath);
        QTimer::singleShot(START_SAMPLE_TIME_INTERVAL_2000MS, this, SLOT(onRecoverSampleView()));
    }
    else
    {
        recoverSample();
    }
}

/*************************************************
函数名： ~TEVPulseView()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
TEVPulsePDAView::~TEVPulsePDAView()
{
    saveConfig();
    stopSampleService();
}

/************************************************
 * 函数名   : createChart
 * 输入参数 : parent---父控件
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 创建图谱
 ************************************************/
ChartWidget* TEVPulsePDAView::createChart(QWidget *parent)
{
    Q_UNUSED(parent);

    ChartWidget *pWidget = new ChartWidget;
    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->setContentsMargins(0, 0, 0, 0);

    //站名
    m_pStationLabel = new QLabel;
    //设备名
    m_pDeviceLabel = new QLabel;
    //测点名
    m_pTestPointLabel = new QLabel;

    //设置style
    QFont font = m_pStationLabel->font();
    font.setPointSize(20);
    m_pStationLabel->setFont(font);
    m_pDeviceLabel->setFont(font);
    m_pTestPointLabel->setFont(font);

    m_pChart = new TEVPulseChart(TEV_CHART_HEIGHT);

    QVBoxLayout *pTestInfoLayout = new QVBoxLayout;
    pTestInfoLayout->addWidget(m_pStationLabel);
    pTestInfoLayout->addWidget(m_pDeviceLabel);
    pTestInfoLayout->addWidget(m_pTestPointLabel);

    vLayout->addLayout(pTestInfoLayout);
    vLayout->addWidget(m_pChart);
    pWidget->setLayout(vLayout);
    pWidget->setFixedWidth(Window::WIDTH);

    return pWidget;
}

/************************************************
 * 函数名   : setTestPointInfo
 * 输入参数 : stTestPointInfo---巡检测点相关信息
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 设置显示测点信息
 ************************************************/
void TEVPulsePDAView::setTestPointInfo(const View::PatrolTestPointInfo &stTestPointInfo)
{
    m_stTestPointInfo = stTestPointInfo;
    m_qsStation = stTestPointInfo.strStationName;
    m_qsDevice = stTestPointInfo.strDeviceName;

    QString qstrInfo = VIEW_TRANSLATE(View::TEXT_STATION_NAME) + m_qsStation;
    m_pStationLabel->setText(getRightEllipsisInfo(m_pStationLabel->font(), width(), qstrInfo));

    qstrInfo = VIEW_TRANSLATE(View::TEXT_DEVICE) + m_qsDevice;
    m_pDeviceLabel->setText(getRightEllipsisInfo(m_pDeviceLabel->font(), width(), qstrInfo));

    qstrInfo = VIEW_TRANSLATE(View::TEXT_TESTPOINT) + stTestPointInfo.strTestPointName;
    m_pTestPointLabel->setText(getRightEllipsisInfo(m_pTestPointLabel->font(), width(), qstrInfo));
}

/************************************************
 * 函数名   : onRecoverSampleView
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :槽函数，起动采样
 ************************************************/
void TEVPulsePDAView::onRecoverSampleView()
{
    buttonBar()->show();
    buttonBar()->setFocus();
    recoverSample();
    return;
}

/************************************************
 * 函数名   : recoverSample
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :起动采样
 ************************************************/
void TEVPulsePDAView::recoverSample()
{
    //设置图谱数据
    setChartDatas();

    //设置最新的参数
    setWorksets();

    //启动采集
    m_pChart->clearDiagRet();
    m_pChart->setRunningStatus(true);
    startSampleService();
    if(!(buttonBar()->button(BUTTON_TEV_SAVE_DATA)->isEnabled()))
    {
        //开启采样之后，使能保存按钮
        buttonBar()->button(BUTTON_TEV_SAVE_DATA)->setEnabled(true);
        buttonBar()->button(BUTTON_TEV_SAVE_DATA)->setActive(true);
        setCurActiveBtnID(BUTTON_TEV_SAVE_DATA);
    }

    return;
}

/************************************************
 * 函数名   : readTestedData
 * 输入参数 : qsFile---数据文件存放路径
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :读取数据文件里的测试数据
 ************************************************/
void TEVPulsePDAView::readTestedData(const QString &qsFile)
{
    TEVPulseDataMapSave fDataSave;
    TEVPulseDataInfo stPlayBackDataInfo;

    if(HC_SUCCESS == fDataSave.getDataFromFile(qsFile, &stPlayBackDataInfo))
    {
        //回放时候还是回放当前值
        TEV::PulseData data;
        data.uiPulseNum = (UINT32)(stPlayBackDataInfo.iPulseCount);  //脉冲计数
        data.uiPerPulseNum = stPlayBackDataInfo.uiPulseCountPerPeriod;  //单周期脉冲数
        data.uiPDSeverity = (UINT32)(Module::dealFloatPrecision(stPlayBackDataInfo.fSeverity, 0));//放电严重程度
        data.cAmpValue = (INT8)(Module::dealFloatPrecision(stPlayBackDataInfo.fTEVAmp, 0));

        m_pChart->setAlarm((int)(stPlayBackDataInfo.fWarningValue), (int)(stPlayBackDataInfo.fAlarmingValue));
        m_pChart->addSample(data);
        m_pChart->setRunningStatus(false);

        if(m_stTestData.bIsBgn)
        {
            m_pChart->clearDiagRet();
            return;
        }

        QString qstrDesInfo = DiagnosisManager::instance()->getDiagInfoByPlayback(stPlayBackDataInfo.stHeadInfo.qstrPDSignalTypeInfos, DIAG_TEV);
        DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
        stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stPlayBackDataInfo.stHeadInfo.ePDDefectLevel);
        stDiagDisplayInfo.qstrPDDesInfo = qstrDesInfo;
        stDiagDisplayInfo.qstrPDSignalInfos = stPlayBackDataInfo.stHeadInfo.qstrPDSignalTypeInfos;
        m_pChart->playbackDiagInfo(stDiagDisplayInfo);
    }
    else
    {
        logError(QString("read file (%1) failed.").arg(qsFile).toLatin1().data());
    }

    return;
}

/*************************************************
函数名： initDatas()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 初始化数据
*************************************************************/
void TEVPulsePDAView::initDatas()
{
    m_pConfig->beginGroup(Module::GROUP_TEV);
    m_eSampleMode = Module::SAMPLEMODE_CONTINUOUS;
    m_ucYellowAlert = m_pConfig->value(TEV::KEY_YELLOW_ALERT).toUInt();
    m_ucRedAlert = m_pConfig->value(TEV::KEY_RED_ALERT).toUInt();
    m_ucPulseLen = m_pConfig->value(TEV::KEY_PULSE_LEN, TEV::GROUP_TEV_PULSE).toUInt();
    m_pConfig->endGroup();

    MultiServiceNS::SampleUser user;
    user.eSampleType = MultiServiceNS::SPECTRUM_TEV_PULSE;
    addUser( user );
}

/*************************************************
函数名： resetAlarmScope()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 重置报警值范围
*************************************************************/
void TEVPulsePDAView::resetAlarmScope()
{
    SliderButton *pYellowAlarmButton = (SliderButton*)(buttonBar()->button(BUTTON_TEV_YELLOW_ALERT));
    pYellowAlarmButton->setRange(TEV::YELLOW_ALERT_MIN, m_ucRedAlert - 1, TEV::ALERT_STEP);
    pYellowAlarmButton->setValue(m_ucYellowAlert);

    SliderButton *pRedAlarmButton = (SliderButton*)(buttonBar()->button(BUTTON_TEV_RED_ALERT));
    pRedAlarmButton->setRange(m_ucYellowAlert + 1, TEV::RED_ALERT_MAX, TEV::ALERT_STEP);
    pRedAlarmButton->setValue(m_ucRedAlert);
}

/*************************************************
函数名： setButtonDatas()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 设置按钮数据
*************************************************************/
void TEVPulsePDAView::setButtonDatas()
{
    ((PopupButton*)(buttonBar()->button(BUTTON_TEV_PULSE_LEN)))->setValue(m_ucPulseLen - 1);
    ((PopupButton*)(buttonBar()->button(BUTTON_TEV_YELLOW_ALERT)))->setValue(m_ucYellowAlert);
    ((PopupButton*)(buttonBar()->button(BUTTON_TEV_RED_ALERT)))->setValue(m_ucRedAlert);
}

/*************************************************
函数名： setChartDatas()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 设置图谱数据
*************************************************************/
void TEVPulsePDAView::setChartDatas()
{
    m_pChart->setSampleMode(m_eSampleMode);
    m_pChart->setAlarm(m_ucYellowAlert, m_ucRedAlert);
}

/*************************************************
函数名： setWorksets()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 设置工作参数
*************************************************************/
void TEVPulsePDAView::setWorksets()
{
    TEVWorkSettings stTEVWorkSettings;
    stTEVWorkSettings.ucWorkMode = (UINT8)TEV::MODE_PULSE;
    stTEVWorkSettings.ucTime = m_ucPulseLen;
    stTEVWorkSettings.ucBackGround = m_ucYellowAlert;
    stTEVWorkSettings.ucAlarm = m_ucRedAlert;

    if(TevPulseService* pService = getTevPulseService())
    {
        pService->setWorksets(stTEVWorkSettings);
    }
}

/*************************************************
函数名： saveConfig()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 保存配置信息
*************************************************************/
void TEVPulsePDAView::saveConfig()
{
    m_pConfig->beginGroup(Module::GROUP_TEV);
    m_pConfig->setValue(m_eSampleMode, TEV::KEY_SAMPLE_MODE, TEV::GROUP_TEV_PULSE);
    m_pConfig->setValue(m_ucYellowAlert, TEV::KEY_YELLOW_ALERT);
    m_pConfig->setValue(m_ucRedAlert, TEV::KEY_RED_ALERT);
    m_pConfig->setValue(m_ucPulseLen, TEV::KEY_PULSE_LEN, TEV::GROUP_TEV_PULSE);
    m_pConfig->endGroup();
    return;
}

void TEVPulsePDAView::showEvent(QShowEvent *event)
{
    Q_UNUSED(event);

    if(m_stTestData.bTested)
    {
        //回放数据时不允许点击保存按钮，开始采样之后才使能
        buttonBar()->button(BUTTON_TEV_SAVE_DATA)->setEnabled(false);
        buttonBar()->button(BUTTON_TEV_SAVE_DATA)->setActive(false);
    }

    if(m_stTestData.bIsBgn)
    {
        //背景测试中不允许新增
        buttonBar()->button(BUTTON_ADD_TEST_DATA)->setEnabled(false);
    }

    return;
}

/*************************************************
函数名： onSKeyPressed()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应S键事件
*************************************************************/
void TEVPulsePDAView::onSKeyPressed()
{
    saveTestData();
    return;
}

/*************************************************
函数名： onButtonValueChanged(int id, int iValue)
输入参数： id：按钮ID
          iValue：按钮值
输出参数： NULL
返回值： NULL
功能： 响应按钮值变化事件
*************************************************************/
void TEVPulsePDAView::onButtonValueChanged(int id, int iValue)
{
    switch (id)
    {
    case BUTTON_TEV_PULSE_LEN:
    {
        int iSize = sizeof(TEV::TEV_PULSE_LEN) / sizeof(int);
        iValue = (iValue < 0) ? 0 : iValue;
        iValue = (iValue >= iSize) ? iSize : iValue;
        int iLen = TEV::TEV_PULSE_LEN[iValue];
        if (iLen != m_ucPulseLen)
        {
            stopSampleService();
            m_ucPulseLen = iLen;
            setWorksets();
            startSampleService();
        }
        break;
    }
    case BUTTON_TEV_YELLOW_ALERT:
    {
        if (iValue != m_ucYellowAlert)
        {
            stopSampleService();

            m_ucYellowAlert = iValue;
            resetAlarmScope();
            m_pChart->setAlarm(m_ucYellowAlert, m_ucRedAlert);
            setWorksets();
            startSampleService();
        }
        break;
    }
    case BUTTON_TEV_RED_ALERT:
    {
        if (iValue != m_ucRedAlert)
        {
            stopSampleService();

            m_ucRedAlert = iValue;
            resetAlarmScope();
            m_pChart->setAlarm(m_ucYellowAlert, m_ucRedAlert);
            setWorksets();
            startSampleService();
        }
        break;
    }
    default:
        break;
    }
    return;
}

/*************************************************
函数名： onCommandButtonPressed(int id)
输入参数： id：按钮ID
输出参数： NULL
返回值： NULL
功能： 响应命令按钮按下事件
*************************************************************/
void TEVPulsePDAView::onCommandButtonPressed(int id)
{
    switch (id)
    {
    case BUTTON_TEV_SAVE_DATA:
    {
        saveTestData();
        break;
    }
    case BUTTON_TEV_MORE:
    {
        showMoreConfigButtonBar();
        break;
    }
    case BUTTON_TEV_RESTORE_DEFAULT:
    {
        m_pChart->setRunningStatus(false);
        stopSampleService();
        restoreDefault();
        m_pChart->setRunningStatus(true);
        startSampleService();
        break;
    }
    case BUTTON_ADD_TEST_DATA://新增测试项
    {
        addTestData();
        break;
    }
    default:
        break;
    }

    return;
}

/*************************************************
函数名： saveTestData
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 测试数据传给pdaservice保存
*************************************************************/
void TEVPulsePDAView::saveTestData()
{
    if(m_bPdaWaiting)
    {
        logInfo("saving data...");
        return;
    }
    m_bPdaWaiting = true;

    //停止采集
    stopSampleService();
    m_pChart->setRunningStatus(false);
    if(savePDAData(m_qsStation, m_qsDevice))
    {
        delayToClose();
    }
    else
    {
        m_bPdaWaiting = false;
        PushButtonBar* pBtnBar = buttonBar();
        QPoint centerPoint;
        centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
        centerPoint.setY(pBtnBar->pos().y()/*+pBtnBar->height()/2*/);

        startSampleService();
        m_pChart->setRunningStatus(true);
        MsgBox::information("", QObject::trUtf8("Save failure!"), centerPoint);
    }

    return;
}

/*************************************************
输入参数：
    stationName：站名
    deviceName：设备名
输出参数： NULL
返回值： NULL
功能：保存数据文件
*************************************************************/
bool TEVPulsePDAView::savePDAData(const QString &stationName, const QString &deviceName)
{
    Q_UNUSED(stationName);
    Q_UNUSED(deviceName);
    PDAService *pPDAService = PDAService::instance();
    PDATask *pTask = pPDAService->currentTask();
    APP_CHECK_RETURN_VAL(pTask, false);

    TEV::PulseData stData;

    if(m_pChart)
    {
        stData = m_pChart->getData();
    }

    // 背景测试不诊断
    if(!m_stTestData.bIsBgn)
    {
        diagDataInfo(stData, true);
    }

    TaskInfo stTaskInfo = pTask->taskInfo();

    TEVPulseDataInfo sSaveDataInfo;
    sSaveDataInfo.stHeadInfo.strSubstationName = m_stTestPointInfo.strStationName;
    sSaveDataInfo.stHeadInfo.strSubstationNumber = m_stTestPointInfo.strStationID;
    sSaveDataInfo.stHeadInfo.eWeather = static_cast<DataFileNS::Weather>(stTaskInfo.stWeatherInfo.getWeatherDataSave());
    sSaveDataInfo.stHeadInfo.fTemperature = stTaskInfo.stWeatherInfo.dTemperature;
    sSaveDataInfo.stHeadInfo.ucHumidity = stTaskInfo.stWeatherInfo.dHumidity;
    sSaveDataInfo.stHeadInfo.strDeviceName = m_stTestPointInfo.strDeviceName;
    sSaveDataInfo.stHeadInfo.strDeviceNumber = m_stTestPointInfo.strDeviceID;
    sSaveDataInfo.stHeadInfo.strTestPointName = m_stTestPointInfo.strTestPointName;
    sSaveDataInfo.stHeadInfo.strTestPointNumber = m_stTestPointInfo.strTestPointID;

    sSaveDataInfo.stHeadInfo.generationDateTime = QDateTime::currentDateTime();

    sSaveDataInfo.stHeadInfo.eCode = DataFileNS::SPECTRUM_CODE_TEV_PULSE;
    sSaveDataInfo.stHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;
    sSaveDataInfo.stHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;
    sSaveDataInfo.stHeadInfo.ePDDefectLevel = static_cast<DataFileNS::PDDefectLevel>(m_pChart->getPDDiagnosisRet());
    sSaveDataInfo.stHeadInfo.qstrPDSignalTypeInfos = m_pChart->getPDSignalInfos();

    sSaveDataInfo.fWarningValue = (float)m_ucYellowAlert;
    sSaveDataInfo.fAlarmingValue = (float)m_ucRedAlert;
    sSaveDataInfo.uiPulseTimeLength = m_ucPulseLen;

    sSaveDataInfo.fTEVAmp = static_cast<float>(stData.cAmpValue);
    sSaveDataInfo.uiPulseCountPerPeriod = stData.uiPerPulseNum;
    sSaveDataInfo.iPulseCount = static_cast<int>(stData.uiPulseNum);
    sSaveDataInfo.fSeverity = static_cast<float>(stData.uiPDSeverity);

    return pTask->saveTEVPulseData(sSaveDataInfo);
}

/*************************************************
函数名： restoreDefault()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 恢复默认
*************************************************************/
void TEVPulsePDAView::restoreDefault()
{
    if (MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to restore default settings?")))
    {
        m_pConfig->beginGroup(Module::GROUP_TEV);

        QVector<Config::GroupKey> totalKeys;
        totalKeys << Config::GroupKey(TEV::KEY_YELLOW_ALERT);
        totalKeys << Config::GroupKey(TEV::KEY_RED_ALERT);
        totalKeys << Config::GroupKey(TEV::KEY_PULSE_LEN, TEV::GROUP_TEV_PULSE);
        m_pConfig->restoreDefault(totalKeys);

        m_pConfig->endGroup();

        //初始化数据
        initDatas();

        //重置报警值范围
        resetAlarmScope();

        //设置按钮数据
        setButtonDatas();

        //设置图谱数据
        setChartDatas();

        //设置工作参数
        setWorksets();
    }
    return;
}

/*************************************************
函数名： onDataRead(TEV::PulseData data)
输入参数： data：幅值数据
输出参数： NULL
返回值： NULL
功能： 响应读取的数据
*************************************************************/
void TEVPulsePDAView::onDataRead(TEV::PulseData data, MultiServiceNS::USERID userId)
{
    if((getUserId() != INVALID_USER) && (getUserId() == userId) && isSampling())
    {
        m_pChart->addSample(data);

        if(m_stTestData.bIsBgn)
        {
            m_pChart->clearDiagRet();
            return;
        }

        diagDataInfo(data);
    }
    else
    {
        //illegal
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
    }

    return;
}

/*************************************************
功能： 弹出新增测试项窗口
*************************************************************/
void TEVPulsePDAView::addTestData()
{
    PDAServiceNS::TestPointType eType = PDAService::instance()->currentTask()->currentTestType()->eTestPointType;
    AddTestDataDialog *pDialog = new AddTestDataDialog(eType);
    pDialog->show();
    connect(pDialog, SIGNAL(sigAddingTestDataChanged(struct_AddingTestData&)), this, SLOT(onAddTestData(struct_AddingTestData&)));
    return;
}

/************************************************
 * 函数名   : onAddTestData
 * 输入参数 :  struct_AddingTestData&
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :槽函数，将新增测试项界面的信息写入pdaTask中
 ************************************************/
void TEVPulsePDAView::onAddTestData( struct_AddingTestData &stAddingTestData)
{
    for(int i = 0, iSize = stAddingTestData.evType.size(); i < iSize; ++i)
    {
        ItemTestData stTestData;
        stTestData.bIsBgn = m_stTestData.bIsBgn;
        stTestData.eDataType = stAddingTestData.evType.at(i);
        stTestData.eDataUnit = m_stTestData.eDataUnit;

        PDAService::instance()->currentTask()->addTestData(stTestData);
    }

    return;
}

/*************************************************
 * 功能：诊断数据
 * 输入参数：
 *      data：待诊断的数据
 *      bSave：是否为保存操作的逻辑，缺省为false
 * ***********************************************/
void TEVPulsePDAView::diagDataInfo(const TEV::PulseData &data, bool bSave)
{
    if(SystemSet::RT_DIAG_ON != SystemSetService::instance()->getRealtimeDiagnosisSwitch())
    {
        return;
    }

    if(bSave)
    {
        //边界处理
        qint8 quiAmpValue = data.cAmpValue;
        quiAmpValue = (TEV::cMinAmpValue > quiAmpValue) ? TEV::cMinAmpValue : quiAmpValue;
        quiAmpValue = (TEV::cMaxAmpValue < quiAmpValue) ? TEV::cMaxAmpValue : quiAmpValue;

        TEVPulseDiagInfo stDiagInfo;
        stDiagInfo.qui8TevAmpVal = static_cast<quint8>(quiAmpValue);
        stDiagInfo.qui32PerPulseCnt = data.uiPerPulseNum;
        stDiagInfo.qui32SysFrequency = SystemSetService::instance()->getFrequency();
        stDiagInfo.stTHInfo.ucThresholdMinor = m_ucYellowAlert;
        stDiagInfo.stTHInfo.ucThresholdSerious = m_ucYellowAlert + 15;
        stDiagInfo.stTHInfo.ucThresholdEmergency = m_ucYellowAlert + 30;

        DiagResultInfo stDiagRetInfo;

        DiagnosisManager::instance()->diagTEVPulseByThreshold(stDiagInfo, stDiagRetInfo);

        DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
        stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stDiagRetInfo.stDiagRet.defectLevel);
        stDiagDisplayInfo.qstrPDDesInfo = stDiagRetInfo.qstrPDDescription;
        stDiagDisplayInfo.qstrPDSignalInfos = stDiagRetInfo.qstrPDSignalTypeInfos;

        if(m_pChart)
        {
            m_pChart->setDiagRet(stDiagDisplayInfo);
        }
    }
    else
    {
        //边界处理
        qint8 quiAmpValue = data.cAmpValue;
        quiAmpValue = (TEV::cMinAmpValue > quiAmpValue) ? TEV::cMinAmpValue : quiAmpValue;
        quiAmpValue = (TEV::cMaxAmpValue < quiAmpValue) ? TEV::cMaxAmpValue : quiAmpValue;

        TEVPulseDiagInfo stDiagInfo;
        stDiagInfo.qui8TevAmpVal = static_cast<quint8>(quiAmpValue);
        stDiagInfo.qui32PerPulseCnt = data.uiPerPulseNum;
        stDiagInfo.qui32SysFrequency = SystemSetService::instance()->getFrequency();
        stDiagInfo.stTHInfo.ucThresholdMinor = m_ucYellowAlert;
        stDiagInfo.stTHInfo.ucThresholdSerious = m_ucYellowAlert + 15;
        stDiagInfo.stTHInfo.ucThresholdEmergency = m_ucYellowAlert + 30;

        DiagResultInfo stDiagRetInfo;

        DiagnosisManager::instance()->diagTEVPulseByThreshold(stDiagInfo, stDiagRetInfo);

        DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
        stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stDiagRetInfo.stDiagRet.defectLevel);
        stDiagDisplayInfo.qstrPDDesInfo = stDiagRetInfo.qstrPDDescription;
        stDiagDisplayInfo.qstrPDSignalInfos = stDiagRetInfo.qstrPDSignalTypeInfos;

        if(m_pChart)
        {
            m_pChart->setDiagRet(stDiagDisplayInfo);
        }
    }

    return;
}
