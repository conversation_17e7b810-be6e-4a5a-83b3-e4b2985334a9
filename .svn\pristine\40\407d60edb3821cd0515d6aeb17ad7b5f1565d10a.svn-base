/*
* Copyright (c) 2017.11，南京华乘电气科技有限公司
* All rights reserved.
*
* infraredplaybackview.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年11月13日
* 摘要：红外回放模块

* 当前版本：1.0
*/

#ifndef GUIDEINFRAREDPLAYBACKVIEW_H
#define GUIDEINFRAREDPLAYBACKVIEW_H

#include "guideinfraredplayback.h"
#include "playbackView/PlayBackBase.h"
#include "guideinfrared/guideinfrareddefine.h"

class GuideInfraredImageWidget;
class GuideInfraredPlaybackView : public PlayBackBase
{
    Q_OBJECT

public:
    /*************************************************
    函数名： GuideInfraredPlaybackView(QWidget *parent = 0)
    输入参数： parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit GuideInfraredPlaybackView(QWidget *parent = 0);
signals:
    /*************************************************
    传递参数： NULL
    说明： 回放下一个文件信号
    *************************************************************/
    void sigPlayNextFile();

    /*************************************************
    传递参数： NULL
    说明： 回放上一个文件信号
    *************************************************************/
    void sigPlayLastFile();

    /*************************************************
    传递参数： NULL
    说明： 退出回放模式的信号
    *************************************************************/
    void sigExit();

private:
    /*************************************************
    函数名： playbackFile(const QString &strFileName)
    输入参数： strFileName：回放文件名
    输出参数： NULL
    返回值： NULL
    功能： 设置回放文件
    *************************************************************/
    void playbackFile(const QString &strFileName);

private slots:
    void onPlayNextFile();

    void onPlayLastFile();

private:
    GuideInfraredPlayback *m_pInfraredPlayback;  //Graphics View红外回放，横屏界面
    GuideInfrared::PlaybackState m_eCurrentPlaybackState;
    bool m_bHasVisiblePhoto;
};

#endif // GUIDEINFRAREDPLAYBACKVIEW_H
