/*
* Copyright (c) 2017.5，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：timezonedefine.h
*
* 初始版本：1.0
* 作者：张浪
* 修改日期：2018年9月22日
* 摘要：时区信息相关定义
* 当前版本：1.0
*/

#ifndef TIMEZONEDEFINE_H
#define TIMEZONEDEFINE_H


#define TIMEZONECOUNT   37

namespace TimezoneInfoConfig
{
const char* const GMT_NEG12     = "GMT-12:00";
const char* const GMT_NEG11     = "GMT-11:00";
const char* const GMT_NEG10     = "GMT-10:00";
const char* const GMT_NEG09_30  = "GMT-09:30";
const char* const GMT_NEG09     = "GMT-09:00";
const char* const GMT_NEG08     = "GMT-08:00";
const char* const GMT_NEG07     = "GMT-07:00";
const char* const GMT_NEG06     = "GMT-06:00";
const char* const GMT_NEG05     = "GMT-05:00";
const char* const GMT_NEG04     = "GMT-04:00";
const char* const GMT_NEG03_30  = "GMT-03:30";
const char* const GMT_NEG03     = "GMT-03:00";
const char* const GMT_NEG02     = "GMT-02:00";
const char* const GMT_NEG01     = "GMT-01:00";
const char* const GMT_POS00     = "GMT+00:00";
const char* const GMT_POS01     = "GMT+01:00";
const char* const GMT_POS02     = "GMT+02:00";
const char* const GMT_POS03     = "GMT+03:00";
const char* const GMT_POS04     = "GMT+04:00";
const char* const GMT_POS04_30  = "GMT+04:30";
const char* const GMT_POS05     = "GMT+05:00";
const char* const GMT_POS05_30  = "GMT+05:30";
const char* const GMT_POS05_45  = "GMT+05:45";
const char* const GMT_POS06     = "GMT+06:00";
const char* const GMT_POS06_30  = "GMT+06:30";
const char* const GMT_POS07     = "GMT+07:00";
const char* const GMT_POS08     = "GMT+08:00";
const char* const GMT_POS08_45  = "GMT+08:45";
const char* const GMT_POS09     = "GMT+09:00";
const char* const GMT_POS09_30  = "GMT+09:30";
const char* const GMT_POS10     = "GMT+10:00";
const char* const GMT_POS10_30  = "GMT+10:30";
const char* const GMT_POS11     = "GMT+11:00";
const char* const GMT_POS12     = "GMT+12:00";
const char* const GMT_POS12_45  = "GMT+12:45";
const char* const GMT_POS13     = "GMT+13:00";
const char* const GMT_POS14     = "GMT+14:00";

const char* const szTimezoneSettings[TIMEZONECOUNT] =
{
    GMT_NEG12, GMT_NEG11, GMT_NEG10, GMT_NEG09_30, GMT_NEG09, GMT_NEG08, GMT_NEG07, GMT_NEG06,
    GMT_NEG05, GMT_NEG04, GMT_NEG03_30, GMT_NEG03, GMT_NEG02, GMT_NEG01, GMT_POS00, GMT_POS01,
    GMT_POS02, GMT_POS03, GMT_POS04, GMT_POS04_30, GMT_POS05, GMT_POS05_30, GMT_POS05_45, GMT_POS06,
    GMT_POS06_30, GMT_POS07, GMT_POS08, GMT_POS08_45, GMT_POS09, GMT_POS09_30, GMT_POS10, GMT_POS10_30,
    GMT_POS11, GMT_POS12, GMT_POS12_45, GMT_POS13, GMT_POS14
};

const double szTimezoneHourVal[TIMEZONECOUNT] =
{
    -12.00, -11.00, -10.00, -9.50, -9.00, -8.00, -7.00, -6.00,
    -5.00, -4.00, -3.50, -3.00, -2.00, -1.00, 0.00, 1.00,
    2.00, 3.00, 4.00, 4.50, 5.00, 5.50, 5.75, 6.00,
    6.50, 7.00, 8.00, 8.75, 9.00, 9.50, 10.00, 10.50,
    11.00, 12.00, 12.75, 13.00, 14.00
};

}

#endif // TIMEZONEDEFINE_H
