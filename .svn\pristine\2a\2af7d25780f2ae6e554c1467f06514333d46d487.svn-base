/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* CmdButton.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2016年11月27日
* 摘要：按钮主体基类
*      用组合的方式，来实现按钮的UI相关行为定义
* 当前版本：1.0
*/
#ifndef CENTRALBUTTON_H
#define CENTRALBUTTON_H

#include <QFrame>

class CentralButton : public QFrame
{
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent -- 父窗体
    *************************************************************/
    CentralButton( QWidget* parent = 0 );

    /*************************************************
    功能： 设置标题
    输入参数:
        strTitle -- 标题
    *************************************************************/
    virtual void setTitle( const QString& strTitle );

    /*************************************************
    功能： 获取标题
    返回值： 标题
    *************************************************************/
    QString title( void ) const;

    /*************************************************
    功能： 设置数值
    输入参数:
        strContent -- 值
    *************************************************************/
    virtual void setContent( const QString& strContent );

    /*************************************************
    功能： 获取内容
    返回值： 内容
    *************************************************************/
    QString content( void ) const;

    /*************************************************
    功能： 设置显示文本字体
    输入参数:
        stFont -- 字体
    *************************************************************/
    virtual void setFont(const QFont& stFont);

    /*************************************************
    功能： 返回显示文本字体
    返回值:
        QFont -- 字体
    *************************************************************/
    QFont font() const;

    /*************************************************
    功能： 设置按钮大小
    输入参数: usWidth -- 按键宽度
             usHeight -- 按键高度
    *************************************************************/
    virtual void setButtonSize( quint16 usWidth, quint16 usHeight );

    /*************************************************
    功能： 响应控制按钮激活状态
    输入参数:
        bActive: 激活状态
    *************************************************************/
    virtual void onActive( bool bActive );
private:
    QString m_strTitle;//标题
    QString m_strContent;//值内容
    QFont   m_stFont;
};

#endif // CENTRALBUTTON_H
