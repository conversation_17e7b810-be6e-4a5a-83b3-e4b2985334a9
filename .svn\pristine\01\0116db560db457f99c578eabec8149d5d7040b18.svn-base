/*
* Copyright (c) 2019.06，南京华乘电气科技有限公司
* All rights reserved.
*
* DiagnosisConfig.h
*
* 初始版本：1.0
* 作者：张浪
* 修改日期：2019年6月6日
*       诊断相关的信息
* 当前版本：1.0
*/

#ifndef DIAGNOSISCONFIG_H
#define DIAGNOSISCONFIG_H

#include <QApplication>
#include <QObject>
#include <QString>
#include "widgetglobal.h"

#define DIAGNOSIS_CONFIG_TRANSLATE(str) qApp->translate("DiagConfig", (str))

namespace DiagConfig
{
const char* const CONTEXT = "DiagConfig";

const char* const TEXT_DIAG_RET = QT_TRANSLATE_NOOP_UTF8("DiagConfig", "Diagnostic Result: ");
const char* const TEXT_NORMAL = QT_TRANSLATE_NOOP_UTF8("DiagConfig", "Normal");
const char* const TEXT_ABNORMAL = QT_TRANSLATE_NOOP_UTF8("DiagConfig", "Abnormal");
const char* const TEXT_MEDIUM = QT_TRANSLATE_NOOP_UTF8("DiagConfig", "Medium");
const char* const TEXT_HIGH = QT_TRANSLATE_NOOP_UTF8("DiagConfig", "High");
const char* const TEXT_MINOR = QT_TRANSLATE_NOOP_UTF8("DiagConfig", "Minor");
const char* const TEXT_SERIOUS = QT_TRANSLATE_NOOP_UTF8("DiagConfig", "Serious");
const char* const TEXT_EMERGENCY = QT_TRANSLATE_NOOP_UTF8("DiagConfig", "Emergency");

const int DIAG_DISPLAY_DATA_SIZE = 6;//诊断可以显示条目数

typedef enum _DiagnoseRet_
{
    Diag_Unrecord = -1, //未记录
    Diag_Normal = 0,  //正常
    Diag_Minor,     //一般
    Diag_Serious,   //严重
    Diag_Emergency, //紧急
}DiagnoseRet;

typedef struct _DiagDisplayInfo_
{
    DiagnoseRet eDiagRet;//诊断严重等级
    QString qstrPDDesInfo;//诊断置信度最高的信号描述
    QString qstrPDSignalInfos;//所有类型信号的诊断信息

    _DiagDisplayInfo_()
    {
        eDiagRet = Diag_Unrecord;
        qstrPDDesInfo = "";
        qstrPDSignalInfos = "";
    }

}DiagDisplayInfo;

/************************************************
 * 功能：获取诊断展示的信息
 * 输入参数：
 *      stDiagDisplayInfo：诊断信息
 *      bShowPDDesInfo: 是否显示信号描述
 * 返回值：
 *      诊断展示的信息
 ************************************************/
QString WIDGET_EXPORT getDiagnosisDisplayInfo(const DiagConfig::DiagDisplayInfo &stDiagDisplayInfo, bool bShowPDDesInfo = false);

/************************************************
 * 功能：获取诊断结论信息
 * 输入参数：
 *      stDiagDisplayInfo：诊断信息
 * 返回值：
 *      诊断展示的信息
 ************************************************/
QString WIDGET_EXPORT getDiagnosisInfo(const DiagConfig::DiagDisplayInfo &stDiagDisplayInfo);


}

#endif // DIAGNOSISCONFIG_H
