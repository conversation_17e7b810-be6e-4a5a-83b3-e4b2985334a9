/*
* Copyright (c) 2016.3，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：SyncWidget.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年12月4日
* 摘要：该文件主要实现了同步状态旋转90°显示
*/

#ifndef SYNCWIDGET_H
#define SYNCWIDGET_H

#include <QWidget>
#include "ae/AE.h"
#include "Module.h"

class SyncWidget : public QWidget
{
    Q_OBJECT
public:
    /************************************************
     * 功能     ：构造函数
     * 输入参数  ：父对象指针
     ************************************************/
    explicit SyncWidget(QWidget *parent = 0);

    /****************************
    输入参数:eSyncState:同步状态
    功能： 设置同步状态
    *****************************/
    void setSyncState( Module::SyncState eSyncState );

    /****************************
    函数名： setSyncSource( SyncSource eSyncSource )
    输入参数:eSyncSource:同步源
    输出参数：NULL
    返回值：NULL
    功能： 设置同步源
    *****************************/
    void setSyncSource( Module::SyncSource eSyncSource );
protected:
    /****************************
    输入参数:pEvent：绘图事件
    功能： 重载绘图函数
    *****************************/
    void paintEvent( QPaintEvent* );

private:

    Module::SyncState   m_eSyncState;                   // 同步状态
    Module::SyncSource  m_eSyncSource;                  // 同步源
};

#endif // SYNCWIDGET_H
