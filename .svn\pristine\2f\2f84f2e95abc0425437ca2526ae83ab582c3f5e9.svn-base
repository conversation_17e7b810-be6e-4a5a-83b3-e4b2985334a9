﻿#ifndef APPCONFIG
#define APPCONFIG

#include "config/ConfigManager.h"
#include "Module.h"
#include "systemsetting/SystemSet.h"
#include "timezonemanager/timezonedefine.h"
#include "datadefine.h"
#include "global_def.h"

namespace APPConfig
{

//配置信息定义
typedef enum _ConfigInfo
{
    //键值
    KEY_SYS_FREQ = 0, //系统频率
    KEY_SYS_LANGAUGE,// 语言
    KEY_BG_LIGHT,// 背光
    KEY_CLOUD_ADDR,// 云地址
    KEY_CLOUD_PORT,// 云端口
    KEY_WIFI_USER,// wifi用户名
    KEY_WIFI_PWD,// wifi密码
    KEY_WIFI_SWITCH,// wifi开关
    KEY_HOT_SWITCH,// 热点开关
    KEY_FOUR_G_SWITCH,// 4g开关
    KEY_AUTO_POWER_OFF_TIME,// 自动关机时间
    KEY_USB_STATE_SWITCH,// USB状态切换
    KEY_DATE_FORMAT,
    KEY_TIME_FORMAT,
    KEY_PRPS_BG_COLOR,
    KEY_PRPS_COLOR,
    KEY_SYSTEM_VOLUME,
    KEY_USE_DST,
    KEY_TIMEZONE,//时区
    KEY_TEMPERATURE_UNIT,//温度单位
    KEY_BLUETOOTH_NAME,//蓝牙名称
    KEY_BLUETOOTH_MAC,//蓝牙设备MAC
    KEY_BLUETOOTH_AUTO_CONNECT,//蓝牙是否自动连接
    KEY_BLUETOOTH_RECORD_DATE,//蓝牙记录的时间 超过一定的时间自动删除相应的蓝牙记录
    KEY_BLUETOOTH_INFO,//蓝牙信息
    KEY_BLUETOOTH_SWITCH,//蓝牙开关
    KEY_PATROL_SWITCH_MODE,//巡检跳转模式
    KEY_RECORD_DURATION,//录音时长
    KEY_KEYBOARD_MODE,//软件盘模式
    KEY_PRPS_SAMPLE_INTERVAL,//PRPS采样间隔
    KEY_AUTO_SYNC,//自动同步
    KEY_FILE_COMMENT_BOX,//文件备注框

    //VPN接入
    KEY_VPN_ACCESS_MODE,//VPN接入模式
    KEY_VPN_ACCESS_DOMAINNAME,//VPN接入域名
    KEY_VPN_ACCESS_USERNAME,//VPN接入用户名
    KEY_VPN_ACCESS_PWD,//VPN接入密码

    //外设匹配
    KEY_UHF_CONDITIONER_ID,//uhf调理器ID
    KEY_HFCT_CONDITIONER_ID,//hfct调理器ID
    KEY_SYNCHRONIZER_CONDITIONER_ID,//synchronizer调理器ID
    KEY_AE_CONDITIONER_ID,//AE调理器ID
    KEY_CURRENT_DETECTION_CONDITIONER_ID,//电流检测调理器ID

    KEY_CA_CONDITIONER_ID, //ca调理器ID

    //智能巡检
    KEY_USER_NAME,   //智能巡检上次登录成功的用户名
    KEY_USER_PWD,    //智能巡检上次登录成功的密码
    KEY_USER_PWD_TIME, //智能巡检上次登录成功记住的时间

    //定制接入热点
    KEY_CUSTOM_ACCESS_MODE,//定制接入模式
    KEY_CUSTOM_ACCESS_SERVER_IP,
    KEY_CUSTOM_ACCESS_SERVER_PORT,
    KEY_CUSTOM_ACCESS_SWITCH_MODE,

    //红外摄像头配置
    KEY_INFRARED_CAMERA_IP,

    //管理员用户调试相关配置
    KEY_SYSTEM_SHUTDOWN_SWITCH,//系统自动关机开关
    KEY_AERECORD_DURATION, //AE录音时长
    KEY_SSL_SWITCH,//ssl认证开关
    KEY_SSL_MODE,//ssl认证模式

    KEY_TEV_CALIB_WRITE_SWITCH,//TEV校准写入开关
    KEY_LCD_INIT_SWITCH,//LCD初始化开关
    KEY_LOWER_BATTERY_VAL,//最低电量值

    KEY_UPGRADE_SRV_ADDR,//远程升级服务地址
    KEY_UPGRADE_SRV_PORT,//远程升级服务端口

    KEY_DIAGNOSIS_REALTIME_SWITCH,//实时诊断开关
    KEY_REMIND_SWITCH,//提醒开关

}ConfigInfo;

//App组的键值配置
static Config::KeyInfo KEYS_APP[] =
{
    { KEY_SYS_FREQ, "SysFreq", Config::NUMBER, QString::number(Module::FREQ_60HZ), Module::FREQ_50HZ, Module::FREQ_60HZ},
    { KEY_SYS_LANGAUGE, "Language", Config::NUMBER, QString::number(SystemSet::SET_EN), SystemSet::SET_MIN, SystemSet::SET_MAX},
    { KEY_BG_LIGHT, "BGLight", Config::NUMBER, QString::number(SystemSet::BL_LEVEL_1), SystemSet::BL_LEVEL_1, SystemSet::BL_LEVEL_3},
    { KEY_CLOUD_ADDR, "CloudAddr", Config::TEXT, "", 0, 0},
    { KEY_CLOUD_PORT, "CloudPort", Config::TEXT, "", 0, 0},
    { KEY_WIFI_USER, "WifiUser", Config::TEXT, "", 0, 0},
    { KEY_WIFI_PWD, "WifiPwd", Config::TEXT, "", 0, 0},
    { KEY_WIFI_SWITCH, "WifiSwitch", Config::NUMBER, QString::number(0), 0, 1},
    { KEY_BLUETOOTH_SWITCH, "WifiSwitch", Config::NUMBER, QString::number(0), 0, 1},
    { KEY_HOT_SWITCH, "HotSwitch", Config::NUMBER, QString::number(SystemSet::HOT_CLOSED), SystemSet::HOT_OPEN, SystemSet::HOT_CLOSED},
    { KEY_FOUR_G_SWITCH, "FourGSwitch", Config::NUMBER, QString::number(SystemSet::FOUR_G_CLOSED), SystemSet::FOUR_G_OPEN, SystemSet::FOUR_G_CLOSED},
    { KEY_AUTO_POWER_OFF_TIME, "AutoPowerOffTime", Config::NUMBER, QString::number(SystemSet::SHUT_DOWN_DEFAULT), SystemSet::SHUT_DOWN_MIN, SystemSet::SHUT_DOWN_MAX},
    { KEY_USB_STATE_SWITCH, "USBSwitch", Config::NUMBER, QString::number(SystemSet::USB_STATE_NET), SystemSet::USB_STATE_NET, SystemSet::USB_STATE_STORGE},
    { KEY_DATE_FORMAT, "DateFormat", Config::NUMBER, QString::number(SystemSet::DATE_FORMAT_M_D_Y), SystemSet::DATE_FORMAT_Y_M_D, SystemSet::DATE_FORMAT_D_M_Y},
    { KEY_TIME_FORMAT, "TimeFormat", Config::NUMBER, QString::number(SystemSet::TIME_FORMAT_24_HOUR), SystemSet::TIME_FORMAT_12_HOUR, SystemSet::TIME_FORMAT_24_HOUR},
    { KEY_PRPS_BG_COLOR, "PRPSBGColor", Config::NUMBER, QString::number(SystemSet::PRPS_BG_COLOR_DEFAULT), SystemSet::PRPS_BG_COLOR_GRAY, SystemSet::PRPS_BG_COLOR_WHITE},
    { KEY_PRPS_COLOR, "PRPSColor", Config::NUMBER, QString::number(SystemSet::PRPS_COLOR_DEFAULT), SystemSet::PRPS_COLOR_BLAZE, SystemSet::PRPS_COLOR_RAINBOW},
    { KEY_SYSTEM_VOLUME, "Volume", Config::NUMBER, QString::number(SystemSet::VOLUME_DEFAULT), SystemSet::VOLUME_MIN, SystemSet::VOLUME_MAX},
    { KEY_USE_DST, "DST", Config::NUMBER, QString::number(0), 0, 1},
    { KEY_TIMEZONE, "Timezone", Config::TEXT, TimezoneInfoConfig::GMT_NEG05, 0, 0},
    { KEY_TEMPERATURE_UNIT, "TemperatureUnit", Config::NUMBER, QString::number(1), 0, 1},
    { KEY_PATROL_SWITCH_MODE, "PatrolSwitchMode", Config::NUMBER, QString::number(SystemSet::PATROL_AUTO_SWITCH), SystemSet::PATROL_AUTO_SWITCH, SystemSet::PATROL_RFID_SCAN_SWITCH},
    { KEY_RECORD_DURATION, "RecordDuration", Config::NUMBER, QString::number(SystemSet::DURA_DEFAULT), SystemSet::DURA_MIN, SystemSet::DURA_MAX},
    { KEY_KEYBOARD_MODE, "KeyboardMode", Config::NUMBER, QString::number(SystemSet::KBM_DEFAULT), SystemSet::KBM_FULL, SystemSet::KBM_SUDOKU},
    { KEY_PRPS_SAMPLE_INTERVAL, "PRPSSampleInterval", Config::NUMBER, QString::number(SystemSet::PRPS_SAMPLE_INT_DEFAULT), SystemSet::PRPS_SAMPLE_INT_MIN, SystemSet::PRPS_SAMPLE_INT_MAX},
    { KEY_AUTO_SYNC, "AutoSync", Config::NUMBER, QString::number(0), 0, 1},
    { KEY_FILE_COMMENT_BOX, "FileCommentBox", Config::NUMBER, QString::number(0), 0, 1},

    //VPN接入
    { KEY_VPN_ACCESS_MODE, "VpnAccessMode", Config::NUMBER, QString::number(0), 0, 5 },
    { KEY_VPN_ACCESS_DOMAINNAME, "VpnAccessDomainName", Config::TEXT, "", 0, 0 },
    { KEY_VPN_ACCESS_USERNAME, "VpnAccessUserName", Config::TEXT, "", 0, 0 },
    { KEY_VPN_ACCESS_PWD, "VpnAccessPassword", Config::TEXT, "", 0, 0 },

    //外设匹配
    { KEY_UHF_CONDITIONER_ID, "UHFConditionerID", Config::TEXT, "", 0, 0 },
    { KEY_HFCT_CONDITIONER_ID, "HFCTConditionerID", Config::TEXT, "", 0, 0 },
    { KEY_SYNCHRONIZER_CONDITIONER_ID, "SynchronizerID", Config::TEXT, "", 0, 0 },
    { KEY_AE_CONDITIONER_ID, "AEConditionerID", Config::TEXT, "", 0, 0 },
    { KEY_CA_CONDITIONER_ID, "CAConditionerID", Config::TEXT, "", 0, 0 },
    { KEY_CURRENT_DETECTION_CONDITIONER_ID, "CurrentDetectionConditionerID", Config::TEXT, "", 0, 0 },

    //智能巡检
    { KEY_USER_NAME, "UserName", Config::TEXT, "", 0, 0 },
    { KEY_USER_PWD, "PWD", Config::TEXT, "", 0, 0 },
    { KEY_USER_PWD_TIME, "loginedTime", Config::TEXT, "", 0, 0 },

    //接入终端
    { KEY_CUSTOM_ACCESS_MODE, "CustomAccessMode", Config::NUMBER, QString::number(SystemSet::ACCESS_DEFAULT_MODE), SystemSet::ACCESS_USB_MODE, SystemSet::ACCESS_WIFI_MODE },
    { KEY_CUSTOM_ACCESS_SERVER_IP, "CustomAccServerIP", Config::TEXT, "", 0, 0 },
    { KEY_CUSTOM_ACCESS_SERVER_PORT, "CustomAccServerPort", Config::TEXT, "", 0 , 0 },
    { KEY_CUSTOM_ACCESS_SWITCH_MODE, "CustomAccessSwitchMode", Config::NUMBER, QString::number(SystemSet::ACCESS_MANUAL_SWITCH), SystemSet::ACCESS_AUTO_SWITCH, SystemSet::ACCESS_MANUAL_SWITCH },

    //蓝牙信息的组
    { KEY_BLUETOOTH_NAME, "BlueToothName", Config::TEXT, "", 0, 0 },
    { KEY_BLUETOOTH_MAC, "BlueToothMac", Config::TEXT, "", 0, 0 },
    { KEY_BLUETOOTH_AUTO_CONNECT, "BlueToothAutoConnect", Config::NUMBER, "", 0, 1 },
    { KEY_BLUETOOTH_RECORD_DATE, "BlueToothRecordDate", Config::TEXT, "", 0, 0 },

    //红外摄像头配置
    { KEY_INFRARED_CAMERA_IP, "InfraredCameraIp", Config::TEXT, SystemSet::TEXT_INFRARED_CAMERA_IP1, 0, 0 },

    //系统自动关机开关
    { KEY_SYSTEM_SHUTDOWN_SWITCH, "SysShutdownSwitch", Config::NUMBER, QString::number(SystemSet::SHUTDOWNSWITCH_DEFAULT), SystemSet::SHUTDOWNSWITCH_ON, SystemSet::SHUTDOWNSWITCH_OFF },

    //AE录音时长
    { KEY_AERECORD_DURATION, "RecordAETime", Config::NUMBER, QString::number(SystemSet::RECORD_DEFAULT), SystemSet::RECORD_MIN, SystemSet::RECORD_MAX},
    //ssl认证开关
    #ifdef _SUPPORT_HTTPS_VERIFY_
    { KEY_SSL_SWITCH, "SslVerifySwitch", Config::NUMBER, QString::number(SystemSet::SSLVERF_ON), SystemSet::SSLVERF_ON, SystemSet::SSLVERF_OFF },
    #else
    { KEY_SSL_SWITCH, "SslVerifySwitch", Config::NUMBER, QString::number(SystemSet::SSLVERF_DEFAULT), SystemSet::SSLVERF_ON, SystemSet::SSLVERF_OFF },
    #endif
    { KEY_SSL_MODE, "SslVerifyMode", Config::NUMBER, QString::number(SystemSet::SSLVERF_MODE_NONE), SystemSet::SSLVERF_MODE_NONE, SystemSet::SSLVERF_MODE_AUTO },
    //TEV校准写入开关
    { KEY_TEV_CALIB_WRITE_SWITCH, "TevCalibWriteSwitch", Config::NUMBER, QString::number(SystemSet::TEV_CALIBWRITE_DEFAULT), SystemSet::TEV_CALIBWRITE_ON, SystemSet::TEV_CALIBWRITE_OFF },
    //LCD初始化开关
    { KEY_LCD_INIT_SWITCH, "LCDInitSwitch", Config::NUMBER, QString::number(SystemSet::LCD_INTI_DEFAULT), SystemSet::LCD_INTI_ON, SystemSet::LCD_INTI_OFF },
    //最低电量值
    { KEY_LOWER_BATTERY_VAL, "LowerBattery", Config::NUMBER, QString::number(SystemSet::LOWER_BATTERY_DEFAULT), SystemSet::LOWER_BATTERY_MIN, SystemSet::LOWER_BATTERY_MAX },

    //远程升级信息
    { KEY_UPGRADE_SRV_ADDR, "UpgradeSrvAddr", Config::TEXT, DEV_PMDT_DEFAULT_UPG_IP, 0, 0},
    { KEY_UPGRADE_SRV_PORT, "UpgradeSrvPort", Config::NUMBER, QString::number(DEV_PMDT_DEFAULT_UPG_PORT), 0, 65535},

    { KEY_DIAGNOSIS_REALTIME_SWITCH, "RealtimeDiagnosisSwitch", Config::NUMBER, QString::number(SystemSet::RT_DIAG_DEFAULT), SystemSet::RT_DIAG_ON, SystemSet::RT_DIAG_OFF },
    { KEY_REMIND_SWITCH, "RemindSwitch", Config::NUMBER, QString::number(SystemSet::REMIND_OPEN), SystemSet::REMIND_OPEN, SystemSet::REMIND_CLOSED},
};

//蓝牙信息的组
static Config::KeyInfo BLUETOOTH_INFO[] =
{
    { KEY_BLUETOOTH_NAME, "BlueToothName", Config::TEXT, "", 0, 0 },
    { KEY_BLUETOOTH_MAC, "BlueToothMac", Config::TEXT, "", 0, 0},
    { KEY_BLUETOOTH_AUTO_CONNECT, "BlueToothAutoConnect", Config::NUMBER, "", 0, 1 },
    { KEY_BLUETOOTH_RECORD_DATE, "BlueToothRecordDate", Config::TEXT, "", 0, 0 },
};

static Config::GroupInfo CONFIG =
{
    Module::GROUP_APP, Config::NORMAL, "APP", KEYS_APP, sizeof( KEYS_APP )/sizeof(Config::KeyInfo), NULL, 0
};


}

#endif // APPCONFIG

