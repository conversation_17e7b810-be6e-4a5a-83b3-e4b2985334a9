/*
* Copyright (c) 2016.03，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: HFCTAmpDataMap.h
*
* 初始版本：1.0
* 作者：洪澄
* 创建日期：2019年05月15日
* 摘要：该文件主要是定义了HFCT幅值检测数据(图谱)存储的子类
*/

#ifndef HFCTAMPDATAMAPSAVE_H
#define HFCTAMPDATAMAPSAVE_H

#include "dataSave/DataSave.h"
#include "module_global.h"

#include "datafile/datamap.h"
#include "datafile/datafile.h"
#include "datafile/amplitude/ampdatamap.h"
#include "config/ConfigManager.h"

typedef struct _HFCTAmpDataInfo
{
    DataMapHead stHeadInfo;            //图谱通用的头部信息

    DataFileNS::AmpUnit eAmpUnit; //幅值单位
    float fAmpLowerLimit;//幅值下限
    float fAmpUpperLimit;//幅值上限
    DataFileNS::MapBandWidth eBandWidth;//超声传感器类型
    float fFrequencyMin;//下限频率
    float fFrequencyMax;//上限频率
    float fWarningValue;                //预警值
    float fAlarmingValue;               //报警值
    quint8 ucaDischargeTypeProb[8];//放电类型概率
    DataFileNS::MapDataSign eDataSign;//数据有效判断标志
    DataFileNS::GainType eGainType; //增益种类
    qint16 sGain;//增益, 60dB；80dB；100dB
    float fSyncFreq;//标识测试系统频率，如果没有同步频率，则存-1

    qint8 iCurrentAmp;  //当前值，单位以幅值单位为准
    qint8 iCurrentMax;  //最大值，单位以幅值单位为准

    _HFCTAmpDataInfo()
    {
        eAmpUnit = DataFileNS::AMP_UNIT_DB;
        fAmpLowerLimit = 0;
        fAmpUpperLimit = 0;
        eBandWidth = DataFileNS::BAND_DEFAULT;
        fFrequencyMin = 0;
        fFrequencyMax = 0;
        fWarningValue = 20;
        fAlarmingValue = 40;
        memset(ucaDischargeTypeProb, 0, sizeof(ucaDischargeTypeProb));
        eDataSign = DataFileNS::DATA_DEFAULT;
        eGainType = DataFileNS::GAIN_TYPE_DB;
        sGain = 0;
        fSyncFreq = -1;

        iCurrentAmp = 0;
        iCurrentMax = 0;
    }

}HFCTAmpDataInfo;

class MODULESHARED_EXPORT HFCTAmpDataMapSave : public DataSave
{
public:
    /************************************************
     * 函数名   : HFCTAmpDataMapSave
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 构造函数
     ************************************************/
    HFCTAmpDataMapSave();

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData);

    /*************************************************
    功能： 保存数据
    返回：
        保存结果
    *************************************************************/
    QString saveData(void *pData, const QString &qsSavedPath);

    /************************************************
     * 函数名   : getDataMap
     * 输入参数 : baData: xml格式数据流;
     * 输出参数 : pDatas: 数据
     * 返回值   : void
     * 功能     : 将xml格式数据提取到指定格式的结构体变量中
     ************************************************/
    void getDataMap(const QByteArray& baData, void *pData);

    /************************************************
     * 函数名   : getDataFromFile
     * 输入参数 : qsFile: 文件名;
     *			 : stData: HFCT幅值数据
     * 返回值   : bool
     * 功能     : 将xml格式数据提取到指定格式的结构体变量中
     ************************************************/
    INT32 getDataFromFile(const QString &qsFile, void* stData);

    /************************************************
     * 函数名   : getDataByteArray
     * 输入参数 : void;
     * 输出参数 : NULL
     * 返回值   : xml格式数据流
     * 功能     : 获取xml格式数据流
     ************************************************/
    QByteArray getDataByteArray(void);

protected:
    /************************************************
     * 函数名   : getDataTypeFolder
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件夹名
     * 功能     : 获取图谱类型文件存储最上层文件夹名
     ************************************************/
    QString getDataTypeFolder(void);

    /************************************************
     * 函数名   : getFileNameSuffix
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件后缀名
     * 功能     : 获取数据文件后缀名
     ************************************************/
    QString getFileNameSuffix(void);

    /************************************************
     * 函数名   : getStringFromData
     * 输入参数 : pDatas: 数据; uiCounts: 数据个数
     * 输出参数 : NULL
     * 返回值   : 转换后的字符串
     * 功能     : 将数据转成base64的字符串
     ************************************************/
    QString getStringFromData( void *pDatas, UINT32 uiCounts);

    /************************************************
     * 函数名   : saveExtInformation
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储可扩展信息
     ************************************************/
    void saveExtInformation(XMLDocument& doc);

    /************************************************
     * 函数名   : saveRawData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储数据部分
     ************************************************/
    void saveRawData(XMLDocument& doc);


    /************************************************
     * 函数名   : parseData
     * 输入参数 : baData: 数据
     * 输出参数 : pData: 解析到的数据
     * 返回值   : void
     * 功能     : 解析数据
     ************************************************/
    void parseData(const QByteArray& baData, void *pData, const QString& strFileName = "");
private:

    void setMapData(AmpDataMap *pMap);

    void setMapInfo(AmpDataMap *pMap);

    void setMapHead(DataMap *pMap);

    void getDataFilePath(QString &strAbsolutePath);

    void addHFCTAmpMap(DataFile *pFile);

    void registerMaps();

    /****************************
    功能： 获取图谱ext数据
    输入参数:
        pMap -- 文件解析得到的图谱
    *****************************/
    void setPlaybackInfoByMapInfo( const AmpMapNS::AmpMapInfo &stMapInfo);

    void setPlaybackInfoByFileHead(DataFile *psDataFile);

    void setPlaybackInfoByMapHead(AmpDataMap * pMap);

private:
    HFCTAmpDataInfo *m_pHFCTAmpDataInfo;
    ConfigInstance* m_pConfig;//配置模块
    QByteArray m_baData;
};

#endif // HFCTAMPDATAMAPSAVE_H
