#include "bluetoothserver.h"
#include <unistd.h>
#include <QVector>
#include <QEventLoop>
#include <QTimer>
#include <QCoreApplication>
#include <QDebug>
#include "datadefine.h"
#include "log/log.h"


/*************************************************
函数名： BluetoothServer)
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
BluetoothServer::BluetoothServer()
{
    m_bScan = false;
}

/*************************************************
函数名： ~BluetoothServer()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
BluetoothServer::~BluetoothServer()
{
    close();
}

/*************************************************
函数名： open()
输入参数： NULL
输出参数： NULL
返回值： 操作结果--true: 打开成功; false: 打开失败
功能： 开启本地蓝牙适配器
*************************************************************/
bool BluetoothServer::open()
{
    if ( m_bOpen )
    {
        logWarning("BluetoothClient has already been opened.");
        return true;
    }

    m_bOpen = initDevice();
    logInfo(QString("bluetooth has been opened, ret: %1.").arg(m_bOpen).toLatin1().data());
    return m_bOpen;
}

/*************************************************
函数名： close()
输入参数： NULL
输出参数： NULL
返回值： 操作结果--true: 关闭成功; false: 关闭失败
功能： 关闭本地蓝牙适配器
*************************************************************/
bool BluetoothServer::close()
{
    if ( !m_bOpen )
    {
        logWarning("Bluetooth has already been closed.");
        return true;
    }

    bool bRes = true;

    //关闭连接端口
    if ( m_nPortHandle > 0 )
    {
        logInfo("begin to closePort...");
        closePort(m_nPortHandle);
        m_nPortHandle = 0;
    }

    bRes = Bluetooth::close();
    logInfo(QString("bluetooth has been closed, ret: %1.").arg(bRes).toLatin1().data());
    return bRes;
}

/*************************************************
函数名： restart()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 重启蓝牙
*************************************************************/
void BluetoothServer::restart()
{
    close();
    open();
    return;
}

/*************************************************
函数名: disconnectFromRemoteDev
输入参数: strMac--远端蓝牙mac地址
输出参数: NULL
返回值: 结果状态--成功返回0
功能: 与远端蓝牙设备断开连接
*************************************************************/
int BluetoothServer::disconnectFromRemoteDev()
{
    m_bConnect = false;
    return closePort(m_nPortHandle);
}

/*************************************************
函数名： closePort
输入参数： port--端口；timeOut--超时时间
输出参数： NULL
返回值： int--成功返回0
功能：关闭连接
*************************************************************/
int BluetoothServer::closePort(uint portHandle, uint timeOut)
{
    int res=0, btRes=0;

    if ( m_bInitialized )
    {
#ifdef Q_WS_QWS
        if ((btRes = SPPM_ClosePort(portHandle, timeOut)) == 0)
        {
            printf("SPPM_ClosePort Success.\r\n");
            res = 0;
        }
        else
        {
            printf("SPPM_ClosePort Failure: %d, %s.\r\n", btRes, ERR_ConvertErrorCodeToString(btRes));
            res = -1;
        }
#else
        Q_UNUSED(portHandle)
        Q_UNUSED(timeOut)
        Q_UNUSED(btRes)
#endif
    }
    else
    {
        printf("Platform Manager has not been initialized.\r\n");
        res = -1;
    }

    return res;
}

/*************************************************
函数名： openServicePort
输入参数： port--端口；timeOut--超时时间
输出参数： NULL
返回值： int--成功返回0
功能：打开服务端蓝牙连接端口服务
*************************************************************/
bool BluetoothServer::openServicePort()
{
    bool res = false;
    if(findFreeServerPort())
    {
        unsigned int ServerPort = 22;
        if(registerServerPort(ServerPort))
        {
            if(queryServerPresent(ServerPort))
            {
                res = true;
                return res;
            }
        }
    }
}

/*************************************************
函数名： updateRemoteDeviceInfos
输入参数： UpdateMask--更新标识;
         RemoteDeviceProperties--远端设备信息
输出参数： NULL
返回值： NULL
功能： 更新远端设备更新信息
*************************************************************/
void BluetoothServer::updateRemoteDeviceInfos(unsigned long UpdateMask, DEVM_Remote_Device_Properties_t *RemoteDeviceProperties)
{
    char Buffer[64];

    if(RemoteDeviceProperties)
    {
        /* First, display any information that is not part of any update  */
        /* mask.                                                          */
        BD_ADDRToStr(RemoteDeviceProperties->BD_ADDR, Buffer);

        printf("BD_ADDR:      %s\r\n", Buffer);

        QString strMac = QString::fromLocal8Bit(Buffer);
        bool bFlag = false;
        if ( !m_destDevInfo.contains(strMac) )
        {
            DeviceInfo devInfo;
            devInfo.strMac = strMac;
            m_destDevInfo[strMac] = devInfo;
        }


        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_CLASS_OF_DEVICE))
            printf("COD:           0x%02X%02X%02X\r\n", RemoteDeviceProperties->ClassOfDevice.Class_of_Device0, RemoteDeviceProperties->ClassOfDevice.Class_of_Device1, RemoteDeviceProperties->ClassOfDevice.Class_of_Device2);

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_DEVICE_NAME))
        {
            bool bFlag = (RemoteDeviceProperties->DeviceNameLength);
            printf("Device Name(%d):   %s\r\n", RemoteDeviceProperties->DeviceNameLength, bFlag ? RemoteDeviceProperties->DeviceName:"");
            QString strTmp = QString::fromLocal8Bit(RemoteDeviceProperties->DeviceName);
            qDebug() << strTmp;
            m_destDevInfo[strMac].strName = bFlag ? QString(RemoteDeviceProperties->DeviceName) : "unknow name";
        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_DEVICE_FLAGS))
            printf("Device Flags:  0x%08lX\r\n", RemoteDeviceProperties->RemoteDeviceFlags);

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_RSSI))
            printf("RSSI:          %d\r\n", RemoteDeviceProperties->RSSI);

        if((!UpdateMask) && (RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_CURRENTLY_CONNECTED))
            printf("Trans. Power:  %d\r\n", RemoteDeviceProperties->TransmitPower);

        if((!UpdateMask) || ((UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_APPLICATION_DATA) && (RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_APPLICATION_DATA_VALID)))
        {
            printf("Friendly Name: %s\r\n", (RemoteDeviceProperties->ApplicationData.FriendlyNameLength)?RemoteDeviceProperties->ApplicationData.FriendlyName:"");

            printf("App. Info:   : %08lX\r\n", RemoteDeviceProperties->ApplicationData.ApplicationInfo);
        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_PAIRING_STATE))
        {
            bFlag = (RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_CURRENTLY_PAIRED);
            printf("Paired State : %s\r\n", bFlag ? "TRUE":"FALSE");
            m_destDevInfo[strMac].bPaired = bFlag;
        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_CONNECTION_STATE))
        {
            bFlag = (RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_CURRENTLY_CONNECTED);
            printf("Connect State: %s\r\n", bFlag ? "TRUE" : "FALSE");
            m_destDevInfo[strMac].bConnected = bFlag;
        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_ENCRYPTION_STATE))
            printf("Encrypt State: %s\r\n", (RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_LINK_CURRENTLY_ENCRYPTED)?"TRUE":"FALSE");

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_SNIFF_STATE))
        {
            if(RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_LINK_CURRENTLY_SNIFF_MODE)
                printf("Sniff State  : TRUE (%u ms)\r\n", RemoteDeviceProperties->SniffInterval);
            else
                printf("Sniff State  : FALSE\r\n");
        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_SERVICES_STATE))
            printf("Serv. Known  : %s\r\n", (RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_SERVICES_KNOWN)?"TRUE":"FALSE");

    }
}

/*************************************************
函数名： getPairState
输入参数： strMac--蓝牙mac地址
输出参数： NULL
返回值： NULL
功能： 获取是否配对（阻塞）
*************************************************************/
bool BluetoothServer::getPairState(QString strMac)
{
    if ( m_pSemPair->available() )
    {
        m_pSemPair->acquire(m_pSemPair->available());
    }

    m_pSemPair->tryAcquire(1, PairTimeOut * 1000);
    logDebug(QString("dev: %1, get pair state.").arg(strMac).toLatin1().data());
    if ( m_destDevInfo.contains(strMac) )
    {
        return m_destDevInfo[strMac].bPaired;
    }
    else
    {
        return false;
    }
}

/*************************************************
函数名： getConnectState
输入参数： strMac--蓝牙mac地址
输出参数： NULL
返回值： NULL
功能： 获取是否连接（阻塞）
*************************************************************/
bool BluetoothServer::getConnectState(QString strMac)
{
    if ( m_pSemConn->available() > 0 )
    {
        m_pSemConn->acquire(m_pSemConn->available());
    }

    m_pSemConn->tryAcquire(1, ConnectTimeOut * 1000);

    m_destDevInfo[strMac].bConnected = m_bConnect;
    return m_bConnect;
}
