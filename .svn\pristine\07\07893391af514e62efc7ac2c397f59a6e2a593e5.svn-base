#ifndef ACCESSG100WAVEDATASAVE_H
#define ACCESSG100WAVEDATASAVE_H

#include "dataSave/DataSave.h"
#include "module_global.h"
#include "pds/pdsmapdefine.h"
#include "pds/wavedatamap.h"

//ca wave检测数据信息
typedef struct _AccessG100WaveDataInfo
{
    //file head
    quint8 ucFreq;

    //map head
    DataMapHead stHeadInfo;            //图谱通用的头部信息


    //map ext
    PDSMapNS::PDSWaveMapInfo stInfo;

    //map data
    QVector<double> vecData;

    QVector<double> vecTime;

    QString qsBinaryFilePath;
    _AccessG100WaveDataInfo()
    {
        ucFreq = 50;
        vecData.clear();
        vecTime.clear();
        qsBinaryFilePath.clear();
    }
}AccessG100WaveDataInfo;

class MODULESHARED_EXPORT AccessG100WaveDataSave : public DataSave
{
public:
    /************************************************
     * 函数名   : AccessG100WaveDataSave
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 构造函数
     ************************************************/
    AccessG100WaveDataSave();

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData);

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData, const QString &qsSavedPath);

    INT32 getData(const QString& strFileName, void *pData);

    void setSaveDataTime(QDateTime SaveDataDateTime);

protected:
    /************************************************
     * 函数名   : getDataTypeFolder
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件夹名
     * 功能     : 获取图谱类型文件存储最上层文件夹名
     ************************************************/
    QString getDataTypeFolder(void);

    /************************************************
     * 函数名   : getFileNameSuffix
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件后缀名
     * 功能     : 获取数据文件后缀名
     ************************************************/
    QString getFileNameSuffix(void);

    /************************************************
     * 函数名   : getStringFromData
     * 输入参数 : pDatas: 数据; uiCounts: 数据个数
     * 输出参数 : NULL
     * 返回值   : 转换后的字符串
     * 功能     : 将数据转成base64的字符串
     ************************************************/
    QString getStringFromData( void *pDatas, UINT32 uiCounts);

    /************************************************
     * 函数名   : saveExtInformation
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储可扩展信息
     ************************************************/
    void saveExtInformation(XMLDocument& doc);

    /************************************************
     * 函数名   : saveRawData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储数据部分
     ************************************************/
    void saveRawData(XMLDocument& doc);


    /************************************************
      * 函数名   : parseData
      * 输入参数 : baData: 数据
      * 输出参数 : pData: 解析到的数据
      * 返回值   : void
      * 功能     : 解析数据
      ************************************************/
    void parseData(const QByteArray& baData, void *pData, const QString& strFileName = "");

    /************************************************
     * 函数名   : getFileName
     * 输入参数 :  dateTime: 时间
     * 输出参数 : NULL
     * 返回值   : 文件名
     * 功能     : 从日期生成指定格式文件名
     ************************************************/
    QString getFileName(const QDateTime& dateTime);

    /************************************************
     * 函数名   : saveHeadData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储数据头信息
     ************************************************/
    void saveHeadData(XMLDocument& doc);

    /************************************************
     * 函数名   : saveToDataFile
     * 输入参数 : eTestedDataType: 测试数据类型; baData: 数据
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 保存到数据文件
     ************************************************/
    bool saveToDataFile(TestedDataType eTestedDataType, QByteArray baData);



private:
    void getFileHead(DataFile *psDataFile);

    void getMapHead(DataFile *psDataFile, WaveDataMap * pMap);

    void getMapInfo(WaveDataMap * pMap);

    /****************************
    功能： 获取图谱ext数据
    输入参数:
        pMap -- 文件解析得到的图谱
        iDataPointCount -- 图谱数据数量
    *****************************/
    void getMapInfo( const PDSMapNS::PDSWaveMapInfo &stMapInfo);

    void setPlaybackData(WaveDataMap *pWaveMap, AccessG100WaveDataInfo *pstInfo);

    void setMapHead(DataMap *pMap);

    void setMapInfo(WaveDataMap *pMap);

    void setMapData(WaveDataMap *pMap);

    void addMap(DataFile *pFile);

private:
    AccessG100WaveDataInfo *m_pAccessG100WaveDataInfo;     //接入G100  Wave数据信息
    QString m_strBinaryFile;   //二进制文件名
    QDateTime m_SaveDataDateTime;
};

#endif // ACCESSG100WAVEDATASAVE_H
