/*
 * bmp.h
 *
 *  Created on: May 22, 2015
 *      Author: feng
 */

#ifndef BMP_H_
#define BMP_H_

#ifdef  __cplusplus
extern "C" {
#endif

typedef   struct   tagBITMAPFILEHEADER   {
      unsigned short     bfType;
      unsigned int       bfSize;
      unsigned short     bfReserved1;
      unsigned short     bfReserved2;
      unsigned int       bfOffBits;
} __attribute__((packed)) BITMAPFILEHEADER,   *PBITMAPFILEHEADER ;

typedef struct tagBITMAPINFOHEADER{
	unsigned int biSize; // 本结构所占用字节数
	long biWidth; // 位图的宽度，以像素为单位
	long biHeight; // 位图的高度，以像素为单位
	unsigned short biPlanes; // 目标设备的级别，必须为1
	unsigned short biBitCount;// 每个像素所需的位数，必须是1(双色),
	// 4(16色)，8(256色)或24(真彩色)之一
	unsigned int biCompression; // 位图压缩类型，必须是 0(不压缩),
	// 1(BI_RLE8压缩类型)或2(BI_RLE4压缩类型)之一
	unsigned int biSizeImage; // 位图的大小，以字节为单位
	long biXPelsPerMeter; // 位图水平分辨率，每米像素数
	long biYPelsPerMeter; // 位图垂直分辨率，每米像素数
	unsigned int biClrUsed;// 位图实际使用的颜色表中的颜色数
	unsigned int biClrImportant;// 位图显示过程中重要的颜色数
} __attribute__((packed)) BITMAPINFOHEADER;
typedef struct tagRGBQUAD {
  unsigned char rgbBlue;
  unsigned char rgbGreen;
  unsigned char rgbRed;
  unsigned char rgbReserved;
} RGBQUAD;
typedef struct tagBITMAPINFO {
    BITMAPINFOHEADER    bmiHeader;
    RGBQUAD       bmiColors[1];
} __attribute__((packed)) BITMAPINFO,  *LPBITMAPINFO, *PBITMAPINFO;

extern  const unsigned char iron[128][3];

int createbmp(char *fname,int width,int height,unsigned char *pdata);
int create_ir_bmp(char *fname,int width,int height,unsigned char *pdata);

#ifdef  __cplusplus
}
#endif

#endif /* BMP_H_ */
