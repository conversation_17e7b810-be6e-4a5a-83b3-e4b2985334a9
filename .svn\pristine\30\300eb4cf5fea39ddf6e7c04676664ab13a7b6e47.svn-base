#include "uploadcontroller.h"
#include "appserverutils.h"
#include "../appserverdefine.h"
#include "pda/cloud/qjason/qjson.h"
#include "pda/pda.h"
#include <QFileInfo>
#include <QDateTime>
#include "globalerrprocess.h"
#include "log/log.h"

using namespace errorProcess;
using namespace AppServerNS;

UploadController::UploadController(QObject *parent) : HttpRequestHandler(parent)
{

}

UploadController::~UploadController()
{

}

void UploadController::service( HttpRequest& request, HttpResponse& response )
{
    int responseCode = REPLY_SUCCESS_CODE;
    /*
    //解析request判断有效性
    QByteArray strPara = request.getParameter( TIME_STAMP_KEY );
    long requestTime = strPara.toLong();
    if( !AppServerUtils::isTimestampValid( requestTime ) )
    {
        responseCode = SIGNATURE_INVALID_ERR;
    }
    */

    QJson respJson;//应答的Json内容

    if(REPLY_SUCCESS_CODE == responseCode)
    {
        //计算MD5判断是否正确
        QByteArray fileContent = request.getParameter(FILE_CONTENT_KEY);  //文件内容
        QString strSuffix = request.getParameter(FILE_TYPE_KEY);          //后缀名
        QByteArray oriMd5 = request.getParameter(BLOCK_MD5_KEY);          //原始MD5码
        if(oriMd5 != AppServerUtils::calcMd5Value(fileContent))
        {
            logError("md5 check error.");
            responseCode = MD5_UNMATCH_ERROR;
        }

        if(REPLY_SUCCESS_CODE == responseCode)
        {
            //保存文件内容到临时文件中，文件名为md5码 AppServerUtils::saveTmpFile
            QString savedPath = ""; //临时文件的保存路径
            if(!AppServerUtils::saveTmpFile(fileContent, oriMd5, strSuffix, savedPath))
            {
                responseCode = FILE_OP_ERROR;
            }
            else
            {
                QJson resultJson;
                QFileInfo fileInfo(savedPath);
                uint ctime = fileInfo.created().toTime_t();
                uint mtime = fileInfo.lastModified().toTime_t();
                resultJson.add(CREATE_TIME_KEY, QString::number(ctime).toLatin1());
                resultJson.add(MODIFY_TIME_KEY, QString::number(mtime).toLatin1());
                resultJson.add(UPLOAD_PATH_KEY, savedPath.toUtf8());
                resultJson.add(FILE_SIZE_KEY, QString::number(fileInfo.size()).toLatin1());
                resultJson.add(BLOCK_MD5_KEY, oriMd5);
                respJson.add(REPLY_RESULT_KEY, resultJson);
            }
        }
    }

    //应答报文填充
    respJson.add(REPLY_CODE_KEY, QString::number(responseCode).toLatin1());
    respJson.add(REPLY_MSG_KEY, AppServerUtils::stateMsgByCode(responseCode).toUtf8());

    response.write(respJson.unformattedData(), true);       //解决content-length没有的问题
    return;
}
