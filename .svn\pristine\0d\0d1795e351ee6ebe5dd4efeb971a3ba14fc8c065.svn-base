#include "httpclientbean.h"
#include <QNetworkAccessManager>
#include <QUrl>
#ifndef QT_NO_OPENSSL
#include <QSslSocket>
#include <QSslKey>
#include <QSslCertificate>
#endif
#include <QFile>
#include <QList>
#include <QTimer>
#include <QEventLoop>
#include <QNetworkReply>
#include <QDebug>

namespace HTTPModule
{

const int g_iRetransTimes = 3;
const int g_iSendDataTimeOut = 10000;
/******************************************************
 * 功能：构造函数
 * 输入参数：
 *      parent：父指针
 * *****************************************************/
HttpClientBean::HttpClientBean(QObject *parent)
    : QObject(parent)
    , m_bStop(false)
    , m_bHttps(false)
    #ifndef QT_NO_OPENSSL
    , m_pSslConfig(NULL)
    #endif
{
    QObject::connect(this, SIGNAL(sigsendRequest()), this, SLOT(onSendRequest()));
#ifndef QT_NO_OPENSSL
    if(!m_pSslConfig)
    {
        m_pSslConfig = new QSslConfiguration();
    }
#endif
}

/*******************************************************
 * 功能：析构函数
 * *****************************************************/
HttpClientBean::~HttpClientBean()
{
    stopRequest();

#ifndef QT_NO_OPENSSL
    if(m_pSslConfig)
    {
        delete m_pSslConfig;
        m_pSslConfig = NULL;
    }
#endif
}

/******************************************************
 * 功能：停止发送请求
 * *****************************************************/
void HttpClientBean::stopRequest()
{
    m_bStop = true;
    return;
}

/******************************************************
 * 功能：停止上传
 * *****************************************************/
void HttpClientBean::stopUpload()
{
    stopRequest();
    emit sigStopUpload();
    return;
}

/******************************************************
 * 功能：停止下载
 * *****************************************************/
void HttpClientBean::stopDownload()
{
    stopRequest();
    emit sigStopDownload();
    return;
}

/******************************************************
 * 功能：设置ssl认证信息
 * 输入参数：
 *      stSslVerfCfgInfo：ssl认证配置信息
 * *****************************************************/
void HttpClientBean::setSslVerifyInfo(const HTTPModule::SslVerifyCfgInfo &stSslVerfCfgInfo)
{
    //qDebug() << "HttpClientBean::setSslVerifyInfo, support ssl: " << QSslSocket::supportsSsl()
    //         << ", ssl build ver: " << QSslSocket::sslLibraryBuildVersionString()
    //         << ", ssl library ver: " << QSslSocket::sslLibraryVersionString();
#ifndef QT_NO_OPENSSL
    if(!(QSslSocket::supportsSsl()))
    {
        qCritical("HttpClientBean::setSslVerifyInfo, current platform not support ssl.");
        m_bHttps = false;
        return;
    }

    if(m_pSslConfig)
    {
        //load the local certificate
        QFile objLocalCertFile(stSslVerfCfgInfo.qstrLocalCertFilePath);
        if(objLocalCertFile.open(QIODevice::ReadOnly))
        {
            QSslCertificate objLocalCertInfo(&objLocalCertFile, QSsl::Pem);
            m_pSslConfig->setLocalCertificate(objLocalCertInfo);
            objLocalCertFile.close();
        }

        //load the private key file
        QFile objPriKeyFile(stSslVerfCfgInfo.qstrPriKeyFilePath);
        if(objPriKeyFile.open(QIODevice::ReadOnly))
        {
            QSslKey objPriKey;
            if(stSslVerfCfgInfo.qbaPhrasePwd.isEmpty())
            {
                objPriKey = QSslKey(&objPriKeyFile, QSsl::Rsa, QSsl::Pem);
            }
            else
            {
                objPriKey = QSslKey(&objPriKeyFile, QSsl::Rsa, QSsl::Pem, QSsl::PrivateKey, stSslVerfCfgInfo.qbaPhrasePwd);
            }

            m_pSslConfig->setPrivateKey(objPriKey);
            objPriKeyFile.close();
        }

        //load the ca certificate
        QFile objCaCertFile(stSslVerfCfgInfo.qstrCaCertFilePath);
        if(objCaCertFile.open(QIODevice::ReadOnly))
        {
            QList<QSslCertificate> qlstCaCertInfos = QSslCertificate::fromDevice(&objCaCertFile, QSsl::Pem);
            m_pSslConfig->setCaCertificates(qlstCaCertInfos);
            objCaCertFile.close();
        }

        m_pSslConfig->setPeerVerifyMode(static_cast<QSslSocket::PeerVerifyMode>(stSslVerfCfgInfo.eMode));
        m_pSslConfig->setProtocol(QSsl::AnyProtocol/*QSsl::TlsV1SslV3*/);

        m_bHttps = true;
    }
#endif
    return;
}

/******************************************************
 * 功能：非使能ssl认证
 * *****************************************************/
void HttpClientBean::disableSslVerify()
{
    m_bHttps = false;
    return;
}

/******************************************************
 * 功能：以GET方式发送请求
 * 输入参数：
 *      stReqInfo：请求数据结构信息
 * 输出参数：
 *      stRspInfo：响应数据结构信息
 * 返回值：
 *      bool：请求响应结果，true -- 成功，false -- 失败
 * *****************************************************/
bool HttpClientBean::getData(const HttpRequestInfo &stReqInfo, HttpRsponseInfo &stRspInfo)
{
    bool bRet = sendRequest(stReqInfo, REQ_MODE_GET, stRspInfo);
    return bRet;
}

/******************************************************
 * 功能：以POST方式发送请求
 * 输入参数：
 *      stReqInfo：请求数据结构信息
 * 输出参数：
 *      stRspInfo：响应数据结构信息
 * 返回值：
 *      bool：请求响应结果，true -- 成功，false -- 失败
 * *****************************************************/
bool HttpClientBean::postData(const HttpRequestInfo &stReqInfo, HttpRsponseInfo &stRspInfo)
{
    bool bRet = sendRequest(stReqInfo, REQ_MODE_POST, stRspInfo);
    return bRet;
}

/******************************************************
 * 功能：以PUT方式发送请求
 * 输入参数：
 *      stReqInfo：请求数据结构信息
 * 输出参数：
 *      stRspInfo：响应数据结构信息
 * 返回值：
 *      bool：请求响应结果，true -- 成功，false -- 失败
 * *****************************************************/
bool HttpClientBean::putData(const HttpRequestInfo &stReqInfo, HttpRsponseInfo &stRspInfo)
{   

    bool bRet = sendRequest(stReqInfo, REQ_MODE_PUT, stRspInfo);
    return bRet;
}

/******************************************************
 * 功能：以POST multipart/form-data方式发送请求
 * 输入参数：
 *      stReqInfo：请求数据结构信息
 * 输出参数：
 *      stRspInfo：响应数据结构信息
 * 返回值：
 *      bool：请求响应结果，true -- 成功，false -- 失败
 * *****************************************************/
bool HttpClientBean::postFormData(const HttpRequestInfo &stReqInfo, HttpRsponseInfo &stRspInfo)
{   
    bool bRet = sendRequest(stReqInfo, REQ_MODE_POST_FORM_DATA, stRspInfo);
    return bRet;
}

/******************************************************
 * 功能：业务相关，以POST multipart/form-data方式上传文件内容，文件信息在HttpRequestInfo的QHttpMultiPart中
 * 输入参数：
 *      stReqInfo：请求数据结构信息
 * 输出参数：
 *      stRspInfo：响应数据结构信息
 * 返回值：
 *      bool：请求响应结果，true -- 成功，false -- 失败
 * *****************************************************/
bool HttpClientBean::uploadFile(const HttpRequestInfo &stReqInfo, HttpRsponseInfo &stRspInfo)
{
    bool bRet = sendRequest(stReqInfo, REQ_MODE_POST_FORM_DATA, stRspInfo);
    return bRet;
}

/******************************************************
 * 功能：业务相关，以POST方式下载文件内容，文件内容在HttpRsponseInfo的qbaRspData中
 * 输入参数：
 *      stReqInfo：请求数据结构信息
 * 输出参数：
 *      stRspInfo：响应数据结构信息
 * 返回值：
 *      bool：请求响应结果，true -- 成功，false -- 失败
 * *****************************************************/
bool HttpClientBean::downloadFile(const HttpRequestInfo &stReqInfo, HttpRsponseInfo &stRspInfo)
{
    bool bRet = sendRequest(stReqInfo, REQ_MODE_POST, stRspInfo);
    return bRet;
}


/******************************************************
 * 功能：发送请求
 * 输入参数：
 *      stReqInfo：请求数据结构信息
 *      eReqMode：请求方式
 * 输出参数：
 *      stRspInfo：响应数据结构信息
 * 返回值：
 *      bool：请求响应结果，true -- 成功，false -- 失败
 * *****************************************************/
void HttpClientBean::onSendRequest()
{
    QNetworkRequest stRequest(QUrl(m_stReqInfo.qstrUrl));

    if(m_stReqInfo.qstrContentTypeHeader.isEmpty())
    {
        if(m_eReqMode != REQ_MODE_POST_FORM_DATA)
        {
            stRequest.setHeader(QNetworkRequest::ContentTypeHeader, QVariant("application/json;charset=UTF-8"));
        }
        else
        {
            //stRequest.setHeader(QNetworkRequest::ContentTypeHeader, QVariant("multipart/form-data;charset=UTF-8"));
        }
    }
    else
    {
        stRequest.setHeader(QNetworkRequest::ContentTypeHeader, QVariant::fromValue(m_stReqInfo.qstrContentTypeHeader));
    }

    for(QHash<QString, QByteArray>::const_iterator iter = m_stReqInfo.qhash4RawHeaders.begin();
        iter != m_stReqInfo.qhash4RawHeaders.end(); ++iter)
    {
        stRequest.setRawHeader(iter.key().toLatin1(), iter.value());
    }
#ifndef QT_NO_OPENSSL
    if(m_bHttps && m_pSslConfig)
    {
        stRequest.setSslConfiguration(*m_pSslConfig);
    }
#endif
    bool bRet = false;
    int iSendTimes = 0;
    m_bStop = false;

    do
    {
        QNetworkAccessManager stAccessManager;
        pReply =NULL;

        switch (m_eReqMode)
        {
        case REQ_MODE_GET:
        {
            pReply = stAccessManager.get(stRequest);
            break;
        }
        case REQ_MODE_POST:
        {
            pReply = stAccessManager.post(stRequest, m_stReqInfo.qbaReqData);
            break;
        }
        case REQ_MODE_PUT:
        {
            pReply = stAccessManager.put(stRequest, m_stReqInfo.qbaReqData);
            break;
        }
        case REQ_MODE_POST_FORM_DATA:
        {
            if(m_stReqInfo.pHttpMtPart)
            {
                pReply = stAccessManager.post(stRequest, m_stReqInfo.pHttpMtPart);
                //stReqInfo.pHttpMtPart->setParent(pReply);//多次重传有问题，不能在这里绑定释放，由外部new的地方进行释放，满足谁申请谁释放的原则
            }
            break;
        }
        default:
        {
            pReply = stAccessManager.get(stRequest);
            break;
        }

        }

        if(!pReply)
        {
            qCritical("HttpClientBean::sendRequest, pReply is NULL.");
            m_stRspInfo.iRspCode = -1;
            //break;
        }
        QTimer stTimer;
        stTimer.setInterval(m_stReqInfo.iTimeout);
        stTimer.setSingleShot(true);

        QEventLoop stLoop;

        QObject::connect(this, SIGNAL(sigStopDownload()), &stTimer, SLOT(stop()));
        QObject::connect(this, SIGNAL(sigStopDownload()), &stLoop, SLOT(quit()));

        QObject::connect(this, SIGNAL(sigStopUpload()), &stTimer, SLOT(stop()));
        QObject::connect(this, SIGNAL(sigStopUpload()), &stLoop, SLOT(quit()));

        QObject::connect(&stTimer, SIGNAL(timeout()), &stLoop, SLOT(quit()));

        QObject::connect(pReply, SIGNAL(uploadProgress(qint64, qint64)), this, SIGNAL(sigUploadProgress(qint64, qint64)));
        QObject::connect(pReply, SIGNAL(downloadProgress(qint64, qint64)), this, SIGNAL(sigDownloadProgress(qint64, qint64)));

        QObject::connect(pReply, SIGNAL(finished()), &stLoop, SLOT(quit()));

        stTimer.start();
        stLoop.exec();


        if (stTimer.isActive())
        {
            stTimer.stop();

            if (QNetworkReply::NoError == pReply->error())
            {
                m_stRspInfo.qbaRspData = pReply->readAll();

                for(QHash<QString, QByteArray>::iterator iter = m_stRspInfo.qhash4RspRawHeaders.begin();
                    iter != m_stRspInfo.qhash4RspRawHeaders.end(); ++iter)
                {
                    *iter = pReply->rawHeader(iter.key().toLatin1());
                    //(*iter).append(pReply->rawHeader(iter.key().toLatin1()));
                }

                m_stRspInfo.iRspCode = static_cast<int>(QNetworkReply::NoError);
                qDebug()<<"m_stRspInfo.iRspCode"<<m_stRspInfo.qbaRspData;
                bRet = true;
            }
            else
            {
                m_stRspInfo.qbaRspData = pReply->readAll();
                m_stRspInfo.iRspCode = static_cast<int>(pReply->error());
                bRet = false;
                qCritical("HttpClientBean::sendRequest, request (%s) error (%d), info (%s).", m_stReqInfo.qstrUrl.toLatin1().data(), m_stRspInfo.iRspCode, pReply->errorString().toLatin1().data());
                qCritical("HttpClientBean::sendRequest, request (%s) .", m_stRspInfo.qbaRspData.data());
            }
        }
        else
        {
            QObject::disconnect(pReply, SIGNAL(finished()), &stLoop, SLOT(quit()));
            pReply->abort();
            m_stRspInfo.iRspCode = static_cast<int>(QNetworkReply::TimeoutError);
            bRet = false;
            qCritical("HttpClientBean::sendRequest, request (%s) timeout.", m_stReqInfo.qstrUrl.toLatin1().data());
        }

        pReply->deleteLater();
        pReply = NULL;
        iSendTimes++;
    }while ((!m_bStop) && (!bRet) && iSendTimes <1);
    m_bResult = bRet;
    qDebug()<<"m_bResult------------------------"<<m_bResult;
    m_semaphoreSendData.release();

}

/******************************************************
 * 功能：发送请求
 * 输入参数：
 *      stReqInfo：请求数据结构信息
 *      eReqMode：请求方式
 * 输出参数：
 *      stRspInfo：响应数据结构信息
 * 返回值：
 *      bool：请求响应结果，true -- 成功，false -- 失败
 * *****************************************************/
bool HttpClientBean::sendRequest(const HttpRequestInfo &stReqInfo, HttpReqMode eReqMode, HttpRsponseInfo &stRspInfo)
{
    QMutexLocker lock(&m_mutexSendData);
    m_semaphoreSendData.acquire(m_semaphoreSendData.available());
    int iTimout = 0;
    if(REQ_MODE_POST == eReqMode)
    {
        iTimout = 5000;
        m_stReqInfo.iTimeout = 3000;
    }else
    {
        iTimout = 100000;
        m_stReqInfo.iTimeout            = stReqInfo.iTimeout;
    }
    m_stReqInfo.qstrUrl             = stReqInfo.qstrUrl;
    //m_stReqInfo.iTimeout            = stReqInfo.iTimeout;
    m_stReqInfo.pHttpMtPart         = stReqInfo.pHttpMtPart;
    m_stReqInfo.qbaReqData          = stReqInfo.qbaReqData;
    m_stReqInfo.qhash4RawHeaders    = stReqInfo.qhash4RawHeaders;
    m_stReqInfo.qstrContentTypeHeader = stReqInfo.qstrContentTypeHeader;
    m_eReqMode  = eReqMode;

    emit sigsendRequest();
    if (m_semaphoreSendData.tryAcquire(1, iTimout))
    {
        stRspInfo.iRspCode = m_stRspInfo.iRspCode;
        stRspInfo.qbaRspData = m_stRspInfo.qbaRspData;
        stRspInfo.qhash4RspRawHeaders = m_stRspInfo.qhash4RspRawHeaders;
        return m_bResult;
    }

    return m_bResult;
}


}
