/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* SystemSetService.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年5月9日
* 摘要：系统设置服务模块接口定义

* 当前版本：1.0
*/

#ifndef SYSTEMSETSERVICE_H
#define SYSTEMSETSERVICE_H


#include "SystemSet.h"
#include "module_global.h"
#include "Module.h"
#include "datadefine.h"
#include "UHFHFCTAETEVApi.h"
#include "usbmode.h"
#include "bluetoothsetservice.h"
#include "dataspecification/dataspecification_def.h"

class QThread;
class MODULESHARED_EXPORT SystemSetService : public QObject
{
    Q_OBJECT
public:
    /****************************
    功能： 模块单例
    *****************************/
    static SystemSetService* instance();

    /*************************************************
    功能： 设置系统日期
    入参：strDate -- 日期
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool setSystemDate( const QString& strDate );

    /************************************************
     * 函数名   : zigbeeVersion(WLDeviceVer *pstWLDeviceVer)
     * 输入参数 : NULL
     * 输出参数 : pstWLDeviceVer--存放版本信息的数据指针
     * 返回值   : 操作是否成功
     * 功能     : 获取Zigbee版本信息
     ************************************************/
    bool zigbeeVersion(WLDeviceVer *pstWLDeviceVer);

    /*************************************************
    功能： 设置gprs的apn信息
    入参：strAPN -- apn信息
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool setMobileAPN(const QString &strAPN);

    /*************************************************
    功能： 设置gprs的apn信息
    入参：strUser -- 用户名
         strPWD -- 密码
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool setMobileAccount(const QString &strUser, const QString &strPWD);

    /*************************************************
    功能： 获取gprs的apn信息
    出参：strAPN -- apn信息
    返回值：void
    *************************************************/
    void getMobileAPN(QString &strAPN);

    /*************************************************
    功能： 获取gprs的用户名和密码
    出参：strUser -- 用户名
         strPWD -- 密码
    返回值：void
    *************************************************/
    void getMobileAccount(QString &strUser, QString &strPWD);

    /*************************************************
    功能： 重新初始化gprs服务
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool reinitMobileService();

    /************************************************
     * 函数名   : stm32Version(QString &strVersion)
     * 输入参数 : NULL
     * 输出参数 : strVersion---返回的STM32版本号
     * 返回值   : 操作是否成功
     * 功能     : 获取STM32版本号
     ************************************************/
    void stm32Version(QString &strVersion);

    /*************************************************
    功能： 获得设备序列号
    返回值：设备序列号
    *************************************************/
    const UINT8 *deviceIdNum( void );

    /*************************************************
    功能： 获得设备序列号
    返回值：设备序列号
    *************************************************/
    QString getDevSerialNum(void);

    /*************************************************
    功能： 获得设备应用软件版本号
    返回值：设备应用软件版本号
    *************************************************/
    QString getDevSoftwareVersion(void);

    /*************************************************
    功能： 获得设备应用软件版本号
    返回值：设备应用软件版本号
    *************************************************/
    QString curSoftwareVersion();

    /*************************************************
    功能： 设置背光
    入参： eOption -- 背光选项
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool setBackLight( SystemSet::BLOption eOption );

    /*************************************************
    功能： 设置频率
    入参： eOption -- 频率选项
    *************************************************/
    void setFrequency( SystemSet::FreqOption eOption );

    /*************************************************
    功能： 设置频率
    返回值： 系统电网频率
    *************************************************/
    int getFrequency();

    /*************************************************
    功能： 设置语言
    入参： eOption -- 语言选项
    *************************************************/
    void setLanguage( SystemSet::LanuageOption eOption );

    /*************************************************
    功能： 获取语言
    入参： void
    返回值：语言值
    *************************************************/
    int getLanguage();

    /*************************************************
    功能： 4g连接状态
    返回值：true -- 已连接
          false -- 未连接
    *************************************************/
    bool isFourGConnected( void );

    /*************************************************
    功能： 设置4G选项
    入参： 开或关
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool setFourGOption( SystemSet::FourGOption eOption );

    /*************************************************
    功能： 设置热点选项
    入参： 开或关
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool setHotSpotOption( SystemSet::HotSpotOption eOption );

    /*************************************************
    功能： 获取蓝牙设置服务实例
    返回值： 蓝牙设置服务实例
    *************************************************/
    BluetoothSetService* bluetoothSetService();
    /*************************************************
    功能： 断电Z200设备
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool shutDownSystem( void );

    /*************************************************
    功能： 开关4G电源
    入参：true -- 打开
         false -- 关闭
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool switchFourGPower( bool bOn );

    /*************************************************
    功能： 唤醒or休眠4G
    入参：true -- 唤醒
         false -- 休眠
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool wakeSleepFourG( bool bWake );

    /*************************************************
    功能： poe上电控制
    入参：true -- 开启
         false -- 关闭
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool poePowerCtrl( bool bOn );

    /*************************************************
    功能： 关闭软键盘
    *************************************************/
    void closeKeyBoard( void );

    /*************************************************
    功能： 初始化串口
    *************************************************/
    void initSerialPort( void );

    /*************************************************
    功能： 关闭串口
    *************************************************/
    void exitSerialPort( void );

    /*************************************************
    功能： 初始化软键盘
    *************************************************/
    void initKeyBoard( void );

    /*************************************************
    功能： stm程序是否运行在app区
    返回值：true -- 是
          false -- 否
    *************************************************/
    bool isStmInApp( void );

    /*************************************************
    功能： stm跳转至app区
    返回值：true -- 成功
          false -- 失败
    *************************************************/
    bool stmGotoApp( void );

    /*************************************************
    功能： 获取电量百分比
    返回值：电量百分比，负数表示通讯问题，不进行处理
    *************************************************/
    float batteryPercent( void );

    /*************************************************
    功能： wifi连接状态
    返回值：true -- 已连接
          false -- 未连接
    *************************************************/
    bool isWifiConnected( void );

    /*************************************************
    功能： BT连接状态
    返回值：true -- 已连接
          false -- 未连接
    *************************************************/
    bool isBTConnected( void );

    /*************************************************
    功能： 开始进行固件更新
    出参： pStmPercent -- stm32的更新进度
          pZigbeePercent -- zigbee的更新进度
    *************************************************/
    void beginToUpdateFirmware( quint8 * pStmPercent,
                                quint8 * pZigbeePercent );

    /*************************************************
    功能： get current hot spot option
    *************************************************/
    SystemSet::HotSpotOption hotSpotOption();

    /*************************************************
    功能： set hot spot option value
    *************************************************/
    void setHotSpotOptionVal(SystemSet::HotSpotOption eOption);

    /*************************************************
    功能： get current disk info
    *************************************************/
    bool getDiskUsedInfo(quint64& llUsedSize, quint64& llAllSize, qint32 &iAvailablePercent);

    /*************************************************
    功能： remove the useless files
          etc:install packages,coredump files,temp files
    *************************************************/
    bool removeUselessFiles();

    /*************************************************
    功能： 设置USB状态
    入参： 开或关
    *************************************************/
    void setUSBState(SystemSet::USBState eOption, bool bDirectCall = false);

    /*************************************************
    功能： 设置USB模式
    入参： 开或关
    返回值：true -- 成功
           false -- 失败
    *************************************************/
    bool setUSBMode(SystemSet::AppUsbMode eOption);

    /*************************************************
    功能： 获取USB模式
    返回值：USB的模式值
    *************************************************/
    SystemSet::AppUsbMode getUSBMode();

    /*************************************************
    功能： 记录存储卡已被格式化成功
    *************************************************/
    void storageFormatted();

    /*************************************************
    功能：关闭wifi连接
    *************************************************/
    void closeWifiConnection();

    /**************************************************
     *功能：控制模拟板上电
     *输入参数：
     *      bOpen：true -- 上电，false -- 下电
     * *************************************************/
    void openAnalogBoardPower(bool bOpen);

    /***************************************************
     * 功能：存储卡是否可操作
     * 返回值：
     *      bool：true -- 可进行操作，false -- 不可进行操作
     * *************************************************/
    bool storageOperEnable();

    /************************************
     * 功能：设置巡检跳转模式
     * 输入参数：
     *      eMode：巡检跳转模式
     * **********************************/
    void setPatrolSwitchMode(SystemSet::PatrolSwitchMode eMode);

    /************************************
     * 功能：获取巡检跳转模式
     * 返回值：
     *      PatrolSwitchMode：巡检跳转模式
     * **********************************/
    SystemSet::PatrolSwitchMode getPatrolSwitchMode();

    /************************************
     * 功能：设置当前定制接入终端接入模式
     * 输入参数：
     *      eMode：0 -- 有线USB直连，1 -- 蓝牙连接，2 -- wifi连接
     * **********************************/
    void setCustomAccessMode(SystemSet::CustomAccessMode eMode);

    /************************************
     * 功能：获取当前定制接入终端接入模式
     * 返回值：
     *      CustomAccessMode：0 -- 有线USB直连，1 -- 蓝牙连接，2 -- wifi连接
     * **********************************/
    SystemSet::CustomAccessMode getCustomAccessMode();

    /************************************
     * 功能：设置设备本地校时
     * 输入参数：
     *      qi64CalibUtcTime：utc时间，单位秒
     *      dTimezoneVal：时区值
     * 返回值：
     *      QString：当前校时后的时间信息，格式为yyyy-MM-dd hh:mm:ss
     * **********************************/
    QString setLocalCalibrateTime(qint64 qi64CalibUtcTime, double dTimezoneVal);

    /************************************
     * 功能：设置设备激活码信息
     * 输入参数：
     *      qbaActiveCodeInfo：激活码信息
     * 返回值：
     *      bool：true -- 激活码设置正确，false -- 激活码设置失败
     * **********************************/
    bool setSysActivateCodeInfo(const QByteArray &qbaActiveCodeInfo);

    /************************************
     * 功能：保存最终用户信息
     * 输入参数：
     *      qstrFinalUserInfo：最终用户信息
     *      bShow：是否显示用户信息，0 -- 不显示，1 -- 显示
     * **********************************/
    void saveFinalUserInfo(const QString &qstrFinalUserInfo, bool bShow);

    /************************************
     * 功能：设备校准功能是否起作用
     * 返回值：
     *      bool：true -- 起作用，false -- 不起作用
     * **********************************/
    bool devCalibrateEnable();

    /************************************
     * 功能：保存当前选中标签的索引
     * 输入参数：
     *
     * **********************************/
    void  saveWifiSelectedIndex(int index, int iSigStrength);

    /************************************
     * 功能：保存当前选中wifi的热点名
     * 输入参数： index 被选中标签索引 strWifiName 热点名
     *
     * **********************************/
    void saveSelectedWifiName(int index, QString strWifiName);

    /************************************
     * 功能：获取被选中索引对应的信号强度
     * 输入参数：
     *
     * **********************************/
    int  readWifiSelectedSigStrength();

    /************************************
     * 功能：获取被选中wifi的热点名
     * **********************************/
    QString readSelectedWifiName();

    /************************************
     * 功能：获取接入终端接入协议类型
     * 返回值：
     *      SystemSet::AccessProtocol：接入终端接入协议类型
     * **********************************/
    SystemSet::AccessProtocol getAccessProtocol();

    /************************************
     * 功能：判断SSL认证是否使能状态
     * 返回值：
     *      bool：状态，true -- 使能，false -- 非使能
     * **********************************/
    bool isSSLVerifyEnable();

    /************************************
     * 功能：设置SSL认证开关状态值
     * 输入参数：
     *      eVal：SSL认证开关状态值
     * **********************************/
    void setSSLVerifyVal(SystemSet::SslVerifySwitch eVal);

    /************************************
     * 功能：获取SSL认证模式
     * 返回值：
     *      SystemSet::SslVerifyMode：SSL认证模式
     * **********************************/
    SystemSet::SslVerifyMode getSSLVerifyMode();

    /************************************
     * 功能：设置SSL认证模式
     * 输入参数：
     *      eVal：SSL认证模式
     * **********************************/
    void setSSLVerifyMode(SystemSet::SslVerifyMode eVal);

    /************************************
     * 功能：判断Tev校准写入是否使能状态
     * 返回值：
     *      bool：状态，true -- 使能，false -- 非使能
     * **********************************/
    bool isTevCalibWriteEnable();

    /************************************
     * 功能：设置Tev校准写入开关状态值
     * 输入参数：
     *      eVal：Tev校准写入开关状态值
     * **********************************/
    void setTevCalibWriteVal(SystemSet::TevCalibWriteSwitch eVal);

    /************************************
     * 功能：设置软键盘模式
     * 输入参数：
     *      eMode：软键盘模式
     * **********************************/
    void setKeyBoardMode(SystemSet::KeyboardMode eMode);

    /************************************
     * 功能：获取软键盘模式
     * 返回值：
     *      SystemSet::KeyBoardMode：软键盘模式
     * **********************************/
    SystemSet::KeyboardMode getKeyBoardMode();

    /************************************************
     * 功能：获取系统设置音量
     * 返回值：
     *      quint8：系统音量
     ************************************************/
    quint8 getSystemVolume();

    /************************************************
     * 功能：获取AE噪声增益状态
     * 返回值：
     *      quint8：增益状态
     ************************************************/
    quint8 getAESoundGain();

    /************************************************
     * 功能：获取AE通道类型
     * 返回值：
     *      quint8：AE通道类型
     ************************************************/
    quint8 getAEChannelType();

    /*******************************************************
     * 功能：读取固件概要信息
     * 输入参数：
     *      stFirmInfo：固件概要信息
     * **********************************************************/
    void readFirmwareInfo(SystemSet::FirmwareInfo &stFirmInfo);

    /*******************************************************
     * 功能：初始化读取固件概要信息
     * 输入参数：
     *      stFirmInfo：固件概要信息
     * **********************************************************/
    void initFirmwareInfo(SystemSet::FirmwareInfo &stFirmInfo);

    /*******************************************************
     * 功能：重新读取固件概要信息
     * 输入参数：
     *      stFirmInfo：固件概要信息
     * **********************************************************/
    void reloadFirmwareInfo(SystemSet::FirmwareInfo &stFirmInfo);

    /******************************************************
     * 功能：读取固件概要信息
     * 输出参数：
     *      stFirmInfo：固件概要信息
     * **********************************************************/
    void getFirmwareInfo(SystemSet::FirmwareInfo &stFirmInfo);

    /******************************************************
     * 功能：读取固件类型
     * 返回值：
     *      SystemSet::FirmType：固件类型
     * **********************************************************/
    SystemSet::FirmType getFirmwareType();

    /******************************************************
     * 功能：获取设备类型
     * 返回值：
     *      QString：设备类型
     * **********************************************************/
    QString getDevModel();

    /******************************************************
     * 功能：获取公司名称
     * 返回值：
     *      QString：公司名称
     * **********************************************************/
    QString getCompanyName();

    /*******************************************************
     * 功能：写入固件概要信息
     * 输入参数：
     *      stFirmInfo：固件概要信息
     *      bNetworkSetting：是否来自于网络设置的调用
     * **********************************************************/
    void setFirmwareInfo(const SystemSet::FirmwareInfo &stFirmInfo, bool bNetworkSetting = true);

    /******************************************************
     * 功能：读取VPN接入信息
     * 输出参数：
     *      stVpnAccessInfo：VPN接入信息
     * **********************************************************/
    void getVpnAccessInfo(SystemSet::VpnAccessInfo &stVpnAccessInfo);

    /*******************************************************
     * 功能：写入VPN接入信息
     * 输入参数：
     *      stVpnAccessInfo：VPN接入信息
     * **********************************************************/
    void setVpnAccessInfo(const SystemSet::VpnAccessInfo &stVpnAccessInfo);

    /*******************************************************
     * 功能：初始化远程升级信息
     * **********************************************************/
    void initUpgradeInfo();

    /*******************************************************
     * 功能：初始化LCD
     * **********************************************************/
    void initLCD();

    /*******************************************************
     * 功能： 设置数据规范版本号
     * 输入参数：
     *      DataSpecificationNS::DataSpecificationVersion：数据规范版本号
     * **********************************************************/
    void setDataSpecificationVersion(const DataSpecificationNS::DataSpecificationVersion eDataSpecificationVersion);

    /******************************************************
     * 功能：获取数据规范版本号
     * 返回值：
     *      DataSpecificationNS::DataSpecificationVersion：数据规范版本号
     * **********************************************************/
    DataSpecificationNS::DataSpecificationVersion getDataSpecificationVersion();

    /******************************************************
     * 功能：设置实时诊断开关
     * 输入参数：
     *      eSwitch：开关值
     * **********************************************************/
    void setRealtimeDiagnosisSwitch(SystemSet::RealtimeDiagnosisSwitch eSwitch);

    /******************************************************
     * 功能：获取实时诊断开关
     * 输入参数：
     *      SystemSet::RealtimeDiagnosisSwitch：开关值
     * **********************************************************/
    SystemSet::RealtimeDiagnosisSwitch getRealtimeDiagnosisSwitch();

    /******************************************************
     * 功能：设置PRPS采样间隔，单位ms
     * 输入参数：
     *      iInterval：采样间隔，单位ms
     * **********************************************************/
    void setPRPSSampleInterval(int iInterval);

    /******************************************************
     * 功能：获取PRPS采样间隔，单位ms
     * 返回值：
     *      int：采样间隔，单位ms
     * **********************************************************/
    int getPRPSSampleInterval();

    /******************************************************
     * 功能：设置PRPS自动同步
     * 输入参数：
     *      bAutoSync：是否同步
     * **********************************************************/
    void setPRPSAutoSync(bool bAutoSync);

    /******************************************************
     * 功能：获取PRPS自动同步
     * 返回值：
     *      是否同步
     * **********************************************************/
    bool getPRPSAutoSync();

    /******************************************************
     * 功能：设置文件备注框使能
     * 输入参数：
     *      bEnable：是否开启
     * **********************************************************/
    void setFileCommentBoxEnabled(bool bEnable);

    /******************************************************
     * 功能：获取文件备注框使能状态
     * 返回值：
     *      是否使能
     * **********************************************************/
    bool isFileCommentBoxEnabled();

    /******************************************************
     * 功能：获取温湿度信息
     * 输出参数：
     *      fTemperature：温度
     *      fHumidity：湿度
     * **********************************************************/
    bool getTemperatureHumidityInfo(float& fTemperature, float& fHumidity);

signals:
    /*************************************************
    功能： 固件更新结果的信号
    参数： iResult -- 更新的结果
    *************************************************/
    void sigUpdateFwResult( qint32 iResult );

    /*************************************************
    功能： 开始固件更新的信号
    出参： pStmPercent -- stm32的更新进度
          pZigbeePercent -- zigbee的更新进度
    *************************************************/
    void sigStartFwUpdate( quint8 * pStmPercent,
                           quint8 * pZigbeePercent );
    /*************************************************
    功能： 发射创建WiFi、蓝牙等子服务的信号
    目的： 在服务执行线程创建子服务实例
    *************************************************/
    void sigCreateSubService();

    /*************************************************
    功能： 发射设置usb状态的结果信号
    *************************************************/
    void sigSetUSBResult( SystemSet::USBState eOption, bool bResult );

    /*************************************************
    功能： 发射设置usb状态的信号
    *************************************************/
    void sigSetUSBState( SystemSet::USBState eOption );

    /*************************************************
    功能： 发射存储卡已被格式化的信号
    *************************************************/
    void sigStorageFormatted();

    /*************************************************
    功能： 发射存储卡已挂载的信号
    *************************************************/
    void sigStorageMounted();

    /*************************************************
    功能： 发射wifi连接状态变化时的信号
    *************************************************/
    void sigWifiStateChanged(bool bConnState);

    /***********************************************
     * 功能：释放设备已被激活的信号
     * *********************************************/
    void sigDeviceActived();

private slots:
    /*************************************************
    功能： 槽，开始固件更新的处理
    出参： pStmPercent -- stm32的更新进度
          pZigbeePercent -- zigbee的更新进度
    *************************************************/
    void onStartFwUpdate( quint8 * pStmPercent,
                          quint8 * pZigbeePercent );
    /*************************************************
    功能： 创建蓝牙子服务
    *************************************************/
    void onCreateSubService(  );

    /*************************************************
    功能： 设置usb状态
    *************************************************/
    void onSetUSBState( SystemSet::USBState eOption );

    /*************************************************
    功能： 时区改变时修改系统时间
    *************************************************/
    void onTimezoneChanged();

private:
    /****************************
    功能： 构造函数
    *****************************/
    SystemSetService();

    /****************************
    功能： 析构函数
    *****************************/
    ~SystemSetService();

    /****************************
    功能： disable 拷贝
    *****************************/
    SystemSetService( const SystemSetService& other );

    /****************************
    功能： disable 赋值
    *****************************/
    SystemSetService & operator = (const SystemSetService &);

    /*************************************************
    功能： 读取电量
    返回值：电量信息，负数表示通讯问题，不进行处理
    *************************************************/
    int getPower( void );

    /****************************
    输入参数:fPercent -- 电量比例
    返回值：float -- 电量百分比
    功能： 将真实电量映射成显示电量
    *****************************/
    float batteryMappingFromRawData( float fPercent );

    /*****************************************
     * 功能：根据原始时s间格式获取显示的时间格式数据
     * 输入参数：
     *      qstrActiveOrigionTimeInfo：原始激活时间格式，yyyyMMddwwhhmmss
     * 返回值：
     *      QString：显示的时间格式，yyyy/MM/dd
     * ***************************************/
    QString getActiveShowTimeInfo(const QString &qstrActiveOrigionTimeInfo);

    /*****************************************
     * 功能：根据原始时间格式获取8字节的时间格式
     * 输入参数：
     *      qstrTimeString：原始时间格式，yyyyMMddwwhhmmss
     * 返回值：
     *      QByteArray：8个字节的时间信息yy yy MM dd ww hh mm ss
     * ***************************************/
    QByteArray getTimeBytesFromTimeStr(const QString &qstrTimeString);

private:
    QThread* m_pThread;//执行线程
    BluetoothSetService* m_pBluetoothService;   //蓝牙设置模块

    SystemSet::HotSpotOption m_eHotSpotOption;        //hot spot option

    const UINT8 *m_pbyDevSN;
    SystemSet::AppUsbMode m_eUsbMode;
    int m_iSysFreq;
    int m_iIndex;                  //被选中标签的索引
    QMap<int, int> m_iSigStrength; //被选中标签对应的信号强度
    QMap<int, QString> m_mapWifiInfo; //保存wifi热点名

    SystemSet::FirmwareInfo m_stFirmwareInfo;//固件信息

    DataSpecificationNS::DataSpecificationVersion m_eDataSpecificationVersion;
    SystemSet::RealtimeDiagnosisSwitch m_eRTDiagSwitch;
};

#endif // SYSTEMSETSERVICE_H
