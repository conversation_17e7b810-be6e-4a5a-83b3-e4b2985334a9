#include "calibratebasewidget.h"
#include <QKeyEvent>



const QString WIDGET_NORMAL_STYLE = "CalibrateBaseWidget{border-radius: 2px; border-width: 1px; border-color:gray; border-style: solid}";
const QString WIDGET_SELECTED_STYLE = "CalibrateBaseWidget{background-color: rgb(108, 166, 205); border-radius: 2px; border-width: 1px; border-color: blue; border-style: solid}";


CalibrateBaseWidget::CalibrateBaseWidget(const int iWidth, const int iHeight, QWidget *parent)
    : QFrame(parent)
{
    setFixedSize(iWidth, iHeight);
    setAttribute(Qt::WA_DeleteOnClose);
    setFrameStyle(QFrame::Panel | QFrame::Plain);
    setWindowFlags(Qt::FramelessWindowHint);
    setItemSelectState(false);
    createUI();
}

CalibrateBaseWidget::~CalibrateBaseWidget()
{
}

void CalibrateBaseWidget::createUI()
{
    return;
}

void CalibrateBaseWidget::setItemSelectState(bool bSelected)
{
    m_bSelected = bSelected;
    if(m_bSelected)
    {
        setStyleSheet(WIDGET_SELECTED_STYLE);
    }
    else
    {
        setStyleSheet(WIDGET_NORMAL_STYLE);
    }

    return;
}

bool CalibrateBaseWidget::isSelected()
{
    return m_bSelected;
}

void CalibrateBaseWidget::setSelectedViewFocused()
{
    return;
}

void CalibrateBaseWidget::clearSelectedViewFocused()
{
    return;
}

bool CalibrateBaseWidget::eventFilter(QObject *pObj, QEvent *pEvent)
{
    return QFrame::eventFilter(pObj, pEvent);
}

