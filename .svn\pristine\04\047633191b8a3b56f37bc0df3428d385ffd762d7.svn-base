#include "prpdviewitem.h"
#include <QDebug>
#include <QColor>
#include <QPainter>
#include <qmath.h>
#include "appfontmanager/appfontmanager.h"


const double Y_AXIS_LENGTH_RATION = 0.9; // Y轴高度占整个外框的比例
const int MAX_ACCUMULATE_VALUE = 200; // 累积的最大次数
const int AXIS_PEN_WIDTH = 2;  // 坐标系线宽
const QPoint INVALID_POINT = QPoint(-1,-1); // 默认PRPD上的点坐标，构造初始值
const int POINT_SIZE = 2;  // PRPD图上点的大小
const int PER_PERIOD_ANGLE = 360; //每周期的角度
#define _ERROR_MSG_PRINT_
/************************************************
 * 功能: 构造函数
 * 入参：parent -- 父控件指针
 *     iPeriodCount -- 周期个数
 *     iPhaseCount -- 每周期相位个数
 *     dMax -- 最大值
 *     dMin -- 最小值
 ************************************************/
PrpdViewItem::PrpdViewItem(qint32 iPeriodCount, qint32 iPhaseCount,
                           double dMax, double dMin, QObject *parent) :
    PrpsPlotItem(parent),
    m_iPeriodCount( iPeriodCount ),
    m_iPhaseCount( iPhaseCount ),
    m_dMax( dMax ),
    m_dMin( dMin ),
    m_iPhaseShift( 0 ),
    m_bIsPowerReferenceVisible( true ),
    m_fTextFont( QFont(AppFontManager::instance()->getAppCurFontFamily(), 20) ),
    m_backgroundColor( Qt::black ),
    m_powerReferenceColor( Qt::yellow ),
    m_axisColor( Qt::white ),
    m_eAccumulation( NOT_ACCUMULATION )
{
    m_textInfos.resize( TEXT_COUNT );
    initAxisText();                           // 初始化界面文本
    initPrpdVector( m_iPhaseCount,m_iPeriodCount ); // 初始化prpd数据缓存
}

/************************************************
 * 功能: 获取对象的类型信息
 * 返回值：对象运行的类型信息
 ************************************************/
int PrpdViewItem::rtti() const
{
    return RTTI_PRPD;
}

/************************************************
 * 返回值: 累积模式
 * 功能: 返回累积模式
 ************************************************/
PrpdViewItem::AccumulationMode PrpdViewItem::accumulationMode( void ) const
{
    return m_eAccumulation;
}

/************************************************
 * 输入参数:
 *        eMode: 累积模式
 * 功能: 设置累积模式
 ************************************************/
void PrpdViewItem::setAccumulationMode( AccumulationMode eMode )
{
    if( m_eAccumulation != eMode )
    {
        m_eAccumulation = eMode;

        opByAccumulationMode( m_eAccumulation );
    }
    else
    {
        error( "PrpdViewItem::setAccumulationMode m_eAccumulation is not changed" );
    }
}

/************************************************
 * 返回值: 正弦参考线的显示状态
 * 功能: 返回正弦参考线的显示状态
 ************************************************/
bool PrpdViewItem::isPowerReferenceVisible( void ) const
{
    return m_bIsPowerReferenceVisible;
}

/************************************************
 * 输入参数:
 *        visible: 是否显示
 * 功能: 设置正弦参考线的显示状态
 ************************************************/
void PrpdViewItem::setPowerReferenceVisible( bool bVisible )
{
    if( m_bIsPowerReferenceVisible != bVisible )
    {
        m_bIsPowerReferenceVisible = bVisible;
        emit sigRequestUpdate();  // 通知plot更新
    }
    else
    {
        error( "PrpdViewItem::setPowerReferenceVisible reference is not changed" );
    }
}

/************************************************
 * 功能: 清除显示
 ************************************************/
void PrpdViewItem::clear( void )
{
    m_vDataPoints.clear();                           // 清除数据点缓存
    initPrpdVector( m_iPhaseCount,m_iPeriodCount );  // 初始化缓存
    initImage( m_backgroundColor );                  // 初始化绘图设备
    emit sigRequestUpdate();                           // 通知plot更新
}

/****************************
功能： 添加指定周期数据
入参： data -- 数据
      vPeriods -- 指定周期集合
*****************************/
void PrpdViewItem::setData( const QVector< double > &data,const QVector<quint16> &vPeriods )
{
    bool bValid = false;

    if( data.size() == m_iPhaseCount * m_iPeriodCount )
    {
        bValid = true;
    }
    else
    {
        // data size is not valid
    }

    if( bValid )
    {
        m_vDataPoints.clear();                      // 清除数据点信息缓存

        opByAccumulationMode( m_eAccumulation );    // 根据累积模式做不同处理

        for( int i = 0;i < vPeriods.size();++i )
        {
            if( vPeriods.at( i ) < m_iPeriodCount )
            {
                //处理指定周期数据
                processData( data.mid( vPeriods.at( i ) * m_iPhaseCount,m_iPhaseCount ) );
            }
            else
            {
                error( "PrpdViewItem::setData period index is out of size" );
            }
        }

        // 将数据缓存生成相应数据点
        makeDataPoints( m_vPrpdData );

        // 将数据点绘制到绘图设备image上
        drawPointsToImage( m_vDataPoints );

        emit sigRequestUpdate(); // 通知plot更新
    }
    else
    {
        // data size is not valid
    }
}

/****************************
功能： 添加原始数据
入参： data -- 数据
*****************************/
void PrpdViewItem::setData( const QVector< double > &data )
{
    bool bValid = false;

    if( data.size() == ( m_iPhaseCount * m_iPeriodCount ) )
    {
        bValid = true;
    }
    else
    {
        // data size is not valid
    }

    if( bValid )
    {
        m_vDataPoints.clear();                      // 清除数据点信息缓存

        opByAccumulationMode( m_eAccumulation );    // 根据累积模式做不同处理

        for( int i = 0;i < m_iPeriodCount;++i )
        {
            processData( data.mid( i * m_iPhaseCount,m_iPhaseCount ) );
        }
        // 将数据缓存生成相应数据点
        makeDataPoints( m_vPrpdData );

        // 将数据点绘制到绘图设备image上
        drawPointsToImage( m_vDataPoints );

        emit sigRequestUpdate(); // 通知刷新
    }
    else
    {
        // data size is not valid
    }
}

/****************************
功能： 添加原始数据
入参： data -- 数据
      iCurrentDataPos -- 当前数据所在缓存中的相对位置位置
      业务逻辑为：
      因video类型的数据回放涉及到对缓存进行操作，且支持快进后退
      故考虑每次advance时，根据传递的相对缓存位置，来将当前数据
      全部刷新下以获得相应的累积效果
*****************************/
void PrpdViewItem::setData( const QVector< double > &data,qint32 iCurrentDataPos )
{
    bool bValid = false;

    if( data.size() % m_iPhaseCount == 0 )
    {
        bValid = true;
    }
    else
    {
        // data size is not valid
    }

    if( bValid )
    {
        m_vDataPoints.clear();                      // 清除数据点信息缓存

        // 清除显示
        initImage( m_backgroundColor );
        // 清除缓存
        initPrpdVector( m_iPhaseCount,m_iPeriodCount );

        for( int i = 0;i < iCurrentDataPos;++i )
        {
            processData( data.mid( i * m_iPhaseCount,m_iPhaseCount ) );
        }
        // 将数据缓存生成相应数据点
        makeDataPoints( m_vPrpdData );

        // 将数据点绘制到绘图设备image上
        drawPointsToImage( m_vDataPoints );

        emit sigRequestUpdate(); // 通知刷新
    }
    else
    {
        // data size is not valid
    }
}

/****************************
功能： 处理数据
入参： data -- 单周期数据
*****************************/
void PrpdViewItem::processData( const QVector< double > &data )
{
    double rawData = 0.0;
    for( int i = 0;i < data.size();++i )
    {
        // 获取数据范围内的合法数据
        rawData = dataInScope( data.at( i ),m_dMax,m_dMin );
        // 将数据映射到缓存中
        mappingDataToCache( rawData,i,m_vPrpdData,m_eAccumulation );
    }
}

/****************************
输入参数:usIndex，数据索引号
返回值：相位索引号
功能： 根据数据索引号返回相位索引号
*****************************/
quint16 PrpdViewItem::phaseFromIndex( quint16 usIndex )
{
    // 根据数据在缓存中的相对位置决定对应的相位编号
    quint16 usPhaseIndex = usIndex % m_iPhaseCount;
    return usPhaseIndex;
}

/****************************
输入参数:usIndex，数据索引号
返回值：周期索引号
功能： 根据数据索引号返回周期索引号
*****************************/
quint16 PrpdViewItem::periodFromIndex( quint16 usIndex )
{
    // 根据数据在缓存中的相对位置决定对应的周期编号，进一
    quint16 usPeriodIndex = ( ( usIndex + m_iPhaseCount - 1 ) / m_iPhaseCount );
    return usPeriodIndex;
}

/****************************
功能： 获取数据范围内的合法数据
入参： data -- 数据
      dMax -- 最大值
      dMin --  最小值
*****************************/
double PrpdViewItem::dataInScope( double dData,double dMax,double dMin )
{
    double rawData = dData;
    if( rawData < dMin )
    {
        rawData = dMin;
    }
    else if( rawData > dMax )
    {
        rawData = dMax;
    }
    else
    {
        // in the Scope
    }
    return rawData;
}

/****************************
功能： 将数据映射到缓存中
入参： data -- 数据
      iPhaseCount -- 该周期上相位点个数
      vCache -- 二维数据缓存
      eAccumulationMode -- 累积模式（累积模式不同，累积的计数值不同）
*****************************/
void PrpdViewItem::mappingDataToCache(double dData, qint32 iPhaseCount,
                                       QVector< qint16 >& vCache , AccumulationMode eAccumulationMode )
{
    double dPeriod = ( ( dData - m_dMin ) * m_iPeriodCount ) / ( m_dMax - m_dMin ); // 根据数据的大小
    qint32 iPeriod = qRound( dPeriod );
    qint32 iPhase = iPhaseCount;
    //周期下标范围（0，m_iPeriodCount - 1）
    if( iPeriod >=  m_iPeriodCount )
    {
        iPeriod = m_iPeriodCount - 1;
    }
    else
    {
        // iPeriod is valid
    }
    //相位下标范围（0，m_iPhaseCount - 1）
    if( iPhase >=  m_iPhaseCount )
    {
        iPhase = m_iPhaseCount - 1;
    }
    else
    {
        // iPeriod is valid
    }
    /*
     * 此处处理的业务逻辑为
     * 累积模式下，数据点的数据范围为0~200，每次重复出现进行+1处理；
     * 非累积模式下，数据点根据幅值的大小显示在不同周期，即数值大小跟周期相关
    */
    if( eAccumulationMode == ACCUMULATION )
    {
        if( vCache[iPeriod * m_iPhaseCount + iPhase] < ( MAX_ACCUMULATE_VALUE - 1 ) )
        {
            vCache[iPeriod * m_iPhaseCount + iPhase]++;
        }
        else
        {
            //accumulate is max
        }
    }
    else
    {
        vCache[iPeriod * m_iPhaseCount + iPhase] = iPeriod;
    }

}

/****************************
功能： 添加回放数据
入参： data -- 数据
*****************************/
void PrpdViewItem::setPlayBackData( const QVector< qint16 > &data )
{
    bool bValid = false;
    // 校验数据size的合法性
    if( data.size() == ( m_iPhaseCount * m_iPeriodCount ) )
    {
        bValid = true;
    }
    else
    {
        // data size is not valid
    }

    if( bValid )
    {
        m_vPrpdData = data;
        // 初始化绘图设备基色
        initImage( m_backgroundColor );
        // 将数据缓存生成相应数据点
        makeDataPoints( m_vPrpdData );

        // 将数据点绘制到绘图设备image上
        drawPointsToImage( m_vDataPoints );

        emit sigRequestUpdate(); // 通知plot刷新
    }
    else
    {
        // data size is not valid
    }
}

/************************************************
 * 功能: 获取当前显示的数据
 * 返回值：数据
 ************************************************/
const QVector<qint16> &PrpdViewItem::data(void) const
{
    return m_vPrpdData;
}

/************************************************
 * 返回值: 当前相位偏移度数
 * 功能: 返回当前相位偏移度数
 ************************************************/
qint32 PrpdViewItem::phaseOffset() const
{
    return m_iPhaseShift;
}

/************************************************
 * 输入参数:
 *        iPhase: 度数
 * 功能: 设置当前相位偏移度数
 ************************************************/
void PrpdViewItem::setPhaseOffset( qint32 iPhase )
{
    if( m_iPhaseShift != iPhase )
    {
        while( iPhase < 0 )
        {
            iPhase += PER_PERIOD_ANGLE;
        }
        while( iPhase > PER_PERIOD_ANGLE )
        {
            iPhase -= PER_PERIOD_ANGLE;
        } // 保证相位偏移在0~360°之间
        m_iPhaseShift = iPhase;
        translate( m_iPhaseShift );
    }
    else
    {
        error( "PrpsViewItem::setPhaseOffset iPhase is not changed" );
    }
}

/************************************************
 * 功能: 根据背景色初始化image
 * 入参：color -- 背景色
 ************************************************/
void PrpdViewItem::initImage( const QColor &color )
{
    m_Image.fill( color );
}

/****************************
功能： 初始化界面显示文本
*****************************/
void PrpdViewItem::initAxisText( void )
{
    m_textInfos[PHASE_0].m_strText = "0°";
    m_textInfos[PHASE_360].m_strText = "360°";
}

/************************************************
 * 功能: 将数据点绘制到image上
 * 入参：data -- 数据信息
 ************************************************/
void PrpdViewItem::drawPointsToImage( const QVector< DataPointsInfo >& data )
{
    int xPos = 0;
    int yPos = 0;
    for( int i = 0;i < data.size();++i )
    {
        DataPointsInfo dataPoint = data[i];
        /* INVALID_POINT为相应数据结构构造的默认值
         * 即出现该值时默认该数据点不存在
         * POINT_SIZE 数据点的大小，默认为实际数据
         * 向外发散POINT_SIZE个像素点
         */
        if( INVALID_POINT != dataPoint.point )
        {
            for( int i = 0;i < POINT_SIZE;++i )
            {
                for( int j = 0;j < POINT_SIZE;++j )
                {
                    xPos = dataPoint.point.x() + i;
                    yPos = dataPoint.point.y() + j;
                    if( m_Image.valid( xPos,yPos ) )
                    {
                        m_Image.setPixel( xPos,yPos,
                                          dataPoint.qColor.rgb() );
                    }
                    else
                    {
                        // image out of size
                    }
                }
            }
        }
        else
        {
            // invalid point
        }
    }
}

/************************************************
 * 输入参数:iPhaseShift -- 相位偏移
 * 功能: 根据相位偏移计算数据点坐标
 ************************************************/
void PrpdViewItem::translate( qint32 iPhaseShift )
{
    quint16 usPhase = 0;
    quint16 usPhaseMoved = iPhaseShift / ( 360 / m_iPhaseCount ); // 当前相位移动偏移位数
    quint16 usActualPhase = 0; // 数据实际相位
    const double xLength = xAxisLength();
    double dPointX = 0;
    int iPointX = 0;
    for( int i = 0;i < m_vDataPoints.size();++i )
    {
        if( m_vDataPoints[i].point != INVALID_POINT )
        {
            usPhase = i % m_iPhaseCount; // 选中数据的相位
            usActualPhase = ( usPhase + usPhaseMoved ) % m_iPhaseCount;
            dPointX = AXIS_PEN_WIDTH + usActualPhase * xLength / m_iPhaseCount;
            //进一处理 因setPixel只能绘制整数点
            iPointX = ( qRound( dPointX ) > dPointX )?qRound( dPointX ):dPointX;
            // 校正数据位置，防止进一处理带来的出界
            if( iPointX > ( AXIS_PEN_WIDTH / 2 + xLength ) )
            {
                iPointX = AXIS_PEN_WIDTH / 2;// 进一出界的防务
            }
            m_vDataPoints[i].point.setX( iPointX );
        }
        else
        {
            // point is invalid
        }
    }
    // 初始化绘图设备
    initImage( m_backgroundColor );
    // 将数据点缓存更新到绘图设备上
    drawPointsToImage( m_vDataPoints );
    //通知plot更新
    emit sigRequestUpdate();
}

/************************************************
 * 输入参数:
 *        eMode: 累积模式
 * 功能: 根据累积模式进行相应处理
 ************************************************/
void PrpdViewItem::opByAccumulationMode( AccumulationMode eMode )
{
    if( NOT_ACCUMULATION == eMode ) // 切换成非累积模式，清除缓存
    {
        // 清除显示
        initImage( m_backgroundColor );
        // 清除缓存
        initPrpdVector( m_iPhaseCount,m_iPeriodCount );
    }
    else
    {
        // do nothing
    }
}

/************************************************
 * 返回值: 可显示的量程最小值
 * 功能: 返回可显示的量程最小值
 ************************************************/
double PrpdViewItem::rangeMin() const
{
    return m_dMin;
}

/************************************************
 * 输入参数:
 *        value: 可显示的量程最小值
 * 功能: 设置可显示的量程最小值
 ************************************************/
void PrpdViewItem::setRangeMin( double value )
{
    if( m_dMin != value )
    {
        m_dMin = value;
    }
    else
    {
        error( "PrpdViewItem::setRangeMin value is not changed" );
    }
}

/************************************************
 * 返回值: 可显示的量程最大值
 * 功能: 返回可显示的量程最大值
 ************************************************/
double PrpdViewItem::rangeMax() const
{
    return m_dMax;
}

/************************************************
 * 输入参数:
 *        value: 可显示的量程最大值
 * 功能: 设置可显示的量程最大值
 ************************************************/
void PrpdViewItem::setRangeMax( double value )
{
    if( m_dMax != value )
    {
        m_dMax = value;
    }
    else
    {
        error( "PrpdViewItem::setRangeMax value is not changed" );
    }
}

/************************************************
 * 输入参数:
 *        font: 字体
 * 功能: 设置绘制坐标轴文字信息所用的字体
 ************************************************/
void PrpdViewItem::setAxisTextFont( const QFont& font)
{
    if( m_fTextFont != font )
    {
        m_fTextFont = font;
    }
    else
    {
        error( "PrpsViewItem::setAxisTextFont font is not changed" );
    }
}

/************************************************
 * 返回值: 字体
 * 功能: 返回绘制坐标轴文字信息所用的字体
 ************************************************/
QFont PrpdViewItem::axisTextFont() const
{
    return m_fTextFont;
}

/************************************************
 * 功能: 设置背景颜色
 * 入参：color -- 颜色
 ************************************************/
void PrpdViewItem::setBackGroundColor( const QColor& color )
{
    if( m_backgroundColor != color )
    {
        m_backgroundColor = color;
    }
    else
    {
        //color not changde
    }
}

/************************************************
 * 功能: 获取背景颜色
 * 返回值：rgb -- 背景色
 ************************************************/
QColor PrpdViewItem::backGroundColor( void )const
{
    return m_backgroundColor;
}

/************************************************
 * 返回值: 坐标系线条及文字颜色
 * 功能: 返回坐标系线条及文字颜色
 ************************************************/
QColor PrpdViewItem::axisColor(void) const
{
    return m_axisColor;
}

/************************************************
 * 输入参数:
 *          color: 坐标系线条及文字颜色
 * 功能: 设置坐标系线条及文字颜色
 ************************************************/
void PrpdViewItem::setAxisColor( const QColor& color )
{
    if( m_axisColor != color )
    {
        m_axisColor = color;
    }
    else
    {
        //color not changde
    }
}

/************************************************
 * 功能: 设置工频参考的颜色
 * 入参：color -- 工频参考颜色
 ************************************************/
void PrpdViewItem::setPowerReferenceColor( const QColor& color )
{
    if( m_powerReferenceColor != color )
    {
        m_powerReferenceColor = color;
    }
    else
    {
        //color not changde
    }
}

/************************************************
 * 功能: 获取工频参考颜色
 * 返回值：工频参考颜色
 ************************************************/
QColor PrpdViewItem::powerRefrenceColor( void )const
{
    return m_powerReferenceColor;
}

/************************************************
 * 返回值: NULL
 * 功能: 坐标系的X轴长度
 ************************************************/
double PrpdViewItem::xAxisLength(void)
{
    double len = 0.0;
    QRectF rect = geometry();
    if( rect.isValid() )
    {
        len = rect.width() - AXIS_PEN_WIDTH * 2;
    }
    else
    {
        error( "PrpdViewItem::xAxisLength geometry is not valid" );
    }
    return len;
}

/************************************************
 * 返回值: y轴长度
 * 功能: 坐标系的y轴长度
 ************************************************/
double PrpdViewItem::yAxisLength(void)
{
    double len = 0.0;
    QRectF rect = geometry();
    if( rect.isValid() )
    {
        QFontMetrics fontMetrics( m_fTextFont );
        int h = fontMetrics.height();       // 当前字体单行所需高度
        len = rect.height() * Y_AXIS_LENGTH_RATION;
        if( ( len + h ) > rect.height() )   // 防止根据预定比例超出显示范围
        {
            len = rect.height() - h;
        }
    }
    else
    {
        error( "PrpdViewItem::yAxisLength geometry is not valid" );
    }
    return len;
}

/************************************************
 * 返回值: 左下角原点横坐标
 * 功能: 原点横坐标
 ************************************************/
double PrpdViewItem::bottomLeftX(void)
{
    double len = 0.0;
    QRectF rect = geometry();
    if( rect.isValid() )
    {
        len = rect.x();
    }
    else
    {
        error( "PrpdViewItem::bottomLeftX geometry is not valid" );
    }
    return len;
}

/************************************************
 * 返回值: 左下角原点纵坐标
 * 功能: 原点纵坐标
 ************************************************/
double PrpdViewItem::bottomLeftY(void)
{
    double len = 0.0;
    double y = 0.0;
    QRectF rect = geometry();
    if( rect.isValid() )
    {
        QFontMetrics fontMetrics( m_fTextFont );
        int h = fontMetrics.height();           // 当前字体单行所需高度
        len = rect.height() * Y_AXIS_LENGTH_RATION;
        if( ( len + h ) > rect.height() )       // 防止根据预定比例超出显示范围
        {
            len = rect.height() - h;
        }
        y = rect.y() + len;
    }
    else
    {
        error( "PrpdViewItem::bottomLeftY geometry is not valid" );
    }
    return y;
}

/************************************************
 * 功能: 提供各item方便绘制的接口
 * 入参：painter -- 绘图工具
 *      canvasRect -- plot的大小
 ************************************************/
void PrpdViewItem::draw( QPainter *painter,const QRectF &canvasRect )
{
    Q_UNUSED( canvasRect )
    painter->save();

    //指定画笔的绘图设备
    QPainter coorPainter( &m_Image );
    coorPainter.setPen( QPen( m_axisColor,AXIS_PEN_WIDTH,Qt::SolidLine ) );
    //prpd实线矩形外框
    coorPainter.drawRect( QRectF( AXIS_PEN_WIDTH / 2,AXIS_PEN_WIDTH / 2,
                                  xAxisLength(),yAxisLength() ) );
    //相关文本信息
    coorPainter.setFont( m_fTextFont );
    for( int i = 0;i < m_textInfos.size();++i )
    {
        coorPainter.drawText( m_textInfos.at( i ).m_rect,m_textInfos.at( i ).m_strText );
    }

    coorPainter.setPen( QPen( m_axisColor,AXIS_PEN_WIDTH,Qt::DotLine ) );
    //坐标系虚线段
    for( int i = 0;i < m_listAxisDotLines.size();++i )
    {
        coorPainter.drawLine( m_listAxisDotLines.at( i ) );
    }

    coorPainter.setPen( QPen( m_powerReferenceColor,AXIS_PEN_WIDTH,Qt::DotLine ) );
    if( m_bIsPowerReferenceVisible )
    {
        coorPainter.drawPolyline( m_PowerReference );
    }
    else
    {
        // refrence not open
    }
    //将image绘制到plot上
    painter->drawImage( geometry(),m_Image );

    painter->restore();
}

/************************************************
 * 功能: item尺寸发生改变时调用的接口
 ************************************************/
void PrpdViewItem::onSizeChanged( void )
{
    m_Image = QImage( geometry().width(),geometry().height(),
                      QImage::Format_RGB888 );
    initImage( m_backgroundColor ); // 初始化绘图设备大小及相应颜色
    makeAxisTextInfo();       // 更新界面文本的位置信息等
    makeAxisLines();          // 更新坐标系的相关信息
    makePowerReference();           // 更新工频参考的相关信息
}

/****************************
功能： 生成坐标系线
*****************************/
void PrpdViewItem::makeAxisLines()
{
    m_listAxisDotLines.clear();
    double originXPos = 0;              // 各位置信息均为相对绘图设备的坐标信息
    double bottomLeftYPos = yAxisLength();
    double xWidth = xAxisLength();
    double yHeight = yAxisLength();

    for( quint16 usCnt = 0;usCnt < HORIZONTAL_LINE_COUNT;usCnt++ )
    {
        // 生成prpd坐标系所用的横向线段
        m_listAxisDotLines << QLineF( originXPos,bottomLeftYPos - ( usCnt + 1 ) * yHeight / ( HORIZONTAL_LINE_COUNT + 1 ),
                                            originXPos + xWidth,bottomLeftYPos - ( usCnt + 1 ) * yHeight / ( HORIZONTAL_LINE_COUNT + 1 ) );
    }

    for( quint16 usCnt = 0;usCnt < VERTICAL_LIEN_COUNT;usCnt++ )
    {
        // 生成prpd坐标系所用的纵向线段
        m_listAxisDotLines << QLineF(originXPos + ( usCnt + 1 ) * xWidth / ( VERTICAL_LIEN_COUNT + 1 ),bottomLeftYPos,
                                           originXPos + ( usCnt + 1 ) * xWidth / ( VERTICAL_LIEN_COUNT + 1 ),bottomLeftYPos - yHeight);
    }
}

/************************************************
 * 功能: 生成坐标系文字相关信息
 ************************************************/
void PrpdViewItem::makeAxisTextInfo(void)
{
    double originXPos = AXIS_PEN_WIDTH;
    double bottomLeftYPos = yAxisLength();
    double xWidth = xAxisLength();

    QFontMetrics fontMetrics( m_fTextFont );
    int h = fontMetrics.height();
    for(int i = 0; i < m_textInfos.size(); ++i)
    {
        int w = fontMetrics.width( m_textInfos[i].m_strText );
        switch( i )
        {
            case PHASE_0:
            {
                m_textInfos[i].m_rect.setRect( originXPos,bottomLeftYPos,w,h );
            }
                break;
            case PHASE_360:
            {
                m_textInfos[i].m_rect.setRect( originXPos + xWidth - w,bottomLeftYPos,w,h );
            }
                break;
            default:
                break;
        }
    }
}

/************************************************
 * 功能: 生成工频参考的相关信息
 ************************************************/
void PrpdViewItem::makePowerReference(void)
{
    int iCount = xAxisLength();         // 工频参考线横向上根据像素点个数决定元素个数
    m_PowerReference.resize(iCount);        // 预定义容器大小
    qreal xStart = AXIS_PEN_WIDTH;
    qreal xOffset = ( xAxisLength() - 1 ) / iCount; // 横向上每个像素点值之间的距离
    qreal amp = yAxisLength() / 2;          // 幅值
    qreal yBase = amp;                      // 横向中心线位置
    for(int i = 0; i < iCount; ++i)
    {
        qreal y = yBase - amp * qSin( 2 * M_PI / iCount * i );
        m_PowerReference[i].setX( xStart + xOffset * i );
        m_PowerReference[i].setY( y );
    }
}

/****************************
功能： 生成数据线点的相应信息
入参： data -- 数据
*****************************/
void PrpdViewItem::makeDataPoints(const QVector<qint16> &data )
{
    m_vDataPoints.clear();
    m_vDataPoints.reserve( m_iPhaseCount * m_iPeriodCount );

    QColor color;
    quint16 usPhaseMoved = 0;
    quint16 usActualPhase = 0;
    quint16 usPhase = 0;
    quint16 usPeriod = 0;
    double dPointX = 0;
    double dPointY = 0;
    int iPointX = 0;
    int iPointY = 0;
    const double xLength = xAxisLength();
    const double yLength = yAxisLength();
    const double bottomLeftYPos = bottomLeftY();
    const double geometryYPos = geometry().y();
    for( int i = 0;i < data.size();++i )
    {
        if( data[i] != 0 )
        {
            //根据缓存中的数据以及累积模式得到相应颜色
            color = colorFromData( data[i],m_eAccumulation );

            //将相应数据添加到数据点集合中去
            //addDataToDataPoints( color,i / m_iPhaseCount,i % m_iPhaseCount );

            usPhase = i % m_iPhaseCount;
            usPeriod = i / m_iPhaseCount;
            usPhaseMoved = m_iPhaseShift / ( 360 / m_iPhaseCount ); // 当前相位移动偏移位数
            usActualPhase = usPhase + usPhaseMoved; // 数据实际相位
            //计算实际相位偏移位数（0~m_iPhaseShift）
            usActualPhase = ( usActualPhase > m_iPhaseCount )?( usActualPhase - m_iPhaseCount ) : usActualPhase;

            dPointX = AXIS_PEN_WIDTH + usActualPhase * xLength / m_iPhaseCount;
            dPointY = bottomLeftYPos - geometryYPos - usPeriod * yLength / m_iPeriodCount;

            //进一处理 因setPixel只能绘制整数点
            iPointX = ( qRound( dPointX ) > dPointX )?qRound( dPointX ):dPointX;
            iPointY = ( qRound( dPointY ) > dPointY )?qRound( dPointY ):dPointY;

            // 校正数据位置，防止进一处理带来的出界
            if( iPointX > ( AXIS_PEN_WIDTH / 2 + xLength ) )
            {
                iPointX = AXIS_PEN_WIDTH / 2;// 进一出界的防务
            }

            m_vDataPoints.append( DataPointsInfo( color,QPoint( iPointX,iPointY ) ) );
        }
        else
        {
            m_vDataPoints.append( DataPointsInfo() );
        }
    }
}

/****************************
输入参数:
       color -- 颜色
       usPeriod -- 周期
       usPhase -- 相位
功能： 将数据转变为数据线段
*****************************/
void PrpdViewItem::addDataToDataPoints( QColor color,
                         quint16 usPeriod,quint16 usPhase )
{
    quint16 usPhaseMoved = m_iPhaseShift / ( 360 / m_iPhaseCount ); // 当前相位移动偏移位数
    quint16 usActualPhase = usPhase + usPhaseMoved; // 数据实际相位
    //计算实际相位偏移位数（0~m_iPhaseShift）
    usActualPhase = ( usActualPhase > m_iPhaseCount )?( usActualPhase - m_iPhaseCount ) : usActualPhase;

    double dPointX = AXIS_PEN_WIDTH + usActualPhase * xAxisLength() / m_iPhaseCount;
    double dPointY = bottomLeftY() - geometry().y() - usPeriod * yAxisLength() / m_iPeriodCount;

    //进一处理 因setPixel只能绘制整数点
    int iPointX = ( qRound( dPointX ) > dPointX )?qRound( dPointX ):dPointX;
    int iPointY = ( qRound( dPointY ) > dPointY )?qRound( dPointY ):dPointY;

    m_vDataPoints.append( DataPointsInfo( color,QPoint( iPointX,iPointY ) ) );
}

/****************************
输入参数:data -- 数据大小百分比
返回值：颜色
功能： 根据数据大小返回颜色
*****************************/
QColor PrpdViewItem::colorFromData( qint16 data , AccumulationMode eAccumulationMode )
{
    QColor color = Qt::yellow;
    switch( eAccumulationMode )
    {
        case ACCUMULATION:
        {
            color = colorFromHSL( ( double )data / MAX_ACCUMULATE_VALUE );
        }
            break;
        case NOT_ACCUMULATION:
        {
            color = colorFromHSL( ( double )data / m_iPeriodCount );
        }
            break;
        default:
            break;
    }
    return color;
}

/****************************
输入参数:dProportion -- 比例
返回值：颜色
功能： 根据数据所占比例，通过hsl映射成对应颜色
*****************************/
QColor PrpdViewItem::colorFromHSL( double dProportion )
{
    QColor color = Qt::yellow;
    if( dProportion > 0 )
    {
        color =  QColor::fromHsl( ( qint32 )( 40 * dProportion ),
                                ( qint32 )( 180 + 30 * dProportion ),
                                ( qint32 )( 120 + 80 * dProportion ) );    // T90源码中关于颜色和比例的对应关系，是H,S,L格式的颜色
    }
    else
    {
        error( "PrpdViewItem::colorFromHSL dProportion is not valid" );
    }
    return color;
}

/************************************************
 * 功能: 初始化缓存
 * 入参：iPhaseCount -- 每周期相位个数
 *      iPeriodCount -- 周期个数
 ************************************************/
void PrpdViewItem::initPrpdVector( qint32 iPhaseCount, qint32 iPeriodCount )
{
    // 利用构造的方法，重定义用于累积显示的数组
    m_vPrpdData = QVector< qint16 >( iPeriodCount * iPhaseCount,0 );
}

/************************************************
 * 功能: 打印错误消息
 * 入参：strError -- 错误信息
 ************************************************/
void PrpdViewItem::error( const QString& strError )
{
#ifdef _ERROR_MSG_PRINT_
    qDebug()<<"error is"<<strError;
#endif
}
