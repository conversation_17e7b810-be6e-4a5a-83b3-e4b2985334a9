/*
 * Copyright (c) 2020.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：pdalistitemdelegate.h
 *
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2020/11/17
 * 摘要：巡检列表项代理类
 * 当前版本：1.0
 */

#ifndef PDALISTITEMDELEGATE_H
#define PDALISTITEMDELEGATE_H

#include <QStyledItemDelegate>
#include <QTimer>

class PDAListItemDelegate : public QStyledItemDelegate
{
    Q_OBJECT
public:
    enum ItemRole
    {
        ITEM_ID_ROLE = Qt::UserRole + 1,
        ITEM_MODE_ROLE,
        ITEM_TICKED_STATE_ROLE,
        ITEM_MULTI_ROW_TEXT_ROLE,
        ITEM_INCOMPLETE_DISPLAY_ROLE,
        ITEM_CONTENT_ROLE,
    };

    enum ItemMode
    {
        LABEL_MODE = 0, // 标签模式
        CHECK_BOX,      // 复选框模式
        CONTENT_MODE, // 显示内容模式
    };

public:
    explicit PDAListItemDelegate(QObject* pParent = NULL);
    virtual ~PDAListItemDelegate();

    virtual void paint(QPainter* pPainter, const QStyleOptionViewItem& option,
               const QModelIndex& index) const;

    static QRect iconRect(const QRect& containerRect);

    static QRect textRect(const QRect& containerRect, const ItemMode eItemMode);

    static QRect contentRect(const QRect& containerRect);

signals:
    void sigLongPressed(const QModelIndex& index);

    void sigPressed(const QModelIndex& index);

protected:
    virtual bool editorEvent(QEvent* pEvent, QAbstractItemModel* pModel,
                             const QStyleOptionViewItem& option, const QModelIndex& index);

private:
    void drawItemText(QPainter* pPainter, const QStyleOptionViewItem& option,
                      const QModelIndex& index) const;

private slots:
    void onLongPressTimeout();

private:
    QTimer m_longPressTimer;
    QPersistentModelIndex m_pressedIndex;
};

#endif // PDALISTITEMDELEGATE_H
