﻿#ifndef PRPSCHART_H
#define PRPSCHART_H

#include <QScopedPointer>
#include "phasechart.h"

class PrpsDraw;
class PrpsMap;

class PrpsChart : public PhaseChart
{
public:
    PrpsChart(int iDataCountPerPeriod = Phase::DefaultDataCountPerPeriod);
    ~PrpsChart();

    // 更新数据内容
    void updatePaintContents(int iStartIndex);

    // 更新参数
    void updateParams();

    void setAmpSymbol(const QString& strSymbol );

    //void setSpectrumState(SpectrumState eSpectrumState);

    void setPhaseAxisOffset(const int iPhaseAxisOffset);

    int phaseAxisOffset() const;

    void setOriginXRatio(const int iOriginXRatio);

    void setOriginYRatio(const int iOriginYRatio);

private:
    int m_iDataCountPerPeriod;  //模型下每周期的点数
    QScopedPointer<PrpsMap> m_pMap;
    QScopedPointer<PrpsDraw> m_pDraw;
};

#endif // PRPSCHART_H
