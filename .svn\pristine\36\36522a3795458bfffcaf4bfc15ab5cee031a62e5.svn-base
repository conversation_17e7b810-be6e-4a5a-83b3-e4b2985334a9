#include "vpnaccessmanager.h"
#include <QProcess>
#include <QByteArray>
#include "systemsetting/systemsetservice.h"
#include "log/log.h"
#include "global_log.h"

const int g_iInvalidTimerId = -1;

VpnAccessManager::VpnAccessManager(QObject *parent)
    : QObject(parent)
    , m_pThread(NULL)
    , m_iQueryStatusTimerId(g_iInvalidTimerId)
    , m_bConnected(false)
    , m_bConnecting(false)
    , m_eConnState(CONN_INVALID)
{
    QObject::connect(this, SIGNAL(sigStartOrStopConnect(bool)), this, SLOT(onStartOrStopConnect(bool)), Qt::QueuedConnection);
    startThread();
}

VpnAccessManager::~VpnAccessManager()
{
    QObject::disconnect(this, SIGNAL(sigStartOrStopConnect(bool)), this, SLOT(onStartOrStopConnect(bool)));
    stopQueryTimer();
    stopThread();
}

VpnAccessManager* VpnAccessManager::instance()
{
    static VpnAccessManager objManager;
    return &objManager;
}

void VpnAccessManager::startVpnConnect(const SystemSet::VpnAccessInfo &stVpnAcInfo)
{
    if(m_bConnecting)
    {
        return;
    }

    m_stVpnAcInfo = stVpnAcInfo;
    emit sigStartOrStopConnect(true);
    return;
}

void VpnAccessManager::stopVpnConnect()
{
    emit sigStartOrStopConnect(false);
    return;
}

bool VpnAccessManager::isVpnConnected()
{
    return m_bConnected;
}

void VpnAccessManager::timerEvent(QTimerEvent *pEvent)
{
    if(pEvent)
    {
        if(m_iQueryStatusTimerId == pEvent->timerId())
        {
            queryConnectState();
        }
    }

    return;
}

void VpnAccessManager::onStartOrStopConnect(bool bStart)
{
    if(bStart)
    {
        startVpnConnectToDo();
    }
    else
    {
        stopVpnConnectToDo();
    }

    return;
}

void VpnAccessManager::startThread()
{
    if(!m_pThread)
    {
        m_pThread = new QThread(this);
        moveToThread(m_pThread);
    }

    if(m_pThread && !(m_pThread->isRunning()))
    {
        m_pThread->start();
    }

    return;
}

void VpnAccessManager::stopThread()
{
    if(m_pThread)
    {
        if(m_pThread->isRunning())
        {
            m_pThread->quit();
            m_pThread->wait(3000);
        }
    }

    return;
}

void VpnAccessManager::startVpnConnectToDo()
{
    switch(static_cast<VpnAccessMode>(m_stVpnAcInfo.iMode))
    {
    case VPN_ACMODE_F5FPC:
    {
        stopQueryTimer();
        m_bConnecting = true;
        QString qstrCmd = QString("f5fpc --start -x -t %1 -u %2 -p %3")
                .arg(m_stVpnAcInfo.qstrDomainName)
                .arg(m_stVpnAcInfo.qstrUserName)
                .arg(m_stVpnAcInfo.qstrPwd);
        QProcess::startDetached(qstrCmd);
        startQueryTimer();
        break;
    }
    default:
    {
        logWarning(QString("unsupport vpn access mode: %1.").arg(m_stVpnAcInfo.iMode));
        break;
    }

    }

    return;
}

void VpnAccessManager::stopVpnConnectToDo()
{
    switch(static_cast<VpnAccessMode>(m_stVpnAcInfo.iMode))
    {
    case VPN_ACMODE_F5FPC:
    {
        QProcess::startDetached("f5fpc --stop");
        break;
    }
    default:
    {
        logWarning(QString("unsupport vpn access mode: %1.").arg(m_stVpnAcInfo.iMode));
        break;
    }

    }

    return;
}

void VpnAccessManager::queryConnectState()
{
    switch(static_cast<VpnAccessMode>(m_stVpnAcInfo.iMode))
    {
    case VPN_ACMODE_F5FPC:
    {
        queryF5fpcConnectState();
        break;
    }
    default:
    {
        logWarning(QString("unsupport vpn access mode: %1.").arg(m_stVpnAcInfo.iMode));
        break;
    }

    }

    return;
}

void VpnAccessManager::queryF5fpcConnectState()
{
    QProcess objProcess;
    objProcess.start("f5fpc --info");
    objProcess.waitForFinished(15 * 1000);
    QByteArray qbaOutputData = objProcess.readAllStandardOutput();
    QString qstrOutputData(qbaOutputData);
    log_debug("%s", qstrOutputData.toLatin1().data());
    if(qstrOutputData.contains("logon failed")
            || qstrOutputData.contains("Disconnected successfully")
            || qstrOutputData.contains("Client not connected"))
    {
        m_eConnState = CONN_FAIL;
        m_bConnected = false;
        emit sigConnectState(m_bConnected);
        stopVpnConnectToDo();
        stopQueryTimer();
        m_bConnecting = false;
    }
    else
    {
        bool bRet = qstrOutputData.contains("Client IPv4 Address") || qstrOutputData.contains("Client IPv6 Address");
        if(bRet)
        {
            //SystemSetService::instance()->setVpnAccessInfo(m_stVpnAcInfo);
            if(CONN_SUCCESS != m_eConnState)
            {
                m_eConnState = CONN_SUCCESS;
                m_bConnected = true;
                emit sigConnectState(m_bConnected);
            }
        }
    }

    return;
}

//开启查询的定时器
void VpnAccessManager::startQueryTimer()
{
    if(g_iInvalidTimerId == m_iQueryStatusTimerId)
    {
        m_iQueryStatusTimerId = startTimer(20 * 1000);
    }

    return;
}

//停止查询的定时器
void VpnAccessManager::stopQueryTimer()
{
    if(g_iInvalidTimerId != m_iQueryStatusTimerId)
    {
        killTimer(m_iQueryStatusTimerId);
        m_iQueryStatusTimerId = g_iInvalidTimerId;
    }

    return;
}



