#include "intervalview.h"
#include "mobileaccessservice.h"
#include "messageBox/msgbox.h"
#include "window/Window.h"
#include "PDAUi/PDAUiBean/pdaprogressdialog.h"
#include "customaccesstask/subtask.h"
#include "commonviewconfig.h"
#include "connectview/connectview.h"
#include "testpointview.h"
#include "datadefine.h"

#include <QtConcurrentRun>

#define SINGLE_TASK     1
#define DEFAULT_DELAY   300
#define TIMER_ID_INIT   -1
#define TIMER_OUT       1000
#define AUTO_SWITCH_VIEW_DELAY  1000

typedef enum _TaskButton_
{
    BUTTON_OPEN = 0,// 打开
    BUTTON_COMMIT,// 提交
}TaskButton;

//控制按钮定义
const ButtonInfo::Info g_OpenBtn = {BUTTON_OPEN, {ButtonInfo::COMMAND, CustomAccessView::TEXT_OPEN, NULL, "", NULL}};
const ButtonInfo::Info g_CommBtn = {BUTTON_COMMIT, {ButtonInfo::COMMAND, CustomAccessView::TEXT_COMMIT, NULL, "", NULL}};


void uploadGapTasks(SubTask *pSubTask, const QStringList& qlstGapIds)
{
    MobileAccessService::instance()->uploadGaps(pSubTask, qlstGapIds);
    return;
}

/*************************************************
功能： 构造函数
输入参数:
    parent:父控件指针
*************************************************************/
IntervalView::IntervalView(const QString &strMainId, const QString &strSubId, QWidget *parent)
    : PDAListView(QObject::trUtf8("Bay List"), parent)
    , m_bIsUploading(false)
    , m_iAutoSwitchTimerId( TIMER_ID_INIT )
    , m_bTestStateChanged( true )
    , m_bInited( false )
    , m_bHasNext(false)
    , m_iNextIndex(0)
    , m_qstrNextID("")
{
    setFixedSize( Window::WIDTH, Window::HEIGHT );

    m_pBtnInfo = NULL;
    m_qui8BtnCnt = 0;

    m_eAccessMode = SystemSetService::instance()->getCustomAccessMode();
    m_eAccessProtocol = SystemSetService::instance()->getAccessProtocol();

    m_pSubTask = TaskManager::instance()->createSubTask( strMainId, strSubId );

    if(m_pSubTask)
    {
        //添加任务信息
        initTaskItem();

        initBtnBarInfo();

        connect( m_pSubTask, SIGNAL(sigTestDataChanged()), this, SLOT(onTestStateChanged()) );
        connect( m_pSubTask, SIGNAL(sigAutoSwitchEnsure(bool)), this, SLOT(onAutoSwitchEnsure(bool)) );

        getNextBayIndex();
        if( m_pSubTask->isAutoSwitch() )
        {
            m_iAutoSwitchTimerId = startTimer( TIMER_OUT );     // 打开自动跳转定时器 间隔1s
        }
    }
    connect(MobileAccessService::instance(), SIGNAL(sigUploadFinished()), this, SLOT(onUploadFinished()));
}

IntervalView::~IntervalView()
{
    saveLocalData();
    if(m_pSubTask)
    {
        TaskManager::instance()->destroySubTask(m_pSubTask);
    }

    if(m_pBtnInfo)
    {
        delete [] m_pBtnInfo;
        m_pBtnInfo = NULL;
    }
}

/*************************************************
功能： 定时器处理函数
输入参数：
        e -- 定时事件
*************************************************************/
void IntervalView::timerEvent( QTimerEvent* e )
{
    if( e->timerId() == m_iAutoSwitchTimerId )
    {
        killTimer( m_iAutoSwitchTimerId );
        m_iAutoSwitchTimerId = TIMER_ID_INIT;       // 关闭定时器
        if( !m_bTestStateChanged )
        {
            //do nothing
        }
        else
        {
            m_bTestStateChanged = false;
            if( m_bHasNext )
            {
                bool bAutoSwitch = true;
                if( !m_bInited )
                {
                    m_bInited = true;
                    if( MsgBox::OK == MsgBox::delayQuestion("", trUtf8("Auto switch to next undetected point.") ) )
                    {
                        bAutoSwitch = true;
                    }
                    else
                    {
                        bAutoSwitch = false;
                    }
                }

                if( bAutoSwitch )
                {
                    TestPointView *pView = new TestPointView( QObject::trUtf8("Test Point List"),
                                                              m_pSubTask, m_qstrNextID );
                    connect( pView,SIGNAL(sigClosed()),this,SLOT(onTestPointViewClosed()) );
                    pView->show();
                }
            }
            else
            {
                qDebug("IntervalView::timerEvent all bays has been tested");
            }
        }
    }
    else
    {
        qWarning(" IntervalView::timerEvent wrong timer id");
    }

    return;
}

void IntervalView::onAutoSwitchEnsure( bool bEnable )
{
    m_bTestStateChanged = bEnable;
}

void IntervalView::onTestStateChanged()
{
    TaskManager::instance()->updateTaskTestStateChanged( m_pSubTask->mainTaskInfo().s_strId,
                                                         m_pSubTask->subTaskInfo().s_strId,
                                                         m_pSubTask->taskTestState(),
                                                         m_pSubTask->isTaskUploadable() );
}

void IntervalView::initTaskItem()
{
    QVector<qint32> tickIndexes = m_pChart->itemsIndexTicked();
    m_pChart->deleteAllItem();
    //添加任务信息
    QList<PDAListChart::ListItemInfo> itemInfos;
    if(m_pSubTask)
    {
        QVector<CustomAccessTaskNS::GapInfo> gapInfos = m_pSubTask->testGaps();
        int iTested = 0;
        for(int i = 0, iSize = gapInfos.size(); i < iSize; ++i)
        {
            bool bTicked = false;
            bTicked = tickIndexes.contains(i);

            iTested = (CustomAccessTaskNS::TASK_TEST == gapInfos.at(i).gapTestState()) ? 1 : 0;

            if (SystemSet::ACCESS_PROTO_JSDKY == m_eAccessProtocol)
            {
                //  江苏电科院要根据诊断结果显示文字是否红色
                PDAListChart::ListItemInfo stListItemInfo(gapInfos.at(i).s_strName, -1, iTested, bTicked, PDAListItem::LABEL_MODE);
                stListItemInfo.bDiagResult = gapInfos.at(i).gapDiagState() == CustomAccessTaskNS::DiagNormal ? 0 : 1;
                itemInfos << stListItemInfo;
            }
            else
            {
                if(gapInfos.at(i).s_strId.isEmpty())
                {
                    itemInfos << PDAListChart::ListItemInfo(gapInfos.at(i).s_strName, -1, iTested, false, PDAListItem::LABEL_MODE);
                }
                else
                {
                    if(SystemSet::ACCESS_USB_MODE == m_eAccessMode)
                    {
                        itemInfos << PDAListChart::ListItemInfo(gapInfos.at(i).s_strName, -1, iTested, bTicked, PDAListItem::LABEL_MODE);
                    }
                    else
                    {
                        itemInfos << PDAListChart::ListItemInfo(gapInfos.at(i).s_strName, -1, iTested, bTicked, PDAListItem::CHECK_BOX);
                    }
                }
            }
        }

        m_pChart->addItems( itemInfos );
        if(m_pChart->allItems().size() > 0)
        {
            m_pChart->setCurrentItemSelected(0);
        }
    }

    return;
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void IntervalView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case BUTTON_OPEN:
    {
        int iSelectItems = m_pChart->itemsIndexSelected().size();
        if(iSelectItems == SINGLE_TASK)                        // 是否选中单个任务，仅允许打开单个
        {
            int iSelectedIndex = m_pChart->itemIndexSelected();
            if(iSelectedIndex != PDAListChart::ERROR_SELECTED_INDEX)  // 是否有条目被选中
            {
                onItemClicked(iSelectedIndex);
            }
            else
            {
                logWarning("no item select");
            }
        }
        else if(iSelectItems > SINGLE_TASK)
        {
            MsgBox::warning("", QObject::trUtf8("Unable to open multiple items."));
        }
        else
        {
            MsgBox::warning("", QObject::trUtf8("No item has been chosen."));
        }

        break;
    }
    case BUTTON_COMMIT:
    {
        // 点击提交时，手动破坏自动跳转的业务流程
        if( TIMER_ID_INIT != m_iAutoSwitchTimerId )
        {
            killTimer( m_iAutoSwitchTimerId );
            m_iAutoSwitchTimerId = TIMER_ID_INIT;
        }

        //1.判断是否选中条目
        QVector<qint32> vIndexs = m_pChart->itemsIndexTicked();
        if( vIndexs.isEmpty() )
        {
            MsgBox::warning( "",QObject::trUtf8("Please select a bay first.") );
        }
        else
        {
            //判断连接是否存在
            if( MobileAccessService::instance()->isAppConnectExist() )
            {
                //upload is busy,do nothing
                if( m_bIsUploading )
                {
                    return;
                }

                QStringList subIdList;
                bool bUploadable = false;
                for(int i = 0, iSize = vIndexs.size(); i < iSize; ++i)
                {
                    if( m_pSubTask->testGaps().at( vIndexs.at(i) ).isUploadable() )
                    {
                        subIdList.append( m_pSubTask->testGaps().at( vIndexs.at(i) ).s_strId );
                        if( !m_pSubTask->testGaps().at( vIndexs.at(i) ).s_strId.isEmpty() )
                        {
                            bUploadable = true;
                        }
                    }
                }

                if( !bUploadable )
                {
                    //TODO
                    MsgBox::warning( "", QObject::trUtf8("Not support to upload empty data or only background data!") );
                    return;
                }

                m_bIsUploading = true;

                //绑定进度显示
                PDAProgressDialog *pDialog = new PDAProgressDialog(false);
                pDialog->setWindowFlags( Qt::WindowStaysOnTopHint | Qt::FramelessWindowHint );
                pDialog->setWindowModality(Qt::ApplicationModal);
                connect(MobileAccessService::instance(), SIGNAL(sigUploadProgress(int)), pDialog, SLOT(onProgressChanged(int)));
                connect(MobileAccessService::instance(), SIGNAL(sigUploadFinished()), pDialog, SLOT(onUploadFinished()));

                pDialog->show();
                Module::mSleep(500);//避免进度条闪现
                //提交选中任务
                MobileAccessService::instance()->uploadGaps( m_pSubTask, subIdList );
            }
            else
            {
                ConnectView* pView = new ConnectView;
                pView->show();
            }
        }
    }
        break;
    default:
        break;
    }
}

/*************************************************
功能： 槽，响应******
*************************************************************/
void IntervalView::onUploadFinished()
{
    m_bIsUploading = false;
}

/*************************************************
功能： 处理条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void IntervalView::onItemClicked( int id )
{
    /*
     * 1.根据id，找到选择的条目信息，获得标题在内的title信息
     * 2.构造函数添加，方便能够获取测点列表
     */
    m_bInited = true;
    if( id < m_pSubTask->testGaps().size() )
    {
        // 手动破坏自动跳转的业务流程
        if( TIMER_ID_INIT != m_iAutoSwitchTimerId )
        {
            killTimer( m_iAutoSwitchTimerId );
            m_iAutoSwitchTimerId = TIMER_ID_INIT;
            m_bTestStateChanged = false;
        }

        //临时修改，目的是为了解决子任务菜单任务名过长的问题
        TestPointView *pView = new TestPointView( QObject::trUtf8("Test Point List"),
                                                  m_pSubTask, m_pSubTask->testGaps().at(id).s_strId,true );
        connect( pView,SIGNAL(sigClosed()),this,SLOT(onTestPointViewClosed()) );
        pView->show();
    }
}

/*************************************************
功能： 测点列表关闭
*************************************************************/
void IntervalView::onTestPointViewClosed( void )
{
    QList<PDAListChart::ListItemInfo> itemInfos = m_pChart->allItems();
    QVector<CustomAccessTaskNS::GapInfo> gaps = m_pSubTask->testGaps();
    if( itemInfos.size() != gaps.size() )
    {
        return;
    }

    for(int i = 0, iSize = itemInfos.size(); i < iSize; ++i)
    {
        PDAListChart::ListItemInfo tmpInfo = itemInfos.at(i);
        CustomAccessTaskNS::GapInfo testGap = gaps.at(i);
        tmpInfo.m_iTotalCount = (testGap.gapTestState() == CustomAccessTaskNS::TASK_TEST) ? 1 : 0;
        m_pChart->setItemInfo( tmpInfo, i );
    }

    // 若当前任务支持自动跳转
    if( m_pSubTask->isAutoSwitch() )
    {
        if( m_bTestStateChanged )
        {
            getNextBayIndex();
        }

        // 测点列表关闭，重新启动自动跳转的业务流程
        if( TIMER_ID_INIT == m_iAutoSwitchTimerId )
        {
            m_iAutoSwitchTimerId = startTimer( AUTO_SWITCH_VIEW_DELAY );
        }
    }
    return;
}

void IntervalView::getNextBayIndex()
{
    QString strBayId = "";
    m_bHasNext = m_pSubTask->getNextTestBay(strBayId);
    if(m_bHasNext)
    {
        for(int iBay = 0, iSize = m_pSubTask->testGaps().size(); iBay < iSize; ++iBay)
        {
            if(strBayId == m_pSubTask->testGaps().at(iBay).s_strId)
            {
                m_iNextIndex = iBay;
                m_qstrNextID = strBayId;
                m_pChart->setCurrentItemSelected(m_iNextIndex);
                break;
            }
        }
    }

    return;
}

/*************************************************
功能： 直连模式保存本地数据
*************************************************************/
void IntervalView::saveLocalData()
{
    if(SystemSet::ACCESS_USB_MODE == m_eAccessMode)
    {
        if(m_pSubTask)
        {
            QStringList qlstGapIds;
            qlstGapIds.clear();

            bool bUploadable = false;
            for(int i = 0, iSize = m_pChart->allItems().size(); i < iSize; ++i)
            {
                if(m_pSubTask->testGaps().at(i).isUploadable())
                {
                    qlstGapIds.append(m_pSubTask->testGaps().at(i).s_strId);
                    if(!m_pSubTask->testGaps().at(i).s_strId.isEmpty())
                    {
                        bUploadable = true;
                    }
                }
            }

            if(bUploadable)
            {
                //QtConcurrent::run(uploadGapTasks, m_pSubTask, qlstGapIds);
                MobileAccessService::instance()->uploadGaps(m_pSubTask, qlstGapIds);
            }
        }
    }

    return;
}

/*****************************************
 * 功能：初始化按钮栏信息
 * **********************************************/
void IntervalView::initBtnBarInfo()
{
    m_pBtnInfo = NULL;
    m_qui8BtnCnt = 0;

    QVector<ButtonInfo::Info> qvtBtnInfos;
    qvtBtnInfos.clear();

    qvtBtnInfos.push_back(g_OpenBtn);
    SystemSet::AccessProtocol eAccessProtocol = SystemSetService::instance()->getAccessProtocol();
    if (SystemSet::ACCESS_PROTO_JSDKY != eAccessProtocol)
    {
        if(SystemSet::ACCESS_USB_MODE != m_eAccessMode)
        {
            qvtBtnInfos.push_back(g_CommBtn);
        }
    }

    m_qui8BtnCnt = static_cast<quint8>(qvtBtnInfos.size());
    m_pBtnInfo = new ButtonInfo::Info[m_qui8BtnCnt];
    for (int i = 0; i < m_qui8BtnCnt; ++i)
    {
        m_pBtnInfo[i] = qvtBtnInfos[i];
    }

    //创建按钮栏
    createButtonBar(CustomAccessView::CONTEXT, m_pBtnInfo, m_qui8BtnCnt);

    return;
}
