/*
* Copyright (c) 2017.7，南京华乘电气科技有限公司
* All rights reserved.
*
* armbluetoothclient.h
*
* 初始版本：1.0
* 作者：wujun
* 创建日期：2017年07月04日
* 摘要：封装蓝牙客户端
*/

#ifndef ARMBLUETOOTHCLIENT_H
#define ARMBLUETOOTHCLIENT_H

#include "bluetooth.h"
#include "btdef.h"
#include <QMap>
#include <QString>
#include <QMutex>
#include "module_global.h"

extern "C"
{
#include "bluetoothapi/SS1BTPM.h"
}


class MODULESHARED_EXPORT BluetoothClient : public Bluetooth
{
    Q_OBJECT
public:
    /*************************************************
    函数名： BluetoothClient)
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit BluetoothClient();

    /*************************************************
    函数名： ~BluetoothClient()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    virtual ~BluetoothClient();

    /*************************************************
    函数名： open()
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果--true: 打开成功; false: 打开失败
    功能： 开启本地蓝牙适配器
    *************************************************************/
    virtual bool open();

    /*************************************************
    函数名： close()
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果--true: 关闭成功; false: 关闭失败
    功能： 关闭本地蓝牙适配器
    *************************************************************/
    virtual bool close();

    /*************************************************
    函数名： restart()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 重启蓝牙
    *************************************************************/
    void restart();

    int closePort();
    
    /*************************************************
    函数名： checkConnectionState
    输入参数： NULL
    输出参数： NULL
    返回值： bool--连接正常返回true，异常返回false
    功能： 检查当前蓝牙连接状态
    *************************************************************/
    bool checkConnectionState();
    
public:
    /*************************************************
    函数名: scanBluetoothDevice
    输入参数: void
    输出参数: NULL
    返回值: 扫描到的设备的mac地址列表
    功能: 扫描蓝牙外设
    *************************************************************/
    QVector<DeviceInfo> scanBluetoothDevice(void);

    /*************************************************
    函数名: startDeviceDiscovery
    输入参数: timeout--扫描时长
    输出参数: NULL
    返回值: 状态--0表示启动扫描成功，否则表示失败
    功能: 扫描蓝牙外设（异步接口，发送sigDeviceFound信号）
    *************************************************************/
    int startDeviceDiscovery(uint timeout);

    /*************************************************
    函数名： stopDeviceDiscovery
    输入参数： NULL
    输出参数： NULL
    返回值： int--成功返回0
    功能： 停止搜索设备
    *************************************************************/
    int stopDeviceDiscovery();

    /*************************************************
    函数名: connectToRemoteDev
    输入参数: strDeviceMAC--远端蓝牙mac地址
    输出参数: NULL
    返回值: 连接状态--true:成功; false:失败
    功能: 连接远端蓝牙设备
    *************************************************************/
    bool connectToRemoteDev(const QString &strDevMac);

    /*************************************************
    函数名: autoConnectToRemoteDev
    输出参数: 无
    返回值: 连接状态--true:成功; false:失败
    功能: 自动重新连接蓝牙设备的逻辑操作
    *************************************************************/
    bool autoConnectToRemoteDev();

    /*************************************************
    函数名: disconnectFromRemoteDev
    输入参数: NULL
    输出参数: NULL
    返回值: 结果状态--成功返回0
    功能: 与远端蓝牙设备断开连接
    *************************************************************/
    int disconnectFromRemoteDev();

    /*************************************************
    函数名: disconnectDeviceOnly
    输入参数: 无
    输出参数: 无
    返回值: 结果状态--成功返回0，失败返回错误码
    功能: 与远端蓝牙设备断开连接但不关闭端口
    *************************************************************/
    int disconnectDeviceOnly();
    /*************************************************
    函数名： closePort
    输入参数： port--端口；timeOut--超时时间
    输出参数： NULL
    返回值： int--成功返回0
    功能：关闭连接
    *************************************************************/
    int closePort(uint portHandle, uint timeOut=0);

    /*************************************************
    函数名： emitConnectState
    输入参数： bFlag--连接状态
    输出参数： NULL
    返回值： NULL
    功能： 发送蓝牙连接状态信号
    *************************************************************/
    //void emitConnectState(bool bFlag);
    /*************************************************
    函数名： openServicePort
    输入参数： port--端口；timeOut--超时时间
    输出参数： NULL
    返回值： int--成功返回0
    功能：打开服务端蓝牙连接端口服务
    *************************************************************/
    bool openServicePort();

    /*************************************************
    函数名： resetConnectionState
    输入参数： strDevMAC--设备MAC地址
    输出参数： NULL
    返回值： NULL
    功能： 重置与指定设备的连接状态
    *************************************************************/
    void resetConnectionState(const QString &strDevMAC);

    /*************************************************
    函数名： connectToRemoteDevWithRetry
    输入参数： strDevMac--远端蓝牙mac地址
              maxRetries--最大重试次数（默认3次）
    输出参数： NULL
    返回值： 连接状态--true:成功; false:失败
    功能： 智能连接远端蓝牙设备，自动处理配对冲突
    *************************************************************/
    bool connectToRemoteDevWithRetry(const QString &strDevMac, int maxRetries = 3);

    /*************************************************
    函数名： connectToRemoteDevSimple
    输入参数： strDevMac--远端蓝牙mac地址
    输出参数： NULL
    返回值： 连接状态--true:成功; false:失败
    功能： 简化的连接方法，专门解决配对冲突问题
    *************************************************************/
    bool connectToRemoteDevSimple(const QString &strDevMac);

protected:
    /*************************************************
    函数名： emitPairState
    输入参数： bState--配对结果
    输出参数： NULL
    返回值： NULL
    功能： 发送配对完成的信号并更新设备状态
    *************************************************************/
    virtual void emitPairState(bool bState) override;

private:
    /*************************************************
    函数名： openRemotePort
    输入参数： strDevMAC--远端设备MAC；port--服务器端口；
    输出参数： portHandle--读写句柄
    返回值： int--成功返回0
    功能： 打开端口，准备连接
    *************************************************************/
    int openRemotePort(const QString &strDevMAC, int port, int &portHandle);

    /*************************************************
    函数名： getPairState
    输入参数： strMac--蓝牙mac地址
    输出参数： NULL
    返回值： NULL
    功能： 获取是否配对（阻塞）
    *************************************************************/
    bool getPairState(QString strMac);

    /*************************************************
    函数名： getConnectState
    输入参数： strMac--蓝牙mac地址
    输出参数： NULL
    返回值： NULL
    功能： 获取是否连接（阻塞）
    *************************************************************/
    bool getConnectState(QString strMac);

    /*************************************************
    函数名： doConnectToDevice
    输入参数： strDevMac--远端蓝牙mac地址
              isAutoConnect--是否为自动连接尝试
    输出参数： NULL
    返回值： 连接状态--true:成功; false:失败
    功能： 执行设备连接的核心逻辑，供public方法调用
    *************************************************************/
    bool doConnectToDevice(const QString &strDevMac, bool isAutoConnect);

public:
    /*************************************************
    函数名： updateRemoteDeviceInfos
    输入参数： UpdateMask--更新标识;
             RemoteDeviceProperties--远端设备信息
    输出参数： NULL
    返回值： NULL
    功能： 更新远端设备更新信息
    *************************************************************/
    virtual void updateRemoteDeviceInfos(unsigned long UpdateMask, DEVM_Remote_Device_Properties_t *RemoteDeviceProperties);
private:
    // 记录扫描到的设备信息
    QMap<QString, DeviceInfo> m_destDevInfo;

    // 多线程互斥锁
    QMutex m_mutex;

    //蓝牙mac地址
    QString m_strDevMac;
};

#endif
