/*
* Copyright (c) 2017.7，南京华乘电气科技有限公司
* All rights reserved.
*
* bluetooth.h
*
* 初始版本：1.0
* 作者：wujun
* 创建日期：2017年07月05日
* 摘要：蓝牙基类，封装了蓝牙sdk的一些控制接口
*/

#ifndef BLUETOOTH_H
#define BLUETOOTH_H


#include "../abstractcomm.h"
#include "btdef.h"
#include <QVector>
#include <QString>
#include <QDebug>
#include <QSemaphore>
#include <QSharedPointer>

extern "C"
{
#include "bluetoothapi/SS1BTPM.h"
}


/*************************************************
函数名： SPPM_Event_Callback
输入参数：EventData--事件信息；
        CallbackParameter--回调参数
输出参数： NULL
返回值： NULL
功能： 处理SPPM事件（由Serial Port Profile Manager触发）
*************************************************************/
void SPPM_Event_Callback(SPPM_Event_Data_t *EventData, void *CallbackParameter);

/*************************************************
函数名： DEVM_Authentication_Callback
输入参数：AuthenticationRequestInformation--身份认证信息；
        CallbackParameter--回调参数
输出参数： NULL
返回值： NULL
功能： 处理身份认证请求事件（由Device Manager触发）
*************************************************************/
void DEVM_Authentication_Callback(DEVM_Authentication_Information_t *AuthenticationRequestInformation, void *CallbackParameter);

/*************************************************
函数名： DEVM_Event_Callback
输入参数：EventData--设备信息；
        CallbackParameter--回调参数
输出参数： NULL
返回值： NULL
功能： 处理设备监控事件（由Device Manager触发）
*************************************************************/
void DEVM_Event_Callback(DEVM_Event_Data_t *EventData, void *CallbackParameter);


/////////////////////////////////////////////////////////////////////////////////


class Bluetooth : public AbstractComm
{
    Q_OBJECT

public:
    /*************************************************
    函数名： Bluetooth()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    Bluetooth();

    /*************************************************
    函数名： ~Bluetooth()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~Bluetooth();

    /*************************************************
    函数名： open()
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果--true: 打开成功; false: 打开失败
    功能： 开启本地蓝牙适配器
    *************************************************************/
    virtual bool open() = 0;

    /*************************************************
    函数名： close()
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果--true: 关闭成功; false: 关闭失败
    功能： 关闭本地蓝牙适配器
    *************************************************************/
    virtual bool close() = 0;

    /*************************************************
    函数名： read(char *pRecvBuf, unsigned int uiWantLen)
    输入参数： uiWantLen：期望读取长度，单位为字节
    输出参数： pRecvBuf：接收缓冲
    返回值： int--实际读取长度；若读取失败返回-1
    功能： 读取操作
    *************************************************************/
    virtual int read(char *pRecvBuf, unsigned int uiWantLen);

    /*************************************************
    函数名： read(unsigned int uiWantLen)
    输入参数： uiWantLen：期望读取长度，单位为字节
    输出参数： NULL
    返回值： 读取到的数据
    功能： 读取操作
    *************************************************************/
    virtual QByteArray read(unsigned int uiWantLen);

    /*************************************************
    函数名： write(const char *pSendBuf, unsigned int uiWantLen)
    输入参数： pSendBuf：发送缓冲
              uiWantLen：期望写入长度，单位为字节
    输出参数： NULL
    返回值： int--实际写入长度；若写入失败返回-1
    功能： 写入操作
    *************************************************************/
    virtual int write(const char *pSendBuf, unsigned int uiWantLen);

    /*************************************************
    函数名： write(const QByteArray &baSendData)
    输入参数： baSendData：待发送数据
    输出参数： NULL
    返回值： 操作结果，-1 - 操作失败，其它值 - 实际写入长度
    功能： 写入操作
    *************************************************************/
    virtual int write(const QByteArray &baSendData);

    /*************************************************
    函数名： isOpen
    输入参数： NULL
    输出参数： NULL
    返回值： 操作结果--true: 打开状态; false: 关闭状态
    功能： 查询本地蓝牙适配器打开状态
    *************************************************************/
    bool isOpen();

    /*************************************************
    函数名： changeBufferSize
    输入参数： nPortHandle--读写句柄；recvBuffSize--接收缓冲区；
             tranBuffSize--发送缓冲区
    输出参数： NULL
    返回值： 操作结果--0：成功；其他，失败
    功能： 设置蓝牙串口的发送缓冲，接收缓冲
    *************************************************************/
    int changeBufferSize(int nPortHandle, int recvBuffSize, int tranBuffSize);

public:

    /*************************************************
    函数名： initialize
    输入参数： registeEvent--是否自动监控设备事件
    输出参数： NULL
    返回值： int--成功返回0
    功能： 初始化BT, protocol stack，BluetopiaPM framework
    *************************************************************/
    int initialize(bool bRegisteEvent=true);

    /*************************************************
    函数名： waitforPowerState
    输入参数： timeout--等待超时（单位：秒）
    输出参数： NULL
    返回值： 蓝牙电源打开返回true，关闭返回false
    功能： 开关电源后，等待结果状态（阻塞）
    *************************************************************/
    bool waitforPowerState(uint timeout=16);

    /*************************************************
    函数名： waitforServiceState
    输入参数： timeout--等待超时（单位：秒）
    输出参数： NULL
    返回值： 对方蓝牙服务状态--true表示正常，false表示异常
    功能： 查询蓝牙服务状态后，等待结果（阻塞）
    *************************************************************/
    bool waitforServiceState(uint timeout=8);

    /*************************************************
    函数名： setPowerState
    输入参数： bOn--true：打开蓝牙电源，false：关闭蓝牙电源
    输出参数： NULL
    返回值： int--成功返回0
    功能： 打开或关闭电源
    *************************************************************/
    int setPowerState(bool bOn);

    /*************************************************
    函数名： setDeviceName
    输入参数： strName--蓝牙名称
    输出参数： NULL
    返回值： int--成功返回0
    功能： 设置蓝牙设备名字
    *************************************************************/
    int setDeviceName(const QString &strName);

    /*************************************************
    函数名： setDiscoverable
    输入参数： bEnable--true表示可见，false表示不可见；
             timeOut--设备可见时间，0表示永久
    输出参数： NULL
    返回值： int--成功返回0
    功能： 设置是否可被发现
    *************************************************************/
    int setDiscoverable(bool bEnable, uint timeOut=0);

    /*************************************************
    函数名： setConnectable
    输入参数： bEnable--true表示可连接，false表示不可；
             timeOut--连接超时
    输出参数： NULL
    返回值： int--成功返回0
    功能： 设置是否可被连接
    *************************************************************/
    int setConnectable(bool bEnable, uint timeOut=0);

    /*************************************************
    函数名： setPairable
    输入参数： bEnable--true表示可配对，false表示不可；
             timeOut--配对超时
    输出参数： NULL
    返回值： int--成功返回0
    功能： 设置是否可配对
    *************************************************************/
    int setPairable(bool bEnable, uint timeOut=0);

    /*************************************************
    函数名： queryLocalDevInfo
    输入参数： NULL
    输出参数： devInfo--设备信息
    返回值： int--成功返回0
    功能： 查询设备信息
    *************************************************************/
    int queryLocalDevInfo(DEVM_Local_Device_Properties_t &devInfo);

    /*************************************************
    函数名： queryRemoteDevList
    输入参数： devNum--最多获取设备个数
    输出参数： bdAddrList--设备macID列表
    返回值： int--成功返回0
    功能： 查询搜索到的设备macID
    *************************************************************/
    int queryRemoteDevList(QVector<QString> &bdAddrList, uint devNum = 5);

    /*************************************************
    函数名： StartDeviceDiscovery
    输入参数： timeout--搜索时间（s）
    输出参数： NULL
    返回值： int--成功返回0
    功能： 搜索设备，并获得发现的设备个数
    *************************************************************/
    int startDeviceDiscovery(uint timeout=10);

    /*************************************************
    函数名： StopDeviceDiscovery
    输入参数： NULL
    输出参数： NULL
    返回值： int--成功返回0
    功能： 停止搜索设备
    *************************************************************/
    int stopDeviceDiscovery();

    /*************************************************
    函数名： RegisterAuthentication
    输入参数： NULL
    输出参数： NULL
    返回值： int--成功返回0
    功能： 注册身份认证服务
    *************************************************************/
    int registerAuthentication();

    /*************************************************
    函数名： unRegisterAuthentication
    输入参数： NULL
    输出参数： NULL
    返回值： int--成功返回0
    功能： 注销身份认证服务
    *************************************************************/
    int unRegisterAuthentication();

    /*************Bluetooth************************************
    函数名： pairWithRemoteDev
    输入参数： strDevMAC--设备MAC；bForce--true表示强制配对
    输出参数： NULL
    返回值： int -- -1：调用配对方法失败； 1：已配对； 0：等待配对
    功能： 和远端设备配对
    *************************************************************/
    int pairWithRemoteDev(const QString &strDevMAC, bool bForce=false);

    /*************************************************
    函数名： queryRemoteDevInfo
    输入参数： strDevMAC--设备MAC；
             bForceUpdate--true表示重新询问
    输出参数： devInfo--设备信息
    返回值： int--成功返回0
    功能： 查询远端设备信息
    *************************************************************/
    int queryRemoteDevInfo(DEVM_Remote_Device_Properties_t &devInfo, const QString &strDevMAC, bool bForceUpdate=false);

    /*************************************************
    函数名： cancelPairWithRemoteDev
    输入参数： strDevMAC--远端设备MAC；
    输出参数： NULL
    返回值： int--成功返回0
    功能： 取消正在进行的配对
    *************************************************************/
    int cancelPairWithRemoteDev(const QString &strDevMAC);

    /*************************************************
    函数名： unPairRemoteDev
    输入参数： strDevMAC--远端设备MAC；
    输出参数： NULL
    返回值： int--成功返回0
    功能： 解除已有配对
    *************************************************************/
    int unPairRemoteDev(const QString &strDevMAC);

    /*************************************************
    函数名： userConfirmationResp
    输入参数： bAccept--true表示同意配对，false表示拒绝
    输出参数： NULL
    返回值： int--成功返回0
    功能： 是否同意配对
    *************************************************************/
    int userConfirmationResp(bool bAccept);

    /*************************************************
    函数名： readData
    输入参数： portHandle--端口句柄； buffer--数据缓冲区；
             dataLen--要读取的长度
    输出参数： NULL
    返回值： int--成功返回实际读取长度，失败返回-1
    功能： 读数据
    *************************************************************/
    int readData(int portHandle, char *buffer, int dataLen);

    /*************************************************
    函数名： writeData
    输入参数： portHandle--端口；buffer--数据缓冲区；
             dataLen--要发送数据的长度
    输出参数： NULL
    返回值： int--成功返回实际写的长度，失败返回-1
    功能： 写数据
    *************************************************************/
    int writeData(int portHandle, const char *buffer, int dataLen);

    /*************************************************
    函数名： registerEventCallback
    输入参数： NULL
    输出参数： NULL
    返回值： int--成功返回0
    功能：注册DeviceManager回调事件
    *************************************************************/
    int registerEventCallback();

    /*************************************************
    函数名： unRegisterEventCallback
    输入参数： NULL
    输出参数： NULL
    返回值： int--成功返回0
    功能：注销DeviceManager回调事件
    *************************************************************/
    int unRegisterEventCallback();

    /*************************************************
    函数名： BD_ADDRToStr
    输入参数： Board_Address--mac地址转字符串
    输出参数： BoardStr--字符串地址
    返回值： NULL
    功能：mac地址转字符串
    *************************************************************/
    static void BD_ADDRToStr(BD_ADDR_t Board_Address, char *BoardStr);

    /*************************************************
    函数名： StrToBD_ADDR
    输入参数： BoardStr--字符串地址
    输出参数： Board_Address--mac地址
    返回值： NULL
    功能：字符串地址转mac地址
    *************************************************************/
    static void StrToBD_ADDR(char *BoardStr, BD_ADDR_t *Board_Address);

    /*************************************************
    函数名： StrToBD_ADDR
    输入参数： strMac--字符串地址
    输出参数： Board_Address--mac地址
    返回值： NULL
    功能：字符串地址转mac地址
    *************************************************************/
    void StrToBD_ADDR(const QString &strMac, BD_ADDR_t *Board_Address);

    /*************************************************
    函数名： initDevice
    输入参数： NULL
    输出参数： NULL
    返回值： bool--ture表示设备完成初始化
    功能：初始化设备
    *************************************************************/
    bool initDevice();

    /*************************************************
    函数名： displayLocalDevInfo
    输入参数： UpdateMask--更新标识;
             LocalDeviceProperties--设备信息
    输出参数： NULL
    返回值： NULL
    功能： 显示更新的设备信息
    *************************************************************/
    void displayLocalDevInfo(unsigned long UpdateMask, DEVM_Local_Device_Properties_t *LocalDeviceProperties);

    /*************************************************
    函数名： queryRemoteDevServices
    输入参数： strDevMAC--设备MAC；
             bForce--true表示重新询问，false表示使用上次查询的结果
             len--字节数（bForce为false时必须指定）
    输出参数： NULL
    返回值： int--成功返回0
    功能： 查询远端设备服务信息
    *************************************************************/
    int queryRemoteDevServices(const QString &strDevMAC, bool bForce, uint len=0);

    /*************************************************
    函数名： queryRemoteDevServices
    输入参数： strDevMAC--设备MAC；
    输出参数： NULL
    返回值： int--成功返回0
    功能： 查询远端设备服务信息
    *************************************************************/
    int queryRemoteDevServices(const QString &strDevMAC, bool bUpdate=true);

    /*************************************************
    函数名： DisplayParsedServiceData
    输入参数： ParsedSDPData--服务端信息；
    输出参数： NULL
    返回值： NULL
    功能： 解析服务端信息
    *************************************************************/
    void DisplayParsedServiceData(DEVM_Parsed_SDP_Data_t *ParsedSDPData);

    /*************************************************
    函数名： DisplaySDPAttributeResponse
    输入参数： SDPServiceAttributeResponse--单条记录信息；
             InitLevel--初始化等级
    输出参数： NULL
    返回值： NULL
    功能： 解析单挑记录
    *************************************************************/
    void DisplaySDPAttributeResponse(SDP_Service_Attribute_Response_Data_t *SDPServiceAttributeResponse, unsigned int InitLevel);

    /*************************************************
    函数名： DisplayDataElement
    输入参数： SDPDataElement--SDP元素结构体；
             Level--等级；
    输出参数： NULL
    返回值： NULL
    功能： 显示SDP元素信息
    *************************************************************/
    void DisplayDataElement(SDP_Data_Element_t *SDPDataElement, unsigned int Level);

    /*************************************************
    函数名： cleanup
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 关闭蓝牙服务
    *************************************************************/
    void cleanup();

    /*************************************************
    函数名： emitDataRecved
    输入参数： nPortHandle--端口句柄；nDataLen--数据长度
    输出参数： NULL
    返回值： int--成功返回0
    功能：关闭连接
    *************************************************************/
    void emitDataRecved(uint nPortHandle, uint nDataLen);

    /*************************************************
    函数名： emitConnectState
    输入参数： bFlag--连接状态
    输出参数： NULL
    返回值： NULL
    功能： 发送蓝牙连接状态信号
    *************************************************************/
    void emitConnectState(bool bFlag);

    /*************************************************
    函数名： emitPowerState
    输入参数： bOn--电源开关状态
    输出参数： NULL
    返回值： NULL
    功能： 发送电源状态更新的信号
    *************************************************************/
    void emitPowerState(bool bOn);

    /*************************************************
    函数名： emitScanStopped
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 发送停止扫描的信号
    *************************************************************/
    void emitScanStopped();

    /*************************************************
    函数名： emitPairState
    输入参数： bState--配对结果
    输出参数： NULL
    返回值： NULL
    功能： 发送配对完成的信号
    *************************************************************/
    void emitPairState(bool bState);

    /*************************************************
    函数名： emitPairState
    输入参数： bState--服务状态，true表示正常，false表示异常
    输出参数： NULL
    返回值： NULL
    功能： 发送服务状态响应信号
    *************************************************************/
    void emitServiceState( bool bState );

    /*************************************************
    函数名： setUUID
    输入参数： strUUID--对方UUID
    输出参数： NULL
    返回值： NULL
    功能： 设置对方UUID
    *************************************************************/
    void setUUID(const QString strUUID);

    /*************************************************
    函数名： setServerName
    输入参数： strName--对方应用名
    输出参数： NULL
    返回值： NULL
    功能： 设置对方应用名
    *************************************************************/
    void setServerName(const QString strName);

    /*************************************************
    函数名： updateRemoteDeviceInfos
    输入参数： UpdateMask--更新标识;
             RemoteDeviceProperties--远端设备信息
    输出参数： NULL
    返回值： NULL
    功能： 更新远端设备更新信息
    *************************************************************/
    virtual void updateRemoteDeviceInfos(unsigned long UpdateMask, DEVM_Remote_Device_Properties_t *RemoteDeviceProperties);

    /*************************************************
    函数名： findFreeServerPort
    输入参数：
    输出参数： NULL
    返回值： int
    功能： 获取本地可用的服务端口号
    *************************************************************/
    bool findFreeServerPort();

    /*************************************************
    函数名： registerServerPort
    输入参数：
    输出参数： NULL
    返回值： bool
    功能： 注册本地服务端口号
    *************************************************************/
    bool registerServerPort(unsigned int ServerPort);

    /*************************************************
    函数名： queryServerPresent
    输入参数：
    输出参数： NULL
    返回值： bool
    功能： 确认本地服务端口号可用
    *************************************************************/
    bool queryServerPresent(unsigned int ServerPort);

    /*************************************************
    函数名： userConfirmationResponse
    输入参数：
    输出参数： NULL
    返回值： bool
    功能： 确认蓝牙连接
    *************************************************************/
    bool userConfirmationResponse();

signals:
    void sigReadyRead(qint64 uiLength);
    // 收到数据
    void dataReceived(uint portHandle, uint dataLen);

    // 扫描到一个设备
    void deviceFound(QString strMacID);

    /*************************************************
    信号名： sigScanStopped
    功能： 扫描已停止
    **************************************************/
    void sigScanStopped();

    // 端口关闭
    void portClosed(int handle);

    /*************************************************
    信号名： sigPowerState
    输入参数： bool--蓝牙电源开启状态
    功能： 蓝牙电源开启状态
    **************************************************/
    void sigPowerState(bool);

    /*************************************************
    信号名： sigServiceState
    输入参数： bool--对方服务状态
    功能： 查询对方服务状态的响应信号
    **************************************************/
    void sigServiceState(bool);

    /*************************************************
    信号名： sigConnectState
    输入参数： 蓝牙连接状态--true:连接; false:未连接
    功能：蓝牙连接状态信号
    **************************************************/
    void sigConnectState(bool);

    // 配对状态
    void sigPairState(bool bPaired);

    // 状态改变
    //void stateChanged();

public:
    // 记录当前请求配对的mac地址
    BD_ADDR_t m_curRemoteBdaddr;
    // 身份认证相关的ID
    uint m_authenCallbackID;
    // 读写句柄
    int m_nPortHandle;
    // 蓝牙是否连接
    bool m_bConnect;
    // 蓝牙是否配对
    bool m_bPaired;
    // 是否正在扫描
    bool m_bScan;

protected:
    /*************************************************
    函数名： queryPowerState
    输入参数： NULL
    输出参数： NULL
    返回值： bool--蓝牙电源打开返回true，关闭返回false
    功能： 查询蓝牙电源开启状态
    *************************************************************/
    bool queryPowerState();

protected:
    // 蓝牙是否已打开
    bool m_bOpen;
    // 记录BluetopiaPM是否初始化
    bool m_bInitialized;
    // 记录Device Manager的回调id
    uint m_devmCallbackID;
    // 蓝牙是否上电
    bool m_bPowerState;
    // 蓝牙通信端口
    uint m_nSevrPort;
    // 蓝牙服务状态
    bool m_bServiceState;

    // 128位UUID
    QString m_strUUID;
    // 服务端应用名
    QString m_strServerName;
    // 根据UUID查找端口号的进度
    uint m_SDPStep;

    QSharedPointer<QSemaphore> m_pSemPower;
    QSharedPointer<QSemaphore> m_pSemScan;
    QSharedPointer<QSemaphore> m_pSemConn;
    QSharedPointer<QSemaphore> m_pSemServ;
    QSharedPointer<QSemaphore> m_pSemPair;
};

#endif
