/*
* Copyright (c) 2017.12，南京华乘电气科技有限公司
* All rights reserved.
*
* scrollbar.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年12月01日
* 摘要：色标滚动条

* 当前版本：1.0
*/

#ifndef SCROLLBAR_H
#define SCROLLBAR_H

#include <QWidget>
#include <QPainter>
#include "infrareddatadefine.h"

class ScrollBar : public QWidget
{
    Q_OBJECT
public:
    /*************************************************
    函数名： ScrollBar(ColorType eColorType, Qt::Orientation orientation = Qt::Vertical, QWidget *parent = 0)
    输入参数： eColorType：色标颜色类型
              orientation：对齐方式
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    ScrollBar(ColorType eColorType, Qt::Orientation orientation = Qt::Vertical, QWidget *parent = 0);

    /*************************************************
    函数名： setColorType(const ColorType &eColorType)
    输入参数： eColorType：色标颜色类型
    输出参数： NULL
    返回值： NULL
    功能： 设置色标颜色类型
    *************************************************************/
    void setColorType(const ColorType &eColorType);


protected:
    /*************************************************
    函数名： paintEvent(QPaintEvent *e)
    输入参数： e：重绘事件
    输出参数： NULL
    返回值： NULL
    功能： 重绘事件处理
    *************************************************************/
    void paintEvent(QPaintEvent *e);


private:
    /*************************************************
    函数名： onColorTypeChanged()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应色标颜色类型改变
    *************************************************************/
    void onColorTypeChanged();


private:
    Qt::Orientation m_Orientation;
    ColorType m_eColorType;
    QPainter *m_pPainter;

};

#endif // SCROLLBAR_H
