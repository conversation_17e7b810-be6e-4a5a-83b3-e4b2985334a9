/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* UHFPRPSService.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年12月14日
* 摘要：为了适配接入鲁软，后产生的多用户的使用场景，进行的修改

* 当前版本：1.0
*/

#ifndef TEVPRPSSERVICE_H
#define TEVPRPSSERVICE_H

#include "tev/tevdefine.h"
#include "module_global.h"
#include "Module.h"
#include "tev/tevservice.h"

//class UHFServicePrivate;
class MODULESHARED_EXPORT TEVPRPSService : public TEVService
{
    Q_OBJECT
public:
    /****************************
    功能： 模块单例
    *****************************/
    static TEVPRPSService* instance();

protected:
    /*************************************************
    功能： 启动采样业务的额外处理   （一般指在子类中的额外操作）
    输入参数：
            userId -- 用户ID
    返回：
          额外处理是否成功
    *************************************************/
    virtual bool startSampleExt( MultiServiceNS::USERID userId );

    /*************************************************
    功能： 停止采样业务的额外处理   （一般指在子类中的额外操作）
    输入参数：
            userId -- 用户ID
    返回：
          额外处理是否成功
    *************************************************/
    virtual bool stopSampleExt( MultiServiceNS::USERID userId );

private:
    /****************************
    功能： 构造函数
    *****************************/
    TEVPRPSService();

    /****************************
    功能： 析构函数
    *****************************/
    ~TEVPRPSService();

    /****************************
    功能： disable 拷贝
    *****************************/
    TEVPRPSService( const TEVPRPSService& other );

    /****************************
    功能： disable 赋值
    *****************************/
    TEVPRPSService & operator = (const TEVPRPSService &);

};

#endif // TEVPRPSSERVICE_H
