#include <QThread>
#include "uhf/uhfampservice.h"
#include "model/HCService.h"
#include "model/HCAffair.h"
#include "datadefine.h"
#include "UHFServicePrivate.h"
#include <UHFHFCTAETEVApi.h>
#include <QMutex>
#include <QMutexLocker>

//static QMutex g_mtUhfAmpObj;


/****************************
功能： 模块单例
*****************************/
UHFAmpService* UHFAmpService::instance()
{
    //QMutexLocker stLocker(&g_mtUhfAmpObj);
    static UHFAmpService service;
    return &service;
}

/****************************
功能： 构造函数
*****************************/
UHFAmpService::UHFAmpService()
{

}

/****************************
功能： 析构函数
*****************************/
UHFAmpService::~UHFAmpService()
{

}
