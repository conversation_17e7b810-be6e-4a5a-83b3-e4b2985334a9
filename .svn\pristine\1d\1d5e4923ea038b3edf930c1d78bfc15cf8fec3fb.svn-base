﻿/*
* Copyright (c) 2015.11，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：alsasound.h
*
*
* 初始版本：1.0
* 作者：吴昌盛
* 创建日期：2016年3月14日
* 摘要：设备名及默认设置
*
* 
*/

#ifndef _ALSASOUND_H_
#define _ALSASOUND_H_

#ifdef __cplusplus    
extern "C" {          
#endif

#include <unistd.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <alsa/asoundlib.h>
#include "alsasoundApi.h"
#include "DriverDataDefine.h"

#define     alsasound_printf(fmt,args...)   printf("%s "fmt,__FUNCTION__,##args)

#define     ALSA_DEVICE         "default"                                       //alsa设备

#define     CAPTURE_MUX         "numid=9,iface=MIXER,name='Capture Mux'"                        //录音音源选择
#define     HEADPHONE_MUX       "numid=8,iface=MIXER,name='Headphone Mux'"                      //耳机音源选择
#define     PLAY_ZC             "numid=6,iface=MIXER,name='Headphone Playback ZC Switch'"       //耳机播放过零检测开关
#define     CAPTURE_ZC          "numid=4,iface=MIXER,name='Capture ZC Switch'"                  //录音过零检测开关
#define     CAPTURE_ATTENUATE   "numid=3,iface=MIXER,name='Capture Attenuate Switch (-6dB)'"    //录音衰减开关
#define     MIC_VOLUME          "numid=7,iface=MIXER,name='Mic Volume'"                         //Mic音量
#define     CAPTURE_VOLUME      "numid=2,iface=MIXER,name='Capture Volume'"                     //录音音量
#define     PCM_VOLUME          "numid=1,iface=MIXER,name='PCM Playback Volume'"                //PCM音量
#define     HEADPHONE_VOLUME    "numid=5,iface=MIXER,name='Headphone Playback Volume'"          //耳机音量

#define     ALSA_SET            "amixer cset"                       //设置
#define     ALSA_GET            "amixer cget"                       //读取

#define     CAPTUREMIC          (0)                                //Mic录音
#define     CAPTURELINE         (1)                                //超声波录音

#define     HEADPHONEDAC        (0)                                //DAC输出至耳机
#define     HEADPHONELINE       (1)                                //超声波直接到耳机

#define     SWITCH_ON           (1)                                //开关 开
#define     SWITCH_OFF          (0)                                //开关 关

#define     MAXPCMVOL           (192)
#define     MAXMICVOL           (3)
#define     MAXCAPTUREVOL       (15)

#define     DEFAULT_ACCESS      SND_PCM_ACCESS_RW_INTERLEAVED
#define     DEFAULT_FORMAT      SND_PCM_FORMAT_S16_LE
#define     DEFAULT_CHANNEL     (1)
#define     DEFAULT_SAMPLE      (44100)

#define     TEMPFILEPATH        "/home/<USER>/SoundTemp.z200"        //临时文件路径

#define     ALSACMDMAXLEN       (128)
#define     ALSASOUNDBITS       (16)

/* 设置参数 */
typedef struct 
{
    UINT8 *pucCmd;
    UINT8 *pucContols;
    UINT16 usVal;
}ALSAPARAM;

/* 设置参数 */
typedef struct 
{
    snd_pcm_stream_t    eSnd_pcm_stream_t;
    snd_pcm_uframes_t   ulFrames;
    int                 iFramesize;
    snd_pcm_uframes_t   ulPeriodsize;
}AudioParam;

/* 临时文件的头 */
typedef struct 
{
	INT8				caRld[4];           //riff 标志符号
	INT32               iNoUseLen;          //保留
	INT8    			cWld[4];            //格式类型（wave）
    INT8    			cfld[4];            //"fmt"
    INT32     			iFormatLen;         //sizeof(wave format matex)
	INT16   			sFormatTag;         //编码格式
    INT16   			sChannels;          //声道数
    INT32               iSample;            //采样频率
	INT32     			iAvgBitsPerSample;  //WAVE文件采样大小
    INT16   			sBlockAlign;        //块对齐
    INT16   			sBits;              //位数
	INT8    			caDld[4];           //”data“
    INT32     			iSampleLength;      //音频数据的大小
}WavDataHeader;

#ifdef __cplusplus
}
#endif

#endif
