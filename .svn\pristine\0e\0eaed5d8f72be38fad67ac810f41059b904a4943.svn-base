/*****< btpmtmr.h >************************************************************/
/*      Copyright 2000 - 2014 Stonestreet One.                                */
/*      All Rights Reserved.                                                  */
/*                                                                            */
/*  BTPMTMR - Timer Module for Stonestreet One Bluetopia Platform Manager     */
/*            Prototypes and Constants.                                       */
/*                                                                            */
/*  Author:  <PERSON>                                                      */
/*                                                                            */
/*** MODIFICATION HISTORY *****************************************************/
/*                                                                            */
/*   mm/dd/yy  F. Lastname    Description of Modification                     */
/*   --------  -----------    ------------------------------------------------*/
/*   04/25/00  D. Lange       Initial creation.                               */
/******************************************************************************/
#ifndef __BTPMTMRH__
#define __BTPMTMRH__

#include "TMRAPI.h"              /* Timer Prototypes and Constants.           */

   /* Initialize the Timer Module.  This function must be called before */
   /* any other Timer Module functions can be called.  The Timer Module */
   /* provides a means for other application modules to use Timing      */
   /* Features (only accessible via a Timer Callback) that provide      */
   /* Timing functions.  This module does not provide any functions that*/
   /* are of a delay type nature (i.e. wait for a specified amount of   */
   /* time and then return).  This function returns zero if the         */
   /* Timer Module was successfully initialized, or a non-zero return   */
   /* code if the Timer Module was unable to be initialized.            */
int TMR_Initialize(void);

   /* This function cleans up any resources that the Timer Module       */
   /* currently owns.  Once this function is called, NO other Timer     */
   /* Module functions will work until a successful call to             */
   /* TMR_Initialize() is performed.                                    */
void TMR_Cleanup(void);

#endif
