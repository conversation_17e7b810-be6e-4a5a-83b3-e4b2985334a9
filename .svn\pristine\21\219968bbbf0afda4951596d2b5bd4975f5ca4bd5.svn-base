#include "InfraredDataSave.h"
#include "datadefine.h"
#include "datafile.h"
#include "model/HCStatus.h"
#include "infrared/infrareddatamap.h"
#include "systemsetting/systemsetservice.h"
#include "mapdatafactory.h"
#include "log/log.h"

/*************************************************
函数名： InfraredDataSave()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
InfraredDataSave::InfraredDataSave()
{
    m_pInfraredDataInfo = NULL;
    MapDataFactory::registerClass<InfraredDataMap>(XML_FILE_NODE_INFRARED);
}

/*************************************************
函数名： saveData(void *pData)
输入参数： pDatas：数据
输出参数： NULL
返回值： 数据文件名
功能： 保存数据到指定格式数据文件
*************************************************************/
QString InfraredDataSave::saveData(void *pData)
{
    QTime tt;
    tt.start();

    QString strRes = "";
    if (NULL == pData)
    {
        return strRes;
    }

    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return "";
    }

    m_pInfraredDataInfo = (InfraredDataInfo *)pData;

    if (!createDataPath(m_pInfraredDataInfo->dateTime))
    {
        return strRes;
    }

    DataFile *pFile = new DataFile;

    //----TODO-----------------
    DataMapHead stHeadInfo;
    stHeadInfo.strSubstationName = m_pInfraredDataInfo->strSubstationName;
    stHeadInfo.strSubstationNumber = "";
    setFileHeads(pFile, stHeadInfo);
    //----

    InfraredDataMap *pMap = new InfraredDataMap;
    setMapHead(pMap);
    setMapInfo(pMap);
    setMapData(pMap);

    pFile->addMap(pMap);

    bool bRes = pFile->save(m_strAbsolutePath, INFRARED_FILE_NAME_SUFFIX, strRes);
 //   bool bRes = pFile->save(m_strAbsolutePath, INFRARED_FILE_NAME_SUFFIX, strRes, false);

    delete pFile;

    if ( !bRes )
    {
        QFile file(strRes);
        file.remove();
        strRes.clear();
    }
    //添加文件备注信息
    RemarkInfoManager::instance()->saveRemarkInfo(strRes, m_pInfraredDataInfo->qstrRemark);
    qDebug() << "----save irfile cost: " << tt.elapsed();

    return strRes;
}

/*************************************************
功能： 保存数据到指定格式数据文件
*************************************************************/
QString InfraredDataSave::saveData(void *pData, const QString &qstrSavePath)
{
    if (!pData)
    {
        return "";
    }

    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return "";
    }

    m_pInfraredDataInfo = (InfraredDataInfo *)pData;

    QDir dir(qstrSavePath);
    if(!dir.exists())
    {
        if(!(dir.mkpath(qstrSavePath)))
        {
            return "";
        }
    }

    DataMapHead stHeadInfo;
    stHeadInfo.strSubstationName = m_pInfraredDataInfo->strSubstationName;
    stHeadInfo.strSubstationNumber = "";

    InfraredDataMap *pMap = new InfraredDataMap;
    setMapHead(pMap);
    setMapInfo(pMap);
    setMapData(pMap);

    DataFile *pFile = new DataFile;
    setFileHeads(pFile, stHeadInfo);
    pFile->addMap(pMap);

    QString qstrFilePath = "";
    if (!(pFile->save(qstrSavePath, INFRARED_FILE_NAME_SUFFIX, qstrFilePath)))
    {
        QFile file(qstrFilePath);
        if(file.exists())
        {
            file.remove();
        }
        qstrFilePath = "";
    }

    if(pFile)
    {
        delete pFile;
        pFile = NULL;
    }

    return qstrFilePath;
}

/************************************************
 * 函数名   : getDataByPDA
 * 输入参数 : strFileName: 文件名; pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 获取结果
 * 功能     : 获取指定数据文件中的数据
 ************************************************/
INT32 InfraredDataSave::getDataFromFile(const QString &strFileName, void *pData)
{
    int nRes = HC_FAILURE;
    DataFile *pDataFile = new DataFile;
    bool bRes = pDataFile->open(strFileName);
    //bool bRes = pDataFile->open(strFileName, false);    // for test
    if ( !bRes )
    {
        logError("Fail to open " + strFileName);
        delete pDataFile;
        return HC_FAILURE;
    }

    m_pInfraredDataInfo = (InfraredDataInfo *)pData;
    m_pInfraredDataInfo->strSubstationName = pDataFile->stationName();

    InfraredDataMap *pMap = dynamic_cast<InfraredDataMap*>(pDataFile->dataMap(DataFileNS::SPECTRUM_CODE_INFRARED));
    if ( pMap == NULL )
    {
        logError("Fail to get InfraredDataMap from DataFile");
        delete pDataFile;
        return HC_FAILURE;
    }

    //设置头部信息
    pMap->getDeviceName(m_pInfraredDataInfo->strTestedDevName);
    QString strDtime;
    pMap->getMapGenerationTime(strDtime);
    m_pInfraredDataInfo->dateTime = QDateTime::fromString(strDtime, "yyyy-MM-dd hh:mm:ss");
    m_stHeadInfo.generationDateTime = m_pInfraredDataInfo->dateTime;
    m_stHeadInfo.strDeviceName = m_pInfraredDataInfo->strTestedDevName;
    pMap->getDataType(m_stHeadInfo.eDataPrimitiveType);
    pMap->getDeviceNumber(m_stHeadInfo.strDeviceNumber);
    pMap->getTestPointName(m_stHeadInfo.strTestPointName);
    pMap->getTestPointNumber(m_stHeadInfo.strTestPointNumber);
    pMap->getMapProperty(m_stHeadInfo.eMapProperty);
    pMap->getTestChannelSign(m_stHeadInfo.ucTestChannelSign);

    //设置图谱信息
    InfraredMapNS::InfraredMapInfo stMapInfo;
    pMap->getInfo(&stMapInfo);
    m_pInfraredDataInfo->uiWidth = stMapInfo.iWidth;
    m_pInfraredDataInfo->uiHeight = stMapInfo.iHeight;
    m_pInfraredDataInfo->ucPixelCount = stMapInfo.iPixCount;
    m_pInfraredDataInfo->ucPixelConvertToTemperature = 1; //TODO 待测试
    m_pInfraredDataInfo->fFactor = stMapInfo.fFactor;
    m_pInfraredDataInfo->fOffset = stMapInfo.fOffset;
    m_pInfraredDataInfo->fMaxTemperature = stMapInfo.fMaxTemp;
    m_pInfraredDataInfo->fMinTemperature = stMapInfo.fMinTemp;
    m_pInfraredDataInfo->fAvgTemperature = stMapInfo.fAvgTemp;
    m_pInfraredDataInfo->fRefTemperature = stMapInfo.fRefTemp;
    m_pInfraredDataInfo->fDifTemperature = stMapInfo.fDifTemp;
    m_pInfraredDataInfo->RadiationRate = stMapInfo.fRadiance;
    m_pInfraredDataInfo->TestDistance = stMapInfo.fMeasureDistance;
    m_pInfraredDataInfo->AtmosphericTemperature = stMapInfo.fAtomTemperature;
    m_pInfraredDataInfo->RelativeHumidity = stMapInfo.cRelatedHumidity / 100.0;
    m_pInfraredDataInfo->ReflectedTemperature = stMapInfo.fReflexTemperature;
    m_pInfraredDataInfo->ExternalOpticalTemperatue = stMapInfo.dExtTemp;
    m_pInfraredDataInfo->ExternalOpticalRate = stMapInfo.dExtRate;
    m_pInfraredDataInfo->TauPlanckConstiR = stMapInfo.iPlanckR;
    m_pInfraredDataInfo->TauPlanckConstdblB = stMapInfo.dPlanckB;
    m_pInfraredDataInfo->TauPlanckConstdblF = stMapInfo.dPlanckF;
    m_pInfraredDataInfo->TauPlanckConstdblO = stMapInfo.dPlanckO;
    m_pInfraredDataInfo->SpectralResponsedblX = stMapInfo.dSpecResX;
    m_pInfraredDataInfo->SpectralResponsedblAlpha1 = stMapInfo.dSpecResA1;
    m_pInfraredDataInfo->SpectralResponsedblAlpha2 = stMapInfo.dSpecResA2;
    m_pInfraredDataInfo->SpectralResponsedblBeta1 = stMapInfo.dSpecResB1;
    m_pInfraredDataInfo->SpectralResponsedblBeta2 = stMapInfo.dSpecResB2;
    m_pInfraredDataInfo->iColorType = stMapInfo.iColorType; 

    //设置图谱数据
    InfraredMapNS::InfraredMapData data;
    data.iRawDataLen = m_pInfraredDataInfo->uiWidth * m_pInfraredDataInfo->uiHeight * m_pInfraredDataInfo->ucPixelCount;
    data.pucRawData = new quint8[data.iRawDataLen];
    if ( pMap->getData(&data) )
    {
        memcpy(m_pInfraredDataInfo->aucInfraredData, data.pucRawData, data.iRawDataLen);
        nRes = HC_SUCCESS;
    }

    delete [] data.pucRawData;
    delete pDataFile;

    return nRes;
}

/*************************************************
函数名： getDataTypeFolder()
输入参数： NULL
输出参数： NULL
返回值： 文件夹名
功能： 获取图谱类型文件存储最上层文件夹名
*************************************************************/
QString InfraredDataSave::getDataTypeFolder()
{
    return INFRARED_FOLDER;
}

/*************************************************
函数名： getFileNameSuffix()
输入参数： NULL
输出参数： NULL
返回值： 文件后缀名
功能： 获取数据文件后缀名
*************************************************************/
QString InfraredDataSave::getFileNameSuffix()
{
    return INFRARED_FILE_NAME_SUFFIX;
}

/*************************************************
函数名： getStringFromData(void *pDatas, UINT32 uiCounts)
输入参数： pDatas：数据
          uiCounts：数据个数
输出参数： NULL
返回值： 转换后的字符串
功能： 将数据转成base64的字符串
*************************************************************/
QString InfraredDataSave::getStringFromData(void *pDatas, UINT32 uiCounts)
{
    Q_UNUSED(pDatas);
    Q_UNUSED(uiCounts);
    return QString("");
}

/*************************************************
函数名： saveExtInformation(XMLDocument &doc)
输入参数： doc：XML文件
输出参数： NULL
返回值： NULL
功能： 在XML文件中存储可扩展信息
*************************************************************/
void InfraredDataSave::saveExtInformation(XMLDocument &doc)
{
    Q_UNUSED(doc);
    return;
}

/*************************************************
函数名： saveRawData(XMLDocument &doc)
输入参数： doc：XML文件
输出参数： NULL
返回值： NULL
功能： 在XML文件中存储数据部分
*************************************************************/
void InfraredDataSave::saveRawData(XMLDocument &doc)
{
    Q_UNUSED(doc);
    return;
}

/*************************************************
函数名： parseData(const QByteArray &baData, void *pData, const QString &strFileName = "")
输入参数： baData：数据
          strFileName：unused
输出参数： pData：解析到的数据
返回值： NULL
功能： 解析数据
*************************************************************/
void InfraredDataSave::parseData(const QByteArray &baData, void *pData, const QString &strFileName)
{
    Q_UNUSED(baData);
    Q_UNUSED(pData);
    Q_UNUSED(strFileName);
    return;
}

/*************************************************
输入参数： pMap -- 图谱指针
输出参数： NULL
返回值： NULL
功能： 设置图谱头信息
*************************************************************/
void InfraredDataSave::setMapHead(DataMap *pMap)
{
    pMap->setCode(DataFileNS::SPECTRUM_CODE_INFRARED);
    pMap->setGenerationTime(m_pInfraredDataInfo->dateTime);
    pMap->setSpectrumProperty(DataFileNS::PROPERTY_TEST);
    pMap->setDeviceName(m_pInfraredDataInfo->strTestedDevName);
    pMap->setDeviceNumber(m_stHeadInfo.strDeviceNumber);
    pMap->setTestPointName(m_stHeadInfo.strTestPointName);
    pMap->setTestPointNumber(m_stHeadInfo.strTestPointNumber);
    pMap->setTestLocation(m_stHeadInfo.eTestLocation);
    pMap->setTestChannelSign(m_stHeadInfo.ucTestChannelSign);
    pMap->setDataPrimitiveType(m_stHeadInfo.eDataPrimitiveType);
    pMap->setRemark(m_pInfraredDataInfo->qstrRemark);
    return;
}

/*************************************************
输入参数： pMap -- 图谱指针
输出参数： NULL
返回值： NULL
功能： 设置图谱信息
*************************************************************/
void InfraredDataSave::setMapInfo(InfraredDataMap *pMap)
{
    InfraredMapNS::InfraredMapInfo stInfo;
    memset(&stInfo, 0, sizeof(InfraredMapNS::InfraredMapInfo));

    // 都是摄氏度
    stInfo.fRadiance = (float)m_pInfraredDataInfo->RadiationRate;
    stInfo.fMeasureDistance = (float)m_pInfraredDataInfo->TestDistance;
    stInfo.fAtomTemperature = (float)m_pInfraredDataInfo->AtmosphericTemperature;
    stInfo.cRelatedHumidity = qint8(m_pInfraredDataInfo->RelativeHumidity * 100);
    stInfo.fReflexTemperature = (float)m_pInfraredDataInfo->ReflectedTemperature;

    stInfo.iWidth = m_pInfraredDataInfo->uiWidth;
    stInfo.iHeight = m_pInfraredDataInfo->uiHeight;
    stInfo.iPixCount = m_pInfraredDataInfo->ucPixelCount;
    stInfo.fFactor = m_pInfraredDataInfo->fFactor;
    stInfo.fOffset = m_pInfraredDataInfo->fOffset;
    stInfo.fMaxTemp = m_pInfraredDataInfo->fMaxTemperature;
    stInfo.fMinTemp = m_pInfraredDataInfo->fMinTemperature;
    stInfo.fAvgTemp = m_pInfraredDataInfo->fAvgTemperature;
    stInfo.fRefTemp = m_pInfraredDataInfo->fRefTemperature;
    stInfo.fDifTemp = m_pInfraredDataInfo->fDifTemperature;
    stInfo.dExtTemp = m_pInfraredDataInfo->ExternalOpticalTemperatue;
    stInfo.dExtRate = m_pInfraredDataInfo->ExternalOpticalRate;
    stInfo.iPlanckR = m_pInfraredDataInfo->TauPlanckConstiR;
    stInfo.dPlanckB = m_pInfraredDataInfo->TauPlanckConstdblB;
    stInfo.dPlanckF = m_pInfraredDataInfo->TauPlanckConstdblF;
    stInfo.dPlanckO = m_pInfraredDataInfo->TauPlanckConstdblO;
    stInfo.dSpecResX = m_pInfraredDataInfo->SpectralResponsedblX;
    stInfo.dSpecResA1 = m_pInfraredDataInfo->SpectralResponsedblAlpha1;
    stInfo.dSpecResA2 = m_pInfraredDataInfo->SpectralResponsedblAlpha2;
    stInfo.dSpecResB1 = m_pInfraredDataInfo->SpectralResponsedblBeta1;
    stInfo.dSpecResB2 = m_pInfraredDataInfo->SpectralResponsedblBeta2;
    stInfo.iColorType = m_pInfraredDataInfo->iColorType; 

    pMap->setInfo(&stInfo);
}

/*************************************************
输入参数： pMap -- 图谱指针
输出参数： NULL
返回值： NULL
功能： 设置图谱数据信息
*************************************************************/
void InfraredDataSave::setMapData(InfraredDataMap *pMap)
{
    InfraredMapNS::InfraredMapData data;

    // 暂时只考虑原始数据的情况
    data.pucRawData = m_pInfraredDataInfo->aucInfraredData;
    data.iRawDataLen = m_pInfraredDataInfo->uiWidth * m_pInfraredDataInfo->uiHeight * m_pInfraredDataInfo->ucPixelCount;

    int iBufSize = sizeof(m_pInfraredDataInfo->aucInfraredData);
    if(data.iRawDataLen > iBufSize)
    {
        logWarning("raw data len is bigger than raw array buffer size.");
        data.iRawDataLen = iBufSize;
    }

    pMap->setData(&data);
}

/********************************
 * 功能：设置图谱的头信息
 * 输入参数：
 *      stHeadInfo：头信息
 * *******************************/
void InfraredDataSave::setDataMapInfo(const DataMapHead& stHeadInfo)
{
    m_stHeadInfo = stHeadInfo;
    return;
}

/********************************
 * 功能：获取图谱的头信息
 * *******************************/
DataMapHead InfraredDataSave::getDataMapInfo()
{
    return m_stHeadInfo;
}
