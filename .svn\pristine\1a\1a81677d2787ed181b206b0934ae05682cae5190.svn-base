#include "UHFPeriodDataSave.h"
#include "datafile/datafile.h"
#include "datafile/period/perioddatamap.h"
#include "uhf/UHFConfig.h"
#include "model/HCStatus.h"
#include "systemsetting/systemsetservice.h"
#include "mapdatafactory.h"

/************************************************
 * 函数名   : UHFPeriodDataSave
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 构造函数
 ************************************************/
UHFPeriodDataSave::UHFPeriodDataSave()
{
    m_pUHFPeriodDataInfo = NULL;
    MapDataFactory::registerClass<PeriodDataMap>(XML_FILE_NODE_PERD);
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString UHFPeriodDataSave::saveData(void *pData)
{
    if(NULL == pData)
    {
        return NULL;
    }
    m_pUHFPeriodDataInfo = (UHFPeriodDataInfo*)pData;
    m_strAbsolutePath = getAbsolutePath(m_pUHFPeriodDataInfo->stHeadInfo.generationDateTime);

    return saveData( pData, m_strAbsolutePath );
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString UHFPeriodDataSave::saveData(void *pData, const QString &qsSavedPath)
{
    QString strSavePath("");
    if(NULL == pData)
    {
        return strSavePath;
    }

    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return strSavePath;
    }

    m_pUHFPeriodDataInfo = (UHFPeriodDataInfo*)pData;

    QDir dir(qsSavedPath);
    if(!dir.exists())
    {
        dir.mkpath(qsSavedPath);
    }

    DataFile *pFile = new DataFile;

    setFileHeads(pFile, m_pUHFPeriodDataInfo->stHeadInfo);

    addMap(pFile);

    bool isSuccess = pFile->save(qsSavedPath, UHF_PERIOD_FILE_NAME_SUFFIX, strSavePath);
    qDebug()<<"saved strDataFile is:"<<strSavePath;

    delete pFile;

    if(isSuccess == false)
    {
        QFile file(strSavePath);
        file.remove();
        strSavePath = "";
    }
    //添加文件备注信息
    RemarkInfoManager::instance()->saveRemarkInfo(strSavePath, m_pUHFPeriodDataInfo->stHeadInfo.qstrRemark);
    return strSavePath;
}

void UHFPeriodDataSave::addMap(DataFile *pFile)
{
    PeriodDataMap *pMap = new PeriodDataMap;
    setMapHead(pMap);
    setMapInfo(pMap);
    setMapData(pMap);
    pFile->addMap(pMap);
}

void UHFPeriodDataSave::setMapHead(DataMap *pMap)
{
    pMap->setCode(m_pUHFPeriodDataInfo->stHeadInfo.eCode);
    pMap->setGenerationTime(m_pUHFPeriodDataInfo->stHeadInfo.generationDateTime);
    pMap->setSpectrumProperty(m_pUHFPeriodDataInfo->stHeadInfo.eMapProperty);

    pMap->setDeviceName(m_pUHFPeriodDataInfo->stHeadInfo.strDeviceName);
    pMap->setDeviceNumber(m_pUHFPeriodDataInfo->stHeadInfo.strDeviceNumber);

    pMap->setTestPointName(m_pUHFPeriodDataInfo->stHeadInfo.strTestPointName);
    pMap->setTestPointNumber(m_pUHFPeriodDataInfo->stHeadInfo.strTestPointNumber);

    pMap->setTestLocation(m_pUHFPeriodDataInfo->stHeadInfo.eTestLocation);
    pMap->setTestChannelSign(m_pUHFPeriodDataInfo->stHeadInfo.ucTestChannelSign);
    pMap->setDataPrimitiveType(m_pUHFPeriodDataInfo->stHeadInfo.eDataPrimitiveType);
    pMap->setRemark(m_pUHFPeriodDataInfo->stHeadInfo.qstrRemark);
}

void UHFPeriodDataSave::setMapInfo(PeriodDataMap *pMap)
{
    PeriodMapNS::PeriodMapInfo stInfo;
    stInfo.eAmpUnit = m_pUHFPeriodDataInfo->eAmpUnit;
    stInfo.fAmpMin = m_pUHFPeriodDataInfo->fAmpMin;
    stInfo.fAmpMax = m_pUHFPeriodDataInfo->fAmpMax;
    stInfo.eMapBandWidth = m_pUHFPeriodDataInfo->bandWidth();
    stInfo.ucFreqMin = m_pUHFPeriodDataInfo->ucFreqMin;
    stInfo.ucFreqMax = m_pUHFPeriodDataInfo->ucFreqMax;

    stInfo.iPhaseCount = m_pUHFPeriodDataInfo->iPhaseIntervalCount;
    stInfo.fWarningVal = m_pUHFPeriodDataInfo->ucWarning;
    stInfo.fAlarmVal = m_pUHFPeriodDataInfo->ucAlarm;

    memcpy(stInfo.ucaPdTypeProb, m_pUHFPeriodDataInfo->ucaDischargeTypeProb, sizeof(stInfo.ucaPdTypeProb));
    stInfo.eSyncSource = (DataFileNS::SyncSource)(m_pUHFPeriodDataInfo->eSyncSource + 1);
    stInfo.ucSyncState = m_pUHFPeriodDataInfo->eSyncState;

    stInfo.eDataSign = (DataFileNS::MapDataSign) m_pUHFPeriodDataInfo->eEffectiveDataSign;
    stInfo.eGainType = m_pUHFPeriodDataInfo->eGainType;
    stInfo.iGain = m_pUHFPeriodDataInfo->iGain;
    stInfo.iSyncFreq = m_pUHFPeriodDataInfo->fSyncFreq;

    pMap->setInfo(&stInfo);
}

void UHFPeriodDataSave::setMapData(PeriodDataMap *pMap)
{
    INT16 asData [SPECTTRUMNUM * 2];
    memset( asData, 0, sizeof(INT16) * SPECTTRUMNUM * 2);

    int intvl = 360/SPECTTRUMNUM;
    for( int i = 0; i < SPECTTRUMNUM; ++i )
    {
        int iPhaseShift = (int)m_pUHFPeriodDataInfo->fPhaseShift;
        int iPos = ( i + iPhaseShift/intvl ) % SPECTTRUMNUM;
        asData[iPos * 2] = m_pUHFPeriodDataInfo->astSpectrum[i].sPeakValue;
        asData[iPos * 2 + 1] = iPos*intvl;
    }

    pMap->setData(asData, SPECTTRUMNUM * 2);
}

INT32 UHFPeriodDataSave::getDataFromFile(const QString& strFileName, void *pData)
{
    DataFile *psDataFile = new DataFile;

    //step1 open data file
    bool isSuccess = psDataFile->open(strFileName);
    if(isSuccess == false)
    {
        qDebug() << "!!!!!!!!!!!!!OPen file failed";
        delete psDataFile;
        return HC_FAILURE;
    }

    m_pUHFPeriodDataInfo = (UHFPeriodDataInfo*)pData;
    //获取文件头信息
    m_pUHFPeriodDataInfo->stHeadInfo.strSubstationName = psDataFile->stationName();

    PeriodDataMap * pMap = dynamic_cast <PeriodDataMap*>(psDataFile->dataMap(DataFileNS::SPECTRUM_CODE_UHF_PERD));
    if( NULL == pMap )
    {
        delete psDataFile;
        return HC_FAILURE;
    }
    //设置头部信息
    pMap->getDataType( m_pUHFPeriodDataInfo->stHeadInfo.eDataPrimitiveType );
    pMap->getDeviceName( m_pUHFPeriodDataInfo->stHeadInfo.strDeviceName );
    pMap->getDeviceNumber( m_pUHFPeriodDataInfo->stHeadInfo.strDeviceNumber );
    pMap->getTestPointName( m_pUHFPeriodDataInfo->stHeadInfo.strTestPointName );
    pMap->getTestPointNumber( m_pUHFPeriodDataInfo->stHeadInfo.strTestPointNumber );
    QString strDateTime;
    pMap->getMapGenerationTime( strDateTime );
    m_pUHFPeriodDataInfo->stHeadInfo.generationDateTime = QDateTime::fromString( strDateTime, "yyyy-MM-dd hh:mm:ss" );
    pMap->getMapProperty( m_pUHFPeriodDataInfo->stHeadInfo.eMapProperty );
    pMap->getTestChannelSign( m_pUHFPeriodDataInfo->stHeadInfo.ucTestChannelSign );


    //设置图谱信息
    PeriodMapNS::PeriodMapInfo stMapInfo;
    pMap->getInfo(&stMapInfo);
    m_pUHFPeriodDataInfo->iGain = stMapInfo.iGain;
    m_pUHFPeriodDataInfo->eSyncSource = (SyncSource)(stMapInfo.eSyncSource - 1);
    m_pUHFPeriodDataInfo->eSyncState = (SyncState)stMapInfo.ucSyncState;
    m_pUHFPeriodDataInfo->iPhaseIntervalCount = stMapInfo.iPhaseCount;
    m_pUHFPeriodDataInfo->ucAlarm = (UINT8)stMapInfo.fAlarmVal;
    m_pUHFPeriodDataInfo->ucWarning = (UINT8)stMapInfo.fWarningVal;
    m_pUHFPeriodDataInfo->setBandWidth( stMapInfo.eMapBandWidth );
    m_pUHFPeriodDataInfo->fAmpMin = stMapInfo.fAmpMin;
    m_pUHFPeriodDataInfo->fAmpMax = stMapInfo.fAmpMax;

    //设置数据
    INT16 asValueBuf[SPECTTRUMNUM *2] = {0};
    pMap->getData( asValueBuf, SPECTTRUMNUM *2 );

    for( int i = 0; i < SPECTTRUMNUM; ++i )
    {
        m_pUHFPeriodDataInfo->astSpectrum[i].sPeakValue = asValueBuf[i * 2];
    }
    m_pUHFPeriodDataInfo->setMaxValue();

    delete psDataFile;

    return HC_SUCCESS;
}

/************************************************
 * 函数名   : saveExtInformation
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储可扩展信息
 ************************************************/
void UHFPeriodDataSave::saveExtInformation(XMLDocument& doc)
{
    Q_UNUSED(doc);
#if 0
    //站名
    doc.setValue("SubstationName",m_pUHFPeriodDataInfo->strSubstationName);
    //被测试设备名称
    doc.setValue("TestedDevName",m_pUHFPeriodDataInfo->strTestedDevName);
    //采样时间
    doc.setValue("SampleTime",convertTimeToSampleTimeFormat(m_pUHFPeriodDataInfo->dateTime));
    //同步方式
    doc.setValue("SyncMode",QString::number(m_pUHFPeriodDataInfo->eSyncSource));
    //同步状态
    doc.setValue("SyncStatus",QString::number(m_pUHFPeriodDataInfo->eSyncState));
    //带宽
    doc.setValue("BandWidth",QString::number(m_pUHFPeriodDataInfo->eBandWidth));
    //前置增益
    doc.setValue("Pregain",QString::number(m_pUHFPeriodDataInfo->ePreGain));
    //报警值
    doc.setValue("Alarm",QString::number(m_pUHFPeriodDataInfo->ucAlarm));
    //预警值
    doc.setValue("Warning",QString::number(m_pUHFPeriodDataInfo->ucWarning));
    //相位偏移(来源手持设备)
    doc.setValue("PhaseShift",QString::number(m_pUHFPeriodDataInfo->fPhaseShift));
    //相位偏移(来源上位机软件)
    doc.setValue("SoftWarePhaseShift",QString::number(m_pUHFPeriodDataInfo->fSoftWarePhaseShift));
    //UHF周期最大值
    doc.setValue("Max",QString::number(m_pUHFPeriodDataInfo->cMax));
#endif
}

/************************************************
 * 函数名   : saveRawData
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储数据部分
 ************************************************/
void UHFPeriodDataSave::saveRawData(XMLDocument& doc)
{
    Q_UNUSED(doc);
#if 0
    doc.setValue("Data", getStringFromData(m_pUHFPeriodDataInfo->acSpectrum, SPECTTRUMNUM));
#endif
}

/************************************************
 * 函数名   : parseData
 * 输入参数 : baData: 数据
 * 输出参数 : pData: 解析到的数据
 * 返回值   : void
 * 功能     : 解析数据
 ************************************************/
void UHFPeriodDataSave::parseData(const QByteArray& baData, void *pData, const QString &strFileName)
{
    Q_UNUSED(baData);
    Q_UNUSED(pData);
    Q_UNUSED(strFileName);
#if 0
    QMutexLocker locker(&m_mutex);
    m_pUHFPeriodDataInfo = (UHFPeriodDataInfo*)pData;

    XMLDocument doc(baData);

    doc.beginElement("ExtInformation");
    m_pUHFPeriodDataInfo->strSubstationName = doc.value("SubstationName");
    m_pUHFPeriodDataInfo->strTestedDevName = doc.value("TestedDevName");
    m_pUHFPeriodDataInfo->dateTime = convertToDateTime(doc.value("SampleTime"));
    m_pUHFPeriodDataInfo->eSyncSource = SyncSource(doc.value("SyncMode").toInt() - 1);
    m_pUHFPeriodDataInfo->eSyncState = SyncState(doc.value("SyncStatus").toInt());
    m_pUHFPeriodDataInfo->eBandWidth = UHFFilterControl(doc.value("BandWidth").toInt());
    m_pUHFPeriodDataInfo->ePreGain = UHFHFCTGain(doc.value("Pregain").toInt());
    m_pUHFPeriodDataInfo->ucAlarm = (UINT8)(doc.value("Alarm").toUInt());
    m_pUHFPeriodDataInfo->ucWarning = (UINT8)(doc.value("Warning").toUInt());
    m_pUHFPeriodDataInfo->fPhaseShift = doc.value("PhaseShift").toFloat();
    m_pUHFPeriodDataInfo->fSoftWarePhaseShift = doc.value("SoftWarePhaseShift").toFloat();
    m_pUHFPeriodDataInfo->cMax = (INT8)(doc.value("Max").toInt());
    doc.endElement();

    memset(m_pUHFPeriodDataInfo->acSpectrum,0x0,sizeof(INT8)*SPECTTRUMNUM);
    QByteArray data = QByteArray::fromBase64(doc.value("Data").toLatin1());
    memcpy(m_pUHFPeriodDataInfo->acSpectrum,(INT8*)(data.data()),data.count());
#endif
}

/************************************************
 * 函数名   : getDataTypeFolder
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件夹名
 * 功能     : 获取图谱类型文件存储最上层文件夹名
 ************************************************/
QString UHFPeriodDataSave::getDataTypeFolder(void)
{
    return UHF_PERIOD_FOLDER;
}

/************************************************
 * 函数名   : getFileNameSuffix
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件后缀名
 * 功能     : 获取数据文件后缀名
 ************************************************/
QString UHFPeriodDataSave::getFileNameSuffix(void)
{
    return UHF_PERIOD_FILE_NAME_SUFFIX;
}
