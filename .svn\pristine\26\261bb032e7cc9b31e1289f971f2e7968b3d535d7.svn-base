/*
* Copyright (c) 2016.05, 华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: FastWavePDSS.h
*
* 初始版本: 1.0
* 作者: 王谦
* 创建日期: 2016年05月10日
* 摘要: 该文件定义了百兆采集接入G100功能，pdss图谱
*/

#ifndef FASTWAVEPDSS_H
#define FASTWAVEPDSS_H

#include <QWidget>
#include <QMouseEvent>
#include <QHash>
#include "FastWaveBase.h"
#include "analysisCurve.h"
#include "datadefine.h"
#include "analysisTypeDefine.h"

class FastWavePDSS : public FastWaveBase
{
    Q_OBJECT

signals:
    /************************************************
     * 函数名    :sigSelectedData(signal)
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：发送选中的数据序号
     ************************************************/
    void sigSelectedData( QVector< UINT16 > );

public:
    /************************************************
     * 函数名    :FastWavePDSS
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：构造函数
     ************************************************/
    explicit FastWavePDSS(QWidget *parent = 0);

    /************************************************
     * 函数名    :FastWavePDSS
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：析构函数
     ************************************************/
     ~FastWavePDSS();

    /************************************************
     * 函数名    :setScale
     * 输入参数  ：min -- 最小值
     *            max -- 最大值
     *            step -- 步进
     *           eAxisScale -- 横、纵坐标轴方向
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置横、纵坐标轴方向的范围和步进
     ************************************************/
    void setScale( AxisScale eAxisScale,double min,double max,double step = 0 );

    void scale( AxisScale eAxisScale,double &min,double &max );
    /************************************************
     * 函数名    :setDatas
     * 输入参数  ：xData -- x轴数据集合
     *           yData -- y轴数据集合
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：添加数据
     ************************************************/
    void setDatas (const QVector<UINT16> &index, const QVector< double > &xData, const QVector< double > &yData );

    /************************************************
     * 函数名    :setAnalysisStateType
     * 输入参数  ：eAnalysisStateType -- 分析状态
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置分析状态
     ************************************************/
    void setAnalysisStateType( CAView::AnalysisStateType eAnalysisStateType );

    /************************************************
     * 函数名    :deleteManualAnalysisGraphical
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：清除手动分析模式
     ************************************************/
    void deleteManualAnalysisGraphical( void );

    /************************************************
     * 函数名    :clear
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：清除显示
     ************************************************/
    void clear( void );

    /************************************************
     * 函数名    :isDataExist
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：判断图谱是否有数据显示
     ************************************************/
    bool isDataExist( void );

    /****************************
    功能： 删除指定图形
    *****************************/
    void deleteItem( analysisCurve::ColorType eType );

    /****************************
    功能： 根据图谱颜色类型获取数据
    *****************************/
    QVector< UINT16 >  dataFromColorType( analysisCurve::ColorType eType ) const;
protected:
    /************************************************
     * 函数名    :eventFilter
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：事件过滤
     ************************************************/
    bool eventFilter(QObject *, QEvent *);

private:
    /************************************************
     * 函数名    :dataFromRect
     * 输入参数  ：qRect:选中的矩形区域
     * 输出参数  ：NULL
     * 返回值   ：QVector< UINT16 > ：选中区域内的数据索引号集合
     * 功能     ：根据获得的矩形，计算落入其中的数据集合
     ************************************************/
    QVector< UINT16 > const& dataFromRect(const QRectF &qRect );

    /************************************************
     * 函数名    :dataFromRect
     * 输入参数  ：qRect:选中的矩形区域
     * 输出参数  ：NULL
     * 返回值   ：QVector< UINT16 > ：选中区域内的数据索引号集合
     * 功能     ：根据获得的矩形，计算落入其中的数据集合
     ************************************************/
    QVector< UINT16 > const& dataFromAnalysisdata(const AnalysisData &qData );

    /************************************************
     * 函数名    :dataFromRect
     * 输入参数  ：qRect:选中的矩形区域
     * 输出参数  ：NULL
     * 返回值   ：QPointF ：选中区域内的数据索引号集合
     * 功能     ：根据获得的矩形，计算落入其中的数据集合
     ************************************************/
    QVector< UINT16 > const& dataFromPoint(const QPointF &qPointF );

    /************************************************
     * 函数名    :isContainsPoint
     * 输入参数  ：point:点
     * 输出参数  ：NULL
     * 返回值   ：bool
     * 功能     ：判断点是否落在椭圆内部
     ************************************************/
    bool isContainsPoint( const QRectF rectf,const QPointF& point );

    /************************************************
     * 函数名    :handlePressEvent
     * 输入参数  ：pos:点
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：根据当前状态处理点击事件
     ************************************************/
    void handlePressEvent( const QPointF &pos );

    /************************************************
     * 函数名    :handlePressEvent
     * 输入参数  ：pos:点
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：根据当前状态处理移动事件
     ************************************************/
    void handleMoveEvent( const QPointF &pos );

    /************************************************
     * 函数名    :handleRealeaseEvent
     * 输入参数  ：pos:点
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：根据当前状态处理鼠标释放事件
     ************************************************/
    void handleRealeaseEvent( const QPointF &pos );

    /************************************************
     * 函数名    :choosenRect
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：获得选中的矩形
     ************************************************/
    AnalysisData choosenRect( void );

    /************************************************
     * 功能     ：创建指定颜色的数据曲线
     ************************************************/
    QwtPlotCurve * createCurve( const QColor& color );

    /************************************************
     * 功能     ：将缓存中的数据刷新至界面
     ************************************************/
    void refresh( const QHash< QString,QVector<QPointF> >& hTempData );
private:
    typedef enum _ColorInfos
    {
        COLOR_TYPE_COUNT = 4,//颜色种类
    }ColorInfos;

    typedef struct _PointData
    {
        QPointF pointF;
        QColor color;
    }PointData;

    bool    m_bPressed;   // 判断是否点击的标志
    bool    m_bIsChoosed; // 判断是否选中移动的标志
    QPointF  m_PressPoint;   // 记录点击时的坐标
    QPointF  m_MovePoint;   // 记录移动时的坐标
    UINT16 m_usMoveEventCnt; // 记录move事件产生的次数，加限制即拖动若干次刷新一次

    QHash< UINT16,PointData > m_hRawData;
    QHash< QString,QwtPlotCurve* > m_hDataCurves;
    QVector< UINT16 > m_vIndexVector;

    static const QString COLOR_TYPE[COLOR_TYPE_COUNT];
    CAView::AnalysisStateType   m_eAnalysisStateType;
    analysisCurve   *m_pAnalysisCurve; // 绘制静态分析的曲线
};

#endif // FASTWAVEPDSS_H
