#include "hfctprpsplaybackview.h"
#include "hfct/HFCT.h"
#include "hfct/HFCTViewConfig.h"
#include "prps/prpddatamap.h"
#include "window/Window.h"
#include "datafile/prps/prpsmapdefine.h"
#include "datafile/mapdatafactory.h"
#include "diagnosismgr/diagnosismanager.h"
#include "dataspecification/dataspecification.h"
#include "dataspecification/prps/prpsspectrum.h"
#include "dataspecification/prps/prpdspectrum.h"
#include "customaccesstask/taskmanager.h"
#include "systemsetting/systemsetservice.h"
#include "appconfig.h"
#include "log/log.h"

const int gs_iPrpdMargin = 60;

enum UHFPRPSPlayBackButton
{
    BUTTON_DELETE_DATA, // 删除数据
};

// 控制按钮定义
const ButtonInfo::Info s_HFCTButtonInfo[] =
{
    { BUTTON_DELETE_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_DELETE_DATA, NULL, ":/images/sampleControl/delete.png", NULL } },//删除数据
};

/****************************
功能： 构造函数
输入参数:
    parent -- 父窗体
*****************************/
HFCTPRPSPlayBackView::HFCTPRPSPlayBackView(QWidget *parent) :
    PlayBackBase(parent)
{
    QPalette p;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    SystemSet::PRPSBGColor eColor = (SystemSet::PRPSBGColor) pConfig->value( APPConfig::KEY_PRPS_BG_COLOR ).toInt();
    pConfig->endGroup();
    if(eColor == SystemSet::PRPS_BG_COLOR_GRAY)
    {
        p.setColor( QPalette::Background,QColor(128, 128, 128) );
    }
    else
    {
        p.setColor( QPalette::Background,Qt::white );
    }
    setPalette( p );
    setAutoFillBackground( true );

    m_pChart = new HfctPrpsUnionView(PERIOD_CNT, PERIOD_CNT, PHASE_CNT, HFCT::CHART_MAX_VALUE,HFCT::CHART_MIN_VALUE);
    m_pChart->setFixedSize(Window::WIDTH, CHART_HEIGHT);
    m_pChart->setAltasType(PhaseAbstractView::PRPS_PRPD);
    m_pChart->setPrpdContentsMargins(0, gs_iPrpdMargin, 0, gs_iPrpdMargin);
    setCenterWidget( m_pChart );
}

/****************************
功能： 回放文件
输入参数:
    strFileName -- 文件名
*****************************/
void HFCTPRPSPlayBackView::playbackFile( const QString& strFileName )
{
    qDebug() << "HFCTPRPSPlayBackView::playbackFile: " << strFileName;
    if (strFileName.contains(".dat"))
    {
        if (!buttonBar())
        {
            createButtonBar(HFCT::CONTEXT, s_HFCTButtonInfo, sizeof(s_HFCTButtonInfo)/sizeof(ButtonInfo::Info));
        }
        playbackBinaryFile(strFileName);
    }
    else
    {
        playbackXmlFile(strFileName);
    }
    m_qstrPlayBackFile = strFileName;
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void HFCTPRPSPlayBackView::onCommandButtonPressed(int id)
{
    switch( id )
    {
    case BUTTON_DELETE_DATA: // 删除数据
    {
        if (MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to delete the file?")))
        {
            emit sigDeleteCurrentFile();
        }
//        TaskManager::instance()->deleteSpecifiedTestFile(m_qstrPlayBackFile);
//        m_qstrPlayBackFile.clear();
    }
        break;
    default:
        break;
    }
}

/****************************
功能： 回放xml文件
输入参数:
    qstrFilePath -- 文件名
*****************************/
void HFCTPRPSPlayBackView::playbackXmlFile(const QString& qstrFilePath)
{
    HFCTPRPSAndPRPDDataSave  fDataSave;
    HFCTPRPSPRPDDataInfo sPlayBackDataInfo;

    int value = fDataSave.getData(qstrFilePath, &sPlayBackDataInfo);

    if( HC_FAILURE == value )
    {
        logError(QString("review file (%1) get data failed.").arg(qstrFilePath));
        return;
    }
    displayMap(sPlayBackDataInfo);
}

/****************************
功能： 回放二进制文件
输入参数:
    qstrFilePath -- 文件名
*****************************/
void HFCTPRPSPlayBackView::playbackBinaryFile(const QString& qstrFilePath)
{
    if (qstrFilePath == m_qstrPlayBackFile)
    {
        return;
    }

    DataSpecificationNS::DataSpecification dataSpecification;
    dataSpecification.setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());
    if(!dataSpecification.parseBinaryFromFile(qstrFilePath))
    {
        dbg_warning("parse binary file failed!\n");
        return;
    }

    HFCTPRPSPRPDDataInfo stHFCTPRPSPRPDDataInfo;
    getDataFromDataSpecification(&dataSpecification, stHFCTPRPSPRPDDataInfo);

    displayMapFromBinaryFile(stHFCTPRPSPRPDDataInfo);
}

/****************************
功能： 在回放中显示
输入参数: void
*****************************/
void HFCTPRPSPlayBackView::displayMap(HFCTPRPSPRPDDataInfo &stInfo)
{
    m_pChart->clearData();
    /*站点名*/
    setStationName( stInfo.stPRPSHeadInfo.strSubstationName );
    /*设备名*/
    setDeviceName( stInfo.stPRPSHeadInfo.strDeviceName );
    /*设置频率*/
    m_pChart->setPowerFreq(PrpsGlobal::FREQ_50);

    /*设置量程*/
    m_pChart->setRangeMin(stInfo.stPRPSInfo.fAmpLowerLimit);
    m_pChart->setRangeMax(stInfo.stPRPSInfo.fAmpUpperLimit);

    m_pChart->setSync((PrpsGlobal::SyncSource) (stInfo.stPRPSInfo.eSyncSource -1), (PrpsGlobal::SyncState) stInfo.stPRPSInfo.ucSyncState);

    //是否无信号状态没有保存到数据文件，回放时，先简单默认不显示无信号
    m_pChart->setConnected(true);

    QVector<qint16> prpdData;
    prpdData.clear();

    int iPeriodCnt = stInfo.stPRPDInfo.iPowerFreCycleCount;
    double dTimePerPeriod = (1000.0 / (double) stInfo.ucFreq) / 1000.0;//每周期时间 单位s
    logInfo(QString("review data period number: %1.").arg(iPeriodCnt));

    double dPRRepeatyData = 0;
    for(int i = 0; i < stInfo.stPRPDInfo.iQuantificationAmp; ++i)
    {
        for(int j = 0; j < stInfo.stPRPDInfo.iPhaseIntervalCount; ++j)
        {
            dPRRepeatyData = stInfo.vecPRRepeatyData.at(j * stInfo.stPRPDInfo.iQuantificationAmp + i);
            dPRRepeatyData = dPRRepeatyData * iPeriodCnt * dTimePerPeriod;
            prpdData.append((qint16)(dPRRepeatyData + 0.5f));
        }
    }

    double dMax = double(stInfo.stPRPSInfo.fMax);
    m_pChart->setPeriodNum(iPeriodCnt);
    m_pChart->addPlayBackData(stInfo.vecPRPSData, prpdData, stInfo.stPRPDInfo.iPowerFreCycleCount);
    /*设置最大值*/
    m_pChart->setMaxSpectrum(dMax, (PrpsGlobal::SpectrumState)(stInfo.stPRPSInfo.eDataSign));

    QString qstrDesInfo = DiagnosisManager::instance()->getDiagInfoByPlayback(stInfo.stPRPSHeadInfo.qstrPDSignalTypeInfos, DIAG_PRPS);
    DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
    stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stInfo.stPRPSHeadInfo.ePDDefectLevel);
    stDiagDisplayInfo.qstrPDDesInfo = qstrDesInfo;
    stDiagDisplayInfo.qstrPDSignalInfos = stInfo.stPRPSHeadInfo.qstrPDSignalTypeInfos;
    m_pChart->playbackDiagInfo(stDiagDisplayInfo);

    m_pChart->setPRPSThreshold(stInfo.stPRPSInfo.fAnalysisThreshold);
    m_pChart->setPRPDThreshold(stInfo.stPRPDInfo.fAnalysisThreshold);
}

/****************************
功能： 在回放中显示
输入参数: void
*****************************/
void HFCTPRPSPlayBackView::displayMapFromBinaryFile(HFCTPRPSPRPDDataInfo& stHFCTPRPSPRPDDataInfo)
{
    m_pChart->clearData();

    //定制接入终端的hfct数据格式和常规hfct数据格式不一致，这里不显示
    //m_pChart->setPowerFreq( (PrpsGlobal::Frequency)( stDataInfo.ucFreq ));

    //log_debug("sync source: %d, state: %d.", stDataInfo.stPRPSInfo.eSyncSource, stDataInfo.stPRPSInfo.ucSyncState);
    //定制接入终端的hfct数据格式和常规hfct数据格式不一致，这里不显示
    //m_pChart->setSync((PrpsGlobal::SyncSource)(stDataInfo.stPRPSInfo.eSyncSource - 1), (PrpsGlobal::SyncState)stDataInfo.stPRPSInfo.ucSyncState);
    m_pChart->clearSyncText();

    /*设置量程*/
    m_pChart->setRangeMin(stHFCTPRPSPRPDDataInfo.stPRPSInfo.fAmpLowerLimit);
    m_pChart->setRangeMax(stHFCTPRPSPRPDDataInfo.stPRPSInfo.fAmpUpperLimit);

    //是否无信号状态没有保存到数据文件，回放时，先简单默认不显示无信号
    m_pChart->setConnected(true);

    QVector<qint16> prpdData;
    prpdData.clear();
    double dPRPulseCnt;

    //NOTE 如果是累计模式，iPeriodCnt的计算不正确
    int iPeriodCnt = stHFCTPRPSPRPDDataInfo.vecPRRepeatyData.size() / stHFCTPRPSPRPDDataInfo.stPRPDInfo.iPhaseIntervalCount;

    for(int i = 0; i < stHFCTPRPSPRPDDataInfo.stPRPDInfo.iQuantificationAmp; ++i)
    {
        for(int j = 0; j < stHFCTPRPSPRPDDataInfo.stPRPDInfo.iPhaseIntervalCount; ++j)
        {
            dPRPulseCnt = stHFCTPRPSPRPDDataInfo.vecPRRepeatyData.at(j * stHFCTPRPSPRPDDataInfo.stPRPDInfo.iQuantificationAmp + i);
            prpdData.append((qint16)(dPRPulseCnt));
        }
    }

    //log_debug("max value: %f.", stDataInfo.stPRPSInfo.fMax);

    m_pChart->setPeriodNum(iPeriodCnt);
    m_pChart->addPlayBackData(stHFCTPRPSPRPDDataInfo.vecPRPSData, prpdData, stHFCTPRPSPRPDDataInfo.stPRPDInfo.iPowerFreCycleCount);
    m_pChart->setPhaseOffset(0);

    double dMax = 0;
    if(stHFCTPRPSPRPDDataInfo.vecPRPSData.size() > 0)
    {
        dMax = stHFCTPRPSPRPDDataInfo.vecPRPSData.at(0);
        for(int i = 1, iSize = stHFCTPRPSPRPDDataInfo.vecPRPSData.size(); i < iSize; ++i)
        {
            if(stHFCTPRPSPRPDDataInfo.vecPRPSData.at(i) > dMax)
            {
                dMax = stHFCTPRPSPRPDDataInfo.vecPRPSData.at(i);
            }
        }
    }

    m_pChart->setMaxSpectrum(dMax, (PrpsGlobal::SpectrumState)(stHFCTPRPSPRPDDataInfo.stPRPSInfo.eDataSign));

    //本地诊断功能
    if(m_pChart->isLocalDiagnosisEnable())
    {
        PRPSDiagInfo stDiagInfo;
        stDiagInfo.qvtDataIn.clear();
        stDiagInfo.dThresholdDbVal = 0;
        stDiagInfo.dSpecMaxVal = m_pChart->rangeMax();
        stDiagInfo.dSpecMinVal = m_pChart->rangeMin();

        stDiagInfo.qvtDataIn = m_pChart->prpsDiagData(stDiagInfo.iPeriodNum, stDiagInfo.iPhaseNum);

        DiagResultInfo stRetInfo;
        DiagnosisManager::instance()->diagPRPSDataDirectly(stDiagInfo, stRetInfo);

        DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
        stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stRetInfo.stDiagRet.defectLevel);
        stDiagDisplayInfo.qstrPDDesInfo = stRetInfo.qstrPDDescription;
        stDiagDisplayInfo.qstrPDSignalInfos = stRetInfo.qstrPDSignalTypeInfos;

        m_pChart->playbackDiagInfo(stDiagDisplayInfo);
    }
}

void HFCTPRPSPlayBackView::getDataFromDataSpecification(DataSpecificationNS::DataSpecification* pDataSpecification, HFCTPRPSPRPDDataInfo& stHFCTPRPSPRPDDataInfo)
{
    getFileHead(pDataSpecification, stHFCTPRPSPRPDDataInfo);

    DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum = dynamic_cast<DataSpecificationNS::PRPSSpectrum*>(pDataSpecification->spectrum(DataSpecificationNS::SPECTRUM_CODE_HFCT_PRPS));
    if(NULL == pPRPSSpectrum)
    {
        dbg_warning("get prps map data failed.");
        return;
    }

    DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum = dynamic_cast<DataSpecificationNS::PRPDSpectrum*>(pDataSpecification->spectrum(DataSpecificationNS::SPECTRUM_CODE_HFCT_PRPD));
    if(NULL == pPRPDSpectrum)
    {
        dbg_warning("get prpd map data failed.");
        return;
    }

    getPRPSMapHead(pPRPSSpectrum, stHFCTPRPSPRPDDataInfo);
    getPRPDMapHead(pPRPDSpectrum, stHFCTPRPSPRPDDataInfo);

    DataSpecificationNS::PRPSExtInformation stPRPSExtInformation;
    pPRPSSpectrum->getPRPSExtInformation(stPRPSExtInformation);
    getPRPSMapInfo(&stPRPSExtInformation, stHFCTPRPSPRPDDataInfo);

    DataSpecificationNS::PRPDExtInformation stPRPDExtInformation;
    pPRPDSpectrum->getPRPDExtInformation(stPRPDExtInformation);
    getPRPDMapInfo(&stPRPDExtInformation, stHFCTPRPSPRPDDataInfo);

    DataSpecificationNS::PRPSData stPRPSData;
    pPRPSSpectrum->getPRPSData(stPRPSData);
    int iDataPointNum = stPRPSExtInformation.iPhaseWindowCount * stPRPSExtInformation.iPowerFreqCycleCount;
    stHFCTPRPSPRPDDataInfo.vecPRPSData.resize(iDataPointNum);
    memcpy(&stHFCTPRPSPRPDDataInfo.vecPRPSData[0], stPRPSData.qbaPDSpectrumData.data(), iDataPointNum * sizeof(double));

    DataSpecificationNS::PRPDData stPRPDData;
    pPRPDSpectrum->getPRPDData(stPRPDData);
    iDataPointNum = stPRPDExtInformation.iPhaseWindowCount * stPRPDExtInformation.iQuantizedAmplitude;
    stHFCTPRPSPRPDDataInfo.vecPRRepeatyData.resize(iDataPointNum);
    memcpy(&stHFCTPRPSPRPDDataInfo.vecPRRepeatyData[0], stPRPDData.qbaPDSpectrumData.data(), iDataPointNum * sizeof(double));
}

void HFCTPRPSPlayBackView::getFileHead(DataSpecificationNS::DataSpecification* pDataSpecification, HFCTPRPSPRPDDataInfo& stHFCTPRPSPRPDDataInfo)
{
    DataSpecificationNS::SpectrumDataFileHead stSpectrumDataFileHead;
    pDataSpecification->getSpectrumDataFileHead(stSpectrumDataFileHead);
    stHFCTPRPSPRPDDataInfo.ucFreq = stSpectrumDataFileHead.fSystemFrequency;
    stHFCTPRPSPRPDDataInfo.stPRPSHeadInfo.strSubstationName = stSpectrumDataFileHead.qstrStationName;
    stHFCTPRPSPRPDDataInfo.stPRPDHeadInfo.strSubstationName = stSpectrumDataFileHead.qstrStationName;
}

void HFCTPRPSPlayBackView::getPRPSMapHead(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum, HFCTPRPSPRPDDataInfo& stHFCTPRPSPRPDDataInfo)
{
    DataSpecificationNS::SpectrumHead stSpectrumHead;
    pPRPSSpectrum->getSpectrumHead(stSpectrumHead);
    stHFCTPRPSPRPDDataInfo.stPRPSHeadInfo.strDeviceName = stSpectrumHead.qstrEquipmentName;
    stHFCTPRPSPRPDDataInfo.stPRPSHeadInfo.eDataPrimitiveType = static_cast<DataFileNS::DataPrimitiveType>(stSpectrumHead.eStorageDataType);
}

void HFCTPRPSPlayBackView::getPRPDMapHead(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum, HFCTPRPSPRPDDataInfo& stHFCTPRPSPRPDDataInfo)
{
    DataSpecificationNS::SpectrumHead stSpectrumHead;
    pPRPDSpectrum->getSpectrumHead(stSpectrumHead);
    stHFCTPRPSPRPDDataInfo.stPRPDHeadInfo.strDeviceName = stSpectrumHead.qstrEquipmentName;
    stHFCTPRPSPRPDDataInfo.stPRPDHeadInfo.eDataPrimitiveType = static_cast<DataFileNS::DataPrimitiveType>(stSpectrumHead.eStorageDataType);
}

void HFCTPRPSPlayBackView::getPRPSMapInfo(const DataSpecificationNS::PRPSExtInformation* pPRPSExtInformation, HFCTPRPSPRPDDataInfo& stHFCTPRPSPRPDDataInfo)
{
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.eSyncSource = static_cast<DataFileNS::SyncSource>(pPRPSExtInformation->eSyncSource);
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.ucSyncState = pPRPSExtInformation->ucSyncState;
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.iPhaseIntervalCount = pPRPSExtInformation->iPhaseWindowCount;
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.iQuantificationAmp = pPRPSExtInformation->iQuantizedAmplitude;
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.iPowerFreCycleCount = pPRPSExtInformation->iPowerFreqCycleCount;
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.eAmpUnit = static_cast<DataFileNS::AmpUnit>(pPRPSExtInformation->eAmpUnit);
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.eBandWidth = static_cast<DataFileNS::MapBandWidth>(pPRPSExtInformation->eFrequencyBand);
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.sGain = pPRPSExtInformation->sGain;
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.eDataSign = static_cast<PRPSMapNS::MapDataSign>(pPRPSExtInformation->eDataJudgmentFlag);
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.fAmpLowerLimit = pPRPSExtInformation->fAmpLowerLimit;
    stHFCTPRPSPRPDDataInfo.stPRPSInfo.fAmpUpperLimit = pPRPSExtInformation->fAmpUpperLimit;
}

void HFCTPRPSPlayBackView::getPRPDMapInfo(const DataSpecificationNS::PRPDExtInformation* pPRPDExtInformation, HFCTPRPSPRPDDataInfo& stHFCTPRPSPRPDDataInfo)
{
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.eSyncSource = static_cast<DataFileNS::SyncSource>(pPRPDExtInformation->eSyncSource);
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.ucSyncState = pPRPDExtInformation->ucSyncState;
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.iPhaseIntervalCount = pPRPDExtInformation->iPhaseWindowCount;
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.iQuantificationAmp = pPRPDExtInformation->iQuantizedAmplitude;
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.iPowerFreCycleCount = pPRPDExtInformation->iPowerFreqCycleCount;
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.eAmpUnit = static_cast<DataFileNS::AmpUnit>(pPRPDExtInformation->eAmpUnit);
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.eBandWidth = static_cast<DataFileNS::MapBandWidth>(pPRPDExtInformation->eFrequencyBand);
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.sGain = pPRPDExtInformation->sGain;
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.eDataSign = static_cast<PRPSMapNS::MapDataSign>(pPRPDExtInformation->eDataJudgmentFlag);
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.fAmpLowerLimit = pPRPDExtInformation->fAmpLowerLimit;
    stHFCTPRPSPRPDDataInfo.stPRPDInfo.fAmpUpperLimit = pPRPDExtInformation->fAmpUpperLimit;
}




