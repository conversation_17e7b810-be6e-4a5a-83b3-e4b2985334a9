#include <QEvent>
#include <QKeyEvent>
#include <QHBoxLayout>
#include <QGraphicsView>
#include "linetemperaturecurve.h"
#include "linetemperaturecurvedialog.h"

const int LINE_ANALYSIS_CURVE_WIDTH = 480;
const int LINE_ANALYSIS_CURVE_HEIGHT = 350;
const int LINE_ANALYSIS_CURVE_OFFSET = 50;

/*************************************************
函数名： LineTemperatureCurveDialog(const QVector<float> &datas, QWidget *parent)
输入参数： datas：温度数据
          parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
LineTemperatureCurveDialog::LineTemperatureCurveDialog(const QVector<float> &datas, QWidget *parent)
    :QDialog(parent)
    ,m_pScene(NULL)
    ,m_datas(datas)
{
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    LineTemperatureCurve *pLineCurve = new LineTemperatureCurve(m_datas);
    pLineCurve->setTitle(trUtf8("Line Temperature"));
    pLineCurve->setFixedSize(LINE_ANALYSIS_CURVE_WIDTH, LINE_ANALYSIS_CURVE_HEIGHT);

    m_pScene = new QGraphicsScene(this);
    m_pScene->addWidget(pLineCurve);
    m_pScene->setFocus(Qt::MouseFocusReason);
    m_pScene->setSceneRect(QRect(0, 0, LINE_ANALYSIS_CURVE_WIDTH, LINE_ANALYSIS_CURVE_HEIGHT));

    installEventFilter(this);

    QGraphicsView *pView = new QGraphicsView(m_pScene, this);
    //pView->setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    pView->setWindowFlags(Qt::FramelessWindowHint);
    pView->setContentsMargins(0, 0, 0, 0);
    pView->setFrameStyle(QFrame::NoFrame);
    pView->setFocusPolicy(Qt::NoFocus);
    pView->rotate(90);

    pView->move(LINE_ANALYSIS_CURVE_OFFSET, LINE_ANALYSIS_CURVE_OFFSET);
    pView->setFixedSize(LINE_ANALYSIS_CURVE_HEIGHT, LINE_ANALYSIS_CURVE_WIDTH);
    pLineCurve->setDistinguishRect(QRect(LINE_ANALYSIS_CURVE_OFFSET, LINE_ANALYSIS_CURVE_OFFSET,
                                         LINE_ANALYSIS_CURVE_HEIGHT, LINE_ANALYSIS_CURVE_WIDTH));

    QHBoxLayout *mainLayout = new QHBoxLayout;
    mainLayout->addWidget(pView);
    setLayout(mainLayout);
}

/*************************************************
函数名： eventFilter(QObject *obj, QEvent *e)
输入参数： obj：事件发生的对象
          e：事件类型
输出参数： NULL
返回值： 操作结果，true - 直接过滤，false - 继续处理
功能： 事件过滤器
*************************************************************/
bool LineTemperatureCurveDialog::eventFilter(QObject *obj, QEvent *e)
{
    if(e->type() == QEvent::KeyPress)
    {
        QKeyEvent *pKeyEvent = static_cast<QKeyEvent *>(e);
        int iKey = pKeyEvent->key();
        if(Qt::Key_Escape == iKey)
        {
            close();
        }
        return true;
    }
    else if ( e->type() == QEvent::ZOrderChange )
    {
        if ( !rect().contains(mapFromGlobal(QCursor::pos())) )
        {
            close();
            return true;
        }
    }

    return QDialog::eventFilter(obj, e);
}
