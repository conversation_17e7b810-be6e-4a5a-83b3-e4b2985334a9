#include <QHBoxLayout>
#include <QDebug>
#include <QTimer>
#include <QApplication>
#include "datetimeedit.h"
//#ifdef _INCLUDE_DATAEXPORT_
#define STATUS_DATETIME_FORMAT "yyyy/MM/dd HH:mm:ss"   //日期时间格式

const QString DATETIMEEDIT_FOCUS_IN_STYLE = "DateTimeEdit{border-style:none;border-width:0px;border-radius:5px;border-color: rgb(207,207,207);}"
                                            "DateTimeEdit:focus{border-radius: 5px;border-width:2px; border-color: rgb( 115,198,242 );border-style: solid;}";
const QString DATETIMEEDIT_FOCUS_OUT_STYLE = "DateTimeEdit{border-style:none;border-width:0px;border-radius:5px;border-color: rgb(207,207,207);}";


/*******************************************/
//构造函数:
//输入参数：
//      datetime -- 显示的时间
/*******************************************/
DateTimeEdit::DateTimeEdit(const QDateTime& datetime, QFrame *parent)
    :QFrame( parent )
{
    m_iAutoChangeValueTimerId = -1;
    mbisFocused = true;
    m_ptimeEdit = new QDateTimeEdit(this);
    m_ptimeEdit->setDisplayFormat( STATUS_DATETIME_FORMAT );
    m_ptimeEdit->setProperty("buttonSymbols",QAbstractSpinBox::NoButtons);
    m_ptimeEdit->setDateTime(datetime);

    //由于此控件获得焦点时，里面的m_ptimeEdit的默认处于focus状态，所以此时stylesheet没有添加focus时的显示效果
    m_ptimeEdit->setStyleSheet("QDateTimeEdit{border-style:1px solid gray;border-width:1px;border-radius:10px;}");

    m_pbutSub = new QToolButton(this);
    m_pbutSub->setText("-");
    m_pbutSub->setFocusProxy(this);

    m_pbutAdd = new QToolButton(this);
    m_pbutAdd->setText("+");
    m_pbutAdd->setFocusProxy(this);

    QHBoxLayout* playoutWidget = new QHBoxLayout(this);
    playoutWidget->addWidget(m_pbutSub);
    playoutWidget->addWidget(m_ptimeEdit);
    playoutWidget->addWidget(m_pbutAdd);
    setLayout(playoutWidget);

    connect(m_pbutSub, SIGNAL(pressed()), this, SLOT(onSubBtnPressed()));
    connect(m_pbutSub, SIGNAL(released()), this, SLOT(onSubBtnReleased()));

    connect(m_pbutAdd, SIGNAL(pressed()), this, SLOT(onAddBtnPressed()));
    connect(m_pbutAdd, SIGNAL(released()), this, SLOT(onAddBtnReleased()));

}

/*******************************************/
//日期时间
/*******************************************/
const QDateTime DateTimeEdit::dateTime() const
{
    return m_ptimeEdit->dateTime();
}
/*******************************************/
//日期
/*******************************************/
const QDate DateTimeEdit::date() const
{
    return m_ptimeEdit->date();
}
/*******************************************/
//时间
/*******************************************/
const QTime DateTimeEdit::time() const
{
    return m_ptimeEdit->time();
}
/*******************************************/
//显示格式
//输入参数：
//      format -- 格式
/*******************************************/
void DateTimeEdit::setDisplayFormat(const QString& format)
{
    m_ptimeEdit->setDisplayFormat(format);
}
/*******************************************/
//设置时间
//输入参数：
//      dateTime -- 显示的时间
/*******************************************/
void DateTimeEdit::setDateTime( const QDateTime& dateTime )
{
    m_ptimeEdit->setDateTime( dateTime );
}

/*******************************************/
//显示日期控件
//输入参数：
//      bPopup -- true:显示;false:隐藏
/*******************************************/
void DateTimeEdit::setCalendarPopup(bool bPopup)
{
    m_ptimeEdit->setCalendarPopup(bPopup);
    if( !bPopup )
    {
        m_ptimeEdit->setProperty("buttonSymbols",QAbstractSpinBox::NoButtons);
    }
}

/*******************************************/
//按下“-”键的操作
/*******************************************/
void DateTimeEdit::subTimeValue()
{
    QDateTime datetime = m_ptimeEdit->dateTime();
    switch( m_ptimeEdit->currentSection() )
    {
        case QDateTimeEdit::YearSection:
            datetime = datetime.addYears(-1);
        break;
        case QDateTimeEdit::MonthSection:
            datetime = datetime = datetime.addMonths(-1);
        break;
        case QDateTimeEdit::DaySection:
            datetime = datetime.addDays(-1);
        break;
        case QDateTimeEdit::HourSection:
            datetime = datetime.addSecs(-3600);
        break;
        case QDateTimeEdit::MinuteSection:
            datetime = datetime.addSecs(-60);
        break;
        case QDateTimeEdit::SecondSection:
            datetime = datetime.addSecs(-1);
        break;
        default:
        break;
    }
    m_ptimeEdit->setDateTime(datetime);
    emit sigDateTimeChanged();
}

/*******************************************/
//根据方向重新设定timeEdit的选中区域
/*******************************************/
void DateTimeEdit::timeSecectionChange(DateTimeEdit::Direction id)
{
    switch( m_ptimeEdit->currentSection() )
    {
        case QDateTimeEdit::YearSection:
            if(id == LEFT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::SecondSection));
            }
            else if(id == RIGHT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::MonthSection));
            }
        break;
        case QDateTimeEdit::MonthSection:
            if(id == LEFT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::YearSection));
            }
            else if(id == RIGHT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::DaySection));
            }
        break;
        case QDateTimeEdit::DaySection:
            if(id == LEFT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::MonthSection));
            }
            else if(id == RIGHT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::HourSection));
            }
        break;
        case QDateTimeEdit::HourSection:
            if(id == LEFT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::DaySection));
            }
            else if(id == RIGHT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::MinuteSection));
            }
        break;
        case QDateTimeEdit::MinuteSection:
            if(id == LEFT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::HourSection));
            }
            else if(id == RIGHT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::SecondSection));
            }
        break;
        case QDateTimeEdit::SecondSection:
            if(id == LEFT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::MinuteSection));
            }
            else if(id == RIGHT)
            {
                m_ptimeEdit->setSelectedSection(m_ptimeEdit->sectionAt(QDateTimeEdit::YearSection));
            }
        break;
        default:
        break;
    }
}
/*******************************************/
//限定时间范围
//输入参数：
//      min:最小时间;max:最大时间
/*******************************************/
void DateTimeEdit::setDateTimeRange(const QDateTime &min, const QDateTime &max)
{
    m_ptimeEdit->setDateTimeRange(min, max);
}

/*******************************************/
//限定时间范围
//输入参数： max:最大时间
/*******************************************/
void DateTimeEdit::setMaximumDateTime(const QDateTime &max)
{
    m_ptimeEdit->setMaximumDateTime(max);
}

/*******************************************/
//限定时间范围
//输入参数： min:最小时间;
/*******************************************/
void DateTimeEdit::setMinimumDateTime(const QDateTime &min)
{
    m_ptimeEdit->setMinimumDateTime(min);
}

/*******************************************/
//按下“+”键的操作
/*******************************************/
void DateTimeEdit::addTimeValue()
{
    QDateTime datetime = m_ptimeEdit->dateTime();
    switch( m_ptimeEdit->currentSection() )
    {
        case QDateTimeEdit::YearSection:
            datetime = datetime.addYears(1);
        break;
        case QDateTimeEdit::MonthSection:
            datetime = datetime.addMonths(1);
        break;
        case QDateTimeEdit::DaySection:
            datetime = datetime.addDays(1);
        break;
        case QDateTimeEdit::HourSection:
            datetime = datetime.addSecs(3600);
        break;
        case QDateTimeEdit::MinuteSection:
            datetime = datetime.addSecs(60);
        break;
        case QDateTimeEdit::SecondSection:
            datetime = datetime.addSecs(1);
        break;
        default:
        break;
    }   
    m_ptimeEdit->setDateTime(datetime);
    emit sigDateTimeChanged();
}
//#endif


/************************************************
 * 函数名   : focusInEvent
 * 输入参数 : pFocusEvent: 焦点事件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 获取焦点事件
 ************************************************/
void DateTimeEdit::focusInEvent(QFocusEvent *pFocusEvent)
{
    Q_UNUSED(pFocusEvent)
    mbisFocused = true;
    this->setStyleSheet(DATETIMEEDIT_FOCUS_IN_STYLE);
}

/************************************************
 * 函数名   : focusOutEvent
 * 输入参数 : pFocusEvent: 焦点事件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 焦点消失事件
 ************************************************/
void DateTimeEdit::focusOutEvent(QFocusEvent *pFocusEvent)
{
    Q_UNUSED(pFocusEvent)
    mbisFocused = false;
    //特别加上这行代码是因为将此控件的焦点委托给m_ptimeEdit时，可以清除此控件得到焦点时的边框显示
    this->setStyleSheet(DATETIMEEDIT_FOCUS_OUT_STYLE);
}
//************************************************
// * 函数名   : 键盘事件
// * 输入参数 :
// *      event -- 事件
// ************************************************/
 void DateTimeEdit::keyPressEvent( QKeyEvent *event )
 {
    //如果此控件未得到焦点且有确认键按下
    if(mbisFocused == true &&(( Qt::Key_Return == event->key() ) || ( Qt::Key_Enter == event->key() )))
    {
        //将此控件的焦点委托给m_ptimeEdit
        this->setFocusProxy(m_ptimeEdit);
        m_ptimeEdit->setFocus();
        //由于此控件获得焦点时，里面的m_ptimeEdit的默认处于focus状态，构造函数中stylesheet没有focus的显示效果，此处添加
        m_ptimeEdit->setStyleSheet("QDateTimeEdit{border-style:1px solid gray;border-width:1px;border-radius:10px;}"
                                  "QDateTimeEdit:focus{border:none; background: rgb( 115,198,242 );color: white;}");
        repaint();
    }
    else if((m_ptimeEdit->hasFocus() == true))
    {
        switch( event->key() )
        {
            case Qt::Key_Left:
           {
               timeSecectionChange(DateTimeEdit::LEFT);
           }
             case Qt::Key_Up:
            {
                addTimeValue();
            }
                break;
            case Qt::Key_Right:
            {
                timeSecectionChange(DateTimeEdit::RIGHT);
            }
             case Qt::Key_Down:
            {
                subTimeValue();
            }
                break;
            case Qt::Key_Escape:
            {
            //将此控件的焦点委托设为它本身
                this->setFocusProxy(0);
                this->setFocus();
                repaint();
            }
               break;
            case Qt::Key_Return:
            case Qt::Key_Enter:
            {
                timeSecectionChange(DateTimeEdit::RIGHT);
            }
                break;
            default:
                break;
        }

    }
    else
    {
        QFrame::keyPressEvent( event );
    }

 }

 /*****************************************************************
  * 函数名  ： onAddBtnPressed(void)
  * 输入参数 ：NULL
  * 输出参数 ：NULL
  * 返回值   ：NULL
  * 功能     ：+按钮按下处理
  ****************************************************************/
 void DateTimeEdit::onAddBtnPressed()
 {
     m_isPressAddBtn = true;
     m_isPressSubBtn = false;
     if(m_iAutoChangeValueTimerId == INVALID_TIMER_ID)
     {
         m_iAutoChangeValueTimerId = startTimer(AUTO_CHANGE_VALUE_TIMER_200_MS);
     }
     else
     {
         killTimer(m_iAutoChangeValueTimerId);
         m_iAutoChangeValueTimerId = startTimer(AUTO_CHANGE_VALUE_TIMER_200_MS);
     }
 }

 /*****************************************************************
  * 函数名  ： onSubBtnPressed(void)
  * 输入参数 ：NULL
  * 输出参数 ：NULL
  * 返回值   ：NULL
  * 功能     ：-按钮按下处理
  ****************************************************************/
 void DateTimeEdit::onSubBtnPressed(void)
 {
     m_isPressAddBtn = false;
     m_isPressSubBtn = true;
     if(m_iAutoChangeValueTimerId == INVALID_TIMER_ID)
     {
         m_iAutoChangeValueTimerId = startTimer(AUTO_CHANGE_VALUE_TIMER_200_MS);
     }
     else
     {
         killTimer(m_iAutoChangeValueTimerId);
         m_iAutoChangeValueTimerId = startTimer(AUTO_CHANGE_VALUE_TIMER_200_MS);
     }
 }
 /*****************************************************************
  * 函数名  ： onAddBtnReleased(void)
  * 输入参数 ：NULL
  * 输出参数 ：NULL
  * 返回值   ：NULL
  * 功能     ：+按钮松开处理
  ****************************************************************/
 void DateTimeEdit::onAddBtnReleased(void)
 {
     m_isPressAddBtn = false;
     m_isPressSubBtn = false;
     if(m_iAutoChangeValueTimerId != INVALID_TIMER_ID)
     {
         killTimer(m_iAutoChangeValueTimerId);
         m_iAutoChangeValueTimerId = startTimer(AUTO_CHANGE_VALUE_TIMER_200_MS);
     }
     addTimeValue();
 }

 /*****************************************************************
  * 函数名  ： onSubBtnReleased(void)
  * 输入参数 ：NULL
  * 输出参数 ：NULL
  * 返回值   ：NULL
  * 功能     ：-按钮松开处理
  ****************************************************************/
 void DateTimeEdit::onSubBtnReleased()
 {
     m_isPressAddBtn = false;
     m_isPressSubBtn = false;
     if(m_iAutoChangeValueTimerId != INVALID_TIMER_ID)
     {
         killTimer(m_iAutoChangeValueTimerId);
         m_iAutoChangeValueTimerId = INVALID_TIMER_ID;
     }
     subTimeValue();
 }

 /*****************************************************************
  * 函数名  ： timerEvent(QTimerEvent * pEvent)
  * 输入参数 ：QTimerEvent指针
  * 输出参数 ：NULL
  * 返回值   ：NULL
  * 功能     ：定时器处理函数
  ****************************************************************/
 void DateTimeEdit::timerEvent(QTimerEvent * pEvent)
 {
     if(pEvent->timerId() == m_iAutoChangeValueTimerId)
     {
         if(m_isPressAddBtn)
         {
             addTimeValue();
         }
         else if(m_isPressSubBtn)
         {
             subTimeValue();
         }
         else
         {

         }
     }

 }
