/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* HFCTampplaybackview.h
*
* 初始版本：1.0
* 作者：洪澄
* 创建日期：2019年05月15日
* 摘要：HFCTAmpPlaybackView模块接口定义

* 当前版本：1.0
*/

#ifndef HFCTAMPPLAYBACKVIEW_H
#define HFCTAMPPLAYBACKVIEW_H

#include "playbackView/PlayBackBase.h"
#include "hfct/dataSave/HFCTAmpDataMapSave.h"
#include "widgets/histogram/HistogramChart.h"


class HFCTAmpPlaybackView : public PlayBackBase
{
    Q_OBJECT
public:
    /*************************************************
    函数名： HFCTAmpPlaybackView(QWidget *parent = 0)
    输入参数： parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit HFCTAmpPlaybackView(QWidget *parent = 0);


private:
    /*************************************************
    函数名： playbackFile(const QString &strFileName)
    输入参数： strFileName：回放文件名
    输出参数： NULL
    返回值： NULL
    功能： 设置回放文件
    *************************************************************/
    void playbackFile(const QString &strFileName);

private:
    HistogramChart *m_pChart; // 图谱部分
};

#endif // HFCTAMPPLAYBACKVIEW_H
