#include "subtaskview.h"
#include <QTimer>
#include "messageBox/msgbox.h"
#include "window/Window.h"
#include "mobileaccessservice.h"
#include "PDAUi/PDAUiBean/pdaprogressdialog.h"
#include "commonviewconfig.h"
#include "connectview/connectview.h"
#include "intervalview.h"
#include "datadefine.h"
#include <QtConcurrentRun>

#define SINGLE_TASK     1
#define DEFAULT_DELAY   300
#define MAX_SYNC_NUM    10

typedef enum _TaskButton_
{
    BUTTON_OPEN = 0,// 打开
    BUTTON_COMMIT,// 提交
    BUTTON_DELETE,// 删除
}TaskButton;

//控制按钮定义
const ButtonInfo::Info g_OpenBtn = {BUTTON_OPEN, {ButtonInfo::COMMAND, CustomAccessView::TEXT_OPEN, NULL, "", NULL}};
const ButtonInfo::Info g_CommBtn = {BUTTON_COMMIT, {ButtonInfo::COMMAND, CustomAccessView::TEXT_COMMIT, NULL, "", NULL}};
const ButtonInfo::Info g_DelBtn = {BUTTON_DELETE, {ButtonInfo::COMMAND, CustomAccessView::TEXT_DELETE, NULL, "", NULL}};


void uploadSubTasks(QString strMainId, QStringList subIds)
{
    MobileAccessService::instance()->uploadSubTasks(strMainId, subIds);
    return;
}

/*************************************************
功能： 构造函数
输入参数:
    parent:父控件指针
*************************************************************/
SubTaskView::SubTaskView(const QString &title, CustomAccessTaskNS::MainTaskInfo mainInfo, QWidget *parent)
    : PDAListView(title,parent)
    , m_mainTaskInfo(mainInfo)
    , m_strCurrentSubTaskName("")
    , m_bIsUploading(false)
{
    setFixedSize( Window::WIDTH, Window::HEIGHT );

    m_eAccessMode = SystemSetService::instance()->getCustomAccessMode();
    m_eAccessProtocol = SystemSetService::instance()->getAccessProtocol();

    //添加任务信息
    initTaskItem();

    initBtnBarInfo();

    connect( TaskManager::instance(), SIGNAL(sigTaskChanged()), this, SLOT(onTaskChanged()) );
    connect(MobileAccessService::instance(), SIGNAL(sigUploadFinished()), this, SLOT(onUploadFinished()));
}

/*************************************************
功能： 析构函数
*************************************************************/
SubTaskView::~SubTaskView()
{
    if(m_pBtnInfo)
    {
        delete [] m_pBtnInfo;
        m_pBtnInfo = NULL;
    }
}

void SubTaskView::onTaskChanged()
{
    initTaskItem();
}

void SubTaskView::initTaskItem()
{
    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("The disk is occupied, please disconnect mobilephone or computer!"));
        return;
    }

    QVector<qint32> tickIndexes = m_pChart->itemsIndexTicked();
    m_pChart->deleteAllItem();
    //添加任务信息
    QList<PDAListChart::ListItemInfo> itemInfos;
    m_subTasks = TaskManager::instance()->subTasks( m_mainTaskInfo.s_strId );
    int iTested = 0;
    bool bTicked = false;

    for(int i = 0, iSize = m_subTasks.size(); i < iSize; ++i)
    {
        bTicked = tickIndexes.contains(i);
        iTested = (CustomAccessTaskNS::TASK_TEST == m_subTasks.at(i).s_eTestState) ? 1 : 0;

        if (SystemSet::ACCESS_PROTO_JSDKY == m_eAccessProtocol)
        {
            //  江苏电科院要根据诊断结果显示文字是否红色
            PDAListChart::ListItemInfo stListItemInfo(m_subTasks.at(i).s_strName, -1, iTested, bTicked);
            stListItemInfo.bDiagResult = TaskManager::instance()->getSubTaskDiagState(m_mainTaskInfo.s_strId, m_subTasks.at(i).s_strId) == CustomAccessTaskNS::DiagNormal ? 0 : 1;
            itemInfos << stListItemInfo;
        }
        else
        {
            if(SystemSet::ACCESS_USB_MODE == m_eAccessMode)
            {
                itemInfos << PDAListChart::ListItemInfo(m_subTasks.at(i).s_strName, -1, iTested, bTicked);
            }
            else
            {
                itemInfos << PDAListChart::ListItemInfo(m_subTasks.at(i).s_strName, -1, iTested, bTicked, PDAListItem::CHECK_BOX);
            }
        }
    }

    m_pChart->addItems( itemInfos );
    if(m_pChart->allItems().size() > 0)
    {
        m_pChart->setCurrentItemSelected(0);
    }

    return;
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void SubTaskView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case BUTTON_OPEN:
    {
        int iSelectItems = m_pChart->itemsIndexSelected().size();
        if(iSelectItems == SINGLE_TASK)                        // 是否选中单个任务，仅允许打开单个
        {
            int iSelectedIndex = m_pChart->itemIndexSelected();
            if(iSelectedIndex != PDAListChart::ERROR_SELECTED_INDEX)  // 是否有条目被选中
            {
                onItemClicked(iSelectedIndex);
            }
            else
            {
                logWarning("no item select");
            }
        }
        else if(iSelectItems > SINGLE_TASK)
        {
            MsgBox::warning("", QObject::trUtf8("Unable to open multiple task files."));
        }
        else
        {
            MsgBox::warning("", QObject::trUtf8("No task file has been chosen."));
        }

        break;
    }
    case BUTTON_COMMIT:
    {
        //1.判断是否选中条目
        QVector<qint32> vIndexs = m_pChart->itemsIndexTicked();
        if( vIndexs.isEmpty() )
        {
            MsgBox::warning( "",QObject::trUtf8("Confirm to select a task.") );
        }
        else
        {
            //判断连接是否存在
            if( MobileAccessService::instance()->isAppConnectExist() )
            {
                //upload is busy,do nothing
                if( m_bIsUploading )
                {
                    return;
                }

                QStringList subIdList;
                for( int i = 0; i < vIndexs.size(); i++ )
                {
                    if(m_subTasks.at( vIndexs.at(i) ).s_bUploadable)
                    {
                        subIdList.append( m_subTasks.at( vIndexs.at(i) ).s_strId );
                    }
                }

                if(subIdList.isEmpty())
                {
                    //TODO
                    MsgBox::warning( "", QObject::trUtf8("Not support to upload empty data or only background data!") );
                    return;
                }

                m_bIsUploading = true;

                //绑定进度显示
                PDAProgressDialog *pDialog = new PDAProgressDialog(false);
                pDialog->setWindowModality(Qt::ApplicationModal);
                pDialog->setWindowFlags(Qt::WindowStaysOnTopHint | Qt::FramelessWindowHint);
                connect(MobileAccessService::instance(), SIGNAL(sigUploadProgress(int)), pDialog, SLOT(onProgressChanged(int)));
                connect(MobileAccessService::instance(), SIGNAL(sigUploadFinished()), pDialog, SLOT(onUploadFinished()));

                pDialog->show();
                Module::mSleep(500);//避免进度条闪现
                //提交选中任务
                if( subIdList.size() < MAX_SYNC_NUM )
                {
                    MobileAccessService::instance()->uploadSubTasks( m_mainTaskInfo.s_strId, subIdList );
                }
                else
                {
                    QtConcurrent::run( uploadSubTasks, m_mainTaskInfo.s_strId, subIdList );
                }
            }
            else {
                ConnectView* pView = new ConnectView;
                pView->show();
            }
        }
    }
        break;
    case BUTTON_DELETE:
    {
        int iSelectSize = 0;
        if(SystemSet::ACCESS_USB_MODE == m_eAccessMode || SystemSet::ACCESS_PROTO_JSDKY == m_eAccessProtocol) // 江苏电科院特殊处理(TODO:以后不应该根据这个来判断，而是根据列表显示状态是否有复选框来)
        {
            iSelectSize = m_pChart->itemsIndexSelected().size();
        }
        else
        {
            iSelectSize = m_pChart->itemsIndexTicked().size();
        }

        if(0 == iSelectSize)
        {
            MsgBox::warning("", QObject::trUtf8("Please choose a task!"));
            return;
        }

        // 1.确认操作
        if( MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to delete the item?")) )
        {
            QVector<qint32> vIndex;
            vIndex.clear();

            if(SystemSet::ACCESS_USB_MODE == m_eAccessMode || SystemSet::ACCESS_PROTO_JSDKY == m_eAccessProtocol)
            {
                vIndex = m_pChart->itemsIndexSelected();
            }
            else
            {
                vIndex = m_pChart->itemsIndexTicked();
            }

            // 2.调用界面组件删除
            //m_pChart->deleteItems(vIndex);

            // 3.调用后台接口清除相关任务
            QVector<CustomAccessTaskNS::SubTaskInfo> taskInfos = m_subTasks;
            QStringList subTaskIds;
            for(int i = 0, iSize = vIndex.size(); i < iSize; ++i)
            {
                qint32 iIndex = vIndex.at(i);
                subTaskIds.append( taskInfos.at(iIndex).s_strId );
            }

            if( !subTaskIds.isEmpty() )
            {
                TaskManager::instance()->deleteSubTasks( m_mainTaskInfo.s_strId, subTaskIds );
            }

//            if(m_pChart->allItems().size() > 0)
//            {
//                m_pChart->setCurrentItemSelected(0);
//            }
        }
        else
        {

        }

    }
        break;
    default:
        break;
    }
}

/*************************************************
功能： 槽，响应******
*************************************************************/
void SubTaskView::onUploadFinished()
{
    m_bIsUploading = false;
}

/*************************************************
功能： 处理条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void SubTaskView::onItemClicked( int id )
{
    /*
     * 1.根据id，找到选择的条目信息，获得标题在内的title信息
     * 2.构造函数添加，方便能够获取测点列表
     */
    if( id < m_subTasks.size() )
    {
        //临时修改，目的是为了解决子任务菜单任务名过长的问题
        //        TestPointView *pView = new TestPointView( m_subTasks.at(id).s_strName,
        //                                                      m_mainTaskInfo.s_strId,
        //                                                      m_subTasks.at(id).s_strId);
        m_strCurrentSubTaskName = m_subTasks.at(id).s_strName;
        IntervalView *pView = new IntervalView( m_mainTaskInfo.s_strId,
                                                m_subTasks.at(id).s_strId);
        connect( pView,SIGNAL(sigClosed()),this,SLOT(onTestPointViewClosed()) );
        pView->show();
    }
}

/*************************************************
功能： 测点列表关闭
*************************************************************/
void SubTaskView::onTestPointViewClosed( void )
{
    QList<PDAListChart::ListItemInfo> itemInfos = m_pChart->allItems();
    m_subTasks = TaskManager::instance()->subTasks( m_mainTaskInfo.s_strId );
    if( itemInfos.size() != m_subTasks.size() )
    {
        return;
    }

    for( int i = 0; i < itemInfos.size(); i++ )
    {
        PDAListChart::ListItemInfo tmpInfo = itemInfos.at(i);
        if( tmpInfo.m_strItemName == m_strCurrentSubTaskName )
        {
            for( int iSub = 0; iSub < m_subTasks.size(); iSub++ )
            {
                if( m_subTasks.at(iSub).s_strName == m_strCurrentSubTaskName )
                {
                    qDebug() << "SubTaskView::onTestPointViewClosed, strId: " << m_subTasks.at(iSub).s_strId <<
                                ", test state: " << m_subTasks.at(iSub).s_eTestState << endl;
                    tmpInfo.m_iTotalCount = (m_subTasks.at(iSub).s_eTestState == CustomAccessTaskNS::TASK_TEST) ? 1 : 0;
                    m_pChart->setItemInfo( tmpInfo, i );
                    break;
                }
            }
            break;
        }
    }

    m_strCurrentSubTaskName = "";
}

/*****************************************
 * 功能：初始化按钮栏信息
 * **********************************************/
void SubTaskView::initBtnBarInfo()
{
    m_pBtnInfo = NULL;
    m_qui8BtnCnt = 0;

    QVector<ButtonInfo::Info> qvtBtnInfos;
    qvtBtnInfos.clear();

    qvtBtnInfos.push_back(g_OpenBtn);
    if (SystemSet::ACCESS_PROTO_JSDKY != m_eAccessProtocol)
    {
        if(SystemSet::ACCESS_USB_MODE != m_eAccessMode)
        {
            qvtBtnInfos.push_back(g_CommBtn);
        }
    }
    qvtBtnInfos.push_back(g_DelBtn);

    m_qui8BtnCnt = static_cast<quint8>(qvtBtnInfos.size());
    m_pBtnInfo = new ButtonInfo::Info[m_qui8BtnCnt];
    for (int i = 0; i < m_qui8BtnCnt; ++i)
    {
        m_pBtnInfo[i] = qvtBtnInfos[i];
    }

    //创建按钮栏
    createButtonBar(CustomAccessView::CONTEXT, m_pBtnInfo, m_qui8BtnCnt);

    return;
}
