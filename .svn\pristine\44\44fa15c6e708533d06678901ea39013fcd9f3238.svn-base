/*
* Copyright (c) 2016.05, 华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: autoMatker.h
*
* 初始版本: 1.0
* 作者: 王谦
* 创建日期: 2016年05月26日
* 摘要: 该文件定义了百兆采集接入G100功能，标记线
*/

#ifndef AUTOMATKER_H
#define AUTOMATKER_H

#include <QWidget>
#include <thirdparty/qwt/qwt_plot.h>
#include <thirdparty/qwt/qwt_plot_marker.h>

class autoMatker : public QwtPlotMarker
{
    //Q_OBJECT
public:
    /************************************************
     * 函数名    :autoMatker
     * 输入参数  ：max -- 最大值
     *            min -- 最小值
     *            eLineStyle -- 水平或竖直
     *            title -- 标题
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：构造函数
     ************************************************/
    explicit autoMatker( double max,
                         double min,
                         const LineStyle& eLineStyle = QwtPlotMarker::VLine ,
                         const QString &title=QString::null);

    /************************************************
     * 函数名    :setMarkerValue
     * 输入参数  ：value -- 最大值
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置标记线的值
     ************************************************/
    void setMarkerValue( double value );

    /************************************************
     * 函数名    :markerValue
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：double：标记线对应值
     * 功能     ：获得当前标记线对应的数值
     ************************************************/
    double markerValue( void );

    /************************************************
     * 函数名    :setMarkerScale
     * 输入参数  ：max：最大值
     *           min：最小值
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置标记线范围
     ************************************************/
    void setMarkerScale( double min,double max );

    /************************************************
     * 函数名    :press
     * 输入参数  ：value：点击的值
     * 输出参数  ：NULL
     * 返回值   ：bool：是否选中标记线的标志
     * 功能     ：判断是否选中标记线
     ************************************************/
    bool press( double value );

    /************************************************
     * 函数名    :move
     * 输入参数  ：value：移动的值
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：移动标记线
     ************************************************/
    void move( double value );

    /************************************************
     * 函数名    :isMarkerValid
     * 输入参数  ：rect -- 新的画布矩形显示范围
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：判断新的画布显示范围内标记线是否可见
     ************************************************/
    bool isMarkerValid( const QRectF &rect );

    /************************************************
     * 函数名    :isMarkerValid
     * 输入参数  ：value：标记线的数值
     * 输出参数  ：NULL
     * 返回值   ：bool：value是否在标记线当前显示范围内的标志
     * 功能     ：判断value是否在标记线当前显示范围内
     ************************************************/
    bool isMarkerValid( double value );

private:
    LineStyle m_eLineStyle; // 标记线的排布方式，水平/竖直

    double m_dMaxValue; // 标记线范围最大值
    double m_dMinValue; // 标记线范围最小值
    double m_dMarkerValue; // 标记线当前值
};

#endif // AUTOMATKER_H
