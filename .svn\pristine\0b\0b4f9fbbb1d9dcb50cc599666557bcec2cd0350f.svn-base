/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* PushSliderPopup.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月10日
* 修改日期：2016年11月15日
*       重构
* 摘要：滑块式弹出式窗体

* 当前版本：1.0
*/
#ifndef PUSHSLIDERPOPUP_H
#define PUSHSLIDERPOPUP_H
#include "SliderPopup.h"

class PushSliderPopup : public SliderPopup
{
    Q_OBJECT
public:
    /*****************************************************************
     * 功能     ：构造函数，初始化滑块类
     * 输入参数 ：
     *      parent -- 父控件
     ****************************************************************/
    PushSliderPopup( QWidget *parent = NULL );
protected:
    /*************************************************
    功能： 绘制事件
    输入参数:
        event -- 事件
    *************************************************************/
    void paintEvent(QPaintEvent *event);
private:
    QSlider *m_pPushSliderPopup;//滑块
    QLabel *m_plabelValue;//当前值标签
};

#endif // SLIDER_H
