/*
* Copyright (c) 2017.1，南京华乘电气科技有限公司
* All rights reserved.
*
* CAConfig.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年1月5日
* 摘要：CA模块的配置定义
* 当前版本：1.0
*/
#ifndef CACONFIG_H
#define CACONFIG_H
#include "config/ConfigManager.h"
#include "CA.h"
#include "Module.h"

namespace CAConfig
{
//配置信息定义
typedef enum _ConfigInfo
{
    //键值
    KEY_SAMPLE_RATE = 0,//采样率 0
    KEY_GAIN,//增益 1
    KEY_BAND_WIDTH,//带宽 2
    KEY_DEVICE_MAC,//前端mac地址 3

    //KEY_WAVE_SAMPLE_TIME,//采样时间 4
    KEY_WAVE_SAMPLE_LEN,//采样长度 4
    KEY_WAVE_SAMPLE_TIMES,//波形采样次数 5
    KEY_WAVE_SAMPLE_INTERVAL, //波形采集间隔 6
    //KEY_WAVE_AMP_RANGE,//波行幅值量程

    KEY_PRPS_THREASHOLD,//阈值 7
    //KEY_PRPS_AMP_RANGE,//PRPS幅值量程
    KEY_PRPS_PHASE_ALIAS,//prps 相位偏移 8
    KEY_PRPS_PRPD_ACCUM,//PRPD累积 9
    KEY_PRPS_RECORD_TIME, //录屏时间

    KEY_PULSE_TRIGWIDTH,//触发宽度 10
    KEY_PULSE_BEFORE_TRIG,//触发前采样长度百分比 11
    KEY_PULSE_TRIG_AMP,//触发幅值 12
    //KEY_PULSE_AMP_RANGE,//脉冲幅值量程
    KEY_PULSE_ACCU_COUNT, //脉冲累积个数 13
    KEY_PULSE_PHASE_ALIAS,//pulse 相位偏移 14
}ConfigInfo;

//CA组的键值配置
static Config::KeyInfo KEYS_CA[] =
{
    //common
    { KEY_GAIN, "Gain", Config::NUMBER, QString::number(CA::GAIN_DEFAULT), CA::GAIN_MIN, CA::GAIN_MAX },
    { KEY_BAND_WIDTH, "BandWidth", Config::NUMBER, QString::number(CA::BAND_WIDTH_DEFAULT), CA::BAND_WIDTH_MIN, CA::BAND_WIDTH_MAX },
    { KEY_SAMPLE_RATE, "SampleRate", Config::NUMBER, QString::number(CA::SAMPLE_RATE_DEFAULT), CA::SAMPLE_RATE_MIN, CA::SAMPLE_RATE_MAX },
    { KEY_DEVICE_MAC, "DeviceMac", Config::TEXT, "", 0, 0 },

    //wave
    //{ KEY_WAVE_SAMPLE_TIME, "WaveSampleTime", Config::NUMBER, QString::number(CA::SAMPLE_TIME_DEFAULT), CA::SAMPLE_TIME_MIN, CA::SAMPLE_TIME_MAX },
    { KEY_WAVE_SAMPLE_LEN, "WaveSampleLength", Config::NUMBER, QString::number(CA::SAMPLE_LENGTH_DEFAULT), CA::SAMPLE_LENGTH_MIN, CA::SAMPLE_LENGTH_MAX },
    { KEY_WAVE_SAMPLE_TIMES, "WaveSampleTimes", Config::NUMBER, QString::number(CA::WAVE_SAMPLE_TIMES_DEFAULT), CA::WAVE_SAMPLE_TIMES_MIN, CA::WAVE_SAMPLE_TIMES_MAX },
    { KEY_WAVE_SAMPLE_INTERVAL, "WaveSampleInterval", Config::NUMBER, QString::number(CA::WAVE_SAMPLE_INTERVAL_DEFAULT), CA::WAVE_SAMPLE_INTERVAL_MIN, CA::WAVE_SAMPLE_INTERVAL_MAX },
    //{ KEY_WAVE_AMP_RANGE, "WaveAmpRange", Config::NUMBER, QString::number(CA::RANGE_MAX_DEFAULT), CA::RANGE_MAX_MIN, CA::RANGE_MAX_MAX },

    //pulse
    { KEY_PULSE_TRIG_AMP, "TriggerAmp", Config::NUMBER, QString::number(20), 0, 20000 },
    { KEY_PULSE_BEFORE_TRIG, "BeforeTrigger", Config::NUMBER, QString::number(5), 5, 50 },
    { KEY_PULSE_TRIGWIDTH, "TriggerWidth", Config::NUMBER, QString::number(CA::TRIGGER_WIDTH_DEFAULT), CA::TRIGGER_WIDTH_MIN, CA::TRIGGER_WIDTH_MAX },
    { KEY_PULSE_ACCU_COUNT, "PulseAccuCount", Config::NUMBER, QString::number(CA::PULSE_ACCU_COUNT_DEFAULT), CA::PULSE_ACCU_COUNT_MIN, CA::PULSE_ACCU_COUNT_MAX },
    //{ KEY_PULSE_AMP_RANGE, "PulseAmpRange", Config::NUMBER, QString::number(CA::RANGE_MAX_DEFAULT), CA::RANGE_MAX_MIN, CA::RANGE_MAX_MAX },
    { KEY_PULSE_PHASE_ALIAS, "PulsePhaseAlias", Config::NUMBER, QString::number(0), 0, 360 },

    //prps
    { KEY_PRPS_THREASHOLD, "Threashold", Config::NUMBER, QString::number(20), 0, 20000 },
    //{ KEY_PRPS_AMP_RANGE, "PrpsAmpRange", Config::NUMBER, QString::number(CA::RANGE_MAX_DEFAULT), CA::RANGE_MAX_MIN, CA::RANGE_MAX_MAX },
    { KEY_PRPS_PRPD_ACCUM, "PrpdAccum", Config::NUMBER, QString::number(1), 0, 1 },
    { KEY_PRPS_RECORD_TIME, "RecordTime", Config::NUMBER, QString::number(Module::ScreenRecordTime_DEFAULT), Module::ScreenRecordTime_MIN, Module::ScreenRecordTime_MAX },
    { KEY_PRPS_PHASE_ALIAS, "PRPSPhaseAlias", Config::NUMBER, QString::number(0), 0, 360 }
};

static Config::GroupInfo CONFIG =
{
    Module::GROUP_CA, Config::NORMAL, "CA", KEYS_CA, sizeof( KEYS_CA )/sizeof(Config::KeyInfo), NULL, 0
};

}

#endif // CACONFIG_H

