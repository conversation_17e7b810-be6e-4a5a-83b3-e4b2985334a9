#include "uhfprpsdistributenetaccesstestview.h"
#include "window/Window.h"
#include "uhf/UHFViewConfig.h"
#include "uhf/UHFConfig.h"
#include "controlButton/PopupButton.h"
#include "controlButton/RadioButton.h"
#include "messageBox/msgbox.h"
#include "appconfig.h"
#include "View.h"
#include "dataSave/DataFileInfos.h"
#include "pda/pdaservice.h"
#include "PDAUi/PDAUiView/pdaloginview.h"
#include "rfid/RFIDScanView.h"
#include "rfid/RFIDReadView.h"
#include "globalerrprocess.h"
#include "systemsetting/shutdownpower.h"
#include "systemsetting/systemsetservice.h"
//data save
#include "equipmentinfo.h"
#include "prps/prpsmapdefine.h"
#include "prps/prpddatamap.h"
#include "prps/prpsdatamap.h"
#include "mapdatafactory.h"
#include "customaccessUi/commonviewconfig.h"
#include "customaccessUi/commonitemview/commonitemlistview.h"
#include "diagnosismgr/diagnosismanager.h"
#include "dataspecification/dataspecification.h"
#include "dataspecification/prps/prpsspectrum.h"
#include "dataspecification/prps/prpdspectrum.h"
#include "customaccessUi/customaccessui_func.h"
#include "global_log.h"
#include "appfontmanager/appfontmanager.h"
#include "fileoper/fileoperutil.h"
#include "distributenetaccess/distributetaskmanager/distributetaskmanager.h"


using namespace errorProcess;

typedef enum _UHFPrpsButton_
{
    BUTTON_UHF_SAMPLE = 0,//采样
    BUTTON_UHF_SAVE_DATA,//保存数据
    BUTTON_UHF_LOAD_DATA,//载入数据
    BUTTON_MENU,//配置

    //more
    BUTTON_UHF_FORWARD_GAIN, //前置增益
    BUTTON_UHF_PHASE_SHIFT,//相位偏移
    BUTTON_IS_ACCUMULATION,  //是否使用累积
    BUTTON_UHF_BAND_WIDTH,//带宽
    BUTTON_UHF_SYNC_SOURCE,//同步方式
    BUTTON_UHF_DELETE_DATA,//删除数据
}UHFPrpsButton;

//相位偏移
const ButtonInfo::SliderValueConfig s_UHFPhaseShift =
{
    UHF::PHASE_MIN, UHF::PHASE_MAX, UHF::PHASE_STEP
};

//前置增益
const ButtonInfo::RadioValueConfig s_UHFForwardGainCfg =
{
    UHF::FORWARD_GAIN_ENABLE_OPTION, sizeof(UHF::FORWARD_GAIN_ENABLE_OPTION)/sizeof(char*)
};

//同步源
const ButtonInfo::RadioValueConfig s_UHFSyncCfg =
{
    UHF::TEXT_SYNC_OPTIONS, sizeof(UHF::TEXT_SYNC_OPTIONS)/sizeof(char*)
};

//带宽
const ButtonInfo::RadioValueConfig s_UHFBandWidthCfg =
{
    UHF::BAND_WIDTH_OPTION, UHF::BAND_WIDTH_COUNT
};

//是否累积
const ButtonInfo::RadioValueConfig s_IsAccumulationCfg =
{
    UHF::TEXT_OFF_ON_OPITIONS, sizeof(UHF::TEXT_OFF_ON_OPITIONS)/sizeof(char*)
};

//控制按钮定义
const ButtonInfo::Info s_UHFButtonInfo[] =
{
    { BUTTON_UHF_SAMPLE, { ButtonInfo::COMMAND, UHF::TEXT_SAMPLE, NULL, ":/images/sampleControl/sample.png",NULL } },//采样
    { BUTTON_UHF_SAVE_DATA, { ButtonInfo::COMMAND, UHF::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL } },//保存数据
    { BUTTON_UHF_LOAD_DATA, { ButtonInfo::COMMAND, UHF::TEXT_LOAD_DATA, NULL, ":/images/sampleControl/importData.png",NULL } },//载入数据
    { BUTTON_MENU, { ButtonInfo::COMMAND, UHF::TEXT_MORE_CONFIG, NULL, NULL, NULL } },//配置
};

//"更多.."按钮定义
const ButtonInfo::Info s_UHFButtonInfoMore[] =
{
    { BUTTON_UHF_FORWARD_GAIN, { ButtonInfo::RADIO, UHF::TEXT_FORWARD_GAIN, NULL, ":/images/sampleControl/forwardGain.png", &s_UHFForwardGainCfg } },//前置增益
    { BUTTON_UHF_PHASE_SHIFT, { ButtonInfo::FIXED_STEP_SLIDER, UHF::TEXT_PHASE_ALIAS, UHF::TEXT_DEGREE, ":/images/sampleControl/phaseShift.png", &s_UHFPhaseShift } },//相位偏移
    { BUTTON_IS_ACCUMULATION, { ButtonInfo::RADIO, UHF::TEXT_USING_ACCUMULATION, NULL, ":/images/sampleControl/brandWidth.png", &s_IsAccumulationCfg } },//是否累积
    { BUTTON_UHF_BAND_WIDTH, { ButtonInfo::RADIO, UHF::TEXT_BAND_WIDTH, NULL, ":/images/sampleControl/brandWidth.png", &s_UHFBandWidthCfg } },//带宽
    { BUTTON_UHF_SYNC_SOURCE, { ButtonInfo::RADIO, UHF::TEXT_SYNC_SOURCE, NULL, ":/images/sampleControl/syncMode.png", &s_UHFSyncCfg } },//同步方式
    { BUTTON_UHF_DELETE_DATA, { ButtonInfo::COMMAND, UHF::TEXT_DELETE_DATA, NULL, ":/images/sampleControl/delete.png", NULL } },//删除数据
};

const int MS_PER_MIN = 60 * 1000;  //每分钟对应的ms
const int MOVE_PERIODS_STEP = 1; //prps每次推进的周期数

/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
UHFPRPSDistributeNetTestView::UHFPRPSDistributeNetTestView(const QString &strTitle, QWidget *parent) :
    UhfPRPSViewBase( strTitle,parent )
{
    registerMaps();

    initData();

    ChartWidget *pWidget = createChart(parent);
    setChart(pWidget);

    PushButtonBar *pButtonBar  = createButtonbar(parent);
    m_pSampleBtn = pButtonBar->button(BUTTON_UHF_SAMPLE);

    m_pMoreConfigView = createMoreButtonBar(pButtonBar);

    setButtonBarDatas();

    setChartParameters();

    setAllWorkSets();

    QList<QString> lStrFileNames;
    for( quint16 i=0; i< m_vecTestData.size(); i++)
    {
        lStrFileNames.append( m_vecTestData.at(i).s_strFileName);
    }
    m_pCommonItemListView = new CommonItemListView(QObject::trUtf8("Test Data List"),lStrFileNames);
    m_pCommonItemListView->hide();
    connect( m_pCommonItemListView, SIGNAL(sigItemSelected( qint32)), this,SLOT(onItemActivated( qint32)));

    startSample();
    m_eState = STATE_SAMPLING;
    setSampleBtnText(m_eState);
    m_bPlayBacked = false;

    if( titleBar() != NULL )
    {
        disconnect(titleBar(), SIGNAL(sigClicked()), this, SLOT(close()));
        connect(titleBar(), SIGNAL(sigClicked()), this, SLOT(onTitleBarClicked()));
    }
}

/*************************************************
功能： 析构
*************************************************************/
UHFPRPSDistributeNetTestView::~UHFPRPSDistributeNetTestView( )
{
    saveConfig(); // 存储到配置文件中

    if(NULL != m_pCommonItemListView)
    {
        delete m_pCommonItemListView;
        m_pCommonItemListView = NULL;
    }
}

void UHFPRPSDistributeNetTestView::setTestInfo(const QString &qstrTaskId, const DistributeNetAccessNS::TestpointInfo &stTestpointInfo, const DistributeNetAccessNS::RealAssetInfo &stRealAssetInfo)
{
    m_qstrTaskId = qstrTaskId;
    m_stTestpointInfo = stTestpointInfo;
    m_stRealAssetInfo = stRealAssetInfo;

    m_pBayNameLabel->setText(QObject::trUtf8("Bay Name: ") + stRealAssetInfo.qstrName );
    m_pTestPointNameLabel->setText(QObject::trUtf8("Test Point Name: ") + stTestpointInfo.qstrName );
}

void UHFPRPSDistributeNetTestView::registerMaps()
{
    MapDataFactory::registerClass<PRPSDataMap>(XML_FILE_NODE_PRPS);//图谱根节点tag名
    MapDataFactory::registerClass<PRPDDataMap>(XML_FILE_NODE_PRPD);//图谱根节点tag名
}
/************************************************
 * 函数名   : onSignalChanged
 * 输入参数 : eState: 信号状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 槽函数，信号状态的改变
 ************************************************/
void UHFPRPSDistributeNetTestView::onSignalChanged(Module::SignalState eState)
{
    if(eState == Module::SIGNAL_STATE_EXIST)
    {
        m_pChart->setConnected(true);
    }
    else
    {
        m_pChart->setConnected(false);
    }
}

/************************************************
 * 函数名   : onSyncStateChanged
 * 输入参数 : eState: 同步状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 槽函数，同步状态的改变
 ************************************************/
void UHFPRPSDistributeNetTestView::onSyncStateChanged(Module::SyncState eState)
{
    if(m_eSyncState != eState)
    {
        m_eSyncState = eState;
        m_pChart->setSync((PrpsGlobal::SyncSource)m_eSyncSource, (PrpsGlobal::SyncState)eState);
    }
    return;
}

/************************************************
 * 函数名   : setChartParameters
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 给图谱设置参数
 ************************************************/
void UHFPRPSDistributeNetTestView::setChartParameters()
{
    m_eDiagRet = CustomAccessTaskNS::DiagNormal;
    m_pChart->setSync( (PrpsGlobal::SyncSource) m_eSyncSource, (PrpsGlobal::SyncState) m_eSyncState );

    /*系统频率*/
    m_pChart->setPowerFreq( (PrpsGlobal::Frequency) m_ucSysFreq );

    /*运行状态*/
    if(m_eState == STATE_SAMPLING)
    {
        m_pChart->setRunningMode( true );
    }
    else
    {
        m_pChart->setRunningMode( false );
    }
    /*相位偏移*/
    m_pChart->setPhaseOffset( m_iPhaseAlias );

    m_pChart->setAccumulationMode( m_isAccumulation );
    m_pChart->setPeriodNum(m_iSysPeriod);

    return;
}

/************************************************
 * 函数名   : setAllWorkSets
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 设置所有的工作参数
 ************************************************/
void UHFPRPSDistributeNetTestView::setAllWorkSets()
{
    m_eDiagRet = CustomAccessTaskNS::DiagNormal;
    setWorkMode( UHF::MODE_PRPS );
    setFordwardGain( m_eForwardGain );
    setBandWidth( m_eBandWidth );
    setSyncSource( m_eSyncSource );
}

/************************************************
 * 函数名   : initData
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 初始化数据成员
 ************************************************/
void UHFPRPSDistributeNetTestView::initData()
{
    m_eDiagRet = CustomAccessTaskNS::DiagNormal;
    m_pService = service();//服务模块
    m_pMoreConfigView = NULL;//更多设置视图
    m_pSampleBtn = NULL;
    m_pLoadingWidget = NULL;

    m_eSignalState = Module::SIGNAL_STATE_NONE;// 信号状态
    m_eSyncState = Module::Not_Sync;//同步状态
    m_eState = STATE_SAMPLING;
    m_eTestState = TEST_NONE;

    m_usMaxSpectrumValue = 0;
    m_vMaxValueVector.clear();
    setConfigData();
}

/*************************************************
功能： 保存设置
*************************************************************/
bool UHFPRPSDistributeNetTestView::saveConfig(void)
{
    int iGroup = UHF::GROUP_UHF_PRPS;
    m_pConfig->beginGroup( Module::GROUP_UHF );
    m_pConfig->setValue( m_eForwardGain, UHF::KEY_FORWARDGAIN );
    m_pConfig->setValue( m_eBandWidth, UHF::KEY_BANDWIDTH );
    m_pConfig->setValue( m_iPhaseAlias, UHF::KEY_PHASEALIAS );
    m_pConfig->setValue( m_eSyncSource, UHF::KEY_SYNC_SOURCE );
    m_pConfig->setValue( (int)m_isAccumulation, UHF::KEY_PRPS_ACCUMULATION, iGroup );
    m_pConfig->endGroup();

    m_pConfig->beginGroup( Module::GROUP_APP );
    m_pConfig->setValue( m_ucSysFreq, APPConfig::KEY_SYS_FREQ );
    m_pConfig->endGroup();
    return true;
}

/************************************************
 * 函数名   : setSampleBtnText
 * 输入参数 : eState: 采样状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 更新采样状态设置采样按钮显示的文本
 ************************************************/
void UHFPRPSDistributeNetTestView::setSampleBtnText(State eState)
{
    if(eState == STATE_SAMPLING)
    {
        m_pSampleBtn->setTitle(UHF_VIEW_CONFIG_TRANSLATE(UHF::TEXT_STOP));
    }
    else
    {
        m_pSampleBtn->setTitle(UHF_VIEW_CONFIG_TRANSLATE(UHF::TEXT_RUN));
    }
}

/************************************************
 * 函数名   : onDataRead
 * 输入参数 : stData: 采样数据
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 槽函数，处理收到的采样数据
 ************************************************/
void UHFPRPSDistributeNetTestView::onDataRead(UHF::PRPSData stData, MultiServiceNS::USERID Id)
{
    if(userId() == Id && !m_bSampleStopped)
    {
        QVector<double> vRawData;
        vRawData.clear();

        for(int i = 0; i < SPECTTRUMNUM; ++i)
        {
            if(stData.vSpectrum[i] < Module::ZERO)
            {
                stData.vSpectrum[i] = 0;
            }

            double dData = (double) stData.vSpectrum[i];
            vRawData << dData;
        }

        m_pChart->setData(vRawData);    // 往图谱添加数据

        diagDataInfo();

        if(m_vMaxValueVector.size() >= m_ucSysFreq)// 若容器中数据的size超出制定个数，将最先加入的数据删除
        {
            m_vMaxValueVector.pop_front();
        }

        m_vMaxValueVector.append((double)(stData.cMaxSpectrum));  // 将每组数据中的最大值加入容器
        double dMaxValue = m_vMaxValueVector.at(0);

        for(UINT16 i = 1, iSize = m_vMaxValueVector.size(); i < iSize; ++i)
        {
            if(m_vMaxValueVector.at(i) > dMaxValue)
            {
                dMaxValue = m_vMaxValueVector.at(i);
            }
        }

        //special deal data in lu ruan
        dMaxValue = dMaxValue < 0 ? 0 : dMaxValue;
        m_usMaxSpectrumValue = (UINT16)dMaxValue;
        //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
        //m_pChart->setMaxSpectrum(dMaxValue);    // 显示最大值
    }
    else
    {
        //illegal
    }

    return;
}

/*************************************************
功能： 槽，响应诊断结果
输入参数：
        qspDiagResultInfo -- 诊断结果
*************************************************************/
void UHFPRPSDistributeNetTestView::onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo)
{
    if(qspDiagResultInfo.data())
    {
        DiagConfig::DiagDisplayInfo stDiagInfo;
        stDiagInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(qspDiagResultInfo.data()->stDiagRet.defectLevel);
        stDiagInfo.eDiagState = static_cast<DiagConfig::DiagnoseState>(qspDiagResultInfo.data()->eState);
        stDiagInfo.qstrPDDesInfo = qspDiagResultInfo.data()->qstrPDDescription;
        stDiagInfo.qstrPDSignalInfos = qspDiagResultInfo.data()->qstrPDSignalTypeInfos;

        m_eDiagRet = (CustomAccessTaskNS::DiagnoseType)(stDiagInfo.eDiagState);

        if(m_pChart)
        {
            m_pChart->setDiagRet(stDiagInfo);
        }
    }

    return;
}

void UHFPRPSDistributeNetTestView::onTitleBarClicked( void )
{
   close();
}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void UHFPRPSDistributeNetTestView::keyPressEvent( QKeyEvent* event )
{
    if(event->key() == Qt::Key_Escape)
    {
         close();
    }
    else
    {
        SampleChartView::keyPressEvent( event );
    }
    return;
}

void UHFPRPSDistributeNetTestView::onItemActivated(qint32 uiItem)
{
    if(m_eOperationType == OPERATION_DELETE)
    {
        deleteSelectedFile(uiItem);
    }
    else if(m_eOperationType == OPERATION_LOAD)
    {
        //停止采样
        bool bIsSampleStopped = false;
        if(m_eState == STATE_SAMPLING)
        {
            stopSample();
            m_eState = STATE_READY;
            bIsSampleStopped = true;
        }

        if( loadSelectedFile(uiItem) )
        {
            buttonBar()->buttons()[BUTTON_UHF_SAVE_DATA]->setEnabled( false );
            m_eState = STATE_READY;
        }
        else
        {
            if( bIsSampleStopped )
            {
                startSample();
                m_eState = STATE_SAMPLING;
            }
        }
        setSampleBtnText(m_eState);
    }
    else
    {
        qWarning("UHFPRPSDistributeNetTestView::onItemActivated: error!");
    }

}
void UHFPRPSDistributeNetTestView::deleteSelectedFile(qint32 uiItem)
{
//    if( uiItem >= m_vecTestData.size())
//    {
//        qWarning("UHFPRPSDistributeNetTestView::deleteSelectedFile: error!");
//        return;
//    }

//    QString strTempFile  = m_pSubTask->dataSavePath() + "/" + m_vecTestData.at(uiItem).s_strFileName;
//    QFile file ( strTempFile );
//    if( file.exists() )
//    {
//         file.remove();
//         m_pSubTask->deleteTestDatFile( m_strGapId, m_strID, m_vecTestData.at( uiItem ).s_strFileName );

//         m_vecTestData.remove(uiItem);
//         m_testedCount = m_vecTestData.size();
//         m_pTestedNumLabel->setText(QObject::trUtf8("Tested Count: ") + QString::number(m_testedCount) );
//         m_pCommonItemListView->clear();

//         QList<QString> lStrFileNames;
//         QString strFileName = "";
//         for(quint16 i = 0, iSize = m_vecTestData.size(); i < iSize; ++i)
//         {
//             strFileName = m_vecTestData.at( i ).s_strFileName;
//             lStrFileNames.append( CustomAccessTaskNS::testFileNameFromFile( strFileName ) );
//         }
//         m_pCommonItemListView->addItems( lStrFileNames );
//         MsgBox::information("", QObject::trUtf8("Delete succeeded!"));
//    }
//    else
//    {
//        qWarning ()<<" UHFPRPSDistributeNetTestView::deleteSelectedFile: no such file: "<< strTempFile;
//    }
}
bool UHFPRPSDistributeNetTestView::loadSelectedFile(qint32 uiItem)
{
//    bool bSuccess = false;
//    if( uiItem >= m_vecTestData.size())
//    {
//        qWarning("UHFPRPSDistributeNetTestView::deleteSelectedFile: error!");
//        return false;
//    }
//    QString strSelectedFile;
//    strSelectedFile = m_pSubTask->dataSavePath() + "/" + m_vecTestData.at( uiItem ).s_strFileName;

//    m_pCommonItemListView->hide();

//    if( loadTestDataFile(strSelectedFile))
//    {
//        m_bPlayBacked = true;
//        bSuccess = true;
//        QString strFileName = m_vecTestData.at( uiItem ).s_strFileName;
//        m_pLoadFileName->setText( CustomAccessTaskNS::testFileNameFromFile( strFileName ) );
//    }
//    else
//    {
//        MsgBox::warning( "", QObject::trUtf8("No file!") );
//    }
//    return bSuccess;
}

/*************************************************
功能： 保存图谱信息部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void UHFPRPSDistributeNetTestView::fillPRPSDataInfo(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum)
{
    pPRPSSpectrum->setStorageDataType(DataSpecificationNS::DATA_TYPE_DOUBLE);

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_UHF );
    //m_eBandWidth = (UHF::BandWidth)pConfig->value( UHF::KEY_BANDWIDTH ).toUInt();
    pConfig->endGroup();

    DataSpecificationNS::PRPSExtInformation stPRPSExtInformation;
    stPRPSExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_DB;
    stPRPSExtInformation.fAmpLowerLimit = UHF::CHART_MIN_VALUE;
    stPRPSExtInformation.fAmpUpperLimit = UHF::CHART_MAX_VALUE;

    stPRPSExtInformation.eFrequencyBand = static_cast<DataSpecificationNS::FrequencyBand>(m_eBandWidth + 1);

    UHF::bandwidth2FreqRange(stPRPSExtInformation.fFrequencyLowerLimit, stPRPSExtInformation.fFequencyUpperLimit, m_eBandWidth);

    stPRPSExtInformation.iPhaseWindowCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stPRPSExtInformation.iQuantizedAmplitude = 0;
    stPRPSExtInformation.iPowerFreqCycleCount = m_pChart->prpsData().size() / stPRPSExtInformation.iPhaseWindowCount;/*ucSysFreq;*///总是认为数据是50*60(50Hz下)
    if(stPRPSExtInformation.iPowerFreqCycleCount < 50)
    {
        stPRPSExtInformation.iPowerFreqCycleCount = 50;
    }
    memset(stPRPSExtInformation.aucPDTypeProbability, 0, sizeof(stPRPSExtInformation.aucPDTypeProbability));

    stPRPSExtInformation.eDataJudgmentFlag = DataSpecificationNS::DATA_NORMAL;

    QString strBGFileName;
    if( !m_pSubTask->getLatestBGFile(m_strGapId, strBGFileName))
    {
        qWarning("UHFPRPSDistributeNetTestView::fillPRPSDataInfo, getLatestBGFile failed!");
    }
    stPRPSExtInformation.qstrBGFileName = QFileInfo(strBGFileName).fileName();
    stPRPSExtInformation.sGain = m_eForwardGain;

    log_debug("sync source: %d.", m_eSyncSource);
    stPRPSExtInformation.eSyncSource = static_cast<DataSpecificationNS::SyncSource>(m_eSyncSource + 1);
    stPRPSExtInformation.ucSyncState = m_eSyncState;
    stPRPSExtInformation.fSyncFrequency = -1;

    pPRPSSpectrum->setPRPSExtInformation(stPRPSExtInformation);

    return;
}


/*************************************************
功能： 保存图谱信息部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void UHFPRPSDistributeNetTestView::fillPRPDDataInfo(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum)
{
    pPRPDSpectrum->setStorageDataType(DataSpecificationNS::DATA_TYPE_DOUBLE);

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_UHF );
    //m_eBandWidth = (UHF::BandWidth)pConfig->value( UHF::KEY_BANDWIDTH ).toUInt();
    pConfig->endGroup();

    DataSpecificationNS::PRPDExtInformation stPRPDExtInformation;
    stPRPDExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_DB;
    stPRPDExtInformation.fAmpLowerLimit = UHF::CHART_MIN_VALUE;
    stPRPDExtInformation.fAmpUpperLimit = UHF::CHART_MAX_VALUE;

    stPRPDExtInformation.eFrequencyBand = static_cast<DataSpecificationNS::FrequencyBand>(m_eBandWidth + 1);

    UHF::bandwidth2FreqRange(stPRPDExtInformation.fFrequencyLowerLimit, stPRPDExtInformation.fFequencyUpperLimit, m_eBandWidth);

    stPRPDExtInformation.iPhaseWindowCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stPRPDExtInformation.iQuantizedAmplitude = PRPSMapNS::QUANTIFICATION_AMP;
    stPRPDExtInformation.iPowerFreqCycleCount = m_pChart->prpdPeriodCount();

    memset(stPRPDExtInformation.aucPDTypeProbability, 0, sizeof(stPRPDExtInformation.aucPDTypeProbability));

    QString strFileName;
    if( !m_pSubTask->getLatestBGFile(m_strGapId, strFileName))
    {
        qWarning("UHFPRPSDistributeNetTestView::fillPRPDDataInfo, getLatestBGFile failed!");
    }
    stPRPDExtInformation.qstrBGFileName = QFileInfo(strFileName).fileName();

    stPRPDExtInformation.eDataJudgmentFlag = DataSpecificationNS::DATA_NORMAL;
    stPRPDExtInformation.sGain = m_eForwardGain;

    stPRPDExtInformation.eSyncSource = static_cast<DataSpecificationNS::SyncSource>(m_eSyncSource + 1);
    stPRPDExtInformation.ucSyncState = 1;// todo
    stPRPDExtInformation.fSyncFrequency = -1;
    pPRPDSpectrum->setPRPDExtInformation(stPRPDExtInformation);
}

void UHFPRPSDistributeNetTestView::fillPRPSData(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum)
{
    QVector< double > vecPRPSData = m_pChart->prpsData();

    //step5 set map data
    int iDataPointNum = vecPRPSData.size();//ex 50*60
    dbg_info("iDataPointNum is %d\n", iDataPointNum);

    int iPhaseShitStep = m_iPhaseAlias / (360 / PRPSMapNS::PHASE_INTERVAL_CNT);
    dbg_info("iPhaseShitStep is %d\n", iPhaseShitStep);


    int iRealPeriod = iDataPointNum / PRPSMapNS::PHASE_INTERVAL_CNT;
    dbg_info("iRealPeriod is %d\n", iRealPeriod);
    if(iRealPeriod > 50)//todo hard code
    {
        iRealPeriod = 50;
    }

    DataSpecificationNS::PRPSData stPRPSData;
    QVector<double> qvtPRPSData;
    qvtPRPSData.resize(PRPSMapNS::PHASE_INTERVAL_CNT * 50);
    for(int i = 0; i < iRealPeriod; ++i)
    {
        for(int iPeriodIndex = 0, j = 0; j < PRPSMapNS::PHASE_INTERVAL_CNT; ++j)
        {
            if(iRealPeriod < 50)
            {
                iPeriodIndex =  50 + i - iRealPeriod;
            }
            else
            {
                iPeriodIndex = i;
            }
            int iNewPhaseIndex = (j + iPhaseShitStep) % PRPSMapNS::PHASE_INTERVAL_CNT;
            qvtPRPSData[iPeriodIndex * PRPSMapNS::PHASE_INTERVAL_CNT + iNewPhaseIndex] = vecPRPSData.at(i * PRPSMapNS::PHASE_INTERVAL_CNT + j);
        }
    }

    stPRPSData.qbaPDSpectrumData.resize(qvtPRPSData.size() * sizeof(double));
    memcpy(stPRPSData.qbaPDSpectrumData.data(), &qvtPRPSData[0], stPRPSData.qbaPDSpectrumData.size());
    pPRPSSpectrum->setPRPSData(stPRPSData);
}


/*************************************************
功能： 保存图谱数据部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void UHFPRPSDistributeNetTestView::fillPRPDData(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum)
{
    int iDataCnt =  PRPSMapNS::PHASE_INTERVAL_CNT * PRPSMapNS::QUANTIFICATION_AMP;
    QVector<qint16> vecPRPDData = m_pChart->prpdData();

    int iAmpAreaCnt = vecPRPDData.size() / PRPSMapNS::PHASE_INTERVAL_CNT;
    dbg_info("iAmpAreaCnt is %d\n", iAmpAreaCnt);
    int iPhaseShitStep = m_iPhaseAlias / (360 / PRPSMapNS::PHASE_INTERVAL_CNT);
    dbg_info(" iPhaseShitStep is %d\n", iPhaseShitStep);

    DataSpecificationNS::PRPDData stPRPDData;
    QVector<double> qvtPRPDData;
    qvtPRPDData.resize(iDataCnt);
    for(int j = 0; j < PRPSMapNS::PHASE_INTERVAL_CNT; ++j)
    {
        for(int i = 0; i< iAmpAreaCnt; ++i)
        {
            int iNewPhaseIndex = (j + iPhaseShitStep) % PRPSMapNS::PHASE_INTERVAL_CNT;

            qint16 sPRPD = vecPRPDData.at(i*PRPSMapNS::PHASE_INTERVAL_CNT + j);
            if(sPRPD <= 0)
            {
                sPRPD = 0;
            }
            qvtPRPDData[iNewPhaseIndex*PRPSMapNS::QUANTIFICATION_AMP + i] = sPRPD;

        }
    }

    dbg_info("m_pChart->prpdPeriodCount() is %d\n", m_pChart->prpdPeriodCount());

    stPRPDData.qbaPDSpectrumData.resize(qvtPRPDData.size() * sizeof(double));
    memcpy(stPRPDData.qbaPDSpectrumData.data(), &qvtPRPDData[0], stPRPDData.qbaPDSpectrumData.size());
    pPRPDSpectrum->setPRPDData(stPRPDData);
}

INT32 UHFPRPSDistributeNetTestView::quantizationAmpSection(INT32 iAmp, float fAmpLower, float fAmpUpper, INT32 iQuantizationAmpCnt)
{
    //dbg_info("iAmp is %d, fAmpLower is %f, fAmpUpper is %f, iQuantizationAmp is %d\n", iAmp, fAmpLower, fAmpUpper, iQuantizationAmp);
    float fRangePerSection = (fAmpUpper - fAmpLower) / (float)iQuantizationAmpCnt;
    int section = 0;
    float fAmp = 0;

    if(iAmp < Module::ZERO)
    {
        fAmp = 0;
    }
    else
    {
        fAmp = (float)iAmp;
    }
    section = fAmp / fRangePerSection;
    //dbg_info("section is %d\n", section);
    return section;
}


/*************************************************
功能： 保存数据
返回：
    保存结果
*************************************************************/
QString UHFPRPSDistributeNetTestView::saveTestData()
{
    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return "";
    }

    diagDataInfo(true);

    DataSpecificationNS::DataSpecification* pDataSpecification = new DataSpecificationNS::DataSpecification;//当前数据文件
    pDataSpecification->setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());
    pDataSpecification->setCompanyCode("1.1");
    pDataSpecification->setInternalVersion("*******");

    //创建图谱保存对象
    DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum = new DataSpecificationNS::PRPSSpectrum;

    //设置头部信息
    pPRPSSpectrum->setSpectrumCharacter(DataSpecificationNS::PROPERTY_TEST);
    pPRPSSpectrum->setEquipmentName(m_stRealAssetInfo.qstrName);
    pPRPSSpectrum->setEquipmentCode(m_stRealAssetInfo.qstrId);
    pPRPSSpectrum->setTestPointName(m_stTestpointInfo.qstrName);
    pPRPSSpectrum->setTestPointCode(m_stTestpointInfo.qstrId);
    pPRPSSpectrum->setSpectrumTypeCode(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPS);

    //设置PRPS ext信息
    fillPRPSDataInfo(pPRPSSpectrum);

    //设置PRPS数据内容
    fillPRPSData(pPRPSSpectrum);

    //设置PRPD ext信息
    DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum = new DataSpecificationNS::PRPDSpectrum;

    //设置头部信息
    pPRPDSpectrum->setSpectrumCharacter(DataSpecificationNS::PROPERTY_TEST);
    pPRPDSpectrum->setEquipmentName(m_stRealAssetInfo.qstrName);
    pPRPDSpectrum->setEquipmentCode(m_stRealAssetInfo.qstrId);
    pPRPDSpectrum->setTestPointName(m_stTestpointInfo.qstrName);
    pPRPDSpectrum->setTestPointCode(m_stTestpointInfo.qstrId);
    pPRPDSpectrum->setSpectrumTypeCode(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPD);

    //设置PRPD ext信息
    fillPRPDDataInfo(pPRPDSpectrum);

    //设置PRPD数据内容
    fillPRPDData(pPRPDSpectrum);

    pDataSpecification->addSpectrum(pPRPSSpectrum);
    pDataSpecification->addSpectrum(pPRPDSpectrum);

    setDataFileHead(pDataSpecification);

    m_strSavedPath = CustomAccessView::CustomAccessUIFunc::getBinaryDataFileSavePath(DistributeNetAccessNS::TASK_PATH);
    if(pDataSpecification->saveAsBinary(m_strSavedPath))
    {
        DistributeNetAccessNS::DefectInfo stDefectInfo;
        stDefectInfo.eDiagRet = static_cast<DistributeNetAccessNS::TestDiagnosisResult>(m_eDiagRet);
        stDefectInfo.qstrTaskId = m_qstrTaskId;
        stDefectInfo.qstrAssetId = m_stRealAssetInfo.qstrId;
        stDefectInfo.qstrTestpointId = m_stTestpointInfo.qstrId;
        stDefectInfo.qstrFileName = FileOperUtil::getFileName(m_strSavedPath);
        stDefectInfo.qstrBgFileName = m_stTestpointInfo.qstrBgFileName;
        stDefectInfo.qstrFilePath = m_strSavedPath;
        stDefectInfo.qstrBgFilePath = QString(DistributeNetAccessNS::TASK_PATH) + "/" + m_stTestpointInfo.qstrBgFileName;
        DistributeTaskManager::instance()->addDefectInfo(stDefectInfo);

    }
    else
    {
        m_strSavedPath = "";
    }

    delete pDataSpecification;
    pDataSpecification = NULL;
    return m_strSavedPath;
}

void UHFPRPSDistributeNetTestView::setDataFileHead(DataSpecificationNS::DataSpecification* pDataSpecification)
{
    if(NULL != pDataSpecification)
    {
        pDataSpecification->setWeather(DataSpecificationNS::WEATHER_UNRECORD);
#if 0
        DevEnvInfo stEnvironmentData;
        Module::DriverErrCode eErrCode = getEnvironmentData(stEnvironmentData);
        if(eErrCode != Module::DRIVER_ERROR_CODE_NONE)
        {
            dbg_warning("get temperature and humidity failed!");
        }

        m_pDataFile->setTemperature((float)stEnvironmentData.qi32iTemperature);
        m_pDataFile->setHumidity(stEnvironmentData.qui32Humidity);
#else
        pDataSpecification->setTemperature(0);
        pDataSpecification->setHumidity(0);
#endif
        pDataSpecification->setInstrumentManufacturer(SystemSetService::instance()->getCompanyName());
        pDataSpecification->setInstrumentModel(SystemSetService::instance()->getDevModel());

        QString strSN = SystemSetService::instance()->getDevSerialNum();
        pDataSpecification->setInstrumentSerialNumber(strSN);
        pDataSpecification->setInstrumentVersion(SystemSetService::instance()->curSoftwareVersion());
        pDataSpecification->setSystemFrequency(static_cast<quint8>(SystemSetService::instance()->getFrequency()));
    }

    return;
}

/************************************************
 * 函数名   : createChart
 * 输入参数 : parent---父控件
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 创建图谱
 ************************************************/
ChartWidget* UHFPRPSDistributeNetTestView::createChart(QWidget *parent)
{
    Q_UNUSED(parent)
    ChartWidget *pWidget = new ChartWidget;
    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->setContentsMargins(0, 0, 0, 0);
    QVBoxLayout *labelLayout = new QVBoxLayout;
    //已测次数
    m_pBayNameLabel = new QLabel;
    m_pTestPointNameLabel = new QLabel;

    QPalette pa;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    SystemSet::PRPSBGColor eColor = (SystemSet::PRPSBGColor) pConfig->value( APPConfig::KEY_PRPS_BG_COLOR ).toInt();
    pConfig->endGroup();
    if(eColor == SystemSet::PRPS_BG_COLOR_BLACK)
    {
        pa.setColor(QPalette::WindowText,Qt::white);
    }
    else
    {
        pa.setColor(QPalette::WindowText,Qt::black);
    }

    //设置style
    QFont font = AppFontManager::instance()->getAppCurFont();
    font.setPointSize(20);

    m_pBayNameLabel->setFont(font);
    m_pBayNameLabel->setPalette(pa);

    m_pTestPointNameLabel->setFont(font);
    m_pTestPointNameLabel->setPalette(pa);

    m_pLoadFileName = new QLabel;
    m_pLoadFileName->setPalette(pa);
    m_pLoadFileName->setFont(font);

    labelLayout->addWidget(m_pBayNameLabel,Qt::AlignLeft);
    labelLayout->addWidget(m_pTestPointNameLabel,Qt::AlignLeft);

    labelLayout->addWidget( m_pLoadFileName,Qt::AlignLeft );

    //图谱
    m_pChart = new UhfPrpsUnionView(UHF::PRPS_PERIOD_CNT,UHF::PRPS_PERIOD_CNT, UHF::PRPS_PHASE_CNT, UHF::CHART_MAX_VALUE,UHF::CHART_MIN_VALUE);
    m_pChart->setFixedHeight( CHART_HEIGHT );

    vLayout->addLayout(labelLayout);
    vLayout->addWidget(m_pChart);

    pWidget->setLayout(vLayout);
    pWidget->setFixedWidth(Window::WIDTH);
    return pWidget;
}

/************************************************
 * 输入参数 : bAccumulation -- 使能标志
 * 功能     : 使能累积
 ************************************************/
void UHFPRPSDistributeNetTestView::enableAccumulation( bool bAccumulation )
{
    if( bAccumulation != m_isAccumulation )
    {
        m_isAccumulation = bAccumulation;
        m_pChart->setAccumulationMode( m_isAccumulation );
    }
    else
    {
        // accumulation not changed
    }
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void UHFPRPSDistributeNetTestView::onButtonValueChanged( int id, int iValue )
{
    switch( id )
    {
        case BUTTON_UHF_PHASE_SHIFT://相位偏移
        {
            m_iPhaseAlias = iValue;
            if( !m_bPlayBacked )
            {
                m_pChart->setPhaseOffset(m_iPhaseAlias);
            }
        }
            break;
        case BUTTON_UHF_FORWARD_GAIN://前置增益
        {
            m_eForwardGain = (UHF::ForwardGain) iValue;
            setFordwardGain(m_eForwardGain);
        }
            break;
        case BUTTON_UHF_SYNC_SOURCE://同步方式
        {
            m_eSyncSource = (Module::SyncSource) (iValue + WIRELESS_SYNC);
            setSyncSource(m_eSyncSource);
            if( !m_bPlayBacked )
            {
                m_pChart->setSync((PrpsGlobal::SyncSource) m_eSyncSource, (PrpsGlobal::SyncState)m_eSyncState);
            }
        }
            break;
        case BUTTON_IS_ACCUMULATION://是否累积
        {
            if( (bool)iValue != m_isAccumulation )
            {
                m_isAccumulation = (bool)iValue;
                if( !m_bPlayBacked )
                {
                    m_pChart->setAccumulationMode( m_isAccumulation );
                }
            }
            else
            {
                // accumulation not changed
            }
        }
        break;
        case BUTTON_UHF_BAND_WIDTH://带宽
        {
            m_eBandWidth = (UHF::BandWidth) iValue;
            setBandWidth(m_eBandWidth);
        }
            break;
        default:
        {
            dbg_warning("wrong case: %d\n", id);
        }
            break;
    }
}

void UHFPRPSDistributeNetTestView::getFileHead(DataSpecificationNS::DataSpecification* pDataSpecification)
{
    DataSpecificationNS::SpectrumDataFileHead stSpectrumDataFileHead;
    pDataSpecification->getSpectrumDataFileHead(stSpectrumDataFileHead);
    m_pUHFPRPSPRPDDataInfo->ucFreq = stSpectrumDataFileHead.fSystemFrequency;
    m_pUHFPRPSPRPDDataInfo->stPRPSHeadInfo.strSubstationName = stSpectrumDataFileHead.qstrStationName;
    m_pUHFPRPSPRPDDataInfo->stPRPDHeadInfo.strSubstationName = stSpectrumDataFileHead.qstrStationName;
}

void UHFPRPSDistributeNetTestView::getPRPSMapHead(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum)
{
    DataSpecificationNS::SpectrumHead stSpectrumHead;
    pPRPSSpectrum->getSpectrumHead(stSpectrumHead);
    m_pUHFPRPSPRPDDataInfo->stPRPSHeadInfo.strDeviceName = stSpectrumHead.qstrEquipmentName;
    m_pUHFPRPSPRPDDataInfo->stPRPSHeadInfo.eDataPrimitiveType = static_cast<DataFileNS::DataPrimitiveType>(stSpectrumHead.eStorageDataType);
}

void UHFPRPSDistributeNetTestView::getPRPDMapHead(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum)
{
    DataSpecificationNS::SpectrumHead stSpectrumHead;
    pPRPDSpectrum->getSpectrumHead(stSpectrumHead);
    m_pUHFPRPSPRPDDataInfo->stPRPDHeadInfo.strDeviceName = stSpectrumHead.qstrEquipmentName;
    m_pUHFPRPSPRPDDataInfo->stPRPDHeadInfo.eDataPrimitiveType = static_cast<DataFileNS::DataPrimitiveType>(stSpectrumHead.eStorageDataType);
}

/****************************
功能： 获取图谱ext数据
输入参数:
    pMap -- 文件解析得到的图谱
    iDataPointCount -- 图谱数据数量
*****************************/
void UHFPRPSDistributeNetTestView::getPRPSMapInfo(const DataSpecificationNS::PRPSExtInformation* pPRPSExtInformation)
{
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.eSyncSource = static_cast<DataFileNS::SyncSource>(pPRPSExtInformation->eSyncSource);
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.ucSyncState = pPRPSExtInformation->ucSyncState;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.iPhaseIntervalCount = pPRPSExtInformation->iPhaseWindowCount;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.iQuantificationAmp = pPRPSExtInformation->iQuantizedAmplitude;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.iPowerFreCycleCount = pPRPSExtInformation->iPowerFreqCycleCount;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.eAmpUnit = static_cast<DataFileNS::AmpUnit>(pPRPSExtInformation->eAmpUnit);
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.eBandWidth = static_cast<DataFileNS::MapBandWidth>(pPRPSExtInformation->eFrequencyBand);
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.sGain = pPRPSExtInformation->sGain;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.eDataSign = static_cast<PRPSMapNS::MapDataSign>(pPRPSExtInformation->eDataJudgmentFlag);
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.fAmpLowerLimit = pPRPSExtInformation->fAmpLowerLimit;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.fAmpUpperLimit = pPRPSExtInformation->fAmpUpperLimit;
}


/****************************
功能： 获取图谱ext数据
输入参数:
    pMap -- 文件解析得到的图谱
    iDataPointCount -- 图谱数据数量
*****************************/
void UHFPRPSDistributeNetTestView::getPRPDMapInfo(const DataSpecificationNS::PRPDExtInformation* pPRPDExtInformation)
{
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.eSyncSource = static_cast<DataFileNS::SyncSource>(pPRPDExtInformation->eSyncSource);
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.ucSyncState = pPRPDExtInformation->ucSyncState;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.iPhaseIntervalCount = pPRPDExtInformation->iPhaseWindowCount;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.iQuantificationAmp = pPRPDExtInformation->iQuantizedAmplitude;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.iPowerFreCycleCount = pPRPDExtInformation->iPowerFreqCycleCount;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.eAmpUnit = static_cast<DataFileNS::AmpUnit>(pPRPDExtInformation->eAmpUnit);
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.eBandWidth = static_cast<DataFileNS::MapBandWidth>(pPRPDExtInformation->eFrequencyBand);
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.sGain = pPRPDExtInformation->sGain;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.eDataSign = static_cast<PRPSMapNS::MapDataSign>(pPRPDExtInformation->eDataJudgmentFlag);
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.fAmpLowerLimit = pPRPDExtInformation->fAmpLowerLimit;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.fAmpUpperLimit = pPRPDExtInformation->fAmpUpperLimit;
}


/****************************
功能： 回放文件
输入参数:
    strFileName -- 文件名
*****************************/
bool UHFPRPSDistributeNetTestView::loadTestDataFile( const QString& strFileName )
{
    qDebug()<<" UHFPRPSDistributeNetTestView::loadTeatDataFile, strFileName"<<strFileName;

    UHFPRPSPRPDDataInfo sPlayBackDataInfo;
    if(!getData(strFileName, &sPlayBackDataInfo))
    {
        qWarning() << "UHFPRPSDistributeNetTestView::loadTeatDataFile :" << strFileName << " get data  failed!";
        return false;
    }

    displayMap(sPlayBackDataInfo);

    return true;
}

bool UHFPRPSDistributeNetTestView::getData(const QString& strFileName, void *pData)
{
    qDebug()<<"UHFPRPSDistributeNetTestView::getData, strFileName:"<<strFileName;

    m_pUHFPRPSPRPDDataInfo = (UHFPRPSPRPDDataInfo*)pData;

    DataSpecificationNS::DataSpecification dataSpecification;
    dataSpecification.setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());
    //step1 open data file
    if(!dataSpecification.parseBinaryFromFile(strFileName))
    {
        log_error("parse binary data failed.");
        return false;
    }

    //step2 get displayed file head
    getFileHead(&dataSpecification);

    DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum = dynamic_cast<DataSpecificationNS::PRPSSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPS));
    if(NULL == pPRPSSpectrum)
    {
        log_error("get prps map data failed.");
        return false;
    }

    DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum = dynamic_cast<DataSpecificationNS::PRPDSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPD));
    if(NULL == pPRPDSpectrum)
    {
        log_error("get prpd map data failed.");
        return false;
    }

    getPRPSMapHead(pPRPSSpectrum);
    getPRPDMapHead(pPRPDSpectrum);

    DataSpecificationNS::PRPSExtInformation stPRPSExtInformation;
    pPRPSSpectrum->getPRPSExtInformation(stPRPSExtInformation);
    getPRPSMapInfo(&stPRPSExtInformation);

    DataSpecificationNS::PRPDExtInformation stPRPDExtInformation;
    pPRPDSpectrum->getPRPDExtInformation(stPRPDExtInformation);
    getPRPDMapInfo(&stPRPDExtInformation);

    DataSpecificationNS::PRPSData stPRPSData;
    pPRPSSpectrum->getPRPSData(stPRPSData);
    int iDataPointNum = stPRPSExtInformation.iPhaseWindowCount * stPRPSExtInformation.iPowerFreqCycleCount;
    m_pUHFPRPSPRPDDataInfo->vecPRPSData.resize(iDataPointNum);
    memcpy(&m_pUHFPRPSPRPDDataInfo->vecPRPSData[0], stPRPSData.qbaPDSpectrumData.data(), iDataPointNum * sizeof(double));

    DataSpecificationNS::PRPDData stPRPDData;
    pPRPDSpectrum->getPRPDData(stPRPDData);
    iDataPointNum = stPRPDExtInformation.iPhaseWindowCount * stPRPDExtInformation.iPowerFreqCycleCount;
    m_pUHFPRPSPRPDDataInfo->vecPRRepeatyData.resize(iDataPointNum);
    memcpy(&m_pUHFPRPSPRPDDataInfo->vecPRRepeatyData[0], stPRPDData.qbaPDSpectrumData.data(), iDataPointNum * sizeof(double));

    return true;
}

/****************************
功能： 在回放中显示
输入参数: void
*****************************/
void UHFPRPSDistributeNetTestView::displayMap(UHFPRPSPRPDDataInfo &PlayBackDataInfo)
{
    m_pChart->clearData();

    //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
    //m_pChart->setPowerFreq( (PrpsGlobal::Frequency)( PlayBackDataInfo.ucFreq ));
    //log_debug("sync source: %d, state: %d.", PlayBackDataInfo.stPRPSInfo.eSyncSource, PlayBackDataInfo.stPRPSInfo.ucSyncState);
    //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
    //m_pChart->setSync((PrpsGlobal::SyncSource) (PlayBackDataInfo.stPRPSInfo.eSyncSource - 1), (PrpsGlobal::SyncState) PlayBackDataInfo.stPRPSInfo.ucSyncState );
    m_pChart->clearSyncText();

    //是否无信号状态没有保存到数据文件，回放时，先简单默认不显示无信号
    m_pChart->setConnected(true);

    QVector<qint16> prpdData;
    prpdData.clear();
    double dPRPulseCnt;

    //NOTE 如果是累计模式，iPeriodCnt的计算不正确
    int iPeriodCnt = PlayBackDataInfo.vecPRRepeatyData.size() / PlayBackDataInfo.stPRPDInfo.iPhaseIntervalCount;

    logInfo(QString("review data period number: %1.").arg(iPeriodCnt));

    for(int i = 0; i < PlayBackDataInfo.stPRPDInfo.iQuantificationAmp; ++i)
    {
        for(int j = 0; j < PlayBackDataInfo.stPRPDInfo.iPhaseIntervalCount; ++j)
        {
            dPRPulseCnt = PlayBackDataInfo.vecPRRepeatyData.at(j * PlayBackDataInfo.stPRPDInfo.iQuantificationAmp + i);
            prpdData.append((qint16)(dPRPulseCnt));
        }
    }

    //log_debug("max value: %f.", PlayBackDataInfo.stPRPSInfo.fMax);
    //PlayBackDataInfo.stPRPSInfo.fMax = PlayBackDataInfo.stPRPSInfo.fMax < 0 ? 0 : PlayBackDataInfo.stPRPSInfo.fMax;
    //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
    //m_pChart->setMaxSpectrum(PlayBackDataInfo.stPRPSInfo.fMax);
    m_pChart->setPeriodNum(iPeriodCnt);
    m_pChart->addPlayBackData(PlayBackDataInfo.vecPRPSData, prpdData, PlayBackDataInfo.stPRPDInfo.iPowerFreCycleCount);
    m_pChart->setPhaseOffset(0);

    //本地诊断功能
    if(m_pChart->isLocalDiagnosisEnable())
    {
        PRPSDiagInfo stDiagInfo;
        stDiagInfo.qvtDataIn.clear();
        stDiagInfo.dThresholdDbVal = 0;
        stDiagInfo.dSpecMaxVal = m_pChart->rangeMax();
        stDiagInfo.dSpecMinVal = m_pChart->rangeMin();

        stDiagInfo.qvtDataIn = m_pChart->prpsDiagData(stDiagInfo.iPeriodNum, stDiagInfo.iPhaseNum);
        DiagnosisManager::instance()->diagPRPSData(stDiagInfo);
        //m_pChart->setDiagRet((DiagConfig::DiagnoseRet)stDiagInfo.stDiagRet.defectLevel, stDiagInfo.qstrPDDescription, stDiagInfo.qstrPDSignalTypeInfos, true);
    }

    m_pChart->setPeriodNum(m_iSysPeriod);

    return;
}

/************************************************
 * 输入参数 : eBandWidth -- 带宽
 * 功能     : 根据带宽类型更新测试状态
 ************************************************/
void UHFPRPSDistributeNetTestView::updateTestState( UHF::BandWidth eBandWidth )
{
    switch( m_eTestState )
    {
        case TEST_NONE:
        {
            if( eBandWidth == UHF::BAND_WIDTH_FULL )
            {
                m_eTestState = TEST_FULL;
            }
            else if( eBandWidth == UHF::BAND_WIDTH_HIGH )
            {
                m_eTestState = TEST_HIGH;
            }
        }
            break;
        case TEST_HIGH:
        {
            if( eBandWidth == UHF::BAND_WIDTH_FULL )
            {
                m_eTestState = TEST_ALL;
            }
        }
            break;
        case TEST_FULL:
        {
            if( eBandWidth == UHF::BAND_WIDTH_HIGH )
            {
                m_eTestState = TEST_ALL;
            }
        }
            break;
        default:
            break;
    }
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void UHFPRPSDistributeNetTestView::onCommandButtonPressed( int id )
{
    switch( id )
    {
        case BUTTON_UHF_SAMPLE://采样
        {
            switchSample(m_eState);
            m_pLoadFileName->clear();
        }
            break;

        case BUTTON_UHF_SAVE_DATA://保存数据
        {
            saveData();
        }
            break;
        case BUTTON_UHF_LOAD_DATA://载入数据
        {
            loadData();
        }
            break;
        case BUTTON_UHF_DELETE_DATA://删除数据
        {
            deleteData();
        }
            break;
        case BUTTON_MENU://配置
        {
            showMoreConfigBar();
        }
            break;
        default:
        {
            dbg_warning("wrong case: %d\n", id);
        }
            break;
    }
}

/************************************************
 * 函数名   : switchSample
 * 输入参数 : eState: 采样状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 起动、暂停采样切换
 ************************************************/
void UHFPRPSDistributeNetTestView::switchSample(State eState)
{
    dbg_info("eState is %d\n", eState);

    if( m_bPlayBacked )
    {
        setChartParameters();
        m_bPlayBacked = false;
    }
    else
    {
        //do nothing
    }

    if(eState == STATE_READY)
    {
        startSample();
        m_eState = STATE_SAMPLING;
        setSampleBtnText(m_eState);
        m_pChart->setRunningMode(true);
        buttonBar()->buttons()[BUTTON_UHF_SAVE_DATA]->setEnabled( true );
    }
    else if(eState == STATE_SAMPLING)
    {
        stopSample();
        m_eState = STATE_READY;
        setSampleBtnText(m_eState);
        m_pChart->setRunningMode(false);
    }
    else
    {
        dbg_warning("wrong state: %d\n", eState);
    }
}

/************************************************
 * 函数名   : loadData
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 载入数据
 ************************************************/
void UHFPRPSDistributeNetTestView::loadData()
{
//    m_vecTestData.clear();
//    for(quint16 i = 0, iSize = m_pSubTask->testPoints(m_strGapId).size(); i < iSize; ++i)
//    {
//        if( m_strID == m_pSubTask->testPoints(m_strGapId).at(i).s_strId )
//        {
//            m_vecTestData = m_pSubTask->testPoints(m_strGapId).at(i).s_vData;
//            m_testedCount = m_vecTestData.size();
//        }
//    }

//    if(m_vecTestData.size() <= 0)
//    {
//        MsgBox::warning("", QObject::trUtf8("No file!"));
//        return;
//    }

//    QList<QString> lStrFileNames;
//    QString strFileName = "";
//    for(quint16 i = 0, iSize = m_vecTestData.size(); i < iSize; ++i)
//    {
//        strFileName = m_vecTestData.at(i).s_strFileName;
//        lStrFileNames.append(CustomAccessTaskNS::testFileNameFromFile(strFileName));
//    }

//    m_eOperationType = OPERATION_LOAD;
//    m_pCommonItemListView->clear();
//    m_pCommonItemListView->addItems(lStrFileNames);
//    m_pCommonItemListView->show();

//    return;
}

/*************************************************
功能： 删除数据
*************************************************************/
void UHFPRPSDistributeNetTestView::deleteData()
{
//    m_vecTestData.clear();
//    for(quint16 i = 0, iSize = m_pSubTask->testPoints(m_strGapId).size(); i < iSize; ++i)
//    {
//        if( m_strID == m_pSubTask->testPoints(m_strGapId).at(i).s_strId )
//        {
//            m_vecTestData = m_pSubTask->testPoints(m_strGapId).at(i).s_vData;
//            m_testedCount = m_vecTestData.size();
//        }
//    }

//    if(m_vecTestData.size() <= 0)
//    {
//        MsgBox::warning("", QObject::trUtf8("No file!"));
//        return;
//    }

//    QList<QString> lItems;
//    lItems.clear();
//    QString strFileName = "";
//    for(int i = 0, iSize = m_vecTestData.size(); i < iSize; ++i)
//    {
//        strFileName = m_vecTestData.at(i).s_strFileName;
//        lItems.append(CustomAccessTaskNS::testFileNameFromFile(strFileName));
//    }

//    m_eOperationType = OPERATION_DELETE;
//    m_pCommonItemListView->clear();
//    m_pCommonItemListView->addItems(lItems);
//    m_pCommonItemListView->show();

//    return;
}

/************************************************
 * 函数名   : showMoreConfigBar
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 显示更多...
 ************************************************/
void UHFPRPSDistributeNetTestView::showMoreConfigBar()
{
    m_pMoreConfigView->resize( Window::WIDTH, Window::HEIGHT );
    m_pMoreConfigView->show();
}

/************************************************
 * 函数名   : setConfigData
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :
 ************************************************/
void UHFPRPSDistributeNetTestView::setConfigData()
{
    //从配置文件读取参数
    int iGroup = UHF::GROUP_UHF_PRPS;
    m_pConfig = ConfigManager::instance()->config();
    m_pConfig->beginGroup( Module::GROUP_UHF );
    m_eForwardGain = (UHF::ForwardGain)m_pConfig->value( UHF::KEY_FORWARDGAIN ).toUInt();
    m_eBandWidth = (UHF::BandWidth)m_pConfig->value( UHF::KEY_BANDWIDTH ).toUInt();

    dbg_info("m_eBandWidth is %d\n", m_eBandWidth);

    m_iPhaseAlias = m_pConfig->value( UHF::KEY_PHASEALIAS ).toUInt();
    m_eSyncSource = (Module::SyncSource)m_pConfig->value( UHF::KEY_SYNC_SOURCE ).toUInt();
    m_isAccumulation = ( bool )m_pConfig->value( UHF::KEY_PRPS_ACCUMULATION, iGroup).toUInt();
    m_pConfig->endGroup();

    m_pConfig->beginGroup( Module::GROUP_APP );
    m_ucSysFreq = m_pConfig->value( APPConfig::KEY_SYS_FREQ ).toUInt();
    m_pConfig->endGroup();
}

/************************************************
 * 函数名   : setButtonBarDatas
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 设置buttonbar显示的参数
 ************************************************/
void UHFPRPSDistributeNetTestView::setButtonBarDatas()
{
    ((PopupButton*)(buttonBar()->button(BUTTON_UHF_PHASE_SHIFT)))->setValue( m_iPhaseAlias );
    ((PopupButton*)(buttonBar()->button(BUTTON_UHF_FORWARD_GAIN)))->setValue( m_eForwardGain );
    ((PopupButton*)(buttonBar()->button(BUTTON_UHF_SYNC_SOURCE)))->setValue( m_eSyncSource - (int)Module::WIRELESS_SYNC );
    ((PopupButton*)(buttonBar()->button(BUTTON_UHF_BAND_WIDTH)))->setValue( m_eBandWidth );
    ((PopupButton*)(buttonBar()->button(BUTTON_IS_ACCUMULATION)))->setValue( m_isAccumulation );
}

/************************************************
 * 函数名   : createMoreButtonBar
 * 输入参数 : pButtonBar---挂接用的控件
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 创建更多...
 ************************************************/
LabelButtonBar *UHFPRPSDistributeNetTestView::createMoreButtonBar(PushButtonBar *pButtonBar)
{
    LabelButtonBar *pMoreButtonBar = createMoreConfigBar( UHF::CONTEXT, s_UHFButtonInfoMore, sizeof(s_UHFButtonInfoMore)/sizeof(ButtonInfo::Info) );
    pMoreButtonBar->resize( Window::WIDTH, Window::HEIGHT );
    pMoreButtonBar->attach( pButtonBar );//做挂接

    return pMoreButtonBar;
}

/************************************************
 * 函数名   : createButtonbar
 * 输入参数 : parent---父控件
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 创建buttonbar
 ************************************************/
PushButtonBar* UHFPRPSDistributeNetTestView::createButtonbar(QWidget *parent)
{
    Q_UNUSED(parent)
    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar( UHF::CONTEXT, s_UHFButtonInfo, sizeof(s_UHFButtonInfo)/sizeof(ButtonInfo::Info) );
    return pButtonBar;
}

/*************************************************
 * 功能：保存数据
 * ***********************************************/
void UHFPRPSDistributeNetTestView::saveData()
{
    saveConfig(); // 存储到配置文件中

    if(m_eState == STATE_SAMPLING)
    {
        //停止采集
        stopSample();
        m_eState = STATE_READY;
        setSampleBtnText(m_eState);
    }
    QString strFile = saveTestData();

    QFileInfo fileInfo(strFile);
    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
    centerPoint.setY(pBtnBar->pos().y()/*+pBtnBar->height()/2*/);

    if(strFile.isEmpty())
    {
        MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("Save failure!"), centerPoint);
    }
    else
    {
        //if( m_pSubTask->isAutoSwitch() )
        {
            if(MsgBox::OK == MsgBox::delayQuestion("", QObject::trUtf8("Save success, auto switching."), centerPoint))
            {
                close();
                emit sigAutoSwitch();
            }
        }
//        else
//        {
//            QString strText = fileInfo.fileName();
//            processTooLongMsgText(strText);
//            MsgBox::information("", strText, centerPoint);
//        }
    }

    /*TODO
    if(bNeedRestoreSample)
    {
        //开始采集
        startSample();
        m_eState = STATE_SAMPLING;
        setSampleBtnText(m_eState);
    }
    */
    return;
}

/*************************************************
功能： 响应S键事件
*************************************************************/
void UHFPRPSDistributeNetTestView::onSKeyPressed()
{
    saveData();
    return;
}

/*************************************************
 * 功能：诊断数据
 * 输入参数：
 *      bSave：是否为保存操作的逻辑，缺省为false
 * ***********************************************/
void UHFPRPSDistributeNetTestView::diagDataInfo(bool bSave)
{
   if(bSave)
   {
       if(m_pChart->isLocalDiagnosisEnable())
       {
           PRPSDiagInfo stDiagInfo;
           stDiagInfo.qvtDataIn.clear();
           stDiagInfo.dThresholdDbVal = 0;
           stDiagInfo.dSpecMaxVal = m_pChart->rangeMax();
           stDiagInfo.dSpecMinVal = m_pChart->rangeMin();

           stDiagInfo.qvtDataIn = m_pChart->prpsDiagData(stDiagInfo.iPeriodNum, stDiagInfo.iPhaseNum);

           DiagResultInfo stRetInfo;
           DiagnosisManager::instance()->diagPRPSDataDirectly(stDiagInfo, stRetInfo);

           DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
           stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stRetInfo.stDiagRet.defectLevel);
           stDiagDisplayInfo.eDiagState = static_cast<DiagConfig::DiagnoseState>(stRetInfo.eState);
           stDiagDisplayInfo.qstrPDDesInfo = stRetInfo.qstrPDDescription;
           stDiagDisplayInfo.qstrPDSignalInfos = stRetInfo.qstrPDSignalTypeInfos;

           m_eDiagRet = (CustomAccessTaskNS::DiagnoseType)(stDiagDisplayInfo.eDiagState);

           if(m_pChart && SystemSet::RT_DIAG_ON == SystemSetService::instance()->getRealtimeDiagnosisSwitch())
           {
               m_pChart->setDiagRet(stDiagDisplayInfo);
           }
       }
   }
   else
   {
       if(SystemSet::RT_DIAG_ON == SystemSetService::instance()->getRealtimeDiagnosisSwitch())
       {
           if(m_pChart->isLocalDiagnosisEnable())
           {
               PRPSDiagInfo stDiagInfo;
               stDiagInfo.qvtDataIn.clear();
               stDiagInfo.dThresholdDbVal = 0;
               stDiagInfo.dSpecMaxVal = m_pChart->rangeMax();
               stDiagInfo.dSpecMinVal = m_pChart->rangeMin();

               stDiagInfo.qvtDataIn = m_pChart->prpsDiagData(stDiagInfo.iPeriodNum, stDiagInfo.iPhaseNum);
               DiagnosisManager::instance()->diagPRPSData(stDiagInfo);
           }
       }
   }

   return;
}
