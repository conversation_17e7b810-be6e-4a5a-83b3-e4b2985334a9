/*
* Copyright (c) 2016.3，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：AEAmpPlayBackView.h
*
* 初始版本：1.0
* 作者：张涛
* 创建日期：2016年3月7日
* 摘要：该文件主要实现了超声波幅值图谱控件回放
*/
#ifndef WAVEPLAYBACKVIEW_H
#define WAVEPLAYBACKVIEW_H

#include <QWidget>
#include <QFrame>
#include <QLabel>
#include "playbackView/PlayBackBase.h"
#include "ca/dataSave/AccessG100WaveDataSave.h"
#include "dataSave/DataStructures.h"
#include "../pulse/FastCollection/FastWaveUT.h"

class WavePlayBackView : public PlayBackBase
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    输入参数:
        parent -- 父窗体
    *****************************/
    explicit WavePlayBackView( QWidget *parent = 0 );

private:
    /****************************
    功能： 回放文件
    输入参数:
        strFileName -- 文件名
    *****************************/
    void playbackFile( const QString& strFileName );


    QFrame *createChart( QWidget *parent );

    void displayMap(const AccessG100WaveDataInfo &sPlayBackDataInfo);

    void setScaleMax(UINT32 &uixScaleMax);
#if 0
    void adjustScaleData(quint16 usSampleTime, qint32 iRangeMax, QVector<double> &vecXData, QVector<double> &vecYData);
#else
    void adjustScaleData(DataFileNS::AmpUnit eAmpUnit, UINT32 &uixScaleMax, qint32 iRangeMax, QVector<double> &vecXData, QVector<double> &vecYData);
#endif
private slots:
    /****************************
    函数名： onZoomBack;
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 响应放大返回
    *****************************/
    void onZoomBack( void );

private:
    AccessG100WaveDataInfo  m_sPlayBackDataInfo; //存放回放数据的结构体

    FastWaveUT *m_pChart;
    QLabel *m_pYTitle;
};

#endif // WAVEPLAYBACKVIEW_H
