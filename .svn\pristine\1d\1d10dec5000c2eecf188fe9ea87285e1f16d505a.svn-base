#include "AEWaveStrategy.h"
#include "model/HCStatus.h"
#include "../peripheral/peripheralservice.h"

#define GET_API_CHANNEL( eChannel ) ((AE_CHANNEL)(eChannel) )//将AEService的Channel类型转换成底层的Channel类型
#define GET_SERVICE_SYNC_STATE( eSyncState ) ((Module::SyncState)(eSyncState) )//将底层的SyncState类型转换成AEService的SyncState类型
#define GET_SERVICE_CHANNEL( eChannel ) ((AE::ChannelType)(eChannel) )//将底层的Channel类型转换成AEService的ChannelType类型

/****************************
功能： 构造函数
*****************************/
AEWaveStrategy::AEWaveStrategy(QObject *parent) :
    AEServiceStrategy(parent), m_uiReadAEWaveDataFailedTimes(0)
{
    memset(&m_dataRead,0x0,sizeof(AEReadData));
}

/*************************************************
功能： 开始采集
*************************************************/
void AEWaveStrategy::startSample()
{
    startSampleTimer();
}

/*************************************************
功能： 定时事件处理
输入参数:
    event -- 事件
*************************************************/
void AEWaveStrategy::timerEvent(QTimerEvent *e)
{
    QMutexLocker qMutexLocker(&m_mutexAffair);  //获取事务操作权限

    if( e->timerId() == timerId() )
    {
        //先停止采样
        stopSampleTimer();
        //读取数据
#ifdef Q_PROCESSOR_ARM
        memset(&m_dataRead,0x0,sizeof(AEReadData));
        int iResult = PeripheralService::instance()->readAEData( &m_dataRead,Waveform  );

        if( HC_SUCCESS == iResult )
        {
            //检查通道是否改变
            if( isChannelChanged( GET_SERVICE_CHANNEL( m_dataRead.eAE_CHANNEL ) ) )
            {
                setChannel( GET_SERVICE_CHANNEL( m_dataRead.eAE_CHANNEL ) );
            }
        }
        else
        {
            // not valid
        }

#else
        //检查通道是否改变
        if( isChannelChanged( GET_SERVICE_CHANNEL( m_dataRead.eAE_CHANNEL ) ) )
        {
            setChannel( GET_SERVICE_CHANNEL( m_dataRead.eAE_CHANNEL ) );
        }
        int iResult = HC_SUCCESS;
        for( int i = 0;i < AEWAVENUM;++i )
        {
            m_dataRead.Data.WaveData.faWaveValue[i] = qrand()%10 + 5;
        }
#endif
        //发射信号：数据
        if( HC_SUCCESS == iResult )
        {
            m_uiReadAEWaveDataFailedTimes = 0;
            //检查同步状态是否改变
            //if( isSyncStateChanged( GET_SERVICE_SYNC_STATE( m_dataRead.eSyncState ) ) )
            //{
            //    setSyncState( GET_SERVICE_SYNC_STATE( m_dataRead.eSyncState ) );
            //}

            setSyncState(GET_SERVICE_SYNC_STATE(m_dataRead.eSyncState));

            QVector< AE::WaveData > datas;
            datas.reserve( AEWAVENUM );
            for( int i = 0;i < AEWAVENUM;++i )
            {
                AE::WaveData data;
                data.eAE_CHANNEL = m_dataRead.eAE_CHANNEL;
                data.fWaveValue = m_dataRead.Data.WaveData.faWaveValue[i];
                datas.append( data );
            }
            emit sigData( datas );
        }
        else
        {
            ++m_uiReadAEWaveDataFailedTimes;
            if(m_uiReadAEWaveDataFailedTimes >=  AE::READ_DATA_FAIL_TIMES_MAX)
            {
                m_uiReadAEWaveDataFailedTimes = 0;
                emit sigReadAEWaveDataFailed();
                //return;
            }
        }
        //重新开始采样
        startSampleTimer();
    }
    else
    {
        killTimer( e->timerId() );
    }
}
