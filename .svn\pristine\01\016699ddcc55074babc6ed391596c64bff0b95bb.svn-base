/*
* Copyright (c) 2016.06，南京华乘电气科技有限公司
* All rights reserved.
*
* CheckBox.cpp
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年6月15日
* 摘要：增加checkBox的识别区域

* 当前版本：1.0
*/

#ifndef CHECKBOX_H
#define CHECKBOX_H

#include <QWidget>
#include <QMouseEvent>
#include <QCheckBox>

class ElidedTextCheckBox : public QCheckBox
{
    Q_OBJECT

public:
    explicit ElidedTextCheckBox(QWidget* pParent = 0);
    explicit ElidedTextCheckBox(const QString& qstrText, QWidget* pParent = 0);

protected:
    virtual void hideEvent(QHideEvent* pEvent) override;

    virtual void paintEvent(QPaintEvent* pEvent) override;

private:
    void showToolTip();

private slots:

    void onStateChanged(int iState);

private:
    bool m_bShowToolTip;
    bool m_bFirstShow;
};

class CheckBox : public QWidget
{
    Q_OBJECT

public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        Id:识别号
        parent: 父控件指针
    *************************************************************/
    CheckBox(const QString &text,int Id,QWidget *parent = 0);

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~CheckBox();

    /*************************************************
    功能： 设置选中状态
    输入参数:
            bChecked -- true:选中
                        false:未选中
    *************************************************************/
    void setChecked( bool bChecked );

    /*************************************************
    功能： 返回当前选中状态
    *************************************************************/
    bool isChecked()const;

    /*************************************************
    函数名： text
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 返回当前文本
    *************************************************************/
    void setText( const QString& text );

    /*************************************************
    函数名： text
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 返回当前文本
    *************************************************************/
    QString text() const;

    /*************************************************
    函数名： Id
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 返回当前标号
    *************************************************************/
    int Id() const;

    /*************************************************
    函数名： deleteLater
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 删除
    *************************************************************/
    void deleteLater();
protected:
    /*************************************************
    函数名： mousePressEvent
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 鼠标事件
    *************************************************************/
    void mousePressEvent(QMouseEvent*e);

    /*************************************************
    函数名： eventFilter
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 事件过滤器
    *************************************************************/
    bool eventFilter(QObject *, QEvent *);
signals:
    /*************************************************
    函数名： sigPressId
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 选中时发出的信号
    *************************************************************/
    void sigPressId( int id );
private:
    /*************************************************
    函数名： setActive
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置选中
    *************************************************************/
    void setActive( void );
private:
    ElidedTextCheckBox* m_pCheckBox;    // checkBox控件
    int m_iId;                 // 序号、封装一层以后不能用buttonGroup装起来，故发送的信号带上ID，方便识别
    QString m_strTitle;        // 控件的标题
};

#endif // WIDGET_H
