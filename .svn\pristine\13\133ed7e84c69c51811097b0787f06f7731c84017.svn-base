﻿/*
* Copyright (c) 2015.11，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：rfidApi.h
*
*
* 初始版本：1.0
* 作者：吴昌盛
* 创建日期：2016年1月27日
*
* 
*/

#ifndef _RFIDAPI_H_
#define _RFIDAPI_H_

#include "DriverDataDefine.h"

//#define     RFID_DEBUG              //调试
//#define     PRINTF_BLOCK            //打印数据块数据
//#define     PRINTF_FRAME            //打印报文

#ifdef __cplusplus    
extern "C" {          
#endif

#ifdef RFID_DEBUG
#define rfid_debug(fmt,args...) do    \
{printf("[RFID:] "fmt,args);}while(0);
#else
#define rfid_debug(fmt,args...)
#endif

#define STATIONNAMEMAXNUM       128
#define SWCABINETSNMAXNUM       32
#define SWCABINETNAMEMAXNUM     128
#define TESTNUMMAXNUM           12

/* RFID读取信息类 */
typedef struct
{
    UINT16  usaStationName[STATIONNAMEMAXNUM];         //站名
    UINT8   ucaSwitchCabinetSn[SWCABINETSNMAXNUM];     //开关柜编号
    UINT16  usaSwitchCabinetName[SWCABINETNAMEMAXNUM]; //开关柜名
    UINT8   ucaTestNum[TESTNUMMAXNUM];                 //测试编号 eg..201602026001时间+版本编号,每个数字一个字节,版本编号每读一次加1
    float   fVoltageClass;                             //电压等级
}ReadRFIDData;

//读取RFID信息
INT32 read_rfid(ReadRFIDData *pstReadRFIDData);

//写入RFID信息
INT32 write_rfid(ReadRFIDData *pstReadRFIDData);

//初始化RFID 每次读写添加
INT32 init_rfid(void);

//退出RFID 每次读写添加
void exit_rfid(void);

#ifdef __cplusplus
}
#endif

#endif

