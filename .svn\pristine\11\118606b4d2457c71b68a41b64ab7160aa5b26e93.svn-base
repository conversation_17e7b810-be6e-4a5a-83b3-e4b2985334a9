#ifndef PRPSRECORDVIEW_H
#define PRPSRECORDVIEW_H

/*
* Copyright (c) 2015.11，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: PrpsRecordView.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年4月20日
* 摘要：定义了prps录制view

* 当前版本：1.0
* 典型应用场景：
* RecordView *pView = new RecordView;
 * // 希望录制的基本界面能够盖住buttonBar部分
 * pView->setGeometry( ButtonBar.geometry() );
 * connect( pView,SIGNAL(sigRecordFinished()),this,SLOT(onFinished()) );
 * connect( pView,SIGNAL(sigRecordCanceled()),this,SLOT(onCanceled()) );
 * pView->show();
*/

#include <QFrame>
#include "progressgroup.h"
#include "recordbuttongroup.h"
#include "Widget.h"

class WIDGET_EXPORT PrpsRecordView : public QFrame
{
    Q_OBJECT
public:
    /************************************************
     * 功能: 构造函数
     * 入参：parent -- 父控件指针
     *      iPeriodCnt -- 总共录制的周期数
     *      iInterval -- 单周期时间间隔（ms）
     ************************************************/
    explicit PrpsRecordView( qint32 iPeriodAll,qint32 iInterval,QWidget *parent = 0);

    /************************************************
     * 功能: 手动设置进度，增量修改
     * 入参：iPeriodShifted -- 进度改变值（数据周期改变数）
     ************************************************/
    void setPeriodShift( qint32 iPeriodShifted );
signals:
    /************************************************
     * 功能: 停止录制时发出的信号
     ************************************************/
    void sigRecordFinished();

    /************************************************
     * 功能: 取消录制时发出的信号
     ************************************************/
    void sigRecordCanceled();
private:
    /************************************************
     * 功能: 判断录制是否结束的标志
     * 入参：iCurrentProgerss -- 当前移动的周期数
     *      iMaxPeriod -- 最大周期数
     * 返回值：true -- 已经结束
     *       false -- 尚未结束
     ************************************************/
    bool isRecordFinished( qint32 iCurrentPeriod,qint32 iMaxPeriod );
private:
    RecordButtonGroup *m_pRecordButton; // 录制按键
    ProgressGroup *m_pProgress;         // 进度条显示

    qint32 m_iMaxPeriod;                // 总录制周期数
    qint32 m_iCurrentPeriod;            // 当前周期数
};

#endif // PRPSRECORDVIEW_H
