#include "xmldocument.h"
#include "QtCrypto.h"
#include "datadefine.h"
#include "global_log.h"
#ifdef Q_PROCESSOR_ARM
#include <unistd.h>
#endif

const QString PDA_DATA_BACKUP_STORAGE_ROOT_PATH = "/media/data/SavedData/BackUp";
/************************************************************************/
//自定义xml类 -- 构造函数
//输入参数：
//      file -- 文件名
//      mode -- 读写权限
//      root -- 根节点名（用于写）
//      pchCodecName -- 编码名（默认UTF-8）
//      parent -- 父Object
//返回参数：
//      根节点
/************************************************************************/
XMLDocument::XMLDocument(const QString& file,
                          QIODevice::OpenMode mode,
                          const QString& root,
                          const char* pchCodecName,
                          bool bIsCrypt,
                          QObject* parent)
    : QObject(parent)
{
    m_strFile = file;
    m_strRootName = root;
    m_mode = mode;
    m_strCodecName = pchCodecName;
    m_bIsCrypt = bIsCrypt;

    QByteArray baPreCryptoData;
    QByteArray baCryptoData;
    baPreCryptoData.clear();
    baCryptoData.clear();

    m_file.setFileName(m_strFile);
    m_bValid = m_file.open(mode);
    if(m_bValid)
    {
        baPreCryptoData = m_file.readAll();
        if(baPreCryptoData.isEmpty())
        {
            baPreCryptoData = readBackUpFile();
        }

        if(m_bIsCrypt)
        {
            if(0 != baPreCryptoData.count())
            {
                baCryptoData = deCrypt(baPreCryptoData);
            }
        }
        else
        {
            baCryptoData = baPreCryptoData;
        }

        QString strErr = "";
        int iErrLine = 0, iErrColumn = 0;
        if(!m_doc.setContent(baCryptoData, &strErr, &iErrLine, &iErrColumn))
        {
            QString strHead = QString("version=\"1.0\" encoding=\"%1\"").arg(m_strCodecName);
            QDomProcessingInstruction instruction = m_doc.createProcessingInstruction("xml", strHead);
            m_doc.appendChild(instruction);
            m_element = m_doc.createElement(m_strRootName);
            m_doc.appendChild(m_element);
        }
        else
        {
            m_element = m_doc.documentElement();
            m_element.setTagName(m_strRootName);
        }
    }
    else
    {
        log_error("open xml file failed.");
    }
}

/************************************************************************/
//自定义xml类 -- 构造函数
//输入参数：
//      root -- XML格式数据，或者根节点名（用于写）
//      pchCodecName -- 编码名（默认UTF-8）
//      parent -- 父Object
//返回参数：
//      根节点
/************************************************************************/
XMLDocument::XMLDocument(const QString& root,
                         const char* pchCodecName,
                         QObject* parent)
    : QObject(parent)
{
    if(!m_doc.setContent(root))
    {
        QString strHead = QString("version=\"1.0\" encoding=\"%1\"").arg(pchCodecName);
        QDomProcessingInstruction instruction = m_doc.createProcessingInstruction("xml", strHead);
        m_doc.appendChild(instruction);

        m_strRootName = root;

        m_element = m_doc.createElement(m_strRootName);
        m_doc.appendChild(m_element);
    }
    else
    {
        m_element = m_doc.documentElement();
    }
}

/************************************************************************/
//自定义xml类 -- 析构函数
//存储
/************************************************************************/
XMLDocument::~XMLDocument()
{
    if(m_file.isOpen())
    {
        m_file.close();
    }
}

/************************************************************************/
//自定义xml类 -- 清除
/************************************************************************/
void XMLDocument::clear()
{
    m_doc.clear();
    m_element = m_doc.createElement(m_strRootName);
    m_doc.appendChild(m_element);
    return;
}

/************************************************************************/
//自定义xml类 -- 子节点
//输入参数：
//      name -- 节点名
//返回参数：
//      元素列表
/************************************************************************/
QList<QDomElement> XMLDocument::childElement(const QString& name) const
{
    QList<QDomElement> elements;
    elements.clear();

    QDomNodeList nodesread = m_element.childNodes();
    for(int i = 0, iCount = nodesread.count(); i < iCount; ++i)
    {
        if(nodesread.at(i).isElement())
        {
            if(name.isNull() || (nodesread.at(i).nodeName() == name))
            {
                elements << nodesread.at(i).toElement();
            }
        }
    }

    return elements;
}

/************************************************************************/
//自定义xml类 -- 开始一个节点
//输入参数：
//      name -- 节点名
/************************************************************************/
void XMLDocument::beginElement(const QString& name)
{
    QDomElement element = m_element.firstChildElement(name);
    if(element.isNull())
    {
        element = m_doc.createElement(name);
        m_element.appendChild(element);
    }
    m_element = element;
    return;
}


/************************************************************************/
//自定义xml类 -- 添加一个节点
//输入参数：
//      name -- 节点名
//返回参数：
//      节点
/************************************************************************/
QDomElement XMLDocument::addElement(const QString &name)
{
    QDomElement element = m_doc.createElement(name);
    m_element.appendChild(element);

    return element;
}

/************************************************************************/
//自定义xml类 -- 结束一个节点
/************************************************************************/
void XMLDocument::endElement()
{
    if(!m_element.isNull())
    {
        m_element = m_element.parentNode().toElement();
    }
    return;
}

/************************************************************************/
//自定义xml类 -- 获取当前node的tagname
/************************************************************************/
QString XMLDocument::getCurTagName()
{
    QString qstrName = "";
    if(!m_element.isNull())
    {
        qstrName = m_element.tagName();
    }
    return qstrName;
}

/************************************************************************/
//自定义xml类 -- 删除节点
//输入参数：
//      name -- 节点名
/************************************************************************/
void XMLDocument::removeElement(const QString& name)
{
    QDomNodeList nodes = m_element.childNodes();
    for(int i = 0, iCount = nodes.count(); i < iCount; ++i)
    {
        QDomNode node = nodes.at(i);
        if(node.nodeName() == name)
        {
            m_element.removeChild(node);
        }
    }
    return;
}

/************************************************************************/
//自定义xml类 -- 是否存在节点
//输入参数：
//      name -- 节点名
/************************************************************************/
bool XMLDocument::hasElement(const QString &name)
{
    bool bRet = false;
    QDomNodeList nodes = m_element.childNodes();
    for(int i = 0, iCount = nodes.count(); i < iCount; ++i)
    {
        if(nodes.at(i).nodeName() == name)
        {
            bRet = true;
        }
    }

    return bRet;
}

/************************************************************************/
//自定义xml类 -- 键值操作
//输入参数：
//      key -- 键
//      value -- 值
/************************************************************************/
void XMLDocument::setValue(const QString& key,const QString& value)
{
    removeElement(key);
    beginElement(key);
    setText(value);
    endElement();
    return;
}

/************************************************************************/
//自定义xml类 -- 键值操作
//输入参数：
//      key -- 键
//      defValue -- 默认值
//返回参数：
//      值
/************************************************************************/
QString XMLDocument::value(const QString& key, const QString& defValue) const
{
    QDomElement element = m_element.firstChildElement(key);
    if(element.isNull())
    {
        return defValue;
    }
    return element.text();
}

/************************************************************************/
//自定义xml类 -- 获取文档字节流
//返回参数：
//      文档字节流
/************************************************************************/
#if 1
QByteArray XMLDocument::getByteArray() const //usage defined by zhang tao
{
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
    QByteArray dataIn = m_doc.toByteArray(2);
    QTextStream in(dataIn);
    QByteArray dataOut;
    dataOut.clear();
    QTextStream out(&dataOut);
    out.setCodec(m_strCodecName.toLatin1().constData());
    out << in.readAll();
    out.flush();
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("GBK"));

    return dataOut;
}
#else
QByteArray XMLDocument::getByteArray() const //new method defined by zhou dingyu in TB
{
    QString str = "";
    QTextStream in(&str);  //QTextStream使用Local设置
    in.setCodec(QTextCodec::codecForName("UTF-8"));  //防止Local不是UTF-8
    QByteArray dataIn = m_doc.toByteArray(2);
    in << dataIn;
    in.flush();

    QByteArray dataOut;
    dataOut.clear();
    QTextStream out(&dataOut);
    out.setCodec(m_strCodecName.toLatin1().constData());

    out << str;
    out.flush();

    return dataOut;
}
#endif

/************************************************************************/
//自定义xml类 -- 设置文本
//输入参数：
//      text -- 文本
/************************************************************************/
void XMLDocument::setText(const QString &text)
{
    QDomNodeList nodes = m_element.childNodes();
    for(int i = 0, iCount = nodes.count(); i < iCount; ++i)
    {
        QDomNode node = nodes.at(i);
        if(node.isText())
        {
            m_element.removeChild(node);
        }
    }

    //写入value
    QDomText textNode = m_doc.createTextNode(text);
    m_element.appendChild(textNode);
    return;
}

/************************************************************************/
//自定义xml类 -- 文本
//返回参数：
//      文本
/************************************************************************/
QString XMLDocument::text() const
{
    return m_element.text();
}

/************************************************************************/
//自定义xml类 -- 判断是否存在文本
/************************************************************************/
bool XMLDocument::hasText()
{
    return (!m_element.text().isNull());
}

/************************************************************************/
//自定义xml类 -- 设置属性
//输入参数：
//      name -- 属性名
//      attribute -- 属性值
/************************************************************************/
void XMLDocument::setAttribute(const QString &name, const QString &attribute)
{
    if( m_element.hasAttribute(name) )
    {
        m_element.removeAttribute(name);
    }
    m_element.setAttribute(name, attribute);
    return;
}

/************************************************************************/
//自定义xml类 -- 删除属性
//输入参数：
//      name -- 属性名
/************************************************************************/
void XMLDocument::removeAttribute(const QString &name)
{
    m_element.removeAttribute(name);
    return;
}

/************************************************************************/
//自定义xml类 -- 是否存在属性
//输入参数：
//      name -- 属性名
/************************************************************************/
bool XMLDocument::hasAttribute(const QString &name)
{
    return m_element.hasAttribute(name);
}

/************************************************************************/
//自定义xml类 -- 是否存在属性
/************************************************************************/
bool XMLDocument::hasAttributes()
{
    return m_element.hasAttributes();
}
/************************************************************************/
//自定义xml类 -- 获取属性
//输入参数：
//      name -- 属性名
//      defAttribute -- 默认属性值
/************************************************************************/
QString XMLDocument::attribute(const QString& name, const QString& defAttribute) const
{
    return m_element.attribute(name, defAttribute);
}

/************************************************************************/
//自定义xml类 -- 获取属性
/************************************************************************/
QXmlStreamAttributes XMLDocument::attributes () const
{
    QDomNamedNodeMap nodes = m_element.attributes();
    QXmlStreamAttributes maps;
    maps.clear();

    for(int i = 0, iCount = nodes.count(); i < iCount; ++i)
    {
        maps.append(nodes.item(i).toAttr().name(), nodes.item(i).toAttr().value());
    }

    return maps;
}

/************************************************************************/
//自定义xml类 -- 存储
/************************************************************************/
bool XMLDocument::save()
{
    qDebug()<<"----------------------------------xmlsave-----------------------------------";
    saveBackUpFile();
    bool bRet = false;
    //写入文件
    if(m_file.exists())
    {
        m_file.resize(0);
        if(m_bIsCrypt)
        {
            QByteArray data = enCrypt(getByteArray());
            m_file.write(data);
        }
        else
        {
            m_file.write(getByteArray());
        }

        bRet = m_file.flush();

#if (defined Q_OS_LINUX) || (defined Q_PROCESSOR_ARM)
        if(bRet)
        {
            //立刻把文件同步到磁盘
            sync();
        }
#endif
    }

    return bRet;
}

/************************************************************************/
//自定义xml类 -- 备份存储
/************************************************************************/
bool XMLDocument::saveBackUpFile()
{
    QDir dir(PDA_DATA_BACKUP_STORAGE_ROOT_PATH);
    if(!dir.exists())
    {
        dir.mkpath(PDA_DATA_BACKUP_STORAGE_ROOT_PATH);
    }

    bool bRet = false;
    //写入文件
    if(m_BackUpfile.exists())
    {
        m_BackUpfile.resize(0);

    }
    else
    {
        QStringList strFile = m_strFile.split('/');
        m_strBackUpFile = QString("%1/%2").arg(PDA_DATA_BACKUP_STORAGE_ROOT_PATH).arg(strFile.last());
        m_BackUpfile.setFileName(m_strBackUpFile);

    }

    if(m_BackUpfile.open(QIODevice::ReadWrite))
    {
        if(m_bIsCrypt)
        {
            QByteArray data = enCrypt(getByteArray());
            m_BackUpfile.write(data);
        }
        else
        {
            m_BackUpfile.write(getByteArray());
        }

        bRet = m_BackUpfile.flush();

    #if (defined Q_OS_LINUX) || (defined Q_PROCESSOR_ARM)
        if(bRet)
        {
            //立刻把文件同步到磁盘
            sync();
        }
    #endif
    }
    return bRet;
}

/************************************************************************/
//自定义xml类 -- 备份存储
/************************************************************************/
QByteArray XMLDocument::readBackUpFile()
{
    bool bRet = false;
    qDebug()<<"222222222222";
    QByteArray tempData;
    tempData.clear();

    QStringList strFile = m_strFile.split('/');
    m_strBackUpFile = QString("%1/%2").arg(PDA_DATA_BACKUP_STORAGE_ROOT_PATH).arg(strFile.last());
    m_BackUpfile.setFileName(m_strBackUpFile);

    if(m_BackUpfile.exists())
    {
        if(m_BackUpfile.open(QIODevice::ReadWrite))
        {
            tempData = m_BackUpfile.readAll();
            m_BackUpfile.flush();

        #if (defined Q_OS_LINUX) || (defined Q_PROCESSOR_ARM)

                //立刻把文件同步到磁盘
            sync();

        #endif
        }

    }

    return tempData;
}

