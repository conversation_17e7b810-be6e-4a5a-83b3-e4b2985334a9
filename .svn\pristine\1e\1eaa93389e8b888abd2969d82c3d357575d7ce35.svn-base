﻿#include "AEAmpView.h"
#include "ae/AEView.h"
#include "ae/AEViewConfig.h"
#include "controlButton/ControlButtonInfo.h"
#include "buttonBar/PushButtonBar.h"
#include "controlButton/CmdButton.h"
#include "controlButton/RadioButton.h"
#include "controlButton/SliderButton.h"
#include "ae/AEConfig.h"
#include "messageBox/msgbox.h"
#include "window/Window.h"
#include "recordplay/RecordView.h"
#include "ae/dataSave/AEAmpDataSave.h"
#include "ae/dataSave/AERecordDataSave.h"
#include "rfid/RFIDScanView.h"
#include "rfid/RFIDReadView.h"
#include "playbackView/PlayBackView.h"
#include "deleteDataView/DeleteDataView.h"
#include "diagnosismgr/diagnosismanager.h"
#include "functionconfigmanager/funccfgsrvmanager.h"
#include "AEAmpPlayBackView.h"
#include "labelButton/LabelButton.h"
#include <datafile.h>
#include "equipmentinfo.h"
#include "appconfig.h"
#include "log/log.h"
#include "recordplay/playview.h"
#include "fileoper/fileoperutil.h"


const int INVALID_USER = -1;
const UINT8 MINUTE_PER_HOUR = 60;
const UINT8 SECOND_PER_MINUTE = 60;

namespace aeampview
{

typedef enum _AEAmpButton
{
    BUTTON_AE_SAMPLE_MODE = 0,//模式
    BUTTON_AE_SINGLE_SAMPLE,//单次采样
    BUTTON_AE_NOISE_TEST,//背景噪声测试
    //BUTTON_AE_CLEAR_NOISE,//清除背景噪声
    BUTTON_AE_SAVE_DATA,//保存数据
    BUTTON_AE_GAIN,//增益
    BUTTON_AE_RECORDER,  //录音启停
    BUTTON_MORE_CONFIG,//更多配置

    BUTTON_AE_TRIGGER_VALUE,//触发值
    BUTTON_AE_VOLUME,//设置音量
    BUTTON_AE_SPECTRUM,//频率成分
    BUTTON_AE_UNIT,//单位
    BUTTON_AE_CHANNEL,//通道
    BUTTON_AE_FILTER,//带宽模式
    BUTTON_AE_RFID_SAVE,//RFID扫描保存
    BUTTON_AE_LOAD_RECORD_DATA, //载入录音数据
    BUTTON_AE_DELETE_RECORD_DATA, //删除录音数据
    BUTTON_AE_LOAD_DATA,//载入数据
    BUTTON_AE_DELETE_DATA,//删除数据
    BUTTON_AE_RESTORE_DEFAULT,//恢复默认参数
    BUTTON_AE_OTHER,//其他
}AEAmpButton;

//采集模式
const ButtonInfo::RadioValueConfig s_AEModeCfg =
{
    AE::TEXT_MODE_OPTIONS, sizeof(AE::TEXT_MODE_OPTIONS)/sizeof(char*)
};

//增益
const ButtonInfo::RadioValueConfig s_AEGainCfg =
{
    AE::TEXT_GAIN_OPTIONS, sizeof(AE::TEXT_GAIN_OPTIONS)/sizeof(char*)
};

//单位
const ButtonInfo::RadioValueConfig s_AEUnitCfg =
{
    AE::TEXT_UNITS, sizeof(AE::TEXT_UNITS)/sizeof(char*)
};

//触发值
const ButtonInfo::RadioValueConfig s_AETriggerCfg =
{
    NULL, AE::TRIGGER_LEVEL_COUNT
};

//带宽模式
const ButtonInfo::RadioValueConfig s_AEFilterCfg =
{
    AE::TEXT_FILTER_OPTIONS, sizeof(AE::TEXT_FILTER_OPTIONS)/sizeof(char*)
};

//通道
const ButtonInfo::RadioValueConfig s_AEChannel=
{
    AE::TEXT_CHANNELS, sizeof(AE::TEXT_CHANNELS)/sizeof(char*)
};

//频率成分
const ButtonInfo::SliderValueConfig s_AESpectrumCfg =
{
    AE::SPECTRUM_MIN, AE::SPECTRUM_MAX, AE::SPECTRUM_STEP
};

//控制按钮定义
const ButtonInfo::Info s_AEButtonInfo[] =
{
    { BUTTON_AE_SAMPLE_MODE, { ButtonInfo::RADIO, AE::TEXT_MODE, NULL, ":/images/sampleControl/sampleMode.png", &s_AEModeCfg } },//模式
    { BUTTON_AE_SINGLE_SAMPLE, { ButtonInfo::COMMAND, AE::TEXT_SINGLE_SAMPLE, NULL, ":/images/sampleControl/sample.png", NULL } },//单次采样
    { BUTTON_AE_TRIGGER_VALUE, {ButtonInfo::RADIO, AE::TEXT_TRIGGER_VALUE, NULL, ":/images/sampleControl/triggerValue.png", &s_AETriggerCfg } },//触发值
    { BUTTON_AE_NOISE_TEST, { ButtonInfo::COMMAND, AE::TEXT_NOISE_TEST, NULL, ":/images/sampleControl/noiseTest.png", NULL } },//背景噪声测试
    //{ BUTTON_AE_CLEAR_NOISE, { ButtonInfo::COMMAND, AE::TEXT_CLEAR_NOISE, NULL, ":/images/sampleControl/noiseClean.png", NULL } },//清除背景噪声
    { BUTTON_AE_SAVE_DATA, { ButtonInfo::COMMAND, AE::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL } },//保存数据
    { BUTTON_AE_GAIN, { ButtonInfo::RADIO, AE::TEXT_GAIN, NULL, ":/images/sampleControl/gain.png", &s_AEGainCfg } },//增益
    { BUTTON_AE_UNIT, { ButtonInfo::RADIO, AE::TEXT_UNIT, NULL, ":/images/sampleControl/unit.png", &s_AEUnitCfg } },//单位
    { BUTTON_MORE_CONFIG, { ButtonInfo::COMMAND, AE::TEXT_MORE_CONFIG, NULL, ":/images/sampleControl/moreconfig.png", NULL } },//更多配置
};

//"更多.."按钮定义
const ButtonInfo::Info g_AEVolumeBtn = {BUTTON_AE_VOLUME, {ButtonInfo::FIXED_STEP_SLIDER, AE::TEXT_AE_VOLUME, NULL, "", &AE::g_VolumeCfg}};//音量
const ButtonInfo::Info g_AESpectrumBtn = {BUTTON_AE_SPECTRUM, {ButtonInfo::FIXED_STEP_SLIDER, AE::TEXT_SPECRUM, AE::TEXT_HZ, ":/images/sampleControl/freqCompent.png", &s_AESpectrumCfg}};//频率成分
//const ButtonInfo::Info g_AEUnitBtn = {BUTTON_AE_UNIT, {ButtonInfo::RADIO, AE::TEXT_UNIT, NULL, ":/images/sampleControl/unit.png", &s_AEUnitCfg}};//单位
const ButtonInfo::Info g_AEChannelBtn = {BUTTON_AE_CHANNEL, {ButtonInfo::RADIO, AE::TEXT_CHANNEL, NULL, "", &s_AEChannel}};//通道
const ButtonInfo::Info g_AEFilterBtn = {BUTTON_AE_FILTER, {ButtonInfo::RADIO, AE::TEXT_FILTER, NULL, "", &s_AEFilterCfg}};//带宽模式;
const ButtonInfo::Info g_AERFIDSaveBtn = {BUTTON_AE_RFID_SAVE, {ButtonInfo::COMMAND, AE::TEXT_RFID_SAVE, NULL, ":/images/sampleControl/save.png", NULL}};//RFID扫描保存
const ButtonInfo::Info g_AELoadRecordDataBtn = {BUTTON_AE_LOAD_RECORD_DATA, {ButtonInfo::COMMAND, AE::TEXT_LOAD_RECORD_DATA, NULL, ":/images/sampleControl/importData.png", NULL}};//载入录音数据
const ButtonInfo::Info g_AEDeleteRecordDataBtn = {BUTTON_AE_DELETE_RECORD_DATA, {ButtonInfo::COMMAND, AE::TEXT_DELETE_RECORD_DATA, NULL, ":/images/sampleControl/triggerValue.png", NULL}};//删除录音数据
const ButtonInfo::Info g_AELoadDataBtn = {BUTTON_AE_LOAD_DATA, {ButtonInfo::COMMAND, AE::TEXT_LOAD_DATA, NULL, ":/images/sampleControl/importData.png", NULL}};//载入数据
const ButtonInfo::Info g_AEDeleteDataBtn = {BUTTON_AE_DELETE_DATA, {ButtonInfo::COMMAND, AE::TEXT_DELETE_DATA, NULL, ":/images/sampleControl/triggerValue.png", NULL}};//删除数据
const ButtonInfo::Info g_AERecordBtn = {BUTTON_AE_RECORDER, { ButtonInfo::COMMAND, AE::TEXT_START_RECORD, NULL, ":/images/sampleControl/record.png", NULL}};//录音启停
const ButtonInfo::Info g_AERestoreDataBtn = {BUTTON_AE_RESTORE_DEFAULT, {ButtonInfo::COMMAND, AE::TEXT_RESTORE_DEFAULT, NULL, ":/images/sampleControl/restoreDefault.png", NULL}};//恢复默认
const ButtonInfo::Info g_AEOtherBtn = {BUTTON_AE_OTHER, {ButtonInfo::COMMAND, AE::TEXT_OTHERS, NULL, ":/images/sampleControl/restoreDefault.png", NULL}};//其他
}

const qint32 AE_AMP_DATA_COUNT  = 8;
const quint8 AE_AMP_SYNC_SUCCESS  = 1;
/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
AEAmpView::AEAmpView( const QString& strTitle, QWidget *parent )
    :AEAmpViewBase( strTitle, parent )
{
    initBtnBarInfo();

    //初始化使用的数据
    initDatas();

    //新建图谱
    m_pChart = createChart(parent);
    setChart( m_pChart );

    //触发值列表根据增益、量纲动态变化
    resetTriggerList( m_eGain, m_eUnit );

    //设置数据
    setButtonBarDatas();

    setChartDatas();

    setWorkMode();

    //设置工作参数
    setAllWorkingSet();

    MultiServiceNS::SampleUser user;
    user.eSampleType = MultiServiceNS::SPECTRUM_AE_AMP;
    addUser(user);

    //启动采集
    startSample();

    //消除噪音，使得在特定回放情形不显示噪音
    clearNoise();

    //设置音量
    RecordPlayService::instance()->setVolume(m_ucVolume);

    connect(RecordPlayService::instance(), SIGNAL(sigSysVolume(quint8)), this, SLOT(onSysVolume(quint8)));
}

/*************************************************
功能： 析构
*************************************************************/
AEAmpView::~AEAmpView( )
{
    disconnect(RecordPlayService::instance(), SIGNAL(sigSysVolume(quint8)), this, SLOT(onSysVolume(quint8)));
    saveConfig();//存储到配置文件中
    stopSample();

    if(m_pMoreBtnInfo)
    {
        delete [] m_pMoreBtnInfo;
        m_pMoreBtnInfo = NULL;
    }
}

/*************************************************
功能： 启动采样
*************************************************************/
void AEAmpView::startSample()
{
    startSampleService();
}

/*************************************************
功能： 停止采样
*************************************************************/
void AEAmpView::stopSample()
{
    stopSampleService();
}

/*****************************************
 * 功能：初始化按钮栏信息
 * **********************************************/
void AEAmpView::initBtnBarInfo()
{
    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar(AE::CONTEXT, aeampview::s_AEButtonInfo, sizeof(aeampview::s_AEButtonInfo) / sizeof(ButtonInfo::Info));
    ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_SAMPLE_MODE)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_UNIT)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_GAIN)))->setPopupMode(PopupWidget::SWITCH_MODE);

    m_pMoreBtnInfo = NULL;
    m_qui8MoreBtnCnt = 0;
    m_pOtherBtnInfo = NULL;
    m_qui8OtherBtnCnt = 0;

    QVector<ButtonInfo::Info> qvtMoreBtnInfos;
    qvtMoreBtnInfos.clear();

    //qvtMoreBtnInfos.push_back(aeampview::g_AETriggerBtn);
    qvtMoreBtnInfos.push_back(aeampview::g_AEVolumeBtn);
    qvtMoreBtnInfos.push_back(aeampview::g_AESpectrumBtn);
    //qvtMoreBtnInfos.push_back(aeampview::g_AEUnitBtn);

    FuncConfigManagerNS::FunctionInfo stAEWirelessInfo;
    stAEWirelessInfo.iFuncID = FuncConfigManagerNS::AE_WIRELESS;
    stAEWirelessInfo.iParentID = FuncConfigManagerNS::AE;

    FuncConfigManagerNS::ConfigInfo stConfigInfo;
    FuncCfgSrvManager::instance()->getConfigInfo(stConfigInfo);
    int iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAEWirelessInfo);
    if(0 <= iIndex && iIndex < stConfigInfo.qvtFuncInfos.size())
    {
        if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
        {
            qvtMoreBtnInfos.push_back(aeampview::g_AEChannelBtn);
        }
    }

#ifdef _R3_DEFINED_
    qvtMoreBtnInfos.push_back(aeampview::g_AEFilterBtn);
#endif
    qvtMoreBtnInfos.push_back(aeampview::g_AERFIDSaveBtn);
    qvtMoreBtnInfos.push_back(aeampview::g_AELoadRecordDataBtn);
    //qvtMoreBtnInfos.push_back(aeampview::g_AEDeleteRecordDataBtn);
    qvtMoreBtnInfos.push_back(aeampview::g_AELoadDataBtn);
    //qvtMoreBtnInfos.push_back(aeampview::g_AEDeleteDataBtn);
    qvtMoreBtnInfos.push_back(aeampview::g_AERecordBtn);
    qvtMoreBtnInfos.push_back(aeampview::g_AERestoreDataBtn);
    qvtMoreBtnInfos.push_back(aeampview::g_AEOtherBtn);
    m_qui8MoreBtnCnt = static_cast<quint8>(qvtMoreBtnInfos.size());
    m_pMoreBtnInfo = new ButtonInfo::Info[m_qui8MoreBtnCnt];
    for (int i = 0; i < m_qui8MoreBtnCnt; ++i)
    {
        m_pMoreBtnInfo[i] = qvtMoreBtnInfos[i];
    }

    // 创建更多设置栏
    LabelButtonBar* pMoreConfigButtonBar = createMoreConfigButtonBar(AE::CONTEXT, m_pMoreBtnInfo, m_qui8MoreBtnCnt);

    QVector<ButtonInfo::Info> qvtOtherBtnInfos;
    qvtOtherBtnInfos.clear();
    //qvtOtherBtnInfos.push_back(aeampview::g_AESpectrumBtn); //频率成分
    //qvtOtherBtnInfos.push_back(aeampview::g_AERFIDSaveBtn);  //RFID保存
    qvtOtherBtnInfos.push_back(aeampview::g_AEDeleteRecordDataBtn); //删除录音数据
    qvtOtherBtnInfos.push_back(aeampview::g_AEDeleteDataBtn);       //删除数据
    m_qui8OtherBtnCnt = static_cast<quint8>(qvtOtherBtnInfos.size());
    m_pOtherBtnInfo = new ButtonInfo::Info[m_qui8OtherBtnCnt];
    for (int i = 0; i < m_qui8OtherBtnCnt; ++i)
    {
        m_pOtherBtnInfo[i] = qvtOtherBtnInfos[i];
    }

    //创建其他设置栏
    m_pOtherConfigView = createLabelButtonBar(AE::CONTEXT, m_pOtherBtnInfo, m_qui8OtherBtnCnt, AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_OTHERS));
    if(m_pOtherConfigView)
    {
        m_pOtherConfigView->resize(Window::WIDTH, Window::HEIGHT);
        //m_pOtherConfigView->attach(pButtonBar);//做挂接
    }
    return;
}

/*************************************************
功能： 初始化参数
*************************************************************/
void AEAmpView::initParameters()
{
    int iGroup = AE::GROUP_AE_AMPLITUDE;
    m_pConfig = ConfigManager::instance()->config();
    m_pConfig->beginGroup( Module::GROUP_AE );

    m_eGain = (AE::GainType)m_pConfig->value( AE::KEY_GAIN ).toUInt();
    m_eFilter = (AEFilter) m_pConfig->value(AE::KEY_FILTER).toUInt();
    m_eSampleMode = (AE::SampleMode)m_pConfig->value( AE::KEY_SAMPLE_MODE, iGroup ).toUInt();
    m_eTriggerValue = (AE::TriggerValue)m_pConfig->value( AE::KEY_TRIGGER_VALUE, iGroup ).toUInt();
    m_eUnit = (AE::UnitOption)m_pConfig->value( AE::KEY_UNIT, iGroup ).toUInt();
    m_usSpectrum = m_pConfig->value( AE::KEY_FREQ_COMPONENT, iGroup ).toUInt();

    if(m_eChannel != AE::SURFACE_MOUNT)
    {
        m_eChannel = (AE::ChannelType)m_pConfig->value( AE::KEY_CHANNEL_TYPE ).toUInt();
    }
    m_pConfig->endGroup();

    m_pConfig->beginGroup( Module::GROUP_APP );
    m_ucVolume = m_pConfig->value(APPConfig::KEY_SYSTEM_VOLUME).toUInt();
    m_pConfig->endGroup();

    m_bTriggered = false;
    m_eNoiseTestState = NOISE_TEST_STATE;
}


/*************************************************
功能： 初始化数据
*************************************************************/
void AEAmpView::initDatas()
{
    initParameters();
}

/*************************************************
功能： 设置表格数据
*************************************************************/
void AEAmpView::setChartDatas()
{
    m_pChart->setUnit( m_eUnit );
    m_pChart->setGain( m_eGain );
    m_pChart->setChannel( m_eChannel );
    m_pChart->setSpectrum( m_usSpectrum );
    m_pChart->setSampleMode( m_eSampleMode );
    m_pChart->setTriggered( m_bTriggered );
}

/*************************************************
功能： 设置工作模式
*************************************************************/
void AEAmpView::setWorkMode()
{
    AEAmpService* pService = getAEAmpService();
    if(NULL != pService)
    {
        pService->transaction();
        pService->setWorkMode( AE::MODE_AMPLITUDE );
    }
    else
    {
        qWarning() << "AEAmpView::setWorkMode: error, service handle is NULL!";
    }
}

/*************************************************
功能： 设置所有工作参数
*************************************************************/
void AEAmpView::setAllWorkingSet()
{
    AEAmpService* pService = getAEAmpService();
    if(pService)
    {
        pService->transaction();
        //m_pService->setWorkMode( AE::MODE_AMPLITUDE );
        pService->setGain( m_eGain );
        pService->setFilter(m_eFilter);
        pService->setTriggerValue( m_eTriggerValue );
        pService->setUnit( m_eUnit );
        pService->setSpectrum( m_usSpectrum );
        pService->setSyncSource( Module::SYNC_SOURCE_DEFAULT );
        pService->commit();

        pService->setChannel(m_eChannel);
    }
    else
    {
        qWarning() << "AEAmpView::setWorkingSet: error, service handle is NULL!";
    }
}

/*************************************************
功能： 初始化图谱状态标识
*************************************************************/
void AEAmpView::initChartState( void )
{
    m_pChart->clear();
    m_bTriggered = false;
    m_pChart->setTriggered( m_bTriggered );         // 设置触发状态
    m_pChart->setGainStatus( AE::GAIN_STATUS_NONE );// 清除增益状态
    return;
}

AEAmpChart *AEAmpView::createChart(QWidget *parent)
{
    Q_UNUSED(parent)
    AEAmpChart *pChart = new AEAmpChart;
    return pChart;
}

/*************************************************
功能： 保存设置
*************************************************************/
bool AEAmpView::saveConfig()
{
    int iGroup = AE::GROUP_AE_AMPLITUDE;
    m_pConfig->beginGroup( Module::GROUP_AE );

    m_pConfig->setValue( m_eGain, AE::KEY_GAIN );
    m_pConfig->setValue( m_eFilter, AE::KEY_FILTER );
    m_pConfig->setValue( m_eSampleMode, AE::KEY_SAMPLE_MODE, iGroup );
    m_pConfig->setValue( m_eTriggerValue, AE::KEY_TRIGGER_VALUE, iGroup );
    m_pConfig->setValue( m_eUnit, AE::KEY_UNIT, iGroup );
    m_pConfig->setValue( m_usSpectrum, AE::KEY_FREQ_COMPONENT, iGroup );
    if(m_eChannel != AE::SURFACE_MOUNT)
    {
        m_pConfig->setValue( m_eChannel, AE::KEY_CHANNEL_TYPE );
    }
    m_pConfig->endGroup();

    return true;
}

/*************************************************
功能： 槽，响应读取的数据
输入参数：
        data -- 幅值数据
*************************************************************/
void AEAmpView::onDataRead(AE::AmplitudeData data, MultiServiceNS::USERID userId)
{
    if((getUserId() != INVALID_USER) && (getUserId() == userId) && isSampling())
    {
        if( RECORD_NOISE_STATE != m_eNoiseTestState )
        {
            if( AE::SAMPLEMODE_SINGLE == m_eSampleMode )
            {
                //单次模式要检查是否触发
                if( !m_bTriggered )
                {
                    m_bTriggered = isTriggered( data );
                    if( m_bTriggered )
                    {
                        m_pChart->setTriggered( m_bTriggered );//设置触发状态和数据
                        addDataToChart( data );  // 将数据添加到图谱
                    }
                }
            }
            else
            {
                //连续模式直接刷新数据
                if (AE::transformChannelFromAPI(data.eAE_CHANNEL) == m_eChannel)
                {
                    addDataToChart( data );  // 将数据添加到图谱
                }
            }
        }
        else
        {
            addDataToChart( data );
        }
    }
    else
    {
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
    }
    return;
}

/*************************************************
入参： data -- 幅值数据
功能： 添加数据到图谱
*************************************************************/
void AEAmpView::addDataToChart( const AE::AmplitudeData &data )
{
    m_pChart->setData( data );
    m_data = data;
    updateGainStatus( data.fPeakValue );

    // 现在ae支持单条数据诊断，将ae单次采样不诊断的逻辑去掉
    diagDataInfo(data);

    return;
}

/*************************************************
功能： 槽，响应诊断结果
输入参数：
        qspDiagResultInfo -- 诊断结果
*************************************************************/
void AEAmpView::onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo)
{
    if(qspDiagResultInfo.data())
    {
        DiagConfig::DiagDisplayInfo stDiagInfo;
        stDiagInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(qspDiagResultInfo.data()->stDiagRet.defectLevel);
        stDiagInfo.qstrPDDesInfo = qspDiagResultInfo.data()->qstrPDDescription;
        stDiagInfo.qstrPDSignalInfos = qspDiagResultInfo.data()->qstrPDSignalTypeInfos;

        if(m_pChart)
        {
            m_pChart->setDiagRet(stDiagInfo);
        }
    }

    return;
}

/*************************************************
功能： 获取数据增益状态
输入参数：
        fValue -- 幅值
*************************************************************/
AE::GainStatus AEAmpView::getGainStatus( float fValue )
{
    float fGainLowThreshold = AE::AE_LOWGAIN_THRESHOLDS[ m_eUnit ][ m_eGain ];
    float fGainHighThreshold = AE::AE_HIGHGAIN_THRESHOLDS[ m_eUnit ][ m_eGain ];

    AE::GainStatus eGainStatus = AE::GAIN_STATUS_NONE;

    if( fValue < fGainLowThreshold )//偏低
    {
        eGainStatus = AE::GAIN_STATUS_LOW;
    }
    else if( fValue > fGainHighThreshold )//偏高
    {
        eGainStatus = AE::GAIN_STATUS_HIGH;
    }
    return eGainStatus;
}


/*************************************************
功能： 更新数据增益状态
输入参数：
        fValue -- 幅值
*************************************************************/
void AEAmpView::updateGainStatus( float fValue )
{
    //检查增益状态（偏高？偏低？）
    AE::GainStatus eGainStatus = getGainStatus( fValue );
    m_pChart->setGainStatus( eGainStatus );//设置增益状态
}

/*************************************************
功能： 槽，响应通道变化
输入参数：
        eChannel -- 变化后的通道
*************************************************************/
void AEAmpView::onChannelChanged( AE::ChannelType eChannel )
{
    if( m_eChannel != eChannel )
    {
        m_eChannel = eChannel;
        m_pChart->setChannel( eChannel );
    }
}

/*************************************************
功能： 槽，响应录音完成
输入参数：
        strRecordFilePath -- 录音文件保存的路径
            uint -- 录音时长
*************************************************************/
void AEAmpView::onRecorded(const QString& strRecordFilePath, const uint uiRecordingTime)
{
    AERecordDataInfo sSaveDataInfo;
    sSaveDataInfo.stHeadInfo.strSubstationName = "";
    sSaveDataInfo.stHeadInfo.strDeviceName = "";
    sSaveDataInfo.stHeadInfo.generationDateTime = QDateTime::currentDateTime();
    sSaveDataInfo.stHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_UNRECORD;

    sSaveDataInfo.stHeadInfo.eCode = DataFileNS::SPECTRUM_CODE_RECORD_AE;
    sSaveDataInfo.stHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;
    sSaveDataInfo.stHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;
    sSaveDataInfo.stHeadInfo.ucTestChannelSign = (m_eChannel == AE::WIRELESS) ? 1 : 0;

    sSaveDataInfo.uiDuration = uiRecordingTime;
    sSaveDataInfo.eGainType = DataFileNS::GAIN_TYPE_AMPLIFY_RATIO;
    sSaveDataInfo.sGain =  AE::g_ausGainValues[m_eGain];

    sSaveDataInfo.eSyncSource = DataFileNS::SYNC_SOURCE_POWER;
    sSaveDataInfo.fSyncFreq = m_usSpectrum;
    sSaveDataInfo.eTransformerType = AE::transformChannelToDataFile(m_eChannel);

    sSaveDataInfo.strFilePath = strRecordFilePath;

    QFileInfo qfDataInfo(strRecordFilePath);
    QString qstrFileName = qfDataInfo.fileName();
    qstrFileName = qstrFileName.left(qstrFileName.lastIndexOf('.'));

    AERecordDataSave hDataSave;
    QString qstrDataFile = hDataSave.saveData(&sSaveDataInfo, qfDataInfo.absolutePath(), qstrFileName);

    QFileInfo fileInfo(qstrDataFile);
    QString strFileName = fileInfo.fileName();
    QString strInfo = qstrDataFile.isEmpty() ? QObject::trUtf8("Save failure!") : strFileName;

    processTooLongMsgText(strInfo);
    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x() + pBtnBar->width() / 2);
    centerPoint.setY(pBtnBar->pos().y() + pBtnBar->height() / 2);
    MsgBox::informationWithoutAutoAccept("", strInfo, centerPoint);
    return;
}

/*************************************************
功能： 槽，响应系统音量变化
输入参数：
        qui8Volume -- 系统音量
*************************************************************/
void AEAmpView::onSysVolume(quint8 qui8Volume)
{
    if(m_ucVolume != qui8Volume)
    {
        m_ucVolume = qui8Volume;
        ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_VOLUME)))->setValue(m_ucVolume);
    }

    return;
}

/*************************************************
功能： 判断是否满足触发条件（单次），最大值大于门槛值
输入参数：
        data -- 采集的数据
*************************************************************/
bool AEAmpView::isTriggered( const AE::AmplitudeData& data )
{
    float fThreshold = AE::getThreshold( m_eUnit, m_eGain, m_eTriggerValue );
    //logInfo(QString("fPeakValue %1 fThreshold %2").arg(data.fPeakValue).arg(fThreshold));
    return (data.fPeakValue >= fThreshold); //修改为大于等于阈值就触发
}

/*************************************************
功能： 设置按钮栏数据
*************************************************************/
void AEAmpView::setButtonBarDatas()
{
    ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_SAMPLE_MODE)))->setValue( m_eSampleMode );
    ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_GAIN)))->setValue( m_eGain );
    ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_UNIT)))->setValue( m_eUnit );
    ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_TRIGGER_VALUE)))->setValue( m_eTriggerValue );
    ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_SPECTRUM)))->setValue( m_usSpectrum );

    if(((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_FILTER))))
    {
        ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_FILTER)))->setValue(m_eFilter);
    }

    int iChannelVal = (AE::WIRELESS == m_eChannel) ? (AE::WIRELESS - 1) : AE::AIR_SOUND;
    if(((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_CHANNEL))))
    {
        ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_CHANNEL)))->setValue(iChannelVal);
    }

    if( m_eSampleMode == AE::SAMPLEMODE_CONTINUOUS)
    {
        buttonBar()->button(aeampview::BUTTON_AE_SINGLE_SAMPLE)->setDisabled(true);
        buttonBar()->button(aeampview::BUTTON_AE_TRIGGER_VALUE)->setDisabled(true);
    }
    else
    {
        buttonBar()->button(aeampview::BUTTON_AE_SINGLE_SAMPLE)->setDisabled(false);
    }

    ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_VOLUME)))->setValue(m_ucVolume);

    return;
}


/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void AEAmpView::onButtonValueChanged( int id, int iValue )
{
    switch( id )
    {
    case aeampview::BUTTON_AE_SAMPLE_MODE://采样模式
    {
        m_eSampleMode = (AE::SampleMode)iValue;
        m_pChart->setSampleMode( m_eSampleMode );//设置采样模式
        if( m_eSampleMode == AE::SAMPLEMODE_SINGLE)
        {
            m_pChart->clearDiagRet();
            buttonBar()->button(aeampview::BUTTON_AE_SINGLE_SAMPLE)->setDisabled(false);
            buttonBar()->button(aeampview::BUTTON_AE_TRIGGER_VALUE)->setDisabled(false);
        }
        else
        {
            buttonBar()->button(aeampview::BUTTON_AE_SINGLE_SAMPLE)->setDisabled(true);
            buttonBar()->button(aeampview::BUTTON_AE_TRIGGER_VALUE)->setDisabled(true);
        }
        if( RECORD_NOISE_STATE != m_eNoiseTestState )//add by bienew 2018.4.17,do nothing when noise test
        {
            initChartState();  // 初始化图谱状态
        }
    }
        break;
    case aeampview::BUTTON_AE_GAIN://增益
    {
        m_eGain = (AE::GainType)iValue;
        if (AEAmpService* pService = getAEAmpService())
        {
            pService->setGain( m_eGain );
        }
        m_pChart->setGain( m_eGain );
        resetTriggerList( m_eGain, m_eUnit );//增益变化时，重置触发值列表
        initChartState();  // 初始化图谱状态
        clearNoise();      // 清除噪声
    }
        break;
    case aeampview::BUTTON_AE_UNIT://量纲
    {
        m_eUnit = (AE::UnitOption)iValue;
        if (AEAmpService* pService = getAEAmpService())
        {
            pService->setUnit( m_eUnit );
        }
        m_pChart->setUnit( m_eUnit );
        resetTriggerList( m_eGain, m_eUnit );//量纲变化时，重置触发值列表
        initChartState();  // 初始化图谱状态
        clearNoise();      // 清除噪声
    }
        break;
    case aeampview::BUTTON_AE_SPECTRUM://频率成分
    {
        m_usSpectrum = iValue;
        if (AEAmpService* pService = getAEAmpService())
        {
            pService->setSpectrum( m_usSpectrum );
        }
        m_pChart->setSpectrum( m_usSpectrum );
        initChartState();  // 初始化图谱状态
    }
        break;
    case aeampview::BUTTON_AE_VOLUME://音量
    {
        if((int)m_ucVolume != iValue)
        {
            m_ucVolume = (quint8)iValue;
            RecordPlayService::instance()->setVolume(m_ucVolume);

            ConfigInstance *pConfig = ConfigManager::instance()->config();
            pConfig->beginGroup(Module::GROUP_APP);
            pConfig->setValue(iValue, APPConfig::KEY_SYSTEM_VOLUME);
            pConfig->endGroup();
        }
    }
        break;
    case aeampview::BUTTON_AE_TRIGGER_VALUE://触发值
    {
        m_eTriggerValue = (AE::TriggerValue)iValue;
        if (AEAmpService* pService = getAEAmpService())
        {
            pService->setTriggerValue( m_eTriggerValue );
        }
        initChartState();  // 初始化图谱状态
    }
        break;
    case aeampview::BUTTON_AE_CHANNEL://通道
    {
        //给底层通道的参数， 空声表贴都是同样处理的方式，但底层会返回真实采集的空声或者表贴，采集界面将其显示出来
        stopSample();

        if (AEAmpService* pService = getAEAmpService())
        {
            if( iValue == 1)
            {
                pService->setChannel( AE::WIRELESS );
                m_eChannel = AE::WIRELESS;
                m_pChart->setChannel( m_eChannel );
            }
            else
            {
                pService->setChannel( AE::AIR_SOUND );
                m_eChannel = AE::AIR_SOUND;
                m_pChart->setChannel( m_eChannel );
            }
        }

        startSample();
        m_pChart->clear();

        //add by bienew 2018.4.17,set trigger false when channel changed
        m_bTriggered = false;
        m_pChart->setTriggered( m_bTriggered );

    }
        break;
    case aeampview::BUTTON_AE_FILTER://带宽
    {
        m_eFilter = (AEFilter)iValue;
        if (AEAmpService* pService = getAEAmpService())
        {
            pService->setFilter(m_eFilter);
        }
        initChartState();  // 初始化图谱状态
        clearNoise();      // 清除噪声
    }
        break;
    default:
        break;
    }
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void AEAmpView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case aeampview::BUTTON_MORE_CONFIG://更多设置
    {
        showMoreConfigButtonBar();
    }
        break;
//    case aeampview::BUTTON_AE_CLEAR_NOISE://清除背景噪声
//    {
//        buttonBar()->button(aeampview::BUTTON_AE_CLEAR_NOISE)->setDisabled(true);
//        buttonBar()->button(aeampview::BUTTON_AE_CLEAR_NOISE)->setActive(false);
//        buttonBar()->button(aeampview::BUTTON_AE_NOISE_TEST)->setActive(true);
//        setCurActiveBtnID(aeampview::BUTTON_AE_NOISE_TEST);
//        clearNoise();
//        if((AE::SAMPLEMODE_SINGLE == m_eSampleMode) && (!m_bTriggered))
//        {
//            m_pChart->clear();
//        }
//    }
//        break;
    case aeampview::BUTTON_AE_SAVE_DATA://保存数据
    {
        pressSaveData();
    }
        break;
    case aeampview::BUTTON_AE_NOISE_TEST://背景噪声测试
    {
        if (NOISE_TEST_STATE == m_eNoiseTestState)
        {
            // 未开始测试，进入测试模式
            m_eNoiseTestState = RECORD_NOISE_STATE;
            m_bTriggered = false;
            this->buttonBar()->button(aeampview::BUTTON_AE_NOISE_TEST)->setTitle( AE_TRANSLATE(AE::TEXT_RECORD_NOISE) );
            m_pChart->startNoiseTest();
        }
        else if (RECORD_NOISE_STATE == m_eNoiseTestState)
        {
            m_eNoiseTestState = CLEAR_NOISE_STATE;
            this->buttonBar()->button(aeampview::BUTTON_AE_NOISE_TEST)->setTitle( AE_TRANSLATE(AE::TEXT_CLEAR_NOISE) );
            // 设置噪声数据
            m_pChart->setNoiseData( m_data );
            m_pChart->stopNoiseTest();
            m_bTriggered = false;
            m_pChart->setTriggered( m_bTriggered );         // 设置触发状态
        }
        else if (CLEAR_NOISE_STATE == m_eNoiseTestState)
        {
            m_eNoiseTestState = NOISE_TEST_STATE;
            this->buttonBar()->button(aeampview::BUTTON_AE_NOISE_TEST)->setTitle( AE_TRANSLATE(AE::TEXT_NOISE_TEST) );
            clearNoise();
            if((AE::SAMPLEMODE_SINGLE == m_eSampleMode) && (!m_bTriggered))
            {
                m_pChart->clear();
            }
        }
    }
        break;
    case aeampview::BUTTON_AE_SINGLE_SAMPLE://单次采样
    {
        if( AE::SAMPLEMODE_SINGLE == m_eSampleMode ) // 连续模式下点击单次采样不生效
        {
            // 清除诊断结果
            m_pChart->clearDiagRet();
            initChartState(); // 初始化图谱状态
        }
    }
        break;
    case aeampview::BUTTON_AE_RESTORE_DEFAULT://恢复默认参数
    {
        restoreDefault();
    }
        break;
    case aeampview::BUTTON_AE_RECORDER://录音
    {
        //recorder();
        if(m_bRecording)
        {
            //本次为停止录音
            if(stopAERecord())
            {
                this->buttonBar()->button(aeampview::BUTTON_AE_RECORDER)->setTitle(AE_TRANSLATE(AE::TEXT_START_RECORD));

                //停止采集
                stopSample();

                saveRecorded(m_qstrAttachPath, m_uiTotalRecordingTime);

                //开始采集
                startSample();
            }
        }
        else
        {
            //本次为开始录音
            if(startAERecord(RecordPlay::AE_AMP))
            {
                //this->buttonBar()->button(aeampview::BUTTON_AE_RECORDER)->setTitle(AE_TRANSLATE(AE::TEXT_STOP_RECORD));
                m_uiTotalRecordingTime = 0;
            }
        }
    }
        break;
    case aeampview::BUTTON_AE_LOAD_RECORD_DATA://载入录音数据
    {
        stopSample();

        loadRecordData();

        startSample();
    }
        break;
    case aeampview::BUTTON_AE_DELETE_RECORD_DATA://删除录音数据
    {
        stopSample();

        deleteRecordData();

        startSample();
    }
        break;
    case aeampview::BUTTON_AE_LOAD_DATA://载入数据
    {
        stopSample();

        loadData();

        startSample();
    }
        break;
    case aeampview::BUTTON_AE_DELETE_DATA://删除数据
    {
        stopSample();

        deleteData();

        startSample();
    }
        break;
    case aeampview::BUTTON_AE_RFID_SAVE://RFID扫描保存
    {
        //停止采集
        stopSample();

        RFIDSaveData();

        //开始采集
        startSample();
    }
        break;
    case aeampview::BUTTON_AE_OTHER://其他设置
    {
        m_pOtherConfigView->resize( Window::WIDTH, Window::HEIGHT );
        m_pOtherConfigView->show();
    }
        break;
    default:
        break;
    }
}

/*************************************************
功能： 触发值按钮的触发值列表根据增益、量纲动态变化
输入参数：
        eGain -- 增益
        eUnit -- 量纲
*************************************************************/
void AEAmpView::resetTriggerList( AE::GainType eGain, AE::UnitOption eUnit )
{
    QStringList listTrigger = AE::getTriggerList( eUnit, eGain );

    RadioButton* pButTrigger = (RadioButton*)( this->buttonBar()->button(aeampview::BUTTON_AE_TRIGGER_VALUE) );
    if( NULL != pButTrigger )
    {
        pButTrigger->setOptionList( listTrigger );
    }
    else
    {
        qWarning() << "AEAmpView::resetTriggerList: error, button is NULL!";
    }

    updateTriggerDefaultValue();
}

/*************************************************
功能： 清除噪声，包括噪声数据部分
*************************************************************/
void AEAmpView::clearNoise( void )
{
    if (CLEAR_NOISE_STATE == m_eNoiseTestState)
    {
        m_eNoiseTestState = NOISE_TEST_STATE;
        this->buttonBar()->button(aeampview::BUTTON_AE_NOISE_TEST)->setTitle( AE_TRANSLATE(AE::TEXT_NOISE_TEST) );
    }
    m_pChart->clearNoise();
}

/*************************************************
功能： 保存数据
输入参数：
        stationName -- 变电站名
        deviceName -- 设备名
返回：
        保存后的文件名
*************************************************************/
QString AEAmpView::saveDataToFile()
{
    // 弹出文件备注框
    showFileCommentBox();

    //保存的时候图谱上数据先诊断，然后一起保存
    AE::AmplitudeData stData;
    if(m_pChart)
    {
        stData = m_pChart->data();
    }

    diagDataInfo(stData, true);

    AEAmpDataInfo sSaveDataInfo;
    sSaveDataInfo.stHeadInfo.strSubstationName = m_qstrStationName;
    sSaveDataInfo.stHeadInfo.strDeviceName = m_qstrDeviceName;
    sSaveDataInfo.stHeadInfo.strDeviceNumber = m_qstrDeviceNumber;
    sSaveDataInfo.stHeadInfo.generationDateTime = QDateTime::currentDateTime();
    sSaveDataInfo.stHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_UNRECORD;

    sSaveDataInfo.stHeadInfo.eCode = DataFileNS::SPECTRUM_CODE_AE_AMP;
    sSaveDataInfo.stHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;
    sSaveDataInfo.stHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;
    sSaveDataInfo.stHeadInfo.ucTestChannelSign = (m_eChannel == AE::WIRELESS) ? 1 : 0;
    sSaveDataInfo.stHeadInfo.ePDDefectLevel = static_cast<DataFileNS::PDDefectLevel>(m_pChart->getPDDiagnosisRet());
    sSaveDataInfo.stHeadInfo.qstrPDSignalTypeInfos = m_pChart->getPDSignalInfos();
    sSaveDataInfo.stHeadInfo.qstrRemark = m_qstrRemark;

    dbg_info("sSaveDataInfo.stHeadInfo.ucTestChannelSign is %d.", sSaveDataInfo.stHeadInfo.ucTestChannelSign);

    //    int iGroup = AE::GROUP_AE_FLY;
    //    m_pConfig->beginGroup( Module::GROUP_AE );
    //    quint16 usGatingTime = m_pConfig->value(AE::KEY_OPEN_DOOR_TIME, iGroup ).toShort();
    //    quint16 usCloseDoorTime = m_pConfig->value( AE::KEY_CLOSE_DOOR_TIME, iGroup ).toUInt();
    //    UINT16 usMaxTimeInterval = m_pConfig->value(AE::KEY_TIME_INTERVAL, iGroup ).toInt();
    //    m_pConfig->endGroup();
    sSaveDataInfo.eTransformerType = AE::transformChannelToDataFile(m_eChannel);
    sSaveDataInfo.iDataPointNum = AE_AMP_DATA_COUNT;
    memset(sSaveDataInfo.ucaDischargeTypeProb, 0, sizeof(sSaveDataInfo.ucaDischargeTypeProb));
    //    sSaveDataInfo.usGatingTime = usGatingTime;
    //    sSaveDataInfo.usShutTime = usCloseDoorTime;
    //    sSaveDataInfo.usMaxTimeInterval = usMaxTimeInterval;
    sSaveDataInfo.eGainType = DataFileNS::GAIN_TYPE_AMPLIFY_RATIO;
    //sSaveDataInfo.setGain(m_eGain);
    sSaveDataInfo.sGain =  AE::g_ausGainValues[m_eGain];
    dbg_info("m_eGain is %d.", m_eGain);
    sSaveDataInfo.setUnit(m_eUnit);
    sSaveDataInfo.eSyncSource = DataFileNS::SYNC_SOURCE_POWER;
    sSaveDataInfo.fSyncFreq = m_usSpectrum;
    sSaveDataInfo.ucSyncState = 0;
    //    sSaveDataInfo.fTrigAmp = AE::getThreshold( m_eUnit, m_eGain, m_eTriggerValue );

    sSaveDataInfo.fPeakMin = AE::AMPLITUDE_MIN_VALUES[m_eUnit][m_eGain][AE::COMPONENT_MAX];
    sSaveDataInfo.fPeakMax = AE::AMPLITUDE_MAX_VALUES[m_eUnit][m_eGain][AE::COMPONENT_MAX];
    sSaveDataInfo.fRMSMin = AE::AMPLITUDE_MIN_VALUES[m_eUnit][m_eGain][AE::COMPONENT_EFFECTIVE];
    sSaveDataInfo.fRMSMax = AE::AMPLITUDE_MAX_VALUES[m_eUnit][m_eGain][AE::COMPONENT_EFFECTIVE];
    sSaveDataInfo.fFreq1Min = AE::AMPLITUDE_MIN_VALUES[m_eUnit][m_eGain][AE::COMPONENT_ONE];
    sSaveDataInfo.fFreq1Max = AE::AMPLITUDE_MAX_VALUES[m_eUnit][m_eGain][AE::COMPONENT_ONE];
    sSaveDataInfo.fFreq2Min = AE::AMPLITUDE_MIN_VALUES[m_eUnit][m_eGain][AE::COMPONENT_TWO];
    sSaveDataInfo.fFreq2Max = AE::AMPLITUDE_MAX_VALUES[m_eUnit][m_eGain][AE::COMPONENT_TWO];

    //图谱数据
    sSaveDataInfo.stAEAmpData.fRms = stData.fRMS;
    sSaveDataInfo.stAEAmpData.fPeak = stData.fPeakValue;
    sSaveDataInfo.stAEAmpData.fFrequency1 = stData.fFirstFreqComValue;
    sSaveDataInfo.stAEAmpData.fFrequency2 = stData.fSecondFreqComValue;

    sSaveDataInfo.stAEAmpData.fRmsBGN = m_pChart->noiseData(AE::COMPONENT_EFFECTIVE);
    sSaveDataInfo.stAEAmpData.fPeakBGN = m_pChart->noiseData(AE::COMPONENT_MAX);
    sSaveDataInfo.stAEAmpData.fFrequency1BGN = m_pChart->noiseData(AE::COMPONENT_ONE);
    sSaveDataInfo.stAEAmpData.fFrequency2BGN = m_pChart->noiseData(AE::COMPONENT_TWO);

    dbg_info("sSaveDataInfo.stAEAmpData.fRms is %f.", sSaveDataInfo.stAEAmpData.fRms);
    dbg_info("sSaveDataInfo.stAEAmpData.fPeak is %f.", sSaveDataInfo.stAEAmpData.fPeak);
    dbg_info("sSaveDataInfo.stAEAmpData.fFrequency1 is %f.", sSaveDataInfo.stAEAmpData.fFrequency1);
    dbg_info("sSaveDataInfo.stAEAmpData.fFrequency2 is %f.", sSaveDataInfo.stAEAmpData.fFrequency2);
    dbg_info("sSaveDataInfo.stAEAmpData.fRmsBGN is %f.", sSaveDataInfo.stAEAmpData.fRmsBGN);
    dbg_info("sSaveDataInfo.stAEAmpData.fPeakBGN is %f.", sSaveDataInfo.stAEAmpData.fPeakBGN);
    dbg_info("sSaveDataInfo.stAEAmpData.fFrequency1BGN is %f.", sSaveDataInfo.stAEAmpData.fFrequency1BGN);
    dbg_info("sSaveDataInfo.stAEAmpData.fFrequency2BGN is %f.", sSaveDataInfo.stAEAmpData.fFrequency2BGN);

    AEAmpDataSave hDataSave;
    return hDataSave.saveData( &sSaveDataInfo );
}

/*************************************************
功能： 载入数据
*************************************************************/
void AEAmpView::loadData()
{
    QString filePath = DATA_STORAGE_PATH + "/" + AE_AMP_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if( !infoList.isEmpty() )
    {
        PlayBackView* pView = new PlayBackView( filePath, AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_AMP_CHART), AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_LOAD_DATA) );
        pView->addPlayback( AE_AMP_FILE_NAME_SUFFIX, new AEAmpPlayBackView() );
        connect(pView, SIGNAL(sigEnterPressed()), this, SLOT(hideMoreConfigButtonBar()));
        pView->exec();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
}

/*************************************************
功能： 删除数据
*************************************************************/
void AEAmpView::deleteData()
{
    QStringList nameFilters;
    nameFilters << AE_AMP_FILE_NAME_SUFFIX;
    nameFilters << ".png";

    QString filePath = DATA_STORAGE_PATH + "/" + AE_AMP_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if( !infoList.isEmpty() )
    {
        DeleteDataView* pView = new DeleteDataView( filePath,
                                                    AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_DELETE_DATA),
                                                    nameFilters
                                                    );
        pView->setRelatedSuffix( AE_AMP_FILE_NAME_SUFFIX, QStringList(".png") );
        pView->exec();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
}

/*************************************************
功能： 录音
*************************************************************/
void AEAmpView::recorder()
{
    //RecordPlayService::instance()->stopListenAE();
    //RecordPlayService::instance()->stop();
    RecordView *pRecordView = new RecordView(QObject::trUtf8("Record"), RecordPlay::AE_RECORD, RecordPlay::AE_AMP);
    connect( pRecordView, SIGNAL(sigRecorded(const QString&, const uint)), this, SLOT(onRecorded(const QString&, const uint)) );
    connect( pRecordView, SIGNAL(sigClosed()), this, SLOT(onStartListenAE()) );//关闭时恢复监听超声波
    pRecordView->show();
}


/*************************************************
功能： 槽，响应录音界面关闭后的处理（重新开始监听超声波）
*************************************************************/
void AEAmpView::onStartListenAE( )
{
    //RecordPlayService::instance()->start();
    //RecordPlayService::instance()->startListenAE();
    //RecordPlayService::instance()->setVolume(m_ucVolume);
    return;
}

/*************************************************
功能： 恢复默认
*************************************************************/
void AEAmpView::restoreDefault()
{
    if(MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to restore default settings?")))
    {
        stopSample();

        int iGroup = AE::GROUP_AE_AMPLITUDE;

        m_pConfig->beginGroup( Module::GROUP_AE );
        QVector<Config::GroupKey> totalKeys;
        totalKeys << Config::GroupKey( AE::KEY_GAIN );
        totalKeys << Config::GroupKey( AE::KEY_SAMPLE_MODE, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_TRIGGER_VALUE, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_UNIT, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_FREQ_COMPONENT, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_CHANNEL_TYPE );

        m_pConfig->restoreDefault( totalKeys );
        m_pConfig->endGroup();

        m_pConfig->beginGroup(Module::GROUP_APP);
        QVector<Config::GroupKey> qvtAppDefaultKeys;
        qvtAppDefaultKeys << Config::GroupKey(APPConfig::KEY_SYSTEM_VOLUME);
        m_pConfig->restoreDefault(qvtAppDefaultKeys);
        m_pConfig->endGroup();

        //重新初始化数据
        initParameters();

        resetTriggerList( m_eGain, m_eUnit );
        //设置数据
        setButtonBarDatas();
        setChartDatas();

        //设置工作参数
        setAllWorkingSet();

        //设置音量
        RecordPlayService::instance()->setVolume(m_ucVolume);

        initChartState();
        startSample();
    }
}

/*************************************************
功能： 响应S键事件
输入参数：
        id -- 按钮ID
*************************************************************/
void AEAmpView::onSKeyPressed()
{
    pressSaveData();

    /*m_pService->stopSample( m_iUserId );

    QString strStationName;
    QString strDeviceName;

    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
    centerPoint.setY(pBtnBar->pos().y()+pBtnBar->height()/2);

    fastSave( "",
              strStationName,
              strDeviceName,
              DATA_STORAGE_PATH + '/' + AE_AMP_FOLDER,
              centerPoint);

    m_pService->startSample( m_iUserId );*/

    return;
}

/*************************************************
功能： 显示事件
输入参数：
       event -- 事件
*************************************************************/
void AEAmpView::showEvent(QShowEvent* event)
{
    Q_UNUSED(event);
    //buttonBar()->button(aeampview::BUTTON_AE_CLEAR_NOISE)->setDisabled(true);
    return;
}


/*************************************************
功能： 载入录音数据
*************************************************************/
void AEAmpView::loadRecordData()
{
    QString filePath = AE_RECORD_FILE_DIR_PATH_AMP;
    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);
    if(infoList.isEmpty())
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
    else
    {
        PlayView* pView = new PlayView(AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_LOAD_RECORD_DATA), RecordPlay::AE_AMP);
        pView->show();
    }

    return;
}

/*************************************************
功能： 删除录音数据
*************************************************************/
void AEAmpView::deleteRecordData()
{
    QStringList nameFilters;
    nameFilters << AE_AUDIO_FILE_NAME_SUFFIX;

    QString filePath = AE_RECORD_FILE_DIR_PATH_AMP;
    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);
    if(infoList.isEmpty())
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));

    }
    else
    {
        DeleteDataView* pView = new DeleteDataView(filePath,
                                                   AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_DELETE_RECORD_DATA),
                                                   nameFilters
                                                   );
        pView->setRelatedSuffix(AE_AUDIO_FILE_NAME_SUFFIX, QStringList(AE_RECORD_FILE_NAME_SURRFIX));
        pView->exec();
    }

    return;
}

/*************************************************
功能： 响应录音完成
输入参数：
        strRecordFilePath -- 录音文件保存的路径
            uint -- 录音时长
*************************************************************/
void AEAmpView::saveRecorded(const QString& strRecordFilePath, const quint32 uiRecordingTime)
{
    AERecordDataInfo sSaveDataInfo;
    sSaveDataInfo.stHeadInfo.strSubstationName = "";
    sSaveDataInfo.stHeadInfo.strDeviceName = "";
    sSaveDataInfo.stHeadInfo.generationDateTime = QDateTime::currentDateTime();
    sSaveDataInfo.stHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_UNRECORD;

    sSaveDataInfo.stHeadInfo.eCode = DataFileNS::SPECTRUM_CODE_RECORD_AE;
    sSaveDataInfo.stHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;
    sSaveDataInfo.stHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;
    sSaveDataInfo.stHeadInfo.ucTestChannelSign = (m_eChannel == AE::WIRELESS) ? 1 : 0;

    sSaveDataInfo.uiDuration = uiRecordingTime;
    sSaveDataInfo.eGainType = DataFileNS::GAIN_TYPE_AMPLIFY_RATIO;
    sSaveDataInfo.sGain =  AE::g_ausGainValues[m_eGain];

    sSaveDataInfo.eSyncSource = DataFileNS::SYNC_SOURCE_POWER;
    sSaveDataInfo.fSyncFreq = m_usSpectrum;
    sSaveDataInfo.eTransformerType = AE::transformChannelToDataFile(m_eChannel);

    sSaveDataInfo.strFilePath = strRecordFilePath;

    QFileInfo qfDataInfo(strRecordFilePath);
    QString qstrFileName = qfDataInfo.fileName();
    qstrFileName = qstrFileName.left(qstrFileName.lastIndexOf('.'));

    AERecordDataSave hDataSave;
    QString qstrDataFile = hDataSave.saveData(&sSaveDataInfo, qfDataInfo.absolutePath(), qstrFileName);

    QFileInfo fileInfo(qstrDataFile);
    QString strFileName = fileInfo.fileName();
    QString strInfo = qstrDataFile.isEmpty() ? QObject::trUtf8("Save failure!") : strFileName;

    processTooLongMsgText(strInfo);
    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x() + pBtnBar->width() / 2);
    centerPoint.setY(pBtnBar->pos().y() + pBtnBar->height() / 2);
    MsgBox::informationWithoutAutoAccept("", strInfo, centerPoint);
    return;
}

/******************************************
 * 功能：AE录音到最大录音时间，处理逻辑
 * ****************************************/
void AEAmpView::stopAERecordNextToDo()
{
    if(stopAERecord())
    {
        this->buttonBar()->button(aeampview::BUTTON_AE_RECORDER)->setTitle(AE_TRANSLATE(AE::TEXT_START_RECORD));
        saveRecorded(m_qstrAttachPath, m_uiTotalRecordingTime);
        m_uiTotalRecordingTime = 0;
    }
    else
    {
        logError("stop record failed.");
    }

    return;
}


/*************************************************
功能： 槽，响应录音时长变化
输入参数：
        id -- 按钮ID
*************************************************************/
void AEAmpView::onTimerRefresh(uint uiRecordingTime)
{
    char cRecordTime[10] = {0};

    UINT32 uiHour = uiRecordingTime / (MINUTE_PER_HOUR * SECOND_PER_MINUTE);
    UINT32 uiMin = (uiRecordingTime % (MINUTE_PER_HOUR * SECOND_PER_MINUTE)) / SECOND_PER_MINUTE;
    UINT32 uiSec = (uiRecordingTime % (MINUTE_PER_HOUR * SECOND_PER_MINUTE)) % SECOND_PER_MINUTE;

    sprintf(cRecordTime, "%02d:%02d", uiMin, uiSec); //显示样式：xx:xx
    logInfo(QString("cRecordTime %1").arg(cRecordTime));

    this->buttonBar()->button(aeampview::BUTTON_AE_RECORDER)->setTitle(AE_TRANSLATE(cRecordTime));
}

/*************************************************
 * 功能：诊断数据
 * 输入参数：
 *      data：待诊断的数据
 *      bSave：是否为保存操作的逻辑，缺省为false
 * ***********************************************/
void AEAmpView::diagDataInfo(const AE::AmplitudeData &data, bool bSave)
{
    if(SystemSet::RT_DIAG_ON != SystemSetService::instance()->getRealtimeDiagnosisSwitch())
    {
        return;
    }

    if(bSave)
    {
        AEAmpDiagInfo stDiagInfo;
        stDiagInfo.stAEAmpVal.iA50Hz = Module::dealFloatPrecision(data.fFirstFreqComValue, 0);
        stDiagInfo.stAEAmpVal.iA100Hz = Module::dealFloatPrecision(data.fSecondFreqComValue, 0);
        stDiagInfo.stAEAmpVal.iARMS = Module::dealFloatPrecision(data.fRMS, 0);
        stDiagInfo.stAEAmpVal.iAPeak = Module::dealFloatPrecision(data.fPeakValue, 0);

        stDiagInfo.eUnit = (UnitLevel)m_eUnit;
        stDiagInfo.eGain = (GainLevel)AE::g_ausGainValues[m_eGain];
        stDiagInfo.stTHInfo.iThresholdMinor = AE::g_aiAEDiagThresholdValues[m_eGain][AE::TH_MINOR];
        stDiagInfo.stTHInfo.iThresholdSerious = AE::g_aiAEDiagThresholdValues[m_eGain][AE::TH_SERIOUS];
        stDiagInfo.stTHInfo.iThresholdEmergency = AE::g_aiAEDiagThresholdValues[m_eGain][AE::TH_EMERGENCY];

        DiagResultInfo stRetInfo;
        DiagnosisManager::instance()->diagAEAmpByThresholdDirectly(stDiagInfo, stRetInfo);

        DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
        stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stRetInfo.stDiagRet.defectLevel);
        stDiagDisplayInfo.qstrPDDesInfo = stRetInfo.qstrPDDescription;
        stDiagDisplayInfo.qstrPDSignalInfos = stRetInfo.qstrPDSignalTypeInfos;

        if(m_pChart)
        {
            m_pChart->setDiagRet(stDiagDisplayInfo);
        }
    }
    else
    {
        AEAmpDiagInfo stDiagInfo;
        stDiagInfo.stAEAmpVal.iA50Hz = Module::dealFloatPrecision(data.fFirstFreqComValue, 0);
        stDiagInfo.stAEAmpVal.iA100Hz = Module::dealFloatPrecision(data.fSecondFreqComValue, 0);
        stDiagInfo.stAEAmpVal.iARMS = Module::dealFloatPrecision(data.fRMS, 0);
        stDiagInfo.stAEAmpVal.iAPeak = Module::dealFloatPrecision(data.fPeakValue, 0);

        stDiagInfo.eUnit = (UnitLevel)m_eUnit;
        stDiagInfo.eGain = (GainLevel)AE::g_ausGainValues[m_eGain];
        stDiagInfo.stTHInfo.iThresholdMinor = AE::g_aiAEDiagThresholdValues[m_eGain][AE::TH_MINOR];
        stDiagInfo.stTHInfo.iThresholdSerious = AE::g_aiAEDiagThresholdValues[m_eGain][AE::TH_SERIOUS];
        stDiagInfo.stTHInfo.iThresholdEmergency = AE::g_aiAEDiagThresholdValues[m_eGain][AE::TH_EMERGENCY];

        DiagnosisManager::instance()->diagAEAmpByThreshold(stDiagInfo);
    }

    return;
}

/****************************************************
 * 功能：保存操作
 * **************************************************/
void AEAmpView::pressSaveData()
{
    //停止采集
    stopSample();

    QString strDataFile = saveDataToFile();

    QFileInfo fileInfo(strDataFile);
    QString strFileName = fileInfo.fileName();
    QString qstrDiagRetInfo = m_pChart->getDiagRetInfo();
    if(!qstrDiagRetInfo.isEmpty())
    {
        strFileName += QString("\n%1").arg(qstrDiagRetInfo);
    }

    showSaveInformation(strFileName, strDataFile);

    //开始采集
    startSample();

    return;
}

/*****************************************
 * 功能：切换触发默认值信息
 * **********************************************/
void AEAmpView::updateTriggerDefaultValue()
{
    if(AE::GAIN_X10 == m_eGain || AE::GAIN_X1 == m_eGain)
    {
        m_eTriggerValue = AE::TRIGGER_LEVEL_1;
    }
    else if(AE::GAIN_X100 == m_eGain)
    {
        m_eTriggerValue = AE::TRIGGER_LEVEL_2;
    }
    ((PopupButton*)(buttonBar()->button(aeampview::BUTTON_AE_TRIGGER_VALUE)))->setValue( m_eTriggerValue );
    if (AEAmpService* pService = getAEAmpService())
    {
        pService->setTriggerValue( m_eTriggerValue );
    }
}
