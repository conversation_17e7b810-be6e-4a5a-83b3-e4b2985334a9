﻿/*
* Copyright (c) 2017.08，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: prpsmap.h
*
* 初始版本: 1.0
* 作者: 张涛
* 创建日期: 2017年11月13日
* 摘要: 该文件定义了PRPS图谱坐标映射类

* 当前版本: 1.0
*/
#ifndef PRPSMAP_H
#define PRPSMAP_H

#include <QLine>
#include <QPair>
#include <QVector>
#include "phasechartmap.h"

class PrpsMap : public PhaseChartMap
{
public:
    PrpsMap(int iPeriodCount, int iPhaseCount);

    MapType type() const
    {
        return PrpsMapper2D;
    }

    // 坐标系的线段
    const QList<QLine>& coordinateLines() const
    {
        return m_coorLines;
    }

    // 坐标系文字及其位置
    const QList<QPair<QString, QRect> >& labels() const
    {
        return m_labels;
    }

    // 列数, 指的是每周期绘制的线段数量
    int columnCount() const
    {
        return m_iColumnCount;
    }

    void setColumnCount(int iPhaseCount);

    // 行数, 指的是最多绘制多少个周期
    int rowCount() const
    {
        return m_iRowCount;
    }

    void setRowCount(int iPeriodCount);

    // 坐标系原点
    inline QPoint originPoint() const
    {
        return m_originPos;
    }

    // 根据参数刷新缓存的映射信息
    void updateFactor();

    // 计算iRowIdx行iColumn列其值为percent的线段
    inline QLine getLine(int iRowIdx, int iColumn, float percent) const
    {
        return QLine(m_linesBottomPoints[iRowIdx][iColumn],
                     QPoint(m_linesBottomPoints[iRowIdx][iColumn].x(),
                            m_linesBottomPoints[iRowIdx][iColumn].y() - m_iAmpAxisLength * percent));
    }

    void setPhaseAxisOffset(const int iPhaseAxisOffset);

    void setOriginXRatio(const int iOriginXRatio);

    void setOriginYRatio(const int iOriginYRatio);

    QRect getPhaseAmpCoordinateRect() const;

    QRect getPhasePeriodCoordinateRect() const;

    int phaseAxisOffset() const;

private:
    // 更新坐标系线段
    void updateCoorLines();

    // 更新文字
    void updateTexts();

    // 更新坐标原点
    void updateOriginPos();

    float getRightMargin() const;

private:
    QPoint m_originPos;
    QList<QLine> m_coorLines;
    QList<QPair<QString, QRect> > m_labels;

    int m_iColumnCount;
    int m_iRowCount;

    int m_iAmpAxisLength;

    QVector<QVector<QPoint> > m_linesBottomPoints;

    enum
    {
        DivCount = 4
    };

    int m_iPhaseAxisOffset;
    int m_iOriginXRatio;
    int m_iOriginYRatio;
};

#endif // PRPSMAP_H
