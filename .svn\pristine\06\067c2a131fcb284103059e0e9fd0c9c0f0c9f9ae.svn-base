﻿#ifndef IMAGECONVERSION_H
#define IMAGECONVERSION_H

#include <cmath>

#if defined(IMAGECONVERSION_LIBRARY)
#  define IMAGECONVERSIONSHARED_EXPORT Q_DECL_EXPORT
#else
#  define IMAGECONVERSIONSHARED_EXPORT Q_DECL_IMPORT
#endif

extern "C"
{

/*
 * 功能: 转换bmp格式的图片为指定格式
 * 参数说明:
 * pDestBuffer: 目标格式的数据缓冲区
 * destLen: 目标格式的数据长度
 * pSrcBmpBuffer: 原bmp格式数据的缓冲区
 * srcLen: 原bmp格式数据的长度
 * destFormat: 目标格式字符串,如"PNG"/"JPG", 支持如下格式:
                BMP
                JPG/JPEG
                PNG
                PPM
                XBM
                XPM
 */

//IMAGECONVERSIONSHARED_EXPORT
void convert_bmp_image(char *pDestBuffer, unsigned int *destLen,
        unsigned char *pSrcBmpBuffer, uint srcLen, const char *destFormat);

}

#endif // IMAGECONVERSION_H
