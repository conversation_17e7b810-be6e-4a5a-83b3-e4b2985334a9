#ifndef __TEMPLATEAPI_H_
#define __TEMPLATEAPI_H_
#ifdef __cplusplus    //__cplusplus是cpp中自定义的一个宏
extern "C" {          //告诉编译器，这部分代码按C语言的格式进行编译，而不是C++的
#endif
#include "DriverDataDefine.h"
//How To Use GNSS API
/*
**
     if((ret = gnss_init(pstCfg))!=GNSS_OK)
     {
         //gnss系统初始化错误
         return ret;
     }

     if((ret = get_gnss_info(pstInfo))==GNSS_OK)
     {
         // 成功获取gnss定位信息

         // 。。。定位信息处理使用
     }
      。。。。
      //释放gnss资源

      gnss_exit(pstCfg);

*/

typedef enum _GnssErr
{
    GNSS_OK = 0,
    GNSS_COMM_CONFIG_ERR = -1,
    GNSS_COMM_ERR = -2,
    GNSS_COMM_RECV_TIMEOUT = -3,
    GNSS_INVALID_PARAM = 501,
    GNSS_OPERATION_NOT_SUPPORT = 502,
    GNSS_SUBSYSTEM_BUSY = 503,
    GNSS_SESSION_IS_ONGOING =504,
    GNSS_SESSION_NOT_ACTIVE =505,
    GNSS_OPERATION_TIMEOUT =506,
    GNSS_FUNCTION_NOT_ENABLED =507,
    GNSS_TIME_INFO_ERROR =508,
    GNSS_VALIDITY_TIME_OUT_OF_RANGE=512,
    GNSS_INTERNAL_RESOUCE_ERROR=513,
    GNSS_LOCKED =514,
    GNSS_END_BY_E911= 515,
    GNSS_NOT_FIXED_NOW = 516, //未获取到定位信息
    GNSS_CMUX_PORT_IS_NOT_OPENED =517,
    GNSS_UNKNOWN_ERROR = 549,
}GnssErr;

typedef enum _GnssType
{
    GNSS_GPS=0,
    GNSS_BeiDou,
    GNSS_ALL,
}GnssType;

typedef struct _GnssConfig
{
    GnssType eGnss;  // 全球定位系统类型
    UINT32 uiResv[20]; //预留参数
    void *priv;// 私有参数配置
}GnssConfig;

typedef struct _GnssInfo
{
    double dfJingDu; //经度信息，正数表示 东半球，负数表示西半球
    double dfWeiDu;  //纬度信息，正数表示 北半球，负数表示南半球
    double dfGaoDu;  //海拔高度
    UINT32 uiResv[20];

}GnssInfo;
/*******************************************************************************
 * 函数名   ：gnss_init
 * 输入参数 ：GnssConfig      -- GNSS 参数配置
 * 返回值   ：GNSS_OK   成功
 *            其他    失败
 * 功能     ：初始化 GNSS 系统，启动定位功能
 ******************************************************************************/
INT32 gnss_init(GnssConfig *pstCfg);
/*******************************************************************************
 * 函数名   ：gnss_exit
 * 输入参数 ：GnssConfig      -- GNSS 参数配置
 * 返回值   ：GNSS_OK   成功
 *            其他    失败
 * 功能     ：释放 GNSS 系统资源，关闭定位功能
 ******************************************************************************/
INT32 gnss_exit(GnssConfig *pstCfg);
/*******************************************************************************
 * 函数名   ：get_gnss_info
 * 输入参数 ：GnssInfo      -- GNSS 定位信息
 *            GnssConfig   --GNSS 配置参数
 * 返回值   ：GNSS_OK   成功
 *            其他    失败
 * 功能     ：获取GNSS 定位信息,仅返回成功时，信息有效
 ******************************************************************************/
INT32 get_gnss_info(GnssInfo *pstInfo,GnssConfig *pstCfg);



#ifdef __cplusplus
}
#endif

#endif
