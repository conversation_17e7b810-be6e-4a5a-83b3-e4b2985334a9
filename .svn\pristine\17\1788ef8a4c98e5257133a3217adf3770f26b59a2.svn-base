#include "scrollbar.h"

/*************************************************
函数名： ScrollBar(ColorType eColorType, Qt::Orientation orientation, QWidget *parent)
输入参数： eColorType：色标颜色类型
          orientation：对齐方式
          parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
ScrollBar::ScrollBar(ColorType eColorType, Qt::Orientation orientation, QWidget *parent)
    :QWidget(parent)
    ,m_Orientation(orientation)
    ,m_eColorType(eColorType)
    ,m_pPainter(NULL)
{
    m_pPainter = new QPainter(this);
}

/*************************************************
函数名： setColorType(const ColorType &eColorType)
输入参数： eColorType：色标颜色类型
输出参数： NULL
返回值： NULL
功能： 设置色标颜色类型
*************************************************************/
void ScrollBar::setColorType(const ColorType &eColorType)
{
    if ((eColorType >= COLOR_TYPE_IRON) && (eColorType < COLOR_TYPE_COUNT))
    {
        m_eColorType = eColorType;
    }

    update();
}

/*************************************************
函数名： paintEvent(QPaintEvent *e)
输入参数： e：重绘事件
输出参数： NULL
返回值： NULL
功能： 重绘事件处理
*************************************************************/
void ScrollBar::paintEvent(QPaintEvent *e)
{
    Q_UNUSED(e)

    m_pPainter->begin(this);
    m_pPainter->setRenderHint(QPainter::Antialiasing, true);
    onColorTypeChanged();
    m_pPainter->drawRect(0, 0, width(), height());
    m_pPainter->setRenderHint(QPainter::Antialiasing, false);
    m_pPainter->end();
}

/*************************************************
函数名： onColorTypeChanged()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应色标颜色类型改变
*************************************************************/
void ScrollBar::onColorTypeChanged()
{
    QLinearGradient qlinearGradient;

    if (Qt::Vertical == m_Orientation)
    {
        qlinearGradient = QLinearGradient(0, 0, 0, height());

    }
    else
    {
        qlinearGradient = QLinearGradient(0, 0, width(), 0);
    }

    switch (m_eColorType)
    {
        case COLOR_TYPE_IRON:
            qlinearGradient.setColorAt(0.05, Qt::white);
            qlinearGradient.setColorAt(0.3,  Qt::yellow);
            qlinearGradient.setColorAt(0.5,  Qt::red);
            qlinearGradient.setColorAt(0.8,  Qt::blue);
            qlinearGradient.setColorAt(1.0,  Qt::black);
        break;
        case COLOR_TYPE_RAIN:
            qlinearGradient.setColorAt(0.05, Qt::black);
            qlinearGradient.setColorAt(0.2,  Qt::red);
            qlinearGradient.setColorAt(0.4,  Qt::blue);
            qlinearGradient.setColorAt(0.6,  Qt::green);
            qlinearGradient.setColorAt(0.8,  Qt::red);
            qlinearGradient.setColorAt(1.0,  Qt::white);
        break;

        case COLOR_TYPE_IRON_REVERSAL:
            qlinearGradient.setColorAt(0.2,  Qt::black);
            qlinearGradient.setColorAt(0.5,  Qt::blue);
            qlinearGradient.setColorAt(0.7,  Qt::red);
            qlinearGradient.setColorAt(0.95, Qt::yellow);
            qlinearGradient.setColorAt(1.0,  Qt::white);
        break;

        case COLOR_TYPE_BLACK_FEVER:
            qlinearGradient.setColorAt(0.2 ,Qt::white);
            qlinearGradient.setColorAt(0.7, Qt::gray);
            qlinearGradient.setColorAt(1.0, Qt::black);
        break;

        case COLOR_TYPE_WHITE_FEVER:
            qlinearGradient.setColorAt(0.3, Qt::black);
            qlinearGradient.setColorAt(0.7, Qt::gray);
            qlinearGradient.setColorAt(1.0, Qt::white);
        break;

    case COLOR_TYPE_LAVA:
            qlinearGradient.setColorAt(0.05, QColor(0, 0, 176));
            qlinearGradient.setColorAt(0.2, QColor(0, 67, 255));
            qlinearGradient.setColorAt(0.4, QColor(27, 254, 228));
            qlinearGradient.setColorAt(0.6, QColor(234, 255, 18));
            qlinearGradient.setColorAt(0.8, QColor(255, 71, 0));
            qlinearGradient.setColorAt(1.0, QColor(127, 0, 0));
        break;

        default:
        break;
    }

    m_pPainter->setBrush(QBrush(qlinearGradient));
}
