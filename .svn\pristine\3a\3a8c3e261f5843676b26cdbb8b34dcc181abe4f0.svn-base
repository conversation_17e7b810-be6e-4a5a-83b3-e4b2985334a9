#include "aeallviewbase.h"
#include "window/Window.h"

/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
AEAllViewBase::AEAllViewBase(const QString& strTitle, QWidget *parent)
    : SampleChartView(strTitle, parent)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);

    AEAllViewService* pService = AEAllViewService::instance();
    setService(pService);
    qRegisterMetaType<AE::AllViewData >("AE::AllViewData");
    qRegisterMetaType<MultiServiceNS::USERID >("MultiServiceNS::USERID");
    qRegisterMetaType<AE::ChannelType >("AE::ChannelType");
    //connect( pService, SIGNAL(sigReadAEAllDataFailed( MultiServiceNS::USERID)), this, SLOT(onReadAEAllDataFailed( MultiServiceNS::USERID) ) );
    connect( pService, SIGNAL(sigData( AE::AllViewData, MultiServiceNS::USERID)), this, SLOT(onDataRead( AE::AllViewData, MultiServiceNS::USERID) ) );
    connect( pService, SIGNAL(sigChannelChanged(AE::ChannelType)), this, SLOT(onChannelChanged(AE::ChannelType)) );//通道变化
    connect( pService, SIGNAL(sigSyncStateChanged(Module::SyncState)), this, SLOT(onSyncStateChanged(Module::SyncState)) );//同步状态变化

    //启动采集
    MultiServiceNS::SampleUser user;
    user.eSampleType = MultiServiceNS::SPECTRUM_AE_ALL;
    addUser(user);

    pService->start();
}

/*************************************************
功能： 析构
*************************************************************/
AEAllViewBase::~AEAllViewBase( )
{
    this->disconnect(this);

    if(AEAllViewService* pService = getAEAllViewService())
    {
        pService->stop();
    }
}

/*************************************************************
 * 功能：设置工作模式
 * ************************************************************/
void AEAllViewBase::setWorkMode()
{
    if(AEAllViewService* pService = getAEAllViewService())
    {
        pService->transaction();
        pService->setWorkMode(AE::MODE_ALLVIEW);
    }
    else
    {
        qWarning() << "AEAllViewBase::setWorkMode: error, service handle is NULL!";
    }
}

/*************************************************************
 * 功能：获取服务类
 * 返回值：
 *         AEAllViewService*：服务类
 * ************************************************************/
AEAllViewService* AEAllViewBase::getAEAllViewService()
{
    if (MultiUserService* pService = service())
    {
        return dynamic_cast<AEAllViewService*>(pService);
    }
    else
    {
        return NULL;
    }
}

