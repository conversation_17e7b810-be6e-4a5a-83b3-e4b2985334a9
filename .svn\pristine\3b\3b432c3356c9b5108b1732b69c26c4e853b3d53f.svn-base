#include "pdataskrunnable.h"
#include "pdaservice.h"

TaskReadRunnable::TaskReadRunnable(int iIndex) : m_iIndex(iIndex)
{
}

TaskReadRunnable::~TaskReadRunnable()
{
}

int TaskReadRunnable::getCurTaskIndex()
{
    return m_iIndex;
}

void TaskReadRunnable::setCurTaskIndex(int iIndex)
{
    m_iIndex = iIndex;
    return;
}

void TaskReadRunnable::run()
{
    PDAService::instance()->loadTask(m_iIndex);
    return;
}

TaskSaveRunnable::TaskSaveRunnable()
{
}

TaskSaveRunnable::~TaskSaveRunnable()
{
}

void TaskSaveRunnable::run()
{
    PDAService::instance()->finishTestingTask();
    return;
}



