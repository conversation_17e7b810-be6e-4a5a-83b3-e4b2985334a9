﻿#include "ampdatamap.h"
#include "datamapconfig.h"
#include "common/dataformat.h"

/*************************************************
功能： 构造函数
*************************************************************/
AmpDataMap::AmpDataMap()
{
    m_sMapHead.eCode = DataFileNS::SPECTRUM_CODE_UHF_AMP;
}
/*************************************************
功能： 析构函数
*************************************************************/
AmpDataMap::~AmpDataMap()
{
#if 0
    DataMapUtils::deleteDataBuf( m_stAmpData );//TODO
#endif
}
/*************************************************
功能： 设置图谱的ext information
输入参数：
        pData -- 指向m图谱的ext information的指针
*************************************************************/
void AmpDataMap::setInfo(AmpMapNS::AmpMapInfo *pMapInfo)
{
    m_stExt.eAmpUnit = pMapInfo->eAmpUnit;
    m_stExt.fAmpLowerLimit = pMapInfo->fAmpLowerLimit;
    m_stExt.fAmpUpperLimit = pMapInfo->fAmpUpperLimit;
    m_stExt.eBandWidth = pMapInfo->eBandWidth;
    m_stExt.fFrequencyMin = pMapInfo->fFrequencyMin;
    m_stExt.fFrequencyMax = pMapInfo->fFrequencyMax;
    m_stExt.fWarningValue = pMapInfo->fWarningValue;
    m_stExt.fAlarmingValue = pMapInfo->fAlarmingValue;
    memcpy(m_stExt.ucaDischargeTypeProb, pMapInfo->ucaDischargeTypeProb, sizeof(pMapInfo->ucaDischargeTypeProb));
    m_stExt.eDataSign = pMapInfo->eDataSign;
    m_stExt.eGainType = pMapInfo->eGainType;
    m_stExt.sGain = pMapInfo->sGain;
    m_stExt.fSyncFreq = pMapInfo->fSyncFreq;
}

/*************************************************
功能： 设置图谱的ext information
输入参数：
        pData -- 指向m图谱的ext information的指针
*************************************************************/
void AmpDataMap::setBinaryInfo(AmpMapNS::AmpBinaryMapInfo *pMapInfo)
{
    m_stBinaryExt.eAmpUnit = pMapInfo->eAmpUnit;
    m_stBinaryExt.fAmpLowerLimit = pMapInfo->fAmpLowerLimit;
    m_stBinaryExt.fAmpUpperLimit = pMapInfo->fAmpUpperLimit;
}

/*************************************************
功能： 设置图谱的data
输入参数：
        pData -- 指向图谱的data的指针
*************************************************************/
void AmpDataMap::setData(AmpMapNS::AmpData* pData)
{
    m_stAmpData.iCurrentAmp = pData->iCurrentAmp;
    m_stAmpData.iCurrentMax = pData->iCurrentMax;
}

/*************************************************
功能： 设置图谱的data
输入参数：
        pData -- 指向图谱的data的指针
*************************************************************/
void AmpDataMap::setBinaryData(AmpMapNS::AmpBinaryData* pData)
{
    m_stAmpBinaryData.fHFCTAmp = pData->fHFCTAmp;
    m_stAmpBinaryData.fHFCTMax = pData->fHFCTMax;
    m_stAmpBinaryData.iPulseCount = pData->iPulseCount;
    m_stAmpBinaryData.strBGFileName = pData->strBGFileName;
}

//get API example
/*************************************************
功能： 读取图谱的ext information
输出参数：
        pInfo -- 指向图谱的ext information的指针
返回值:
        true: 成功 ;false:失败
*************************************************************/
void AmpDataMap::getInfo(AmpMapNS::AmpMapInfo *pMapInfo)
{
    pMapInfo->eAmpUnit = m_stExt.eAmpUnit;
    pMapInfo->fAmpLowerLimit = m_stExt.fAmpLowerLimit;
    pMapInfo->fAmpUpperLimit = m_stExt.fAmpUpperLimit;
    pMapInfo->eBandWidth = m_stExt.eBandWidth;
    pMapInfo->fFrequencyMin = m_stExt.fFrequencyMin;
    pMapInfo->fFrequencyMax = m_stExt.fFrequencyMax;
    pMapInfo->fWarningValue = m_stExt.fWarningValue;
    pMapInfo->fAlarmingValue = m_stExt.fAlarmingValue;
    memcpy(pMapInfo->ucaDischargeTypeProb, m_stExt.ucaDischargeTypeProb, sizeof(m_stExt.ucaDischargeTypeProb));
    pMapInfo->eDataSign = m_stExt.eDataSign;
    pMapInfo->eGainType = m_stExt.eGainType;
    pMapInfo->sGain = m_stExt.sGain;
    pMapInfo->fSyncFreq = m_stExt.fSyncFreq;
}
/*************************************************
功能： 读取图谱的data
输出参数：
        pData -- 指向图谱的data的指针
返回值:
        true: 成功 ;false:失败
*************************************************************/
void AmpDataMap::getData(AmpMapNS::AmpData* pData)
{
    pData->iCurrentAmp = m_stAmpData.iCurrentAmp;
    pData->iCurrentMax = m_stAmpData.iCurrentMax;
}

/*************************************************
功能： 解析map的扩展字段
输入参数：
        baData -- 图谱数据的xml文本内容
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool AmpDataMap::parseMapExtXML(XMLDocument *pDoc , const QString &strRootTag)
{
    bool bRet = true;

    pDoc->beginElement( strRootTag );
    pDoc->beginElement( XML_FILE_NODE_EXTINFORMATION );

    m_stExt.eAmpUnit = (DataFileNS::AmpUnit)pDoc->value(AmpInfoNode:: TEXT_AMP_UNIT).toInt();
    m_stExt.fAmpLowerLimit = pDoc->value(AmpInfoNode:: TEXT_AMP_MIN).toFloat();
    m_stExt.fAmpUpperLimit = pDoc->value(AmpInfoNode:: TEXT_AMP_MAX).toFloat();
    m_stExt.eBandWidth = (DataFileNS::MapBandWidth)pDoc->value(AmpInfoNode:: TEXT_BAND_WIDTH).toInt();
    m_stExt.fFrequencyMin = pDoc->value(AmpInfoNode:: TEXT_FRQ_MIN).toFloat();
    m_stExt.fFrequencyMax = pDoc->value(AmpInfoNode:: TEXT_AMP_MAX).toFloat();
    m_stExt.fWarningValue = pDoc->value(AmpInfoNode:: TEXT_WARNING_VALUE).toFloat();
    m_stExt.fAlarmingValue = pDoc->value(AmpInfoNode:: TEXT_ALARM_VALUE).toFloat();

    QByteArray baDischargeTypeProb = QByteArray::fromBase64(pDoc->value(AmpInfoNode:: TEXT_PD_TYPE_PR).toLatin1());
    memcpy(m_stExt.ucaDischargeTypeProb,(quint8*)(baDischargeTypeProb.data()),baDischargeTypeProb.count());

    m_stExt.eDataSign = (DataFileNS::MapDataSign)pDoc->value(AmpInfoNode:: TEXT_EFFECIVE_SIGN).toInt();
    m_stExt.eGainType = (DataFileNS::GainType)pDoc->value(AmpInfoNode:: TEXT_GAIN_TYPE).toInt();
    m_stExt.sGain = (quint16)pDoc->value(AmpInfoNode:: TEXT_GAIN).toInt();
    m_stExt.fSyncFreq = pDoc->value(AmpInfoNode:: TEXT_SYNC_FREQ).toFloat();

    pDoc->endElement();
    pDoc->endElement();
    return bRet;
}
/*************************************************
功能： 解析map的data字段
输出参数：
        baData -- 图谱数据的xml文本内容
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool AmpDataMap::parseMapDataXML(XMLDocument *pDoc , const QString &strRootTag)
{
    bool bRet = true;

    pDoc->beginElement( strRootTag );
    pDoc->beginElement( XML_FILE_NODE_DATA );
    m_stAmpData.iCurrentAmp = (quint8)pDoc->value(AmpInfoNode:: TEXT_CURRENT_AMP).toInt();
    m_stAmpData.iCurrentMax = (quint8)pDoc->value(AmpInfoNode:: TEXT_CURRENT_MAX).toInt();

    pDoc->endElement();
    pDoc->endElement();
    return bRet;
}

/*************************************************
功能： 生成map扩展部分的xml文本
输入参数：
        baData -- 保存图谱数据的xml文本内容
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool AmpDataMap::saveMapExtXML(XMLDocument *pDoc, QDomElement &element, bool bCrypt)
{
    pDoc->beginElement(element);

    pDoc->beginElement(XML_FILE_NODE_EXTINFORMATION);
    pDoc->setValue( AmpInfoNode::TEXT_AMP_UNIT, QString::number(m_stExt.eAmpUnit) );
    pDoc->setValue( AmpInfoNode::TEXT_AMP_MIN, QString::number(m_stExt.fAmpLowerLimit,'f',1));
    pDoc->setValue( AmpInfoNode::TEXT_AMP_MAX, QString::number(m_stExt.fAmpUpperLimit,'f',1) );
    pDoc->setValue( AmpInfoNode::TEXT_BAND_WIDTH, QString::number(m_stExt.eBandWidth) );
    pDoc->setValue( AmpInfoNode::TEXT_FRQ_MIN, QString::number(m_stExt.fFrequencyMin,'f',1));
    pDoc->setValue( AmpInfoNode::TEXT_FRQ_MAX, QString::number(m_stExt.fFrequencyMax,'f',1) );
    pDoc->setValue( AmpInfoNode::TEXT_WARNING_VALUE, QString::number(m_stExt.fWarningValue,'f',1) );
    pDoc->setValue( AmpInfoNode::TEXT_ALARM_VALUE, QString::number(m_stExt.fAlarmingValue,'f',1) );

    QString strPR;
    //放电概率
    for(int i = 0; i < 8; i ++)
    {

        QString strPRTemp = QString::number(m_stExt.ucaDischargeTypeProb[i]);
        strPR = strPR + strPRTemp;
        strPR = strPR + ",";
    }
    strPR.remove(strPR.size()-1, 1);
    pDoc->setValue( AEAmpInfoNode::TEXT_PDT_TYPE_PR, strPR );

    pDoc->setValue( AmpInfoNode::TEXT_EFFECIVE_SIGN, QString::number(m_stExt.eDataSign) );
    pDoc->setValue( AmpInfoNode::TEXT_GAIN_TYPE, QString::number(m_stExt.eGainType) );
    pDoc->setValue( AmpInfoNode::TEXT_GAIN, QString::number(m_stExt.sGain) );
    pDoc->setValue( AmpInfoNode::TEXT_SYNC_FREQ, QString::number(m_stExt.fSyncFreq,'f',1) );

    bool isSuccess = pDoc->save(bCrypt);
    return isSuccess;
}

/*************************************************
功能： 生成map扩展部分的qbytearray文本，方便存储为二进制
*************************************************************/
void AmpDataMap::saveMapExtBinary( QByteArray &baPackage )
{
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.setFloatingPointPrecision(QDataStream::SinglePrecision);
    out.device()->seek(out.device()->size());
    out << (quint8)m_stBinaryExt.eAmpUnit;
    out << m_stBinaryExt.fAmpLowerLimit;
    out << m_stBinaryExt.fAmpUpperLimit;
}

/*************************************************
功能： 生成map数据部分的xml文本
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool AmpDataMap::saveMapDataXML(XMLDocument *pDoc, QDomElement &element,bool bCrypt)
{
    pDoc->beginElement(element);
    element = pDoc->addElement(XML_FILE_NODE_DATA);
    pDoc->beginElement( element );
    pDoc->setValue( AmpInfoNode::TEXT_CURRENT_AMP, QString::number(m_stAmpData.iCurrentAmp) );
    pDoc->setValue( AmpInfoNode::TEXT_CURRENT_MAX, QString::number(m_stAmpData.iCurrentMax) );
    pDoc->endElement();
    bool isSuccess = pDoc->save(bCrypt);
    return isSuccess;
}

/*************************************************
功能： 生成map数据部分的qbytearray文本，方便存储为二进制
*************************************************************/
void AmpDataMap::saveMapDataBinary( QByteArray &baPackage )
{
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.setFloatingPointPrecision(QDataStream::SinglePrecision);
    out.device()->seek(out.device()->size());

    out << m_stAmpBinaryData.fHFCTAmp;
    out << m_stAmpBinaryData.fHFCTMax;
    out << (qint32)m_stAmpBinaryData.iPulseCount;

    DataFormat::addBinaryInfo(m_stAmpBinaryData.strBGFileName, DataFileNS::TYPE_ASCII, out, DataFileNS::BG_FILE_NAME_LEN);

    saveBinaryMapReserve(baPackage);
}

/*************************************************
功能： 生成map预留字段的qbytearray文本，方便存储为二进制
*************************************************************/
void AmpDataMap::saveBinaryMapReserve(QByteArray &baPackage )
{
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.device()->seek(out.device()->size());

//    quint8 caData [DataFileNS::HFCT_AMP_BINARY_RESERVE];
//    memset(caData, 0, DataFileNS::HFCT_AMP_BINARY_RESERVE);
//    for(int i = 0; i < DataFileNS::HFCT_AMP_BINARY_RESERVE; i ++)
//    {
//        out << (quint8)caData[i];
//    }
}

/*************************************************
功能： 生成map预留字段的qbytearray文本，方便存储为二进制
*************************************************************/
void AmpDataMap::saveMapReserve(QByteArray &baPackage )
{
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.setFloatingPointPrecision(QDataStream::SinglePrecision);
    out.device()->seek(out.device()->size());

    quint8 caData [DataFileNS::HFCT_AMP_RESERVE];
    memset(caData, 0, DataFileNS::HFCT_AMP_RESERVE);
    for(int i = 0; i < DataFileNS::HFCT_AMP_RESERVE; i ++)
    {
        out << (quint8)caData[i];
    }
}
/*************************************************
功能： 获取图谱根节点的标签名
返回值:
        图谱对应的标签名
*************************************************************/
QString AmpDataMap::mapRootTag()
{
    return XML_FILE_NODE_AMP;
}

/*************************************************
功能： 解析map除头以外的所有字段
输入参数：
        bytes -- 图谱数据的二进制文件内容
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool AmpDataMap::parseMapDataBinary(const QByteArray &bytes)
{
    QDataStream in(bytes);
    in.setByteOrder(QDataStream::LittleEndian);

    quint8 ampUnit = 0;

    DataFormat::getUInt8(in, ampUnit);                        //幅值单位
    DataFormat::getFloat(in, m_stBinaryExt.fAmpLowerLimit);   //幅值下限
    DataFormat::getFloat(in, m_stBinaryExt.fAmpUpperLimit);   //幅值上限
    DataFormat::getFloat(in, m_stAmpBinaryData.fHFCTAmp);        //幅值数据
    DataFormat::getFloat(in, m_stAmpBinaryData.fHFCTMax);        //幅值最大值
    DataFormat::getInt32(in, m_stAmpBinaryData.iPulseCount);    //脉冲数
    //背景文件名称
    DataFormat::getAscii(in, DataFileNS::BG_FILE_NAME_LEN, m_stAmpBinaryData.strBGFileName);

    m_stBinaryExt.eAmpUnit = DataFileNS::AmpUnit(ampUnit);
/*
    qDebug() << "parse HFCT amp" << m_stExt.eAmpUnit << m_stExt.fAmpLowerLimit
             << m_stExt.fAmpUpperLimit << m_HFCTData.fHFCTAmp << m_HFCTData.fHFCTMax << m_HFCTData.iPulseCount
             << m_HFCTData.fSeverity;
*/
    return (in.status() == QDataStream::Ok);
}
AmpMapNS::AmpBinaryMapInfo AmpDataMap::BinaryInfo( )
{
    return  m_stBinaryExt;
}
AmpMapNS::AmpBinaryData AmpDataMap::BinaryData()
{
    return m_stAmpBinaryData;
}
