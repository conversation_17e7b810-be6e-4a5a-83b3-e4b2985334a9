/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* LabelButton.cpp
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月7日
* 摘要：控制按钮基类实现

* 当前版本：1.0
*/
#include <QVBoxLayout>
#include <QApplication>
#include <QDebug>
#include "LabelButton.h"
#include "Widget.h"
#include "controlButton/ControlButton.h"
#include "appfontmanager/appfontmanager.h"
#include "label/newtooltip.h"

const QString BUTTON_NORMAL_STYLE = "QFrame#labelButton{border-radius: 5px; border-width: 1px; border-color:gray;border-style: solid;"
        "background-color: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,stop: 0 #E0E0E0, stop: 1 #FFFFFF);}";
const QString BUTTON_PRESS_STYLE = "QFrame#labelButton{background-color: rgb(205, 232, 255); border-radius: 5px; border-width: 2px; border-color:blue;border-style: solid}";
const QString ARROW_RIGHT_PIXMAP = ":/images/arrow_right.png";
const QString ARROW_DOWN_PIXMAP = ":/images/arrow_down.png";
const int BASIC_BUTTON_HEIGHT = 75;
const int CONTENT_LABLE_WIDTH = 148; // 内容文字宽度
#define TITLEBAR_HEIGHT 102 //标题栏高度
#define LABELBUTTON_TEXT_RATIO 0.4 //文字的高度比例
/*************************************************
功能： 构造函数
输入参数:
    parent -- 父窗体
*************************************************************/
LabelButton::LabelButton( ControlButton::Type eBtnType, QWidget* parent )
    : CentralButton( parent ),
      m_plabelArrow(NULL),
      m_plabelTitle(NULL),
      m_plabelContent(NULL),
      m_eBtnType(eBtnType),
      m_bTitleIncompleteDisplay(false),
      m_bContentIncompleteDisplay(false)
{
    setObjectName("labelButton");

    setFrameStyle(QFrame::Panel | QFrame::Raised);

    m_stFont = AppFontManager::instance()->getAppCurFont();

    //标题
    m_plabelTitle = new QLabel( this );
    m_plabelTitle->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred); 

    //布局
    QBoxLayout *boxLayout = new QBoxLayout(QBoxLayout::LeftToRight);
    boxLayout->setContentsMargins(0, 0, 0, 0);
    boxLayout->addWidget(m_plabelTitle);
    if(m_eBtnType != ControlButton::CMD)
    {
        //箭头标签
        m_plabelArrow = new QLabel(this);
        m_plabelArrow->setPixmap(QPixmap( ARROW_RIGHT_PIXMAP ));
        m_plabelArrow->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);

        m_plabelContent = new QLabel(this);
        m_plabelContent->setAlignment(Qt::AlignVCenter | Qt::AlignRight);
        m_plabelContent->setContentsMargins(0, 0, 20, 0);
        m_plabelContent->setFixedWidth(CONTENT_LABLE_WIDTH);

        boxLayout->insertWidget(0, m_plabelArrow);
        boxLayout->addWidget(m_plabelContent);
    }

    setLayout(boxLayout);   // 创建各标签，设置布局、背景、焦点策略等

    setHeight( BASIC_BUTTON_HEIGHT );
    setStyleSheet( BUTTON_NORMAL_STYLE );
}

/*************************************************
功能： 设置标题
输入参数:
    strTitle -- 标题
*************************************************************/
void LabelButton::setTitle( const QString& strTitle )
{
    CentralButton::setTitle(strTitle);

    updateTitleText();
}

/*************************************************
功能： 设置数值
输入参数:
    strContent -- 值
*************************************************************/
void LabelButton::setContent( const QString& strContent )
{
    CentralButton::setContent(strContent);

    updateContentText();
}

/*************************************************
功能： 响应控制按钮激活状态
输入参数:
    bActive: 激活状态
*************************************************************/
void LabelButton::onActive( bool bActive )
{
    bool bNeedShowToolTip = false;
    if (NULL != m_plabelContent)
    {
        bNeedShowToolTip = m_bTitleIncompleteDisplay || m_bContentIncompleteDisplay;
    }
    else
    {
        bNeedShowToolTip = m_bTitleIncompleteDisplay;
    }

    if( bActive )
    {
        if(m_eBtnType != ControlButton::CMD)
        {
            m_plabelArrow->setPixmap(QPixmap( ARROW_DOWN_PIXMAP ));
        }
        setStyleSheet( BUTTON_PRESS_STYLE );
        setFrameStyle(QFrame::Panel | QFrame::Sunken);

        if (bNeedShowToolTip && !toolTip().isEmpty())
        {
            QFont font = m_stFont;
            font.setPixelSize(25);
            NewToolTip::setFont(font);
            // 如果是第一行按钮，Tooltip显示在下方
            QPoint leftTopPos = mapToGlobal(QPoint(0, 0));
            if (leftTopPos.y() <= TITLEBAR_HEIGHT)
            {
                leftTopPos.setY(TITLEBAR_HEIGHT + height());
                NewToolTip::showTextBelow(leftTopPos, toolTip(), this);
            }
            else
            {
                NewToolTip::showTextAbove(leftTopPos, toolTip(), this);
            }
        }
    }
    else
    {
        if(m_eBtnType != ControlButton::CMD)
        {
            m_plabelArrow->setPixmap(QPixmap( ARROW_RIGHT_PIXMAP ));
        }
        setStyleSheet( BUTTON_NORMAL_STYLE );
        setFrameStyle(QFrame::Panel | QFrame::Raised);

        if (bNeedShowToolTip && !toolTip().isEmpty())
        {
            NewToolTip::hideText();
        }
    }
}

/*************************************************
功能： 设置按钮高度
输入参数:
         usHeight -- 按键高度
*************************************************************/
void LabelButton::setHeight( quint16 usHeight )
{
    setFixedHeight( usHeight );

    //自适应字体大小
    QFont qFont = getReheightFont(m_stFont, usHeight * LABELBUTTON_TEXT_RATIO);
    m_plabelTitle->setFont( qFont );
    if (NULL != m_plabelContent)
    {
        m_plabelContent->setFont(qFont);
    }
}

/*************************************************
功能： 设置显示文本字体
输入参数:
    stFont -- 字体
*************************************************************/
void LabelButton::setFont(const QFont& stFont)
{
    m_stFont = getReheightFont(stFont, (this->height()) * LABELBUTTON_TEXT_RATIO);
    m_plabelTitle->setFont( m_stFont );
    if (NULL != m_plabelContent)
    {
        m_plabelContent->setFont(m_stFont);
    }
    return;
}

/*************************************************
功能： 返回显示文本字体
返回值:
    QFont -- 字体
*************************************************************/
QFont LabelButton::font() const
{
    return m_stFont;
}

/*************************************************
功能： 大小改变事件
输入参数:
    pEvent: 事件
*************************************************************/
void LabelButton::resizeEvent(QResizeEvent* pEvent)
{
    CentralButton::resizeEvent(pEvent);

    updateTitleText();

    updateContentText();
}

void LabelButton::updateTitleText()
{
    QString qstrNewTitle = title();
    int iTitleTextWidth = m_plabelTitle->fontMetrics().width(qstrNewTitle);
    m_bTitleIncompleteDisplay = iTitleTextWidth > m_plabelTitle->contentsRect().width();

    if (m_bTitleIncompleteDisplay)
    {
        qstrNewTitle = m_plabelTitle->fontMetrics().elidedText(qstrNewTitle, Qt::ElideRight, m_plabelTitle->contentsRect().width());
    }

    m_plabelTitle->setText(qstrNewTitle);
}

void LabelButton::updateContentText()
{
    if (NULL != m_plabelContent)
    {
        int iContentTextWidth = m_plabelContent->fontMetrics().width(content());
        m_bContentIncompleteDisplay = iContentTextWidth > m_plabelContent->contentsRect().width();
        QString qstrNewContent = content();
        if (m_bContentIncompleteDisplay)
        {
            qstrNewContent = m_plabelContent->fontMetrics().elidedText(qstrNewContent, Qt::ElideRight, m_plabelContent->contentsRect().width());
        }
        m_plabelContent->setText(qstrNewContent);
    }
}
