#include "prpsplayview.h"
#include "phasechart/phasedatamodel.h"
#include "Module.h"
#include <QVBoxLayout>
#include <QFile>
#include <QDateTime>
#include <QTimerEvent>
#include <QDebug>
#include <QStyle>
const int PRPS_VIEW_FACTOR = 8;            // 时间标签的拉伸因子
const int PROGRESS_SLIDER_FACTOR = 3;      // 进度滑块的拉伸因子
const int BUTTON_GROUP_FACTOR = 3;      // 进度滑块的拉伸因子
/************************************************
 * 功能: 构造函数
 * 入参：parent -- 父控件指针
 *     iInterval -- 每周期数据的时间间隔
 *     iPeriodCount -- 周期个数
 *     iPhaseCount -- 每周期相位个数
 *     dMax -- 最大值
 *     dMin -- 最小值
 ************************************************/
PrpsPlayView::PrpsPlayView(qint32 iInterval, qint32 iPeriodAll,
                             qint32 iPeriodCount, qint32 iPhaseCount,
                             double dMax, double dMin, QWidget *parent) :
    QFrame( parent),
    m_iInterval( iInterval ),
    m_iPeriodAll( iPeriodAll ),
    m_iPhaseCount( iPhaseCount ),
    m_iPeridoCurrent( 0 ),
    m_iTimerId( -1 ),
    m_lPlayTime( 0 ),
    m_bIsPlay( false ),
    m_currentData( iPhaseCount )
{
    setWindowFlags( Qt::FramelessWindowHint );
    setFocusPolicy( Qt::NoFocus );
    setObjectName( "VideoView" );       // 设置窗口属性，焦点属性，以及别名

    // 统一设置样式
    QFile qss(":/playvideoStyle.qss");
    qss.open( QFile::ReadOnly );
    setStyleSheet( QString(qss.readAll()) );
    qss.close();

    m_pPrpsView = new PhaseAbstractView( iPeriodCount, iPeriodCount,iPhaseCount,dMax,dMin,this );
    m_pPrpsView->setPrpsAccumulateEnabled( true );                  // 设置prps view为累积模式
    m_pPrpsView->setAccumulativeTime(Module::ScreenRecordTime_MAX); // 设置累积时长为录屏最大值，这样录屏回放期间prpd就是一直累积状态
    //m_pPrpsView->setBufferSize( iPeriodAll * iPhaseCount );         // 设置缓存的上限
    m_pPrpsView->setConnected( true );                              // 回放数据默认为连接上

    // 初始化滑块
    m_pProgress = new ProgressGroup( ProgressGroup::PLAY_PROGRESS,iPeriodAll,
                                     iInterval,this );
    m_pProgress->enableSliderDrag( false );                           // 未开始播放前不允许拖动滑块
    connect( m_pProgress,SIGNAL(sigPeriodShifted(int)),this,SLOT(onPeriodShifted(int)) );
    // 初始化按键集合
    m_pButtonGroup = new PlayButtonGroup( this );
    m_pButtonGroup->setFocus();         // 将焦点集中在按键部分
    // 绑定按键集合中的三种类型按键（后退、播放/暂停、快进）
    connect( m_pButtonGroup,SIGNAL(sigPeriodShifted(int)),this,SLOT(onPeriodShifted(int)) );
    connect( m_pButtonGroup,SIGNAL(sigPlayStateChanged(bool)),this,SLOT(onPlayStateChanged(bool)) );
    // 添加布局
    QVBoxLayout *vLayout = new QVBoxLayout( this );
    vLayout->setContentsMargins( 0,0,0,5 );             // 设置和边框间隙分别为左 上 右 下
    vLayout->setSpacing( 0 );
    vLayout->addWidget( m_pPrpsView, PRPS_VIEW_FACTOR);
    vLayout->addWidget( m_pProgress, PROGRESS_SLIDER_FACTOR );
    vLayout->addWidget( m_pButtonGroup, BUTTON_GROUP_FACTOR );
    setLayout( vLayout );


}

PrpsPlayView::~PrpsPlayView()
{
    if(m_iTimerId != -1)
    {
        killTimer(m_iTimerId);
    }
}

void PrpsPlayView::setGainState( PrpsGlobal::SpectrumState eSpectrumState )
{
    m_pPrpsView->setGainState(eSpectrumState);
}

/************************************************
 * 功能: 设置界面显示的信息
 * 入参：iPeriodCnt -- 总周期数
 *      iInterval -- 时间间隔
 ************************************************/
void PrpsPlayView::setViewInfo( qint32 iPeriodCnt,qint32 iInterval )
{
    m_pPrpsView->model()->clear();      // 切换进度信息时清空缓存数据
    m_pProgress->setProgressInfo( iPeriodCnt,iInterval );// 更新滑块信息

    m_iPeriodAll = iPeriodCnt;
    m_iPeridoCurrent = 0;
    m_iInterval = iInterval;                // 将各播放相关标志置位

    onPlayStateChanged( false );            // 停止播放
    m_pButtonGroup->setPlayState( false );  // 更新播放按钮图标
    //m_pPrpsView->setBufferSize( iPeriodCnt * m_iPhaseCount ); // 设置缓存大小
    //m_pPrpsView->model()->reset();          // 数据model进行重置，恢复初始值
}

/****************************
输入参数:rawData:Video类型全部数据
功能： 添加数据
业务逻辑：
    图谱推动的逻辑为每添加一次数据推动一周期数据
*****************************/
void PrpsPlayView::setData( const QVector< double > &rawData )
{
    PhaseData tmpData( m_iPhaseCount );

    PhaseData stData = m_pPrpsView->prpsToPhaseData( rawData );

    tmpData.append( stData );

    PhaseDataModel *pModel = m_pPrpsView->model();
    pModel->clear();


    pModel->addData( tmpData );

    m_currentData = tmpData;
}

void PrpsPlayView::clearModel()
{
    PhaseDataModel *pModel = m_pPrpsView->model();
    pModel->clear();
}

/****************************
输入参数:eSyncState:同步状态 eSyncSource
功能： 设置同步状态
*****************************/
void PrpsPlayView::setSync( PrpsGlobal::SyncSource eSyncSource,PrpsGlobal::SyncState eSyncState )
{
    m_pPrpsView->setSync( eSyncSource,eSyncState );
}

/************************************************
 * 功能: 设置prps图谱显示的 T=xxx
 ************************************************/
void PrpsPlayView::setDisplayedPeriodCnt(int iDisplayedPeriodCnt)
{
    m_pPrpsView->setDisplayedPeriodCnt(iDisplayedPeriodCnt);
}

/****************************
输入参数:eFrequency:设置电源频率
功能： 设置电源频率
*****************************/
void PrpsPlayView::setPowerFreq( PrpsGlobal::Frequency eFrequency )
{
    m_pPrpsView->setPowerFreq( eFrequency );
}

/************************************************
 * 输入参数:
 *          color: 坐标系线条及文字颜色
 * 功能: 设置坐标系线条及文字颜色
 ************************************************/
void PrpsPlayView::setCoordinateColor( const QColor& color )
{
    m_pPrpsView->setAxisColor( color );
}

/************************************************
 * 功能: 设置背景颜色
 * 入参：color -- 颜色
 ************************************************/
void PrpsPlayView::setBackGroundColor( const QColor& color )
{
    m_pPrpsView->setBackGroundColor( color );
}

/************************************************
 * 功能: 设置轴线颜色
 * 入参：color -- 颜色
 ************************************************/
void PrpsPlayView::setAxisColor( const QColor& color )
{
    m_pPrpsView->setAxisColor( color);
}

/************************************************
 * 输入参数:
 *        iPhase: 度数
 * 功能: 设置当前相位偏移度数
 ************************************************/
void PrpsPlayView::setPhaseOffset( qint32 iPhase )
{
    m_pPrpsView->setPhaseOffset( iPhase );
}

/************************************************
 * 输入参数:
 *          fThreshold: 阈值
 * 功能: 设置阈值
 ************************************************/
void PrpsPlayView::setPRPSThreshold(float fThreshold)
{
    m_pPrpsView->setPRPSThreshold(fThreshold);
}

/************************************************
 * 输入参数:
 *          fThreshold: 阈值
 * 功能: 设置阈值
 ************************************************/
void PrpsPlayView::setPRPDThreshold(float fThreshold)
{
    m_pPrpsView->setPRPDThreshold(fThreshold);
}

void PrpsPlayView::setDataItemColorStyle(Phase::DataItemColorStyle eStyle)
{
    if (NULL != m_pPrpsView)
    {
        m_pPrpsView->setDataItemColorStyle(eStyle);
    }
}

void PrpsPlayView::setPhaseAxisOffset(const int iPhaseAxisOffset)
{
    if (NULL != m_pPrpsView)
    {
        m_pPrpsView->setPhaseAxisOffset(iPhaseAxisOffset);
    }
}

void PrpsPlayView::setOriginXRatio(const int iOriginXRatio)
{
    if (NULL != m_pPrpsView)
    {
        m_pPrpsView->setOriginXRatio(iOriginXRatio);
    }
}

void PrpsPlayView::setAltasType(PhaseAbstractView::AltasType eAltasType)
{
    if (NULL != m_pPrpsView)
    {
        m_pPrpsView->setAltasType(eAltasType);
    }
}

void PrpsPlayView::setPrpdContentsMargins(int left, int top, int right, int bottom)
{
    if (NULL != m_pPrpsView)
    {
        m_pPrpsView->setPrpdContentsMargins(left, top, right, bottom);
    }
}

/************************************************
 * 输入参数:
 *        dMin: 量程最小值
 *        dMax:量程最大值
 * 功能: 设置量程
 ************************************************/
void PrpsPlayView::setRange( double dMin,double dMax )
{
    m_pPrpsView->setRangeMin( dMin );
    m_pPrpsView->setRangeMax( dMax );
}

/****************************
输入参数:qsMax:文本信息
功能： 设置最大频谱值
*****************************/
void PrpsPlayView::setMaxSpectrumText( const QString& qsMax )
{
    m_pPrpsView->setMaxSpectrumText( qsMax );
}

/************************************************
 * 输入参数:
 *        strSuffix: 单位名称(一般为mV或dB)
 * 功能: 设置单位名称
 ************************************************/
void PrpsPlayView::setSuffix( const QString& strSuffix )
{
    m_pPrpsView->setSuffix( strSuffix );
}

/************************************************
 * 功能: 响应手动拖动滑块或快进后退键
 * 入参：iValue -- slider值（相应移动周期数）
 ************************************************/
void PrpsPlayView::onPeriodShifted( int iValue )
{
    if( m_bIsPlay )
    {
        /*
         * 隐藏的业务逻辑为
         * 在手动拖动进度条或者响应快进后退时，先停止自动推进定时器，
         * 进度信息更新后再重新启动
        */
        stopPlay();

        //m_pPrpsView->model()->reset();
        //PhaseData ori
        m_pPrpsView->model()->clear();
        m_pPrpsView->model()->addData( m_currentData );
        m_iPeridoCurrent += iValue;
        m_iPeridoCurrent = m_pPrpsView->model()->advance( m_iPeridoCurrent );
        m_pProgress->setPeriod( m_iPeridoCurrent );

        startPlay();
    }
    else
    {
        //当前为未播放状态，则不处理slider的拖动和快进后退的信号
    }
}

/************************************************
 * 功能: 绑定播放状态的信号
 * 入参：true -- 播放
 *       false -- 暂停
 ************************************************/
void PrpsPlayView::onPlayStateChanged( bool bIsPlay )
{
    if( m_bIsPlay != bIsPlay )
    {
        m_bIsPlay = bIsPlay;
        m_pProgress->enableSliderDrag( m_bIsPlay );   // 非播放状态 滑块不可被拖动
        m_pPrpsView->setRunningMode( m_bIsPlay );   // 设置运行模式

        if( m_bIsPlay )
        {
            /* 业务逻辑：
             * 判断是否重新开始播放数据，若是清空当前显示
            */
            if( isRestartPlay( m_iPeridoCurrent ) )
            {
                m_pPrpsView->clearView();
            }
            else
            {
                // restart when pause
            }

            startPlay();            
            m_lPlayTime = QDateTime::currentMSecsSinceEpoch(); // 记下当前时间
        }
        else
        {
            stopPlay();
            m_lPlayTime = 0;                        // 初始化播放计时时间
        }
    }
    else
    {
        //same play state
    }
}

/************************************************
 * 功能: 便于在设定的定时间隔内完成推动
 * 入参：iValue -- slider值（相应移动周期数）
 ************************************************/
void PrpsPlayView::timerEvent( QTimerEvent *e )
{
    if( e->timerId() == m_iTimerId )
    {
        qint64 lCurrentTime = QDateTime::currentMSecsSinceEpoch();

        qint32 iStep = ( lCurrentTime - m_lPlayTime ) / m_iInterval;
        m_lPlayTime = lCurrentTime;             // 计算当前时间和相应的时间间隔，以便获知推进多少周期数据
        /*播放未结束时方做以下操作
         * 业务逻辑为 --
         * 1）当前播放已经结束 -- 重置（
         *                    刷新播放状态
         *                    清空当前运行周期标识
         *                    重置view的数据模型
         *                    更新按键样式 ）
         * 2）当前正在播放 -- 根据实际推进周期，更新当前时间
        */
        if( !isPlayFinished( m_iPeridoCurrent,m_iPeriodAll ) )
        {
            // 根据实际推进的周期数，更新当前周期计数值
            m_iPeridoCurrent += m_pPrpsView->model()->advance( iStep );
            m_pProgress->setPeriod( m_iPeridoCurrent ); // 更新进度条信息显示
        }
        else
        {
            reset();  // 播放结束后，进行重置
        }
    }
    else
    {
        //timer id is wrong
    }
}

/************************************************
 * 功能: 开始播放
 ************************************************/
void PrpsPlayView::startPlay( void )
{
    if( -1 == m_iTimerId )
    {
        m_iTimerId = startTimer( m_iInterval );
    }
    else
    {
        //timer already start
    }
}

/************************************************
 * 功能: 停止播放
 ************************************************/
void PrpsPlayView::stopPlay( void )
{
    if( -1 != m_iTimerId )
    {
        killTimer( m_iTimerId );
        m_iTimerId = -1;
    }
    else
    {
        //timer already stop
    }
}

/************************************************
 * 功能: 判断是否播放结束
 * 入参：iCurrentPeriod -- 当前推进的步数（周期数）
 *      iPeriodAll -- 共有周期数
 * 返回值：是否播放结束 true -- 是
 *                  false -- 否
 ************************************************/
bool PrpsPlayView::isPlayFinished( qint32 iCurrentPeriod,qint32 iPeriodAll )
{
    bool bIsFinished = false;
    if( iCurrentPeriod == iPeriodAll )
    {
        bIsFinished = true;
    }
    return bIsFinished;
}

/************************************************
 * 功能: 判断是否重新开始播放
 * 入参：iCurrentPeriod -- 当前推进的步数（周期数）
 * 返回值：是否重新开始播放 true -- 是
 *                      false -- 否
 ************************************************/
bool PrpsPlayView::isRestartPlay( qint32 iCurrentPeriod )
{
    bool bIsRestart = false;
    if( 0 == iCurrentPeriod )
    {
        bIsRestart = true;
    }
    else
    {
        bIsRestart = false;
    }
    return bIsRestart;
}

/************************************************
 * 功能: 播放结束后进行重置
 ************************************************/
void PrpsPlayView::reset( void )
{
    onPlayStateChanged( false );            // 更新播放状态为停止
    m_pButtonGroup->setPlayState( false );  // 更新button的状态为停止播放
    m_iPeridoCurrent = 0;                   // 将当前播放周期的计数值清零
    //m_pPrpsView->model()->reset();          // 将数据模型重置方便下次继续播放
    m_pPrpsView->model()->addData( m_currentData );
    //TODO 界面清空，将录制数据重新add到buffer中
}

/****************************
输入参数: eSyncSource 同步源
功能： CA回放录屏不显示同步丢失
*****************************/
void PrpsPlayView::setSync( PrpsGlobal::SyncSource eSyncSource )
{
    m_pPrpsView->setSync( eSyncSource );
}

/****************************
输入参数:NULL
功能： 隐藏同步源显示
*****************************/
void PrpsPlayView::clearSyncText()
{
    m_pPrpsView->clearSyncText();
}

