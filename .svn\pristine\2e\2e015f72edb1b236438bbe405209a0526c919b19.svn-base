#include "querytestlistcontroller.h"
#include <QFileInfo>
#include <QDateTime>
#include "../appserverdefine.h"
#include "appserverutils.h"
#include "pda/pdaservice.h"
#include "time.h"
#include "global_log.h"
#include "pda/cloud/qjason/qjson.h"
#include "timezonemanager/timezonemanager.h"
#include "log/log.h"

using namespace PDAServiceNS;
using namespace AppServerNS;

QueryTestListController::QueryTestListController(QObject *parent) : HttpRequestHandler(parent)
{

}

QueryTestListController::~QueryTestListController()
{

}

//由于对接app单拉测试数据时，http接口里的type节点对应的枚举不同于对接pdst的获取数据列表的接口里的type节点，
//故使用该函数作数据类型转换
TestedDataType QueryTestListController::appType2PdstType(TestListDataType eAPPType)
{
    TestedDataType eType = ALL_DATA_TYPE;
    switch (eAPPType)
    {
        case TEST_LIST_DATA_TYPE_TEV_AMP:
            eType = TEV_AMP_MAP;
            break;
        case TEST_LIST_DATA_TYPE_AE_AMPLITUDE:
            eType = AE_AMP_WITH_MAP;
            break;
        case TEST_LIST_DATA_TYPE_AE_WAVE:
            eType = AE_WAVE_WITH_MAP;
            break;
        case TEST_LIST_DATA_TYPE_AE_PHASE:
            eType = AE_PHASE_WITH_MAP;
            break;
        case TEST_LIST_DATA_TYPE_AE_FLY:
            eType = AE_FILGHT_WITH_MAP;
            break;
        case TEST_LIST_DATA_TYPE_UHF_AMP:
            eType = UHF_AMP_MAP;
            break;
        case TEST_LIST_DATA_TYPE_UHF_PRPS:
            eType = UHF_PRPS_PRPD_WITH_MAP;
            break;
        case TEST_LIST_DATA_TYPE_UHF_PERIOD:
            eType = UHF_PERIOD_WITH_MAP;
            break;
        case TEST_LIST_DATA_TYPE_HFCT_AMP:
            eType = HFCT_AMP_MAP;
            break;
        case TEST_LIST_DATA_TYPE_HFCT_PRPS:
            eType = HFCT_PRPS_PRPD_WITH_MAP;
            break;
        case TEST_LIST_DATA_TYPE_HFCT_PERIOD:
            eType = HFCT_PERIOD_WITH_MAP;
            break;
        case TEST_LIST_DATA_TYPE_INFRARED:
            eType = INFRARED_WITH_MAP;
            break;
        case TEST_LIST_DATA_TYPE_TEV_PRPS:
            eType = TEV_PRPS;
            break;
        default:
            break;
    }

    return eType;
}

void QueryTestListController::service( HttpRequest& request, HttpResponse& response )
{
    int responseCode = REPLY_SUCCESS_CODE;
    /*
    //解析request判断有效性
    long requestTime = request.getParameter( TIME_STAMP_KEY ).toLong();
    if( !AppServerUtils::isTimestampValid( requestTime ) )
    {
        responseCode = SIGNATURE_INVALID_ERR;
    }
    */

    //生成应答报文
    QJson respJson;//应答的Json内容

    if( REPLY_SUCCESS_CODE == responseCode )
    {
        //拉取文件开始时间
        long startTs =request.getParameter(START_TIME).toLong();
        startTs = TimezoneManager::instance()->formatUTCTimeToLocalValSec(startTs);
        //拉取文件结束时间
        long endTs = request.getParameter(END_TIME).toLong();
        endTs = TimezoneManager::instance()->formatUTCTimeToLocalValSec(endTs);

        log_debug("start time: %s, %s, end time %s, %s.", \
                  QString::number(startTs).toLatin1().data(), \
                  TimezoneManager::instance()->getFormatTimeStr(startTs).toLatin1().data(), \
                  QString::number(endTs).toLatin1().data(), \
                  TimezoneManager::instance()->getFormatTimeStr(endTs).toLatin1().data());

        TestListDataType eAPPType =(TestListDataType)request.getParameter(TEST_TYPE).toInt();//测试类型
        TestedDataType eType = appType2PdstType(eAPPType);
        logDebug(QString("query test list, type: %1, pdst type: %2.").arg(eAPPType).arg(eType));
        if(eType == ALL_DATA_TYPE)
        {
            logError("test list not support type, all data type.");
        }
        else
        {
            //通过查找指定类型下所有单测数据文件得到所有符合路径
            QList<QString> testDataPaths = PDAService::instance()->testDataFiles(eType, startTs, endTs);
            logDebug(QString("test list file size: %1.").arg(testDataPaths.size()));
            QJson resultJson(QJson::Array);
            for(int i = 0, iSize = testDataPaths.size(); i < iSize; ++i)
            {
                QJson tempJason;
                QFileInfo fileInfo(testDataPaths.at(i));
                uint ctime = fileInfo.created().toTime_t();
                ctime = TimezoneManager::instance()->formatLocalTimeToUTCValSec(ctime);
                QString fileName = fileInfo.fileName();

                //完整路径
                tempJason.add(TEST_LIST_PATH_KEY, testDataPaths.at(i).toUtf8());
                //文件名
                tempJason.add(FILE_NAME_KEY, fileName.toLatin1());
                //文件创建时间戳
                tempJason.add(CREATE_TIME, QString::number(ctime).toLatin1());

                resultJson.addItemToArray(tempJason);
            }
            respJson.add(REPLY_RESULT_KEY, resultJson);
        }
    }

    //生成应答
    respJson.add(REPLY_CODE_KEY, QString::number(responseCode).toLatin1());
    respJson.add(REPLY_MSG_KEY, AppServerUtils::stateMsgByCode(responseCode).toUtf8());

    response.write(respJson.unformattedData(), true);       //解决content-length没有的问题
    return;
}
