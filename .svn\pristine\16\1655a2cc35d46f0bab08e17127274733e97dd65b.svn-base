#ifndef REMARKSERVICE_H
#define REMARKSERVICE_H

#include <QObject>
#include "module_global.h"
#include "Module.h"
#include <QSettings>
#include <QList>
#include <QMutex>

typedef struct _RemarkSaveInfo_
{
    QString qstrName;
    QString qstrInfo;

    _RemarkSaveInfo_()
    {
        qstrName = "";
        qstrInfo = "";
    }

    bool operator==(const _RemarkSaveInfo_ &stOther)
    {
        return (this->qstrName == stOther.qstrName && this->qstrInfo == stOther.qstrInfo);
    }

}RemarkSaveInfo;

class MODULESHARED_EXPORT RemarkService : public QObject
{
    Q_OBJECT
public:
    explicit RemarkService(QObject *parent = 0);
    ~RemarkService();

    /****************************
    功能： 模块单例
    *****************************/
    static RemarkService* instance();

    /*************************************************
    功能： 保存文件备注信息
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    //void saveRemarkInfo(QString qsFileName, QString qsRemarkInfo);

    /*************************************************
    功能： 获取文件备注信息
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    QList<RemarkSaveInfo> getRemarkInfo();

    /*************************************************
    功能： 获取指定文件备注信息
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    QString getSingleRemarkInfo(QString qsFileName);

    /*************************************************
    功能： 将文件备注信息刷到临时数据区
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    void refreshInfos();


signals:

public slots:

private:
    QSettings* m_pSettings;
    QMutex m_qmt4RemarkInfos;
    QList<RemarkSaveInfo> m_qlstInfos;
};

#endif // REMARKSERVICE_H
