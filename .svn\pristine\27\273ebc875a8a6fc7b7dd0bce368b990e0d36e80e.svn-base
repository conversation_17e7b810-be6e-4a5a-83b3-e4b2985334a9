#include "linklayerprotocolbase.h"
#include <QDateTime>
#include <QString>
#include <QDebug>
#include <QEventLoop>
#include <QTimer>
#include "comm/abstractcomm.h"
#include "datadefine.h"
#include "log/log.h"


/****************************
功能： 构造函数
输入参数:
    pCom -- 通讯组件
    parent -- 父控件
*****************************/
LinkLayerProtocolBase::LinkLayerProtocolBase(AbstractComm *pCom, QObject *parent)
    : QObject(parent)
    , m_pCom(pCom)
    , m_uiWantLen(0)
    , m_bSendingMultiFrame(false)
{

}

/********************************
 * 析构函数
 * ********************************/
LinkLayerProtocolBase::~LinkLayerProtocolBase()
{

}

/***********************************************************
 * 功能：是否发送分帧数据中
 * 返回值：
 *      bool：结果，true -- 分帧发送中，false -- 未处于分帧发送中
 * *********************************************************/
bool LinkLayerProtocolBase::isSendingMultiFrame()
{
    return m_bSendingMultiFrame;
}

/****************************
功能： 根据error code添加不同打印，方便调试
输入参数:
    eErrorCode -- error类型
*****************************/
QString LinkLayerProtocolBase::error(Protocol::ErrorCode eErrorCode)
{
    QString qstrErrInfo = "";
    switch(eErrorCode)
    {
    case Protocol::ERROR_NONE:
    {
        qstrErrInfo = "no error";
        break;
    }
    case Protocol::READ_TIME_OUT:
    {
        qstrErrInfo = "read timeout";
        break;
    }
    case Protocol::HEAD_NOT_VALID:
    {
        qstrErrInfo = "head is not valid";
        break;
    }
    case Protocol::FRAME_NOT_VALID:
    {
        qstrErrInfo = "frame is not valid";
        break;
    }
    case Protocol::CRC_NOT_VALID:
    {
        qstrErrInfo = "crc is not valid";
        break;
    }
    case Protocol::HEAD_LENGTH_SHORT:
    {
        qstrErrInfo = "head length is too short";
        break;
    }
    case Protocol::OFFSET_NOT_VALID:
    {
        qstrErrInfo = "offset is not valid";
        break;
    }
    case Protocol::GET_FRAME_TOO_SHORT:
    {
        qstrErrInfo = "frame length is too short";
        break;
    }
    case Protocol::FRAME_NOT_COMPLETE:
    {
        qstrErrInfo = "frame is not complete";
        break;
    }
    case Protocol::OTHER_ERROR:
    {
        qstrErrInfo = "other error";
        break;
    }
    case Protocol::CRC_CHECK_ERR:
    {
        qstrErrInfo = "crc check error";
        break;
    }
    case Protocol::UNSUPPORT_CMD:
    {
        qstrErrInfo = "unsupport cmd id";
        break;
    }
    case Protocol::COMM_SERIAL_ERR:
    {
        qstrErrInfo = "communicate serial number error";
        break;
    }
    case Protocol::REQ_DATA_ERR:
    {
        qstrErrInfo = "deal request data error";
        break;
    }
    case Protocol::RSP_DATA_ERR:
    {
        qstrErrInfo = "deal response data error";
        break;
    }
    case Protocol::HEAD_TAG_ERR:
    {
        qstrErrInfo = "head tag error";
        break;
    }
    case Protocol::ZIP_FORMAT_ERR:
    {
        qstrErrInfo = "zip format error";
        break;
    }
    case Protocol::DATA_NOT_COMPLETE:
    {
        qstrErrInfo = "data not recv complete";
        break;
    }
    case Protocol::DATA_SEND_FAILED:
    {
        qstrErrInfo = "data send failed";
        break;
    }
    case Protocol::UNKNOWN_ERR:
    default:
    {
        qstrErrInfo = "unknown error";
        break;
    }

    }

    logError(qstrErrInfo);
    return qstrErrInfo;
}

/****************************
功能： ilBytesAvailable：可读取的数据大小
输入参数:
    bytesAvailable -- 缓冲区可读取的数据，单位为字节
*****************************/
void LinkLayerProtocolBase::onReadyRead(qint64 bytesAvailable)
{
    Q_UNUSED(bytesAvailable);
    return;
}

/****************************
功能： 发送数据内容
输入参数:
    data -- 数据内容
    stParam -- 协议参数
*****************************/
void LinkLayerProtocolBase::onSendData(const QByteArray &data, const Protocol::ProtocolParam &stParam)
{
    Q_UNUSED(data);
    Q_UNUSED(stParam);
    return;
}

/****************************
功能： 发送数据内容
输入参数:
    stFrameInfo -- 帧信息
*****************************/
void LinkLayerProtocolBase::onSendData_EX(const Protocol::CommFrameInfo &stFrameInfo)
{
    Q_UNUSED(stFrameInfo);
    return;
}

