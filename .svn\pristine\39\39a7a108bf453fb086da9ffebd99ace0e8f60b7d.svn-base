/*
* Copyright (c) 2016.05, 华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: FastWaveUT.h
*
* 初始版本: 1.0
* 作者: 王谦
* 创建日期: 2016年05月6日
* 摘要: 该文件定义了百兆采集接入G100功能，u-t图谱
*/

#ifndef FASTWAVEUT_H
#define FASTWAVEUT_H

#include <QWidget>
#include <QMouseEvent>
#include <thirdparty/qwt/qwt_symbol.h>
#include <thirdparty/qwt/qwt_plot_marker.h>
#include <thirdparty/qwt/qwt_plot_zoomer.h>
#include <QToolButton>
#include "DriverDataDefine.h"
#include "FastWaveBase.h"
#include "autoMatker.h"
#include "nodeInformation.h"

class FastWaveUT : public FastWaveBase
{
    Q_OBJECT
public:
    typedef enum _JumpDirection
    {
        JUMP_TO_MAX = 0,//跳转至最大值
        JUMP_TO_MIN // 跳转至最小值
    }JumpDirection;
public:
    /************************************************
     * 函数名    :FastWaveUT
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：构造函数
     ************************************************/
    explicit FastWaveUT(QWidget *parent = 0);

    /************************************************
     * 函数名    :FastWaveUT
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：析构函数
     ************************************************/
     ~FastWaveUT();

    /************************************************
     * 函数名    :setScale
     * 输入参数  ：min -- 最小值
     *            max -- 最大值
     *            step -- 步进
     *           eAxisScale -- 横、纵坐标轴方向
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置横、纵坐标轴方向的范围和步进
     ************************************************/
    void setScale( AxisScale eAxisScale,double min,double max,double step = 0 );

    void xScale( float &fMin,float &fMax );

    /************************************************
     * 功能     ：设置小数点后位数
     ************************************************/
    void setDotCnt( nodeInformation::DotCnt eCnt );

    /************************************************
     * 函数名    :enableMarker
     * 输入参数  ：使能标志
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置是否隐藏标记线
     ************************************************/
    void enableMarker( bool bEnabled );

    /************************************************
     * 函数名    :enablePowerReference
     * 输入参数  ：使能标志
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置是否隐藏工频参考
     ************************************************/
    void enablePowerReference( bool bEnabled );

    /************************************************
     * 函数名    :jumpTo
     * 输入参数  ：eJumpDirection -- 跳转方向
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：跳转到最小、大值
     ************************************************/
    void jumpTo( JumpDirection eJumpDirection );

    /************************************************
     * 函数名    :zoomNext
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：退回之前的缩放状态
     ************************************************/
    void zoomNext( void );

    /************************************************
     * 函数名    :setDatas
     * 输入参数  ：xData -- x轴数据集合
     *           yData -- y轴数据集合
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：添加数据
     ************************************************/
    void setDatas( const QVector< double > &xData, const QVector< double > &yData );

    /************************************************
     * 函数名    :plotDatas
     * 输入参数  ：xData -- x轴数据集合
     *           yData -- y轴数据集合
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：添加数据
     ************************************************/
    void plotDatas( const QVector< double > &xData, const QVector< double > &yData );

    /************************************************
     * 函数名    :xDatas
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：x轴数据
     ************************************************/
    QVector< double > const& xDatas ( void ) const;

    /************************************************
     * 函数名    :yDatas
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：y轴数据
     ************************************************/
    QVector< double > const& yDatas ( void ) const;

    /************************************************
     * 函数名    :setZoomEnabled
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：使能放大缩小功能
     ************************************************/
    void setZoomEnabled( bool bEnbable );

    /************************************************
     * 功能     ：更新极值
     ************************************************/
    void replotLimitValue( bool bUpdate );

protected:
    /************************************************
     * 函数名    :eventFilter
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：事件过滤
     ************************************************/
    bool eventFilter(QObject *, QEvent *);

    /************************************************
     * 函数名    :axisScaleChanged
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：父类重新设置x，y轴最大最小值时调用，便于子类将类似工频参考等数据重新处理
     ************************************************/
     void axisScaleChanged( void );

     /************************************************
      * 函数名    :clearEvent
      * 输入参数  ：NULL
      * 输出参数  ：NULL
      * 返回值   ：NULL
      * 功能     ： 清除显示数据时，若此时标记线和数据有交点，清除显示
      ************************************************/
     void clearEvent( void );

     /*********************************************
     功能：重绘事件
     输入参数： event -- 事件
     *********************************************/
     void paintEvent( QPaintEvent *event );

private slots:
     /************************************************
      * 函数名    :onZoomed(slot)
      * 输入参数  ：rect:放大/缩小的区域
      * 输出参数  ：NULL
      * 返回值   ：NULL
      * 功能     ：槽函数，绑定放大缩小控件，获得放大/缩小的区域
      ************************************************/
     void onZoomed( const QRectF &rect );

     /*********************************************
     功能：放大返回
     *********************************************/
     void onZoomBack( void );

private:
    typedef enum _ZoomedStatus
    {
        ZOOMED_NONE = 0,//未放大
        ZOOMED_ALREADY // 已放大
    }ZoomedStatus;

private:
    /************************************************
     * 函数名    :dataInit
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：参数初始化
     ************************************************/
    void dataInit( void );

    /************************************************
     * 函数名    :moveMarker
     * 输入参数  ：dOffset:偏移量
     * 输出参数  ：NULL
     * 返回值   ：INT32,标记线和数据线的交点的下标
     * 功能     :移动标记线，只在各数据点之间移动
     ************************************************/
    INT32 moveMarker( double dOffset );

    /************************************************
     * 函数名    :initReferenceCurve
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：初始化工频参考曲线
   ************************************************/
    void initReferenceCurve( void );

    /************************************************
     * 函数名    :createUI
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：创建界面元素
   ************************************************/
    void createUI( void );

    /************************************************
     * 函数名    :adjustSizeByZoomStatus
     * 输入参数  ：eZoomedStatus -- 当前放大的状态
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：根据放大状态调整界面尺寸，需求有提及在放大状态时获取到新的数据，界面横坐标初始状态，纵坐标保留放大状态
   ************************************************/
    void adjustSizeByZoomStatus( ZoomedStatus eZoomedStatus );

    /************************************************
     * 函数名    :judgeMarkerIsValid
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：判断标记线是否在范围内可见，有需求 需要在marker不可见时，界面能够获知该信息，将marker隐藏
   ************************************************/
    void judgeMarkerIsValid( const QRectF& rect );

    /************************************************
     * 函数名    :findMinimumIndex
     * 输入参数  ：data -- 数据容器
     * 输出参数  ：NULL
     * 返回值   ：HC_FAILURE 失败的标志
     * 功能     ：获取容器中最小值的序号
   ************************************************/
    int findMinimumIndex(const QVector< double > & data);

    /************************************************
     * 函数名    :findMaximumIndex
     * 输入参数  ：data -- 数据容器
     * 输出参数  ：NULL
     * 返回值   ：HC_FAILURE 失败的标志
     * 功能     ：获取容器中最大值的序号
   ************************************************/
    int findMaximumIndex( const QVector< double > & data );

    /************************************************
     * 函数名    :updateZoomerBase
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：更新放大缩小控件的默认大小，即返回时默认的大小
     ************************************************/
    void updateZoomerBase( void );

    /************************************************
     * 函数名    :markerIndexOffset
     * 输入参数  ：dOffset -- 偏移量
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：偏移后marker的值相对应的数据下标
     ************************************************/
    INT32 markerIndexOffset( double dOffset );

    /************************************************
     * 函数名    :globalMapToChart
     * 输入参数  ：pressPoint -- 点击的坐标
     * 输出参数  ：NULL
     * 返回值   ：转换后的坐标
     * 功能     ：将鼠标点击的坐标转换成图谱中的坐标
     ************************************************/
    QPointF globalMapToChart( const QPointF &pressPoint );

    /************************************************
     * 函数名    :updateNodeInformation
     * 输入参数  ：iMarkerIndex -- 标记线对应的数值序号
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：更新交点信息的 显示
     ************************************************/
    void updateNodeInformation( INT32 iMarkerIndex );

    /*********************************************
    功能：创建放大返回按键
    *********************************************/
    void createZoomBackButton( void );
signals:
    /************************************************
     * 函数名    :sigMarkerOutside
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：标记线超出显示范围发出的信号
     ************************************************/
    void sigMarkerOutside();

private:
    QwtPlotCurve *m_pRefrenceCurve;     // 工频参考曲线
    bool m_bRefrenceEnabled;            // 工频参考线使能标志
    autoMatker *m_pMarker;           // 标记线
    bool m_bMarkerEnabled;              // 标记线使能标志

    QPoint m_PressPoint;                // 记录press时的点
    bool m_bIsPressed;                  // 是否点击在标记线范围内的标志
    INT32 m_iMarkerIndex;               // 标记线对应值的序号
    UINT16 m_usMoveEventCnt; // 记录move事件产生的次数，加限制即拖动若干次刷新一次

    QwtPlotZoomer   *m_pZoomer;         // 放大缩小控件；
    //图谱缩放相关
    QToolButton * m_pHomeButton;    //返回原有比例按钮
    nodeInformation *m_pNodeInformation; // 交点信息

    bool m_bIsZoomed;   // 用于确认是否放大缩小的标志
    ZoomedStatus m_eZoomedStatus; // 标志当前是否处于放大状态的标志

    QVector< double >  m_vXdata;
    QVector< double >  m_vYdata;
    double m_dXmax;
    double m_dXmin;
    double m_dYmax;
    double m_dYmin;

    bool m_bResetXCale;
};

#endif // FASTWAVEUT_H
