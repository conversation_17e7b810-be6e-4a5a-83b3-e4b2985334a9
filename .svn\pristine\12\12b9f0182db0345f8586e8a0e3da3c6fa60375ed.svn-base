#include "infraredparameterrotatedialog.h"
#include <QPaintEvent>
#include <QPainter>
#include <QGraphicsScene>
#include <QHBoxLayout>
#include <QGraphicsView>
#include <QApplication>
#include <QScrollBar>
#include "datadefine.h"
#include <softkeyboard.h>
#include "systemsetting/systemsetservice.h"


const QString SCOROLLBAR_STYLE = "QScrollBar:vertical"
                                 "{"
                                 "width:30px;"
                                 "background:rgba(0,0,0,0%);"
                                 "margin:0px,0px,0px,0px;"
                                 "padding-top:9px;"
                                 "padding-bottom:9px;"
                                 "}"
                                 "QScrollBar::handle:vertical"
                                 "{"
                                 "width:8px;"
                                 "background:rgba(0,0,0,25%);"
                                 " border-radius:4px;"
                                 "min-height:20;"
                                 "}"
                                 "QScrollBar::handle:vertical:hover"
                                 "{"
                                 "width:8px;"
                                 "background:rgba(0,0,0,50%);"
                                 " border-radius:4px;"
                                 "min-height:20;"
                                 "}"
                                 "QScrollBar::add-line:vertical"
                                 "{"
                                 "height:9px;width:8px;"
                                 "subcontrol-position:bottom;"
                                 "}"
                                 "QScrollBar::sub-line:vertical"
                                 "{"
                                 "height:9px;width:8px;"
                                 "subcontrol-position:top;"
                                 "}"
                                 "QScrollBar::add-line:vertical:hover"
                                 "{"
                                 "height:9px;width:8px;"
                                 "subcontrol-position:bottom;"
                                 "}"
                                 "QScrollBar::sub-line:vertical:hover"
                                 "{"
                                 "height:9px;width:8px;"
                                 "subcontrol-position:top;"
                                 "}"
                                 "QScrollBar::add-page:vertical,QScrollBar::sub-page:vertical"
                                 "{"
                                 "background:rgba(0,0,0,10%);"
                                 "border-radius:4px;"
                                 "}";
const int WIDTH_OFFSET = 10;  // 横坐标偏移
const int HEIGHT_OFFSET = 30;  // 纵坐标偏移
const int DIALOG_WIDTH = 460;   //对话框宽度
const int DIALOG_HEIGHT = 500;  //对话框高度
/*****************************************************************
 * 函数名   : RotateDialog
 * 输入参数 :  pDialog -- 待旋转对话框
 *            angle -- 角度
 *           parent: 父对象指针
 * 输出参数 : NULL
 * 返回值  : NULL
 * 功能   : 构造函数
 ****************************************************************/
InfraredParameterRotateDialog::InfraredParameterRotateDialog(QDialog *pDialog, int angle, QWidget *parent) :
    QDialog(parent)
{
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_DeleteOnClose);
    QGraphicsScene *pScene = new QGraphicsScene(this);
    pScene->addWidget(pDialog);

    QGraphicsView *pView = new QGraphicsView(pScene, this);

    pView->setFixedSize( DIALOG_WIDTH,DIALOG_HEIGHT );
    setFixedSize( DIALOG_WIDTH,DIALOG_HEIGHT );

    pView->setFrameShape(QFrame::NoFrame);
    pView->setBackgroundBrush(QBrush(QColor(0, 0, 0, 0)));
    pView->setAttribute(Qt::WA_TranslucentBackground);
    pView->setAutoFillBackground(true);
    pView->rotate(angle);
    pView->setVerticalScrollBarPolicy( Qt::ScrollBarAlwaysOff );
    pView->horizontalScrollBar()->setStyleSheet( SCOROLLBAR_STYLE );
    pView->setContentsMargins( 0,0,0,0 );
    pView->setFocusProxy(pDialog);

    QHBoxLayout *hLayout = new QHBoxLayout(this);
    hLayout->setSpacing( 0 );
    hLayout->setContentsMargins(0, 0, 0, 0);
    hLayout->addWidget(pView);

    this->move( WIDTH_OFFSET,HEIGHT_OFFSET );

    connect(pDialog, SIGNAL(accepted()), this, SLOT(accept()));
    connect(pDialog, SIGNAL(rejected()), this, SLOT(reject()));
    connect( this,SIGNAL(finished(int)),this,SLOT(onFinished()) );
}

/*****************************************************************
 * 函数名   : paintEvent
 * 输入参数 :
 *           e: 重绘事件指针
 * 输出参数 : NULL
 * 返回值  : NULL
 * 功能   : 实现圆角绘制
 ****************************************************************/
void InfraredParameterRotateDialog::paintEvent(QPaintEvent *e)
{
    Q_UNUSED(e)

    QPainter painter(this);

    painter.setPen(Qt::NoPen);

    painter.setBrush(QBrush(QColor(0, 0, 0, 0)));
    painter.drawRect(rect());
}

/*****************************************************************
 * 函数名   : showEvent
 * 输入参数 :NULL
 * 输出参数 : NULL
 * 返回值  : NULL
 * 功能   : 重写显示事件
 ****************************************************************/
void InfraredParameterRotateDialog::showEvent( QShowEvent* )
{
    SoftKeyBoard::setAttribute( KEYBOARD_ROTATE_PROPERTY,SoftKeyBoard::ROTATE_90_ANGLE );
    SoftKeyBoard::setAttribute( KEYBOARD_DISPLAY_PROPERTY,SoftKeyBoard::SHOW_NUMBER_PUNC_PAGE );
    SoftKeyBoard::setAttribute( KEYBOARD_LOCATE_PROPERTY,SoftKeyBoard::LOCATE_BOTTOM );
    SoftKeyBoard::setAttribute( KEYBOARD_STYLE_PROPERTY,SoftKeyBoard::STYLE_WHITE );
    SoftKeyBoard::setAttribute( KEYBOARD_MODALITY_PROPERTY,SoftKeyBoard::APPLICATION_MODAL );
    return;
}

/*****************************************************************
 * 函数名   : onFinished
 * 输入参数 :NULL
 * 输出参数 : NULL
 * 返回值  : NULL
 * 功能   : 响应该控件关闭信号，重置键盘
 ****************************************************************/
void InfraredParameterRotateDialog::onFinished( void )
{
    SoftKeyBoard::resetAttribute();
    SystemSetService::instance()->closeKeyBoard();
    SystemSetService::instance()->initKeyBoard();
    return;
}
