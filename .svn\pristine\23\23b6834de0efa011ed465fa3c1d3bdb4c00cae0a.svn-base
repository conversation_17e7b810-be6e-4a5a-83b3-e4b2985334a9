#include "HCProtocol.h"

/*************************************************
功能： 构造函数
输入参数:
    parent -- 父Object
输出参数：NULL
返回值： NULL
*************************************************/
HCProtocol::HCProtocol(QObject *parent)
    : QObject(parent)
{
    m_pCom = NULL;
}

/*************************************************
功能： 设置通讯组件
输入参数:
    pCom -- 通讯组件
输出参数：NULL
返回值： NULL
*************************************************/
void HCProtocol::setCom( HCCom* pCom )
{
    if( m_pCom != pCom )
    {
        //非空得先停止
        if( NULL != m_pCom )
        {
            disconnect( m_pCom, 0, this, 0 );
            m_pCom->close();
        }
        m_pCom = pCom;
        connect( pCom, SIGNAL(sigDataReady()), this, SLOT(onDataReady() ) );
    }
    else
    {
        //did nothing if the same Com
    }
}

/*************************************************
功能： 通讯组件
输入参数:void
输出参数：NULL
返回值： NULL
*************************************************/
HCCom* HCProtocol::com( void )
{
    return m_pCom;
}
