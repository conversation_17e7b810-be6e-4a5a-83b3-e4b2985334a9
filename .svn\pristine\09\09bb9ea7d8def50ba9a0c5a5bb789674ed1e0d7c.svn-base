#include "searchfunc.h"
#include <QHBoxLayout>
#include "Widget.h"

/**************************************************
 *          载入gif的路径
 * ***********************************************/
const QString WAITING_MOVIE_PATH = ":/gif/loading.gif";

/**************************************************
 *          伸缩因子，布局使用
 * ***********************************************/
//按键伸缩因子
typedef enum _LabelStretch
{
    TEXT_STRETCH = 1,//文本的伸缩因子
    LOAD_STRTCH = 4, //等待图标的伸缩因子
}LabelStretch;

#define WIDTH_RATIO 0.8
#define HEIGHT_RATION 0.5
#define DEFAULT_FONT 30

/*************************************************
输入参数:
       parent -- 父窗体指针
功能： 构造函数
*************************************************************/
SearchFunc::SearchFunc(QWidget *parent) :
    QFrame(parent)
{
    m_pGifMovie = new QMovie(this);
    m_pGifMovie->setFileName(":/gif/loading.gif");

    m_pGifLabel = new QLabel(this);
    m_pGifLabel->setAlignment(Qt::AlignLeft);
    m_pGifLabel->setMovie(m_pGifMovie);

    m_pTextLabel = new QLabel( this );
    m_pTextLabel->setAlignment(Qt::AlignCenter);
    QFont font = m_pTextLabel->font();
    font.setPointSize( DEFAULT_FONT ); // 临时添加，定义一个足够大的字号
    m_pTextLabel->setFont(font);

    QHBoxLayout *hLayout = new QHBoxLayout;
    hLayout->addWidget(m_pTextLabel, TEXT_STRETCH, Qt::AlignLeft | Qt::AlignVCenter);
    hLayout->addWidget( m_pGifLabel,LOAD_STRTCH );
    hLayout->setSpacing( 0 );
    hLayout->setMargin( 0 );
    setLayout( hLayout );
}

/*************************************************
功能： 设置text内容
入参：text -- 显示的文本
*************************************************************/
void SearchFunc::setText( const QString& text )
{
    m_pTextLabel->setText( text );
}

/*************************************************
功能： 启动
*************************************************************/
void SearchFunc::start( void )
{
    m_pGifLabel->show();
    m_pGifMovie->start();
    return;
}

/*************************************************
功能： 重新启动
*************************************************************/
void SearchFunc::resume( void )
{
    m_pGifMovie->start();
    m_pGifLabel->show();
    return;
}

/*************************************************
功能： 暂停
*************************************************************/
void SearchFunc::pause( void )
{
    m_pGifMovie->stop();
    m_pGifLabel->hide();
    return;
}

/*************************************************
功能： 停止
*************************************************************/
void SearchFunc::stop( void )
{
    m_pGifLabel->hide();
    m_pGifMovie->stop();
    return;
}

/*************************************************
功能： resize事件，用来重定义图片和文字的大小
*************************************************************/
void SearchFunc::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED( e )
    QFont fontHeight = getReheightFont( m_pTextLabel->font(), height()*HEIGHT_RATION );

    QFontMetrics fm( fontHeight );
    QFont font = fontHeight;
    if( m_pTextLabel->width()*WIDTH_RATIO < fm.width( m_pTextLabel->text() ) )
    {
        font = getRewidthFont( m_pTextLabel->font(), m_pTextLabel->text(), m_pTextLabel->width()*WIDTH_RATIO );
    }
    m_pTextLabel->setFont( font );
}
