/*
 * Copyright (c) 2019.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：xmlprocesshelper.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/11/07
 * 摘要：XML处理助手
 * 当前版本：1.0
 */

#pragma once

#include "xmldocument.h"

namespace DataSpecificationNS
{
    template<typename T,
             bool isUnsigned = std::is_unsigned<typename std::decay<T>::type>::value>
    typename std::enable_if<std::is_integral<typename std::decay<T>::type>::value, bool>::type parseXML(const QString& qstrXMLValue, T&& objValue)
    {
        if (isUnsigned)
        {
            objValue = qstrXMLValue.toUInt();
            return true;
        }
        else
        {
            objValue = qstrXMLValue.toInt();
            return true;
        }

        return false;
    }

    bool parseXML(const QString& qstrXMLValue, float& objValue);

    bool parseXML(const QString& qstrXMLValue, QString& objValue);

    template<typename T>
    bool parseXML(const QString& qstrXMLValue, std::vector<T>& vecValue)
    {
        QStringList qstrlstXMLValue = qstrXMLValue.split(',');
        vecValue.reserve(qstrlstXMLValue.size());
        for (const QString& qstrTmp : qstrlstXMLValue)
        {
            T tmpValue;
            if (parseXML(qstrTmp, tmpValue))
            {
                vecValue.push_back(tmpValue);
            }
        }
        return true;
    }


    template<typename T>
    void parseXMLField(const XMLDocument& xmlDocumentObj, const char* pFiledName, T& field, bool& bFieldExist)
    {
        bFieldExist = false;
        if (!pFiledName)
        {
            return;
        }

        if (xmlDocumentObj.hasElement(pFiledName))
        {
            const QString& qstrXMLValue = xmlDocumentObj.value(pFiledName);
            if (parseXML(qstrXMLValue, field))
            {
                bFieldExist = true;
            }
        }
    }
}
