#ifndef HFCTPRPSUNIONVIEW_H
#define HFCTPRPSUNIONVIEW_H

/*
* Copyright (c) 2015.11，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: HfctPrpsUnionView.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年4月7日
* 摘要：该文件主要定义了hfct prps和prpd整合的界面

* 当前版本：1.0
*/

#include "prpsunionview.h"
#include "Widget.h"
class WIDGET_EXPORT HfctPrpsUnionView : public PrpsUnionView
{
    Q_OBJECT
public:
    /************************************************
     * 功能: 构造函数
     * 入参：parent -- 父控件指针
     *      iPeriodCount -- 周期个数
     *      iPhaseCount -- 每周期相位个数
     *      dMax -- 量程最大值
     *      dMin -- 量程最小值
     ************************************************/
    explicit HfctPrpsUnionView( qint32 iDisplayedPeriodCount, qint32 iPeriodCount, qint32 iPhaseCount,
                                double dMax,double dMin,QWidget *parent = 0 );

    /****************************
    输入参数:cMaxSpectrum:最大频谱值;eSpectrumState:HFCT独有,频谱状态
    功能： 设置最大频谱值
    业务逻辑：
            增益偏低       MAX < value
            增益正常       MAX = value
            增益偏高       MAX > value
    *****************************/
    void setMaxSpectrum( double dMaxSpectrum,PrpsGlobal::SpectrumState eSpectrumState );

	/****************************
    输入参数:eSpectrumState:HFCT独有,频谱状态
    功能： 设置初始状态 0dB
    业务逻辑：
                   MAX < 0dB
    *****************************/
    void setMaxSpectrum( PrpsGlobal::SpectrumState eSpectrumState );
};

#endif // HFCTPRPSUNIONVIEW_H
