#include <QObject>
#include "globalerrprocess.h"

namespace errorProcess
{
QString stateMsgByCode(ReplyCode code)
{
    QString strMsg = "";

    switch( code )
    {
    case ERR_UNAUTHORIZED:
    {
        strMsg = QObject::trUtf8("Please login.");
        break;
    }
    case ERR_TOKEN:
    {
        strMsg = QObject::trUtf8("Please login again.");
        break;
    }
    case ERR_CMS_SYSTEM:
    {
        strMsg = QObject::trUtf8("Service deals error.");
        break;
    }
    case ERR_USERNAME_PWD:
    case USER_CODE_ERR:
    {
        strMsg = QObject::trUtf8("User name or password error.");
        break;
    }
    case ERR_USERNAME_LOCKED:
    {
        strMsg = QObject::trUtf8("Account locked.");
        break;
    }
    case SERVICE_UNAVILABLE_ERR:
    {
        strMsg = QObject::trUtf8("Service unavailable.");
        break;
    }
    case SIGNATURE_INVALID_ERR:
    {
        strMsg = QObject::trUtf8("Signature verification error.");
        break;
    }
    case PARAMETER_ERR:
    {
        strMsg = QObject::trUtf8("Request parameter error.");
        break;
    }
    case OPERATING_PERMISSION_ERR:
    {
        strMsg = QObject::trUtf8("No operation permission.");
        break;
    }
    case REQUEST_TIMEOUT_ERR:
    {
        strMsg = QObject::trUtf8("Request timeout.");
        break;
    }
    case CODE_FOMAT_ERR:
    {
        strMsg = QObject::trUtf8("Password format error.");
        break;
    }
    case CODE_PARSE_ERR:
    {
        strMsg = QObject::trUtf8("Password parse error.");
        break;
    }
    case USER_NOT_EXISIST_ERR:
    {
        strMsg = QObject::trUtf8("User name not exist.");
        break;
    }
    case USER_PERMISSION_ERR:
    {
        strMsg = QObject::trUtf8("No operation permission.");
        break;
    }
    case USER_FORMAT_ERR:
    {
        strMsg = QObject::trUtf8("User name format error.");
        break;
    }
    case PARAMETER_FOMAT_ERR:
    {
        strMsg = QObject::trUtf8("Device submit parameter format error.");
        break;
    }
    case NO_LOGIN_INFO_ERR:
    {
        strMsg = QObject::trUtf8("Login info not exist.");
        break;
    }
    case TASK_FOR_DOWNLOADING_ERR:
    {
        strMsg = QObject::trUtf8("Task is not in being download state.");
        break;
    }
    case TASK_FOR_UPLOADING_ERR:
    {
        strMsg = QObject::trUtf8("Task is not in being submit state.");
        break;
    }
    case TASK_UNMATCHED_ERR:
    {
        strMsg = QObject::trUtf8("Patrol file is unmatched.");
        break;
    }
    case NO_TASK_OR_TASK_EXCEPTION_ERR:
    {
        strMsg = QObject::trUtf8("Task file is not exist or record error.");
        break;
    }
    case NO_TASK_FOR_UPLOAD_ERR:
    {
        strMsg = QObject::trUtf8("Task has no data to submit.");
        break;
    }
    case NO_TASK_ID_ERR:
    {
        strMsg = QObject::trUtf8("Task number is empty.");
        break;
    }
    case NO_OPERATING_PERMISSION_ERR:
    {
        strMsg = QObject::trUtf8("No operation permission.");
        break;
    }
    case NO_TASK_EXISIST_ERR:
    {
        strMsg = QObject::trUtf8("Task is not exist.");
        break;
    }
    case TASK_COMPLETED_UPLOAD_ERR:
    {
        strMsg = QObject::trUtf8("Task has been completed, cannot be uploaded repeatedly.");
        break;
    }
    case DOWNLOAD_TASK_ERR:
    {
        strMsg = QObject::trUtf8("Download task error.");
        break;
    }
    case TASK_ACCEPT_ERR:
    {
        strMsg = QObject::trUtf8("Accept task error.");
        break;
    }
    case UPLOAD_TASK_ERROR:
    {
        strMsg = QObject::trUtf8("Upload task fail.");
        break;
    }
    case UPLOAD_ERR:
    {
        strMsg = QObject::trUtf8("Upload fail.");
        break;
    }
    case USAGE_UNDEFINED_ERR:
    {
        strMsg = QObject::trUtf8("Undefined function.");
        break;
    }
    case NO_UNZIP_REQUEST_ERR:
    {
        strMsg = QObject::trUtf8("No requested unzip method.");
        break;
    }
    case FILE_PATH_ERR:
    {
        strMsg = QObject::trUtf8("File path error.");
        break;
    }
    case REQUEST_PAGE_UNMATCHED_ERR:
    {
        strMsg = QObject::trUtf8("Request task list error.");
        break;
    }
    case TASK_EXIST_ERROR:
    {
        strMsg = QObject::trUtf8("Patrol task is not exist.");
        break;
    }
    case TASK_IS_TESTING_ERROR:
    {
        strMsg = QObject::trUtf8("Task is in patrolling.");
        break;
    }
    case FILE_OP_ERROR:
    {
        strMsg = QObject::trUtf8("File operation error.");
        break;
    }
    case MD5_UNMATCH_ERROR:
    {
        strMsg = QObject::trUtf8("MD5 verification error.");
        break;
    }
    case HTTP_REPLY_EMPTY:
    {
        strMsg = QObject::trUtf8("Http response message is empty.");
        break;
    }
    case SAVE_TEST_NUMBER_ERROR:
    {
        strMsg = QObject::trUtf8("Save task number error.");
        break;
    }
    case UNABLE_GET_TEST_NUMBER_ERROR:
    {
        strMsg = QObject::trUtf8("Get task number fail.");
        break;
    }
    case UPLOAD_TEST_DATA_ERROR:
    {
        strMsg = QObject::trUtf8("Upload test data fail.");
        break;
    }
    case SAVE_TEST_DATA_ERROR:
    {
        strMsg = QObject::trUtf8("Save test data fail.");
        break;
    }
    case UPLOAD_TASK_FILE_ERROR:
    {
        strMsg = QObject::trUtf8("Upload task description file fail.");
        break;
    }
    case UPLAOD_TASK_INFO_ERR:
    {
        strMsg = QObject::trUtf8("Upload task info error.");
        break;
    }
    case TASK_NUMBER_UMMATCHED_ERR:
    {
        strMsg = QObject::trUtf8("Task number unmatched.");
        break;
    }
    case METHOD_UNMATCHED_ERR:
    {
        strMsg = QObject::trUtf8("Method unmatched.");
        break;
    }
    case TASK_STATUS_UNMATCHED_ERR:
    {
        strMsg = QObject::trUtf8("Task state unmatched.");
        break;
    }
    case TASK_INDEX_ERR:
    {
        strMsg = QObject::trUtf8("Task serial number error.");
        break;
    }
    case CLIENT_ID_ERR:
    {
        strMsg = QObject::trUtf8("Client verification fail.");
        break;
    }
    case CLOUD_DIGNOS_IUPUT_ERR:
    {
        strMsg = QObject::trUtf8("Input parameter error.");
        break;
    }
    case TASK_GET_TASK_LIST_EMPTY:
    {
        strMsg = QObject::trUtf8("Task list is empty.");
        break;
    }
    default:
    {
        strMsg = QObject::trUtf8("Connect fail.");
        break;
    }

    }

    return strMsg;
}

}
