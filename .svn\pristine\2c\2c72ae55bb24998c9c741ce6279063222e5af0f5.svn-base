/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* SampleChartView.h
*
* 初始版本：1.0
* 作者：邵震宇
* 修改日期：2016年12月16日
*       重构
* 摘要：采集类图表视图基类声明

* 当前版本：1.0
*/
#ifndef SAMPLECHARTVIEW_H
#define SAMPLECHARTVIEW_H

#include "chartview/ChartView.h"
#include "recordplay/RecordPlay.h"
#include "multiservice/multiservicedefine.h"

class MultiUserService;
class SampleChartView : public ChartView
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    输入参数:
        strTitle -- 标题
        parent -- 父窗体
    *****************************/
    explicit SampleChartView(const QString& strTitle, QWidget *parent = 0);

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~SampleChartView();

    /*************************************************
    功能： 设置service类
    输入参数：
            pService -- 服务类
    *************************************************************/
    void setService(MultiUserService* pService);

    /*************************************************
    功能： 获取service类
    返回值：
            MultiUserService* -- 服务类
    *************************************************************/
    MultiUserService* service();

    /*************************************************
    功能： 增加采集用户
    输入参数：
            user -- 采集用户的信息
    *************************************************/
    void addUser(const MultiServiceNS::SampleUser& user);

    /*************************************************
    功能： 获取采集用户id
    返回值：
            MultiServiceNS::USERID -- 采集用户的信息
    *************************************************/
    MultiServiceNS::USERID getUserId() const;

    /*************************************************
    功能： 开始采集
    *************************************************************/
    bool startSampleService();

    /*************************************************
    功能： 停止采集
    *************************************************************/
    bool stopSampleService();

    /*************************************************************
     * 功能：是否正在采集
     * 输入参数：
     *         bool：是否正在采集
     * ************************************************************/
    bool isSampling();

    /*************************************************
    功能： 快速保存
    输入参数：
            strTitle -- 标题
            strStationName -- 站点名
            strDeviceName -- 设备名
            strScreenShotPath -- 截屏路径
    *************************************************************/
    bool fastSave( const QString& strTitle,
                   const QString& strStationName,
                   const QString strDeviceName,
                   const QString& strScreenShotPath,
                   const QPoint &centerPoint);


    /*************************************************
    功能： 根据font对单行string进行省略操作
    *************************************************************/
    QString getRightEllipsisInfo(const QFont qFont, const int iWidth, const QString& qstrInfo);

    /**************************************************
     * 功能：设置录音类型
     * 输入参数：
     *      eType：录音类型
     * *************************************************/
    void setRecordType(RecordPlay::Type eType, RecordPlay::AEType eAEType);

    /******************************************
     * 功能：开启录音
     * 输入参数：
     *      bPdaRecord：是否PDA的录音
     * ****************************************/
    bool startAERecord(bool bPdaRecord = true, const QString& qstrSavePath = "");

    /******************************************
     * 功能：AE开启录音
     * 输入参数：
     *      bPdaRecord：是否PDA的录音
     * ****************************************/
    bool startAERecord(RecordPlay::AEType eAEType);

    /******************************************
     * 功能：停止录音
     * ****************************************/
    bool stopAERecord();
	
    /*****************************************
     * 功能：wav文件转为mp3文件
     * **********************************************/
    void wav2MP3(QString qstrWavFilePath);

    /******************************************
     * 功能：显示保存信息提示框
     * 输入参数：
     *      qstrInfoText：信息提示框内容
     *      qstrSaveDataPath：文件保存路径
     * ****************************************/
    void showSaveInformation(QString& qstrInfoText, const QString& qstrSaveDataPath);

    void showFileCommentBox();

signals:
    /*************************************************
    功能： 信号，窗口关闭
    *************************************************************/
    void sigClosed();

	/*************************************************
    功能： 发送录音时长信号
    *************************************************************/
    void sigTimerRefresh(uint uiRecordingTime);
	
    /*************************************************
    功能： 释放检测信号
    *************************************************************/
    void sigTested();

    /*************************************************
    功能： 释放退出检测信号
    *************************************************************/
    void sigExitTest();

protected slots:
	/*************************************************
    功能： 槽，录音时长变化
    输入参数：
          	uiRecordingTime 录音时长 单位s
    *************************************************************/
    virtual void onTimerRefresh(uint uiRecordingTime);

    /*************************************************
    功能： 显示更多配置菜单
    *************************************************************/
    void showMoreConfigButtonBar();

    /*************************************************
    功能： 隐藏更多配置菜单
    *************************************************************/
    void hideMoreConfigButtonBar();

protected:
    /*************************************************
    功能： 键盘事件
    输入参数:
        event -- 事件
    *************************************************************/
    void keyPressEvent( QKeyEvent* event );

    virtual QString saveDataToFile();

    virtual QString saveDataToFile(const QString &stationName, const QString& deviceName);

    /*************************************************
    功能： 响应S键事件
    *************************************************************/
    virtual void onSKeyPressed();

    /******************************************
     * 功能：AE录音到最大录音时间，处理逻辑 
     * 这里是pda的最大录音时长
     * ****************************************/
    virtual void stopAERecordToDo();

	/******************************************
     * 功能：AE录音到最大录音时间，处理逻辑
     * ****************************************/
    virtual void stopAERecordNextToDo();

    /*************************************************
    功能： 窗口关闭事件
    输入参数:
        event -- 事件
    *************************************************************/
    void closeEvent( QCloseEvent* event );

    /*处理msgbox里文本内容过长（目前仅保存成功显示文件名时使用）*/
    void processTooLongMsgText(QString &strText);

    /*************************************************
    功能：处理云诊断返回的数据
    输入参数:
        strText -- 云诊断结果
    *************************************************************/
    void processCloudDiagnoiseMsgText(QString &strText);

    /*************************************************
    功能： 延迟关闭界面，用于查看检测数据
    *************************************************************/
    void delayToClose();

    /*************************************************
    功能： 关闭自动跳转定时器
    *************************************************************/
    void killAutoSwitchTimer();

    /*************************************************
    功能： 关闭录音最小时限定时器
    *************************************************************/
    void killAERecordMinTimer();

    /*************************************************
    功能： 关闭录音最大时限定时器
    *************************************************************/
    void killAERecordMaxTimer();

	/*************************************************
    功能： 关闭录音最大时限定时器 非pda
    *************************************************************/
    void killAERecordMax();
	
	/*************************************************
    功能： 关闭计数器定时器
    *************************************************************/
    void killAETimer();
	
    /*************************************************
    功能： 定时器处理函数
    输入参数：
         event -- 定时事件
    *************************************************************/
    void timerEvent(QTimerEvent* event);

    /****************************************************
     * 功能：保存操作
     * **************************************************/
    virtual void pressSaveData();

    /*************************************************
    功能： RFID扫描保存
    *************************************************************/
    void RFIDSaveData();

    /*************************************************
    功能： 创建控更多信息按钮栏
    输入参数:
        pchContext -- 国际化用的域
        pInfos -- 按钮配置信息
        iCount -- 按钮个数
        qstrTitile -- 更多界面标题名称，为空时是More...
    返回值：创建的ButtonBar
    *************************************************************/
    LabelButtonBar* createMoreConfigButtonBar(const char* const pchContext, const ButtonInfo::Info* pInfos, int iCount, QString qstrTitile = QString());

    /*************************************************
    功能： 获取更多信息按钮栏
    返回值： 更多信息按钮栏
    *************************************************************/
    LabelButtonBar* moreConfigButtonBar();

private:
    void setPNGFileNamePath(const QString &strDataFile, QString &strPngFilePath, QString &strPngFile);

public:
    /*************************************************
    功能： 截屏并保存
    输入参数：
            strAbsolutePath -- 路径
    返回值：
        true -- 成功
        其它 -- 失败
    *************************************************************/
    bool saveScreenShot( const QString& strAbsolutePath, const QString &strPNGFile )const;

public:
    volatile bool m_bSaveData;
    bool m_bPdaWaiting;
    bool m_bRecording;
    int m_iAutoSwitchTimerId;
    QString m_qstrAttachPath;
    quint8 m_ucVolume;//音量
	quint32 m_uiTotalRecordingTime;//录制时长	
    quint8 m_qui8RealDiagInterval;

protected:
    QString m_qstrStationName;
    QString m_qstrDeviceName;
    QString m_qstrDeviceNumber;
    QString m_qstrRemark;

private:
    bool m_bStopRecordEnable;
    bool m_bSampling; // 是否正在采样
    int m_iAERecordMinTimerId;
    int m_iAERecordMaxTimerId;
	int m_iAERecordMaxId;   //最大录音时长ID
	int m_iTimerIDRefresh;  //定时器id
	quint8 m_ui8RecordTime;
    RecordPlay::Type m_eType;
    RecordPlay::AEType m_eAEType;
    MultiServiceNS::USERID m_iUserId; // 服务模块分配的用户id，用以识别用户
    MultiUserService* m_pService;
    LabelButtonBar* m_pMoreConfigView; // 更多设置视图
};

#endif // SAMPLECHARTVIEW_H
