﻿#include <QStringList>
#include <QFileInfo>
#include <QFile>
#include <QDebug>
#include "dataSave/QtCrypto.h"
#include "datadefine.h"
#include "cloudservice.h"
#include "time.h"
#include "cloud/cloudprotocol.h"
#include "cloud/sha1.h"
#include "cloud/qjason/qjson.h"
#include "taskinteractiveio.h"
#include "DesCrypto.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "appconfig.h"
#include "AfDes_CBC.h"
#include "pdatask.h"
#include "systemsetting/systemsetservice.h"
#include "webserver/controller/appserverutils.h"
#include "fileoper/devfileoper.h"
#include "appfontmanager/appfontmanager.h"
#include "timezonemanager/timezonemanager.h"
#include "global_log.h"
#include "log/log.h"

using namespace CLOUD_PROTOCOL;


CloudService::CloudService(QObject *parent) : QObject(parent)
{
    m_bLogined = false;
    m_pHttpBean = new HttpBean(this);
    m_bFilterSetted = false;

    readCloudConfig();
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：NULL
功能：读取云配置信息(ip,端口)
*************************************************************/
void CloudService::readCloudConfig()
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    //m_strCloudIP = pConfig->value( APPConfig::KEY_CLOUD_ADDR );
    //m_strCloudPort = pConfig->value( APPConfig::KEY_CLOUD_PORT );
    m_strCloudAddr = pConfig->value( APPConfig::KEY_CLOUD_ADDR );
    pConfig->endGroup();

    m_iLoginType = LOGIN_TYPE;
    m_qstrAuthComNme = COMPANY_NAME;

    //QString qstrInfo = QString("CMS info: %1:%2").arg(m_strCloudIP).arg(m_strCloudPort);
    QString qstrInfo = QString("CMS info: %1").arg(m_strCloudAddr);
    logInfo(qstrInfo.toLatin1().data());

    return;
}

/*************************************************************************
 * 功能：获取完整的url
 * 输入参数：
 *      qstrPartUrl：部分url
 * 返回值：
 *      QString：完整的url
 * ***********************************************************************/
QString CloudService::getWholeRequestUrl(QString qstrPartUrl)
{
    QString qstrUrl = m_strCloudAddr + qstrPartUrl;
    log_debug("%s", qstrUrl.toLatin1().data());
    return qstrUrl;
}

/*************************************************
函数名：
输入参数: qsTaskNum--任务编号,innerId
输出参数：NULL
返回值：
功能：获取下载任务的url
*************************************************************/
QUrl CloudService::getDownloadTaskUrl(const QString &qsInrId, QHttpMultiPart &stMultiPart)
{
    //生成发布路径
    //QUrl stDwnUrl(QString(QString(URL_HEAD_FORM) + DOWNLOAD_TASK_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl stDwnUrl(getWholeRequestUrl(DOWNLOAD_TASK_URL));

    QHttpPart stTstNumPart;
    QString qstrInrIdHeader = QString("form-data; name=\"%1\"").arg(INNER_ID_KEY);
    stTstNumPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrInrIdHeader));
    stTstNumPart.setBody(qsInrId.toUtf8());
    stMultiPart.append(stTstNumPart);

    return stDwnUrl;
}


/*************************************************
函数名：
输入参数: strFileUrl-任务描述文件下载路径
输出参数：NULL
返回值：
功能：下载任务文件
*************************************************************/
QUrl CloudService::downloadFileUrl(const QString &strFileUrl, int currentBlockIndex, QHttpMultiPart &stMultiPart)
{
    //生成发布路径
    //QUrl stDownloadFileUrl(QString(QString(URL_HEAD_FORM) + DOWNLOAD_FILE_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl stDownloadFileUrl(getWholeRequestUrl(DOWNLOAD_FILE_URL));

    QHttpPart stFilePathPart;
    QString qstrFilePath = QString("form-data; name=\"%1\"").arg(DOWNLOAD_FILE_PATH_KEY);
    stFilePathPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrFilePath));
    stFilePathPart.setBody(strFileUrl.toUtf8());
    stMultiPart.append(stFilePathPart);

    QHttpPart stIdxPart;
    QString qstrIdx = QString("form-data; name=\"%1\"").arg(FILE_BLOCK_INDEX_KEY);
    stIdxPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrIdx));
    stIdxPart.setBody(QString::number(currentBlockIndex).toUtf8());
    stMultiPart.append(stIdxPart);

    return stDownloadFileUrl;
}

/*************************************************
函数名：
输入参数: strUser--用户名
         strPwd--密码
输出参数：NULL
返回值：
功能：获取登录的对应url
*************************************************************/
QUrl CloudService::getRequestLoginUrl(const QString& strUser, const QString& strPwd)
{
    //生成发布路径
    //QUrl requestLoginUrl(QString(QString(URL_HEAD_FORM) + USER_LOGIN_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl requestLoginUrl(getWholeRequestUrl(USER_LOGIN_URL));

    //添加所用的参数
    QMap<QString, QString> params;
    params.insert(CMS_METHOD_KEY, USER_LOGIN_URL);
    params.insert(CLIENT_ID_KEY, CLIENT_ID_VALUE);
    params.insert(APP_VERSION_KEY, APP_VERSION_VALUE);
    params.insert(PROTOCOL_V_KEY, PROTOCOL_V_VALUE);
    params.insert(USER_NAME_KEY, strUser);
    params.insert(USER_PASSWORD_KEY, strPwd);


    //填充参数到url中
    addParamsToUrl(params, requestLoginUrl);

    return requestLoginUrl;
}

/*************************************************
函数名：
输入参数: strUser--用户名
         strPwd--密码
         uiTimeOut--超时时间
输出参数：NULL
返回值：ReplyCode
功能：登录云服务
*************************************************************/
ReplyCode CloudService::requestLogin(const QString &strUser, const QString &strPwd)
{
    //拿权限认证
    return loginToGetToken(strUser, strPwd);
}

/*************************************************
函数名：
输入参数: strID--内部标识号
输出参数：NULL
返回值：
功能：获取任务编号接口
*************************************************************/
QUrl CloudService::getTestNumberUrl(const QString& strID)
{
    //生成发布路径
    //QUrl testNumberUrl(QString(QString(URL_HEAD_FORM) + GET_TESTNUMBER_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl testNumberUrl(getWholeRequestUrl(GET_TESTNUMBER_URL));

    //添加所用的参数
    QMap<QString, QString> params;
    params.insert(CMS_METHOD_KEY, GET_TESTNUMBER_URL);
    params.insert(CLIENT_ID_KEY, CLIENT_ID_VALUE);
    params.insert(APP_VERSION_KEY, APP_VERSION_VALUE);
    params.insert(PROTOCOL_V_KEY, PROTOCOL_V_VALUE);
    params.insert(REQUEST_AUTH_ID_KEY, m_authId);
    params.insert(INNER_ID_KEY, strID);

    //填充参数到url中
    addParamsToUrl(params, testNumberUrl);
    return testNumberUrl;
}

/*************************************************
函数名：
输入参数: taskInfo--任务的基本信息
         uiTimeOut--超时时间
输出参数：NULL
返回值：ReplyCode
功能：获取任务编号
*************************************************************/
ReplyCode CloudService::getTestNumber(const TaskInfo & taskInfo, QString &strTestNumber, UINT32 uiTimeOut )
{
    ReplyCode reply;
    //生成请求url
    QString strInnerID;
    strInnerID = taskInfo.strInnerId;

    QUrl tmpUrl = getTestNumberUrl( strInnerID );

    //发送请求并接收应答
    QByteArray baPost;//发送内容
    QByteArray baResponse; //接收内容
    QByteArray baResponseHeader;
    QByteArray baRawHeader;
    bool bRet;
    bRet = m_pHttpBean->getData(tmpUrl, baPost, baResponse, baRawHeader, baResponseHeader, uiTimeOut);
    if(bRet)
    {
        if(!baResponse.isEmpty())
        {
            QJson json(baResponse);
            reply = (ReplyCode) json.value(REPLY_CODE_KEY).toNumber();
            if(reply == REPLY_SUCCESS_CODE)
            {
                strTestNumber = json.value(REPLY_RESULT_KEY).toString();
                bRet = PDATask::uploadTestNumber(taskInfo, strTestNumber );
                if (bRet == false)
                {
                    reply = SAVE_TEST_NUMBER_ERROR;
                }
            }
        }
        else
        {
            reply = HTTP_REPLY_EMPTY;
        }
    }
    else
    {
        reply = REQUEST_TIMEOUT_ERR;
    }

    return reply;
}

/*************************************************
函数名：
输入参数: strUser--任务查询的用户名
         uiTimeOut--查询的超时时间
输出参数：qvTaskInfoList--cms上查询的信息列表
返回值：ReplyCode
功能：获取任务信息
*************************************************************/
ReplyCode CloudService::getTaskLists(const QString &strUser, QVector<TaskInfo> &qvTaskInfoList, UINT32 uiTimeOut)
{
    Q_UNUSED(strUser);
    ReplyCode reply;
    bool bRet = true;
    int totalPage = 1; //总页数 至少为1
    int totalRecords = 0;//总记录数
    int iMaxRecords = 50;       //为了避免cms中维护的任务很多，导致设备一直获取cms任务情况，暂时设置设备获取前50条记录。
    int iStartCount = qvTaskInfoList.size();

    for(int currentPage = 1; bRet && (currentPage <= totalPage); ++currentPage)
    {
        if((qvTaskInfoList.size() - iStartCount) >= iMaxRecords)
        {
            //为了避免cms中维护的任务很多，导致设备一直获取cms任务情况，暂时设置设备获取前50条记录。
            qDebug() << "CloudService::getTaskLists, task vector size: "<< qvTaskInfoList.size() << ", start size: " << iStartCount << ", max size: " << iMaxRecords << endl;
            break;
        }

        QHttpMultiPart *pMultiPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);
        //生成对应的url
        QUrl tmpUrl = getTaskListsUrl(*pMultiPart, currentPage);

        QByteArray baResponse; //接收内容

        //httpbean发送数据
        bRet = m_pHttpBean->postDataToCmsByFormData(tmpUrl, m_qstrToken, *pMultiPart, baResponse, uiTimeOut);

        //处理返回的结果，并填充qvTaskInfoList
        if(bRet)
        {
            reply = parseQueryTaskListResponse(baResponse, currentPage, totalPage, totalRecords, qvTaskInfoList);
            if(reply != REPLY_SUCCESS_CODE)
            {
                break;
            }
        }
        else
        {
            reply = REQUEST_TIMEOUT_ERR;
            break;
        }
    }

    return reply;
}

/************************************************************
 * 功能：上传快速巡检的数据信息
 * 输入参数：
 *      qstrTaskDir：任务所在的文件夹
 *      qstrlstFilePaths：需要上传的所有文件集合
 *      iTimeout：网络通信超时时间
 * 返回值：
 *      执行结果
 * ***********************************************************/
ReplyCode CloudService::uploadFastPatrolInfo(const QString qstrTaskDir, const QStringList &qstrlstFilePaths, int iTimeout)
{
    Q_UNUSED(qstrTaskDir);
    Q_UNUSED(qstrlstFilePaths);
    Q_UNUSED(iTimeout);
    return UPLOAD_ERR;
}

void CloudService::setCloudTaskFilter(const TaskFilter& taskFilter)
{
    m_taskFilter = taskFilter;
    m_bFilterSetted = true;
}

/*************************************************
功能：清空云端获取任务列表的过滤项
*************************************************************/
void CloudService::cleanCloudTaskFilter()
{
    m_bFilterSetted = false;
}

/*************************************************
函数名：
输入参数: baResponse---response
输出参数：qsFileUrl---任务文件存储在云端的url
返回值：ReplyCode
功能：解析下载任务应答，读出任务文件存储在云端的url
*************************************************************/
ReplyCode CloudService::parseDownloadTaskResponse(const QByteArray &baRespone, QString &qsFileUrl)
{
    ReplyCode replyCode;
    if(!baRespone.isEmpty())
    {
        QJson json(baRespone);
        replyCode = (ReplyCode)(json.value(REPLY_CODE_KEY).toNumber());
        if(replyCode == REPLY_SUCCESS_CODE)
        {
            QJson jsonResult(json.value(REPLY_RESULT_KEY));
            qsFileUrl = jsonResult.value(FILE_URL).toString();
        }
    }
    else
    {
        replyCode = HTTP_REPLY_EMPTY;
    }

    return replyCode;
}

ReplyCode CloudService::parseDownloadFileResponse(const QByteArray &baResult, int &totalBlockIndex, int &currentBlockIndex)
{
    ReplyCode replyCode = REPLY_SUCCESS_CODE;
    //对字段内容进行解析
    if(!baResult.isEmpty())
    {
        QJson jsonObj(baResult);
        QJson jsonResult = QJson(jsonObj.value(REPLY_RESULT_KEY));
        currentBlockIndex = jsonResult.value(BLOCK_INDEX_KEY).toNumber();
        totalBlockIndex = jsonResult.value(TOTAL_BLOCKS_KEY).toNumber();
    }
    else
    {
        qDebug() << "CloudService::parseDownloadFileResponse, network file content is empty." << endl;
        replyCode = HTTP_REPLY_EMPTY;
    }

    return replyCode;
}

/*************************************************
函数名：
输入参数: taskInfo--任务的基本信息
         uiTimeOut--超时时间
输出参数：baFileData---任务文件的数据
返回值：ReplyCode
功能：下载任务
*************************************************************/
ReplyCode CloudService::downloadTask(TaskInfo &taskInfo, QByteArray &baFileData, UINT32 uiTimeOut)
{
    m_bStopOp = false;
    ReplyCode reply;

    QHttpMultiPart *pMultiPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);

    //生成对应的url
    //QUrl tmpUrl = getDownloadTaskUrl(taskInfo.strTestNumber, *pMultiPart);
    QUrl tmpUrl = getDownloadTaskUrl(taskInfo.strInnerId, *pMultiPart);      //与cms下载任务时，根据innerId去下载，cms端命名叫testNumber而已

    QByteArray baResponse; //接收内容

    //httpbean发送数据
    bool bRet = m_pHttpBean->postDataToCmsByFormData(tmpUrl, m_qstrToken, *pMultiPart, baResponse, uiTimeOut);

    //处理返回的结果
    if(bRet)
    {
        reply = parseDownloadTaskResponse(baResponse, taskInfo.strFileURL);
        if(reply == REPLY_SUCCESS_CODE)
        {
            if(taskInfo.strFileURL.isEmpty())
            {
                reply = FILE_PATH_ERR;
                qDebug() << "CloudService::downloadTask, download task url info is empty." << endl;
                return reply;
            }

            int currentBlockIndex = 1;
            int totalBlockIndex = 1;

            while(!m_bStopOp)
            {
                QByteArray qbyaRawHeaderKey(REPLY_RESULT_KEY);
                QByteArray qbyaResult;          //含有result的json数据
                QByteArray qbyaFileContentTmp;          //文件内容

                QHttpMultiPart *pTmpMPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);
                //调用文件下载接口
                tmpUrl = downloadFileUrl(taskInfo.strFileURL, currentBlockIndex, *pTmpMPart);
                bRet = m_pHttpBean->fileDownload(tmpUrl, m_qstrToken, *pTmpMPart, qbyaRawHeaderKey, qbyaResult, qbyaFileContentTmp);
                if(bRet)
                {
                    reply = parseDownloadFileResponse(qbyaResult, totalBlockIndex, currentBlockIndex);
                    if(reply == REPLY_SUCCESS_CODE)
                    {
                        baFileData.append(qbyaFileContentTmp);
                        if(currentBlockIndex < totalBlockIndex)
                        {
                            ++currentBlockIndex;
                        }
                        else
                        {
                            break;
                        }
                    }
                    else
                    {
                        qDebug() << "CloudService::downloadTask, download url ( " << taskInfo.strFileURL << " ), return code: "
                                 << reply << endl;
                        break;
                    }
                }
                else
                {
                    reply = REQUEST_TIMEOUT_ERR;
                    qDebug() << "CloudService::downloadTask, download file failed. url: " << taskInfo.strFileURL << endl;
                    break;
                }
            }
        }
    }
    else
    {
        reply = REQUEST_TIMEOUT_ERR;
    }

    return reply;
}

/*************************************************
函数名：
输入参数: qsTaskNum--要下载的任务编号
返回值：ReplyCode
功能：请求单个接受任务
*************************************************************/
ReplyCode CloudService::acceptTask(const QString &qstrInrId, UINT32 uiTimeOut)
{
    ReplyCode reply;

    //生成发布路径
    //QUrl stAcTsksUrl(QString(QString(URL_HEAD_FORM) + ACCEPT_TASK_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl stAcTsksUrl(getWholeRequestUrl(ACCEPT_TASK_URL));

    QHttpMultiPart *pMultiPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);

    QHttpPart stTstNumsPart;
    QString qstrInrIds = QString("form-data; name=\"%1\"").arg(INNER_IDLST_KEY);
    stTstNumsPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrInrIds));
    stTstNumsPart.setBody(qstrInrId.toUtf8());
    pMultiPart->append(stTstNumsPart);

    QByteArray baResponse; //接收内容

    //httpbean发送数据
    bool bRet = m_pHttpBean->postDataToCmsByFormData(stAcTsksUrl, m_qstrToken, *pMultiPart, baResponse, uiTimeOut);

    //处理返回的结果
    if(bRet)
    {
        reply = parseAcceptTaskResponse(baResponse, qstrInrId);
    }
    else
    {
        reply = REQUEST_TIMEOUT_ERR;
    }

    return reply;
}

/*************************************************
函数名：
输入参数: qlTaskNumbers--要下载的任务编号,innerIds
输出参数：acceptedTasks---容器， 存放被接受任务的编号和索引
返回值：ReplyCode
功能：请求批量接受任务
*************************************************************/
ReplyCode CloudService::acceptTasks(const QStringList &qlInnerIds, QMap<QString, UINT32> &acceptedTasks, UINT32 uiTimeOut)
{
    ReplyCode reply;

    QString qsInrIds = "";
    for(int i = 0; i < qlInnerIds.size(); ++i)
    {
        qsInrIds += qlInnerIds.at(i);
        if(i < (qlInnerIds.size() -1))
        {
            qsInrIds += ",";
        }
    }

    //生成发布路径
    //QUrl stAcTsksUrl(QString(QString(URL_HEAD_FORM) + ACCEPT_TASK_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl stAcTsksUrl(getWholeRequestUrl(ACCEPT_TASK_URL));

    QHttpMultiPart *pMultiPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);

    QHttpPart stTstNumsPart;
    QString qstrTstNums = QString("form-data; name=\"%1\"").arg(INNER_IDLST_KEY);
    stTstNumsPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrTstNums));
    stTstNumsPart.setBody(qsInrIds.toUtf8());
    pMultiPart->append(stTstNumsPart);

    QByteArray baResponse; //接收内容

    //httpbean发送数据
    bool bRet = m_pHttpBean->postDataToCmsByFormData(stAcTsksUrl, m_qstrToken, *pMultiPart, baResponse, uiTimeOut);

    //处理返回的结果
    if(bRet)
    {
        reply = parseAcceptTasksResponse(baResponse, acceptedTasks);
    }
    else
    {
        reply = REQUEST_TIMEOUT_ERR;
    }

    return reply;
}

/*************************************************
函数名：
输入参数: baResponse--应答的内容
输出参数：acceptedTasks---容器， 存放被接受任务的编号和索引
返回值：ReplyCode
功能：解析批量接受任务的应答
*************************************************************/
ReplyCode CloudService::parseAcceptTaskResponse(const QByteArray &baResponse, QString qstrTaskNum)
{
    ReplyCode replyCode;
    if(!baResponse.isEmpty())
    {
        QJson json(baResponse);
        replyCode = (ReplyCode) json.value(REPLY_CODE_KEY).toNumber();
        if(replyCode == REPLY_SUCCESS_CODE )
        {
            QJson jsonResult(json.value(REPLY_RESULT_KEY));
            int TasksCnt = jsonResult.value(TASK_ACCEPT_LIST_KEY).count();

            for(int i = 0; i < TasksCnt; ++i)
            {
                QJson jsonTask = jsonResult.value(TASK_ACCEPT_LIST_KEY).at(i);
                QString qstrInrId = jsonTask.value(INNER_ID_KEY).toString();
                TaskStatus eStatus = (TaskStatus)(jsonTask.value(TASK_STATUS_KEY).toNumber());
                QString qstrRemark = jsonTask.value(REMARK_KEY).toString();

                qDebug() << "CloudService::parseAcceptTaskResponse, taskNum: " << qstrInrId << ", status: " << eStatus << ", remark: " << qstrRemark << endl;

                if(qstrTaskNum == qstrInrId)
                {
                    if(eStatus != TASK_ACCPETED)
                    {
                        replyCode = TASK_STATUS_UNMATCHED_ERR;
                        break;
                    }
                }
            }
        }
    }
    else
    {
        replyCode = HTTP_REPLY_EMPTY;
    }

    return replyCode;
}

/*************************************************
函数名：
输入参数: baResponse--应答的内容
输出参数：acceptedTasks---容器， 存放被接受任务的编号和索引
返回值：接受任务是否成功
功能：解析批量接受任务的应答
*************************************************************/
ReplyCode CloudService::parseAcceptTasksResponse(const QByteArray &baResponse, QMap<QString, UINT32> &acceptedTasks)
{
    ReplyCode replyCode;
    if(!baResponse.isEmpty())
    {
        QJson json(baResponse);

        replyCode = (ReplyCode)json.value(REPLY_CODE_KEY).toNumber();
        if(replyCode == REPLY_SUCCESS_CODE )
        {
            QJson jsonResult(json.value(REPLY_RESULT_KEY));
            int TasksCnt = jsonResult.value(TASK_LIST_KEY).count();

            for(int i = 0; i < TasksCnt; ++i)
            {
                QJson jsonTask = jsonResult.value(TASK_LIST_KEY).at(i);
                QString qstrInrId = jsonTask.value(INNER_ID_KEY).toString();
                TaskStatus eStatus = (TaskStatus)(jsonTask.value(TASK_STATUS_KEY).toNumber());
                QString qstrRemark = jsonTask.value(REMARK_KEY).toString();
                qDebug() << "CloudService::parseAcceptTasksResponse, taskNum: " << qstrInrId << ", status: " << eStatus << ", remark: " << qstrRemark << endl;

                if(eStatus == TASK_ACCPETED)
                {
                    acceptedTasks.insert(qstrInrId, i);
                    //acceptedTasks[qsTaskNum] = i;
                }
                else
                {
                    replyCode = TASK_STATUS_UNMATCHED_ERR;
                }
            }
        }
    }
    else
    {
        replyCode = HTTP_REPLY_EMPTY;
    }

    return replyCode;
}
/*************************************************
函数名：
输入参数: files--任务所有要上传的数据文件
        totalCount--文件总块数
         uiTimeOut--超时时间
输出参数：oriUrlFileMap--数据文件名和返回path的map映射
返回值：是否上传成功
功能：上传测试数据文件
*************************************************************/
bool CloudService::sendTestDataFile(const QList<TestFileInfo> &files, int totalCount, QMap<QString,QString> &oriUrlFileMap, UINT32 uiTimeOut)
{
    bool bRet = true;
    int iProcess = 0;
    //逐一上传数据文件
    for(int i = 0; !m_bStopOp && bRet && (i < files.size()); ++i)
    {
        QString urlPath;
        ReplyCode replyCode  = uploadWholeFile(files.at(i).filePath, urlPath, true, uiTimeOut);
        bRet = (replyCode == REPLY_SUCCESS_CODE);
        if(bRet)
        {
            oriUrlFileMap.insert(files.at(i).filePath, urlPath);
            //上报进度
            ++m_iHasSended;
            iProcess = m_iHasSended * 100 / totalCount;
            emit sigUploadProcess(iProcess);
        }
        else
        {
            qWarning() << "CloudService::sendTestDataFile, upload fail, file path: " << files.at(i).filePath << endl;
            break;
        }
    }

    return bRet;
}

/*************************************************
函数名：
输入参数: taskPath--任务所有要上传的任务描述文件路径
         totalCount--文件总块数
         uiTimeOut--超时时间
输出参数：taskUrlPath--上传任务描述文件所返回的path字段值
返回值：是否上传成功
功能：上传任务描述文件
*************************************************************/
ReplyCode CloudService::sendTaskFile(const QString &taskPath, int totalCount, QString &taskUrlPath, UINT32 uiTimeOut)
{
    ReplyCode replyCode;
    int iProcess = 0;
    if(!m_bStopOp)
    {
        replyCode = uploadWholeFile(taskPath, taskUrlPath, false, uiTimeOut);
        if(replyCode == REPLY_SUCCESS_CODE)
        {
            //上报进度
            ++m_iHasSended;
            iProcess = m_iHasSended * 100 / totalCount;
            emit sigUploadProcess(iProcess);
        }
        else
        {
            qWarning() << "CloudService::sendTaskFile, error in uploading task desc file: " << taskPath <<endl;
        }
    }
    else
    {
        replyCode = UPLOAD_ERR;
    }
    return replyCode;
}

/*************************************************
函数名：
输入参数: taskInfo--任务基本信息
         files--任务所有要上传的数据文件
输出参数：NULL
返回值：是否上传成功
功能：上传指定的测试数据文件
*************************************************************/
ReplyCode CloudService::uploadTask(const TaskInfo& taskInfo, const QList<TestFileInfo> &files, UINT32 uiTimeOut)
{
    ReplyCode reply = REPLY_SUCCESS_CODE;
    m_bStopOp = false;//操作初始时设置为true
    bool bRet;

    qDebug() << "CloudService::uploadTask, test number: " << taskInfo.strTestNumber << endl;

    //任务编码绝不能为空
    QString strTestNumber;
    if(taskInfo.strTestNumber.isEmpty())
    {
        return NO_TASK_EXISIST_ERR;
    }

    strTestNumber = taskInfo.strTestNumber;

    //总的上传流程为数据文件+任务描述文件+任务上传指令
    int totalCount = files.size() + 1 + 1;//总的指令交互数
    m_iHasSended = 0;
    //遍历上传测试数据，返回的远端路径保存起来
    QMap<QString, QString> oriUrlFileMap;
    if (reply == REPLY_SUCCESS_CODE)
    {
        //该函数上传数据列表文件，结果保存在oriUrlFileMap中，并在函数中上报进度
        bRet = sendTestDataFile(files, totalCount, oriUrlFileMap, uiTimeOut);
        if(!bRet)
        {
            return UPLOAD_TEST_DATA_ERROR;
        }
    }

    //保存url地址到任务描述文件 PDATask::saveTestDataUrlPath()
    if( reply == REPLY_SUCCESS_CODE)
    {
        bRet = PDATask::saveLocalTestDataUrlPath(taskInfo, oriUrlFileMap);
        if(!bRet)
        {
            return SAVE_TEST_DATA_ERROR;
        }
    }

    //上传任务描述文件
    QString taskUrlPath;
    if( reply == REPLY_SUCCESS_CODE )
    {
        //上传任务描述文件
        //modify by Mountains
        //上传删除remote节点的临时文件
        TaskFileIO tmpFileIO;
        QString strTmpFile;
        if(!(tmpFileIO.createTmpCMSTaskFilePath(taskInfo.strFilePath, strTmpFile)))
        {
            qDebug() << "CloudService::uploadTask, create cms task temp file fail." << endl;
            return UPLOAD_TEST_DATA_ERROR;
        }

        reply = sendTaskFile( strTmpFile, totalCount, taskUrlPath, uiTimeOut);
        //上传后删除临时文件
        QFile file(strTmpFile);
        if(file.exists())
        {
            file.setPermissions(QFile::WriteOther);
            file.remove();
        }
    }

    //发送任务上传指令
    if(!m_bStopOp && reply == REPLY_SUCCESS_CODE)
    {
        reply = uploadTaskInfo(strTestNumber, taskUrlPath, uiTimeOut);
        if(reply == REPLY_SUCCESS_CODE)
        {
            //上报进度
            emit sigUploadProcess(100);
        }
    }

    return reply;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：
功能：停止下载
*************************************************************/
void CloudService::stopDownload()
{
    m_pHttpBean->stopDownload();
    m_bStopOp = true;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：
功能：停止上传
*************************************************************/
void CloudService::stopUpload()
{
    m_pHttpBean->stopUpload();
    m_bStopOp = true;
}

/*************************************************
函数名：NOTE----根据协议修改
输入参数: baData--进行云诊断的数据
输出参数：NULL
返回值： NULL
功能：将当前的测试数据进行cms云诊断
*************************************************************/
bool CloudService::cloudDiagnosis(const QString &qsSavedFilePath, QString &strResult, QString &strCode, UINT32 uiTimeOut)
{
    //获取文件名
    int first = qsSavedFilePath.lastIndexOf ("/");
    QString qsFileName = qsSavedFilePath.right(qsSavedFilePath.length() - first - 1);

    //获取测试数据
    QFile qsSavedFile(qsSavedFilePath);
    qsSavedFile.open(QFile::ReadWrite);
    QByteArray baData = qsSavedFile.readAll();
    qsSavedFile.close();

    //生成对应的url
    //QUrl stCloudDigUrl(QString(QString(URL_HEAD_FORM) + CLOUD_DIAGNOSIS_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl stCloudDigUrl(getWholeRequestUrl(CLOUD_DIAGNOSIS_URL));

    QHttpMultiPart *pMultiPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);

    QHttpPart stFileNamePart;
    QString qstrFileName = QString("form-data; name=\"%1\"").arg(FILE_NAME_KEY);
    stFileNamePart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrFileName));
    stFileNamePart.setBody(qsFileName.toUtf8());
    pMultiPart->append(stFileNamePart);

    QHttpPart stTstDataPart;
    QString qstrTstData = QString("form-data; name=\"%1\"").arg(TEST_DATA_KEY);
    stTstDataPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrTstData));
    stTstDataPart.setBody(dataFile2ByteArray(baData));
    pMultiPart->append(stTstDataPart);

    QHttpPart stTstLangPart;
    QString qstrTstLang = QString("form-data; name=\"%1\"").arg(LANGUAGE_KEY);
    stTstLangPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrTstLang));
    stTstLangPart.setBody(AppFontManager::instance()->getAppCurLangStr().toUtf8());
    pMultiPart->append(stTstLangPart);

    QByteArray baResponse; //接收内容

    //httpbean发送数据
    bool bRet = m_pHttpBean->postDataToCmsByFormData(stCloudDigUrl, m_qstrToken, *pMultiPart, baResponse, uiTimeOut);

    //返回的结果进行处理
    QJson json( baResponse );
    //strResult = json.value( DIAGNOSIS_RESULT_KEY ).toString();
    //strCode = json.value( REPLY_CODE_KEY ).toString();          //文档中为string

    strCode = QString::number(json.value(REPLY_CODE_KEY).toNumber());          //文档中为int
    ReplyCode replyCode = (ReplyCode)(json.value(REPLY_CODE_KEY).toNumber());
    if(replyCode == REPLY_SUCCESS_CODE)
    {
        //save 权限码
        QJson jsonResult = json.value(REPLY_RESULT_KEY);
        strResult = jsonResult.value(DIAGNOSIS_RESULT_KEY).toString();
    }

    qDebug() << "CloudService::cloudDiagnosis, rsp code: " << strCode << ", result: " << strResult << endl;

    return bRet;
}

//添加timestamp & sn & sign
void CloudService::addParamsToUrl(const QMap<QString, QString> &queryItems, QUrl &url)
{
    QMap<QString, QString> tmpItems = queryItems;
    //设置当前的时间戳
    time_t tmp_ts = time(NULL);
    tmpItems.insert(TIME_STAMP_KEY, QString::number(tmp_ts));

    tmpItems.insert(LANGUAGE_KEY, "zh_CN");

    //增加客户端序列号
    const UINT8 *pucDeviceSN = SystemSetService::instance()->deviceIdNum();
    QString strSN = QByteArray((const char*) pucDeviceSN ).toHex();
    tmpItems.insert(CLIENT_SN_KEY, strSN);

    /******生成待签名连接字符串*****/
    //参数集合转换为待计算的字符串集合
    QStringList kvStrList;
    QMapIterator<QString, QString> i(tmpItems);
    while (i.hasNext())
    {
        i.next();
        kvStrList.append(QString("%1=%2").arg(i.key()).arg(i.value()));
    }
    //添加密钥字段
    kvStrList.append(QString("%1=%2").arg(CLIENT_SECRET_KEY).arg(CLIENT_SECRET_VALUE));
    //各个字段按照字典序排序
    qSort(kvStrList.begin(), kvStrList.end());
    //按照格式连接各个字段
    QString strSort;
    for(int i = 0; i < kvStrList.size(); i++)
    {
        if(0!=i)
            strSort.append("&");
        strSort.append(kvStrList.at(i));
    }

    /******生成密钥并放在参数列表中*****/
    //生成sha1密钥
    QByteArray tmpSign = SHA1::encode(strSort);
    //将密钥字段加入参数列表中
    tmpItems.insert(PARAMS_SIGN_KEY, tmpSign);

    //填充url
    QMapIterator<QString, QString> iterParams(tmpItems);
    while (iterParams.hasNext())
    {
        iterParams.next();
        url.addQueryItem(iterParams.key(), iterParams.value());
    }
}

void CloudService::addParamsToUrl_EX(const QMap<QString, QString> &queryItems, QUrl &url)
{
    //填充url
    QMapIterator<QString, QString> iterParams(queryItems);
    while (iterParams.hasNext())
    {
        iterParams.next();
        url.addQueryItem(iterParams.key(), iterParams.value());
    }
}

//任务接受URL
QUrl CloudService::acceptTasksUrl(const QString &qsTaskNums, QByteArray &qbyaPostData)
{
    //生成发布路径
    //QUrl acceptTasksUrl(QString(QString(URL_HEAD_FORM) + ACCEPT_TASK_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl acceptTasksUrl(getWholeRequestUrl(ACCEPT_TASK_URL));

    QJson stJsonObj;
    //stJsonObj.add(AUTHORIZATION_KEY, m_qstrToken.toUtf8());
    stJsonObj.add(TEST_NUMBERS_KEY, qsTaskNums.toUtf8());

    qbyaPostData = stJsonObj.unformattedData();

    return acceptTasksUrl;
}

//获取指定页号的任务列表
QUrl CloudService::getTaskListsUrl(QHttpMultiPart &stMultiPart, int pageNo, int pageLen)
{
    //生成发布路径
    //QUrl reqTaskListUrl(QString(QString(URL_HEAD_FORM) + QUERY_TASK_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl reqTaskListUrl(getWholeRequestUrl(QUERY_TASK_URL));

    QHttpPart stPagePart;
    QString strPage = QString("form-data; name=\"%1\"").arg(PAGE_KEY);
    stPagePart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(strPage));
    stPagePart.setBody(QString::number(pageNo).toUtf8());
    stMultiPart.append(stPagePart);

    QHttpPart stLimitPart;
    QString strLimit = QString("form-data; name=\"%1\"").arg(LIMIT_KEY);
    stLimitPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(strLimit));
    stLimitPart.setBody(QString::number(pageLen).toUtf8());
    stMultiPart.append(stLimitPart);

    //添加可选的筛选字段
    if(m_bFilterSetted)
    {
        QDateTime tmpDateTime;

        if(!m_taskFilter.strPlanExecuteDateStart.isEmpty())
        {
            tmpDateTime = QDateTime::fromString(m_taskFilter.strPlanExecuteDateStart, TASK_TIME_FORMAT);
            int iTime = tmpDateTime.toMSecsSinceEpoch() / 1000;     //ms

            QHttpPart stTmpPart;
            QString strTmp = QString("form-data; name=\"%1\"").arg(PLAN_TIME_BEGIN_KEY);
            stTmpPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(strTmp));
            stTmpPart.setBody(QString::number(iTime).toUtf8());
            stMultiPart.append(stTmpPart);
        }

        if(!m_taskFilter.strPlanExecuteDateEnd.isEmpty())
        {
            tmpDateTime = QDateTime::fromString(m_taskFilter.strPlanExecuteDateEnd, TASK_TIME_FORMAT);
            int iTime = tmpDateTime.toMSecsSinceEpoch() / 1000;     //ms

            QHttpPart stTmpPart;
            QString strTmp = QString("form-data; name=\"%1\"").arg(PLAN_TIME_END_KEY);
            stTmpPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(strTmp));
            stTmpPart.setBody(QString::number(iTime).toUtf8());
            stMultiPart.append(stTmpPart);
        }

        /*if(m_taskFilter.eTaskState != PDAServiceNS::TaskUndefineed)
        {
            QHttpPart stTmpPart;
            QString strTmp = QString("form-data; name=\"%1\"").arg(TEST_STATUS_KEY);
            stTmpPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(strTmp));
            stTmpPart.setBody(QString::number((int)(m_taskFilter.eTaskState)).toUtf8());
            stMultiPart.append(stTmpPart);
        }*/
    }

    return reqTaskListUrl;
}

/*************************************************
函数名：
输入参数: baResponse--应答的内容
输出参数：NULL
返回值：ReplyCode
功能：解析登录请求的应答
*************************************************************/
ReplyCode CloudService::parseLoginResponse(const QByteArray &baResponse)
{
    ReplyCode replyCode;
    if(!baResponse.isEmpty())
    {
        QJson json(baResponse);
        replyCode = (ReplyCode) json.value(REPLY_CODE_KEY).toNumber();
        if(replyCode == REPLY_SUCCESS_CODE)
        {
            //save 权限码
            QJson jsonResult = json.value(REPLY_RESULT_KEY);
            m_authId = jsonResult.value(RESPONSE_AUTH_ID_KEY).toString();
        }
    }
    else
    {
        replyCode = HTTP_REPLY_EMPTY;
    }

    qDebug()<<"CloudService::parseLoginResponse, m_authId: "<<m_authId;
    return replyCode;
}

//解析请求任务列表反馈数据 解析结果添加到taskInfos中
ReplyCode CloudService::parseQueryTaskListResponse(const QByteArray& baResponse, const int expectPageNo,
                                                   int &totalPage, int &totalRecords,
                                                   QVector<TaskInfo>& taskInfos)
{
    ReplyCode replyCode;
    if(!baResponse.isEmpty())
    {
        QJson json(baResponse);
        //解析状态和方法，判断有效性
        replyCode = (ReplyCode)json.value(REPLY_CODE_KEY).toNumber();
        QString strMsg = json.value(REPLY_MSG_KEY).toString();

        qDebug() << "CloudService::parseQueryTaskListResponse, code: " << replyCode << ", msg: " << strMsg << endl;

        if(replyCode == REPLY_SUCCESS_CODE)
        {
            //taskInfos.clear();
            //解析结果字段
            QJson jsonResult(json.value(REPLY_RESULT_KEY));

            totalPage = jsonResult.value(TOTAL_PAGE_KEY).toNumber();
            totalRecords = jsonResult.value(TOTAL_RECORD_KEY).toNumber();

            qDebug() << "CloudService::parseQueryTaskListResponse, total records: " << totalRecords << endl;

            int pageNo = jsonResult.value(PAGE_NO_KEY).toNumber();//返回页号
            int pageLength = jsonResult.value(REPLY_PAGE_LEN_KEY).toNumber();   //页面长度

            //判断应答的页码是否是请求的页码
            if(pageNo == expectPageNo)
            {
                //计算当前应答的任务个数
                int iLeftCount = totalRecords - pageLength * (pageNo - 1);
                int iTaskNum = pageLength > iLeftCount ? iLeftCount : pageLength;

                int iTaskSize = jsonResult.value(TASK_LIST_KEY).count();

                if(iTaskNum != iTaskSize)
                {
                    qDebug() << QString("CloudService::parseQueryTaskListResponse, iTaskNum(%1) != iTaskSize(%2).").arg(iTaskNum).arg(iTaskSize) << endl;
                }

                //遍历各个任务信息
                for(int i = 0; i < iTaskSize; ++i)
                {
                    TaskInfo tmpInfo;   //存储当前任务信息
                    QJson jsonTask(jsonResult.value(TASK_LIST_KEY).at(i));    //任务json

                    qint64 lTime;
                    //lTime= jsonTask.value(TASK_CREATED_KEY).toNumber();
                    //lTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(lTime * 1000);   //ms
                    //tmpInfo.strCreateTime = QDateTime::fromMSecsSinceEpoch(lTime).toString(TASK_TIME_FORMAT);
                    //tmpInfo.lCreateTime = lTime;

                    lTime= jsonTask.value(TASK_EXECUTED_KEY).toNumber();        //s
                    lTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(lTime * 1000);     //ms
                    tmpInfo.strPlanTime = QDateTime::fromMSecsSinceEpoch(lTime).toString(TASK_TIME_FORMAT);
                    tmpInfo.lPlanTime = lTime;

                    tmpInfo.strStationName = jsonTask.value(STATION_NAME_KEY).toString();

                    tmpInfo.strItemName = jsonTask.value(TEST_NAME_KEY).toString();
                    tmpInfo.strInnerId = jsonTask.value(INNER_ID_KEY).toString();

                    tmpInfo.eTaskState = PDAServiceNS::TaskState(jsonTask.value(TEST_STATUS_KEY).toNumber());

                    tmpInfo.uiTestedCount = jsonTask.value(TESTED_COUNT_KEY).toNumber();
                    tmpInfo.uiTotalCount = jsonTask.value(TOTAL_TEST_COUNT_KEY).toNumber();

                    taskInfos.append(tmpInfo);

                    qDebug() << "CloudService::parseQueryTaskListResponse, task name: " << tmpInfo.strItemName << endl;

                }

            }
            else//返回错误页码
            {
                replyCode = REQUEST_PAGE_UNMATCHED_ERR;
            }
        }

    }
    else
    {
        replyCode = HTTP_REPLY_EMPTY;
    }

    return replyCode;
}

//上传完整文件
ReplyCode CloudService::uploadWholeFile(const QString& filePath, QString &urlPath, bool bNeedDeCrypt, UINT32 uiTimeout)
{
    ReplyCode replyCode = REPLY_SUCCESS_CODE;
    //获取文件类型
    QFileInfo fileInfo(filePath);
    QString strSuffix = fileInfo.suffix();
    //获取文件内容
    QByteArray fileContent;
    QFile file(filePath);
    if(file.open(QFile::ReadOnly))
    {
        fileContent = file.readAll();
        file.close();
        if(bNeedDeCrypt)
        {
            //fileContent = deCrypt(fileContent);
        }
    }
    else
    {
        qDebug() << "CloudService::uploadWholeFile, cann't open file : " << filePath << endl;
        return FILE_OP_ERROR;
    }

    if(replyCode == REPLY_SUCCESS_CODE)
    {
        if((UINT32) fileContent.size() < MAX_BLOCK_SIZE)//文件不大时整体上传
        {
            replyCode = uploadFileContent(fileContent, fileInfo.fileName(), strSuffix, urlPath, WHOLE_FILE_TYPE, uiTimeout);
        }
        else//文件过大时分块上传
        {
            QStringList urlList;
            int totalCount = fileContent.size(); //文件总大小
            int leftCount = totalCount;//剩余字节的大小
            while(leftCount > 0)
            {
                QString strTmpUrl;
                int sendCount = (UINT32) leftCount > MAX_BLOCK_SIZE ? MAX_BLOCK_SIZE : leftCount;
                QByteArray sendContent = fileContent.left(sendCount);
                fileContent.remove(0, sendCount);

                //发送数据内容
                replyCode = uploadFileContent(sendContent, fileInfo.fileName(), strSuffix, strTmpUrl, PART_FILE_TYPE, uiTimeout);
                //qDebug() << "CloudService::uploadWholeFile, left content size : " << leftCount << endl;
                if(replyCode == REPLY_SUCCESS_CODE)
                {
                    urlList.append(strTmpUrl);
                    leftCount -= sendCount;
                }
                else
                {
                    qDebug() << "CloudService::uploadWholeFile, upload fail, file : " << filePath << endl;
                    break;
                }
            }

            if(replyCode == REPLY_SUCCESS_CODE)
            {
                replyCode = mergeFileBlocks(urlList, strSuffix, urlPath, uiTimeout);
            }
        }
    }

    return replyCode;
}

ReplyCode CloudService::uploadFileContent(const QByteArray& fileContent, const QString& fileName, const QString strSuffix, QString& urlPath,
                                          FileType eType, UINT32 uiTimeout)
{
    ReplyCode reply;
    UploadCMSFileInfo stUplFileInfo;

    //创建URL头部
    //QUrl uploadFileUrl(QString(QString(URL_HEAD_FORM) + UPLOAD_FILE_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl uploadFileUrl(getWholeRequestUrl(UPLOAD_FILE_URL));

    //进行MD5码校验
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(fileContent);

    stUplFileInfo.qstrMd5Code = QString(hash.result().toHex());
    stUplFileInfo.qstrFileName = fileName;
    stUplFileInfo.qstrFileType = strSuffix;
    stUplFileInfo.qbaFileContent = fileContent;
    stUplFileInfo.qstrType = QString::number(int(eType));

    qDebug() << "CloudService::uploadFileContent, file name: " << fileName << endl;
    //qDebug() << "CloudService::uploadFileContent, md5: " << stUplFileInfo.strMd5Code << endl;

    //为文件内容创建body
    QHttpMultiPart *pMultiPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);
    createUploadFileParts(stUplFileInfo, *pMultiPart);

    QByteArray byaResponse;          //接收内容
    //httpbean发送数据
    bool bRet = m_pHttpBean->fileUpload(uploadFileUrl, m_qstrToken, *pMultiPart, byaResponse, uiTimeout);

    //处理返回的结果，并填充urlPath
    if(bRet)
    {
        reply = parseUploadWholeFileResponse(byaResponse, urlPath);
    }
    else
    {
        reply = REQUEST_TIMEOUT_ERR;
    }

    qDebug() << "CloudService::uploadFileContent, file url: " << urlPath << endl;

    return reply;
}

/*************************************************
函数名：
输入参数: fileContent--文件内容
         strSuffix--文件的后缀
         eType--文件功能代码
输出参数： multiPart--需要填充的multiPart
返回值：
功能：获取上传文件信息的multiPart
*************************************************************/
void CloudService::createUploadFileParts(const QByteArray& fileContent, const QString& strFileName, QHttpMultiPart &multiPart)
{
    //填充文件内容字段
    QHttpPart filePart;
    QString strHeader = QString("form-data; name=\"%1\"; filename=\"%2\"").arg("file").arg(strFileName);
    filePart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(strHeader));
    filePart.setHeader(QNetworkRequest::ContentTypeHeader, QVariant("application/octet-stream"));
    filePart.setBody(fileContent);
    multiPart.append(filePart);

    return;
}

void CloudService::createUploadFileParts(const UploadCMSFileInfo &stUploadFileInfo, QHttpMultiPart &multiPart)
{
    //填充文件内容字段
    QHttpPart stFilePart;
    QString strHeader = QString("form-data; name=\"%1\"; filename=\"%2\"").arg("file").arg(stUploadFileInfo.qstrFileName);
    stFilePart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(strHeader));
    stFilePart.setHeader(QNetworkRequest::ContentTypeHeader, QVariant("application/octet-stream"));
    stFilePart.setBody(stUploadFileInfo.qbaFileContent);
    multiPart.append(stFilePart);

    QHttpPart stMd5Part;
    QString qstrMd5 = QString("form-data; name=\"md5\"");
    stMd5Part.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrMd5));
    stMd5Part.setBody(stUploadFileInfo.qstrMd5Code.toUtf8());
    multiPart.append(stMd5Part);

    QHttpPart stFileTypePart;
    QString qstrFileType = QString("form-data; name=\"filetype\"");
    stFileTypePart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrFileType));
    stFileTypePart.setBody(stUploadFileInfo.qstrFileType.toUtf8());
    multiPart.append(stFileTypePart);

    QHttpPart stTypePart;
    QString qstrType = QString("form-data; name=\"type\"");
    stTypePart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrType));
    stTypePart.setBody(stUploadFileInfo.qstrType.toUtf8());
    multiPart.append(stTypePart);

    return;
}

QUrl CloudService::getUploadFileUrl(const QByteArray& fileContent, const QString &strSuffix, FileType eType, QByteArray& qbyaPostData)
{
    //生成发布路径
    //QUrl uploadFileUrl(QString(QString(URL_HEAD_FORM) + UPLOAD_FILE_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl uploadFileUrl(getWholeRequestUrl(UPLOAD_FILE_URL));

    //进行MD5码校验
    QCryptographicHash hash(QCryptographicHash::Md5);
    hash.addData(fileContent);
    QByteArray calcedMd5 = hash.result().toHex();

    QJson stJsonObj;
    stJsonObj.add(FILE_TYPE_KEY, strSuffix.toUtf8());
    stJsonObj.add(BLOCK_MD5_KEY, calcedMd5);
    stJsonObj.add(FILE_CODE_KEY, int(eType));
    stJsonObj.add(FILE_CONTENT_KEY, fileContent);

    qbyaPostData = stJsonObj.unformattedData();

    return uploadFileUrl;
}

ReplyCode CloudService::parseUploadWholeFileResponse(const QByteArray &baResponse, QString &path)
{
    ReplyCode replyCode ;

    if(!baResponse.isEmpty())
    {
        QJson json(baResponse);
        //解析状态和方法，判断有效性
        replyCode = (ReplyCode)json.value(REPLY_CODE_KEY).toNumber();
        if(replyCode == REPLY_SUCCESS_CODE)
        {
            //解析结果字段
            QJson jsonResult(json.value(REPLY_RESULT_KEY));
            path = jsonResult.value(UPLOAD_PATH_KEY).toString();
        }
    }
    else
    {
        replyCode = HTTP_REPLY_EMPTY;
    }

    return replyCode;
}

//上传任务信息
ReplyCode CloudService::uploadTaskInfo(const QString& qstrInrId, const QString& urlPath, UINT32 uiTimeout)
{
    ReplyCode reply;

    //获取对应的url
    //QUrl stUploadTaskUrl(QString(QString(URL_HEAD_FORM) + UPLOAD_TASK_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl stUploadTaskUrl(getWholeRequestUrl(UPLOAD_TASK_URL));

    QHttpMultiPart *pMultiPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);

    QHttpPart stTestNumPart;
    QString qstrInrIdHeader = QString("form-data; name=\"%1\"").arg(INNER_ID_KEY);
    stTestNumPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrInrIdHeader));
    stTestNumPart.setBody(qstrInrId.toUtf8());
    pMultiPart->append(stTestNumPart);

    QHttpPart stTaskUrlPart;
    QString qstrTaskUrl = QString("form-data; name=\"%1\"").arg(TASK_URL_KEY);
    stTaskUrlPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrTaskUrl));
    stTaskUrlPart.setBody(urlPath.toUtf8());
    pMultiPart->append(stTaskUrlPart);

    QByteArray baResponse;      //接收内容

    //httpbean发送数据
    bool bRet = m_pHttpBean->postDataToCmsByFormData(stUploadTaskUrl, m_qstrToken, *pMultiPart, baResponse, uiTimeout);

    //处理返回的结果，并填充urlPath
    if(bRet)
    {
        reply = parseUploadTaskResponse(baResponse, qstrInrId);
    }
    else
    {
        reply = REQUEST_TIMEOUT_ERR;
    }

    return reply;
}

//获取任务上传的对应接口
QUrl CloudService::getUploadTaskUrl(const QString& taskNum, const QString& urlPath, QByteArray& qbyaPostData)
{
    //生成发布路径
    //QUrl uploadTaskUrl(QString(QString(URL_HEAD_FORM) + UPLOAD_TASK_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl uploadTaskUrl(getWholeRequestUrl(UPLOAD_TASK_URL));

    QJson stJsonObj;
    //stJsonObj.add(AUTHORIZATION_KEY, m_qstrToken.toUtf8());
    stJsonObj.add(TEST_NUMBER_KEY, taskNum.toUtf8());
    stJsonObj.add(TASK_URL_KEY, urlPath.toUtf8());

    qbyaPostData = stJsonObj.unformattedData();

    return uploadTaskUrl;
}

//任务上传收到的应答解析
ReplyCode CloudService::parseUploadTaskResponse(const QByteArray& baResponse, const QString& qstInrId)
{
    ReplyCode replyCode;//处理结果

    if(!baResponse.isEmpty())
    {
        QJson json(baResponse);
        //解析状态和方法，判断有效性
        replyCode = (ReplyCode)json.value(REPLY_CODE_KEY).toNumber();
        if(replyCode == REPLY_SUCCESS_CODE)
        {

            //解析结果字段
            QJson jsonResult(json.value(REPLY_RESULT_KEY));
            QString qstrRspInrId = jsonResult.value(INNER_ID_KEY).toString();
            qDebug() << "CloudService::parseUploadTaskResponse, upload test number: " << qstInrId << ", rsp number: " << qstrRspInrId << endl;
            if(qstrRspInrId != qstInrId)
            {
                replyCode = TASK_NUMBER_UMMATCHED_ERR;
            }
        }
    }
    else
    {
        replyCode = HTTP_REPLY_EMPTY;
    }

    return replyCode;
}

QByteArray CloudService::dataFile2ByteArray(const QByteArray& qbaFileData)
{
    //special process for requirement of cms
    QByteArray qbaData = qbaFileData.toBase64();

    qbaData.replace(QByteArray("+"), QByteArray("%2B"));
    qbaData.replace(QByteArray(" "), QByteArray("%20"));
    qbaData.replace(QByteArray("/"), QByteArray("%2F"));
    qbaData.replace(QByteArray("?"), QByteArray("%3F"));
    qbaData.replace(QByteArray("%"), QByteArray("%25"));
    qbaData.replace(QByteArray("#"), QByteArray("%23"));
    qbaData.replace(QByteArray("&"), QByteArray("%26"));
    qbaData.replace(QByteArray("="), QByteArray("%3D"));

    return qbaData;
}

//云端文件块合并
ReplyCode CloudService::mergeFileBlocks(const QStringList& urlNames, const QString& strSuffix, QString &urlPath, UINT32 uiTimeout)
{
    ReplyCode reply;

    //配置url
    //QUrl stMergeFileUrl(QString(QString(URL_HEAD_FORM) + MERGER_FILE_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl stMergeFileUrl(getWholeRequestUrl(MERGER_FILE_URL));

    QHttpMultiPart *pMultiPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);

    QHttpPart stFileTypePart;
    QString qstrFileType = QString("form-data; name=\"%1\"").arg(FILE_TYPE_KEY);
    stFileTypePart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrFileType));
    stFileTypePart.setBody(strSuffix.toUtf8());
    pMultiPart->append(stFileTypePart);

    QHttpPart stTypePart;
    QString qstrType = QString("form-data; name=\"%1\"").arg(FILE_CODE_KEY);
    stTypePart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrType));
    stTypePart.setBody(QString::number((int)WHOLE_FILE_TYPE).toUtf8());
    pMultiPart->append(stTypePart);

    QHttpPart stParamPart;
    QString qstrParam = QString("form-data; name=\"%1\"").arg(FILE_BLOCKS_KEY);
    stParamPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(qstrParam));
    QByteArray qbyaParam;
    qbyaParam.append("[");
    for(int i = 0; i < urlNames.size(); ++i)
    {
        QString stTmpUrl = urlNames.at(i);
        QStringList qlsTmp = stTmpUrl.split("\\");
        QString strFileName = qlsTmp.last();
        QString strTemp;
        strTemp += "\"";
        strTemp += QFileInfo(strFileName).baseName();
        strTemp += "\"";

        qbyaParam.append(strTemp);

        if(i == (urlNames.size() - 1))
        {
            qbyaParam.append("]");
        }
        else
        {
            qbyaParam.append(",");
        }
    }

    qDebug() << "CloudService::mergeFileBlocks, url param: " << qbyaParam << endl;

    stParamPart.setBody(qbyaParam);
    pMultiPart->append(stParamPart);


    QByteArray baResponse;  //接收内容

    //httpbean发送数据
    bool bRet = m_pHttpBean->postDataToCmsByFormData(stMergeFileUrl, m_qstrToken, *pMultiPart, baResponse, uiTimeout);

    //处理返回的结果，并填充urlPath
    if(bRet)
    {
        reply = parseMergeFileResponse(baResponse, urlPath);
    }
    else
    {
        reply = HTTP_REPLY_EMPTY;
    }

    return reply;
}

QUrl CloudService::mergeFileUrl(const QStringList& urlNames, const QString& strSuffix, QByteArray& qbyaPostData)
{
    //生成发布路径
    //QUrl mergeFileUrl(QString(QString(URL_HEAD_FORM) + MERGER_FILE_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl mergeFileUrl(getWholeRequestUrl(MERGER_FILE_URL));

    QJson stJsonObj;
    //stJsonObj.add(AUTHORIZATION_KEY, m_qstrToken.toUtf8());
    stJsonObj.add(FILE_TYPE_KEY, strSuffix.toUtf8());
    stJsonObj.add(FILE_CODE_KEY, int(WHOLE_FILE_TYPE));

    QJson stJSParam(QJson::Array);
    for(int i = 0; i < urlNames.size(); ++i)
    {
        QString tmpUrl = urlNames.at(i);
        QStringList tmpList = tmpUrl.split("\\");
        QString fileName = tmpList.last();
        QString strParam = QFileInfo(fileName).baseName();
        stJSParam.addStrToArray(strParam.toUtf8());
    }
    stJsonObj.add(FILE_BLOCKS_KEY, stJSParam);

    qbyaPostData = stJsonObj.unformattedData();

    return mergeFileUrl;
}

/*************************************************
函数名：
输入参数: baResponse---response
输出参数：qsFileUrl---任务文件存储在云端的url
返回值：ReplyCode
功能：解析下载任务应答，读出任务文件存储在云端的url
*************************************************************/
ReplyCode CloudService::parseMergeFileResponse(const QByteArray &baResponse, QString &qsFileUrl)
{
    ReplyCode replyCode;

    if(!baResponse.isEmpty())
    {
        QJson json(baResponse);
        //解析状态和方法，判断有效性
        replyCode = (ReplyCode)json.value(REPLY_CODE_KEY).toNumber();
        if(replyCode == REPLY_SUCCESS_CODE)
        {
            //解析结果字段
            QJson jsonResult(json.value(REPLY_RESULT_KEY));
            qsFileUrl = jsonResult.value(UPLOAD_PATH_KEY).toString();
        }

    }
    else
    {
        replyCode = HTTP_REPLY_EMPTY;
    }

    return replyCode;
}

/*************************************************
函数名：downloadImgMediaFiles
输入参数: strTaskFilePath--任务描述文件
输出参数：NULL
返回值：ReplyCode
功能：下载任务描述文件的音频与图片文件
*************************************************************/
ReplyCode CloudService::downloadImgMediaFiles(const QString &strTaskFilePath)
{
    //modify by Mountains

    ReplyCode reply = REPLY_SUCCESS_CODE;

    TaskFileIO tmpFileIO;

    ImgMediaPath stPath;
    //如果没有音频、图片文件，则直接返回
    if( !tmpFileIO.getImgMediaFilesPath(strTaskFilePath, stPath) )
    {
        return reply;
    }

    reply = downloadFiles(stPath);

    //音频、图片文件下载完成后更新任务描述文件内的相关path信息
    if(REPLY_SUCCESS_CODE == reply)
    {
        DevFileOper fileOper;
        fileOper.moveImgMediaFile( QFileInfo(strTaskFilePath).absolutePath(), stPath );
        tmpFileIO.updateTaskFile(strTaskFilePath, stPath);
    }
    else
    {
        //删除所有临时文件
        QStringList listTmpFiles = TaskFileIO::allImgMediaFilesPath(stPath);
        QFile file;
        for(int i=0; i<listTmpFiles.size(); i++)
        {
            file.setFileName(listTmpFiles.at(i));

            if(file.exists())
            {
                file.setPermissions(QFile::WriteOther);
                file.remove();
            }
        }
    }

    return reply;
}

/*************************************************
函数名：downloadFiles
输入参数: NULL
输出参数：stFilesPath--文件路径
返回值：ReplyCode
功能：下载任务描述文件的音频和图片文件
*************************************************************/
ReplyCode CloudService::downloadFiles(ImgMediaPath &stFilesPath)
{
    ReplyCode reply = REPLY_SUCCESS_CODE;

    reply = downloadFile(stFilesPath.strMainImgPath);
    if( REPLY_SUCCESS_CODE != reply )
    {
        return reply;
    }

    for(int i=0; i<stFilesPath.listMediaPath.size(); i++)
    {
        reply = downloadFile(stFilesPath.listMediaPath[i]);

        if( REPLY_SUCCESS_CODE != reply )
        {
            return reply;
        }
    }

    for(int i=0; i<stFilesPath.listImgPath.size(); i++)
    {
        reply = downloadFile(stFilesPath.listImgPath[i]);

        if( REPLY_SUCCESS_CODE != reply )
        {
            return reply;
        }
    }

    for(int i=0; (i < stFilesPath.iSubCount) && (i < stFilesPath.vtSubLevel.size()); ++i)
    {
        ImgMediaPath stIMPathTmp = stFilesPath.vtSubLevel.at(i);
        reply = downloadFiles(stIMPathTmp);
        stFilesPath.vtSubLevel.replace(i, stIMPathTmp);

        if( REPLY_SUCCESS_CODE != reply )
        {
            return reply;
        }
    }

    return reply;
}

/*************************************************
函数名：downloadFile
输入参数: uiTimeOut--超时时间
输出参数：strFilePath--文件路径
返回值：ReplyCode
功能：下载任务描述文件的单个音频或图片文件
*************************************************************/
ReplyCode CloudService::downloadFile(QString &strFilePath, UINT32 uiTimeOut)
{
    if(strFilePath.isEmpty())
    {
        return REPLY_SUCCESS_CODE;
    }

    ReplyCode reply = REPLY_SUCCESS_CODE;

    QByteArray qbyaFileData;

    int currentBlockIndex = 1;
    int totalBlockIndex = 1;

    while( !m_bStopOp )
    {
        QByteArray qbyaRawHeaderKey(REPLY_RESULT_KEY);
        QByteArray qbyaResult;          //含有result的json数据
        QByteArray qbyaFileContentTmp;          //文件内容

        QHttpMultiPart *pTmpMPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);
        //调用文件下载接口
        QUrl tmpUrl = downloadFileUrl(strFilePath, currentBlockIndex, *pTmpMPart);

        bool bRet = m_pHttpBean->fileDownload(tmpUrl, m_qstrToken, *pTmpMPart, qbyaRawHeaderKey, qbyaResult, qbyaFileContentTmp, uiTimeOut);
        if(bRet)
        {
            reply = parseDownloadFileResponse(qbyaResult, totalBlockIndex, currentBlockIndex);
            if(reply == REPLY_SUCCESS_CODE)
            {
                qbyaFileData.append(qbyaFileContentTmp);
                if(currentBlockIndex < totalBlockIndex)
                {
                    ++currentBlockIndex;
                }
                else
                {
                    //保存到临时文件
                    QByteArray baMd5 = AppServerUtils::calcMd5Value( qbyaFileData );
                    QString strSuffix = QFileInfo(strFilePath).suffix();
                    if( !AppServerUtils::saveTmpFile( qbyaFileData, baMd5, strSuffix, strFilePath ) )
                    {
                        reply = FILE_OP_ERROR;
                        dbg_warning("CloudService::downloadFile : request to download file failed because of save tmp file failed!\n");
                    }

                    break;
                }
            }
            else
            {
                dbg_warning("CloudService::downloadFile : request to download file failed because of reply error!\n");
                break;
            }
        }
        else
        {
            reply = REQUEST_TIMEOUT_ERR;
            dbg_warning("CloudService::downloadFile : request to download file failed because of timeout!\n");
            break;
        }
    }

    return reply;
}

ReplyCode CloudService::loginToGetToken(const QString qstrUserNme, const QString qstrPwd)
{
    readCloudConfig();  //read the lastest cms config information

    AfDes_CBC des_cbc(LOGIN_PWD_DES_KEY, strlen(LOGIN_PWD_DES_KEY), iPKCS5Padding);
    des_cbc.SetIV(LOGIN_PWD_DES_KEY);
    QString qstrCryptedPwd = des_cbc.enCryptDes(qstrPwd.toLatin1()).toHex();
    Q_UNUSED(qstrCryptedPwd);

    QByteArray byaPost;              //发送内容

    //生成对应的url
    //QUrl stTmpUrl = getLoginGetTokenUrlAndParam(qstrUserNme, qsCryptedPwd, byaPost);       //加密做法
    QUrl stTmpUrl = getLoginGetTokenUrlAndParam(qstrUserNme, qstrPwd, byaPost);              //暂时不用加密

    QByteArray byaResponse;          //接收内容
    QByteArray byaResponseHeader;
    QByteArray byaRawHeader;

    //httpbean发送数据
    bool bRet = m_pHttpBean->postData(stTmpUrl, "", byaPost, byaResponse, byaRawHeader, byaResponseHeader);

    //处理返回的结果
    ReplyCode reply = bRet ? parseLoginGetTokenResponse(byaResponse) : REQUEST_TIMEOUT_ERR;

    reply = (reply == REPLY_CMS_SUCCESS) ? REPLY_SUCCESS_CODE : reply;     //类型码值转换一下

    m_bLogined = (reply == REPLY_SUCCESS_CODE) ? true : false;

    return reply;
}

QUrl CloudService::getLoginGetTokenUrlAndParam(const QString qstrUserNme, const QString qstrPwd, QByteArray & byaPostData)
{
    //拿取最新的cms信息
    readCloudConfig();
    //生成发布路径
    //QUrl stReqUrl(QString(QString(URL_HEAD_FORM) + LOGIN_TOKEN_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl stReqUrl(getWholeRequestUrl(LOGIN_TOKEN_URL));

    //QT 5.0
    //QJsonObject stJsonObj;
    //stJsonObj.insert(AUTH_COMPANY_NAME_KEY, COMPANY_NAME);
    //stJsonObj.insert(AUTH_USER_NAME_KEY, qstrUserNme);
    //stJsonObj.insert(AUTH_PWD_KEY, qstrPwd);
    //stJsonObj.insert(AUTH_LOG_TYPE, m_qstrLoginType);

    //QJsonDocument stJsonDoc;
    //stJsonDoc.setObject(stJsonObj);
    //byaPostData = stJsonDoc.toJson(QJsonDocument::Compact);

    QJson stJsonObj;
    //stJsonObj.add(AUTH_COMPANY_NAME_KEY, COMPANY_NAME);
    stJsonObj.add(AUTH_USER_NAME_KEY, qstrUserNme.toUtf8());
    stJsonObj.add(AUTH_PWD_KEY, qstrPwd.toUtf8());
    stJsonObj.add(AUTH_LOG_TYPE, m_iLoginType);

    byaPostData = stJsonObj.unformattedData();

    return stReqUrl;
}

ReplyCode CloudService::parseLoginGetTokenResponse(const QByteArray &byaRespone)
{
    ReplyCode replyCode;
    if(!byaRespone.isEmpty())
    {
        QJson json(byaRespone);

        //qDebug() << "CloudService::parseLoginGetTokenResponse, json data: " << json.toString() << endl;

        replyCode = (ReplyCode)(json.value(REPLY_CODE_KEY).toNumber());
        if(replyCode == REPLY_CMS_SUCCESS)
        {
            //token
            QJson stResult = json.value(REPLY_RESULT_KEY);
            m_qstrToken = stResult.value(AUTH_TOKEN_KEY).toString();
        }

        qDebug() << "CloudService::parseLoginGetTokenResponse, code: " << replyCode << ", msg: " << json.value(REPLY_MSG_KEY).toString() << endl;
        //qDebug() << "CloudService::parseLoginGetTokenResponse, m_qstrToken: " << m_qstrToken << endl;
    }
    else
    {
        replyCode = HTTP_REPLY_EMPTY;
        qDebug() << "CloudService::parseLoginGetTokenResponse, response is empty." << endl;
    }

    return replyCode;
}

ReplyCode CloudService::logoutToReleaseToken()
{
    QByteArray byaPost;              //发送内容
    //生成对应的url
    QUrl stTmpUrl = getLogoutUrlAndParam(byaPost);

    QByteArray byaResponse;          //接收内容
    QByteArray byaResponseHeader;
    QByteArray byaRawHeader;

    //httpbean发送数据
    bool bRet = m_pHttpBean->postData(stTmpUrl, "", byaPost, byaResponse, byaRawHeader, byaResponseHeader);

    //处理返回的结果
    ReplyCode reply = bRet ? parseLogoutResponse(byaResponse) : REQUEST_TIMEOUT_ERR;

    reply = (reply == REPLY_CMS_SUCCESS) ? REPLY_SUCCESS_CODE : reply;     //类型码值转换一下
    m_bLogined = (reply == REPLY_SUCCESS_CODE) ? false : true;      //登出

    return reply;
}

QUrl CloudService::getLogoutUrlAndParam(QByteArray & byaPostData)
{
    //生成发布路径
    //QUrl stReqUrl(QString(QString(URL_HEAD_FORM) + LOGOUT_TOKEN_URL).arg(m_strCloudIP).arg(m_strCloudPort));
    QUrl stReqUrl(getWholeRequestUrl(LOGOUT_TOKEN_URL));

    //添加所用的参数
    //QT 5.0
    //QJsonObject stJsonObj;
    //stJsonObj.insert(AUTH_LOG_TYPE, m_qstrLoginType);

    //QJsonDocument stJsonDoc;
    //stJsonDoc.setObject(stJsonObj);
    //byaPostData = stJsonDoc.toJson(QJsonDocument::Compact);

    QJson stJsonObj;
    stJsonObj.add(AUTH_LOG_TYPE, m_iLoginType);

    byaPostData = stJsonObj.unformattedData();

    return stReqUrl;
}

ReplyCode CloudService::parseLogoutResponse(const QByteArray &byaRespone)
{
    ReplyCode replyCode;
    if(!byaRespone.isEmpty())
    {
        QJson json(byaRespone);
        replyCode = (ReplyCode)(json.value(REPLY_CODE_KEY).toNumber());
        if(replyCode == REPLY_CMS_SUCCESS)
        {
            //token
            m_qstrToken.clear();
        }

        qDebug() << "CloudService::parseLoginGetTokenResponse, code: " << replyCode << ", msg: " << json.value(REPLY_MSG_KEY).toString() << endl;
    }
    else
    {
        replyCode = HTTP_REPLY_EMPTY;
        qDebug() << "CloudService::parseLoginGetTokenResponse, response is empty." << endl;
    }

    return replyCode;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：ReplyCode
功能：校验认证
*************************************************************/
ReplyCode CloudService::checkToken()
{
    return REPLY_SUCCESS_CODE;
}

/***********************************************
 * 功能：停止网络请求
 * ***********************************************/
void CloudService::stopRequest()
{
    return;
}
