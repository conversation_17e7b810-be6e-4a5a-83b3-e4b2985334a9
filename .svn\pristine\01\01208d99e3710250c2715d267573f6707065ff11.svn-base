#ifndef NEWTOOLTIP_H
#define NEWTOOLTIP_H

#include <QLabel>
#include <QBasicTimer>
#include <QVariant>

class NewToolTip
{
    NewToolTip();
public:
    // 文字方向
    enum TextDirection
    {
        Horizontal = 0,
        Vertical
    };

    // ### Qt 6 - merge the three showText functions below
    static void showText(const QPoint &pos, const QString &text, const TextDirection eTextDirection = Horizontal, QWidget *w = NULL);
    static void showText(const QPoint &pos, const QString &text, const TextDirection eTextDirection, QWidget *w, const QRect &rect);
    static void showText(const QPoint &pos, const QString &text, const TextDirection eTextDirection, QWidget *w, const QRect &rect, int msecDisplayTime);

    // 在点的上方显示提示
    static void showTextAbove(const QPoint &pos, const QString &text, QWidget *w = NULL);
    static void showTextAbove(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect);
    static void showTextAbove(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect, int msecDisplayTime);

    // 不处理文字
    static void showTextAboveWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w = NULL);
    static void showTextAboveWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect);
    static void showTextAboveWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect, int msecDisplayTime);

    // 在点的下方显示提示
    static void showTextBelow(const QPoint &pos, const QString &text, QWidget *w = NULL);
    static void showTextBelow(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect);
    static void showTextBelow(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect, int msecDisplayTime);

    static void showTextBelowWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w = NULL);
    static void showTextBelowWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect);
    static void showTextBelowWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect, int msecDisplayTime);

    static inline void hideText() { showText(QPoint(), QString()); }

    static bool isVisible();
    static QString text();

    static QPalette palette();
    static void setPalette(const QPalette &);
    static QFont font();
    static void setFont(const QFont &);
};

class NewTipLabel : public QLabel
{
    Q_OBJECT
public:
    NewTipLabel(const QString &text, const QPoint &pos, const NewToolTip::TextDirection eTextDirection, QWidget *w, int msecDisplayTime);
    ~NewTipLabel();
    static NewTipLabel *instance;

    bool eventFilter(QObject *, QEvent *);

    QBasicTimer hideTimer, expireTimer;

    bool fadingOut;

    void reuseTip(const QString &text, int msecDisplayTime, const QPoint &pos, const NewToolTip::TextDirection eTextDirection, bool bTextProcessed = true);
    void hideTip();
    void hideTipImmediately();
    void setTipRect(QWidget *w, const QRect &r);
    void restartExpireTimer(int msecDisplayTime);
    bool tipChanged(const QPoint &pos, const QString &text, QObject *o);
    void placeTip(const QPoint &pos, QWidget *w);
    void placeTipAbove(const QPoint &pos, QWidget *w);
    void placeTipBelow(const QPoint &pos, QWidget *w);

    static int getTipScreen(const QPoint &pos, QWidget *w);
protected:
    void timerEvent(QTimerEvent *e);
    void paintEvent(QPaintEvent *e);
    void mouseMoveEvent(QMouseEvent *e);
    void resizeEvent(QResizeEvent *e);
    QSize sizeHint() const;
    QSize minimumSizeHint() const;

private:
    // 计算最小行数的宽度
    int calMinRowCountWidth(const QString& qstrText, const int iScreenWidth, int& iOutRow);

private:
    QWidget *widget;
    QRect rect;
    NewToolTip::TextDirection m_eTextDirection;
};

#endif // NEWTOOLTIP_H
