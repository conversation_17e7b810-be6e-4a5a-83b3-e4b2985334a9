/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* PulseAnalysisView.h
*
* 初始版本：1.0
* 作者：陈康
* 创建日期：2017年4月12日
*       新版本重构
* 摘要：接入G100的脉冲分析界面

* 当前版本：1.0
*/

/*
 * 该界面需求
 * 显示脉冲采集的数据在PDSS图谱上
 * 提供两种模式进行聚类操作和分析
 *      （1）自动：将数据点最多分为四类，每个点使用各类特定的颜色的小椭圆形标识出来
 *              可进行的操作：在“图谱切换”按钮选择聚类颜色进入分析界面
 *      （2）手动：手动画出椭圆形区域，在椭圆形线框范围内的数据点为一类
 *              可进行的操作：“绘制”--画出椭圆形线框
 *                   “移动”--对椭圆形线框进行拖拽
 *                   “删除”--删除点中的椭圆形线框
 *                   “选择”--选中椭圆形线框，进入该类数据的分析界面
*/

#ifndef PULSEANALYSISVIEW_H
#define PULSEANALYSISVIEW_H

#include "chartview/ChartView.h"
#include "ca/CA.h"
#include "FastCollection/FastWavePDSS.h"
#include "datadefine.h"

#include <gridcluster.h>

#include "pushButton/RadioPushButton.h"
#include "CAViewConfig.h"

typedef enum _ClusterAnalysisMode
{
    CLUSTER_ANALYSIS_NONE = -1,    //初始模式
    CLUSTER_ANALYSIS_AUTO = 0,    //自动分析
    CLUSTER_ANALYSIS_MANUAL = 1   //手动分析
}ClusterAnalysisMode;

#define MAX_AUTO_CLUSTER 4
#define DEFAULT_CURVE_SIZE

class PulseAnalysisView : public ChartView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数: strTitle: 标题
            lData: 脉冲数据
            sampRateOpition: 采样率
            parent:父窗口
    TODO：直接传输容器数据（lData），进行大数据拷贝是否影响性能（注意：qt容器是隐式共享）
    *************************************************************/
    explicit PulseAnalysisView(const QString& strTitle,
                               const QList<CA::PulseData> &lData,
                               CA::SampleRate sampRateOpition,
                               double dPulseWidth,
                               qint32 iRangeMax,
                               float fDiagnosisYRange,
                               QWidget *parent = 0);


    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~PulseAnalysisView();

    void setDiagnosisYRange(float fDiagnosisYRange);

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

private slots:
    /*************************************************
    函数名： onSelectedData
    输入参数: lData--被选中数据点的序号集合
    输出参数： NULL
    返回值： NULL
    功能： 在手动分析模式下，对图谱中选中聚类包含的数据进行处理
    *************************************************************/
    void onSelectedData( QVector< UINT16 > lData);

private:
    /*************************************************
    函数名： initData
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能：初始化使用的数据
    *************************************************************/
    void initData(void);

    /*************************************************
    函数名： createUI
    输入参数: parent--父窗口
    输出参数：NULL
    返回值： NULL
    功能：初始化用户界面布局和显示
    *************************************************************/
    void createUI(QWidget *parent);

    /*************************************************
    输入参数: parent--父窗口
    输出参数：NULL
    返回值： NULL
    功能： 创建图谱显示区域
    *************************************************************/
    ChartWidget * createCharts(QWidget *parent);

    /*************************************************
    输入参数: parent--父窗口
    输出参数：NULL
    返回值： NULL
    功能： 创建PDSS图谱
    *************************************************************/
    void createPDSSChart(QWidget *parent);

    /*************************************************
    函数名： setUIData
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能：设置用户界面的显示数据
    *************************************************************/
    void setUIData(void);

    /*************************************************
    函数名： switchAnalysisMode
    输入参数: eMode -- 当前的分析模式（自动/手动）
    输出参数：NULL
    返回值： NULL
    功能：切换分析模式时进行的相应处理
    *************************************************************/
    void switchAnalysisMode(ClusterAnalysisMode eMode);

    /*************************************************
    函数名： initManualMode
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能：切换到手动模式进行的初始化操作
    *************************************************************/
    void initManualMode();

    /*************************************************
    函数名： initAutoMode
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能：切换到自动模式进行的初始化操作
    *************************************************************/
    void initAutoMode();

    /*************************************************
    功能： 将自动聚类的分析结果画在图谱上
    *************************************************************/
    void drawAutoCluster();

    /*************************************************
    功能： 设置“图谱显示”按钮
    *************************************************************/
    void setAutoAnalysisButton();

    /*************************************************
    函数名： enableButtonByAnalysisMode
    输入参数: eMode -- 当前的分析模式
    输出参数： NULL
    返回值： NULL
    功能： 根据分析模式的不同使能相应按键
    *************************************************************/
    void enableButtonByAnalysisMode( ClusterAnalysisMode eMode );

    /*************************************************
    功能： 手动模式下选择聚类
    *************************************************************/
    void chooseCluster();

    /*************************************************
    功能： 手动模式下绘制聚类
    *************************************************************/
    void drawCluster();

    /*************************************************
    功能： 手动模式下移动聚类
    *************************************************************/
    void moveCluster();

    /*************************************************
    功能： 手动模式下删除聚类
    *************************************************************/
    void deleteCluster();

    /*************************************************
    函数名： autoClusterChange
    输入参数: usIndex -- 数据切换序号
    输出参数： NULL
    返回值： NULL
    功能： 自动聚类分析完成后进行数据的切换
    *************************************************************/
    void autoClusterChange( UINT16 usIndex );

    /*************************************************
    输入参数: data -- 数据序列号集合
    输出参数： NULL
    返回值： NULL
    功能： 对数据序号集合转换为脉冲数据集合（转换结果存放在m_lSelectedData中）
    *************************************************************/
    void covertIndexsToRawData(const QListInt &data);

    /*************************************************
    输入参数: data -- 数据序列号集合
    输出参数： NULL
    返回值： NULL
    功能： 对数据序号集合转换为脉冲数据集合（转换结果存放在m_lSelectedData中）
    *************************************************************/
    void covertIndexsToRawData(const QVector<UINT16> &data);

    /*************************************************
    输入参数: NULL
    输出参数： indexPDSS--数据序号集合
              xDataPDSS--转换后的x坐标值集合
              yDataPDSS--转换后的y坐标值集合
    返回值： NULL
    功能： 将脉冲数据集合（m_lRawData）转换为对应的坐标值集合
    *************************************************************/
    void covertRawToCoord(QVector< UINT16 > &indexPDSS, QVector< double > &xDataPDSS, QVector< double > &yDataPDSS);


    /*************************************************
    函数名： setChartData
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置图谱的显示数据
    *************************************************************/
    void setChartData( void );

    /*************************************************
    函数名： initButtonBarData
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置按钮需要显示的数据
    *************************************************************/
    void setButtonBarData( ClusterAnalysisMode eMode );

    /*************************************************
    函数名： updateButtonBarData
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置按钮需要显示的数据
    *************************************************************/
    void updateButtonBarData(ClusterAnalysisMode eMode );

    /*************************************************
    函数名： clearAnalysisGraphical
    输入参数: eMode -- 手动/自动
    输出参数： NULL
    返回值： NULL
    功能： 清除分析图形
    *************************************************************/
    void clearAnalysisGraphical( ClusterAnalysisMode eMode );

    /*************************************************
    输入参数: NULL
    输出参数： bool -- 可以进行为true，否则为false
    返回值： NULL
    功能： 检查是否可以进行手动分类操作
    *************************************************************/
    bool checkManualAction();

    void changeButtonBarBtns(ClusterAnalysisMode eMode);

    void cancelSelectColorCluser();

    void setDiagnosisBtnStatusByColor(CAViewConfig::ClusterColor eColorCluster);

    void diagnosisDatas();

    void calculateAxisScale(double &dXMinScale, double &dXMaxScale, double &dYMinScale, double &dYMaxScale);

    void updateBtnBarBtns(ClusterAnalysisMode eMode);

private:
    QList<CA::PulseData> m_lRawData;//存放从点击采样开始所有的脉冲数据
    QList<CA::PulseData> m_lSelectedData;//被选中分析的脉冲数据
    QList< QListInt > m_lAutoCluster;//自动分类的结果数据
    QVector< QColor > m_vecColor;//自动分类的颜色集合

    FastWavePDSS    *m_pFastWavePDSS;//PDSS图谱
    ClusterAnalysisMode m_eCurrentAnalysisMode;//当前的聚类分析模式
    RadioPushButton *m_pAutoAnalysisButton;//类别选择按钮
    ControlButton *m_pDiagnosisBtn;
    CAViewConfig::ClusterColor m_eClusterColor;

    //UINT16 m_usCurrentSize;
    CA::SampleRate m_eSampleRate;//采样率
    INT32 m_iCurveSize;//初始状态下图元的个数
    UINT16 m_usAutoClusterIndex;//自动分类模式下当前选中的类别

    bool m_isEnableDraw;

    double m_dPulseWidth;
    qint32 m_iRangeMax;

    float m_fDiagnosisYRange;

    double m_dYMinAxisScale;
    double m_dXMinAxisScale;
    double m_dYMaxAxisScale;
    double m_dXMaxAxisScale;
};

#endif // PULSEANALYSISVIEW_H
