﻿#ifndef CAMATCHVIEW_H
#define CAMATCHVIEW_H

#include <QObject>
#include <QWidget>
#include <QStackedLayout>
#include "View.h"

class TitleBar;
class QListView;
class QStandardItemModel;
class LoadingView;
class QModelIndex;
class CAClientsManager;
class QHBoxLayout;
class QLabel;
class QPushButton;
class CAMatchView : public QWidget
{
    Q_OBJECT
public:
    explicit CAMatchView(QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~CAMatchView( );

protected:
    /*************************************************
    功能： 事件处理的函数
    输入参数：
            event -- 事件对象
    *************************************************************/
    bool event ( QEvent * event );

    bool eventFilter(QObject *obj, QEvent *event);

    /*************************************************
    函数名： timerEvent(QTimerEvent *pEvent)
    输入参数:
    输出参数：
    返回值：
    功能： 定时器处理函数
    *************************************************************/
    void timerEvent(QTimerEvent *pEvent);

private slots:

    void onStartUpdateFW();

    /*************************************************
    函数名： connectSelectedConditioner
    输入参数:
        index：item在model中的下标
    输出参数： NULL
    返回值： NULL
    功能： 当某个设备ID被点击后，根据设备的状态确认是否连接，并给出提示信息
    *************************************************************/
    void connectSelectedConditioner(const QModelIndex& index);

    /*************************************************
    函数名： refreshConditionerList
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 断开当前连接的设备
    *************************************************************/
    void disconnectCurrentDevice();

    void onDeviceDisconnected(QString strMac);

    void onDeviceConneted(QString strMac);

    void onRefreshBtnClicked();

private:
    /*************************************************
    函数名： createCurrentIDLayout()
    输入参数:qsID:上次已连接的调理器ID
    输出参数： NULL
    返回值： QHBoxLayout
    功能： 创建显示上次已连接调理器ID的Layout
    *************************************************************/
    QHBoxLayout *createCurrentIDLayout();

    /*************************************************
    函数名： createDisconnectLayout()
    返回值： QHBoxLayout
    功能： 创建含有断开连接按钮的Layout
    *************************************************************/
    QHBoxLayout *createDisconnectLayout();

    /************************************************
     * 函数名   : adjustFontSize
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置字体大小
     ************************************************/
    void adjustFontSize(void);

    /*************************************************
    函数名： createSubTitleLayout(WLDeviceType eDeviceType)
    输入参数:NULL
    输出参数： NULL
    返回值： 子标题栏对象：调理器名称+刷新按钮
    功能： 根据调理器类型创建子标题栏
    *************************************************************/
    QHBoxLayout *createSubTitleLayout();

    /*************************************************
    函数名： setDeviceModel
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 显示设备列表信息
    *************************************************************/
    void setDeviceModel(const QStringList &listDeviceMac);


    void updateConnectState();

    /************************************************
     * 函数名   : activeNextWidget
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 将焦点往后移动
     ************************************************/
    void activeNextWidget( void );

    /************************************************
     * 函数名   : activePreviousWidget
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 将焦点往前移动
     ************************************************/
    void activePreviousWidget( void );

    void autoOpenHotSpot();

    /*************************************************
    函数名： refreshConditionerList
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 重新搜索调理器，刷新列表
    *************************************************************/
    bool refreshConditionerList(bool bStateChanged = false);

    /*询问是否要更新HAS02的固件*/
    void queryToUpdateG100WFW();

    /*更新HAS02(G100W)固件*/
    void updateG100WFW();

    void processUpdateG100WResult();

    /*************************************************
    函数名： thread_save_data
    输入参数: pstDataInfo---写到xml文件的数据
    输出参数：NULL
    返回值： NULL
    功能： 子线程函数,写数据到xml文件
    *************************************************************/
    void threadUpdateG100WFW(CAMatchView *pInstance);

private:
    enum {
        QUERY_CONDITIONER_INTERVAL = 2000,
		SACN_CONDITIONER_FAILED_COUNT = 30,
    };

    struct UpdateG100WFWEvent : public QEvent
    {
        enum {EventId = QEvent::User + View::EVENT_UPDATE_G100W_FW};

        explicit UpdateG100WFWEvent(bool saved_, const QString &message_)

            : QEvent(static_cast<Type>(EventId)),

              saved(saved_), message(message_) {}

        const bool saved;

        const QString message;

    };

    int m_iQueryTimerId;
	int m_iFaileCount;                            //调理器匹配失败次数                        
	
    QLabel *m_pTitleLabel;                        // 显示标题Label
    QLabel *m_pCurrentIdLabel;                    // 显示当前设备Id的Label
    QPushButton *m_pRefreshButton;//刷新按钮
    TitleBar *m_pTitleBar;//titlebar对象
    QListView *m_pView;                           // 显示设备列表的view
    QStandardItemModel *m_pModel;                 // 显示设备列表的model

    CAClientsManager *m_pCAClientsManager;     //CA的客户端管理类
    QPushButton *m_pDisconnectButton;
    QVector<QWidget*>   m_vAllin;       // 各子窗体的结合，用来完成焦点的切换
    LoadingView *m_pLoadingWidget;
    QStackedLayout* m_pStackedLayout;

    QStringList m_listDeviceMac;
    bool m_bRefreshing;

    QFuture<void> m_UpdateG100WFWFuture;

    bool m_isSuccessfullyUpdate;

    LoadingView *m_pUpdateFWLoading;
};
#endif // CAMATCHVIEW_H
