#ifndef PRPSUNIONVIEW_H
#define PRPSUNIONVIEW_H

/*
* Copyright (c) 2015.11，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: PrpsUnionView.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年4月7日
* 摘要：该文件主要定义了uhf、hfct prps和prpd整合的界面，主要完成的功能是添加数据和数据推动的业务逻辑

* 当前版本：1.0
*/

#include "phaseabstractview.h"
#include "prps/prpsglobal.h"
#include "Widget.h"

class WIDGET_EXPORT PrpsUnionView : public PhaseAbstractView
{
    Q_OBJECT
public:
    /************************************************
     * 功能: 构造函数
     * 入参：parent -- 父控件指针
     *     iPeriodCount -- 周期个数
     *     iPhaseCount -- 每周期相位个数
     *     dMax -- 最大值
     *     dMin -- 最小值
     ************************************************/
    explicit PrpsUnionView( int iDisplayedPeriodCnt, qint32 iPeriodCount, qint32 iPhaseCount,
                            double dMax,double dMin,QWidget *parent = 0 );

    /****************************
    输入参数:rawData:单周期原始数据
    功能： 添加数据
    业务逻辑：
        图谱推动的逻辑为没添加一次数据推动一周期数据
    *****************************/
    void setData( const QVector< double > &rawData );


    void setAdvanceStep( quint8 ucStep);


private:
    quint8 m_ucStep;
};

#endif // PRPSUNIONVIEW_H
