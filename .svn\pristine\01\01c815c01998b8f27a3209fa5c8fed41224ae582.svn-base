/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* ChartView.cpp
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月7日
* 修改日期：2016年11月14日
*       重构
* 摘要：图表视图基类实现

* 当前版本：1.0
*/

#include <QApplication>
#include <QDir>
#include <QDateTime>
#include <QVBoxLayout>
#include "ChartView.h"
#include "pushButton/CmdPushButton.h"
#include "pushButton/RadioPushButton.h"
#include "pushButton/SliderPushButton.h"
#include "labelButton/CmdLabelButton.h"
#include "labelButton/RadioLabelButton.h"
#include "labelButton/SliderLabelButton.h"
#include "loadingView/loadingdialog.h"

//一些宏定义
#define TITLEBAR_HEIGHT 100 //标题栏高度

struct ChartViewPrivate
{
    TitleBar* pTitleBar;//标题栏
    ChartWidget* pChart;//图表
    PushButtonBar* pButtonBar;//控制按钮栏
    QVBoxLayout* playoutChart;//图表布局
    QVBoxLayout* playoutButton;//按钮布局
    char* pchContext;//国际化用的域
};

/****************************
功能： 构造函数
输入参数:
    parent -- 父窗体
*****************************/
ChartView::ChartView(const QString& strTitle, QWidget *parent)
    : Widget(parent)
{
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute( Qt::WA_DeleteOnClose );

    //私有变量
    d = new struct ChartViewPrivate;
    d->pTitleBar = NULL;

    //标题栏
    if(strTitle != "" && !strTitle.isEmpty())
    {
        d->pTitleBar = new TitleBar( strTitle, this );
        d->pTitleBar->setFixedHeight( TITLEBAR_HEIGHT );
        connectTitleClicked();
    }

    //预留layout，等待后续setChart,setButtonbar等时使用
    d->playoutChart = new QVBoxLayout();
    d->playoutButton = new QVBoxLayout();

    d->pChart = NULL;
    d->pButtonBar = NULL;

    QVBoxLayout* pmainLayout = new QVBoxLayout;
    pmainLayout->setSpacing(0);
    pmainLayout->setMargin(0);
    if(NULL != d->pTitleBar)
    {
        pmainLayout->addWidget( d->pTitleBar, 0, Qt::AlignTop );
    }
    pmainLayout->addLayout( d->playoutChart, 1 );
    pmainLayout->addLayout( d->playoutButton, 0 );

    setLayout( pmainLayout );
}

/*************************************************
功能： 槽，为了保证点击标题栏关闭界面时，能够收起所有弹出按键
*************************************************************/
void ChartView::onClose( void )
{
    closeAllButtons();
    close();
    return;
}

/*************************************************
函数名： ~ChartView
输入参数: NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
ChartView::~ChartView()
{
#ifdef OLD_VERSION
    Config *pConfig = Config::getAutoSaveSettingInstance();
    APP_CHECK_RETURN(pConfig);
    pConfig->flush();
#endif

    if( NULL != d )
    {
        delete d;
        d = NULL;
    }
}

/*************************************************
函数名： setTitle
输入参数:
    strTitle:标题
输出参数： NULL
返回值： NULL
功能： 设置标题
*************************************************************/
void ChartView::setTitle(const QString& strTitle)
{
    // 标题栏
    if(strTitle != "" && !strTitle.isEmpty())
    {
        if (NULL == d->pTitleBar)
        {
            d->pTitleBar = new TitleBar(strTitle, this);
            d->pTitleBar->setFixedHeight(TITLEBAR_HEIGHT);
            connectTitleClicked();

            QVBoxLayout* pMainLayout = qobject_cast<QVBoxLayout*>(layout());
            if(NULL != pMainLayout)
            {
                pMainLayout->insertWidget(0, d->pTitleBar, 0, Qt::AlignTop);
            }
        }
        else
        {
            d->pTitleBar->setTitle(strTitle);
        }
    }
}

/****************************
功能： 获取标题
返回值： 标题
*****************************/
QString ChartView::title()
{
    return d->pTitleBar->title();
}

/*************************************************
函数名： titleBar
输入参数: NULL
输出参数： NULL
返回值： NULL
功能： 获取标题栏
*************************************************************/
TitleBar* ChartView::titleBar( void )
{
    return d->pTitleBar;
}

/****************************
功能： 连接标题栏点击信息
*****************************/
void ChartView::connectTitleClicked()
{
    connect(d->pTitleBar, SIGNAL(sigClicked()), this, SLOT(onClose()));
    return;
}

/****************************
功能： 断开连接标题栏点击信息
*****************************/
void ChartView::disconnectTitleClicked()
{
    disconnect(d->pTitleBar, SIGNAL(sigClicked()), this, SLOT(onClose()));
    return;
}

/*************************************************
函数名： setCharts
输入参数:
    pChart:图表
输出参数： NULL
返回值： NULL
功能： 设置图表
*************************************************************/
void ChartView::setChart( ChartWidget *pChart )
{
    //移除原有chart
    if( NULL != d->pChart )
    {
        d->playoutChart->removeWidget( d->pChart );
        delete d->pChart;
    }
    d->pChart = pChart;
    d->playoutChart->addWidget( d->pChart, 0, Qt::AlignCenter );

    //点击图谱，取消按钮选中状态
    if( (NULL != d->pChart) && (NULL != d->pButtonBar ) )
    {
        connect(d->pChart, SIGNAL(sigPressed()), d->pButtonBar, SLOT(disActive()));
    }
}
/*************************************************
函数名： chart
输入参数: NULL
输出参数： NULL
返回值： NULL
功能： 获取图谱控件
*************************************************************/
ChartWidget* ChartView::chart( void )
{
    return d->pChart;
}

/*************************************************
功能： 获取控制按钮栏
返回值： 控制按钮栏
*************************************************************/
PushButtonBar* ChartView::buttonBar( void )
{
    return d->pButtonBar;
}

/*************************************************
功能： 截屏
返回值：
        截屏的图谱
*************************************************************/
QPixmap ChartView::screenShot()const
{
    QPixmap pixmap;
    if( NULL != d->pChart )
    {
        //截屏
        QRect rect = d->pChart->rect();
        //        QRect screenRect = QRect( mapToParent( rect .topLeft()),QSize( rect.width(),rect.height() ));

        pixmap = QPixmap::grabWidget( d->pChart, rect );
    }
    else
    {
        qWarning() << "ChartView::screenShot: error, chart is null!";
    }

    return pixmap;
}

/*************************************************
功能： 创建控制按钮栏
输入参数:
    pchContext -- 国际化用的域
    pInfos -- 按钮配置信息
    iCount -- 按钮个数
    iColumnCount -- 列数
    mode -- 显示模式
返回值：创建的ButtonBar
*************************************************************/
PushButtonBar* ChartView::createButtonBar( const char* const pchContext, const ButtonInfo::Info* pInfos, int iCount, int iColumnCount, PushButton::Mode mode )
{
    if( ( NULL == pInfos ) || ( iCount <= 0 ) )
    {
        qWarning() << "ChartView::setButtonBar: error, input invalid!";
        return NULL;
    }
    d->pchContext = (char*)pchContext;

    //新建ButtonBar并且添加到布局中
    if( NULL != d->pButtonBar )
    {
        d->playoutButton->removeWidget( d->pButtonBar );
        delete d->pButtonBar;
    }
    d->pButtonBar = new PushButtonBar( iColumnCount );
    d->playoutButton->addWidget( d->pButtonBar, 0, Qt::AlignBottom  );

    //信号、槽连接：点击图谱，取消按钮选中状态
    if( (NULL != d->pChart) && (NULL != d->pButtonBar ) )
    {
        connect(d->pChart, SIGNAL(sigPressed()), d->pButtonBar, SLOT(disActive()));
        connect( this, SIGNAL(sigPressed()), d->pButtonBar, SLOT(disActive()) );
    }


    //添加Button
    for(int i = 0; i < iCount; ++i)
    {
        ControlButton* pButton = NULL;
        switch ( pInfos[i].config.eType )
        {
        case ButtonInfo::COMMAND://命令类型
        {
            pButton = new CmdPushButton( mode );
            pButton->setTitle(  qApp->translate( pchContext, pInfos[i].config.pchName ) );

            d->pButtonBar->addButton( pButton );
            connect( (CmdPushButton*)pButton, SIGNAL(sigPressed(int)), this, SLOT(onCommandButtonPressed(int) ) );
        }
            break;
        case ButtonInfo::RADIO://Radio类型
        {
            ButtonInfo::RadioValueConfig* pConfig = (ButtonInfo::RadioValueConfig*)pInfos[i].config.pValueConfig;
            if( NULL != pConfig )
            {
                pButton = new RadioPushButton( qApp->translate( pchContext, pInfos[i].config.pchName ) );
                ((RadioPushButton*)pButton)->setOptionList( pchContext, pConfig->pstrValues, pConfig->iCount );
                ((RadioPushButton*)pButton)->setMode( mode );

                d->pButtonBar->addButton( pButton );
                connect( (RadioPushButton*)pButton, SIGNAL(sigValueChanged(int,int)), this, SLOT(onButtonValueChanged(int,int) ) );
            }
            else
            {
                qWarning() << "ChartView::createButtonBar:error, NULL valueConfig!";
            }
        }
            break;
        case ButtonInfo::FIXED_STEP_SLIDER:
        {
            ButtonInfo::SliderValueConfig* pConfig = (ButtonInfo::SliderValueConfig*)pInfos[i].config.pValueConfig;
            if( NULL != pConfig )
            {
                pButton = new SliderPushButton( qApp->translate( pchContext, pInfos[i].config.pchName ),
                                                pConfig->iMin,
                                                pConfig->iMax,
                                                pConfig->iStep,
                                                qApp->translate( pchContext, pInfos[i].config.pchUnit ) );
                ((RadioPushButton*)pButton)->setMode( mode );

                d->pButtonBar->addButton( pButton );
                connect( (SliderPushButton*)pButton, SIGNAL(sigValueChanged(int,int)), this, SLOT(onButtonValueChanged(int,int) ) );
            }
            else
            {
                qWarning() << "ChartView::createButtonBar:error, NULL valueConfig!";
            }
        }
            break;
        case ButtonInfo::FIXED_GROUP_SLIDER://Slider类型
        {
            ButtonInfo::GroupSliderValueConfig* pConfig = (ButtonInfo::GroupSliderValueConfig*)pInfos[i].config.pValueConfig;
            if( NULL != pConfig )
            {
                pButton = new SliderPushButton( qApp->translate( pchContext, pInfos[i].config.pchName ),
                                                pConfig->piValues,
                                                pConfig->iCount,
                                                qApp->translate( pchContext, pInfos[i].config.pchUnit ) );
                ((SliderPushButton*)pButton)->setMode( mode );

                d->pButtonBar->addButton( pButton );
                connect( (SliderPushButton*)pButton, SIGNAL(sigValueChanged(int,int)), this, SLOT(onButtonValueChanged(int,int) ) );
            }
            else
            {
                qWarning() << "ChartView::createButtonBar:error, NULL valueConfig!";
            }
        }
            break;
        default:
            qWarning() << "ChartView::createButtonBar: error, invalid button type " << pInfos[i].config.eType;
            break;
        }
        if( NULL != pButton )
        {
            pButton->setID( pInfos[i].id );
        }
    }

    return d->pButtonBar;
}

/*************************************************
功能： 更新控制按钮栏
输入参数:
    pchContext -- 国际化用的域
    pInfos -- 按钮配置信息
    iCount -- 按钮个数
    iColumnCount -- 列数
    mode -- 显示模式
返回值：更新后的ButtonBar
*************************************************************/
void ChartView::updateButtonBar( const char* const pchContext, const ButtonInfo::Info* pInfos, int iCount, int iColumnCount, PushButton::Mode mode )
{
    Q_UNUSED(iColumnCount)
    if( NULL == d->pButtonBar )
    {
        return;
    }
    d->pchContext = (char*)pchContext;


    //添加Button
    for( int i=0; i<iCount; i++ )
    {
        ControlButton* pButton = NULL;
        switch ( pInfos[i].config.eType )
        {
        case ButtonInfo::COMMAND://命令类型
        {
            pButton = new CmdPushButton( mode );
            pButton->setTitle(  qApp->translate( pchContext, pInfos[i].config.pchName ) );

            d->pButtonBar->addButton( pButton );
            connect( (CmdPushButton*)pButton, SIGNAL(sigPressed(int)), this, SLOT(onCommandButtonPressed(int) ) );
        }
            break;
        case ButtonInfo::RADIO://Radio类型
        {
            ButtonInfo::RadioValueConfig* pConfig = (ButtonInfo::RadioValueConfig*)pInfos[i].config.pValueConfig;
            if( NULL != pConfig )
            {
                pButton = new RadioPushButton( qApp->translate( pchContext, pInfos[i].config.pchName ) );
                ((RadioPushButton*)pButton)->setOptionList( pchContext, pConfig->pstrValues, pConfig->iCount );
                ((RadioPushButton*)pButton)->setMode( mode );

                d->pButtonBar->addButton( pButton );
                connect( (RadioPushButton*)pButton, SIGNAL(sigValueChanged(int,int)), this, SLOT(onButtonValueChanged(int,int) ) );
            }
            else
            {
                qWarning() << "ChartView::updateButtonBar:error, NULL valueConfig!";
            }
        }
            break;
        case ButtonInfo::FIXED_STEP_SLIDER:
        {
            ButtonInfo::SliderValueConfig* pConfig = (ButtonInfo::SliderValueConfig*)pInfos[i].config.pValueConfig;
            if( NULL != pConfig )
            {
                pButton = new SliderPushButton( qApp->translate( pchContext, pInfos[i].config.pchName ),
                                                pConfig->iMin,
                                                pConfig->iMax,
                                                pConfig->iStep,
                                                qApp->translate( pchContext, pInfos[i].config.pchUnit ) );
                ((RadioPushButton*)pButton)->setMode( mode );

                d->pButtonBar->addButton( pButton );
                connect( (SliderPushButton*)pButton, SIGNAL(sigValueChanged(int,int)), this, SLOT(onButtonValueChanged(int,int) ) );
            }
            else
            {
                qWarning() << "ChartView::updateButtonBar:error, NULL valueConfig!";
            }
        }
            break;
        case ButtonInfo::FIXED_GROUP_SLIDER://Slider类型
        {
            ButtonInfo::GroupSliderValueConfig* pConfig = (ButtonInfo::GroupSliderValueConfig*)pInfos[i].config.pValueConfig;
            if( NULL != pConfig )
            {
                pButton = new SliderPushButton( qApp->translate( pchContext, pInfos[i].config.pchName ),
                                                pConfig->piValues,
                                                pConfig->iCount,
                                                qApp->translate( pchContext, pInfos[i].config.pchUnit ) );
                ((SliderPushButton*)pButton)->setMode( mode );

                d->pButtonBar->addButton( pButton );
                connect( (SliderPushButton*)pButton, SIGNAL(sigValueChanged(int,int)), this, SLOT(onButtonValueChanged(int,int) ) );
            }
            else
            {
                qWarning() << "ChartView::updateButtonBar:error, NULL valueConfig!";
            }
        }
            break;
        default:
            qWarning() << "ChartView::updateButtonBar: error, invalid button type " << pInfos[i].config.eType;
            break;
        }
        if( NULL != pButton )
        {
            pButton->setID( pInfos[i].id );
        }
    }
    return;
}

/*************************************************
功能： 创建控更多信息按钮栏
输入参数:
    pchContext -- 国际化用的域
    pInfos -- 按钮配置信息
    iCount -- 按钮个数
    qstrTitile -- 更多界面标题名称，为空时是More...
返回值：创建的ButtonBar
*************************************************************/
LabelButtonBar* ChartView::createLabelButtonBar(const char* const pchContext, const ButtonInfo::Info* pInfos, int iCount, QString qstrTitile)
{
    if( ( NULL == pInfos ) || ( iCount <= 0 ) )
    {
        qWarning() << "ChartView::createMoreConfigBar: error, input invalid " << pInfos << iCount;
        return NULL;
    }

    qstrTitile = qstrTitile.isEmpty() ? QObject::trUtf8("More...") : qstrTitile;
    LabelButtonBar* pButtonBar = new LabelButtonBar(qstrTitile);

    //添加Button
    for( int i=0; i<iCount; i++ )
    {
        ControlButton* pButton = NULL;
        switch ( pInfos[i].config.eType )
        {
        case ButtonInfo::COMMAND://命令类型
        {
            pButton = new CmdLabelButton(pButtonBar);
            pButton->setTitle(  qApp->translate( pchContext, pInfos[i].config.pchName ) );
            pButtonBar->addButton( pButton );
            connect( (CmdLabelButton*)pButton, SIGNAL(sigPressed(int)), this, SLOT(onCommandButtonPressed(int) ) );
        }
            break;
        case ButtonInfo::RADIO://Radio类型
        {
            ButtonInfo::RadioValueConfig* pConfig = (ButtonInfo::RadioValueConfig*)pInfos[i].config.pValueConfig;
            if( NULL != pConfig )
            {
                pButton = new RadioLabelButton( qApp->translate( pchContext, pInfos[i].config.pchName ), QStringList(), pButtonBar);
                ((RadioLabelButton*)pButton)->setOptionList( pchContext, pConfig->pstrValues, pConfig->iCount );
                pButtonBar->addButton( pButton );
                connect( (RadioPushButton*)pButton, SIGNAL(sigValueChanged(int,int)), this, SLOT(onButtonValueChanged(int,int) ) );
            }
            else
            {
                qWarning() << "ChartView::createMoreConfigBar:error, NULL valueConfig!";
            }
        }
            break;
        case ButtonInfo::FIXED_STEP_SLIDER:
        {
            ButtonInfo::SliderValueConfig* pConfig = (ButtonInfo::SliderValueConfig*)pInfos[i].config.pValueConfig;
            if( NULL != pConfig )
            {
                pButton = new SliderLabelButton( qApp->translate( pchContext, pInfos[i].config.pchName ),
                                                 pConfig->iMin,
                                                 pConfig->iMax,
                                                 pConfig->iStep,
                                                 qApp->translate( pchContext, pInfos[i].config.pchUnit ),
                                                 pButtonBar);
                pButtonBar->addButton( pButton );
                connect( (SliderLabelButton*)pButton, SIGNAL(sigValueChanged(int,int)), this, SLOT(onButtonValueChanged(int,int) ) );
            }
            else
            {
                qWarning() << "ChartView::createMoreConfigBar:error, NULL valueConfig!";
            }
        }
            break;
        case ButtonInfo::FIXED_GROUP_SLIDER://Slider类型
        {
            ButtonInfo::GroupSliderValueConfig* pConfig = (ButtonInfo::GroupSliderValueConfig*)pInfos[i].config.pValueConfig;
            if( NULL != pConfig )
            {
                pButton = new SliderLabelButton( qApp->translate( pchContext, pInfos[i].config.pchName ),
                                                 pConfig->piValues,
                                                 pConfig->iCount,
                                                 qApp->translate(pchContext, pInfos[i].config.pchUnit),
                                                 pButtonBar);
                pButtonBar->addButton( pButton );
                connect( (SliderLabelButton*)pButton, SIGNAL(sigValueChanged(int,int)), this, SLOT(onButtonValueChanged(int,int) ) );
            }
            else
            {
                qWarning() << "ChartView::createMoreConfigBar:error, NULL valueConfig!";
            }
        }
            break;
        default:
            qWarning() << "ChartView::createMoreConfigBar: error, invalid button type " << pInfos[i].config.eType;
            break;
        }
        if( NULL != pButton )
        {
            pButton->setID( pInfos[i].id );
        }
    }

    return pButtonBar;
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
            id -- 按钮ID
            iValue -- 按钮值
*************************************************************/
void ChartView::onButtonValueChanged( int id, int iValue )
{
    Q_UNUSED( id );
    Q_UNUSED( iValue );
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void ChartView::onCommandButtonPressed( int id )
{
    Q_UNUSED( id );
}

/*************************************************
功能： 重载鼠标点击事件
输入参数：
        event -- 事件
*************************************************************/
void ChartView::mousePressEvent( QMouseEvent* event )
{
    if( Qt::LeftButton == event->button() )
    {
        emit sigPressed();
    }

    Widget::mousePressEvent( event );
}

/*************************************************
功能： 收起所有弹出按键
*************************************************************/
void ChartView::closeAllButtons()
{
    if(NULL != d->pButtonBar)
    {
        d->pButtonBar->disActive();
    }
    return;
}

/*************************************************
功能： 设置当前激活的按钮
*************************************************************/
void ChartView::setCurActiveBtnID(int iBtnID)
{
    if(d->pButtonBar)
    {
        d->pButtonBar->setCurActiveBtnID(iBtnID);
    }
    return;
}

/*************************************************
 输入参数： qstrMsg -- 提示信息
 输出参数： NULL
 返回值： NULL
 功能： 显示等待对话框
 *************************************************************/
void ChartView::showWaitingDialog(QString qstrMsg)
{
    LoadingDialog* pLoadingDialog = new LoadingDialog(qstrMsg, this);
    pLoadingDialog->setWindowModality(Qt::ApplicationModal);
    pLoadingDialog->setAttribute(Qt::WA_DeleteOnClose);
    connect(this, SIGNAL(sigWaitOperFinished()), pLoadingDialog, SLOT(accept()));
    pLoadingDialog->show();

    return;
}

