﻿/*
* Copyright (c) 2017.2，南京华乘电气科技有限公司
* All rights reserved.
*
* caserviceprivate.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年2月15日
* 摘要：ca pulse服务模块接口定义

* 修改版本：1.1
* 作者：曹山
* 创建日期：2018年6月5日
* 摘要：ca服务私有接口定义

* 当前版本：1.1
*/

#ifndef CASERVICEPRIVATE_H
#define CASERVICEPRIVATE_H

#include <QObject>
#include <QMutex>
#include <QTimerEvent>
#include "CA.h"
#include "datadefine.h"

class QThread;
class CAService;
class CAProtocol;
class HCAffair;

class CAServicePrivate : public QObject
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    *************************************************/
    CAServicePrivate(QObject *parent);

    /*************************************************
    功能： 析构函数
    *************************************************/
    ~CAServicePrivate();

    /************************************************
     * 函数名   : isStarted
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 查询结果  true--已启动; false--未启动
     * 功能     : 查询是否已启动
     ************************************************/
    bool isStarted(void) const;

    /*************************************************
    功能： 起动service
    输入参数:NULL
    *************************************************/
    bool start();

    /*************************************************
    功能： 停止service
    输入参数:NULL
    *************************************************/
    bool stop();

    /************************************************
     * 函数名   : hasCandidates
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 查询结果  true--有; false--无
     * 功能     : 查询是否有候选前端待连接
     ************************************************/
    bool hasCandidates(void) const;

    /************************************************
     * 函数名   : hasConnectedDevice
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 查询结果  true--有; false--无
     * 功能     : 查询是否有已连接的前端
     ************************************************/
    bool hasConnectedDevice(void) const;


    void setExpectedPulseCnt(quint32 uiCnt);

    /************************************************
     * 函数名   : connectedDevice
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 设备的MAC地址
     * 功能     : 查看已连接设备的MAC地址
     ************************************************/
    QString connectedDevice(void) const;

    /*************************************************
    功能： 启动采样,收到应答后返回true，延时到返回false
    输入参数:NULL
    *************************************************/
    bool startSample(CA::CAWorkMode eMode);

    /*************************************************
    功能： 停止采样,收到应答后返回true，延时到返回false
    输入参数:NULL
    *************************************************/
    bool stopSample();
#ifdef _SUPPORT_SET_SYS_FREQ_
    /************************************************
     * 函数名   : setSampleParam
     * 输入参数 : eSampleRate: 采样率; usMinSampleLenTimes: 最小采样长度的倍数, 最小采样长度为1024
     *          uiSyncPeriod:内同步周期，即电网频率换算的周期，单位us
     * 输出参数 : NULL
     * 返回值   : 设置结果  true--成功; false--失败
     * 功能     : 设置采样参数
     ************************************************/
    bool setSampleParam(CA::SampleRate eSampleRate, UINT32 uiSyncPeriod, UINT16 usMinSampleLenTimes = 1);
#else

    /************************************************
     * 函数名   : setSampleParam
     * 输入参数 : eSampleRate: 采样率; usMinSampleLenTimes: 最小采样长度的倍数, 最小采样长度为1024
     * 输出参数 : NULL
     * 返回值   : 设置结果  true--成功; false--失败
     * 功能     : 设置采样参数
     ************************************************/
    bool setSampleParam(CA::SampleRate eSampleRate, UINT16 usMinSampleLenTimes = 1);
#endif
    /*************************************************
    功能： 设置增益
    输入参数:eGain---增益
    *************************************************/
    bool setGain(CA::Gain eGain);

    /************************************************
     * 函数名   : setTrigParam
     * 输入参数 : stParam: 触发参数, 包括: 触发值、触发宽度、触发前百分比
     * 输出参数 : NULL
     * 返回值   : 设置结果  true--成功; false--失败
     * 功能     : 设置触发参数
     ************************************************/
    bool setTrigParam(const CA::TrigParam stParam);

    /************************************************
     * 函数名   : setFreqBand
     * 输入参数 : eFreqBand: 频带
     * 输出参数 : NULL
     * 返回值   : 设置结果  true--成功; false--失败
     * 功能     : 设置频带; 注: 当前CA调理器不支持
     ************************************************/
    bool setFreqBand(CA::FreqBand eFreqBand);

    /************************************************
     * 函数名   : setFIRCoefficient
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 设置结果  true--成功; false--失败
     * 功能     : 设置FIR滤波器系数; 注: 当前CA调理器不支持
     ************************************************/
    bool setFIRCoefficient(void);

    /*************************************************
    功能： ca校准参数写到G100
    输入参数:pstData---校准参数
    *************************************************/
    bool setCalibratedParameters(const CA::CalibratedParameter stData);

    /************************************************
     * 函数名   : calibratedParam
     * 输入参数 : eGain: 增益
     * 输出参数 : NULL
     * 返回值   : 校准参数
     * 功能     : 读取校准参数
     ************************************************/
    CA::CalibratedParameter calibratedParam(CA::Gain eGain);

    /*************************************************
    功能： 读取service保存的G100信息
    输出参数:pstG100Info---指向g100信息的指针
    *************************************************/
    CA::CAConditionerInfo CAConditionerInfo() const;

private:
    /*************************************************
    功能： 连接信号
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    void connectSignals();

    /*************************************************
    功能： 断开信号连接
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    void disconnectSignals();

private slots:

    /*************************************************
    功能： 槽，处理pulse数据
    输入参数:
        PulseData -- pulse数据
    *************************************************/
    void onPulseData(CA::DataHeadInfo stHead, QList<CA::PulseData> vecData);

    /*************************************************
    功能： 槽，处理wave数据
    输入参数:
        vecXData -- wave view需要的x坐标数据
        vecYData -- wave view需要的y坐标数据
    *************************************************/
    void onWaveData(CA::DataHeadInfo stHead, QList<CA::WaveData> listWaveData);

    /*************************************************
    功能： 槽，处理prps数据
    输入参数:
        vecData -- prps view需要的百分比数据
    *************************************************/
    void onPRPSData(CA::DataHeadInfo stHead, QVector<double> vecData);

    /*************************************************
    功能： 槽，处理校准用AD数据
    输入参数:
        vecADData -- 校准用AD数据
    *************************************************/
    void onCalibrateADData(CA::DataHeadInfo stHead, QVector<UINT16> vecADData);

    /*************************************************
    功能： 槽，处理校准无效信息
    输入参数:
    *************************************************/
    void onInvalidCalibrateCo();

    void onNoCalibrateCo();

    /*************************************************
    功能： 槽，处理通讯建立
    输入参数:NULL
    *************************************************/
    void onConnected(QString strMacAddr);

    /*************************************************
    功能： 槽，处理通讯断开
    输入参数:NULL
    *************************************************/
    void onDisconnected(QString strMacAddr);

    /*************************************************
    功能： 槽，同步事件处理完毕
    输入参数:
        eAffair -- 同步事件枚举
    *************************************************/
    void onAffairDone( CA::CAAffair eAffair  );

    /*************************************************
    功能： 槽，同步事件处理完毕
    输入参数:
        usAffair -- 同步事件
        usID---事件ID
    输出参数:
        pInfo---事件处理结果的数据
    *************************************************/
    void onAffair( quint16 usAffair, void* pInfo, quint16 usID  );

    /*************************************************
    功能： 槽，处理增益状态信息
    输入参数:eState ---增益状态
    *************************************************/
    void onGainState(CA::GainState eState);

private://成员
    CAService* p;//parent事务

    CAProtocol *m_pCAProtocol;//CA protocol对象

    HCAffair *m_pCAAffair;//事务对象

    QThread *m_pThread;//子线程对象

    bool m_bStarted; //已开启服务

    enum {
        SYNC_AFFAIR_TIME_DELAY = 5000//同步事务延时返回的时间间隔
    };

};

#endif // CASERVICEPRIVATE_H
