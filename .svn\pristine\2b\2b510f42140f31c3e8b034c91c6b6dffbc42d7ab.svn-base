﻿#include "taskmanager.h"
#include <QFileInfo>
#include <QDateTime>
#include <QSettings>
#include "JlCompress.h"
#include "dataSave/xmldocument.h"
#include "systemsetting/systemsetservice.h"
#include "appcomm/qjason/qjson.h"
#include "fileoper/fileoperutil.h"
#include "current/currentservice.h"
#include "datadefine.h"
#include "Module.h"
#include "subtask.h"
#include "taskio.h"
#include "global_log.h"
#include "gnss/gnssservice.h"

#ifdef Q_PROCESSOR_ARM
#include <unistd.h>
#endif

const QString GrpTestState = "testStateGroup";
const QString KeyIsTested = "isTested";
const QString KeyIsFinished = "isFinished";
const QString KeyBGNIsTested = "isBGNTested";
const QString KeyBGNFileName = "BGNFileName";
QMutex TaskManager::m_mutex(QMutex::Recursive);
QThread* TaskManager::m_pThread = NULL;
/****************************
功能： 本地通讯模块单例
*****************************/
TaskManager* TaskManager::instance()
{
    static TaskManager manager;
    return &manager;
}

TaskManager::TaskManager(QObject *parent)
    : QObject(parent)
    , m_qstrMainTaskId("")
    , m_qstrSubTaskId("")
    , m_qstrAssetId("")
    , m_bJSONTesting(false)
    , m_qstrPhaseType("")
{
    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        m_pCurTaskInfo = NULL;
        m_qmt4CurTaskInfo.unlock();
    }

    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        m_pCurCircuitInfo = NULL;
        m_qmt4CurCircuitInfo.unlock();
    }

    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        m_pCurAssetInfo = NULL;
        m_qmt4CurAssetInfo.unlock();
    }

    m_pThread = new QThread(this);
    this->moveToThread(m_pThread);
    m_pThread->start();
    qRegisterMetaType<QVector<CustomAccessTaskNS::MainTaskInfo> >("QVector<CustomAccessTaskNS::MainTaskInfo>");

    connect(SystemSetService::instance(), SIGNAL(sigStorageFormatted()), this, SLOT(onStorageFormatted()));
    connect(this, SIGNAL(sigReadCustomaccessTaskInfos()), this, SLOT(onReadCustomaccessTaskInfos()));
    createTaskDir();
    //TODO scanMainTask
    //mainTasks();
}

TaskManager::~TaskManager()
{

}

/************************************
 * 功能：创建任务文件目录
 * ***********************************/
bool TaskManager::createTaskDir()
{
    return FileOperUtil::createDirectory(CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH);
}

/*************************************************
函数名：
输入参数： NULL
输出参数： NULL
返回值： 本地主任务信息列表
功能： 获取本地主任务列表
*************************************************************/
void TaskManager::readMainTasks()
{
    emit sigReadCustomaccessTaskInfos();
    return;
}

//获取本地主任务列表
QVector<CustomAccessTaskNS::MainTaskInfo> TaskManager::mainTasks()
{
    logInfo("scan main task infos");
	QMutexLocker locker(&m_qmtReadTaskInfos);

    SystemSet::CustomAccessMode eMode = SystemSetService::instance()->getCustomAccessMode();
    if(SystemSet::ACCESS_USB_MODE == eMode)
    {
        m_vMainTask.clear();
        if(!(SystemSetService::instance()->storageOperEnable()))
        {
            logWarning("unable to operate storage space");
            return m_vMainTask;
        }
    }
    else
    {
        m_vMainTask.clear();
        if(!m_vMainTask.isEmpty())
        {
            return m_vMainTask;
        }
    }
    m_vMainTask.clear();
    FileOperUtil::refreshToSystemDisk();

    QString strMainTaskRootPath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH;
    QDir dir(strMainTaskRootPath);
    if(!dir.exists())
    {
        logError("root path not exist...");
        return m_vMainTask;
    }

    //获取主任务的文件夹路径
    QVector<QString> vecMainTaskPath;
    mainTasksPath(strMainTaskRootPath, vecMainTaskPath);

    //遍历各个主任务下的子任务文件，并解析任务信息
    m_tasksMap.clear();
    QVector<QString> vecSubTaskPath;
    for(int i = 0, iSize = vecMainTaskPath.size(); i < iSize; ++i)
    {
        subTasksPath(vecMainTaskPath.at(i), vecSubTaskPath);
        if(vecSubTaskPath.size() > 0)
        {
            QVector<QString> vecTaskFilePath;
            tasksFilePath(vecSubTaskPath, vecTaskFilePath);
            if(vecTaskFilePath.size() > 0)
            {
                /*
                CustomAccessTaskNS::MainTaskInfo stMainTaskInfo;
                CustomAccessTaskNS::SubTaskInfo stSubTaskInfo;
                if(parseTaskFile(vecTaskFilePath[0], stMainTaskInfo, stSubTaskInfo))
                {
                    m_vMainTask.append(stMainTaskInfo);
                }
                */

                CustomAccessTaskNS::MainTaskInfo stMainTaskInfo;
                for( int i = 0, iSize = vecTaskFilePath.size(); i < iSize; ++i )
                {
                    CustomAccessTaskNS::SubTaskInfo stSubTaskInfo;

                    if(parseTaskFile(vecTaskFilePath.at(i), stMainTaskInfo, stSubTaskInfo))
                    {
                        //m_vMainTask.append(stMainTaskInfo);
                    }
                }
                m_vMainTask.append(stMainTaskInfo);
            }
        }
    }

    return m_vMainTask;
}

/*************************************************
函数名：
输入参数： strMainTaskRootPath -- 主任务的根路径
输出参数： vecMainTaskPath---存储主任务路径的qvector
返回值： NULL
功能： 获取所有的主任务的路径
*************************************************************/
void TaskManager::mainTasksPath(const QString &strMainTaskRootPath, QVector<QString> &vecMainTaskPath)
{
    vecMainTaskPath.clear();
    QDir dir(strMainTaskRootPath);
    dir.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden | QDir::Readable);
    foreach(QFileInfo mfi, dir.entryInfoList())
    {
        if(mfi.isDir())
        {
            if(!(mfi.fileName()=="." || mfi.fileName() == ".."))
            {
                vecMainTaskPath.append(mfi.absoluteFilePath());
            }
        }
    }

    logInfo(QString("main dir size: %1.").arg(vecMainTaskPath.size()));

    return;
}

/*************************************************
函数名：
输入参数： strMainTaskPath -- 存储主任务的路径
输出参数： vecSubTaskPath---存储子任务路径的qvector
返回值： NULL
功能： 获取主任务里的子任务路径
*************************************************************/
void TaskManager::subTasksPath(const QString& strMainTaskPath, QVector<QString> &vecSubTaskPath)
{
    vecSubTaskPath.clear();

    //遍历主任务下的子任务文件夹
    QDir dir(strMainTaskPath);
    dir.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden | QDir::Readable);
    foreach(QFileInfo mfi, dir.entryInfoList())
    {
        if(mfi.isDir())
        {
            if(!(mfi.fileName()=="." || mfi.fileName() == ".."))
            {
                vecSubTaskPath.append(mfi.absoluteFilePath());
            }
        }
    }

    logInfo(QString("sub dir size: %1.").arg(vecSubTaskPath.size()));

    return;
}

/*************************************************
函数名：
输入参数： strSubTaskPath -- 存储子任务的路径
输出参数： vecTaskFilePath---存储任务文件路径的qvector
返回值： NULL
功能： 获取子任务里的任务文件
*************************************************************/
void TaskManager::tasksFilePath(const QVector<QString> vecTaskPath, QVector<QString> &vecTaskFilePath)
{
    vecTaskFilePath.clear();

    for( int i = 0, iSize = vecTaskPath.size(); i < iSize; ++i )
    {
        QString taskPath = vecTaskPath.at(i);
        QDir dir(taskPath);
        dir.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden | QDir::Readable);
        foreach(QFileInfo mfi, dir.entryInfoList())
        {
            if(mfi.isFile() && mfi.suffix() == "xml")
            {
                vecTaskFilePath.append(taskPath + "/" + mfi.fileName());
            }
        }
    }

    logInfo(QString("task size: %1.").arg(vecTaskFilePath.size()));

    return;
}

//sub task state changed
void TaskManager::updateTaskTestStateChanged(const QString& strMainId, const QString& strSubId,
                                             CustomAccessTaskNS::SubTaskTestState eState , bool bUploadable)
{
    if( !m_tasksMap.contains(strMainId) )
    {
        return;
    }

    logInfo(QString("strMainId %1 strSubId %2").arg(strMainId).arg(strSubId));

    for( int iSub = 0, iSize = m_tasksMap.value( strMainId ).size(); iSub < iSize; ++iSub )
    {
        if( strSubId == m_tasksMap.value( strMainId ).at(iSub).s_strId )
        {
            m_tasksMap[strMainId][iSub].s_eTestState = eState;
            m_tasksMap[strMainId][iSub].s_bUploadable = bUploadable;
            break;
        }
    }

    for(int i = 0, iSize = m_vMainTask.size(); i < iSize; ++i)
    {
        if(m_vMainTask[i].s_strId == strMainId)
        {
            CustomAccessTaskNS::SubTaskTestState eStateTmp = CustomAccessTaskNS::TASK_TEST;
            for(int j = 0, iSubSize = m_tasksMap[strMainId].size(); j < iSubSize; ++j)
            {
                if(m_tasksMap[strMainId][j].s_eTestState == CustomAccessTaskNS::TASK_UNTEST)
                {
                    eStateTmp = CustomAccessTaskNS::TASK_UNTEST;
                    break;
                }
            }

            if(m_vMainTask[i].s_eTestState != eStateTmp)
            {
                m_vMainTask[i].s_eTestState = eStateTmp;
                int iState = (int)(m_vMainTask[i].s_eTestState);
                emit sigMainTaskStateChanged(m_vMainTask[i].s_strId, m_vMainTask[i].s_strName, iState);
            }

            break;
        }
    }

    return;
}

CustomAccessTaskNS::DiagnoseType TaskManager::getMainTaskDiagState( const QString& strMainId)
{
    CustomAccessTaskNS::DiagnoseType eStateTmp = CustomAccessTaskNS::DiagNormal;
    for(int i = 0, iSize = m_vMainTask.size(); i < iSize; ++i)
    {
        if(m_vMainTask[i].s_strId == strMainId)
        {

            for(int j = 0, iSubSize = m_tasksMap[strMainId].size(); j < iSubSize; ++j)
            {
                QString subTaskId = m_tasksMap[strMainId][j].s_strId; 
                SubTask * pSubTask = createSubTask(strMainId, subTaskId);
                if(pSubTask->taskDiagState() == CustomAccessTaskNS::DiagGeneral)
                {
                    eStateTmp = CustomAccessTaskNS::DiagGeneral;
                    break;
                }
            }
            break;
        }
    }

    return eStateTmp;
}

CustomAccessTaskNS::DiagnoseType TaskManager::getSubTaskDiagState( const QString& strMainId, const QString& strSubId)
{
    CustomAccessTaskNS::DiagnoseType eStateTmp = CustomAccessTaskNS::DiagNormal;
    for(int i = 0, iSize = m_vMainTask.size(); i < iSize; ++i)
    {
        if(m_vMainTask[i].s_strId == strMainId)
        {

            for(int j = 0, iSubSize = m_tasksMap[strMainId].size(); j < iSubSize; ++j)
            {
                QString subTaskId = m_tasksMap[strMainId][j].s_strId; //临时修改
                if(subTaskId == strSubId)
                {
                    SubTask * pSubTask = createSubTask(strMainId, subTaskId);
                    if(pSubTask->taskDiagState() == CustomAccessTaskNS::DiagGeneral)
                    {
                        eStateTmp = CustomAccessTaskNS::DiagGeneral;

                    }
                }
                break;
            }
            break;
        }
    }

    return eStateTmp;
}
//获取主任务下的子任务列表
QVector<CustomAccessTaskNS::SubTaskInfo> TaskManager::subTasks( const QString& strMainId )
{
    //获取子任务的信息
    QVector<CustomAccessTaskNS::SubTaskInfo> vecSubTask;
    vecSubTask.clear();

    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return vecSubTask;
    }

    FileOperUtil::refreshToSystemDisk();

    if( m_tasksMap.contains(strMainId) )
    {
        return m_tasksMap.value( strMainId );
    }

    //获取子任务的信息

    QString strMainTaskPath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + strMainId;
    QDir dir(strMainTaskPath);
    if(!dir.exists())
    {
        return vecSubTask;
    }

    QVector<QString> vecSubTaskPath;
    subTasksPath(strMainTaskPath, vecSubTaskPath);

    if(vecSubTaskPath.size() <= 0)
    {
        return vecSubTask;
    }
    QVector<QString> vecTaskFilePath;
    tasksFilePath(vecSubTaskPath, vecTaskFilePath);

    for( int i = 0, iSize = vecTaskFilePath.size(); i < iSize; ++i )
    {
        CustomAccessTaskNS::MainTaskInfo stMainTaskInfo;
        CustomAccessTaskNS::SubTaskInfo stSubTaskInfo;

        if(parseTaskFile(vecTaskFilePath.at(i), stMainTaskInfo, stSubTaskInfo))
        {
            vecSubTask.append(stSubTaskInfo);
        }
    }
    m_tasksMap[strMainId] = vecSubTask;

    return vecSubTask;
}

/*************************************************
函数名：
输入参数： strTaskFilePath -- 任务文件(含路径)
输出参数： stMainTaskInfo---主任务信息
        stSubTaskInfo---子任务信息
返回值： NULL
功能： 解析任务文件，信息写入主任务和子任务信息里
*************************************************************/
bool TaskManager::parseTaskFile(const QString& strTaskFilePath, CustomAccessTaskNS::MainTaskInfo &stMainTaskInfo, CustomAccessTaskNS::SubTaskInfo &stSubTaskInfo)
{
    FileOperUtil::refreshToSystemDisk();

    QByteArray taskData;
    QFile file(strTaskFilePath);
    QFileInfo fileInfo(strTaskFilePath);

    if (file.open(QIODevice::ReadOnly))
    {
        taskData = file.readAll();
        file.close();
    }
    else
    {
        logError(QString("open file (%1) failed.").arg(strTaskFilePath));
        return false;
    }

    if(taskData.size() <= 0)
    {
        logError(QString("read file (%1) size is 0.").arg(strTaskFilePath));
        return false;
    }

    //生成对应的保存路径，保存文件
    QDomDocument document;
    document.setContent(QString::fromUtf8(taskData.data(), taskData.size()));
    QDomElement root = document.documentElement();

    // 解析操作结果结点
    QDomElement firstChild = root.firstChildElement( CustomAccessTaskNS::TASK_NODE_MAIN_TASK );
    stMainTaskInfo.s_strId = firstChild.toElement().attribute(CustomAccessTaskNS::TASK_NODE_ID);
    stMainTaskInfo.s_strName = firstChild.toElement().attribute(CustomAccessTaskNS::TASK_NODE_NAME);
    stMainTaskInfo.s_eType = (CustomAccessTaskNS::TaskType) (firstChild.toElement().attribute(CustomAccessTaskNS::TASK_NODE_TYPE).toUInt());

    //logInfo(QString("task id: %1, path: %2.").arg(stMainTaskInfo.s_strId).arg(strTaskFilePath));

    QDomNodeList childNodesList = firstChild.childNodes();
    for(int i = 0, iSize = childNodesList.size(); i < iSize; ++i)
    {
        QDomNode childNode = childNodesList.at(i);
        QString strNodeName = childNode.nodeName();
        if(strNodeName == CustomAccessTaskNS::TASK_NODE_SUB_TASK)
        {
            stSubTaskInfo.s_strId = childNode.toElement().attribute(CustomAccessTaskNS::TASK_NODE_ID);
            stSubTaskInfo.s_strName = childNode.toElement().attribute(CustomAccessTaskNS::TASK_NODE_NAME);
            QString strState = childNode.toElement().attribute(CustomAccessTaskNS::TASK_TEST_STATE);
            QString strUploadable = childNode.toElement().attribute(CustomAccessTaskNS::TASK_UPLOADABLE);
            if( strState.isEmpty() || strUploadable.isEmpty() )
            {
                getSubTaskTestState( strTaskFilePath, stSubTaskInfo.s_eTestState, stSubTaskInfo.s_bUploadable );
            }
            else
            {
                CustomAccessTaskNS::SubTaskTestState eState = CustomAccessTaskNS::SubTaskTestState(strState.toInt());
                bool bUploadable = strUploadable.toInt() > 0;
                stSubTaskInfo.s_eTestState = eState;
                stSubTaskInfo.s_bUploadable = bUploadable;
            }
            stSubTaskInfo.s_strFileName = fileInfo.fileName();
            m_tasksMap[stMainTaskInfo.s_strId].append( stSubTaskInfo );
            break;
        }
    }

    //目前一条主任务就只有一条子任务
    if(CustomAccessTaskNS::TASK_UNTEST == stSubTaskInfo.s_eTestState)
    {
        stMainTaskInfo.s_eTestState = stSubTaskInfo.s_eTestState;
    }



    return true;
}

void TaskManager::getSubTaskTestState( const QString& strTaskFilePath, CustomAccessTaskNS::SubTaskTestState& eState, bool& bUploadable )
{
    eState = CustomAccessTaskNS::TASK_TEST;
    bUploadable = false;
    SubTask *pSubTask = createSubTask( strTaskFilePath );
    if( NULL != pSubTask )
    {
        eState = pSubTask->taskTestState();
        bUploadable = pSubTask->isTaskUploadable();
    }
    destroySubTask( pSubTask );

    return;
}

//获取指定id的子任务实例
SubTask* TaskManager::createSubTask( const QString& mainId, const QString& subId )
{
    //生成指定任务文件路径
    QString strSubTaskPath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + mainId + "/" + subId;
    QDir tmpDir( strSubTaskPath );
    QFileInfoList fileList = tmpDir.entryInfoList( QStringList( "*.xml" ) );
    if( fileList.size() > 0 )
    {
        QString subTaskFile = fileList.first().absoluteFilePath();
        return createSubTask( subTaskFile );
    }
    else
    {
        return NULL;
    }
}

//获取指定任务文件的子任务实例
SubTask* TaskManager::createSubTask( const QString& subTaskFile)
{
    SubTask *pSubTask = new SubTask(subTaskFile);

    return pSubTask;
}

//回收子任务实例
void TaskManager::destroySubTask( SubTask* pSubTask )
{
    if(pSubTask)
    {
        pSubTask->saveTaskFile();
        delete pSubTask;
        pSubTask = NULL;
    }

    return;
}

/*************************************************
函数名：
输入参数： strMainTaskID---主任务ID
        strSubTaskID -- 子任务ID
输出参数： NULL
返回值： 子任务是否存在
功能： 判断子任务是否存在
*************************************************************/
bool TaskManager::isSubTaskExist(const QString &strMainTaskID, const QString &strSubTaskID, QString &taskFile)
{
    for(int i = 0, iSize = m_vMainTask.size(); i < iSize; ++i)
    {
        if(m_vMainTask.at(i).s_strId == strMainTaskID)
        {
            QVector<CustomAccessTaskNS::SubTaskInfo> vecSubTask;
            vecSubTask = subTasks(strMainTaskID);
            for(int j = 0, iVcSize = vecSubTask.size(); j < iVcSize; ++j)
            {
                CustomAccessTaskNS::SubTaskInfo stSubTaskInfo;
                stSubTaskInfo = vecSubTask[j];
                if(stSubTaskInfo.s_strId == strSubTaskID)
                {
                    taskFile = stSubTaskInfo.s_strFileName;
                    return true;
                }
            }
        }
    }
    return false;
}

/*************************************************
函数名：
输入参数： strMainTaskID -- 主任务ID
          strSubTaskID -- 子任务ID
输出参数： strSavedPath---任务文件和数据文件的存储路径
返回值： 创建存储路径是否成功
功能： 创建任务文件和数据文件的存储路径
*************************************************************/
bool TaskManager::createFileSavedPath(const QString &strMainTaskID, const QString &strSubTaskID, QString &strSavedPath)
{
    FileOperUtil::refreshToSystemDisk();

    strSavedPath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + strMainTaskID + "/" + strSubTaskID;

    QDir dir(strSavedPath);
    bool isSuccess = false;

    if(!dir.exists())
    {
        isSuccess = dir.mkpath(strSavedPath);
        if(!isSuccess)
        {
            qDebug("TaskManager::createFileSavedPath, create saved path for sub task failed!");
        }
    }
    else
    {
        isSuccess = true;
    }
    return isSuccess;

}

/*************************************************
函数名：
输入参数： stTaskInfo -- 主任务信息
输出参数： NULL
返回值： NULL
功能： 创建并保存主任务信息
*************************************************************/
void TaskManager::createMainTask( const CustomAccessTaskNS::MainTaskInfo &stTaskInfo )
{
    //qDebug("TaskManager::createMainTask");
    CustomAccessTaskNS::MainTaskInfo stMainTaskInfo;
    stMainTaskInfo.s_strId = stTaskInfo.s_strId;
    stMainTaskInfo.s_strName = stTaskInfo.s_strName;
    stMainTaskInfo.s_eType = stTaskInfo.s_eType;
    m_vMainTask.append(stMainTaskInfo);
}

/*************************************************
函数名：
输入参数： taskContent -- 任务xml数据
         strPath ---任务文件的存储路径
输出参数： NULL
返回值： 任务xml数据写入任务文件是否成功
功能： 任务xml数据写入任务文件
*************************************************************/
bool TaskManager::writeTaskFile(const QByteArray& taskContent, QString &strPath)
{
    FileOperUtil::refreshToSystemDisk();

    bool isSuccess = false;

    QString strFileName = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");

    strPath = strPath + "/" + strFileName + CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_SUFFIX;

    QFile file(strPath);

    if(!file.open(QIODevice::WriteOnly))
    {
        qWarning("TaskManager::writeTaskFile, open task file failed!");
    }
    else
    {
        qint64 iWrotenSize = file.write(taskContent.data(), taskContent.size());
        if(iWrotenSize != taskContent.size())
        {
            qWarning("TaskManager::writeTaskFile, write task file content failed!");
        }
        else
        {
            isSuccess = true;
        }
        file.close();
    }

    FileOperUtil::refreshToSystemDisk();
    return isSuccess;
}

//保存任务文件 返回保存路径
QString TaskManager::saveTaskFile( const QByteArray& taskContent )
{
    FileOperUtil::refreshToSystemDisk();

    QString strSavedPath = "";
    //emit sigTaskChanged();
    //QMutexLocker locker(&m_qmtReadTaskInfos);
   // if(m_qmtReadTaskInfos.tryLock(1000000))
    //{
        FileOperUtil::refreshToSystemDisk();
        SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
        if((SystemSet::ACCESS_PROTO_BJRZT == eProtocol))
        {
            strSavedPath = parseJSONTaskFile(taskContent);
        }
        else if((SystemSet::ACCESS_PROTO_JSDKY == eProtocol))
        {
            QVector<QByteArray> qvecXMLData = parseJSDKYJsonTaskToXmlData(taskContent);
            for(int i = 0; i < qvecXMLData.size(); ++i)
            {
                strSavedPath = parseXMLTaskFile(qvecXMLData.at(i));
            }
        }
        else
        {
            strSavedPath = parseXMLTaskFile(taskContent);
        }

        if(!strSavedPath.isEmpty())
        {
             emit sigTaskChanged();
            FileOperUtil::refreshToSystemDisk();
        }

       // m_qmtReadTaskInfos.unlock();
//}
    return strSavedPath;
}

/*************************************************
 * 功能：解析JSON格式的任务
 * 输入参数：
 *      taskContent：任务内容
 * 输出参数：
 *      stTaskInfo：任务信息
 *      qstrlstDataFiles：任务中的所有数据文件集合，文件名
 * 返回值：
 *      bool：解析结果，true -- 成功，false -- 失败
 * *************************************************/
bool TaskManager::parseJsonTask(const QByteArray &taskContent, TaskModeViewNS::CustomTaskInfo &stTaskInfo, QStringList &qstrlstDataFiles)
{
    bool bRet = false;
    //FileOperUtil::writeFile("/media/data/test.xml", taskContent, true);
    do
    {
        QJson objTaskInfo(taskContent);
        stTaskInfo.qstrCreateTime = objTaskInfo.value(TaskModeViewNS::TASK_NODE_TASK_TIME).toString();
        stTaskInfo.qstrTestTime = objTaskInfo.value(TaskModeViewNS::TASK_NODE_TASK_TEST_TIME).toString();
        stTaskInfo.qstrId = objTaskInfo.value(TaskModeViewNS::TASK_NODE_TASK_ID).toString();
        stTaskInfo.qstrName = objTaskInfo.value(TaskModeViewNS::TASK_NODE_TASK_NAME).toString();
        stTaskInfo.dLoadCurrentVal = objTaskInfo.value(TaskModeViewNS::TASK_NODE_TASK_LOAD_CURRENT).toNumber();

        if(stTaskInfo.qstrId.isEmpty() || stTaskInfo.qstrName.isEmpty())
        {
            logError("parse task id or name is empty.");
            break;
        }

        QJson objCircuitInfos = objTaskInfo.value(TaskModeViewNS::TASK_NODE_CIRCUITINFOS);
        if(objCircuitInfos.isArray())
        {
            bool bParsedOk = true;
            for(int i = 0, iCnt = objCircuitInfos.count(); i < iCnt; ++i)
            {
                TaskModeViewNS::CircuitInfo stCircuitInfo;
                stCircuitInfo.qstrTaskFilePath = "";
                stCircuitInfo.qstrId = objCircuitInfos.at(i).value(TaskModeViewNS::TASK_NODE_CIRCUITID).toString();
                stCircuitInfo.qstrName = objCircuitInfos.at(i).value(TaskModeViewNS::TASK_NODE_CIRCUITNAME).toString();
                stCircuitInfo.iTestSN = static_cast<int>(objCircuitInfos.at(i).value(TaskModeViewNS::TASK_NODE_TESTSN).toNumber());

                if(stCircuitInfo.iTestSN != i)
                {
                    stCircuitInfo.iTestSN = i;//修正测试顺序，避免测试顺序重复
                }

                if(stCircuitInfo.qstrId.isEmpty() || stCircuitInfo.qstrName.isEmpty())
                {
                    logError(QString("parse task (%1) circuit id or name is empty.").arg(stTaskInfo.qstrId));
                    break;
                }
                else
                {
                    stCircuitInfo.iTestSN = static_cast<int>(objCircuitInfos.at(i).value(TaskModeViewNS::TASK_NODE_TESTSN).toNumber());
                    QJson objAssetInfos = objCircuitInfos.at(i).value(TaskModeViewNS::TASK_NODE_ASSETINFOS);
                    if(objAssetInfos.isArray())
                    {
                        for(int j = 0, iAsCnt = objAssetInfos.count(); j < iAsCnt; ++j)
                        {
                            TaskModeViewNS::AssetInfo stAssetInfo;
                            stAssetInfo.qstrId = objAssetInfos.at(j).value(TaskModeViewNS::TASK_NODE_ASSETID).toString();
                            stAssetInfo.qstrName = objAssetInfos.at(j).value(TaskModeViewNS::TASK_NODE_ASSETNAME).toString();
                            stAssetInfo.qstrRFIDCode = objAssetInfos.at(j).value(TaskModeViewNS::TASK_NODE_ASSETRFIDCODE).toString();
                            stAssetInfo.qstrTestTime = objAssetInfos.at(j).value(TaskModeViewNS::TASK_NODE_TASK_TEST_TIME).toString();
                            stAssetInfo.qstrTestPosition = objAssetInfos.at(j).value(TaskModeViewNS::TASK_NODE_TESTPOS).toString();
                            stAssetInfo.iTestSN = static_cast<int>(objAssetInfos.at(j).value(TaskModeViewNS::TASK_NODE_TESTSN).toNumber());
                            stAssetInfo.eType = static_cast<TaskModeViewNS::TestType>(objAssetInfos.at(j).value(TaskModeViewNS::TASK_NODE_TESTTYPE).toNumber());
                            stAssetInfo.dLoadCurrentVal = objAssetInfos.at(j).value(TaskModeViewNS::TASK_NODE_TASK_LOAD_CURRENT).toNumber();

                            QJson objPhaseTypes = objAssetInfos.at(j).value(TaskModeViewNS::TASK_NODE_PHASETYPE_INFOS);
                            if(objPhaseTypes.isArray())
                            {
                                QString qstrDbgInfo = "[";
                                for(int q = 0, iPhaseCnt = objPhaseTypes.count(); q < iPhaseCnt; ++q)
                                {
                                    if(0 != q)
                                    {
                                        qstrDbgInfo += ", ";
                                    }
                                    int iPhaseVal = static_cast<int>(objPhaseTypes.at(q).toNumber());
                                    if(!(stAssetInfo.qvtPhaseTypes.contains(iPhaseVal)))
                                    {
                                        stAssetInfo.qvtPhaseTypes.append(iPhaseVal);
                                        qstrDbgInfo += QString("%1").arg(QString::number(iPhaseVal));
                                    }
                                }
                                qstrDbgInfo += "]";
                                log_debug("asset (%s) has phase infos: %s.", stAssetInfo.qstrId.toLatin1().data(), qstrDbgInfo.toLatin1().data());
                            }
                            else
                            {
                                logError(QString("asset (%1) phase infos is not an array.").arg(stAssetInfo.qstrId));
                            }

                            if(stAssetInfo.iTestSN != j)
                            {
                                stAssetInfo.iTestSN = j;//修正测试顺序，避免测试顺序重复
                            }

                            //修正测试类型为负荷电流的类型为接地电流类型，因为负荷电流类型的不单独作为一个设备进行测试
                            if(TaskModeViewNS::TYPE_LOAD_CURRENT == stAssetInfo.eType)
                            {
                                stAssetInfo.eType = TaskModeViewNS::TYPE_CURRENT;
                            }

                            QJson objTestDataInfos = objAssetInfos.at(j).value(TaskModeViewNS::TASK_NODE_TESTDATAINFOS);
                            if(objTestDataInfos.isArray())
                            {
                                for(int k = 0, iTestDataCnt = objTestDataInfos.count(); k < iTestDataCnt; ++k)
                                {
                                    TaskModeViewNS::BaseTestDataInfo *pDataInfo = NULL;
                                    switch(stAssetInfo.eType)
                                    {
                                    case TaskModeViewNS::TYPE_PD:
                                    {
                                        pDataInfo = new TaskModeViewNS::PDDataInfo();
                                        if(pDataInfo)
                                        {
                                            pDataInfo->ePhaseType = static_cast<TaskModeViewNS::PhaseType>(objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_PHASETYPE).toNumber());
                                            pDataInfo->qstrUUID = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_UUID).toString();
                                            pDataInfo->qstrDiagResult = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_TESTRESULT).toString();
                                            static_cast<TaskModeViewNS::PDDataInfo *>(pDataInfo)->ePDType =static_cast<TaskModeViewNS::PDType>(objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_PDTYPE).toNumber());
                                            static_cast<TaskModeViewNS::PDDataInfo *>(pDataInfo)->eUnit =static_cast<TaskModeViewNS::DataUnit>(objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_PDUNIT).toNumber());
                                            static_cast<TaskModeViewNS::PDDataInfo *>(pDataInfo)->dPDVal = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_PDVALUE).toString().toDouble();
                                            static_cast<TaskModeViewNS::PDDataInfo *>(pDataInfo)->qstrBGFileName = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_BGFILENAME).toString();
                                            static_cast<TaskModeViewNS::PDDataInfo *>(pDataInfo)->qstrDataFileName = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_DATAFILENAME).toString();

                                            if(!qstrlstDataFiles.contains(static_cast<TaskModeViewNS::PDDataInfo *>(pDataInfo)->qstrBGFileName))
                                            {
                                                qstrlstDataFiles.append(static_cast<TaskModeViewNS::PDDataInfo *>(pDataInfo)->qstrBGFileName);
                                            }

                                            if(!qstrlstDataFiles.contains(static_cast<TaskModeViewNS::PDDataInfo *>(pDataInfo)->qstrDataFileName))
                                            {
                                                qstrlstDataFiles.append(static_cast<TaskModeViewNS::PDDataInfo *>(pDataInfo)->qstrDataFileName);
                                            }
                                        }

                                        break;
                                    }
                                    case TaskModeViewNS::TYPE_INFRARED:
                                    {
                                        pDataInfo = new TaskModeViewNS::InfraredDataInfo();
                                        if(pDataInfo)
                                        {
                                            pDataInfo->ePhaseType = static_cast<TaskModeViewNS::PhaseType>(objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_PHASETYPE).toNumber());
                                            pDataInfo->qstrUUID = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_UUID).toString();
                                            pDataInfo->qstrDiagResult = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_TESTRESULT).toString();
                                            static_cast<TaskModeViewNS::InfraredDataInfo *>(pDataInfo)->eUnit =static_cast<TaskModeViewNS::DataUnit>(objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_TEMPUNIT).toNumber());
                                            static_cast<TaskModeViewNS::InfraredDataInfo *>(pDataInfo)->dEnvTemperVal = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_ENVTMP).toNumber();
                                            static_cast<TaskModeViewNS::InfraredDataInfo *>(pDataInfo)->dMaxTemperVal = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_MAXTMP).toNumber();
                                            static_cast<TaskModeViewNS::InfraredDataInfo *>(pDataInfo)->dMinTemperVal = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_MINTMP).toNumber();
                                            static_cast<TaskModeViewNS::InfraredDataInfo *>(pDataInfo)->qstrPicFileName = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_PICFILENAME).toString();
                                            static_cast<TaskModeViewNS::InfraredDataInfo *>(pDataInfo)->qstrDataFileName = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_DATAFILENAME).toString();

                                            if(!qstrlstDataFiles.contains(static_cast<TaskModeViewNS::InfraredDataInfo *>(pDataInfo)->qstrPicFileName))
                                            {
                                                qstrlstDataFiles.append(static_cast<TaskModeViewNS::InfraredDataInfo *>(pDataInfo)->qstrPicFileName);
                                            }

                                            if(!qstrlstDataFiles.contains(static_cast<TaskModeViewNS::InfraredDataInfo *>(pDataInfo)->qstrDataFileName))
                                            {
                                                qstrlstDataFiles.append(static_cast<TaskModeViewNS::InfraredDataInfo *>(pDataInfo)->qstrDataFileName);
                                            }
                                        }

                                        break;
                                    }
                                    case TaskModeViewNS::TYPE_CURRENT:
                                    {
                                        pDataInfo = new TaskModeViewNS::GroundCurrentDataInfo();
                                        if(pDataInfo)
                                        {
                                            pDataInfo->ePhaseType = static_cast<TaskModeViewNS::PhaseType>(objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_PHASETYPE).toNumber());
                                            pDataInfo->qstrUUID = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_UUID).toString();
                                            pDataInfo->qstrDiagResult = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_TESTRESULT).toString();
                                            static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(pDataInfo)->dCurrentVal = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_CURRENT).toNumber();
                                            static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(pDataInfo)->qstrDataFileName = objTestDataInfos.at(k).value(TaskModeViewNS::TASK_NODE_DATAFILENAME).toString();
                                        }

                                        break;
                                    }
                                    default:
                                    {
                                        break;
                                    }

                                    }

                                    if(pDataInfo)
                                    {
                                        QSharedPointer<TaskModeViewNS::BaseTestDataInfo> qspDataInfo(pDataInfo);
                                        stAssetInfo.qvtTestDataInfos.append(qspDataInfo);
                                    }
                                }
                            }

                            if(stCircuitInfo.qvtAssetInfos.contains(stAssetInfo))
                            {
                                //合并测试数据
                                if(!stAssetInfo.qvtTestDataInfos.isEmpty())
                                {
                                    int iExistAsIndex = stCircuitInfo.qvtAssetInfos.indexOf(stAssetInfo);
                                    if(0 <= iExistAsIndex && iExistAsIndex < stCircuitInfo.qvtAssetInfos.size())
                                    {
                                        if(stCircuitInfo.qvtAssetInfos[iExistAsIndex].qvtTestDataInfos.isEmpty())
                                        {
                                            for(int n = 0, iNewTdSize = stAssetInfo.qvtTestDataInfos.size();
                                                n < iNewTdSize; ++n)
                                            {
                                                stCircuitInfo.qvtAssetInfos[iExistAsIndex].qvtTestDataInfos.append(stAssetInfo.qvtTestDataInfos[n]);
                                            }
                                        }
                                        else
                                        {
                                            for(int n = 0, iNewTdSize = stAssetInfo.qvtTestDataInfos.size();
                                                n < iNewTdSize; ++n)
                                            {
                                                if(!isTestDataExistInVector(stCircuitInfo.qvtAssetInfos[iExistAsIndex].qvtTestDataInfos,
                                                                            stAssetInfo.eType, stAssetInfo.qvtTestDataInfos[n]))
                                                {
                                                    stCircuitInfo.qvtAssetInfos[iExistAsIndex].qvtTestDataInfos.append(stAssetInfo.qvtTestDataInfos[n]);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            else
                            {
                                stCircuitInfo.qvtAssetInfos.append(stAssetInfo);
                            }
                        }
                    }
                    else
                    {
                        logError(QString("parse task (%1) circuit (%2) error.").arg(stTaskInfo.qstrId).arg(stCircuitInfo.qstrId));
                        bParsedOk = false;
                        break;
                    }

                    if(!(stTaskInfo.qvtCircuitInfos.contains(stCircuitInfo)))
                    {
                        stTaskInfo.qvtCircuitInfos.append(stCircuitInfo);
                    }
                }
            }

            if(!bParsedOk)
            {
                logError(QString("parse task (%1) error.").arg(stTaskInfo.qstrId));
                break;
            }

            bRet = true;
        }
        else
        {
            logError(QString("parse task (%1) circuit info is not an array.").arg(stTaskInfo.qstrId));
            break;
        }

    }while(0);

    return bRet;
}

/*************************************************
 * 功能：根据任务内容封装JSON格式信息
 * 输入参数：
 *      stTaskInfo：任务信息
 * 输出参数：
 *      qvtJsonInfos：JSON数据集合
 * 返回值：
 *      bool：解析结果，true -- 成功，false -- 失败
 * *************************************************/
bool TaskManager::packageJsonTask(const TaskModeViewNS::CustomTaskInfo &stTaskInfo, QVector<QByteArray> qvtJsonInfos)
{
    bool bRet = false;

    do
    {
        if(stTaskInfo.qstrId.isEmpty() || stTaskInfo.qstrName.isEmpty())
        {
            logError("parse task id or name is empty.");
            break;
        }

        bool bPackOk = true;

        for(int i = 0, iCirCnt = stTaskInfo.qvtCircuitInfos.size(); i < iCirCnt; ++i)
        {
            if(stTaskInfo.qvtCircuitInfos[i].qstrId.isEmpty() || stTaskInfo.qvtCircuitInfos[i].qstrName.isEmpty())
            {
                logError(QString("parse task (%1) circuit id or name is empty.").arg(stTaskInfo.qstrId));
                bPackOk = false;
                break;
            }
            else
            {
                QJson objTaskInfo;
                objTaskInfo.add(TaskModeViewNS::TASK_NODE_TASK_TIME, stTaskInfo.qstrCreateTime.toLatin1());
                objTaskInfo.add(TaskModeViewNS::TASK_NODE_TASK_TEST_TIME, stTaskInfo.qstrTestTime.toLatin1());
                objTaskInfo.add(TaskModeViewNS::TASK_NODE_TASK_ID, stTaskInfo.qstrId.toLatin1());
                objTaskInfo.add(TaskModeViewNS::TASK_NODE_TASK_NAME, stTaskInfo.qstrName.toUtf8());
                QString qstrdTaskLoadCurrentVal = QString::number(stTaskInfo.dLoadCurrentVal,10,1);
                objTaskInfo.add(TaskModeViewNS::TASK_NODE_TASK_LOAD_CURRENT, qstrdTaskLoadCurrentVal.toLatin1());

                QJson objCirInfo;
                objCirInfo.add(TaskModeViewNS::TASK_NODE_CIRCUITID, stTaskInfo.qvtCircuitInfos[i].qstrId.toLatin1());
                objCirInfo.add(TaskModeViewNS::TASK_NODE_CIRCUITNAME, stTaskInfo.qvtCircuitInfos[i].qstrName.toUtf8());
                objCirInfo.add(TaskModeViewNS::TASK_NODE_TESTSN, stTaskInfo.qvtCircuitInfos[i].iTestSN);

                QJson objAssetList(QJson::Array);
                for(int j = 0, iAsCnt = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos.size(); j < iAsCnt; ++j)
                {
                    //不单独添加测试类型为负荷电流的设备节点
                    if(TaskModeViewNS::TYPE_LOAD_CURRENT == stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].eType)
                    {
                        logWarning(QString("current asset (%1) is load current test type, it will not add to json info.").arg(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrId));
                        continue;
                    }

                    QJson objAssetInfo;
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_ASSETID, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrId.toLatin1());
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_ASSETNAME, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrName.toUtf8());
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_ASSETRFIDCODE, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrRFIDCode.toLatin1());
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_TESTPOS, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrTestPosition.toUtf8());
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_TESTSN, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].iTestSN);
                    QString qstrdLoadCurrentVal = QString::number(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].dLoadCurrentVal,10,1);
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_TASK_LOAD_CURRENT, qstrdLoadCurrentVal.toLatin1());
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_TESTTYPE, static_cast<int>(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].eType));
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_TASK_TEST_TIME, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrTestTime.toLatin1());

                    if(!(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtPhaseTypes.isEmpty()))
                    {
                        QJson objPhaseInfos(QJson::Array);
                        for(int q = 0, iPhaseCnt = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtPhaseTypes.size();
                            q < iPhaseCnt; ++q)
                        {
                            objPhaseInfos.addIntToArray(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtPhaseTypes[q]);
                        }

                        objAssetInfo.add(TaskModeViewNS::TASK_NODE_PHASETYPE_INFOS, objPhaseInfos);
                    }

                    QJson objTestDataList(QJson::Array);
                    for(int k = 0, iTdCnt = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtTestDataInfos.size();
                        k < iTdCnt; ++k)
                    {
                        const QSharedPointer<TaskModeViewNS::BaseTestDataInfo> &qspDataInfo = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtTestDataInfos[k];
                        QJson objTestDataInfo;
                        switch(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].eType)
                        {
                        case TaskModeViewNS::TYPE_PD:
                        {
                            TaskModeViewNS::PDDataInfo *pPDDataInfo = static_cast<TaskModeViewNS::PDDataInfo *>(qspDataInfo.data());
                            if(pPDDataInfo)
                            {
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_UUID, pPDDataInfo->qstrUUID.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PHASETYPE, static_cast<int>(pPDDataInfo->ePhaseType));
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PDTYPE, static_cast<int>(pPDDataInfo->ePDType));
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PDUNIT, static_cast<int>(pPDDataInfo->eUnit));
                                QString qstrdPDVal = QString::number(pPDDataInfo->dPDVal,10,1);
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PDVALUE, qstrdPDVal.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_BGFILENAME, pPDDataInfo->qstrBGFileName.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_DATAFILENAME, pPDDataInfo->qstrDataFileName.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_TESTRESULT, pPDDataInfo->qstrDiagResult.toLatin1());
                            }

                            break;
                        }
                        case TaskModeViewNS::TYPE_INFRARED:
                        {
                            TaskModeViewNS::InfraredDataInfo *pIRDataInfo = static_cast<TaskModeViewNS::InfraredDataInfo *>(qspDataInfo.data());
                            if(pIRDataInfo)
                            {
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_UUID, pIRDataInfo->qstrUUID.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PHASETYPE, static_cast<int>(pIRDataInfo->ePhaseType));
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_TEMPUNIT, static_cast<int>(pIRDataInfo->eUnit));
                                QString qstrdEnvTemperVal = QString::number(pIRDataInfo->dEnvTemperVal,10,1);
                                QString qstrdMaxTemperVal = QString::number(pIRDataInfo->dMaxTemperVal,10,1);
                                QString qstrdMinTemperVal = QString::number(pIRDataInfo->dMinTemperVal,10,1);
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_ENVTMP, qstrdEnvTemperVal.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_MAXTMP, qstrdMaxTemperVal.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_MINTMP, qstrdMinTemperVal.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PICFILENAME, pIRDataInfo->qstrPicFileName.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_DATAFILENAME, pIRDataInfo->qstrDataFileName.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_TESTRESULT, pIRDataInfo->qstrDiagResult.toLatin1());
                            }

                            break;
                        }
                        case TaskModeViewNS::TYPE_CURRENT:
                        {
                            TaskModeViewNS::GroundCurrentDataInfo *pGCDataInfo = static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(qspDataInfo.data());
                            if(pGCDataInfo)
                            {
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_UUID, pGCDataInfo->qstrUUID.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PHASETYPE, static_cast<int>(pGCDataInfo->ePhaseType));
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_CURRENT, pGCDataInfo->dCurrentVal);
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_TESTRESULT, pGCDataInfo->qstrDiagResult.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_DATAFILENAME, pGCDataInfo->qstrDataFileName.toLatin1());
                            }

                            break;
                        }
                        default:
                        {
                            break;
                        }

                        }

                        objTestDataList.addItemToArray(objTestDataInfo);
                    }

                    if(0 < objTestDataList.count())
                    {
                        objAssetInfo.add(TaskModeViewNS::TASK_NODE_TESTDATAINFOS, objTestDataList);
                    }

                    objAssetList.addItemToArray(objAssetInfo);
                }

                objCirInfo.add(TaskModeViewNS::TASK_NODE_ASSETINFOS, objAssetList);

                QJson objCirInfoList(QJson::Array);
                objCirInfoList.addItemToArray(objCirInfo);
                objTaskInfo.add(TaskModeViewNS::TASK_NODE_CIRCUITINFOS, objCirInfoList);

                QByteArray qbaJsonData = objTaskInfo.unformattedData();

                if(!stTaskInfo.qvtCircuitInfos[i].qstrTaskFilePath.isEmpty())
                {
                    //写入文件中
                    FileOperUtil::writeFile(stTaskInfo.qvtCircuitInfos[i].qstrTaskFilePath, qbaJsonData, true);
                }

                qvtJsonInfos.append(qbaJsonData);
            }
        }

        if(!bPackOk)
        {
            break;
        }

        bRet = true;

    }while(0);

    return bRet;
}

/*************************************************
 * 功能：根据任务内容封装JSON格式信息
 * 输入参数：
 *      stTaskInfo：任务信息
 * 输出参数：
 *      qstrlstDataFiles：任务中的所有数据文件集合，文件全路径
 * 返回值：
 *      QByteArray：JSON格式信息
 * *************************************************/
QByteArray TaskManager::packageTaskJSONData(const TaskModeViewNS::CustomTaskInfo &stTaskInfo, QStringList &qstrlstDataFiles)
{
    QByteArray qbaJsonData;
    qbaJsonData.clear();

    do
    {
        if(stTaskInfo.qstrId.isEmpty() || stTaskInfo.qstrName.isEmpty())
        {
            logError("parse task id or name is empty.");
            break;
        }

        QJson objTaskInfo;
        objTaskInfo.add(TaskModeViewNS::TASK_NODE_TASK_TIME, stTaskInfo.qstrCreateTime.toLatin1());
        objTaskInfo.add(TaskModeViewNS::TASK_NODE_TASK_TEST_TIME, stTaskInfo.qstrTestTime.toLatin1());
        objTaskInfo.add(TaskModeViewNS::TASK_NODE_TASK_ID, stTaskInfo.qstrId.toLatin1());
        objTaskInfo.add(TaskModeViewNS::TASK_NODE_TASK_NAME, stTaskInfo.qstrName.toUtf8());
        QString qstrdLoadCurrentVal = QString::number(stTaskInfo.dLoadCurrentVal,10,1);
        objTaskInfo.add(TaskModeViewNS::TASK_NODE_TASK_LOAD_CURRENT, qstrdLoadCurrentVal.toLatin1());

        QJson objCirInfoList(QJson::Array);

        for(int i = 0, iCirCnt = stTaskInfo.qvtCircuitInfos.size(); i < iCirCnt; ++i)
        {
            if(!(stTaskInfo.qvtCircuitInfos[i].bUpload))
            {
                logWarning(QString("task (%1) circuit id (%2) will not be upload.").arg(stTaskInfo.qstrId).arg(stTaskInfo.qvtCircuitInfos[i].qstrId));
                continue;
            }

            if(stTaskInfo.qvtCircuitInfos[i].qstrId.isEmpty() || stTaskInfo.qvtCircuitInfos[i].qstrName.isEmpty())
            {
                logError(QString("parse task (%1) circuit id or name is empty.").arg(stTaskInfo.qstrId));
                continue;
            }
            else
            {
                QJson objCirInfo;
                objCirInfo.add(TaskModeViewNS::TASK_NODE_CIRCUITID, stTaskInfo.qvtCircuitInfos[i].qstrId.toLatin1());
                objCirInfo.add(TaskModeViewNS::TASK_NODE_CIRCUITNAME, stTaskInfo.qvtCircuitInfos[i].qstrName.toUtf8());
                objCirInfo.add(TaskModeViewNS::TASK_NODE_TESTSN, stTaskInfo.qvtCircuitInfos[i].iTestSN);

                QString qstrFileParentPath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH
                        + "/" + stTaskInfo.qstrId + "/" + stTaskInfo.qvtCircuitInfos[i].qstrId + "/";

                QJson objAssetList(QJson::Array);
                for(int j = 0, iAsCnt = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos.size(); j < iAsCnt; ++j)
                {
                    //不单独添加测试类型为负荷电流的设备节点
                    if(TaskModeViewNS::TYPE_LOAD_CURRENT == stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].eType)
                    {
                        logWarning(QString("current asset (%1) is load current test type, it will not add to json info.").arg(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrId));
                        continue;
                    }

                    int iTestpointSize = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtTestDataInfos.size();
                    if(0 >= iTestpointSize)
                    {
                        logWarning(QString("current asset (%1) testpoint data size is empty.").arg(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrId));
                        continue;
                    }

                    QJson objAssetInfo;
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_ASSETID, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrId.toLatin1());
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_ASSETNAME, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrName.toUtf8());
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_ASSETRFIDCODE, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrRFIDCode.toLatin1());
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_TESTPOS, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrTestPosition.toUtf8());
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_TESTSN, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].iTestSN);
                    QString qstrdAssetLoadCurrentVal = QString::number( stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].dLoadCurrentVal,10,1);
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_TASK_LOAD_CURRENT, qstrdAssetLoadCurrentVal.toLatin1());
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_TESTTYPE, static_cast<int>(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].eType));
                    objAssetInfo.add(TaskModeViewNS::TASK_NODE_TASK_TEST_TIME, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrTestTime.toLatin1());

                    QJson objTestDataList(QJson::Array);
                    for(int k = 0; k < iTestpointSize; ++k)
                    {
                        const QSharedPointer<TaskModeViewNS::BaseTestDataInfo> &qspDataInfo = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtTestDataInfos[k];
                        QJson objTestDataInfo;
                        switch(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].eType)
                        {
                        case TaskModeViewNS::TYPE_PD:
                        {
                            TaskModeViewNS::PDDataInfo *pPDDataInfo = static_cast<TaskModeViewNS::PDDataInfo *>(qspDataInfo.data());
                            if(pPDDataInfo)
                            {
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_UUID, pPDDataInfo->qstrUUID.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PHASETYPE, static_cast<int>(pPDDataInfo->ePhaseType));
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PDTYPE, static_cast<int>(pPDDataInfo->ePDType));
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PDUNIT, static_cast<int>(pPDDataInfo->eUnit));
                                QString qstrdPDVal = QString::number(pPDDataInfo->dPDVal,10,1);
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PDVALUE, qstrdPDVal.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_BGFILENAME, pPDDataInfo->qstrBGFileName.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_DATAFILENAME, pPDDataInfo->qstrDataFileName.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_TESTRESULT, pPDDataInfo->qstrDiagResult.toLatin1());

                                if(!(pPDDataInfo->qstrBGFileName.isEmpty()))
                                {
                                    if(!(qstrlstDataFiles.contains(qstrFileParentPath + pPDDataInfo->qstrBGFileName)))
                                    {
                                        if(FileOperUtil::checkFileOrDirExist(qstrFileParentPath + pPDDataInfo->qstrBGFileName))
                                        {
                                            qstrlstDataFiles.append(qstrFileParentPath + pPDDataInfo->qstrBGFileName);
                                        }
                                        else
                                        {
                                            logError(QString("file (%1) not exist.").arg(qstrFileParentPath + pPDDataInfo->qstrBGFileName));
                                        }
                                    }
                                }

                                if(!(pPDDataInfo->qstrDataFileName.isEmpty()))
                                {
                                    if(!(qstrlstDataFiles.contains(qstrFileParentPath + pPDDataInfo->qstrDataFileName)))
                                    {
                                        if(FileOperUtil::checkFileOrDirExist(qstrFileParentPath + pPDDataInfo->qstrDataFileName))
                                        {
                                            qstrlstDataFiles.append(qstrFileParentPath + pPDDataInfo->qstrDataFileName);
                                        }
                                        else
                                        {
                                            logError(QString("file (%1) not exist.").arg(qstrFileParentPath + pPDDataInfo->qstrDataFileName));
                                        }
                                    }
                                }
                            }

                            break;
                        }
                        case TaskModeViewNS::TYPE_INFRARED:
                        {
                            TaskModeViewNS::InfraredDataInfo *pIRDataInfo = static_cast<TaskModeViewNS::InfraredDataInfo *>(qspDataInfo.data());
                            if(pIRDataInfo)
                            {
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_UUID, pIRDataInfo->qstrUUID.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PHASETYPE, static_cast<int>(pIRDataInfo->ePhaseType));
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_TEMPUNIT, static_cast<int>(pIRDataInfo->eUnit));
                                QString qstrdEnvTemperVal = QString::number(pIRDataInfo->dEnvTemperVal,10,1);
                                QString qstrdMaxTemperVal = QString::number(pIRDataInfo->dMaxTemperVal,10,1);
                                QString qstrdMinTemperVal = QString::number(pIRDataInfo->dMinTemperVal,10,1);
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_ENVTMP, qstrdEnvTemperVal.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_MAXTMP, qstrdMaxTemperVal.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_MINTMP, qstrdMinTemperVal.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PICFILENAME, pIRDataInfo->qstrPicFileName.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_DATAFILENAME, pIRDataInfo->qstrDataFileName.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_TESTRESULT, pIRDataInfo->qstrDiagResult.toLatin1());

                                if(!(pIRDataInfo->qstrPicFileName.isEmpty()))
                                {
                                    if(!(qstrlstDataFiles.contains(qstrFileParentPath + pIRDataInfo->qstrPicFileName)))
                                    {
                                        if(FileOperUtil::checkFileOrDirExist(qstrFileParentPath + pIRDataInfo->qstrPicFileName))
                                        {
                                            qstrlstDataFiles.append(qstrFileParentPath + pIRDataInfo->qstrPicFileName);
                                        }
                                        else
                                        {
                                            logError(QString("file (%1) not exist.").arg(qstrFileParentPath + pIRDataInfo->qstrPicFileName));
                                        }
                                    }
                                }

                                if(!(pIRDataInfo->qstrDataFileName.isEmpty()))
                                {
                                    if(!(qstrlstDataFiles.contains(qstrFileParentPath + pIRDataInfo->qstrDataFileName)))
                                    {
                                        if(FileOperUtil::checkFileOrDirExist(qstrFileParentPath + pIRDataInfo->qstrDataFileName))
                                        {
                                            qstrlstDataFiles.append(qstrFileParentPath + pIRDataInfo->qstrDataFileName);
                                        }
                                        else
                                        {
                                            logError(QString("file (%1) not exist.").arg(qstrFileParentPath + pIRDataInfo->qstrDataFileName));
                                        }
                                    }
                                }

                            }

                            break;
                        }
                        case TaskModeViewNS::TYPE_CURRENT:
                        {
                            TaskModeViewNS::GroundCurrentDataInfo *pGCDataInfo = static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(qspDataInfo.data());
                            if(pGCDataInfo)
                            {
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_UUID, pGCDataInfo->qstrUUID.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_PHASETYPE, static_cast<int>(pGCDataInfo->ePhaseType));
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_CURRENT, pGCDataInfo->dCurrentVal);
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_TESTRESULT, pGCDataInfo->qstrDiagResult.toLatin1());
                                objTestDataInfo.add(TaskModeViewNS::TASK_NODE_DATAFILENAME, pGCDataInfo->qstrDataFileName.toLatin1());
                            }

                            break;
                        }
                        default:
                        {
                            break;
                        }

                        }

                        objTestDataList.addItemToArray(objTestDataInfo);
                    }

                    if(0 < objTestDataList.count())
                    {
                        objAssetInfo.add(TaskModeViewNS::TASK_NODE_TESTDATAINFOS, objTestDataList);
                    }

                    objAssetList.addItemToArray(objAssetInfo);
                }

                objCirInfo.add(TaskModeViewNS::TASK_NODE_ASSETINFOS, objAssetList);

                objCirInfoList.addItemToArray(objCirInfo);
            }
        }

        if(0 < objCirInfoList.count())
        {
            objTaskInfo.add(TaskModeViewNS::TASK_NODE_CIRCUITINFOS, objCirInfoList);
        }

        qbaJsonData = objTaskInfo.unformattedData();

    }while(0);

    return qbaJsonData;
}

//扫描更新主任务信息列表
void TaskManager::scanMainTask( const QString & strRootpath )
{
    Q_UNUSED(strRootpath);
    //TODO
    //扫描文件夹
    //解析子任务文件
    //获取主任务信息
}

/*************************************************
函数名：
输入参数: floderDir--文件夹名称
输出参数：NULL
返回值：是否删除成功
功能：删除指定文件夹下所有文件
*************************************************************/
bool TaskManager::removeFolder(const QString &folderDir)
{
    FileOperUtil::refreshToSystemDisk();

    //判断文件是否存在
    QDir dir(folderDir);
    if(!dir.exists())
    {
        return false;
    }

    //遍历删除文件
    QFileInfoList fileList;//待删除文件列表
    QFileInfo curFile;//当前处理的文件
    fileList.append(QFileInfo(folderDir));
    while(fileList.size() > 0)
    {
        int infoNum = fileList.size();
        //逆序遍历：因为遍历过程中会删除fileList内容，逆序不会改变未处理条目的索引
        for(int i = infoNum - 1; i >= 0; --i)
        {
            curFile = fileList[i];
            if(curFile.isFile())//如果是文件，删除文件
            {
                QFile fileTemp(curFile.filePath());
                fileTemp.remove();
                fileList.removeAt(i);
            }
            if(curFile.isDir())//如果是文件夹
            {
                QDir dirTemp(curFile.filePath());
                /*QFileInfoList subFileList=dirTemp.entryInfoList(QDir::Dirs | QDir::Files
                                                              | QDir::Readable | QDir::Writable
                                                              | QDir::Hidden | QDir::NoDotAndDotDot
                                                              , QDir::Name);*/

                dirTemp.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Readable | QDir::Writable | QDir::Hidden);
                QFileInfoList subFileList = dirTemp.entryInfoList();
                if(0 == subFileList.size())//下层没有文件或文件夹
                {
                    dirTemp.rmpath(dirTemp.absolutePath());
                    fileList.removeAt(i);
                }
                else//下层有文件夹或文件
                {
                    for(int j = 0, iSize = subFileList.size(); j < iSize; ++j)
                    {
                        //将未处理的文件夹放入待删除列表中
                        if(!(fileList.contains(subFileList[j])))
                        {
                            fileList.append(subFileList[j]);
                        }
                    }
                }
            }
        }
    }

    //接入终端根路径需要存在
    QString strMainTaskRootPath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH;
    FileOperUtil::createDirectory(strMainTaskRootPath);
    FileOperUtil::refreshToSystemDisk();
    return true;
}

//删除指定主任务
bool TaskManager::deleteMainTask( const QString& strMainId )
{
    deleteMainTaskFloder( strMainId );

    emit sigTaskChanged();
    return true;
}

bool TaskManager::deleteMainTasks( const QStringList &strMainIds )
{
    for( int iTask = 0, iSize = strMainIds.size(); iTask < iSize; ++iTask )
    {
        deleteMainTaskFloder( strMainIds[iTask] );
    }

    emit sigTaskChanged();
    return true;
}

/*****************************************************
 * 功能：删除JSON格式的主任务
 * 输入参数：
 *      qstrlstMainTaskIds：主任务ID集合
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ***************************************************/
bool TaskManager::deleteJsonTasks(const QStringList &qstrlstMainTaskIds)
{
    bool bRet = false;

    if(!(qstrlstMainTaskIds.isEmpty()))
    {
        QString qstrTaskPath = "";
        for(int i = 0, iSize = qstrlstMainTaskIds.size(); i < iSize; ++i)
        {
            if(!(qstrlstMainTaskIds.at(i).isEmpty()))
            {
                TaskModeViewNS::CustomTaskInfo stTaskInfo;
                stTaskInfo.qstrId = qstrlstMainTaskIds.at(i);

                qstrTaskPath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + stTaskInfo.qstrId;
                if(FileOperUtil::deleteDir(qstrTaskPath))
                {
                    bRet = true;
                    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
                    {
                        int iIndex = m_qvt4TaskInfos.indexOf(stTaskInfo);
                        if(0 <= iIndex && iIndex < m_qvt4TaskInfos.size())
                        {
                            m_qvt4TaskInfos.remove(iIndex);
                        }

                        m_qmt4TaskInfos.unlock();
                    }
                }
                else
                {
                    logError(QString("delete file (%1) failed.").arg(qstrTaskPath));
                    bRet = false;
                    break;
                }

                qstrTaskPath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + stTaskInfo.qstrId + ".zip";
                bRet = FileOperUtil::deleteFile(qstrTaskPath);
                if(!bRet)
                {
                    bRet = false;
                    break;
                }
            }
        }
    }

    return bRet;
}

void TaskManager::deleteMainTaskFloder( const QString& strMainId )
{
    //删除主任务对应的文件夹及其下所有文件
    if( !strMainId.isEmpty() )
    {
        QString strMainTaskFolder = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + strMainId;
        removeFolder( strMainTaskFolder );
    }

    m_tasksMap.remove( strMainId );
    deleteTaskBuffer( strMainId );
}

//删除指定子任务
bool TaskManager::deleteSubTask( const QString& strMainId, const QString& strSubId )
{
    deleteSubTaskFloder( strMainId, strSubId );

    emit sigTaskChanged();
    return true;
}

bool TaskManager::deleteSubTasks( const QString& strMainId, const QStringList& strSubIds )
{
    for( int iSub = 0, iSize = strSubIds.size(); iSub < iSize; ++iSub )
    {
        deleteSubTaskFloder( strMainId, strSubIds[iSub] );
    }

    emit sigTaskChanged();
    return true;
}

void TaskManager::deleteSubTaskFloder( const QString& strMainId, const QString& strSubId )
{
    //删除子任务对应的文件夹及其下所有文件
    QString strSubTaskFolder = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + strMainId + "/" + strSubId;
    removeFolder( strSubTaskFolder );

    if( m_tasksMap.contains( strMainId ) )
    {
        for( int iSub = 0, iSize = m_tasksMap.value(strMainId).size(); iSub < iSize; ++iSub )
        {
            if( m_tasksMap.value(strMainId).at(iSub).s_strId == strSubId )
            {
                m_tasksMap[strMainId].remove( iSub );
                if( m_tasksMap[strMainId].isEmpty() )
                {
                    m_tasksMap.remove( strMainId );
                    deleteTaskBuffer( strMainId );
                }
                break;
            }
        }
    }
}

//update main task
void TaskManager::updateMainTask( const CustomAccessTaskNS::MainTaskInfo& mainTask )
{
    bool bFind = false;
    for( int iTask = 0, iSize = m_vMainTask.size(); iTask < iSize; ++iTask )
    {
        if( mainTask.s_strId == m_vMainTask[iTask].s_strId )
        {
            m_vMainTask[iTask] = mainTask;
            bFind = true;
            break;
        }
    }
    if( !bFind )
    {
        m_vMainTask.append( mainTask );
    }
}

//delete main task
void TaskManager::deleteTaskBuffer( const QString &strTaskId )
{
    for( int iTask = 0, iSize = m_vMainTask.size(); iTask < iSize; ++iTask )
    {
        if( strTaskId == m_vMainTask[iTask].s_strId )
        {
            m_vMainTask.remove( iTask );
            break;
        }
    }
}

QByteArray TaskManager::generateTasksZipXml( const QString&strMainId, const QStringList& subTaskIds, QString& strTasksZipFile )
{
    FileOperUtil::refreshToSystemDisk();

    QByteArray qbaTaskData;
    qbaTaskData.clear();

    do
    {
        if(subTaskIds.isEmpty())
        {
            break;
        }

        SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
        if(SystemSet::ACCESS_PROTO_BJRZT == eProtocol)
        {

        }
        else
        {
            QString subTaskId = subTaskIds.first(); //临时修改
            SubTask * pSubTask = createSubTask(strMainId, subTaskId);
            if(pSubTask)
            {
                QStringList fileLists;
                CustomAccessTaskNS::MainTaskInfo mainInfo = pSubTask->m_sMainTaskInfo;
                XMLDocument doc(CustomAccessTaskIONS::ROOT_NODE);

                doc.addElement(CustomAccessTaskIONS::VERSION_NODE);
                doc.setValue(CustomAccessTaskIONS::VERSION_NODE, pSubTask->m_strVersion);

                doc.addElement(CustomAccessTaskIONS::MAIN_TASK_NODE);
                doc.beginElement( CustomAccessTaskIONS::MAIN_TASK_NODE );

                doc.setAttribute(CustomAccessTaskIONS::ID, pSubTask->m_sMainTaskInfo.s_strId);
                doc.setAttribute(CustomAccessTaskIONS::TYPE, QString::number(pSubTask->m_sMainTaskInfo.s_eType));
                doc.setAttribute(CustomAccessTaskIONS::NAME, pSubTask->m_sMainTaskInfo.s_strName);
                doc.addElement(CustomAccessTaskIONS::FILE_COUNT_NODE);
                doc.addElement(CustomAccessTaskIONS::FILE_TYPE_NODE);
                doc.setValue(CustomAccessTaskIONS::FILE_TYPE_NODE, QString::number(SubTask::ZIP_TYPE));

                pSubTask->addSubTaskXml(doc, fileLists);
                destroySubTask( pSubTask );

                for( int i = 1, iSize = subTaskIds.size(); i < iSize; ++i )
                {
                    subTaskId = subTaskIds[i];
                    pSubTask = createSubTask( strMainId, subTaskId );
                    if( NULL != pSubTask )
                    {
                        pSubTask->addSubTaskXml( doc, fileLists );
                        destroySubTask( pSubTask );
                    }
                }

                doc.setValue(CustomAccessTaskIONS::FILE_COUNT_NODE, QString::number( fileLists.size() ));

                //对所有数据文件进行压缩
                strTasksZipFile = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + mainInfo.s_strId + ".zip";
                //如果已存在则删除
                QFile zipFile(strTasksZipFile);
                if(zipFile.exists())
                {
                    zipFile.remove();
                }

                // 压缩文件
                bool cmpResult = JlCompress::compressFiles( strTasksZipFile, fileLists );
                FileOperUtil::refreshToSystemDisk();
                if(cmpResult)
                {
                    qbaTaskData = doc.getByteArray();
                }
                else
                {
                    qbaTaskData.clear();
                }
            }
        }

    }while(0);

    return qbaTaskData;
}

/************************************************************
 * 功能：生产JSON任务的压缩文件和任务内容
 * 输入参数：
 *      qstrMainTaskId：主任务的ID
 *      qstrlstSubTaskIds：子任务的ID集合
 * 输出参数：
 *      qbaJSONTaskContent：JSON任务数据内容
 * 返回值：
 *      QString：ZIP文件的路径
 * **********************************************************/
QString TaskManager::generateJSONTasksZipFiles(const QString &qstrMainTaskId, const QStringList &qstrlstSubTaskIds, QByteArray &qbaJSONTaskContent)
{
    QString qstrZipFilePath = "";

    TaskModeViewNS::CustomTaskInfo stTaskInfo;
    stTaskInfo.qstrId = qstrMainTaskId;

    bool bFound = false;
    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        int iIndex = m_qvt4TaskInfos.indexOf(stTaskInfo);
        if(0 <= iIndex && iIndex < m_qvt4TaskInfos.size())
        {
            stTaskInfo = m_qvt4TaskInfos[iIndex];
            bFound = true;
        }
        else
        {
            logError(QString("find task (%1) failed.").arg(stTaskInfo.qstrId));
        }

        m_qmt4TaskInfos.unlock();
    }

    if(bFound)
    {
        qstrZipFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + stTaskInfo.qstrId + ".zip";
        FileOperUtil::deleteFile(qstrZipFilePath);

        QStringList qstrlstFiles;
        qstrlstFiles.clear();

        //QStringList qstrlstTypes;
        //qstrlstTypes.clear();

        //qstrlstTypes.append("*.dat");
        //qstrlstTypes.append("*.jpg");

        QString qstrTaskFolder = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + stTaskInfo.qstrId + "/";

        if(qstrlstSubTaskIds.isEmpty())
        {
            //所有子任务上传
            for(int i = 0, iSize = stTaskInfo.qvtCircuitInfos.size(); i < iSize; ++i)
            {
                stTaskInfo.qvtCircuitInfos[i].bUpload = true;
                /*QString qstrFilePath = qstrTaskFolder + stTaskInfo.qvtCircuitInfos[i].qstrId;
                QStringList qstrlstTmpFiles = FileOperUtil::getFilePathsUnderDirPath(qstrFilePath, qstrlstTypes);
                if(!qstrlstTmpFiles.isEmpty())
                {
                    qstrlstFiles.append(qstrlstTmpFiles);
                }*/
            }
        }
        else
        {
            //上传部分子任务
            for(int i = 0, iSize = qstrlstSubTaskIds.size(); i < iSize; ++i)
            {
                if(!(qstrlstSubTaskIds.at(i).isEmpty()))
                {
                    TaskModeViewNS::CircuitInfo stCirInfo;
                    stCirInfo.qstrId = qstrlstSubTaskIds.at(i);
                    int iCirIndex = stTaskInfo.qvtCircuitInfos.indexOf(stCirInfo);
                    if(0 <= iCirIndex && iCirIndex < stTaskInfo.qvtCircuitInfos.size())
                    {
                        stTaskInfo.qvtCircuitInfos[iCirIndex].bUpload = true;
                        /*QString qstrFilePath = qstrTaskFolder + stTaskInfo.qvtCircuitInfos[iCirIndex].qstrId;
                        QStringList qstrlstTmpFiles = FileOperUtil::getFilePathsUnderDirPath(qstrFilePath, qstrlstTypes);
                        if(!(qstrlstTmpFiles.isEmpty()))
                        {
                            qstrlstFiles.append(qstrlstTmpFiles);
                        }*/
                    }
                    else
                    {
                        logError(QString("task (%1) not contains sub task id (%2).").arg(stTaskInfo.qstrId).arg(stCirInfo.qstrId));
                    }
                }
            }
        }

        qbaJSONTaskContent = packageTaskJSONData(stTaskInfo, qstrlstFiles);
        if(qbaJSONTaskContent.isEmpty())
        {
            qstrZipFilePath = "";
        }
        else
        {
            FileOperUtil::refreshToSystemDisk();

            QString qstrTaskJsonFilePath = qstrTaskFolder + stTaskInfo.qstrId + CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_SUFFIX;
            FileOperUtil::writeFile(qstrTaskJsonFilePath, qbaJSONTaskContent, true);
            qstrlstFiles.append(qstrTaskJsonFilePath);

            if(!qstrlstFiles.isEmpty())
            {
                if(!(JlCompress::compressFiles(qstrZipFilePath, qstrlstFiles)))
                {
                    //try again
                    QStringList qstrlstTmpFiles = qstrlstFiles;

                    for(int i = 0, iSize = qstrlstTmpFiles.size(); i < iSize; ++i)
                    {
                        if(!FileOperUtil::checkFileOrDirExist(qstrlstTmpFiles[i]))
                        {
                            qstrlstFiles.removeAll(qstrlstTmpFiles[i]);
                            logError(QString("file (%1) is not exist.").arg(qstrlstTmpFiles[i]));
                        }
                    }

                    FileOperUtil::deleteFile(qstrZipFilePath);
                    if(!(JlCompress::compressFiles(qstrZipFilePath, qstrlstFiles)))
                    {
                        logError(QString("compress zip file (%1) failed.").arg(qstrZipFilePath));
                        qstrZipFilePath = "";
                    }
                }
            }
            else
            {
                logError(QString("task (%1) file info is empty.").arg(stTaskInfo.qstrId));
            }

            FileOperUtil::deleteFile(qstrTaskJsonFilePath);
            FileOperUtil::refreshToSystemDisk();
        }
    }

    return qstrZipFilePath;
}

/*************************************************
功能： 槽，当存储卡被格式化之后，重新扫描任务
*************************************************/
void TaskManager::onStorageFormatted()
{
    m_vMainTask.clear();

    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        m_qvt4TaskInfos.clear();
        m_qmt4TaskInfos.unlock();
    }

    return;
}

void TaskManager::onReadCustomaccessTaskInfos()
{
    QVector<CustomAccessTaskNS::MainTaskInfo> qvt4TaskInfos = mainTasks();
    emit sigCustomAccessTaskInfosFinished(qvt4TaskInfos);
    return;
}
/*************************************************************************
 * 功能：解析XML格式的任务信息结构
 * 输入参数：
 *      taskContent：任务内容
 * 返回值：
 *      QString：任务文件保存的路径
 * ***********************************************************************/
QString TaskManager::parseXMLTaskFile(const QByteArray &taskContent)
{
    QString qstrFilePath = "";
    CustomAccessTaskNS::MainTaskInfo stTaskInfo;
    CustomAccessTaskNS::SubTaskInfo stSubTaskInfo;

    //生成对应的保存路径，保存文件
    QDomDocument document;
    document.setContent(QString::fromUtf8(taskContent.data(), taskContent.size()));
    QDomElement root = document.documentElement();
    // 解析操作结果结点
    QDomElement firstChild = root.firstChildElement( CustomAccessTaskNS::TASK_NODE_MAIN_TASK );
    QString strMainTaskID = firstChild.toElement().attribute(CustomAccessTaskNS::TASK_NODE_ID);
    stTaskInfo.s_strId = strMainTaskID;
    stTaskInfo.s_strName = firstChild.toElement().attribute(CustomAccessTaskNS::TASK_NODE_NAME);
    stTaskInfo.s_eType = (CustomAccessTaskNS::TaskType) (firstChild.toElement().attribute(CustomAccessTaskNS::TASK_NODE_TYPE).toUInt());
    //logInfo(QString("s_strName %1").arg(stTaskInfo.s_strName));

    QString strSubTaskID = "";
    QDomNodeList childNodesList = firstChild.childNodes();
    for(int i = 0, iSize = childNodesList.size(); i < iSize; ++i)
    {
        QDomNode childNode = childNodesList.at(i);
        QString strNodeName = childNode.nodeName();
        if(strNodeName == CustomAccessTaskNS::TASK_NODE_SUB_TASK)
        {
            strSubTaskID = childNode.toElement().attribute(CustomAccessTaskNS::TASK_NODE_ID);
            stSubTaskInfo.s_strId = strSubTaskID;
            stSubTaskInfo.s_strName = childNode.toElement().attribute(CustomAccessTaskNS::TASK_NODE_NAME);
            stSubTaskInfo.s_eTestState = CustomAccessTaskNS::TASK_UNTEST;
            stSubTaskInfo.s_eType = (CustomAccessTaskNS::TaskType) (childNode.toElement().attribute(CustomAccessTaskNS::TASK_NODE_TYPE).toUInt());
            stSubTaskInfo.s_bUploadable = false;
            break;
        }
    }

    QString strOriFile;
    bool bTaskExist = isSubTaskExist(strMainTaskID, strSubTaskID, strOriFile);
    //create main task
    //createMainTask(stTaskInfo);

    //create folder path to save task file and data file
    bool isSuccess = createFileSavedPath(strMainTaskID, strSubTaskID, qstrFilePath);
    if(isSuccess)
    {
        writeTaskFile(taskContent, qstrFilePath);
    }

    //如果任务已存在，合并原有测试数据
    if( bTaskExist )
    {
        SubTask * pNewTask = createSubTask( qstrFilePath );
        QString strOriTaskPath = pNewTask->dataSavePath() + "/" + strOriFile;
        SubTask * pOriTask = createSubTask( strOriTaskPath );
        if( (NULL != pNewTask) && (NULL != pOriTask) &&
                (pNewTask->m_vGaps.size() == pOriTask->m_vGaps.size()) )
        {
            for( int iGap = 0, iGapSize = pNewTask->m_vGaps.size(); iGap < iGapSize; ++iGap )
            {
                for( int iTb = 0, iTbSize = pNewTask->m_vGaps[iGap].s_vTestPoints.size(); iTb < iTbSize; ++iTb )
                {
                    if( !CustomAccessTaskIO::isTestPointExisted( pOriTask->m_vGaps[iGap].s_vTestPoints,
                                                                 pNewTask->m_vGaps[iGap].s_vTestPoints[iTb].s_strId ) )
                    {
                        pOriTask->addTestPoint( pNewTask->m_vGaps.at(iGap).s_strId,
                                                pNewTask->m_vGaps[iGap].s_vTestPoints[iTb] );
                    }
                    else
                    {
                        pOriTask->updateTestPointDiagResult(pNewTask->m_vGaps.at(iGap).s_strId,
                                                            pNewTask->m_vGaps[iGap].s_vTestPoints[iTb]);
                    }
                }
            }

            pOriTask->saveTaskFile();
            destroySubTask( pNewTask );
            destroySubTask( pOriTask );
        }

        QFile fileTemp(qstrFilePath);
        fileTemp.remove();
    }
    else
    {
        stSubTaskInfo.s_strFileName = QFileInfo(qstrFilePath).fileName();
        m_tasksMap[strMainTaskID].append( stSubTaskInfo );
        updateMainTask( stTaskInfo );
    }

    return qstrFilePath;
}

/*************************************************************************
 * 功能：解析XML格式的任务信息结构
 * 输入参数：
 *      taskContent：任务内容
 * 返回值：
 *      QString：任务文件保存的路径
 * ***********************************************************************/
QString TaskManager::parseJSDKYXMLTaskFile(const QByteArray &taskContent)
{
    QString qstrFilePath = "";
    CustomAccessTaskNS::MainTaskInfo stTaskInfo;
    CustomAccessTaskNS::SubTaskInfo stSubTaskInfo;

    //生成对应的保存路径，保存文件
    QDomDocument document;
    document.setContent(QString::fromUtf8(taskContent.data(), taskContent.size()));
    QDomElement root = document.documentElement();
    // 解析操作结果结点
    QDomElement firstChild = root.firstChildElement( CustomAccessTaskNS::TASK_NODE_MAIN_TASK );
    QString strMainTaskID = firstChild.toElement().attribute(CustomAccessTaskNS::TASK_NODE_ID);
    stTaskInfo.s_strId = strMainTaskID;
    stTaskInfo.s_strName = firstChild.toElement().attribute(CustomAccessTaskNS::TASK_NODE_NAME);
    stTaskInfo.s_eType = (CustomAccessTaskNS::TaskType) (firstChild.toElement().attribute(CustomAccessTaskNS::TASK_NODE_TYPE).toUInt());
    //logInfo(QString("s_strName %1").arg(stTaskInfo.s_strName));

    QString strSubTaskID = "";
    QDomNodeList childNodesList = firstChild.childNodes();
    for(int i = 0, iSize = childNodesList.size(); i < iSize; ++i)
    {
        QDomNode childNode = childNodesList.at(i);
        QString strNodeName = childNode.nodeName();
        if(strNodeName == CustomAccessTaskNS::TASK_NODE_SUB_TASK)
        {
            strSubTaskID = childNode.toElement().attribute(CustomAccessTaskNS::TASK_NODE_ID);
            stSubTaskInfo.s_strId = strSubTaskID;
            stSubTaskInfo.s_strName = childNode.toElement().attribute(CustomAccessTaskNS::TASK_NODE_NAME);
            stSubTaskInfo.s_eTestState = CustomAccessTaskNS::TASK_UNTEST;
            stSubTaskInfo.s_eType = (CustomAccessTaskNS::TaskType) (childNode.toElement().attribute(CustomAccessTaskNS::TASK_NODE_TYPE).toUInt());
            stSubTaskInfo.s_bUploadable = false;


            QString strOriFile;
            bool bTaskExist = isSubTaskExist(strMainTaskID, strSubTaskID, strOriFile);
            //create main task
            //createMainTask(stTaskInfo);

            //create folder path to save task file and data file
            bool isSuccess = createFileSavedPath(strMainTaskID, strSubTaskID, qstrFilePath);
            if(isSuccess)
            {
                writeTaskFile(taskContent, qstrFilePath);
            }

            //如果任务已存在，合并原有测试数据
            if( bTaskExist )
            {
                SubTask * pNewTask = createSubTask( qstrFilePath );
                QString strOriTaskPath = pNewTask->dataSavePath() + "/" + strOriFile;
                SubTask * pOriTask = createSubTask( strOriTaskPath );
                if( (NULL != pNewTask) && (NULL != pOriTask) &&
                        (pNewTask->m_vGaps.size() == pOriTask->m_vGaps.size()) )
                {
                    for( int iGap = 0, iGapSize = pNewTask->m_vGaps.size(); iGap < iGapSize; ++iGap )
                    {
                        for( int iTb = 0, iTbSize = pNewTask->m_vGaps[iGap].s_vTestPoints.size(); iTb < iTbSize; ++iTb )
                        {
                            if( !CustomAccessTaskIO::isTestPointExisted( pOriTask->m_vGaps[iGap].s_vTestPoints,
                                                                         pNewTask->m_vGaps[iGap].s_vTestPoints[iTb].s_strId ) )
                            {
                                pOriTask->addTestPoint( pNewTask->m_vGaps.at(iGap).s_strId,
                                                        pNewTask->m_vGaps[iGap].s_vTestPoints[iTb] );
                            }
                            else
                            {
                                pOriTask->updateTestPointDiagResult(pNewTask->m_vGaps.at(iGap).s_strId,
                                                                    pNewTask->m_vGaps[iGap].s_vTestPoints[iTb]);
                            }
                        }
                    }

                    pOriTask->saveTaskFile();
                    destroySubTask( pNewTask );
                    destroySubTask( pOriTask );
                }

                QFile fileTemp(qstrFilePath);
                fileTemp.remove();
            }
            else
            {
                stSubTaskInfo.s_strFileName = QFileInfo(qstrFilePath).fileName();
                m_tasksMap[strMainTaskID].append( stSubTaskInfo );
                updateMainTask( stTaskInfo );
            }

        }

    }


    return qstrFilePath;
}

/*************************************************************************
 * 功能：解析JSON格式的任务信息结构
 * 输入参数：
 *      taskContent：任务内容
 * 返回值：
 *      QString：任务文件保存的路径
 * ***********************************************************************/
QString TaskManager::parseJSONTaskFile(const QByteArray &taskContent)
{
    QString qstrFilePath = "";

    QStringList qstrlstDataFiles;
    qstrlstDataFiles.clear();

    TaskModeViewNS::CustomTaskInfo stTaskInfo;
    if(parseJsonTask(taskContent, stTaskInfo, qstrlstDataFiles))
    {
        if(stTaskInfo.qstrId.isEmpty())
        {
            logError("parse josn content failed, task id is empty.");
        }
        else
        {
            for(int i = 0, iCirCnt = stTaskInfo.qvtCircuitInfos.size(); i < iCirCnt; ++i)
            {
                stTaskInfo.qvtCircuitInfos[i].qstrTaskFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH
                        + "/" + stTaskInfo.qstrId + "/" + stTaskInfo.qvtCircuitInfos[i].qstrId
                        + "/" + QDateTime::currentDateTime().toString("yyyyMMddhhmmss") + CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_SUFFIX;
            }

            if(isExistJsonTaskInfo(stTaskInfo))
            {
                //合并任务
                mergeJsonTaskInfo(stTaskInfo);
            }
            else
            {
                QVector<QByteArray> qbaJsonInfos;
                qbaJsonInfos.clear();
                if(packageJsonTask(stTaskInfo, qbaJsonInfos))
                {
                    addJsonTaskInfoToVector(stTaskInfo);
                }
            }

            qstrFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + stTaskInfo.qstrId;
        }
    }

    return qstrFilePath;
}

/***************************************************************
 * 功能：扫描JSON格式的任务信息集合
 * *************************************************************/
void TaskManager::scanJSONTaskInfos()
{
    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        m_qvt4TaskInfos.clear();
        m_qmt4TaskInfos.unlock();
    }

    QString qstrTaskDir = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH;

    QStringList qstrlstFileTypes;
    qstrlstFileTypes.clear();

    QStringList qstrlstTaskFiles = FileOperUtil::getFilePathsUnderDirPath(qstrTaskDir, qstrlstFileTypes);
    for(int i = 0, iSize = qstrlstTaskFiles.size(); i < iSize; ++i)
    {
        if(!(qstrlstTaskFiles.at(i).isEmpty())
                && (CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_SUFFIX == FileOperUtil::getFileSuffix(qstrlstTaskFiles.at(i))))
        {
            QByteArray qbaJsonContent;
            qbaJsonContent.clear();
            FileOperUtil::readFile(qstrlstTaskFiles.at(i), qbaJsonContent);

            QStringList qstrlstDataFiles;
            qstrlstDataFiles.clear();

            TaskModeViewNS::CustomTaskInfo stTaskInfo;
            if(parseJsonTask(qbaJsonContent, stTaskInfo, qstrlstDataFiles))
            {
                if(stTaskInfo.qstrId.isEmpty())
                {
                    logError("parse josn content failed, task id is empty.");
                }
                else
                {
                    QStringList qstrlstTypes;
                    qstrlstTypes.clear();

                    qstrlstTypes.append("*.dat");
                    qstrlstTypes.append("*.jpg");

                    //目前任务文件中是一个子任务
                    for(int j = 0, iCirCnt = stTaskInfo.qvtCircuitInfos.size(); j < iCirCnt; ++j)
                    {
                        QString qstrTaskParentFolder = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH
                                + "/" + stTaskInfo.qstrId + "/" + stTaskInfo.qvtCircuitInfos[j].qstrId;

                        //删除无效的文件
                        QStringList qstrlstTmpFiles = FileOperUtil::getFilePathsUnderDirPath(qstrTaskParentFolder, qstrlstTypes);
                        for(int k = 0, iFileSize = qstrlstTmpFiles.size(); k < iFileSize; ++k)
                        {
                            if(!(qstrlstTmpFiles.at(k).isEmpty()))
                            {
                                if(!(qstrlstDataFiles.contains(FileOperUtil::getFileName(qstrlstTmpFiles.at(k)))))
                                {
                                    if(FileOperUtil::deleteFile(qstrlstTmpFiles.at(k)))
                                    {
                                        logWarning(QString("delete unused file (%1) in sub task (%2).").arg(qstrlstTmpFiles.at(k)).arg(stTaskInfo.qvtCircuitInfos[j].qstrId));
                                    }
                                }
                            }
                        }

                        if(qstrTaskParentFolder == FileOperUtil::getFileParentFolder(qstrlstTaskFiles.at(i)))
                        {
                            stTaskInfo.qvtCircuitInfos[j].qstrTaskFilePath = qstrlstTaskFiles.at(i);
                            //break;//存在多个任务时
                        }
                    }

                    addJsonSubTaskInfoToVector(stTaskInfo);
                }
            }
        }
    }

    return;
}

/***************************************************************
 * 功能：获取JSON格式的任务信息集合
 * 返回值：
 *      QVector<TaskModeViewNS::CustomTaskInfo>：任务信息集合
 * *************************************************************/
QVector<TaskModeViewNS::CustomTaskInfo> TaskManager::getJSONTaskInfos()
{
    QVector<TaskModeViewNS::CustomTaskInfo> qvt4TaskInfos;

    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        qvt4TaskInfos = m_qvt4TaskInfos;
        m_qmt4TaskInfos.unlock();
    }

    return qvt4TaskInfos;
}

/***************************************************************
 * 功能：获取当前JSON格式的线路信息集合
 * 返回值：
 *      QVector<TaskModeViewNS::CircuitInfo>：线路信息集合
 * *************************************************************/
QVector<TaskModeViewNS::CircuitInfo> TaskManager::getCurJSONTaskCircuitInfos()
{
    TaskModeViewNS::CustomTaskInfo stTaskInfo;
    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurTaskInfo)
        {
            stTaskInfo = *m_pCurTaskInfo;
        }
        else
        {
            logError("current task info pointer is NULL.");
        }

        m_qmt4CurTaskInfo.unlock();
    }

    return stTaskInfo.qvtCircuitInfos;
}

/***************************************************************
 * 功能：获取当前JSON格式的接头设备信息集合
 * 返回值：
 *      QVector<TaskModeViewNS::AssetInfo>：接头设备信息集合
 * *************************************************************/
QVector<TaskModeViewNS::AssetInfo> TaskManager::getCurJSONTaskAssetInfos()
{
    QVector<TaskModeViewNS::AssetInfo> qvtAssetInfos;
    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurCircuitInfo)
        {
            for(int i = 0, iSize = m_pCurCircuitInfo->qvtAssetInfos.size(); i < iSize; ++i)
            {
                bool bRet =true;
                for(int j = 0, jSize = qvtAssetInfos.size(); j < jSize; ++j)
                {
                    if(qvtAssetInfos.at(j).qstrId == m_pCurCircuitInfo->qvtAssetInfos[i].qstrId)
                    {
                        bRet =false;
                    }
                }
                if(bRet)
                {
                    qvtAssetInfos.append(m_pCurCircuitInfo->qvtAssetInfos[i]);
                }
            }
        }
        m_qmt4CurCircuitInfo.unlock();
    }
    return qvtAssetInfos;
}

/*************************************************************************
 * 功能：判断JSON任务是否存在与集合中
 * 输入参数：
 *      stTaskInfo：任务信息
 * 返回值：
 *      bool：判断结果，true -- 存在，false -- 不存在
 * ***********************************************************************/
bool TaskManager::isExistJsonTaskInfo(const TaskModeViewNS::CustomTaskInfo &stTaskInfo)
{
    bool bRet = false;

    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        bRet = m_qvt4TaskInfos.contains(stTaskInfo);
        m_qmt4TaskInfos.unlock();
    }

    return bRet;
}

/*************************************************************************
 * 功能：判断测试数据是否存在于集合中
 * 输入参数：
 *      qvtTestDataInfos：数据集合
 *      eTestType：测试类型
 *      qspTestDataInfo：当前数据
 * 返回值：
 *      bool：判断结果，true -- 相同，false -- 不相同
 * ***********************************************************************/
bool TaskManager::isTestDataExistInVector(const QVector<QSharedPointer<TaskModeViewNS::BaseTestDataInfo> > &qvtTestDataInfos,
                                          TaskModeViewNS::TestType eTestType,
                                          const QSharedPointer<TaskModeViewNS::BaseTestDataInfo> &qspTestDataInfo)
{
    Q_UNUSED(eTestType);
    bool bRet = false;
    //bool bMatched = false;

    for(int i = 0, iSize = qvtTestDataInfos.size(); i < iSize; ++i)
    {
        if(qvtTestDataInfos[i].data() && qspTestDataInfo.data())
        {
            if(qvtTestDataInfos[i].data()->qstrUUID == qspTestDataInfo.data()->qstrUUID)
            {
                bRet = true;
                break;
            }
        }

        /*switch(eTestType)
        {
        case TaskModeViewNS::TYPE_PD:
        {
            TaskModeViewNS::PDDataInfo *pOriDataInfo = static_cast<TaskModeViewNS::PDDataInfo *>(qvtTestDataInfos[i].data());
            TaskModeViewNS::PDDataInfo *pCurDataInfo = static_cast<TaskModeViewNS::PDDataInfo *>(qspTestDataInfo.data());

            if(pOriDataInfo && pCurDataInfo)
            {
                if(pOriDataInfo->ePhaseType == pCurDataInfo->ePhaseType
                        && pOriDataInfo->ePDType == pCurDataInfo->ePDType
                        && pOriDataInfo->qstrDataFileName == pCurDataInfo->qstrDataFileName)
                {
                    bRet = true;
                    bMatched = true;
                }
            }

            break;
        }
        case TaskModeViewNS::TYPE_INFRARED:
        {
            TaskModeViewNS::InfraredDataInfo *pOriDataInfo = static_cast<TaskModeViewNS::InfraredDataInfo *>(qvtTestDataInfos[i].data());
            TaskModeViewNS::InfraredDataInfo *pCurDataInfo = static_cast<TaskModeViewNS::InfraredDataInfo *>(qspTestDataInfo.data());

            if(pOriDataInfo && pCurDataInfo)
            {
                if(pOriDataInfo->ePhaseType == pCurDataInfo->ePhaseType
                        && pOriDataInfo->qstrDataFileName == pCurDataInfo->qstrDataFileName)
                {
                    bRet = true;
                    bMatched = true;
                }
            }

            break;
        }
        case TaskModeViewNS::TYPE_CURRENT:
        {
            TaskModeViewNS::GroundCurrentDataInfo *pOriDataInfo = static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(qvtTestDataInfos[i].data());
            TaskModeViewNS::GroundCurrentDataInfo *pCurDataInfo = static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(qspTestDataInfo.data());

            if(pOriDataInfo && pCurDataInfo)
            {
                if(pOriDataInfo->ePhaseType == pCurDataInfo->ePhaseType)
                {
                    bRet = true;
                    bMatched = true;
                }
            }

            break;
        }
        default:
        {
            break;
        }

        }

        if(bMatched)
        {
            break;
        }*/
    }

    return bRet;
}

/*************************************************************************
 * 功能：添加JSON任务到集合中
 * 输入参数：
 *      stTaskInfo：任务信息
 * ***********************************************************************/
void TaskManager::addJsonTaskInfoToVector(const TaskModeViewNS::CustomTaskInfo &stTaskInfo)
{
    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        m_qvt4TaskInfos.append(stTaskInfo);
        m_qmt4TaskInfos.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：添加JSON的子任务到集合中
 * 输入参数：
 *      stTaskInfo：任务信息
 * ***********************************************************************/
void TaskManager::addJsonSubTaskInfoToVector(const TaskModeViewNS::CustomTaskInfo &stTaskInfo)
{
    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        int iIndex = m_qvt4TaskInfos.indexOf(stTaskInfo);
        if(0 <= iIndex && iIndex < m_qvt4TaskInfos.size())
        {
            //已存在主任务，添加子任务
            for(int i = 0, iCirCnt = stTaskInfo.qvtCircuitInfos.size(); i < iCirCnt; ++i)
            {
                if(m_qvt4TaskInfos[iIndex].qvtCircuitInfos.contains(stTaskInfo.qvtCircuitInfos[i]))
                {
                    //子任务已存在，不添加
                    logWarning(QString("sub task (%1) is exist in main task (%2).").arg(stTaskInfo.qvtCircuitInfos[i].qstrId).arg(stTaskInfo.qstrId));
                }
                else
                {
                    //子任务不存在
                    m_qvt4TaskInfos[iIndex].qvtCircuitInfos.append(stTaskInfo.qvtCircuitInfos[i]);
                }
            }
        }
        else
        {
            //不存在
            m_qvt4TaskInfos.append(stTaskInfo);
        }

        m_qmt4TaskInfos.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：合并JSON任务到集合中
 * 输入参数：
 *      stTaskInfo：任务信息
 * ***********************************************************************/
void TaskManager::mergeJsonTaskInfo(const TaskModeViewNS::CustomTaskInfo &stTaskInfo)
{
    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        int iIndex = m_qvt4TaskInfos.indexOf(stTaskInfo);
        if(0 <= iIndex && iIndex < m_qvt4TaskInfos.size())
        {
            if(m_qvt4TaskInfos[iIndex].qstrName != stTaskInfo.qstrName)
            {
                m_qvt4TaskInfos[iIndex].qstrName = stTaskInfo.qstrName;
            }

            if(m_qvt4TaskInfos[iIndex].qstrCreateTime != stTaskInfo.qstrCreateTime)
            {
                m_qvt4TaskInfos[iIndex].qstrCreateTime = stTaskInfo.qstrCreateTime;
            }

            if(m_qvt4TaskInfos[iIndex].qstrTestTime < stTaskInfo.qstrTestTime)
            {
                m_qvt4TaskInfos[iIndex].qstrTestTime = stTaskInfo.qstrTestTime;
            }

            if(!Module::dValEqual(m_qvt4TaskInfos[iIndex].dLoadCurrentVal, stTaskInfo.dLoadCurrentVal))
            {
                if(!Module::dValEqual(0, stTaskInfo.dLoadCurrentVal))
                {
                    m_qvt4TaskInfos[iIndex].dLoadCurrentVal = stTaskInfo.dLoadCurrentVal;
                }
            }

            for(int i = 0, iCirCnt = stTaskInfo.qvtCircuitInfos.size(); i < iCirCnt; ++i)
            {
                int iCirIndex = m_qvt4TaskInfos[iIndex].qvtCircuitInfos.indexOf(stTaskInfo.qvtCircuitInfos[i]);
                if(0 <= iCirIndex && iCirIndex < m_qvt4TaskInfos[iIndex].qvtCircuitInfos.size())
                {
                    //存在
                    if(m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qstrName != stTaskInfo.qvtCircuitInfos[i].qstrName)
                    {
                        m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qstrName = stTaskInfo.qvtCircuitInfos[i].qstrName;
                    }

                    if(m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].iTestSN != stTaskInfo.qvtCircuitInfos[i].iTestSN)
                    {
                        m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].iTestSN = stTaskInfo.qvtCircuitInfos[i].iTestSN;
                    }

                    for(int j = 0, iAsCnt = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos.size(); j < iAsCnt; ++j)
                    {
                        int iAsIndex = m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos.indexOf(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j]);
                        if(0 <= iAsIndex && iAsIndex < m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos.size())
                        {
                            //存在
                            if(m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qstrName != stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrName)
                            {
                                m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qstrName = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrName;
                            }

                            if(m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qstrRFIDCode != stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrRFIDCode)
                            {
                                m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qstrRFIDCode = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrRFIDCode;
                            }

                            if(m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qstrTestPosition != stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrTestPosition)
                            {
                                m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qstrTestPosition = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrTestPosition;
                            }

                            if(m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].iTestSN != stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].iTestSN)
                            {
                                m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].iTestSN = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].iTestSN;
                            }

                            if(m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qstrTestTime < stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrTestTime)
                            {
                                m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qstrTestTime = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qstrTestTime;
                            }

                            if(!Module::dValEqual(m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].dLoadCurrentVal, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].dLoadCurrentVal))
                            {
                                if(!Module::dValEqual(0, stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].dLoadCurrentVal))
                                {
                                    m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].dLoadCurrentVal = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].dLoadCurrentVal;
                                }
                            }

                            for(int q = 0, iPhaseCnt = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtPhaseTypes.size();
                                q < iPhaseCnt; ++q)
                            {
                                int iPhaseVal = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtPhaseTypes[q];
                                if(!(m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qvtPhaseTypes.contains(iPhaseVal)))
                                {
                                    m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qvtPhaseTypes.append(iPhaseVal);
                                }
                            }

                            QString qstrFileDir = FileOperUtil::getFileParentFolder(m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qstrTaskFilePath);

                            for(int k = 0, iTdCnt = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtTestDataInfos.size();
                                k < iTdCnt; ++k)
                            {
                                const QSharedPointer<TaskModeViewNS::BaseTestDataInfo> &qspDataInfo = stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].qvtTestDataInfos[k];
                                if(!qspDataInfo->qstrUUID.isEmpty())
                                {
                                    bool bFound = false;
                                    for(int l = 0, iOriTdCnt = m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qvtTestDataInfos.size();
                                        l < iOriTdCnt; ++l)
                                    {
                                        const QSharedPointer<TaskModeViewNS::BaseTestDataInfo> &qspOriDataInfo = m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qvtTestDataInfos[l];
                                        if(qspDataInfo.data() && qspOriDataInfo.data())
                                        {
                                            if(qspDataInfo.data()->qstrUUID == qspOriDataInfo.data()->qstrUUID)
                                            {
                                                switch(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j].eType)
                                                {
                                                case TaskModeViewNS::TYPE_PD:
                                                {
                                                    TaskModeViewNS::PDDataInfo *pPDDataInfo = static_cast<TaskModeViewNS::PDDataInfo *>(qspDataInfo.data());
                                                    if(pPDDataInfo)
                                                    {
                                                        TaskModeViewNS::PDDataInfo *pOriDataInfo = static_cast<TaskModeViewNS::PDDataInfo *>(qspOriDataInfo.data());
                                                        if(pOriDataInfo)
                                                        {
                                                            if(pOriDataInfo->ePhaseType == pPDDataInfo->ePhaseType
                                                                    && pOriDataInfo->ePDType == pPDDataInfo->ePDType)
                                                            {
                                                                bFound = true;//找到

                                                                if(pOriDataInfo->eUnit != pPDDataInfo->eUnit)
                                                                {
                                                                    pOriDataInfo->eUnit = pPDDataInfo->eUnit;
                                                                }

                                                                if(!Module::dValEqual(pOriDataInfo->dPDVal, pPDDataInfo->dPDVal))
                                                                {
                                                                    pOriDataInfo->dPDVal = pPDDataInfo->dPDVal;
                                                                }

                                                                if(pOriDataInfo->qstrDiagResult != pPDDataInfo->qstrDiagResult
                                                                        && !(pPDDataInfo->qstrDiagResult.isEmpty()))
                                                                {
                                                                    pOriDataInfo->qstrDiagResult = pPDDataInfo->qstrDiagResult;
                                                                }

                                                                if(pOriDataInfo->qstrBGFileName != pPDDataInfo->qstrBGFileName
                                                                        && !(pPDDataInfo->qstrBGFileName.isEmpty()))
                                                                {
                                                                    //删除之前的数据文件
                                                                    QString qstrFilePath = qstrFileDir + "/" + pOriDataInfo->qstrBGFileName;
                                                                    FileOperUtil::deleteFile(qstrFilePath);
                                                                    pOriDataInfo->qstrBGFileName = pPDDataInfo->qstrBGFileName;
                                                                }

                                                                if(pOriDataInfo->qstrDataFileName != pPDDataInfo->qstrDataFileName
                                                                        && !(pPDDataInfo->qstrDataFileName.isEmpty()))
                                                                {
                                                                    //删除之前的数据文件
                                                                    QString qstrFilePath = qstrFileDir + "/" + pOriDataInfo->qstrDataFileName;
                                                                    FileOperUtil::deleteFile(qstrFilePath);
                                                                    pOriDataInfo->qstrDataFileName = pPDDataInfo->qstrDataFileName;
                                                                }
                                                            }
                                                        }
                                                    }

                                                    break;
                                                }
                                                case TaskModeViewNS::TYPE_INFRARED:
                                                {
                                                    TaskModeViewNS::InfraredDataInfo *pIRDataInfo = static_cast<TaskModeViewNS::InfraredDataInfo *>(qspDataInfo.data());
                                                    if(pIRDataInfo)
                                                    {
                                                        TaskModeViewNS::InfraredDataInfo *pOriDataInfo = static_cast<TaskModeViewNS::InfraredDataInfo *>(qspOriDataInfo.data());
                                                        if(pOriDataInfo)
                                                        {
                                                            if(pOriDataInfo->ePhaseType == pIRDataInfo->ePhaseType)
                                                            {
                                                                bFound = true;//找到
                                                                if(pOriDataInfo->eUnit != pIRDataInfo->eUnit)
                                                                {
                                                                    pOriDataInfo->eUnit = pIRDataInfo->eUnit;
                                                                }

                                                                if(!Module::dValEqual(pOriDataInfo->dEnvTemperVal, pIRDataInfo->dEnvTemperVal))
                                                                {
                                                                    pOriDataInfo->dEnvTemperVal = pIRDataInfo->dEnvTemperVal;
                                                                }

                                                                if(!Module::dValEqual(pOriDataInfo->dMaxTemperVal, pIRDataInfo->dMaxTemperVal))
                                                                {
                                                                    pOriDataInfo->dMaxTemperVal = pIRDataInfo->dMaxTemperVal;
                                                                }

                                                                if(!Module::dValEqual(pOriDataInfo->dMinTemperVal, pIRDataInfo->dMinTemperVal))
                                                                {
                                                                    pOriDataInfo->dMinTemperVal = pIRDataInfo->dMinTemperVal;
                                                                }

                                                                if(pOriDataInfo->qstrDiagResult != pIRDataInfo->qstrDiagResult
                                                                        && !(pIRDataInfo->qstrDiagResult.isEmpty()))
                                                                {
                                                                    pOriDataInfo->qstrDiagResult = pIRDataInfo->qstrDiagResult;
                                                                }

                                                                if(pOriDataInfo->qstrPicFileName != pIRDataInfo->qstrPicFileName
                                                                        && !(pIRDataInfo->qstrPicFileName.isEmpty()))
                                                                {
                                                                    //删除之前的数据文件
                                                                    QString qstrFilePath = qstrFileDir + "/" + pOriDataInfo->qstrPicFileName;
                                                                    FileOperUtil::deleteFile(qstrFilePath);
                                                                    pOriDataInfo->qstrPicFileName = pIRDataInfo->qstrPicFileName;
                                                                }

                                                                if(pOriDataInfo->qstrDataFileName != pIRDataInfo->qstrDataFileName
                                                                        && !(pIRDataInfo->qstrDataFileName.isEmpty()))
                                                                {
                                                                    //删除之前的数据文件
                                                                    QString qstrFilePath = qstrFileDir + "/" + pOriDataInfo->qstrDataFileName;
                                                                    FileOperUtil::deleteFile(qstrFilePath);
                                                                    pOriDataInfo->qstrDataFileName = pIRDataInfo->qstrDataFileName;
                                                                }
                                                            }
                                                        }
                                                    }

                                                    break;
                                                }
                                                case TaskModeViewNS::TYPE_CURRENT:
                                                {
                                                    TaskModeViewNS::GroundCurrentDataInfo *pGCDataInfo = static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(qspDataInfo.data());
                                                    if(pGCDataInfo)
                                                    {
                                                        TaskModeViewNS::GroundCurrentDataInfo *pOriDataInfo = static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(qspOriDataInfo.data());
                                                        if(pOriDataInfo)
                                                        {
                                                            if(pOriDataInfo->ePhaseType == pGCDataInfo->ePhaseType)
                                                            {
                                                                bFound = true;//找到
                                                                if(!Module::dValEqual(pOriDataInfo->dCurrentVal, pGCDataInfo->dCurrentVal))
                                                                {
                                                                    pOriDataInfo->dCurrentVal = pGCDataInfo->dCurrentVal;
                                                                }
                                                                if(pOriDataInfo->qstrDiagResult != pGCDataInfo->qstrDiagResult
                                                                        && !(pGCDataInfo->qstrDiagResult.isEmpty()))
                                                                {
                                                                    pOriDataInfo->qstrDiagResult = pGCDataInfo->qstrDiagResult;
                                                                }
                                                                if(pOriDataInfo->qstrDataFileName != pGCDataInfo->qstrDataFileName
                                                                        && !(pGCDataInfo->qstrDataFileName.isEmpty()))
                                                                {
                                                                    pOriDataInfo->qstrDataFileName = pGCDataInfo->qstrDataFileName;
                                                                }
                                                            }
                                                        }
                                                    }

                                                    break;
                                                }
                                                default:
                                                {
                                                    break;
                                                }

                                                }

                                                break;
                                            }
                                        }
                                    }

                                    if(!bFound)
                                    {
                                        if(qspDataInfo.data() && !(qspDataInfo.data()->qstrUUID.isEmpty()))
                                        {
                                            m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos[iAsIndex].qvtTestDataInfos.append(qspDataInfo);
                                        }
                                    }
                                }
                                else
                                {
                                    logError("test data uuid info is empty.");
                                }
                            }
                        }
                        else
                        {
                            //不存在
                            m_qvt4TaskInfos[iIndex].qvtCircuitInfos[iCirIndex].qvtAssetInfos.append(stTaskInfo.qvtCircuitInfos[i].qvtAssetInfos[j]);
                        }
                    }
                }
                else
                {
                    //不存在
                    m_qvt4TaskInfos[iIndex].qvtCircuitInfos.append(stTaskInfo.qvtCircuitInfos[i]);
                }
            }

            QVector<QByteArray> qbaJsonInfos;
            qbaJsonInfos.clear();

            if(!packageJsonTask(m_qvt4TaskInfos[iIndex], qbaJsonInfos))
            {
                logError("package json task info failed.");
            }
        }

        m_qmt4TaskInfos.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：更新JSON任务到集合中
 * 输入参数：
 *      stTaskInfo：任务信息
 * ***********************************************************************/
void TaskManager::updateJsonTaskInfo(const TaskModeViewNS::CustomTaskInfo &stTaskInfo)
{
    QVector<QByteArray> qbaJsonInfos;
    qbaJsonInfos.clear();

    if(!packageJsonTask(stTaskInfo, qbaJsonInfos))
    {
        logError("package json task info failed.");
    }

    return;
}

/*************************************************************************
 * 功能：是否有JSON任务处于测试中
 * 返回值：
 *      bool：状态，true -- 测试中，false -- 未测试中
 * ***********************************************************************/
bool TaskManager::isJSONTaskTesting()
{
    return m_bJSONTesting;
}

/*************************************************************************
 * 功能：设置JSON任务已测试
 * 输入参数：
 *      qstrTaskId：主任务ID
 * ***********************************************************************/
void TaskManager::setJSONTaskTested(QString qstrTaskId, bool bTested)
{
    TaskModeViewNS::CustomTaskInfo stTaskInfo;
    stTaskInfo.qstrId = qstrTaskId;
    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_qvt4TaskInfos.contains(stTaskInfo))
        {
            QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
                    + stTaskInfo.qstrId + "/" + stTaskInfo.qstrId + ".ini";

            QSettings sets(qstrIniFilePath, QSettings::IniFormat);
            sets.beginGroup(GrpTestState);
            sets.setValue(KeyIsTested, bTested);
            sets.endGroup();
            sets.sync();
        }

        m_qmt4TaskInfos.unlock();
    }

    if(bTested)
    {
        emit sigCurJSONMainTaskTested(qstrTaskId, getJSONTaskTestVal(qstrTaskId));
    }
    else
    {
        emit sigCurJSONMainTaskTested(qstrTaskId, TaskModeViewNS::g_iUntestVal);
    }

    return;
}

/*************************************************************************
 * 功能：设置JSON子任务已测试
 * 输入参数：
 *      qstrSubTaskId：子任务ID
 * ***********************************************************************/
void TaskManager::setJSONSubTaskTested(QString qstrSubTaskId, bool bTested)
{
    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurTaskInfo)
        {
            QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
                    + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

            TaskModeViewNS::CircuitInfo stCircuitInfo;
            stCircuitInfo.qstrId = qstrSubTaskId;
            if(m_pCurTaskInfo->qvtCircuitInfos.contains(stCircuitInfo))
            {
                //存在才写入
                QSettings sets(qstrIniFilePath, QSettings::IniFormat);
                sets.beginGroup(QString("%1").arg(qstrSubTaskId));
                sets.setValue(KeyIsTested, bTested);
                sets.endGroup();
                sets.sync();
            }
        }

        m_qmt4CurTaskInfo.unlock();
    }

    QString qstrId = getUnfinishedJSONAssetId();
    if(qstrId.isEmpty())
    {
        setJSONSubInfoFinished(m_qstrSubTaskId);
        emit sigCurJSONSubTaskTested(qstrSubTaskId, TaskModeViewNS::g_iTestedVal);
    }
    else if(!bTested)
    {
        setJSONSubInfoFinished(m_qstrSubTaskId, false);
        emit sigCurJSONSubTaskTested(qstrSubTaskId, TaskModeViewNS::g_iUntestVal);
    }
    else
    {
        setJSONSubInfoFinished(m_qstrSubTaskId, false);
        emit sigCurJSONSubTaskTested(qstrSubTaskId, TaskModeViewNS::g_iTestingVal);
    }

    QString qstrCirId = getUnfinishedJSONCircuitId();
    if(qstrCirId.isEmpty())
    {
        setJSONTaskFinished();
    }

    return;
}

/*************************************************************************
 * 功能：设置JSON接头的设备已测试
 * 输入参数：
 *      qstrAssetId：接头的设备ID
 * ***********************************************************************/
void TaskManager::setJSONAssetTested(QString qstrAssetId, bool bTested)
{
    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurCircuitInfo)
        {
            QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
                    + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

            TaskModeViewNS::AssetInfo stAssetInfo;
            stAssetInfo.qstrId = qstrAssetId;
            stAssetInfo.eType = m_eTestType;

            if(m_pCurCircuitInfo->qvtAssetInfos.contains(stAssetInfo))
            {
                //存在才写入
                QSettings sets(qstrIniFilePath, QSettings::IniFormat);
                sets.beginGroup(QString("%1_%2").arg(m_qstrSubTaskId).arg(qstrAssetId));
                sets.setValue(KeyIsTested, bTested);
                sets.endGroup();
                sets.sync();
            }
        }

        m_qmt4CurCircuitInfo.unlock();
    }

    if((TaskModeViewNS::PHASE_INVALID == getAssetUnfinishedPhaseInfo()) && (isJSONTaskLoadCurrentTested(m_qstrMainTaskId)))
    {
        setJSONAssetFinished(qstrAssetId);
        emit sigCurJSONAssetTested(qstrAssetId, TaskModeViewNS::g_iTestedVal);
    }
    else if(!bTested)
    {
        setJSONAssetFinished(qstrAssetId, false);
        emit sigCurJSONAssetTested(qstrAssetId, TaskModeViewNS::g_iUntestVal);
    }
    else
    {
        setJSONAssetFinished(qstrAssetId, false);
        emit sigCurJSONAssetTested(qstrAssetId, TaskModeViewNS::g_iTestingVal);
    }


    return;
}

/*************************************************************************
 * 功能：设置JSON任务的局放背景已测试
 * ***********************************************************************/
void TaskManager::setJSONTaskBGNTested()
{
    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurCircuitInfo)
        {
            QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
                    + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

            TaskModeViewNS::AssetInfo stAssetInfo;
            stAssetInfo.qstrId = m_qstrAssetId;
            stAssetInfo.eType = m_eTestType;

            if(m_pCurCircuitInfo->qvtAssetInfos.contains(stAssetInfo))
            {
                //存在才写入
                QSettings sets(qstrIniFilePath, QSettings::IniFormat);
                sets.beginGroup(QString("%1_%2_%3_%4").arg(m_qstrSubTaskId).arg(m_qstrAssetId).arg(m_eTestType).arg(m_ePDType));
                sets.setValue(KeyBGNIsTested, true);
                sets.endGroup();
                sets.sync();
            }
        }

        m_qmt4CurCircuitInfo.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：设置JSON任务负荷电流已测试
 * 输入参数：
 *      qstrTaskId：主任务ID
 * ***********************************************************************/
void TaskManager::setJSONTaskLoadCurrentTested(QString qstrTaskId)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + qstrTaskId + "/" + qstrTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1_%2_%3_%4").arg(qstrTaskId).arg(m_qstrSubTaskId).arg(TaskModeViewNS::TYPE_LOAD_CURRENT).arg(TaskModeViewNS::PD_NOT));
    sets.setValue(KeyIsFinished, true);
    sets.endGroup();
    sets.sync();

    return;
}

/*************************************************************************
 * 功能：设置JSON任务的已完成测试
 * ***********************************************************************/
void TaskManager::setJSONTaskFinished(bool bTested)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1").arg(m_qstrMainTaskId));
    sets.setValue(KeyIsFinished, bTested);
    sets.endGroup();
    sets.sync();

    return;
}

/*************************************************************************
 * 功能：设置JSON任务的已完成测试
 * 输入参数：
 *      qstrId：ID标识
 * ***********************************************************************/
void TaskManager::setJSONSubInfoFinished(QString qstrId, bool bTested)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1").arg(qstrId));
    sets.setValue(KeyIsFinished, bTested);
    sets.endGroup();
    sets.sync();

    return;
}

/*************************************************************************
 * 功能：设置JSON任务的设备测试状态
 * 输入参数：
 *      qstrId：ID标识
 * ***********************************************************************/
void TaskManager::setJSONAssetFinished(QString qstrId, bool bTested)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1_%2").arg(m_qstrSubTaskId).arg(qstrId));
    sets.setValue(KeyIsFinished, bTested);
    sets.endGroup();
    sets.sync();

    return;
}

/*************************************************************************
 * 功能：设置JSON任务的已完成测试
 * 输入参数：
 *      qstrId：ID标识
 *      ePhaseType：相位类型
 * ***********************************************************************/
void TaskManager::setJSONPhaseFinished(QString qstrId, TaskModeViewNS::PhaseType ePhaseType)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1_%2_%3_%4_%5").arg(m_qstrSubTaskId).arg(qstrId).arg(m_eTestType).arg(m_ePDType).arg(ePhaseType));
    sets.setValue(KeyIsFinished, true);
    sets.endGroup();
    sets.sync();

    return;
}

/*************************************************************************
 * 功能：删除文件时设置JSON任务的未完成测试
 * 输入参数：
 *      qstrId：ID标识
 *      ePhaseType：相位类型
 * ***********************************************************************/
void TaskManager::setJSONPhaseUnFinished(QString qstrId, TaskModeViewNS::PhaseType ePhaseType)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1_%2_%3_%4_%5").arg(m_qstrSubTaskId).arg(qstrId).arg(m_eTestType).arg(m_ePDType).arg(ePhaseType));
    sets.setValue(KeyIsFinished, false);
    sets.endGroup();
    sets.sync();

    //更新当前设备测试状态
    updateAssetTestedState(qstrId);
    updateSubTaskTestedState(m_qstrSubTaskId);
    updateTaskTestedState(m_qstrMainTaskId);

    return;
}

/*************************************************************************
 * 功能：JSON任务是否已测试
 * 输入参数：
 *      qstrTaskId：主任务ID
 * 返回值：
 *      bool：测试状态，true -- 已测试，false -- 未测试
 * ***********************************************************************/
bool TaskManager::isJSONTaskTested(QString qstrTaskId)
{
    bool bRet = false;

    TaskModeViewNS::CustomTaskInfo stTaskInfo;
    stTaskInfo.qstrId = qstrTaskId;
    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_qvt4TaskInfos.contains(stTaskInfo))
        {
            QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
                    + stTaskInfo.qstrId + "/" + stTaskInfo.qstrId + ".ini";

            QSettings sets(qstrIniFilePath, QSettings::IniFormat);
            sets.beginGroup(GrpTestState);
            bRet = sets.value(KeyIsTested).toBool();
            sets.endGroup();
        }

        m_qmt4TaskInfos.unlock();
    }

    return bRet;
}

/*************************************************************************
 * 功能：JSON子任务是否已测试
 * 输入参数：
 *      qstrSubTaskId：子任务ID
 * 返回值：
 *      bool：测试状态，true -- 已测试，false -- 未测试
 * ***********************************************************************/
bool TaskManager::isJSONSubTaskTested(QString qstrSubTaskId)
{
    bool bRet = false;

    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurTaskInfo)
        {
            QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
                    + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

            TaskModeViewNS::CircuitInfo stCircuitInfo;
            stCircuitInfo.qstrId = qstrSubTaskId;

            if(m_pCurTaskInfo->qvtCircuitInfos.contains(stCircuitInfo))
            {
                QSettings sets(qstrIniFilePath, QSettings::IniFormat);
                sets.beginGroup(QString("%1").arg(qstrSubTaskId));
                bRet = sets.value(KeyIsTested).toBool();
                sets.endGroup();
            }
        }

        m_qmt4CurTaskInfo.unlock();
    }

    return bRet;
}

/*************************************************************************
 * 功能：JSON接头的设备是否已测试
 * 输入参数：
 *      qstrAssetId：接头的设备ID
 * 返回值：
 *      bool：测试状态，true -- 已测试，false -- 未测试
 * ***********************************************************************/
bool TaskManager::isJSONAssetTested(QString qstrAssetId)
{
    bool bRet = false;

    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurCircuitInfo)
        {
            QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
                    + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

            TaskModeViewNS::AssetInfo stAssetInfo;
            stAssetInfo.qstrId = qstrAssetId;
            stAssetInfo.eType = m_eTestType;

            if(m_pCurCircuitInfo->qvtAssetInfos.contains(stAssetInfo))
            {
                QSettings sets(qstrIniFilePath, QSettings::IniFormat);
                sets.beginGroup(QString("%1_%2").arg(m_qstrSubTaskId).arg(qstrAssetId));
                bRet = sets.value(KeyIsTested).toBool();
                sets.endGroup();
            }
        }

        m_qmt4CurCircuitInfo.unlock();
    }

    return bRet;
}

/*************************************************************************
 * 功能：获取JSON任务的局放背景是否已测试
 * 返回值：
 *      bool：测试状态，true -- 已测试，false -- 未测试
 * ***********************************************************************/
bool TaskManager::isJSONTaskBGNTested()
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";
    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1_%2_%3_%4").arg(m_qstrSubTaskId).arg(m_qstrAssetId).arg(m_eTestType).arg(m_ePDType));
    bool bRet = sets.value(KeyBGNIsTested).toBool();
    sets.endGroup();

    return bRet;
}

/*************************************************************************
 * 功能：获取JSON设备的局放背景是否已测试
 * 返回值：
 *      bool：测试状态，true -- 已测试，false -- 未测试
 * ***********************************************************************/
bool TaskManager::isAssetJSONTaskBGNTested(QString qstrAssetId)
{
    bool bRet = true;
    for(int i = TaskModeViewNS::PD_AE; i < (TaskModeViewNS::PD_HFCT + 1); ++i)
    {
        QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
              + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";
        QSettings sets(qstrIniFilePath, QSettings::IniFormat);
        sets.beginGroup(QString("%1_%2_%3_%4").arg(m_qstrSubTaskId).arg(qstrAssetId).arg(TaskModeViewNS::TYPE_PD).arg(static_cast<TaskModeViewNS::PDType>(i)));
        if(!sets.value(KeyBGNIsTested).toBool())
        {
            bRet = false;
        }
        sets.endGroup();
    }

    return bRet;
}

/*************************************************************************
 * 功能：获取JSON设备的局放背景是否都未测试
 * 返回值：
 *      bool：测试状态，true -- 已测试，false -- 未测试
 * ***********************************************************************/
bool TaskManager::isAssetJSONTaskBGNUnTested(QString qstrAssetId)
{
    bool bRet = true;
    for(int i = TaskModeViewNS::PD_AE; i < (TaskModeViewNS::PD_HFCT + 1); ++i)
    {
        QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
              + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";
        QSettings sets(qstrIniFilePath, QSettings::IniFormat);
        sets.beginGroup(QString("%1_%2_%3_%4").arg(m_qstrSubTaskId).arg(qstrAssetId).arg(TaskModeViewNS::TYPE_PD).arg(static_cast<TaskModeViewNS::PDType>(i)));
        if(sets.value(KeyBGNIsTested).toBool())
        {
            bRet = false;
            break;
        }
        sets.endGroup();
    }

    return bRet;
}

/*************************************************************************
 * 功能：判断JSON任务的是否已完成测试
 * 返回值：
 *      bool：测试状态，true -- 已完成测试，false -- 未完成测试
 * ***********************************************************************/
bool TaskManager::isJSONTaskFinished()
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1").arg(m_qstrMainTaskId));
    bool bRet = sets.value(KeyIsFinished).toBool();
    sets.endGroup();

    return bRet;
}

/*************************************************************************
 * 功能：判断JSON任务的是否已完成测试
 * 返回值：
 *      bool：测试状态，true -- 已完成测试，false -- 未完成测试
 * ***********************************************************************/
bool TaskManager::isJSONTaskFinished(QString qstrId)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + qstrId + "/" + qstrId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1").arg(qstrId));
    bool bRet = sets.value(KeyIsFinished).toBool();
    sets.endGroup();

    return bRet;
}


/*************************************************************************
 * 功能：判断JSON任务的是否已完成测试
 * 输入参数：
 *      qstrId：任务ID
 *      eTestType：测试类型
 *      ePDType：局放类型
 * 返回值：
 *      bool：测试状态，true -- 已完成测试，false -- 未完成测试
 * ***********************************************************************/
bool TaskManager::isJSONTaskFinished(QString qstrId, TaskModeViewNS::TestType eTestType, TaskModeViewNS::PDType ePDType)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + qstrId + "/" + qstrId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1_%2_%3").arg(qstrId).arg(eTestType).arg(ePDType));
    bool bRet = sets.value(KeyIsFinished).toBool();
    sets.endGroup();

    return bRet;
}

/*************************************************************************
 * 功能：判断JSON子任务已完成测试
 * 输入参数：
 *      qstrId：ID标识
 * 返回值：
 *      bool：测试状态，true -- 已完成测试，false -- 未完成测试
 * ***********************************************************************/
bool TaskManager::isJSONSubInfoFinished(QString qstrId)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1").arg(qstrId));
    bool bRet = sets.value(KeyIsFinished).toBool();
    sets.endGroup();

    return bRet;
}

/*************************************************************************
 * 功能：判断JSON子任务的设备已完成测试
 * 输入参数：
 *      qstrId：ID标识
 * 返回值：
 *      bool：测试状态，true -- 已完成测试，false -- 未完成测试
 * ***********************************************************************/
bool TaskManager::isJSONAssetFinished(QString qstrId)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1_%2").arg(m_qstrSubTaskId).arg(qstrId));
    bool bRet = sets.value(KeyIsFinished).toBool();
    sets.endGroup();

    return bRet;
}

/*************************************************************************
 * 功能：判断JSON设备下的已完成测试（内部使用）
 * 输入参数：
 *      qstrId：ID标识
 *      ePhaseType：相位类型
 * 返回值：
 *      bool：测试状态，true -- 已完成测试，false -- 未完成测试
 * ***********************************************************************/
bool TaskManager::isJSONAssetPhaseFinished(QString qstrId, TaskModeViewNS::TestType eTestType, TaskModeViewNS::PDType ePDType, TaskModeViewNS::PhaseType ePhaseType)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1_%2_%3_%4_%5").arg(m_qstrSubTaskId).arg(qstrId).arg(eTestType).arg(ePDType).arg(ePhaseType));
    bool bRet = sets.value(KeyIsFinished).toBool();
    sets.endGroup();

    return bRet;
}

/*************************************************************************
 * 功能：判断JSON设备下的已完成测试（内部使用）
 * 输入参数：
 *      qstrId：ID标识
 *      ePhaseType：相位类型
 * 返回值：
 *      bool：测试状态，true -- 已完成测试，false -- 未完成测试
 * ***********************************************************************/
bool TaskManager::isJSONPhaseFinished(QString qstrId, TaskModeViewNS::PhaseType ePhaseType)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1_%2_%3_%4_%5").arg(m_qstrSubTaskId).arg(qstrId).arg(m_eTestType).arg(m_ePDType).arg(ePhaseType));
    bool bRet = sets.value(KeyIsFinished).toBool();
    sets.endGroup();

    return bRet;
}

/*************************************************************************
 * 功能：判断JSON任务负荷电流已测试
 * 输入参数：
 *      qstrTaskId：主任务ID
 * 返回值：
 *      bool：测试状态，true -- 已完成测试，false -- 未完成测试
 * ***********************************************************************/
bool TaskManager::isJSONTaskLoadCurrentTested(QString qstrTaskId)
{
    QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
            + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

    QSettings sets(qstrIniFilePath, QSettings::IniFormat);
    sets.beginGroup(QString("%1_%2_%3_%4").arg(m_qstrMainTaskId).arg(m_qstrSubTaskId).arg(TaskModeViewNS::TYPE_LOAD_CURRENT).arg(TaskModeViewNS::PD_NOT));
    bool bRet = sets.value(KeyIsFinished).toBool();
    sets.endGroup();

    return bRet;
}

/*************************************************************************
 * 功能：JSON任务测试状态值
 * 输入参数：
 *      qstrTaskId：主任务ID
 * 返回值：
 *      int：测试状态值，2 -- 测试中，1 -- 已测试，0 -- 未测试
 * ***********************************************************************/
int TaskManager::getJSONTaskTestVal(QString qstrTaskId)
{
    int iTestVal = TaskModeViewNS::g_iUntestVal;

    if(isJSONTaskTested(qstrTaskId))
    {
        iTestVal = TaskModeViewNS::g_iTestingVal;

        /*
        do
        {
            if(!isJSONTaskFinished(qstrTaskId, TaskModeViewNS::TYPE_LOAD_CURRENT, TaskModeViewNS::PD_NOT))
            {
                break;
            }

            if(!isJSONTaskFinished(qstrTaskId, TaskModeViewNS::TYPE_CURRENT, TaskModeViewNS::PD_NOT))
            {
                break;
            }

            if(!isJSONTaskFinished(qstrTaskId, TaskModeViewNS::TYPE_INFRARED, TaskModeViewNS::PD_NOT))
            {
                break;
            }

            if(!isJSONTaskFinished(qstrTaskId, TaskModeViewNS::TYPE_PD, TaskModeViewNS::PD_AE))
            {
                break;
            }

            if(!isJSONTaskFinished(qstrTaskId, TaskModeViewNS::TYPE_PD, TaskModeViewNS::PD_UHF))
            {
                break;
            }

            if(!isJSONTaskFinished(qstrTaskId, TaskModeViewNS::TYPE_PD, TaskModeViewNS::PD_HFCT))
            {
                break;
            }

            iTestVal = TaskModeViewNS::g_iTestedVal;

        }while(0);*/

        if(isJSONTaskFinished(qstrTaskId))
        {
            iTestVal = TaskModeViewNS::g_iTestedVal;
        }
    }

    return iTestVal;
}

/*************************************************************************
 * 功能：JSON子任务测试状态值
 * 输入参数：
 *      qstrTaskId：主任务ID
 * 返回值：
 *      int：测试状态值，2 -- 测试中，1 -- 已测试，0 -- 未测试
 * ***********************************************************************/
int TaskManager::getJSONSubTaskTestVal(QString qstrId)
{
    int iTestVal = TaskModeViewNS::g_iUntestVal;
    if(isJSONSubTaskTested(qstrId))
    {
        iTestVal = TaskModeViewNS::g_iTestingVal;

        if(isJSONSubInfoFinished(qstrId))
        {
            iTestVal = TaskModeViewNS::g_iTestedVal;
        }
    }

    return iTestVal;
}

/*************************************************************************
 * 功能：JSON设备测试状态值
 * 输入参数：
 *      qstrTaskId：主任务ID
 * 返回值：
 *      int：测试状态值，2 -- 测试中，1 -- 已测试，0 -- 未测试
 * ***********************************************************************/
int TaskManager::getJSONAssetTestVal(QString qstrId)
{
    int iTestVal = TaskModeViewNS::g_iUntestVal;
    if(isJSONAssetTested(qstrId))
    {
        iTestVal = TaskModeViewNS::g_iTestingVal;

        if(isJSONAssetFinished(qstrId))
        {
            iTestVal = TaskModeViewNS::g_iTestedVal;
        }
    }

    return iTestVal;
}

/*************************************************************************
 * 功能：保存当前JSON任务的局放背景检测数据文件名
 * ***********************************************************************/
void TaskManager::setJSONTaskBGNFileName(QString qstrFileName)
{
    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurCircuitInfo)
        {
            QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
                    + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

            TaskModeViewNS::AssetInfo stAssetInfo;
            stAssetInfo.qstrId = m_qstrAssetId;
            stAssetInfo.eType = m_eTestType;

            if(m_pCurCircuitInfo->qvtAssetInfos.contains(stAssetInfo))
            {
                //存在才写入
                QSettings sets(qstrIniFilePath, QSettings::IniFormat);
                sets.beginGroup(QString("%1_%2_%3").arg(m_qstrAssetId).arg(m_eTestType).arg(m_ePDType));
                sets.setValue(KeyBGNFileName, qstrFileName);
                sets.endGroup();
                sets.sync();
            }
        }

        m_qmt4CurCircuitInfo.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：获取当前JSON任务的局放背景检测数据文件名
 * ***********************************************************************/
QString TaskManager::getJSONTaskBGNFileName()
{
    QString qstrFileName = "";

    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurCircuitInfo)
        {
            QString qstrIniFilePath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/"
                    + m_qstrMainTaskId + "/" + m_qstrMainTaskId + ".ini";

            TaskModeViewNS::AssetInfo stAssetInfo;
            stAssetInfo.qstrId = m_qstrAssetId;
            stAssetInfo.eType = m_eTestType;

            if(m_pCurCircuitInfo->qvtAssetInfos.contains(stAssetInfo))
            {
                //存在才写入
                QSettings sets(qstrIniFilePath, QSettings::IniFormat);
                sets.beginGroup(QString("%1_%2_%3").arg(m_qstrAssetId).arg(m_eTestType).arg(m_ePDType));
                qstrFileName = sets.value(KeyBGNFileName).toString();
                sets.endGroup();
            }
        }

        m_qmt4CurCircuitInfo.unlock();
    }

    return qstrFileName;
}

/*************************************************************************
 * 功能：获取当前JSON任务信息
 * 返回值：
 *      TaskModeViewNS::CustomTaskInfo*：当前JSON任务信息指针
 * ***********************************************************************/
TaskModeViewNS::CustomTaskInfo* TaskManager::getJSONCurTaskInfo()
{
    TaskModeViewNS::CustomTaskInfo* pCurTaskInfo = NULL;

    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        pCurTaskInfo = m_pCurTaskInfo;
        m_qmt4CurTaskInfo.unlock();
    }

    return pCurTaskInfo;
}

/*************************************************************************
 * 功能：获取当前JSON任务线路信息
 * 返回值：
 *      TaskModeViewNS::CircuitInfo*：当前JSON任务线路信息指针
 * ***********************************************************************/
TaskModeViewNS::CircuitInfo* TaskManager::getJSONCurCircuitInfo()
{
    TaskModeViewNS::CircuitInfo* pCurCircuitInfo = NULL;

    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        pCurCircuitInfo = m_pCurCircuitInfo;
        m_qmt4CurCircuitInfo.unlock();
    }

    return pCurCircuitInfo;
}

/*************************************************************************
 * 功能：获取当前JSON任务设备信息
 * 返回值：
 *      TaskModeViewNS::AssetInfo*：当前JSON任务设备信息指针
 * ***********************************************************************/
TaskModeViewNS::AssetInfo* TaskManager::getJSONCurAssetInfo()
{
    TaskModeViewNS::AssetInfo* pCurAssetInfo = NULL;

    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        pCurAssetInfo = m_pCurAssetInfo;
        m_qmt4CurAssetInfo.unlock();
    }

    return pCurAssetInfo;
}

/*************************************************************************
 * 功能：退出JSON任务检测
 * ***********************************************************************/
void TaskManager::exitJSONTask()
{
    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        m_qstrMainTaskId = "";
        m_pCurTaskInfo = NULL;
        m_qmt4CurTaskInfo.unlock();
    }

    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        m_qstrSubTaskId = "";
        m_pCurCircuitInfo = NULL;
        m_qmt4CurCircuitInfo.unlock();
    }

    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        m_qstrAssetId = "";
        m_pCurAssetInfo = NULL;
        m_qmt4CurAssetInfo.unlock();
    }

    m_bJSONTesting = false;

    return;
}

/*************************************************************************
 * 功能：设置JSON主任务ID
 * 输入参数：
 *      qstrMainId：主任务ID
 * ***********************************************************************/
void TaskManager::setJSONMainTaskId(QString qstrMainId)
{
    m_bJSONTesting = true;
    m_qstrMainTaskId = qstrMainId;

    TaskModeViewNS::CustomTaskInfo stTaskInfo;
    stTaskInfo.qstrId = m_qstrMainTaskId;
    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        int iTaskIndex = m_qvt4TaskInfos.indexOf(stTaskInfo);
        if(0 <= iTaskIndex && iTaskIndex < m_qvt4TaskInfos.size())
        {
            if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
            {
                m_pCurTaskInfo = &m_qvt4TaskInfos[iTaskIndex];
                m_qmt4CurTaskInfo.unlock();
            }
        }

        m_qmt4TaskInfos.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：获取JSON主任务ID
 * 返回值：
 *      QString：主任务ID
 * ***********************************************************************/
QString TaskManager::getJSONMainTaskId()
{
    return m_qstrMainTaskId;
}

/*************************************************************************
 * 功能：设置JSON子任务ID
 * 输入参数：
 *      qstrSubId：子任务ID
 * ***********************************************************************/
void TaskManager::setJSONSubTaskId(QString qstrSubId)
{
    m_qstrSubTaskId = qstrSubId;

    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurTaskInfo)
        {
            TaskModeViewNS::CircuitInfo stCircuitInfo;
            stCircuitInfo.qstrId = m_qstrSubTaskId;

            int iIndex = m_pCurTaskInfo->qvtCircuitInfos.indexOf(stCircuitInfo);
            if(0 <= iIndex && iIndex < m_pCurTaskInfo->qvtCircuitInfos.size())
            {
                if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
                {
                    m_pCurCircuitInfo = &(m_pCurTaskInfo->qvtCircuitInfos[iIndex]);

                    //如果没有同测试类型的设备时，需要添加同种测试类型的设备，临时变量
                    QVector<TaskModeViewNS::AssetInfo> qvtAssetInfos = m_pCurCircuitInfo->qvtAssetInfos;
                    for(int i = 0, iSize = qvtAssetInfos.size(); i < iSize; ++i)
                    {
                        qvtAssetInfos[i].eType = m_eTestType;
                        qvtAssetInfos[i].qvtTestDataInfos.clear();

                        if(!(m_pCurCircuitInfo->qvtAssetInfos.contains(qvtAssetInfos[i])))
                        {
                            qvtAssetInfos[i].iTestSN = m_pCurCircuitInfo->qvtAssetInfos.size();
                            m_pCurCircuitInfo->qvtAssetInfos.append(qvtAssetInfos[i]);
                            log_debug("add asset info (%s) test type: %d.", qvtAssetInfos[i].qstrId.toLatin1().data(), qvtAssetInfos[i].eType);
                        }
                    }

                    m_qmt4CurCircuitInfo.unlock();
                }
            }
        }
        else
        {
            logError("current json task info pointer is NULL.");
        }

        m_qmt4CurTaskInfo.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：获取JSON子任务ID
 * 返回值：
 *      QString：子任务ID
 * ***********************************************************************/
QString TaskManager::getJSONSubTaskId()
{
    return m_qstrSubTaskId;
}

/*************************************************************************
 * 功能：设置JSON子任务设备ID
 * 输入参数：
 *      qstrAssetId：子任务设备ID
 * ***********************************************************************/
void TaskManager::setJSONAssetId(QString qstrAssetId)
{
    m_qstrAssetId = qstrAssetId;

    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurCircuitInfo)
        {
            TaskModeViewNS::AssetInfo stAssetInfo;
            stAssetInfo.qstrId = m_qstrAssetId;
            stAssetInfo.eType = m_eTestType;

            int iIndex = m_pCurCircuitInfo->qvtAssetInfos.indexOf(stAssetInfo);
            if(0 <= iIndex && iIndex < m_pCurCircuitInfo->qvtAssetInfos.size())
            {
                //存在，直接设置
                if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
                {
                    m_pCurAssetInfo = &(m_pCurCircuitInfo->qvtAssetInfos[iIndex]);
                    m_qmt4CurAssetInfo.unlock();
                }
            }
            else
            {
                //不存在，添加相同设备ID的不同测试类型的设备
                bool bFound = false;
                for(int i = 0, iAsSize = m_pCurCircuitInfo->qvtAssetInfos.size();
                    i < iAsSize; ++i)
                {
                    if(m_qstrAssetId == m_pCurCircuitInfo->qvtAssetInfos[i].qstrId)
                    {
                        stAssetInfo = m_pCurCircuitInfo->qvtAssetInfos[i];
                        bFound = true;
                        break;
                    }
                }

                if(bFound)
                {
                    stAssetInfo.qstrId = m_qstrAssetId;
                    stAssetInfo.eType = m_eTestType;
                    stAssetInfo.iTestSN = m_pCurCircuitInfo->qvtAssetInfos.size();
                    stAssetInfo.qvtTestDataInfos.clear();//擦除旧的数据信息

                    m_pCurCircuitInfo->qvtAssetInfos.append(stAssetInfo);//添加

                    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
                    {
                        iIndex = m_pCurCircuitInfo->qvtAssetInfos.indexOf(stAssetInfo);
                        if(0 <= iIndex && iIndex < m_pCurCircuitInfo->qvtAssetInfos.size())
                        {
                            m_pCurAssetInfo = &(m_pCurCircuitInfo->qvtAssetInfos[iIndex]);
                        }
                        else
                        {
                            logError(QString("task (%1) add asset (%2) test type (%3) in circuit (%4) failed.")
                                     .arg(m_qstrMainTaskId).arg(m_qstrAssetId).arg(m_eTestType).arg(m_qstrSubTaskId));
                        }
                        m_qmt4CurAssetInfo.unlock();
                    }
                }
            }
        }
        else
        {
            logError("current json task circuit info pointer is NULL.");
        }

        m_qmt4CurCircuitInfo.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：获取JSON子任务设备ID
 * 返回值：
 *      QString：子任务设备ID
 * ***********************************************************************/
QString TaskManager::getJSONAssetId()
{
    return m_qstrAssetId;
}

/*************************************************************************
 * 功能：设置当前JSON任务的负荷电流
 * 输入参数：
 *      fLoadCurrentVal：负荷电流值
 * ***********************************************************************/
void TaskManager::setCurJSONTaskLoadCurrentVal(float fLoadCurrentVal)
{
    TaskModeViewNS::CustomTaskInfo stTaskInfo;
    stTaskInfo.qstrId = m_qstrMainTaskId;
    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        int iTaskIndex = m_qvt4TaskInfos.indexOf(stTaskInfo);
        if(0 <= iTaskIndex && iTaskIndex < m_qvt4TaskInfos.size())
        {
            m_qvt4TaskInfos[iTaskIndex].dLoadCurrentVal = static_cast<double>(fLoadCurrentVal);
            for(int i = 0, iCirSize = m_qvt4TaskInfos[iTaskIndex].qvtCircuitInfos.size();
                i < iCirSize; ++i)
            {
                for(int j = 0, iAsSize = m_qvt4TaskInfos[iTaskIndex].qvtCircuitInfos[i].qvtAssetInfos.size();
                    j < iAsSize; ++j)
                {
                    m_qvt4TaskInfos[iTaskIndex].qvtCircuitInfos[i].qvtAssetInfos[j].dLoadCurrentVal = m_qvt4TaskInfos[iTaskIndex].dLoadCurrentVal;
                }
            }

            QVector<QByteArray> qbaJsonInfos;
            qbaJsonInfos.clear();

            if(!packageJsonTask(m_qvt4TaskInfos[iTaskIndex], qbaJsonInfos))
            {
                logError("package json task info failed.");
            }
        }

        setJSONTaskLoadCurrentTested(m_qstrMainTaskId);

        m_qmt4TaskInfos.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：设置当前JSON任务的接地电流
 * 输入参数：
 *      ePhaseType：相别
 *      fGroundCurrentVal：接地电流值
 * ***********************************************************************/
void TaskManager::setCurJSONTaskGroundCurrentVal(const TaskModeViewNS::GroundCurrentDataInfo &stDataInfo)
{
    QString qstrCurTime = QDateTime::currentDateTime().toString("yyyy/MM/dd-HH:mm:ss.zzz");

    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurAssetInfo)
        {
            if(m_pCurAssetInfo->qvtTestDataInfos.isEmpty())
            {
                //数据为空，直接添加
                TaskModeViewNS::GroundCurrentDataInfo *pDataInfo = new TaskModeViewNS::GroundCurrentDataInfo();
                if(pDataInfo)
                {
                    pDataInfo->ePhaseType = stDataInfo.ePhaseType;
                    pDataInfo->qstrUUID = Module::getUUIDInfo();
                    pDataInfo->qstrDiagResult = getCurrentDiagRet(stDataInfo.dCurrentVal);
                    pDataInfo->dCurrentVal = static_cast<double>(stDataInfo.dCurrentVal);
                    pDataInfo->qstrDataFileName = stDataInfo.qstrDataFileName;

                    QSharedPointer<TaskModeViewNS::BaseTestDataInfo> qspDataInfo(pDataInfo);
                    m_pCurAssetInfo->qvtTestDataInfos.append(qspDataInfo);
                    m_pCurAssetInfo->qstrTestTime = qstrCurTime;
                }
            }
            else
            {
                //更新数据
                bool bExist = false;
                for(int i = 0, iTdSize = m_pCurAssetInfo->qvtTestDataInfos.size(); i < iTdSize; ++i)
                {
                    if(m_pCurAssetInfo->qvtTestDataInfos[i].data())
                    {
                        if(stDataInfo.ePhaseType == m_pCurAssetInfo->qvtTestDataInfos[i].data()->ePhaseType)
                        {
                            m_pCurAssetInfo->qvtTestDataInfos[i].data()->qstrDiagResult = getCurrentDiagRet(stDataInfo.dCurrentVal);
                            (static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(m_pCurAssetInfo->qvtTestDataInfos[i].data()))->dCurrentVal = static_cast<double>(stDataInfo.dCurrentVal);
                            (static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(m_pCurAssetInfo->qvtTestDataInfos[i].data()))->qstrDataFileName = stDataInfo.qstrDataFileName;
                            m_pCurAssetInfo->qstrTestTime = qstrCurTime;
                            bExist = true;
                            break;
                        }
                    }
                }

                if(!bExist)
                {
                    TaskModeViewNS::GroundCurrentDataInfo *pDataInfo = new TaskModeViewNS::GroundCurrentDataInfo();
                    if(pDataInfo)
                    {
                        pDataInfo->ePhaseType = stDataInfo.ePhaseType;
                        pDataInfo->qstrUUID = Module::getUUIDInfo();
                        pDataInfo->qstrDiagResult = getCurrentDiagRet(stDataInfo.dCurrentVal);
                        pDataInfo->dCurrentVal = static_cast<double>(stDataInfo.dCurrentVal);
                        pDataInfo->qstrDataFileName = stDataInfo.qstrDataFileName;

                        QSharedPointer<TaskModeViewNS::BaseTestDataInfo> qspDataInfo(pDataInfo);
                        m_pCurAssetInfo->qvtTestDataInfos.append(qspDataInfo);
                        m_pCurAssetInfo->qstrTestTime = qstrCurTime;
                    }
                }
            }
        }

        m_qmt4CurAssetInfo.unlock();
    }

    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurTaskInfo)
        {
            m_pCurTaskInfo->qstrTestTime = qstrCurTime;
            updateJsonTaskInfo(*m_pCurTaskInfo);
        }
        m_qmt4CurTaskInfo.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：保存当前JSON任务的红外检测数据
 * ***********************************************************************/
void TaskManager::setInfraredTestDataInfo(const TaskModeViewNS::InfraredDataInfo &stDataInfo)
{
    QString qstrCurTime = QDateTime::currentDateTime().toString("yyyy/MM/dd-HH:mm:ss.zzz");

    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurAssetInfo)
        {
            //支持单相别下测试多个数据
            TaskModeViewNS::InfraredDataInfo *pDataInfo = new TaskModeViewNS::InfraredDataInfo();
            if(pDataInfo)
            {
                pDataInfo->ePhaseType = m_ePhaseType;
                pDataInfo->qstrUUID = Module::getUUIDInfo();
                pDataInfo->qstrDiagResult = stDataInfo.qstrDiagResult;
                pDataInfo->eUnit = stDataInfo.eUnit;
                pDataInfo->dEnvTemperVal = stDataInfo.dEnvTemperVal;
                pDataInfo->dMaxTemperVal = stDataInfo.dMaxTemperVal;
                pDataInfo->dMinTemperVal = stDataInfo.dMinTemperVal;
                pDataInfo->qstrPicFileName = stDataInfo.qstrPicFileName;
                pDataInfo->qstrDataFileName = stDataInfo.qstrDataFileName;

                QSharedPointer<TaskModeViewNS::BaseTestDataInfo> qspDataInfo(pDataInfo);
                m_pCurAssetInfo->qvtTestDataInfos.append(qspDataInfo);
                m_pCurAssetInfo->qstrTestTime = qstrCurTime;
            }

            /*if(m_pCurAssetInfo->qvtTestDataInfos.isEmpty())
            {
                //数据为空，直接添加
                TaskModeViewNS::InfraredDataInfo *pDataInfo = new TaskModeViewNS::InfraredDataInfo();
                if(pDataInfo)
                {
                    pDataInfo->ePhaseType = m_ePhaseType;
                    pDataInfo->qstrUUID = Module::getUUIDInfo();
                    pDataInfo->qstrDiagResult = stDataInfo.qstrDiagResult;
                    pDataInfo->eUnit = stDataInfo.eUnit;
                    pDataInfo->dEnvTemperVal = stDataInfo.dEnvTemperVal;
                    pDataInfo->dMaxTemperVal = stDataInfo.dMaxTemperVal;
                    pDataInfo->dMinTemperVal = stDataInfo.dMinTemperVal;
                    pDataInfo->qstrPicFileName = stDataInfo.qstrPicFileName;
                    pDataInfo->qstrDataFileName = stDataInfo.qstrDataFileName;

                    QSharedPointer<TaskModeViewNS::BaseTestDataInfo> qspDataInfo(pDataInfo);
                    m_pCurAssetInfo->qvtTestDataInfos.append(qspDataInfo);
                    m_pCurAssetInfo->qstrTestTime = qstrCurTime;
                }
            }
            else
            {
                //更新数据
                bool bExist = false;
                for(int i = 0, iTdSize = m_pCurAssetInfo->qvtTestDataInfos.size(); i < iTdSize; ++i)
                {
                    if(m_pCurAssetInfo->qvtTestDataInfos[i].data())
                    {
                        if(m_ePhaseType == m_pCurAssetInfo->qvtTestDataInfos[i].data()->ePhaseType)
                        {
                            TaskModeViewNS::InfraredDataInfo *pDataInfo = static_cast<TaskModeViewNS::InfraredDataInfo *>(m_pCurAssetInfo->qvtTestDataInfos[i].data());
                            if(pDataInfo)
                            {
                                pDataInfo->qstrDiagResult = stDataInfo.qstrDiagResult;
                                pDataInfo->eUnit = stDataInfo.eUnit;
                                pDataInfo->dEnvTemperVal = stDataInfo.dEnvTemperVal;
                                pDataInfo->dMaxTemperVal = stDataInfo.dMaxTemperVal;
                                pDataInfo->dMinTemperVal = stDataInfo.dMinTemperVal;

                                if(!(pDataInfo->qstrPicFileName.isEmpty())
                                        && pDataInfo->qstrPicFileName != stDataInfo.qstrPicFileName)
                                {
                                    //删除之前的数据
                                    FileOperUtil::deleteFile(getCurJSONTaskTestDataSavePath() + "/" + pDataInfo->qstrPicFileName);
                                }

                                pDataInfo->qstrPicFileName = stDataInfo.qstrPicFileName;

                                if(!(pDataInfo->qstrDataFileName.isEmpty())
                                        && pDataInfo->qstrDataFileName != stDataInfo.qstrDataFileName)
                                {
                                    //删除之前的数据
                                    FileOperUtil::deleteFile(getCurJSONTaskTestDataSavePath() + "/" + pDataInfo->qstrDataFileName);
                                }

                                pDataInfo->qstrDataFileName = stDataInfo.qstrDataFileName;

                                m_pCurAssetInfo->qstrTestTime = qstrCurTime;
                                bExist = true;

                                break;
                            }
                        }
                    }
                }

                if(!bExist)
                {
                    TaskModeViewNS::InfraredDataInfo *pDataInfo = new TaskModeViewNS::InfraredDataInfo();
                    if(pDataInfo)
                    {
                        pDataInfo->ePhaseType = m_ePhaseType;
                        pDataInfo->qstrUUID = Module::getUUIDInfo();
                        pDataInfo->qstrDiagResult = stDataInfo.qstrDiagResult;
                        pDataInfo->eUnit = stDataInfo.eUnit;
                        pDataInfo->dEnvTemperVal = stDataInfo.dEnvTemperVal;
                        pDataInfo->dMaxTemperVal = stDataInfo.dMaxTemperVal;
                        pDataInfo->dMinTemperVal = stDataInfo.dMinTemperVal;
                        pDataInfo->qstrPicFileName = stDataInfo.qstrPicFileName;
                        pDataInfo->qstrDataFileName = stDataInfo.qstrDataFileName;

                        QSharedPointer<TaskModeViewNS::BaseTestDataInfo> qspDataInfo(pDataInfo);
                        m_pCurAssetInfo->qvtTestDataInfos.append(qspDataInfo);
                        m_pCurAssetInfo->qstrTestTime = qstrCurTime;
                    }
                }
            }*/
        }

        m_qmt4CurAssetInfo.unlock();
    }

    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurTaskInfo)
        {
            m_pCurTaskInfo->qstrTestTime = qstrCurTime;
            updateJsonTaskInfo(*m_pCurTaskInfo);
        }
        m_qmt4CurTaskInfo.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：保存当前JSON任务的局放检测数据
 * ***********************************************************************/
void TaskManager::setPDTestDataInfo(const TaskModeViewNS::PDDataInfo &stDataInfo)
{
    QString qstrCurTime = QDateTime::currentDateTime().toString("yyyy/MM/dd-HH:mm:ss.zzz");

    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurAssetInfo)
        {
            //支持单相别下测试多个数据
            /*
            TaskModeViewNS::PDDataInfo *pDataInfo = new TaskModeViewNS::PDDataInfo();
            if(pDataInfo)
            {
                pDataInfo->ePhaseType = m_ePhaseType;
                pDataInfo->qstrUUID = Module::getUUIDInfo();
                pDataInfo->eUnit = stDataInfo.eUnit;
                pDataInfo->ePDType = stDataInfo.ePDType;
                pDataInfo->dPDVal = stDataInfo.dPDVal;
                pDataInfo->qstrBGFileName = stDataInfo.qstrBGFileName;
                pDataInfo->qstrDataFileName = stDataInfo.qstrDataFileName;
                pDataInfo->qstrDiagResult = stDataInfo.qstrDiagResult;

                QSharedPointer<TaskModeViewNS::BaseTestDataInfo> qspDataInfo(pDataInfo);
                m_pCurAssetInfo->qvtTestDataInfos.append(qspDataInfo);
                m_pCurAssetInfo->qstrTestTime = qstrCurTime;
            }*/

            if(m_pCurAssetInfo->qvtTestDataInfos.isEmpty())
            {
                //数据为空，直接添加
                TaskModeViewNS::PDDataInfo *pDataInfo = new TaskModeViewNS::PDDataInfo();
                if(pDataInfo)
                {
                    pDataInfo->ePhaseType = m_ePhaseType;
                    pDataInfo->qstrUUID = Module::getUUIDInfo();
                    pDataInfo->eUnit = stDataInfo.eUnit;
                    pDataInfo->ePDType = stDataInfo.ePDType;
                    pDataInfo->dPDVal = stDataInfo.dPDVal;
                    pDataInfo->qstrBGFileName = stDataInfo.qstrBGFileName;
                    pDataInfo->qstrDataFileName = stDataInfo.qstrDataFileName;
                    pDataInfo->qstrDiagResult = stDataInfo.qstrDiagResult;

                    QSharedPointer<TaskModeViewNS::BaseTestDataInfo> qspDataInfo(pDataInfo);
                    m_pCurAssetInfo->qvtTestDataInfos.append(qspDataInfo);
                    m_pCurAssetInfo->qstrTestTime = qstrCurTime;
                }
            }
            else
            {
                //更新数据
                bool bExist = false;
                for(int i = 0, iTdSize = m_pCurAssetInfo->qvtTestDataInfos.size(); i < iTdSize; ++i)
                {
                    if(m_pCurAssetInfo->qvtTestDataInfos[i].data())
                    {
                        if(m_ePhaseType == m_pCurAssetInfo->qvtTestDataInfos[i].data()->ePhaseType)
                        {
                            TaskModeViewNS::PDDataInfo *pDataInfo = static_cast<TaskModeViewNS::PDDataInfo *>(m_pCurAssetInfo->qvtTestDataInfos[i].data());
                            if(pDataInfo && pDataInfo->ePDType == stDataInfo.ePDType)
                            {
                                pDataInfo->eUnit = stDataInfo.eUnit;
                                pDataInfo->ePDType = stDataInfo.ePDType;
                                pDataInfo->dPDVal = stDataInfo.dPDVal;

                                //if(!(pDataInfo->qstrBGFileName.isEmpty())
                                //        && pDataInfo->qstrBGFileName != stDataInfo.qstrBGFileName)
                                //{
                                //    //删除之前的数据
                                //    FileOperUtil::deleteFile(getCurJSONTaskTestDataSavePath() + "/" + pDataInfo->qstrBGFileName);
                                //}

                                pDataInfo->qstrBGFileName = stDataInfo.qstrBGFileName;

                                if(!(pDataInfo->qstrDataFileName.isEmpty())
                                        && pDataInfo->qstrDataFileName != stDataInfo.qstrDataFileName)
                                {
                                    //删除之前的数据
                                    FileOperUtil::deleteFile(getCurJSONTaskTestDataSavePath() + "/" + pDataInfo->qstrDataFileName);
                                }

                                pDataInfo->qstrDataFileName = stDataInfo.qstrDataFileName;
                                pDataInfo->qstrDiagResult = stDataInfo.qstrDiagResult;

                                m_pCurAssetInfo->qstrTestTime = qstrCurTime;
                                bExist = true;

                                break;
                            }
                        }
                    }
                }

                if(!bExist)
                {
                    TaskModeViewNS::PDDataInfo *pDataInfo = new TaskModeViewNS::PDDataInfo();
                    if(pDataInfo)
                    {
                        pDataInfo->ePhaseType = m_ePhaseType;
                        pDataInfo->qstrUUID = Module::getUUIDInfo();
                        pDataInfo->eUnit = stDataInfo.eUnit;
                        pDataInfo->ePDType = stDataInfo.ePDType;
                        pDataInfo->dPDVal = stDataInfo.dPDVal;
                        pDataInfo->qstrBGFileName = stDataInfo.qstrBGFileName;
                        pDataInfo->qstrDataFileName = stDataInfo.qstrDataFileName;
                        pDataInfo->qstrDiagResult = stDataInfo.qstrDiagResult;

                        QSharedPointer<TaskModeViewNS::BaseTestDataInfo> qspDataInfo(pDataInfo);
                        m_pCurAssetInfo->qvtTestDataInfos.append(qspDataInfo);
                        m_pCurAssetInfo->qstrTestTime = qstrCurTime;
                    }
                }
            }
        }

        m_qmt4CurAssetInfo.unlock();
    }

    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurTaskInfo)
        {
            m_pCurTaskInfo->qstrTestTime = qstrCurTime;
            updateJsonTaskInfo(*m_pCurTaskInfo);
        }
        m_qmt4CurTaskInfo.unlock();
    }

    return;
}

/*************************************************************************
 * 功能：获取当前JSON任务的负荷电流
 * 返回值：
 *      float：负荷电流值
 * ***********************************************************************/
float TaskManager::getCurJSONTaskLoadCurrentVal()
{
    float fLoadCurrentVal = 0.0f;

    TaskModeViewNS::CustomTaskInfo stTaskInfo;
    stTaskInfo.qstrId = m_qstrMainTaskId;
    if(m_qmt4TaskInfos.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        int iTaskIndex = m_qvt4TaskInfos.indexOf(stTaskInfo);
        if(0 <= iTaskIndex && iTaskIndex < m_qvt4TaskInfos.size())
        {
            fLoadCurrentVal = static_cast<float>(m_qvt4TaskInfos[iTaskIndex].dLoadCurrentVal);
        }

        m_qmt4TaskInfos.unlock();
    }

    return fLoadCurrentVal;
}

/*************************************************************************
 * 功能：获取当前JSON任务的接地电流
 * 输入参数：
 *      ePhaseType：相别
 * 返回值：
 *      float：接地电流值
 * ***********************************************************************/
float TaskManager::getCurJSONTaskGroundCurrentVal(TaskModeViewNS::PhaseType ePhaseType)
{
    float fGroundCurrentVal = 0.0f;

    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurAssetInfo)
        {
            for(int i = 0, iTdSize = m_pCurAssetInfo->qvtTestDataInfos.size(); i < iTdSize; ++i)
            {
                TaskModeViewNS::GroundCurrentDataInfo *pDataInfo = static_cast<TaskModeViewNS::GroundCurrentDataInfo *>(m_pCurAssetInfo->qvtTestDataInfos[i].data());
                if(pDataInfo && ePhaseType == pDataInfo->ePhaseType)
                {
                    fGroundCurrentVal = static_cast<float>(pDataInfo->dCurrentVal);
                    break;
                }
            }
        }
        m_qmt4CurAssetInfo.unlock();
    }

    return fGroundCurrentVal;
}

/*************************************************************************
 * 功能：设置当前JSON任务的检测类型和局放类型
 * 输入参数：
 *      eTestType：检测类型
 *      ePDType：局放类型
 * ***********************************************************************/
void TaskManager::setJSONTaskTestTypeAndPDType(TaskModeViewNS::TestType eTestType, TaskModeViewNS::PDType ePDType)
{
    m_eTestType = eTestType;
    m_ePDType = ePDType;
    setJSONAssetId(m_qstrAssetId);
    return;
}

/*************************************************************************
 * 功能：设置当前JSON任务的相位类型
 * 输入参数：
 *      ePhaseType：相位类型
 *      qstrPhaseTypeInfo：相位类型信息
 * ***********************************************************************/
void TaskManager::setJSONTaskPhaseType(TaskModeViewNS::PhaseType ePhaseType, QString qstrPhaseTypeInfo)
{
    m_ePhaseType = ePhaseType;
    m_qstrPhaseType = qstrPhaseTypeInfo;
    return;
}

/*************************************************************************
 * 功能：获取当前JSON任务的检测类型
 * 返回值：
 *      TaskModeViewNS::TestType：检测类型
 * ***********************************************************************/
TaskModeViewNS::TestType TaskManager::getCurTestType()
{
    return m_eTestType;
}

/*************************************************************************
 * 功能：获取当前JSON任务的局放类型
 * 返回值：
 *      TaskModeViewNS::PDType：局放类型
 * ***********************************************************************/
TaskModeViewNS::PDType TaskManager::getCurPDType()
{
    return m_ePDType;
}

/*************************************************************************
 * 功能：获取当前JSON任务的相位类型
 * 返回值：
 *      TaskModeViewNS::PhaseType：相位类型
 * ***********************************************************************/
TaskModeViewNS::PhaseType TaskManager::getCurPhaseType()
{
    return m_ePhaseType;
}

/*************************************************************************
 * 功能：获取当前JSON任务的相位类型信息
 * 返回值：
 *      QString：相位类型信息
 * ***********************************************************************/
QString TaskManager::getCurPhaseTypeInfo()
{
    return m_qstrPhaseType;
}

/*************************************************************************
 * 功能：获取当前JSON任务的检测数据保存路径
 * 返回值：
 *      QString：保存路径
 * ***********************************************************************/
QString TaskManager::getCurJSONTaskTestDataSavePath()
{
    QString qstrPath = "";

    if(!(m_qstrMainTaskId.isEmpty()) && !(m_qstrSubTaskId.isEmpty()))
    {
        qstrPath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + m_qstrMainTaskId + "/" + m_qstrSubTaskId;
    }

    return qstrPath;
}

/*************************************************************************
 * 功能：获取诊断描述信息
 * 输入参数：
 *      eDiagVal：诊断类型
 * 返回值：
 *      QString：描述信息
 * ***********************************************************************/
QString TaskManager::getDiagnosisDescription(CustomAccessTaskNS::DiagnoseType eDiagVal)
{
    QString qstrInfo = "";

    switch(eDiagVal)
    {
    case CustomAccessTaskNS::DiagNormal:
    {
        qstrInfo = TaskModeViewNS::TASK_DIAG_NORMAL;
        break;
    }
    case CustomAccessTaskNS::DiagGeneral:
    {
        qstrInfo = TaskModeViewNS::TASK_DIAG_MINOR;
        break;
    }
    case CustomAccessTaskNS::DiagSerious:
    {
        qstrInfo = TaskModeViewNS::TASK_DIAG_SERIOUS;
        break;
    }
    case CustomAccessTaskNS::DiagUrgent:
    {
        qstrInfo = TaskModeViewNS::TASK_DIAG_EMERGENCY;
        break;
    }
    default:
    {
        qstrInfo = TaskModeViewNS::TASK_DIAG_NORMAL;
        break;
    }

    }

    return qstrInfo;
}

/*************************************************************************
 * 功能：获取当前任务未测试完成的线路ID
 * 返回值：
 *      QString：当前任务未测试完成的线路ID
 * ***********************************************************************/
QString TaskManager::getUnfinishedJSONCircuitId()
{
    QString qstrId = "";

    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurTaskInfo)
        {
            for(int i = 0, iSize = m_pCurTaskInfo->qvtCircuitInfos.size(); i < iSize; ++i)
            {
                if(!isJSONSubInfoFinished(m_pCurTaskInfo->qvtCircuitInfos[i].qstrId))
                {
                    qstrId = m_pCurTaskInfo->qvtCircuitInfos[i].qstrId;
                    break;
                }
            }
        }

        m_qmt4CurTaskInfo.unlock();
    }

    return qstrId;
}

/*************************************************************************
 * 功能：获取当前任务未测试完成的设备ID
 * 返回值：
 *      QString：当前任务未测试完成的设备ID
 * ***********************************************************************/
QString TaskManager::getUnfinishedJSONAssetId()
{
    QString qstrId = "";

    if(m_qmt4CurCircuitInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurCircuitInfo)
        {
            for(int i = 0, iSize = m_pCurCircuitInfo->qvtAssetInfos.size(); i < iSize; ++i)
            {
                if(!isJSONAssetFinished(m_pCurCircuitInfo->qvtAssetInfos[i].qstrId))
                {
                    qstrId = m_pCurCircuitInfo->qvtAssetInfos[i].qstrId;
                    break;
                }
            }
        }

        m_qmt4CurCircuitInfo.unlock();
    }

    return qstrId;
}

/*************************************************************************
 * 功能：获取当前任务未测试完成的相位
 * 返回值：
 *      TaskModeViewNS::PhaseType：当前任务未测试完成的相位
 * ***********************************************************************/
TaskModeViewNS::PhaseType TaskManager::getUnfinishedPhaseInfo()
{
    TaskModeViewNS::PhaseType eType = TaskModeViewNS::PHASE_INVALID;
    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurAssetInfo)
        {
            if(TaskModeViewNS::TYPE_CURRENT == m_pCurAssetInfo->eType)
            {
                for(int i = TaskModeViewNS::PHASE_N; i < TaskModeViewNS::PHASE_COUNT; ++i)
                {
                    if(m_pCurAssetInfo->qvtPhaseTypes.contains(i))
                    {
                        if(!isJSONPhaseFinished(m_pCurAssetInfo->qstrId, static_cast<TaskModeViewNS::PhaseType>(i)))
                        {
                            eType = static_cast<TaskModeViewNS::PhaseType>(i);
                            break;
                        }
                    }
                }
            }
            else if(TaskModeViewNS::TYPE_INFRARED == m_pCurAssetInfo->eType)
            {
                for(int i = TaskModeViewNS::PHASE_N; i < TaskModeViewNS::PHASE_COUNT; ++i)
                {
                    if(m_pCurAssetInfo->qvtPhaseTypes.contains(i))
                    {
                        if(!isJSONPhaseFinished(m_pCurAssetInfo->qstrId, static_cast<TaskModeViewNS::PhaseType>(i)))
                        {
                            eType = static_cast<TaskModeViewNS::PhaseType>(i);
                            break;
                        }
                    }
                }
            }
            else
            {
                if(isJSONTaskBGNTested())
                {
                    for(int i = TaskModeViewNS::PHASE_N; i < TaskModeViewNS::PHASE_COUNT; ++i)
                    {
                        if(m_pCurAssetInfo->qvtPhaseTypes.contains(i))
                        {
                            if(!isJSONPhaseFinished(m_pCurAssetInfo->qstrId, static_cast<TaskModeViewNS::PhaseType>(i)))
                            {
                                eType = static_cast<TaskModeViewNS::PhaseType>(i);
                                break;
                            }
                        }
                    }
                }
                else
                {
                    eType = TaskModeViewNS::PHASE_BGN;
                }
            }
        }

        m_qmt4CurAssetInfo.unlock();
    }

    return eType;
}

/*************************************************************************
 * 功能：获取当前设备未测试完成的相位
 * 返回值：
 *      TaskModeViewNS::PhaseType：所有未测试完成的相位
 * ***********************************************************************/
TaskModeViewNS::PhaseType TaskManager::getAssetUnfinishedPhaseInfo()
{
    TaskModeViewNS::PhaseType eType = TaskModeViewNS::PHASE_INVALID;
        if(m_pCurAssetInfo)
        {

                for(int i = TaskModeViewNS::PHASE_N; i < TaskModeViewNS::PHASE_COUNT; ++i)
                {
                    if(m_pCurAssetInfo->qvtPhaseTypes.contains(i)&&m_pCurAssetInfo->eType != TaskModeViewNS::TYPE_PD)
                    {
                        if(!isJSONAssetPhaseFinished(m_pCurAssetInfo->qstrId, m_pCurAssetInfo->eType, TaskModeViewNS::PD_NOT, static_cast<TaskModeViewNS::PhaseType>(i)))
                        {
                            eType = static_cast<TaskModeViewNS::PhaseType>(i);
                        }
                    }
                }

                if(isAssetJSONTaskBGNTested(m_pCurAssetInfo->qstrId))
                {
                    for(int i = TaskModeViewNS::PHASE_N; i < TaskModeViewNS::PHASE_COUNT; ++i)
                    {
                        if(m_pCurAssetInfo->qvtPhaseTypes.contains(i))
                        {
                            if(!isJSONAssetPhaseFinished(m_pCurAssetInfo->qstrId,TaskModeViewNS::TYPE_PD, TaskModeViewNS::PD_AE,static_cast<TaskModeViewNS::PhaseType>(i)))
                            {
                                eType = static_cast<TaskModeViewNS::PhaseType>(i);
                            }
                            if(!isJSONAssetPhaseFinished(m_pCurAssetInfo->qstrId,TaskModeViewNS::TYPE_PD, TaskModeViewNS::PD_UHF,static_cast<TaskModeViewNS::PhaseType>(i)))
                            {
                                eType = static_cast<TaskModeViewNS::PhaseType>(i);
                            }
                            if(!isJSONAssetPhaseFinished(m_pCurAssetInfo->qstrId,TaskModeViewNS::TYPE_PD, TaskModeViewNS::PD_HFCT,static_cast<TaskModeViewNS::PhaseType>(i)))
                            {
                                eType = static_cast<TaskModeViewNS::PhaseType>(i);
                            }
                        }
                    }
                }
                else
                {
                    eType = TaskModeViewNS::PHASE_BGN;
                }
        }
    return eType;
}

/*************************************************************************
 * 功能：获取当前设备未测试完成的相位
 * 返回值：
 *      TaskModeViewNS::PhaseType：所有未测试完成的相位
 * ***********************************************************************/
void TaskManager::updateAssetTestedState(QString qstrAssetId)
{
    TaskModeViewNS::PhaseType eType = TaskModeViewNS::PHASE_INVALID;
        if(m_pCurAssetInfo)
        {

                for(int i = TaskModeViewNS::PHASE_N; i < TaskModeViewNS::PHASE_COUNT; ++i)
                {
                    if(m_pCurAssetInfo->qvtPhaseTypes.contains(i))
                    {
                        if(isJSONAssetPhaseFinished(m_pCurAssetInfo->qstrId,TaskModeViewNS::TYPE_CURRENT, TaskModeViewNS::PD_NOT, static_cast<TaskModeViewNS::PhaseType>(i)))
                        {
                            eType = static_cast<TaskModeViewNS::PhaseType>(i);
                        }
                    }
                }

                for(int i = TaskModeViewNS::PHASE_N; i < TaskModeViewNS::PHASE_COUNT; ++i)
                {

                    {
                        if(isJSONAssetPhaseFinished(m_pCurAssetInfo->qstrId,TaskModeViewNS::TYPE_INFRARED, TaskModeViewNS::PD_NOT, static_cast<TaskModeViewNS::PhaseType>(i)))
                        {
                            eType = static_cast<TaskModeViewNS::PhaseType>(i);
                        }
                    }
                }

                if(isAssetJSONTaskBGNUnTested(m_pCurAssetInfo->qstrId))
                {
                    for(int i = TaskModeViewNS::PHASE_N; i < TaskModeViewNS::PHASE_COUNT; ++i)
                    {
                        if(m_pCurAssetInfo->qvtPhaseTypes.contains(i))
                        {
                            if(isJSONAssetPhaseFinished(m_pCurAssetInfo->qstrId,TaskModeViewNS::TYPE_PD, TaskModeViewNS::PD_AE,static_cast<TaskModeViewNS::PhaseType>(i)))
                            {
                                eType = static_cast<TaskModeViewNS::PhaseType>(i);
                            }
                            if(isJSONAssetPhaseFinished(m_pCurAssetInfo->qstrId,TaskModeViewNS::TYPE_PD, TaskModeViewNS::PD_UHF,static_cast<TaskModeViewNS::PhaseType>(i)))
                            {
                                eType = static_cast<TaskModeViewNS::PhaseType>(i);
                            }
                            if(isJSONAssetPhaseFinished(m_pCurAssetInfo->qstrId,TaskModeViewNS::TYPE_PD, TaskModeViewNS::PD_HFCT,static_cast<TaskModeViewNS::PhaseType>(i)))
                            {
                                eType = static_cast<TaskModeViewNS::PhaseType>(i);
                            }
                        }
                    }
                }
                else
                {
                    eType = TaskModeViewNS::PHASE_BGN;
                }

        }
    if(TaskModeViewNS::PHASE_INVALID == eType)
    {
        setJSONAssetTested(qstrAssetId, false);
    }
    else
    {
        setJSONAssetTested(qstrAssetId, true);
    }
}

/*************************************************************************
 * 功能：获取当前设备未测试完成的相位
 * 返回值：
 *      TaskModeViewNS::PhaseType：所有未测试完成的相位
 * ***********************************************************************/
void TaskManager::updateSubTaskTestedState(QString qstrSubTaskId)
{
    bool bRet =false;
        if(m_pCurCircuitInfo)
        {
            for(int i = 0, iSize = m_pCurCircuitInfo->qvtAssetInfos.size(); i < iSize; ++i)
            {
                if(isJSONAssetTested(m_pCurCircuitInfo->qvtAssetInfos[i].qstrId))
                {
                    bRet = true;
                    break;
                }
            }
        }
    setJSONSubTaskTested(qstrSubTaskId, bRet);

}

/*************************************************************************
 * 功能：获取当前设备未测试完成的相位
 * 返回值：
 *      TaskModeViewNS::PhaseType：所有未测试完成的相位
 * ***********************************************************************/
void TaskManager::updateTaskTestedState(QString qstrTaskId)
{
    bool bRet =false;
        if(m_pCurTaskInfo)
        {
            for(int i = 0, iSize = m_pCurTaskInfo->qvtCircuitInfos.size(); i < iSize; ++i)
            {
                if(isJSONSubTaskTested(m_pCurTaskInfo->qvtCircuitInfos[i].qstrId))
                {
                    bRet = true;
                    break;
                }
            }
        }
    setJSONTaskTested(qstrTaskId, bRet);
}

/*************************************************
 * 功能：获取电流诊断结果
 * 输入参数：
 *      fVal：电流值
 * 返回值：
 *      QString：诊断结果
 * ************************************************/
QString TaskManager::getCurrentDiagRet(float fVal)
{
    QString qstrInfo = TaskModeViewNS::TASK_DIAG_NORMAL;

    int iRetVal = CurrentService::instance()->getCurrentDiagVal(fVal);
    if(0 == iRetVal)
    {
        qstrInfo = TaskModeViewNS::TASK_DIAG_NORMAL;
    }
    else if(1 == iRetVal)
    {
        qstrInfo = TaskModeViewNS::TASK_DIAG_MINOR;
    }
    else if(2 == iRetVal)
    {
        qstrInfo = TaskModeViewNS::TASK_DIAG_SERIOUS;
    }
    else
    {
        qstrInfo = TaskModeViewNS::TASK_DIAG_EMERGENCY;
    }

    return qstrInfo;
}

/*************************************************
 * 功能：获取当前相别类型的测试数据文件列表
 * 输入参数：
 * 返回值：
 * ************************************************/
QStringList TaskManager::getCurrentTestFileList()
{
    QStringList qstrList;
    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurAssetInfo)
        {
            if(TaskModeViewNS::TYPE_PD == m_eTestType)
            {
                for(int i = 0, iTdSize = m_pCurAssetInfo->qvtTestDataInfos.size(); i < iTdSize; ++i)
                {
                    TaskModeViewNS::PDDataInfo *pDataInfo = static_cast<TaskModeViewNS::PDDataInfo *>(m_pCurAssetInfo->qvtTestDataInfos[i].data());
                    if((m_ePhaseType == pDataInfo->ePhaseType)&&(m_ePDType == pDataInfo->ePDType))
                    {
                        qstrList.append(pDataInfo->qstrDataFileName);
                    }
                }
            }
            else if(TaskModeViewNS::TYPE_INFRARED == m_eTestType)
            {
                for(int i = 0, iTdSize = m_pCurAssetInfo->qvtTestDataInfos.size(); i < iTdSize; ++i)
                {
                    TaskModeViewNS::InfraredDataInfo *pDataInfo = static_cast<TaskModeViewNS::InfraredDataInfo *>(m_pCurAssetInfo->qvtTestDataInfos[i].data());
                    if(m_ePhaseType == pDataInfo->ePhaseType)
                    {
                        qstrList.append(pDataInfo->qstrDataFileName);
                    }
                }
            }
        }
        m_qmt4CurAssetInfo.unlock();
    }
    return qstrList;
}

/*************************************************
 * 功能：删除当前相别下指定的文件
 * 输入参数：
 * 返回值：
 * ************************************************/
void TaskManager::deleteSpecifiedTestFile(QString qstrDataFileName)
{
    QString qstrFileDir = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH + "/" + m_qstrMainTaskId + "/" + m_qstrSubTaskId;
    if(m_qmt4CurAssetInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurAssetInfo)
        {

            if(TaskModeViewNS::TYPE_PD == m_pCurAssetInfo->eType)
            {
                for(int i = 0, iTdSize = m_pCurAssetInfo->qvtTestDataInfos.size(); i < iTdSize; ++i)
                {
                    TaskModeViewNS::PDDataInfo *pDataInfo = static_cast<TaskModeViewNS::PDDataInfo *>(m_pCurAssetInfo->qvtTestDataInfos[i].data());
                    if((m_ePhaseType == pDataInfo->ePhaseType)&&(0 == qstrDataFileName.compare(pDataInfo->qstrDataFileName)))
                    {
                        m_pCurAssetInfo->qvtTestDataInfos.remove(i);
                        //更新相位测试状态
                        setJSONPhaseUnFinished(m_pCurAssetInfo->qstrId, m_ePhaseType);
                        QString qstrFilePath = qstrFileDir + "/" + qstrDataFileName;
                        FileOperUtil::deleteFile(qstrFilePath);
                    }
                }
            }
            else if(TaskModeViewNS::TYPE_INFRARED == m_pCurAssetInfo->eType)
            {
                int iRet = 0;
                for(int i = 0, iTdSize = m_pCurAssetInfo->qvtTestDataInfos.size(); i < iTdSize; ++i)
                {
                    TaskModeViewNS::InfraredDataInfo *pDataInfo = static_cast<TaskModeViewNS::InfraredDataInfo *>(m_pCurAssetInfo->qvtTestDataInfos[i].data());

                    if(m_ePhaseType == pDataInfo->ePhaseType)
                    {
                        iRet++;
                    }

                    if((m_ePhaseType == pDataInfo->ePhaseType)&&(0 == qstrDataFileName.compare(pDataInfo->qstrDataFileName)))
                    {
                        QString qstrPicFileName = pDataInfo->qstrPicFileName;
                        m_pCurAssetInfo->qvtTestDataInfos.remove(i);
                        QString qstrFilePath = qstrFileDir + "/" + qstrDataFileName;
                        FileOperUtil::deleteFile(qstrFilePath);
                        QString qstrPicFilePath = qstrFileDir + "/" + qstrPicFileName;
                        FileOperUtil::deleteFile(qstrPicFilePath);
                        iRet--;
                    }
                }

                //更新相位测试状态
                if(0 == iRet)
                {
                    setJSONPhaseUnFinished(m_pCurAssetInfo->qstrId, m_ePhaseType);
                }
            }
        }
        m_qmt4CurAssetInfo.unlock();
    }

    if(m_qmt4CurTaskInfo.tryLock(TaskModeViewNS::g_iLockWaitTime))
    {
        if(m_pCurTaskInfo)
        {
            updateJsonTaskInfo(*m_pCurTaskInfo);
        }
        m_qmt4CurTaskInfo.unlock();
    }
    return;
}

/*************************************************
 * 功能：解析江苏电科院JSON格式的任务
 * 输入参数：
 *      taskContent：json格式任务信息
 * 返回值：
 *      QVector<XMLDocument>:xml格式任务信息
 * *************************************************/
QVector<QByteArray> TaskManager::parseJSDKYJsonTaskToXmlData(const QByteArray &taskContent)
{
    QJson objTaskInfo(taskContent);
    FileOperUtil::writeFile("/media/data/testa.xml", taskContent.data(), true);
    QVector<QByteArray> vecXMLDocument;
    //主任务
    QJson objMainTasks(objTaskInfo.value(JSDKYAccessTaskNS::MAIN_TASKS));
    if(objMainTasks.isArray())
    {
        for(int i = 0, iCount = objMainTasks.count(); i < iCount; ++i)
        {

            //子任务
            QJson objSubTasks(objMainTasks.at(i).value(JSDKYAccessTaskNS::SUB_TASKS));
            if(objSubTasks.isArray())
            {
                for(int j = 0, jCount = objSubTasks.count(); j < jCount; ++j)
                {
                    XMLDocument doc(CustomAccessTaskIONS::ROOT_NODE);
                    doc.addElement(CustomAccessTaskIONS::VERSION_NODE);
                    QDomElement elemMain = doc.addElement(CustomAccessTaskIONS::MAIN_TASK_NODE);
                    doc.beginElement( elemMain );
                    doc.setAttribute(CustomAccessTaskIONS::ID, QString::number(objMainTasks.at(i).value(JSDKYAccessTaskNS::MAIN_TASK_ID).toNumber()));
                    doc.setAttribute(CustomAccessTaskIONS::NAME, objMainTasks.at(i).value(JSDKYAccessTaskNS::MAIN_TASK_NAME).toString());
                    QDomElement elemSub = doc.addElement(CustomAccessTaskIONS::SUB_TASK_NODE);
                    doc.beginElement( elemSub );
                    QString qstrSubId = QString("%1-%2").arg(QString::number(objSubTasks.at(j).value(JSDKYAccessTaskNS::SUB_TASK_ID).toNumber())).arg(QString(objSubTasks.at(j).value(JSDKYAccessTaskNS::SUB_TASK_TYPE).toString()));
                    doc.setAttribute(CustomAccessTaskIONS::ID, qstrSubId);
                    doc.setAttribute(CustomAccessTaskIONS::TYPE, objSubTasks.at(j).value(JSDKYAccessTaskNS::SUB_TASK_TYPE).toString());
                    doc.setAttribute(CustomAccessTaskIONS::NAME, objSubTasks.at(j).value(JSDKYAccessTaskNS::SUB_TASK_NAME).toString());

                    //间隔
                    QJson objClearances(objSubTasks.at(j).value(JSDKYAccessTaskNS::CLEARANCES));
                    for(int k = 0, kCount = objClearances.count(); k < kCount; ++k)
                    {
                        QDomElement elemClearance = doc.addElement(CustomAccessTaskIONS::CLEARANCE_NODE);
                        doc.beginElement( elemClearance );
                        doc.setAttribute(CustomAccessTaskIONS::ID, objClearances.at(k).value(JSDKYAccessTaskNS::CLEARANCE_ID).toString());
                        doc.setAttribute(CustomAccessTaskIONS::NAME, objClearances.at(k).value(JSDKYAccessTaskNS::CLEARANCE_NAME).toString());
                        doc.setAttribute(CustomAccessTaskIONS::ORDER, QString::number(objClearances.at(k).value(JSDKYAccessTaskNS::CLEARANCE_SN).toNumber()));
                        //测点
                        QJson objTestPoints(objClearances.at(k).value(JSDKYAccessTaskNS::TESTPOINTS));
                        for(int l = 0, lCount = objTestPoints.count(); l < lCount; ++l)
                        {
                            QDomElement elemtest = doc.addElement(CustomAccessTaskIONS::TEST_POINT_NODE);
                            doc.beginElement( elemtest);
                            doc.setAttribute(CustomAccessTaskIONS::ID, QString::number(objTestPoints.at(l).value(JSDKYAccessTaskNS::TESTPOINT_ID).toNumber()));
                            doc.setAttribute(CustomAccessTaskIONS::NAME, objTestPoints.at(l).value(JSDKYAccessTaskNS::TESTPOINT_NAME).toString());
                            doc.setAttribute(CustomAccessTaskIONS::ORDER, objTestPoints.at(l).value(JSDKYAccessTaskNS::TESTPOINT_SN).toString());
                            doc.endElement();
                        }
                        doc.endElement();
                    }
                    doc.endElement();
                    vecXMLDocument.append(doc.getByteArray());
                    //FileOperUtil::writeFile("/media/data/test4.xml", doc.getByteArray().data(), true);

                }
            }
        }
    }
    else
    {
        XMLDocument doc(CustomAccessTaskIONS::ROOT_NODE);
        doc.addElement(CustomAccessTaskIONS::VERSION_NODE);
        //doc.setValue(CustomAccessTaskIONS::VERSION_NODE, );
        doc.addElement(CustomAccessTaskIONS::MAIN_TASK_NODE);
        doc.beginElement( CustomAccessTaskIONS::MAIN_TASK_NODE );
        doc.setAttribute(CustomAccessTaskIONS::ID, QString::number(objMainTasks.value(JSDKYAccessTaskNS::MAIN_TASK_ID).toNumber()));
        //doc.setAttribute(CustomAccessTaskIONS::TYPE, objMainTasks.value(JSDKYAccessTaskNS::MAIN_TASK_TYPE).toString());
        doc.setAttribute(CustomAccessTaskIONS::NAME, objMainTasks.value(JSDKYAccessTaskNS::MAIN_TASK_NAME).toString());

        //子任务
        QJson objSubTasks(objMainTasks.value(JSDKYAccessTaskNS::SUB_TASKS));
        if(objSubTasks.isArray())
        {
            for(int j = 0, jCount = objSubTasks.count(); j < jCount; ++j)
            {
                //QDomElement elem = doc.addElement(CustomAccessTaskIONS::SUB_TASK_NODE);
                doc.beginElement( CustomAccessTaskIONS::SUB_TASK_NODE );
                doc.setAttribute(CustomAccessTaskIONS::ID, objSubTasks.at(j).value(JSDKYAccessTaskNS::SUB_TASK_ID).toString());
                doc.setAttribute(CustomAccessTaskIONS::TYPE, objSubTasks.at(j).value(JSDKYAccessTaskNS::SUB_TASK_TYPE).toString());
                doc.setAttribute(CustomAccessTaskIONS::NAME, objSubTasks.at(j).value(JSDKYAccessTaskNS::SUB_TASK_NAME).toString());

                //间隔
                QJson objClearances(objSubTasks.at(j).value(JSDKYAccessTaskNS::CLEARANCES));
                for(int k = 0, kCount = objClearances.count(); k < kCount; ++k)
                {
                    doc.addElement(CustomAccessTaskIONS::CLEARANCE_NODE);
                    doc.beginElement( CustomAccessTaskIONS::CLEARANCE_NODE );
                    doc.setAttribute(CustomAccessTaskIONS::ID, objClearances.at(k).value(JSDKYAccessTaskNS::CLEARANCE_ID).toString());
                    doc.setAttribute(CustomAccessTaskIONS::NAME, objClearances.at(k).value(JSDKYAccessTaskNS::CLEARANCE_NAME).toString());
                    doc.setAttribute(CustomAccessTaskIONS::ORDER, QString::number(objClearances.at(k).value(JSDKYAccessTaskNS::CLEARANCE_SN).toNumber()));
                    //测点
                    QJson objTestPoints(objClearances.at(k).value(JSDKYAccessTaskNS::TESTPOINTS));
                    for(int l = 0, lCount = objTestPoints.count(); l < lCount; ++l)
                    {
                        doc.addElement(CustomAccessTaskIONS::TEST_POINT_NODE);
                        doc.beginElement( CustomAccessTaskIONS::TEST_POINT_NODE );
                        doc.setAttribute(CustomAccessTaskIONS::ID, objTestPoints.at(l).value(JSDKYAccessTaskNS::TESTPOINT_ID).toString());
                        doc.setAttribute(CustomAccessTaskIONS::NAME, objTestPoints.at(l).value(JSDKYAccessTaskNS::TESTPOINT_NAME).toString());
                        doc.setAttribute(CustomAccessTaskIONS::ORDER, QString::number(objTestPoints.at(l).value(JSDKYAccessTaskNS::TESTPOINT_SN).toNumber()));

                    	doc.setAttribute(CustomAccessTaskIONS::DIAGNOSERET, QString::number(objTestPoints.at(l).value(JSDKYAccessTaskNS::DIAGRESULT).toNumber()));
                        doc.setAttribute(CustomAccessTaskIONS::FILENAME, objTestPoints.at(l).value(JSDKYAccessTaskNS::TESTFILE_NAME).toString());
                        doc.setAttribute(CustomAccessTaskIONS::BGFILENAME, objTestPoints.at(l).value(JSDKYAccessTaskNS::BGFILE_NAME).toString());
                        doc.endElement();
                    }
                    doc.endElement();
                }
                doc.endElement();
            }
        }
        doc.endElement();
        vecXMLDocument.append(doc.getByteArray());
    }

    return vecXMLDocument;
}

/*************************************************
 * 功能：解析江苏电科院JSON格式的任务
 * 输入参数：
 *      taskContent：xml格式任务信息
 * 返回值：
 *      QByteArray:json格式任务信息
 * *************************************************/
QByteArray TaskManager::parseJSDKYXmlDataToJsonTask(const QByteArray &taskContent, QStringList& fileList)
{
    //FileOperUtil::writeFile("/media/data/test3.xml", taskContent.data(), true);
    QDomDocument document;
    document.setContent(QString::fromUtf8(taskContent.data(), taskContent.size()));
    QDomElement root = document.documentElement();
    QDomElement mainTaskDom   = root.firstChildElement( CustomAccessTaskIONS::MAIN_TASK_NODE );

    QJson objJsonInfo;
    QJson objTaskInfo;
    QJson objMainTaskInfo;
    for(int i = 0, iCount = 1; i < iCount; ++i)
    {

        objMainTaskInfo.add(JSDKYAccessTaskNS::MAIN_TASK_ID,   mainTaskDom.toElement().attribute(CustomAccessTaskIONS::ID).toInt());
        //objMainTaskInfo.add(JSDKYAccessTaskNS::MAIN_TASK_TYPE, mainTaskDom.toElement().attribute(CustomAccessTaskIONS::TYPE).toUtf8());
        objMainTaskInfo.add(JSDKYAccessTaskNS::MAIN_TASK_NAME, mainTaskDom.toElement().attribute(CustomAccessTaskIONS::NAME).toUtf8());

        QJson objSubTaskList(QJson::Array);
        //QDomElement subTaskDom = mainTaskDom.firstChildElement( CustomAccessTaskIONS::SUB_TASK_NODE );
        QDomNodeList subDomList = mainTaskDom.childNodes();
        for(int j = 0, jCount = subDomList.size(); j < jCount; ++j)
        {
            QDomNode subDom = subDomList.at(j);
            if(subDom.nodeName() != CustomAccessTaskIONS::SUB_TASK_NODE)
            {
                continue;
            }
            QJson objSubTaskInfo;
            QString qstrSubIdList = subDom.toElement().attribute(CustomAccessTaskIONS::ID);
            QStringList list = qstrSubIdList.split("-");
            QString qstrSubId = list.first();
            objSubTaskInfo.add(JSDKYAccessTaskNS::SUB_TASK_ID,   qstrSubId.toInt());
            objSubTaskInfo.add(JSDKYAccessTaskNS::SUB_TASK_NAME, subDom.toElement().attribute(CustomAccessTaskIONS::NAME).toUtf8());
            objSubTaskInfo.add(JSDKYAccessTaskNS::SUB_TASK_TYPE, subDom.toElement().attribute(CustomAccessTaskIONS::TYPE).toUtf8());

            QJson objClearanceList(QJson::Array);
            QDomNodeList gapDomList = subDom.childNodes();
            for(int k = 0, kCount = gapDomList.size(); k < kCount; ++k)
            {
                QJson objClearanceInfo;
                QDomNode gapDom = gapDomList.at(k);
                objClearanceInfo.add(JSDKYAccessTaskNS::CLEARANCE_ID,   gapDom.toElement().attribute(CustomAccessTaskIONS::ID).toUtf8());
                objClearanceInfo.add(JSDKYAccessTaskNS::CLEARANCE_NAME, gapDom.toElement().attribute(CustomAccessTaskIONS::NAME).toUtf8());
                objClearanceInfo.add(JSDKYAccessTaskNS::CLEARANCE_SN,   gapDom.toElement().attribute(CustomAccessTaskIONS::ORDER).toInt());

                QJson objTestpointList(QJson::Array);
                QDomNodeList testpointDomList = gapDom.childNodes();
                QVector<CustomAccessTaskNS::TestPointInfo> qvecStTestPointInfo;
                QStringList qstrList;
                for(int l = 0, lCount = testpointDomList.size(); l < lCount; ++l)
                {
                    CustomAccessTaskNS::TestPointInfo stTestPointInfo;
                    QDomNode testpointDom = testpointDomList.at(l);
                    CustomAccessTaskNS::TestData stTestData;
                    stTestPointInfo.s_strId = testpointDom.toElement().attribute(CustomAccessTaskIONS::ID);
                    if(qstrList.contains(stTestPointInfo.s_strId))
                    {
                        continue;
                    }
                    else
                    {
                        qstrList.append(stTestPointInfo.s_strId);
                    }
                    stTestPointInfo.s_strName = testpointDom.toElement().attribute(CustomAccessTaskIONS::NAME);
                    stTestPointInfo.s_iOrder = testpointDom.toElement().attribute(CustomAccessTaskIONS::ORDER).toInt();

                    for(int m = 0, mCount = testpointDomList.size(); m < mCount; ++m)
                    {
                        QDomNode testDom = testpointDomList.at(m);
                        if(stTestPointInfo.s_strId == testDom.toElement().attribute(CustomAccessTaskIONS::ID))
                        {
                            stTestData.s_strFileName = testDom.toElement().attribute(CustomAccessTaskIONS::FILENAME);
                        }

                    }
                    stTestData.s_strBgFileList.append(testpointDom.toElement().attribute(CustomAccessTaskIONS::BGFILENAME));
                    stTestPointInfo.s_vData.append(stTestData);
                    qvecStTestPointInfo.append(stTestPointInfo);
                }

                QVector<CustomAccessTaskNS::TestPointInfo>::iterator iter;
                for(iter = qvecStTestPointInfo.begin(); iter!=qvecStTestPointInfo.end(); iter++)
                {
                    QJson objTestpointInfo;
                    objTestpointInfo.add(JSDKYAccessTaskNS::TESTPOINT_ID,   iter->s_strId.toUtf8());
                    objTestpointInfo.add(JSDKYAccessTaskNS::TESTPOINT_NAME, iter->s_strName.toUtf8());
                    objTestpointInfo.add(JSDKYAccessTaskNS::TESTPOINT_SN,   iter->s_iOrder);
                    objTestpointInfo.add(JSDKYAccessTaskNS::TESTFILE_NAME,  iter->s_vData.first().s_strFileName.toUtf8() );
                    objTestpointInfo.add(JSDKYAccessTaskNS::BGFILE_NAME,    iter->s_vData.first().s_strBgFileList.join(CustomAccessTaskNS::BG_FILE_SEPARATOR).toUtf8());

                    HGnssInfo stInfo;
                    /*
                    if(!GnssService::instance()->getGnssInfo(stInfo))
                    {
                        stInfo.dfJingDu = 0;
                        stInfo.dfWeiDu = 0;
                        stInfo.dfGaoDu = 0;
                    }
*/
                    QJson objLocationInfo;
                    objLocationInfo.add(JSDKYAccessTaskNS::LONGITUDE,      QByteArray::number(stInfo.dfJingDu));
                    objLocationInfo.add(JSDKYAccessTaskNS::LATITUDE,       QByteArray::number(stInfo.dfWeiDu));
                    objLocationInfo.add(JSDKYAccessTaskNS::ALTITUDE,       QByteArray::number(stInfo.dfGaoDu));
                    objTestpointInfo.add(JSDKYAccessTaskNS::LOCATION, objLocationInfo);
                    objTestpointList.addItemToArray(objTestpointInfo);
                }
                objClearanceInfo.add(JSDKYAccessTaskNS::TESTPOINTS, objTestpointList);
                objClearanceList.addItemToArray(objClearanceInfo);
            }
            objSubTaskInfo.add(JSDKYAccessTaskNS::CLEARANCES, objClearanceList);
            objSubTaskList.addItemToArray(objSubTaskInfo);
        }
        objMainTaskInfo.add(JSDKYAccessTaskNS::SUB_TASKS, objSubTaskList);
        //objMainTaskList.addItemToArray(objMainTaskInfo);
    }

    objTaskInfo.add(JSDKYAccessTaskNS::MAIN_TASKS, objMainTaskInfo);


    objJsonInfo.add(JSDKYAccessTaskNS::DATA, objTaskInfo);
    //FileOperUtil::writeFile("/media/data/test2.xml", objTaskInfo.data(), true);
    return objTaskInfo.data();

}

