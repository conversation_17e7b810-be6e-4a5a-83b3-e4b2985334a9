#ifndef PRPSPLOT_H
#define PRPSPLOT_H

#include <QFrame>
#include "prps/prpscomponent/prpsplotdict.h"
#include "prps/prpscomponent/prpsplotitem.h"
#include "Widget.h"

class WIDGET_EXPORT PrpsPlot : public QFrame,public PrpsPlotDict
{
    Q_OBJECT
public:
    /************************************************
     * 功能: 构造函数
     ************************************************/
    explicit PrpsPlot(QWidget *parent = 0);

    /************************************************
     * 功能: 析构函数
     ************************************************/
    ~PrpsPlot();

    /************************************************
     * 功能: 界面刷新
     * 入参：rect -- 刷新界面指定区域
     ************************************************/
    void replot( const QRect& rect );

    /************************************************
     * 功能: 界面全部刷新
     ************************************************/
    void replot( void );
protected:
    /************************************************
     * 功能: plot添加item
     * 入参：item -- 相应item
     ************************************************/
    void paintEvent( QPaintEvent* e );

    /************************************************
     * 功能: plot添加item
     * 入参：item -- 相应item
     ************************************************/
    void resizeEvent( QResizeEvent* e );
private slots:
    /************************************************
     * 功能: 响应item变化即请求更新的槽（指定区域刷新）
     ************************************************/
    void onRequestUpdate( void );
};

#endif // PRPSPLOT_H
