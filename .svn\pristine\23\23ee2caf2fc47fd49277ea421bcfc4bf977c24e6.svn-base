#include "datadefine.h"

#include <UHFHFCTAETEVApi.h>

#include "messageBox/msgbox.h"
#include "dataSave/DataFileInfos.h"
#include "controlButton/SliderButton.h"
#include "window/Window.h"
#include "tev/tevconfig.h"
#include "tevviewdefine.h"
#include "tevpulseviewbase.h"

/*************************************************
函数名： TEVAmpView(const QString &strTitle, QWidget *parent)
输入参数： strTitle：标题
          parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
TEVPulseViewBase::TEVPulseViewBase(const QString &strTitle, QWidget *parent)
    :SampleChartView(strTitle, parent)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);

    //初始化数据
    initDatas();

    //新建图谱
    ChartWidget *pChart = createChart(parent);
    setChart(pChart);

    //业务
    TevPulseService* pService = TevPulseService::instance();
    setService(pService);
    connect(pService, SIGNAL(sigData(TEV::PulseData,MultiServiceNS::USERID)),
            this, SLOT(onDataRead(TEV::PulseData, MultiServiceNS::USERID)));
    pService->start();

}

/*************************************************
函数名： ~TEVAmpView()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
TEVPulseViewBase::~TEVPulseViewBase()
{
    if(TevPulseService* pService = getTevPulseService())
    {
        pService->stop();
    }
}

/*************************************************
功能： 响应S键事件
输入参数：
        id -- 按钮ID
*************************************************************/
void TEVPulseViewBase::onSKeyPressed()
{
    return;
}

/*************************************************
函数名：service
输入参数: NULL
输出参数：NULL
返回值：service对象
功能：返回service对象
*************************************************************/
TevPulseService* TEVPulseViewBase::getTevPulseService()
{
    if (MultiUserService* pService = service())
    {
        return dynamic_cast<TevPulseService*>(pService);
    }
    else
    {
        return NULL;
    }
}

/************************************************
 * 函数名   : createChart
 * 输入参数 : parent---父控件
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 创建图谱
 ************************************************/
ChartWidget *TEVPulseViewBase::createChart(QWidget *parent)
{
    Q_UNUSED(parent)

    ChartWidget *pChart = new ChartWidget();
    return pChart;
}

/*************************************************
函数名： initDatas()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 初始化数据
*************************************************************/
void TEVPulseViewBase::initDatas()
{
    return;
}



