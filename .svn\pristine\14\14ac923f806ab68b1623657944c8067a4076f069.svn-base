#include "distributenettaskview.h"
#include <QtConcurrentRun>
#include "distributenetviewconfig.h"
#include "distributenetdeviceview.h"
#include "distributenetaddtaskview.h"
#include "filtersettingdlg.h"
#include "systemsetting/systemsetservice.h"
#include "PDAUi/PDAUiBean/pdalistchart.h"
#include "distributenetaccess/distributenetaccessservice.h"
#include "distributenetaccess/distributetaskmanager/distributetaskmanager.h"
#include "loadingView/loadingdialog.h"
#include "window/Window.h"
#include "global_log.h"

#define SINGLE_TASK 1

typedef enum _TaskButton_
{
    BUTTON_OPEN = 0, // 打开
    BUTTON_NEW, // 新建
    BUTTON_SIFT, // 筛选
    BUTTON_DELETE, // 删除
}TaskButton;

//控制按钮定义
const ButtonInfo::Info g_ButtonInfos[] =
{
    { BUTTON_OPEN, { ButtonInfo::COMMAND, DistributeNetView::TEXT_OPEN, NULL, "", NULL } }, // 打开
    { BUTTON_NEW, { ButtonInfo::COMMAND, DistributeNetView::TEXT_NEW, NULL, "", NULL } }, // 新建
    { BUTTON_SIFT, { ButtonInfo::COMMAND, DistributeNetView::TEXT_SIFT, NULL, "", NULL } }, // 筛选
    { BUTTON_DELETE, { ButtonInfo::COMMAND, DistributeNetView::TEXT_DELETE, NULL, "", NULL } }, // 删除
};

void completedTask(QString qstrTaskId)
{
    DistributeTaskManager::instance()->completedTask(qstrTaskId);
    return;
}


DistributeNetTaskView::DistributeNetTaskView(QWidget* parent)
    : PDAListView(QObject::trUtf8("Task List"), parent)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);
    m_eAccessMode = SystemSetService::instance()->getCustomAccessMode();

    DistributeNetAccessService::instance()->start();

    //创建按钮栏
    /*ListPushButtonBar* pButtonBar = */createButtonBar(DistributeNetView::CONTEXT, g_ButtonInfos, sizeof(g_ButtonInfos) / sizeof(ButtonInfo::Info));

    if (m_pChart)
    {
        disconnect(m_pChart, SIGNAL(sigItemPressed(int)), this, SLOT(onItemClicked(int)));
    }
    connect(DistributeTaskManager::instance(), SIGNAL(sigTaskInfoChanged()), this, SLOT(onTaskInfoChanged()));
    connect(DistributeTaskManager::instance(), SIGNAL(sigTaskTestStateChanged(QString)), this, SLOT(onTaskTestStateChanged(QString)));
    initTaskInfos();
}

DistributeNetTaskView::~DistributeNetTaskView()
{
    disconnect(DistributeTaskManager::instance(), SIGNAL(sigTaskInfoChanged()), this, SLOT(onTaskInfoChanged()));
    disconnect(DistributeTaskManager::instance(), SIGNAL(sigTaskTestStateChanged(QString)), this, SLOT(onTaskTestStateChanged(QString)));
    DistributeNetAccessService::instance()->stop();
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void DistributeNetTaskView::onCommandButtonPressed(int id)
{
    switch (id)
    {
    case BUTTON_OPEN:
    {
        int iSelectItems = m_pChart->itemsIndexSelected().size();
        if(iSelectItems == SINGLE_TASK)                        // 是否选中单个任务，仅允许打开单个
        {
            int iSelectedIndex = m_pChart->itemIndexSelected();
            if(iSelectedIndex != PDAListChart::ERROR_SELECTED_INDEX)  // 是否有条目被选中
            {
                onItemClicked(iSelectedIndex);
            }
            else
            {
                logWarning("no item select");
            }
        }
        else if(iSelectItems > SINGLE_TASK)
        {
            MsgBox::warning("", QObject::trUtf8("Unable to open multiple task files."));
        }
        else
        {
            MsgBox::warning("", QObject::trUtf8("No task file has been chosen."));
        }
        break;
    }
    case BUTTON_NEW:
    {
        DistributeNetAddTaskView* pView = new DistributeNetAddTaskView();
        pView->setFixedSize(Window::WIDTH, Window::HEIGHT);
        pView->show();
        break;
    }
    case BUTTON_DELETE:
    {
        // 删除任务
        deleteSelectedTasks();
        break;
    }
    case BUTTON_SIFT:
    {
        FilterSettingDlg filterSettingDlg(m_pChart->filterKeyword());
        if (QDialog::Accepted == filterSettingDlg.exec())
        {
            QString qstrKeyword = filterSettingDlg.getKeyword();
            m_pChart->setFilterKeyword(qstrKeyword);
        }
        break;
    }
    default:
        break;
    }
}

void DistributeNetTaskView::initTaskInfos()
{
    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("The disk is occupied, please disconnect mobilephone or computer!"));
        return;
    }

    DistributeTaskManager::instance()->scanAllTaskInfos();
    updateTaskInfos();

    return;
}

/*************************************************
功能： 处理条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void DistributeNetTaskView::onItemClicked(int id)
{
    /*
     * 1.根据id，找到选择的条目信息，获得标题在内的title信息
     * 2.构造函数添加，能唯一识别子任务的task信息
     */
    if(id < m_pChart->allItems().size())
    {
        DistributeNetAccessNS::TaskInfo stTaskInfo;
        if(m_qmt4TaskInfos.tryLock(3000))
        {
            if(0 <= id && id < m_qvtTaskInfos.size())
            {
                stTaskInfo =  m_qvtTaskInfos.at(id);
            }
            m_qmt4TaskInfos.unlock();
        }

        if(!(stTaskInfo.qstrId.isEmpty()))
        {
            DistributeTaskManager::instance()->setCurTaskInfo(stTaskInfo.qstrId);
            DistributeNetDeviceView* pView = new DistributeNetDeviceView(stTaskInfo);
            QObject::connect(pView, SIGNAL(sigClosed()), this, SLOT(onAssetListViewClosed()));
            pView->show();
        }
        else
        {
            logError("task id is empty.");
        }
    }
}

void DistributeNetTaskView::deleteSelectedTasks()
{
    QVector<qint32> selectItems = m_pChart->itemsIndexSelected();
    if(!selectItems.isEmpty())
    {
        if( MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to delete?")))
        {
            for(int i = 0, iSize = selectItems.size(); i < iSize; ++i)
            {
                if(0 <= selectItems[i] && selectItems[i] < m_qvtTaskInfos.size())
                {
                    QString qstrTaskId =  m_qvtTaskInfos.at(selectItems[i]).qstrId;
                    if(!(DistributeTaskManager::instance()->deleteTaskInfo(qstrTaskId)))
                    {
                        MsgBox::warning("", QObject::trUtf8("Cannot be deleted!"));
                        break;
                    }
                }
            }

            updateTaskInfos();
        }
    }
    else
    {
        MsgBox::warning("", trUtf8("No task has been chosen."));
    }
}

void DistributeNetTaskView::updateTaskInfos()
{
    if(m_qmt4TaskInfos.tryLock(3000))
    {
        m_pChart->deleteAllItem();
        // 添加任务信息
        QList<PDAListChart::ListItemInfo> itemInfos;
        itemInfos.clear();

        bool bTicked = false;
        //int iTested = 0;

        DistributeTaskManager::instance()->getTaskInfos(m_qvtTaskInfos);
        for(int i = 0, iSize = m_qvtTaskInfos.size(); i < iSize; ++i)
        {
            bTicked = m_qvtTaskInfos.at(i).uiTotalCount == m_qvtTaskInfos.at(i).uiTestedCount;
            // -1为特殊表示，原item为已测设备和设备总数，此处标定为-1则item不再显示
            if(SystemSet::ACCESS_USB_MODE == m_eAccessMode)
            {
                itemInfos << PDAListChart::ListItemInfo(m_qvtTaskInfos.at(i).qstrName, m_qvtTaskInfos.at(i).uiTestedCount, m_qvtTaskInfos.at(i).uiTotalCount, bTicked, PDAListItem::LABEL_MODE, m_qvtTaskInfos.at(i).qstrId);
            }
            else
            {
                itemInfos << PDAListChart::ListItemInfo(m_qvtTaskInfos.at(i).qstrName, m_qvtTaskInfos.at(i).uiTestedCount, m_qvtTaskInfos.at(i).uiTotalCount, bTicked, PDAListItem::CHECK_BOX, m_qvtTaskInfos.at(i).qstrId);
            }
        }

        m_pChart->addItems(itemInfos);
        if(m_pChart->allItems().size() > 0)
        {
            m_pChart->setCurrentItemSelected(0);
        }

        m_qmt4TaskInfos.unlock();
    }
}

void DistributeNetTaskView::onTaskInfoChanged()
{
    updateTaskInfos();
    return;
}

void DistributeNetTaskView::onTaskTestStateChanged(QString qstrTaskId)
{
    DistributeNetAccessNS::TaskInfo stTaskInfo = DistributeTaskManager::instance()->getCurTaskInfo();
    if(qstrTaskId == stTaskInfo.qstrId)
    {
        int iIndex = m_pChart->indexOfItem(qstrTaskId);
        QList<PDAListChart::ListItemInfo> itemInfos = m_pChart->allItems();
        if (iIndex >= 0 && iIndex < itemInfos.size())
        {
            PDAListChart::ListItemInfo tmpInfo = itemInfos.at(iIndex);
            tmpInfo.m_iTestedCount = static_cast<int>(stTaskInfo.uiTestedCount);
            tmpInfo.m_iTotalCount = static_cast<int>(stTaskInfo.uiTotalCount);

            tmpInfo.m_bIsTicked = (stTaskInfo.uiTestedCount == stTaskInfo.uiTotalCount);
            m_pChart->setItemInfo(tmpInfo, iIndex);
        }

        for (int i = 0, iSize = m_qvtTaskInfos.size(); i < iSize; ++i)
        {
            if (qstrTaskId == m_qvtTaskInfos[i].qstrId)
            {
                m_qvtTaskInfos[i] = stTaskInfo;
                break;
            }
        }
    }

    return;
}

void DistributeNetTaskView::onAssetListViewClosed()
{
    QString qstrTaskId = DistributeTaskManager::instance()->getCurTestTaskId();
    DistributeTaskManager::instance()->releaseCurTaskInfo();

    QTimer::singleShot(100, this, SLOT(onShowSavingTaskDialog()));

    QtConcurrent::run(completedTask, qstrTaskId);

    return;
}

void DistributeNetTaskView::onShowSavingTaskDialog()
{
    if(isActiveWindow())
    {
        LoadingDialog* pLoadingDialog = new LoadingDialog(QObject::trUtf8("Saving the task ..."), this);
        pLoadingDialog->setWindowModality(Qt::ApplicationModal);
        pLoadingDialog->setAttribute(Qt::WA_DeleteOnClose);
        connect(DistributeTaskManager::instance(), SIGNAL(sigTaskSavingDone()), pLoadingDialog, SLOT(accept()));

        pLoadingDialog->show();
    }
    else
    {
        logWarning("current window is not activing.");
    }

    return;
}

