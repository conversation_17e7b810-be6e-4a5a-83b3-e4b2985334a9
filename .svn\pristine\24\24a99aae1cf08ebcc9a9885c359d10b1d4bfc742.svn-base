/*****< ss1btavr.h >***********************************************************/
/*      Copyright 2006 - 2014 Stonestreet One.                                */
/*      All Rights Reserved.                                                  */
/*                                                                            */
/*  SS1BTAVR - Stonestreet One Bluetooth Protocol Stack Audio/Video Remote    */
/*             Control Profile (AVRCP) using Audio/Video Control Transport    */
/*             Protocol (AVCTP) Type Definitions, Prototypes, and Constants.  */
/*                                                                            */
/*  Author:  Nik<PERSON>o                                                       */
/*                                                                            */
/*** MODIFICATION HISTORY *****************************************************/
/*                                                                            */
/*   mm/dd/yy  F. Lastname    Description of Modification                     */
/*   --------  -----------    ------------------------------------------------*/
/*   01/09/06  N. Deo            Initial creation.                            */
/******************************************************************************/
#ifndef __SS1BTAVRH__
#define __SS1BTAVRH__

#include "AVRTypes.h"           /* Bluetooth AVRCP Type Definitions.          */
#include "AVRCPAPI.h"           /* Bluetooth AVRC Profile Prototypes.         */

#endif
