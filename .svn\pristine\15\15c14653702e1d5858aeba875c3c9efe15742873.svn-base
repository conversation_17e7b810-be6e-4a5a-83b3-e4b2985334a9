#ifndef PRPSPLOTITEM_H
#define PRPSPLOTITEM_H

/*
* Copyright (c) 2015.11，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: PrpsPlotItem.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年4月7日
* 摘要：该文件主要定义了prps模块的界面图元
*
* samples:
*
* PrpsPlotItem *pItem1 = new PrpsPlotItem;
* PrpsPlotItem *pItem2 = new PrpsPlotItem;
*
* PrpsPlot *plot = new PrpsPlot;
*
* pItem1->attach( plot );
* pItem2->attach( plot );
*
* pItem1->setGeometry( QRectF );
* pItem2->setGeometry( QRectF );

* 当前版本：1.0
*/

#include <QObject>
#include <QRectF>
#include <QRgb>
#include "Widget.h"

class QPainter;
class PrpsPlot;

class WIDGET_EXPORT PrpsPlotItem : public QObject
{
    Q_OBJECT
public:
    /*运行时的类型信息
     * 通过定义该类型信息，方便各item之间通过plot或得相应
     * 包括位置信息在内的信息
     */
    typedef enum _RttiValues
    {
        RTTI_ITEM = -1,
        RTTI_PRPS = 0,      // PRPS图谱
        RTTI_PRPD,          // PRPD图谱
        RTTI_PRPS_TEXT,     // PRPS图谱文本
        RTTI_PRPD_TEXT,     // PRPD图谱文本
        RTTI_SYNC_TEXT,     // 同步标识文本
        RTTI_CONNECT_TEXT,  // 连接状态文本
        RTTI_RUNNING_MODE,  // 运行状态文本
        RTTI_GAIN_TEXT,     // 增益文本

        RTTI_USER = 1000,
    }RttiValues;

    /************************************************
     * 功能: 构造函数
     * 入参：parent -- 父控件指针
     ************************************************/
    explicit PrpsPlotItem( QObject *parent = 0 );

    /************************************************
     * 功能: 将item添加到指定plot上
     * 入参：指定plot
     ************************************************/
    void attach( PrpsPlot* plot );

    /************************************************
     * 功能: 解绑控件
     ************************************************/
    void detach( void );

    /************************************************
     * 功能: 获得item绘制的矩形区域(相对plot而言)
     * 返回值：item的位置信息
     ************************************************/
    const QRectF &geometry () const;

    /************************************************
     * 功能: 设置item相对plot的位置
     ************************************************/
    void setGeometry ( qreal x, qreal y, qreal w, qreal h );

    /************************************************
     * 功能: 设置item相对plot的位置
     ************************************************/
    void setGeometry ( const QRectF &rect );

    /************************************************
     * 功能: 获取对象的类型信息
     ************************************************/
    virtual int rtti() const;

    /************************************************
     * 功能: 获得item的order值（用以完成绘制的顺序 降序绘制即先绘制Z值大的item）
     * 返回值：order值
     ************************************************/
    qint32 paintOrder() const;

    /************************************************
     * 功能: 设置item的order值（用以完成绘制的顺序 降序绘制即先绘制Z值大的item ）
     * 入参：iPaintOrder -- 设定的order值
     ************************************************/
    void setPaintOrder( qint32 iPaintOrder );

    /************************************************
     * 功能: item尺寸发生改变时调用的接口
     ************************************************/
    virtual void onSizeChanged( void ) = 0;

    /************************************************
     * 功能: 提供各item方便绘制的接口
     * 入参：painter -- 绘图工具
     *      canvasRect -- plot的大小
     ************************************************/
    virtual void draw( QPainter *painter,
                       const QRectF &canvasRect ) = 0;

signals:
    /************************************************
     * 功能: 发射item发生变化的信号，请求更新相应界面
     ************************************************/
    void sigRequestUpdate( void );
private:
    typedef enum _Info
    {
        DEFAULT_ORDER_VALUE = -100, // 默认ORDER值
    }Info;
    QRectF m_rGeometry;
    qint32 m_iPaintOrder;
    PrpsPlot* m_pPlot;
};

#endif // PRPSPLOTITEM_H
