#include "UHFSampleStrategy.h"
#include "systemsetting/systemsetservice.h"
#include "log/log.h"
#include "global_log.h"

/****************************
功能： 构造函数
*****************************/
UHFSampleStrategy::UHFSampleStrategy(QObject *parent)
    : QObject(parent)
{
    m_iTimerId = -1;
    m_iSampleInterval = UHF::SAMPLE_INTERVAL;
    m_eSignalState = Module::SIGNAL_STATE_INVALID;
    m_eSyncSource = Module::INVALID_SYNC_SOURCE;
    m_eSyncState = Module::Invalid_Sync;
    m_bAutoSync = false;
    m_qstrOrigionFileName = "";
    m_qstrChangeFileName = "";
}

/*************************************************
功能： 开始采集
*************************************************/
void UHFSampleStrategy::startSample(UHF::WorkMode eWorkMode)
{
    startSampleTimer(eWorkMode);
}

/*************************************************
功能： 停止采集
*************************************************/
void UHFSampleStrategy::stopSample()
{
    stopSampleTimer();
}

/*************************************************
入参：iInterval -- 采样间隔
功能： 设置采样间隔
*************************************************/
void UHFSampleStrategy::setSampleInterval( int iInterval )
{
    m_iSampleInterval = iInterval;
}

/*************************************************
返回值：int -- 定时器id
功能： 获得定时器id
*************************************************/
int UHFSampleStrategy::timerId( void )
{
    return m_iTimerId;
}

/*************************************************
功能： 开启取数据定时器
*************************************************/
void UHFSampleStrategy::startSampleTimer(UHF::WorkMode eWorkMode)
{
    if( -1 == m_iTimerId )
    {
        m_iSampleInterval = (UHF::MODE_PRPS == eWorkMode) ? SystemSetService::instance()->getPRPSSampleInterval() : UHF::SAMPLE_INTERVAL;
        m_bAutoSync = SystemSetService::instance()->getPRPSAutoSync();
        if(m_bAutoSync)
        {
            QString qstrTimeInfo = Module::getCurDateTimeFormatInfo("yyyyMMdd_hhmmsszzz");
            m_qstrOrigionFileName = QString("/media/data/origion_%1.dat").arg(qstrTimeInfo);
            m_qstrChangeFileName = QString("/media/data/change_%1.dat").arg(qstrTimeInfo);
        }

        log_debug("sample interval: %d.", m_iSampleInterval);
        m_iTimerId = startTimer(m_iSampleInterval);
    }
}

/*************************************************
功能： 关闭取数据定时器
*************************************************/
void UHFSampleStrategy::stopSampleTimer( void )
{
    if( -1 != m_iTimerId )
    {
        killTimer( m_iTimerId );
        m_iTimerId = -1;
    }
}

/**************************************
 * 功能：设置同步源
 * 输入参数：
 *      eSyncSource：同步源
 * ***************************************/
void UHFSampleStrategy::setSyncSource(Module::SyncSource eSyncSource)
{
    m_eSyncSource = eSyncSource;
    return;
}

/*************************************************
功能： 更新同步状态
入参：eSyncSource -- 同步源
     eSyncState -- 同步状态
*************************************************/
void UHFSampleStrategy::updateSyncState(Module::SyncSource eSyncSource, Module::SyncState eSyncState)
{
    //logInfo(QString("m_SyncSource: %1, sensor sync source: %2, state: %3.").arg(m_eSyncSource).arg(eSyncSource).arg(eSyncState).toLatin1().data());
    if(m_eSyncSource != (Module::SyncSource)eSyncSource)
    {
        eSyncState = Module::Not_Sync;
        emit sigSyncSourceChanged(m_eSyncSource);   //同步源不相符时，优先重新设置应用层的同步源
    }

    if(m_eSyncState != eSyncState)
    {
        m_eSyncState = eSyncState;
        emit sigSyncStateChanged(m_eSyncState);
    }

    return;
}

/*************************************************
功能： 更新采样状态
入参：stSampleState -- 采样状态
*************************************************/
void UHFSampleStrategy::updateState(const Module::SampleState &stSampleState)
{
    if(m_eSignalState != stSampleState.eSignalState)
    {
        m_eSignalState = stSampleState.eSignalState;
    }

    if(m_eSyncSource != stSampleState.eSyncSource)
    {
        m_eSyncSource = stSampleState.eSyncSource;
    }

    if(m_eSyncState != stSampleState.eSyncState)
    {
        m_eSyncState = stSampleState.eSyncState;
    }

    emit sigStateChanged(stSampleState);

    return;
}

