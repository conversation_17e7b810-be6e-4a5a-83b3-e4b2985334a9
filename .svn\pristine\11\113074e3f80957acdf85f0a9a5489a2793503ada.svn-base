/*
* Copyright (c) 2016.03，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: AEAmpWithoutMap.h
*
* 初始版本：1.0
* 作者：曹山
* 创建日期：2016年03月24日
* 摘要：该文件主要是定义了AE幅值检测数据(非图谱)存储的子类
*/

#ifndef AEAMPWITHOUTMAP_H
#define AEAMPWITHOUTMAP_H

#include "dataSave/DataSave.h"

class AEAmpWithoutMap : public DataSave
{
public:
    /************************************************
     * 函数名   : AEAmpWithoutMap
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 构造函数
     ************************************************/
    AEAmpWithoutMap();

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData);

    /************************************************
     * 函数名   : getDataWithoutMap
     * 输入参数 : baData: xml格式数据流;
     * 输出参数 : pDatas: 数据
     * 返回值   : void
     * 功能     : 将xml格式数据提取到指定格式的结构体变量中
     ************************************************/
    void getDataWithoutMap(const QByteArray& baData, void *pData);

    /************************************************
     * 函数名   : getDataByteArray
     * 输入参数 : void;
     * 输出参数 : NULL
     * 返回值   : xml格式数据流
     * 功能     : 获取xml格式数据流
     ************************************************/
    QByteArray getDataByteArray(void);

protected:
    /************************************************
     * 函数名   : getDataTypeFolder
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件夹名
     * 功能     : 获取图谱类型文件存储最上层文件夹名
     ************************************************/
    QString getDataTypeFolder(void);

    /************************************************
     * 函数名   : getFileNameSuffix
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件后缀名
     * 功能     : 获取数据文件后缀名
     ************************************************/
    QString getFileNameSuffix(void);

    /************************************************
     * 函数名   : getStringFromData
     * 输入参数 : pDatas: 数据; uiCounts: 数据个数
     * 输出参数 : NULL
     * 返回值   : 转换后的字符串
     * 功能     : 将数据转成base64的字符串
     ************************************************/
    QString getStringFromData( void *pDatas, UINT32 uiCounts);

    /************************************************
     * 函数名   : saveExtInformation
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储可扩展信息
     ************************************************/
    void saveExtInformation(XMLDocument& doc);

    /************************************************
     * 函数名   : saveRawData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储数据部分
     ************************************************/
    void saveRawData(XMLDocument& doc);


    /************************************************
     * 函数名   : parseData
     * 输入参数 : baData: 数据
     * 输出参数 : pData: 解析到的数据
     * 返回值   : void
     * 功能     : 解析数据
     ************************************************/
    void parseData(const QByteArray& baData, void *pData, const QString& strFileName = "");

private:
    AEAmpDataInfoWithoutMap *m_pAEAmpDataInfo;     //AE幅值检测数据信息(非图谱)
    QByteArray m_baData;
};

#endif // AEAMPWITHOUTMAP_H
