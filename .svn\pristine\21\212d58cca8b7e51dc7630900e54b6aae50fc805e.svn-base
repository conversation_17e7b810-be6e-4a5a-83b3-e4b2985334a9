﻿#include <QDebug>
#include <QVBoxLayout>
#include <QButtonGroup>
#include <QPainter>
#include "RadioPopup.h"

//const QString RadioPopup_RADIO_STYLE = "QRadioButton{color:rgb(255,255,255);font-size:30px;font-family:msyh}"
//                                       "QRadioButton::checked {color:rgb(255, 170, 0)}"
//                                       "QRadioButton::unchecked {color:white}"; //RadioButton的样式

const QString RadioPopup_RADIO_STYLE = "QRadioButton{color:rgb(255, 255, 255); font-size:30px;}"
                                       "QRadioButton::checked {color:rgb(255, 170, 0)}"
                                       "QRadioButton::unchecked {color:white}"; //RadioButton的样式

const QString RadioPopup_WINDOW_STYLE = "color: rgb(255,255,255)"; //窗体样式

//一些宏定义
#define RadioPopup_MAX_SPACING_RATIO 0.0125 //最大垂直间距

/*************************************************
功能： 构造函数
输入参数:
    parent -- 父控件指针
    listOptions -- 选项数组
输出参数： NULL
返回值： NULL
*************************************************************/
RadioPopup::RadioPopup( const QStringList& listOptions,  QWidget *parent )
    :PopupWidget( parent ), m_iIndex( -1 )
{
    m_listOptions = listOptions;
    createUI( m_listOptions );
}

/*************************************************
功能： 设置字符串数组列表
输入参数:
    listOptions -- 选项数组
*************************************************************/
void RadioPopup::setOptionList( const QStringList& listOptions )
{
    //原来列表为空
    if( 0 == m_listOptions.count() )
    {
        m_listOptions = listOptions;
        //创建界面
        createUI( m_listOptions );
    }
    //维数一样
    else if( m_listOptions.count() == listOptions.count() )
    {
        m_listOptions = listOptions;
        updateUI( m_listOptions );//刷新界面
    }
    else
    {
        qWarning() << "PushRadioPopup::setOptionList: input count " << listOptions.count() << " local count " << optionList().count() << ",not equal!";
    }
}

/*************************************************
功能： 获取字符串数组列表
输入参数:
    listOptions -- 选项数组
*************************************************************/
QStringList RadioPopup::optionList() const
{
    return m_listOptions;
}

/*************************************************
功能： 事件过滤器，过滤按钮导航事件等
输入参数:
    obj -- 对象
    event -- 事件
返回：
    true -- 被过滤；
    false -- 未过滤
*************************************************************/
bool RadioPopup::eventFilter(QObject *obj, QEvent *event)
{
    QWidget *btn = qobject_cast<QWidget *>(obj);
    if( (btn != NULL) )
    {
        if( QEvent::MouseButtonRelease == event->type())
        {
            hide();
        }
        else if( QEvent::KeyPress == event->type() )
        {
            int index;
            QKeyEvent *pKeyEvent = static_cast<QKeyEvent *>(event);

            if( ( Qt::Key_Down == pKeyEvent->key()) || ( Qt::Key_Left == pKeyEvent->key() ) )
            {
                index = (m_iIndex+1)%m_listOptions.count();
                setCurrentIndex( index );

                return true;
            }
            else if( ( Qt::Key_Up == pKeyEvent->key()) || ( Qt::Key_Right == pKeyEvent->key() ) )
            {
                index = (m_iIndex+m_listOptions.count()-1)%m_listOptions.count();
                setCurrentIndex( index );
                return true;
            }
            else if( ( Qt::Key_Enter == pKeyEvent->key()) || ( Qt::Key_Return == pKeyEvent->key() ) )
            {
                confirmButton( m_iIndex );
                hide();

                return true;
            }
        }
    }

    return PopupWidget::eventFilter(obj, event);
}


/*************************************************
功能： 设置视图数据
输入参数:
    iValue:当前值
*************************************************************/
void RadioPopup::setViewValue( int iValue )
{
    if( ( iValue >= 0) && ( iValue < m_listOptions.count() ) )
    {
        setCurrentIndex( iValue );
    }
    else
    {
        qWarning() << "RadioPopup::setViewValue: error, value " << iValue << " out of range!";
    }
}
/*************************************************
功能： 设置当前索引（激活）
输入参数：
        index -- 选项索引
                 -1 表示取消所有
*************************************************************/
void RadioPopup::setCurrentIndex( int index )
{
    onIndexActived( m_iIndex, false );
    m_iIndex = index;
    onIndexActived( index, true );
}

/*************************************************
功能： 响应索引激活变化
输入参数：
        index -- 选项索引
        bActive -- true:激活
                   false：取消激活
*************************************************************/
void RadioPopup::onIndexActived( int index, bool bActive )
{
    Q_UNUSED(index)
    Q_UNUSED(bActive)
}

/*************************************************
功能： 获取当前索引
返回：
    当前索引
    -1表示没有
*************************************************************/
int RadioPopup::currentIndex()
{
    return m_iIndex;
}

/*************************************************
功能： 根据设立的字符串列表建立界面
输入参数:
    listOptions -- 字符串列表
*************************************************************/
void RadioPopup::createUI( const QStringList& listOptions )
{
    Q_UNUSED(listOptions)
}

/*************************************************
功能： 根据设立的字符串列表刷新界面
输入参数:
    listOptions -- 字符串列表
*************************************************************/
void RadioPopup::updateUI( const QStringList& listOptions )
{
    Q_UNUSED(listOptions)
}

/*************************************************
功能： 槽，确认radio按钮
输入参数:
    id -- 选中的radio item选项的索引
*************************************************************/
void RadioPopup::confirmButton(int id)
{
    if( -1 != id )
    {
        setValue( id );
        m_iIndex = id;

        emit sigValueChanged( id, m_listOptions.at(id) );
    }
}

/*************************************************
功能：popup控件为SWITCH_MODE模式时，切换两个数据值
*************************************************************/
void RadioPopup::switchValue()
{
    //SWITCH_MODE模式下，只有两个选项，索引为0或者1
    ++m_iIndex;
    m_iIndex = (m_iIndex >= m_listOptions.size()) ? 0 : m_iIndex;
    confirmButton(m_iIndex);

    return;
}

