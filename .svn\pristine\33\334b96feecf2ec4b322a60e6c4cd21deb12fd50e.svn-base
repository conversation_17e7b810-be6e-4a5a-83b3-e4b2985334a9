#include "hfctprpscabeltestbgview.h"
#include "window/Window.h"
#include "hfct/HFCTConfig.h"
#include "hfct/HFCTViewConfig.h"
#include "controlButton/PopupButton.h"
#include "controlButton/RadioButton.h"
#include "messageBox/msgbox.h"
#include "View.h"
#include "appconfig.h"
#include "dataSave/DataFileInfos.h"
#include "systemsetting/shutdownpower.h"
#include "appfontmanager/appfontmanager.h"
#include "customaccesstask/taskmanager.h"
#include "mobileaccessservice.h"
#include "fileoper/fileoperutil.h"
#include "iniconfig/iniconfig.h"
//data save
#include "equipmentinfo.h"
#include "prps/prpsmapdefine.h"
#include "prps/prpddatamap.h"
#include "prps/prpsdatamap.h"
#include "mapdatafactory.h"
#include "customaccessUi/commonviewconfig.h"
#include "customaccessUi/commonitemview/commonitemlistview.h"
#include "log/log.h"
#include "iniconfig/iniconfig.h"
#include "dataspecification/dataspecification.h"
#include "dataspecification/prps/prpsspectrum.h"
#include "dataspecification/prps/prpdspectrum.h"
#include "customaccessUi/customaccessui_func.h"


typedef enum _HFCTPrpsButton_
{
    BUTTON_HFCT_SAMPLE = 0,//采样
    BUTTON_HFCT_SAVE_DATA,//保存数据
    BUTTON_HFCT_LOAD_DATA,//载入数据
    BUTTON_MORE_MENU,//配置

    //more
    BUTTON_ALTAS_TYPE,//图谱类型
    BUTTON_THRESHOLD,//阈值
    BUTTON_HFCT_GAIN,//增益
    BUTTON_HFCT_PHASE_SHIFT,//相位偏移
    BUTTON_HFCT_ACCUMULATION,//是否使用累积
    BUTTON_HFCT_SYNC_SOURCE,//同步方式
    //BUTTON_HFCT_DELETE_DATA,//删除数据
}HFCTPrpsButton;

//增益
const ButtonInfo::RadioValueConfig s_HFCTGainCfg =
{
    HFCT::GAIN_OPTIONS, HFCT::GAIN_COUNT
};

//相位偏移
const ButtonInfo::SliderValueConfig s_HFCTPhaseShift =
{
    HFCT::PHASE_MIN, HFCT::PHASE_MAX, HFCT::PHASE_STEP
};

//同步源
const ButtonInfo::RadioValueConfig s_HFCTSyncCfg =
{
    HFCT::TEXT_SYNC_OPTIONS, sizeof(HFCT::TEXT_SYNC_OPTIONS)/sizeof(char*)
};

//是否累积
const ButtonInfo::RadioValueConfig s_IsAccumulationCfg =
{
    HFCT::TEXT_ACCUMULATIVE_TIME_OPTIONS, sizeof(HFCT::TEXT_ACCUMULATIVE_TIME_OPTIONS)/sizeof(char*)
};

//图谱类型
const ButtonInfo::RadioValueConfig s_AltasTypeCfg =
{
    HFCT::TEXT_ALTAS_TYPE_OPTIONS, sizeof(HFCT::TEXT_ALTAS_TYPE_OPTIONS)/sizeof(char*)
};

//阈值 单位:%
const ButtonInfo::RadioValueConfig s_ThresholdCfg =
{
    HFCT::TEXT_THRESHOLD_OPTIONS, sizeof(HFCT::TEXT_THRESHOLD_OPTIONS)/sizeof(char*)
};
//控制按钮定义
const ButtonInfo::Info s_HFCTButtonInfo[] =
{
    { BUTTON_HFCT_SAMPLE, { ButtonInfo::COMMAND, HFCT::TEXT_STOP, NULL, ":/images/sampleControl/sample.png",NULL } },//采样
    { BUTTON_HFCT_SAVE_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL } },//保存数据
    { BUTTON_HFCT_GAIN, { ButtonInfo::RADIO, HFCT::TEXT_GAIN, NULL, ":/images/sampleControl/gain.png", &s_HFCTGainCfg } },//增益
    //{ BUTTON_HFCT_LOAD_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_LOAD_DATA, NULL, ":/images/sampleControl/importData.png",NULL } },//载入数据
    { BUTTON_MORE_MENU, { ButtonInfo::COMMAND, HFCT::TEXT_MORE_CONFIG, NULL, NULL, NULL } },//配置
};

//"更多.."按钮定义
const ButtonInfo::Info s_HFCTButtonInfoMore[] =
{
    { BUTTON_ALTAS_TYPE, { ButtonInfo::RADIO, HFCT::TEXT_ALTAS_TYPE, NULL, NULL, &s_AltasTypeCfg } },//图谱类型
    { BUTTON_THRESHOLD, { ButtonInfo::RADIO, HFCT::TEXT_PRPS_NOISEREDUCTION, NULL, NULL, &s_ThresholdCfg } },//阈值
    //{ BUTTON_HFCT_GAIN, { ButtonInfo::RADIO, HFCT::TEXT_GAIN, NULL, ":/images/sampleControl/gain.png", &s_HFCTGainCfg } },//增益
    { BUTTON_HFCT_PHASE_SHIFT, { ButtonInfo::FIXED_STEP_SLIDER, HFCT::TEXT_PHASE_ALIAS, HFCT::TEXT_DEGREE, ":/images/sampleControl/phaseShift.png", &s_HFCTPhaseShift } },//相位偏移
    { BUTTON_HFCT_ACCUMULATION, { ButtonInfo::RADIO, HFCT::TEXT_ACCUMULATIVE_TIME, NULL, ":/images/sampleControl/brandWidth.png", &s_IsAccumulationCfg } },//是否累积
    //{ BUTTON_HFCT_SYNC_SOURCE, { ButtonInfo::RADIO, HFCT::TEXT_SYNC_SOURCE, NULL, ":/images/sampleControl/syncMode.png", &s_HFCTSyncCfg } },//同步方式
    //{ BUTTON_HFCT_DELETE_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_DELETE_DATA, NULL, ":/images/sampleControl/delete.png", NULL } },//删除数据
};

const int MS_PER_MIN = 60 * 1000;  //每分钟对应的ms
const int MOVE_PERIODS_STEP = 1; //prps每次推进的周期数
const int INVALID_USER = -1;

/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
HFCTPRPSCabelTestBGView::HFCTPRPSCabelTestBGView(const QString &strTitle, QWidget *parent)
    : HFCTPrpsViewBase(strTitle, parent)
{
    registerMaps();

    QList<QString> lStrBGFileNames;
    m_pCommonItemListView = new CommonItemListView(QObject::trUtf8("Test Data List"), lStrBGFileNames);
    m_pCommonItemListView->hide();
    connect(m_pCommonItemListView, SIGNAL(sigItemSelected(qint32)), this, SLOT(onItemActivated(qint32)));

    TaskModeViewNS::AssetInfo *pAssetInfo = TaskManager::instance()->getJSONCurAssetInfo();
    if(pAssetInfo)
    {
        m_strBayName = pAssetInfo->qstrName;
    }

    initData();

    ChartWidget *pWidget = createChart(parent);
    setChart(pWidget);

    PushButtonBar *pButtonBar  = createButtonbar(parent);
    m_pSampleBtn = pButtonBar->button(BUTTON_HFCT_SAMPLE);

    createMoreConfigButtonBar(HFCT::CONTEXT, s_HFCTButtonInfoMore, sizeof(s_HFCTButtonInfoMore) / sizeof(ButtonInfo::Info));
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_ACCUMULATION)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_GAIN)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_ALTAS_TYPE)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_THRESHOLD)))->setPopupMode(PopupWidget::SWITCH_MODE);

    setButtonBarDatas();
    setChartParameters();
    setAllWorkSets();

    startSample();
    setSampleBtnText(isSampling());
    m_bPlayBacked = false;

    if(titleBar())
    {
        disconnect(titleBar(), SIGNAL(sigClicked()), this, SLOT(close()));
        connect(titleBar(), SIGNAL(sigClicked()), this, SLOT(onTitleBarClicked()));
    }
}

/*************************************************
功能： 析构
*************************************************************/
HFCTPRPSCabelTestBGView::~HFCTPRPSCabelTestBGView( )
{
    saveConfig(); // 存储到配置文件中

    if(m_pCommonItemListView)
    {
        delete m_pCommonItemListView;
        m_pCommonItemListView = NULL;
    }
}

void HFCTPRPSCabelTestBGView::registerMaps()
{
    MapDataFactory::registerClass<PRPSDataMap>(XML_FILE_NODE_PRPS);//图谱根节点tag名
    MapDataFactory::registerClass<PRPDDataMap>(XML_FILE_NODE_PRPD);//图谱根节点tag名
    return;
}

/************************************************
 * 函数名   : onSignalChanged
 * 输入参数 : eState: 信号状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 槽函数，信号状态的改变
 ************************************************/
void HFCTPRPSCabelTestBGView::onSignalChanged(Module::SignalState eState)
{
    bool bConnected = (eState == Module::SIGNAL_STATE_EXIST) ? true : false;
    m_pChart->setConnected(bConnected);
    return;
}

/************************************************
 * 函数名   : onSyncStateChanged
 * 输入参数 : eState: 同步状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 槽函数，同步状态的改变
 ************************************************/
void HFCTPRPSCabelTestBGView::onSyncStateChanged(Module::SyncState eState)
{
    if(m_eSyncState != eState)
    {
        m_eSyncState = eState;
        m_pChart->setSync((PrpsGlobal::SyncSource)m_eSyncSource, (PrpsGlobal::SyncState)eState);
    }
    return;
}

/************************************************
 * 函数名   : setChartParameters
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 给图谱设置参数
 ************************************************/
void HFCTPRPSCabelTestBGView::setChartParameters()
{
    m_pChart->setAltasType(m_eAltasType);

    m_pChart->setSync((PrpsGlobal::SyncSource)m_eSyncSource, (PrpsGlobal::SyncState) m_eSyncState);

    /*系统频率*/
    m_pChart->setPowerFreq(PrpsGlobal::FREQ_50);

    /*运行状态*/
    m_pChart->setRunningMode(isSampling());

    /*设置量程*/
    m_pChart->setRangeMin(HFCT::GAIN_VALUES[m_eGain]);
    m_pChart->setRangeMax(HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE);

    /*相位偏移*/
    m_pChart->setPhaseOffset(m_iPhaseAlias);

    m_pChart->setPeriodNum(m_iSysPeriod);

    updateAccumulativeTime();
    m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
    m_pChart->setPRPDThresholdPercentage(m_fThresholdPercentage);

    return;
}

/************************************************
 * 函数名   : setAllWorkSets
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 设置所有的工作参数
 ************************************************/
void HFCTPRPSCabelTestBGView::setAllWorkSets()
{
    setWorkMode(HFCT::MODE_PRPS);
    setGain(m_eGain);
    setSyncSource(m_eSyncSource);
    return;
}

/************************************************
 * 函数名   : initData
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 初始化数据成员
 ************************************************/
void HFCTPRPSCabelTestBGView::initData()
{
    m_pConfig = ConfigManager::instance()->config();

    m_pSampleBtn = NULL;
    m_pLoadingWidget = NULL;

    m_eSignalState = Module::SIGNAL_STATE_NONE;// 信号状态
    m_eSyncState = Module::Not_Sync;//同步状态

    m_usMaxSpectrumValue = 0;
    m_vMaxValueVector.clear();
    setConfigData();

    return;
}

/*************************************************
功能： 保存设置
*************************************************************/
bool HFCTPRPSCabelTestBGView::saveConfig(void)
{
    if(m_pConfig)
    {
        int iGroup = HFCT::GROUP_HFCT_PRPS;
        m_pConfig->beginGroup(Module::GROUP_HFCT);

        m_pConfig->setValue(m_eGain, HFCT::KEY_GAIN);
        m_pConfig->setValue(m_iPhaseAlias, HFCT::KEY_PHASEALIAS);
        m_pConfig->setValue(m_eSyncSource, HFCT::KEY_SYNC_SOURCE);
        m_pConfig->setValue( m_iAccumulationTime, HFCT::KEY_PRPS_ACCUMULATION_TIME, iGroup );
        m_pConfig->setValue( m_fThresholdPercentage, HFCT::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup );
        m_pConfig->setValue( m_eThresholdMode, HFCT::KEY_PRPS_THRESHOLD_MODE, iGroup );
        m_pConfig->endGroup();

        //未进行修改，无需保存
        //m_pConfig->beginGroup( Module::GROUP_APP );
        //m_pConfig->setValue(m_ucSysFreq, APPConfig::KEY_SYS_FREQ);
        //m_pConfig->endGroup();
    }

    return true;
}

/************************************************
 * 函数名   : setSampleBtnText
 * 输入参数 : bIsSampling: 是否处于采样状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 更新采样状态设置采样按钮显示的文本
 ************************************************/
void HFCTPRPSCabelTestBGView::setSampleBtnText(bool bIsSampling)
{
    if(bIsSampling)
    {
        m_pSampleBtn->setTitle(HFCT_VIEW_CONFIG_TRANSLATE(HFCT::TEXT_PAUSE));
    }
    else
    {
        m_pSampleBtn->setTitle(HFCT_VIEW_CONFIG_TRANSLATE(HFCT::TEXT_RUN));
    }

    return;
}

/************************************************
 * 函数名   : onDataRead
 * 输入参数 : stData: 采样数据
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 槽函数，处理收到的采样数据
 ************************************************/
void HFCTPRPSCabelTestBGView::onDataRead(HFCT::PRPSData stData, MultiServiceNS::USERID userId)
{
    if((getUserId() != INVALID_USER) && (getUserId() == userId) && isSampling())
    {
        QVector<double> vRawData;
        vRawData.clear();

        m_stPRPSFeateurInfo.dSpecMaxVal = m_pChart->rangeMax();
        m_stPRPSFeateurInfo.dSpecMinVal = m_pChart->rangeMin();
        m_stPRPSFeateurInfo.iPhaseNum = m_pChart->phaseCount();
        m_stPRPSFeateurInfo.iPeriodNum = m_pChart->periodCount();

        for(int i = 0, iSize = stData.vSpectrum.size(); i < iSize; ++i)
        {
            if(stData.vSpectrum[i] < Module::ZERO)
            {
                stData.vSpectrum[i] = 0;
            }

            vRawData.append((double)stData.vSpectrum[i]);
            m_stPRPSFeateurInfo.qvtDataIn.append(static_cast<double>(stData.vSpectrum[i]));
        }

        if(HFCT::THRESHOLD_AUTO == m_eThresholdMode && DealData::denoisePRPSData(m_stPRPSFeateurInfo))
        {
            m_fThresholdPercentage = static_cast<float>(m_stPRPSFeateurInfo.dThresholdDbVal);
            m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
            m_pChart->setPRPDThresholdPercentage(m_fThresholdPercentage);
            //log_debug("--------------------threshold: %f.", m_fThresholdPercentage);
        }

        m_pChart->setData(vRawData);// 往图谱添加数据

        if(m_vMaxValueVector.size() >= m_ucSysFreq)// 若容器中数据的size超出制定个数，将最先加入的数据删除
        {
            m_vMaxValueVector.pop_front();
        }

        m_vMaxValueVector.append(PRPSMaxValue((double)stData.cMaxSpectrum, stData.eSpectrumState));

        //计算每周期最大值集合中的最大值，即当前显示数据中的最大值
        double dMaxValue = 0;
        HFCT::SpectrumState eState = m_vMaxValueVector.first().eSpectrumState;
        for( int i = 0, iSize = m_vMaxValueVector.size(); i < iSize; ++i )
        {
            if(m_vMaxValueVector.at(i).cMaxValue > dMaxValue)
            {
                dMaxValue = m_vMaxValueVector.at(i).cMaxValue;
                eState = m_vMaxValueVector.at(i).eSpectrumState;
            }
        }

        m_usMaxSpectrumValue = dMaxValue;
        m_eSpectrumState = eState;
        m_pChart->setMaxSpectrum(dMaxValue, (PrpsGlobal::SpectrumState)eState);
    }
    else
    {
        //illegal
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
    }

    return;
}

/*************************************************
功能： 槽，响应诊断结果
输入参数：
        qspDiagResultInfo -- 诊断结果
*************************************************************/
void HFCTPRPSCabelTestBGView::onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo)
{
    Q_UNUSED(qspDiagResultInfo);
    return;
}

void HFCTPRPSCabelTestBGView::onTitleBarClicked(void)
{
    close();
    return;
}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void HFCTPRPSCabelTestBGView::keyPressEvent(QKeyEvent* event)
{
    if(event->key() == Qt::Key_Escape)
    {
        close();
    }
    else
    {
        SampleChartView::keyPressEvent(event);
    }
    return;
}

void HFCTPRPSCabelTestBGView::onItemActivated(qint32 uiItem)
{
    if(m_eOperationType == OPERATION_DELETE)
    {
        deleteSelectedFile(uiItem);
    }
    else if(m_eOperationType == OPERATION_LOAD)
    {
        //停止采样
        bool bIsSampleStopped = false;
        if(isSampling())
        {
            stopSample();
            bIsSampleStopped = true;
        }
        if(loadSelectedFile(uiItem))
        {
            buttonBar()->buttons()[BUTTON_HFCT_SAVE_DATA]->setEnabled(false);
            buttonBar()->buttons()[BUTTON_HFCT_SAVE_DATA]->setActive(false);
        }
        else
        {
            if(bIsSampleStopped)
            {
                setChartParameters();
                startSample();
            }
        }

        setSampleBtnText(isSampling());
    }
    else
    {
        logError("item active error");
    }
    return;
}
void HFCTPRPSCabelTestBGView::deleteSelectedFile(qint32 uiItem)
{
    QList<QString> lStrBGFileNames;

    if(uiItem >= lStrBGFileNames.size())
    {
        logError("select file index error.");
        return;
    }

    QString strTempFile  = lStrBGFileNames.at(uiItem);
    QFile file (strTempFile);
    if(file.exists())
    {
        file.remove();
        lStrBGFileNames.removeAt(uiItem);
        //m_pSubTask->deleteBGFile(strTempFile);

        m_pCommonItemListView->clear();
        m_pCommonItemListView->addItems( lStrBGFileNames );
    }
    else
    {
        logError(QString("file (%1) not exist.").arg(strTempFile));
    }

    return;
}

bool HFCTPRPSCabelTestBGView::loadSelectedFile(qint32 uiItem)
{
    QList<QString> lStrBGFileNames;

    bool bSuccess = false;

    if(uiItem >= lStrBGFileNames.size())
    {
        logError("select file index error.");
        return false;
    }

    QString strTempFile  = "";
    m_pCommonItemListView->hide();
    if(loadTestDataFile(strTempFile))
    {
        m_bPlayBacked = true;
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }

    return bSuccess;
}

/*************************************************
功能： 保存图谱信息部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void HFCTPRPSCabelTestBGView::fillPRPSDataInfo(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum)
{
    pPRPSSpectrum->setStorageDataType(DataSpecificationNS::DATA_TYPE_DOUBLE);

    DataSpecificationNS::PRPSExtInformation stPRPSExtInformation;
    stPRPSExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_DB;
    stPRPSExtInformation.fAmpLowerLimit = HFCT::GAIN_VALUES[m_eGain];
    stPRPSExtInformation.fAmpUpperLimit = HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE;

    stPRPSExtInformation.eFrequencyBand = DataSpecificationNS::BAND_DEFAULT;
    stPRPSExtInformation.iPhaseWindowCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stPRPSExtInformation.iQuantizedAmplitude = 0;
    stPRPSExtInformation.iPowerFreqCycleCount = m_pChart->prpsData().size() / stPRPSExtInformation.iPhaseWindowCount;//总是认为数据是50*60(50Hz下)
    if(stPRPSExtInformation.iPowerFreqCycleCount < 50)
    {
        stPRPSExtInformation.iPowerFreqCycleCount = 50;
    }
    memset(stPRPSExtInformation.aucPDTypeProbability, 0, sizeof(stPRPSExtInformation.aucPDTypeProbability));

    stPRPSExtInformation.eDataJudgmentFlag = static_cast<DataSpecificationNS::DataJudgmentFlag>(m_eSpectrumState);

    stPRPSExtInformation.sGain = -HFCT::GAIN_VALUES[m_eGain];
    logInfo(QString("sync source: %1.").arg(m_eSyncSource));
    stPRPSExtInformation.eSyncSource = static_cast<DataSpecificationNS::SyncSource>(m_eSyncSource + 1);
    stPRPSExtInformation.ucSyncState = m_eSyncState;
    stPRPSExtInformation.fSyncFrequency = -1;

    pPRPSSpectrum->setPRPSExtInformation(stPRPSExtInformation);
}

/*************************************************
功能： 保存图谱信息部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void HFCTPRPSCabelTestBGView::fillPRPDDataInfo(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum)
{
    pPRPDSpectrum->setStorageDataType(DataSpecificationNS::DATA_TYPE_DOUBLE);

    DataSpecificationNS::PRPDExtInformation stPRPDExtInformation;
    stPRPDExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_DB;
    stPRPDExtInformation.fAmpLowerLimit = HFCT::GAIN_VALUES[m_eGain];
    stPRPDExtInformation.fAmpUpperLimit = HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE;

    stPRPDExtInformation.eFrequencyBand = DataSpecificationNS::BAND_DEFAULT;

    stPRPDExtInformation.iPhaseWindowCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stPRPDExtInformation.iQuantizedAmplitude = PRPSMapNS::QUANTIFICATION_AMP;
    stPRPDExtInformation.iPowerFreqCycleCount = m_pChart->prpdPeriodCount();
    memset(stPRPDExtInformation.aucPDTypeProbability, 0, sizeof(stPRPDExtInformation.aucPDTypeProbability));

    stPRPDExtInformation.eDataJudgmentFlag = DataSpecificationNS::DATA_NORMAL;
    stPRPDExtInformation.sGain = -HFCT::GAIN_VALUES[m_eGain];
    stPRPDExtInformation.eSyncSource = static_cast<DataSpecificationNS::SyncSource>(m_eSyncSource + 1);
    stPRPDExtInformation.ucSyncState = 1;
    stPRPDExtInformation.fSyncFrequency = -1;
    pPRPDSpectrum->setPRPDExtInformation(stPRPDExtInformation);
}

void HFCTPRPSCabelTestBGView::fillPRPSData(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum)
{
    QVector< double > vecPRPSData = m_pChart->prpsData();
    //step5 set map data
    int iDataPointNum = vecPRPSData.size();
    int iPhaseShitStep = m_iPhaseAlias / (360 / PRPSMapNS::PHASE_INTERVAL_CNT);
    int iRealPeriod = iDataPointNum / PRPSMapNS::PHASE_INTERVAL_CNT;
    if(iRealPeriod > 50)//todo hard code
    {
        iRealPeriod = 50;
    }

    DataSpecificationNS::PRPSData stPRPSData;
    QVector<double> qvtPRPSData;
    qvtPRPSData.resize(PRPSMapNS::PHASE_INTERVAL_CNT * 50);

    for(int i = 0; i < iRealPeriod; ++i)
    {
        for(int j = 0, iPeriodIndex = 0; j < PRPSMapNS::PHASE_INTERVAL_CNT; ++j)
        {
            if(iRealPeriod < 50)
            {
                iPeriodIndex =  50 + i - iRealPeriod;
            }
            else
            {
                iPeriodIndex = i;
            }
            int iNewPhaseIndex = (j + iPhaseShitStep) % PRPSMapNS::PHASE_INTERVAL_CNT;

            qvtPRPSData[iPeriodIndex*PRPSMapNS::PHASE_INTERVAL_CNT + iNewPhaseIndex] = vecPRPSData.at(i*PRPSMapNS::PHASE_INTERVAL_CNT + j);
        }
    }
    stPRPSData.qbaPDSpectrumData.resize(qvtPRPSData.size() * sizeof(double));
    memcpy(stPRPSData.qbaPDSpectrumData.data(), &qvtPRPSData[0], stPRPSData.qbaPDSpectrumData.size());
    pPRPSSpectrum->setPRPSData(stPRPSData);
}


/*************************************************
功能： 保存图谱数据部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void HFCTPRPSCabelTestBGView::fillPRPDData(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum)
{
    int iDataCnt =  PRPSMapNS::PHASE_INTERVAL_CNT * PRPSMapNS::QUANTIFICATION_AMP;

    QVector< qint16 > vecPRPDData = m_pChart->prpdData();

    int iAmpAreaCnt = vecPRPDData.size() / PRPSMapNS::PHASE_INTERVAL_CNT;
    int iPhaseShitStep = m_iPhaseAlias / (360 / PRPSMapNS::PHASE_INTERVAL_CNT);

    DataSpecificationNS::PRPDData stPRPDData;
    QVector<double> qvtPRPDData;
    qvtPRPDData.resize(iDataCnt);
    for(int  j = 0; j < PRPSMapNS::PHASE_INTERVAL_CNT; ++j)
    {
        for(int  i = 0; i< iAmpAreaCnt; ++i)
        {
            int iNewPhaseIndex = (j + iPhaseShitStep) % PRPSMapNS::PHASE_INTERVAL_CNT;

            qint16 sPRPD = vecPRPDData.at(i*PRPSMapNS::PHASE_INTERVAL_CNT + j);
            if(sPRPD <= 0)
            {
                sPRPD = 0;
            }
            qvtPRPDData[iNewPhaseIndex*PRPSMapNS::QUANTIFICATION_AMP + i] = sPRPD;
        }
    }

    stPRPDData.qbaPDSpectrumData.resize(qvtPRPDData.size() * sizeof(double));
    memcpy(stPRPDData.qbaPDSpectrumData.data(), &qvtPRPDData[0], stPRPDData.qbaPDSpectrumData.size());
    pPRPDSpectrum->setPRPDData(stPRPDData);
}

INT32 HFCTPRPSCabelTestBGView::quantizationAmpSection(INT32 iAmp, float fAmpLower, float fAmpUpper, INT32 iQuantizationAmpCnt)
{
    //dbg_info("iAmp is %d, fAmpLower is %f, fAmpUpper is %f, iQuantizationAmp is %d\n", iAmp, fAmpLower, fAmpUpper, iQuantizationAmp);
    float fRangePerSection = (fAmpUpper - fAmpLower) / (float)iQuantizationAmpCnt;
    float fAmp = (iAmp < Module::ZERO) ? 0 : (float)iAmp;
    int section = fAmp / fRangePerSection;
    return section;
}

/*************************************************
功能： 保存数据
返回：
    保存结果
*************************************************************/
QString HFCTPRPSCabelTestBGView::saveTestData()
{
    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return "";
    }

    DataSpecificationNS::DataSpecification* pDataSpecification = new DataSpecificationNS::DataSpecification;//当前数据文件
    pDataSpecification->setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());

    //创建图谱保存对象
    DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum = new DataSpecificationNS::PRPSSpectrum;

    //设置头部信息
    pPRPSSpectrum->setSpectrumCharacter(DataSpecificationNS::PROPERTY_BG_NOISE);
    pPRPSSpectrum->setSpectrumTypeCode(DataSpecificationNS::SPECTRUM_CODE_HFCT_PRPS);
    TaskModeViewNS::AssetInfo *pAssetInfo = TaskManager::instance()->getJSONCurAssetInfo();
    if(pAssetInfo)
    {
        pPRPSSpectrum->setEquipmentName(pAssetInfo->qstrName);
        pPRPSSpectrum->setEquipmentCode(pAssetInfo->qstrId);
        pPRPSSpectrum->setTestPointName(pAssetInfo->qstrTestPosition);
        pPRPSSpectrum->setTestPointCode("");
    }

    //设置PRPS ext信息
    fillPRPSDataInfo(pPRPSSpectrum);

    //设置PRPS数据内容
    fillPRPSData(pPRPSSpectrum);

    //设置PRPD ext信息
    DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum = new DataSpecificationNS::PRPDSpectrum;

    //设置头部信息
    pPRPDSpectrum->setSpectrumCharacter(DataSpecificationNS::PROPERTY_BG_NOISE);
    pPRPDSpectrum->setSpectrumTypeCode(DataSpecificationNS::SPECTRUM_CODE_HFCT_PRPD);
    if(pAssetInfo)
    {
        pPRPDSpectrum->setEquipmentName(pAssetInfo->qstrName);
        pPRPDSpectrum->setEquipmentCode(pAssetInfo->qstrId);
        pPRPDSpectrum->setTestPointName(pAssetInfo->qstrTestPosition);
        pPRPDSpectrum->setTestPointCode("");
    }

    //设置PRPD ext信息
    fillPRPDDataInfo(pPRPDSpectrum);

    //设置PRPD数据内容
    fillPRPDData(pPRPDSpectrum);

    pDataSpecification->addSpectrum(pPRPSSpectrum);
    pDataSpecification->addSpectrum(pPRPDSpectrum);

    CustomAccessView::CustomAccessUIFunc::setDataFileHead(pDataSpecification);
    m_strSavedPath = CustomAccessView::CustomAccessUIFunc::getBinaryDataFileSavePath(TaskManager::instance()->getCurJSONTaskTestDataSavePath());
    if(pDataSpecification->saveAsBinary(m_strSavedPath))
    {
        TaskManager::instance()->setJSONTaskBGNFileName(FileOperUtil::getFileName(m_strSavedPath));
        TaskManager::instance()->setJSONTaskBGNTested();

        emit sigTested();

        IniConfig::writeHfctPrpsInfo(QFileInfo(m_strSavedPath).fileName(), m_eSpectrumState);
    }
    else
    {
        m_strSavedPath = "";
    }

    delete pDataSpecification;
    pDataSpecification = NULL;
    return m_strSavedPath;
}

/************************************************
 * 函数名   : createChart
 * 输入参数 : parent---父控件
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 创建图谱
 ************************************************/
ChartWidget* HFCTPRPSCabelTestBGView::createChart(QWidget *parent)
{
    Q_UNUSED(parent)
    ChartWidget *pWidget = new ChartWidget;
    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->setContentsMargins(0, 0, 0, 0);
    QVBoxLayout *labelLayout = new QVBoxLayout;

    m_pBayNameLabel = new QLabel;

    QPalette labelPalette;
    QPalette bgPalette;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    SystemSet::PRPSBGColor eColor = (SystemSet::PRPSBGColor) pConfig->value(APPConfig::KEY_PRPS_BG_COLOR).toInt();
    pConfig->endGroup();
    if(eColor == SystemSet::PRPS_BG_COLOR_GRAY)
    {
        labelPalette.setColor(QPalette::WindowText,Qt::white);
        bgPalette.setColor( QPalette::Background,QColor(128, 128, 128) );
    }
    else
    {
        labelPalette.setColor(QPalette::WindowText,Qt::black);
        bgPalette.setColor( QPalette::Background,Qt::white );
    }

    //设置style
    QFont font = AppFontManager::instance()->getAppCurFont();
    font.setPointSize(20);

    m_pBayNameLabel->setFont(font);
    m_pBayNameLabel->setText(m_strBayName);
    m_pBayNameLabel->setPalette(labelPalette);

    labelLayout->addWidget( m_pBayNameLabel,Qt::AlignLeft );

    //图谱
    int iMax = HFCT::GAIN_BASE + HFCT::GAIN_VALUES[m_eGain];
    m_pChart = new HfctPrpsUnionView(PERIOD_CNT, PERIOD_CNT, PHASE_CNT, iMax, HFCT::CHART_MIN_VALUE);
    m_pChart->setFixedHeight(CHART_HEIGHT);
    m_pChart->setPrpdContentsMargins(0, PRPD_MARGIN, 0, PRPD_MARGIN);
    m_pChart->setPeriodNum(m_iSysPeriod);

    vLayout->addLayout(labelLayout);
    vLayout->addWidget(m_pChart);

    pWidget->setLayout(vLayout);
    pWidget->setFixedWidth(Window::WIDTH);

    setPalette(bgPalette);
    setAutoFillBackground(true);

    return pWidget;
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void HFCTPRPSCabelTestBGView::onButtonValueChanged(int id, int iValue)
{
    switch(id)
    {
    case BUTTON_HFCT_PHASE_SHIFT://相位偏移
    {
        m_iPhaseAlias = iValue;
        m_pChart->setPhaseOffset(m_iPhaseAlias);
    }
        break;
    case BUTTON_HFCT_GAIN://增益
    {
        if((HFCT::Gain)iValue != m_eGain)
        {
            m_eGain = (HFCT::Gain)iValue;
            setGain(m_eGain);
            m_pChart->clearData();
            m_pChart->setRangeMin(HFCT::GAIN_VALUES[m_eGain]);
            m_pChart->setRangeMax(HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE);
        }
    }
        break;
    case BUTTON_HFCT_SYNC_SOURCE://同步方式
    {
        m_eSyncSource = (Module::SyncSource) (iValue + WIRELESS_SYNC);
        setSyncSource(m_eSyncSource);
        m_pChart->setSync((PrpsGlobal::SyncSource) m_eSyncSource, (PrpsGlobal::SyncState)m_eSyncState);
    }
        break;
    case BUTTON_HFCT_ACCUMULATION://是否累积
    {
        m_iAccumulationTime = iValue;

        updateAccumulativeTime();
    }
        break;
    case BUTTON_ALTAS_TYPE://图谱类型
    {
        m_eAltasType = static_cast<PhaseAbstractView::AltasType>(iValue);
        m_pChart->setAltasType(m_eAltasType);
    }
        break;
    case BUTTON_THRESHOLD://阈值
    {
        m_eThresholdMode = static_cast<HFCT::ThresholdMode>(iValue);
        updateThresholdPercentage();
        m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
        m_pChart->setPRPDThresholdPercentage(m_fThresholdPercentage);
    }
        break;
    default:
    {
        logError(QString("invalid btn id: %1").arg(id));
    }
        break;
    }

    return;
}

void HFCTPRPSCabelTestBGView::getFileHead(DataSpecificationNS::DataSpecification* pDataSpecification)
{
    DataSpecificationNS::SpectrumDataFileHead stSpectrumDataFileHead;
    pDataSpecification->getSpectrumDataFileHead(stSpectrumDataFileHead);
    m_pHFCTPRPSPRPDDataInfo->ucFreq = stSpectrumDataFileHead.fSystemFrequency;
    m_pHFCTPRPSPRPDDataInfo->stPRPSHeadInfo.strSubstationName = stSpectrumDataFileHead.qstrStationName;
    m_pHFCTPRPSPRPDDataInfo->stPRPDHeadInfo.strSubstationName = stSpectrumDataFileHead.qstrStationName;
}

void HFCTPRPSCabelTestBGView::getPRPSMapHead(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum)
{
    DataSpecificationNS::SpectrumHead stSpectrumHead;
    pPRPSSpectrum->getSpectrumHead(stSpectrumHead);
    m_pHFCTPRPSPRPDDataInfo->stPRPSHeadInfo.strDeviceName = stSpectrumHead.qstrEquipmentName;
    m_pHFCTPRPSPRPDDataInfo->stPRPSHeadInfo.eDataPrimitiveType = static_cast<DataFileNS::DataPrimitiveType>(stSpectrumHead.eStorageDataType);
}

void HFCTPRPSCabelTestBGView::getPRPDMapHead(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum)
{
    DataSpecificationNS::SpectrumHead stSpectrumHead;
    pPRPDSpectrum->getSpectrumHead(stSpectrumHead);
    m_pHFCTPRPSPRPDDataInfo->stPRPDHeadInfo.strDeviceName = stSpectrumHead.qstrEquipmentName;
    m_pHFCTPRPSPRPDDataInfo->stPRPDHeadInfo.eDataPrimitiveType = static_cast<DataFileNS::DataPrimitiveType>(stSpectrumHead.eStorageDataType);
}

void HFCTPRPSCabelTestBGView::getPRPSMapInfo(const DataSpecificationNS::PRPSExtInformation* pPRPSExtInformation)
{
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.eSyncSource = static_cast<DataFileNS::SyncSource>(pPRPSExtInformation->eSyncSource);
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.ucSyncState = pPRPSExtInformation->ucSyncState;
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.iPhaseIntervalCount = pPRPSExtInformation->iPhaseWindowCount;
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.iQuantificationAmp = pPRPSExtInformation->iQuantizedAmplitude;
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.iPowerFreCycleCount = pPRPSExtInformation->iPowerFreqCycleCount;
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.eAmpUnit = static_cast<DataFileNS::AmpUnit>(pPRPSExtInformation->eAmpUnit);
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.eBandWidth = static_cast<DataFileNS::MapBandWidth>(pPRPSExtInformation->eFrequencyBand);
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.sGain = pPRPSExtInformation->sGain;
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.eDataSign = static_cast<PRPSMapNS::MapDataSign>(pPRPSExtInformation->eDataJudgmentFlag);
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.fAmpLowerLimit = pPRPSExtInformation->fAmpLowerLimit;
    m_pHFCTPRPSPRPDDataInfo->stPRPSInfo.fAmpUpperLimit = pPRPSExtInformation->fAmpUpperLimit;
}

/****************************
功能： 获取图谱ext数据
输入参数:
    pMap -- 文件解析得到的图谱
    iDataPointCount -- 图谱数据数量
*****************************/
void HFCTPRPSCabelTestBGView::getPRPDMapInfo(const DataSpecificationNS::PRPDExtInformation* pPRPDExtInformation)
{
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.eSyncSource = static_cast<DataFileNS::SyncSource>(pPRPDExtInformation->eSyncSource);
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.ucSyncState = pPRPDExtInformation->ucSyncState;
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.iPhaseIntervalCount = pPRPDExtInformation->iPhaseWindowCount;
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.iQuantificationAmp = pPRPDExtInformation->iQuantizedAmplitude;
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.iPowerFreCycleCount = pPRPDExtInformation->iPowerFreqCycleCount;
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.eAmpUnit = static_cast<DataFileNS::AmpUnit>(pPRPDExtInformation->eAmpUnit);
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.eBandWidth = static_cast<DataFileNS::MapBandWidth>(pPRPDExtInformation->eFrequencyBand);
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.sGain = pPRPDExtInformation->sGain;
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.eDataSign = static_cast<PRPSMapNS::MapDataSign>(pPRPDExtInformation->eDataJudgmentFlag);
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.fAmpLowerLimit = pPRPDExtInformation->fAmpLowerLimit;
    m_pHFCTPRPSPRPDDataInfo->stPRPDInfo.fAmpUpperLimit = pPRPDExtInformation->fAmpUpperLimit;
}

/****************************
功能： 回放文件
输入参数:
    strFileName -- 文件名
*****************************/
bool HFCTPRPSCabelTestBGView::loadTestDataFile( const QString& strFileName)
{
    HFCTPRPSPRPDDataInfo sPlayBackDataInfo;
    if(!getData(strFileName, &sPlayBackDataInfo))
    {
        return false;
    }

    displayMap(sPlayBackDataInfo, strFileName);
    return true;
}

bool HFCTPRPSCabelTestBGView::getData(const QString& strFileName, void *pData)
{
    m_pHFCTPRPSPRPDDataInfo = (HFCTPRPSPRPDDataInfo*)pData;

    DataSpecificationNS::DataSpecification dataSpecification;
    dataSpecification.setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());

    //step1 open data file
    if(!dataSpecification.parseBinaryFromFile(strFileName))
    {
        return false;
    }

    //step2 get displayed file head
    getFileHead(&dataSpecification);

    DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum = dynamic_cast<DataSpecificationNS::PRPSSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_HFCT_PRPS));
    if(NULL == pPRPSSpectrum)
    {
        return false;
    }

    DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum = dynamic_cast<DataSpecificationNS::PRPDSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_HFCT_PRPD));
    if(NULL == pPRPDSpectrum)
    {
        return false;
    }

    getPRPSMapHead(pPRPSSpectrum);
    getPRPDMapHead(pPRPDSpectrum);

    DataSpecificationNS::PRPSExtInformation stPRPSExtInformation;
    pPRPSSpectrum->getPRPSExtInformation(stPRPSExtInformation);
    getPRPSMapInfo(&stPRPSExtInformation);

    DataSpecificationNS::PRPDExtInformation stPRPDExtInformation;
    pPRPDSpectrum->getPRPDExtInformation(stPRPDExtInformation);
    getPRPDMapInfo(&stPRPDExtInformation);

    DataSpecificationNS::PRPSData stPRPSData;
    pPRPSSpectrum->getPRPSData(stPRPSData);
    int iDataPointNum = stPRPSExtInformation.iPhaseWindowCount * stPRPSExtInformation.iPowerFreqCycleCount;
    m_pHFCTPRPSPRPDDataInfo->vecPRPSData.resize(iDataPointNum);
    memcpy(&m_pHFCTPRPSPRPDDataInfo->vecPRPSData[0], stPRPSData.qbaPDSpectrumData.data(), iDataPointNum * sizeof(double));

    DataSpecificationNS::PRPDData stPRPDData;
    pPRPDSpectrum->getPRPDData(stPRPDData);
    iDataPointNum = stPRPDExtInformation.iPhaseWindowCount * stPRPDExtInformation.iQuantizedAmplitude;
    m_pHFCTPRPSPRPDDataInfo->vecPRRepeatyData.resize(iDataPointNum);
    memcpy(&m_pHFCTPRPSPRPDDataInfo->vecPRRepeatyData[0], stPRPDData.qbaPDSpectrumData.data(), iDataPointNum * sizeof(double));

    return true;
}

/****************************
功能： 在回放中显示
输入参数: void
*****************************/
void HFCTPRPSCabelTestBGView::displayMap(HFCTPRPSPRPDDataInfo &stDataInfo, const QString& strFileName)
{
    m_pChart->clearData();

    //定制接入终端的hfct数据格式和常规hfct数据格式不一致，这里不显示
    //m_pChart->setPowerFreq( (PrpsGlobal::Frequency)( stDataInfo.ucFreq ));

    //log_debug("sync source: %d, state: %d.", stDataInfo.stPRPSInfo.eSyncSource, stDataInfo.stPRPSInfo.ucSyncState);
    //定制接入终端的hfct数据格式和常规hfct数据格式不一致，这里不显示
    //m_pChart->setSync((PrpsGlobal::SyncSource)(stDataInfo.stPRPSInfo.eSyncSource - 1), (PrpsGlobal::SyncState)stDataInfo.stPRPSInfo.ucSyncState);
    m_pChart->clearSyncText();

    /*设置量程*/
    m_pChart->setRangeMin(stDataInfo.stPRPSInfo.fAmpLowerLimit);
    m_pChart->setRangeMax(stDataInfo.stPRPSInfo.fAmpUpperLimit);

    //是否无信号状态没有保存到数据文件，回放时，先简单默认不显示无信号
    m_pChart->setConnected(true);

    QVector<qint16> prpdData;
    prpdData.clear();
    double dPRPulseCnt;

    //NOTE 如果是累计模式，iPeriodCnt的计算不正确
    int iPeriodCnt = stDataInfo.vecPRRepeatyData.size() / stDataInfo.stPRPDInfo.iPhaseIntervalCount;

    for(int i = 0; i < stDataInfo.stPRPDInfo.iQuantificationAmp; ++i)
    {
        for(int j = 0; j < stDataInfo.stPRPDInfo.iPhaseIntervalCount; ++j)
        {
            dPRPulseCnt = stDataInfo.vecPRRepeatyData.at(j * stDataInfo.stPRPDInfo.iQuantificationAmp + i);
            prpdData.append((qint16)(dPRPulseCnt));
        }
    }

    m_pChart->setPeriodNum(iPeriodCnt);
    m_pChart->addPlayBackData(stDataInfo.vecPRPSData, prpdData, stDataInfo.stPRPDInfo.iPowerFreCycleCount);

    double dMax = 0;
    if(stDataInfo.vecPRPSData.size() > 0)
    {
        dMax = stDataInfo.vecPRPSData.at(0);
        for(int i = 1, iSize = stDataInfo.vecPRPSData.size(); i < iSize; ++i)
        {
            if(stDataInfo.vecPRPSData.at(i) > dMax)
            {
                dMax = stDataInfo.vecPRPSData.at(i);
            }
        }
    }

    m_pChart->setMaxSpectrum(dMax, (PrpsGlobal::SpectrumState)(stDataInfo.stPRPSInfo.eDataSign));

    m_pChart->setPhaseOffset(0);

    m_pChart->setPeriodNum(m_iSysPeriod);

    return;
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void HFCTPRPSCabelTestBGView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case BUTTON_HFCT_SAMPLE://采样
    {
        if(isSampling())
        {
            stopSample();
        }
        else
        {
            m_pChart->clearData();
            setChartParameters();
            startSample();

            if(!(buttonBar()->buttons()[BUTTON_HFCT_SAVE_DATA]->isEnabled()))
            {
                buttonBar()->buttons()[BUTTON_HFCT_SAVE_DATA]->setEnabled(true);
            }
        }

        setSampleBtnText(isSampling());
    }
        break;

    case BUTTON_HFCT_SAVE_DATA://保存数据
    {
        saveConfig();
        saveData();
    }
        break;
    case BUTTON_HFCT_LOAD_DATA://载入数据
    {
        loadData();
    }
        break;
        //        case BUTTON_HFCT_DELETE_DATA://删除数据
        //        {
        //            deleteData();
        //        }
        //            break;
    case BUTTON_MORE_MENU://配置
    {
        showMoreConfigButtonBar();
    }
        break;
    default:
    {
        logError(QString("invalid btn id: %1").arg(id));
    }
        break;
    }

    return;
}

/************************************************
 * 函数名   : loadData
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 载入数据
 ************************************************/
void HFCTPRPSCabelTestBGView::loadData()
{
    QList<QString> lStrBGFileNames;
    m_pCommonItemListView->clear();

    if(lStrBGFileNames.size() <= 0)
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
        return;
    }

    QList<QString> lStrFileNames;
    QString strFileName = "";
    for(quint16 i = 0, iSize = lStrBGFileNames.size(); i < iSize; ++i)
    {
        strFileName = lStrBGFileNames[i];
        lStrFileNames.append(CustomAccessTaskNS::testFileNameFromFile(strFileName));
    }

    m_eOperationType = OPERATION_LOAD;
    m_pCommonItemListView->addItems(lStrFileNames);

    m_pCommonItemListView->show();

    return;
}

/*************************************************
功能： 删除数据
*************************************************************/
void HFCTPRPSCabelTestBGView::deleteData()
{
    m_pCommonItemListView->show();
    m_eOperationType = OPERATION_DELETE;
}

/************************************************
 * 函数名   : setConfigData
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :
 ************************************************/
void HFCTPRPSCabelTestBGView::setConfigData()
{
    //从配置文件读取参数
    int iGroup = HFCT::GROUP_HFCT_PRPS;
    m_pConfig = ConfigManager::instance()->config();

    m_pConfig->beginGroup(Module::GROUP_HFCT);

    m_eGain = (HFCT::Gain)m_pConfig->value(HFCT::KEY_GAIN).toUInt();
    m_iPhaseAlias = m_pConfig->value(HFCT::KEY_PHASEALIAS).toUInt();
    m_eSyncSource = (Module::SyncSource)m_pConfig->value(HFCT::KEY_SYNC_SOURCE).toUInt();
    m_iAccumulationTime = m_pConfig->value(HFCT::KEY_PRPS_ACCUMULATION_TIME, iGroup).toUInt();
    m_fThresholdPercentage = m_pConfig->value( HFCT::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup).toFloat();
    m_eThresholdMode = static_cast<HFCT::ThresholdMode>(m_pConfig->value( HFCT::KEY_PRPS_THRESHOLD_MODE, iGroup).toUInt());
    m_pConfig->endGroup();

    m_pConfig->beginGroup(Module::GROUP_APP);
    m_ucSysFreq = m_pConfig->value(APPConfig::KEY_SYS_FREQ).toUInt();
    m_pConfig->endGroup();

    return;
}

/************************************************
 * 函数名   : setButtonBarDatas
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 设置buttonbar显示的参数
 ************************************************/
void HFCTPRPSCabelTestBGView::setButtonBarDatas()
{
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_PHASE_SHIFT)))->setValue(m_iPhaseAlias);
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_GAIN)))->setValue(m_eGain);
    //((PopupButton*)(buttonBar()->button(BUTTON_HFCT_SYNC_SOURCE)))->setValue(m_eSyncSource - (int)Module::WIRELESS_SYNC);
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_ACCUMULATION)))->setValue(m_iAccumulationTime);
    ((PopupButton*)(buttonBar()->button(BUTTON_ALTAS_TYPE)))->setValue(m_eAltasType);
    ((PopupButton*)(buttonBar()->button(BUTTON_THRESHOLD)))->setValue(m_eThresholdMode);
    return;
}

/************************************************
 * 函数名   : createButtonbar
 * 输入参数 : parent---父控件
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 创建buttonbar
 ************************************************/
PushButtonBar* HFCTPRPSCabelTestBGView::createButtonbar(QWidget *parent)
{
    Q_UNUSED(parent);
    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar(HFCT::CONTEXT, s_HFCTButtonInfo, sizeof(s_HFCTButtonInfo) / sizeof(ButtonInfo::Info));
    return pButtonBar;
}

/***************************************************
 * 功能：保存数据
 * *************************************************/
void HFCTPRPSCabelTestBGView::saveData()
{
    if(isSampling())
    {
        //停止采集
        stopSample();
        setSampleBtnText(isSampling());
    }

    QString strFile = saveTestData();
    QFileInfo fileInfo(strFile);
    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
    centerPoint.setY(pBtnBar->pos().y()/*+pBtnBar->height()/2*/);
    if(strFile.isEmpty())
    {
        MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("Save failure!"), centerPoint);
    }
    else
    {
        int iMode = MobileAccessService::instance()->getSwitchMode();
        if(SystemSet::ACCESS_AUTO_SWITCH == static_cast<SystemSet::AccessSwitchMode>(iMode))
        {
            if(MsgBox::OK == MsgBox::delayQuestion("", QObject::trUtf8("Save success, auto switching."), centerPoint))
            {
                emit sigExitTest();
                close();
            }
        }
        else
        {
            //提示文件名
            QString strText = fileInfo.fileName();
            processTooLongMsgText(strText);
            MsgBox::informationWithoutAutoAccept("", strText, centerPoint);

            //重新开始采集
            startSample();
            setSampleBtnText(isSampling());
            m_bPlayBacked = false;
        }
    }

    return;
}

/*************************************************
功能： 响应S键事件
*************************************************************/
void HFCTPRPSCabelTestBGView::onSKeyPressed()
{
    saveConfig(); // 存储到配置文件中
    saveData();
    return;
}

