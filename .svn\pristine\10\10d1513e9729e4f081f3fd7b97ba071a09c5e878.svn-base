﻿/*
* Copyright (c) 2019.4，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：PDdiagnosis.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2019年4月2日
* 摘要：局放单条数据诊断算法信息定义
*/

#ifndef PDDIAGNOSIS_H
#define PDDIAGNOSIS_H

#include "DiagDefines.h"


#ifdef WIN32
#ifdef __cplusplus
#if defined(DIAGNOSIS_LIBRARY)
#  define DIAGNOSISSHARED_EXPORT extern "C" __declspec(dllexport)
#else
#  define DIAGNOSISSHARED_EXPORT extern "C" __declspec(dllimport)
#endif
#else
#if defined(DIAGNOSIS_LIBRARY)
#  define DIAGNOSISSHARED_EXPORT __declspec(dllexport)
#else
#  define DIAGNOSISSHARED_EXPORT __declspec(dllimport)
#endif
#endif
#else
#ifdef __cplusplus
# define DIAGNOSISSHARED_EXPORT extern "C"
#else
# define DIAGNOSISSHARED_EXPORT
#endif
#endif


/*****************************
 *  诊断方法
 *****************************/
typedef enum _DiagnosisMethod
{
    DIAGNOSIS_BY_AMPLITUDE = 0,         //幅值
    DIAGNOSIS_BY_BACKGROUND_DIFFER,     //背景差值
    DIAGNOSIS_BY_HISTORY_DIFFER,        //历史差值
    DIAGNOSIS_BY_TRANSVERSE_DIFFER,     //横向差值
    DIAGNOSIS_BY_CLOUD,                 //云诊断

    DIAGNOSISMETHOD_COUNT               //诊断方法个数

}DiagnosisMethod;


#endif // PDDIAGNOSIS_H

