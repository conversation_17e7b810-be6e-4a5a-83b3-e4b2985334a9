/*
* Copyright (c) 2016.09，南京华乘电气科技有限公司
* All rights reserved.
*
* qrcodeview.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年8月15日
* 摘要：二维码扫描界面和功能实现接口声明

* 当前版本：1.0
*/

#ifndef QRCODEVIEW_H
#define QRCODEVIEW_H

#include <QLabel>
#include "widgets/sampleChartView/SampleChartView.h"
#include "qrcode/qrcodeservice.h"

class QrCodeView  :public SampleChartView
{
    Q_OBJECT
public:
    /*************************************************
        函数名： QrCodeView(const QString &qsTitle, QWidget *parent = NULL)
        输入参数:qsTitle---标题
                parent---父控件
        输出参数：NULL
        返回值： NULL
        功能： 构造函数
        *************************************************************/
    explicit QrCodeView(const QString &qsTitle, QWidget *parent = NULL);


    /*************************************************
        函数名： ~QrCodeView()
        输入参数: NULL
        输出参数：NULL
        返回值： NULL
        功能： 析构函数
        *************************************************************/
    ~QrCodeView();

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

};

#endif // QRCODEVIEW_H
