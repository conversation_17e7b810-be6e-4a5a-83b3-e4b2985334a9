/*
* Copyright (c) 2017.9，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：MapDataFactory.h
*
* 初始版本：1.0
* 作者：
* 创建日期：2017年10月27日
* 摘要：根图谱类的标签名称创建对象实例的工厂类
*
* 参考：http://www.mimec.org/node/350
*/
#ifndef OBJECTFACTORY_H
#define OBJECTFACTORY_H

#include <QByteArray>
#include <QMetaObject>
#include <QHash>

class MapDataFactory
{
public:
    /*****************************************************************
     * 功能：注册指定类，便于以后生成对象实例
     ****************************************************************/
    template<typename T>
    static void registerClass( const QByteArray & classTagName )
    {
        constructors().insert( /*T::staticMetaObject.className()*/
                               classTagName, &constructorHelper<T> );
    }

    /*****************************************************************
     * 功能：根据类名生成对象实例
     * 入参：className--类名字符串
     *      parent--生成对象的父对象
     ****************************************************************/
    static QObject* createObject( const QByteArray& classTagName )
    {
        Constructor constructor = constructors().value( classTagName );
        if ( constructor == NULL )
            return NULL;
        return (*constructor)(  );
    }

private:
    //定义函数指针类型
    typedef QObject* (*Constructor)(  );

    /*****************************************************************
     * 功能：生成器辅助类
     * 入参：parent--生成对象的父对象
     * 返回值：生成的对象指针
     ****************************************************************/
    template<typename T>
    static QObject* constructorHelper(  )
    {
        return new T(  );
    }

    /*****************************************************************
     * 功能：获取构造器集合
     * 返回值：类名和构造器的集合
     ****************************************************************/
    static QHash<QByteArray, Constructor>& constructors()
    {
        static QHash<QByteArray, Constructor> instance;
        return instance;
    }
};

#endif // OBJECTFACTORY_H
