/*
* Copyright (c) 2016.03，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: AEWaveDataSave.h
*
* 初始版本：1.0
* 作者：曹山
* 创建日期：2016年03月16日
* 摘要：该文件主要是定义了AE波形检测数据存储的子类
*/

#ifndef AEWAVEDATASAVE_H
#define AEWAVEDATASAVE_H

#include "dataSave/DataSave.h"
#include "ae/AE.h"
#include "module_global.h"
#include "ae/aemapdefine.h"

typedef struct _AEWaveData
{
    float   fWavePeakValue;         //采样点数据，单位mV
    float   fWaveTimeValue;          //采样点时间，单位T
}AEWaveData;
//AE波形检测数据信息
typedef struct _AEWaveDataInfo
{
    /************************************************
     * 功能     : 设置量纲（并完成枚举和存储类型转换）
     ************************************************/
    void setUnit( AE::UnitOption eUnit )
    {
        if( eUnit == AE::UNIT_DB )
        {
            eDataUnit = DataFileNS::AMP_UNIT_DB;
        }
        else if( eUnit == AE::UNIT_MV )
        {
            eDataUnit = DataFileNS::AMP_UNIT_mV;
        }
    }

    /************************************************
     * 功能     : 设置增益（并完成枚举和存储类型转换）
     ************************************************/
    void setGain( AE::GainType eGain )
    {
        if( eGain == AE::GAIN_X1 )
        {
            eGainValue = GAIN_SIXTY;
        }
        else if( eGain == AE::GAIN_X10 )
        {
            eGainValue = GAIN_EIGHTY;
        }
        else if( eGain == AE::GAIN_X100 )
        {
            eGainValue = GAIN_HUNDRED;
        }
    }

    /************************************************
     * 功能     : 设置采样模式（并完成枚举和存储类型转换）
     ************************************************/
    /*
    void setSampleMode( AE::SampleMode eMode )
    {
        if( AE::SAMPLEMODE_CONTINUOUS == eMode)
        {
            eWorkingMode = MODE_CONTINUOUS;
        }
        else
        {
            eWorkingMode = MODE_TRIG;
        }
    }
    */

    AE::GainType gain()
    {
         AE::GainType eGain;
         if( AE::g_ausGainValues[AE::GAIN_X1]  == sGainValue )
         {
             eGain = AE::GAIN_X1;
         }
         else if( AE::g_ausGainValues[AE::GAIN_X10]  == sGainValue )
         {
             eGain = AE::GAIN_X10;
         }
         else
         {
             eGain = AE::GAIN_X100;
         }
//         if( GAIN_SIXTY  == eGainValue )
//         {
//             eGain = AE::GAIN_X1;
//         }
//         else if( GAIN_EIGHTY  == eGainValue )
//         {
//             eGain = AE::GAIN_X10;
//         }
//         else
//         {
//             eGain = AE::GAIN_X100;
//         }
         return eGain;
    }

    AE::UnitOption unit()
    {
        AE::UnitOption eUnit = AE::UNIT_DB;

        if( eDataUnit == DataFileNS::AMP_UNIT_mV )
        {
            eUnit = AE::UNIT_MV;
        }
        return eUnit;
    }

    DataMapHead stHeadInfo;            //图谱通用的头部信息

    UINT32 uiSamplingRate;             //采样率，default (250*50/2）

    DataFileNS::AmpUnit eDataUnit;                  //幅值单位
    float fAmpLowerLimit;                           //幅值下限
    float fAmpUpperLimit;                           //幅值上限
    AEMapNS::AETransformerType eTransformerType;    //超声传感器类型
    quint8 ucaDischargeTypeProb[8];                 //放电类型概率
//    quint16 usGatingTime;                           //开门时间,单位us
//    quint16 usShutTime;                             //关门时间,单位ms
//    quint16 usMaxTimeInterval;                      //最大间隔时间,单位ms
    qint16 sGainValue;                           //增益
    DataFileNS::GainType eGainType;             //增益种类
    GainValue eGainValue;                           //增益, 60dB；80dB；100dB
//    DataFileNS::GainFactor eGainFactor;             //放大器放大倍数
    float fTrigThrhd;                               //触发阈值，单位mV
    SyncSource eSyncSource;                         //同步源
    quint8 ucSyncState;                             //同步状态,失败：0x00 成功：0x01
    float fSyncFreq;

    quint8 ucSamplePeriod;            //采样周期
    qint32 iDataPointNum;                           //数据点个数
    AEWaveData stWaveValue[AEWAVENUM];               //250个采样数据

    _AEWaveDataInfo()
    {
        stHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_DEFAULT;
        uiSamplingRate = AE::SAMPLING_RATE;

        eDataUnit = DataFileNS::AMP_UNIT_DEFAULT;
        fAmpLowerLimit = 0;
        fAmpUpperLimit = 0;
        eTransformerType = AEMapNS::AE_TRANSFORMER_DEFAULT;
        memset( ucaDischargeTypeProb, 0, sizeof(ucaDischargeTypeProb) );
//        usGatingTime = 0;
//        usShutTime = 0;
//        usMaxTimeInterval = 0;
        eGainValue = GAIN_HUNDRED;
        eGainType = DataFileNS::GAIN_TYPE_DEFAULT;
//        eGainFactor = DataFileNS::GAIN_X100;
        fTrigThrhd = 0;
        eSyncSource = WIRELESS_SYNC;
        ucSyncState = 0;
        fSyncFreq = -1;
        ucSamplePeriod = 0;

        iDataPointNum = AEWAVENUM;
        memset(stWaveValue,0x0,sizeof(AEWaveData)*AEWAVENUM);
    }
}AEWaveDataInfo;

class DataFile;
class DataMap;
class AEWaveDataMap;
class MODULESHARED_EXPORT AEWaveDataSave : public DataSave
{
public:
    /************************************************
     * 函数名   : AEWaveDataSave
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 构造函数
     ************************************************/
    AEWaveDataSave();

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData);

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData, const QString &qsSavedPath);

    /************************************************
     * 函数名   : getDataByPDA
     * 输入参数 : strFileName: 文件名; pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 获取结果
     * 功能     : 获取指定数据文件中的数据
     ************************************************/
   INT32 getDataFromFile(const QString& strFileName, void *pData);

protected:
    /************************************************
     * 函数名   : getDataTypeFolder
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件夹名
     * 功能     : 获取图谱类型文件存储最上层文件夹名
     ************************************************/
    QString getDataTypeFolder(void);

    /************************************************
     * 函数名   : getFileNameSuffix
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件后缀名
     * 功能     : 获取数据文件后缀名
     ************************************************/
    QString getFileNameSuffix(void);

    /************************************************
     * 函数名   : getStringFromData
     * 输入参数 : pDatas: 数据; uiCounts: 数据个数
     * 输出参数 : NULL
     * 返回值   : 转换后的字符串
     * 功能     : 将数据转成base64的字符串
     ************************************************/
    QString getStringFromData( void *pDatas, UINT32 uiCounts);

    /************************************************
     * 函数名   : saveExtInformation
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储可扩展信息
     ************************************************/
    void saveExtInformation(XMLDocument& doc);

    /************************************************
     * 函数名   : saveRawData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储数据部分
     ************************************************/
    void saveRawData(XMLDocument& doc);


    /************************************************
     * 函数名   : parseData
     * 输入参数 : baData: 数据
     * 输出参数 : pData: 解析到的数据
     * 返回值   : void
     * 功能     : 解析数据
     ************************************************/
    void parseData(const QByteArray& baData, void *pData, const QString& strFileName = "");

private:
     void addAEWaveMap(DataFile *pFile);
     void setMapData(AEWaveDataMap *pMap);
     void setMapInfo(AEWaveDataMap *pMap);
     void setMapHead(DataMap *pMap);

private:
    AEWaveDataInfo *m_pAEWaveDataInfo;     //AE波形检测数据信息
};

#endif // AEWAVEDATASAVE_H
