/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* PDADownloadTaskView.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年5月18日
* 摘要：下载任务列表界面

* 当前版本：1.0
*/

#ifndef PDADOWNLOADTASKVIEW_H
#define PDADOWNLOADTASKVIEW_H

#include "PDAUi/PDAUiView/pdalistview.h"
#include "PDAUi/PDAUiBean/pdalistchart.h"
#include "pda/pda.h"
#include "globalerrprocess.h"

class PDADownloadTaskView : public PDAListView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        taskInfos -- 任务概要信息
        parent:父控件指针
    *************************************************************/
    explicit PDADownloadTaskView( const QVector<TaskInfo>& taskInfos, QWidget *parent = 0);

    /***********************************************
     * 功能：析构函数
     * *************************************************/
    ~PDADownloadTaskView();

signals:
    /*************************************************
    功能：下载结束后，关闭下载进度框
    *************************************************/
    void sigDownloadFinished( void );

protected:
    /**************************************
     * 功能：处理显示事件
     * 输入参数：
     *      pEvent：显示事件
     * ************************************/
    void showEvent(QShowEvent *pEvent);

protected slots:
    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应条目被点击后的事件
    输入参数：
            id -- 条目序号
    *************************************************************/
    void onItemClicked( int id );

    /*************************************************
    功能： 槽，响应多选模式下，条目被点击后的事件
    输入参数：
            id -- 条目序号
    *************************************************************/
    void onItemClickedUnderMutil( int id );

private slots:
    /*************************************************
    功能：  通知任务信息列表结果已更新
    *************************************************/
    void onCmsTaskInfoChanged();

    /*************************************************
    功能：  通知任务查询操作结束
    *************************************************/
    void onQueryListFinished(errorProcess::ReplyCode eCode);

    /*************************************************
    功能：  index -- cms的任务列表的序号
    通知序号为index任务已经下载完毕
    *************************************************/
    void onTaskDownloaded(UINT32 index);

    /*************************************************
    功能：eCode--下载结果
     通知所有需要下载的任务已经下载完毕
    *************************************************/
    void onTaskDowndFinished( errorProcess::ReplyCode eCodee );
    
    /*************************************************
    功能：filter--过滤条件
    将筛选界面返回的过滤条件传给pdaservice模块
    *************************************************/
    void onCloudTaskFiltered(TaskFilter &filter);

    /*************************************************
    功能：将筛选界面返回的过滤条件清除
    *************************************************/
    void onCloudTaskFilterCleared();

    /*************************************************
    功能：等待下载任务时，生成messagebox提示
    *************************************************/
    void onInitWaitingDialog();

private:
    /*************************************************
    功能： 初始化任务信息
    输入参数:
        taskInfos -- 任务概要信息
    *************************************************************/
    void initTaskInfos( const QVector<TaskInfo>& taskInfos );

    /*************************************************
    功能： 刷新
    *************************************************************/
    void refresh( void );

    /*************************************************
    功能： 下载任务(该接口抽象，仅为过于冗长的代码集中在一个函数内有碍观瞻)
    *************************************************************/
    void downLoadTask( void );

private:
    QList<PDAListChart::ListItemInfo> m_lItemInfos;

    bool m_bIsGetTaskInfoing;   // 是否正在获取任务信息的标志
};

#endif // PDADOWNLOADTASKVIEW_H
