﻿#include <QScrollBar>
#include <QVBoxLayout>
#include <QListWidgetItem>
#include "deviceselfinspectionview.h"
#include "appfontmanager/appfontmanager.h"
#include "Window.h"
#include "log/log.h"
#include "systemsetview/SystemViewConfig.h"
#include "selfinspectionmanager/selfinspectionmanager.h"
#include "appfontmanager/appfontmanager.h"
#include <QShortcut>

#define TITLEBAR_HEIGHT 100 //标题栏高度
#define ITEM_HEIGHT 50  //每个item的高度
#define BUTTON_WIDTH 250  //按钮的宽度
#define BUTTON_HEIGHT 60  //按钮的高度

const QString BUTTON_STYLE = "QPushButton{color: black;border-radius: 10px;  border: 1px;"
                             "border-style: outset;}"
                             "QPushButton:pressed{background-color:rgb(85, 170, 255);"
                             "border-style: inset; }";

const QString LIST_HIDDEN_STYLE = "QListWidget{background:rgb(211,215,224);outline:0px;border-radius: 15px 15px;border:none}";
const QString LIST_STYLE = "QListWidget{background:white;outline:0px;border-radius: 15px 15px;border:none}";
const QString LISTWIDGET_STYLE = "QListWidget::item{height: 50px;border-radius: 5px; border-width: 1px; border-color:gray;border-style: solid;}";
const QString SCOROLLBAR_STYLE = "QScrollBar:vertical{width:40px;background:rgba(0,0,0,0%);margin:0px,0px,0px,0px;padding-top:30px;padding-bottom:30px;}"
                                 "QScrollBar::handle:vertical{width:8px;background:rgba(0,0,0,25%);border-radius:4px;min-height:20;}"
                                 "QScrollBar::handle:vertical:hover{width:8px;background:rgba(0,0,0,50%);border-radius:4px;min-height:20;}"
                                 "QScrollBar::add-line:vertical{height:30px;width:30px;subcontrol-position:bottom;image: url(:/images/arrow_down_pda.png);}"
                                 "QScrollBar::sub-line:vertical{height:30px;width:30px;subcontrol-position:top;image: url(:/images/arrow_up_pda.png);}"
                                 "QScrollBar::add-line:vertical:hover{height:30px;width:30px;subcontrol-position:bottom;}"
                                 "QScrollBar::sub-line:vertical:hover{height:30px;width:30px;subcontrol-position:top;}"
                                 "QScrollBar::add-page:vertical,QScrollBar::sub-page:vertical{background:rgba(0,0,0,10%);border-radius:4px;}";


/****************************
功能： 构造函数
    parent -- 父窗体
*****************************/
DeviceSelfInspectionView::DeviceSelfInspectionView(QWidget *parent)
    : QWidget(parent)
    , m_bInspecting(false)
    , m_pTitleBar(NULL)
    , m_pListWidget(NULL)
    , m_pSelfInspectionButton(NULL)
    , m_pLoadingWidget(NULL)
{
    setAttribute(Qt::WA_DeleteOnClose);     //设置整体显示属性
    setWindowFlags(Qt::FramelessWindowHint);    //设置无标题栏外框
    setFixedSize(Window::WIDTH, Window::HEIGHT);
    createView();
    addListInfos();

    connect(SelfInspectionManager::instance(), SIGNAL(sigSingleSelfInspResult(SelfInspModule, SelfState)), this, SLOT(onSingleSelfInspectFinished(SelfInspModule, SelfState)));
    connect(SelfInspectionManager::instance(), SIGNAL(sigSelfInspResult(SelfState)), this, SLOT(onSelfInspectFinished(SelfState)));
}

DeviceSelfInspectionView::~DeviceSelfInspectionView()
{
    if (m_bInspecting)
    {
        // 停止自检
        SelfInspectionManager::instance()->stopSelfInspection();
    }
}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void DeviceSelfInspectionView::keyPressEvent(QKeyEvent* event)
{
    if(Qt::Key_Escape == event->key())
    {
        close();
    }
    else if (Qt::Key_Enter == event->key() || Qt::Key_Return == event->key())
    {
        m_pSelfInspectionButton->animateClick();
    }
    else
    {
        QWidget::keyPressEvent(event);
    }
}

/*************************************************
功能： 自检按钮点击槽函数
*************************************************************/
void DeviceSelfInspectionView::onSelfInspectionButtonClicked()
{
    if (!m_bInspecting)
    {
        // 开始自检
        clearItemStatus();
        updateItemStatus(0, StatusListWidgetItem::LOADING);
        SelfInspectionManager::instance()->startSelfInspection();
        m_bInspecting = true;
    }
    else
    {
        // 停止自检
        SelfInspectionManager::instance()->stopSelfInspection();

        if (!m_pLoadingWidget)
        {
            m_pLoadingWidget = new TextLoadingView(trUtf8("Stop self-checking ..."));
        }
        m_pLoadingWidget->show();
    }

    changeButtonText();

    return;
}

/****************************************************
 * 功能：单个模块自检接结束槽函数
 * 输入参数：
 *      eModule：模块信息
 *      eState：自检结果
 * ******************************************************/
void DeviceSelfInspectionView::onSingleSelfInspectFinished(SelfInspModule eModule, SelfState eState)
{
    updateItemStatus(eModule, SelfState2ItemStatus(eState));

    int iRow = eModule + 1;
    // 如果不是最后一个且没有停止自检，更新下一个item的状态
    if (m_bInspecting && SELF_INSP_COUNT != iRow)
    {
        updateItemStatus(iRow, StatusListWidgetItem::LOADING);
    }

    // 如果停止自检
    if(m_pLoadingWidget && !m_pLoadingWidget->isHidden())
    {
        m_pLoadingWidget->hide();
    }

    return;
}

/****************************************************
 * 功能：所有模块自检接结束槽函数
 * 输入参数：
 *      eState：自检结果
 * ******************************************************/
void DeviceSelfInspectionView::onSelfInspectFinished(SelfState eState)
{
    Q_UNUSED(eState);
    // 如果停止自检
    if(m_pLoadingWidget && !m_pLoadingWidget->isHidden())
    {
        m_pLoadingWidget->hide();
    }

    m_bInspecting = false;
    changeButtonText();

    return;
}

/*************************************************
功能： 创建界面
*************************************************************/
void DeviceSelfInspectionView::createView()
{
    m_pTitleBar = new TitleBar(SYSTEM_INFO_TRANSLATE(SystemSet::TEXT_DEVICE_SELF_INSPECTION), this);
    m_pTitleBar->setFixedSize(Window::WIDTH, TITLEBAR_HEIGHT);
    connect(m_pTitleBar, SIGNAL(sigClicked()), this, SLOT(close()));

    m_pListWidget = new QListWidget(this);
    m_pListWidget->setFocusPolicy(Qt::NoFocus);
    m_pListWidget->setSelectionMode(QAbstractItemView::NoSelection);
    m_pListWidget->setFrameShape(QListWidget::NoFrame);
    m_pListWidget->verticalScrollBar()->setStyleSheet(SCOROLLBAR_STYLE);
    m_pListWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pListWidget->setFixedSize(Window::WIDTH, Window::HEIGHT - TITLEBAR_HEIGHT - BUTTON_HEIGHT - 5);
    m_pListWidget->setStyleSheet(LISTWIDGET_STYLE);

    m_pSelfInspectionButton = new QPushButton(this);
    m_pSelfInspectionButton->setFixedSize(BUTTON_WIDTH, BUTTON_HEIGHT);
    m_pSelfInspectionButton->setStyleSheet(BUTTON_STYLE);
    QFont font = AppFontManager::instance()->getAppCurFont();
    font.setPointSize(24);
    m_pSelfInspectionButton->setFont(font);
    m_pSelfInspectionButton->setFocus();
    changeButtonText();

    connect(m_pSelfInspectionButton, SIGNAL(clicked()), this, SLOT(onSelfInspectionButtonClicked()));

    QVBoxLayout* pMainlayout= new QVBoxLayout(this);
    pMainlayout->setSpacing(0);
    pMainlayout->setMargin(0);
    pMainlayout->setContentsMargins(0, 0, 0, 1);

    if(m_pTitleBar)
    {
        pMainlayout->addWidget(m_pTitleBar, 0, Qt::AlignTop);
    }

    pMainlayout->addWidget(m_pListWidget, 0, Qt::AlignCenter);
    pMainlayout->addStretch();
    pMainlayout->addWidget(m_pSelfInspectionButton, 0, Qt::AlignHCenter);

    setLayout(pMainlayout);

    return;
}

/*************************************************
功能： 添加list列表信息
*************************************************************/
void DeviceSelfInspectionView::addListInfos()
{
    if (NULL == m_pListWidget)
    {
        return;
    }

    QVector<SelfInfo> qvtSelfInfos = SelfInspectionManager::instance()->getSelfInfo();
    QFont contentFont = AppFontManager::instance()->getAppCurFont();
    contentFont.setPointSize(20);
    for(int i = 0; i < qvtSelfInfos.size(); ++i)
    {
        SelfInspModule eInspModule = qvtSelfInfos[i].eInspModule;
        QString strSelfInspModule = SelfInspModule2QString(eInspModule);
        //logInfo(QString("DeviceSelfInspectionView::addListInfos module: %1, state: %2.").arg(strSelfInspModule).arg(qvtSelfInfos[i].eInspState));

        QListWidgetItem* pItem = new QListWidgetItem(m_pListWidget);
        pItem->setData(Qt::UserRole, eInspModule);

        StatusListWidgetItem* pStatusListWidgetItem = new StatusListWidgetItem(m_pListWidget);
        pStatusListWidgetItem->setText(strSelfInspModule);
        pStatusListWidgetItem->setFont(contentFont);
        pStatusListWidgetItem->setStatus(SelfState2ItemStatus(qvtSelfInfos[i].eInspState));
        m_pListWidget->setItemWidget(pItem, pStatusListWidgetItem);

        m_pListWidget->addItem(pItem);
    }
}

/*************************************************
功能： 切换按钮文字
*************************************************************/
void DeviceSelfInspectionView::changeButtonText()
{
    if (m_bInspecting)
    {
        m_pSelfInspectionButton->setText(trUtf8("Stop Self-Check"));
    }
    else
    {
        m_pSelfInspectionButton->setText(trUtf8("Start Self-Check"));
    }
}

/*************************************************
功能： 自检模块枚举转字符串
*************************************************************/
QString DeviceSelfInspectionView::SelfInspModule2QString(const SelfInspModule eModule)
{
    QString strSelfInspModule;
    switch (eModule)
    {
    case SELF_INVALID:
        strSelfInspModule = trUtf8("Unknown");
        break;
    case SELF_WIFI:
        strSelfInspModule = trUtf8("WiFi");
        break;
    case SELF_DISK:
        strSelfInspModule = trUtf8("Disk");
        break;
    case SELF_RTC:
        strSelfInspModule = trUtf8("RTC");
        break;
    case SELF_RFID:
        strSelfInspModule = trUtf8("RFID");
        break;
    case SELF_AE:
        strSelfInspModule = trUtf8("AE");
        break;
    case SELE_TEV:
        strSelfInspModule = trUtf8("TEV");
        break;
    default:
        break;
    }

    return strSelfInspModule;
}

/*************************************************
功能： 清空item的状态
*************************************************************/
void DeviceSelfInspectionView::clearItemStatus()
{
    int iRowCount = m_pListWidget->count();
    for (int i = 0; i < iRowCount; ++i)
    {
        updateItemStatus(i, StatusListWidgetItem::NORMAL);
    }
}

/*************************************************
功能： 更新item的状态
*************************************************************/
void DeviceSelfInspectionView::updateItemStatus(const int iRow, const StatusListWidgetItem::ItemStatus eItemStatus)
{
    QListWidgetItem* pItem = m_pListWidget->item(iRow);
    if (NULL == pItem)
    {
        return;
    }

    StatusListWidgetItem* pStatusListWidgetItem = qobject_cast<StatusListWidgetItem*>(m_pListWidget->itemWidget(pItem));
    if (NULL == pStatusListWidgetItem)
    {
        return;
    }
    pStatusListWidgetItem->setStatus(eItemStatus);
}

/*************************************************
功能： 自检模块枚举转ItemStatus
*************************************************************/
StatusListWidgetItem::ItemStatus DeviceSelfInspectionView::SelfState2ItemStatus(const SelfState eState)
{
    StatusListWidgetItem::ItemStatus eItemStatus = StatusListWidgetItem::NORMAL;
    switch (eState)
    {
    case SELF_IDLE:
        eItemStatus = StatusListWidgetItem::NORMAL;
        break;
    case SELF_ING:
        eItemStatus = StatusListWidgetItem::LOADING;
        break;
    case SELF_PASS:
        eItemStatus = StatusListWidgetItem::SUCCESS;
        break;
    case SELF_FAIL:
        eItemStatus = StatusListWidgetItem::FAILED;
        break;
    default:
        break;
    }

    return eItemStatus;
}
