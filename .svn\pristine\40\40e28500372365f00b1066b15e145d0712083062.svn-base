﻿#include "bluetoothsettingview.h"
#include <QKeyEvent>
#include <QDebug>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include "config/ConfigManager.h"
#include "Module.h"
#include "appconfig.h"
#include "messageBox/msgbox.h"
#include "appfontmanager/appfontmanager.h"
#include "log/log.h"

#define TOOL_ICON_LEN 80

/*************************************************
功能： 构造函数
*************************************************************/
BlueToothSettingView::BlueToothSettingView(bool bOpen)
    : LinkSettingView(trUtf8("Bluetooth"), trUtf8("Asset"))
{
    setAttribute(Qt::WA_DeleteOnClose);

    //底部增加列表刷新按钮
    m_pFreshBtn = new QToolButton();
    m_pFreshBtn->setIconSize(QSize(TOOL_ICON_LEN, TOOL_ICON_LEN));
    m_pFreshBtn->setIcon(QIcon(":/images/FunctionImage/synchronizer.png"));
    m_pFreshBtn->setText(QObject::trUtf8("Refresh"));
    m_pFreshBtn->setEnabled(false);
    connect(m_pFreshBtn, SIGNAL(clicked()), this, SLOT(onRefreshBtDeviceList()));

    QHBoxLayout* btnLayout = new QHBoxLayout();
    btnLayout->addStretch();
    btnLayout->addWidget(m_pFreshBtn);
    btnLayout->addStretch();
    QVBoxLayout* mainLayout = dynamic_cast<QVBoxLayout*>(layout());
    if(mainLayout)
    {
        mainLayout->addLayout(btnLayout);
    }

    m_pScanLoading = new ScanLoading();
    m_pScanLoading->hide();

    if(bOpen)
    {
        m_bBluetoothOn = bOpen;
    }
    else
    {
        m_bBluetoothOn = SystemSetService::instance()->bluetoothSetService()->isBluetoothOpened();
    }

    if(m_bBluetoothOn)           // 若bluetooth被标记为打开的状态
    {
        setSwitchState(m_bBluetoothOn);
        updateBySwitchState(m_bBluetoothOn);
        addNewAddedItem(SystemSetService::instance()->bluetoothSetService()->scanBluetoothInfos());
        pauseSearch();
    }

    //qRegisterMetaType<QVector<WifiSetService::WifiInfo> >("QVector<WifiSetService::WifiInfo>");
    connect(SystemSetService::instance()->bluetoothSetService(), SIGNAL(sigScanBluetoothInfos(QVector<BluetoothSetService::BluetoothInfo>)),
            this, SLOT(onGetBluetoothInfoList(QVector<BluetoothSetService::BluetoothInfo>)), Qt::QueuedConnection);
    connect(SystemSetService::instance()->bluetoothSetService(), SIGNAL(sigBluetoothConnected(QString, bool)),
            this, SLOT(onConnectResult(QString, bool)), Qt::QueuedConnection);
    connect(SystemSetService::instance()->bluetoothSetService(), SIGNAL(sigControlBluetoothResult(bool)),
            this, SLOT(onControlBluetoothResult(bool)), Qt::QueuedConnection);
    connect(SystemSetService::instance()->bluetoothSetService(), SIGNAL(sigBeginToConnect(QString)),
            this, SLOT(onBeginToConnected(QString)));

    onRefreshBtDeviceList();
    switchStatusChanged(m_bBluetoothOn);
}

BlueToothSettingView::~BlueToothSettingView()
{
    disconnect(SystemSetService::instance()->bluetoothSetService(), SIGNAL(sigScanBluetoothInfos(QVector<BluetoothSetService::BluetoothInfo>)),
               this, SLOT(onGetBluetoothInfoList(QVector<BluetoothSetService::BluetoothInfo>)));
    disconnect(SystemSetService::instance()->bluetoothSetService(), SIGNAL(sigBluetoothConnected(QString, bool)),
               this, SLOT(onConnectResult(QString, bool)));
    disconnect(SystemSetService::instance()->bluetoothSetService(), SIGNAL(sigControlBluetoothResult(bool)),
               this, SLOT(onControlBluetoothResult(bool)));
    disconnect(SystemSetService::instance()->bluetoothSetService(), SIGNAL(sigBeginToConnect(QString)),
               this, SLOT(onBeginToConnected(QString)));
    if(m_pScanLoading)
    {
        delete m_pScanLoading;
        m_pScanLoading = NULL;
    }
}

void BlueToothSettingView::onUpdateBluetoothConnectionResult(bool bState)
{
    if(!bState)
    {
        for(int i = 0 , iSize = m_vBluetoothInfo.size(); i < iSize; ++i)
        {
            unSelectedIndex(i);       // 设置选中蓝牙对应条目序号更新其它条目状态
        }
    }
    logInfo(QString("connect state: %1.").arg(bState));
    return;
}

/*************************************************
功能： 处理按键事件
*************************************************************/
void BlueToothSettingView::keyPressEvent(QKeyEvent *e)
{
    switch(e->key())
    {
    case Qt::Key_Escape:
        if(m_pScanLoading->isHidden() && !isConnecting() && !m_bViewOperating)
        {
            close();
        }
        break;
    case Qt::Key_Enter:
    case Qt::Key_Return:
    {
        int iIndex = selectedIndex();
        if(iIndex >= 0 && iIndex < m_vBluetoothInfo.size())
        {
            connectDevice(m_vBluetoothInfo.at(iIndex).m_strName, m_vBluetoothInfo.at(iIndex).m_strMac);
        }
        break;
    }
    default:
    {
        LinkSettingView::keyPressEvent(e);
        break;
    }
    }

    return;
}

/************************************************
 * 输入参数 : bool：发出当前切换开关状态的信号
 * true -- 打开
 * false -- 关闭
 * 功能     : 接收切换开关状态的信号
 ************************************************/
void BlueToothSettingView::switchStatusChanged(bool bState)
{
    m_bBluetoothOn = bState;
    if(!m_bBluetoothOn)
    {
        SystemSetService::instance()->bluetoothSetService()->closeBluetooth();
    }
    else
    {
        SystemSetService::instance()->bluetoothSetService()->openBluetooth();
    }

    updateBySwitchState(m_bBluetoothOn);   // 根据开关状态更新界面
    return;
}

/************************************************
 * 输入参数 : name -- 条目名称
 * 功能     : 条目被点击时调用
 ************************************************/
void BlueToothSettingView::itemPressed(const QString& name)
{
    int index = -1;
    for(int i = 0, iSize = m_vBluetoothInfo.size(); i < iSize; ++i)
    {
        if(name == m_vBluetoothInfo.at(i).m_strName)
        {
            index = i;
            break;
        }
    }

    if(index >= 0 && index < m_vBluetoothInfo.size())
    {
        setCurItemSelected(index);
        connectDevice(m_vBluetoothInfo.at(index).m_strName, m_vBluetoothInfo.at(index).m_strMac);
        setLoadingViewVisible(true);

        //蓝牙最长名字限制长度为32个
        QString qstrName = name.left(32);       //长度不满32也没有问题
        QString qstrBTName = "\r\n" + qstrName;

        setLoadingName(trUtf8("Connect to device: ") + qstrBTName);
    }
    return;
}

/************************************************
 * 参数：wifiName -- 热点名
 *      bResult -- true 加入成功
 *                 false 加入失败
 * 功能    :处理加入结果
 ************************************************/
void BlueToothSettingView::onConnectResult(QString strMac, bool bResult)
{
    setLoadingViewVisible(false);

    if(bResult)
    {
        for(int i = 0, iSize = m_vBluetoothInfo.size(); i < iSize; ++i)
        {
            if(m_vBluetoothInfo.at(i).m_strMac == strMac)
            {
                setSelectedIndex(i);       // 设置选中蓝牙对应条目序号更新其它条目状态
                break;
            }
        }
        MsgBox::information("", trUtf8("Bluetooth connect success!"));
    }
    else
    {
        for(int i = 0, iSize = m_vBluetoothInfo.size(); i < iSize; ++i)
        {
            unSelectedIndex(i);
        }

        MsgBox::information("", trUtf8("Bluetooth connect fail!"));
    }

    return;
}

/*************************************************
功能： 添加wifi信息列表
*************************************************************/
void BlueToothSettingView::onGetBluetoothInfoList(const QVector<BluetoothSetService::BluetoothInfo> &infoList)
{
    logInfo(QString("scan bluetooth size: %1.").arg(infoList.size()).toLatin1().data());
    m_bViewOperating = true;
    m_pScanLoading->hide();
    pauseSearch();
    if(m_bBluetoothOn)
    {
        deleteRedundancyItem(infoList);               // 删除冗余条目
        addNewAddedItem(infoList);                    // 增加新增条目
    }
    m_bViewOperating = false;
    return;
}

/*************************************************
功能： 控制蓝牙的结果
参数： bStart -- true代表开启 false代表关闭
*************************************************************/
void BlueToothSettingView::onControlBluetoothResult(bool bStart)
{
    logInfo(QString("m_bBluetoothOn %1 bStart: %2.").arg(m_bBluetoothOn).arg(bStart));
    if(m_bBluetoothOn != bStart)
    {
        m_bBluetoothOn = bStart;
        setSwitchState(m_bBluetoothOn);

        if(!m_bBluetoothOn)
        {
            MsgBox::warning("", trUtf8("Open bluetooth fail!"));
        }
        else
        {
            MsgBox::warning("", trUtf8("Close bluetooth fail!"));
        }
        updateBySwitchState(m_bBluetoothOn);   // 根据开关状态更新界面
    }
    else
    {
        if(m_bBluetoothOn)
        {
            m_pFreshBtn->setEnabled(true);
            onRefreshBtDeviceList();
        }
    }

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(bStart, APPConfig::KEY_BLUETOOTH_SWITCH);
    pConfig->endGroup();
    return;
}

/****************************
 * 入参：bIsBlueToothOn -- true -- 开启
 *                        false -- 关闭
功能： 根据蓝牙的开关状态更新界面
*****************************/
void BlueToothSettingView::updateBySwitchState(bool bIsBlueToothOn)
{
    if(!bIsBlueToothOn)
    {
        //加锁处理
        {
            QMutexLocker stBTInfoLocker(&m_mt4BTInfo);
            m_vBluetoothInfo.clear();
        }

        clear();    // 清除界面元素
        m_pFreshBtn->setEnabled(false);
        stopSearch();
    }
    else
    {
        m_pFreshBtn->setEnabled(true);
        startSearch();
        pauseSearch();
    }
    return;
}

/****************************
入参： name -- 账号
      strMac -- mac地址
功能： 连接指定设备
*****************************/
void BlueToothSettingView::connectDevice(const QString& name, const QString& strMac)
{
    SystemSetService::instance()->bluetoothSetService()->connectDevice(name, strMac);
    return;
}

/****************************
入参： infoList -- 当前搜索到列表信息的集合
功能： 删除不存在的热点
*****************************/
void BlueToothSettingView::deleteRedundancyItem(const QVector<BluetoothSetService::BluetoothInfo> &infoList)
{
    if(m_bBluetoothOn)
    {
        QVector<int> deleteItemIndex;
        deleteItemIndex.clear();

        //加锁处理
        QMutexLocker stBTInfoLocker(&m_mt4BTInfo);
        for(int j = 0, iSize = m_vBluetoothInfo.size(); j < iSize; ++j)
        {
            if(indexof(m_vBluetoothInfo.at(j), infoList) == -1)
            {
                deleteItemIndex << j;
            }
        }

        deleteItems(deleteItemIndex);

        QVector<BluetoothSetService::BluetoothInfo> tmpInfoList;
        tmpInfoList.clear();

        for(int i = 0, iSize = m_vBluetoothInfo.size(); i < iSize; ++i)
        {
            if(deleteItemIndex.indexOf(i) == -1)
            {
                tmpInfoList << m_vBluetoothInfo.at(i);
            }
        }

        m_vBluetoothInfo = tmpInfoList;
    }
    return;
}

/****************************
入参： infoList -- 当前搜索到列表信息的集合
功能： 添加新增条目
*****************************/
void BlueToothSettingView::addNewAddedItem(const QVector<BluetoothSetService::BluetoothInfo> &infoList)
{
    if(!m_bBluetoothOn)
    {
        logWarning("bluetooth has been closed.");
        return;
    }

    QVector<BluetoothSetService::BluetoothInfo> newItems;
    newItems.clear();

    //加锁处理
    {
        QMutexLocker stBTInfoLocker(&m_mt4BTInfo);
        for(int i = 0, iSize = infoList.size(); i < iSize; ++i)
        {
            if(indexof(infoList.at(i), m_vBluetoothInfo) == -1) // 避免出现重名wifi热点
            {
                if(indexof(infoList[i], newItems) == -1)   // 底层接口无法避免同名wifi
                {
                    newItems << infoList[i];
                }
            }
        }
        m_vBluetoothInfo += newItems;
    }

    QVector<ItemInfo> infos;
    infos.clear();

    for(int i = 0, iSize = newItems.size(); i < iSize; ++i)
    {
        infos << LinkSettingView::ItemInfo(newItems.at(i).m_strName);
    }

    //connect bluetooth info
    int connectIndex = -1;

    //加锁处理
    {
        QMutexLocker stBTInfoLocker(&m_mt4BTInfo);
        for(int i = 0, iSize = m_vBluetoothInfo.size(); i < iSize; ++i)
        {
            if(m_vBluetoothInfo[i].m_bConnected)
            {
                connectIndex = i;
                break;
            }
        }
    }

    addItems(infos);
    //耗时之后再次判断
    if(!m_bBluetoothOn)
    {
        logWarning("bluetooth has been closed.");
        return;
    }

    if(-1 != connectIndex)
    {
        setSelectedIndex(connectIndex);
    }

    return;
}

/****************************
 * 入参：sInfo -- 待查找信息
 *      vInfos -- 供查找容器
 出参：int -- 对应下标
      -1 -- 未找到相应下标
功能： 查找符合对应信息的下标
*****************************/
int BlueToothSettingView::indexof(BluetoothSetService::BluetoothInfo sInfo, const QVector<BluetoothSetService::BluetoothInfo>& vInfos)
{
    int index = -1;
    for(int i = 0, iSize = vInfos.size(); i < iSize; ++i)
    {
        if(vInfos.at(i).m_strName == sInfo.m_strName)
        {
            index = i;
            break;
        }
    }
    return index;
}

//处理后台开始自动连接信号
void BlueToothSettingView::onBeginToConnected(QString strName)
{
    Q_UNUSED(strName);
    return;
}

//处理列表刷新按钮点击事件
void BlueToothSettingView::onRefreshBtDeviceList()
{
    if(m_bBluetoothOn)
    {
        continueSearch();   // 播放搜索动画
        SystemSetService::instance()->bluetoothSetService()->scanDeviceList(true);
        setLoadingViewVisible(false);
        m_pScanLoading->show();
    }

    return;
}
