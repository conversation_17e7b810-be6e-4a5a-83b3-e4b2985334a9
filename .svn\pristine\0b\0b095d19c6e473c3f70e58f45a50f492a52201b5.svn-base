///*
//* Copyright (c) 2016.03，南京华乘电气科技有限公司
//* All rights reserved.
//*
//* ControlPushButtonBar.h
//*
//* 初始版本：1.0
//* 作者：赵勇军
//* 创建日期：2016年3月7日
//* 修改日期：2016年11月15日
//*       重构
//* 摘要：控制按钮栏定义

//* 当前版本：1.0
//*/
//#ifndef PushButtonBar_H
//#define PushButtonBar_H

//#include <QFrame>
//#include <QVBoxLayout>
//#include "ButtonBar.h"
//#include "widgetglobal.h"

//class WIDGET_EXPORT PushButtonBar : public ButtonBar
//{
//    Q_OBJECT

//public:
//    /*************************************************
//    功能： 构造函数
//    输入参数:
//        iColumnCount -- 列数
//        parent -- 父控件
//    *************************************************************/
//    PushButtonBar( int iColumnCount = 4, QWidget *parent = 0 );

//    /*************************************************
//    功能： 添加按钮
//    输入参数:
//        pButton -- 按钮指针
//        id -- 按钮ID（注：如果为-1，则按钮用自身设置的ID）
//    *************************************************************/
//    virtual void addButton( ControlButton* pButton );

//     /************************************************
//      * 功能     : 设置列数
//      * 输入参数 :
//      *      iColumnCount -- 列数
//      ************************************************/
//     void setColumnCount( int iColumnCount );
//protected:
//    /************************************************
//     * 函数名   : 键盘事件
//     * 输入参数 :
//     *      event -- 事件
//     ************************************************/
//     void keyPressEvent( QKeyEvent* event );
//private:
//    int m_iColumnCount;//列数
//};
//#endif // CONTROLPushButtonBar

