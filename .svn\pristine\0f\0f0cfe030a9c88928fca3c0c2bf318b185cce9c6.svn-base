/*
 * Copyright (c) 2019.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：spectrum.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/11/04
 * 摘要：图谱基类
 * 当前版本：1.0
 */

#pragma once

#include <QObject>
#include <QString>
#include <QDomElement>
#include "dataspecification_global.h"
#include "spectrumdefine.h"
#include "common/jsonprocesshelper.h"
#include "dataspecification_def.h"

namespace DataSpecificationNS
{
    class SpectrumPrivate;
    class XMLDocument;
    class DATASPECIFICATIONSHARED_EXPORT Spectrum : public QObject
    {
    public:
        Spectrum();
        virtual ~Spectrum();

        /************************************************
         * 函数名   : setDataSpecificationVersion
         * 输入参数 :
           const DataSpecificationVersion eDataSpecificationVersion: 数据规范版本号
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置数据规范版本号
         ************************************************/
        virtual void setDataSpecificationVersion(const DataSpecificationVersion eDataSpecificationVersion);

        /************************************************
         * 函数名   : setSpectrumHead
         * 输入参数 :
           const SpectrumHead& stSpectrumHead: 图谱头
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置图谱头
         ************************************************/
        void setSpectrumHead(const SpectrumHead& stSpectrumHead);

        /************************************************
         * 函数名   : setSpectrumTypeCode
         * 输入参数 :
           const SpectrumCode eSpectrumCode: 图谱类型编码
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置图谱类型编码
         ************************************************/
        void setSpectrumTypeCode(const SpectrumTypeCode eSpectrumCode);

        /************************************************
         * 函数名   : setSpectrumTypeCode
         * 输入参数 :
           const QString& qstrSpectrumGenerationTime: 图谱生成时间
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置图谱生成时间
         ************************************************/
        void setSpectrumGenerationTime(const QString& qstrSpectrumGenerationTime);

        /************************************************
         * 函数名   : setSpectrumCharacter
         * 输入参数 :
           const MapProperty eSpectrumCharacter: 图谱性质
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置图谱性质
         ************************************************/
        void setSpectrumCharacter(const SpectrumCharacter eSpectrumCharacter);

        /************************************************
         * 函数名   : setEquipmentName
         * 输入参数 :
           const QString& qstrEquipmentName: 设备名称
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置设备名称
         ************************************************/
        void setEquipmentName(const QString& qstrEquipmentName);

        /************************************************
         * 函数名   : setEquipmentCode
         * 输入参数 :
           const QString& qstrEquipmentCode: 设备编码
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置设备编码
         ************************************************/
        void setEquipmentCode(const QString& qstrEquipmentCode);

        /************************************************
         * 函数名   : setTestPointName
         * 输入参数 :
           const QString& qstrTestPointName: 测点名称
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置测点名称
         ************************************************/
        void setTestPointName(const QString& qstrTestPointName);

        /************************************************
         * 函数名   : setTestPointCode
         * 输入参数 :
           const QString& qstrTestPointCode: 测点编码
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置测点编码
         ************************************************/
        void setTestPointCode(const QString& qstrTestPointCode);

        /************************************************
         * 函数名   : setDetectionChannelIdentification
         * 输入参数 :
           const qint16 sDetectionChannelIdentification: 检测通道标识
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置检测通道标识
         ************************************************/
        void setDetectionChannelIdentification(const qint16 sDetectionChannelIdentification);

        /************************************************
         * 函数名   : setStorageDataType
         * 输入参数 :
           const StorageDataType eStorageDataType: 存储数据类型
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置存储数据类型
         ************************************************/
        void setStorageDataType(const StorageDataType eStorageDataType);

        /************************************************
         * 函数名   : getSpectrumHead
         * 输入参数 : NULL
         * 输出参数 :
           SpectrumHead& stSpectrumHead: 图谱头结构体
         * 返回值   : void
         * 功能     : 获取图谱头
         ************************************************/
        void getSpectrumHead(SpectrumHead& stSpectrumHead);

        /************************************************
         * 函数名   : saveAsBinary
         * 输入参数 :
           QByteArray& qbaPackage: 数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存成二进制数据
         ************************************************/
        bool saveAsBinary(QByteArray& qbaPackage);

        /************************************************
         * 函数名   : saveAsXML
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存成XML数据
         ************************************************/
        bool saveAsXML(XMLDocument& xmlDocumentObj);

        /************************************************
         * 函数名   : saveAsJSON
         * 输入参数 :
           rapidjson::Document::AllocatorType& alloc:
           rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存成JSON数据
         ************************************************/
        bool saveAsJSON(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : parseBinaryData
         * 输入参数 :
           const QByteArray& qbaData: 二进制数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析二进制数据
         ************************************************/
        bool parseBinaryData(const QByteArray& qbaData);

        /************************************************
         * 函数名   : parseXMLData
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析XML数据
         ************************************************/
        bool parseXMLData(XMLDocument& xmlDocumentObj);

        /************************************************
         * 函数名   : parseJSONData
         * 输入参数 :
           const rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析JSON数据
         ************************************************/
        bool parseJSONData(const rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : spectrumName
         * 输入参数 : NULL
         * 输出参数 : NULL
         * 返回值   : QString
         * 功能     : 图谱名称
         ************************************************/
        virtual QString spectrumName() const = 0;

        /************************************************
         * 函数名   : storageDataType
         * 输入参数 : NULL
         * 输出参数 : NULL
         * 返回值   : StorageDataType
         * 功能     : 图谱的数据类型
         ************************************************/
        StorageDataType storageDataType();

        /************************************************
         * 函数名   : spectrumTypeCode
         * 输入参数 : NULL
         * 输出参数 : NULL
         * 返回值   : SpectrumTypeCode
         * 功能     : 图谱类型编码
         ************************************************/
        SpectrumTypeCode spectrumTypeCode();

    protected:
        /************************************************
         * 函数名   : saveBinarySpectrumHead
         * 输入参数 :
           QDataStream& out: 输出流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存二进制头部
         ************************************************/
        bool saveBinarySpectrumHead(QDataStream& out);

        /************************************************
         * 函数名   : saveXMLSpectrumHead
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
           QDomElement& element: dom元素
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存XML头部
         ************************************************/
        bool saveXMLSpectrumHead(XMLDocument& xmlDocumentObj, QDomElement& element);

        /************************************************
         * 函数名   : saveJSONSpectrumHead
         * 输入参数 :
           rapidjson::Document::AllocatorType& alloc:
           rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存JSON头部
         ************************************************/
        bool saveJSONSpectrumHead(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : saveBinarySpectrumExtInfo
         * 输入参数 :
           QDataStream& out: 输出流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存二进制可扩展信息段
         ************************************************/
        virtual bool saveBinarySpectrumExtInfo(QDataStream& out) = 0;

        /************************************************
         * 函数名   : saveXMLSpectrumExtInfo
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
           QDomElement& element: dom元素
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存XML可扩展信息段
         ************************************************/
        virtual bool saveXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj, QDomElement& element) = 0;

        /************************************************
         * 函数名   : saveJSONSpectrumExtInfo
         * 输入参数 :
           rapidjson::Document::AllocatorType& alloc:
           rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存JSON可扩展信息段
         ************************************************/
        virtual bool saveJSONSpectrumExtInfo(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue) = 0;

        /************************************************
         * 函数名   : saveBinarySpectrumData
         * 输入参数 :
           QDataStream& out: 输出流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存二进制图谱数据段
         ************************************************/
        virtual bool saveBinarySpectrumData(QDataStream& out) = 0;

        /************************************************
         * 函数名   : saveXMLSpectrumData
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
           QDomElement& element: dom元素
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存XML图谱数据段
         ************************************************/
        virtual bool saveXMLSpectrumData(XMLDocument& xmlDocumentObj, QDomElement& element) = 0;

        /************************************************
         * 函数名   : saveJSONSpectrumData
         * 输入参数 :
           rapidjson::Document::AllocatorType& alloc:
           rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存JSON图谱数据段
         ************************************************/
        virtual bool saveJSONSpectrumData(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue) = 0;

        /************************************************
         * 函数名   : parseBinarySpectrumHead
         * 输入参数 :
           QDataStream& in: 输入流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析二进制头部
         ************************************************/
        bool parseBinarySpectrumHead(QDataStream& in);

        /************************************************
         * 函数名   : parseXMLSpectrumHead
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析XML头部
         ************************************************/
        bool parseXMLSpectrumHead(XMLDocument& xmlDocumentObj);

        /************************************************
         * 函数名   : parseJSONSpectrumHead
         * 输入参数 :
           const rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析JSON头部
         ************************************************/
        bool parseJSONSpectrumHead(const rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : parseBinarySpectrumExtInfo
         * 输入参数 :
           QDataStream& in: 输入流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析二进制可扩展信息段
         ************************************************/
        virtual bool parseBinarySpectrumExtInfo(QDataStream& in) = 0;

        /************************************************
         * 函数名   : parseXMLSpectrumExtInfo
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析XML可扩展信息段
         ************************************************/
        virtual bool parseXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj) = 0;

        /************************************************
         * 函数名   : parseJSONSpectrumExtInfo
         * 输入参数 :
           const rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析JSON可扩展信息段
         ************************************************/
        virtual bool parseJSONSpectrumExtInfo(const rapidjson::Value& jsonValue) = 0;

        /************************************************
         * 函数名   : parseBinarySpectrumData
         * 输入参数 :
           QDataStream& in: 输入流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析二进制图谱数据段
         ************************************************/
        virtual bool parseBinarySpectrumData(QDataStream& in) = 0;

        /************************************************
         * 函数名   : parseXMLSpectrumData
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析XML图谱数据段
         ************************************************/
        virtual bool parseXMLSpectrumData(XMLDocument& xmlDocumentObj) = 0;

        /************************************************
         * 函数名   : parseJSONSpectrumData
         * 输入参数 :
           const rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析JSON图谱数据段
         ************************************************/
        virtual bool parseJSONSpectrumData(const rapidjson::Value& jsonValue) = 0;

        /************************************************
         * 函数名   : updateBinaryFileLength
         * 输入参数 :
           QDataStream& out: 输出流
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 更新二进制数据长度
         ************************************************/
        void updateBinaryDataLength(QDataStream& out);

        /************************************************
         * 函数名   : perDataSize
         * 输入参数 : NULL
         * 输出参数 : NULL
         * 返回值   : int
         * 功能     : 获取当前存储数据类型每个占用的字节数
         ************************************************/
        int perDataSize();
    public:
        SpectrumPrivate* m_pSpectrumPrivate{ Q_NULLPTR };
    };
}
