#include "UHFServicePrivate.h"
#include "UHFAmpStrategy.h"
#include "UHFIntervalStrategy.h"
#include "uhfprpsstrategy.h"
#include "../peripheral/peripheralservice.h"
#include "uhf/uhfampservice.h"
#include "uhf/UHFPeriodService.h"
#include "uhf/UHFPRPSService.h"
#include "global_log.h"


/*************************************************
功能： 构造函数
输入参数:
    parent -- 父Object
*************************************************/
UHFServicePrivate::UHFServicePrivate( QObject* parent )
    :QObject( parent )
{
    qRegisterMetaType<UHF::AmplitudeData>("UHF::AmplitudeData");
    qRegisterMetaType<UHF::IntervalData>("UHF::IntervalData");
    qRegisterMetaType<UHF::PRPSData>("UHF::PRPSData");

    qRegisterMetaType<Module::SyncState>("Module::SyncState");
    qRegisterMetaType<Module::SignalState>("Module::SignalState");
    qRegisterMetaType<MultiServiceNS::USERID>("MultiServiceNS::USERID");
    qRegisterMetaType<Module::SampleState>("Module::SampleState");

    m_eFordwardGain = UHF::FORWARD_GAIN_DEFAULT;
    m_eBandWidth = UHF::BAND_WIDTH_DEFAULT;
    m_eSyncSource = Module::SYNC_SOURCE_DEFAULT;
    m_pUHFSampleStrategy = NULL;
    p = ( UHFService* )parent;
    m_eSignalState = Module::SIGNAL_STATE_INVALID;
    m_eSyncState = Module::Invalid_Sync;
    m_qi32SyncFailedCnt = 0;
    m_qi32SignalFailedCnt = 0;

    m_pAffair = new HCAffair( UHF_AFFAIR_COUNT, this );
    connect( m_pAffair, SIGNAL(sigAffair(quint16,void*,quint16)), this, SLOT(onAffair(quint16,void*,quint16)), Qt::QueuedConnection );

    m_pThread = new QThread( this );
    parent->moveToThread( m_pThread );
}

/*************************************************
功能： 析构函数
*************************************************/
UHFServicePrivate::~UHFServicePrivate()
{

}

Module::SyncState UHFServicePrivate::syncState()
{
    return m_pUHFSampleStrategy->m_eSyncState;
}

/*************************************************
入参：eMode -- 工作模式
功能： 设置数据和采样服务策略
*************************************************/
void UHFServicePrivate::setServiceStrategy( UHF::WorkMode eMode )
{
    if( m_pUHFSampleStrategy == NULL )
    {
        switch( eMode )
        {
        case UHF::MODE_AMPLITUDE:
        {
            UHFAmpStrategy *pStrategy = new UHFAmpStrategy;
            connect( pStrategy,SIGNAL(sigData(UHF::AmplitudeData)),
                     p,SLOT(onData(UHF::AmplitudeData)) );
            connect( pStrategy,SIGNAL(sigSignalChanged(Module::SignalState)),
                     this,SLOT(onSignalChanged(Module::SignalState)));
            m_pUHFSampleStrategy = pStrategy;
        }
            break;
        case UHF::MODE_SPECTRUM:
        {
            UHFIntervalStrategy *pStrategy = new UHFIntervalStrategy;
            connect( pStrategy,SIGNAL(sigData(UHF::IntervalData)),
                     p,SLOT(onData(UHF::IntervalData)) );
            connect( pStrategy,SIGNAL(sigSignalChanged(Module::SignalState)),
                     this,SLOT(onSignalChanged(Module::SignalState)));
            m_pUHFSampleStrategy = pStrategy;
        }
            break;
        case UHF::MODE_PRPS:
        {
            UHFPRPSStrategy *pStrategy = new UHFPRPSStrategy;
            connect( pStrategy,SIGNAL(sigData(UHF::PRPSData)),
                     p,SLOT(onData(UHF::PRPSData)) );
            connect( pStrategy,SIGNAL(sigReadUHFPRPSPRPDDataFailed()),
                     p,SIGNAL(sigReadUHFPRPSPRPDDataFailed()) );
            connect( pStrategy,SIGNAL(sigSignalChanged(Module::SignalState)),
                     this,SLOT(onSignalChanged(Module::SignalState)));
            m_pUHFSampleStrategy = pStrategy;
        }
            break;
        default:
            break;
        }

        if(m_pUHFSampleStrategy)
        {
            connect( m_pUHFSampleStrategy,SIGNAL(sigSyncStateChanged(Module::SyncState)),
                     p, SIGNAL(sigSyncStateChanged(Module::SyncState)));

            connect(m_pUHFSampleStrategy, SIGNAL(sigSyncSourceChanged(Module::SyncSource)),
                    this, SLOT(onSyncSourceChanged(Module::SyncSource)));

            connect(m_pUHFSampleStrategy, SIGNAL(sigStateChanged(Module::SampleState)),
                    this, SLOT(onStateChanged(Module::SampleState)));

            m_pUHFSampleStrategy->setSyncSource(m_eSyncSource);
        }
    }
    else
    {
        //workMode already exist
    }
}

/*************************************************
功能： 信号  数据信号状态发生变化
*************************************************/
void UHFServicePrivate::onSignalChanged( Module::SignalState eSignalState )
{
    if( eSignalState == Module::SIGNAL_STATE_EXIST ) // 无信号变为有信号时，重新设置工作参数
    {
        setGain( m_eFordwardGain );
        setBandWidth( m_eBandWidth );
        setSyncSource( m_eSyncSource );
    }
    emit sigSignalChanged( eSignalState );
    m_eSignalState = eSignalState;
    return;
}

/*************************************************
功能： 槽，处理异步事务
输入参数:
    usAffair -- 事务
    pInfo -- 存放结果数据指针
    usID -- 当前事务的序号
*************************************************/
void UHFServicePrivate::onAffair( quint16 usAffair, void* pInfo, quint16 usID  )
{
    int iResult = HC_SUCCESS;
    switch( usAffair )
    {
    case START_SAMPLE://启动采集
    {
        startSample();
    }
        break;
    case STOP_SAMPLE://停止采样
    {
        stopSample();
    }
        break;
    case SET_FORWARDGAIN://设置工作参数
    {
        iResult = setGain( m_eFordwardGain );
    }
        break;
    case SET_BANDWIDTH:
    {
        iResult = setBandWidth( m_eBandWidth );
    }
        break;
    case SET_SYNCSOURCE:
    {
        iResult = setSyncSource( m_eSyncSource );
    }
        break;
    case SET_STRATEGY:
    {
        setServiceStrategy( m_eWorkMode );
    }
        break;
    default:
        break;
    }
    if( NULL != pInfo )
    {
        *((int*)pInfo) = iResult;
    }
    m_pAffair->commit( usAffair, usID );
}

/*************************************************
功能： 设置增益
入参：eGain -- 增益
返回值：结果
*************************************************/
int UHFServicePrivate::setGain( UHF::ForwardGain eGain )
{
    int iResult = HC_SUCCESS;
#ifdef Q_PROCESSOR_ARM
    m_mutexWorkset.lock();
    iResult = PeripheralService::instance()->setUhfGain(GET_API_FORWARDGAIN(eGain));
    m_mutexWorkset.unlock();
#endif
    return iResult;
}

/*************************************************
功能： 设置同步源
入参：eSource -- 同步源
返回值：结果
*************************************************/
int UHFServicePrivate::setSyncSource( Module::SyncSource eSource )
{
    if(m_pUHFSampleStrategy)
    {
        m_pUHFSampleStrategy->setSyncSource(eSource);
    }

    int iResult = HC_SUCCESS;
#ifdef Q_PROCESSOR_ARM
    m_mutexWorkset.lock();
    iResult = PeripheralService::instance()->setUhfSyncsource(GET_API_SYNCSOURCE(eSource));
    m_mutexWorkset.unlock();
#endif

    return iResult;
}

/*************************************************
功能： 设置带宽
入参：eWidth -- 带宽
返回值：结果
*************************************************/
int UHFServicePrivate::setBandWidth(UHF::BandWidth eWidth )
{
    int iResult = HC_SUCCESS;
#ifdef Q_PROCESSOR_ARM
    m_mutexWorkset.lock();
    iResult = PeripheralService::instance()->setUhfFilter(GET_API_BANDWIDTH(eWidth));
    m_mutexWorkset.unlock();
#endif
    return iResult;
}

/*************************************************
功能： 开始采集
*************************************************/
void UHFServicePrivate::startSample()
{
    if( m_pUHFSampleStrategy != NULL )
    {
        m_pUHFSampleStrategy->startSample(m_eWorkMode);
    }
}

/*************************************************
功能： 停止采集
*************************************************/
void UHFServicePrivate::stopSample()
{
    if( m_pUHFSampleStrategy != NULL )
    {
        m_pUHFSampleStrategy->stopSample();
    }
}

/*************************************************
功能： 信号  同步源发生变化
*************************************************/
void UHFServicePrivate::onSyncSourceChanged(Module::SyncSource eSyncSource)
{
    if(eSyncSource != m_eSyncSource)
    {
        logError(QString("Service sync source (%1) != strategy sync source (%2).").arg(m_eSyncSource).arg(eSyncSource));
        return;
    }

    setSyncSource(m_eSyncSource);
    return;
}

/*************************************************
功能： 信号  状态发生变化
*************************************************/
void UHFServicePrivate::onStateChanged(Module::SampleState stSampleState)
{
    if(m_eSignalState != stSampleState.eSignalState)
    {
        // UHFPRPSStrategy中已经做了稳定性控制，这里不需要再控制
        m_qi32SignalFailedCnt = 0;
        m_eSignalState = stSampleState.eSignalState;
        emit sigSignalChanged(m_eSignalState);
    }

    if(m_eSyncState != stSampleState.eSyncState)
    {
        ++m_qi32SyncFailedCnt;
        if(Module::Invalid_Sync == m_eSyncState)
        {
            m_qi32SyncFailedCnt = 0;
            m_eSyncState = stSampleState.eSyncState;
            emit p->sigSyncStateChanged(m_eSyncState);
        }
        else
        {
            if(m_qi32SyncFailedCnt >= Module::readFailedCount(stSampleState.iSampleInterval))
            {
                m_qi32SyncFailedCnt = 0;
                m_eSyncState = stSampleState.eSyncState;
                emit p->sigSyncStateChanged(m_eSyncState);
            }
        }
    }
    else
    {
        m_qi32SyncFailedCnt = 0;
    }

    if(Module::SIGNAL_STATE_EXIST == stSampleState.eSignalState)
    {
        //信号存在时，参数不一致，需要进行重置参数
        if(m_eFordwardGain != static_cast<UHF::ForwardGain>(stSampleState.iGain))
        {
            setGain(m_eFordwardGain);
        }

        if(m_eBandWidth != static_cast<UHF::BandWidth>(stSampleState.iBandWidth))
        {
            setBandWidth(m_eBandWidth);
        }

        if(m_eSyncSource != stSampleState.eSyncSource)
        {
            setSyncSource(m_eSyncSource);
        }
    }

    return;
}
