#include <QBoxLayout>
#include <QPainter>
#include <QDebug>
#include "PushSliderPopup.h"

#define PUSHSLIDERPOPUP_OPACITY 0.8
/*****************************************************************
 * 功能     ：构造函数，初始化滑块类
 * 输入参数 ：
 *      parent -- 父控件
 ****************************************************************/
PushSliderPopup::PushSliderPopup( QWidget *parent )
    :SliderPopup( parent )
{
    setAttribute(Qt::WA_TranslucentBackground, true);
    setWindowOpacity( PUSHSLIDERPOPUP_OPACITY );
}

/*************************************************
功能： 绘制事件
输入参数:
    event -- 事件
*************************************************************/
void PushSliderPopup::paintEvent(QPaintEvent *event)
{
    Q_UNUSED( event );
    QPainter painter( this );

    painter.setRenderHint(QPainter::Antialiasing);
    painter.setPen(Qt::NoPen);
    painter.setBrush(QBrush(QColor(0, 0, 0, 200)));
    painter.drawRoundedRect(rect(), 10, 10);
}
