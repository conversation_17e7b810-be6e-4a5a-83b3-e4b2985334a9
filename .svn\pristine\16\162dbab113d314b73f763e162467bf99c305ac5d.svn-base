#include "linetemperaturecurve.h"
#include <QMouseEvent>
#include <thirdparty/qwt/qwt_plot_canvas.h>
#include <thirdparty/qwt/qwt_curve_fitter.h>
#include <thirdparty/qwt/qwt_scale_widget.h>
#include <thirdparty/qwt/qwt_plot_layout.h>
#include "config/ConfigManager.h"
#include "systemsetting/SystemSet.h"
#include "appconfig.h"

/*************************************************
函数名： LineTemperatureCurve(const QVector<float> &datas, QWidget *parent)
输入参数： datas：温度数据
          parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
LineTemperatureCurve::LineTemperatureCurve(const QVector<float> &datas, QWidget *parent)
    :QwtPlot(parent)
    ,m_pCurve(NULL)
    ,m_pGrid(NULL)
{
    setAttribute(Qt::WA_DeleteOnClose, true);
    setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);

    QwtPlotCanvas *pCanvas = new QwtPlotCanvas();
    pCanvas->setFrameStyle(QFrame::StyledPanel | QFrame::Plain);
    pCanvas->setLineWidth(0);
    pCanvas->setPalette(QColor(255, 255, 219));
    setCanvas(pCanvas);

    for (int i = 0; i < QwtPlot::axisCnt; i++)
    {
        QwtScaleWidget *scaleWidget = axisWidget(i);  //轴窗体
        if (scaleWidget)
        {
            scaleWidget->setMargin(0);
        }

        QwtScaleDraw *scaleDraw = axisScaleDraw(i);  //轴绘制
        if (scaleDraw)
        {
            scaleDraw->enableComponent(QwtAbstractScaleDraw::Backbone, false);
        }
    }
    plotLayout()->setAlignCanvasToScales(true);

    m_pGrid = new QwtPlotGrid();
    m_pGrid->setPen(QPen(QColor(220, 221, 197)));
    m_pGrid->attach(this);

    m_pCurve = new QwtPlotCurve("Data Moving Right");
    m_pCurve->setPen(Qt::blue);
    updateDatas(datas);
    m_pCurve->setCurveFitter(new QwtSplineCurveFitter());  //插值
    m_pCurve->attach(this);

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    SystemSet::TemperatureUnitOption eUnit = (SystemSet::TemperatureUnitOption) pConfig->value( APPConfig::KEY_TEMPERATURE_UNIT ).toInt();
    pConfig->endGroup();

    setAxisTitle(QwtPlot::xBottom, QObject::trUtf8("Temp. Points Count"));
    if(eUnit == SystemSet::TEMPERATURE_UNIT_FAHRENHEIT)
    {
        setAxisTitle(QwtPlot::yLeft, QObject::trUtf8("Unit(℉)"));
    }
    else
    {
        setAxisTitle(QwtPlot::yLeft, QObject::trUtf8("Unit(℃)"));
    }

    axisScaleDraw(QwtPlot::yLeft)->setTickLength(QwtScaleDiv::MinorTick, 0);
    axisScaleDraw(QwtPlot::xBottom)->setTickLength(QwtScaleDiv::MinorTick, 0);

    setAxisAutoScale(QwtPlot::xBottom);
    setAxisAutoScale(QwtPlot::yLeft);
}

/*************************************************
函数名： setDistinguishRect(const QRect &rect)
输入参数： rect：有效区域
输出参数： NULL
返回值： NULL
功能： 设置有效区域
*************************************************************/
void LineTemperatureCurve::setDistinguishRect(const QRect &rect)
{
    if (!rect.isEmpty())
    {
        m_rDistinguishRect = rect;
    }
}

/*************************************************
函数名： showEvent(QShowEvent *e)
输入参数： e：显示事件
输出参数： NULL
返回值： NULL
功能： 显示事件处理
*************************************************************/
void LineTemperatureCurve::showEvent(QShowEvent *e)
{
    Q_UNUSED(e)

    m_rDistinguishRect = rect();
}

/*************************************************
函数名： mousePressEvent(QMouseEvent *e)
输入参数： e：鼠标事件
输出参数： NULL
返回值： NULL
功能： 鼠标事件处理
*************************************************************/
//void LineTemperatureCurve::mousePressEvent(QMouseEvent *e)
//{
//    if (!m_rDistinguishRect.contains(e->pos()))
//    {
//        close();
//    }
//}

/*************************************************
函数名： updateDatas(const QVector<float> &datas)
输入参数： datas：温度数据
输出参数： NULL
返回值： NULL
功能： 刷新曲线数据
*************************************************************/
void LineTemperatureCurve::updateDatas(const QVector<float> &datas)
{
    QVector<QPointF> pointDatas;
    for (int i = 0; i < datas.size(); ++i)
    {
        pointDatas << QPointF(i, datas.at(i));
    }

    m_pCurve->setSamples(pointDatas);
    replot();
}
