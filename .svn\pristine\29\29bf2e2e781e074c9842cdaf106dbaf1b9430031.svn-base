#include "uhfprpsplaybackview.h"
#include "datadefine.h"
#include "uhf/dataSave/UHFPRPSAndPRPDDataSave.h"
#include "uhf/UHFViewConfig.h"
#include "window/Window.h"
#include "playbackView/PlayBackBase.h"
#include "diagnosismgr/diagnosismanager.h"
//data save
#include "prps/prpddatamap.h"
#include "datafile/prps/prpsmapdefine.h"
#include "datafile/mapdatafactory.h"
#include "dataspecification/dataspecification.h"
#include "dataspecification/prps/prpsspectrum.h"
#include "dataspecification/prps/prpdspectrum.h"
#include "customaccesstask/taskmanager.h"
#include "appconfig.h"
#include "log/log.h"

const int gs_iPrpdMargin = 60;

enum UHFPRPSPlayBackButton
{
    BUTTON_DELETE_DATA, // 删除数据
};

// 控制按钮定义
const ButtonInfo::Info s_UHFButtonInfo[] =
{
    { BUTTON_DELETE_DATA, { ButtonInfo::COMMAND, UHF::TEXT_DELETE_DATA, NULL, ":/images/sampleControl/delete.png", NULL } },//删除数据
};

/****************************
功能： 构造函数
输入参数:
    parent -- 父窗体
*****************************/
UHFPRPSPlayBackView::UHFPRPSPlayBackView(QWidget *parent) :
    PlayBackBase(parent)
{
    QPalette p;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    SystemSet::PRPSBGColor eColor = (SystemSet::PRPSBGColor) pConfig->value( APPConfig::KEY_PRPS_BG_COLOR ).toInt();
    pConfig->endGroup();
    if(eColor == SystemSet::PRPS_BG_COLOR_GRAY)
    {
        p.setColor( QPalette::Background,QColor(128, 128, 128) );
    }
    else
    {
        p.setColor( QPalette::Background,Qt::white );
    }
    setPalette( p );
    setAutoFillBackground( true );

    m_pChart = new UhfPrpsUnionView(UHF::PRPS_PERIOD_CNT,UHF::PRPS_PERIOD_CNT, UHF::PRPS_PHASE_CNT, UHF::CHART_MAX_VALUE,UHF::CHART_MIN_VALUE);
    m_pChart->setFixedSize(Window::WIDTH, CHART_HEIGHT);
    m_pChart->setPrpdContentsMargins(0, gs_iPrpdMargin, 0, gs_iPrpdMargin);
    m_pChart->setAltasType(PhaseAbstractView::PRPS_PRPD);
    m_pChart->setPRPDThreshold(10.0);
    setCenterWidget( m_pChart );
}

/****************************
功能： 回放文件
输入参数:
    strFileName -- 文件名
*****************************/
void UHFPRPSPlayBackView::playbackFile( const QString& strFileName )
{
    qDebug() << "UHFPRPSPlayBackView::playbackFile: " << strFileName;
    if (strFileName.contains(".dat"))
    {
        if (!buttonBar())
        {
            createButtonBar(UHF::CONTEXT, s_UHFButtonInfo, sizeof(s_UHFButtonInfo)/sizeof(ButtonInfo::Info));
        }
        playbackBinaryFile(strFileName);
    }
    else
    {
        playbackXmlFile(strFileName);
    }
    m_qstrPlayBackFile = strFileName;
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void UHFPRPSPlayBackView::onCommandButtonPressed(int id)
{
    switch( id )
    {
    case BUTTON_DELETE_DATA: // 删除数据
    {
        if (MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to delete the file?")))
        {
            emit sigDeleteCurrentFile();
        }
//        TaskManager::instance()->deleteSpecifiedTestFile(m_qstrPlayBackFile);
//        m_qstrPlayBackFile.clear();
    }
        break;
    default:
        break;
    }
}

/****************************
功能： 回放xml文件
输入参数:
    qstrFilePath -- 文件名
*****************************/
void UHFPRPSPlayBackView::playbackXmlFile(const QString& qstrFilePath)
{
    UHFPRPSAndPRPDDataSave  fDataSave;
    UHFPRPSPRPDDataInfo sPlayBackDataInfo;
    int value = fDataSave.getData(qstrFilePath, &sPlayBackDataInfo);

    if( HC_FAILURE == value )
    {
        logError(QString("review file (%1) get data failed.").arg(qstrFilePath));
        return;
    }

    displayMap(sPlayBackDataInfo);
}

/****************************
功能： 回放二进制文件
输入参数:
    qstrFilePath -- 文件名
*****************************/
void UHFPRPSPlayBackView::playbackBinaryFile(const QString& qstrFilePath)
{
    if (qstrFilePath == m_qstrPlayBackFile)
    {
        return;
    }

    DataSpecificationNS::DataSpecification dataSpecification;
    dataSpecification.setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());
    if(!dataSpecification.parseBinaryFromFile(qstrFilePath))
    {
        dbg_warning("parse binary file failed!\n");
        return;
    }

    UHFPRPSPRPDDataInfo stUHFPRPSPRPDDataInfo;
    getDataFromDataSpecification(&dataSpecification, stUHFPRPSPRPDDataInfo);

    displayMapFromBinaryFile(stUHFPRPSPRPDDataInfo);
}

/****************************
功能： 在回放中显示
输入参数: void
*****************************/
void UHFPRPSPlayBackView::displayMap(UHFPRPSPRPDDataInfo &stPlayBackDataInfo)
{
    m_pChart->clearData();

    //    if(PlayBackDataInfo.ucFreq == PrpsGlobal::FREQ_50)
    //    {
    //        m_pChart->setDisplayedPeriodCnt(PrpsGlobal::FREQ_50);
    //    }
    //    else
    //    {
    //        m_pChart->setDisplayedPeriodCnt(PrpsGlobal::FREQ_60);
    //    }

    /*设置频率*/
    m_pChart->setPowerFreq( PrpsGlobal::FREQ_50);

    m_pChart->setSync((PrpsGlobal::SyncSource) (stPlayBackDataInfo.stPRPSInfo.eSyncSource -1), (PrpsGlobal::SyncState) stPlayBackDataInfo.stPRPSInfo.ucSyncState );

    //是否无信号状态没有保存到数据文件，回放时，先简单默认不显示无信号
    m_pChart->setConnected(true);

    setStationName(stPlayBackDataInfo.stPRPSHeadInfo.strSubstationName);
    setDeviceName(stPlayBackDataInfo.stPRPSHeadInfo.strDeviceName);


    QVector<qint16> prpdData;
    prpdData.clear();
    double dPRRepeatyData;

    //NOTE 如果是累计模式，iPeriodCnt的计算不正确
    //int iPeriodCnt = PlayBackDataInfo.vecPRRepeatyData.size() / PlayBackDataInfo.stPRPDInfo.iPhaseIntervalCount;

    int iPeriodCnt = stPlayBackDataInfo.stPRPDInfo.iPowerFreCycleCount;
    double dTimePerPeriod = (1000.0 / (double) stPlayBackDataInfo.ucFreq) / 1000.0;//每周期时间 单位s

    logInfo(QString("review data period number: %1.").arg(iPeriodCnt));

    for(int i = 0; i < stPlayBackDataInfo.stPRPDInfo.iQuantificationAmp; ++i)
    {
        for(int j = 0; j < stPlayBackDataInfo.stPRPDInfo.iPhaseIntervalCount; ++j)
        {
            dPRRepeatyData = stPlayBackDataInfo.vecPRRepeatyData.at(j * stPlayBackDataInfo.stPRPDInfo.iQuantificationAmp + i);
            dPRRepeatyData = dPRRepeatyData * iPeriodCnt * dTimePerPeriod;
            prpdData.append((qint16)(dPRRepeatyData + 0.5f));
        }
    }

    m_pChart->setPeriodNum(iPeriodCnt);
    m_pChart->addPlayBackData( stPlayBackDataInfo.vecPRPSData, prpdData,stPlayBackDataInfo.stPRPDInfo.iPowerFreCycleCount );
    m_pChart->setMaxSpectrum(stPlayBackDataInfo.stPRPSInfo.fMax);

    QString qstrDesInfo = DiagnosisManager::instance()->getDiagInfoByPlayback(stPlayBackDataInfo.stPRPSHeadInfo.qstrPDSignalTypeInfos, DIAG_PRPS);

    DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
    stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stPlayBackDataInfo.stPRPSHeadInfo.ePDDefectLevel);
    stDiagDisplayInfo.qstrPDDesInfo = qstrDesInfo;
    stDiagDisplayInfo.qstrPDSignalInfos = stPlayBackDataInfo.stPRPSHeadInfo.qstrPDSignalTypeInfos;
    m_pChart->playbackDiagInfo(stDiagDisplayInfo);

    m_pChart->setPRPSThreshold(stPlayBackDataInfo.stPRPSInfo.fAnalysisThreshold);

    return;
}

/****************************
功能： 在回放中显示
输入参数: void
*****************************/
void UHFPRPSPlayBackView::displayMapFromBinaryFile(UHFPRPSPRPDDataInfo &PlayBackDataInfo)
{
    m_pChart->clearData();

    //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
    //m_pChart->setPowerFreq( (PrpsGlobal::Frequency)( PlayBackDataInfo.ucFreq ));
    //log_debug("sync source: %d, state: %d.", PlayBackDataInfo.stPRPSInfo.eSyncSource, PlayBackDataInfo.stPRPSInfo.ucSyncState);
    //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
    //m_pChart->setSync((PrpsGlobal::SyncSource) (PlayBackDataInfo.stPRPSInfo.eSyncSource - 1), (PrpsGlobal::SyncState) PlayBackDataInfo.stPRPSInfo.ucSyncState );
    m_pChart->clearSyncText();

    //是否无信号状态没有保存到数据文件，回放时，先简单默认不显示无信号
    m_pChart->setConnected(true);

    QVector<qint16> prpdData;
    prpdData.clear();
    double dPRPulseCnt;

    //NOTE 如果是累计模式，iPeriodCnt的计算不正确
    int iPeriodCnt = PlayBackDataInfo.vecPRRepeatyData.size() / PlayBackDataInfo.stPRPDInfo.iPhaseIntervalCount;

    logInfo(QString("review data period number: %1.").arg(iPeriodCnt));

    for(int i = 0; i < PlayBackDataInfo.stPRPDInfo.iQuantificationAmp; ++i)
    {
        for(int j = 0; j < PlayBackDataInfo.stPRPDInfo.iPhaseIntervalCount; ++j)
        {
            dPRPulseCnt = PlayBackDataInfo.vecPRRepeatyData.at(j * PlayBackDataInfo.stPRPDInfo.iQuantificationAmp + i);
            prpdData.append((qint16)(dPRPulseCnt));
        }
    }

    //log_debug("max value: %f.", PlayBackDataInfo.stPRPSInfo.fMax);
    //PlayBackDataInfo.stPRPSInfo.fMax = PlayBackDataInfo.stPRPSInfo.fMax < 0 ? 0 : PlayBackDataInfo.stPRPSInfo.fMax;
    //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
    //m_pChart->setMaxSpectrum(PlayBackDataInfo.stPRPSInfo.fMax);
    m_pChart->setPeriodNum(iPeriodCnt);
    m_pChart->addPlayBackData(PlayBackDataInfo.vecPRPSData, prpdData, PlayBackDataInfo.stPRPDInfo.iPowerFreCycleCount);
    m_pChart->setPhaseOffset(0);

    //本地诊断功能
    if(m_pChart->isLocalDiagnosisEnable())
    {
        PRPSDiagInfo stDiagInfo;
        stDiagInfo.qvtDataIn.clear();
        stDiagInfo.dThresholdDbVal = 0;
        stDiagInfo.dSpecMaxVal = m_pChart->rangeMax();
        stDiagInfo.dSpecMinVal = m_pChart->rangeMin();

        stDiagInfo.qvtDataIn = m_pChart->prpsDiagData(stDiagInfo.iPeriodNum, stDiagInfo.iPhaseNum);

        DiagResultInfo stRetInfo;
        DiagnosisManager::instance()->diagPRPSDataDirectly(stDiagInfo, stRetInfo);

        DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
        stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stRetInfo.stDiagRet.defectLevel);
        stDiagDisplayInfo.qstrPDDesInfo = stRetInfo.qstrPDDescription;
        stDiagDisplayInfo.qstrPDSignalInfos = stRetInfo.qstrPDSignalTypeInfos;

        m_pChart->playbackDiagInfo(stDiagDisplayInfo);
    }
}

void UHFPRPSPlayBackView::getDataFromDataSpecification(DataSpecificationNS::DataSpecification* pDataSpecification, UHFPRPSPRPDDataInfo& stUHFPRPSPRPDDataInfo)
{
    getFileHead(pDataSpecification, stUHFPRPSPRPDDataInfo);

    DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum = dynamic_cast<DataSpecificationNS::PRPSSpectrum*>(pDataSpecification->spectrum(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPS));
    if(NULL == pPRPSSpectrum)
    {
        dbg_warning("get prps map data failed.");
        return;
    }

    DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum = dynamic_cast<DataSpecificationNS::PRPDSpectrum*>(pDataSpecification->spectrum(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPD));
    if(NULL == pPRPDSpectrum)
    {
        dbg_warning("get prpd map data failed.");
        return;
    }

    getPRPSMapHead(pPRPSSpectrum, stUHFPRPSPRPDDataInfo);
    getPRPDMapHead(pPRPDSpectrum, stUHFPRPSPRPDDataInfo);

    DataSpecificationNS::PRPSExtInformation stPRPSExtInformation;
    pPRPSSpectrum->getPRPSExtInformation(stPRPSExtInformation);
    getPRPSMapInfo(&stPRPSExtInformation, stUHFPRPSPRPDDataInfo);

    DataSpecificationNS::PRPDExtInformation stPRPDExtInformation;
    pPRPDSpectrum->getPRPDExtInformation(stPRPDExtInformation);
    getPRPDMapInfo(&stPRPDExtInformation, stUHFPRPSPRPDDataInfo);

    DataSpecificationNS::PRPSData stPRPSData;
    pPRPSSpectrum->getPRPSData(stPRPSData);
    int iDataPointNum = stPRPSExtInformation.iPhaseWindowCount * stPRPSExtInformation.iPowerFreqCycleCount;
    stUHFPRPSPRPDDataInfo.vecPRPSData.resize(iDataPointNum);
    memcpy(&stUHFPRPSPRPDDataInfo.vecPRPSData[0], stPRPSData.qbaPDSpectrumData.data(), iDataPointNum * sizeof(double));

    DataSpecificationNS::PRPDData stPRPDData;
    pPRPDSpectrum->getPRPDData(stPRPDData);
    iDataPointNum = stPRPDExtInformation.iPhaseWindowCount * stPRPDExtInformation.iQuantizedAmplitude;
    stUHFPRPSPRPDDataInfo.vecPRRepeatyData.resize(iDataPointNum);
    memcpy(&stUHFPRPSPRPDDataInfo.vecPRRepeatyData[0], stPRPDData.qbaPDSpectrumData.data(), iDataPointNum * sizeof(double));
}

void UHFPRPSPlayBackView::getFileHead(DataSpecificationNS::DataSpecification *pDataSpecification, UHFPRPSPRPDDataInfo &stUHFPRPSPRPDDataInfo)
{
    DataSpecificationNS::SpectrumDataFileHead stSpectrumDataFileHead;
    pDataSpecification->getSpectrumDataFileHead(stSpectrumDataFileHead);
    stUHFPRPSPRPDDataInfo.ucFreq = stSpectrumDataFileHead.fSystemFrequency;
    stUHFPRPSPRPDDataInfo.stPRPSHeadInfo.strSubstationName = stSpectrumDataFileHead.qstrStationName;
    stUHFPRPSPRPDDataInfo.stPRPDHeadInfo.strSubstationName = stSpectrumDataFileHead.qstrStationName;
}

void UHFPRPSPlayBackView::getPRPSMapHead(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum, UHFPRPSPRPDDataInfo &stUHFPRPSPRPDDataInfo)
{
    DataSpecificationNS::SpectrumHead stSpectrumHead;
    pPRPSSpectrum->getSpectrumHead(stSpectrumHead);
    stUHFPRPSPRPDDataInfo.stPRPSHeadInfo.strDeviceName = stSpectrumHead.qstrEquipmentName;
    stUHFPRPSPRPDDataInfo.stPRPSHeadInfo.eDataPrimitiveType = static_cast<DataFileNS::DataPrimitiveType>(stSpectrumHead.eStorageDataType);
}

void UHFPRPSPlayBackView::getPRPDMapHead(DataSpecificationNS::PRPDSpectrum *pPRPDSpectrum, UHFPRPSPRPDDataInfo &stUHFPRPSPRPDDataInfo)
{
    DataSpecificationNS::SpectrumHead stSpectrumHead;
    pPRPDSpectrum->getSpectrumHead(stSpectrumHead);
    stUHFPRPSPRPDDataInfo.stPRPDHeadInfo.strDeviceName = stSpectrumHead.qstrEquipmentName;
    stUHFPRPSPRPDDataInfo.stPRPDHeadInfo.eDataPrimitiveType = static_cast<DataFileNS::DataPrimitiveType>(stSpectrumHead.eStorageDataType);
}

/****************************
功能： 获取图谱ext数据
输入参数:
    pMap -- 文件解析得到的图谱
    iDataPointCount -- 图谱数据数量
*****************************/
void UHFPRPSPlayBackView::getPRPSMapInfo(const DataSpecificationNS::PRPSExtInformation* pPRPSExtInformation, UHFPRPSPRPDDataInfo &stUHFPRPSPRPDDataInfo)
{
    stUHFPRPSPRPDDataInfo.stPRPSInfo.eSyncSource = static_cast<DataFileNS::SyncSource>(pPRPSExtInformation->eSyncSource);
    stUHFPRPSPRPDDataInfo.stPRPSInfo.ucSyncState = pPRPSExtInformation->ucSyncState;
    stUHFPRPSPRPDDataInfo.stPRPSInfo.iPhaseIntervalCount = pPRPSExtInformation->iPhaseWindowCount;
    stUHFPRPSPRPDDataInfo.stPRPSInfo.iQuantificationAmp = pPRPSExtInformation->iQuantizedAmplitude;
    stUHFPRPSPRPDDataInfo.stPRPSInfo.iPowerFreCycleCount = pPRPSExtInformation->iPowerFreqCycleCount;
    stUHFPRPSPRPDDataInfo.stPRPSInfo.eAmpUnit = static_cast<DataFileNS::AmpUnit>(pPRPSExtInformation->eAmpUnit);
    stUHFPRPSPRPDDataInfo.stPRPSInfo.eBandWidth = static_cast<DataFileNS::MapBandWidth>(pPRPSExtInformation->eFrequencyBand);
    stUHFPRPSPRPDDataInfo.stPRPSInfo.sGain = pPRPSExtInformation->sGain;
    stUHFPRPSPRPDDataInfo.stPRPSInfo.eDataSign = static_cast<PRPSMapNS::MapDataSign>(pPRPSExtInformation->eDataJudgmentFlag);
    stUHFPRPSPRPDDataInfo.stPRPSInfo.fAmpLowerLimit = pPRPSExtInformation->fAmpLowerLimit;
    stUHFPRPSPRPDDataInfo.stPRPSInfo.fAmpUpperLimit = pPRPSExtInformation->fAmpUpperLimit;
}

/****************************
功能： 获取图谱ext数据
输入参数:
    pMap -- 文件解析得到的图谱
    iDataPointCount -- 图谱数据数量
*****************************/
void UHFPRPSPlayBackView::getPRPDMapInfo(const DataSpecificationNS::PRPDExtInformation* pPRPDExtInformation, UHFPRPSPRPDDataInfo &stUHFPRPSPRPDDataInfo)
{
    stUHFPRPSPRPDDataInfo.stPRPDInfo.eSyncSource = static_cast<DataFileNS::SyncSource>(pPRPDExtInformation->eSyncSource);
    stUHFPRPSPRPDDataInfo.stPRPDInfo.ucSyncState = pPRPDExtInformation->ucSyncState;
    stUHFPRPSPRPDDataInfo.stPRPDInfo.iPhaseIntervalCount = pPRPDExtInformation->iPhaseWindowCount;
    stUHFPRPSPRPDDataInfo.stPRPDInfo.iQuantificationAmp = pPRPDExtInformation->iQuantizedAmplitude;
    stUHFPRPSPRPDDataInfo.stPRPDInfo.iPowerFreCycleCount = pPRPDExtInformation->iPowerFreqCycleCount;
    stUHFPRPSPRPDDataInfo.stPRPDInfo.eAmpUnit = static_cast<DataFileNS::AmpUnit>(pPRPDExtInformation->eAmpUnit);
    stUHFPRPSPRPDDataInfo.stPRPDInfo.eBandWidth = static_cast<DataFileNS::MapBandWidth>(pPRPDExtInformation->eFrequencyBand);
    stUHFPRPSPRPDDataInfo.stPRPDInfo.sGain = pPRPDExtInformation->sGain;
    stUHFPRPSPRPDDataInfo.stPRPDInfo.eDataSign = static_cast<PRPSMapNS::MapDataSign>(pPRPDExtInformation->eDataJudgmentFlag);
    stUHFPRPSPRPDDataInfo.stPRPDInfo.fAmpLowerLimit = pPRPDExtInformation->fAmpLowerLimit;
    stUHFPRPSPRPDDataInfo.stPRPDInfo.fAmpUpperLimit = pPRPDExtInformation->fAmpUpperLimit;
}

