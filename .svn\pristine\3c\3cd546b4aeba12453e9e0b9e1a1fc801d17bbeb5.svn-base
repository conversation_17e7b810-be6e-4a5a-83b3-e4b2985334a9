#!/bin/bash

#######error code define ########

orig_md5_file_err=108
md5_check_err=109
tarball_not_exist_err=110
tarball_format_err=111
version_not_exist_err=112
net_err=113
cmd_err=114
upgrade_err=127
#########################################################
# function name: upgrade_tarball
# input        : $1 -- tarball name
#              : $2 -- untar dir
# retval       : 0- ok;>0- bad
# description  : this function will untar the tarball file 
#                to designated dir
#########################################################
function upgrade_tarball()
{
	tarball=$1
	destdir=$2
	if [ ! -e $tarball ];then
		echo "Err,target:$tarball not found!"
		return $tarball_not_exist_err
	fi 
	tar xzf $tarball -C $destdir
	if [ $? != 0 ];then
		echo "untar $tarball to $destdir failed!"
		return $tarball_format_err
	fi
	sync
	return 0 
}
#########################################################
# function name: check_md5
# input        : $1 -- md5 file name
#              : $2 -- directory name to be checked
# retval       : 0- ok; >0 - bad
# description  : this function is used to check if the md5sum
#                of upgrade target listed in md5 file $1
#                is correct in directory $2
#########################################################
function check_md5()
{
	orig_md5_file=$1
	dirname=$2
	cd $dirname
	if [ ! -e $orig_md5_file ];then
		echo "Err,md5 file: $orig_md5_file not found in directory $dirname"
		#cd -
		return $orig_md5_file_err
	fi
	md5sum  -c $orig_md5_file 2>/dev/null
	if [ $? != 0 ];then
		echo "Err,md5 sum: $orig_md5_file  check error in directory $dirname"
		#cd -
		return $md5_check_err
	else
		#cd -
		return 0
	fi
}
#########################################################
# function name: check_md5
# input        : $1 -- upgrade tarball name
#              : $2 -- upgrade dirname
# retval       : 0- ok; >0 - bad
# description  : this function is used to check if the md5sum
#                of upgrade target $1 is correct 
#########################################################
function check_target_md5()
{
	tarball_name=$1
	upgrade_dirname=$2
	upgrade_target_name=$(basename $tarball_name .tar.gz)
	md5_file_name=$upgrade_target_name.orig.md5	
	check_md5 $md5_file_name $upgrade_dirname
	ret=$?
	return $ret
}
#########################################################
# function name: upgrade_target
# input        : $1 --  upgrade target tarball name 
#              : $2 --  upgrade dirname
# retval       : 0- ok;>0- bad
# description  : this function will untar the tarball file 
#                to designated dir,and check if the md5sum of
#                tarball files are correct
#########################################################
function upgrade_target()
{
	tarball_name=$1
	upgrade_dirname=$2
	upgrade_target_name=$(basename $tarball_name .tar.gz)
	md5_file_name=$upgrade_target_name.orig.md5
	
	upgrade_tarball $tarball_name $upgrade_dirname
	ret=$?
	[ $ret != 0 ] && return $ret
	
	check_md5 $md5_file_name $upgrade_dirname
	ret=$?
	return $ret
	
}


#########################################################
# function name: check_app_status
# input        : $1 -- app name
# retval       : 0- ok;1- bad
# description  : this function is used to check if app 
#                start up successfully
#########################################################
function check_app_status()
{
	base_app_name=$1
	if [ -e "/var/run/matrix-gui-2.0.pid" ];then
	    TARGET_PROGRAM_PID="/proc/$(cat /var/run/matrix-gui-2.0.pid)"
		if [ -d $TARGET_PROGRAM_PID ] && [ $(cat $TARGET_PROGRAM_PID/comm) = $base_app_name ]; then
			return 0;
		else
			echo "pid dir $TARGET_PROGRAM_PID "
			echo "pid name $(cat $TARGET_PROGRAM_PID/comm)"
			echo " target file name $base_app_name"
			return $upgrade_err;
		fi
	fi
	echo "matrix pid not exist!!!"
	return $upgrade_err;
}

function close_app()
{
	app_name=$1
	base_app_name=$(basename $app_name)
	/etc/init.d/matrix-gui-2.0 stop
	if [ -e "/var/run/matrix-gui-2.0.pid" ];then
		sleep 2
	fi
	return 0
}
function start_app()
{
	app_name=$1
	base_app_name=$(basename $app_name)
	/etc/init.d/matrix-gui-2.0 start
	###wait until target progess start up####
	sleep 2
	check_app_status $base_app_name
	ret=$?
	return $ret
}

cmd=$1
echo "cmd is $cmd"
case $cmd in
	"upgrade")
		##$2 -- upgrade tarball name
		##$3 -- upgrade dirname
		upgrade_target $2 $3
		exit $?
	;;
	"close_app")
		##$2 --app name
		close_app $2
		exit $?
	;;
	"start_app")
		##$2 -- app name
		start_app $2
		exit $?
	;;
	"check_md5")
		##$2 upgrade tarball name
		##$3 upgrade dirname
		check_target_md5 $2 $3
		exit $?
	;;
	*)
		echo "Usage:$0 [cmd [cmd param]]"
		echo "Valid Cmd:"
		echo "	upgrade [target name] [upgrade dirname]"
		echo "	close_app [app name]"
		echo "	start_app [app name]"
		echo "	check_md5 [target name] [upgrade dirname]"
		exit $cmd_err
	;;
esac
