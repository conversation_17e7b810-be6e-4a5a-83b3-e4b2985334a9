/*
 * new-upgrade.h
 *
 *  Created on: 2017年5月8日
 *      Author: Administrator
 */

#ifndef UPGRADE_NEW_UPGRADE_H_
#define UPGRADE_NEW_UPGRADE_H_
#ifdef __cplusplus    //__cplusplus是cpp中自定义的一个宏
extern "C" {          //告诉编译器，这部分代码按C语言的格式进行编译，而不是C++的
#endif

typedef char INT8;
typedef unsigned char UINT8;
typedef int INT32;

/*错误码定义*/
#define NULL_PARAM_ERR        (-1)
#define BASH_CMD_ERR          (-2)
#define MD5_FILE_ERR          (108)
#define MD5_CHECK_ERR         (109)
#define TARBALL_NOT_EXIST_ERR (110)
#define TARBALL_FORMAT_ERR    (111)
#define VERSION_NOT_EXIST_ERR (112)
#define NET_ERR               (113)
#define UPGRADE_CMD_ERR       (114)
#define FIRMWARE_BURN_ERR     (120) //固件烧写错误
#define UPGRADE_FAILED        (127)
/*****************************************************************
 * 函数名   : do_upgrade
 * 输入参数 ：pucTargetName --升级包文件名，包含绝对路径
 *        pucUpgradePath --升级包对应升级路径名
 * 输出参数 ：NULL
 * 返回值   ：0:SUCCESS; anyother value:FAILED 通过错误码查看相关错误定义
 * 功能     ：升级@pucTargetName指定的目标升级包
 * 说明     ：本函数依赖于upgrade.sh脚本执行具体的升级操作：
 *        1）检查升级目录是否存在@pucTargetName指定类型的升级包：升级包内包含待升级文件和md5校验和文件
 *        2）将升级包解压缩到对应的目录@pucUpgradePath下
 *        3）同步写入到磁盘内
 *        4）进行md5校验，确认写入磁盘文件的完整性
 ****************************************************************/
INT32 do_upgrade(UINT8 *pucTargetName,UINT8 *pucUpgradePath);
/*****************************************************************
 * 函数名   : close_app
 * 输入参数 ：pucAppName --app程序名
 * 输出参数 ：NULL
 * 返回值   ：0:SUCCESS; anyother value:FAILED 通过错误码查看相关错误定义
 * 功能     ：关闭@pucAppName程序
 * 说明     ：升级各个组件包前，需要将受影响的正在运行的app程序关闭，避免app运行出错。
 ****************************************************************/
INT32 close_app(UINT8 *pucAppName);
/*****************************************************************
 * 函数名   : start_app
 * 输入参数 ：pucAppName --app程序名
 * 输出参数 ：NULL
 * 返回值   ：0:SUCCESS; anyother value:FAILED 通过错误码查看相关错误定义
 * 功能     ：开启@pucAppName程序
 * 说明     ：升级各个组件包完成后，需要将受影响的app程序开启。
 ****************************************************************/
INT32 start_app(UINT8 *pucAppName);
/*****************************************************************
 * 函数名   : check_md5
 * 输入参数 ：pucTargetName --升级包文件名
 *        pucUpgradePath --升级包对应升级路径名
 * 输出参数 ：NULL
 * 返回值   ：0:SUCCESS; any other value:FAILED 通过错误码查看相关错误定义
 * 功能     ：校验md5文件@pucUpgradePath内包含的每个文件对应的md5码值是否正确
 * 说明     ：升级包对应md5文件命名规则：xxx.tar.gz 《--》 xxx.orig.md5
 ****************************************************************/
INT32 check_md5(UINT8 *pucTargetName,UINT8 *pucUpgradePath);
//获取libupgrade.so库版本号
const INT8 *get_upgrade_version(void);
#ifdef __cplusplus    //__cplusplus是cpp中自定义的一个宏
}        //告诉编译器，这部分代码按C语言的格式进行编译，而不是C++的
#endif
#endif /* UPGRADE_NEW_UPGRADE_H_ */
