/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* SliderPopup.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月10日
* 修改日期：2016年11月15日
* 作者：邵震宇
*       重构
* 摘要：滑块式弹出式窗体基类定义
*     1、从弹出窗体PopupWidget继承
*     2、可设置范围、步长、最大最小值等
* 当前版本：1.0
*/
#ifndef SLIDERPOPUP_H
#define SLIDERPOPUP_H
#include <QLabel>
#include <QPushButton>
#include <QSlider>
#include "PopupWidget.h"
#include "Slider.h"

class SliderPopup : public PopupWidget
{
    Q_OBJECT
public:
    /*****************************************************************
     * 功能     ：构造函数，初始化滑块类
     * 输入参数 ：
     *      parent -- 父控件
     ****************************************************************/
    SliderPopup( QWidget *parent = NULL );

    /*****************************************************************
     * 功能     ：设置滑块的数值选项
     * 输入参数 ：
     *      vecValues -- 滑块的数值选项数组
     ****************************************************************/
    void setValues( const QVector<int>& vecValues );

    /*****************************************************************
     * 输入参数 ：
     *      iMin -- 最小值
     *      iMax -- 最大值
     *      iStep -- 步长
     * 功能     ：设置滑块的取值范围
     ****************************************************************/
    void setRange( int iMin, int iMax, int iStep );

    /*****************************************************************
     * 功能     ：返回滑块的取值数组
     * 返回值   ：滑块的取值数组
     ****************************************************************/
    QVector<int> values() const;

    /*****************************************************************
     * 功能     ：设置滑块的后缀
     * 输入参数 ：后缀(一般为各种单位的字符串，如mV)
     ****************************************************************/
    void setSuffix(const QString &suffix);

    /*****************************************************************
     * 功能     ：返回滑块的后缀
     * 返回值   ：后缀(一般为各种单位的字符串，如mV)
     ****************************************************************/
    QString suffix() const;

    /*****************************************************************
     * 功能     ：返回滑块的最小值
     * 返回值   ：最小值
     ****************************************************************/
    int mininum() const;

    /*****************************************************************
     * 功能     ：返回滑块的最大值
     * 返回值   ：最大值
     ****************************************************************/
    int maxium() const;

    /*****************************************************************
     * 输入参数 ：当前值
     * 功能     ：返回不在步进值映射下的当前值的近似值
     * 返回值   ：近似值
     ****************************************************************/
    qint16 getApproximateVal(qint16 wCurVal);

protected:
    /*************************************************
    功能： 鼠标点击事件
    输入参数:
        event -- 事件
    *************************************************************/
    void mousePressEvent(QMouseEvent *event);

    /*************************************************
    功能： 焦点消失时间
    输入参数:
        event -- 事件
    *************************************************************/
    void focusOutEvent(QFocusEvent *event);

    /*************************************************
    功能： 根据设立的数组列表建立界面
    输入参数:
        vecValues -- 可选数组
    *************************************************************/
    virtual void createUI( const QVector<int>& vecValues );

    /*************************************************
    功能： 根据设立的数组列表刷新界面
    输入参数:
        vecValues -- 可选数组
    *************************************************************/
    virtual void updateUI( const QVector<int>& vecValues );

    /*************************************************
    功能： 设置视图数据
    输入参数:
        iValue:当前值
    *************************************************************/
    void setViewValue( int iValue );

    /*************************************************
    功能： 响应索引刷新
    输入参数:
        iIndex:索引
    *************************************************************/
    virtual void onIndexUpdate( int iIndex );

    /*************************************************
    功能： 获取当前索引
    返回：
        当前索引
    *************************************************************/
    virtual int getCurrentIndex();

    /*************************************************
    功能： 事件过滤器
    返回：
        true -- 不在继续处理该事件
        false -- 继续处理该事件
    *************************************************************/
    virtual bool eventFilter(QObject *pObj, QEvent *pEv);
protected slots:
    /*****************************************************************
     * 功能     ：槽，确认修改
     ****************************************************************/
    void confirmValue();

    /*****************************************************************
     * 函数名  ： timerEvent(QTimerEvent * pEvent)
     * 输入参数 ：QTimerEvent指针
     * 输出参数 ：NULL
     * 返回值   ：NULL
     * 功能     ：定时器处理函数
     ****************************************************************/
    void timerEvent(QTimerEvent * pEvent);

private:
    void paraseAddAction( void );


    void paraseSubAction( void );

private slots:
    /*****************************************************************
     * 功能     ：槽，往前移一步
     ****************************************************************/
    void moveForward();
    /*****************************************************************
     * 功能     ：槽，响应减键
     ****************************************************************/
    void moveBackward();

    /*****************************************************************
     * 功能     ：槽，响应值变化
     ****************************************************************/
    void onValueChanged(int value);

    /*****************************************************************
     * 函数名  ： onAddBtnPressed(void)
     * 输入参数 ：NULL
     * 输出参数 ：NULL
     * 返回值   ：NULL
     * 功能     ：+按钮按下处理
     ****************************************************************/
    void onAddBtnPressed();

    /*****************************************************************
     * 函数名  ： onSubBtnPressed(void)
     * 输入参数 ：NULL
     * 输出参数 ：NULL
     * 返回值   ：NULL
     * 功能     ：-按钮按下处理
     ****************************************************************/
    void onSubBtnPressed(void);

    /*****************************************************************
     * 函数名  ： onAddBtnReleased(void)
     * 输入参数 ：NULL
     * 输出参数 ：NULL
     * 返回值   ：NULL
     * 功能     ：+按钮松开处理
     ****************************************************************/
    void onAddBtnReleased(void);

    /*****************************************************************
     * 函数名  ： onSubBtnReleased(void)
     * 输入参数 ：NULL
     * 输出参数 ：NULL
     * 返回值   ：NULL
     * 功能     ：-按钮松开处理
     ****************************************************************/
    void onSubBtnReleased();

private:
    enum {
        INVALID_TIMER_ID = -1,
        AUTO_CHANGE_VALUE_TIMER_200_MS = 200
    };
    Slider* m_pSlider;//滑块
    QLabel* m_plabelValue;//当前值标签
    QPushButton* m_pBtnAdd;
    QPushButton* m_pBtnSub;

    QString m_strSuffix;//量纲
    QVector<int> m_vecValues;//滑块可选值

    bool m_isPressAddBtn;
    bool m_isPressSubBtn;
    int m_iAutoChangeValueTimerId;

    int m_iIndex;//当前索引
    int m_iChangedCount;            //记录由于拖动变化引起的改变次数
    qint64 m_lLastValChangedTime;           //拖动变化防抖动策略

};

#endif // SLIDER_H
