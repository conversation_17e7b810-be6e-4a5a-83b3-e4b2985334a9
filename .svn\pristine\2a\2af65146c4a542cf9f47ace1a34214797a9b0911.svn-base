/*
* Copyright (c) 2017.11，南京华乘电气科技有限公司
* All rights reserved.
*
* infraredviewbase.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年11月14日
* 摘要：红外视图基类

* 当前版本：1.0
*/

#ifndef INFRAREDVIEWBASE_H
#define INFRAREDVIEWBASE_H

#include <QWidget>
#include <QGraphicsView>
#include <QGraphicsScene>
#include <QGraphicsProxyWidget>
#include <QGraphicsLinearLayout>
#include "Module.h"
#include "infrared/infraredimagingview.h"
#include "messageBox/msgbox.h"
#include "infrared/controlButton/infraredcontrolbuttonbar.h"

//站点信息
typedef struct _StationInfo_
{
    QString strStation;
    QString strDevice;
    QString strTestPoint;
    bool bShow;

    _StationInfo_()
    {
        strStation = "";
        strDevice = "";
        strTestPoint = "";
        bShow = false;
    }
}StationInfo;

typedef struct _InfraredSaveInfo_
{
    QString qstrDataFilePath;
    QString qstrPicturePath;

    _InfraredSaveInfo_()
    {
        qstrDataFilePath = "";
        qstrPicturePath = "";
    }

}InfraredSaveInfo;


class InfraredViewBase : public QWidget
{
    Q_OBJECT
public:
    /*************************************************
    函数名： InfraredViewBase(QWidget *parent = 0)
    输入参数： parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit InfraredViewBase(QWidget *parent = 0);

    /*************************************************
    函数名： ~InfraredViewBase()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~InfraredViewBase();

    /*************************************************
    函数名： rotateMsgBox(MsgBox *pMsgBox, int angle = 90)
    输入参数： pMsgBox：消息提示框指针
              angle：旋转角度
    输出参数： NULL
    返回值： NULL
    功能： 旋转消息提示框
    *************************************************************/
    static int rotateMsgBox(MsgBox *pMsgBox, int angle = 90);

    /*************************************************
    功能： 旋转消息提示框
    *************************************************************/
    static int rotateDialog(QDialog *pDialog, int angle = 90);

    /*******************************
     * 功能：设置站点信息
     * 输入参数：
     *      station：站点名称
     *      device：设备名称
     *      testpoint：测点名称
     * ********************************/
    void setStationInfo(const QString &station, const QString &device, const QString &testpoint);

    /*******************************
     * 功能：显示或隐藏站点信息
     * 输入参数：
     *      bShow：true -- 显示，false -- 隐藏
     * ********************************/
    void showStationInfo(bool bShow);

    /**********************************************
     * 功能，获取红外显示界面
     * *********************************************/
    InfraredImagingView* getIRImageView();

    /**********************************************
     * 功能：保存数据
     * 输入参数：
     *      bSaveJpeg：是否保存图片
     * ************************************************/
    virtual void saveData(bool bSaveJpeg = false);

    /***********************************************************
     * 功能：设置当前界面供回放使用标识
     * 输入参数：
     *      bPlaybackView：标识，true -- 是供回放使用，false -- 不是供回放使用
     * **********************************************************/
    void setPlaybackViewTag(bool bPlaybackView);

signals:
    /*************************************************
    功能： 信号，窗口关闭
    *************************************************************/
    void sigClosed();

    /*************************************************
    功能： 保存结束的信号
    *************************************************************/
    void sigSaveFinished();

    /*************************************************
    功能： 保存图片信号
    *************************************************************/
    void sigSavePicture(QString qstrPicPath);

    /*************************************************
    功能： 释放退出红外检测信号
    *************************************************************/
    void sigExitInfrared();

private slots:
    /*************************************************
    函数名： onButtonPressed()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 按钮响应槽函数
    *************************************************************/
    void onButtonPressed();

    /****************************************************
     * 功能：保存图片数据槽函数
     * ******************************************************/
    void onSavePicture(QString qstrPicPath);

    /*************************************************
    功能： 响应设置参数值槽函数
    输入参数： valueList：参数值列表
    *************************************************************/
    void onSetInfraredCalibrationParams(QList<double> valueList);

    /*************************************************
    功能： 响应按钮状态变化
    *************************************************************/
    void onChangeButtonState(int iFlag);

protected:
    /*************************************************
    函数名： createButtonBar(const InfraredButtonInfo *pButtonInfos, int iCount)
    输入参数： pButtonInfos：按钮信息
              iCount：按钮个数
    输出参数： NULL
    返回值： ButtonBar指针
    功能： 创建ButtonBar
    *************************************************************/
    InfraredControlButtonBar* createButtonBar(const InfraredButtonInfo *pButtonInfos, int iCount);

    /*************************************************
    函数名： setButtonBar(InfraredControlButtonBar *pButtonBar)
    输入参数： pButtonBar：ButtonBar指针
    输出参数： NULL
    返回值： NULL
    功能： 设置ButtonBar
    *************************************************************/
    void setButtonBar(InfraredControlButtonBar *pButtonBar);

    /*************************************************
    函数名： onButtonPressed(UINT8 ucID)
    输入参数： ucID：按钮ID
    输出参数： NULL
    返回值： NULL
    功能： 按钮响应处理
    *************************************************************/
    virtual void onButtonPressed(UINT8 ucID);

    virtual void keyPressEvent(QKeyEvent* pEvent);

    /*************************************************
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 关闭所有非模态对话框
    *************************************************************/
    void closeAllNonModal();

    /*处理msgbox里文本内容过长（目前仅保存成功显示文件名时使用）*/
    void processTooLongMsgText(QString &strText);

    /*************************************************
    功能： 显示事件处理
    *************************************************************/
    void showEvent(QShowEvent *e);

    /*************************************************
    功能： 窗口关闭事件
    输入参数:
        event -- 事件
    *************************************************************/
    void closeEvent(QCloseEvent* event);

    /*************************************************
    功能： 延迟关闭界面，用于查看检测数据
    *************************************************************/
    void delayToClose();

    /*************************************************
    功能： 关闭自动跳转定时器
    *************************************************************/
    void killAutoSwitchTimer();

    /*************************************************
    功能： 定时器处理函数
    输入参数：
         event -- 定时事件
    *************************************************************/
    void timerEvent(QTimerEvent* event);

    /*************************************************
    输入参数： qstrMsg -- 提示信息
    输出参数： NULL
    返回值： NULL
    功能： 显示等待对话框
    *************************************************************/
    void showWaitingDialog(QString qstrMsg);

    /*************************************************
    功能： 显示文件备注框
    *************************************************************/
    QString showFileCommentBox();

private:
    /*************************************************
    函数名： changePalette()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 改变调色板
    *************************************************************/
    void changePalette();

    /*************************************************
    函数名： analysisShape()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 分析图形
    *************************************************************/
    void analysisShape();

    /*************************************************
    函数名： adjustImageViewSize()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 调整ImageView的尺寸
    *************************************************************/
    void adjustImageViewSize();

    /*************************************************
    功能：显示校准界面
    *************************************************************/
    void showCalibrationDialog();

protected:
    enum
    {
        VIEW_ROTATE_ANGLE = 90,
        SCENE_OFFSET = 5,
        BUTTONBAR_COLUMN_NUM = 2,
        BUTTON_FONT_SIZE = 20,
        BUTTON_ACTIVE_FONT_SIZE = 20,
        BUTTON_WIDTH = 90,
        BUTTON_HEIGHT = 90,
    };

    // 菜单按钮管理
    InfraredControlButtonBar *m_pButtonBar;
    InfraredImagingView *m_pInfraredImagingView;
    ColorType m_eCurrentPalette;        // 显示模式
    bool m_bPdaWaiting;

private:
    //图像显示区
    QGraphicsView *m_pImageView;
    QGraphicsScene *m_pImageScene;

    //菜单按钮区
    QGraphicsView *m_pButtonBarView;
    QGraphicsScene *m_pButtonBarScene;

    QGraphicsProxyWidget *m_pButtonBarProxyWidget;

    QGraphicsLinearLayout *m_pButtonBarLayout;
    QGraphicsLinearLayout *m_pImageLayout;

    ButtonShape m_eCurrentShape;

    StationInfo m_stationInfo;

    int m_iAutoSwitchTimerId;

protected:
    int m_iPrecious;//温度精度，几位小数
};

#endif // INFRAREDVIEWBASE_H
