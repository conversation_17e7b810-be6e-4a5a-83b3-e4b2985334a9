#include "getrefont.h"
#include <QFontMetrics>
#include <QDebug>
#include <QApplication>
#include <QDesktopWidget>
#include "Widget.h"

GetReFont::GetReFont()
{
}

/************************************************
 * 功能     : 根据缩放需求获取Font
 * 输入参数 :
 *      fontNow -- 当前字体
 *      strText -- 显示的字符串
 *      fWidthTo -- 目标宽度
 ************************************************/
QFont GetReFont::getRewidthFont( const QFont& fontNow, const QString& strText, float fWidthTo )
{
    QFontMetrics fm( fontNow );

    float fRatio = fWidthTo/fm.width( strText );
    QFont font = fontNow;
    if( font.pointSizeF()*fRatio < 0 )
    {
        font.setPointSizeF( font.pixelSize()*fRatio );
    }
    else
    {
        font.setPointSizeF( font.pointSizeF()*fRatio );
    }

    return font;
}

/************************************************
 * 功能     : 根据缩放需求获取Font
 * 输入参数 :
 *      fontNow -- 当前字体
 *      fHeightTo -- 目标高度
 ************************************************/
QFont GetReFont::getReheightFont( const QFont& fontNow, float fHeightTo )
{
    QFontMetrics fm( fontNow );
    float fRatio = fHeightTo / fm.height() * getFontRatio();

    QFont font = fontNow;
    if( font.pointSizeF()*fRatio < 0 )
    {
        font.setPointSizeF( font.pixelSize()*fRatio );
    }
    else
    {
        font.setPointSizeF( font.pointSizeF()*fRatio );
    }

    return font;
}

/************************************************
 * 功能     : 获取适合屏幕的位置（避免出界）
 * 输入参数 :
 *      pos -- 当前位置
 *      size -- 窗体大小
 ************************************************/
QPoint GetReFont::getFitScreenPosition( const QPoint& pos, const QSize& size )
{
    QRect rect = qApp->desktop()->screenGeometry();
    QPoint posFit = pos;

    //左边出界
    if( pos.x() < rect.left())
    {
        posFit.setX( rect.left() );
    }
    //上边出界
    if( pos.y() < rect.top() )
    {
        posFit.setY( rect.top() );
    }
    //右边出界
    if( pos.x() + size.width() > rect.left() + rect.width() )
    {
        posFit.setX( rect.left() + rect.width() - size.width() );
    }
    //下边出界
    if( pos.y() + size.height() > rect.top() + rect.height() )
    {
        posFit.setY( rect.y() + rect.height() - size.height() );
    }

    return posFit;
}
