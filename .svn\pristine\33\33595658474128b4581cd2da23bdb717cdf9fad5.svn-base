#include "tevpulsechart.h"
#include <QVBoxLayout>
#include "window/Window.h"
#include "appfontmanager/appfontmanager.h"

#define RUN_FLAG_SIZE 30

/*************************************************
函数名： TEVPulseChart(QObject *parent)
输入参数： parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
TEVPulseChart::TEVPulseChart(int iHeight, QWidget *parent)
    :ChartWidget(parent)
    ,m_iTimer(-1)
    ,m_eSampleMode(Module::SAMPLEMODE_CONTINUOUS)
    ,m_uc<PERSON><PERSON>wAlert(TEV::YELLOW_ALERT_DEFAULT)
    ,m_ucRedAlert(TEV::RED_ALERT_DEFAULT)
{
    setAutoFillBackground(true);
    setFixedSize(Window::WIDTH, iHeight);

    //QLinearGradient linearGrad(QPointF(0, 0), QPointF(0, CHART_MIN_HEIGHT));
    //linearGrad.setColorAt(0, Qt::white);
    //linearGrad.setColorAt(1, QColor(181, 210, 236));
    //QPalette pal;
    //pal.setBrush(QPalette::Window, QBrush(linearGrad));
    //setPalette(pal);

    createLabels();
    layoutContents();

    memset(&m_data, 0, sizeof(TEV::PulseData));
    m_data.cAmpValue = -1;
    m_pCapacityValueLabel->setText(QString("<font color=%1>%2</font>")
                                   .arg(getCapacityTextColorName(m_data.cAmpValue))
                                   .arg(getCapacityValueText(m_data.cAmpValue)));

}

/*************************************************
函数名： createLabels()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 创建所有Label
*************************************************************/
void TEVPulseChart::createLabels()
{
    m_pRunningFlagLabel = new QLabel(this);
    m_pRunningFlagLabel->setAlignment(Qt::AlignCenter);
    m_pRunningFlagLabel->setFixedSize(RUN_FLAG_SIZE, RUN_FLAG_SIZE);

    m_pCountTextLabel = new QLabel(trUtf8("Pulse Count: "), this);
    QFont textFont = m_pCountTextLabel->font();
    textFont.setPointSize(30);
    m_pCountTextLabel->setFont(textFont);
    m_pCountValueLabel = new QLabel(this);
    m_pCountValueLabel->setFont(textFont);

    m_pCircleTextLabel = new QLabel(trUtf8("Pulses/Cycle: "), this);
    m_pCircleTextLabel->setFont(textFont);
    m_pCircleValueLabel = new QLabel(this);
    m_pCircleValueLabel->setFont(textFont);

    m_pLevelTextLabel = new QLabel(trUtf8("Severity: "), this);
    m_pLevelTextLabel->setFont(textFont);
    m_pLevelValueLabel = new QLabel(this);
    m_pLevelValueLabel->setFont(textFont);

    m_pCapacityValueLabel = new QLabel(this);
    m_pCapacityValueLabel->setAlignment(Qt::AlignRight | Qt::AlignBottom);
    QFont font = m_pCapacityValueLabel->font();
    font.setPointSize(60);
    m_pCapacityValueLabel->setFont(font);
    m_pCapacityUnitLabel = new QLabel("dB", this);
    m_pCapacityUnitLabel->setFont(font);
    m_pCapacityUnitLabel->setAlignment(Qt::AlignBottom | Qt::AlignLeft);

    m_pDiagInfo = new QLabel();
    m_pDiagInfo->setAlignment(Qt::AlignCenter);
    m_pDiagInfo->setMargin(5);
    font = AppFontManager::instance()->getAppCurFont();
    font.setPointSize(24);
    m_pDiagInfo->setFont(font);
    m_pDiagInfo->setText("");

    return;
}

/*************************************************
函数名： layoutContents()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 布局内容
*************************************************************/
void TEVPulseChart::layoutContents()
{
    QVBoxLayout *hFlagLayout = new QVBoxLayout;
    hFlagLayout->addWidget(m_pDiagInfo, 0, Qt::AlignRight);
    hFlagLayout->addWidget(m_pRunningFlagLabel, 0, Qt::AlignRight);

    QHBoxLayout *hCountLayout = new QHBoxLayout;
    hCountLayout->setAlignment(Qt::AlignVCenter | Qt::AlignLeft);
    hCountLayout->addWidget(m_pCountTextLabel);
    hCountLayout->addWidget(m_pCountValueLabel);

    QHBoxLayout *hCircleLayout = new QHBoxLayout;
    hCircleLayout->setAlignment(Qt::AlignVCenter | Qt::AlignLeft);
    hCircleLayout->addWidget(m_pCircleTextLabel);
    hCircleLayout->addWidget(m_pCircleValueLabel);

    QHBoxLayout *hLevelLayout = new QHBoxLayout;
    hLevelLayout->setAlignment(Qt::AlignVCenter | Qt::AlignLeft);
    hLevelLayout->addWidget(m_pLevelTextLabel);
    hLevelLayout->addWidget(m_pLevelValueLabel);

    QHBoxLayout *hCapacityLayout = new QHBoxLayout;
    hCapacityLayout->setAlignment(Qt::AlignRight | Qt::AlignVCenter);
    hCapacityLayout->addWidget(m_pCapacityValueLabel);
    hCapacityLayout->addWidget(m_pCapacityUnitLabel);

    QVBoxLayout *vMainLayout = new QVBoxLayout;
    vMainLayout->addLayout(hFlagLayout);
    vMainLayout->addLayout(hCountLayout);
    vMainLayout->addLayout(hCircleLayout);
    vMainLayout->addLayout(hLevelLayout);
    vMainLayout->addLayout(hCapacityLayout);

    setLayout(vMainLayout);
}

/*************************************************
函数名： getCapacityValueText(INT8 cAmpValue)
输入参数： cAmpValue：幅值
输出参数： NULL
返回值： 幅值文本
功能： 获取幅值文本
*************************************************************/
QString TEVPulseChart::getCapacityValueText(INT8 cAmpValue)
{
    QString strValue;

    if (cAmpValue < TEV::CHART_MIN_VALUE)
    {
        strValue = QString("&lt;%1").arg(INT32(TEV::CHART_MIN_VALUE));
    }
    else if (cAmpValue > TEV::CHART_MAX_VALUE)
    {
        strValue = QString("&gt;%1").arg(INT32(TEV::CHART_MAX_VALUE));
    }
    else
    {
        strValue = QString("%1").arg(INT32(cAmpValue));
    }

    return strValue;
}

/*************************************************
函数名： addSample(TEV::PulseData &data)
输入参数： data：脉冲数据
输出参数： NULL
返回值： NULL
功能： 添加新数据
*************************************************************/
void TEVPulseChart::addSample(TEV::PulseData &data)
{
    memcpy(&m_data, &data, sizeof(TEV::PulseData));

    m_pCountValueLabel->setText(QString("%1").arg(data.uiPulseNum));
    m_pCircleValueLabel->setText(QString("%1").arg(data.uiPerPulseNum));
    m_pLevelValueLabel->setText(QString("%1").arg(data.uiPDSeverity));

    m_pCapacityValueLabel->setText(QString("<font color=%1>%2</font>")
                                   .arg(getCapacityTextColorName(data.cAmpValue))
                                   .arg(getCapacityValueText(data.cAmpValue)));
}

/*************************************************
函数名： getCapacityTextColorName(INT8 cAmpValue)
输入参数： cAmpValue：幅值
输出参数： NULL
返回值： 幅值文本颜色名称
功能： 获取幅值文本颜色名称
*************************************************************/
QString TEVPulseChart::getCapacityTextColorName(INT8 cAmpValue)
{
    QColor color;

    if (cAmpValue < m_ucYellowAlert)
    {
        //color = Qt::green;
        color = Qt::darkGreen;
    }
    else if (cAmpValue <= m_ucRedAlert)
    {
        //color = Qt::yellow;
        color = QColor(255, 140, 0);
    }
    else
    {
        color = Qt::red;
    }

    return color.name();
}

/*************************************************
函数名： setAlarm(UINT8 iYellowAlarm, UINT8 iRedAlarm)
输入参数： iYellowAlarm：黄色报警值
          iRedAlarm：红色报警值
输出参数： NULL
返回值： NULL
功能： 设置红黄报警值
*************************************************************/
void TEVPulseChart::setAlarm(UINT8 iYellowAlarm, UINT8 iRedAlarm)
{
    m_ucYellowAlert = iYellowAlarm;
    m_ucRedAlert = iRedAlarm;

    if (Module::SAMPLEMODE_SINGLE == m_eSampleMode)
    {
        m_pCapacityValueLabel->setText(QString("<font color=%1>%2</font>")
                                       .arg(getCapacityTextColorName(m_data.cAmpValue))
                                       .arg(getCapacityValueText(m_data.cAmpValue)));
    }
}

/*************************************************
函数名： setSampleMode(Module::SampleMode eSampleMode)
输入参数： eSampleMode：采集模式
输出参数： NULL
返回值： NULL
功能： 设置采集模式
*************************************************************/
void TEVPulseChart::setSampleMode(Module::SampleMode eSampleMode)
{
    m_eSampleMode = eSampleMode;
    if (Module::SAMPLEMODE_SINGLE == m_eSampleMode)
    {
        setRunningStatus(false);  //单次模式下运行光标不闪烁
    }
    else
    {
        setRunningStatus(true);
    }
}

/*************************************************
函数名： setRunningStatus(bool running)
输入参数： running：运行标志状态
输出参数： NULL
返回值： NULL
功能： 设置运行标志状态
*************************************************************/
void TEVPulseChart::setRunningStatus(bool running)
{
    if (running)
    {
        if (-1 == m_iTimer)
        {
            m_iTimer = startTimer(DEFAULT_TIMER_INTERVAL);
        }
    }
    else
    {
        if (m_iTimer != -1)
        {
            killTimer(m_iTimer);
            m_iTimer = -1;
        }

        m_pRunningFlagLabel->setPixmap(QPixmap(""));
    }
}

/*****************************************
 * 功能：获取数据
 * 返回值：
 *      TEV::PulseData：当前数据
 * ***************************************/
TEV::PulseData TEVPulseChart::getData()
{
    return m_data;
}

/************************************************
 * 函数名    :getTEVAmp
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：脉冲幅值数据
 * 功能     ：获取脉冲幅值数据
 ************************************************/
INT8 TEVPulseChart::getTEVAmp()
{
    return m_data.cAmpValue;
}

/************************************************
 * 函数名    :getPulseCount
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：脉冲计数
 * 功能     ：获取脉冲计数
 ************************************************/
UINT32 TEVPulseChart::getPulseCount()
{
    return m_data.uiPulseNum;
}

/************************************************
 * 函数名    :getPulseCountPerPeriod
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：单周期脉冲数
 * 功能     ：获取单周期脉冲数
 ************************************************/
UINT32 TEVPulseChart::getPulseCountPerPeriod()
{
    return m_data.uiPerPulseNum;
}

/************************************************
 * 函数名    :getSeverity
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：严重程度
 * 功能     ：获取严重程度
 ************************************************/
UINT32 TEVPulseChart::getSeverity()
{
    return m_data.uiPDSeverity;
}

/*************************************************
函数名： timerEvent(QTimerEvent *event)
输入参数： event：定时器事件
输出参数： NULL
返回值： NULL
功能： 定时器事件处理
*************************************************************/
void TEVPulseChart::timerEvent(QTimerEvent *event)
{
    if (event->timerId() == m_iTimer)
    {
        const QPixmap *pixmap = m_pRunningFlagLabel->pixmap();
        if (NULL == pixmap || pixmap->isNull())
        {
            m_pRunningFlagLabel->setPixmap(QPixmap(":/images/Start_24px.png"));
        }
        else
        {
            m_pRunningFlagLabel->setPixmap(QPixmap(""));
        }
    }
}

/************************************************
 * 功能：设置诊断状态
 * 输入参数：
 *      stDiagDisplayInfo：诊断信息
 ************************************************/
void TEVPulseChart::setDiagRet(const DiagConfig::DiagDisplayInfo &stDiagDisplayInfo, const bool bShowDiagDisplayInfo)
{
    m_stDiagInfo = stDiagDisplayInfo;
    if(m_pDiagInfo && bShowDiagDisplayInfo)
    {
        m_pDiagInfo->setText(DiagConfig::getDiagnosisDisplayInfo(m_stDiagInfo));
    }

    return;
}

/************************************************
 * 功能：回放诊断信息
 * 输入参数：
 *      stDiagDisplayInfo：诊断信息
 ************************************************/
void TEVPulseChart::playbackDiagInfo(const DiagConfig::DiagDisplayInfo& stDiagDisplayInfo)
{
    m_stDiagInfo = stDiagDisplayInfo;

    if(m_pDiagInfo)
    {
        if (DiagConfig::Diag_Unrecord != m_stDiagInfo.eDiagRet)
        {
            m_pDiagInfo->setText(DiagConfig::getDiagnosisDisplayInfo(m_stDiagInfo, true));
        }
        else
        {
            m_pDiagInfo->clear();
        }
    }
}

/************************************************
 * 功能：清除数据
 ************************************************/
void TEVPulseChart::clear()
{
    memset(&m_data, 0, sizeof(TEV::PulseData));
    m_data.cAmpValue = -1;

    m_pCountValueLabel->clear();
    m_pCircleValueLabel->clear();
    m_pLevelValueLabel->clear();
    m_pCapacityValueLabel->clear();
}

/************************************************
 * 功能：清除诊断状态
 ************************************************/
void TEVPulseChart::clearDiagRet()
{
    m_stDiagInfo.eDiagRet = DiagConfig::Diag_Unrecord;
    m_stDiagInfo.qstrPDDesInfo = "";
    m_stDiagInfo.qstrPDSignalInfos = "";

    if(m_pDiagInfo)
    {
        m_pDiagInfo->setText("");
    }

    return;
}

/*********************************************
 * 功能：获取局放诊断结果
 * 返回值：
 *      局放诊断结果
 * *********************************************/
DiagConfig::DiagnoseRet TEVPulseChart::getPDDiagnosisRet()
{
    return m_stDiagInfo.eDiagRet;
}

/*********************************************
 * 功能：获取局放诊断信号类型描述信息
 * 返回值：
 *      局放诊断信号类型描述信息
 * *********************************************/
QString TEVPulseChart::getPDSignalInfos()
{
    return m_stDiagInfo.qstrPDSignalInfos;
}

/*********************************************
 * 功能：获取当前诊断信息
 * 返回值：
 *      QString：当前诊断信息
 * *******************************************/
QString TEVPulseChart::getDiagRetInfo()
{
    return DiagConfig::getDiagnosisInfo(m_stDiagInfo);
}
