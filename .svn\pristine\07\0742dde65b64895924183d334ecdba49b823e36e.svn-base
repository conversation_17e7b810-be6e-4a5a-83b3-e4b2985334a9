#include "tevcalibratetestview.h"
#include <QBoxLayout>
#include <QKeyEvent>
#include <QtConcurrentRun>
#include <QMetaType>
#include <QTimer>
#include <QTimerEvent>
#include "UHFHFCTAETEVApi.h"
#include "Window.h"
#include "titlebar/TitleBar.h"
#include "tev/calibrate/calibrateviewdefine.h"
#include "Module.h"
#include "log/log.h"





//static void pfnStartTest(TEVCalibrateTestView *pUser)
//{
//    if(pUser)
//    {
//        pUser->startCalibrateTest();
//    }

//    return;
//}

#define INVALID_TIMERID -1
#define READ_TEST_INTERVAL 1000

TEVCalibrateTestService::TEVCalibrateTestService()
    : m_pThread(NULL)
    , m_iTestTimerId(INVALID_TIMERID)
{
    connect(this, SIGNAL(sigStart()), this, SLOT(onStart()));
    connect(this, SIGNAL(sigStop()), this, SLOT(onStop()));

    m_pThread = new QThread(this);
    moveToThread(m_pThread);

    if(!m_pThread->isRunning())
    {
        m_pThread->start();
    }
}

TEVCalibrateTestService::~TEVCalibrateTestService()
{
    stop();

    disconnect(this, SIGNAL(sigStart()), this, SLOT(onStart()));
    disconnect(this, SIGNAL(sigStop()), this, SLOT(onStop()));

    if(m_pThread->isRunning())
    {
        m_pThread->quit();
        m_pThread->wait(3000);
    }
}

TEVCalibrateTestService* TEVCalibrateTestService::instace()
{
    static TEVCalibrateTestService objServicre;
    return &objServicre;
}

void TEVCalibrateTestService::start()
{
    emit sigStart();
    return;
}

void TEVCalibrateTestService::stop()
{
    emit sigStop();
    return;
}

void TEVCalibrateTestService::timerEvent(QTimerEvent* event)
{
    if(event->timerId() == m_iTestTimerId)
    {
        startTest();
    }

    return;
}

void TEVCalibrateTestService::onStart()
{
    if(INVALID_TIMERID == m_iTestTimerId)
    {
        m_iTestTimerId = startTimer(READ_TEST_INTERVAL);
    }

    return;
}

void TEVCalibrateTestService::onStop()
{
    if(INVALID_TIMERID != m_iTestTimerId)
    {
        killTimer(m_iTestTimerId);
        m_iTestTimerId = INVALID_TIMERID;
    }

    return;
}

void TEVCalibrateTestService::startTest()
{
    TevSample stData;
    memset(&stData, 0, sizeof(stData));
    if(0 == test_tev_sample(&stData))
    {
        TEVTestSampleData stSampleData;

        stSampleData.qui8DBVal = stData.ucDB;
        memcpy(stSampleData.aui16DACVal, stData.ausDacVal, sizeof(stData.ausDacVal));
        memcpy(stSampleData.aui16DACRef, stData.ausDacRef, sizeof(stData.ausDacRef));

        emit sigTestRet(stSampleData);
    }
    else
    {
        logWarning("tev test sample failed.");
    }

    return;
}


const int TITLE_HEIGHT = 100;
const int CALIBITEM_HEIGHT = 135;

const QString LABEL_STYLE = "QLabel{color: black; font-size: 60px}";
const QString TITLE_STYLE = "QLabel{background-color: rgb(30, 114, 255); color: white; font-size: 28px}";

TEVCalibrateTestView::TEVCalibrateTestView(QWidget *parent)
    : QWidget(parent)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);
    setAttribute(Qt::WA_DeleteOnClose);
    setWindowFlags(Qt::FramelessWindowHint);
    //qRegisterMetaType<QSharedPointer<TEVTestSampleData> >("QSharedPointer<TEVTestSampleData>");
    qRegisterMetaType<TEVTestSampleData>("TEVTestSampleData");

    createUI();
}

TEVCalibrateTestView::~TEVCalibrateTestView()
{

}

void TEVCalibrateTestView::createUI()
{
    m_plblCurVal = NULL;
    m_pCabItem20 = NULL;
    m_pCabItem40 = NULL;
    m_pCabItem60 = NULL;

    TitleBar* pTitleBar = new TitleBar(CALIBRATE_VIEW_TRANSLATE(Calibrate::TEXT_CALIBRATE_TEST));
    pTitleBar->setFixedSize(Window::WIDTH, TITLE_HEIGHT);
    connect(pTitleBar, SIGNAL(sigClicked()), this, SLOT(close()));

    m_plblCurVal = new QLabel();
    m_plblCurVal->setStyleSheet(LABEL_STYLE);
    m_plblCurVal->setAlignment(Qt::AlignVCenter);
    m_plblCurVal->setText("0 dB");

    m_pCabItem20 = new CalibrateTestWidget(Window::WIDTH, CALIBITEM_HEIGHT);
#ifdef VERSION_TEV_CALIBRATION
    m_pCabItem20->setCalibrateGearInfo(CALIBRATE_VIEW_TRANSLATE(Calibrate::TEXT_FIRST_REFERENCE_ARRAYS));
#else
    m_pCabItem20->setCalibrateGearInfo(Calibrate::TEXT_0_20DB);
#endif

    m_pCabItem40 = new CalibrateTestWidget(Window::WIDTH, CALIBITEM_HEIGHT);
#ifdef VERSION_TEV_CALIBRATION
    m_pCabItem40->setCalibrateGearInfo(CALIBRATE_VIEW_TRANSLATE(Calibrate::TEXT_SECOND_REFERENCE_ARRAYS));
#else
    m_pCabItem40->setCalibrateGearInfo(Calibrate::TEXT_20_40DB);
#endif

    m_pCabItem60 = new CalibrateTestWidget(Window::WIDTH, CALIBITEM_HEIGHT);
#ifdef VERSION_TEV_CALIBRATION
    m_pCabItem60->setCalibrateGearInfo(CALIBRATE_VIEW_TRANSLATE(Calibrate::TEXT_THIRD_REFERENCE_ARRAYS));
#else
    m_pCabItem60->setCalibrateGearInfo(Calibrate::TEXT_40_60DB);
#endif

    QVBoxLayout* pMainLayout = new QVBoxLayout(this);
    pMainLayout->setMargin(0);
    pMainLayout->setSpacing(0);

    pMainLayout->addWidget(pTitleBar, 0, Qt::AlignTop);
    pMainLayout->addSpacing(10);
    pMainLayout->addWidget(m_plblCurVal, 0, Qt::AlignHCenter);
    pMainLayout->addSpacing(5);
    pMainLayout->addWidget(m_pCabItem20);
    pMainLayout->addSpacing(5);
    pMainLayout->addWidget(m_pCabItem40);
    pMainLayout->addSpacing(5);
    pMainLayout->addWidget(m_pCabItem60);
    pMainLayout->addStretch();

    setLayout(pMainLayout);

    return;
}

void TEVCalibrateTestView::keyPressEvent(QKeyEvent* event)
{
    if(event->key() == Qt::Key_Escape)
    {
        close();
    }

    return;
}

void TEVCalibrateTestView::closeEvent(QCloseEvent *event)
{
    Q_UNUSED(event);
    disconnect(TEVCalibrateTestService::instace(), SIGNAL(sigTestRet(TEVTestSampleData)), this, SLOT(onTestRet(const TEVTestSampleData &)));
    TEVCalibrateTestService::instace()->stop();
    return;
}

void TEVCalibrateTestView::showEvent(QShowEvent *event)
{
    Q_UNUSED(event);
    connect(TEVCalibrateTestService::instace(), SIGNAL(sigTestRet(TEVTestSampleData)), this, SLOT(onTestRet(const TEVTestSampleData &)));
    TEVCalibrateTestService::instace()->start();
    return;
}

void TEVCalibrateTestView::onTestRet(const TEVTestSampleData &stSampleData)
{
    if(m_plblCurVal)
    {
        m_plblCurVal->setText(QString("%1 dB").arg(stSampleData.qui8DBVal));
    }

    if(m_pCabItem20)
    {
        m_pCabItem20->setCurrentTestInfo(stSampleData.aui16DACVal[TEV_CHANNEL_0dB]);
        m_pCabItem20->setDACRefVals(stSampleData.aui16DACRef[TEV_CHANNEL_0dB]);
    }

    if(m_pCabItem40)
    {
        m_pCabItem40->setCurrentTestInfo(stSampleData.aui16DACVal[TEV_CHANNEL_20dB]);
        m_pCabItem40->setDACRefVals(stSampleData.aui16DACRef[TEV_CHANNEL_20dB]);
    }

    if(m_pCabItem60)
    {
        m_pCabItem60->setCurrentTestInfo(stSampleData.aui16DACVal[TEV_CHANNEL_40dB]);
        m_pCabItem60->setDACRefVals(stSampleData.aui16DACRef[TEV_CHANNEL_40dB]);
    }

    return;
}

