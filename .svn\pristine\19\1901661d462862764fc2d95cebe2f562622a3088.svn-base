/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* UHFSampleStrategy.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年2月7日
* 摘要：UHF服务模块数据和采样管理模块的基类

* 当前版本：1.0
*/

#ifndef TEVSAMPLESTRATEGY_H
#define TEVSAMPLESTRATEGY_H

#include <QObject>
#include "tevdefine.h"
#include "datadefine.h"

#include <UHFHFCTAETEVApi.h>

#include "Module.h"

class TEVSampleStrategy : public QObject
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    *****************************/
    explicit TEVSampleStrategy(QObject *parent = 0);

    /*************************************************
    功能： 开始采集
    *************************************************/
    void startSample();

    /*************************************************
    功能： 停止采集
    *************************************************/
    void stopSample();
protected:
    /*************************************************
    入参：iInterval -- 采样间隔
    功能： 设置采样间隔
    *************************************************/
    void setSampleInterval( int iInterval );

    /*************************************************
    返回值：int -- 定时器id
    功能： 获得定时器id
    *************************************************/
    int timerId( void );

    /*************************************************
    功能： 开启取数据定时器
    *************************************************/
    void startSampleTimer( void );

    /*************************************************
    功能： 关闭取数据定时器
    *************************************************/
    void stopSampleTimer( void );
signals:
    /*************************************************
    功能： 同步状态变化
    *************************************************/
    void sigSyncStateChanged( Module::SyncState eSyncState );

    /*************************************************
    功能： 信号  数据信号状态发生变化
    *************************************************/
    void sigSignalChanged( Module::SignalState eSignalState );
private:
    int m_iTimerId;
    int m_iSampleInterval;
};

#endif // TEVSAMPLESTRATEGY_H
