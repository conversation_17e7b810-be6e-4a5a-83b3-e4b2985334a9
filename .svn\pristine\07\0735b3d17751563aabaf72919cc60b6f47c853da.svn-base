﻿#include "taskfileio.h"
#include <QFileInfo>
#include <QDateTime>
#include <QObject>
#include <QDebug>
#ifdef Q_OS_LINUX
#include <sys/vfs.h>
#include <unistd.h>
#endif
#include "datafile.h"
#include "ae/aeampdatamap.h"
#include "datafile/mapdatafactory.h"
#include "dataSave/DataFileInfos.h"
#include "fileoper/fileoperutil.h"
#include "timezonemanager/timezonemanager.h"
#include "global_def.h"
#include "log/log.h"


/* root */
const QString ROOT_NODE = "description";                  //根节点

/* 任务节点相关节点名常量定义 */
const QString TASK_NODE = "taskInfo";                     //任务子节点
const QString TASK_NAME = "name";                         //任务名
const QString TASK_TEST_NUMBER = "testNumber";            //测试任务编号
const QString TEST_SOURCE_PLATFORM = "testSource";        //任务创建平台
const QString TASK_INNER_ID = "innerId";                  //内部ID
const QString STATION_NAME = "substationName";            //站点名
const QString STATION_NUMBER = "substationId";            //站点编号
const QString TASK_TESTED_DATA = "testedTestData";        //任务已测数据
const QString TASK_TOTAL_DATA = "totalTestData";          //任务测试项总数
const QString TASK_CREATE_TIME = "createTime";            //任务创建时间
const QString TASK_PLAN_TIME = "planTime";                //计划时间
const QString TASK_TEST_TIME = "testTime";                //任务测试时间
const QString TASK_VOLTAGE = "voltage";                   //电压
const QString TASK_VOLTAGE_UNIT = "voltageUnit";                 //电压单位
const QString TASK_BAY_COUNT = "bayCount";                   //测试间隔数量
const QString TASK_WEATHER = "weather";                   //天气
const QString TASK_TEMPERATURE = "temperature";                   //温度
const QString TASK_TEMPERATURE_UNIT = "temperatureUnit";                   //温度单位
const QString TASK_HUMIDITY = "humidity";                   //湿度
const QString TASK_MAIN_IMAGE_FILE_PATH = "mainImgFilePath";                   //测试任务主照片
const QString TASK_MEDIA_FILE_PATH = "mediaFilePath";                   //音频文件
const QString TASK_IMAGE_FILE_PATH = "imgFilePath";                   //图片文件
const QString TASK_REMOTE_MAIN_IMAGE_FILE_PATH = "remoteMainImgFilePath";                   //测试任务主照片
const QString TASK_REMOTE_MEDIA_FILE_PATH = "remoteMediaFilePath";                   //音频文件
const QString TASK_REMOTE_IMAGE_FILE_PATH = "remoteImgFilePath";                   //图片文件

/* 测试间隔相关节点名常量定义 */
const QString BAY_NODE = "bay";                           //间隔节点
const QString BAY_SOURCE_ID = "SourceId";                 //间隔标识
const QString BAY_NUMBER = "number";                 //间隔编号
const QString BAY_DEVICE_TYPE = "deviceType";                 //该间隔分组所属的设备类型
const QString BAY_VOLTAGE = "voltage";                 //电压
const QString BAY_VOLTAGE_UNIT = "voltageUnit";                 //电压单位
const QString BAY_INDEX = "index";                 //间隔序号
const QString BAY_IS_DEFAULT = "isDefault";                 //是否是默认间隔
const QString BAY_NAME = "name";                 //间隔分组的名字
const QString BAY_DEVICE_COUNT = "deviceCount";                 //测试设备数量
const QString BAY_BGN = "isBgn";                 //当前间隔是否为背景值
const QString BAY_MAIN_IMAGE_FILE_PATH = "mainImgFilePath";                 //间隔主照片
const QString BAY_MEDIA_FILE_PATH = "mediaFilePath";                 //间隔的音频文件
const QString BAY_IMAGE_FILE_PATH = "imgFilePath";                 //间隔的图片
const QString BAY_REMOTE_MAIN_IMAGE_FILE_PATH = "remoteMainImgFilePath";                 //间隔主照片
const QString BAY_REMOTE_MEDIA_FILE_PATH = "remoteMediaFilePath";                 //间隔的音频文件
const QString BAY_REMOTE_IMAGE_FILE_PATH = "remoteImgFilePath";                 //间隔的图片

/* 设备相关节点名常量定义 */
const QString DEVICE_INDEX = "index";                       //设备名称
const QString DEVICE_NODE = "device";                     //设备节点
const QString DEVICE_NAME = "name";                       //设备名称
const QString DEVICE_NUMBER = "number";                   //设备编号
const QString DEVICE_SOURCE_ID = "SourceId";              //设备标识
const QString DEVICE_PATROL_TYPE = "type";                //巡检类型
const QString DEVICE_GROUP = "Group";                //组名
const QString DEVICE_VOLTAGE = "voltage";                //电压
const QString DEVICE_VOLTAGE_UNIT = "voltageUnit";                 //电压单位
const QString DEVICE_LOAD_CURRENT = "loadCurrent";      //负荷电流
const QString DEVICE_LOAD_CURRENT_UNIT = "loadCurrentUnit";     //负荷电流单位
const QString DEVICE_LOAD_CURRENT_SIGN = "loadCurrentSign";     //负荷电流填写标志
const QString DEVICE_MODEL = "model";                //设备型号
const QString DEVICE_MANUFACTURER = "manufacturer";                //生产厂家
const QString DEVICE_PRODUCE_DATE = "dateOfProduction";                //出厂日期
const QString DEVICE_TEST_POINT_COUNT = "testPointCount";
const QString DEVICE_TEST_POINT_FILTER_SIGN = "testPointFilterSign";
const QString DEVICE_BGN = "isBgn";//是否背景测试
const QString DEVICE_MAIN_IMAGE_FILE_PATH = "mainImgFilePath";
const QString DEVICE_MEDIA_FILE_PATH = "mediaFilePath";
const QString DEVICE_IMAGE_FILE_PATH = "imgFilePath";
const QString DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH = "remoteMainImgFilePath";
const QString DEVICE_REMOTE_MEDIA_FILE_PATH = "remoteMediaFilePath";
const QString DEVICE_REMOTE_IMAGE_FILE_PATH = "remoteImgFilePath";

/* 测点相关节点名常量定义 */
const QString DEVICE_TEST_POINT = "testPoint";            //测点
const QString TEST_POINT_NUMBER = "number";               //测点编号 TODO 暂时找个不同的标识代替
const QString TEST_POINT_NAME = "name";                   //设备名称
const QString TEST_POINT_IS_TEST = "isTest";              //是否已测
const QString TEST_POINT_DATA_COUNT = "testDataCount";    //测试项数量
const QString TEST_POINT_TYPE = "type";                   //测点类型
const QString TEST_POINT_MAIN_IMAGE_FILE_PATH = "mainImgFilePath";
const QString TEST_POINT_MEDIA_FILE_PATH = "mediaFilePath";
const QString TEST_POINT_IMAGE_FILE_PATH = "imgFilePath";
const QString TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH = "remoteMainImgFilePath";
const QString TEST_POINT_REMOTE_MEDIA_FILE_PATH = "remoteMediaFilePath";
const QString TEST_POINT_REMOTE_IMAGE_FILE_PATH = "remoteImgFilePath";
const QString TEST_POINT_POSITION = "position";                     //用于识别开关柜的几个固定测试部位
const QString TEST_POINT_INDEX = "index";


/* 测试数据相关节点名常量定义 */
const QString TEST_DATA = "testData";                    //测试数据
const QString TEST_DATA_DATA_INDEX = "dataIndex";        //测试数据索引
const QString TEST_DATA_NUMBER = "number";        //测点数据编码
const QString TEST_DATA_INDEX = "index";                 //测点序号
const QString TEST_SOURCE_ID = "sourceId";               //测点数据ID
const QString TEST_DATA_IS_TEST = "isTest";              //是否已测
const QString TEST_DATA_FILE_NAME = "fileName";          //测试数据文件名
const QString TEST_DATA_FILE_PATH = "filePath";          //测试数据文件完整路径
const QString TEST_DATA_ATTACH_PATH = "attachmentPath";  //附件完整路径
const QString TEST_DATA_REMOTE_PATH = "remotePath";      //上传文件服务器路径
const QString TEST_DATA_REMOTE_ATTACH_PATH = "remoteAttachPath";      //上传附件服务器路径
const QString TEST_DATA_TYPE = "type";                   //测试类型
const QString TEST_DATA_BGN = "isBgn";                     //是否背景值
const QString TEST_DATA_BAND_WIDTH = "bandWidth";         //特高频带宽类型
const QString TEST_DATA_DATA_UNIT = "ampUnit";              //AE data amp unit

/* 命名常量定义 */
const QString DEFAULT_EMPTY_STATION_NAME = QObject::trUtf8("Unnamed");

const int ERROR_INDEX = -1;                              //出错序号（默认）
/************************************************
 * 功能: 构造函数
 ************************************************/
TaskFileIO::TaskFileIO()
{
    m_qstrFrontKey = QObject::trUtf8("Front");
    m_qstrSideKey = QObject::trUtf8("Side");
    m_qstrBackKey = QObject::trUtf8("Back");

    MapDataFactory::registerClass<AEAmpDataMap>(XML_FILE_NODE_AE_AMP);
}

void TaskFileIO::printIMPInfo(ImgMediaPath &stFilesInfo)
{
    qDebug() << "TaskFileIO::printIMPInfo, print test begin***************************" << endl;

    qDebug() << stFilesInfo.eRank << ", " << stFilesInfo.strRankNumber << ", " <<  stFilesInfo.iSubCount << endl;
    qDebug() << stFilesInfo.strMainImgPath << endl;

    foreach (QString strInfo, stFilesInfo.listImgPath)
    {
        qDebug() << strInfo << endl;
    }

    foreach (QString strInfo, stFilesInfo.listMediaPath)
    {
        qDebug() << strInfo << endl;
    }

    foreach (ImgMediaPath stTemp, stFilesInfo.vtSubLevel)
    {
        printIMPInfo(stTemp);
    }

    qDebug() << "TaskFileIO::printIMPInfo, print test end***************************" << endl;

    return;
}

//测试用 打印任务概要信息外的其他信息
void TaskFileIO::printTaskData(QVector<ItemPatrolType> &vPatrolTypes)
{
    //dbg_info("vPatrolTypes.size is %d\n", vPatrolTypes.size());
    for(int i = 0; i < vPatrolTypes.size(); i ++)
    {
        ItemPatrolType stPatrolType = vPatrolTypes.at(i);
        //dbg_info("stPatrolType(%d).testTypes.size is %d\n", i, stPatrolType.testTypes.size());

        for(int j = 0; j < stPatrolType.testTypes.size(); j ++)
        {
            ItemTestType stTestType = stPatrolType.testTypes.at(j);
            //qDebug()<<"TaskFileIO::printTaskData, stTestType, name:"<<stTestType.strItemName;

            //dbg_info("stPatrolType(%d).testTypes(%d).bays.size is %d\n", i, j, stTestType.bays.size());

            for(int k = 0; k < stTestType.bays.size(); k ++)
            {
                ItemBay stBay = stTestType.bays.at(k);
                printBayInfo(stBay);
                //dbg_info("stPatrolType(%d).testTypes(%d).bays(%d).vecDevice size is %d\n", i, j, k, stBay.vecDevice.size());
                for(int g = 0; g < stBay.vecDevice.size(); g ++)
                {
                    printDeviceInfoInBay(stBay.vecDevice.at(g));
                }
            }

            dbg_info("stPatrolType(%d).testTypes(%d).vecDevice size is %d\n", i, j, stTestType.devices.size());
            for(int m = 0; m < stTestType.devices.size(); m ++)
            {
                ItemDevice stDevice = stTestType.devices.at(m);
                printDeviceInfoInTestType(stDevice);

                dbg_info("stPatrolType(%d).testTypes(%d).vecDevice(%d), testPoints size is %d\n", i, j, m, stDevice.testPoints.size());
                for(int p = 0; p < stDevice.testPoints.size(); p ++)
                {
                    ItemTestPoint stTestPoint = stDevice.testPoints.at(p);
                    printTestPointInfo(stTestPoint);
                    //dbg_info("stPatrolType(%d).testTypes(%d).vecDevice(%d), testPoints(%d), testDatas,size is %d\n", i, j, m, p, stTestPoint.testDatas.size());
                    for(int n = 0; n < stTestPoint.testDatas.size(); n ++)
                    {
                        ItemTestData stTestData = stTestPoint.testDatas.at(n);
                        //printTestDataInfo(stTestData);
                    }
                }
            }
        }
    }
}

/****************************
功能： 打印间隔里所有信息
*****************************/
void TaskFileIO::printBayInfo(ItemBay &stBay)
{
    //QVector<ItemDeviceTaskFileInfo> vecDevice;//设备节点


    qDebug()<<"TaskFileIO::printBayInfo, stBay.strNumber"<<stBay.strNumber;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.eType"<<stBay.eType;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.dVoltage"<<stBay.dVoltage;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.eVoltageUnit"<<stBay.eVoltageUnit;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.usIndex"<<stBay.usIndex;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.isDefault"<<stBay.isDefault;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.strName"<<stBay.strName;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.usDeviceCount"<<stBay.usDeviceCount;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.isBGN"<<stBay.isBGN;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.strMainImgFilePath"<<stBay.strMainImgFilePath;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.strRemoteMainImgFilePath"<<stBay.strRemoteMainImgFilePath;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.lMediaFilePath"<<stBay.lMediaFilePath;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.lRemoteMediaFilePath"<<stBay.lRemoteMediaFilePath;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.lImageFilePath"<<stBay.lImageFilePath;
    qDebug()<<"TaskFileIO::printBayInfo, stBay.lRemoteImageFilePath"<<stBay.lRemoteImageFilePath;
}

/************************************************
 * 功能: 测试用 修改任务概要信息
 ************************************************/
void TaskFileIO::changeTaskInfo(TaskInfo &sTaskInfo)
{
    sTaskInfo.strMainImgFilePath = "taskinfomainimagepath123";
    sTaskInfo.strRemoteMainImgFilePath = "taskinforemotemainimagepath123";
    sTaskInfo.lMediaFilePath.clear();
    sTaskInfo.lMediaFilePath<<"taskinfolMediaFilePath"<<"taskinfolMediaFilePath1";
    sTaskInfo.lRemoteMediaFilePath.clear();
    sTaskInfo.lRemoteMediaFilePath<<"taskinfolRemoteMediaFilePath"<<"taskinfolRemoteMediaFilePath1";
    sTaskInfo.lImageFilePath.clear();
    sTaskInfo.lImageFilePath<<"taskinfolImageFilePath"<<"taskinfolImageFilePath1";
    sTaskInfo.lRemoteImageFilePath.clear();
    sTaskInfo.lRemoteImageFilePath<<"taskinfolRemoteImageFilePath"<<"taskinfolRemoteImageFilePath1";
    sTaskInfo.strFilePath = "/media/data/SavedData/201805231040.t13";
    sTaskInfo.strFileURL = "strFileURL";
    sTaskInfo.isDownloaded = true;
}

/************************************************
 * 功能: 测试用 修改概要信息外的其他信息
 ************************************************/
void TaskFileIO::changePatrolTypeData(QVector<ItemPatrolType> &vPatrolTypes)
{
    for(int i = 0, iPatrolSize = vPatrolTypes.size(); i < iPatrolSize; ++i)
    {
        ItemPatrolType *pstPatrolType = &vPatrolTypes[i];
        for(int j = 0, iTestTypeSize = pstPatrolType->testTypes.size(); j < iTestTypeSize; ++j)
        {
            ItemTestType *pstTestType = &pstPatrolType->testTypes[j];
            for(int k = 0, iBaySize = pstTestType->bays.size(); k < iBaySize; ++k)
            {
                ItemBay *pstBay = &pstTestType->bays[k];
                pstBay->strMainImgFilePath = "baymainimage12345";
                pstBay->strRemoteMainImgFilePath = "bayremotemainimage12345";
                pstBay->lRemoteMediaFilePath.clear();
                pstBay->lRemoteMediaFilePath<<"bayremotemedia"<<"bayremotemedia1";
                pstBay->lRemoteImageFilePath.clear();
                pstBay->lRemoteImageFilePath<<"bayremoteimage"<<"bayremoteimage1";
            }

            //test point
            for(int n = 0, iDevSize = pstTestType->devices.size(); n < iDevSize; ++n)
            {
                ItemDevice *pstDevice = &pstTestType->devices[n];
                pstDevice->strMainImgFilePath = "deviceintetstypemainimage12345";
                pstDevice->strRemoteMainImgFilePath = "deviceintetstyperemotemainimage12345";
                pstDevice->lRemoteMediaFilePath.clear();
                pstDevice->lRemoteMediaFilePath<<"deviceintetstyperemotemedia"<<"deviceintetstyperemotemedia1";
                pstDevice->lRemoteImageFilePath.clear();
                pstDevice->lRemoteImageFilePath<<"deviceintetstyperemoteimage"<<"deviceintetstyperemoteimage1";

                for(int p = 0, iTestPointSize = pstDevice->testPoints.size(); p < iTestPointSize; ++p)
                {
                    ItemTestPoint *pstTestPoint = &pstDevice->testPoints[p];
                    pstTestPoint->bTested = true;
                    pstTestPoint->strMainImgFilePath = "testpointmainimage12345";
                    pstTestPoint->strRemoteMainImgFilePath = "testpointremotemainimage12345";
                    pstTestPoint->lRemoteMediaFilePath.clear();
                    pstTestPoint->lRemoteMediaFilePath<<"testpointremotemedia"<<"testpointremotemedia1";
                    pstTestPoint->lRemoteImageFilePath.clear();
                    pstTestPoint->lRemoteImageFilePath<<"testpointremoteimage"<<"testpointremoteimage1";

                    for(int w = 0, iTestDataSize = pstTestPoint->testDatas.size(); w < iTestDataSize; ++w)
                    {
                        ItemTestData *pstTestData = &pstTestPoint->testDatas[w];
                        pstTestData->bTested = true;
                        pstTestData->strFilePath = "testdataFilePath";
                        pstTestData->strRemotePath = "testdataremoteFilePath";
                    }
                }
            }
        }
    }
    return;
}

/************************************************
 * 功能: 读取任务文件里间隔/设备/测点/测试项各层级信息(不含任务概要信息)
 * 入参：strFilePath -- 文件路径
 * 出参：vPatrolTypes -- 巡检类型集合
 * 返回值：I/O错误类型
 * added by zhaoyongjun, 2018.5.16
 ************************************************/
TaskFileIO::IOStateType TaskFileIO::readTaskFile( TaskInfo &sTaskInfo, QVector<ItemPatrolType> &vPatrolTypes, const QString &strFilePath )
{
    IOStateType eType = checkFileValidity( strFilePath );
    if( OPERATE_SUCCESS == eType ) // 文件合法性校验
    {
        XMLDocument doc(strFilePath, QIODevice::ReadWrite/*QIODevice::ReadOnly*/, ROOT_NODE);
        if( doc.isValid() )
        {
            readTaskInfo( sTaskInfo, doc, strFilePath );               // 添加任务概要信息
            //printTaskInfo(sTaskInfo);

            bool bChanged = false;
            // 解析间隔
            QList<QDomElement> lBayElement = doc.childElement( BAY_NODE );
            for (QList<QDomElement>::iterator iterBay = lBayElement.begin();
                 iterBay != lBayElement.end(); ++iterBay)
            {
                doc.beginElement(*iterBay);
                //读取任务文件里间隔节点的一些信息
                ItemBay stBay;
                stBay.strNumber = doc.value( BAY_NUMBER );
                stBay.eType = (PDAServiceNS::PatrolType) doc.value( BAY_DEVICE_TYPE ).toUShort();
                stBay.dVoltage =  doc.value( BAY_VOLTAGE ).toDouble();
                stBay.eVoltageUnit =  (PDAServiceNS::TaskUnitCode) doc.value( BAY_VOLTAGE_UNIT ).toUShort();
                stBay.usIndex =  doc.value( BAY_INDEX ).toUShort();
                stBay.isDefault = doc.value( BAY_IS_DEFAULT ).toUShort();
                stBay.strName = doc.value( BAY_NAME );
                stBay.usDeviceCount = doc.value( BAY_DEVICE_COUNT ).toUShort();
                stBay.isBGN = doc.value( BAY_BGN ).toUShort();

                //读取任务文件里间隔节点的图片，音频文件路径
                stBay.strMainImgFilePath = doc.value( BAY_MAIN_IMAGE_FILE_PATH );

                //remote main image file path
                if(doc.hasElement(BAY_REMOTE_MAIN_IMAGE_FILE_PATH))
                {
                    stBay.strRemoteMainImgFilePath = doc.value( BAY_REMOTE_MAIN_IMAGE_FILE_PATH );
                }

                //mediaFilePath
                QList<QDomElement> qlMediaFilePath = doc.childElement(BAY_MEDIA_FILE_PATH);
                for(QList<QDomElement>::iterator iter = qlMediaFilePath.begin();
                    iter != qlMediaFilePath.end(); ++iter)
                {
                    doc.beginElement(*iter);
                    if(!(stBay.lMediaFilePath.contains(doc.text())))
                    {
                        stBay.lMediaFilePath.append(doc.text());
                    }
                    doc.endElement();
                }

                //remote mediaFilePath
                QList<QDomElement> qlRemoteMediaFilePath = doc.childElement(BAY_REMOTE_MAIN_IMAGE_FILE_PATH);
                for(QList<QDomElement>::iterator iter = qlRemoteMediaFilePath.begin();
                    iter != qlRemoteMediaFilePath.end(); ++iter)
                {
                    doc.beginElement(*iter);
                    if(!(stBay.lRemoteMediaFilePath.contains(doc.text())))
                    {
                        stBay.lRemoteMediaFilePath.append(doc.text());
                    }
                    doc.endElement();
                }

                //imgFilePath
                QList<QDomElement> qlImageFilePath = doc.childElement(BAY_IMAGE_FILE_PATH);
                for(QList<QDomElement>::iterator iter = qlImageFilePath.begin();
                    iter != qlImageFilePath.end(); ++iter)
                {
                    doc.beginElement(*iter);
                    if(!(stBay.lImageFilePath.contains(doc.text())))
                    {
                        stBay.lImageFilePath.append(doc.text());
                    }
                    doc.endElement();
                }

                //remote imgFilePath
                QList<QDomElement> qlRemoteImageFilePath = doc.childElement(BAY_REMOTE_IMAGE_FILE_PATH);
                for(QList<QDomElement>::iterator iter = qlRemoteImageFilePath.begin();
                    iter != qlRemoteImageFilePath.end(); ++iter)
                {
                    doc.beginElement(*iter);
                    if(!(stBay.lRemoteImageFilePath.contains(doc.text())))
                    {
                        stBay.lRemoteImageFilePath.append(doc.text());
                    }
                    doc.endElement();
                }

                //printBayInfo(stBay);

                //读取任务文件里设备节点的一些信息
                QList<QDomElement> lDeviceElement = doc.childElement( DEVICE_NODE );
                for (QList<QDomElement>::iterator iterDev = lDeviceElement.begin();
                     iterDev != lDeviceElement.end(); ++iterDev)
                {
                    doc.beginElement(*iterDev);

                    taskfileioNS::device_info stDev;
                    stDev.usIndex = doc.value( DEVICE_INDEX ).toUShort();   // 设备名
                    stDev.b_isBGNTest = doc.value( DEVICE_BGN ).toInt();     //是否背景测试
                    QString strDevRealName = doc.value( DEVICE_NAME );
                    QString strDevRealNum = doc.value( DEVICE_NUMBER );

                    //GIS类型的间隔，设备名,设备编号存间隔的名称和编号
                    if(stBay.eType == PDAServiceNS::COMBINED_ELECTRIC)
                    {
                        stDev.strBayDevName = stBay.strName;            //间隔名称
                        stDev.strBayDevNum = stBay.strNumber;           //间隔编号
                        stDev.strDevName = strDevRealName;      //设备真实的名称
                        stDev.strDevNum = strDevRealNum;     //设备真实的编号
                    }
                    else
                    {
                        stDev.strDevName = stDev.strBayDevName = strDevRealName;     //设备真实的名称
                        stDev.strDevNum = stDev.strBayDevNum = strDevRealNum;     //设备真实的编号
                    }

                    stDev.iDevType = doc.value( DEVICE_PATROL_TYPE ).toInt();     // 设备类型
                    stDev.strType = stringFromPatrolType(static_cast<PDAServiceNS::PatrolType>(stDev.iDevType));
                    stDev.eType = stBay.eType;

                    stDev.dVoltage =  doc.value( DEVICE_VOLTAGE ).toDouble();
                    stDev.eVoltageUnit =  (PDAServiceNS::TaskUnitCode) doc.value( DEVICE_VOLTAGE_UNIT ).toUShort();

                    stDev.dLoadCurrent = doc.value(DEVICE_LOAD_CURRENT).toDouble();
                    stDev.eLoadCurrentUnit = (PDAServiceNS::DataUnit)(doc.value(DEVICE_LOAD_CURRENT_UNIT).toUShort());
                    stDev.dwLoadCurrentSign = doc.value(DEVICE_LOAD_CURRENT_SIGN).toUInt();

                    stDev.strDevModel = doc.value( DEVICE_MODEL );     // 设备型号
                    stDev.strManufacturer = doc.value( DEVICE_MANUFACTURER );     // 生产厂家
                    //stDev.strProduceDate = doc.value( DEVICE_PRODUCE_DATE );     // 出厂日期
                    qint64 lUtcTime = doc.value(DEVICE_PRODUCE_DATE).toLongLong();       //UTC时间(s)，实际app使用的ms，因为版本已经发布，因而适配为ms
                    lUtcTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(lUtcTime);
                    QDateTime stFmtTime = QDateTime::fromTime_t((uint)(lUtcTime / 1000));           //计算值单位为s计算
                    stDev.strProduceDate = stFmtTime.toString(DEVICE_TIME_FORMAT);     // 出厂日期

                    stDev.usTestPointCount = doc.value( DEVICE_TEST_POINT_COUNT ).toUShort();
                    stDev.eFilterSign = (PDAServiceNS::TestPointFilterSign) doc.value( DEVICE_TEST_POINT_FILTER_SIGN ).toUShort();

                    //读取任务文件里设备节点的图片，音频文件路径
                    stDev.strMainImgFilePath = doc.value( DEVICE_MAIN_IMAGE_FILE_PATH );
                    //remote main image file path
                    if(doc.hasElement(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH))
                    {
                        stDev.strRemoteMainImgFilePath = doc.value( DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH );
                    }

                    //读取任务文件里设备节点的media file path
                    QList<QDomElement> qlMediaFilePath = doc.childElement(DEVICE_MEDIA_FILE_PATH);
                    for (QList<QDomElement>::iterator iter = qlMediaFilePath.begin();
                         iter != qlMediaFilePath.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        if(!(stDev.lMediaFilePath.contains(doc.text())))
                        {
                            stDev.lMediaFilePath.append(doc.text());
                        }
                        doc.endElement();
                    }

                    //remote media file path
                    QList<QDomElement> qlRemoteMediaFilePath = doc.childElement(DEVICE_REMOTE_MEDIA_FILE_PATH);
                    for (QList<QDomElement>::iterator iter = qlRemoteMediaFilePath.begin();
                         iter != qlRemoteMediaFilePath.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        if(!(stDev.lRemoteMediaFilePath.contains(doc.text())))
                        {
                            stDev.lRemoteMediaFilePath.append(doc.text());
                        }
                        doc.endElement();
                    }

                    //读取任务文件里设备节点的image file path
                    QList<QDomElement> qlImageFilePath = doc.childElement(DEVICE_IMAGE_FILE_PATH);
                    for (QList<QDomElement>::iterator iter = qlImageFilePath.begin();
                         iter != qlImageFilePath.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        if(!(stDev.lImageFilePath.contains(doc.text())))
                        {
                            stDev.lImageFilePath.append(doc.text());
                        }
                        doc.endElement();
                    }

                    //remote image file path
                    QList<QDomElement> qlRemoteImageFilePath = doc.childElement(DEVICE_REMOTE_IMAGE_FILE_PATH);
                    for (QList<QDomElement>::iterator iter = qlRemoteImageFilePath.begin();
                         iter != qlRemoteImageFilePath.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        if(!(stDev.lRemoteImageFilePath.contains(doc.text())))
                        {
                            stDev.lRemoteImageFilePath.append(doc.text());
                        }
                        doc.endElement();
                    }

                    //printDeviceInfo(stDev);

                    //获取巡检类型的枚举
                    PDAServiceNS::PatrolType ePatrolType = stBay.eType;

                    //根据巡检类型枚举获取相应巡检类型对象
                    ItemPatrolType* pItemPatrolType = itemPatrolType( ePatrolType, vPatrolTypes );

                    // 将测点根据测点类型（测试类型+电压等级）进行分类，增添进testPointList
                    QList<ItemTestPoint> testPointList;
                    testPointList.clear();
                    QList<QDomElement> lTestPointElement = doc.childElement( DEVICE_TEST_POINT );

                    if(classifyTestPoint(testPointList, lTestPointElement, doc, stDev))
                    {
                        bChanged = true;
                    }

                    // 将根据测点类型（测试类型+电压等级）映射出的不同设备添加到相应巡检类型中去
                    addBayDevicesToPatrolType(testPointList, stBay, stDev, pItemPatrolType, strDevRealName, strDevRealNum);

                    doc.endElement();
                }
                doc.endElement();
            }

            //xml有变化，需保存修改
            if(bChanged)
            {
                doc.save();
            }

            setTestIndexofAllPatrolTypes(vPatrolTypes);
        }
        else
        {
            eType = FILE_TYPE_NOT_VALID;
            logError("open xml doc failed.");
        }
    }
    else
    {
        eType = FILE_NOT_EXIST;
        logError("file is invalid.");
    }

    return eType;
}

/****************************
功能： 打印device信息
*****************************/
void TaskFileIO::printDeviceInfoInBay(const ItemDeviceTaskFileInfo &stDev)
{
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.strDevName:"<<stDev.strItemName;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.strDevNum:"<<stDev.strDevNum;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.strSourceId:"<<stDev.strSourceId;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.strType:"<<stDev.strType;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.ucGroupName:"<<stDev.ucGroupName;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.dVoltage:"<<stDev.dVoltage;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.eVoltageUnit:"<<stDev.eVoltageUnit;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.strDevModel:"<<stDev.strDevModel;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.strManufacturer:"<<stDev.strManufacturer;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.strProduceDate:"<<stDev.strProduceDate;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.b_isBGNTest:"<<stDev.b_isBGNTest;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.usIndex:"<<stDev.usIndex;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.eFilterSign:"<<stDev.eFilterSign;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.strMainImgFilePath:"<<stDev.strMainImgFilePath;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.lMediaFilePath:"<<stDev.lMediaFilePath;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.lImageFilePath:"<<stDev.lImageFilePath;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.strRemoteMainImgFilePath:"<<stDev.strRemoteMainImgFilePath;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.lRemoteMediaFilePath:"<<stDev.lRemoteMediaFilePath;
    qDebug()<<"TaskFileIO::printDeviceInfoInBay, stDev.lRemoteImageFilePath:"<<stDev.lRemoteImageFilePath;
}

/****************************
功能： 打印device信息
*****************************/
void TaskFileIO::printDeviceInfoInTestType(ItemDevice &stDev)
{
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.strDevName:"<<stDev.strItemName;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.strDevNum:"<<stDev.strDevNum;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.strSourceId:"<<stDev.strSourceId;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.strType:"<<stDev.strType;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.ucGroupName:"<<stDev.ucGroupName;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.dVoltage:"<<stDev.dVoltage;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.eVoltageUnit:"<<stDev.eVoltageUnit;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.strDevModel:"<<stDev.strDevModel;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.strManufacturer:"<<stDev.strManufacturer;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.strProduceDate:"<<stDev.strProduceDate;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.b_isBGNTest:"<<stDev.b_isBGNTest;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.usIndex:"<<stDev.usIndex;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.eFilterSign:"<<stDev.eFilterSign;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.strMainImgFilePath:"<<stDev.strMainImgFilePath;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.lMediaFilePath:"<<stDev.lMediaFilePath;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.lImageFilePath:"<<stDev.lImageFilePath;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.strRemoteMainImgFilePath:"<<stDev.strRemoteMainImgFilePath;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.lRemoteMediaFilePath:"<<stDev.lRemoteMediaFilePath;
    qDebug()<<"TaskFileIO::printDeviceInfoInTestType, stDev.lRemoteImageFilePath:"<<stDev.lRemoteImageFilePath;
}

/****************************
输入参数: start-- ae/tev/uhf/hfct/infrared下switch type起点
        end-- ae/tev/uhf/hfct/infrared下switch type终点
        devices---设备对象
输出参数:uiTestIndex---testIndex
功能： 设置开关柜巡检下非背景设备各测点的testindex
*****************************/
void TaskFileIO::setNonBGNOfCabinetDeviceTestIndex(PDAServiceNS::AutoSwitchType start, PDAServiceNS::AutoSwitchType end, QVector<ItemDevice> &devices, quint32 &uiTestIndex)
{
    //testposition节点为空或0的单独挑出来，等其他测点的testindex设置后，再设置这些testpoint
    QVector<ItemTestPoint*> vecAutoJumpTestPoint;
    vecAutoJumpTestPoint.clear();
    for(QVector<ItemDevice>::iterator iterDev = devices.begin(); iterDev != devices.end(); ++iterDev)
    {
        if(iterDev->b_isBGNTest)
        {
            continue;
        }

        QVector<ItemTestPoint>::iterator iterPoint = iterDev->testPoints.begin();
        for(; iterPoint != iterDev->testPoints.end(); ++iterPoint)
        {
            if(iterPoint->eSwitchType == PDAServiceNS::AUTO_BY_INDEX)
            {
                vecAutoJumpTestPoint.append(iterPoint);
            }
        }
    }

    //设置testposition节点不为空或0的testindex
    for(int l = start; l <= end; ++l)
    {
        if(((l - start) % 2) == 0)
        {
            for(int j = 0, iDevSize = devices.size(); j < iDevSize; ++j)
            {
                if(devices.at(j).b_isBGNTest)
                {
                    continue;
                }

                ItemDevice *pstDevice = &devices[j];
                for(int k = 0, iPointSize = pstDevice->testPoints.size(); k < iPointSize; ++k)
                {
                    ItemTestPoint *pstTestPoint = &pstDevice->testPoints[k];
                    if(pstTestPoint->eSwitchType == PDAServiceNS::AUTO_BY_INDEX)
                    {
                        continue;
                    }
                    if(pstTestPoint->eSwitchType == l)
                    {
                        pstTestPoint->uiTestIndex = uiTestIndex++;
                    }
                }
            }
        }
        else
        {
            for(int j = devices.size() - 1; j >= 0; --j)
            {
                ItemDevice *pstDevice = &devices[j];
                if(devices.at(j).b_isBGNTest)
                {
                    continue;
                }
                for(int k = 0, iPointSize = pstDevice->testPoints.size(); k < iPointSize; ++k)
                {
                    ItemTestPoint *pstTestPoint = &(pstDevice->testPoints[k]);
                    if(pstTestPoint->eSwitchType == PDAServiceNS::AUTO_BY_INDEX)
                    {
                        continue;
                    }
                    if(pstTestPoint->eSwitchType == l)
                    {
                        pstTestPoint->uiTestIndex = uiTestIndex++;
                    }
                }
            }
        }
    }

    //set test index for test point, which auto jump by order
    QVector<ItemTestPoint*>::iterator iterAutoJump = vecAutoJumpTestPoint.begin();
    for(; iterAutoJump != vecAutoJumpTestPoint.end(); ++iterAutoJump)
    {
        //iterAutoJump 指针的指针
        (*iterAutoJump)->uiTestIndex = uiTestIndex++;
    }

    return;
}

/****************************
输入参数: devices---设备对象
输出参数:uiTestIndex---testIndex
功能： 设置非开关柜巡检下非背景设备各测点的testindex
*****************************/
void TaskFileIO::setNonBGNOfNonCabinetDeviceTestIndex(QVector<ItemDevice> &devices, quint32 &uiTestIndex)
{
    QVector<ItemDevice>::iterator iter = devices.begin();
    for(; iter != devices.end(); ++iter)
    {
        if(iter->b_isBGNTest)
        {
            continue;
        }

        QVector<ItemTestPoint>::iterator iterPoint = iter->testPoints.begin();
        for(; iterPoint != iter->testPoints.end(); ++iterPoint)
        {
            iterPoint->uiTestIndex = uiTestIndex++;
        }
    }

    return;
}

/****************************
输入参数:pPatrolType -- 巡检对象
功能： 设置开关柜巡检类型各测点的testindex
*****************************/
void TaskFileIO::setCabinetTestIndex(ItemPatrolType *pPatrolType)
{
    quint32 uiTestIndex = 0;
    for(QVector<ItemTestType>::iterator iter = pPatrolType->testTypes.begin(); iter != pPatrolType->testTypes.end(); ++iter)
    {
        //BGN设备测点的testindex先设置
        setBGNDeviceTestIndex(iter->devices, uiTestIndex);

        PDAServiceNS::AutoSwitchType start = PDAServiceNS::UNDEFINE_AUTO;
        PDAServiceNS::AutoSwitchType end = PDAServiceNS::UNDEFINE_AUTO;

        if(iter->eTestPointType == PDAServiceNS::TEV_TYPE)
        {
            start = PDAServiceNS::TEV_FRONT_AUTO;
            end = PDAServiceNS::TEV_SIDE_AUTO;
        }
        else if(iter->eTestPointType == PDAServiceNS::AE_TYPE)
        {
            start = PDAServiceNS::AE_FRONT_AUTO;
            end = PDAServiceNS::AE_BACK_AUTO;
        }
        else if(iter->eTestPointType == PDAServiceNS::UHF_TYPE)
        {
            start = PDAServiceNS::UHF_PRPS_FRONT_AUTO;
            end = PDAServiceNS::UHF_PRPS_BACK_AUTO;
        }
        else if(iter->eTestPointType == PDAServiceNS::INFRARED_TYPE)
        {
            start = PDAServiceNS::INFRARED_FRONT_AUTO;
            end = PDAServiceNS::INFRARED_BACK_AUTO;
        }
        else
        {
            logWarning("Unknown test point type.");
        }

        setNonBGNOfCabinetDeviceTestIndex(start, end, iter->devices, uiTestIndex);
    }

    return;
}

/****************************
输入参数: pstDevice---单个背景设备
输出参数:uiTestIndex---最新的testindex
功能： 设置单个背景设备下所有测点的test index
*****************************/
void TaskFileIO::setBGNDeviceTestPointsTestIndex(ItemDevice *pstDevice, quint32 &uiTestIndex)
{
    for(QVector<ItemTestPoint>::iterator iter = pstDevice->testPoints.begin(); iter != pstDevice->testPoints.end(); ++iter)
    {
        iter->uiTestIndex = uiTestIndex++;
    }

    return;
}

/****************************
输入参数: devices---所有背景设备
输出参数:uiTestIndex---最新的testindex
功能： 设置所有背景设备下所有测点的test index
*****************************/
void TaskFileIO::setBGNDeviceTestIndex(QVector<ItemDevice> &devices, quint32 &uiTestIndex)
{
    for(QVector<ItemDevice>::iterator iter = devices.begin(); iter != devices.end(); ++iter)
    {
        if(iter->b_isBGNTest)
        {
            setBGNDeviceTestPointsTestIndex(iter, uiTestIndex);
        }
    }

    return;
}

/****************************
输入参数:pPatrolType -- 巡检对象
功能： 设置非开关柜巡检类型各测点的testindex
*****************************/
void TaskFileIO::setNonCabinetTestIndex(ItemPatrolType *pPatrolType)
{
    quint32 uiTestIndex = 0;
    QVector<ItemTestType>::iterator iter = pPatrolType->testTypes.begin();
    for(; iter != pPatrolType->testTypes.end(); ++iter)
    {
        //BGN设备测点的testindex先设置
        setBGNDeviceTestIndex(iter->devices, uiTestIndex);
        setNonBGNOfNonCabinetDeviceTestIndex(iter->devices, uiTestIndex);
    }

    return;
}

/****************************
输入参数:pPatrolType -- 所有巡检类型对象
功能： 设置所有巡检类型各测点的testindex
*****************************/
void TaskFileIO::setTestIndexofAllPatrolTypes(QVector<ItemPatrolType> &vPatrolTypes)
{
    for(QVector<ItemPatrolType>::iterator iter = vPatrolTypes.begin(); iter != vPatrolTypes.end(); ++iter)
    {
        if(iter->ePatrolType == PDAServiceNS::SWITCH_CABINET)
        {
            setCabinetTestIndex(iter);
        }
        else
        {
            setNonCabinetTestIndex(iter);
        }
    }

    return;
}

/************************************************
 * 功能: 读任务文件，仅获取概要信息
 * 入参：strFilePath -- 文件路径
 * 出参：sTaskInfo -- 任务概要信息
 * 返回值：I/O错误类型
 ************************************************/
TaskFileIO::IOStateType TaskFileIO::readTaskFile( TaskInfo &sTaskInfo,const QString &strFilePath )
{
    IOStateType eType = checkFileValidity(strFilePath);
    if(OPERATE_SUCCESS == eType)
    {
        XMLDocument doc(strFilePath, QIODevice::ReadWrite/*QIODevice::ReadOnly*/, ROOT_NODE);

        if(doc.isValid())
        {
            readTaskInfo(sTaskInfo, doc, strFilePath);
        }
        else
        {
            logError("open xml doc failed.");
        }
    }
    else
    {
        logError("file is invalid.");
    }

    return eType;
}


/************************************************
 * 功能: 写任务文件
 * 入参：sTaskInfo -- 任务概要信息（包含filePath）
 *      vPatrolTypes -- 巡检类型集合
 * 返回值：I/O错误类型
 * added by zhaoyongjun,2018.5.16
 ************************************************/
TaskFileIO::IOStateType TaskFileIO::writeTaskInfoToTaskFile( XMLDocument &doc, const TaskInfo &sTaskInfo )
{
    IOStateType eType = OPERATE_SUCCESS;//checkFileValidity( sTaskInfo.strFilePath );
    if( doc.isValid() )
    {
        //更新任务概要信息
        doc.beginElement(TASK_NODE);
        doc.setValue(TASK_TEST_NUMBER, sTaskInfo.strTestNumber);
        doc.setValue(TASK_TOTAL_DATA, QString::number(sTaskInfo.uiTotalCount));
        doc.setValue(TASK_TESTED_DATA, QString::number(sTaskInfo.uiTestedCount));
        doc.setValue(TASK_TEST_TIME, QString::number(sTaskInfo.lTestTime));
        doc.setValue(TASK_WEATHER, QString::number((int)sTaskInfo.stWeatherInfo.eWeather));
        doc.setValue(TASK_TEMPERATURE, QString::number(sTaskInfo.stWeatherInfo.dTemperature));
        doc.setValue(TASK_TEMPERATURE_UNIT, QString::number((int)sTaskInfo.eTempUnit));
        doc.setValue(TASK_HUMIDITY, QString::number(sTaskInfo.stWeatherInfo.dHumidity));

        //去除不存在的任务图片文件，避免后期任务上传失败
        if(!(sTaskInfo.strMainImgFilePath.isEmpty()))
        {
            if(FileOperUtil::checkFileOrDirExist(sTaskInfo.strMainImgFilePath))
            {
                if(!(doc.hasElement(TASK_MAIN_IMAGE_FILE_PATH)))
                {
                    doc.addElement(TASK_MAIN_IMAGE_FILE_PATH);
                }
                doc.setValue(TASK_MAIN_IMAGE_FILE_PATH, sTaskInfo.strMainImgFilePath);
            }
            else
            {
                if(doc.hasElement(TASK_MAIN_IMAGE_FILE_PATH))
                {
                    doc.removeElement(TASK_MAIN_IMAGE_FILE_PATH);
                }
            }
        }

        if(!(sTaskInfo.strRemoteMainImgFilePath.isEmpty()))
        {
            if(!(doc.hasElement(TASK_REMOTE_MAIN_IMAGE_FILE_PATH)))
            {
                doc.addElement(TASK_REMOTE_MAIN_IMAGE_FILE_PATH);
            }
            doc.setValue( TASK_REMOTE_MAIN_IMAGE_FILE_PATH,sTaskInfo.strRemoteMainImgFilePath );
        }

        //去除不存在的任务媒体文件，避免后期任务上传失败
        while(doc.hasElement(TASK_MEDIA_FILE_PATH))
        {
            doc.removeElement(TASK_MEDIA_FILE_PATH);     //clear previous element
        }

        for(QStringList::const_iterator iter = sTaskInfo.lMediaFilePath.begin();
            iter != sTaskInfo.lMediaFilePath.end(); ++iter)
        {
            if(FileOperUtil::checkFileOrDirExist(*iter))
            {
                //存在才添加
                QDomElement element = doc.addElement(TASK_MEDIA_FILE_PATH);
                doc.beginElement(element);
                doc.setText(*iter);
                doc.endElement();
            }
        }

        while(doc.hasElement(TASK_REMOTE_MEDIA_FILE_PATH))
        {
            doc.removeElement(TASK_REMOTE_MEDIA_FILE_PATH);     //clear previous element
        }

        for(QStringList::const_iterator iter = sTaskInfo.lRemoteMediaFilePath.begin();
            iter != sTaskInfo.lRemoteMediaFilePath.end(); ++iter)
        {
            QDomElement element = doc.addElement(TASK_REMOTE_MEDIA_FILE_PATH);
            doc.beginElement(element);
            doc.setText(*iter);
            doc.endElement();
        }

        //去除不存在的任务图片文件，避免后期任务上传失败
        while(doc.hasElement(TASK_IMAGE_FILE_PATH))
        {
            doc.removeElement(TASK_IMAGE_FILE_PATH);     //clear previous element
        }

        for(QStringList::const_iterator iter = sTaskInfo.lImageFilePath.begin();
            iter != sTaskInfo.lImageFilePath.end(); ++iter)
        {
            if(FileOperUtil::checkFileOrDirExist(*iter))
            {
                //存在才添加
                QDomElement element = doc.addElement(TASK_IMAGE_FILE_PATH);
                doc.beginElement(element);
                doc.setText(*iter);
                doc.endElement();
            }
        }

        while(doc.hasElement(TASK_REMOTE_IMAGE_FILE_PATH))
        {
            doc.removeElement(TASK_REMOTE_IMAGE_FILE_PATH);     //clear previous element
        }

        for(QStringList::const_iterator iter = sTaskInfo.lRemoteImageFilePath.begin();
            iter != sTaskInfo.lRemoteImageFilePath.end(); ++iter)
        {
            QDomElement element = doc.addElement(TASK_REMOTE_IMAGE_FILE_PATH);
            doc.beginElement(element);
            doc.setText(*iter);
            doc.endElement();
        }

        doc.endElement();
    }
    else
    {
        eType = FILE_TYPE_NOT_VALID;
        logError("open xml doc failed.");
    }

    return eType;
}

/************************************************
 * 功能: 写任务文件
 * 入参：sTaskInfo -- 任务概要信息（包含filePath）
 * 返回值：I/O错误类型
 ************************************************/
TaskFileIO::IOStateType TaskFileIO::writeTaskInfoToTaskFile(const TaskInfo &sTaskInfo)
{
    IOStateType eType = OPERATE_SUCCESS;
    if(sTaskInfo.strFilePath.isEmpty())
    {
        return FILE_NOT_EXIST;
    }

    XMLDocument doc(sTaskInfo.strFilePath, QIODevice::ReadWrite, ROOT_NODE);
    if(doc.isValid())
    {
        //更新任务概要信息
        doc.beginElement(TASK_NODE);
        doc.setValue(TASK_TEST_NUMBER, sTaskInfo.strTestNumber);
        doc.setValue(TASK_TOTAL_DATA, QString::number(sTaskInfo.uiTotalCount));
        doc.setValue(TASK_TESTED_DATA, QString::number(sTaskInfo.uiTestedCount));
        doc.setValue(TASK_TEST_TIME, QString::number(sTaskInfo.lTestTime));
        doc.setValue(TASK_WEATHER, QString::number((int)sTaskInfo.stWeatherInfo.eWeather));
        doc.setValue(TASK_TEMPERATURE, QString::number(sTaskInfo.stWeatherInfo.dTemperature));
        doc.setValue(TASK_TEMPERATURE_UNIT, QString::number((int)sTaskInfo.eTempUnit));
        doc.setValue(TASK_HUMIDITY, QString::number(sTaskInfo.stWeatherInfo.dHumidity));
        doc.endElement();

        if(!doc.save())
        {
            eType = OPERATE_FAIL;
            logError("save xml doc failed.");
        }
    }
    else
    {
        eType = FILE_TYPE_NOT_VALID;
        logError("open xml doc failed.");
    }

    return eType;
}

/************************************************
 * 功能: 写任务文件
 * 入参：sTaskInfo -- 任务概要信息（包含filePath）
 *      vPatrolTypes -- 巡检类型集合
 * 返回值：I/O错误类型
 ************************************************/
TaskFileIO::IOStateType TaskFileIO::writeTaskFile(TaskInfo &sTaskInfo, QVector<ItemPatrolType> &vPatrolTypes)
{
    IOStateType eType = checkFileValidity( sTaskInfo.strFilePath );
    if( OPERATE_SUCCESS == eType )
    {
        XMLDocument doc(sTaskInfo.strFilePath, QIODevice::ReadWrite, ROOT_NODE);
        if( doc.isValid() )
        {
            //测试完成时间，由T95填写
            sTaskInfo.lTestTime = TimezoneManager::instance()->formatLocalTimeToUTCValMsec(QDateTime::currentDateTimeUtc().toMSecsSinceEpoch());

            writeTaskInfoToTaskFile(doc, sTaskInfo);

            QString strNumber = "";
            // 遍历间隔分组
            QList<QDomElement> lBayElement = doc.childElement( BAY_NODE );
            for (QList<QDomElement>::iterator iterBay = lBayElement.begin();
                 iterBay != lBayElement.end(); ++iterBay)
            {
                ItemBay stBay;
                doc.beginElement(*iterBay);
                strNumber = doc.value(BAY_NUMBER);

                if(!findMatchedBay(vPatrolTypes, strNumber, stBay))
                {
                    continue;
                }

                //去除不存在的任务图片文件，避免后期任务上传失败
                if(!(stBay.strMainImgFilePath.isEmpty()))
                {
                    if(FileOperUtil::checkFileOrDirExist(stBay.strMainImgFilePath))
                    {
                        if(!(doc.hasElement(BAY_MAIN_IMAGE_FILE_PATH)))
                        {
                            doc.addElement(BAY_MAIN_IMAGE_FILE_PATH);
                        }
                        doc.setValue(BAY_MAIN_IMAGE_FILE_PATH, stBay.strMainImgFilePath);
                    }
                    else
                    {
                        if(doc.hasElement(BAY_MAIN_IMAGE_FILE_PATH))
                        {
                            doc.removeElement(BAY_MAIN_IMAGE_FILE_PATH);
                        }
                    }
                }

                if(!(stBay.strRemoteMainImgFilePath.isEmpty()))
                {
                    if(!doc.hasElement(BAY_REMOTE_MAIN_IMAGE_FILE_PATH))
                    {
                        doc.addElement(BAY_REMOTE_MAIN_IMAGE_FILE_PATH);
                    }
                    doc.setValue( BAY_REMOTE_MAIN_IMAGE_FILE_PATH, stBay.strRemoteMainImgFilePath );
                }

                //去除不存在的任务媒体文件，避免后期任务上传失败
                while(doc.hasElement(BAY_MEDIA_FILE_PATH))
                {
                    doc.removeElement(BAY_MEDIA_FILE_PATH);     //clear previous element
                }

                for(QStringList::const_iterator iter = stBay.lMediaFilePath.begin();
                    iter != stBay.lMediaFilePath.end(); ++iter)
                {
                    if(FileOperUtil::checkFileOrDirExist(*iter))
                    {
                        //存在才添加
                        QDomElement element = doc.addElement(BAY_MEDIA_FILE_PATH);
                        doc.beginElement(element);
                        doc.setText(*iter);
                        doc.endElement();
                    }
                }

                //更新任务文件里该间隔的media file path节点
                while(doc.hasElement(BAY_REMOTE_MEDIA_FILE_PATH))
                {
                    doc.removeElement(BAY_REMOTE_MEDIA_FILE_PATH);     //clear previous element
                }

                for(QStringList::iterator iter = stBay.lRemoteMediaFilePath.begin();
                    iter != stBay.lRemoteMediaFilePath.end(); ++iter)
                {
                    QDomElement element = doc.addElement(BAY_REMOTE_MEDIA_FILE_PATH);
                    doc.beginElement(element);
                    doc.setText(*iter);
                    doc.endElement();
                }

                //去除不存在的任务图片文件，避免后期任务上传失败
                while(doc.hasElement(BAY_IMAGE_FILE_PATH))
                {
                    doc.removeElement(BAY_IMAGE_FILE_PATH);     //clear previous element
                }

                for(QStringList::const_iterator iter = stBay.lImageFilePath.begin();
                    iter != stBay.lImageFilePath.end(); ++iter)
                {
                    if(FileOperUtil::checkFileOrDirExist(*iter))
                    {
                        //存在才添加
                        QDomElement element = doc.addElement(BAY_IMAGE_FILE_PATH);
                        doc.beginElement(element);
                        doc.setText(*iter);
                        doc.endElement();
                    }
                }

                //更新任务文件里该间隔的image file path节点
                while(doc.hasElement(BAY_REMOTE_IMAGE_FILE_PATH))
                {
                    doc.removeElement(BAY_REMOTE_IMAGE_FILE_PATH);     //clear previous element
                }

                for(QStringList::iterator iter = stBay.lRemoteImageFilePath.begin();
                    iter != stBay.lRemoteImageFilePath.end(); ++iter)
                {
                    QDomElement element = doc.addElement(BAY_REMOTE_IMAGE_FILE_PATH);
                    doc.beginElement(element);
                    doc.setText(*iter);
                    doc.endElement();
                }

                QString strDeviceNum = "";
                if(stBay.eType == PDAServiceNS::COMBINED_ELECTRIC)
                {
                    //组合电气(GIS)
                    //根据设备更新测点和测试数据信息
                    QList<QDomElement> lDeviceElement = doc.childElement( DEVICE_NODE );
                    for (QList<QDomElement>::iterator iter = lDeviceElement.begin();
                         iter != lDeviceElement.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        // 获取设备的编号
                        strDeviceNum = doc.value( DEVICE_NUMBER );

                        QVector<ItemDeviceTaskFileInfo*> vGISDevicesInBay;
                        QVector<ItemDevice*> vGISDevicesInTestType;
                        vGISDevicesInBay.clear();
                        vGISDevicesInTestType.clear();

                        // 根据设备编号获取相同测试类型（测试类型+电压等级）设备的集合,因文件中设备编号唯一，内存中会将不同测试类型的测点映射成在不同设备下
                        //vGISDevicesInBay= getGISItemDevicesInBay( strDeviceNum, vPatrolTypes );
                        // 根据设备编号获取相同测试类型（测试类型+电压等级）设备的集合,因文件中设备编号唯一，内存中会将不同测试类型的测点映射成在不同设备下
                        //vGISDevicesInTestType = itemDevicesInAllPatrolType( vPatrolTypes );

                        getGISItemDevices(strDeviceNum, vPatrolTypes, vGISDevicesInBay, vGISDevicesInTestType);

                        if(vGISDevicesInBay.size() > 0)
                        {
                            //回写各file path
                            saveGISDeviceFilePathInfoToTaskFile(doc, vGISDevicesInBay.at(0));
                        }

                        QList<QDomElement> lTestPointElement = doc.childElement( DEVICE_TEST_POINT );
                        // 将设备中的信息保存到任务文件的测点中
                        saveToTestPoint( lTestPointElement, doc, vGISDevicesInTestType );
                        doc.endElement();
                    }
                }
                else
                {
                    //根据设备更新测点和测试数据信息
                    QList<QDomElement> lDeviceElement = doc.childElement( DEVICE_NODE );
                    for (QList<QDomElement>::iterator iter = lDeviceElement.begin();
                         iter != lDeviceElement.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        // 获取设备的编号
                        strDeviceNum = doc.value( DEVICE_NUMBER );
                        QString strDevId = "";//baySourceId + strLocalId;  to fix
                        // 根据设备编号获取相同测试类型（测试类型+电压等级）设备的集合,因文件中设备编号唯一，内存中会将不同测试类型的测点映射成在不同设备下
                        QVector<ItemDevice*> vDevices = itemDevices( strDeviceNum, strDevId, vPatrolTypes );
                        if(vDevices.size() > 0)
                        {
                            //把设备层级所有的信息回写到任务文件
                            saveDeviceFilePathInfoToTaskFile(doc, vDevices.at(0));
                        }

                        QList<QDomElement> lTestPointElement = doc.childElement( DEVICE_TEST_POINT );
                        // 将设备中的信息保存到任务文件的测点中
                        saveToTestPoint( lTestPointElement, doc, vDevices );
                        doc.endElement();
                    }
                }

                doc.endElement();
            }

            if(!doc.save())
            {
                eType = OPERATE_FAIL;
                logError("save xml doc failed.");
            }
        }
        else
        {
            eType = FILE_TYPE_NOT_VALID;
            logError("open xml doc failed.");
        }
    }
    else
    {
        eType = FILE_NOT_EXIST;
        logError("file is invalid.");
    }

    return eType;
}

/**************************************************
 * 功能：保存临时任务文件
 * 输入参数：
 *      bChanged：是否发生变化
 *      sTaskInfo：任务信息
 *      qstrBayNumber：间隔的number
 *      stDevice：设备信息
 * 返回值：
 *      bool：true -- 存入成功，false -- 存入失败
 * ******************************************************/
bool TaskFileIO::saveTaskTmpFile(bool bChanged, TaskInfo &sTaskInfo, QString qstrBayNumber, ItemDevice &stDevice)
{
    bool bRet = false;

    if(bChanged)
    {
        QString qstrTaskTmpFile = sTaskInfo.strFilePath + "_tmp";

        if(!FileOperUtil::checkFileOrDirExist(qstrTaskTmpFile))
        {
            FileOperUtil::copyFileToPath(qstrTaskTmpFile, sTaskInfo.strFilePath);
        }

        XMLDocument doc(qstrTaskTmpFile, QIODevice::ReadWrite, ROOT_NODE);
        if(doc.isValid())
        {
            doc.beginElement(TASK_NODE);
            doc.setValue(TASK_TEST_NUMBER, sTaskInfo.strTestNumber);
            doc.setValue(TASK_TOTAL_DATA, QString::number(sTaskInfo.uiTotalCount));
            doc.setValue(TASK_TESTED_DATA, QString::number(sTaskInfo.uiTestedCount));
            doc.setValue(TASK_TEST_TIME, QString::number(sTaskInfo.lTestTime));
            doc.setValue(TASK_WEATHER, QString::number(static_cast<int>(sTaskInfo.stWeatherInfo.eWeather)));
            doc.setValue(TASK_TEMPERATURE, QString::number(sTaskInfo.stWeatherInfo.dTemperature));
            doc.setValue(TASK_TEMPERATURE_UNIT, QString::number(static_cast<int>(sTaskInfo.eTempUnit)));
            doc.setValue(TASK_HUMIDITY, QString::number(sTaskInfo.stWeatherInfo.dHumidity));
            doc.endElement();

            QString qstrNumber = "";
            // 遍历间隔分组
            QList<QDomElement> lBayElement = doc.childElement(BAY_NODE);
            for (QList<QDomElement>::iterator iterBay = lBayElement.begin();
                 iterBay != lBayElement.end(); ++iterBay)
            {
                doc.beginElement(*iterBay);
                qstrNumber = doc.value(BAY_NUMBER);

                if(qstrNumber == qstrBayNumber)
                {
                    QString qstrDevNumber = "";
                    QList<QDomElement> lDeviceElement = doc.childElement(DEVICE_NODE);
                    for (QList<QDomElement>::iterator iter = lDeviceElement.begin();
                         iter != lDeviceElement.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        qstrDevNumber = doc.value(DEVICE_NUMBER);
                        if(qstrDevNumber == stDevice.strDevNum)
                        {
                            doc.setValue(DEVICE_LOAD_CURRENT, QString::number(stDevice.dLoadCurrent));
                            doc.setValue(DEVICE_LOAD_CURRENT_UNIT, QString::number(static_cast<int>(stDevice.eLoadCurrentUnit)));
                            doc.setValue(DEVICE_LOAD_CURRENT_SIGN, QString::number(stDevice.dwLoadCurrentSign));

                            QVector<ItemDevice*> qvtDevices;
                            qvtDevices.clear();
                            qvtDevices.append(&stDevice);

                            QList<QDomElement> lTestPointElement = doc.childElement(DEVICE_TEST_POINT);
                            saveToTestPoint(lTestPointElement, doc, qvtDevices);
                            doc.endElement();
                        }
                    }
                }

                doc.endElement();
            }

            if(!doc.save())
            {
                bRet = false;
                logError("save tmp xml doc failed.");
            }
        }
        else
        {
            bRet = false;
            logError("tmp xml doc is invalid.");
        }
    }
    else
    {
        bRet = true;
        logWarning("task info is not changed.");
    }

    return bRet;
}

/****************************
输入参数:strNumber -- rank number
        pstDestFilesPath -- media/image path链表
        cnt---链表item个数
输出参数:iMatchedIndex---在链表里匹配的item index
功能： 根据media/image path链表里的rank number找到匹配的media/image path结构
*****************************/
bool TaskFileIO::findMatchedRemotePathIndexByRankNumber(const QString &strNumber, const QVector<_ImgMediaPath> &vtDestFilesPath, int cnt, int &iMatchedIndex)
{
    for(int i = 0, iSize = vtDestFilesPath.size(); (i < cnt) && (i < iSize); ++i)
    {
        if(vtDestFilesPath.at(i).strRankNumber == strNumber)
        {
            iMatchedIndex = i;
            return true;
        }
    }

    return false;
}

/****************************
输入参数:strTaskFilePath -- 任务描述文件
        stDestFilesPath -- 新的media/image path链表
功能： 更新任务描述文件里的任务概要信息里的media/image path节点
*****************************/
void TaskFileIO::updateTaskInfoMediaRelatedOfTaskFile(const QString &strTaskFilePath, const ImgMediaPath &stDestFilesPath)
{
    XMLDocument doc( strTaskFilePath,QIODevice::ReadWrite,ROOT_NODE );
    doc.beginElement(TASK_NODE);

    //去除不存在的任务图片文件，避免后期任务上传失败
    if(FileOperUtil::checkFileOrDirExist(stDestFilesPath.strMainImgPath))
    {
        if(!(doc.hasElement(TASK_MAIN_IMAGE_FILE_PATH)))
        {
            doc.addElement(TASK_MAIN_IMAGE_FILE_PATH);
        }
        doc.setValue(TASK_MAIN_IMAGE_FILE_PATH, stDestFilesPath.strMainImgPath);
    }
    else
    {
        if(doc.hasElement(TASK_MAIN_IMAGE_FILE_PATH))
        {
            doc.removeElement(TASK_MAIN_IMAGE_FILE_PATH);
        }
    }

    //去除不存在的任务媒体文件，避免后期任务上传失败
    while(doc.hasElement(TASK_MEDIA_FILE_PATH))
    {
        doc.removeElement(TASK_MEDIA_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = stDestFilesPath.listMediaPath.begin();
        iter != stDestFilesPath.listMediaPath.end(); ++iter)
    {
        if(FileOperUtil::checkFileOrDirExist(*iter))
        {
            //存在才添加
            QDomElement element = doc.addElement(TASK_MEDIA_FILE_PATH);
            doc.beginElement(element);
            doc.setText(*iter);
            doc.endElement();
        }
    }

    //去除不存在的任务图片文件，避免后期任务上传失败
    while(doc.hasElement(TASK_IMAGE_FILE_PATH))
    {
        doc.removeElement(TASK_IMAGE_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = stDestFilesPath.listImgPath.begin();
        iter != stDestFilesPath.listImgPath.end(); ++iter)
    {
        if(FileOperUtil::checkFileOrDirExist(*iter))
        {
            //存在才添加
            QDomElement element = doc.addElement(TASK_IMAGE_FILE_PATH);
            doc.beginElement(element);
            doc.setText(*iter);
            doc.endElement();
        }
    }

    doc.endElement();
    doc.save();     // 完成写入
    //#ifdef Q_OS_LINUX
    //    sync();
    //#endif
    return;
}

//更新测点内的音频、图片文件路径--added by zhaoyongjun,2018.5.18
void TaskFileIO::updateTestPointMediaRelatedOfTaskFile(XMLDocument &doc, const QVector<_ImgMediaPath> &vtDestFilesPath, int cnt)
{
    int iSize = vtDestFilesPath.size();
    if(iSize <= 0)
    {
        qDebug() << "TaskFileIO::updateTestPointMediaRelatedOfTaskFile, vector size <= 0." << endl;
        return;
    }

    int iFindCount = 0;
    QString strNumber = "";
    int iIdx = 0;

    const QList<QDomElement> qlTestPointNode = doc.childElement(DEVICE_TEST_POINT);
    QList<QDomElement>::const_iterator iterTestPointNode = qlTestPointNode.begin();
    for(; iterTestPointNode != qlTestPointNode.end(); ++iterTestPointNode)
    {
        doc.beginElement(*iterTestPointNode);
        strNumber = doc.value(TEST_POINT_NUMBER);
        if(!findMatchedRemotePathIndexByRankNumber(strNumber, vtDestFilesPath, cnt, iIdx))
        {
            continue;
        }

        ImgMediaPath stIMPathTmp = vtDestFilesPath.at(iIdx);
        //去除不存在的任务图片文件，避免后期任务上传失败
        if(FileOperUtil::checkFileOrDirExist(stIMPathTmp.strMainImgPath))
        {
            if(!(doc.hasElement(TEST_POINT_MAIN_IMAGE_FILE_PATH)))
            {
                doc.addElement(TEST_POINT_MAIN_IMAGE_FILE_PATH);
            }
            doc.setValue(TEST_POINT_MAIN_IMAGE_FILE_PATH, stIMPathTmp.strMainImgPath);
        }
        else
        {
            if(doc.hasElement(TEST_POINT_MAIN_IMAGE_FILE_PATH))
            {
                doc.removeElement(TEST_POINT_MAIN_IMAGE_FILE_PATH);
            }
        }

        //去除不存在的任务媒体文件，避免后期任务上传失败
        while(doc.hasElement(TEST_POINT_MEDIA_FILE_PATH))
        {
            doc.removeElement(TEST_POINT_MEDIA_FILE_PATH);     //clear previous element
        }

        for(QStringList::const_iterator iter = stIMPathTmp.listMediaPath.begin();
            iter != stIMPathTmp.listMediaPath.end(); ++iter)
        {
            if(FileOperUtil::checkFileOrDirExist(*iter))
            {
                //存在才添加
                QDomElement element = doc.addElement(TEST_POINT_MEDIA_FILE_PATH);
                doc.beginElement(element);
                doc.setText(*iter);
                doc.endElement();
            }
        }

        //去除不存在的任务图片文件，避免后期任务上传失败
        while(doc.hasElement(TEST_POINT_IMAGE_FILE_PATH))
        {
            doc.removeElement(TEST_POINT_IMAGE_FILE_PATH);     //clear previous element
        }

        for(QStringList::const_iterator iter = stIMPathTmp.listImgPath.begin();
            iter != stIMPathTmp.listImgPath.end(); ++iter)
        {
            if(FileOperUtil::checkFileOrDirExist(*iter))
            {
                //存在才添加
                QDomElement element = doc.addElement(TEST_POINT_IMAGE_FILE_PATH);
                doc.beginElement(element);
                doc.setText(*iter);
                doc.endElement();
            }
        }

        doc.endElement();

        if((++iFindCount) >= iSize)
        {
            //一般iFindCount的次数不超过vtDestFilesPath的size，
            //当FindCount等于vtDestFilesPath的size时，表示查找完毕，跳出循环
            break;
        }
    }

    return;
}

//更新设备内的音频、图片文件路径--added by zhaoyongjun,2018.5.18
void TaskFileIO::updateDeviceMediaRelatedOfTaskFile(XMLDocument &doc, const QVector<_ImgMediaPath> &vtDestFilesPath, int cnt)
{
    int iSize = vtDestFilesPath.size();
    if(iSize <= 0)
    {
        qDebug() << "TaskFileIO::updateDeviceMediaRelatedOfTaskFile, vector size <= 0." << endl;
        return;
    }

    int iFindCount = 0;
    QString strNumber = "";
    int iIdx = 0;

    const QList<QDomElement> qlDeviceNode = doc.childElement(DEVICE_NODE);
    QList<QDomElement>::const_iterator iterDevNode = qlDeviceNode.begin();
    for(; iterDevNode != qlDeviceNode.end(); ++iterDevNode)
    {
        doc.beginElement(*iterDevNode);
        strNumber = doc.value(DEVICE_NUMBER);
        if(!findMatchedRemotePathIndexByRankNumber(strNumber, vtDestFilesPath, cnt, iIdx))
        {
            continue;
        }

        ImgMediaPath stIMPathTmp = vtDestFilesPath.at(iIdx);

        //去除不存在的任务图片文件，避免后期任务上传失败
        if(FileOperUtil::checkFileOrDirExist(stIMPathTmp.strMainImgPath))
        {
            if(!(doc.hasElement(DEVICE_MAIN_IMAGE_FILE_PATH)))
            {
                doc.addElement(DEVICE_MAIN_IMAGE_FILE_PATH);
            }
            doc.setValue(TASK_MAIN_IMAGE_FILE_PATH, stIMPathTmp.strMainImgPath);
        }
        else
        {
            if(doc.hasElement(DEVICE_MAIN_IMAGE_FILE_PATH))
            {
                doc.removeElement(DEVICE_MAIN_IMAGE_FILE_PATH);
            }
        }

        //去除不存在的任务媒体文件，避免后期任务上传失败
        while(doc.hasElement(DEVICE_MEDIA_FILE_PATH))
        {
            doc.removeElement(DEVICE_MEDIA_FILE_PATH);     //clear previous element
        }

        for(QStringList::const_iterator iter = stIMPathTmp.listMediaPath.begin();
            iter != stIMPathTmp.listMediaPath.end(); ++iter)
        {
            if(FileOperUtil::checkFileOrDirExist(*iter))
            {
                //存在才添加
                QDomElement element = doc.addElement(DEVICE_MEDIA_FILE_PATH);
                doc.beginElement(element);
                doc.setText(*iter);
                doc.endElement();
            }
        }

        //去除不存在的任务图片文件，避免后期任务上传失败
        while(doc.hasElement(DEVICE_IMAGE_FILE_PATH))
        {
            doc.removeElement(DEVICE_IMAGE_FILE_PATH);     //clear previous element
        }

        for(QStringList::const_iterator iter = stIMPathTmp.listImgPath.begin();
            iter != stIMPathTmp.listImgPath.end(); ++iter)
        {
            if(FileOperUtil::checkFileOrDirExist(*iter))
            {
                //存在才添加
                QDomElement element = doc.addElement(DEVICE_IMAGE_FILE_PATH);
                doc.beginElement(element);
                doc.setText(*iter);
                doc.endElement();
            }
        }

        updateTestPointMediaRelatedOfTaskFile(doc, stIMPathTmp.vtSubLevel, stIMPathTmp.iSubCount);
        doc.endElement();

        if((++iFindCount) >= iSize)
        {
            //一般iFindCount的次数不超过vtDestFilesPath的size，
            //当FindCount等于vtDestFilesPath的size时，表示查找完毕，跳出循环
            break;
        }
    }

    return;
}

QString TaskFileIO::getTmpTaskFilePath(const QString &qstrTaskFilePath)
{
    //为了避免同一目录下多个t13文件，需要另外设置目录
    QFileInfo stFileInfo(qstrTaskFilePath);

    QString qstrFileDir = stFileInfo.absolutePath();
    qstrFileDir = qstrFileDir.endsWith("/") ? qstrFileDir : (qstrFileDir + "/");
    qstrFileDir += PDA_TASK_TEMP_FOLDER;

    QDir stDir(qstrFileDir);
    if(!(stDir.exists()))
    {
        stDir.mkpath(qstrFileDir);
    }

    QString qstrFileName = stFileInfo.fileName();
    int index = qstrFileName.lastIndexOf(PDA_TASK_FILE_SUFFIX);

    QString qstrTmpTaskFilePath = qstrFileDir + qstrFileName.mid(0, index);
    qstrTmpTaskFilePath += "_tmp";
    qstrTmpTaskFilePath += PDA_TASK_FILE_SUFFIX;

    logDebug(QString("create temp file (%1).").arg(qstrTmpTaskFilePath));

    return qstrTmpTaskFilePath;
}

/*
 * 根据任务描述文件创建新的临时任务描述文件(如果该文件中含有remotefilepath节点, 则新建一个删除所有该同名节点的临时文件,
 * 如果没有remote节点则无需创建临时文件)
 * 注: 该临时文件删除所有的remote节点, 如: RemoteFilePath, RemoteMainImgFilePath, RemoteMediaFilePath, RemoteImgFilePath
 *
*/
bool TaskFileIO::createTmpAPPTaskFilePath(const QString &strTaskFilePath, QString &strTmpTaskFilePath)
{
    strTmpTaskFilePath = getTmpTaskFilePath(strTaskFilePath);

    FileOperUtil::deleteFile(strTmpTaskFilePath);//删除之前的文件，因为文件拷贝式备份，复制的文件，目标文件必须不存在

    if(!(QFile::copy(strTaskFilePath, strTmpTaskFilePath)))
    {
        logError(QString("copy temp file (%1) failed.").arg(strTmpTaskFilePath));
        return false;
    }

    /*XMLDocument doc( strTmpTaskFilePath,QIODevice::ReadWrite,ROOT_NODE );
    //task info node
    doc.beginElement(TASK_NODE);
    //remove remote main image file path of task info node
    if(doc.hasElement(TASK_REMOTE_MAIN_IMAGE_FILE_PATH))
    {
        doc.removeElement(TASK_REMOTE_MAIN_IMAGE_FILE_PATH);
    }
    //remove remote media files path of task info node
    const QList<QDomElement> qlMediaFilePathNode = doc.childElement(TASK_REMOTE_MEDIA_FILE_PATH);
    for(int i = 0; i < qlMediaFilePathNode.size(); ++i)
    {
        doc.removeElement(TASK_REMOTE_MEDIA_FILE_PATH);
    }
    //remove remote image files path of task info node
    const QList<QDomElement> qlImageFilePathNode = doc.childElement(TASK_REMOTE_IMAGE_FILE_PATH);
    for(int i = 0; i < qlImageFilePathNode.size(); ++i)
    {
        doc.removeElement(TASK_REMOTE_IMAGE_FILE_PATH);
    }
    doc.endElement();

    //bay node
    const QList<QDomElement> qlBayNode = doc.childElement(BAY_NODE);
    for(int i = 0; i < qlBayNode.size(); ++i)
    {
        doc.beginElement(qlBayNode.at(i));

        //remove remote main image file path of bay node
        if(doc.hasElement(BAY_REMOTE_MAIN_IMAGE_FILE_PATH))
        {
            doc.removeElement(BAY_REMOTE_MAIN_IMAGE_FILE_PATH);
        }
        //remove remote media files path of bay node
        const QList<QDomElement> qlMediaFilePathNode = doc.childElement(BAY_REMOTE_MEDIA_FILE_PATH);
        for(int i = 0; i < qlMediaFilePathNode.size(); ++i)
        {
            doc.removeElement(BAY_REMOTE_MEDIA_FILE_PATH);
        }
        //remove remote image files path of bay node
        const QList<QDomElement> qlImageFilePathNode = doc.childElement(BAY_REMOTE_IMAGE_FILE_PATH);
        for(int i = 0; i < qlImageFilePathNode.size(); ++i)
        {
            doc.removeElement(BAY_REMOTE_IMAGE_FILE_PATH);
        }
        //device node
        const QList<QDomElement> qlDeviceNode = doc.childElement(DEVICE_NODE);
        for(int i = 0; i < qlDeviceNode.size(); ++i)
        {
            doc.beginElement(qlDeviceNode.at(i));

            //remove remote main image file path of device node
            if(doc.hasElement(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH))
            {
                doc.removeElement(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH);
            }
            //remove remote media files path of device node
            const QList<QDomElement> qlMediaFilePathNode = doc.childElement(DEVICE_REMOTE_MEDIA_FILE_PATH);
            for(int i = 0; i < qlMediaFilePathNode.size(); ++i)
            {
                doc.removeElement(DEVICE_REMOTE_MEDIA_FILE_PATH);
            }
            //remove remote image files path of device node
            const QList<QDomElement> qlImageFilePathNode = doc.childElement(DEVICE_REMOTE_IMAGE_FILE_PATH);
            for(int i = 0; i < qlImageFilePathNode.size(); ++i)
            {
                doc.removeElement(DEVICE_REMOTE_IMAGE_FILE_PATH);
            }

            //test point node
            const QList<QDomElement> qlTestPointNode = doc.childElement(DEVICE_TEST_POINT);
            for(int i = 0; i < qlTestPointNode.size(); ++i)
            {
                doc.beginElement(qlTestPointNode.at(i));

                //remove remote main image file path of test point node
                if(doc.hasElement(TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH))
                {
                    doc.removeElement(TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH);
                }
                //remove remote media files path of test point node
                const QList<QDomElement> qlMediaFilePathNode = doc.childElement(TEST_POINT_REMOTE_MEDIA_FILE_PATH);
                for(int i = 0; i < qlMediaFilePathNode.size(); ++i)
                {
                    doc.removeElement(TEST_POINT_REMOTE_MEDIA_FILE_PATH);
                }
                //remove remote image files path of test point node
                const QList<QDomElement> qlImageFilePathNode = doc.childElement(TEST_POINT_REMOTE_IMAGE_FILE_PATH);
                for(int i = 0; i < qlImageFilePathNode.size(); ++i)
                {
                    doc.removeElement(TEST_POINT_REMOTE_IMAGE_FILE_PATH);
                }

                //remove remote path of test data node
                const QList<QDomElement> qlTestDataNode = doc.childElement(TEST_DATA);
                for(int i = 0; i < qlTestDataNode.size(); ++i)
                {
                    doc.beginElement(qlTestDataNode.at(i));
                    if(doc.hasElement(TEST_DATA_REMOTE_PATH))
                    {
                        doc.removeElement(TEST_DATA_REMOTE_PATH);
                    }
                    doc.endElement();
                }
                doc.endElement();
            }
            doc.endElement();
        }
        doc.endElement();
    }
    doc.endElement();

    doc.save();
    //#ifdef Q_OS_LINUX
    //    sync();
    //#endif
    */
    return true;
}

/*
**
 * 根据任务描述文件创建新的临时任务描述文件(如果该文件中含有remotefilepath节点, 则新建一个临时文件,
 * 该文件把remotefilepath节点的内容替换掉filePath节点的内容, 然后删除remotefilepath节点)
 * 注: 该修改包含所有的remote节点, 如: RemoteFilePath, RemoteMainImgFilePath, RemoteMediaFilePath, RemoteImgFilePath
*
*/
bool TaskFileIO::createTmpCMSTaskFilePath(const QString &strTaskFilePath, QString &strTmpTaskFilePath)
{
    strTmpTaskFilePath = getTmpTaskFilePath(strTaskFilePath);

    FileOperUtil::deleteFile(strTmpTaskFilePath);//删除之前的文件，因为文件拷贝式备份，复制的文件，目标文件必须不存在

    if(!(QFile::copy(strTaskFilePath, strTmpTaskFilePath)))
    {
        logError(QString("copy temp file (%1) failed.").arg(strTmpTaskFilePath));
        return false;
    }

    XMLDocument doc(strTmpTaskFilePath, QIODevice::ReadWrite, ROOT_NODE);
    QString strPath = "";
    bool bTestCountChanged = false;

    //task info node
    doc.beginElement(TASK_NODE);
    quint32 dwTestCount = doc.value(TASK_TESTED_DATA).toUShort();

    //remove remote main image file path of task info node
    if(doc.hasElement(TASK_REMOTE_MAIN_IMAGE_FILE_PATH))
    {
        strPath = doc.value(TASK_REMOTE_MAIN_IMAGE_FILE_PATH);
        doc.setValue(TASK_MAIN_IMAGE_FILE_PATH, strPath);
        doc.removeElement(TASK_REMOTE_MAIN_IMAGE_FILE_PATH);
    }
    //remove remote media files path of task info node, and replace file path value with it
    const QList<QDomElement> qlMediaFilePathNode = doc.childElement(TASK_MEDIA_FILE_PATH);
    const QList<QDomElement> qlRemoteMediaFilePathNode = doc.childElement(TASK_REMOTE_MEDIA_FILE_PATH);
    QList<QDomElement>::const_iterator iterMediaFile = qlMediaFilePathNode.begin();
    QList<QDomElement>::const_iterator iterRemoteMediaFile = qlRemoteMediaFilePathNode.begin();
    for(; iterMediaFile != qlMediaFilePathNode.end() && iterRemoteMediaFile != qlRemoteMediaFilePathNode.end();
        ++iterMediaFile, ++iterRemoteMediaFile)
    {
        doc.beginElement(*iterRemoteMediaFile);
        strPath = doc.text();
        doc.endElement();
        doc.removeElement(TASK_REMOTE_MEDIA_FILE_PATH);

        doc.beginElement(*iterMediaFile);
        doc.setText(strPath);
        doc.endElement();
    }

    //remove remote media files path of task info node, and replace file path value with it
    const QList<QDomElement> qlImageFilePathNode = doc.childElement(TASK_IMAGE_FILE_PATH);
    const QList<QDomElement> qlRemoteImageFilePathNode = doc.childElement(TASK_REMOTE_IMAGE_FILE_PATH);
    QList<QDomElement>::const_iterator iterImageFile = qlImageFilePathNode.begin();
    QList<QDomElement>::const_iterator iterRemoteImageFile = qlRemoteImageFilePathNode.begin();
    for(; iterImageFile != qlImageFilePathNode.end() && iterRemoteImageFile != qlRemoteImageFilePathNode.end();
        ++iterImageFile, ++iterRemoteImageFile)
    {
        doc.beginElement(*iterRemoteImageFile);
        strPath = doc.text();
        doc.endElement();
        doc.removeElement(TASK_REMOTE_IMAGE_FILE_PATH);

        doc.beginElement(*iterImageFile);
        doc.setText(strPath);
        doc.endElement();
    }

    doc.endElement();

    //bay node
    const QList<QDomElement> qlBayNode = doc.childElement(BAY_NODE);
    for(int i = 0, iSize = qlBayNode.size(); i < iSize; ++i)
    {
        doc.beginElement(qlBayNode.at(i));

        //remove remote main image file path of bay node
        if(doc.hasElement(BAY_REMOTE_MAIN_IMAGE_FILE_PATH))
        {
            strPath = doc.value(BAY_REMOTE_MAIN_IMAGE_FILE_PATH);
            doc.setValue(BAY_MAIN_IMAGE_FILE_PATH, strPath);
            doc.removeElement(BAY_REMOTE_MAIN_IMAGE_FILE_PATH);
        }
        //remove remote media files path of task info node, and replace file path value with it
        const QList<QDomElement> qlMediaFilePathNode = doc.childElement(BAY_MEDIA_FILE_PATH);
        const QList<QDomElement> qlRemoteMediaFilePathNode = doc.childElement(BAY_REMOTE_MEDIA_FILE_PATH);
        QList<QDomElement>::const_iterator iterMediaFile = qlMediaFilePathNode.begin();
        QList<QDomElement>::const_iterator iterRemoteMediaFile = qlRemoteMediaFilePathNode.begin();
        for(; iterMediaFile != qlMediaFilePathNode.end() && iterRemoteMediaFile != qlRemoteMediaFilePathNode.end();
            ++iterMediaFile, ++iterRemoteMediaFile)
        {
            doc.beginElement(*iterRemoteMediaFile);
            strPath = doc.text();
            doc.endElement();
            doc.removeElement(BAY_REMOTE_MEDIA_FILE_PATH);

            doc.beginElement(*iterMediaFile);
            doc.setText(strPath);
            doc.endElement();
        }

        //remove remote media files path of task info node, and replace file path value with it
        const QList<QDomElement> qlImageFilePathNode = doc.childElement(BAY_IMAGE_FILE_PATH);
        const QList<QDomElement> qlRemoteImageFilePathNode = doc.childElement(BAY_REMOTE_IMAGE_FILE_PATH);
        QList<QDomElement>::const_iterator iterImageFile = qlImageFilePathNode.begin();
        QList<QDomElement>::const_iterator iterRemoteImageFile = qlRemoteImageFilePathNode.begin();
        for(; iterImageFile != qlImageFilePathNode.end() && iterRemoteImageFile != qlRemoteImageFilePathNode.end();
            ++iterImageFile, ++iterRemoteImageFile)
        {
            doc.beginElement(*iterRemoteImageFile);
            strPath = doc.text();
            doc.endElement();
            doc.removeElement(BAY_REMOTE_IMAGE_FILE_PATH);

            doc.beginElement(*iterImageFile);
            doc.setText(strPath);
            doc.endElement();
        }

        //device node
        const QList<QDomElement> qlDeviceNode = doc.childElement(DEVICE_NODE);
        for(int i = 0, iSize = qlDeviceNode.size(); i < iSize; ++i)
        {
            doc.beginElement(qlDeviceNode.at(i));

            //remove remote main image file path of device node
            if(doc.hasElement(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH))
            {
                strPath = doc.value(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH);
                doc.setValue(DEVICE_MAIN_IMAGE_FILE_PATH, strPath);
                doc.removeElement(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH);
            }
            //remove remote media files path of task info node, and replace file path value with it
            const QList<QDomElement> qlMediaFilePathNode = doc.childElement(DEVICE_MEDIA_FILE_PATH);
            const QList<QDomElement> qlRemoteMediaFilePathNode = doc.childElement(DEVICE_REMOTE_MEDIA_FILE_PATH);
            QList<QDomElement>::const_iterator iterMediaFile = qlMediaFilePathNode.begin();
            QList<QDomElement>::const_iterator iterRemoteMediaFile = qlRemoteMediaFilePathNode.begin();
            for(; iterMediaFile != qlMediaFilePathNode.end() && iterRemoteMediaFile != qlRemoteMediaFilePathNode.end();
                ++iterMediaFile, ++iterRemoteMediaFile)
            {
                doc.beginElement(*iterRemoteMediaFile);
                strPath = doc.text();
                doc.endElement();
                doc.removeElement(DEVICE_REMOTE_MEDIA_FILE_PATH);

                doc.beginElement(*iterMediaFile);
                doc.setText(strPath);
                doc.endElement();
            }

            //remove remote media files path of task info node, and replace file path value with it
            const QList<QDomElement> qlImageFilePathNode = doc.childElement(DEVICE_IMAGE_FILE_PATH);
            const QList<QDomElement> qlRemoteImageFilePathNode = doc.childElement(DEVICE_REMOTE_IMAGE_FILE_PATH);
            QList<QDomElement>::const_iterator iterImageFile = qlImageFilePathNode.begin();
            QList<QDomElement>::const_iterator iterRemoteImageFile = qlRemoteImageFilePathNode.begin();
            for(; iterImageFile != qlImageFilePathNode.end() && iterRemoteImageFile != qlRemoteImageFilePathNode.end();
                ++iterImageFile, ++iterRemoteImageFile)
            {
                doc.beginElement(*iterRemoteImageFile);
                strPath = doc.text();
                doc.endElement();
                doc.removeElement(DEVICE_REMOTE_IMAGE_FILE_PATH);

                doc.beginElement(*iterImageFile);
                doc.setText(strPath);
                doc.endElement();
            }

            //test point node
            const QList<QDomElement> qlTestPointNode = doc.childElement(DEVICE_TEST_POINT);
            for(int i = 0, iSize = qlTestPointNode.size(); i < iSize; ++i)
            {
                doc.beginElement(qlTestPointNode.at(i));

                //remove remote main image file path of test point node
                if(doc.hasElement(TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH))
                {
                    strPath = doc.value(TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH);
                    doc.setValue(TEST_POINT_MAIN_IMAGE_FILE_PATH, strPath);
                    doc.removeElement(TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH);
                }
                //remove remote media files path of task info node, and replace file path value with it
                const QList<QDomElement> qlMediaFilePathNode = doc.childElement(TEST_POINT_MEDIA_FILE_PATH);
                const QList<QDomElement> qlRemoteMediaFilePathNode = doc.childElement(TEST_POINT_REMOTE_MEDIA_FILE_PATH);
                QList<QDomElement>::const_iterator iterMediaFile = qlMediaFilePathNode.begin();
                QList<QDomElement>::const_iterator iterRemoteMediaFile = qlRemoteMediaFilePathNode.begin();
                for(; iterMediaFile != qlMediaFilePathNode.end() && iterRemoteMediaFile != qlRemoteMediaFilePathNode.end();
                    ++iterMediaFile, ++iterRemoteMediaFile)
                {
                    doc.beginElement(*iterRemoteMediaFile);
                    strPath = doc.text();
                    doc.endElement();
                    doc.removeElement(TEST_POINT_REMOTE_MEDIA_FILE_PATH);

                    doc.beginElement(*iterMediaFile);
                    doc.setText(strPath);
                    doc.endElement();
                }

                //remove remote media files path of task info node, and replace file path value with it
                const QList<QDomElement> qlImageFilePathNode = doc.childElement(TEST_POINT_IMAGE_FILE_PATH);
                const QList<QDomElement> qlRemoteImageFilePathNode = doc.childElement(TEST_POINT_REMOTE_IMAGE_FILE_PATH);
                QList<QDomElement>::const_iterator iterImageFile = qlImageFilePathNode.begin();
                QList<QDomElement>::const_iterator iterRemoteImageFile = qlRemoteImageFilePathNode.begin();
                for(; iterImageFile != qlImageFilePathNode.end() && iterRemoteImageFile != qlRemoteImageFilePathNode.end();
                    ++iterImageFile, ++iterRemoteImageFile)
                {
                    doc.beginElement(*iterRemoteImageFile);
                    strPath = doc.text();
                    doc.endElement();
                    doc.removeElement(TEST_POINT_REMOTE_IMAGE_FILE_PATH);

                    doc.beginElement(*iterImageFile);
                    doc.setText(strPath);
                    doc.endElement();
                }

                //remove remote path of test data node
                const QList<QDomElement> qlTestDataNode = doc.childElement(TEST_DATA);
                for(int i = 0, iSize = qlTestDataNode.size(); i < iSize; ++i)
                {
                    doc.beginElement(qlTestDataNode.at(i));
                    if(doc.hasElement(TEST_DATA_REMOTE_PATH))
                    {
                        strPath = doc.value(TEST_DATA_REMOTE_PATH);
                        doc.setValue(TEST_DATA_FILE_PATH, strPath);
                        doc.removeElement(TEST_DATA_REMOTE_PATH);
                    }

                    if(doc.hasElement(TEST_DATA_REMOTE_ATTACH_PATH))
                    {
                        strPath = doc.value(TEST_DATA_REMOTE_ATTACH_PATH);
                        doc.setValue(TEST_DATA_ATTACH_PATH, strPath);
                        doc.removeElement(TEST_DATA_REMOTE_ATTACH_PATH);
                    }

                    //如果替换失败，还有T95本地路径，则删除该路径，文件名，测点为未测等
                    QString qstrFilePath = doc.value(TEST_DATA_FILE_PATH);
                    if(qstrFilePath.contains("/data/SavedData/TestTask"))
                    {
                        logWarning(QString("data file (%1) replace failed.").arg(qstrFilePath).toLatin1().data());
                        doc.setValue(TEST_DATA_FILE_PATH, "");
                        doc.setValue(TEST_DATA_FILE_NAME, "");
                        doc.setValue(TEST_DATA_IS_TEST, QString::number(0));
                        bTestCountChanged = true;
                        dwTestCount = (0 == dwTestCount) ? 0 : (dwTestCount - 1);
                    }

                    //如果替换失败，还有T95本地路径，则删除该路径，文件名，测点为未测等
                    QString qstrAttachFilePath = doc.value(TEST_DATA_ATTACH_PATH);
                    if(qstrAttachFilePath.contains("/data/SavedData/TestTask"))
                    {
                        logWarning(QString("data file (%1) replace failed.").arg(qstrAttachFilePath).toLatin1().data());
                        doc.setValue(TEST_DATA_ATTACH_PATH, "");
                    }

                    doc.endElement();
                }
                doc.endElement();
            }
            doc.endElement();
        }
        doc.endElement();
    }

    if(bTestCountChanged)
    {
        //有改变，则要更新已完成测试项信息
        doc.beginElement(TASK_NODE);
        doc.setValue(TASK_TESTED_DATA, QString::number(dwTestCount));
        doc.endElement();
    }

    doc.endElement();
    doc.save();         //save中已经有了sync

    return true;
}

//更新任务文件里各层级的音频、图片文件路径--added by zhaoyongjun,2018.5.18
void TaskFileIO::updateTaskMediaRelatedOfTaskFile(const QString &strTaskFilePath, const QVector<_ImgMediaPath> &vtDestFilesPath, int cnt)
{
    int iSize = vtDestFilesPath.size();
    if(iSize <= 0)
    {
        qDebug() << "TaskFileIO::updateTaskMediaRelatedOfTaskFile, vector size <= 0." << endl;
        return;
    }

    int iFindCount = 0;
    QString strNumber = "";
    int iIdx = 0;

    XMLDocument doc( strTaskFilePath,QIODevice::ReadWrite,ROOT_NODE );
    const QList<QDomElement> qlBayNode = doc.childElement(BAY_NODE);

    QList<QDomElement>::const_iterator iterBayNode = qlBayNode.begin();
    for(; iterBayNode != qlBayNode.end(); ++iterBayNode)
    {
        doc.beginElement(*iterBayNode);
        strNumber = doc.value(BAY_NUMBER);
        if(!findMatchedRemotePathIndexByRankNumber(strNumber, vtDestFilesPath, cnt, iIdx))
        {
            continue;
        }

        ImgMediaPath stIMPathTmp = vtDestFilesPath.at(iIdx);

        //去除不存在的任务图片文件，避免后期任务上传失败
        if(FileOperUtil::checkFileOrDirExist(stIMPathTmp.strMainImgPath))
        {
            if(!(doc.hasElement(BAY_MAIN_IMAGE_FILE_PATH)))
            {
                doc.addElement(BAY_MAIN_IMAGE_FILE_PATH);
            }
            doc.setValue(TASK_MAIN_IMAGE_FILE_PATH, stIMPathTmp.strMainImgPath);
        }
        else
        {
            if(doc.hasElement(BAY_MAIN_IMAGE_FILE_PATH))
            {
                doc.removeElement(BAY_MAIN_IMAGE_FILE_PATH);
            }
        }

        //去除不存在的任务媒体文件，避免后期任务上传失败
        while(doc.hasElement(BAY_MEDIA_FILE_PATH))
        {
            doc.removeElement(BAY_MEDIA_FILE_PATH);     //clear previous element
        }

        for(QStringList::const_iterator iter = stIMPathTmp.listMediaPath.begin();
            iter != stIMPathTmp.listMediaPath.end(); ++iter)
        {
            if(FileOperUtil::checkFileOrDirExist(*iter))
            {
                //存在才添加
                QDomElement element = doc.addElement(BAY_MEDIA_FILE_PATH);
                doc.beginElement(element);
                doc.setText(*iter);
                doc.endElement();
            }
        }

        //去除不存在的任务图片文件，避免后期任务上传失败
        while(doc.hasElement(BAY_IMAGE_FILE_PATH))
        {
            doc.removeElement(BAY_IMAGE_FILE_PATH);     //clear previous element
        }

        for(QStringList::const_iterator iter = stIMPathTmp.listImgPath.begin();
            iter != stIMPathTmp.listImgPath.end(); ++iter)
        {
            if(FileOperUtil::checkFileOrDirExist(*iter))
            {
                //存在才添加
                QDomElement element = doc.addElement(BAY_IMAGE_FILE_PATH);
                doc.beginElement(element);
                doc.setText(*iter);
                doc.endElement();
            }
        }

        updateDeviceMediaRelatedOfTaskFile(doc, stIMPathTmp.vtSubLevel, stIMPathTmp.iSubCount);
        doc.endElement();

        if((++iFindCount) >= iSize)
        {
            //一般iFindCount的次数不超过vtDestFilesPath的size，
            //当FindCount等于vtDestFilesPath的size时，表示查找完毕，跳出循环
            break;
        }
    }

    doc.save();     // 完成写入
    //#ifdef Q_OS_LINUX
    //    sync();
    //#endif
    return;
}

//更新任务描述文件内的音频、图片文件路径--added by zhaoyongjun,2018.5.18
void TaskFileIO::updateTaskFile(const QString &strTaskFilePath, const ImgMediaPath &stFilesPath)
{
    updateTaskInfoMediaRelatedOfTaskFile(strTaskFilePath, stFilesPath);
    updateTaskMediaRelatedOfTaskFile(strTaskFilePath, stFilesPath.vtSubLevel, stFilesPath.iSubCount);
}

bool TaskFileIO::getImgMediaFilesPath(const QString &strTaskFilePath, ImgMediaPath &stPath) const
{
    bool isHasMediaFilePath = false;
    XMLDocument doc(strTaskFilePath, QIODevice::ReadWrite, ROOT_NODE);
    doc.beginElement(TASK_NODE);
    QString strNumber = doc.value(TASK_TEST_NUMBER);
    stPath.eRank = RANK_ROOT;
    stPath.strRankNumber = strNumber;
    QString strMainImgPath = doc.value(TASK_MAIN_IMAGE_FILE_PATH);
    if(!strMainImgPath.isEmpty())
    {
        stPath.strMainImgPath = strMainImgPath;
        isHasMediaFilePath = true;
    }

    const QList<QDomElement> qlMeidaFileNode = doc.childElement(TASK_MEDIA_FILE_PATH);
    foreach (QDomElement qDocElement, qlMeidaFileNode)
    {
        QString strPath =  qDocElement.text();
        if(!strPath.isEmpty())
        {
            if(!(stPath.listMediaPath.contains(strPath)))
            {
                stPath.listMediaPath.append(strPath);
                isHasMediaFilePath = true;
            }
            //qDebug() << "TaskFileIO::getImgMediaFilesPath, " << "list media path: " << stPath.listMediaPath.at(i) << endl;
        }
    }

    const QList<QDomElement> qlImageFileNode = doc.childElement(TASK_IMAGE_FILE_PATH);
    foreach (QDomElement qDocElement, qlImageFileNode)
    {
        QString strPath =  qDocElement.text();
        if(!strPath.isEmpty())
        {
            if(!(stPath.listImgPath.contains(strPath)))
            {
                stPath.listImgPath.append(strPath);
                isHasMediaFilePath = true;
            }
            //qDebug() << "TaskFileIO::getImgMediaFilesPath, " << "list image path: " << stPath.listImgPath.at(i) << endl;
        }
    }
    doc.endElement();

    const QList<QDomElement> qlSubNode = doc.childElement(BAY_NODE);
    foreach (QDomElement qDocElement, qlSubNode)
    {
        doc.beginElement(qDocElement);
        ImgMediaPath stIMPthTmp;
        if(getBayImgMediaFilesPath(doc, stIMPthTmp))
        {
            stPath.vtSubLevel.append(stIMPthTmp);
            isHasMediaFilePath = true;
        }
        doc.endElement();
    }

    stPath.iSubCount = stPath.vtSubLevel.size();

    return isHasMediaFilePath;
}

/****************************
输入参数:doc -- xmldocument对象
输出参数:stPath---media/image file path链表
功能： 从任务描述文件读取间隔里所有的media/image file path
*****************************/
bool TaskFileIO::getBayImgMediaFilesPath(XMLDocument &doc, ImgMediaPath &stPath) const
{
    bool isHasMediaFilePath = false;
    QString strNumber = doc.value(BAY_NUMBER);
    stPath.eRank = RANK_BAY;
    stPath.strRankNumber = strNumber;
    QString strMainImgPath = doc.value(BAY_MAIN_IMAGE_FILE_PATH);
    if(!strMainImgPath.isEmpty())
    {
        stPath.strMainImgPath = strMainImgPath;
        isHasMediaFilePath = true;
    }

    const QList<QDomElement> qlMeidaFileNode = doc.childElement(BAY_MEDIA_FILE_PATH);
    foreach (QDomElement qDocElement, qlMeidaFileNode)
    {
        QString strPath = qDocElement.text();
        if(!strPath.isEmpty())
        {
            if(!(stPath.listMediaPath.contains(strPath)))
            {
                stPath.listMediaPath.append(strPath);
                isHasMediaFilePath = true;
            }
        }
    }

    const QList<QDomElement> qlImageFileNode = doc.childElement(BAY_IMAGE_FILE_PATH);
    foreach (QDomElement qDocElement, qlImageFileNode)
    {
        QString strPath = qDocElement.text();
        if(!strPath.isEmpty())
        {
            if(!(stPath.listImgPath.contains(strPath)))
            {
                stPath.listImgPath.append(strPath);
                isHasMediaFilePath = true;
            }
        }
    }

    const QList<QDomElement> qlDeviceSubNode = doc.childElement(DEVICE_NODE);
    foreach (QDomElement qDocElement, qlDeviceSubNode)
    {
        doc.beginElement(qDocElement);
        ImgMediaPath stIMPathTmp;
        if(getDeviceImgMediaFilesPath(doc, stIMPathTmp))
        {
            stPath.vtSubLevel.append(stIMPathTmp);
            isHasMediaFilePath = true;
        }
        doc.endElement();
    }

    stPath.iSubCount = stPath.vtSubLevel.size();

    return isHasMediaFilePath;
}

/****************************
输入参数:doc -- xmldocument对象
输出参数:stPath---media/image file path链表
功能： 从任务描述文件读取每个设备节点的media/image file path
*****************************/
bool TaskFileIO::getDeviceImgMediaFilesPath(XMLDocument &doc, ImgMediaPath &stPath) const
{
    bool isHasMediaFilePath = false;
    QString strNumber = doc.value(DEVICE_NUMBER);
    stPath.eRank = RANK_DEVICE;
    stPath.strRankNumber = strNumber;
    QString strMainImgPath = doc.value(DEVICE_MAIN_IMAGE_FILE_PATH);
    if(!strMainImgPath.isEmpty())
    {
        stPath.strMainImgPath = strMainImgPath;
        isHasMediaFilePath = true;
    }

    const QList<QDomElement> qlMeidaFileNode = doc.childElement(DEVICE_MEDIA_FILE_PATH);
    foreach (QDomElement qDocElement, qlMeidaFileNode)
    {
        QString strPath = qDocElement.text();
        if(!strPath.isEmpty())
        {
            if(!(stPath.listMediaPath.contains(strPath)))
            {
                stPath.listMediaPath.append(strPath);
                isHasMediaFilePath = true;
            }
        }
    }

    const QList<QDomElement> qlImageFileNode = doc.childElement(DEVICE_IMAGE_FILE_PATH);
    foreach (QDomElement qDocElement, qlImageFileNode)
    {
        QString strPath = qDocElement.text();
        if(!strPath.isEmpty())
        {
            if(!(stPath.listImgPath.contains(strPath)))
            {
                stPath.listImgPath.append(strPath);
                isHasMediaFilePath = true;
            }
        }
    }

    const QList<QDomElement> qlSubNode = doc.childElement(DEVICE_TEST_POINT);
    foreach (QDomElement qDocElement, qlSubNode)
    {
        doc.beginElement(qDocElement);
        ImgMediaPath stIMPathTmp;
        if(getTestPointImgMediaFilesPath(doc, stIMPathTmp))
        {
            stPath.vtSubLevel.append(stIMPathTmp);
            isHasMediaFilePath = true;
        }
        doc.endElement();
    }

    stPath.iSubCount = stPath.vtSubLevel.size();

    return isHasMediaFilePath;
}

/****************************
输入参数:doc -- xmldocument对象
输出参数:stPath---media/image file path链表
功能： 从任务描述文件读取每个测点节点的media/image file path
*****************************/
bool TaskFileIO::getTestPointImgMediaFilesPath(XMLDocument &doc, ImgMediaPath &stPath) const
{
    bool isHasMediaFilePath = false;
    QString strNumber = doc.value(TEST_POINT_NUMBER);
    stPath.eRank = RANK_TEST_POINT;
    stPath.strRankNumber = strNumber;
    QString strMainImgPath = doc.value(TEST_POINT_MAIN_IMAGE_FILE_PATH);
    if(!strMainImgPath.isEmpty())
    {
        stPath.strMainImgPath = strMainImgPath;
        isHasMediaFilePath = true;
    }

    const QList<QDomElement> qlMeidaFileNode = doc.childElement(TEST_POINT_MEDIA_FILE_PATH);
    foreach (QDomElement qDocElement, qlMeidaFileNode)
    {
        doc.beginElement(qDocElement);
        QString strPath = doc.text();
        if(!strPath.isEmpty())
        {
            if(!(stPath.listMediaPath.contains(strPath)))
            {
                stPath.listMediaPath.append(strPath);
                isHasMediaFilePath = true;
            }
        }
        doc.endElement();
    }

    const QList<QDomElement> qlImageFileNode = doc.childElement(TEST_POINT_IMAGE_FILE_PATH);
    foreach(QDomElement qDocElement, qlImageFileNode)
    {
        doc.beginElement(qDocElement);
        QString strPath = doc.text();
        if(!strPath.isEmpty())
        {
            if(!(stPath.listImgPath.contains(strPath)))
            {
                stPath.listImgPath.append(strPath);
                isHasMediaFilePath = true;
            }
        }
        doc.endElement();
    }

    return isHasMediaFilePath;
}

/************************************************
 * 功能: 把测点层级所有的图片音频信息回写到任务文件
 * 入参：doc -- 任务文件
 *      pTestPoint -- 指向某个测点的对象
 * 返回值：NULL
 ************************************************/
void TaskFileIO::saveTestPointImageReleatedToTaskFile(XMLDocument &doc, ItemTestPoint* pTestPoint)
{
    //去除不存在的任务图片文件，避免后期任务上传失败
    if(!(pTestPoint->strMainImgFilePath.isEmpty()))
    {
        if(FileOperUtil::checkFileOrDirExist(pTestPoint->strMainImgFilePath))
        {
            if(!(doc.hasElement(TEST_POINT_MAIN_IMAGE_FILE_PATH)))
            {
                doc.addElement(TEST_POINT_MAIN_IMAGE_FILE_PATH);
            }
            doc.setValue(TEST_POINT_MAIN_IMAGE_FILE_PATH, pTestPoint->strMainImgFilePath);
        }
        else
        {
            if(doc.hasElement(TEST_POINT_MAIN_IMAGE_FILE_PATH))
            {
                doc.removeElement(TEST_POINT_MAIN_IMAGE_FILE_PATH);
            }
        }
    }

    if(!(pTestPoint->strRemoteMainImgFilePath.isEmpty()))
    {
        if(!doc.hasElement(TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH))
        {
            doc.addElement(TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH);
        }
        doc.setValue( TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH, pTestPoint->strRemoteMainImgFilePath );
    }

    //去除不存在的任务媒体文件，避免后期任务上传失败
    while(doc.hasElement(TEST_POINT_MEDIA_FILE_PATH))
    {
        doc.removeElement(TEST_POINT_MEDIA_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pTestPoint->lMediaFilePath.begin();
        iter != pTestPoint->lMediaFilePath.end(); ++iter)
    {
        if(FileOperUtil::checkFileOrDirExist(*iter))
        {
            //存在才添加
            QDomElement element = doc.addElement(TEST_POINT_MEDIA_FILE_PATH);
            doc.beginElement(element);
            doc.setText(*iter);
            doc.endElement();
        }
    }

    while(doc.hasElement(TEST_POINT_REMOTE_MEDIA_FILE_PATH))
    {
        doc.removeElement(TEST_POINT_REMOTE_MEDIA_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pTestPoint->lRemoteMediaFilePath.begin();
        iter != pTestPoint->lRemoteMediaFilePath.end(); ++iter)
    {
        QDomElement element = doc.addElement(TEST_POINT_REMOTE_MEDIA_FILE_PATH);
        doc.beginElement(element);
        doc.setText(*iter);
        doc.endElement();
    }
    //    foreach (QString qstrPath, pTestPoint->lRemoteMediaFilePath)
    //    {
    //        QDomElement element = doc.addElement(TEST_POINT_REMOTE_MEDIA_FILE_PATH);
    //        doc.beginElement(element);
    //        doc.setText(qstrPath);
    //        doc.endElement();
    //    }

    //去除不存在的任务图片文件，避免后期任务上传失败
    while(doc.hasElement(TEST_POINT_IMAGE_FILE_PATH))
    {
        doc.removeElement(TEST_POINT_IMAGE_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pTestPoint->lImageFilePath.begin();
        iter != pTestPoint->lImageFilePath.end(); ++iter)
    {
        if(FileOperUtil::checkFileOrDirExist(*iter))
        {
            //存在才添加
            QDomElement element = doc.addElement(TEST_POINT_IMAGE_FILE_PATH);
            doc.beginElement(element);
            doc.setText(*iter);
            doc.endElement();
        }
    }

    while(doc.hasElement(TEST_POINT_REMOTE_IMAGE_FILE_PATH))
    {
        doc.removeElement(TEST_POINT_REMOTE_IMAGE_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pTestPoint->lRemoteImageFilePath.begin();
        iter != pTestPoint->lRemoteImageFilePath.end(); ++iter)
    {
        QDomElement element = doc.addElement(TEST_POINT_REMOTE_IMAGE_FILE_PATH);
        doc.beginElement(element);
        doc.setText(*iter);
        doc.endElement();
    }
    //    foreach (QString qstrPath, pTestPoint->lRemoteImageFilePath)
    //    {
    //        QDomElement element = doc.addElement(TEST_POINT_REMOTE_IMAGE_FILE_PATH);
    //        doc.beginElement(element);
    //        doc.setText(qstrPath);
    //        doc.endElement();
    //    }

    return;
}

/************************************************
 * 功能: 把设备层级所有的信息回写到任务文件
 * 入参：doc -- 任务文件
 *      pDevices -- 任务文件里同一间隔下具有相同编号的设备(非虚拟设备，是任务文件里的实际设备)
 * 返回值：NULL
 ************************************************/
void TaskFileIO::saveDeviceFilePathInfoToTaskFile(XMLDocument &doc, const ItemDevice *pDevice)
{
    //去除不存在的任务图片文件，避免后期任务上传失败
    if(!(pDevice->strMainImgFilePath.isEmpty()))
    {
        if(FileOperUtil::checkFileOrDirExist(pDevice->strMainImgFilePath))
        {
            if(!(doc.hasElement(DEVICE_MAIN_IMAGE_FILE_PATH)))
            {
                doc.addElement(DEVICE_MAIN_IMAGE_FILE_PATH);
            }
            doc.setValue(DEVICE_MAIN_IMAGE_FILE_PATH, pDevice->strMainImgFilePath);
        }
        else
        {
            if(doc.hasElement(DEVICE_MAIN_IMAGE_FILE_PATH))
            {
                doc.removeElement(DEVICE_MAIN_IMAGE_FILE_PATH);
            }
        }
    }

    if(!(pDevice->strRemoteMainImgFilePath.isEmpty()))
    {
        if(!doc.hasElement(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH))
        {
            doc.addElement(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH);
        }
        doc.setValue( DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH, pDevice->strRemoteMainImgFilePath );
    }

    //写入负荷电流信息
    doc.setValue(DEVICE_LOAD_CURRENT, QString::number(pDevice->dLoadCurrent));
    doc.setValue(DEVICE_LOAD_CURRENT_UNIT, QString::number((int)(pDevice->eLoadCurrentUnit)));
    doc.setValue(DEVICE_LOAD_CURRENT_SIGN, QString::number(pDevice->dwLoadCurrentSign));

    //去除不存在的任务媒体文件，避免后期任务上传失败
    while(doc.hasElement(DEVICE_MEDIA_FILE_PATH))
    {
        doc.removeElement(DEVICE_MEDIA_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pDevice->lMediaFilePath.begin();
        iter != pDevice->lMediaFilePath.end(); ++iter)
    {
        if(FileOperUtil::checkFileOrDirExist(*iter))
        {
            //存在才添加
            QDomElement element = doc.addElement(DEVICE_MEDIA_FILE_PATH);
            doc.beginElement(element);
            doc.setText(*iter);
            doc.endElement();
        }
    }

    //remote media files path
    while(doc.hasElement(DEVICE_REMOTE_MEDIA_FILE_PATH))
    {
        doc.removeElement(DEVICE_REMOTE_MEDIA_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pDevice->lRemoteMediaFilePath.begin();
        iter != pDevice->lRemoteMediaFilePath.end(); ++iter)
    {
        QDomElement element = doc.addElement(DEVICE_REMOTE_MEDIA_FILE_PATH);
        doc.beginElement(element);
        doc.setText(*iter);
        doc.endElement();
    }
    //    foreach (QString strPath, pDevice->lRemoteMediaFilePath)
    //    {
    //        QDomElement element = doc.addElement(DEVICE_REMOTE_MEDIA_FILE_PATH);
    //        doc.beginElement(element);
    //        doc.setText(strPath);
    //        doc.endElement();
    //    }

    //去除不存在的任务图片文件，避免后期任务上传失败
    while(doc.hasElement(DEVICE_IMAGE_FILE_PATH))
    {
        doc.removeElement(DEVICE_IMAGE_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pDevice->lImageFilePath.begin();
        iter != pDevice->lImageFilePath.end(); ++iter)
    {
        if(FileOperUtil::checkFileOrDirExist(*iter))
        {
            //存在才添加
            QDomElement element = doc.addElement(DEVICE_IMAGE_FILE_PATH);
            doc.beginElement(element);
            doc.setText(*iter);
            doc.endElement();
        }
    }

    //remote image files path
    while(doc.hasElement(DEVICE_REMOTE_IMAGE_FILE_PATH))
    {
        doc.removeElement(DEVICE_REMOTE_IMAGE_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pDevice->lRemoteImageFilePath.begin();
        iter != pDevice->lRemoteImageFilePath.end(); ++iter)
    {
        QDomElement element = doc.addElement(DEVICE_REMOTE_IMAGE_FILE_PATH);
        doc.beginElement(element);
        doc.setText(*iter);
        doc.endElement();
    }
    //    foreach (QString strPath, pDevice->lRemoteImageFilePath)
    //    {
    //        QDomElement element = doc.addElement(DEVICE_REMOTE_IMAGE_FILE_PATH);
    //        doc.beginElement(element);
    //        doc.setText(strPath);
    //        doc.endElement();
    //    }

    return;
}

/************************************************
 * 功能: 把设备层级所有的信息回写到任务文件
 * 入参：doc -- 任务文件
 *      pDevices -- 任务文件里同一间隔下具有相同编号的设备(非虚拟设备，是任务文件里的实际设备)
 * 返回值：NULL
 ************************************************/
void TaskFileIO::saveGISDeviceFilePathInfoToTaskFile(XMLDocument &doc, const ItemDeviceTaskFileInfo *pDevice)
{
    //去除不存在的任务图片文件，避免后期任务上传失败
    if(!(pDevice->strMainImgFilePath.isEmpty()))
    {
        if(FileOperUtil::checkFileOrDirExist(pDevice->strMainImgFilePath))
        {
            if(!(doc.hasElement(DEVICE_MAIN_IMAGE_FILE_PATH)))
            {
                doc.addElement(DEVICE_MAIN_IMAGE_FILE_PATH);
            }
            doc.setValue(DEVICE_MAIN_IMAGE_FILE_PATH, pDevice->strMainImgFilePath);
        }
        else
        {
            if(doc.hasElement(DEVICE_MAIN_IMAGE_FILE_PATH))
            {
                doc.removeElement(DEVICE_MAIN_IMAGE_FILE_PATH);
            }
        }
    }

    if(!(pDevice->strRemoteMainImgFilePath.isEmpty()))
    {
        if(!doc.hasElement(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH))
        {
            doc.addElement(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH);
        }
        doc.setValue( DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH, pDevice->strRemoteMainImgFilePath );
    }

    //写入负荷电流信息
    doc.setValue(DEVICE_LOAD_CURRENT, QString::number(pDevice->dLoadCurrent));
    doc.setValue(DEVICE_LOAD_CURRENT_UNIT, QString::number((int)(pDevice->eLoadCurrentUnit)));
    doc.setValue(DEVICE_LOAD_CURRENT_SIGN, QString::number(pDevice->dwLoadCurrentSign));

    //去除不存在的任务媒体文件，避免后期任务上传失败
    while(doc.hasElement(DEVICE_MEDIA_FILE_PATH))
    {
        doc.removeElement(DEVICE_MEDIA_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pDevice->lMediaFilePath.begin();
        iter != pDevice->lMediaFilePath.end(); ++iter)
    {
        if(FileOperUtil::checkFileOrDirExist(*iter))
        {
            //存在才添加
            QDomElement element = doc.addElement(DEVICE_MEDIA_FILE_PATH);
            doc.beginElement(element);
            doc.setText(*iter);
            doc.endElement();
        }
    }

    //remote media files path
    while(doc.hasElement(DEVICE_REMOTE_MEDIA_FILE_PATH))
    {
        doc.removeElement(DEVICE_REMOTE_MEDIA_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pDevice->lRemoteMediaFilePath.begin();
        iter != pDevice->lRemoteMediaFilePath.end(); ++iter)
    {
        QDomElement element = doc.addElement(DEVICE_REMOTE_MEDIA_FILE_PATH);
        doc.beginElement(element);
        doc.setText(*iter);
        doc.endElement();
    }
    //    foreach (QString qstrPath, pDevice->lRemoteMediaFilePath)
    //    {
    //        QDomElement element = doc.addElement(DEVICE_REMOTE_MEDIA_FILE_PATH);
    //        doc.beginElement(element);
    //        doc.setText(qstrPath);
    //        doc.endElement();
    //    }

    //去除不存在的任务图片文件，避免后期任务上传失败
    while(doc.hasElement(DEVICE_IMAGE_FILE_PATH))
    {
        doc.removeElement(DEVICE_IMAGE_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pDevice->lImageFilePath.begin();
        iter != pDevice->lImageFilePath.end(); ++iter)
    {
        if(FileOperUtil::checkFileOrDirExist(*iter))
        {
            //存在才添加
            QDomElement element = doc.addElement(DEVICE_IMAGE_FILE_PATH);
            doc.beginElement(element);
            doc.setText(*iter);
            doc.endElement();
        }
    }

    //remote image files path
    while(doc.hasElement(DEVICE_REMOTE_IMAGE_FILE_PATH))
    {
        doc.removeElement(DEVICE_REMOTE_IMAGE_FILE_PATH);     //clear previous element
    }

    for(QStringList::const_iterator iter = pDevice->lRemoteImageFilePath.begin();
        iter != pDevice->lRemoteImageFilePath.end(); ++iter)
    {
        QDomElement element = doc.addElement(DEVICE_REMOTE_IMAGE_FILE_PATH);
        doc.beginElement(element);
        doc.setText(*iter);
        doc.endElement();
    }
    //    foreach (QString qstrPath, pDevice->lRemoteImageFilePath)
    //    {
    //        QDomElement element = doc.addElement(DEVICE_REMOTE_IMAGE_FILE_PATH);
    //        doc.beginElement(element);
    //        doc.setText(qstrPath);
    //        doc.endElement();
    //    }

    return;
}

/************************************************
 * 功能: 文件是否有效
 * 入参：strFilePath -- 文件绝对路径
 * 返回值：读写错误类型
 ************************************************/
TaskFileIO::IOStateType TaskFileIO::checkFileValidity( const QString &strFilePath )
{
    TaskFileIO::IOStateType eType = OPERATE_SUCCESS;
    QFileInfo stFileInfo(strFilePath);
    if(stFileInfo.exists())                             // 判断文件是否存在
    {
        QString strSuffix = '.' + stFileInfo.suffix();
        if(strSuffix != PDA_TASK_FILE_SUFFIX)     // 判断文件后缀是否正确
        {
            eType = FILE_TYPE_NOT_VALID;
        }
    }
    else
    {
        eType = FILE_NOT_EXIST;
    }

    return eType;
}

/****************************
输入参数:eType -- 巡检类型
返回值：相应中文字符串
功能： 根据巡检类型获得相应的字符串
*****************************/
QString TaskFileIO::stringFromPatrolType(PDAServiceNS::PatrolType eType)
{
    QString patrolStr = "";
    switch(eType)
    {
    case PDAServiceNS::TRANSFORMER:
    {
        patrolStr = QObject::trUtf8("Transformer");//变压器
        break;
    }
    case PDAServiceNS::CIRCUIT_BREAKER:
    {
        patrolStr = QObject::trUtf8("Circuit Breaker");//断路器
        break;
    }
    case PDAServiceNS::ISLATING_SWITCHER:
    {
        patrolStr = QObject::trUtf8("Isolation Switcher");//隔离开关
        break;
    }
    case PDAServiceNS::KNIFE_GATE:
    {
        patrolStr = QObject::trUtf8("Knife Gate");//刀闸
        break;
    }
    case PDAServiceNS::LIGHTNING_ARRESTER:
    {
        patrolStr = QObject::trUtf8("Lightning Arrester");//避雷器
        break;
    }
    case PDAServiceNS::VOLTAGE_TRANSFORMER:
    {
        patrolStr = QObject::trUtf8("Voltage Transformer");//电压互感器
        break;
    }
    case PDAServiceNS::CURRENT_TRANSFORMER:
    {
        patrolStr = QObject::trUtf8("Current Transformer");//电流互感器
        break;
    }
    case PDAServiceNS::BUS_BAR:
    {
        patrolStr = QObject::trUtf8("Bus Bar");//母线
        break;
    }
    case PDAServiceNS::BUS_COUPLER:
    {
        patrolStr = QObject::trUtf8("Bus Coupler");//母联
        break;
    }
    case PDAServiceNS::SWITCH_CABINET:
    {
        patrolStr = QObject::trUtf8("Switchgear");//开关柜
        break;
    }
    case PDAServiceNS::POWER_CABLE:
    {
        patrolStr = QObject::trUtf8("Cable");//电力电缆
        break;
    }
    case PDAServiceNS::LIGHTNING_ROD:
    {
        patrolStr = QObject::trUtf8("Lightning Rod");//避雷针
        break;
    }
    case PDAServiceNS::WALL_BUSHING:
    {
        patrolStr = QObject::trUtf8("Wall Bushing");//穿墙套管
        break;
    }
    case PDAServiceNS::ELECTRIC_RECATOR:
    {
        patrolStr = QObject::trUtf8("Reactor");//电抗器
        break;
    }
    case PDAServiceNS::ELECTRIC_CONDUCTOR:
    {
        patrolStr = QObject::trUtf8("Conductor");//电力导线
        break;
    }
    case PDAServiceNS::POWER_CAPACITOR:
    {
        patrolStr = QObject::trUtf8("Capacitor");//电力电容器
        break;
    }
    case PDAServiceNS::DISCHARGE_COIL:
    {
        patrolStr = QObject::trUtf8("Discharge Coil");//放电线圈
        break;
    }
    case PDAServiceNS::LOAD_SWITCHER:
    {
        patrolStr = QObject::trUtf8("Load Switcher");//负荷开关
        break;
    }
    case PDAServiceNS::GROUND_CHANGE:
    {
        patrolStr = QObject::trUtf8("Grounding Transformer");//接地变
        break;
    }
    case PDAServiceNS::GROUND_RESISTANCE:
    {
        patrolStr = QObject::trUtf8("Grounding Resistor");//接地电阻
        break;
    }
    case PDAServiceNS::GROUND_GRID:
    {
        patrolStr = QObject::trUtf8("Grounding Grid");//接地网
        break;
    }
    case PDAServiceNS::COMBINED_FILTER:
    {
        patrolStr = QObject::trUtf8("Combined Filter");//结合滤波器
        break;
    }
    case PDAServiceNS::POWER_INSULATOR:
    {
        patrolStr = QObject::trUtf8("Insulator");//绝缘子
        break;
    }
    case PDAServiceNS::COUPLING_CAPACITOR:
    {
        patrolStr = QObject::trUtf8("Coupling Capacitor");//耦合电容器
        break;
    }
    case PDAServiceNS::POWER_CABINET:
    {
        patrolStr = QObject::trUtf8("Power Cabinet");//屏柜
        break;
    }
    case PDAServiceNS::OTHER_DEVICE:
    {
        patrolStr = QObject::trUtf8("Other Device");//其他
        break;
    }
    case PDAServiceNS::POWER_FUSE:
    {
        patrolStr = QObject::trUtf8("Power Fuse");//熔断器
        break;
    }
    case PDAServiceNS::CHANGE_USED:
    {
        patrolStr = QObject::trUtf8("Spot Transformer");//所用变
        break;
    }
    case PDAServiceNS::ARC_SUPPRESSION_DEVICE:
    {
        patrolStr = QObject::trUtf8("Arc Suppression Device");//消弧装置
        break;
    }
    case PDAServiceNS::WAVE_BLOCKER:
    {
        patrolStr = QObject::trUtf8("Blocker");//阻波器
        break;
    }
    case PDAServiceNS::COMBINED_ELECTRIC:
    {
        patrolStr = QObject::trUtf8("GIS");//组合电器
        break;
    }
    case PDAServiceNS::COMBINED_TRANSFORMER:
    {
        patrolStr = QObject::trUtf8("Combined Transformer");//组合互感器
        break;
    }
    default:
    {
        logWarning(QString("unknown patrol type %1.").arg(eType));
        patrolStr = QObject::trUtf8("Unknown");
        break;
    }
    }

    return patrolStr;
}

/****************************
输入参数:qstrPatrolInfo -- 巡检类型字符串信息
返回值：巡检类型
功能： 根据字符串信息获得巡检类型
*****************************/
PDAServiceNS::PatrolType TaskFileIO::patrolTypeFromString(QString qstrPatrolInfo)
{
    PDAServiceNS::PatrolType eType = PDAServiceNS::PATROL_INVALID;

    if("开关柜" == qstrPatrolInfo || "Switchgear" == qstrPatrolInfo)
    {
        eType = PDAServiceNS::SWITCH_CABINET;
    }
    else if("变压器" == qstrPatrolInfo || "Transformer" == qstrPatrolInfo)
    {
        eType = PDAServiceNS::TRANSFORMER;
    }
    else if("电力电缆" == qstrPatrolInfo || "Cable" == qstrPatrolInfo)
    {
        eType = PDAServiceNS::POWER_CABLE;
    }
    else if("组合电器" == qstrPatrolInfo || "GIS" == qstrPatrolInfo)
    {
        eType = PDAServiceNS::COMBINED_ELECTRIC;
    }
    else
    {
        logWarning(QString("unknown patrol type: %1").arg(qstrPatrolInfo));
    }

    return eType;
}

/****************************
输入参数:qstrTestInfo -- 测试类型字符串信息
返回值：测试类型
功能： 根据字符串信息获得测试类型
*****************************/
PDAServiceNS::TestPointType TaskFileIO::testTypeFromString(QString qstrTestInfo)
{
    PDAServiceNS::TestPointType eType = PDAServiceNS::TEV_TYPE;

    if("暂态地" == qstrTestInfo || "TEV" == qstrTestInfo)
    {
        eType = PDAServiceNS::TEV_TYPE;
    }
    else if("超声波" == qstrTestInfo || "AE" == qstrTestInfo)
    {
        eType = PDAServiceNS::AE_TYPE;
    }
    else if("特高频" == qstrTestInfo || "UHF" == qstrTestInfo)
    {
        eType = PDAServiceNS::UHF_TYPE;
    }
    else if("高频电流" == qstrTestInfo || "HFCT" == qstrTestInfo)
    {
        eType = PDAServiceNS::HFCT_TYPE;
    }
    else if("红外" == qstrTestInfo || "Infrared" == qstrTestInfo)
    {
        eType = PDAServiceNS::INFRARED_TYPE;
    }
    else if("电流分析" == qstrTestInfo || "CA" == qstrTestInfo)
    {
        eType = PDAServiceNS::CA_TYPE;
    }
    else if("电缆外护层接地" == qstrTestInfo || "CableGround" == qstrTestInfo)
    {
        eType = PDAServiceNS::CABLEGROUND_TYPE;
    }
    else if("变压器铁芯接地" == qstrTestInfo || "CoreGround" == qstrTestInfo)
    {
        eType = PDAServiceNS::COREGOUND_TYPE;
    }
    else
    {
        logWarning(QString("unknown patrol type: %1").arg(qstrTestInfo));
    }

    return eType;
}

/****************************
输入参数:eType -- 测点类型
返回值：相应中文字符串
功能： 根据测点类型获得相应的字符串
*****************************/
QString TaskFileIO::stringFromTestType(PDAServiceNS::TestPointType eType)
{
    QString testPointStr = "";
    switch(eType)
    {
    case PDAServiceNS::TEV_TYPE:
    {
        testPointStr = "TEV";
        break;
    }
    case PDAServiceNS::AE_TYPE:
    {
        testPointStr = "AE";
        break;
    }
    case PDAServiceNS::UHF_TYPE:
    {
        testPointStr = "UHF";
        break;
    }
    case PDAServiceNS::HFCT_TYPE:
    {
        testPointStr = "HFCT";
        break;
    }
    case PDAServiceNS::INFRARED_TYPE:
    {
        testPointStr = "Infrared";
        break;
    }
    case PDAServiceNS::CA_TYPE:
    {
        testPointStr = "CA";
        break;
    }
    case PDAServiceNS::CABLEGROUND_TYPE:
    {
        testPointStr = "Current";
        break;
    }
    case PDAServiceNS::COREGOUND_TYPE:
    {
        testPointStr = "CoreGround";
        break;
    }
    default:
    {
        logWarning("unknown test point type.");
        testPointStr = "Unknown";
        break;
    }
    }
    return testPointStr;
}

/****************************
输入参数:strPatrolType -- 巡检类型对应的字符串
        vPatrolTypes -- 巡检类型的集合
返回值：序号，非法值为ERROR_INDEX
功能： 获取巡检类型的集合中存在指定巡检类型的序号
*****************************/
int TaskFileIO::indexOfPatrolType( const QString& strPatrolType,const QVector<ItemPatrolType> &vPatrolTypes )
{
    int iIndex = ERROR_INDEX;
    for(int i = 0, iSize = vPatrolTypes.size(); i < iSize; ++i)
    {
        if( strPatrolType == vPatrolTypes[i].strItemName )
        {
            iIndex = i;
            break;
        }
    }
    return iIndex;
}

/****************************
输入参数:strTestType -- 测点类型对应的字符串
        vTestTypes -- 测点类型的集合
返回值：序号，非法值为ERROR_INDEX
功能： 获取测点类型的集合中是否存在指定测点类型的序号
*****************************/
int TaskFileIO::indexOfTestType( const QString& strTestTypeName, double dVoltage, PDAServiceNS::TaskUnitCode eVoltageUnit, const QVector<ItemTestType> &vTestTypes  )
{
    Q_UNUSED(dVoltage);
    Q_UNUSED(eVoltageUnit);
    int iIndex = ERROR_INDEX;
    for(int i = 0, iSize = vTestTypes.size(); i < iSize; ++i)
    {
        if(strTestTypeName == vTestTypes[i].strItemName/* && eVoltageUnit == vTestTypes[i].eVoltageUnit && dVoltage == vTestTypes[i].dVoltage*/)
        {
            iIndex = i;
            break;
        }
    }
    return iIndex;
}

/****************************
输入参数:strTestType -- 测点类型对应的字符串(ae/tev/uhf/hfct)
        vTestTypes -- 测点类型的集合
输出参数:        lIndex---相同测点类型的索引集合
功能： 获取测点类型的集合中具有相同类型的测点类型索引集合
*****************************/
void TaskFileIO::indexListWithSameTestType( const QString &strTestType, const QVector<ItemTestType> &vTestTypes, QList<int> &lIndex)
{
    QString strTestTypeName = "";
    int iPos = 0;
    int iIdx = 0;
    QVector<ItemTestType>::const_iterator iter = vTestTypes.begin();
    for (; iter != vTestTypes.end(); ++iter)
    {
        strTestTypeName = iter->strItemName;
        iPos = strTestTypeName.lastIndexOf("_");
        strTestTypeName = strTestTypeName.mid(0, iPos);
        if( strTestType == strTestTypeName)
        {
            lIndex.append(iIdx);
        }
        ++iIdx;
    }

    return;
}

/****************************
输入参数:doc -- 自定义xml文件
        strFilePath -- 文件路径
出参：sTaskInfo -- 任务概要信息
功能： 从xml文件中提取任务概要信息（抽象依据，多次调用）,添加到指定出参中
added by zhaoyongjun, 2018.5.16
*****************************/
void TaskFileIO::readTaskInfo( TaskInfo &sTaskInfo, XMLDocument& doc, const QString &strFilePath)
{
    sTaskInfo.strFilePath = strFilePath;
    sTaskInfo.isDownloaded = true;

    setCurTaskIniFolder(sTaskInfo.strFilePath);

    doc.beginElement(TASK_NODE);
    sTaskInfo.uiTotalCount = doc.value( TASK_TOTAL_DATA ).toUInt();
    sTaskInfo.uiTestedCount = doc.value( TASK_TESTED_DATA ).toUInt();
    sTaskInfo.eSourcePlatform = (PDAServiceNS::PDASourcePlatform) doc.value( TEST_SOURCE_PLATFORM ).toUShort();
    sTaskInfo.strTestNumber = doc.value( TASK_TEST_NUMBER );
    sTaskInfo.strInnerId = doc.value( TASK_INNER_ID );
    sTaskInfo.strItemName = doc.value( TASK_NAME );
    sTaskInfo.lCreateTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(doc.value(TASK_CREATE_TIME).toLongLong());  //UTC时间(s)，实际app使用的ms，因为版本已经发布，因而适配为ms

    QDateTime stCreateTime = QDateTime::fromTime_t((uint)(sTaskInfo.lCreateTime / 1000));           //计算值单位为s计算
    sTaskInfo.strCreateTime = stCreateTime.toString(TASK_TIME_FORMAT);

    sTaskInfo.lPlanTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(doc.value(TASK_PLAN_TIME).toLongLong());
    sTaskInfo.lTestTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(doc.value(TASK_TEST_TIME).toLongLong());

    QDateTime stPlanTime = QDateTime::fromTime_t((uint)(sTaskInfo.lPlanTime / 1000));           //计算值单位为s计算
    sTaskInfo.strPlanTime = stPlanTime.toString(TASK_TIME_FORMAT);

    QDateTime stTestTime = QDateTime::fromTime_t((uint)(sTaskInfo.lTestTime / 1000));           //计算值单位为s计算
    sTaskInfo.strTestTime = stTestTime.toString(TASK_TIME_FORMAT);

    sTaskInfo.strStationNo = doc.value( STATION_NUMBER );
    sTaskInfo.strStationName = doc.value( STATION_NAME );

    sTaskInfo.dVoltage = doc.value( TASK_VOLTAGE ).toDouble();
    sTaskInfo.eVoltageUnit = (PDAServiceNS::TaskUnitCode) doc.value( TASK_VOLTAGE_UNIT ).toUShort();

    sTaskInfo.stWeatherInfo.eWeather = (PDAServiceNS::Weather)(doc.value(TASK_WEATHER).toUShort());
    sTaskInfo.stWeatherInfo.dTemperature = doc.value(TASK_TEMPERATURE).toDouble();
    sTaskInfo.eTempUnit = (PDAServiceNS::DataUnit)(doc.value(TASK_TEMPERATURE_UNIT).toUShort());
    sTaskInfo.stWeatherInfo.dHumidity = doc.value(TASK_HUMIDITY).toDouble();

    //同步缓存信息
    QString qstrTaskTmpFilePath = getCurTaskIniFolder() + sTaskInfo.strInnerId + PDA_TASK_TEMP_INI_SUFFIX;
    TaskTmpInfo stTaskTmpInfo;
    if(IniConfig::readTaskTmpInfo(qstrTaskTmpFilePath, stTaskTmpInfo))
    {
        if(sTaskInfo.strInnerId == stTaskTmpInfo.qstrInnerId)
        {
            sTaskInfo.uiTotalCount = static_cast<unsigned int>(stTaskTmpInfo.qi32TotalTestData);
            sTaskInfo.uiTestedCount = static_cast<unsigned int>(stTaskTmpInfo.qi32TestedTestData);
            sTaskInfo.lTestTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(stTaskTmpInfo.qi64TestTime);

            QDateTime stTmpTestTime = QDateTime::fromTime_t((uint)(sTaskInfo.lTestTime / 1000));           //计算值单位为s计算
            sTaskInfo.strTestTime = stTmpTestTime.toString(TASK_TIME_FORMAT);

            doc.setValue(TASK_TOTAL_DATA, QString::number(sTaskInfo.uiTotalCount));
            doc.setValue(TASK_TESTED_DATA, QString::number(sTaskInfo.uiTestedCount));
            doc.setValue(TASK_TEST_TIME, QString::number(TimezoneManager::instance()->formatLocalTimeToUTCValMsec(sTaskInfo.lTestTime)));

            if(doc.save())
            {
                FileOperUtil::deleteFile(qstrTaskTmpFilePath);
                logInfo(QString("delete temp file: %1").arg(qstrTaskTmpFilePath));
            }
        }
    }

    sTaskInfo.usBayCount = doc.value( TASK_BAY_COUNT ).toUShort();
    sTaskInfo.strMainImgFilePath = doc.value( TASK_MAIN_IMAGE_FILE_PATH );
    if(doc.hasElement(TASK_REMOTE_MAIN_IMAGE_FILE_PATH))
    {
        sTaskInfo.strRemoteMainImgFilePath = doc.value( TASK_REMOTE_MAIN_IMAGE_FILE_PATH );
    }

    QList<QDomElement> qlMediaFilePath = doc.childElement(TASK_MEDIA_FILE_PATH);
    for(QList<QDomElement>::iterator iter = qlMediaFilePath.begin();
        iter != qlMediaFilePath.end(); ++iter)
    {
        doc.beginElement(*iter);
        if(!(sTaskInfo.lMediaFilePath.contains(doc.text())))
        {
            sTaskInfo.lMediaFilePath.append(doc.text());
        }
        doc.endElement();
    }

    QList<QDomElement> qlRemoteMediaFilePath = doc.childElement(TASK_REMOTE_MEDIA_FILE_PATH);
    for(QList<QDomElement>::iterator iter = qlRemoteMediaFilePath.begin();
        iter != qlRemoteMediaFilePath.end(); ++iter)
    {
        doc.beginElement(*iter);
        if(!(sTaskInfo.lRemoteMediaFilePath.contains(doc.text())))
        {
            sTaskInfo.lRemoteMediaFilePath.append(doc.text());
        }
        doc.endElement();
    }

    QList<QDomElement> qlImageFilePath = doc.childElement(TASK_IMAGE_FILE_PATH);
    for(QList<QDomElement>::iterator iter = qlImageFilePath.begin();
        iter != qlImageFilePath.end(); ++iter)
    {
        doc.beginElement(*iter);
        if(!(sTaskInfo.lImageFilePath.contains(doc.text())))
        {
            sTaskInfo.lImageFilePath.append(doc.text());
        }
        doc.endElement();
    }

    QList<QDomElement> qlRemoteImageFilePath = doc.childElement(TASK_REMOTE_IMAGE_FILE_PATH);
    for(QList<QDomElement>::iterator iter = qlRemoteImageFilePath.begin();
        iter != qlRemoteImageFilePath.end(); ++iter)
    {
        doc.beginElement(*iter);
        if(!(sTaskInfo.lRemoteImageFilePath.contains(doc.text())))
        {
            sTaskInfo.lRemoteImageFilePath.append(doc.text());
        }
        doc.endElement();
    }

    doc.endElement();

    return;
}

/****************************
输入参数:doc -- 自定义xml文件
        lTestDataElement -- 节点的集合
出参：测点
功能： 从xml文件中提取测试项信息添加到测点
*****************************/
void TaskFileIO::addTestDataToPoint( const QList<QDomElement>& lTestDataElement,XMLDocument& doc,ItemTestPoint &tmpPoint )
{
    quint16 qui16LastIndex = 0;
    for (QList<QDomElement>::const_iterator iterData = lTestDataElement.begin();
         iterData != lTestDataElement.end(); ++iterData)
    {
        doc.beginElement(*iterData);

        ItemTestData tmpTestData;
        tmpTestData.usIndex = doc.value( TEST_DATA_INDEX ).toUShort();
        if(tmpTestData.usIndex <= qui16LastIndex)
        {
            tmpTestData.usIndex = qui16LastIndex + 1;
        }

        qui16LastIndex = tmpTestData.usIndex;

        tmpTestData.strNumber = doc.value( TEST_DATA_NUMBER );
        tmpTestData.bTested = (bool)doc.value( TEST_DATA_IS_TEST ).toInt();
        tmpTestData.strFileName = doc.value( TEST_DATA_FILE_NAME );
        tmpTestData.strFilePath = doc.value( TEST_DATA_FILE_PATH );
        tmpTestData.strAttachPath = doc.value( TEST_DATA_ATTACH_PATH );
        tmpTestData.eDataType = (PDAServiceNS::TestDataType)doc.value( TEST_DATA_TYPE ).toInt();
        tmpTestData.bIsBgn = (bool)doc.value( TEST_DATA_BGN ).toInt();
        if(doc.hasElement(TEST_DATA_REMOTE_PATH))
        {
            tmpTestData.strRemotePath = doc.value( TEST_DATA_REMOTE_PATH );
        }

        if(doc.hasElement(TEST_DATA_REMOTE_ATTACH_PATH))
        {
            tmpTestData.strRemoteAttachPath = doc.value( TEST_DATA_REMOTE_ATTACH_PATH );
        }

        tmpTestData.eBandWidth = (PDAServiceNS::UhfBWType)(doc.value( TEST_DATA_BAND_WIDTH ).toInt());
        tmpTestData.eDataUnit = (PDAServiceNS::DataUnit)(doc.value(TEST_DATA_DATA_UNIT).toInt());

        //同步缓存信息
        QString qstrTestDataTmpFilePath = getCurTaskIniFolder() + tmpTestData.strNumber + PDA_TASK_TEMP_INI_SUFFIX;
        TestDataTmpInfo stTestDataTmpInfo;
        if(IniConfig::readTestDataTmpInfo(qstrTestDataTmpFilePath, stTestDataTmpInfo))
        {
            if(tmpTestData.strNumber == stTestDataTmpInfo.qstrNumber)
            {
                tmpTestData.bTested = stTestDataTmpInfo.bTested;
                tmpTestData.strFileName = stTestDataTmpInfo.qstrFileName;
                tmpTestData.strFilePath = stTestDataTmpInfo.qstrFilePath;
                tmpTestData.strAttachPath = stTestDataTmpInfo.qstrAttachPath;
                tmpTestData.strRemotePath = stTestDataTmpInfo.qstrRemotePath;
                tmpTestData.strRemoteAttachPath = stTestDataTmpInfo.qstrRemoteAttachPath;

                doc.setValue(TEST_DATA_IS_TEST, QString::number(tmpTestData.bTested));
                doc.setValue(TEST_DATA_FILE_NAME, tmpTestData.strFileName);
                doc.setValue(TEST_DATA_FILE_PATH, tmpTestData.strFilePath);
                doc.setValue(TEST_DATA_ATTACH_PATH, tmpTestData.strAttachPath);

                if(doc.hasElement(TEST_DATA_REMOTE_PATH))
                {
                    doc.setValue(TEST_DATA_REMOTE_PATH, tmpTestData.strRemotePath);
                }

                if(doc.hasElement(TEST_DATA_REMOTE_ATTACH_PATH))
                {
                    doc.setValue(TEST_DATA_REMOTE_ATTACH_PATH, tmpTestData.strRemoteAttachPath);
                }

                //if(doc.save())
                //{
                    FileOperUtil::deleteFile(qstrTestDataTmpFilePath);
                    logInfo(QString("delete temp file: %1").arg(qstrTestDataTmpFilePath));
                //}
            }
        }

        tmpPoint.testDatas.append(tmpTestData);

        doc.endElement();

        //printTestDataInfo(tmpTestData);
    }

    //同步新增的测试项信息
    QStringList qslstFilePaths = FileOperUtil::getFileListUnderFolder(getCurTaskIniFolder());
    for(int i = 0, iSize = qslstFilePaths.size(); i < iSize; ++i)
    {
        //logInfo(qslstFilePaths[i]);
        TestDataTmpInfo stTestDataTmpInfo;
        if(IniConfig::readTestDataTmpInfo(qslstFilePaths[i], stTestDataTmpInfo))
        {
            if(tmpPoint.strItemNumber == stTestDataTmpInfo.qstrPointNumber &&
                    tmpPoint.eType == stTestDataTmpInfo.qi32PointType &&
                    tmpPoint.eTestPosition == stTestDataTmpInfo.qi32PointPos)
            {
                ItemTestData tmpTestData;
                tmpTestData.usIndex = stTestDataTmpInfo.qui16Index;
                tmpTestData.strNumber = stTestDataTmpInfo.qstrNumber;
                tmpTestData.bIsBgn = stTestDataTmpInfo.bIsBgn;
                tmpTestData.bTested = stTestDataTmpInfo.bTested;
                tmpTestData.eBandWidth = static_cast<PDAServiceNS::UhfBWType>(stTestDataTmpInfo.qi32BandWidth);
                tmpTestData.eDataUnit = static_cast<PDAServiceNS::DataUnit>(stTestDataTmpInfo.qi32Unit);
                tmpTestData.eDataType = static_cast<PDAServiceNS::TestDataType>(stTestDataTmpInfo.qi32DataType);
                tmpTestData.strFileName = stTestDataTmpInfo.qstrFileName;
                tmpTestData.strFilePath = stTestDataTmpInfo.qstrFilePath;
                tmpTestData.strAttachPath = stTestDataTmpInfo.qstrAttachPath;
                tmpTestData.strRemotePath = stTestDataTmpInfo.qstrRemotePath;
                tmpTestData.strRemoteAttachPath = stTestDataTmpInfo.qstrRemoteAttachPath;

                QDomElement elementTestData = doc.addElement(TEST_DATA);
                doc.beginElement(elementTestData);

                doc.setValue(TEST_DATA_INDEX, QString::number(tmpTestData.usIndex));
                doc.setValue(TEST_DATA_NUMBER, tmpTestData.strNumber);
                doc.setValue(TEST_DATA_TYPE, QString::number(tmpTestData.eDataType));
                doc.setValue(TEST_DATA_BGN, QString::number(tmpTestData.bIsBgn));
                doc.setValue(TEST_DATA_BAND_WIDTH, QString::number(tmpTestData.eBandWidth));
                doc.setValue(TEST_DATA_DATA_UNIT, QString::number(tmpTestData.eDataUnit));
                doc.setValue(TEST_DATA_IS_TEST, QString::number(tmpTestData.bTested));
                doc.setValue(TEST_DATA_FILE_NAME, tmpTestData.strFileName);
                doc.setValue(TEST_DATA_FILE_PATH, tmpTestData.strFilePath);
                doc.setValue(TEST_DATA_ATTACH_PATH, tmpTestData.strAttachPath);

                if(!(tmpTestData.strRemotePath.isEmpty()))
                {
                    if(!(doc.hasElement(TEST_DATA_REMOTE_PATH)))
                    {
                        doc.addElement(TEST_DATA_REMOTE_PATH);
                    }
                    doc.setValue(TEST_DATA_REMOTE_PATH, tmpTestData.strRemotePath);
                }

                if(!(tmpTestData.strRemoteAttachPath.isEmpty()))
                {
                    if(!(doc.hasElement(TEST_DATA_REMOTE_ATTACH_PATH)))
                    {
                        doc.addElement(TEST_DATA_REMOTE_ATTACH_PATH);
                    }
                    doc.setValue(TEST_DATA_REMOTE_ATTACH_PATH, tmpTestData.strRemoteAttachPath);
                }

                doc.endElement();

                //if(doc.save())
                //{
                    tmpPoint.testDatas.append(tmpTestData);
                    FileOperUtil::deleteFile(qslstFilePaths[i]);
                    logInfo(QString("delete temp file: %1").arg(qslstFilePaths[i]));
                //}
            }
        }
    }

    //sort
    qSort(tmpPoint.testDatas.begin(), tmpPoint.testDatas.end(), testDataSort);

    return;
}

/****************************
功能： 打印测试项信息
*****************************/
void TaskFileIO::printTestDataInfo(ItemTestData &tmpTestData)
{
    qDebug()<<"TaskFileIO::printTestDataInfo, tmpTestData.usIndex:"<<tmpTestData.usIndex;
    qDebug()<<"TaskFileIO::printTestDataInfo, tmpTestData.strNumber:"<<tmpTestData.strNumber;
    qDebug()<<"TaskFileIO::printTestDataInfo, tmpTestData.eDataType:"<<tmpTestData.eDataType;
    qDebug()<<"TaskFileIO::printTestDataInfo, tmpTestData.bTested:"<<tmpTestData.bTested;
    qDebug()<<"TaskFileIO::printTestDataInfo, tmpTestData.bIsBgn:"<<tmpTestData.bIsBgn;
    qDebug()<<"TaskFileIO::printTestDataInfo, tmpTestData.strFileName:"<<tmpTestData.strFileName;
    qDebug()<<"TaskFileIO::printTestDataInfo, tmpTestData.strFilePath:"<<tmpTestData.strFilePath;
    qDebug()<<"TaskFileIO::printTestDataInfo, tmpTestData.strRemotePath:"<<tmpTestData.strRemotePath;
}

/****************************
输入参数:lActualKeys -- 存放测点类型（测试类型+电压等级）的list
        stType -- 测试类型
        eVoltageLevel -- 电压等级
出参：NULL
功能： 判断lActualKeys是否已存在指定的测点类型(测试类型+电压等级)
*****************************/
bool TaskFileIO::isTestPointVoltageTypeExist(const QList<TestPointVoltageType> &lActualKeys, PDAServiceNS::TestPointType eType, double dVoltage, PDAServiceNS::TaskUnitCode eUnit)
{
    for (QList<TestPointVoltageType>::const_iterator iter = lActualKeys.begin();
         iter != lActualKeys.end(); ++iter)
    {
        if(iter->eType == eType && iter->eVoltageUnit == eUnit && iter->dVoltage == dVoltage)
        {
            return true;
        }
    }

    return false;
}

/****************************
输入参数:testPointList -- 存放测点的list
        stType -- 测试类型
        eVoltageLevel -- 电压等级
出参：lTestPoint---具有相同测点类型(测试类型+电压等级)的测点list
功能： 找出具有相同测点类型(测试类型+电压等级)的测点
*****************************/
void TaskFileIO::testPointListWithSameTypeVoltage(const QList<ItemTestPoint> &testPointList,PDAServiceNS::TestPointType eType,
                                                  double dVoltage, PDAServiceNS::TaskUnitCode eVoltageUnit, QList<ItemTestPoint> &lTestPoint)
{
    for (QList<ItemTestPoint>::const_iterator iter = testPointList.begin();
         iter != testPointList.end(); ++iter)
    {
        if(iter->eType == eType && iter->dVoltage == dVoltage && iter->eVoltageUnit == eVoltageUnit)
        {
            lTestPoint.append(*iter);
        }
    }

    return;
}

/****************************
输入参数: devices---判断某个测试类型对象里已有的gis device
        strBayNumber---test type里device的number（bay number）
输出参数:matchIndex---匹配的gis device index
功能： 判断某个测试类型对象里是否已存在给定device number的GIS device
*****************************/
bool TaskFileIO::isGISItemDeviceExistInTestType(const QVector<ItemDevice> &devices, const QString &strBayNumber, int &matchIndex)
{
    for(int i = 0, iSize = devices.size(); i < iSize; ++i)
    {
        if(devices.at(i).strDevNum == strBayNumber)
        {
            matchIndex = i;
            return true;
        }
    }

    return false;
}

/****************************
输入参数: stSourceDevice---源设备
        strDevName---目标设备的设备名
        strDevNum---目标设备的设备编号
输出参数:stDestDevice---目标设备
功能： 拷贝一个device, 不含test point信息
*****************************/
void TaskFileIO::InitializeItemDeviceWithDeviceInfo(const taskfileioNS::device_info &stSourceDevice, ItemDevice &stDestDevice, const QString &strDevName, const QString &strDevNum)
{
    //初始化设备
    stDestDevice.strItemName = strDevName;//设备名称
    stDestDevice.strDevNum = strDevNum;//设备编号
    stDestDevice.strSourceId = stSourceDevice.strSourceId;//设备标识号
    stDestDevice.iDevType = stSourceDevice.iDevType;
    stDestDevice.strType = stSourceDevice.strType;//设备类型
    stDestDevice.ucGroupName = stSourceDevice.ucGroupName;//组名
    stDestDevice.dVoltage = stSourceDevice.dVoltage;//电压等级
    stDestDevice.eVoltageUnit = stSourceDevice.eVoltageUnit;//电压单位
    stDestDevice.dLoadCurrent = stSourceDevice.dLoadCurrent;//负荷电流
    stDestDevice.eLoadCurrentUnit = stSourceDevice.eLoadCurrentUnit;//负荷电流单位
    stDestDevice.dwLoadCurrentSign = stSourceDevice.dwLoadCurrentSign;//负荷电流输入标识
    stDestDevice.strDevModel = stSourceDevice.strDevModel;//设备型号
    stDestDevice.strManufacturer = stSourceDevice.strManufacturer;//生产厂家
    stDestDevice.strProduceDate = stSourceDevice.strProduceDate;//出厂日期
    //先new出的device,下面2个字段需要使用初始值
    stDestDevice.uiTestedCount = 0;
    stDestDevice.uiTotalCount = 0;
    stDestDevice.b_isBGNTest = stSourceDevice.b_isBGNTest;
    stDestDevice.usIndex = stSourceDevice.usIndex;
    stDestDevice.eFilterSign = stSourceDevice.eFilterSign;
    stDestDevice.strMainImgFilePath = stSourceDevice.strMainImgFilePath;
    stDestDevice.strRemoteMainImgFilePath = stSourceDevice.strRemoteMainImgFilePath;
    stDestDevice.lMediaFilePath = stSourceDevice.lMediaFilePath;
    stDestDevice.lRemoteMediaFilePath = stSourceDevice.lRemoteMediaFilePath;
    stDestDevice.lImageFilePath = stSourceDevice.lImageFilePath;
    stDestDevice.lRemoteImageFilePath = stSourceDevice.lRemoteImageFilePath;
    return;
}

/****************************
输入参数:testPointList -- 测点列表对象
        stBay -- 间隔对象
        stDev -- 设备对象
        strRealDevName---设备真实的设备名
        strRealDevNum---设备真实的编号
出参：pItemPatrolType -- 巡检类型
功能： 将根据测点类型进行分类的测点集合，映射成不同设备添加到巡检类型中去
*****************************/
void TaskFileIO::addBayDevicesToPatrolType( const QList<ItemTestPoint> &testPointList,
                                            ItemBay &stBay,
                                            const taskfileioNS::device_info &stDev,
                                            ItemPatrolType* pItemPatrolType,
                                            const QString &strRealDevName,
                                            const QString &strRealDevNum)
{
    //QList<PDAServiceNS::TestPointType> lKeys = hash.keys();         //测点类型的集合
    QList<TestPointVoltageType> lActualKeys;                 //存在一对多，避免重复，将不重复的键值筛选出来
    for (QList<ItemTestPoint>::const_iterator iterPoint = testPointList.begin();
         iterPoint != testPointList.end(); ++iterPoint)
    {
        if(!isTestPointVoltageTypeExist(lActualKeys, iterPoint->eType, iterPoint->dVoltage, iterPoint->eVoltageUnit))
        {
            TestPointVoltageType eType;
            eType.eType = iterPoint->eType;
            eType.dVoltage = iterPoint->dVoltage;
            eType.eVoltageUnit = iterPoint->eVoltageUnit;
            lActualKeys.append(eType);
        }
        else
        {
            //already contains
        }
    }

    int matchedIndex = 0;
    QString strTestFile = "";
    // 同种测点类型生成相应设备
    for (QList<TestPointVoltageType>::iterator iterKey = lActualKeys.begin();
         iterKey != lActualKeys.end(); ++iterKey)
    {
        //获取相应的测试类型 todo, pItemTestType should be distinguished by data type and voltage
        ItemTestType* pItemTestType = itemTestType(iterKey->eType, iterKey->dVoltage, iterKey->eVoltageUnit, pItemPatrolType);
        ItemDevice tmpDevice;
        ItemDevice GISDeviceInBay;

        if(pItemPatrolType->ePatrolType == PDAServiceNS::COMBINED_ELECTRIC)
        {
            //GIS设备
            if(!(isGISItemDeviceExistInTestType(pItemTestType->devices, stDev.strBayDevNum, matchedIndex)))
            {
                //GIS的设备列表还是展示设备，而不是间隔列表
                InitializeItemDeviceWithDeviceInfo(stDev, tmpDevice, stDev.strDevName/*stDev.strBayDevName*/, stDev.strDevNum/*stDev.strBayDevNum*/);
            }
            else
            {
                tmpDevice = pItemTestType->devices[matchedIndex];
            }

            InitializeItemDeviceWithDeviceInfo(stDev, GISDeviceInBay, strRealDevName, strRealDevNum);
        }
        else
        {
            //非GIS设备
            InitializeItemDeviceWithDeviceInfo(stDev, tmpDevice, strRealDevName, strRealDevNum);
        }

        // 指定类型下测点的集合
        QList<ItemTestPoint> lTestPoint;
        lTestPoint.clear();
        testPointListWithSameTypeVoltage(testPointList, iterKey->eType,
                                         iterKey->dVoltage, iterKey->eVoltageUnit,
                                         lTestPoint);

        //modified by zhaoyongjun, 解决测点列表界面显示的item顺序是反的
        for (QList<ItemTestPoint>::iterator iterPointTmp = lTestPoint.begin();
             iterPointTmp != lTestPoint.end(); ++iterPointTmp)
        {
            for(QVector<ItemTestData>::iterator iterTestData = iterPointTmp->testDatas.begin();
                iterTestData != iterPointTmp->testDatas.end(); ++iterTestData)
            {
                if(pItemPatrolType->ePatrolType == PDAServiceNS::COMBINED_ELECTRIC)
                {
                    ++(GISDeviceInBay.uiTotalCount);
                }

                //各级节点测试项总数 + 1
                ++(tmpDevice.uiTotalCount), ++(pItemTestType->uiTotalCount), ++(pItemPatrolType->uiTotalCount);

                if(iterTestData->bTested)
                {
                    //各级节点已测测试项 + 1;
                    ++(pItemTestType->uiTestedCount), ++(tmpDevice.uiTestedCount), ++(pItemPatrolType->uiTestedCount);
                    if(pItemPatrolType->ePatrolType == PDAServiceNS::COMBINED_ELECTRIC)
                    {
                        ++(GISDeviceInBay.uiTestedCount);
                    }

                    if(iterTestData->eDataType == PDAServiceNS::AE_AMP_CHART_DATA)
                    {
                        strTestFile = iterTestData->strFilePath;        //用于解析AE噪声背景值
                    }
                }
            }

            tmpDevice.testPoints.append(*iterPointTmp);  // 测点添加测试项
        }

        //如果有ae幅值数据，则解析背景值
        if(!strTestFile.isEmpty())
        {
            //tmpDevice.b_isBGNTested = true;
            //setDeviceBNG(strTestFile, tmpDevice);
        }

        if(pItemPatrolType->ePatrolType == PDAServiceNS::COMBINED_ELECTRIC)
        {
            int matchedIndex = 0;
            if(isGISItemDeviceExistInTestType(pItemTestType->devices, tmpDevice.strDevNum, matchedIndex))
            {
                pItemTestType->devices.remove(matchedIndex);
            }

            ItemDeviceTaskFileInfo stGISDeviceInBay;
            createDeviceInBay(GISDeviceInBay, stGISDeviceInBay);
            if(!isDeviceExistInBay(stBay.vecDevice, stGISDeviceInBay.strDevNum))
            {
                stBay.vecDevice.append(stGISDeviceInBay);
            }
        }
        else
        {
            ItemDeviceTaskFileInfo stDeviceInBay;
            createDeviceInBay(tmpDevice, stDeviceInBay);
            if(!isDeviceExistInBay(stBay.vecDevice, stDeviceInBay.strDevNum))
            {
                stBay.vecDevice.append(stDeviceInBay);
            }
        }

        //设备排序
        //qSort(stBay.vecDevice.begin(), stBay.vecDevice.end(), testDeviceTaskInfoSort);

        addDeviceToItemTestType(tmpDevice, pItemTestType);

        //设备排序
        //qSort(pItemTestType->devices.begin(), pItemTestType->devices.end(), testDeviceSort);

        if(stBay.isBGN)
        {
            //获取相应的测试类型 只根据数据类型（ae/tev/uhf/hfct/infrared）区分
            QList<int> lIndex;
            lIndex.clear();
            QString strTestType = stringFromTestType(iterKey->eType); // 测试类型对应的字符串
            indexListWithSameTestType(strTestType, pItemPatrolType->testTypes, lIndex); // 指定测试类型名称在现有集合中的序号，若无则返回ERROR_INDEX

            for(int i = 0, iSize = lIndex.size(); i < iSize; ++i)
            {
                if(!isBayExistInTestType(&(pItemPatrolType->testTypes[i]), stBay.strNumber))
                {
                    pItemPatrolType->testTypes[i].bays.append(stBay);
                }

                //间隔排序
                //qSort(pItemPatrolType->testTypes[i].bays.begin(), pItemPatrolType->testTypes[i].bays.end(), testBaySort);
            }
        }
        else
        {
            if(!isBayExistInTestType(pItemTestType, stBay.strNumber))
            {
                pItemTestType->bays.append(stBay);
            }

            //间隔排序
            //qSort(pItemTestType->bays.begin(), pItemTestType->bays.end(), testBaySort);
        }
    }

    return;
}

void TaskFileIO::addDeviceToItemTestType(const ItemDevice &stDevice, ItemTestType* pItemTestType)
{
    if(!pItemTestType)
    {
        return;
    }

    int iIndex = 0;
    bool bFound = false;
    for(int i = 0, iSize = pItemTestType->devices.size(); i < iSize; ++i)
    {
        if(pItemTestType->devices[i].strDevNum == stDevice.strDevNum)
        {
            bFound = true;
            iIndex = i;
            break;
        }
    }

    if(bFound)
    {
        //合并测点信息
        //pItemTestType->devices[iIndex].testPoints.append(stDevice.testPoints);
        for(int j = 0, iTpSize = stDevice.testPoints.size(); j < iTpSize; ++j)
        {
            pItemTestType->devices[iIndex].testPoints.append(stDevice.testPoints[j]);
        }

        pItemTestType->devices[iIndex].uiTotalCount += stDevice.uiTotalCount;
        pItemTestType->devices[iIndex].uiTestedCount += stDevice.uiTestedCount;
    }
    else
    {
        //将背景放在前面，符合使用习惯
        if(stDevice.b_isBGNTest)
        {
            pItemTestType->devices.push_front(stDevice);
        }
        else
        {
            pItemTestType->devices.append(stDevice);// 测试类型添加设备
        }
    }

    return;
}

/****************************
输入参数: vecDevice---bay里的device
        strDevNum---指定的device
功能： 判断指定的device是否存在于bay里
*****************************/
bool TaskFileIO::isDeviceExistInBay(const QVector<ItemDeviceTaskFileInfo> &vecDevice, const QString &strDevNum)
{
    for (QVector<ItemDeviceTaskFileInfo>::const_iterator iter = vecDevice.begin();
         iter != vecDevice.end(); ++iter)
    {
        if(strDevNum == iter->strDevNum)
        {
            return true;
        }
    }

    return false;
}

/****************************
输入参数: pTestType---测试类型对象
        strBayNum---指定的bay number
功能： 判断指定的bay是否已在测试类型对象里
*****************************/
bool TaskFileIO::isBayExistInTestType(ItemTestType *pTestType, const QString &strBayNum)
{
    for(QVector<ItemBay>::iterator iter = pTestType->bays.begin();
        iter != pTestType->bays.end(); ++iter)
    {
        if(strBayNum == iter->strNumber)
        {
            return true;
        }
    }

    return false;
}

/****************************
输入参数: tmpDevice---testtype里的device
输出参数:stDevice---bay里的device(不含测点及测试项信息)
功能： copy device相关信息，创建bay下的device
*****************************/
void TaskFileIO::createDeviceInBay(const ItemDevice &tmpDevice, ItemDeviceTaskFileInfo &stDevice)
{
    stDevice.usIndex = tmpDevice.usIndex;
    stDevice.b_isBGNTest = tmpDevice.b_isBGNTest;
    stDevice.strItemName = tmpDevice.strItemName;
    stDevice.strDevNum = tmpDevice.strDevNum;
    stDevice.iDevType = tmpDevice.iDevType;
    stDevice.strType = tmpDevice.strType;
    stDevice.eVoltageUnit = tmpDevice.eVoltageUnit;
    stDevice.dVoltage = tmpDevice.dVoltage;
    stDevice.dLoadCurrent = tmpDevice.dLoadCurrent;
    stDevice.eLoadCurrentUnit = tmpDevice.eLoadCurrentUnit;
    stDevice.dwLoadCurrentSign = tmpDevice.dwLoadCurrentSign;
    stDevice.strDevModel = tmpDevice.strDevModel;
    stDevice.strManufacturer = tmpDevice.strManufacturer;
    stDevice.strProduceDate = tmpDevice.strProduceDate;
    stDevice.uiTotalCount = tmpDevice.uiTotalCount;
    stDevice.eFilterSign = tmpDevice.eFilterSign;
    stDevice.strMainImgFilePath = tmpDevice.strMainImgFilePath;
    stDevice.strRemoteMainImgFilePath = tmpDevice.strRemoteMainImgFilePath;
    stDevice.lMediaFilePath = tmpDevice.lMediaFilePath;
    stDevice.lRemoteMediaFilePath = tmpDevice.lRemoteMediaFilePath;
    stDevice.lImageFilePath = tmpDevice.lImageFilePath;
    stDevice.lRemoteImageFilePath = tmpDevice.lRemoteImageFilePath;
    stDevice.strSourceId = tmpDevice.strSourceId;
    stDevice.ucGroupName = tmpDevice.ucGroupName;
    stDevice.uiTestedCount = tmpDevice.uiTestedCount;
    return;
}

/*
void TaskFileIO::setDeviceBNG( const QString & strFilePath, ItemDevice & stDevice )
{
    DataFile * pDataFile = new DataFile;
    pDataFile->open( strFilePath );
    AEAmpDataMap * pMap = dynamic_cast <AEAmpDataMap*>(pDataFile->dataMap(DataFileNS::SPECTRUM_CODE_AE_AMP));
    if( NULL != pMap )
    {
        AEMapNS::AEAmpData  m_pAeData;
        pMap->getData(&m_pAeData);

        stDevice.fRmsBGN = m_pAeData.fRmsBGN;
        stDevice.fPeakBGN = m_pAeData.fPeakBGN;
        stDevice.fFrequency1BGN = m_pAeData.fFrequency1BGN;
        stDevice.fFrequency2BGN = m_pAeData.fFrequency2BGN;
    }
}
*/

/****************************
输入参数:doc -- 自定义xml文件
        lTestPointElement -- 节点的集合
出参：hash -- 将测点根据类型进行分类
功能： 根据类型将测点进行分类
返回值：xml是否发生变化，true：发生变化，false：未发生变化
*****************************/
bool TaskFileIO::classifyTestPoint(QList<ItemTestPoint> &testPointList, const QList<QDomElement>& lTestPointElement, XMLDocument &doc, taskfileioNS::device_info &stDev)
{
    bool bChanged = false;
    quint16 qui16LastIndex = 0;
    for (QList<QDomElement>::const_iterator iterPoint = lTestPointElement.begin();
         iterPoint != lTestPointElement.end(); ++iterPoint)
    {
        doc.beginElement(*iterPoint);

        ItemTestPoint tmpPoint;
        tmpPoint.strItemNumber = doc.value( TEST_POINT_NUMBER );

        if(PDAServiceNS::COMBINED_ELECTRIC == stDev.eType && !(stDev.b_isBGNTest))
        {
            tmpPoint.strItemName = stDev.strDevName + doc.value(TEST_POINT_NAME);
        }
        else
        {
            tmpPoint.strItemName = doc.value( TEST_POINT_NAME );
        }

        QString strTested = doc.value( TEST_POINT_IS_TEST );
        tmpPoint.bTested = (bool)( strTested.toInt() );
        PDAServiceNS::TestPointType eType = (PDAServiceNS::TestPointType)( doc.value( TEST_POINT_TYPE ).toInt());
        tmpPoint.eType = eType;
        if(doc.value( TEST_POINT_TYPE ).isEmpty())
        {
            tmpPoint.eTestPosition = PDAServiceNS::TEST_POSITION_UNUSED;
        }
        else
        {
            tmpPoint.eTestPosition = static_cast<PDAServiceNS::TestPosition>(doc.value(TEST_POINT_POSITION).toShort());
        }

        /*
        if(stDev.b_isBGNTest)
        {
            tmpPoint.ePatrolPosition = PDAServiceNS::PATROL_POS_NONE;
        }
        else
        {
            tmpPoint.ePatrolPosition = getPatrolPosition(tmpPoint.eTestPosition);
        }
        */
        tmpPoint.ePatrolPosition = getPatrolPosition(tmpPoint.eTestPosition);
        tmpPoint.usTestDataCount = doc.value( TEST_POINT_DATA_COUNT ).toUShort();
        tmpPoint.usIndex = doc.value( TEST_POINT_INDEX ).toShort();

        if(tmpPoint.usIndex <= qui16LastIndex)
        {
            tmpPoint.usIndex = qui16LastIndex + 1;
        }

        qui16LastIndex = tmpPoint.usIndex;

        tmpPoint.dVoltage = stDev.dVoltage;
        tmpPoint.eVoltageUnit = stDev.eVoltageUnit;


        //读取任务文件里设备节点的图片，音频文件路径
        tmpPoint.strMainImgFilePath = doc.value( TEST_POINT_MAIN_IMAGE_FILE_PATH );

        //remote main image file path
        if(doc.hasElement(TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH))
        {
            tmpPoint.strRemoteMainImgFilePath = doc.value( TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH );
        }

        //读取任务文件里设备节点的media file path
        QList<QDomElement> qlMediaFilePath = doc.childElement(TEST_POINT_MEDIA_FILE_PATH);
        for(QList<QDomElement>::iterator iter = qlMediaFilePath.begin();
            iter != qlMediaFilePath.end(); ++iter)
        {
            doc.beginElement(*iter);
            if(!(tmpPoint.lMediaFilePath.contains(doc.text())))
            {
                tmpPoint.lMediaFilePath.append(doc.text());
            }
            doc.endElement();
        }

        //remote media file path
        QList<QDomElement> qlRemoteMediaFilePath = doc.childElement(TEST_POINT_REMOTE_MEDIA_FILE_PATH);
        for(QList<QDomElement>::iterator iter = qlRemoteMediaFilePath.begin();
            iter != qlRemoteMediaFilePath.end(); ++iter)
        {
            doc.beginElement(*iter);
            if(!(tmpPoint.lRemoteMediaFilePath.contains(doc.text())))
            {
                tmpPoint.lRemoteMediaFilePath.append(doc.text());
            }
            doc.endElement();
        }

        //读取任务文件里设备节点的image file path
        QList<QDomElement> qlImageFilePath = doc.childElement(TEST_POINT_IMAGE_FILE_PATH);
        for(QList<QDomElement>::iterator iter = qlImageFilePath.begin();
            iter != qlImageFilePath.end(); ++iter)
        {
            doc.beginElement(*iter);
            if(!(tmpPoint.lImageFilePath.contains(doc.text())))
            {
                tmpPoint.lImageFilePath.append(doc.text());
            }
            doc.endElement();
        }

        //remote image file path
        QList<QDomElement> qlRemoteImageFilePath = doc.childElement(TEST_POINT_REMOTE_IMAGE_FILE_PATH);
        for(QList<QDomElement>::iterator iter = qlRemoteImageFilePath.begin();
            iter != qlRemoteImageFilePath.end(); ++iter)
        {
            doc.beginElement(*iter);
            if(!(tmpPoint.lRemoteImageFilePath.contains(doc.text())))
            {
                tmpPoint.lRemoteImageFilePath.append(doc.text());
            }
            doc.endElement();
        }

        //设置各个测点的内部跳转类型
        setAutoTestType(tmpPoint, tmpPoint.eTestPosition);

        QList<QDomElement> lTestDataElement = doc.childElement( TEST_DATA );
        addTestDataToPoint( lTestDataElement, doc, tmpPoint ); // 将测试项添加到测点数据中

        //更新测点的数目
        int iSize = tmpPoint.testDatas.size();
        if(tmpPoint.usTestDataCount != iSize)
        {
            tmpPoint.usTestDataCount = static_cast<quint16>(iSize);
            doc.setValue(TEST_POINT_DATA_COUNT, QString::number(tmpPoint.usTestDataCount));
            bChanged = true;
            //提高效率，xml有变化，外部统一进行保存修改
            //doc.save();
        }

        //更新测试状态
        if(tmpPoint.isTestFinished())
        {
            tmpPoint.bTested = true;
            doc.setValue( TEST_POINT_IS_TEST, QString::number( tmpPoint.bTested ) );

        }

        testPointList.append(tmpPoint);
        doc.endElement();
    }

    //sort
    qSort(testPointList.begin(), testPointList.end(), testPointSort);

    return bChanged;
}

/***********************************************************
 * 功能：根据测试位置获取巡检的位置
 * 输入参数：
 *      eTestPosition：测试位置
 * 返回值：
 *      PDAServiceNS::PatrolPosition：巡检位置
 * *********************************************************/
PDAServiceNS::PatrolPosition TaskFileIO::getPatrolPosition(PDAServiceNS::TestPosition eTestPosition)
{
    PDAServiceNS::PatrolPosition ePatrolPosition = PDAServiceNS::PATROL_POS_NONE;

    switch(eTestPosition)
    {
    case PDAServiceNS::TEV_FRONT_MIDDLE:
    case PDAServiceNS::TEV_FRONT_DOWN:
    case PDAServiceNS::CABINET_FRONT_AE_AMP:
    case PDAServiceNS::CABINET_FRONT_UHF_PRPSPRPD:
    case PDAServiceNS::CABINET_FRONT_INFRARED_SPEC:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_CABINET_FRONT;
        break;
    }
    case PDAServiceNS::TEV_SIDE_UP:
    case PDAServiceNS::TEV_SIDE_MIDDLE:
    case PDAServiceNS::TEV_SIDE_DOWN:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_CABINET_SIDE;
        break;
    }
    case PDAServiceNS::TEV_BACK_UP:
    case PDAServiceNS::TEV_BACK_MIDDLE:
    case PDAServiceNS::TEV_BACK_DOWN:
    case PDAServiceNS::CABINET_BACK_AE_AMP:
    case PDAServiceNS::CABINET_BACK_UHF_PRPSPRPD:
    case PDAServiceNS::CABINET_BACK_INFRARED_SPEC:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_CABINET_BACK;
        break;
    }
    case PDAServiceNS::CABLEGROUND_A_CURRENT:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_CABLE_A;
        break;
    }
    case PDAServiceNS::CABLEGROUND_B_CURRENT:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_CABLE_B;
        break;
    }
    case PDAServiceNS::CABLEGROUND_C_CURRENT:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_CABLE_C;
        break;
    }
    case PDAServiceNS::CABLEGROUND_GENERAL_CURRENT:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_GENERALGROUND;
        break;
    }
    case PDAServiceNS::CABLE_A_LOADCURRENT:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_CABLE_A_LOADCURRENT;
        break;
    }
    case PDAServiceNS::CABLE_B_LOADCURRENT:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_CABLE_B_LOADCURRENT;
        break;
    }
    case PDAServiceNS::CABLE_C_LOADCURRENT:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_CABLE_C_LOADCURRENT;
        break;
    }
    case PDAServiceNS::COREGROUND_CURRENT:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_COREGROUND;
        break;
    }
    case PDAServiceNS::CLAMPGROUND_CURRENT:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_CLAMPGROUND;
        break;
    }
    case PDAServiceNS::TEST_POSITION_UNUSED:
    case PDAServiceNS::TEST_POSITION_DEFAULT_NO_SPECIAL_POS:
    default:
    {
        ePatrolPosition = PDAServiceNS::PATROL_POS_NONE;
        break;
    }

    }

    return ePatrolPosition;
}

//测试用 打印任务概要信息
void TaskFileIO::printTaskInfo(TaskInfo &sTaskInfo)
{
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.strFilePath:"<<sTaskInfo.strFilePath;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.isDownloaded:"<<sTaskInfo.isDownloaded;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.uiTotalCount:"<<sTaskInfo.uiTotalCount;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.uiTestedCount:"<<sTaskInfo.uiTestedCount;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.eSourcePlatform:"<<sTaskInfo.eSourcePlatform;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.strTestNumber:"<<sTaskInfo.strTestNumber;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.strInnerId:"<<sTaskInfo.strInnerId;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.strItemName:"<<sTaskInfo.strItemName;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.lCreateTime:"<<sTaskInfo.lCreateTime;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.lPlanTime:"<<sTaskInfo.lPlanTime;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.lTestTime:"<<sTaskInfo.lTestTime;


    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.strStationNo:"<<sTaskInfo.strStationNo;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.strStationName:"<<sTaskInfo.strStationName;

    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.dVoltage:"<<sTaskInfo.dVoltage;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.eVoltageUnit:"<<sTaskInfo.eVoltageUnit;

    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.usBayCount:"<<sTaskInfo.usBayCount;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.strMainImgFilePath:"<<sTaskInfo.strMainImgFilePath;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.strRemoteMainImgFilePath:"<<sTaskInfo.strRemoteMainImgFilePath;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.lMediaFilePath:"<<sTaskInfo.lMediaFilePath;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.lRemoteMediaFilePath:"<<sTaskInfo.lRemoteMediaFilePath;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.lImageFilePath:"<<sTaskInfo.lImageFilePath;
    qDebug()<<"TaskFileIO::readTaskInfo, sTaskInfo.lRemoteImageFilePath:"<<sTaskInfo.lRemoteImageFilePath;
}

/****************************
功能： 打印测点信息
*****************************/
void TaskFileIO::printTestPointInfo(ItemTestPoint &tmpPoint)
{
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.strItemName:"<<tmpPoint.strItemName;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.strItemNumber:"<<tmpPoint.strItemNumber;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.bTested:"<<tmpPoint.bTested;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.eType:"<<tmpPoint.eType;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.dVoltage:"<<tmpPoint.dVoltage;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.eVoltageUnit:"<<tmpPoint.eVoltageUnit;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.usTestDataCount:"<<tmpPoint.usTestDataCount;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.strMainImgFilePath:"<<tmpPoint.strMainImgFilePath;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.strRemoteMainImgFilePath:"<<tmpPoint.strRemoteMainImgFilePath;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.lMediaFilePath:"<<tmpPoint.lMediaFilePath;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.lRemoteMediaFilePath:"<<tmpPoint.lRemoteMediaFilePath;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.lImageFilePath:"<<tmpPoint.lImageFilePath;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.lRemoteImageFilePath:"<<tmpPoint.lRemoteImageFilePath;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.usIndex:"<<tmpPoint.usIndex;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.eTestPosition:"<<tmpPoint.eTestPosition;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.eSwitchType:"<<tmpPoint.eSwitchType;
    qDebug()<<"TaskFileIO::printTestPointInfo, mpPoint.uiTestIndex:"<<tmpPoint.uiTestIndex;
}

/****************************
输入参数:tmpPoint -- 测点信息
        eType -- 测点类型
出参：NULL
功能： 设置测点的自动跳转类别
*****************************/
void TaskFileIO::setAutoTestType(ItemTestPoint& tmpPoint, PDAServiceNS::TestPosition eTestPosition)
{
    PDAServiceNS::AutoSwitchType eSwitchType;

    switch(eTestPosition)
    {
    case PDAServiceNS::TEV_FRONT_MIDDLE:
    case PDAServiceNS::TEV_FRONT_DOWN:
        eSwitchType = PDAServiceNS::TEV_FRONT_AUTO;
        break;
    case PDAServiceNS::TEV_SIDE_UP:
    case PDAServiceNS::TEV_SIDE_MIDDLE:
    case PDAServiceNS::TEV_SIDE_DOWN:
        eSwitchType = PDAServiceNS::TEV_SIDE_AUTO;
        break;
    case PDAServiceNS::TEV_BACK_UP:
    case PDAServiceNS::TEV_BACK_MIDDLE:
    case PDAServiceNS::TEV_BACK_DOWN:
        eSwitchType = PDAServiceNS::TEV_BACK_AUTO;
        break;
    case PDAServiceNS::CABINET_FRONT_AE_AMP:
        eSwitchType = PDAServiceNS::AE_FRONT_AUTO;
        break;
    case PDAServiceNS::CABINET_BACK_AE_AMP:
        eSwitchType = PDAServiceNS::AE_BACK_AUTO;
        break;
    case PDAServiceNS::CABINET_FRONT_UHF_PRPSPRPD:
        eSwitchType = PDAServiceNS::UHF_PRPS_FRONT_AUTO;
        break;
    case PDAServiceNS::CABINET_BACK_UHF_PRPSPRPD:
        eSwitchType = PDAServiceNS::UHF_PRPS_BACK_AUTO;
        break;
    case PDAServiceNS::CABINET_FRONT_INFRARED_SPEC:
        eSwitchType = PDAServiceNS::INFRARED_FRONT_AUTO;
        break;
    case PDAServiceNS::CABINET_BACK_INFRARED_SPEC:
        eSwitchType = PDAServiceNS::INFRARED_BACK_AUTO;
        break;
    case PDAServiceNS::TEST_POSITION_UNUSED:
    case PDAServiceNS::TEST_POSITION_DEFAULT_NO_SPECIAL_POS:
    case PDAServiceNS::CABLEGROUND_A_CURRENT:
    case PDAServiceNS::CABLEGROUND_B_CURRENT:
    case PDAServiceNS::CABLEGROUND_C_CURRENT:
    case PDAServiceNS::CABLEGROUND_GENERAL_CURRENT:
    case PDAServiceNS::CABLE_A_LOADCURRENT:
    case PDAServiceNS::CABLE_B_LOADCURRENT:
    case PDAServiceNS::CABLE_C_LOADCURRENT:
    case PDAServiceNS::COREGROUND_CURRENT:
    case PDAServiceNS::CLAMPGROUND_CURRENT:
        eSwitchType = PDAServiceNS::AUTO_BY_INDEX;
        break;
    default:
        eSwitchType = PDAServiceNS::UNDEFINE_AUTO;
        break;
    }
    tmpPoint.eSwitchType = eSwitchType;

    return;
}

/************************************************
 * 功能: 找到虚拟出的具有同编号的间隔
 *      同一个间隔，根据测点类型和电压等级虚拟出了若干间隔
 * 入参：vPatrolTypes -- 存放所有巡检数据的对象
 *      strNumber -- 间隔编号
 * 出参：stBay---任意一个具有相同编号的间隔
 * 返回值：true:存在匹配的间隔;false:不存在匹配的间隔
 ************************************************/
bool TaskFileIO::findMatchedBay(const QVector<ItemPatrolType> &vPatrolTypes, const QString &strNumber, ItemBay &stBay)
{
    //没有内存拷贝，执行效率更高
    bool bRet = false;
    for(int i = 0, iPatrolSize = vPatrolTypes.size(); i < iPatrolSize; ++i)
    {
        for(int j = 0, iTestSize = vPatrolTypes[i].testTypes.size(); j < iTestSize; ++j)
        {
            for(int k = 0, iBaySize = vPatrolTypes[i].testTypes[j].bays.size(); k < iBaySize; ++k)
            {
                if(strNumber == vPatrolTypes[i].testTypes[j].bays[k].strNumber)
                {
                    stBay = vPatrolTypes[i].testTypes[j].bays[k];
                    bRet = true;
                }
            }
        }
    }

    return bRet;
}

/****************************
输入参数:ePatrolType -- 巡检类型枚举
出参：vPatrolTypes -- 巡检类型的集合
返回值：巡检类型的指针
功能： 获取巡检类型定义的巡检类型的指针
*****************************/
ItemPatrolType*  TaskFileIO::itemPatrolType( PDAServiceNS::PatrolType ePatrolType,QVector<ItemPatrolType> &vPatrolTypes )
{
    QString strPatrolType = stringFromPatrolType(ePatrolType);            // 巡检类型对应的字符串
    int iPatrolTypeIndex = indexOfPatrolType( strPatrolType,vPatrolTypes ); // 指定巡检类型名称在现有集合中的序号，若无则返回ERROR_INDEX
    if( iPatrolTypeIndex == ERROR_INDEX )                                   // 若当前巡检类型在现有类型中不存在，则在末尾添加
    {
        ItemPatrolType tmpPat;
        tmpPat.strItemName = strPatrolType;
        tmpPat.uiTestedCount = 0;
        tmpPat.uiTotalCount = 0;
        tmpPat.ePatrolType = ePatrolType;
        vPatrolTypes.append( tmpPat );                                      // 增加巡检类型
        iPatrolTypeIndex = vPatrolTypes.size() - 1;
    }
    else
    {
        //itemPatrolType is already exist
    }

    return &(vPatrolTypes[iPatrolTypeIndex]);
}

/****************************
输入参数:eTestType -- 测试类型枚举
        eVoltageLevel---电压等级
出参：pItemPatrolType -- 巡检类型的指针
返回值：测试类型的指针
功能： 获取测试类型定义的测试类型的指针
*****************************/
ItemTestType* TaskFileIO::itemTestType( PDAServiceNS::TestPointType eTestType, double dVoltage, PDAServiceNS::TaskUnitCode eVoltageUnit, ItemPatrolType* pItemPatrolType )
{
    QString strVoltage = QString::number(dVoltage, 'f', 2);
    int index = strVoltage.indexOf('.');
    if(strVoltage.endsWith(".00"))
    {
        //such as 10.00
        strVoltage.remove(index, 3);
    }
    else if(strVoltage.endsWith("0"))
    {
        //such as 5.10
        strVoltage.remove(strVoltage.size() - 1, 1);
    }
    else
    {
        //such as 5.16
    }

    QString strTestType = stringFromTestType(eTestType);
    QString strUnit = (PDAServiceNS::Voltage_V_Unit == eVoltageUnit) ? "V" : "kV";
    QString strName = strTestType + "_" + strVoltage + strUnit;

    // 测试类型对应的字符串
    int iTestTypeIndex = indexOfTestType(strName, dVoltage, eVoltageUnit, pItemPatrolType->testTypes); // 指定测试类型名称在现有集合中的序号，若无则返回ERROR_INDEX

    // 若当前测试类型在现有类型中不存在，则在末尾添加
    if(iTestTypeIndex == ERROR_INDEX)
    {
        ItemTestType tmpTestType;
        tmpTestType.strItemName = strTestType;
        QString strUnit = (PDAServiceNS::Voltage_V_Unit == eVoltageUnit) ? "V" : "kV";

        tmpTestType.strItemName += "_" + strVoltage + strUnit;
        tmpTestType.uiTestedCount = 0;
        tmpTestType.uiTotalCount = 0;
        tmpTestType.eTestPointType = eTestType;
        tmpTestType.dVoltage = dVoltage;
        tmpTestType.eVoltageUnit = eVoltageUnit;
        pItemPatrolType->testTypes.append(tmpTestType);
        iTestTypeIndex = pItemPatrolType->testTypes.size() - 1;
    }
    else
    {
        //itemTestType is already exist
    }

    return &(pItemPatrolType->testTypes[iTestTypeIndex]);
}

/****************************
输入参数:strDeviceNum -- 设备编号
        vPatrolTypes -- 巡检类型集合
返回值：根据设备编号，从指定巡检类型的集合中找到相应的虚拟设备集合
功能： 获取指定设备名的设备集合
*****************************/
QVector<ItemDevice*> TaskFileIO::itemDevices(const QString &strDeviceNum, const QString &strSourceId, QVector<ItemPatrolType> &vPatrolTypes )
{
    QVector<ItemDevice*> vDevices;
    if(strDeviceNum.isEmpty())
    {
        return vDevices;
    }

    bool bMatched = false;                                              // 是否匹配上具体设备的标志
    for (QVector<ItemPatrolType>::iterator iterPatrol = vPatrolTypes.begin();
         iterPatrol != vPatrolTypes.end();
         ++iterPatrol)
    {

        for (QVector<ItemTestType>::iterator iterTestType = iterPatrol->testTypes.begin();
             iterTestType != iterPatrol->testTypes.end();
             ++iterTestType)
        {
            for(QVector<ItemDevice>::iterator iterDev = iterTestType->devices.begin();
                iterDev != iterTestType->devices.end();
                ++iterDev)
            {
                if(isDeviceMatched( strDeviceNum, strSourceId, *iterDev))
                {
                    vDevices.append(iterDev);
                    bMatched = true;
                }
            }
        }

        if(bMatched)
        {
            break;                                                      // 已找到匹配设备则退出循环
        }
    }

    //    for (int i = 0; i < vPatrolTypes.size(); ++i)
    //    {
    //        for (int j = 0; j < vPatrolTypes[i].testTypes.size(); ++j)
    //        {
    //            for(int k = 0; k < vPatrolTypes[i].testTypes[j].devices.size(); ++k)
    //            {
    //                if(isDeviceMatched( strDeviceNum, strSourceId, vPatrolTypes[i].testTypes[j].devices[k]))
    //                {
    //                    vDevices.append(&(vPatrolTypes[i].testTypes[j].devices[k]));
    //                    bMatched = true;
    //                }
    //            }
    //        }

    //        if(bMatched)
    //        {
    //            break;                                                      // 已找到匹配设备则退出循环
    //        }
    //    }

    return vDevices;
}


/****************************
输入参数:vPatrolTypes -- 巡检类型集合
返回值：根据设备编号，从指定巡检类型的集合中找到相应的虚拟设备集合
功能： 获取任务文件里所有test type里的testDevice
*****************************/
QVector<ItemDevice*> TaskFileIO::itemDevicesInAllPatrolType( QVector<ItemPatrolType> &vPatrolTypes )
{
    QVector<ItemDevice*> vDevices;
    for(QVector<ItemPatrolType>::iterator iterPatrol = vPatrolTypes.begin();
        iterPatrol != vPatrolTypes.end();
        ++iterPatrol)
    {
        for (QVector<ItemTestType>::iterator iterTestType = iterPatrol->testTypes.begin();
             iterTestType != iterPatrol->testTypes.end();
             ++iterTestType)
        {
            for(QVector<ItemDevice>::iterator iterDev = iterTestType->devices.begin();
                iterDev != iterTestType->devices.end();
                ++iterDev)
            {
                vDevices.append(iterDev);
            }
        }
    }

    return vDevices;
}


/****************************
输入参数:strDeviceNum -- 设备编号
        vPatrolTypes -- 巡检类型集合
返回值：根据设备编号，从指定巡检类型的集合中找到相应的虚拟设备集合
功能： 获取指定设备名的设备集合
*****************************/
QVector<ItemDeviceTaskFileInfo*> TaskFileIO::getGISItemDevicesInBay(const QString &strDeviceNum, QVector<ItemPatrolType> &vPatrolTypes)
{
    QVector<ItemDeviceTaskFileInfo*> vDevices;
    if(strDeviceNum.isEmpty())
    {
        //qDebug() << "TaskFileIO::GISItemDevicesInBay, strDeviceNum is empty." << endl;
        return vDevices;
    }

    bool bMatched = false;                                              // 是否匹配上具体设备的标志
    for (QVector<ItemPatrolType>::iterator iterPatrol = vPatrolTypes.begin();
         iterPatrol != vPatrolTypes.end();
         ++iterPatrol)
    {
        for (QVector<ItemTestType>::iterator iterTestType = iterPatrol->testTypes.begin();
             iterTestType != iterPatrol->testTypes.end();
             ++iterTestType)
        {
            for (QVector<ItemBay>::iterator iterBay = iterTestType->bays.begin();
                 iterBay != iterTestType->bays.end();
                 ++iterBay)
            {
                for (QVector<ItemDeviceTaskFileInfo>::iterator iterDev = iterBay->vecDevice.begin();
                     iterDev != iterBay->vecDevice.end();
                     ++iterDev)
                {
                    if(strDeviceNum == iterDev->strDevNum)
                    {
                        vDevices.append(iterDev);
                        bMatched = true;
                    }
                }
            }
        }

        if(bMatched)
        {
            break;                                                      // 已找到匹配设备则退出循环
        }
    }

    //    for (int i = 0; i < vPatrolTypes.size(); ++i)
    //    {
    //        for (int j = 0; j < vPatrolTypes[i].testTypes.size(); ++j)
    //        {
    //            for (int k = 0; k < vPatrolTypes[i].testTypes[j].bays.size(); ++k)
    //            {
    //                for (int l = 0; l < vPatrolTypes[i].testTypes[j].bays[k].vecDevice.size(); ++l)
    //                {
    //                    if(strDeviceNum == (vPatrolTypes[i].testTypes[j].bays[k].vecDevice[l]).strDevNum)
    //                    {
    //                        vDevices.append(&(vPatrolTypes[i].testTypes[j].bays[k].vecDevice[l]));
    //                        bMatched = true;
    //                    }
    //                }
    //            }
    //        }

    //        if(bMatched)
    //        {
    //            break;                                                      // 已找到匹配设备则退出循环
    //        }
    //    }

    return vDevices;
}

/****************************
功能： 获取组合电气中设备集合
*****************************/
void TaskFileIO::getGISItemDevices(const QString &strDeviceNum, QVector<ItemPatrolType> &vPatrolTypes, QVector<ItemDeviceTaskFileInfo*> &vtBayDevs, QVector<ItemDevice*> &vtTestDevs)
{
    bool bMatched = false;                                              // 是否匹配上具体设备的标志
    bool bFindBayDevs = false;

    for (QVector<ItemPatrolType>::iterator iterPatrol = vPatrolTypes.begin();
         iterPatrol != vPatrolTypes.end();
         ++iterPatrol)
    {
        for (QVector<ItemTestType>::iterator iterTestType = iterPatrol->testTypes.begin();
             iterTestType != iterPatrol->testTypes.end();
             ++iterTestType)
        {
            for(QVector<ItemDevice>::iterator iterTestDev = iterTestType->devices.begin();
                iterTestDev != iterTestType->devices.end();
                ++iterTestDev)
            {
                vtTestDevs.append(iterTestDev);
            }

            if(!bFindBayDevs)
            {
                for (QVector<ItemBay>::iterator iterBay = iterTestType->bays.begin();
                     iterBay != iterTestType->bays.end();
                     ++iterBay)
                {
                    for (QVector<ItemDeviceTaskFileInfo>::iterator iterBayDev = iterBay->vecDevice.begin();
                         iterBayDev != iterBay->vecDevice.end();
                         ++iterBayDev)
                    {
                        if(strDeviceNum == iterBayDev->strDevNum)
                        {
                            vtBayDevs.append(iterBayDev);
                            bMatched = true;
                        }
                    }
                }
            }
        }

        if(bMatched)
        {
            bFindBayDevs = true;                                                      // 已找到匹配设备则退出循环
        }
    }

    return;
}

/****************************
输入参数:strDeviceNum -- 设备编号
        strSourceId -- 旧任务文件里的sourceid,新任务文件没有此字段
        device---设备
返回值：true:相同; false:不相同
功能： 判断是否是相同的设备
*****************************/
bool TaskFileIO::isDeviceMatched( const QString& strDeviceNum, const QString& strSourceId,
                                  const ItemDevice& device )
{
    Q_UNUSED(strSourceId);
    bool bMatched = false;
    if(!(strDeviceNum.isEmpty()))
    {
        bMatched = ( strDeviceNum == device.strDevNum );
    }
    //    else
    //    {
    //        if( !strSourceId.isEmpty() )
    //        {
    //            bMatched = ( strSourceId == device.strSourceId );
    //        }
    //        else
    //        {
    //            qDebug() << "!!!!!!!!!!!!!!!!!!!!strDeviceNum and strSourceId are empty!";
    //        }
    //    }

    return bMatched;
}

/****************************
输入参数:doc -- 自定义xml文件
       pTestPoint -- 测点
出参：  lTestDataElement -- 测试数据节点的集合
功能： 将相关信息保存到测试数据中
*****************************/
void TaskFileIO::saveToTestData( QList<QDomElement>& lTestDataElement,XMLDocument& doc,ItemTestPoint* pTestPoint )
{
    bool bTestDataExist = false;   // 测试项在任务文件中是否存在的标志
    QString qstrNumber = "";
    //quint16 wIndex = 0;
    for (QVector<ItemTestData>::iterator iterTestData = pTestPoint->testDatas.begin();
         iterTestData != pTestPoint->testDatas.end(); ++iterTestData)
    {
        for (QList<QDomElement>::iterator iterElement = lTestDataElement.begin();
             iterElement != lTestDataElement.end(); ++iterElement)
        {
            doc.beginElement(*iterElement);
            qstrNumber = doc.value(TEST_DATA_NUMBER);
            //wIndex = doc.value( TEST_DATA_INDEX ).toUShort();
            //通过number将测试项唯一识别
            //通过index将测试项遍历，注意：当index不唯一时存在bug
            if( iterTestData->strNumber == qstrNumber/*iterTestData->usIndex == wIndex*/)
            {
                //已存在的测试项
                doc.setValue(TEST_DATA_IS_TEST, QString::number(iterTestData->bTested));
                doc.setValue(TEST_DATA_FILE_NAME, iterTestData->strFileName);
                doc.setValue(TEST_DATA_FILE_PATH, iterTestData->strFilePath);
                doc.setValue(TEST_DATA_ATTACH_PATH, iterTestData->strAttachPath);
                //doc.setValue(TEST_DATA_NUMBER, iterTestData->strNumber);
                doc.setValue(TEST_DATA_BAND_WIDTH, QString::number(iterTestData->eBandWidth));

                if(!(iterTestData->strRemotePath.isEmpty()))
                {
                    if(!(doc.hasElement(TEST_DATA_REMOTE_PATH)))
                    {
                        doc.addElement(TEST_DATA_REMOTE_PATH);
                    }
                    doc.setValue(TEST_DATA_REMOTE_PATH, iterTestData->strRemotePath);
                }

                if(!(iterTestData->strRemoteAttachPath.isEmpty()))
                {
                    if(!(doc.hasElement(TEST_DATA_REMOTE_ATTACH_PATH)))
                    {
                        doc.addElement(TEST_DATA_REMOTE_ATTACH_PATH);
                    }
                    doc.setValue(TEST_DATA_REMOTE_ATTACH_PATH, iterTestData->strRemoteAttachPath);
                }

                doc.endElement();
                bTestDataExist = true;
                break;
            }
            else
            {
                bTestDataExist = false;                // 未找到文件中的匹配项
            }

            doc.endElement();
        }

        if(!bTestDataExist)
        {
            // 新增测试项
            QDomElement elementTestData = doc.addElement(TEST_DATA);
            doc.beginElement(elementTestData);

            doc.setValue(TEST_DATA_INDEX, QString::number(iterTestData->usIndex));
            doc.setValue(TEST_DATA_IS_TEST, QString::number(iterTestData->bTested));
            doc.setValue(TEST_DATA_FILE_NAME, iterTestData->strFileName);
            doc.setValue(TEST_DATA_FILE_PATH, iterTestData->strFilePath);
            doc.setValue(TEST_DATA_ATTACH_PATH, iterTestData->strAttachPath);
            doc.setValue(TEST_DATA_NUMBER, iterTestData->strNumber);
            if(!(iterTestData->strRemotePath.isEmpty()))
            {
                if(!(doc.hasElement(TEST_DATA_REMOTE_PATH)))
                {
                    doc.addElement(TEST_DATA_REMOTE_PATH);
                }
                doc.setValue(TEST_DATA_REMOTE_PATH, iterTestData->strRemotePath);
            }

            if(!(iterTestData->strRemoteAttachPath.isEmpty()))
            {
                if(!(doc.hasElement(TEST_DATA_REMOTE_ATTACH_PATH)))
                {
                    doc.addElement(TEST_DATA_REMOTE_ATTACH_PATH);
                }
                doc.setValue(TEST_DATA_REMOTE_ATTACH_PATH, iterTestData->strRemoteAttachPath);
            }

            doc.setValue(TEST_DATA_TYPE, QString::number(iterTestData->eDataType));
            doc.setValue(TEST_DATA_BGN, QString::number(iterTestData->bIsBgn));
            doc.setValue(TEST_DATA_BAND_WIDTH, QString::number(iterTestData->eBandWidth));
            doc.setValue(TEST_DATA_DATA_UNIT, QString::number(iterTestData->eDataUnit));

            doc.endElement();
        }
    }

    return;
}

/****************************
输入参数:doc -- 自定义xml文件
       vDevices -- 设备的集合（saveToTestData同名设备）
出参：  lTestPointElement -- 测点的集合
功能： 将相关信息保存到测点中
*****************************/
void TaskFileIO::saveToTestPoint( QList<QDomElement> &lTestPointElement, XMLDocument &doc, QVector<ItemDevice*> &vDevices )
{
    QString strTestPointNumber = "";
    ItemTestPoint* pTestPoint = NULL;

    for(QList<QDomElement>::iterator iter = lTestPointElement.begin();
        iter != lTestPointElement.end(); ++iter)
    {
        doc.beginElement(*iter);

        strTestPointNumber = doc.value( TEST_POINT_NUMBER );                //测点序号

        pTestPoint = testPoint( strTestPointNumber, vDevices );       //获取指定测试序号的测点

        if(pTestPoint)
        {
            saveTestPointImageReleatedToTaskFile(doc, pTestPoint);
            doc.setValue( TEST_POINT_IS_TEST, QString::number( pTestPoint->bTested ) );
            doc.setValue( TEST_POINT_DATA_COUNT, QString::number( pTestPoint->testDatas.size() ) );

            //将测试数据保存到测点中
            QList<QDomElement> lTestDataElement = doc.childElement( TEST_DATA );
            saveToTestData( lTestDataElement, doc, pTestPoint );
        }
        else
        {
            qWarning("TaskFileIO::saveToTestPoint, not matched testpoint.");
        }

        doc.endElement();
    }

    return;
}

/****************************
输入参数:strTestPointNumber -- 测点序号
        vDevices -- 设备的集合（同名设备）
返回值：  ItemTestPoint -- 跟number匹配的测点
功能： 根据测点序号获取相应测点
*****************************/
ItemTestPoint* TaskFileIO::testPoint(const QString &strTestPointNumber, QVector<ItemDevice*> &vDevices)
{
    for(int i = 0, iDevSize = vDevices.size(); i < iDevSize; ++i)
    {
        for(int j = 0, iTestPointSize = vDevices[i]->testPoints.size(); j < iTestPointSize; ++j)
        {
            if(strTestPointNumber == vDevices[i]->testPoints[j].strItemNumber)
            {
                return &(vDevices[i]->testPoints[j]);
            }
        }
    }

    return NULL;
}

/************************************************
 * 输入参数 : dateTime: 时间
 * 返回值   : 指定格式时间字符串
 * 功能     : 转换时间为测试时间格式
 ************************************************/
QString TaskFileIO::convertTimeToTestTimeFormat( const QDateTime& dateTime )
{
    QString strDateTime = dateTime.toString( TASK_TIME_FORMAT );
    return strDateTime;
}

/************************************************
 * 输入参数 : strValue: 电压等级的字符串表示   数字+标示+展示组成，如220_5_V
 * 输出参数 : uiVoltage--电压数值   eUnitCode--电压单位
 * 返回值   : 是否成功解析
 * 功能     : 将电压等级的字符串表示解析为相应的数值
 ************************************************/
bool TaskFileIO::parseVoltageValue( PDAServiceNS::VoltageLevel eVoltageLevel,
                                    float& fVoltage, PDAServiceNS::TaskUnitCode& eUnitCode )
{
    bool bSucceed = true;
    switch (eVoltageLevel)
    {
    case PDAServiceNS::VOLTAGE_110_V:
    {
        fVoltage = 110;
        eUnitCode = PDAServiceNS::Voltage_V_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_220_V:
    {
        fVoltage = 220;
        eUnitCode = PDAServiceNS::Voltage_V_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_380_V:
    {
        fVoltage = 380;
        eUnitCode = PDAServiceNS::Voltage_V_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_6_kV:
    {
        fVoltage = 6;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_10_kV:
    {
        fVoltage = 10;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_12_kV:
    {
        fVoltage = 12;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_20_kV:
    {
        fVoltage = 20;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_35_kV:
    {
        fVoltage = 35;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_66_kV:
    {
        fVoltage = 66;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_110_kV:
    {
        fVoltage = 110;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_220_kV:
    {
        fVoltage = 220;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_330_kV:
    {
        fVoltage = 330;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_CH_500_kV:
    case PDAServiceNS::VOLTAGE_US_500_kV:
    {
        fVoltage = 500;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_750_kV:
    {
        fVoltage = 750;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_800_kV:
    {
        fVoltage = 800;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_1000_kV:
    {
        fVoltage = 1000;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_0_POINT_24_kV:
    {
        fVoltage = 0.24;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_0_POINT_48_kV:
    {
        fVoltage = 0.48;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_0_POINT_6_kV:
    {
        fVoltage = 0.6;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_2_POINT_4_kV:
    {
        fVoltage = 2.4;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_4_POINT_16_kV:
    {
        fVoltage = 4.16;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_4_POINT_8_kV:
    {
        fVoltage = 4.8;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_6_POINT_9_kV:
    {
        fVoltage = 6.9;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_13_POINT_8_kV:
    {
        fVoltage = 13.8;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_23_kV:
    {
        fVoltage = 23;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_34_POINT_5_kV:
    {
        fVoltage = 34.5;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_46_kV:
    {
        fVoltage = 46;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_69_kV:
    {
        fVoltage = 69;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_115_kV:
    {
        fVoltage = 115;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_138_kV:
    {
        fVoltage = 138;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_161_kV:
    {
        fVoltage = 161;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_230_kV:
    {
        fVoltage = 230;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_345_kV:
    {
        fVoltage = 345;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_765_kV:
    {
        fVoltage = 765;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    case PDAServiceNS::VOLTAGE_1100_kV:
    {
        fVoltage = 1100;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
    }
        break;
    default:
    {
        fVoltage = 0;
        eUnitCode = PDAServiceNS::Voltage_KV_Unit;
        break;
    }
    }

    return bSucceed;
}

/************************************************
 * 输入参数 : stPath: media/image file path链表
 * 输出参数 : NULL
 * 返回值   : 所有file path的list
 * 功能     : 遍历media/image file path链表，获取所有的file path
 ************************************************/
QStringList TaskFileIO::allImgMediaFilesPath(const ImgMediaPath &stPath)
{
    QStringList listFilesPath;
    listFilesPath.append(stPath.strMainImgPath);
    listFilesPath.append(stPath.listMediaPath);
    listFilesPath.append(stPath.listImgPath);

    for(int i = 0, iSize = stPath.vtSubLevel.size(); (i < stPath.iSubCount) && (i < iSize); ++i)
    {
        listFilesPath.append(allImgMediaFilesPath(stPath.vtSubLevel.at(i)));
    }

    return listFilesPath;
}

bool TaskFileIO::testDataSort(const ItemTestData& stTestData1, const ItemTestData& stTestData2)
{
    return (stTestData1.usIndex <= stTestData2.usIndex);
}

bool TaskFileIO::testPointSort(const ItemTestPoint& stTestPoint1, const ItemTestPoint& stTestPoint2)
{
    return (stTestPoint1.usIndex <= stTestPoint2.usIndex);
}

bool TaskFileIO::testDeviceSort(const ItemDevice& stDev1, const ItemDevice& stDev2)
{
    return (stDev1.usIndex <= stDev2.usIndex);
}

bool TaskFileIO::testDeviceTaskInfoSort(const ItemDeviceTaskFileInfo& stDev1, const ItemDeviceTaskFileInfo& stDev2)
{
    return (stDev1.usIndex <= stDev2.usIndex);
}

bool TaskFileIO::testBaySort(const ItemBay& stBay1, const ItemBay& stBay2)
{
    return (stBay1.usIndex <= stBay2.usIndex);
}

/*******************************************************
 * 功能：获取当前任务临时数据ini文件的文件夹路径
 * 返回值：
 *      QString：当前任务临时数据ini文件的文件夹路径
 * *********************************************************/
QString TaskFileIO::getCurTaskIniFolder()
{
    return m_qstrCurTaskIniFolder;
}

/*******************************************************
 * 功能：设置当前任务临时数据ini文件的文件夹路径
 * 输入参数：
 *      qstrCurTaskFilePath：当前任务文件路径
 * *********************************************************/
void TaskFileIO::setCurTaskIniFolder(const QString qstrCurTaskFilePath)
{
    m_qstrCurTaskIniFolder = FileOperUtil::getFileParentFolder(qstrCurTaskFilePath) + "/" + PDA_TASK_TEMP_INI_FOLDER;
    return;
}

/*****************************************
 * 功能：解析任务信息
 * ***************************************/
void TaskFileIO::parseTaskInfo(const QString &qstrFilePath, TaskInfo &stTaskInfo, QVector<ItemBay> &qvtItemBays)
{
    IOStateType eType = checkFileValidity(qstrFilePath);
    if(OPERATE_SUCCESS == eType) // 文件合法性校验
    {
        XMLDocument doc(qstrFilePath, QIODevice::ReadOnly, ROOT_NODE);
        if(doc.isValid())
        {
            stTaskInfo.strFilePath = qstrFilePath;

            doc.beginElement(TASK_NODE);
            stTaskInfo.uiTotalCount = doc.value(TASK_TOTAL_DATA).toUInt();
            stTaskInfo.uiTestedCount = doc.value(TASK_TESTED_DATA).toUInt();
            stTaskInfo.eSourcePlatform = (PDAServiceNS::PDASourcePlatform) doc.value(TEST_SOURCE_PLATFORM).toUShort();
            stTaskInfo.strTestNumber = doc.value(TASK_TEST_NUMBER);
            stTaskInfo.strInnerId = doc.value(TASK_INNER_ID);
            stTaskInfo.strItemName = doc.value(TASK_NAME);
            stTaskInfo.lCreateTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(doc.value(TASK_CREATE_TIME).toLongLong());  //UTC时间(s)，实际app使用的ms，因为版本已经发布，因而适配为ms

            QDateTime stCreateTime = QDateTime::fromTime_t((uint)(stTaskInfo.lCreateTime / 1000));           //计算值单位为s计算
            stTaskInfo.strCreateTime = stCreateTime.toString(TASK_TIME_FORMAT);

            stTaskInfo.lPlanTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(doc.value(TASK_PLAN_TIME).toLongLong());
            stTaskInfo.lTestTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(doc.value(TASK_TEST_TIME).toLongLong());

            QDateTime stPlanTime = QDateTime::fromTime_t((uint)(stTaskInfo.lPlanTime / 1000));           //计算值单位为s计算
            stTaskInfo.strPlanTime = stPlanTime.toString(TASK_TIME_FORMAT);

            QDateTime stTestTime = QDateTime::fromTime_t((uint)(stTaskInfo.lTestTime / 1000));           //计算值单位为s计算
            stTaskInfo.strTestTime = stTestTime.toString(TASK_TIME_FORMAT);

            stTaskInfo.strStationNo = doc.value(STATION_NUMBER);
            stTaskInfo.strStationName = doc.value(STATION_NAME);

            stTaskInfo.dVoltage = doc.value(TASK_VOLTAGE).toDouble();
            stTaskInfo.eVoltageUnit = (PDAServiceNS::TaskUnitCode)doc.value(TASK_VOLTAGE_UNIT).toUShort();

            stTaskInfo.stWeatherInfo.eWeather = (PDAServiceNS::Weather)(doc.value(TASK_WEATHER).toUShort());
            stTaskInfo.stWeatherInfo.dTemperature = doc.value(TASK_TEMPERATURE).toDouble();
            stTaskInfo.eTempUnit = (PDAServiceNS::DataUnit)(doc.value(TASK_TEMPERATURE_UNIT).toUShort());
            stTaskInfo.stWeatherInfo.dHumidity = doc.value(TASK_HUMIDITY).toDouble();

            stTaskInfo.usBayCount = doc.value(TASK_BAY_COUNT).toUShort();
            stTaskInfo.strMainImgFilePath = doc.value(TASK_MAIN_IMAGE_FILE_PATH);
            if(doc.hasElement(TASK_REMOTE_MAIN_IMAGE_FILE_PATH))
            {
                stTaskInfo.strRemoteMainImgFilePath = doc.value(TASK_REMOTE_MAIN_IMAGE_FILE_PATH);
            }

            QList<QDomElement> qlMediaFilePath = doc.childElement(TASK_MEDIA_FILE_PATH);
            for(QList<QDomElement>::iterator iter = qlMediaFilePath.begin();
                iter != qlMediaFilePath.end(); ++iter)
            {
                doc.beginElement(*iter);
                if(!(stTaskInfo.lMediaFilePath.contains(doc.text())))
                {
                    stTaskInfo.lMediaFilePath.append(doc.text());
                }
                doc.endElement();
            }

            QList<QDomElement> qlRemoteMediaFilePath = doc.childElement(TASK_REMOTE_MEDIA_FILE_PATH);
            for(QList<QDomElement>::iterator iter = qlRemoteMediaFilePath.begin();
                iter != qlRemoteMediaFilePath.end(); ++iter)
            {
                doc.beginElement(*iter);
                if(!(stTaskInfo.lRemoteMediaFilePath.contains(doc.text())))
                {
                    stTaskInfo.lRemoteMediaFilePath.append(doc.text());
                }
                doc.endElement();
            }

            QList<QDomElement> qlImageFilePath = doc.childElement(TASK_IMAGE_FILE_PATH);
            for(QList<QDomElement>::iterator iter = qlImageFilePath.begin();
                iter != qlImageFilePath.end(); ++iter)
            {
                doc.beginElement(*iter);
                if(!(stTaskInfo.lImageFilePath.contains(doc.text())))
                {
                    stTaskInfo.lImageFilePath.append(doc.text());
                }
                doc.endElement();
            }

            QList<QDomElement> qlRemoteImageFilePath = doc.childElement(TASK_REMOTE_IMAGE_FILE_PATH);
            for(QList<QDomElement>::iterator iter = qlRemoteImageFilePath.begin();
                iter != qlRemoteImageFilePath.end(); ++iter)
            {
                doc.beginElement(*iter);
                if(!(stTaskInfo.lRemoteImageFilePath.contains(doc.text())))
                {
                    stTaskInfo.lRemoteImageFilePath.append(doc.text());
                }
                doc.endElement();
            }

            doc.endElement();

            // 解析间隔
            QList<QDomElement> lBayElement = doc.childElement(BAY_NODE);
            for(QList<QDomElement>::iterator iterBay = lBayElement.begin();
                iterBay != lBayElement.end(); ++iterBay)
            {
                doc.beginElement(*iterBay);

                //读取任务文件里间隔节点的一些信息
                ItemBay stBay;
                stBay.strNumber = doc.value(BAY_NUMBER);
                stBay.eType = (PDAServiceNS::PatrolType) doc.value(BAY_DEVICE_TYPE).toUShort();
                stBay.dVoltage =  doc.value(BAY_VOLTAGE).toDouble();
                stBay.eVoltageUnit =  (PDAServiceNS::TaskUnitCode) doc.value(BAY_VOLTAGE_UNIT).toUShort();
                stBay.usIndex =  doc.value(BAY_INDEX).toUShort();
                stBay.isDefault = doc.value(BAY_IS_DEFAULT).toUShort();
                stBay.strName = doc.value(BAY_NAME);
                stBay.usDeviceCount = doc.value(BAY_DEVICE_COUNT).toUShort();
                stBay.isBGN = doc.value(BAY_BGN).toUShort();

                //读取任务文件里间隔节点的图片，音频文件路径
                stBay.strMainImgFilePath = doc.value(BAY_MAIN_IMAGE_FILE_PATH);

                //remote main image file path
                if(doc.hasElement(BAY_REMOTE_MAIN_IMAGE_FILE_PATH))
                {
                    stBay.strRemoteMainImgFilePath = doc.value(BAY_REMOTE_MAIN_IMAGE_FILE_PATH);
                }

                //mediaFilePath
                QList<QDomElement> qlMediaFilePath = doc.childElement(BAY_MEDIA_FILE_PATH);
                for(QList<QDomElement>::iterator iter = qlMediaFilePath.begin();
                    iter != qlMediaFilePath.end(); ++iter)
                {
                    doc.beginElement(*iter);
                    if(!(stBay.lMediaFilePath.contains(doc.text())))
                    {
                        stBay.lMediaFilePath.append(doc.text());
                    }
                    doc.endElement();
                }

                //remote mediaFilePath
                QList<QDomElement> qlRemoteMediaFilePath = doc.childElement(BAY_REMOTE_MAIN_IMAGE_FILE_PATH);
                for(QList<QDomElement>::iterator iter = qlRemoteMediaFilePath.begin();
                    iter != qlRemoteMediaFilePath.end(); ++iter)
                {
                    doc.beginElement(*iter);
                    if(!(stBay.lRemoteMediaFilePath.contains(doc.text())))
                    {
                        stBay.lRemoteMediaFilePath.append(doc.text());
                    }
                    doc.endElement();
                }

                //imgFilePath
                QList<QDomElement> qlImageFilePath = doc.childElement(BAY_IMAGE_FILE_PATH);
                for(QList<QDomElement>::iterator iter = qlImageFilePath.begin();
                    iter != qlImageFilePath.end(); ++iter)
                {
                    doc.beginElement(*iter);
                    if(!(stBay.lImageFilePath.contains(doc.text())))
                    {
                        stBay.lImageFilePath.append(doc.text());
                    }
                    doc.endElement();
                }

                //remote imgFilePath
                QList<QDomElement> qlRemoteImageFilePath = doc.childElement(BAY_REMOTE_IMAGE_FILE_PATH);
                for(QList<QDomElement>::iterator iter = qlRemoteImageFilePath.begin();
                    iter != qlRemoteImageFilePath.end(); ++iter)
                {
                    doc.beginElement(*iter);
                    if(!(stBay.lRemoteImageFilePath.contains(doc.text())))
                    {
                        stBay.lRemoteImageFilePath.append(doc.text());
                    }
                    doc.endElement();
                }

                //读取任务文件里设备节点的一些信息
                QList<QDomElement> lDeviceElement = doc.childElement(DEVICE_NODE);
                for (QList<QDomElement>::iterator iterDev = lDeviceElement.begin();
                     iterDev != lDeviceElement.end(); ++iterDev)
                {
                    doc.beginElement(*iterDev);

                    ItemDeviceTaskFileInfo stDev;
                    stDev.usIndex = doc.value(DEVICE_INDEX).toUShort();   // 设备名
                    stDev.b_isBGNTest = doc.value(DEVICE_BGN).toInt();     //是否背景测试
                    stDev.strItemName = doc.value(DEVICE_NAME);
                    stDev.strDevNum = doc.value(DEVICE_NUMBER);

                    stDev.iDevType = doc.value( DEVICE_PATROL_TYPE ).toInt();     // 设备类型
                    stDev.strType = stringFromPatrolType(static_cast<PDAServiceNS::PatrolType>(stDev.iDevType));

                    stDev.dVoltage =  doc.value(DEVICE_VOLTAGE).toDouble();
                    stDev.eVoltageUnit =  (PDAServiceNS::TaskUnitCode) doc.value(DEVICE_VOLTAGE_UNIT).toUShort();

                    stDev.dLoadCurrent = doc.value(DEVICE_LOAD_CURRENT).toDouble();
                    stDev.eLoadCurrentUnit = (PDAServiceNS::DataUnit)(doc.value(DEVICE_LOAD_CURRENT_UNIT).toUShort());
                    stDev.dwLoadCurrentSign = doc.value(DEVICE_LOAD_CURRENT_SIGN).toUInt();

                    stDev.strDevModel = doc.value(DEVICE_MODEL);     // 设备型号
                    stDev.strManufacturer = doc.value(DEVICE_MANUFACTURER);     // 生产厂家
                    //stDev.strProduceDate = doc.value(DEVICE_PRODUCE_DATE);     // 出厂日期
                    qint64 lUtcTime = doc.value(DEVICE_PRODUCE_DATE).toLongLong();       //UTC时间(s)，实际app使用的ms，因为版本已经发布，因而适配为ms
                    lUtcTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(lUtcTime);
                    QDateTime stFmtTime = QDateTime::fromTime_t((uint)(lUtcTime / 1000));           //计算值单位为s计算
                    stDev.strProduceDate = stFmtTime.toString(DEVICE_TIME_FORMAT);     // 出厂日期

                    stDev.uiTotalCount = doc.value(DEVICE_TEST_POINT_COUNT).toUShort();
                    stDev.eFilterSign = (PDAServiceNS::TestPointFilterSign)doc.value(DEVICE_TEST_POINT_FILTER_SIGN).toUShort();

                    //读取任务文件里设备节点的图片，音频文件路径
                    stDev.strMainImgFilePath = doc.value(DEVICE_MAIN_IMAGE_FILE_PATH);
                    //remote main image file path
                    if(doc.hasElement(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH))
                    {
                        stDev.strRemoteMainImgFilePath = doc.value(DEVICE_REMOTE_MAIN_IMAGE_FILE_PATH);
                    }

                    //读取任务文件里设备节点的media file path
                    QList<QDomElement> qlMediaFilePath = doc.childElement(DEVICE_MEDIA_FILE_PATH);
                    for (QList<QDomElement>::iterator iter = qlMediaFilePath.begin();
                         iter != qlMediaFilePath.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        if(!(stDev.lMediaFilePath.contains(doc.text())))
                        {
                            stDev.lMediaFilePath.append(doc.text());
                        }
                        doc.endElement();
                    }

                    //remote media file path
                    QList<QDomElement> qlRemoteMediaFilePath = doc.childElement(DEVICE_REMOTE_MEDIA_FILE_PATH);
                    for (QList<QDomElement>::iterator iter = qlRemoteMediaFilePath.begin();
                         iter != qlRemoteMediaFilePath.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        if(!(stDev.lRemoteMediaFilePath.contains(doc.text())))
                        {
                            stDev.lRemoteMediaFilePath.append(doc.text());
                        }
                        doc.endElement();
                    }

                    //读取任务文件里设备节点的image file path
                    QList<QDomElement> qlImageFilePath = doc.childElement(DEVICE_IMAGE_FILE_PATH);
                    for (QList<QDomElement>::iterator iter = qlImageFilePath.begin();
                         iter != qlImageFilePath.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        if(!(stDev.lImageFilePath.contains(doc.text())))
                        {
                            stDev.lImageFilePath.append(doc.text());
                        }
                        doc.endElement();
                    }

                    //remote image file path
                    QList<QDomElement> qlRemoteImageFilePath = doc.childElement(DEVICE_REMOTE_IMAGE_FILE_PATH);
                    for (QList<QDomElement>::iterator iter = qlRemoteImageFilePath.begin();
                         iter != qlRemoteImageFilePath.end(); ++iter)
                    {
                        doc.beginElement(*iter);
                        if(!(stDev.lRemoteImageFilePath.contains(doc.text())))
                        {
                            stDev.lRemoteImageFilePath.append(doc.text());
                        }
                        doc.endElement();
                    }

                    QList<QDomElement> lTestPointElement = doc.childElement(DEVICE_TEST_POINT);
                    for (QList<QDomElement>::const_iterator iterPoint = lTestPointElement.begin();
                         iterPoint != lTestPointElement.end(); ++iterPoint)
                    {
                        doc.beginElement(*iterPoint);

                        ItemTestPoint tmpPoint;
                        tmpPoint.strItemNumber = doc.value(TEST_POINT_NUMBER);
                        tmpPoint.strItemName = doc.value(TEST_POINT_NAME);

                        QString strTested = doc.value(TEST_POINT_IS_TEST);
                        tmpPoint.bTested = (bool)(strTested.toInt());
                        tmpPoint.eType = (PDAServiceNS::TestPointType)(doc.value(TEST_POINT_TYPE).toInt());
                        tmpPoint.usTestDataCount = doc.value(TEST_POINT_DATA_COUNT).toUShort();
                        tmpPoint.usIndex = doc.value(TEST_POINT_INDEX).toShort();

                        //读取任务文件里设备节点的图片，音频文件路径
                        tmpPoint.strMainImgFilePath = doc.value(TEST_POINT_MAIN_IMAGE_FILE_PATH);

                        //remote main image file path
                        if(doc.hasElement(TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH))
                        {
                            tmpPoint.strRemoteMainImgFilePath = doc.value(TEST_POINT_REMOTE_MAIN_IMAGE_FILE_PATH);
                        }

                        //读取任务文件里设备节点的media file path
                        QList<QDomElement> qlMediaFilePath = doc.childElement(TEST_POINT_MEDIA_FILE_PATH);
                        for(QList<QDomElement>::iterator iter = qlMediaFilePath.begin();
                            iter != qlMediaFilePath.end(); ++iter)
                        {
                            doc.beginElement(*iter);
                            if(!(tmpPoint.lMediaFilePath.contains(doc.text())))
                            {
                                tmpPoint.lMediaFilePath.append(doc.text());
                            }
                            doc.endElement();
                        }

                        //remote media file path
                        QList<QDomElement> qlRemoteMediaFilePath = doc.childElement(TEST_POINT_REMOTE_MEDIA_FILE_PATH);
                        for(QList<QDomElement>::iterator iter = qlRemoteMediaFilePath.begin();
                            iter != qlRemoteMediaFilePath.end(); ++iter)
                        {
                            doc.beginElement(*iter);
                            if(!(tmpPoint.lRemoteMediaFilePath.contains(doc.text())))
                            {
                                tmpPoint.lRemoteMediaFilePath.append(doc.text());
                            }
                            doc.endElement();
                        }

                        //读取任务文件里设备节点的image file path
                        QList<QDomElement> qlImageFilePath = doc.childElement(TEST_POINT_IMAGE_FILE_PATH);
                        for(QList<QDomElement>::iterator iter = qlImageFilePath.begin();
                            iter != qlImageFilePath.end(); ++iter)
                        {
                            doc.beginElement(*iter);
                            if(!(tmpPoint.lImageFilePath.contains(doc.text())))
                            {
                                tmpPoint.lImageFilePath.append(doc.text());
                            }
                            doc.endElement();
                        }

                        //remote image file path
                        QList<QDomElement> qlRemoteImageFilePath = doc.childElement(TEST_POINT_REMOTE_IMAGE_FILE_PATH);
                        for(QList<QDomElement>::iterator iter = qlRemoteImageFilePath.begin();
                            iter != qlRemoteImageFilePath.end(); ++iter)
                        {
                            doc.beginElement(*iter);
                            if(!(tmpPoint.lRemoteImageFilePath.contains(doc.text())))
                            {
                                tmpPoint.lRemoteImageFilePath.append(doc.text());
                            }
                            doc.endElement();
                        }

                        QList<QDomElement> lTestDataElement = doc.childElement(TEST_DATA);
                        for (QList<QDomElement>::const_iterator iterData = lTestDataElement.begin();
                             iterData != lTestDataElement.end(); ++iterData)
                        {
                            doc.beginElement(*iterData);

                            ItemTestData tmpTestData;
                            tmpTestData.usIndex = doc.value(TEST_DATA_INDEX).toUShort();

                            tmpTestData.strNumber = doc.value(TEST_DATA_NUMBER);
                            tmpTestData.bTested = (bool)doc.value(TEST_DATA_IS_TEST).toInt();
                            tmpTestData.strFileName = doc.value(TEST_DATA_FILE_NAME);
                            tmpTestData.strFilePath = doc.value(TEST_DATA_FILE_PATH);
                            tmpTestData.strAttachPath = doc.value(TEST_DATA_ATTACH_PATH);
                            tmpTestData.eDataType = (PDAServiceNS::TestDataType)doc.value(TEST_DATA_TYPE).toInt();
                            tmpTestData.bIsBgn = (bool)doc.value(TEST_DATA_BGN).toInt();
                            if(doc.hasElement(TEST_DATA_REMOTE_PATH))
                            {
                                tmpTestData.strRemotePath = doc.value(TEST_DATA_REMOTE_PATH);
                            }

                            if(doc.hasElement(TEST_DATA_REMOTE_ATTACH_PATH))
                            {
                                tmpTestData.strRemoteAttachPath = doc.value(TEST_DATA_REMOTE_ATTACH_PATH);
                            }

                            tmpTestData.eBandWidth = (PDAServiceNS::UhfBWType)(doc.value(TEST_DATA_BAND_WIDTH).toInt());
                            tmpTestData.eDataUnit = (PDAServiceNS::DataUnit)(doc.value(TEST_DATA_DATA_UNIT).toInt());

                            doc.endElement();

                            tmpPoint.testDatas.append(tmpTestData);
                        }

                        doc.endElement();

                        stDev.testPoints.append(tmpPoint);
                    }

                    doc.endElement();

                    stBay.vecDevice.append(stDev);
                }

                doc.endElement();

                qvtItemBays.append(stBay);
            }

        }
        else
        {
            logError("open xml doc failed.");
        }
    }
    else
    {
        logError("file is invalid.");
    }

    return;
}

/*****************************************
 * 功能：写入任务信息
 * ***************************************/
void TaskFileIO::updateTaskInfo(const TaskInfo &stTaskInfo, const QVector<ItemBay> &qvtItemBays)
{
    IOStateType eType = checkFileValidity(stTaskInfo.strFilePath);
    if(OPERATE_SUCCESS == eType)
    {
        XMLDocument doc(stTaskInfo.strFilePath, QIODevice::ReadWrite, ROOT_NODE);
        if(doc.isValid())
        {
            //测试完成时间，由T95填写
            qint64 lTestTime = TimezoneManager::instance()->formatLocalTimeToUTCValMsec(QDateTime::currentDateTimeUtc().toMSecsSinceEpoch());

            doc.beginElement(TASK_NODE);
            //doc.setValue(TASK_TEST_NUMBER, stTaskInfo.strTestNumber);
            doc.setValue(TASK_TOTAL_DATA, QString::number(stTaskInfo.uiTotalCount));
            doc.setValue(TASK_TESTED_DATA, QString::number(stTaskInfo.uiTestedCount));
            doc.setValue(TASK_TEST_TIME, QString::number(lTestTime));
            doc.setValue(TASK_WEATHER, QString::number((int)stTaskInfo.stWeatherInfo.eWeather));
            doc.setValue(TASK_TEMPERATURE, QString::number(stTaskInfo.stWeatherInfo.dTemperature));
            doc.setValue(TASK_TEMPERATURE_UNIT, QString::number((int)stTaskInfo.eTempUnit));
            doc.setValue(TASK_HUMIDITY, QString::number(stTaskInfo.stWeatherInfo.dHumidity));
            doc.endElement();

            QString strNumber = "";
            bool bFound = false;

            // 遍历间隔分组
            QList<QDomElement> lBayElement = doc.childElement(BAY_NODE);
            for (QList<QDomElement>::iterator iterBay = lBayElement.begin();
                 iterBay != lBayElement.end(); ++iterBay)
            {
                const ItemBay *pBay = NULL; //指向常量的指针，内容为常量
                doc.beginElement(*iterBay);
                strNumber = doc.value(BAY_NUMBER);

                for(int i = 0, iSize = qvtItemBays.size(); i < iSize; ++i)
                {
                    if(strNumber == qvtItemBays[i].strNumber)
                    {
                        pBay = &(qvtItemBays[i]);
                        bFound = true;
                        break;
                    }
                }

                if(bFound && pBay)
                {
                    //根据设备更新测点和测试数据信息
                    QList<QDomElement> lDeviceElement = doc.childElement( DEVICE_NODE );
                    for (QList<QDomElement>::iterator iter = lDeviceElement.begin();
                         iter != lDeviceElement.end(); ++iter)
                    {
                        doc.beginElement(*iter);

                        // 获取设备的编号
                        QString strDeviceNum = doc.value(DEVICE_NUMBER);
                        bool bDevFound = false;
                        const ItemDeviceTaskFileInfo *pDev = NULL;
                        for(int j = 0, iDevSize = pBay->vecDevice.size(); j < iDevSize; ++j)
                        {
                            if(strDeviceNum == pBay->vecDevice[j].strDevNum)
                            {
                                pDev = &(pBay->vecDevice[j]);
                                bDevFound = true;
                                break;
                            }
                        }

                        if(bDevFound && pDev)
                        {
                            //写入负荷电流信息
                            doc.setValue(DEVICE_LOAD_CURRENT, QString::number(pDev->dLoadCurrent));
                            doc.setValue(DEVICE_LOAD_CURRENT_UNIT, QString::number((int)(pDev->eLoadCurrentUnit)));
                            doc.setValue(DEVICE_LOAD_CURRENT_SIGN, QString::number(pDev->dwLoadCurrentSign));

                            //写入测点，将设备中的信息保存到任务文件的测点中
                            QList<QDomElement> lTestPointElement = doc.childElement(DEVICE_TEST_POINT);
                            for(QList<QDomElement>::iterator iter = lTestPointElement.begin();
                                iter != lTestPointElement.end(); ++iter)
                            {
                                doc.beginElement(*iter);

                                QString strTestPointNumber = doc.value(TEST_POINT_NUMBER);                //测点序号
                                const ItemTestPoint *pTestPoint = NULL;
                                bool bTestPointFound = false;
                                for(int k = 0, iTPSize = pDev->testPoints.size(); k < iTPSize; ++k)
                                {
                                    if(strTestPointNumber == pDev->testPoints[k].strItemNumber)
                                    {
                                        pTestPoint = &(pDev->testPoints[k]);
                                        bTestPointFound = true;
                                        break;
                                    }
                                }

                                if(bTestPointFound && pTestPoint)
                                {
                                    doc.setValue(TEST_POINT_IS_TEST, QString::number(pTestPoint->bTested));
                                    doc.setValue(TEST_POINT_DATA_COUNT, QString::number(pTestPoint->testDatas.size()));

                                    //将测试数据保存到测点中
                                    QList<QDomElement> lTestDataElement = doc.childElement(TEST_DATA);
                                    for (QList<QDomElement>::iterator iterElement = lTestDataElement.begin();
                                         iterElement != lTestDataElement.end(); ++iterElement)
                                    {
                                        doc.beginElement(*iterElement);
                                        QString qstrTestDataNumber = doc.value(TEST_DATA_NUMBER);
                                        const ItemTestData *pTestData = NULL;
                                        bool bTestDataFound = false;

                                        for(int l = 0, iTDSize = pTestPoint->testDatas.size(); l < iTDSize; ++l)
                                        {
                                            if(qstrTestDataNumber == pTestPoint->testDatas[l].strNumber)
                                            {
                                                pTestData = &(pTestPoint->testDatas[l]);
                                                bTestDataFound = true;
                                                break;
                                            }
                                        }

                                        if(bTestDataFound && pTestData)
                                        {
                                            doc.setValue(TEST_DATA_IS_TEST, QString::number(pTestData->bTested));
                                            doc.setValue(TEST_DATA_FILE_NAME, pTestData->strFileName);
                                            doc.setValue(TEST_DATA_FILE_PATH, pTestData->strFilePath);
                                            doc.setValue(TEST_DATA_ATTACH_PATH, pTestData->strAttachPath);
                                        }

                                        doc.endElement();
                                    }

                                }

                                doc.endElement();
                            }
                        }

                        doc.endElement();
                    }
                }

                doc.endElement();
            }

            if(!doc.save())
            {
                logError("save xml doc failed.");
            }
        }
        else
        {
            logError("open xml doc failed.");
        }
    }
    else
    {
        logError("file is invalid.");
    }

    return;
}

