#include "newtooltip.h"
#include <QApplication>
#include <QStyle>
#include <QMouseEvent>
#include <QStylePainter>
#include <QStyleHintReturnMask>
#include <QScreen>
#include <QTextDocument>
#include <QDesktopWidget>
#include <QDebug>

NewTipLabel *NewTipLabel::instance = 0;

NewTipLabel::NewTipLabel(const QString &text, const QPoint &pos, const NewToolTip::TextDirection eTextDirection, QWidget *w, int msecDisplayTime)
    : QLabel(w, Qt::ToolTip | Qt::BypassGraphicsProxyWidget), widget(0), m_eTextDirection(NewToolTip::Horizontal)
{
    delete instance;
    instance = this;
    setForegroundRole(QPalette::ToolTipText);
    setBackgroundRole(QPalette::ToolTipBase);
    setPalette(NewToolTip::palette());
    ensurePolished();
    setMargin(1 + style()->pixelMetric(QStyle::PM_ToolTipLabelFrameWidth, 0, this));
    setFrameStyle(QFrame::NoFrame);
    setAlignment(Qt::AlignLeft);
    setIndent(1);
    qApp->installEventFilter(this);
    setWindowOpacity(style()->styleHint(QStyle::SH_ToolTipLabel_Opacity, 0, this) / 255.0);
    setMouseTracking(true);
    fadingOut = false;
    reuseTip(text, msecDisplayTime, pos, eTextDirection);

#ifndef QT_NO_STYLE_STYLESHEET
    if (testAttribute(Qt::WA_StyleSheet))
    {
        setStyleSheet("background-color: rgb(239, 239, 239);");
    }
#endif //QT_NO_STYLE_STYLESHEET
}

void NewTipLabel::restartExpireTimer(int msecDisplayTime)
{
    int time = 10000 + 40 * qMax(0, text().length()-100);
    if (msecDisplayTime > 0)
        time = msecDisplayTime;
    expireTimer.start(time, this);
    hideTimer.stop();
}

void NewTipLabel::reuseTip(const QString &text, int msecDisplayTime, const QPoint &pos, const NewToolTip::TextDirection eTextDirection, bool bTextProcessed)
{
    if (eTextDirection != m_eTextDirection)
    {
        m_eTextDirection = eTextDirection;
    }

#if 0 // Used to be included in Qt4 for Q_WS_MAC
    // When in full screen mode, there is no Dock nor Menu so we can use
    // the whole screen for displaying the tooltip. However when not in
    // full screen mode we need to save space for the dock, so we use
    // availableGeometry instead.
    extern bool qt_mac_app_fullscreen; //qapplication_mac.mm
    QRect screen;
    if(qt_mac_app_fullscreen)
        screen = QApplication::desktop()->screenGeometry(getTipScreen(pos, widget));
    else
        screen = QApplication::desktop()->availableGeometry(getTipScreen(pos, widget));
#else
    QRect screen = QApplication::desktop()->screenGeometry(getTipScreen(pos, widget));
#endif

    if (bTextProcessed)
    {

        QStringList qlstSentences = text.split("\n");

        if (NewToolTip::Vertical == m_eTextDirection)
        {
    //        qDebug () << "iTextWidth: " << iTextWidth;
    //        qDebug () << "screen.height(): " << screen.height();
    //        if (iTextWidth >= screen.height())
    //        {
    //            qDebug () << " NewTipLabel::reuseTip1: ";
    //            setWordWrap(true);
    //            setMinimumWidth(screen.height());
    //        }
    //        else
    //        {
    //            qDebug () << " NewTipLabel::reuseTip2: ";
    //            setMinimumWidth(iTextWidth);
    //        }
        }
        else
        {
            int iMinRowCountWidthMax = 0;
            int iMaxRowCount = 0;
            // 计算出最小行数的宽度
            for (int i = 0; i < qlstSentences.size(); ++i)
            {
                int iRow = 0;
                int iMinRowCountWidth = calMinRowCountWidth(qlstSentences[i], screen.width(), iRow);

                if (iMinRowCountWidth > iMinRowCountWidthMax)
                {
                    iMinRowCountWidthMax = iMinRowCountWidth;
                }

                if (iRow > iMaxRowCount)
                {
                    iMaxRowCount = iRow;
                }
            }

            if (iMaxRowCount > 1)
            {
                // 如果某段句子中有需要超过1行显示则需要换行
                setWordWrap(true);
            }
            else
            {
                setWordWrap(Qt::mightBeRichText(text));
            }
            setMinimumWidth(iMinRowCountWidthMax);
        }

    }
    setText(text);

    QFontMetrics fm(font());
    QSize extra(1, 0);
    // Make it look good with the default ToolTip font on Mac, which has a small descent.
    if (fm.descent() == 2 && fm.ascent() >= 11)
        ++extra.rheight();
    resize(sizeHint() + extra);
    restartExpireTimer(msecDisplayTime);
}

void NewTipLabel::paintEvent(QPaintEvent *ev)
{
    QStylePainter p(this);
    QStyleOptionFrame opt;
    opt.init(this);
    p.drawPrimitive(QStyle::PE_PanelTipLabel, opt);
    p.end();

    if (NewToolTip::Vertical == m_eTextDirection)
    {
        QPainter painter(this);
        painter.translate(width(), 0);
        painter.rotate(90);
        int iMargin = margin();
        painter.drawText(QRect(iMargin,iMargin, height()-2*iMargin, width()-2*iMargin), alignment(), text());
    }
    else
    {
        QLabel::paintEvent(ev);
    }
}

void NewTipLabel::resizeEvent(QResizeEvent *e)
{
    QStyleHintReturnMask frameMask;
    QStyleOption option;
    option.init(this);
    if (style()->styleHint(QStyle::SH_ToolTip_Mask, &option, this, &frameMask))
        setMask(frameMask.region);

    QLabel::resizeEvent(e);
}

QSize NewTipLabel::sizeHint() const
{
    if (NewToolTip::Vertical == m_eTextDirection)
    {
        QSize s = QLabel::minimumSizeHint();
        return QSize(s.height(), s.width());
    }
    else
    {
        return QLabel::sizeHint();
    }
}

QSize NewTipLabel::minimumSizeHint() const
{
    if (NewToolTip::Vertical == m_eTextDirection)
    {
        QSize s = QLabel::sizeHint();
        return QSize(s.height(), s.width());
    }
    else
    {
        return QLabel::minimumSizeHint();
    }
}

int NewTipLabel::calMinRowCountWidth(const QString& qstrText, const int iScreenWidth, int& iOutRow)
{
    if (qstrText.isEmpty())
    {
        return 0;
    }

    int iContentMargin = 2 * margin() + 1;
    QStringList qlstWords = qstrText.split(' ');
    // 如果字符串长度加边距大于屏幕且有空格，则计算出合适的宽度
    QString qstrTextTmp = qlstWords[0];
    int iRowWidth = fontMetrics().width(qstrTextTmp) + iContentMargin;
    if (iRowWidth > iScreenWidth)
    {
        // 如果第一个单词就超过屏幕宽度，则返回屏幕宽度
        return iScreenWidth;
    }

    QVector<int> qvtRows;
    QVector<int> qvtRowWidths;
    for (int i = 1; i < qlstWords.size(); ++i)
    {
        qstrTextTmp += ' ' + qlstWords[i];
        if (fontMetrics().width(qstrTextTmp) + iContentMargin > iScreenWidth)
        {
            // 如果这一区间组成的单词的长度超出了屏幕宽度，计算这个宽度需要多少行来显示
            int iRow = 1;
            QString qstrTextRow = qlstWords[0];
            for (int j = 0; j < qlstWords.size(); ++j)
            {
                if (fontMetrics().width(qstrTextRow) + iContentMargin > iRowWidth)
                {
                    ++iRow;
                    if (qlstWords.size() != j + 1)
                    {
                        qstrTextRow = qlstWords[j + 1];
                    }
                }
                else
                {
                    if (qlstWords.size() != j + 1)
                    {
                        qstrTextRow += ' ' + qlstWords[j + 1];
                    }
                }
            }
            qvtRows.append(iRow);
            qvtRowWidths.append(iRowWidth);
            qstrTextTmp = qlstWords[i];
        }

        iRowWidth = fontMetrics().width(qstrTextTmp) + iContentMargin;
    }

    if (!qvtRows.isEmpty())
    {
        int iRowMin = qvtRows[0];
        int iRowWidthMax = qvtRowWidths[0];
        for(int i = 1; i < qvtRows.size(); ++i)
        {
            if (iRowMin >= qvtRows[i])
            {
                iRowMin = qvtRows[i];

                if (iRowWidthMax < qvtRowWidths[i])
                {
                    iRowWidthMax = qvtRowWidths[i];
                }
            }
        }

        if (iRowWidthMax > iScreenWidth)
        {
            iRowWidthMax = iScreenWidth;
        }

        iOutRow = iRowMin;
        return iRowWidthMax;
    }
    else
    {
        iOutRow = 1;
        return iRowWidth;
    }
}

void NewTipLabel::mouseMoveEvent(QMouseEvent *e)
{
    if (!rect.isNull()) {
        QPoint pos = e->globalPos();
        if (widget)
            pos = widget->mapFromGlobal(pos);
        if (!rect.contains(pos))
            hideTip();
    }
    QLabel::mouseMoveEvent(e);
}

NewTipLabel::~NewTipLabel()
{
    instance = 0;
}

void NewTipLabel::hideTip()
{
    if (!hideTimer.isActive())
        hideTimer.start(300, this);
}

void NewTipLabel::hideTipImmediately()
{
    close(); // to trigger QEvent::Close which stops the animation
    deleteLater();
}

void NewTipLabel::setTipRect(QWidget *w, const QRect &r)
{
    if (Q_UNLIKELY(!r.isNull() && !w)) {
        qWarning("QToolTip::setTipRect: Cannot pass null widget if rect is set");
        return;
    }
    widget = w;
    rect = r;
}

void NewTipLabel::timerEvent(QTimerEvent *e)
{
    if (e->timerId() == hideTimer.timerId()
        || e->timerId() == expireTimer.timerId()){
        hideTimer.stop();
        expireTimer.stop();
        hideTipImmediately();
    }
}

bool NewTipLabel::eventFilter(QObject *o, QEvent *e)
{
    switch (e->type()) {
#ifdef Q_OS_MACOS
    case QEvent::KeyPress:
    case QEvent::KeyRelease: {
        const int key = static_cast<QKeyEvent *>(e)->key();
        // Anything except key modifiers or caps-lock, etc.
        if (key < Qt::Key_Shift || key > Qt::Key_ScrollLock)
            hideTipImmediately();
        break;
    }
#endif
    case QEvent::Leave:
        hideTip();
        break;


#if defined (Q_OS_QNX) // On QNX the window activate and focus events are delayed and will appear
                       // after the window is shown.
    case QEvent::WindowActivate:
    case QEvent::FocusIn:
        return false;
    case QEvent::WindowDeactivate:
        if (o != this)
            return false;
        hideTipImmediately();
        break;
    case QEvent::FocusOut:
        if (reinterpret_cast<QWindow*>(o) != windowHandle())
            return false;
        hideTipImmediately();
        break;
#else
    case QEvent::WindowActivate:
    case QEvent::WindowDeactivate:
    case QEvent::FocusIn:
    case QEvent::FocusOut:
#endif
    case QEvent::Close: // For QTBUG-55523 (QQC) specifically: Hide tooltip when windows are closed
    case QEvent::MouseButtonPress:
    case QEvent::MouseButtonRelease:
    case QEvent::MouseButtonDblClick:
    case QEvent::Wheel:
        hideTipImmediately();
        break;

    case QEvent::MouseMove:
        if (o == widget && !rect.isNull() && !rect.contains(static_cast<QMouseEvent*>(e)->pos()))
            hideTip();
    default:
        break;
    }
    return false;
}

int NewTipLabel::getTipScreen(const QPoint &pos, QWidget *w)
{
    if (QApplication::desktop()->isVirtualDesktop())
        return QApplication::desktop()->screenNumber(pos);
    else
        return QApplication::desktop()->screenNumber(w);
}

void NewTipLabel::placeTip(const QPoint &pos, QWidget *w)
{
#if 0 // Used to be included in Qt4 for Q_WS_MAC
    // When in full screen mode, there is no Dock nor Menu so we can use
    // the whole screen for displaying the tooltip. However when not in
    // full screen mode we need to save space for the dock, so we use
    // availableGeometry instead.
    extern bool qt_mac_app_fullscreen; //qapplication_mac.mm
    QRect screen;
    if(qt_mac_app_fullscreen)
        screen = QApplication::desktop()->screenGeometry(getTipScreen(pos, w));
    else
        screen = QApplication::desktop()->availableGeometry(getTipScreen(pos, w));
#else
    QRect screen = QApplication::desktop()->screenGeometry(getTipScreen(pos, w));
#endif

    qDebug() << "NewTipLabel::instance->mapToGlobal(QPoint(0,0)) before: " << pos;
    QPoint p = pos;
    p += QPoint(0,
#if 0 // Used to be included in Qt4 for Q_WS_WIN
                21
#else
                16
#endif
        );
    if (p.x() + this->width() > screen.x() + screen.width())
        p.rx() -= 4 + this->width();
    if (p.y() + this->height() > screen.y() + screen.height())
        //p.ry() -= 24 + this->height();
        p.ry() = screen.y() + screen.height() - this->height();
    if (p.y() < screen.y())
        p.setY(screen.y());
    if (p.x() + this->width() > screen.x() + screen.width())
        p.setX(screen.x() + screen.width() - this->width());
    if (p.x() < screen.x())
        p.setX(screen.x());
    if (p.y() + this->height() > screen.y() + screen.height())
        p.setY(screen.y() + screen.height() - this->height());
    this->move(p);
    qDebug() << "NewTipLabel::instance->mapToGlobal(QPoint(0,0)) after: " << p;
}

void NewTipLabel::placeTipAbove(const QPoint &pos, QWidget *w)
{
#if 0 // Used to be included in Qt4 for Q_WS_MAC
    // When in full screen mode, there is no Dock nor Menu so we can use
    // the whole screen for displaying the tooltip. However when not in
    // full screen mode we need to save space for the dock, so we use
    // availableGeometry instead.
    extern bool qt_mac_app_fullscreen; //qapplication_mac.mm
    QRect screen;
    if(qt_mac_app_fullscreen)
        screen = QApplication::desktop()->screenGeometry(getTipScreen(pos, w));
    else
        screen = QApplication::desktop()->availableGeometry(getTipScreen(pos, w));
#else
    QRect screen = QApplication::desktop()->screenGeometry(getTipScreen(pos, w));
#endif

    QPoint p = pos;
    p.setY(pos.y() - height());
    if (p.x() + this->width() > screen.x() + screen.width())
        p.rx() -= 4 + this->width();
    if (p.y() + this->height() > screen.y() + screen.height())
        //p.ry() -= 24 + this->height();
        p.ry() = screen.y() + screen.height() - this->height();
    if (p.y() < screen.y())
        p.setY(screen.y());
    if (p.x() + this->width() > screen.x() + screen.width())
        p.setX(screen.x() + screen.width() - this->width());
    if (p.x() < screen.x())
        p.setX(screen.x());
    if (p.y() + this->height() > screen.y() + screen.height())
        p.setY(screen.y() + screen.height() - this->height());
    this->move(p);
}

void NewTipLabel::placeTipBelow(const QPoint &pos, QWidget *w)
{
#if 0 // Used to be included in Qt4 for Q_WS_MAC
    // When in full screen mode, there is no Dock nor Menu so we can use
    // the whole screen for displaying the tooltip. However when not in
    // full screen mode we need to save space for the dock, so we use
    // availableGeometry instead.
    extern bool qt_mac_app_fullscreen; //qapplication_mac.mm
    QRect screen;
    if(qt_mac_app_fullscreen)
        screen = QApplication::desktop()->screenGeometry(getTipScreen(pos, w));
    else
        screen = QApplication::desktop()->availableGeometry(getTipScreen(pos, w));
#else
    QRect screen = QApplication::desktop()->screenGeometry(getTipScreen(pos, w));
#endif

    QPoint p = pos;
    if (p.x() + this->width() > screen.x() + screen.width())
        p.rx() -= 4 + this->width();
    if (p.y() + this->height() > screen.y() + screen.height())
        //p.ry() -= 24 + this->height();
        p.ry() = screen.y() + screen.height() - this->height();
    if (p.y() < screen.y())
        p.setY(screen.y());
    if (p.x() + this->width() > screen.x() + screen.width())
        p.setX(screen.x() + screen.width() - this->width());
    if (p.x() < screen.x())
        p.setX(screen.x());
    if (p.y() + this->height() > screen.y() + screen.height())
        p.setY(screen.y() + screen.height() - this->height());
    this->move(p);
}

bool NewTipLabel::tipChanged(const QPoint &pos, const QString &text, QObject *o)
{
    if (NewTipLabel::instance->text() != text)
        return true;

    if (o != widget)
        return true;

    if (!rect.isNull())
        return !rect.contains(pos);
    else
       return false;
}


/*!
    Shows \a text as a tool tip, with the global position \a pos as
    the point of interest. The tool tip will be shown with a platform
    specific offset from this point of interest.

    If you specify a non-empty rect the tip will be hidden as soon
    as you move your cursor out of this area.

    The \a rect is in the coordinates of the widget you specify with
    \a w. If the \a rect is not empty you must specify a widget.
    Otherwise this argument can be 0 but it is used to determine the
    appropriate screen on multi-head systems.

    If \a text is empty the tool tip is hidden. If the text is the
    same as the currently shown tooltip, the tip will \e not move.
    You can force moving by first hiding the tip with an empty text,
    and then showing the new tip at the new position.
*/

void NewToolTip::showText(const QPoint &pos, const QString &text, const TextDirection eTextDirection, QWidget *w, const QRect &rect)
{
    showText(pos, text, eTextDirection, w, rect, -1);
}

/*!
   \since 5.2
   \overload
   This is similar to QToolTip::showText(\a pos, \a text, \a w, \a rect) but with an extra parameter \a msecDisplayTime
   that specifies how long the tool tip will be displayed, in milliseconds.
*/

void NewToolTip::showText(const QPoint &pos, const QString &text, const TextDirection eTextDirection, QWidget *w, const QRect &rect, int msecDisplayTime)
{
    if (NewTipLabel::instance && NewTipLabel::instance->isVisible()){ // a tip does already exist
        if (text.isEmpty()){ // empty text means hide current tip
            NewTipLabel::instance->hideTip();
            return;
        }
        else if (!NewTipLabel::instance->fadingOut){
            // If the tip has changed, reuse the one
            // that is showing (removes flickering)
            QPoint localPos = pos;
            if (w)
                localPos = w->mapFromGlobal(pos);
            if (NewTipLabel::instance->tipChanged(localPos, text, w)){
                NewTipLabel::instance->reuseTip(text, msecDisplayTime, pos, eTextDirection);
                NewTipLabel::instance->setTipRect(w, rect);
                NewTipLabel::instance->placeTip(pos, w);
            }
            return;
        }
    }

    if (!text.isEmpty()){ // no tip can be reused, create new tip:
#ifdef Q_OS_WIN32
        // On windows, we can't use the widget as parent otherwise the window will be
        // raised when the tooltip will be shown
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
        new NewTipLabel(text, pos, eTextDirection, QApplication::desktop()->screen(NewTipLabel::getTipScreen(pos, w)), msecDisplayTime);
QT_WARNING_POP
#else
        new NewTipLabel(text, pos, eTextDirection, w, msecDisplayTime); // sets QTipLabel::instance to itself
#endif
        //NewTipLabel::instance->resize( NewTipLabel::instance->height(),  NewTipLabel::instance->width());
        NewTipLabel::instance->setTipRect(w, rect);
        NewTipLabel::instance->placeTip(pos, w);
        NewTipLabel::instance->setObjectName(QLatin1String("newtooltip_label"));


//#if QT_CONFIG(effects) && !0 /* Used to be included in Qt4 for Q_WS_MAC */
//        if (QApplication::isEffectEnabled(Qt::UI_FadeTooltip))
//            qFadeEffect(CTipLabel::instance);
//        else if (QApplication::isEffectEnabled(Qt::UI_AnimateTooltip))
//            qScrollEffect(CTipLabel::instance);
//        else
//            CTipLabel::instance->showNormal();
//#else
        NewTipLabel::instance->showNormal();
//#endif
    }
}

/*!
    \overload

    This is analogous to calling QToolTip::showText(\a pos, \a text, \a w, QRect())
*/

void NewToolTip::showText(const QPoint &pos, const QString &text, const TextDirection eTextDirection, QWidget *w)
{
    NewToolTip::showText(pos, text, eTextDirection, w, QRect());
}

void NewToolTip::showTextAbove(const QPoint &pos, const QString &text, QWidget *w)
{
    NewToolTip::showTextAbove(pos, text, w, QRect());
}

void NewToolTip::showTextAbove(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect)
{
    showTextAbove(pos, text, w, rect, -1);
}

void NewToolTip::showTextAbove(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect, int msecDisplayTime)
{
    if (NewTipLabel::instance && NewTipLabel::instance->isVisible()){ // a tip does already exist
        if (text.isEmpty()){ // empty text means hide current tip
            NewTipLabel::instance->hideTip();
            return;
        }
        else if (!NewTipLabel::instance->fadingOut){
            // If the tip has changed, reuse the one
            // that is showing (removes flickering)
            QPoint localPos = pos;
            if (w)
                localPos = w->mapFromGlobal(pos);
            if (NewTipLabel::instance->tipChanged(localPos, text, w)){
                NewTipLabel::instance->reuseTip(text, msecDisplayTime, pos, NewToolTip::Horizontal);
                NewTipLabel::instance->setTipRect(w, rect);
                NewTipLabel::instance->placeTipAbove(pos, w);
            }
            return;
        }
    }

    if (!text.isEmpty()){ // no tip can be reused, create new tip:
#ifdef Q_OS_WIN32
        // On windows, we can't use the widget as parent otherwise the window will be
        // raised when the tooltip will be shown
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
        new NewTipLabel(text, pos, NewToolTip::Horizontal, QApplication::desktop()->screen(NewTipLabel::getTipScreen(pos, w)), msecDisplayTime);
QT_WARNING_POP
#else
        new NewTipLabel(text, pos, NewToolTip::Horizontal, w, msecDisplayTime); // sets QTipLabel::instance to itself
#endif
        //NewTipLabel::instance->resize( NewTipLabel::instance->height(),  NewTipLabel::instance->width());
        NewTipLabel::instance->setTipRect(w, rect);
        NewTipLabel::instance->placeTipAbove(pos, w);
        NewTipLabel::instance->setObjectName(QLatin1String("newtooltip_label"));


//#if QT_CONFIG(effects) && !0 /* Used to be included in Qt4 for Q_WS_MAC */
//        if (QApplication::isEffectEnabled(Qt::UI_FadeTooltip))
//            qFadeEffect(CTipLabel::instance);
//        else if (QApplication::isEffectEnabled(Qt::UI_AnimateTooltip))
//            qScrollEffect(CTipLabel::instance);
//        else
//            CTipLabel::instance->showNormal();
//#else
        NewTipLabel::instance->showNormal();
//#endif
    }
}

void NewToolTip::showTextAboveWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w)
{
    NewToolTip::showTextAboveWithNoTextProcessed(pos, text, w, QRect());
}

void NewToolTip::showTextAboveWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect)
{
    showTextAboveWithNoTextProcessed(pos, text, w, rect, -1);
}

void NewToolTip::showTextAboveWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect, int msecDisplayTime)
{
    if (NewTipLabel::instance && NewTipLabel::instance->isVisible()){ // a tip does already exist
        if (text.isEmpty()){ // empty text means hide current tip
            NewTipLabel::instance->hideTip();
            return;
        }
        else if (!NewTipLabel::instance->fadingOut){
            // If the tip has changed, reuse the one
            // that is showing (removes flickering)
            QPoint localPos = pos;
            if (w)
                localPos = w->mapFromGlobal(pos);
            if (NewTipLabel::instance->tipChanged(localPos, text, w)){
                NewTipLabel::instance->reuseTip(text, msecDisplayTime, pos, NewToolTip::Horizontal, false);
                NewTipLabel::instance->setTipRect(w, rect);
                NewTipLabel::instance->placeTipAbove(pos, w);
            }
            return;
        }
    }

    if (!text.isEmpty()){ // no tip can be reused, create new tip:
#ifdef Q_OS_WIN32
        // On windows, we can't use the widget as parent otherwise the window will be
        // raised when the tooltip will be shown
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
        new NewTipLabel(text, pos, NewToolTip::Horizontal, QApplication::desktop()->screen(NewTipLabel::getTipScreen(pos, w)), msecDisplayTime);
QT_WARNING_POP
#else
        new NewTipLabel(text, pos, NewToolTip::Horizontal, w, msecDisplayTime); // sets QTipLabel::instance to itself
#endif
        //NewTipLabel::instance->resize( NewTipLabel::instance->height(),  NewTipLabel::instance->width());
        NewTipLabel::instance->setTipRect(w, rect);
        NewTipLabel::instance->placeTipAbove(pos, w);
        NewTipLabel::instance->setObjectName(QLatin1String("newtooltip_label"));


//#if QT_CONFIG(effects) && !0 /* Used to be included in Qt4 for Q_WS_MAC */
//        if (QApplication::isEffectEnabled(Qt::UI_FadeTooltip))
//            qFadeEffect(CTipLabel::instance);
//        else if (QApplication::isEffectEnabled(Qt::UI_AnimateTooltip))
//            qScrollEffect(CTipLabel::instance);
//        else
//            CTipLabel::instance->showNormal();
//#else
        NewTipLabel::instance->showNormal();
//#endif
    }
}

void NewToolTip::showTextBelow(const QPoint &pos, const QString &text, QWidget *w)
{
    NewToolTip::showTextBelow(pos, text, w, QRect());
}

void NewToolTip::showTextBelow(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect)
{
    showTextBelow(pos, text, w, rect, -1);
}

void NewToolTip::showTextBelow(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect, int msecDisplayTime)
{
    if (NewTipLabel::instance && NewTipLabel::instance->isVisible()){ // a tip does already exist
        if (text.isEmpty()){ // empty text means hide current tip
            NewTipLabel::instance->hideTip();
            return;
        }
        else if (!NewTipLabel::instance->fadingOut){
            // If the tip has changed, reuse the one
            // that is showing (removes flickering)
            QPoint localPos = pos;
            if (w)
                localPos = w->mapFromGlobal(pos);
            if (NewTipLabel::instance->tipChanged(localPos, text, w)){
                NewTipLabel::instance->reuseTip(text, msecDisplayTime, pos, NewToolTip::Horizontal);
                NewTipLabel::instance->setTipRect(w, rect);
                NewTipLabel::instance->placeTipBelow(pos, w);
            }
            return;
        }
    }

    if (!text.isEmpty()){ // no tip can be reused, create new tip:
#ifdef Q_OS_WIN32
        // On windows, we can't use the widget as parent otherwise the window will be
        // raised when the tooltip will be shown
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
        new NewTipLabel(text, pos, NewToolTip::Horizontal, QApplication::desktop()->screen(NewTipLabel::getTipScreen(pos, w)), msecDisplayTime);
QT_WARNING_POP
#else
        new NewTipLabel(text, pos, NewToolTip::Horizontal, w, msecDisplayTime); // sets QTipLabel::instance to itself
#endif
        //NewTipLabel::instance->resize( NewTipLabel::instance->height(),  NewTipLabel::instance->width());
        NewTipLabel::instance->setTipRect(w, rect);
        NewTipLabel::instance->placeTipBelow(pos, w);
        NewTipLabel::instance->setObjectName(QLatin1String("newtooltip_label"));


//#if QT_CONFIG(effects) && !0 /* Used to be included in Qt4 for Q_WS_MAC */
//        if (QApplication::isEffectEnabled(Qt::UI_FadeTooltip))
//            qFadeEffect(CTipLabel::instance);
//        else if (QApplication::isEffectEnabled(Qt::UI_AnimateTooltip))
//            qScrollEffect(CTipLabel::instance);
//        else
//            CTipLabel::instance->showNormal();
//#else
        NewTipLabel::instance->showNormal();
//#endif
    }
}

void NewToolTip::showTextBelowWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w)
{
    NewToolTip::showTextBelowWithNoTextProcessed(pos, text, w, QRect());
}

void NewToolTip::showTextBelowWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect)
{
    showTextBelowWithNoTextProcessed(pos, text, w, rect, -1);
}

void NewToolTip::showTextBelowWithNoTextProcessed(const QPoint &pos, const QString &text, QWidget *w, const QRect &rect, int msecDisplayTime)
{
    if (NewTipLabel::instance && NewTipLabel::instance->isVisible()){ // a tip does already exist
        if (text.isEmpty()){ // empty text means hide current tip
            NewTipLabel::instance->hideTip();
            return;
        }
        else if (!NewTipLabel::instance->fadingOut){
            // If the tip has changed, reuse the one
            // that is showing (removes flickering)
            QPoint localPos = pos;
            if (w)
                localPos = w->mapFromGlobal(pos);
            if (NewTipLabel::instance->tipChanged(localPos, text, w)){
                NewTipLabel::instance->reuseTip(text, msecDisplayTime, pos, NewToolTip::Horizontal, false);
                NewTipLabel::instance->setTipRect(w, rect);
                NewTipLabel::instance->placeTipBelow(pos, w);
            }
            return;
        }
    }

    if (!text.isEmpty()){ // no tip can be reused, create new tip:
#ifdef Q_OS_WIN32
        // On windows, we can't use the widget as parent otherwise the window will be
        // raised when the tooltip will be shown
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
        new NewTipLabel(text, pos, NewToolTip::Horizontal, QApplication::desktop()->screen(NewTipLabel::getTipScreen(pos, w)), msecDisplayTime);
QT_WARNING_POP
#else
        new NewTipLabel(text, pos, NewToolTip::Horizontal, w, msecDisplayTime); // sets QTipLabel::instance to itself
#endif
        //NewTipLabel::instance->resize( NewTipLabel::instance->height(),  NewTipLabel::instance->width());
        NewTipLabel::instance->setTipRect(w, rect);
        NewTipLabel::instance->placeTipBelow(pos, w);
        NewTipLabel::instance->setObjectName(QLatin1String("newtooltip_label"));


//#if QT_CONFIG(effects) && !0 /* Used to be included in Qt4 for Q_WS_MAC */
//        if (QApplication::isEffectEnabled(Qt::UI_FadeTooltip))
//            qFadeEffect(CTipLabel::instance);
//        else if (QApplication::isEffectEnabled(Qt::UI_AnimateTooltip))
//            qScrollEffect(CTipLabel::instance);
//        else
//            CTipLabel::instance->showNormal();
//#else
        NewTipLabel::instance->showNormal();
//#endif
    }
}


/*!
    \fn void QToolTip::hideText()
    \since 4.2

    Hides the tool tip. This is the same as calling showText() with an
    empty string.

    \sa showText()
*/


/*!
  \since 4.4

  Returns \c true if this tooltip is currently shown.

  \sa showText()
 */
bool NewToolTip::isVisible()
{
    return (NewTipLabel::instance != 0 && NewTipLabel::instance->isVisible());
}

/*!
  \since 4.4

  Returns the tooltip text, if a tooltip is visible, or an
  empty string if a tooltip is not visible.
 */
QString NewToolTip::text()
{
    if (NewTipLabel::instance)
        return NewTipLabel::instance->text();
    return QString();
}


Q_GLOBAL_STATIC(QPalette, tooltip_palette)

/*!
    Returns the palette used to render tooltips.

    \note Tool tips use the inactive color group of QPalette, because tool
    tips are not active windows.
*/
QPalette NewToolTip::palette()
{
    return *tooltip_palette();
}

/*!
    \since 4.2

    Returns the font used to render tooltips.
*/
QFont NewToolTip::font()
{
    return QApplication::font("NewTipLabel");
}

/*!
    \since 4.2

    Sets the \a palette used to render tooltips.

    \note Tool tips use the inactive color group of QPalette, because tool
    tips are not active windows.
*/
void NewToolTip::setPalette(const QPalette &palette)
{
    *tooltip_palette() = palette;
    if (NewTipLabel::instance)
        NewTipLabel::instance->setPalette(palette);
}

/*!
    \since 4.2

    Sets the \a font used to render tooltips.
*/
void NewToolTip::setFont(const QFont &font)
{
    QApplication::setFont(font, "NewTipLabel");
}
