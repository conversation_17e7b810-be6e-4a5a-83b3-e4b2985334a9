﻿/*
* Copyright (c) 2017.08，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: prpdpointmatrix.h
*
* 初始版本: 1.0
* 作者: 张涛
* 创建日期: 2017年12月5日
* 摘要: 该文件定义了周期PRPD图谱点阵类,记录每个位置对应的数据出现次数

* 当前版本: 1.0
*/

#ifndef PRPDPOINTMATRIX_H
#define PRPDPOINTMATRIX_H

#include <QVector>
#include "phasedef.h"

class PHASECHARTSHARED_EXPORT PrpdPointMatrix
{
public:
    PrpdPointMatrix(int iRowCount, int iColCount);

    // 获取矩阵行数
    int rowCount() const
    {
        return m_iRowCount;
    }

    // 设置矩阵行数
    void setRowCount(int iCount)
    {
        PHASE_ASSERT(iCount > 0);
        if(iCount != m_iRowCount)
        {
            m_iRowCount = iCount;
            reset();
        }
    }

    // 获取列数
    int columnCount() const
    {
        return m_iColumnCount;
    }

    // 设置矩阵列数
    void setColumnCount(int iCount)
    {
        PHASE_ASSERT(iCount > 0);
        if(iCount != m_iColumnCount)
        {
            m_iColumnCount = iCount;
            reset();
        }
    }

    // 获取当前数据对应的周期数
    int periodCount() const
    {
        return m_iPeriodCount;
    }

    // 设置当前数据对应的周期个数
    void setPeriodCount(int iCount)
    {
        PHASE_ASSERT(iCount >= 0);

        m_iPeriodCount = iCount;

        if(0 == iCount)            // 计算脉冲重复率需要除以累积周期数,因此如果周期为0则赋值为1,避免做除法时判断
        {
            m_iPeriodCountProxy = 1;
        }
        else
        {
            m_iPeriodCountProxy = iCount;
        }
    }

    void setAccumulative(bool bAccumulative)
    {
        m_bAccumulative = bAccumulative;
    }

    void setAccumulativeTime(int iSecond);

    // 设置每秒有多少组数据
    void setDataCountPerSecond(unsigned int uiDataCountPerSecond);

    // 重置
    void reset();

    // 获取重复率, 个数/总周期数, 如果需要脉冲重复率则还需除以工频频率
    float repetitionRate(int iRowIdx, int iColumnIdx) const
    {
        return m_qvtPRPDData[iRowIdx][iColumnIdx] / static_cast<float>(m_iPeriodCountProxy);
    }

    // 获取对应点的累积个数
    int& value(int iRowIdx, int iColumnIdx);

    // 获取对应点的累积个数
    const int& value(int iRowIdx, int iColumnIdx) const;

    void advance(int iStep);

    void addBufferData(QList<QVector<QVector<int> > >& qlstBufferData);

private:
    void updateAccumulativeDataCount();

private:
    int m_iRowCount;
    int m_iColumnCount;
    int m_iPeriodCount;
    /*
     * 由于计算重复率时需要除以周期个数, 周期个数有可能为0, 为避免每次都判断是否为0而采取一个代理,
     * 使其始终不为0, 当实际的周期个数不为0时与实际值保持一致, 否则其值等于1, 由于实际周期个数为0时,
     * 矩阵的每个位置都是0, 因此不影响最终计算的重复率
     */
    int m_iPeriodCountProxy;
    bool m_bAccumulative;

    unsigned int m_uiDataCountPerSecond;
    unsigned int m_uiAccumulativeTime; // 累积时间（单位秒）
    unsigned int m_uiAccumulativeDataCount;

    QList<QVector<QVector<int> > > m_accumulativeData;

    QVector<QVector<int> > m_qvtPRPDData;
};

#endif // PRPDPOINTMATRIX_H
