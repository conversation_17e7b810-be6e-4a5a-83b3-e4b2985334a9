#ifndef GUIDEINFRAREDIMAGEWIDGET_H
#define GUIDEINFRAREDIMAGEWIDGET_H

#include <QWidget>
#include "DriverDataDefine.h"
#include "guideinfrareddefine.h"

class GuideInfraredImageWidget : public QWidget
{
    Q_OBJECT
public:
    explicit GuideInfraredImageWidget(QWidget* pParent = NULL);
    QSize sizeHint() const;

    void setDisplayMode(GuideInfrared::GuideInfraredDisplayMode eGuideInfraredDisplayMode);

    void setInfraredRgbPixmap(QSharedPointer<QPixmap> qspInfraredRgbPixmap);

    void setInfraredRgbImage(QSharedPointer<QImage> qspInfraredRgbImage);

    void setVisibleLightPixmap(QSharedPointer<QPixmap> qspVisibleLightPixmap);

    void playbackVisibleLightPhoto();

    void playbackInfraredPhoto();
    
protected:
    virtual void paintEvent(QPaintEvent* pEvent);

private:
    GuideInfrared::PlaybackState m_eCurrentPlaybackState;
    GuideInfrared::GuideInfraredDisplayMode m_eGuideInfraredDisplayMode;

    QPixmap m_infraredPixmap;
    QSharedPointer<QPixmap> m_qspInfraredRgbPixmap;
    QSharedPointer<QImage> m_qspInfraredRgbImage;
    QSharedPointer<QPixmap> m_qspVisibleLightPixmap;
};

#endif // GUIDEINFRAREDIMAGEWIDGET_H
