#include "pdatask.h"
#include <QUuid>
#include <time.h>
#include "uhf/dataSave/UHFPRPSAndPRPDDataSave.h"
#include "ae/dataSave/AEAmpDataSave.h"
#include "infrared/InfraredDataSave.h"
#include "infrared/guideinfrareddatasave.h"
#include "fileoper/fileoperutil.h"
#include "timezonemanager/timezonemanager.h"
#include "iniconfig/iniconfig.h"
#include "systemsetting/systemsetservice.h"
#include "log/log.h"
#include "global_log.h"
#ifdef Q_OS_LINUX
#include <sys/vfs.h>
#include <unistd.h>
#endif


const QString SOURCE_PREFIX = "t95";
const char SOURCE_SPILT_CHAR = '_';



PDATask::PDATask(const QString &filePath, QObject *parent) : QObject(parent)
{
    taskInfoFromFile(filePath, m_taskInfo);

    m_iNeedTestDevicePos = -1;
    m_iNeedTestPointPos = -1;
    m_iNeedTestDataPos = -1;

    m_bTaskChanged = false;
    m_qstrCurBayNumber = "";

    if(readTaskFile())
    {
        m_bSynchronized = true;
        setCurPatrolPosition(PDAServiceNS::PATROL_POS_NONE);
    }
    else//解析失败
    {
        qDebug() << "Error in parse task file : " << filePath;
    }
}

PDATask::PDATask(const TaskInfo& taskInfo, QObject *parent) : QObject(parent)
{
    m_taskInfo = taskInfo;

    m_iNeedTestDevicePos = -1;
    m_iNeedTestPointPos = -1;
    m_iNeedTestDataPos = -1;

    m_bTaskChanged = false;
    m_qstrCurBayNumber = "";

    if(readTaskFile())//解析成功
    {
        m_bSynchronized = true;
        setCurPatrolPosition(PDAServiceNS::PATROL_POS_NONE);
    }
    else//解析失败
    {
        qDebug() << "PDATask::PDATask, Error in parse task file: " << m_taskInfo.strFilePath;
    }
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：NULL
功能：创建目录
*************************************************************/
bool PDATask::createDir(const QString qsDir)
{
    qDebug()<<"PDATask::createDir, qsDir:"<<qsDir;

    QDir dir(qsDir);
    if(dir.exists())
    {
        return true;
    }
    return dir.mkpath(qsDir);
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：NULL
功能：任务文件解析后， 创建该任务下存储数据文件的各子目录
*************************************************************/
bool PDATask::createDataSavedSubDirs()
{
    //if root dir doesn't exist, return
    QString qsTaskFilePath = m_taskInfo.strFilePath;
    int iPosBeforeFileName = qsTaskFilePath.lastIndexOf('/');
    QString qsPatrolTypeUpperDir = qsTaskFilePath.mid(0,iPosBeforeFileName);
    qDebug()<<"PDATask::createDataSavedSubDirs, qsPatrolTypeUpperDir:"<<qsPatrolTypeUpperDir;

    QDir dir(qsPatrolTypeUpperDir);
    if(!dir.exists())
    {
        return false;
    }

    int iPatrolTypeCnt = m_patrolTypes.size();
    QString qsPatrolInfo = "";
    QString qsPatrolTypeDir = "";
    //创建各巡检类型目录、各巡检类型下的测点类型目录
    for(int i = 0; i < iPatrolTypeCnt; ++i)
    {
        qsPatrolInfo = getInfoByPatrolType(m_patrolTypes.at(i).ePatrolType);
        qsPatrolTypeDir = qsPatrolTypeUpperDir + "/" + qsPatrolInfo;
        createDir(qsPatrolTypeDir);
        createTestPointsTypeDirs(m_patrolTypes.at(i).testTypes, qsPatrolTypeDir);
    }

    return true;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：NULL
功能：创建任务包含的所有测点类型目录
*************************************************************/
bool PDATask::createTestPointsTypeDirs(const QVector<ItemTestType> &testTypes, const QString &qsTestPointUpperDir)
{
    QDir dir(qsTestPointUpperDir);
    if(!dir.exists())
    {
        return false;
    }

    int iTestPointTypeCnt = testTypes.size();
    QString qsName;
    QString qsTestPointTypeDir;
    for(int i = 0; i < iTestPointTypeCnt; ++i)
    {
        qsName = testTypes.at(i).strItemName;
        qsTestPointTypeDir = qsTestPointUpperDir + "/" + qsName;
        createDir(qsTestPointTypeDir);
    }
    return true;
}

/*************************************************
函数名：
输入参数:NULL
输出参数:NULL
返回值：false--读取失败 true--读取成功
功能：解析任务描述文件
*************************************************************/
bool PDATask::readTaskFile()
{
    bool bRet = true;
    m_bTaskChanged = false;
    m_patrolTypes.clear();

    TaskFileIO tmpFileIO;
    TaskFileIO::IOStateType eResult = tmpFileIO.readTaskFile(m_taskInfo, m_patrolTypes, m_taskInfo.strFilePath);
    if(TaskFileIO::OPERATE_SUCCESS == eResult)
    {
        bRet = true;
        quint32 qui32TotalCount = 0;
        quint32 qui32TestedCount = 0;

        //新增业务逻辑，当任务文件解析成功后，将最近一次的背景更新到缓存中（暂时存在于超声检测中）
        QVector<ItemPatrolType>::iterator iterPatrol = m_patrolTypes.begin();
        for(; iterPatrol != m_patrolTypes.end(); ++iterPatrol)
        {
            qui32TotalCount += iterPatrol->uiTotalCount;
            qui32TestedCount += iterPatrol->uiTestedCount;

            QVector<ItemTestType>::iterator iterTestType = iterPatrol->testTypes.begin();
            for(; iterTestType != iterPatrol->testTypes.end(); ++iterTestType)
            {
                if(iterTestType->eTestPointType == PDAServiceNS::AE_TYPE )
                {
                    // 开关柜需要根据测试部位来定义不同的背景，故区分处理
                    if(iterPatrol->ePatrolType == PDAServiceNS::SWITCH_CABINET)
                    {
                        updateSwitchCabinetBGN(iterTestType->devices, *iterTestType);
                    }
                    else
                    {
                        updateGISAndOtherBGN(iterTestType->devices, *iterTestType);
                    }
                }
                else
                {
                    continue;
                }
            }
        }

        if(m_taskInfo.uiTotalCount != qui32TotalCount || m_taskInfo.uiTestedCount != qui32TestedCount)
        {
            m_taskInfo.uiTotalCount = qui32TotalCount;
            m_taskInfo.uiTestedCount = qui32TestedCount;
            m_bTaskChanged = true;
            logInfo(QString("new task info, total count: %1, tested count: %2.").arg(qui32TotalCount).arg(qui32TestedCount));
        }
    }
    else
    {
        logError(QString("read task file (%1) failed.").arg(m_taskInfo.strFilePath));
        bRet = false;
    }

    return bRet;
}

/********************************************
 * 功能：任务是否发生变化
 * 返回值：
 *      bool：true -- 已发送变化，false -- 未发生变化
 * ******************************************/
bool PDATask::isTaskChanged()
{
    return m_bTaskChanged;
}

//产生测试数据
void PDATask::productTestData()
{
    int iTestIndex = 0;
    //测试数据
    for(int i1 = 0; i1 < 3; i1++)
    {
        ItemPatrolType tmpPat;
        tmpPat.strItemName = QString("PatrolType%1").arg(i1);
        tmpPat.uiTestedCount = 0;
        tmpPat.uiTotalCount = 27;
        for(int i2 = 0; i2 < 3; i2++)
        {
            ItemTestType tmpTestType;
            tmpTestType.strItemName = QString("TestType%1").arg(i2);
            tmpTestType.uiTestedCount = 0;
            tmpTestType.uiTotalCount = 9;
            tmpTestType.eTestPointType = (PDAServiceNS::TestPointType)i2;
            //tmpTestType.eVoltageLevel = PDAServiceNS::VOLTAGE_220_kV;
            for(int i3 = 0; i3 < 3; i3++)
            {
                ItemDevice tmpDevice;
                tmpDevice.strItemName = QString("Device%1").arg(i3);
                tmpDevice.uiTestedCount = 0;
                tmpDevice.uiTotalCount = 3;
                tmpDevice.strDevNum = QString("test%1").arg(i3);
                //tmpDevice.eVoltageLevel = PDAServiceNS::VOLTAGE_23_kV;

                if( i3 == 0 )
                {
                    tmpDevice.b_isBGNTest = true;
                }
                else
                {
                    tmpDevice.b_isBGNTest = false;
                }

                for(int i4 = 0; i4 < 3; i4++)
                {
                    ItemTestPoint tmpPoint;
                    tmpPoint.strItemName = QString("TestPoint%1").arg(i4);
                    tmpPoint.bTested = false;
                    iTestIndex++;
                    tmpPoint.uiTestIndex = iTestIndex;
                    ItemTestData tmpTestData;
                    tmpTestData.strFileName = QString("testDataFile1");
                    tmpTestData.usDataIndex = 1;
                    tmpTestData.bTested = false;
                    tmpTestData.eDataType = PDAServiceNS::AE_AMP_CHART_DATA;
                    if( i3 == 0 )
                    {
                        tmpTestData.bIsBgn = true;
                    }
                    else
                    {
                        tmpTestData.bIsBgn = false;
                    }
                    tmpPoint.testDatas.append(tmpTestData);

                    ItemTestData tmpTestData1;
                    tmpTestData1.strFileName = QString("testDataFile12");
                    tmpTestData1.usDataIndex = 2;
                    tmpTestData1.bTested = false;
                    tmpTestData1.eDataType = PDAServiceNS::AE_AMP_CHART_DATA;
                    if( i3 == 0 )
                    {
                        tmpTestData1.bIsBgn = true;
                    }
                    else
                    {
                        tmpTestData1.bIsBgn = false;
                    }
                    tmpPoint.testDatas.append(tmpTestData1);

                    tmpDevice.testPoints.append(tmpPoint);
                }

                tmpTestType.devices.append(tmpDevice);
            }
            tmpPat.testTypes.append(tmpTestType);
        }

        m_patrolTypes.append(tmpPat);
    }
}

/*************************************************
函数名：
输入参数: taskFile--任务描述文件地址
输出参数：NULL
返回值：对应的任务信息
功能：通过任务描述文件获取任务概要信息
*************************************************************/
bool PDATask::taskInfoFromFile(const QString & taskFile, TaskInfo &info)
{
    TaskFileIO tmpFileIO;
    TaskFileIO::IOStateType eRet = tmpFileIO.readTaskFile(info, taskFile);
    return ((TaskFileIO::OPERATE_SUCCESS == eRet) ? true : false);
}

/*************************************************
输入参数: itemDevices-- 设备集合
功能：将开关柜设备集合中的背景测试项更新至缓存
*************************************************************/
void PDATask::updateSwitchCabinetBGN( const QVector<ItemDevice> &itemDevices, const ItemTestType &testType )
{
    for (QVector<ItemDevice>::const_iterator iterDev = itemDevices.begin();
         iterDev != itemDevices.end(); ++iterDev)
    {
        if(iterDev->b_isBGNTest)
        {
            for(QVector<ItemTestPoint>::const_iterator iterPoint = iterDev->testPoints.begin();
                iterPoint != iterDev->testPoints.end(); ++iterPoint)
            {
                if(iterPoint->bTested && !(iterPoint->testDatas.isEmpty()))
                {
                    addBGN(iterPoint->testDatas.last().strFilePath,
                           iterDev->iDevType,
                           testType.strItemName,
                           iterPoint->eTestPosition);
                }
            }

            break;
        }
    }

    return;
}

/*************************************************
输入参数: itemDevices-- 设备集合
功能：将GIS和其它设备集合中的背景测试项更新至缓存
*************************************************************/
void PDATask::updateGISAndOtherBGN(const QVector<ItemDevice> &itemDevices, const ItemTestType &testType)
{
    for (QVector<ItemDevice>::const_iterator iterDev = itemDevices.begin();
         iterDev != itemDevices.end(); ++iterDev)
    {
        if(iterDev->b_isBGNTest)
        {
            if(!(iterDev->testPoints.isEmpty()))
            {
                if((iterDev->testPoints.last().bTested) && !(iterDev->testPoints.last().testDatas.isEmpty()))
                {
                    addBGN(iterDev->testPoints.last().testDatas.last().strFilePath,
                           iterDev->iDevType,
                           testType.strItemName,
                           iterDev->testPoints.last().eTestPosition);
                }
            }

            break;
        }
    }

    return;
}

/*************************************************
输入参数: strDeviceId -- 设备id
功能：根据设备id返回相应的间隔id
*************************************************************/
QString PDATask::bayId( const QString& strDeviceId,const ItemTestType &testType ) const
{
    QString strBayId = "";
    bool bFinded = false;
    for( int i = 0;i < testType.bays.size();++i )
    {
        for( int j = 0;j < testType.bays[i].vecDevice.size();++j )
        {
            if( testType.bays[i].vecDevice[j].strDevNum == strDeviceId )
            {
                bFinded = true;
                strBayId = testType.bays[i].strNumber;
                break;
            }
        }

        if( bFinded )
        {
            break;
        }
    }
    return strBayId;
}

/*************************************************
函数名：
输入参数: strType -- 设备类型
         uiVolNum -- 电压等级
         eVoltageUnit -- 电压单位
         ampData -- 背景噪声测试值
输出参数：NULL
返回值：NULL
功能：获取同一设备类型、同一电压等级下的背景噪声测试值
*************************************************************/
void PDATask::addBGN(const QString& filePath,
                     int iDevType,
                     const QString& strType,
                     PDAServiceNS::TestPosition eTestPosition)
{
    AEAmpDataSave  fDataSave;
    AEAmpDataInfo  stPlayBackDataInfo;

    if(-1 == fDataSave.getDataByPDA(filePath,&stPlayBackDataInfo))
    {
        qWarning() << "PDATask::addBGN, qsFile: " << filePath << " get data failed.";
        return;
    }

    AE::AmplitudeData ampData;
    ampData.eUnitOption = stPlayBackDataInfo.unit();
    ampData.eGain = stPlayBackDataInfo.gain();
    ampData.fRMS = stPlayBackDataInfo.stAEAmpData.fRmsBGN;
    ampData.fPeakValue = stPlayBackDataInfo.stAEAmpData.fPeakBGN;
    ampData.fFirstFreqComValue = stPlayBackDataInfo.stAEAmpData.fFrequency1BGN;
    ampData.fSecondFreqComValue = stPlayBackDataInfo.stAEAmpData.fFrequency2BGN;
    setAEBgnData(iDevType, strType, eTestPosition, ampData);

    return;
}

/*************************************************
功能：获取任务中所有关联文件
返回值：true--该任务可上传，false--该任务不可上传
*************************************************************/
bool PDATask::getTaskRefFiles(const TaskInfo& taskInfo, QList<TestFileInfo>& qlstFiles)
{
    bool bEnableUpload = true;

    //添加音频与图片文件
    ImgMediaPath stPath;
    TaskFileIO tmpFileIO;
    if(tmpFileIO.getImgMediaFilesPath(taskInfo.strFilePath, stPath))
    {
        QString strFilePath;
        QFileInfo fileInfo;
        QStringList listFiles = TaskFileIO::allImgMediaFilesPath(stPath);
        for(int i = 0, iSize = listFiles.size(); i < iSize; ++i)
        {
            strFilePath = listFiles.at(i);
            if(!strFilePath.isEmpty())
            {
                //文件存在才上传，否则不上传，避免影响后面的测试数据上传
                if(FileOperUtil::checkFileOrDirExist(strFilePath))
                {
                    fileInfo.setFile(strFilePath);
                    TestFileInfo info;
                    info.filePath = strFilePath;
                    info.originalFileName = fileInfo.fileName();
                    if(qlstFiles.contains(info))
                    {
                        logError(QString("repeat file path (%1).").arg(info.filePath));
                    }
                    else
                    {
                        qlstFiles.append(info);
                    }
                }
                else
                {
                    logWarning(QString("file path (%1) not exist.").arg(strFilePath));
                }
            }
        }
    }

    int iMediaFileCount = qlstFiles.size(); //记录有多少个图片，音频文件，从而得到多少个数据文件。

    //parse task file
    TaskInfo sTaskInfo = taskInfo;
    QVector<ItemPatrolType> vPatrolTypes;
    if(TaskFileIO::OPERATE_SUCCESS == tmpFileIO.readTaskFile(sTaskInfo, vPatrolTypes, taskInfo.strFilePath))
    {
        //get data files
        QString patrolInfo = "";
        QString testTypeName = "";
        for(int patrolIndex = 0, iPatrolSize = vPatrolTypes.size(); patrolIndex < iPatrolSize; ++patrolIndex)
        {
            patrolInfo = getInfoByPatrolType(vPatrolTypes.at(patrolIndex).ePatrolType);
            ItemPatrolType* tmpPatrol = &(vPatrolTypes[patrolIndex]);
            for(int typeIndex = 0; typeIndex < tmpPatrol->testTypes.size(); ++typeIndex)
            {
                testTypeName = tmpPatrol->testTypes.at(typeIndex).strItemName;

                QStringList subDirNames;
                QString testTypeDirName;
                for(int iDevice = 0, iDevSize = tmpPatrol->testTypes.at(typeIndex).devices.size(); iDevice < iDevSize; ++iDevice)
                {
                    ItemDevice tmpDevice = tmpPatrol->testTypes.at(typeIndex).devices.at(iDevice);

                    QString strVoltage = QString::number(tmpDevice.dVoltage, 'f', 2);
                    int index = strVoltage.indexOf('.');
                    if(strVoltage.endsWith(".00"))
                    {
                        //such as 10.00
                        strVoltage.remove(index, 3);
                    }
                    else if(strVoltage.endsWith("0"))
                    {
                        //such as 5.10
                        strVoltage.remove(strVoltage.size() - 1, 1);
                    }
                    else
                    {
                        //such as 5.16
                    }

                    if(tmpDevice.eVoltageUnit == PDAServiceNS::Voltage_V_Unit)
                    {
                        testTypeDirName = testTypeName + "_" + strVoltage + "V";
                    }
                    else
                    {
                        testTypeDirName = testTypeName + "_" + strVoltage + "kV";
                    }

                    if(!(subDirNames.contains(testTypeDirName)))
                    {
                        //减少多次循环的时间开销
                        subDirNames.append(testTypeDirName);

                        QString dataPath = QFileInfo(taskInfo.strFilePath).path() + "/"
                                + patrolInfo + "/" + testTypeDirName;

                        QDir dataDir(dataPath);
                        QFileInfoList dataFileList = dataDir.entryInfoList(QDir::Files);
                        //判断文件是否属于任务描述文件中记载的测试数据文件
                        for (int i = 0, iFileSize = dataFileList.size(); i < iFileSize; ++i)
                        {
                            if(isTaskTestData(tmpPatrol->testTypes.at(typeIndex), dataFileList[i].fileName()))
                            {
                                TestFileInfo tmpFileInfo;
                                tmpFileInfo.filePath = dataFileList[i].absoluteFilePath();
                                tmpFileInfo.originalFileName = dataFileList[i].fileName();
                                if(qlstFiles.contains(tmpFileInfo))
                                {
                                    logError(QString("repeat file path (%1).").arg(tmpFileInfo.filePath));
                                }
                                else
                                {
                                    qlstFiles.append(tmpFileInfo);
                                }
                            }
                            else if(isTaskAttachFile(tmpPatrol->testTypes.at(typeIndex), dataFileList[i].absoluteFilePath()))
                            {
                                TestFileInfo tmpFileInfo;
                                tmpFileInfo.filePath = dataFileList[i].absoluteFilePath();
                                tmpFileInfo.originalFileName = dataFileList[i].fileName();
                                if(qlstFiles.contains(tmpFileInfo))
                                {
                                    logError(QString("repeat file path (%1).").arg(tmpFileInfo.filePath));
                                }
                                else
                                {
                                    qlstFiles.append(tmpFileInfo);
                                }
                            }
                            else
                            {
                                //删除不匹配的数据文件
                                logWarning(QString("delete unmatched data file (%1).").arg(dataFileList[i].filePath()).toLatin1().data());
                                dataDir.remove(dataFileList[i].fileName());
                            }
                        }
                    }
                    else
                    {
                        logWarning("contains this folder, will not append files.");
                    }
                }
            }
        }
    }
    else
    {
        logError(QString("open file (%1) error.").arg(taskInfo.strFilePath).toLatin1().data());
    }

    int iDataFileCount = qlstFiles.size() - iMediaFileCount;    //任务中有多少个测试数据文件
    logInfo(QString("task data file count: %1.").arg(iDataFileCount).toLatin1().data());

    return bEnableUpload;
}

/*************************************************
函数名：saveImgMediaUrlPath
输入参数: strMainImgFilePath--主照片路径
         listMediaFilePath--音频路径
         listImageFilePath--图片路径
         localRemoteMap--文件本地路径和上传的远端路径的匹配
输出参数：strRemoteMainImgFilePath--远端主照片路径
         listRemoteMediaFilePath--远端音频路径
         listRemoteImageFilePath--远端图片路径
返回值：void
功能：保存音频、图片的上传远端路径
*************************************************************/
void PDATask::saveImgMediaUrlPath( const QString &strMainImgFilePath,
                                   const QStringList &listMediaFilePath,
                                   const QStringList &listImageFilePath,
                                   const QMap<QString, QString> &localRemoteMap,
                                   QString &strRemoteMainImgFilePath,
                                   QStringList &listRemoteMediaFilePath,
                                   QStringList &listRemoteImageFilePath)
{
    //modify by Mountains
    listRemoteImageFilePath.clear();
    listRemoteMediaFilePath.clear();

    //保存音频、图片的远端路径
    if( localRemoteMap.contains( strMainImgFilePath ) )
    {
        QString dataFileUrl = localRemoteMap.value( strMainImgFilePath );
        strRemoteMainImgFilePath = dataFileUrl;
    }

    QString strFile;
    for(int i = 0, iSize = listMediaFilePath.size(); i < iSize; ++i)
    {
        strFile = listMediaFilePath.at(i);
        if( localRemoteMap.contains( strFile ) )
        {
            QString dataFileUrl = localRemoteMap.value( strFile );
            if(i >= listRemoteMediaFilePath.size())
            {
                listRemoteMediaFilePath.append(dataFileUrl);
            }
            else
            {
                listRemoteMediaFilePath.replace(i, dataFileUrl);
            }
        }
    }

    for(int i = 0, iSize = listImageFilePath.size(); i < iSize; ++i)
    {
        strFile = listImageFilePath.at(i);
        if( localRemoteMap.contains( strFile ) )
        {
            QString dataFileUrl = localRemoteMap.value( strFile );
            if(i >= listRemoteImageFilePath.size())
            {
                listRemoteImageFilePath.append(dataFileUrl);
            }
            else
            {
                listRemoteImageFilePath.replace(i, dataFileUrl);
            }
        }
    }

    return;
}

bool PDATask::saveLocalTestDataUrlPath(const TaskInfo &taskInfo, const QMap<QString, QString> &localRemoteMap)
{
    bool bRet = true;
    //读取任务的详细信息
    TaskInfo sTaskInfo = taskInfo;
    sTaskInfo.lRemoteImageFilePath.clear();
    sTaskInfo.lRemoteMediaFilePath.clear();

    //modify by Mountains
    //保存taskinfo的音频、图片的远端路径
    saveImgMediaUrlPath(sTaskInfo.strMainImgFilePath,
                        sTaskInfo.lMediaFilePath,
                        sTaskInfo.lImageFilePath,
                        localRemoteMap,
                        sTaskInfo.strRemoteMainImgFilePath,
                        sTaskInfo.lRemoteMediaFilePath,
                        sTaskInfo.lRemoteImageFilePath);

    QStringList qlstRMFilePathTmp = sTaskInfo.lRemoteMediaFilePath;
    QStringList qlstRIFilePathTmp = sTaskInfo.lRemoteImageFilePath;

    QVector<ItemPatrolType> vPatrolTypes;
    vPatrolTypes.clear();

    TaskFileIO tmpFileIO;
    if(TaskFileIO::OPERATE_SUCCESS == tmpFileIO.readTaskFile(sTaskInfo, vPatrolTypes, taskInfo.strFilePath))
    {
        sTaskInfo.lRemoteMediaFilePath = qlstRMFilePathTmp;
        sTaskInfo.lRemoteImageFilePath = qlstRIFilePathTmp;

        //遍历各个测试项的数据
        for( QVector<ItemPatrolType>::iterator patrolTypeIter = vPatrolTypes.begin();
             patrolTypeIter != vPatrolTypes.end(); ++patrolTypeIter )
        {
            PDAServiceNS::PatrolType ePatrolType = patrolTypeIter->ePatrolType;
            for( QVector<ItemTestType>::iterator testTypeIter = patrolTypeIter->testTypes.begin();
                 testTypeIter != patrolTypeIter->testTypes.end(); ++testTypeIter )
            {
                for( QVector<ItemBay>::iterator bayIter = testTypeIter->bays.begin();
                     bayIter != testTypeIter->bays.end(); ++bayIter )
                {
                    //modify by Mountains
                    //保存bay的音频、图片的远端路径
                    QStringList qlstRMFPathTmp;
                    QStringList qlstRIFPathTmp;

                    saveImgMediaUrlPath(bayIter->strMainImgFilePath,
                                        bayIter->lMediaFilePath,
                                        bayIter->lImageFilePath,
                                        localRemoteMap,
                                        bayIter->strRemoteMainImgFilePath,
                                        qlstRMFPathTmp,
                                        qlstRIFPathTmp);

                    bayIter->lRemoteMediaFilePath = qlstRMFPathTmp;
                    bayIter->lRemoteImageFilePath = qlstRIFPathTmp;
                }

                if(ePatrolType != PDAServiceNS::COMBINED_ELECTRIC)//non gis
                {
                    for( QVector<ItemDevice>::iterator deviceIter = testTypeIter->devices.begin();
                         deviceIter != testTypeIter->devices.end(); ++deviceIter )
                    {
                        //modify by Mountains
                        //保存device的音频、图片的远端路径

                        QStringList qlstRMFPathTmp;
                        QStringList qlstRIFPathTmp;

                        saveImgMediaUrlPath(deviceIter->strMainImgFilePath,
                                            deviceIter->lMediaFilePath,
                                            deviceIter->lImageFilePath,
                                            localRemoteMap,
                                            deviceIter->strRemoteMainImgFilePath,
                                            qlstRMFPathTmp,
                                            qlstRIFPathTmp);

                        deviceIter->lRemoteMediaFilePath = qlstRMFPathTmp;
                        deviceIter->lRemoteImageFilePath = qlstRIFPathTmp;

                        for( QVector<ItemTestPoint>::iterator testPointIter = deviceIter->testPoints.begin();
                             testPointIter != deviceIter->testPoints.end(); ++testPointIter )
                        {
                            //modify by Mountains
                            //保存testpoint的音频、图片的远端路径
                            QStringList qlstRMFPathTmp;
                            QStringList qlstRIFPathTmp;
                            saveImgMediaUrlPath(testPointIter->strMainImgFilePath,
                                                testPointIter->lMediaFilePath,
                                                testPointIter->lImageFilePath,
                                                localRemoteMap,
                                                testPointIter->strRemoteMainImgFilePath,
                                                qlstRMFPathTmp,
                                                qlstRIFPathTmp);

                            testPointIter->lRemoteMediaFilePath = qlstRMFPathTmp;
                            testPointIter->lRemoteImageFilePath = qlstRIFPathTmp;

                            for( QVector<ItemTestData>::iterator testDataIter = testPointIter->testDatas.begin();
                                 testDataIter != testPointIter->testDatas.end(); ++testDataIter )
                            {
                                if( localRemoteMap.contains( testDataIter->strFilePath ) )
                                {
                                    QString dataFileUrl = localRemoteMap.value( testDataIter->strFilePath );
                                    testDataIter->strRemotePath = dataFileUrl;
                                }
                                else
                                {
                                    logWarning(QString("not contains file path (%1).").arg(testDataIter->strFilePath).toLatin1().data());
                                }

                                if(localRemoteMap.contains(testDataIter->strAttachPath))
                                {
                                    testDataIter->strRemoteAttachPath = localRemoteMap.value(testDataIter->strAttachPath);
                                }
                            }
                        }
                    }
                }
                else  //gis
                {
                    //device
                    for( QVector<ItemBay>::iterator bayIter = testTypeIter->bays.begin();
                         bayIter != testTypeIter->bays.end(); ++bayIter )
                        for( QVector<ItemDeviceTaskFileInfo>::iterator deviceIter = bayIter->vecDevice.begin();
                             deviceIter != bayIter->vecDevice.end(); ++deviceIter )
                        {
                            //modify by Mountains
                            //保存device的音频、图片的远端路径
                            QStringList qlstRMFPathTmp;
                            QStringList qlstRIFPathTmp;
                            saveImgMediaUrlPath(deviceIter->strMainImgFilePath,
                                                deviceIter->lMediaFilePath,
                                                deviceIter->lImageFilePath,
                                                localRemoteMap,
                                                deviceIter->strRemoteMainImgFilePath,
                                                qlstRMFPathTmp,
                                                qlstRIFPathTmp);
                            deviceIter->lRemoteMediaFilePath = qlstRMFPathTmp;
                            deviceIter->lRemoteImageFilePath = qlstRIFPathTmp;
                        }

                    //test point
                    for( QVector<ItemDevice>::iterator deviceIter = testTypeIter->devices.begin();
                         deviceIter != testTypeIter->devices.end(); ++deviceIter )
                    {
                        for( QVector<ItemTestPoint>::iterator testPointIter = deviceIter->testPoints.begin();
                             testPointIter != deviceIter->testPoints.end(); ++testPointIter )
                        {
                            //modify by Mountains
                            //保存testpoint的音频、图片的远端路径
                            QStringList qlstRMFPathTmp;
                            QStringList qlstRIFPathTmp;
                            saveImgMediaUrlPath(testPointIter->strMainImgFilePath,
                                                testPointIter->lMediaFilePath,
                                                testPointIter->lImageFilePath,
                                                localRemoteMap,
                                                testPointIter->strRemoteMainImgFilePath,
                                                qlstRMFPathTmp,
                                                qlstRIFPathTmp);
                            testPointIter->lRemoteMediaFilePath = qlstRMFPathTmp;
                            testPointIter->lRemoteImageFilePath = qlstRIFPathTmp;

                            for( QVector<ItemTestData>::iterator testDataIter = testPointIter->testDatas.begin();
                                 testDataIter != testPointIter->testDatas.end(); ++testDataIter )
                            {
                                if( localRemoteMap.contains( testDataIter->strFilePath ) )
                                {
                                    QString dataFileUrl = localRemoteMap.value( testDataIter->strFilePath );
                                    testDataIter->strRemotePath = dataFileUrl;
                                }
                                else
                                {
                                    logWarning(QString("not contains file path (%1).").arg(testDataIter->strFilePath).toLatin1().data());
                                }

                                if(localRemoteMap.contains(testDataIter->strAttachPath))
                                {
                                    testDataIter->strRemoteAttachPath = localRemoteMap.value(testDataIter->strAttachPath);
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    else
    {
        bRet = false;
    }

    //保存任务到文件
    if( bRet )
    {
        if( TaskFileIO::OPERATE_SUCCESS != tmpFileIO.writeTaskFile(sTaskInfo, vPatrolTypes) )
        {
            bRet = false;
        }
    }

    return bRet;
}

/*************************************************
函数名：
输入参数: strInnerId--任务的概要信息
         strTestNumber--任务待保存的测试编号
输出参数：NULL
返回值：false--保存任务编号失败 true--保存任务编号成功
功能：保存任务测试编号到任务描述文件中
*************************************************************/
bool PDATask::uploadTestNumber( const TaskInfo& taskInfo,const QString & strTestNumber )
{
    bool bRet = true;
    //读取任务的详细信息
    TaskInfo sTaskInfo = taskInfo;
    sTaskInfo.strTestNumber = strTestNumber;
    QVector<ItemPatrolType> vPatrolTypes;
    TaskFileIO tmpFileIO;
    if( TaskFileIO::OPERATE_SUCCESS != tmpFileIO.writeTaskFile(sTaskInfo, vPatrolTypes) )
    {
        bRet = false;
    }

    return bRet;
}

bool PDATask::isTaskTestData(const ItemTestType& testType, const QString& fileName)
{
    bool bRet = false;
    for(int i = 0, iDevSize = testType.devices.size(); i < iDevSize; ++i)
    {
        for(int j = 0, iPointSize = testType.devices[i].testPoints.size(); j < iPointSize; ++j)
        {
            for(int k = 0, iDataSize = testType.devices[i].testPoints[j].testDatas.size(); k < iDataSize; ++k)
            {
                if(fileName == testType.devices[i].testPoints[j].testDatas[k].strFileName)
                {
                    bRet = true;
                }
            }
        }
    }

    return bRet;
}

/*************************************************
功能：检查文件是否属于任务描述文件中记载的附件
*************************************************************/
bool PDATask::isTaskAttachFile(const ItemTestType& testType, const QString& qstrAttachPath)
{
    bool bRet = false;
    for(int i = 0, iDevSize = testType.devices.size(); i < iDevSize; ++i)
    {
        for(int j = 0, iPointSize = testType.devices[i].testPoints.size(); j < iPointSize; ++j)
        {
            for(int k = 0, iDataSize = testType.devices[i].testPoints[j].testDatas.size(); k < iDataSize; ++k)
            {
                if(qstrAttachPath == testType.devices[i].testPoints[j].testDatas[k].strAttachPath)
                {
                    bRet = true;
                }
            }
        }
    }

    return bRet;
}

bool PDATask::updateTestData(const QString& dataFile, QString qstrAttachPath)
{
    bool bRet = false;

    if(dataFile.isEmpty())
    {
        bRet = false;
        logError("dataFile is empty.");
    }
    else
    {
        //保存成功则更新测点状态
        bool bChanged = false;//测试项完成情况有没有改变
        if(m_pCurrentData)
        {
            bChanged = false;
            //log_debug("cur test number: %s, isTest: %d.", m_pCurrentData->strNumber.toLatin1().data(), m_pCurrentData->bTested);
            if(m_pCurrentData->bTested)
            {
                //如果测试项已测试完毕，则删除原有文件
                QString oldFilePath = m_pCurrentData->strFilePath;
                if(oldFilePath != dataFile)
                {
                    //避免删除同名的文件，导致最终文件不存在
                    FileOperUtil::deleteFile(oldFilePath);
                }

                QString qstrOldAttPath = m_pCurrentData->strAttachPath;
                if(qstrOldAttPath != qstrAttachPath)
                {
                    //避免删除同名的文件，导致最终文件不存在
                    FileOperUtil::deleteFile(qstrOldAttPath);
                    //马来西亚去除音频WAV转换MP3的需求
                    //#ifdef _MALAYSIA_VERSION_DEFINED_
                    //QString qstrWavFile = FileOperUtil::replaceFileSurrfix(qstrOldAttPath, AE_RECORD_FILE_NAME_SURRFIX);
                    //FileOperUtil::deleteFile(qstrWavFile);
                    //#endif
                }
            }
            else
            {
                bChanged = true;
                m_pCurrentData->bTested = true;
            }

            //更新数据文件信息
            m_pCurrentData->strFilePath = dataFile;
            m_pCurrentData->strFileName = QFileInfo(dataFile).fileName();
            m_pCurrentData->strAttachPath = qstrAttachPath;

            //判断测点的完成情况
            bool bPointTested = false;
            for(int i = 0, iSize = m_pCurrentPoint->testDatas.size(); i < iSize; ++i)
            {
                if(m_pCurrentPoint->testDatas.at(i).bTested)
                {
                    bChanged = true;
                    bPointTested = true;
                    break;
                }
            }

            m_pCurrentPoint->bTested = bPointTested;
            QString qstrInfo = QString("testData: %1, tested: %2")
                    .arg(m_pCurrentData->strNumber).arg(m_pCurrentData->bTested);
            logDebug(qstrInfo.toLatin1().data());
        }
        else
        {
            logError("Error: Current test data is NULL!");
        }

        //如果测试项完成情况改变
        if(bChanged)
        {
            //保存临时测试数据
            saveTestDataTmpInfo(*m_pCurrentData);

            //刷新各层节点的属性及显示
            refresh();
            //emit sigTaskChanged();

            //通知界面可进行自动跳转操作
            //QTimer::singleShot(Module::SWITCH_TIME_INTERVAL, this, SLOT(onAutoSwitched()));       //send singnal fail in sub thread
            //Module::mSleepWithBlock(Module::SWITCH_TIME_INTERVAL);      //block mode to protect program
            //Module::mSleep(Module::SWITCH_TIME_INTERVAL);
            //emit sigAutoSwitched();
        }
        else
        {
            //Module::mSleep(Module::SWITCH_TIME_INTERVAL);      //block mode to protect program
        }

        bRet = true;
    }

    return bRet;
}

bool PDATask::updateNonSupportTestData()
{
    if(m_pCurrentData)
    {
        m_pCurrentData->bTested = true;

        //log_debug("test data: %s, is tested: %d.", m_pCurrentData->strNumber.toLatin1().data(), m_pCurrentData->bTested);
        QString qstrInfo = QString("testData: %1, tested: %2.")
                .arg(m_pCurrentData->strNumber).arg(m_pCurrentData->bTested);
        logDebug(qstrInfo.toLatin1().data());

        bool bPointTested = false;
        for(int i = 0, iSize = m_pCurrentPoint->testDatas.size(); i < iSize; ++i)
        {
            if(m_pCurrentPoint->testDatas.at(i).bTested)
            {
                bPointTested = true;
                break;
            }
        }

        m_pCurrentPoint->bTested = bPointTested;

        //刷新各层节点的属性及显示
        refresh();
        //通知界面可进行自动跳转操作
        sendAutoSwitchSignal();
    }
    else
    {
        logError("current test data is NULL.");
    }

    return true;
}

/*************************************************
函数名：
输入参数: 将AE Amp测试数据保存在对应文件夹下
输出参数：NULL
返回值：保存是否成功
功能：保存测试数据
*************************************************************/
bool PDATask::saveAEAmpData(const AEAmpDataInfo &testData, QString qstrAttachPath)
{
    bool bRet = false;

    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        QString qsSavedPath;
        AEAmpDataSave hDataSave;

        qDebug()<<"PDATask::saveAEAmpData, currentTestType()->strItemName:"<<currentTestType()->strItemName<<endl;

        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);
        qDebug()<<"PDATask::saveAEAmpData, testDataFileSavedPath:"<<qsSavedPath<<endl;

        QString qsDataFile = hDataSave.saveData( (void*)&testData, qsSavedPath );
        logDebug(qsDataFile);
        if(!(qsDataFile.isEmpty()))
        {
            //根据保存结果更新测试点状态
            bRet = updateTestData(qsDataFile, qstrAttachPath);
        }
    }
    else
    {
        //根据保存结果更新测试点状态
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}

/*************************************************
函数名：
输入参数: 将AE phase测试数据保存在对应文件夹下
输出参数：NULL
返回值：保存是否成功
功能：保存测试数据
*************************************************************/
bool PDATask::saveAEPhaseData(const AEPhaseDataInfo &testData, QString qstrAttachPath)
{
    bool bRet = false;

    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        QString qsSavedPath;
        AEPhaseDataSave hDataSave;

        qDebug()<<"PDATask::saveAEPhaseData, currentTestType()->strItemName:"<<currentTestType()->strItemName<<endl;

        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);
        qDebug()<<"PDATask::saveAEPhaseData, testDataFileSavedPath:"<<qsSavedPath<<endl;

        QString qsDataFile = hDataSave.saveData( (void*)&testData, qsSavedPath );
        logDebug(qsDataFile);
        if(!(qsDataFile.isEmpty()))
        {
            //根据保存结果更新测试点状态
            bRet = updateTestData(qsDataFile, qstrAttachPath);
        }
    }
    else
    {
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}

/**********************
 * 功能：保存AE飞行数据
 * **************************/
bool PDATask::saveAEFlyData(const AEFlightDataInfo &testData, QString qstrAttachPath)
{
    bool bRet = false;

    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        QString qsSavedPath;
        AEFlightDataSave hDataSave;

        qDebug()<<"PDATask::saveAEFlyData, currentTestType()->strItemName:"<<currentTestType()->strItemName<<endl;

        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);
        qDebug()<<"PDATask::saveAEFlyData, testDataFileSavedPath:"<<qsSavedPath<<endl;

        QString qsDataFile = hDataSave.saveData( (void*)&testData, qsSavedPath );
        logDebug(qsDataFile);
        if(!(qsDataFile.isEmpty()))
        {
            //根据保存结果更新测试点状态
            bRet = updateTestData(qsDataFile, qstrAttachPath);
        }
    }
    else
    {
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}

/**********************
 * 功能：保存AE波形数据
 * **************************/
bool PDATask::saveAEWaveData(const AEWaveDataInfo &testData, QString qstrAttachPath)
{
    bool bRet = false;

    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        QString qsSavedPath;
        AEWaveDataSave hDataSave;

        qDebug() << "PDATask::saveAEWaveData, currentTestType()->strItemName: "<< currentTestType()->strItemName << endl;

        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);
        qDebug() << "PDATask::saveAEWaveData, testDataFileSavedPath: "<< qsSavedPath << endl;

        QString qsDataFile = hDataSave.saveData((void*)&testData, qsSavedPath);
        logDebug(qsDataFile);
        if(!(qsDataFile.isEmpty()))
        {
            //根据保存结果更新测试点状态
            bRet = updateTestData(qsDataFile, qstrAttachPath);
        }
    }
    else
    {
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}


/*************************************************
函数名：
输入参数: 将TEV测试数据保存在对应文件夹下
输出参数：NULL
返回值：保存是否成功
功能：保存测试数据
*************************************************************/
bool PDATask::saveTEVAMPData(const TEVAmpDataInfo &testData)
{
    bool bRet = false;

    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        QString qsSavedPath = "";
        TEVAmpDataMapSave hDataSave;
        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);
        //qDebug() << "PDATask::saveTEVAMPData, testDataFileSavedPath: " << qsSavedPath << endl;
        QString qsDataFile = hDataSave.saveData( (void*)&testData,qsSavedPath );
        logDebug(qsDataFile);
        if(!(qsDataFile.isEmpty()))
        {
            //根据保存结果更新测试点状态
            bRet = updateTestData(qsDataFile);
        }
    }
    else
    {
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}

/*************************************************
函数名：
输入参数: 将TEV测试数据保存在对应文件夹下
输出参数：NULL
返回值：保存是否成功
功能：保存测试数据
*************************************************************/
bool PDATask::saveTEVPulseData(const TEVPulseDataInfo &testData)
{
    bool bRet = false;

    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        QString qsSavedPath = "";
        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);

        TEVPulseDataMapSave hDataSave;
        QString qsDataFile = hDataSave.saveData((void*)&testData, qsSavedPath);
        logDebug(qsDataFile);
        if(!(qsDataFile.isEmpty()))
        {
            //根据保存结果更新测试点状态
            bRet = updateTestData(qsDataFile);
        }
    }
    else
    {
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}

/*************************************************
函数名：
输入参数: 将uhf prps测试数据保存在对应文件夹下
输出参数：NULL
返回值：保存是否成功
功能：保存测试数据
*************************************************************/
bool PDATask::saveUHFPRPSTestData(const UHFPRPSPRPDDataInfo &stData)
{
    bool bRet = false;

    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        if(static_cast<int>(stData.stPRPSInfo.eBandWidth) != static_cast<int>(m_pCurrentData->eBandWidth))
        {
            logError(QString("Current test point bandwidth (%1), data bandwidth (%2), can't save test data!").arg(m_pCurrentData->eBandWidth).arg(stData.stPRPSInfo.eBandWidth));
            return bRet;
        }

        QString qsSavedPath = "";
        UHFPRPSAndPRPDDataSave cUHFPRPSAndPRPDDataSave;
        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);
        QString qsDataFile = cUHFPRPSAndPRPDDataSave.saveData((void*)&stData, qsSavedPath);
        //根据保存结果更新测试点状态
        if(!(qsDataFile.isEmpty()))
        {
            bRet = updateTestData(qsDataFile);
        }
    }
    else
    {
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}

/*************************************************
函数名：
输入参数: 将hfct prps测试数据保存在对应文件夹下
输出参数：NULL
返回值：保存是否成功
功能：保存测试数据
*************************************************************/
bool PDATask::saveHFCTPRPSTestData(const HFCTPRPSPRPDDataInfo &stData)
{
    bool bRet = false;

    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        QString qsSavedPath = "";
        HFCTPRPSAndPRPDDataSave cHFCTPRPSAndPRPDDataSave;
        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);
        qDebug() << "PDATask::saveHFCTPRPSTestData, testDataFileSavedPath:" << qsSavedPath << endl;
        QString qsDataFile = cHFCTPRPSAndPRPDDataSave.saveData((void*)&stData, qsSavedPath);
        logDebug(qsDataFile);
        //根据保存结果更新测试点状态
        if(!(qsDataFile.isEmpty()))
        {
            bRet = updateTestData(qsDataFile);
        }
    }
    else
    {
        logError("Current test data is NULL, Can't save test data!");
    }

    return bRet;
}

/*************************************************
函数名：saveUHFPRPSCloudDiagnosisData
输入参数: stData---云诊断数据
输出参数：qsFilePath---数据文件路径
返回值：保存是否成功
功能：将云诊断数据测试数据保存在对应文件夹下
*************************************************************/
bool PDATask::saveUHFPRPSCloudDiagnosisData(const UHFPRPSPRPDDataInfo &stData, QString &qsFilePath)
{
    bool bRet = false;

    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        if(static_cast<int>(stData.stPRPSInfo.eBandWidth) != static_cast<int>(m_pCurrentData->eBandWidth))
        {
            logError(QString("Current test point bandwidth (%1), data bandwidth (%2), can't save test data!").arg(m_pCurrentData->eBandWidth).arg(stData.stPRPSInfo.eBandWidth));
            return bRet;
        }

        QString qsSavedPath = "";
        UHFPRPSAndPRPDDataSave uHFPRPSAndPRPDDataSave;
        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);
        qsFilePath = uHFPRPSAndPRPDDataSave.saveData((void*)&stData, qsSavedPath);
        logDebug(qsFilePath);
        if(!(qsFilePath.isEmpty()))
        {
            //bRet = true;
            bRet = updateTestData(qsFilePath);
        }
    }
    else
    {
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}

/***************************************
 * 功能：保存红外检测的数据
 * **************************************/
bool PDATask::saveInfraredTestData(const InfraredDataInfo &stData, const DataMapHead &stHeadInfo
                                   , QString &qstrTestDataPath, QString &qstrPicPath)
{
    bool bRet = false;
    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        QString qsSavedPath = "";
        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);

        InfraredDataSave hDataSave;
        hDataSave.setDataMapInfo(stHeadInfo);
        qstrTestDataPath = hDataSave.saveData((void*)&stData, qsSavedPath);
        logDebug(qstrTestDataPath);
        if(!(qstrTestDataPath.isEmpty()))
        {
            //根据保存结果更新测试点状态
            int iPos = qstrTestDataPath.lastIndexOf('.');
            qstrPicPath = qstrTestDataPath.left(iPos) + "_infrared.jpg";
            bRet = updateTestData(qstrTestDataPath, qstrPicPath);
        }
    }
    else
    {
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}

/***************************************
 * 功能：保存高德红外检测的数据
 * **************************************/
bool PDATask::saveGuideInfraredTestData(const GuideInfraredDataInfo &stData, const DataMapHead &stHeadInfo, QString &qstrTestDataPath, QString &qstrPicPath)
{
    bool bRet = false;
    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        QString qsSavedPath = "";
        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qsSavedPath);

        GuideInfraredDataSave hDataSave;
        hDataSave.setDataMapInfo(stHeadInfo);
        qstrTestDataPath = hDataSave.saveData((void*)&stData, qsSavedPath);
        logDebug(qstrTestDataPath);
        if(!(qstrTestDataPath.isEmpty()))
        {
            //根据保存结果更新测试点状态
            int iPos = qstrTestDataPath.lastIndexOf('.');
            qstrPicPath = qstrTestDataPath.left(iPos) + "_infrared.jpg";
            bRet = updateTestData(qstrTestDataPath, qstrPicPath);
        }
    }
    else
    {
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}

/***************************************
 * 功能：保存电流幅值数据
 * **************************************/
bool PDATask::saveCurrentAmpData(const DataSpecificationNS::SpectrumHead& stSpectrumHead, const DataSpecificationNS::CurrentAmpExtInformation& stCurrentAmpExtInformation, const DataSpecificationNS::CurrentAmpData& stCurrentAmpData)
{
    bool bRet = false;
    if(m_pCurrentData)
    {
        //获取存储路径
        //将testData保存到文件中
        QString qstrSavedPath;
        testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qstrSavedPath);

        CurrentDetectionDataSave stCurrentDetectionDataSave;
        QString qstrFinalSavePath = stCurrentDetectionDataSave.saveBinaryData(stSpectrumHead, stCurrentAmpExtInformation, stCurrentAmpData, qstrSavedPath);
        logDebug(qstrFinalSavePath);
        if(!qstrFinalSavePath.isEmpty())
        {
            //根据保存结果更新测试点状态
            bRet = updateTestData(qstrFinalSavePath);
        }
    }
    else
    {
        logError("Current test data is NULL, can't save test data!");
    }

    return bRet;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：false--保存任务描述文件失败 true--保存任务描述文件成功
功能：保存信息到任务描述文件中
*************************************************************/
bool PDATask::saveTaskFile()
{
    bool bRet = true;
    //if(!m_bSynchronized)
    {
        if(!(SystemSetService::instance()->storageOperEnable()))
        {
            logWarning("unable to operate storage space");
            bRet = false;
        }
        else
        {
            //将当前任务信息保存到文件中
            TaskFileIO tmpFileIO;
            if(TaskFileIO::OPERATE_SUCCESS == tmpFileIO.writeTaskFile(m_taskInfo, m_patrolTypes))
            {
                m_bSynchronized = true;
            }
            else
            {
                bRet = false;
            }
        }
    }
    /*
    else
    {
        bRet = true;
        qDebug() << "Task file has saved.";
    }
    */

    return bRet;
}

/*************************************************
函数名：saveTaskInfoBG
功能：后台保存巡检任务信息
*************************************************************/
void PDATask::saveTaskInfoBG()
{
    //保存临时巡检信息，使用临时变量，避免访问冲突和不同步的问题
    TaskInfo stTmpInfo = m_taskInfo;
    QVector<ItemPatrolType> vtTmpPatrolType = m_patrolTypes;
    TaskFileIO tmpFileIO;
    tmpFileIO.writeTaskFile(stTmpInfo, vtTmpPatrolType);
    return;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：NULL
功能：重新计算已完成测点和总测点
*************************************************************/
void PDATask::refresh()
{
    if((NULL != m_pCurrentPatrolType) && (NULL != m_pCurrentTestType) &&
            (NULL != m_pCurrentDevice) && (NULL != m_pCurrentPoint) &&
            (NULL != m_pCurrentData))
    {
        //从底往上逐级更新测试项统计数据
        int finishedCount = 0;  //已完成的计数
        int totalCount = 0;     //测试项总数
        //更新设备的测点数目
        for(int i = 0, iTestPointSize = m_pCurrentDevice->testPoints.size(); i < iTestPointSize; ++i)
        {
            ItemTestPoint* pCurTestPoint = &(m_pCurrentDevice->testPoints[i]);
            int iTestDataSize = pCurTestPoint->testDatas.size();
            totalCount += iTestDataSize;
            //遍历计算测点下已测测试项个数
            for(int j = 0; j < iTestDataSize; ++j)
            {
                if(pCurTestPoint->testDatas.at(j).bTested)
                {
                    ++finishedCount;
                }
            }
        }
        m_pCurrentDevice->uiTotalCount = static_cast<unsigned int>(totalCount);
        m_pCurrentDevice->uiTestedCount = static_cast<unsigned int>(finishedCount);

        //更新测点类型数目
        finishedCount = 0;
        totalCount = 0;
        for(int i = 0, iSize = m_pCurrentTestType->devices.size(); i <iSize ; ++i)
        {
            totalCount += m_pCurrentTestType->devices.at(i).uiTotalCount;
            finishedCount += m_pCurrentTestType->devices.at(i).uiTestedCount;
        }
        m_pCurrentTestType->uiTotalCount = static_cast<unsigned int>(totalCount);
        m_pCurrentTestType->uiTestedCount = static_cast<unsigned int>(finishedCount);

        //更新巡检类型数目
        finishedCount = 0;
        totalCount = 0;
        for(int i = 0, iSize = m_pCurrentPatrolType->testTypes.size(); i < iSize; ++i)
        {
            totalCount += m_pCurrentPatrolType->testTypes.at(i).uiTotalCount;
            finishedCount += m_pCurrentPatrolType->testTypes.at(i).uiTestedCount;
        }
        m_pCurrentPatrolType->uiTotalCount = static_cast<unsigned int>(totalCount);
        m_pCurrentPatrolType->uiTestedCount = static_cast<unsigned int>(finishedCount);

        //更新任务测点情况
        finishedCount = 0;
        totalCount = 0;
        for(int i = 0, iSize = m_patrolTypes.size(); i < iSize; ++i)
        {
            totalCount += m_patrolTypes.at(i).uiTotalCount;
            finishedCount += m_patrolTypes.at(i).uiTestedCount;
        }
        m_taskInfo.uiTotalCount = static_cast<unsigned int>(totalCount);
        m_taskInfo.uiTestedCount = static_cast<unsigned int>(finishedCount);

        QDateTime currentTime = QDateTime::currentDateTime();
        m_taskInfo.strTestTime = currentTime.toString(TASK_TIME_FORMAT);
        m_taskInfo.lTestTime = TimezoneManager::instance()->formatLocalTimeToUTCValMsec(QDateTime::currentDateTimeUtc().toMSecsSinceEpoch());

        m_bTaskChanged = true;
        m_bSynchronized = false;

        TaskTmpInfo stTaskTmpInfo;
        stTaskTmpInfo.qstrInnerId = m_taskInfo.strInnerId;
        stTaskTmpInfo.qi64TestTime = m_taskInfo.lTestTime;
        stTaskTmpInfo.qi32TotalTestData = static_cast<int>(m_taskInfo.uiTotalCount);
        stTaskTmpInfo.qi32TestedTestData =  static_cast<int>(m_taskInfo.uiTestedCount);

        QString qstrTaskTmpIniFolder = getCurTaskIniFolder();
        FileOperUtil::createDirectory(qstrTaskTmpIniFolder);
        QString qstrTaskTmpIniFile = qstrTaskTmpIniFolder + stTaskTmpInfo.qstrInnerId + PDA_TASK_TEMP_INI_SUFFIX;
        IniConfig::writeTaskTmpInfo(qstrTaskTmpIniFile, stTaskTmpInfo);
    }

    return;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：该任务下巡检类型的集合
功能：获取任务下巡检类型的集合
*************************************************************/
QVector<ItemPatrolType>& PDATask::patrolTypes()
{
    return m_patrolTypes;
}

/*************************************************
函数名：
输入参数: taskFile--任务描述文件地址
输出参数：NULL
返回值：对应的任务信息
功能：通过该任务对应的任务概要信息
*************************************************************/
TaskInfo PDATask::taskInfo()
{
    return m_taskInfo;
}

/*************************************************
函数名：
输入参数: patrolType--当前进入的巡检类型
输出参数：NULL
返回值：是否设置成功
功能：设置当前进入的巡检类型
*************************************************************/
bool PDATask::setCurrentPatrolType(const ItemPatrolType& patrolType)
{
    bool bFinded = false;
    m_pCurrentPatrolType = NULL;

    //在本地查找对应的巡检类型
    for(int i = 0; i < m_patrolTypes.size(); i++)
    {
        if(m_patrolTypes.at(i).strItemName == patrolType.strItemName)
        {
            m_pCurrentPatrolType = &m_patrolTypes[i];
            bFinded = true;
        }
    }

    return bFinded;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：当前进入的巡检类型的指针
功能：获取当前进入的巡检类型
*************************************************************/
ItemPatrolType* PDATask::currentPatrolType()
{
    return m_pCurrentPatrolType;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：当前进入的巡检类型的指针
功能：获取当前进入的巡检类型
*************************************************************/
double PDATask::voltage()
{
    dbg_info("m_taskInfo.dVoltage is %f\n", m_taskInfo.dVoltage);
    return m_taskInfo.dVoltage;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：NULL
功能：在当前测点下新增测试项
*************************************************************/
void PDATask::addTestData(PDAServiceNS::TestDataType type, PDAServiceNS::UhfBWType eBwType, bool bIsBgn)
{
    ItemTestData testData;
    testData.eDataType = type;
    testData.bIsBgn = bIsBgn;
    testData.eBandWidth = eBwType;

    return addTestData(testData);
}

void PDATask::addTestData(ItemTestData &stTestData)
{
    if(m_pCurrentPoint != NULL)
    {
        quint16 dataPos = m_pCurrentData->usIndex;

        //设置新增数据项的属性
        //stTestData.usIndex = m_pCurrentPoint->testDatas.size();
        stTestData.usIndex = (getTestDatasMaxIndex(m_pCurrentPoint->testDatas) + 1);
        stTestData.bTested = false;
        stTestData.strFileName = "";
        stTestData.strFilePath = "";
        stTestData.strAttachPath = "";

        //QT生成的uuid较java生成的uuid来说前后多了{}标记，
        //即java为102532b3-3d04-47b8-bb93-b853dd35917b, QT为{102532b3-3d04-47b8-bb93-b853dd35917b}
        QUuid uuid = QUuid::createUuid();
        QString strUuid = uuid.toString();
        if(0 == strUuid.indexOf("{") && (strUuid.length() - 1) == strUuid.indexOf("}"))
        {
            strUuid = strUuid.mid(1, strUuid.length() - 2);
        }
        stTestData.strNumber = strUuid;
        //qDebug() << "PDATask::addTestData, add test data number: " << stTestData.strNumber << endl;

        //设置类型内的序号
        int iLastIndex = 0;//该类型的最后一个测试项的序号
        int iMaxT95Id = 0;//t95生成的SourceId最大值
        //sourceId生成规则：1.格式为  t95_数字_时间 2.数字为t95开头的测试项的数字最大值+1
        for(int i = 0, iSize = m_pCurrentPoint->testDatas.size(); i < iSize; ++i)
        {
            //统计同类型下索引
            if(m_pCurrentPoint->testDatas.at(i).eDataType == stTestData.eDataType)
            {
                iLastIndex = m_pCurrentPoint->testDatas.at(i).usDataIndex;
            }
            //统计t95生成的SourceId
            QString strSourceId = m_pCurrentPoint->testDatas.at(i).strSourceId;
            if( strSourceId.startsWith( SOURCE_PREFIX ) )
            {
                QStringList strList = strSourceId.split( SOURCE_SPILT_CHAR );
                if( strList.size() == 3 )
                {
                    int iId = strList.at( 1 ).toInt();
                    iMaxT95Id = iId > iMaxT95Id ? iId : iMaxT95Id;
                }
            }
        }
        stTestData.usDataIndex = iLastIndex + 1;
        //生成测试项的SourceId
        stTestData.strSourceId = SOURCE_PREFIX + SOURCE_SPILT_CHAR + QString::number( iMaxT95Id + 1 )
                + SOURCE_SPILT_CHAR + QString::number( time( NULL ) );

        //保存临时测试数据
        saveTestDataTmpInfo(stTestData);

        //加入测点
        m_pCurrentPoint->testDatas.append(stTestData);
        for(int i = 0, iSize = m_pCurrentPoint->testDatas.size(); i < iSize; ++i)
        {
            if(dataPos == m_pCurrentPoint->testDatas.at(i).usIndex)
            {
                m_pCurrentData = &(m_pCurrentPoint->testDatas[i]);
                break;
            }
        }

        //更新统计数据
        refresh();
        emit sigTaskChanged();
    }
    else
    {
        qDebug() << "PDATask::addTestData(2), m_pCurrentPoint == NULL." << endl;
    }

    return;
}

/***************************************************
功能：获取Testdatas中最大的index
******************************************************/
quint16 PDATask::getTestDatasMaxIndex(QVector<ItemTestData>& vtTestDatas)
{
    quint16 wMaxIndex = 0;
    for(int i = 0, iSize = vtTestDatas.size(); i < iSize; ++i)
    {
        wMaxIndex = (vtTestDatas[i].usIndex > wMaxIndex) ? vtTestDatas[i].usIndex : wMaxIndex;
    }

    return wMaxIndex;
}

/*************************************************
函数名：
输入参数: testType--当前进入的测点类型
输出参数：NULL
返回值：是否设置成功
功能：设置当前进入的测点类型
*************************************************************/
bool PDATask::setCurrentTestType(const ItemTestType& testType)
{
    bool bFinded = false;
    m_pCurrentTestType = NULL;

    if(m_pCurrentPatrolType != NULL)
    {
        //在当前巡检类型中查找对应的测试类型
        QVector<ItemTestType> &testTypes = m_pCurrentPatrolType->testTypes;
        for(int i = 0, iSize = testTypes.size(); i < iSize; ++i)
        {
            if(testTypes.at(i).strItemName == testType.strItemName)
            {
                m_pCurrentTestType = &testTypes[i];
                if(m_pCurrentTestType->bays.size() > 0)
                {
                    m_qstrCurBayNumber = m_pCurrentTestType->bays[0].strNumber;
                }
                bFinded = true;
            }
        }

        //初始化跳转位置
        m_iNeedTestDevicePos = -1;
        m_iNeedTestPointPos = -1;
        m_iNeedTestDataPos = -1;
    }

    return bFinded;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：当前进入的测点类型的指针
功能：获取当前进入的测点类型
*************************************************************/
ItemTestType* PDATask::currentTestType()
{
    return m_pCurrentTestType;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：NULL
功能：根据当前巡检类型和测点类型，得到数据文件存储路径
*************************************************************/
void PDATask::testDataFileSavedPath(const PDAServiceNS::PatrolType eType, const QString &qsTestPointTypeName, QString &qsSavedPath)
{
    QString qsTaskFilePath = m_taskInfo.strFilePath;
    int iPosBeforeFileName = qsTaskFilePath.lastIndexOf('/');
    QString qsTaskFileDir = qsTaskFilePath.mid(0, iPosBeforeFileName);

    QString strVoltage = QString::number(m_pCurrentDevice->dVoltage, 'f', 2);
    int index = strVoltage.indexOf('.');
    if(strVoltage.endsWith(".00"))
    {
        //such as 10.00
        strVoltage.remove(index, 3);
    }
    else if(strVoltage.endsWith("0"))
    {
        //such as 5.10
        strVoltage.remove(strVoltage.size() - 1, 1);
    }
    else
    {
        //such as 5.16
    }

    QString strUnit = (PDAServiceNS::Voltage_V_Unit == m_pCurrentDevice->eVoltageUnit) ? "V" : "kV";
    QString qstrPatrolType = getInfoByPatrolType(eType);
    qsSavedPath = qsTaskFileDir + "/" + qstrPatrolType + "/" + qsTestPointTypeName + "_"
            + strVoltage + strUnit;
    logDebug(qsSavedPath.toLatin1().data());
    return;
}

QString PDATask::getDataFileSavedPath(const QString &qsTaskFilePath, const PDAServiceNS::PatrolType eType,
                                      const QString &qsTestPointTypeName, UINT32 uiVoltage,
                                      PDAServiceNS::TaskUnitCode eUnitCode)
{
    QFileInfo tmpInfo(qsTaskFilePath);
    QString dirPath = tmpInfo.absolutePath();
    QString qsVoltage = QString::number(uiVoltage);
    QString savedPath = dirPath + "/" + getInfoByPatrolType(eType) + "/" +
            qsTestPointTypeName + "_" + qsVoltage + taskUnitName( eUnitCode );
    return savedPath;
}

/*************************************************
函数名：
输入参数: device--当前进入的设备
输出参数：NULL
返回值：是否设置成功
功能：设置当前进入的设备
*************************************************************/
bool PDATask::setCurrentDevice(const ItemDevice& device)
{
    bool bFinded = false;
    m_pCurrentDevice = NULL;

    //在本地查找对应的设备
    if(m_pCurrentTestType != NULL)
    {
        //在当前测试类型中查找对应的设备
        QVector<ItemDevice> &devices = m_pCurrentTestType->devices;
        for(int i = 0, iSize = devices.size(); i < iSize; ++i)
        {
            if(isDeviceMatched(devices.at(i), device))//devices.at(i).strDevNum == device.strDevNum)
            {
                m_pCurrentDevice = &devices[i];
                bFinded = true;
                m_bTaskChanged = false;

                //与自动跳转设置的位置不一致，则初始化测点测试项设置的位置
                if(i != m_iNeedTestDevicePos)
                {
                    m_iNeedTestPointPos = -1;
                    m_iNeedTestDataPos = -1;
                    m_iNeedTestDevicePos = i;
                }
            }
        }
    }

    return bFinded;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：当前进入的设备的指针
功能：获取当前进入的设备
*************************************************************/
ItemDevice* PDATask::currentDevice()
{
    return m_pCurrentDevice;
}

/*************************************************
函数名：
输入参数: testPoint--当前进入的测点
输出参数：NULL
返回值：是否设置成功
功能：设置当前进入的测点
*************************************************************/
bool PDATask::setCurrentTestPoint(const ItemTestPoint& testPoint)
{
    bool bFinded = false;
    m_pCurrentPoint = NULL;

    if(m_pCurrentDevice != NULL)
    {
        //在当前设备中查找对应的测点
        QVector<ItemTestPoint> &testPoints = m_pCurrentDevice->testPoints;
        for(int i = 0; i < testPoints.size(); i++)
        {
            if(testPoints.at(i).strItemNumber == testPoint.strItemNumber)
            {
                m_pCurrentPoint = &testPoints[i];
                bFinded = true;
                //与自动跳转设置的位置不一致，则初始化测试项设置的位置
                if(i != m_iNeedTestPointPos)
                {
                    m_iNeedTestPointPos = i;
                    m_iNeedTestDataPos = -1;
                }
            }
        }
    }

    return bFinded;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：当前进入的测点的指针
功能：获取当前进入的测点
*************************************************************/
ItemTestPoint* PDATask::currentTestPoint()
{
    return m_pCurrentPoint;
}

/*************************************************
函数名：
输入参数: testData--当前进入的测试项
输出参数：NULL
返回值：是否设置成功
功能：设置当前进入的测试项
*************************************************************/
bool PDATask::setCurrentTestData(const ItemTestData& testData)
{
    bool bFinded = false;
    m_pCurrentData = NULL;

    if(m_pCurrentPoint != NULL)
    {
        //在当前设备中查找对应的测点
        QVector<ItemTestData> &testDatas = m_pCurrentPoint->testDatas;
        for(int i = 0; i < testDatas.size(); i++)
        {
            if(testDatas.at(i).strNumber == testData.strNumber)
            {
                m_pCurrentData = &testDatas[i];
                bFinded = true;
                break;
            }
        }
    }
    else
    {
        qDebug() << "!!!!!!!!!!!!!!!!Set test data index is out of range : " << testData.usIndex;
    }

    return bFinded;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：当前进入的测试项
功能：获取当前进入的测试项
*************************************************************/
ItemTestData* PDATask::currentTestData()
{
    return m_pCurrentData;
}

bool PDATask::enterUntestPointCrossDevice(const ItemTestPoint& preTestPoint)
{
    //查找下一设备的同类型未测测点
    QVector<ItemDevice> &devices = m_pCurrentTestType->devices;
    int devicePos = 0, iDevSize = devices.size();
    bool bFinded = false;

    //确定当前设备的位置
    for(devicePos = 0; devicePos < iDevSize; ++devicePos)
    {
        if(isDeviceMatched(*m_pCurrentDevice, devices.at(devicePos)))//m_pCurrentDevice->strDevNum == devices.at(devicePos).strDevNum)
        {
            bFinded = true;
            break;
        }
    }

    //从当前位置向后查找测点
    if(!bFinded)
    {
        //未匹配到指定设备，异常处理
        logError("not matched device.");
        return false;
    }

    //最终目的是为了找到测点
    bFinded = false;

    bool bNextTestPointFinded = false;
    quint32 iOrder = preTestPoint.uiTestIndex + 1;
    int iNextDevPos = 0;

    // 开关柜存在柜前为从第一个柜子->最后一个柜子跳动 柜后则由最后一个柜子->第一个柜子跳动
    if(m_pCurrentPatrolType)
    {
        if(m_pCurrentPatrolType->ePatrolType != PDAServiceNS::SWITCH_CABINET)
        {
            iNextDevPos = devicePos + 1;
        }
        else
        {
            iNextDevPos = 0;
        }
    }

    quint32 iNextMinUntestIndex = -1;
    quint32 iMinIndexInDevice = 0;
    quint32 iMinIndexInTestPoints = 0;
    // 以前是直接按顺序遍历结构体找到测试顺序等于iOrder的测点，如果该测点已测试完毕，会iOrder+1再去找下一个；如果测点的测试顺序不是连续的就可能有问题
    // 现在修改为先找到测试顺序大于上一个测点中最小测试顺序的测点
    for(int i = iNextDevPos; (!bNextTestPointFinded) && (i < iDevSize); ++i)
    {
        QVector<ItemTestPoint> &tmpPoints = devices[i].testPoints;
        // 原实现是去找同一类型的测点，现修改为通过测点的测试顺序
        for(int j = 0, iPointSize = tmpPoints.size(); j < iPointSize; ++j)
        {
            if(tmpPoints.at(j).uiTestIndex >= iOrder)
            {
                if (!tmpPoints.at(j).isTestFinished() && tmpPoints.at(j).uiTestIndex <= iNextMinUntestIndex)
                {
                    iNextMinUntestIndex = tmpPoints.at(j).uiTestIndex;
                    iMinIndexInDevice = i;
                    iMinIndexInTestPoints = j;
                }
            }
        }
    }

    if(-1 != iNextMinUntestIndex)
    {
        m_pCurrentDevice = &devices[iMinIndexInDevice];

        //设置该测点
        setSwitchTestData(iMinIndexInTestPoints);

        m_iNeedTestDevicePos = iMinIndexInDevice;
        bNextTestPointFinded = true;
        bFinded = true;
        emit sigDeviceChanged(iMinIndexInDevice);
    }

    return bFinded;
}

/*************************************************
函数名：
输入参数: preTestPoint--跳转测试项所在的测点
功能：设置指定测点下的跳转测试项
*************************************************************/
void PDATask::setSwitchTestData(int pointPos)
{
    if( ( pointPos < 0 ) || ( pointPos >= m_pCurrentDevice->testPoints.size() ) )
    {
        qDebug() << "PDATask::setSwitchTestData error testpoint size is:"
                 << m_pCurrentDevice->testPoints.size()
                 << "set potinPos is:"
                 << pointPos << endl;
        return;
    }

    //设置下一跳测点
    m_pCurrentPoint = &m_pCurrentDevice->testPoints[pointPos];
    m_iNeedTestPointPos = pointPos;

    QVector<ItemTestData> &testDatas = m_pCurrentPoint->testDatas;
    for(int iDataPos = 0, iSize = testDatas.size(); iDataPos < iSize; ++iDataPos)
    {
        if(!testDatas[iDataPos].bTested)
        {
            //设置下一跳测试项
            m_pCurrentData = &(m_pCurrentPoint->testDatas[iDataPos]);
            m_iNeedTestDataPos = iDataPos;
            break;
        }
    }

    return;
}

/*************************************************
函数名：
输入参数: preTestPoint--上一个已测完的测点
输出参数：testDataPos--跳入测试项的位置
返回值：true--进入同一跳转类型下的下一个未测测点 false--同一跳转类型下的测点已跳完
功能：获取当前进入的测点
*************************************************************/
bool PDATask::enterNextTestData(const ItemTestPoint& currentTestPoint, int &testDataPos )
{
    Q_UNUSED(testDataPos);
    bool bEnterSuccessed = false;//是否查找到下一条应进的测试项

    if(m_pCurrentDevice)
    {
        bool bFinded = false;
        int iPrePointPos = 0, iSize = 0;

        //在当前设备中定位当前测点位置
        QVector<ItemTestPoint> &testPoints = m_pCurrentDevice->testPoints;
        for(iPrePointPos = 0, iSize = testPoints.size(); iPrePointPos < iSize; ++iPrePointPos)
        {
            if(testPoints[iPrePointPos].strItemNumber == currentTestPoint.strItemNumber)
            {
                bFinded = true;
                break;
            }
        }

        if(!bFinded)
        {
            logError("current test point is not found.");
            return false;
        }

        if(!currentTestPoint.isTestFinished())
        {
            //在当前测点中设置下一测试项
            setSwitchTestData(iPrePointPos);
            bEnterSuccessed = true;
        }
        else
        {
            bFinded = false;
            quint32 iOrder = currentTestPoint.uiTestIndex + 1;

            //同设备中查找下一同类型未测测试项
            for(int i = iPrePointPos + 1, iSize = testPoints.size(); i < iSize; ++i)
            {
                if(testPoints.at(i).uiTestIndex == iOrder)
                {
                    if(!testPoints.at(i).isTestFinished())
                    {
                        if(SystemSet::PATROL_AUTO_SWITCH == m_ePatrolMode)
                        {
                            //设置下一跳测点
                            setSwitchTestData(i);
                            bEnterSuccessed = true;
                            bFinded = true;
                            //发送测点改变信号
                            emit sigTestPointChanged(m_iNeedTestPointPos);
                            break;
                        }
                        else
                        {
                            if(PDAServiceNS::PATROL_POS_NONE == m_eCurPatrolPos)
                            {
                                //设置下一跳测点
                                setSwitchTestData(i);
                                bEnterSuccessed = true;
                                bFinded = true;
                                //发送测点改变信号
                                emit sigTestPointChanged(m_iNeedTestPointPos);
                                break;
                            }
                            else
                            {
                                if(testPoints.at(i).ePatrolPosition == m_eCurPatrolPos)
                                {
                                    //设置下一跳测点
                                    setSwitchTestData(i);
                                    bEnterSuccessed = true;
                                    bFinded = true;
                                    //发送测点改变信号
                                    emit sigTestPointChanged(m_iNeedTestPointPos);
                                    break;
                                }
                                else
                                {
                                    ++iOrder;
                                }
                            }
                        }
                    }
                    else
                    {
                        ++iOrder;
                    }
                }
            }

            //跨设备查找同类型未测测点
            if(!bFinded)
            {
                if(SystemSet::PATROL_AUTO_SWITCH == m_ePatrolMode)
                {
                    if(enterUntestPointCrossDevice(currentTestPoint))
                    {
                        bEnterSuccessed = true;
                    }
                    else
                    {
                        m_iNeedTestDevicePos = -1;
                        m_iNeedTestPointPos = -1;
                        m_iNeedTestDataPos = -1;
                        emit sigDeviceChanged(-1);
                    }
                }
                else
                {
                    m_iNeedTestDevicePos = -1;
                    m_iNeedTestPointPos = -1;
                    m_iNeedTestDataPos = -1;
                    emit sigDeviceChanged(-1);
                }
            }
        }
    }

    return bEnterSuccessed;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：testPointPos--跳入测点的位置
返回值：true--进入当前测点类型下的下一个未测测点 false--该测试类型下所有测点已测完
功能：进入当前测点类型下第一个未测测点
*************************************************************/
bool PDATask::enterFirstUntestPoint(UINT32 &iDevicePos, int &iTestPointPos)
{
    bool bEnterSuccessed = false;

    if(m_pCurrentTestType->uiTestedCount == m_pCurrentTestType->uiTotalCount)
    {
        bEnterSuccessed = false;
    }
    else
    {
        int devicePos = 0, pointPos = 0, iDevSize = 0, iPointSize = 0;
        QVector<ItemDevice> &devices = m_pCurrentTestType->devices;
        for(devicePos = 0, iDevSize = devices.size(); devicePos < iDevSize; ++devicePos)
        {
            if(devices.at(devicePos).uiTestedCount < devices.at(devicePos).uiTotalCount)
            {
                QVector<ItemTestPoint> &tmpPoints = devices[devicePos].testPoints;
                for(pointPos = 0, iPointSize = tmpPoints.size(); pointPos < iPointSize; ++pointPos)
                {
                    if(!tmpPoints.at(pointPos).isTestFinished())
                    {
                        bEnterSuccessed = true;
                        break;
                    }
                }
                break;
            }
        }

        if(bEnterSuccessed)
        {
            iDevicePos = devicePos;
            m_pCurrentDevice = &m_pCurrentTestType->devices[iDevicePos];
            iTestPointPos = pointPos;
            m_pCurrentPoint = &m_pCurrentDevice->testPoints[iTestPointPos];
            m_iNeedTestPointPos = iTestPointPos;
            //emit sigDeviceChanged(iDevicePos);
        }
    }

    if(!bEnterSuccessed)
    {
        m_iNeedTestPointPos = -1;
        m_iNeedTestDataPos = -1;
    }

    return bEnterSuccessed;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：-1--ERROR CODE >=0--test device pos
功能：获取未测试完成的设备索引
*************************************************************/
int PDATask::getNeedTestDevPos()
{
    int iPos = 0;
    if(-1 == m_iNeedTestDevicePos && m_pCurrentTestType)
    {
        // 以前是直接按顺序遍历结构体找到第一个未测设备就是下一个未测设备，但是可能会有不按照测试顺序存储的情况，所以需要先遍历，找到最小测试顺序的设备
        quint32 iMinIndexInDevice = 0;
        quint32 iMinIndex = -1;
        for(quint32 i = 0, iDevSize = m_pCurrentTestType->devices.size(); i < iDevSize; ++i)
        {
            if((0 != m_pCurrentTestType->devices.at(i).uiTotalCount)
                    && (m_pCurrentTestType->devices.at(i).uiTestedCount < m_pCurrentTestType->devices.at(i).uiTotalCount))
            {
                for (quint32 j = 0, iTestPointSize = m_pCurrentTestType->devices.at(i).testPoints.size(); j < iTestPointSize; ++j)
                {
                    if (m_pCurrentTestType->devices.at(i).testPoints.at(j).isTestFinished())
                    {
                        continue;
                    }

                    if (iMinIndex > m_pCurrentTestType->devices.at(i).testPoints.at(j).uiTestIndex)
                    {
                        iMinIndex = m_pCurrentTestType->devices.at(i).testPoints.at(j).uiTestIndex;
                        iMinIndexInDevice = i;
                    }
                }
            }
        }
        iPos = m_iNeedTestDevicePos = iMinIndexInDevice;
    }
    else
    {
        iPos = m_iNeedTestDevicePos;
    }

    return iPos;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：-1--ERROR CODE >=0--test device pos
功能：获取未测试虚拟背景设备
*************************************************************/
int PDATask::getUntestBGDevPos()
{
    int iPos = -1;
    if(m_pCurrentTestType)
    {
        for(int i = 0, iDevSize = m_pCurrentTestType->devices.size(); i < iDevSize; ++i)
        {
            if((0 != m_pCurrentTestType->devices[i].uiTotalCount)
                    && (m_pCurrentTestType->devices[i].uiTestedCount < m_pCurrentTestType->devices[i].uiTotalCount))
            {
                if(m_pCurrentTestType->devices[i].b_isBGNTest)
                {
                    iPos = m_iNeedTestDevicePos = i;
                    break;
                }
            }
        }
    }

    return iPos;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：-1--ERROR CODE >=0--test point pos
功能：获取当前应测的测点位置
*************************************************************/
int PDATask::getNeedTestPointPos()
{
    int iTestPointPos = 0;
    if(-1 == m_iNeedTestPointPos && m_pCurrentDevice)
    {
        //如果自动跳转未指定下一个应测的测点位置，则查找第一个未测测点的位置
        for(int i = 0, iSize = m_pCurrentDevice->testPoints.size(); i < iSize; ++i)
        {
            if(!m_pCurrentDevice->testPoints.at(i).isTestFinished())
            {
                if(SystemSet::PATROL_RFID_SCAN_SWITCH == m_ePatrolMode)
                {
                    if(PDAServiceNS::PATROL_POS_NONE == m_eCurPatrolPos)
                    {
                        iTestPointPos = m_iNeedTestPointPos = i;
                        break;
                    }
                    else
                    {
                        if(m_pCurrentDevice->testPoints.at(i).ePatrolPosition == m_eCurPatrolPos)
                        {
                            iTestPointPos = m_iNeedTestPointPos = i;
                            break;
                        }
                    }
                }
                else
                {
                    iTestPointPos = m_iNeedTestPointPos = i;
                    break;
                }
            }
        }
    }
    else
    {
        //如果已指定下一个应测测点的位置，则设置为该位置
        iTestPointPos = m_iNeedTestPointPos;
    }

    return iTestPointPos;
}

/*************************************************
功能：初始化测试位置
*************************************************************/
void PDATask::initTestPos( void )
{
    m_iNeedTestPointPos = -1;
    m_iNeedTestDataPos = -1;
    return;
}

/*************************************************
函数名：
输入参数: NULL
输出参数：NULL
返回值：-1--ERROR CODE >=0--test point pos
功能：获取当前应测的测试项位置
*************************************************************/
int PDATask::getNeedTestDataPos()
{
    int iPos = 0;
    if(-1 == m_iNeedTestDataPos && m_pCurrentPoint)
    {
        //如果自动跳转未指定下一个应测的测试项位置，则查找第一个未测测试项的位置
        for(int i = 0, iSize = m_pCurrentPoint->testDatas.size(); i < iSize; ++i)
        {
            if(!(m_pCurrentPoint->testDatas.at(i).bTested))
            {
                iPos = m_iNeedTestDataPos = i;
                break;
            }
        }
    }
    else
    {
        iPos = m_iNeedTestDataPos;
    }

    return iPos;
}

/*************************************************
函数名：
输入参数: eUnitCode--任务内单位的枚举
输出参数：NULL
返回值：单位对应的名称
功能：获取指定单位的文字表示
*************************************************************/
QString PDATask::taskUnitName( PDAServiceNS::TaskUnitCode eUnitCode )
{
    QString strUnitName = "";
    switch( eUnitCode )
    {
    case PDAServiceNS::Voltage_V_Unit:
        strUnitName = "V";
        break;
    case PDAServiceNS::Voltage_KV_Unit:
        strUnitName = "kV";
        break;
    default:
        strUnitName = "NON";
        break;
    }
    return strUnitName;
}

/*************************************************
输入参数: ldevice--比较的设备文件信息
         rdevice--比较的设备文件信息
返回值：两个设备文件是否匹配
功能：匹配成功返回true，失败返回false
*************************************************************/
bool PDATask::isDeviceMatched( const ItemDevice& ldevice, const ItemDevice& rdevice )
{
    bool bMatched = false;
    if( !ldevice.strDevNum.isEmpty() )
    {
        bMatched = ( ldevice.strDevNum == rdevice.strDevNum ) ;
    }
    else
    {
        logError("device's devNum is empty.");
    }
    return bMatched;
}

/*************************************************
函数名：
输入参数: ePatrolType -- 设备类型
         uiVolNum -- 电压等级
         eVoltageUnit -- 电压单位
输出参数：ampData -- 背景噪声测试值
返回值：是否有背景噪声测试值，false为未进行背景噪声测试
功能：获取同一设备类型、同一电压等级下的背景噪声测试值
*************************************************************/
bool PDATask::getAEBgnData(int iDevType,
                           const QString& strType,
                           PDAServiceNS::TestPosition eTestPosition,
                           AE::AmplitudeData& ampData)
{
    DeviceBGNInfo tmpBgnInfo;
    tmpBgnInfo.iDevType = iDevType;
    tmpBgnInfo.strType = strType;
    tmpBgnInfo.eTestPosition = eTestPosition;
    bool bExist = m_aeBgnMap.contains(tmpBgnInfo);
    if(bExist)
    {
        ampData = m_aeBgnMap[tmpBgnInfo];
    }

    return bExist;
}

/*************************************************
函数名：
输入参数: ePatrolType -- 设备类型
         uiVolNum -- 电压等级
         eVoltageUnit -- 电压单位
         ampData -- 背景噪声测试值
输出参数：NULL
返回值：NULL
功能：获取同一设备类型、同一电压等级下的背景噪声测试值
*************************************************************/
void PDATask::setAEBgnData(int iDevType,
                           const QString& strType,
                           PDAServiceNS::TestPosition eTestPosition,
                           const AE::AmplitudeData& ampData)
{
    DeviceBGNInfo tmpBgnInfo;
    tmpBgnInfo.iDevType = iDevType;
    tmpBgnInfo.strType = strType;
    tmpBgnInfo.eTestPosition = eTestPosition;
    m_aeBgnMap[tmpBgnInfo] = ampData;

    return;
}

/*************************************************
函数名：
输入参数: ePatrolType -- 设备类型
         uiVolNum -- 电压等级
         eVoltageUnit -- 电压单位
输出参数：ampData -- 背景噪声测试值
返回值：是否有背景噪声测试值，false为未进行背景噪声测试
功能：获取同一设备类型、同一电压等级下的背景噪声测试值
*************************************************************/
void PDATask::clearAEBgnData(int iDevType, const QString& strType, PDAServiceNS::TestPosition eTestPosition)
{
    DeviceBGNInfo tmpBgnInfo;
    tmpBgnInfo.iDevType = iDevType;
    tmpBgnInfo.strType = strType;
    tmpBgnInfo.eTestPosition = eTestPosition;
    m_aeBgnMap.remove(tmpBgnInfo);
    return;
}

/*************************************************
函数名：sendAutoSwitchSignal
功能：释放自动跳转跳转信号
*************************************************************/
void PDATask::sendAutoSwitchSignal()
{
    emit sigTaskChanged();
    emit sigAutoSwitched();
    logInfo("send switch signal success.");
    return;
}

/*************************************************
函数名：sendAutoSwitchSignal
功能：根据巡检类型获取文件夹格式
*************************************************************/
QString PDATask::getInfoByPatrolType(const PDAServiceNS::PatrolType eType)
{
    QString qstrInfo = "";
    switch(eType)
    {
    case PDAServiceNS::TRANSFORMER:
    {
        qstrInfo = "Transformer";//变压器
        break;
    }
    case PDAServiceNS::CIRCUIT_BREAKER:
    {
        qstrInfo = "Circuit_Breaker";//断路器
        break;
    }
    case PDAServiceNS::ISLATING_SWITCHER:
    {
        qstrInfo = "Isolation_Switcher";//隔离开关
        break;
    }
    case PDAServiceNS::KNIFE_GATE:
    {
        qstrInfo = "Knife_Gate";//刀闸
        break;
    }
    case PDAServiceNS::LIGHTNING_ARRESTER:
    {
        qstrInfo = "Lightning_Arrester";//避雷器
        break;
    }
    case PDAServiceNS::VOLTAGE_TRANSFORMER:
    {
        qstrInfo = "Voltage_Transformer";//电压互感器
        break;
    }
    case PDAServiceNS::CURRENT_TRANSFORMER:
    {
        qstrInfo = "Current_Transformer";//电流互感器
        break;
    }
    case PDAServiceNS::BUS_BAR:
    {
        qstrInfo = "Bus_Bar";//母线
        break;
    }
    case PDAServiceNS::BUS_COUPLER:
    {
        qstrInfo = "Bus_Coupler";//母联
        break;
    }
    case PDAServiceNS::SWITCH_CABINET:
    {
        qstrInfo = "Switchgear";//开关柜
        break;
    }
    case PDAServiceNS::POWER_CABLE:
    {
        qstrInfo = "Cable";//电力电缆
        break;
    }
    case PDAServiceNS::LIGHTNING_ROD:
    {
        qstrInfo = "Lightning_Rod";//避雷针
        break;
    }
    case PDAServiceNS::WALL_BUSHING:
    {
        qstrInfo = "Wall_Bushing";//穿墙套管
        break;
    }
    case PDAServiceNS::ELECTRIC_RECATOR:
    {
        qstrInfo = "Reactor";//电抗器
        break;
    }
    case PDAServiceNS::ELECTRIC_CONDUCTOR:
    {
        qstrInfo = "Conductor";//电力导线
        break;
    }
    case PDAServiceNS::POWER_CAPACITOR:
    {
        qstrInfo = "Capacitor";//电力电容器
        break;
    }
    case PDAServiceNS::DISCHARGE_COIL:
    {
        qstrInfo = "Discharge_Coil";//放电线圈
        break;
    }
    case PDAServiceNS::LOAD_SWITCHER:
    {
        qstrInfo = "Load_Switcher";//负荷开关
        break;
    }
    case PDAServiceNS::GROUND_CHANGE:
    {
        qstrInfo = "Grounding_Transformer";//接地变
        break;
    }
    case PDAServiceNS::GROUND_RESISTANCE:
    {
        qstrInfo = "Grounding_Resistor";//接地电阻
        break;
    }
    case PDAServiceNS::GROUND_GRID:
    {
        qstrInfo = "Grounding_Grid";//接地网
        break;
    }
    case PDAServiceNS::COMBINED_FILTER:
    {
        qstrInfo = "Combined_Filter";//结合滤波器
        break;
    }
    case PDAServiceNS::POWER_INSULATOR:
    {
        qstrInfo = "Insulator";//绝缘子
        break;
    }
    case PDAServiceNS::COUPLING_CAPACITOR:
    {
        qstrInfo = "Coupling_Capacitor";//耦合电容器
        break;
    }
    case PDAServiceNS::POWER_CABINET:
    {
        qstrInfo = "Power_Cabinet";//屏柜
        break;
    }
    case PDAServiceNS::OTHER_DEVICE:
    {
        qstrInfo = "Other_Device";//其他
        break;
    }
    case PDAServiceNS::POWER_FUSE:
    {
        qstrInfo = "Power_Fuse";//熔断器
        break;
    }
    case PDAServiceNS::CHANGE_USED:
    {
        qstrInfo = "Spot_Transformer";//所用变
        break;
    }
    case PDAServiceNS::ARC_SUPPRESSION_DEVICE:
    {
        qstrInfo = "Arc_Suppression_Device";//消弧装置
        break;
    }
    case PDAServiceNS::WAVE_BLOCKER:
    {
        qstrInfo = "Blocker";//阻波器
        break;
    }
    case PDAServiceNS::COMBINED_ELECTRIC:
    {
        qstrInfo = "GIS";//组合电器
        break;
    }
    case PDAServiceNS::COMBINED_TRANSFORMER:
    {
        qstrInfo = "Combined_Transformer";//组合互感器
        break;
    }
    default:
    {
        logWarning("unknown patrol type.");
        qstrInfo = "Unknown";
        break;
    }
    }
    return qstrInfo;
}

/*************************************************
功能：检测设备列表中负荷电流输入完成情况
返回值：true：表示负荷电流输入完毕，false：表示负荷电流输入未完成
*************************************************************/
bool PDATask::checkLoadCurrentInputState(const QVector<ItemDevice> vtDevices)
{
    bool bFinished = true;
    for(int i = 0, iSize = vtDevices.size(); i < iSize; ++i)
    {
        if(!(vtDevices[i].b_isBGNTest) && vtDevices[i].uiTestedCount > 0)
        {
            //如果已经被测试过，则进行负荷电流校验
            if(0 == vtDevices[i].dwLoadCurrentSign)
            {
                bFinished = false; //只要出现过一次，测试了但是未输入负荷电流，则不允许上传
            }
        }
    }
    return bFinished;
}

/*************************************************
功能：根据输入的负荷电流信息，设置相同设备编码的所有设备的负荷电流
*************************************************************/
void PDATask::setDevicesLoadCurrent(const LoadCurrentItemInfo& stLoadCurrentInfo)
{
    for(int i = 0, iTestTypeSize = m_pCurrentPatrolType->testTypes.size(); i < iTestTypeSize; ++i)
    {
        for(int j = 0, iDevSize = m_pCurrentPatrolType->testTypes[i].devices.size(); j < iDevSize; ++j)
        {
            if(m_pCurrentPatrolType->testTypes[i].devices[j].b_isBGNTest)
            {
                //虚拟背景设备
                m_pCurrentPatrolType->testTypes[i].devices[j].dLoadCurrent = 0;
                m_pCurrentPatrolType->testTypes[i].devices[j].dwLoadCurrentSign = 1;
                m_pCurrentPatrolType->testTypes[i].devices[j].eLoadCurrentUnit = stLoadCurrentInfo.eUnit;
            }
            else
            {
                //真实设备
                if(stLoadCurrentInfo.strDevNum == m_pCurrentPatrolType->testTypes[i].devices[j].strDevNum)
                {
                    m_pCurrentPatrolType->testTypes[i].devices[j].dLoadCurrent = stLoadCurrentInfo.dLoadCurrent;
                    m_pCurrentPatrolType->testTypes[i].devices[j].dwLoadCurrentSign = stLoadCurrentInfo.dwSign;
                    m_pCurrentPatrolType->testTypes[i].devices[j].eLoadCurrentUnit = stLoadCurrentInfo.eUnit;
                }
            }
        }
    }
    return;
}

/*************************************************
功能：检验当前测点的测试状态
*************************************************************/
void PDATask::checkCurTestPointState(int& iTotalCount, int& iTestedCount)
{
    if(m_pCurrentDevice)
    {
        iTotalCount = m_pCurrentDevice->testPoints.size();
        for(int i = 0; i < iTotalCount; ++i)
        {
            if(m_pCurrentDevice->testPoints[i].isTestFinished())
            {
                ++iTestedCount;
            }
        }
    }
    return;
}

/***********************************************
 * 功能：获取AE录音文件路径
 * 返回值：
 *     AE录音文件路径
 * **********************************************/
QString PDATask::getPdaAERecordPath()
{
    QString qstrRecordFile = "";
    if(m_pCurrentPoint)
    {
        if(SystemSetService::instance()->storageOperEnable())
        {
            QString qstrSavedPath = "";
            testDataFileSavedPath(currentPatrolType()->ePatrolType, currentTestType()->strItemName, qstrSavedPath);
            if(FileOperUtil::createDirectory(qstrSavedPath))
            {
                QString qstrFileName = QDateTime::currentDateTime().toString("yyyyMMdd_hhmmsszzz") + AE_RECORD_FILE_NAME_SURRFIX;
                qstrRecordFile = qstrSavedPath + "/" + qstrFileName;
                logInfo(qstrRecordFile);
            }
            else
            {
                logError("create file path failed.");
            }
        }
        else
        {
            logWarning("unable to operate storage space");
        }
    }
    else
    {
        logError("current test point is NULL.");
    }

    return qstrRecordFile;
}

/*******************************************************
 * 功能：检查设备巡检状态
 * 输入参数：
 * 返回值：
 *      true：所有设备巡检完毕，false：所有设备中存在未检查设备
 * ********************************************************/
bool PDATask::checkDeviceTestFinished(const QVector<ItemDevice> &qvtDevices)
{
    bool bRet = true;
    for(int i = 0, iSize = qvtDevices.size(); i < iSize; ++i)
    {
        if(qvtDevices[i].uiTotalCount != qvtDevices[i].uiTestedCount)
        {
            bRet = false;
            break;
        }
    }

    return bRet;
}

/*******************************************************
 * 功能：获取当前任务的文件夹路径
 * 返回值：
 *      QString：当前任务所在的文件夹
 * *********************************************************/
QString PDATask::getCurTaskFolder()
{
    QString qstrFileFolder = FileOperUtil::getFileParentFolder(m_taskInfo.strFilePath);
    return qstrFileFolder;
}

/*******************************************************
 * 功能：获取当前任务临时数据ini文件的文件夹路径
 * 返回值：
 *      QString：当前任务临时数据ini文件的文件夹路径
 * *********************************************************/
QString PDATask::getCurTaskIniFolder()
{
    return (getCurTaskFolder() + "/" + PDA_TASK_TEMP_INI_FOLDER);
}

/******************************************************
 * 功能：删除临时的ini文件
 * *********************************************************/
void PDATask::delTempIniFolder()
{
    FileOperUtil::deleteDir(getCurTaskIniFolder());
    logInfo(QString("delete temp file: %1").arg(getCurTaskIniFolder()));
    return;
}

/************************************************************
 * 功能：保存临时测试数据信息
 * 输入参数：
 *      stTestData：测试数据
 * **************************************************************/
void PDATask::saveTestDataTmpInfo(const ItemTestData &stTestData)
{
    TestDataTmpInfo stTestDataTmpInfo;
    if(m_pCurrentPoint)
    {
        stTestDataTmpInfo.qi32PointType = m_pCurrentPoint->eType;
        stTestDataTmpInfo.qi32PointPos = m_pCurrentPoint->eTestPosition;
        stTestDataTmpInfo.qstrPointNumber = m_pCurrentPoint->strItemNumber;
    }

    stTestDataTmpInfo.bIsBgn = stTestData.bIsBgn;
    stTestDataTmpInfo.bTested = stTestData.bTested;
    stTestDataTmpInfo.qstrNumber = stTestData.strNumber;
    stTestDataTmpInfo.qi32Unit = stTestData.eDataUnit;
    stTestDataTmpInfo.qui16Index = stTestData.usIndex;
    stTestDataTmpInfo.qi32DataType = stTestData.eDataType;
    stTestDataTmpInfo.qstrFileName = stTestData.strFileName;
    stTestDataTmpInfo.qstrFilePath = stTestData.strFilePath;
    stTestDataTmpInfo.qi32BandWidth = stTestData.eBandWidth;
    stTestDataTmpInfo.qstrAttachPath = stTestData.strAttachPath;
    stTestDataTmpInfo.qstrRemotePath = stTestData.strRemotePath;
    stTestDataTmpInfo.qstrRemoteAttachPath = stTestData.strRemoteAttachPath;

    QString qstrTaskTmpIniFolder = getCurTaskIniFolder();
    FileOperUtil::createDirectory(qstrTaskTmpIniFolder);

    QString qstrTestDataIniFile = qstrTaskTmpIniFolder + stTestDataTmpInfo.qstrNumber + PDA_TASK_TEMP_INI_SUFFIX;
    IniConfig::writeTestDataTmpInfo(qstrTestDataIniFile, stTestDataTmpInfo);

    return;
}

/********************************************
 * 功能：设置当前巡检的位置
 * 输入参数：
 *      ePosition：巡检的位置
 * ******************************************/
void PDATask::setCurPatrolPosition(PDAServiceNS::PatrolPosition ePosition)
{
    if(ePosition != m_eCurPatrolPos)
    {
        m_eCurPatrolPos = ePosition;
    }

    return;
}

/********************************************
 * 功能：获取当前巡检的位置
 * 返回值：
 *      PDAServiceNS::PatrolPosition：巡检的位置
 * ******************************************/
PDAServiceNS::PatrolPosition PDATask::getCurPatrolPosition()
{
    return m_eCurPatrolPos;
}

/********************************************
 * 功能：获取当前检测设备下各测点的巡检的位置集合
 * 返回值：
 *      QVector<PDAServiceNS::PatrolPosition>：巡检的位置集合
 * ******************************************/
QVector<PDAServiceNS::PatrolPosition> PDATask::getCurTestDevPatrolPositions()
{
    QVector<PDAServiceNS::PatrolPosition> qvtPatrolPositions;
    qvtPatrolPositions.clear();

    if(m_pCurrentDevice)
    {
        for(int i = 0, iSize = m_pCurrentDevice->testPoints.size(); i < iSize; ++i)
        {
            if(!qvtPatrolPositions.contains(m_pCurrentDevice->testPoints[i].ePatrolPosition))
            {
                qvtPatrolPositions.append(m_pCurrentDevice->testPoints[i].ePatrolPosition);
            }
        }
    }

    if(0 < qvtPatrolPositions.size())
    {
        qSort(qvtPatrolPositions.begin()
              , qvtPatrolPositions.end()
              , [](PDAServiceNS::PatrolPosition ePos1, PDAServiceNS::PatrolPosition ePos2)
        {
            return (ePos1 <= ePos2);
        });
    }

    return qvtPatrolPositions;
}

/************************************
 * 功能：设置巡检跳转模式
 * 输入参数：
 *      eMode：巡检跳转模式
 * **********************************/
void PDATask::setPatrolSwitchMode(SystemSet::PatrolSwitchMode eMode)
{
    if(eMode != m_ePatrolMode)
    {
        m_ePatrolMode = eMode;
    }

    return;
}

