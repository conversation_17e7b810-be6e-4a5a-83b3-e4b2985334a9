/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* PulseClusterView.h
*
* 初始版本：1.0
* 作者：陈康
* 创建日期：2017年4月13日
*       新版本重构
* 摘要：接入G100的脉冲分析界面

* 当前版本：1.0
*/

/*
 * 该界面需求
 * 根据聚类的数据，显示对应的UT、UF、PRPD图谱
   提供两种操作
        （1）放大返回
        （2）选择分析的点，UT、UF图谱跟着变换
*/

#ifndef PULSEDETAILVIEW_H
#define PULSEDETAILVIEW_H

#include "chartview/ChartView.h"
#include "ca/CA.h"
#include "FastCollection/FastWaveUF.h"
#include "FastCollection/FastWaveUT.h"
#include "pushButton/SliderPushButton.h"
#include "datadefine.h"

class PulseDetailView : public ChartView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数: strTitle: 标题
            lData: 脉冲数据
            sampRateOpition: 采样率
            parent:父窗口
    *************************************************************/
    explicit PulseDetailView(const QString& strTitle,
                             const QList<CA::PulseData> &lData,
                             CA::SampleRate sampRateOpition,
                             double dPulseWidth,
                             qint32 iRangeMax,
                             QWidget *parent = 0);


    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~PulseDetailView();

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

protected:
    /*************************************************
    功能： 显示事件
    输入参数:
       event -- 事件
    *************************************************************/
    void showEvent(QShowEvent* event);

private:
    /*************************************************
    功能： 初始化成员变量
    *************************************************************/
    void initData();

    /*************************************************
    函数名： createUI
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能：初始化用户界面布局和显示
    *************************************************************/
    void createUI(QWidget *parent);

    /*************************************************
    函数名： createCharts
    输入参数: parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 创建图谱显示区域
    *************************************************************/
    ChartWidget * createCharts(QWidget *parent);

    /*************************************************
    功能： 创建UT图谱
    *************************************************************/
    FastWaveUT *createUTChart(QWidget *parent);

    /*************************************************
    功能： 创建UF图谱
    *************************************************************/
    FastWaveUF *createUFChart(QWidget *parent);


    /*************************************************
    功能： 设置界面的显示数据
    *************************************************************/
    void setUIData();

    /*************************************************
    功能： 设置按钮栏数据
    *************************************************************/
    void setButtonBarData(void);

    /*************************************************
    功能： 设置图谱数据
    *************************************************************/
    void setChartData();

    /*************************************************
    函数名： setUTData
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 处理选中u-t分析数据
    *************************************************************/
    void setUTChartData(const QList<float> &dataUT, CA::SampleRate eSampleRate);

    /*************************************************
    函数名： setUFData
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 处理选中u-f分析数据
    *************************************************************/
    void setUFChartData(const QList<float> &dataUF, CA::SampleRate eSampleRate);

    /*************************************************
    函数名： restoreToOriginalRatio
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 原始比例
    *************************************************************/
    void restoreToOriginalRatio();

    void setPulseCntLabelText(QLabel *pLabel);

    void selectLastPulse();

    void selectNextPulse();

    void changePulseIndex(quint32 iIndex);

    void setUTChartYTitle(float fRangeMax);

    void setUFChartYTitle(float fRangeMax);

    void setUTChartYScale(int iRangeMax);

    void setDisplayedCnt(UINT32 &uixScaleMax);

    void setUFYScale(int iRangeMax);

private:
    QList< CA::PulseData > m_lRawData;//供分析的原始脉冲数据
    FastWaveUT  *m_pFastWaveUT;//UT图谱显示控件
    FastWaveUF  *m_pFastWaveUF;//UF图谱显示控件
    CA::SampleRate m_eSampleRate;//当前采样率
    QLabel *m_pPulseCntLabel;
    qint32 m_iCurrentPulseIndex;
    double m_dPulseWidth;
    qint32 m_iRangeMax;
    QLabel *m_pUTYTitle;
    QLabel *m_pUFYTitle;

    enum
    {
        TITLE_SIZE = 15,   // u-t,pdss图谱标题的字号
        CHART_HEIGHT = 360,//图谱高度
        BELOW_CHART_HEIGHT = 250, // 下方图谱高度
        STATUS_LABEL_HEIGHT = 20, // 状态显示标签高度

        SAMPLE_LENGTH = 2000 //采样长度
    };
};

#endif // PULSEDETAILVIEW_H
