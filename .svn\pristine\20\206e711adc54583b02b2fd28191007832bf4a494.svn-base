/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* HFCTConfig.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2017年04月17日
* 摘要：HFCT模块的配置定义
* 当前版本：1.0
*/
#ifndef HFCTCONFIG_H
#define HFCTCONFIG_H

#include "config/ConfigManager.h"
#include "pm.h"
#include "module_global.h"
#include "Module.h"

namespace PMConfig
{
    //配置信息定义
    typedef enum _ConfigInfo
    {        //键值
        KEY_UHF_CONDITIONER_ADDR = 0, //uhf调理器地址
        KEY_HFCT_CONDITIONER_ADDR,//hfct调理器地址
        KEY_SYNCHRONIZER_CONDITIONER_ADDR,//synchronizer调理器地址
        KEY_UHF_CONDITIONER_NAME,//uhf调理器名称
        KEY_HFCT_CONDITIONER_NAME,//uhf调理器名称
        KEY_SYNCHRONIZER_CONDITIONER_NAME,//uhf调理器名称
    }ConfigInfo;

    //HFCT组的键值配置
    static Config::KeyInfo KEYS_HFCT[] =
    {
        { KEY_UHF_CONDITIONER_ADDR, "UHFConditionerAddr", Config::TEXT, QString(""), 0, 0 },
        { KEY_HFCT_CONDITIONER_ADDR, "HFCTConditionerAddr", Config::TEXT, QString(""), 0, 0 },
        { KEY_SYNCHRONIZER_CONDITIONER_ADDR, "SynchronizerAddr", Config::TEXT, QString(""), 0, 0 },
        { KEY_UHF_CONDITIONER_NAME, "UHFConditionerName", Config::TEXT, QString(""), 0, 0 },
        { KEY_HFCT_CONDITIONER_NAME, "HFCTConditionerName", Config::TEXT, QString(""), 0, 0 },
        { KEY_SYNCHRONIZER_CONDITIONER_NAME, "SynchronizerName", Config::TEXT, QString(""), 0, 0 },
    };



    //HFCT组的组配置
    static Config::GroupInfo GROUPS_HFCT[] =
    {
        { Module::GROUP_APP, Config::NORMAL, "HFCTAmplitude", KEYS_AMPLITUDE, sizeof( KEYS_AMPLITUDE )/sizeof(Config::KeyInfo), NULL, 0 },
        { Module::GROUP_APP, Config::NORMAL, "HFCTPeriod", KEYS_PERIOD, sizeof( KEYS_PERIOD )/sizeof(Config::KeyInfo), NULL, 0 },
        { Module::GROUP_APP, Config::NORMAL, "HFCTPrps", KEYS_PRPS, sizeof( KEYS_PRPS )/sizeof(Config::KeyInfo), NULL, 0 },
    };

    static Config::GroupInfo CONFIG =
    {
        Module::GROUP_HFCT, Config::NORMAL, "HFCT", KEYS_HFCT, sizeof( KEYS_HFCT )/sizeof(Config::KeyInfo), GROUPS_HFCT, sizeof(GROUPS_HFCT)/sizeof(Config::GroupInfo)
    };
}
#endif // HFCTCONFIG_H
