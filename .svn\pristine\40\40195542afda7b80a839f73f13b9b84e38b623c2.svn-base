/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* CmdPushButton.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月7日
* 修改日期：2016年11月15日
* 作者：邵震宇
*       重构
* 摘要：Push型控制按钮，点击执行控制指令
* 当前版本：1.0
*/
#ifndef CMDPUSHBUTTON_H
#define CMDPUSHBUTTON_H

#include <QMouseEvent>
#include "controlButton/CmdButton.h"
#include "PushButton.h"

class WIDGET_EXPORT CmdPushButton : public CmdButton
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        mode -- 显示模式
        parent -- 父窗体
    *************************************************************/
    explicit CmdPushButton( PushButton::Mode mode, QWidget* parent = 0 );

    /*************************************************
    功能： 设置标题
    输入参数:
        strTitle -- 标题
    *************************************************************/
    virtual void setTitle(const QString& strTitle);
};

#endif // CMDCONTROLBUTTON_H

