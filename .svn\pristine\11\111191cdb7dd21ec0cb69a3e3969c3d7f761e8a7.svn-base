﻿#ifndef DRIVERDATADEFINE_H
#define DRIVERDATADEFINE_H

#define printferr(fmt, args...)   do{ printf("****error in %s "fmt, __FUNCTION__, ##args); }while(0)

#define	  HARDEARENEWV2			//第二版硬件 Zigbee、GPS直连 RFID改成I2C

#define HC_SUCCESS          0
#define HC_FAILURE          -1
#define HC_TIMEOUT          -2
#define PARAMETER_ERROR     -100

typedef signed char         INT8;
typedef signed short        INT16;
typedef signed int          INT32;
typedef signed long long    INT64;
typedef unsigned char       UINT8;
typedef unsigned short      UINT16;
typedef unsigned int        UINT32;
typedef unsigned long long  UINT64;

#endif // DRIVERDATADEFINE_H
