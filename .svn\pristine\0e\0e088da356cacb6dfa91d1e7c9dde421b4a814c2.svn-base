﻿#ifndef _WIFI_INCLUDE_
#define _WIFI_INCLUDE_

#include "DriverDataDefine.h"

#ifdef __cplusplus    //__cplusplus是cpp中自定义的一个宏
extern "C" {          //告诉编译器，这部分代码按C语言的格式进行编译，而不是C++的
#endif

#define WLAN_DEBUG
#define wlan_err(fmt,args...) \
        do {printf("[WLAN ERR]:%s %d "fmt,__FUNCTION__,__LINE__,##args);}while(0)
#ifdef WLAN_DEBUG
	#define wlan_dbg(fmt...) \
	        do { printf("[WLAN DBG]:"fmt);}while(0)
#else
	#define wlan_dbg(fmt...)
#endif
				
#define WIFI_DEV_NAME "wlan0"
#define MAX_CMD_LEN  (1024)
#define MAX_SSID_NUM  (100)

typedef enum _wifimode
{
	WIFI_CLIENT_MODE = 0,
	WIFI_AP_MODE,
}WifiMode;
typedef enum _wificiphermode
{
	WIFI_CIPHER_WPA_PSK = 0, //家庭级加密模式
	WIFI_CIPHER_NONE,//无加密模式
	WIFI_CIPHER_WPA_EAP, //企业级加密模式
}WifiCipherMode;
typedef enum _wifiSigMode
{
	WIFI_2_4G = 0,
	WIFI_5_0G,
}WifiSigMode;
typedef struct _ssidlist
{
	UINT32 uiSsidNum;
	INT8 *pacSsids[MAX_SSID_NUM];
	WifiCipherMode aeMode[MAX_SSID_NUM];
	WifiSigMode aeSigMode[MAX_SSID_NUM];
	INT32 iSigStrength [MAX_SSID_NUM];
}SsidList;

/************************************************
 * 函数名    :wifi_init
 * 输入参数  ：
 *            eMode -- wifi work mode
 *                     
 * 输出参数  ：NULL
 * 返回值   ：
 *      0 -- 成功
 *      -1 -- 失败
 * 功能     ：wifi 初始化
 ************************************************/
INT32 wifi_init(WifiMode eMode);
/************************************************
 * 函数名    :wifi_exit
 * 输入参数  ：
 *            eMode -- wifi work mode
 *                     
 * 输出参数  ：NULL
 * 返回值   ：
 *      0 -- 成功
 *      -1 -- 失败
 * 功能     ：wifi 退出
 * 说明     ：
 ************************************************/
INT32 wifi_exit(WifiMode eMode);
/************************************************
 * 函数名    :wifi_ssid_scan
 * 输入参数  ：
 *            ucMaxSsidNumAllow -- max allowed saving ssid number
 *                     
 * 输出参数  ：NULL
 * 返回值   ：
 *            SsidList -- pointer of ssid string buffer
 *            NULL     -- no ssid scanned.
 * 功能     ：wifi 扫描可用的AP信息。
 * 说明     ：函数返回的SsidList指针如果不为NULL，调用者在获取到
 *            SsidList缓冲区内的数据后，必须调用 @ssid_list_free
 *            函数释放内存，防止发生内存泄露。
 ************************************************/
SsidList * wifi_ssid_scan(UINT8 ucMaxSsidNumAllow);
/************************************************
 * 函数名    :ssid_list_free
 * 输入参数  ：
 *            pstData -- pointer of ssid string buffer
 *                     
 * 输出参数  ：NULL
 * 返回值   ：NULL

 * 功能     ：wifi 扫描可用的AP信息。
 * 说明     ：释放@pstData指向的内存。
 ************************************************/
void ssid_list_free(SsidList *pstData);
/************************************************
 * 函数名    :wifi_connect_ssid
 * 输入参数  ：
 *            pcSsid -- ssid string pointer
 *            pcPsk  -- psk of @ssid 
 *                      if pcPsk = NULL,connect the ssid with no psk code.
 * 输出参数  ：NULL
 * 返回值   ：0 -- succeed
 *            -1 -- failed
 * 功能     ：建立wifi连接,支持wap-psk/wap2-psk加密连接和无加密连接
 *            函数返回0 表示station与Ap连接已成功建立，否则为失败
 ************************************************/
INT32 wifi_connect_ssid(const INT8 *pcSsid,const INT8 *pcPsk);
/************************************************
 * 函数名    : stop_wait_wifi_connect
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值    ：NULL
 * 功能      ：当线程A调用wifi_connect_ssid接口阻塞等待
 *           ：wifi连接状态时，线程B可以通过调用stop_wait_wifi_connect
 *           ：接口结束线程A的阻塞状态。
 ************************************************/
void stop_wifi_connect(void);
/************************************************
 * 函数名    :wifi_disconnect
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：0 -- succeed
 *            -1 -- failed
 * 功能     ：断开wifi连接
 ************************************************/
INT32 wifi_disconnect(void);
/************************************************
 * 函数名    :wifi_is_connected
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：0 -- wifi is not connected 
 *            1 -- wifi is connected
 *            -1-- error
 * 功能     ：检查wifi连接状态
 ************************************************/
INT32 wifi_is_connected(void);
const INT8 * wifi_version(void);
#ifdef __cplusplus
}


#endif
#endif //_WIFI_INCLUDE_


