/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* tevpulseview.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年04月10日
* 摘要：TEVPulseView模块接口定义

* 当前版本：1.0
*/

#ifndef TEVPULSEPDAVIEW_H
#define TEVPULSEPDAVIEW_H

#include "tev/tevpulseviewbase.h"
#include "tev/tevdefine.h"
#include "tev/TevPulseService.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "tevpulsechart.h"
#include "pda/pda.h"
#include "PDAUi/PDAUiBean/addtestdatadialog.h"
#include "View.h"

class TEVPulsePDAView : public TEVPulseViewBase
{
    Q_OBJECT
public:
    /*************************************************
    函数名： TEVPulsePDAView(const QString &strTitle, const ItemTestData &stTestDataInfo, QWidget *parent = 0)
    输入参数： strTitle：标题
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit TEVPulsePDAView(const QString &strTitle, const ItemTestData &stTestDataInfo, QWidget *parent = 0);

    /*************************************************
    函数名： ~TEVPulseView()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~TEVPulsePDAView();

    /************************************************
     * 函数名   : setTestPointInfo
     * 输入参数 : stTestPointInfo---巡检测点相关信息
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置显示测点信息
     ************************************************/
    void setTestPointInfo(const View::PatrolTestPointInfo &stTestPointInfo);

protected:
    /*************************************************
    函数名： onSKeyPressed()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应S键事件
    *************************************************************/
    void onSKeyPressed();

    /*************************************************
    功能： 显示事件
    输入参数:
        event -- 事件
    *************************************************************/
    void showEvent(QShowEvent *event);

    /*************************************************
     * 功能：诊断数据
     * 输入参数：
     *      data：待诊断的数据
     *      bSave：是否为保存操作的逻辑，缺省为false
     * ***********************************************/
    virtual void diagDataInfo(const TEV::PulseData &data, bool bSave = false);

protected slots:
    /*************************************************
    函数名： onButtonValueChanged(int id, int iValue)
    输入参数： id：按钮ID
              iValue：按钮值
    输出参数： NULL
    返回值： NULL
    功能： 响应按钮值变化事件
    *************************************************************/
    void onButtonValueChanged(int id, int iValue);

    /*************************************************
    函数名： onCommandButtonPressed(int id)
    输入参数： id：按钮ID
    输出参数： NULL
    返回值： NULL
    功能： 响应命令按钮按下事件
    *************************************************************/
    void onCommandButtonPressed(int id);

private slots:
    /*************************************************
    函数名： onDataRead(TEV::PulseData data)
    输入参数： data：幅值数据
    输出参数： NULL
    返回值： NULL
    功能： 响应读取的数据
    *************************************************************/
    void onDataRead(TEV::PulseData data,MultiServiceNS::USERID userId);

    /************************************************
     * 函数名   : onRecoverSampleView
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :槽函数，起动采样
     ************************************************/
    void onRecoverSampleView();

    /************************************************
     * 函数名   : onAddTestData
     * 输入参数 :  struct_AddingTestData&
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :槽函数，将新增测试项界面的信息写入pdaTask中
     ************************************************/
    void onAddTestData(struct_AddingTestData& stAddingTestData);

private:
    /*************************************************
    函数名： initDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 初始化数据
    *************************************************************/
    void initDatas();

    /*************************************************
    函数名： resetAlarmScope()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 重置报警值范围
    *************************************************************/
    void resetAlarmScope();

    /*************************************************
    函数名： setButtonDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置按钮数据
    *************************************************************/
    void setButtonDatas();

    /*************************************************
    函数名： setChartDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置图谱数据
    *************************************************************/
    void setChartDatas();

    /*************************************************
    函数名： setWorksets()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置工作参数
    *************************************************************/
    void setWorksets();

    /*************************************************
    函数名： saveConfig()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 保存配置信息
    *************************************************************/
    void saveConfig();

    /*************************************************
    函数名： restoreDefault()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 恢复默认
    *************************************************************/
    void restoreDefault();

    /************************************************
     * 函数名   : createChart
     * 输入参数 : parent---父控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建图谱
     ************************************************/
    ChartWidget* createChart(QWidget *parent);

    /*************************************************
    输入参数：
        stationName：站名
        deviceName：设备名
    输出参数： NULL
    返回值： NULL
    功能：保存数据文件
    *************************************************************/
    bool savePDAData(const QString &stationName, const QString& deviceName);

    /*************************************************
    函数名： saveTestData
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 测试数据传给pdaservice保存
    *************************************************************/
    void saveTestData();

    /*************************************************
    功能： 弹出新增测试项窗口
    *************************************************************/
    void addTestData();

    /************************************************
     * 函数名   : readTestedData
     * 输入参数 : qsFile---数据文件存放路径
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :读取数据文件里的测试数据
     ************************************************/
    void readTestedData(const QString &qsFile);

    /************************************************
     * 函数名   : recoverSample
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :起动采样
     ************************************************/
    void recoverSample();

private:

    //站名label
    QLabel *m_pStationLabel;
    //设备名label
    QLabel *m_pDeviceLabel;
    //测点名label
    QLabel *m_pTestPointLabel;

    ConfigInstance *m_pConfig;
    TEVPulseChart *m_pChart;

    UINT8 m_ucYellowAlert;
    UINT8 m_ucRedAlert;
    UINT8 m_ucPulseLen;
    Module::SampleMode m_eSampleMode;

    QString m_qsTestDataFilePath;//测试数据文件路径
    ItemTestData m_stTestData;      //存放测试项的一些信息

    QString m_qsStation;
    QString m_qsDevice;
    View::PatrolTestPointInfo m_stTestPointInfo;

    enum {
        START_SAMPLE_TIME_INTERVAL_2000MS = 2000
    };

};

#endif // TEVPULSEPDAVIEW_H
