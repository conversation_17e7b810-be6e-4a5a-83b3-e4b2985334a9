/*
* Copyright (c) 2016.012，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：WifiFrame.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年12月08日
* 摘要：该文件主要是定义了获取wifi热点列表，以及返回加入wifi超时结果
*
*/

#ifndef WIFISERVICE_H
#define WIFISERVICE_H

#include <QObject>
#include <QThread>
#include <QVector>
#include "module_global.h"
#include "Module.h"
typedef struct _WifiInfo
{
    QString m_strWifiName;    // 热点名
    bool m_bIsCipher;         // false 加密
    int m_iSigStrength;
}WifiInfo;

typedef QVector<WifiInfo> QVWifiInfoData;

class MODULESHARED_EXPORT WifiService : public QObject
{
    Q_OBJECT
public:
    /************************************************
     * 输入参数 : parent -- 父控件指针
     * 功能    : 构造函数
     ************************************************/
    explicit WifiService(QObject *parent = 0);

    /************************************************
     * 功能    : 开启服务
     ************************************************/
    void start( void );

    /************************************************
     * 功能    : 停止服务
     ************************************************/
    void stop( void );

    /************************************************
     * 功能    : 扫描热点
     ************************************************/
    void scanWifiList( void );

    /************************************************
     * 功能    : 连接热点
     ************************************************/
    void connectWifi(QString qstrName, QString qstrPwd);

    /************************************************
     * 功能    : wifi连接是否存在
     ************************************************/
    static bool isConnected( void );

	/************************************************
     * 功能    : 转换wifi中文名称 utf8-gbk
     ************************************************/
    QString conWifiNameFromUft8ToGbk(char* pUtf8Encode);

	/************************************************
     * 功能    : 中文热点名解析
     ************************************************/
    void wifiNameParse(QString strWifiName,     char* pUtf8Encode, int len);
	
	/************************************************
     * 功能    : 字符转换
     ************************************************/
    char charToValue(QString strWifiName);
	
	/************************************************
     * 功能    : 析构函数
     ************************************************/
    ~WifiService();

signals:
    /************************************************
     * 参数：搜索结果
     * 功能    :发送搜索结果
     ************************************************/
    void sigWifiInfo(QVWifiInfoData vWifiInfo);

    /************************************************
     * 参数：wifiName -- 热点名
     *      passWD -- 密码
     *      bResult -- true 加入成功
     *                 false 加入失败
     * 功能    :发射加入结果
     ************************************************/
    void sigConnectResult( QString wifiName, QString passWD, bool bResult );

    /************************************************
     * 功能    :释放开始wifi扫描信号
     ************************************************/
    void sigStart();

    /************************************************
     * 功能    :释放停止wifi扫描信号
     ************************************************/
    void sigStop();

    /************************************************
     * 功能    :释放搜索热点信号
     ************************************************/
    void sigWifiScan();

    /************************************************
     * 功能    :释放连接热点信号
     ************************************************/
    void sigConnectWifi(QString qstrName, QString qstrPwd);

private slots:
    /************************************************
     * 功能    : 搜索热点信号
     ************************************************/
    void onScanWifiSsidList( void );

    /************************************************
     * 参数：wifiName -- 热点名
     *      passWD  --密码
     * 功能    : 连接指定热点名
     ************************************************/
    void onConnectWifi(QString wifiName, QString passWD);

    /************************************************
     * 功能    :启动wifi client服务
     ************************************************/
    void onStart();

    /************************************************
     * 功能    :停止 wifi client服务
     ************************************************/
    void onStop();
	
private:
    QThread *m_pthread;
};

#endif // WIFISERVICE_H
