/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* PushButton.cpp
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月7日
* 摘要：控制按钮基类实现

* 当前版本：1.0
*/
#include <QVBoxLayout>
#include <QApplication>
#include <QDebug>
#include "PushButtonContentLabel.h"
#include "PushButton.h"
#include "Widget.h"
#include "label/newtooltip.h"
#include "appfontmanager/appfontmanager.h"

//一些宏定义
//#define CONTROLBUTTON_TITLE_STYLE "QLabel{color:rgb(255,255,255); border:none; font-family:msyh}" //标题栏文字样式
#define CONTROLBUTTON_TITLE_STYLE "QLabel{color:rgb(255,255,255); border:none;}" //标题栏文字样式
#define CONTROLBUTTON_BACKGROUND_PNG ":/images/controlButton/background.png"
#define CONTROLBUTTON_ACTIVE_RATIO 1.1//激活放大倍数
#define CONTROLBUTTON_DEFAULT_WIDTH 116//默认宽度
#define CONTROLBUTTON_DEFAULT_HEIGHT 116//默认高度

//文字在图表下方模式
#define CONTROLBUTTON_ICON_HEIGHT_RATIO 0.7 //图标高度比例
#define CONTROLBUTTON_ACTIVE_STYLE "QLabel{border-width:1px; border-style:solid; border-color:rgb(255, 170, 0); border-radius:1px; background:rgb(255, 170, 0)}"//激活样式
#define CONTROLBUTTON_INACTIVE_STYLE "QLabel{border:none}"

/*************************************************
功能： 构造函数
输入参数:
    mode -- 显示模式
    parent -- 父窗体
*************************************************************/
PushButton::PushButton( PushButton::Mode mode, QWidget* parent )
    :CentralButton( parent ), m_mode( mode ), m_bShowToolTipEnable(false)
{
    setAttribute(Qt::WA_TranslucentBackground, true);//半透明
    setFocusPolicy(Qt::NoFocus);

    //初始化尺寸
    m_usWidth = CONTROLBUTTON_DEFAULT_WIDTH;
    m_usHeight = CONTROLBUTTON_DEFAULT_HEIGHT;

    //内容区
    m_plabelContent = new PushButtonContentLabel( mode );

    //标题区
    m_plabelTitle = new QLabel();
    m_plabelTitle->setStyleSheet( CONTROLBUTTON_TITLE_STYLE );

    QVBoxLayout* pmainLayout = new QVBoxLayout;
    pmainLayout->setSpacing( 0 );
    pmainLayout->setMargin( 0 );
    pmainLayout->addWidget( m_plabelContent );
    pmainLayout->addWidget( m_plabelTitle );

    setLayout( pmainLayout );

    //设置显示模式
    setMode( m_mode );
}

/*************************************************
功能： 设置标题
输入参数:
    strTitle -- 标题
*************************************************************/
void PushButton::setTitle( const QString& strTitle )
{
    CentralButton::setTitle( strTitle );
    m_plabelContent->setTitle(strTitle);
}

/*************************************************
功能： 设置图标路径
输入参数:
    strIconPath -- 图标路径
*************************************************************/
void PushButton::setIcon( const QString& strIconPath )
{
    m_strIconPath = strIconPath;
    m_plabelContent->setIcon( strIconPath );
}

/*************************************************
功能： 设置显示模式
输入参数:
    mode -- 模式
*************************************************************/
void PushButton::setMode( PushButton::Mode mode )
{
    m_mode = mode;

    switch ( m_mode )
    {
        case PushButton::TEXT_ONLY:
        {
            m_plabelTitle->hide();
            m_plabelContent->setIcon( CONTROLBUTTON_BACKGROUND_PNG );
        }
            break;
        case PushButton::ICON_ONLY:
        {
            m_plabelTitle->hide();
            m_plabelContent->setIcon( m_strIconPath );
        }
            break;
        case PushButton::TEXT_UNDER_ICON:
        {
            m_plabelTitle->show();
            m_plabelContent->setIcon( m_strIconPath );
        }
            break;
        default:
            break;
    }
    m_plabelContent->setMode( mode );
}

/*************************************************
功能： 显示模式
返回值： 当前模式
*************************************************************/
PushButton::Mode PushButton::mode() const
{
    return m_mode;
}

/*************************************************
功能： 设置数值
输入参数:
    strContent -- 值
*************************************************************/
void PushButton::setContent( const QString& strContent )
{
    CentralButton::setContent(strContent);
    m_plabelContent->setText(strContent);
}

/*************************************************
功能： 设置显示文本字体
输入参数:
    stFont -- 字体
*************************************************************/
void PushButton::setFont(const QFont& stFont)
{
    m_plabelContent->setFont(stFont);
    return;
}

/*************************************************
功能： 返回显示文本字体
返回值:
    QFont -- 字体
*************************************************************/
QFont PushButton::font() const
{
    return m_plabelContent->font();
}

/*************************************************
功能： 响应控制按钮激活状态
输入参数:
    bActive: 激活状态
*************************************************************/
void PushButton::onActive(bool bActive)
{
    if(bActive)
    {
        m_plabelContent->setStyleSheet(CONTROLBUTTON_ACTIVE_STYLE);
        //applyButtonSize(m_usWidth * CONTROLBUTTON_ACTIVE_RATIO, m_usHeight * CONTROLBUTTON_ACTIVE_RATIO);     //英文模式下会影响单词的显示
        applyButtonSize(m_usWidth, m_usHeight);

        if (m_bShowToolTipEnable && m_plabelContent->isIncompleteDisplay())
        {
            QFont font = AppFontManager::instance()->getAppCurFont();
            font.setPixelSize(25);
            NewToolTip::setFont(font);
            NewToolTip::showTextAbove(mapToGlobal(QPoint(0, 0)), toolTip(), this);
        }
    }
    else
    {
        m_plabelContent->setStyleSheet(CONTROLBUTTON_INACTIVE_STYLE);
        applyButtonSize(m_usWidth, m_usHeight);

        if (m_bShowToolTipEnable && m_plabelContent->isIncompleteDisplay())
        {
            NewToolTip::hideText();
        }
    }

    return;
}

/*************************************************
功能： 设置按钮大小
输入参数: usWidth -- 按键宽度
         usHeight -- 按键高度
*************************************************************/
void PushButton::setButtonSize(quint16 usWidth, quint16 usHeight)
{
    m_usWidth = usWidth;
    m_usHeight = usHeight;
    applyButtonSize(usWidth, usHeight);
    return;
}

/*************************************************
功能： 设置显示ToolTip使能
输入参数:
    bEnable -- 使能
*************************************************************/
void PushButton::setToolTipEnable(bool bEnable)
{
    m_bShowToolTipEnable = bEnable;
}

/*************************************************
功能： 应用按钮大小
输入参数: usWidth -- 按键宽度
         usHeight -- 按键高度
*************************************************************/
void PushButton::applyButtonSize( quint16 usWidth, quint16 usHeight )
{
    switch( m_mode )
    {
        case PushButton::TEXT_ONLY:
        {
            m_plabelContent->setSize( QSize( usWidth, usHeight ) );
            m_plabelContent->setIcon( CONTROLBUTTON_BACKGROUND_PNG );
        }
            break;
        case PushButton::ICON_ONLY:
        {
            m_plabelContent->setSize( QSize( usWidth, usHeight ) );
            m_plabelContent->setIcon( m_strIconPath );
        }
            break;
        case PushButton::TEXT_UNDER_ICON:
        {
            quint16 usIconHeight = usHeight*CONTROLBUTTON_ICON_HEIGHT_RATIO;
            m_plabelContent->setSize( QSize( usWidth, usIconHeight ) );
            m_plabelContent->setIcon( m_strIconPath );

            m_plabelTitle->resize( usWidth, usHeight - usIconHeight );
        }
            break;
        default:
            break;
    }
    return;
}
