#include <QList>
#include <QDateTime>
#include <QEventLoop>
#include <QByteArray>
#include "localservice.h"
#include "protocol/linklayerprotocol.h"
#include "business/localbusiness.h"

#define LOCALSERVICE_PRINT

inline void outputMsg(QString msg)
{
#ifdef LOCALSERVICE_PRINT
    qDebug() << QDateTime::currentDateTime().toString() << " " << msg;
#endif
}

/****************************
功能： 本地通讯模块单例
*****************************/
LocalService* LocalService::instance()
{
    static LocalService service;

    return &service;
}

/****************************
功能： 构造函数
输入参数:
    pParent -- 父控件
*****************************/
LocalService::LocalService(QObject *pParent):
    QObject(pParent), m_isConnected(false)
{
    // 创建通讯组件与服务组件
    m_pClient = new TcpClient( "127.0.0.1",12345,this );
    m_pProtocol = new LinkLayerProtocol(m_pClient, this);
    m_pLocalBusiness = new LocalBusiness;

    qRegisterMetaType<TcpClient::TcpLinkState>("TcpClient::TcpLinkState");
    qRegisterMetaType<Protocol::ProgressState>("Protocol::ProgressState");
    qRegisterMetaType<Protocol::ProtocolParam>("Protocol::ProtocolParam");
    qRegisterMetaType<QVector<UINT8> >("QVector<UINT8>");

    // 通讯组件可读信号->链路层协议解析->本地业务处理->通讯组件发送应答
        // 通讯组件可读数据信号->链路层协议解析
    connect(m_pClient, SIGNAL(sigReadyRead(qint64)),
            m_pProtocol, SLOT(readyRead(qint64)));

    // 链路层协议解析数据->本地业务处理
    connect(m_pProtocol, SIGNAL(sigDataRead(QByteArray, Protocol::ProtocolParam)),
            m_pLocalBusiness, SLOT(parseData(QByteArray, Protocol::ProtocolParam)));

    // 本地业务处理数据->发送到PDSERVER
    connect(m_pLocalBusiness, SIGNAL(sigDataToRemote(QByteArray,Protocol::ProtocolParam)),
            m_pProtocol, SLOT(sendData(QByteArray,Protocol::ProtocolParam)));

    // 通讯组件连接状态改变
    connect( m_pClient, SIGNAL(sigLinkState(TcpClient::TcpLinkState)),
             this, SLOT(onLinkStateChanged(TcpClient::TcpLinkState)));

    connect(m_pLocalBusiness, SIGNAL(sigFigureType(QVector<UINT8>)),
            this, SIGNAL(sigFigureType(QVector<UINT8>)));

    connect(this, SIGNAL(sigOpen()),
        this, SLOT(onOpenClient()));

    m_pThread = new QThread;
    moveToThread(m_pThread);
    m_pThread->start();
}

/****************************
功能： 析构函数
*****************************/
LocalService::~LocalService()
{
    if (m_pThread)
    {
        m_pThread->exit();
        m_pThread->wait();
        delete m_pThread;
        m_pThread = NULL;
    }

    if( m_pLocalBusiness != NULL )
    {
        delete m_pLocalBusiness;
        m_pLocalBusiness = NULL;
    }

    moveToThread(QThread::currentThread());
}

void LocalService::sendDatFileData(const QByteArray &baFileData)
{
    m_pLocalBusiness->sendDatFileData(baFileData);
}

void LocalService::onOpenClient()
{
    m_pClient->open();
}

/****************************
功能： 打开
*****************************/
void LocalService::open( void )
{
    emit sigOpen();
}

/****************************
功能： 关闭
*****************************/
void LocalService::close( void )
{
    m_pClient->close();
}

/****************************
功能： 设置通讯参数
入参：ip，port
*****************************/
void LocalService::setCommParam( const QString& ip,quint16 usSocketPort )
{
    m_strIP = ip;
    m_strPort = usSocketPort;
    m_pClient->setIpAndPort( ip,usSocketPort );
}

bool LocalService::isConnected()
{
    return m_isConnected;
}

/*************************************************
功能： 槽 连接状态改变时触发
参数：eLinkState -- 连接状态
*************************************************/
void LocalService::onLinkStateChanged( TcpClient::TcpLinkState eLinkState )
{
    dbg_info("eLinkState is %d\n", eLinkState);
    if (TcpClient::TCP_CONNECTED == eLinkState)
    {
        m_isConnected = true;
        // 重置协议
        m_pProtocol->reset();

        emit sigConnectionStatusChanged(true);
    }
    else
    {
        // 未成功连接
        m_isConnected = false;
        emit sigConnectionStatusChanged(false);
    }

    if (TcpClient::TCP_FAIL == eLinkState)
    {

    }

    // 通知业务层链路状态改变
    m_pLocalBusiness->setLinkState(TcpClient::TCP_CONNECTED == eLinkState);
}
