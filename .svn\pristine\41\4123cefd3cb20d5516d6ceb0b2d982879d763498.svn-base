#include <QHBoxLayout>
#include <QPushButton>
#include <QPainter>
#include <QLabel>
#include <QComboBox>
#include "softkeyboard.h"
#include "valuesettingitem.h"
#include "environmentsettingdialog.h"
#include "appfontmanager/appfontmanager.h"
#include "window/Window.h"

namespace
{
    const int g_iDialogWidth = 410;
    const int g_iSpacerItemSize = 10;
    const int g_iItemSpacing = 12;
    const double g_dTemperatureMin = -40;
    const double g_dTemperatureMax = 85;
    const double g_dHumidityMin = 0;
    const double g_dHumidityMax = 100;

    const int g_iWeatherFontSize = 20;
    const int g_iWeatherComboBoxHeight = 50;
    const QString g_qstrWeatherComboBoxStyle = "QComboBox{border: 1px solid gray; border-radius: 3px; padding: 1px 2px 1px 2px;}"
                                               "QComboBox::drop-down {width:40px; background-color: transparent; border-left-width: 1px; border-left-color: darkgray; border-left-style: solid;}"
                                               "QComboBox::down-arrow {image: url(://images/down_arrow.png);}"
                                               "QComboBox:on {border-color: #1082c6;}";

    const int g_iButtonSpacing = 10;
    const int g_iButtonWidth = 130;
    const int g_iButtonHeight = 60;
    const int g_iButtonFontSize = 24;
    const QString g_iButtonStyle = "QPushButton{border-style: none;border: 0px;color: #F0F0F0;padding: 5px;border-radius:5px;"
        "background: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1, stop:0 #1B89CA, stop:1 #1077B5)}";

}

QString getTemperatureStr(PDAServiceNS::Weather eWeather)
{
    QString qstrWeather;
    switch (eWeather)
    {
    case PDAServiceNS::WEATHER_SUNSHINE:
        qstrWeather = QObject::tr("Sunshine");
        break;
    case PDAServiceNS::WEATHER_OVERCAST:
        qstrWeather = QObject::tr("Overcast");
        break;
    case PDAServiceNS::WEATHER_RAINY:
        qstrWeather = QObject::tr("Rainy");
        break;
    case PDAServiceNS::WEATHER_SNOWY:
        qstrWeather = QObject::tr("Snowy");
        break;
    case PDAServiceNS::WEATHER_FOG:
        qstrWeather = QObject::tr("Fog");
        break;
    case PDAServiceNS::WEATHER_THUNDERSTORM:
        qstrWeather = QObject::tr("Thunderstorm");
        break;
    case PDAServiceNS::WEATHER_CLOUDY:
        qstrWeather = QObject::tr("Cloudy");
        break;
    }

    return qstrWeather;
}

EnvironmentSettingDialog::EnvironmentSettingDialog(const WeatherInfo& stWeatherInfo, QWidget *parent)
    : QDialog(parent),
      m_pWeatherComboBox(NULL)
{
    setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground, true);

    QString qstrTemperature = QString::number(stWeatherInfo.dTemperature);
    ValueSettingItem* pTemperatureItem = new ValueSettingItem(qstrTemperature, 0.1, this);
    pTemperatureItem->setRange(g_dTemperatureMin, g_dTemperatureMax, 1);
    pTemperatureItem->setFixedHeight(60);
    m_qvtItems.append(pTemperatureItem);

    QString qstrHumidity = QString::number(stWeatherInfo.dHumidity);
    ValueSettingItem* pHumidityItem = new ValueSettingItem(qstrHumidity, 1, this);
    pHumidityItem->setRange(g_dHumidityMin, g_dHumidityMax, 0);
    pHumidityItem->setFixedHeight(60);
    m_qvtItems.append(pHumidityItem);

    QFont font = AppFontManager::instance()->getAppCurFont();
    font.setPointSize(g_iWeatherFontSize);

    // 天气下拉框
    m_pWeatherComboBox = new QComboBox(this);
    m_pWeatherComboBox->setFont(font);
    m_pWeatherComboBox->setFixedHeight(g_iWeatherComboBoxHeight);
    m_pWeatherComboBox->setStyleSheet(g_qstrWeatherComboBoxStyle);
    m_pWeatherComboBox->setSizePolicy(QSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding));

    for (int i = PDAServiceNS::WEATHER_SUNSHINE; i < PDAServiceNS::WEATHER_COUNT; ++i)
    {
        m_pWeatherComboBox->addItem(getTemperatureStr(static_cast<PDAServiceNS::Weather>(i)), i);
    }
    int iIndex = m_pWeatherComboBox->findData(stWeatherInfo.eWeather);
    if (-1 != iIndex)
    {
        m_pWeatherComboBox->setCurrentIndex(iIndex);
    }

    QLabel* pTemperatureLabel = new QLabel(QObject::tr("Temperature") + QString::fromUtf8("(℃)"));
    pTemperatureLabel->setFont(font);
    pTemperatureLabel->setAlignment(Qt::AlignCenter);
    QLabel* pHumidityLabel = new QLabel(QObject::tr("Humidity") + "(%RH)");
    pHumidityLabel->setFont(font);
    pHumidityLabel->setAlignment(Qt::AlignCenter);
    QLabel* pWeatherLabel = new QLabel(QObject::tr("Weather"));
    pWeatherLabel->setFont(font);
    pWeatherLabel->setAlignment(Qt::AlignCenter);

    QGridLayout* pCenterLayout = new QGridLayout();
    pCenterLayout->setMargin(8);
    pCenterLayout->setVerticalSpacing(12);
    pCenterLayout->setHorizontalSpacing(8);
    pCenterLayout->addWidget(pTemperatureLabel, 0, 0);
    pCenterLayout->addWidget(pTemperatureItem, 0, 1);
    pCenterLayout->addWidget(pHumidityLabel, 1, 0);
    pCenterLayout->addWidget(pHumidityItem, 1, 1);
    pCenterLayout->addWidget(pWeatherLabel, 2, 0);
    pCenterLayout->addWidget(m_pWeatherComboBox, 2, 1);
    pCenterLayout->setColumnStretch(0, 1);
    pCenterLayout->setColumnStretch(1, 3);

    font.setPointSize(g_iButtonFontSize);

    QPushButton* pOKButton = new QPushButton(QObject::trUtf8("OK"), this);
    pOKButton->setFixedSize(g_iButtonWidth, g_iButtonHeight);
    pOKButton->setStyleSheet(g_iButtonStyle);
    pOKButton->setFont(font);
    pOKButton->setShortcut(Qt::Key_Return);
    connect(pOKButton, SIGNAL(clicked()), this, SLOT(accept()));

    QPushButton* pCancelButton = new QPushButton(QObject::trUtf8("Cancel"), this);
    pCancelButton->setFixedSize(g_iButtonWidth, g_iButtonHeight);
    pCancelButton->setStyleSheet(g_iButtonStyle);
    pCancelButton->setFont(font);
    pCancelButton->setShortcut(Qt::Key_Escape);
    connect(pCancelButton, SIGNAL(clicked()), this, SLOT(reject()));

    QHBoxLayout* pCommondButtonLayout = new QHBoxLayout();
    pCommondButtonLayout->setContentsMargins(0, 0, 0, 0);
    pCommondButtonLayout->setSpacing(g_iButtonSpacing);
    pCommondButtonLayout->addItem(new QSpacerItem(g_iSpacerItemSize, g_iSpacerItemSize, QSizePolicy::Expanding, QSizePolicy::Minimum));
    pCommondButtonLayout->addWidget(pOKButton);
    pCommondButtonLayout->addWidget(pCancelButton);

    QVBoxLayout* pMainLayout = new QVBoxLayout(this);
    pMainLayout->setContentsMargins(4, 2, 2, 4);
    pMainLayout->setSpacing(4);
    pMainLayout->addLayout(pCenterLayout);
    pMainLayout->addLayout(pCommondButtonLayout);
    setLayout(pMainLayout);

    setFixedSize(g_iDialogWidth, sizeHint().height());
}

EnvironmentSettingDialog::~EnvironmentSettingDialog()
{
}

/*************************************************************
 * 功能：获取环境信息
 * 输出参数：
 *         stWeatherInfo：环境信息
 * ************************************************************/
void EnvironmentSettingDialog::getEnvironmentInfo(WeatherInfo& stWeatherInfo)
{
    if (2 != m_qvtItems.size())
    {
        return;
    }

    if (m_qvtItems[0])
    {
        stWeatherInfo.dTemperature = m_qvtItems[0]->getValueString().toDouble();
    }

    if (m_qvtItems[1])
    {
        stWeatherInfo.dHumidity = m_qvtItems[1]->getValueString().toDouble();
    }

    if (m_pWeatherComboBox)
    {
        stWeatherInfo.eWeather = static_cast<PDAServiceNS::Weather>(m_pWeatherComboBox->itemData(m_pWeatherComboBox->currentIndex()).toInt());
    }
}

/*************************************************
函数名： paintEvent(QPaintEvent* pEvent)
输入参数： pEvent：重绘事件
输出参数： NULL
返回值： NULL
功能： 重绘事件处理
*************************************************************/
void EnvironmentSettingDialog::paintEvent(QPaintEvent* pEvent)
{
    Q_UNUSED(pEvent)

    QPainter painter(this);
    painter.setPen(Qt::NoPen);
    QLinearGradient linearGradient(QPoint(0, 0), QPoint(width(), height()));
    linearGradient.setColorAt(0, QColor(130, 181, 238));
    linearGradient.setColorAt(1, QColor(189, 217, 247));
    painter.setBrush(QBrush(linearGradient));

    QRect rect = this->rect();
    rect.setWidth(rect.width() - 1);
    rect.setHeight(rect.height() - 1);
    painter.drawRoundedRect(rect, 15, 15);
}

/*************************************************
函数名： showEvent(QShowEvent* pEvent)
输入参数： pEvent：显示事件
输出参数： NULL
返回值： NULL
功能： 显示事件处理
*************************************************************/
void EnvironmentSettingDialog::showEvent(QShowEvent* pEvent)
{
    SoftKeyBoard::setAttribute(KEYBOARD_MODALITY_PROPERTY, SoftKeyBoard::APPLICATION_MODAL);
    SoftKeyBoard::setAttribute(KEYBOARD_DISPLAY_PROPERTY, SoftKeyBoard::SHOW_NUMBER_PAGE);

    QDialog::showEvent(pEvent);
}

/*************************************************
函数名： hideEvent(QHideEvent* pEvent)
输入参数： pEvent：隐藏事件
输出参数： NULL
返回值： NULL
功能： 隐藏事件处理
*************************************************************/
void EnvironmentSettingDialog::hideEvent(QHideEvent* pEvent)
{
    SoftKeyBoard::setAttribute(KEYBOARD_MODALITY_PROPERTY, SoftKeyBoard::NON_MODAL);
    SoftKeyBoard::setAttribute(KEYBOARD_DISPLAY_PROPERTY, SoftKeyBoard::SHOW_ALL_PAGE);

    QDialog::hideEvent(pEvent);
}
