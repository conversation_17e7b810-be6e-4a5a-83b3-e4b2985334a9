#-------------------------------------------------
#
# Project created by QtCreator 2016-11-13T21:39:34
#
#-------------------------------------------------
QT       += xml
QT       -= gui

TARGET = service
TEMPLATE = lib

DEFINES += SERVICE_LIBRARY

unix {
    LIBS += -L"$$PWD/../lib/arm/app" -llog_r_487
}

SOURCES += service.cpp \
    config/xmlsetting/XmlSettings.cpp \
    config/ConfigManager.cpp \
    config/ConfigInstance.cpp \
    config/ConfigInfo.cpp
    #log/qslog/QsLogDest.cpp \
    #log/qslog/QsLogDestConsole.cpp \
    #log/qslog/QsLogDestFile.cpp \
    #log/qslog/QsLogDestFunctor.cpp \
    #log/Logger.cpp \
    #log/LogManager.cpp \
    #log/LogInstance.cpp \
    #log/LoggerPrivate.cpp \
    #log/LogInfo.cpp

HEADERS += service.h\
        service_global.h \
    config/xmlsetting/XmlSettings.h \
    config/ConfigManager.h \
    config/ConfigInfo.h \
    config/ConfigInstance.h
    #log/qslog/QsLogDest.h \
    #log/qslog/QsLogDestConsole.h \
    #log/qslog/QsLogDestFile.h \
    #log/qslog/QsLogDestFunctor.h \
    #log/qslog/QsLogLevel.h \
    #log/LogInfo.h \
    #log/Logger.h \
    #log/LogManager.h \
    #log/LogInstance.h \
    #log/LoggerPrivate.h

unix {
    target.path = /usr/lib
    INSTALLS += target
}

win32:CONFIG(release, debug|release): LIBS += -L$$OUT_PWD/../core/release/ -lcore
else:win32:CONFIG(debug, debug|release): LIBS += -L$$OUT_PWD/../core/debug/ -lcore
else:unix: LIBS += -L$$OUT_PWD/../core/ -lcore

INCLUDEPATH += $$PWD/../core
INCLUDEPATH += $$PWD/../lib/include
DEPENDPATH += $$PWD/../core
