#include <QGraphicsScene>
#include <QHBoxLayout>
#include <QGraphicsView>
#include <QBitmap>
#include <QDebug>
#include "softkeyboard.h"
#include "rotateinfraredparameterdialog.h"
#include "systemsetting/systemsetservice.h"
#include "global_log.h"
#include "infrareddatadefine.h"

const int WIDTH_OFFSET = 10;        //横坐标偏移
const int HEIGHT_OFFSET = 30;       //纵坐标偏移
const int DIALOG_WIDTH = 460;       //对话框宽度
const int DIALOG_HEIGHT = 550/*500*/;      //对话框高度
const int ROUND_RADIUS = 15;        //圆角半径

/*************************************************
函数名： RotateInfraredParameterDialog(QDialog *pDialog, int angle, QWidget *parent)
输入参数： pDialog：对话框指针
          angle：旋转角度
          parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
RotateInfraredParameterDialog::RotateInfraredParameterDialog(QDialog *pDialog, int angle, QWidget *parent)
    :QDialog(parent)
{
    // 不带标题栏，无法移动和调整大小；顶层显示
    setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint);
    // 调用close后，释放堆内存，调用析构函数；默认只是hide界面
    setAttribute(Qt::WA_DeleteOnClose);

    QGraphicsScene *pScene = new QGraphicsScene(this);
    pScene->addWidget(pDialog);

    QGraphicsView *pView = new QGraphicsView(pScene, this);
    pView->setFrameShape(QFrame::NoFrame);
    pView->setBackgroundBrush(QBrush(QColor(0, 0, 0, 0)));
    pView->setAttribute(Qt::WA_TranslucentBackground);
    pView->setAutoFillBackground(true);
    pView->rotate(angle);
    pView->setFocusProxy(pDialog);
    //pView->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    pView->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    pView->setFixedSize(pDialog->height(), pDialog->width());
    setFixedSize(pDialog->height(), pDialog->width());
    // 移动到中间
    move((IMAGE_SCALED_HEIGHT - pDialog->height()) / 2, (IMAGE_SCALED_WIDTH - pDialog->width()) / 2);

    QHBoxLayout *hLayout = new QHBoxLayout(this);
    hLayout->setSpacing(0);
    hLayout->setContentsMargins(0, 0, 0, 0);
    hLayout->addWidget(pView);

    connect(pDialog, SIGNAL(accepted()), this, SLOT(accept()));
    connect(pDialog, SIGNAL(rejected()), this, SLOT(reject()));
    connect(this, SIGNAL(finished(int)), this, SLOT(onFinished()));

    // 圆角去白边
    QBitmap bmp(size());
    bmp.fill();
    QPainter pt(&bmp);
    pt.setPen(Qt::NoPen);
    pt.setBrush(Qt::black);
    pt.drawRoundedRect(bmp.rect(), ROUND_RADIUS, ROUND_RADIUS);
    setMask(bmp);
}

/*************************************************
函数名： showEvent(QShowEvent *e)
输入参数： e：显示事件
输出参数： NULL
返回值： NULL
功能： 显示事件处理
*************************************************************/
void RotateInfraredParameterDialog::showEvent(QShowEvent *e)
{
    Q_UNUSED(e);
    SoftKeyBoard::setAttribute(KEYBOARD_ROTATE_PROPERTY,   SoftKeyBoard::ROTATE_90_ANGLE);
    SoftKeyBoard::setAttribute(KEYBOARD_DISPLAY_PROPERTY,  SoftKeyBoard::SHOW_NUMBER_PUNC_PAGE);
    SoftKeyBoard::setAttribute(KEYBOARD_LOCATE_PROPERTY,   SoftKeyBoard::LOCATE_BOTTOM);
    SoftKeyBoard::setAttribute(KEYBOARD_STYLE_PROPERTY,    SoftKeyBoard::STYLE_WHITE);
    // modify by wujun, for test
    //SoftKeyBoard::setAttribute(KEYBOARD_MODALITY_PROPERTY, SoftKeyBoard::APPLICATION_MODAL);
    SoftKeyBoard::setAttribute(KEYBOARD_MODALITY_PROPERTY, SoftKeyBoard::NON_MODAL);
    return;
}

/*************************************************
函数名： onFinished()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 对话框关闭响应槽函数
*************************************************************/
void RotateInfraredParameterDialog::onFinished()
{
    SoftKeyBoard::resetAttribute();
    SystemSetService::instance()->closeKeyBoard();
    SystemSetService::instance()->initKeyBoard();
    return;
}
