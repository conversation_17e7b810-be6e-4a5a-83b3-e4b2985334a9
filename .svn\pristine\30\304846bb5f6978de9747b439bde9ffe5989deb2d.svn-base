#include "prpsprpdspectrumprivatedefine.h"

namespace DataSpecificationNS
{
    PRPSExtInformationPrivate::PRPSExtInformationPrivate()
    {
        ucAmpUnit = 1;
        bSkipAmpUnit = false;
        bHasAmpUnit = false;

        fAmpLowerLimit = 0;
        bSkipAmpLowerLimit = false;
        bHasAmpLowerLimit = false;

        fAmpUpperLimit = 0;
        bSkipAmpUpperLimit = false;
        bHasAmpUpperLimit = false;

        ucFrequencyBand = 255;
        bSkipFrequencyBand = false;
        bHasFrequencyBand = false;

        fFrequencyLowerLimit = 0;
        bSkipFrequencyLowerLimit = false;
        bHasFrequencyLowerLimit = false;

        fFequencyUpperLimit = 0;
        bSkipFequencyUpperLimit = false;
        bHasFequencyUpperLimit = false;

        iPhaseWindowCount = 60;
        bSkipPhaseWindowCount = false;
        bHasPhaseWindowCount = false;

        iQuantizedAmplitude = 50;
        bSkipQuantizedAmplitude = false;
        bHasQuantizedAmplitude = false;

        iPowerFreqCycleCount = 50;
        bSkipPowerFreqCycleCount = false;
        bHasPowerFreqCycleCount = false;

        bSkipPDTypePR = false;
        bHasPDTypeProbability = false;

        bSkipBGFileName = false;
        bHasBGFileName = false;

        ucDataJudgmentFlag = 0;
        bSkipDataJudgmentFlag = false;
        bHasDataJudgmentFlag = false;

        sGain = 60;
        bSkipGain = false;
        bHasgain = false;

        ucSyncSource = 1;
        bSkipSyncSource = false;
        bHasSyncSource = false;

        ucSyncState = 0;
        bSkipSyncState = false;
        bHasSyncState = false;

        fSyncFrequency = -1;
        bSkipSyncFrequency = false;
        bHasSyncFrequency = false;

        ucDischargeSeverity = 0;
        bSkipDischargeSeverity = false;
        bHasDischargeSeverity = false;

        ucDenoisingThreshold = 0;
        bSkipDenoisingThreshold = false;
        bHasDenoisingThreshold = false;
    }

    PRPSExtInformationPrivate& PRPSExtInformationPrivate::operator=(const PRPSExtInformationPrivate& stPRPSExtInformationPrivate)
    {
        this->ucAmpUnit = stPRPSExtInformationPrivate.ucAmpUnit;
        this->fAmpLowerLimit = stPRPSExtInformationPrivate.fAmpLowerLimit;
        this->fAmpUpperLimit = stPRPSExtInformationPrivate.fAmpUpperLimit;
        this->ucFrequencyBand = stPRPSExtInformationPrivate.ucFrequencyBand;
        this->fFrequencyLowerLimit = stPRPSExtInformationPrivate.fFrequencyLowerLimit;
        this->fFequencyUpperLimit = stPRPSExtInformationPrivate.fFequencyUpperLimit;
        this->iPhaseWindowCount = stPRPSExtInformationPrivate.iPhaseWindowCount;
        this->iQuantizedAmplitude = stPRPSExtInformationPrivate.iQuantizedAmplitude;
        this->iPowerFreqCycleCount = stPRPSExtInformationPrivate.iPowerFreqCycleCount;
        this->vecPDTypeProbability = stPRPSExtInformationPrivate.vecPDTypeProbability;
        this->qstrBGFileName = stPRPSExtInformationPrivate.qstrBGFileName;
        this->ucDataJudgmentFlag = stPRPSExtInformationPrivate.ucDataJudgmentFlag;
        this->sGain = stPRPSExtInformationPrivate.sGain;
        this->ucSyncSource = stPRPSExtInformationPrivate.ucSyncSource;
        this->ucSyncState = stPRPSExtInformationPrivate.ucSyncState;
        this->fSyncFrequency = stPRPSExtInformationPrivate.fSyncFrequency;
        this->ucDischargeSeverity = stPRPSExtInformationPrivate.ucDischargeSeverity;
        this->ucDenoisingThreshold = stPRPSExtInformationPrivate.ucDenoisingThreshold;
        return *this;
    }

    bool PRPSExtInformationPrivate::operator==(const PRPSExtInformationPrivate& stPRPSExtInformationPrivate) const
    {
        if (!(this->ucAmpUnit == stPRPSExtInformationPrivate.ucAmpUnit)) return false;
        if (!qFuzzyCompare(this->fAmpLowerLimit, stPRPSExtInformationPrivate.fAmpLowerLimit)) return false;
        if (!qFuzzyCompare(this->fAmpUpperLimit, stPRPSExtInformationPrivate.fAmpUpperLimit)) return false;
        if (!(this->ucFrequencyBand == stPRPSExtInformationPrivate.ucFrequencyBand)) return false;
        if (!qFuzzyCompare(this->fFrequencyLowerLimit, stPRPSExtInformationPrivate.fFrequencyLowerLimit)) return false;
        if (!qFuzzyCompare(this->fFequencyUpperLimit, stPRPSExtInformationPrivate.fFequencyUpperLimit)) return false;
        if (!(this->iPhaseWindowCount == stPRPSExtInformationPrivate.iPhaseWindowCount)) return false;
        if (!(this->iQuantizedAmplitude == stPRPSExtInformationPrivate.iQuantizedAmplitude)) return false;
        if (!(this->iPowerFreqCycleCount == stPRPSExtInformationPrivate.iPowerFreqCycleCount)) return false;
        if (!(this->vecPDTypeProbability == stPRPSExtInformationPrivate.vecPDTypeProbability)) return false;
        if (!(this->qstrBGFileName == stPRPSExtInformationPrivate.qstrBGFileName)) return false;
        if (!(this->ucDataJudgmentFlag == stPRPSExtInformationPrivate.ucDataJudgmentFlag)) return false;
        if (!(this->sGain == stPRPSExtInformationPrivate.sGain)) return false;
        if (!(this->ucSyncSource == stPRPSExtInformationPrivate.ucSyncSource)) return false;
        if (!(this->ucSyncState == stPRPSExtInformationPrivate.ucSyncState)) return false;
        if (!qFuzzyCompare(this->fSyncFrequency, stPRPSExtInformationPrivate.fSyncFrequency)) return false;
        if (!(this->ucDischargeSeverity == stPRPSExtInformationPrivate.ucDischargeSeverity)) return false;
        if (!(this->ucDenoisingThreshold == stPRPSExtInformationPrivate.ucDenoisingThreshold)) return false;
        return true;
    }


    PRPSDataPrivate::PRPSDataPrivate()
    {
        bSkipPDSpectrumData = false;
        bHasPDSpectrumData = false;
    }

    PRPSDataPrivate& PRPSDataPrivate::operator=(const PRPSDataPrivate& stPRPSDataPrivate)
    {
        this->qbaPDSpectrumData = stPRPSDataPrivate.qbaPDSpectrumData;
        return *this;
    }

    bool PRPSDataPrivate::operator==(const PRPSDataPrivate& stPRPSDataPrivate) const
    {
        if (!(this->qbaPDSpectrumData == stPRPSDataPrivate.qbaPDSpectrumData)) return false;
        return true;
    }


    PRPDExtInformationPrivate::PRPDExtInformationPrivate()
    {
        ucAmpUnit = 1;
        bSkipAmpUnit = false;
        bHasAmpUnit = false;

        fAmpLowerLimit = 0;
        bSkipAmpLowerLimit = false;
        bHasAmpLowerLimit = false;

        fAmpUpperLimit = 0;
        bSkipAmpUpperLimit = false;
        bHasAmpUpperLimit = false;

        ucFrequencyBand = 255;
        bSkipFrequencyBand = false;
        bHasFrequencyBand = false;

        fFrequencyLowerLimit = 0;
        bSkipFrequencyLowerLimit = false;
        bHasFrequencyLowerLimit = false;

        fFequencyUpperLimit = 0;
        bSkipFequencyUpperLimit = false;
        bHasFequencyUpperLimit = false;

        iPhaseWindowCount = 60;
        bSkipPhaseWindowCount = false;
        bHasPhaseWindowCount = false;

        iQuantizedAmplitude = 50;
        bSkipQuantizedAmplitude = false;
        bHasQuantizedAmplitude = false;

        iPowerFreqCycleCount = 50;
        bSkipPowerFreqCycleCount = false;
        bHasPowerFreqCycleCount = false;

        bSkipPDTypePR = false;
        bHasPDTypeProbability = false;

        bSkipBGFileName = false;
        bHasBGFileName = false;

        ucDataJudgmentFlag = 0;
        bSkipDataJudgmentFlag = false;
        bHasDataJudgmentFlag = false;

        sGain = 60;
        bSkipGain = false;
        bHasgain = false;

        ucSyncSource = 1;
        bSkipSyncSource = false;
        bHasSyncSource = false;

        ucSyncState = 0;
        bSkipSyncState = false;
        bHasSyncState = false;

        fSyncFrequency = -1;
        bSkipSyncFrequency = false;
        bHasSyncFrequency = false;

        ucDischargeSeverity = 0;
        bSkipDischargeSeverity = false;
        bHasDischargeSeverity = false;

        ucDenoisingThreshold = 0;
        bSkipDenoisingThreshold = false;
        bHasDenoisingThreshold = false;
    }

    PRPDExtInformationPrivate& PRPDExtInformationPrivate::operator=(const PRPDExtInformationPrivate& stPRPDExtInformationPrivate)
    {
        this->ucAmpUnit = stPRPDExtInformationPrivate.ucAmpUnit;
        this->fAmpLowerLimit = stPRPDExtInformationPrivate.fAmpLowerLimit;
        this->fAmpUpperLimit = stPRPDExtInformationPrivate.fAmpUpperLimit;
        this->ucFrequencyBand = stPRPDExtInformationPrivate.ucFrequencyBand;
        this->fFrequencyLowerLimit = stPRPDExtInformationPrivate.fFrequencyLowerLimit;
        this->fFequencyUpperLimit = stPRPDExtInformationPrivate.fFequencyUpperLimit;
        this->iPhaseWindowCount = stPRPDExtInformationPrivate.iPhaseWindowCount;
        this->iQuantizedAmplitude = stPRPDExtInformationPrivate.iQuantizedAmplitude;
        this->iPowerFreqCycleCount = stPRPDExtInformationPrivate.iPowerFreqCycleCount;
        this->vecPDTypeProbability = stPRPDExtInformationPrivate.vecPDTypeProbability;
        this->qstrBGFileName = stPRPDExtInformationPrivate.qstrBGFileName;
        this->ucDataJudgmentFlag = stPRPDExtInformationPrivate.ucDataJudgmentFlag;
        this->sGain = stPRPDExtInformationPrivate.sGain;
        this->ucSyncSource = stPRPDExtInformationPrivate.ucSyncSource;
        this->ucSyncState = stPRPDExtInformationPrivate.ucSyncState;
        this->fSyncFrequency = stPRPDExtInformationPrivate.fSyncFrequency;
        this->ucDischargeSeverity = stPRPDExtInformationPrivate.ucDischargeSeverity;
        this->ucDenoisingThreshold = stPRPDExtInformationPrivate.ucDenoisingThreshold;
        return *this;
    }

    bool PRPDExtInformationPrivate::operator==(const PRPDExtInformationPrivate& stPRPDExtInformationPrivate) const
    {
        if (!(this->ucAmpUnit == stPRPDExtInformationPrivate.ucAmpUnit)) return false;
        if (!qFuzzyCompare(this->fAmpLowerLimit, stPRPDExtInformationPrivate.fAmpLowerLimit)) return false;
        if (!qFuzzyCompare(this->fAmpUpperLimit, stPRPDExtInformationPrivate.fAmpUpperLimit)) return false;
        if (!(this->ucFrequencyBand == stPRPDExtInformationPrivate.ucFrequencyBand)) return false;
        if (!qFuzzyCompare(this->fFrequencyLowerLimit, stPRPDExtInformationPrivate.fFrequencyLowerLimit)) return false;
        if (!qFuzzyCompare(this->fFequencyUpperLimit, stPRPDExtInformationPrivate.fFequencyUpperLimit)) return false;
        if (!(this->iPhaseWindowCount == stPRPDExtInformationPrivate.iPhaseWindowCount)) return false;
        if (!(this->iQuantizedAmplitude == stPRPDExtInformationPrivate.iQuantizedAmplitude)) return false;
        if (!(this->iPowerFreqCycleCount == stPRPDExtInformationPrivate.iPowerFreqCycleCount)) return false;
        if (!(this->vecPDTypeProbability == stPRPDExtInformationPrivate.vecPDTypeProbability)) return false;
        if (!(this->qstrBGFileName == stPRPDExtInformationPrivate.qstrBGFileName)) return false;
        if (!(this->ucDataJudgmentFlag == stPRPDExtInformationPrivate.ucDataJudgmentFlag)) return false;
        if (!(this->sGain == stPRPDExtInformationPrivate.sGain)) return false;
        if (!(this->ucSyncSource == stPRPDExtInformationPrivate.ucSyncSource)) return false;
        if (!(this->ucSyncState == stPRPDExtInformationPrivate.ucSyncState)) return false;
        if (!qFuzzyCompare(this->fSyncFrequency, stPRPDExtInformationPrivate.fSyncFrequency)) return false;
        if (!(this->ucDischargeSeverity == stPRPDExtInformationPrivate.ucDischargeSeverity)) return false;
        if (!(this->ucDenoisingThreshold == stPRPDExtInformationPrivate.ucDenoisingThreshold)) return false;
        return true;
    }


    PRPDDataPrivate::PRPDDataPrivate()
    {
        bSkipPDSpectrumData = false;
        bHasPDSpectrumData = false;
    }

    PRPDDataPrivate& PRPDDataPrivate::operator=(const PRPDDataPrivate& stPRPDDataPrivate)
    {
        this->qbaPDSpectrumData = stPRPDDataPrivate.qbaPDSpectrumData;
        return *this;
    }

    bool PRPDDataPrivate::operator==(const PRPDDataPrivate& stPRPDDataPrivate) const
    {
        if (!(this->qbaPDSpectrumData == stPRPDDataPrivate.qbaPDSpectrumData)) return false;
        return true;
    }
}
