#ifndef NETTCPCLIENT_H
#define NETTCPCLIENT_H

#include <QObject>
#include <QTcpServer>
#include <QTcpSocket>
#include <QAbstractSocket>

class NetTcpClient : public QObject
{
    Q_OBJECT
public:
    explicit NetTcpClient( QObject *parent = NULL);
    ~NetTcpClient();

    bool connectServer(const QString& qstrSrvIP, const quint16 wSrvPort, int iTimeOut = 10000);

    void disconnectServer();

    QByteArray read();

    int write(const QByteArray& qbaData);

signals:
    void sigReadyRead();

public slots:
    void onDisconnectSock();

    void onReadyRead();

    void onSocketError(QAbstractSocket::SocketError stError);

private:
    QTcpSocket*     m_pSock;
    bool            m_bConnected;
    QString         m_qstrSvrIP;
    quint16         m_wSvrPort;
};

#endif // NETTCPCLIENT_H
