#include "tevpdamainview.h"
#include <QVBoxLayout>
#include <QDateTime>
#include "window/Window.h"
#include "tev/tevamppdaview.h"
#include "tev/tevpulsepdaview.h"
#include "pda/pdaservice.h"
#include "global_log.h"

const int g_iInvalidTimeId = -1;
const int g_iAutoSwitchTime = 2000;

TEVPDAMainView::TEVPDAMainView(const ItemTestData& stTestDataInfo, const PDAView::PatrolTestPointInfo& stPatrolTestPointInfo, QWidget* parent)
    : QWidget(parent),
      m_iAutoSwitchTimerId(g_iInvalidTimeId)
{
    // 无边框，析构时删除
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_DeleteOnClose);

    m_pStackedLayout = new QStackedLayout();
    m_pStackedLayout->setContentsMargins(0, 0, 0, 0);
    setLayout(m_pStackedLayout);

    qint64 qi64MSecTime = QDateTime::currentDateTime().toMSecsSinceEpoch();

    showTEVView(stTestDataInfo, stPatrolTestPointInfo);

    log_debug("waste %lld ms", QDateTime::currentDateTime().toMSecsSinceEpoch() - qi64MSecTime);
}

TEVPDAMainView::~TEVPDAMainView()
{
}

void TEVPDAMainView::onTested()
{
    // 跳转下一个同类型测点
    ItemTestData stTestDataInfo;
    PDAView::PatrolTestPointInfo stTestPointInfo;
    if (PDAView::enterNextTestItem(stTestDataInfo, stTestPointInfo))
    {

        showTEVView(stTestDataInfo, stTestPointInfo);
    }
    else
    {
        delayToClose();
    }
}

void TEVPDAMainView::keyPressEvent(QKeyEvent *pEvent)
{
    switch(pEvent->key())
    {
    case Qt::Key_Escape:
    {
        close();
        break;
    }
    case Qt::Key_Left:
    {
        break;
    }
    case Qt::Key_Right:
    {
        break;
    }
    default:
    {
        QWidget::keyPressEvent(pEvent);
        break;
    }
    }

    return;
}

bool TEVPDAMainView::eventFilter(QObject *pObj, QEvent *pEvent)
{
    bool bRet = false;
    if(pEvent->type() == QEvent::KeyPress)
    {
        QKeyEvent* pKeyEvent = static_cast<QKeyEvent*>(pEvent);
        if(Qt::Key_Escape == pKeyEvent->key())
        {
            close();
            bRet = true;
        }
        else if(Qt::Key_Left == pKeyEvent->key())
        {
            //bRet = true;
        }
        else if(Qt::Key_Right == pKeyEvent->key())
        {
            //bRet = true;
        }
        else
        {
            bRet = QWidget::eventFilter(pObj, pEvent);
        }
    }
    else
    {
        bRet = QWidget::eventFilter(pObj, pEvent);
    }

    return bRet;
}

/*************************************************
功能： 定时器处理函数
输入参数：
     pEvent -- 定时事件
*************************************************************/
void TEVPDAMainView::timerEvent(QTimerEvent* pEvent)
{
    if(pEvent->timerId() == m_iAutoSwitchTimerId)
    {
        killAutoSwitchTimer();
        close();
        PDAService::instance()->currentTask()->sendAutoSwitchSignal();
    }
}

/*************************************************
功能： 窗口关闭事件
输入参数:
    pEvent -- 事件
*************************************************************/
void TEVPDAMainView::closeEvent(QCloseEvent* pEvent)
{
    QWidget::closeEvent(pEvent);
    emit sigClosed();
}

void TEVPDAMainView::showTEVView(const ItemTestData& stTestDataInfo, const PDAView::PatrolTestPointInfo& stPatrolTestPointInfo)
{
    switch (stTestDataInfo.eDataType)
    {
    case PDAServiceNS::TEV_AMP_DATA:
    {
        TEVAmpPDAView* pTEVAmpPDAView = findChild<TEVAmpPDAView*>();
        if (NULL == pTEVAmpPDAView)
        {
            pTEVAmpPDAView = new TEVAmpPDAView(PDA_VIEW_CONFIG_TRANSLATE(PDAView::TEXT_AMP_TEST), stTestDataInfo, this);
            // 禁用自动析构，因为统一由AEPDAMainView来管理
            pTEVAmpPDAView->setAttribute(Qt::WA_DeleteOnClose, false);
            pTEVAmpPDAView->setTestPointInfo(stPatrolTestPointInfo);
            pTEVAmpPDAView->installEventFilter(this);
            connect(pTEVAmpPDAView->titleBar(), SIGNAL(sigClicked()), this, SLOT(close()));
            connect(pTEVAmpPDAView, SIGNAL(sigTested()), this, SLOT(onTested()));

            m_pStackedLayout->addWidget(pTEVAmpPDAView);
        }
        else
        {
            pTEVAmpPDAView->updateTestInfo(stTestDataInfo, stPatrolTestPointInfo);
        }
        m_pStackedLayout->setCurrentWidget(pTEVAmpPDAView);
        break;
    }
    case PDAServiceNS::TEV_PULSE_DATA:
    {
        TEVPulsePDAView* pTEVPulsePDAView = findChild<TEVPulsePDAView*>();
        if (NULL == pTEVPulsePDAView)
        {
            pTEVPulsePDAView = new TEVPulsePDAView(PDA_VIEW_CONFIG_TRANSLATE(PDAView::TEXT_PULSE_TEST), stTestDataInfo, this);
            // 禁用自动析构，因为统一由AEPDAMainView来管理
            pTEVPulsePDAView->setAttribute(Qt::WA_DeleteOnClose, false);
            pTEVPulsePDAView->setTestPointInfo(stPatrolTestPointInfo);
            pTEVPulsePDAView->installEventFilter(this);
            connect(pTEVPulsePDAView->titleBar(), SIGNAL(sigClicked()), this, SLOT(close()));
            connect(pTEVPulsePDAView, SIGNAL(sigTested()), this, SLOT(onTested()));

            m_pStackedLayout->addWidget(pTEVPulsePDAView);
        }
        else
        {
            pTEVPulsePDAView->updateTestInfo(stTestDataInfo, stPatrolTestPointInfo);
        }
        m_pStackedLayout->setCurrentWidget(pTEVPulsePDAView);
        break;
    }
    default:
        break;
    }
}

/*************************************************
功能： 延迟关闭界面，用于查看检测数据
*************************************************************/
void TEVPDAMainView::delayToClose()
{
    if(g_iInvalidTimeId == m_iAutoSwitchTimerId)
    {
        m_iAutoSwitchTimerId = startTimer(Module::SWITCH_TIME_INTERVAL);
        logInfo(QString("start switch timer id: %1.").arg(m_iAutoSwitchTimerId).toLatin1().data());
        Module::mSleep(Module::SWITCH_TIME_INTERVAL);
    }
}

/*************************************************
功能： 关闭自动跳转定时器
*************************************************************/
void TEVPDAMainView::killAutoSwitchTimer()
{
    if(g_iInvalidTimeId != m_iAutoSwitchTimerId)
    {
        logInfo(QString("kill switch timer id: %1.").arg(m_iAutoSwitchTimerId).toLatin1().data());
        killTimer(m_iAutoSwitchTimerId);
        m_iAutoSwitchTimerId = g_iInvalidTimeId;
    }
}
