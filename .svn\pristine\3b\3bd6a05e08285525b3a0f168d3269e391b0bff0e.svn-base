/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* uhfprpsview.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年4月21日
*       新版本重构
* 摘要：UHF prps view接口和成员申明

* 当前版本：1.0
*/

#ifndef UHFPRPSVIEW_H
#define UHFPRPSVIEW_H

#include <QWidget>
#include "uhf/UHF.h"
#include "buttonBar/LabelButtonBar.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "prps/prpsview/uhfprpsunionview.h"
#include "uhf/dataSave/UHFPRPSAndPRPDDataSave.h"
#include "prps/prpsvideo/prpsrecordview.h"
#include "uhfprpsviewbase.h"
#include "messageBox/msgbox.h"
#include "loadingView/textloadingview.h"
#include "View.h"
//data save
#include "datafile/datamap.h"
#include "datafile/datafile.h"
#include "datafile/prps/prpsdatamap.h"
#include "datafile/prps/prpddatamap.h"
#include "diagnosis/PDdiagnosis.h"
class UhfPRPSView : public UhfPRPSViewBase
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        parent:父控件指针
    *************************************************************/
    explicit UhfPRPSView(const QString &strTitle, QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~UhfPRPSView( );

protected:
    /*************************************************
    功能： 响应S键事件
    *************************************************************/
    void onSKeyPressed();

    /*************************************************
    功能： 事件处理的函数
    输入参数：
            event -- 事件对象
    *************************************************************/
    bool event ( QEvent * event );

    /*************************************************
    功能： 键盘事件
    输入参数:
        event -- 事件
    *************************************************************/
    void keyPressEvent( QKeyEvent* event );

    /*************************************************
     * 功能：诊断数据
     * 输入参数：
     *      bSave：是否为保存操作的逻辑，缺省为false
     * ***********************************************/
    virtual void diagDataInfo(bool bSave = false);

    /****************************************************
     * 功能：保存操作
     * **************************************************/
    void pressSaveData();

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

    /*************************************************
    功能： 槽，响应诊断结果
    输入参数：
            qspDiagResultInfo -- 诊断结果
    *************************************************************/
    void onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo);

private:

    /*************************************************
    函数名： thread_save_video_data
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能：回调函数， 保存录屏数据
    *************************************************************/
    void thread_save_video_data();

    /************************************************
     * 函数名   : setButtonBarDatas
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置buttonbar显示的参数
     ************************************************/
    void setButtonBarDatas();

    /************************************************
     * 函数名   : createButtonbar
     * 输入参数 : parent---父控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建buttonbar
     ************************************************/
    PushButtonBar* createButtonbar(QWidget *parent);


    /************************************************
     * 函数名   : switchSample
     * 输入参数 : eState: 采样状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 起动、暂停采样切换
     ************************************************/
    void switchSample(State eState);


    /************************************************
     * 函数名   : loadData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 载入数据
     ************************************************/
    void loadData();

    /************************************************
     * 函数名   : deleteData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 删除数据
     ************************************************/
    void deleteData();

    /************************************************
     * 函数名   : restoreDefault
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 恢复默认参数
     ************************************************/
    void restoreDefault();

    /************************************************
     * 函数名   : loadPRPSVideoData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 载入录屏数据
     ************************************************/
    void loadPRPSVideoData();


    /************************************************
     * 函数名   : deletePRPSVideoData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 删除录屏数据
     ************************************************/
    void deletePRPSVideoData();


    void composeSavedVideoData(const QString &stationName, const QString& deviceName, UHFPRPSPRPDDataInfo &stSavedData);

    /************************************************
     * 函数名   : recordPRPS
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 起动录屏
     ************************************************/
    void recordPRPS();



    /************************************************
     * 函数名   : setSampleBtnText
     * 输入参数 : eState: 采样状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 更新采样状态设置采样按钮显示的文本
     ************************************************/
    void setSampleBtnText(State eState);

    /*************************************************
    功能： 停止录制
    *************************************************************/
    void stopRecord( void );

    /*************************************************
    功能： 清除录制界面
    *************************************************************/
    void deleteRecordView( void );

    /*************************************************
    功能： 启动录制
    入参：iPeriodAll -- 录制总周期数
         iInterval -- 周期间隔
    *************************************************************/
    void startRecord( qint32 iPeriodAll,qint32 iInterval );

    void saveVideoDataDone(bool isSuccess);

    /************************************************
     * 函数名   : setConfigData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :
     ************************************************/
    void setConfigData();


    /*************************************************
    功能： 保存设置
    *************************************************************/
    bool saveConfig(void);


    /************************************************
     * 函数名   : initData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 初始化数据成员
     ************************************************/
    void initData();


    /*************************************************
    函数名： saveVideoData
    输入参数: NULL
    输出参数：NULL
    返回值： 数据存储的文件路径
    功能：存储录屏数据
    *************************************************************/
    QString saveVideoData();

    /************************************************
     * 函数名   : cloudDiagnosis
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 云诊断采样数据
     ************************************************/
    void cloudDiagnosis();

    /************************************************
     * 函数名   : createChart
     * 输入参数 : parent---父控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建图谱
     ************************************************/
    ChartWidget *createChart(QWidget *parent);

    /************************************************
     * 函数名   : setChartParameters
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 给图谱设置参数
     ************************************************/
    void setChartParameters();

    /************************************************
     * 函数名   : setAllWorkSets
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置所有的工作参数
     ************************************************/
    void setAllWorkSets();

    /*************************************************
    功能： 保存数据
    返回：
        保存结果
    *************************************************************/
    QString saveDataToFile();

    /*************************************************
    功能： 保存云诊断数据
    返回：
        保存结果
    *************************************************************/
    QString saveCloudDiagnosisData();

    /*************************************************
    功能： 保存video数据
    返回：
        保存结果
    *************************************************************/
    QString saveVideoDataToFile(const QString &strStation, const QString &strDevice);

    void getDataFilePath(QString &strAbsolutePath);

    void getVideoDataFilePath(QString &strAbsolutePath);

    void composeSavedData(UHFPRPSPRPDDataInfo &stSavedData);


    void setPRPSMapHead(UHFPRPSPRPDDataInfo &stSavedData);

    void setPRPDMapHead(UHFPRPSPRPDDataInfo &stSavedData);

    void setPRPSMapInfo(UHFPRPSPRPDDataInfo &stSavedData);

    void setPRPSMapVideoInfo(UHFPRPSPRPDDataInfo &stSavedData);

    void setPRPDMapInfo(UHFPRPSPRPDDataInfo &stSavedData);

    void setPRPDMapVideoInfo(UHFPRPSPRPDDataInfo &stSavedData);

    void setPRPDData(UHFPRPSPRPDDataInfo &stSavedData);

    void setPRPDVideoData(UHFPRPSPRPDDataInfo &stSavedData);

    void setPRPSData(UHFPRPSPRPDDataInfo &stSavedData);

    void setPRPSVideoData(UHFPRPSPRPDDataInfo &stSavedData);

private slots:
    /*************************************************
    函数名： onRecordPRPSCompleted
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能：槽函数，录屏完成后的处理
    *************************************************************/
    void onRecordPRPSCompleted();

    /*************************************************
    函数名： onRecordPRPSCompleted
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能：槽函数，录屏取消后的处理
    *************************************************************/
    void onRecordPRPSCancelled();

    void startColudDiagnosis();

    void onTitleBarClicked( void );

    /************************************************
     * 函数名   : onDataRead
     * 输入参数 : stData: 采样数据
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，处理收到的采样数据
     ************************************************/
    void onDataRead(UHF::PRPSData stData, MultiServiceNS::USERID Id);

    /************************************************
     * 函数名   : onSignalChanged
     * 输入参数 : eState: 信号状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，信号状态的改变
     ************************************************/
    void onSignalChanged(Module::SignalState eState);

    /************************************************
     * 函数名   : onSyncStateChanged
     * 输入参数 : eState: 同步状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，同步状态的改变
     ************************************************/
    void onSyncStateChanged(Module::SyncState eState);

    /*************************************************
    参数： NULL
    功能： 云诊断的结果
    *************************************************/
    void onCloudDiagnosis(QString strResult, QString strCode, bool bRet);

private:
    ConfigInstance* m_pConfig;//配置模块

    Module::SignalState m_eSignalState;// 信号状态
    Module::SyncSource m_eSyncSource;//同步方式
    Module::SyncState m_eSyncState;//同步状态
    UHF::ForwardGain m_eForwardGain;//前置增益
    UHF::BandWidth m_eBandWidth;//带宽

    int m_iPhaseAlias; //相移角度
    UHF::PRPSData m_stData; //采样数据
    UINT16 m_usMaxSpectrumValue;// 最大值
    //    UHFPRPSPRPDDataInfo m_sUHFPRPSPRPDDataInfo; // 存放图谱数据用于保存的结构体
    QVector<double> m_vMaxValueVector;  //存放最大采样数据的容器
    UINT8 m_ucSysFreq;//系统频率    
    ControlButton *m_pSampleBtn; //采样按钮

    UINT8 m_ucRecordTime;//录屏时间 Unit:s
    PrpsRecordView *m_pRecordView; // 录制界面
    bool m_bIsRecording;    //是否正在录屏标志
    QFuture<void> m_SaveVideoFuture;
    UHFPRPSPRPDVideoDataInfo m_sUHFPRPSVideoDataInfo; // 存放图谱video数据
    bool m_bIsStartColudDiagnosis;
    State m_eState;

    struct SaveUHFPRPSVideoDataEvent : public QEvent
    {
        enum {EventId = QEvent::User + View::EVENT_SAVE_UHF_PRPS_VIDEO};

        explicit SaveUHFPRPSVideoDataEvent(bool saved_, const QString &message_)

            : QEvent(static_cast<Type>(EventId)),

              saved(saved_), message(message_) {}

        const bool saved;

        const QString message;
    };
    TextLoadingView *m_pLoadingWidget;

    bool m_bAccumulationChangedByRecord;
    QString m_strVideoFile;
};

#endif // UHFPRPSVIEW_H
