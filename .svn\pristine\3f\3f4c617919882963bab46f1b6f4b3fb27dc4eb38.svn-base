#include "doublevalidator.h"
#include "global_log.h"

/*************************************************
功能： 构造函数
输入参数： parent--父指针
输出参数： NULL
返回值： NULL
*************************************************/
DoubleValidator::DoubleValidator(QObject *parent)
    : QDoubleValidator(parent)
{
}

#if 1
/*************************************************
功能： 校验输入（内容改变自动触发该函数）
输入参数： input--输入内容；pos--光标位置
输出参数： NULL
返回值： QValidator::State--校验结果
*************************************************/
QValidator::State DoubleValidator::validate(QString &input, int &pos) const
{
    if(input.isEmpty())
    {
        return QValidator::Intermediate;
    }

    if (bottom() >= 0 && input.startsWith('-'))
    {
        return Invalid;
    }

    int dotPos = input.indexOf(".");
    if(dotPos > 0 && input.right(input.length() - dotPos - 1).length() > decimals())  //判断小数点位数
    {
        return Invalid;
    }

    if (input.contains(' ')
            || input.startsWith("00")
            || input.startsWith("-00"))
    {
        return QValidator::Invalid;
    }

    bool ok = false;
    double val = input.toDouble(&ok);
    if(!ok)
    {
        return (bottom() < 0 && !input.compare("-")) ? Intermediate : Invalid;
    }

    if(val <= top() && val >= bottom())
    {
        return Acceptable;
    }

    if (val >= 0)
    {
        return (val > top() && -val < bottom()) ? Invalid : Intermediate;
    }
    else
    {
        return (val < bottom()) ? Invalid : Intermediate;
    }
}
#else
/*************************************************
功能： 校验输入（内容改变自动触发该函数）
输入参数： input--输入内容；pos--光标位置
输出参数： NULL
返回值： QValidator::State--校验结果
*************************************************/
QValidator::State DoubleValidator::validate(QString &input, int &pos) const
{
    Q_UNUSED(pos);
    // 输入为空或"-"时，Intermediate状态
    if (input.isEmpty() || input == "-")
    {
        return QValidator::Intermediate;
    }

    // 校验是否可转为double（可过滤非法字符）
    bool bOk = false;
    double val = input.toDouble(&bOk);
    if (!bOk)
    {
        return QValidator::Invalid;
    }

    if (input.contains(' ')
            || input.startsWith("00")
            || input.startsWith("-00"))
    {
        return QValidator::Invalid;
    }

    //校验小数点个数
    if(getDoublePrecision(input) > decimals())
    {
        return QValidator::Invalid;
    }

    // 校验是否越界
    if (val > top() || val < bottom())
    {
        return QValidator::Invalid;
    }

    return QValidator::Acceptable;
}
#endif

