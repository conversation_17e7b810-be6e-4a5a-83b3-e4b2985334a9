#include "AEAmpDataSave.h"
#include "datadefine.h"
#ifdef Q_PROCESSOR_ARM
#include <unistd.h>
#endif
#include "datafile/datafile.h"
#include "datafile/ae/aephasedatamap.h"
#include "ae/AEConfig.h"
#include "datafile/mapdatafactory.h"
#include "model/HCStatus.h"
#include "systemsetting/systemsetservice.h"
#include "log/log.h"
/************************************************
 * 函数名   : AEAmpDataSave
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 构造函数
 ************************************************/
AEAmpDataSave::AEAmpDataSave()
{
    m_pAEAmpDataInfo = NULL;
    MapDataFactory::registerClass<AEAmpDataMap>(XML_FILE_NODE_AE_AMP);
}
/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString AEAmpDataSave::saveData(void *pData)
{
    if(NULL == pData)
    {
        return NULL;
    }
    m_pAEAmpDataInfo = (AEAmpDataInfo*)pData;
    m_strAbsolutePath = getAbsolutePath(m_pAEAmpDataInfo->stHeadInfo.generationDateTime);

    return saveData( pData, m_strAbsolutePath );
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString AEAmpDataSave::saveData(void *pData, const QString &qsSavedPath)
{
    QString strSavePath("");
    if(NULL == pData)
    {
        return strSavePath;
    }

    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return strSavePath;
    }

    m_pAEAmpDataInfo = (AEAmpDataInfo*)pData;

    QDir dir(qsSavedPath);
    if(!dir.exists())
    {
        dir.mkpath(qsSavedPath);
    }

    DataFile *pFile = new DataFile;

    setFileHeads(pFile, m_pAEAmpDataInfo->stHeadInfo );

    AEAmpDataMap *pMap = new AEAmpDataMap;
    setMapHead(pMap);
    setMapInfo(pMap);
    setMapData(pMap);
    pFile->addMap(pMap);

    if(!(pFile->save(qsSavedPath, AE_AMP_FILE_NAME_SUFFIX, strSavePath)))
    {
        QFile file(strSavePath);
        file.remove();
        strSavePath = "";
    }

    delete pFile;
    pFile = NULL;
    logDebug(strSavePath);
    //添加文件备注信息
    RemarkInfoManager::instance()->saveRemarkInfo(strSavePath, m_pAEAmpDataInfo->stHeadInfo.qstrRemark);
    return strSavePath;
}

void AEAmpDataSave::setMapHead(DataMap *pMap)
{
    pMap->setCode(DataFileNS::SPECTRUM_CODE_AE_AMP);
    pMap->setGenerationTime(m_pAEAmpDataInfo->stHeadInfo.generationDateTime);
    pMap->setSpectrumProperty(m_pAEAmpDataInfo->stHeadInfo.eMapProperty);
    pMap->setDeviceName(m_pAEAmpDataInfo->stHeadInfo.strDeviceName);
    pMap->setDeviceNumber(m_pAEAmpDataInfo->stHeadInfo.strDeviceNumber);
    pMap->setTestPointName(m_pAEAmpDataInfo->stHeadInfo.strTestPointName);
    pMap->setTestLocation(m_pAEAmpDataInfo->stHeadInfo.eTestLocation);
    pMap->setTestPointNumber(m_pAEAmpDataInfo->stHeadInfo.strTestPointNumber);
    pMap->setTestChannelSign(m_pAEAmpDataInfo->stHeadInfo.ucTestChannelSign);
    pMap->setDataPrimitiveType(m_pAEAmpDataInfo->stHeadInfo.eDataPrimitiveType);
    pMap->setPDDefectLevel(m_pAEAmpDataInfo->stHeadInfo.ePDDefectLevel);
    pMap->setPDSignalTypeInfos(m_pAEAmpDataInfo->stHeadInfo.qstrPDSignalTypeInfos);
    pMap->setRemark(m_pAEAmpDataInfo->stHeadInfo.qstrRemark);

    return;
}

void AEAmpDataSave::setMapInfo(AEAmpDataMap *pMap)
{
    //step4 set map informations
    AEMapNS::AEAmpMapInfo stInfo;
    stInfo.eAmpUnit =m_pAEAmpDataInfo->eAmpUnit;

    stInfo.fPeakMin = m_pAEAmpDataInfo->fPeakMin;
    stInfo.fPeakMax = m_pAEAmpDataInfo->fPeakMax;
    stInfo.fRMSMin = m_pAEAmpDataInfo->fRMSMin;
    stInfo.fRMSMax = m_pAEAmpDataInfo->fRMSMax;
    stInfo.fFreq1Min = m_pAEAmpDataInfo->fFreq1Min;
    stInfo.fFreq1Max = m_pAEAmpDataInfo->fFreq1Max;
    stInfo.fFreq2Min = m_pAEAmpDataInfo->fFreq2Min;
    stInfo.fFreq2Max = m_pAEAmpDataInfo->fFreq2Max;

    stInfo.eTransformerType = m_pAEAmpDataInfo->eTransformerType;
    stInfo.iDataPointNum = m_pAEAmpDataInfo->iDataPointNum;
    memcpy(stInfo.ucaDischargeTypeProb, m_pAEAmpDataInfo->ucaDischargeTypeProb, sizeof(stInfo.ucaDischargeTypeProb));
//    stInfo.fTrigAmp = m_pAEAmpDataInfo->fTrigAmp;
//    stInfo.usMaxTimeInterval = m_pAEAmpDataInfo->usMaxTimeInterval;
//    stInfo.eGainOption = m_pAEAmpDataInfo->eGainType;
    stInfo.sGain = m_pAEAmpDataInfo->sGain;
//    stInfo.eGainFactor = m_pAEAmpDataInfo->eGainFactor;
    stInfo.eSyncSource = m_pAEAmpDataInfo->eSyncSource;
    stInfo.ucSyncState = m_pAEAmpDataInfo->ucSyncState;
    stInfo.fSyncFreq = m_pAEAmpDataInfo->fSyncFreq;
    pMap->setInfo(&stInfo);
}

void AEAmpDataSave::setMapData(AEAmpDataMap *pMap)
{
    AEMapNS::AEAmpData pData;
    pData.fRms = m_pAEAmpDataInfo->stAEAmpData.fRms;
    pData.fPeak = m_pAEAmpDataInfo->stAEAmpData.fPeak;
    pData.fFrequency1 = m_pAEAmpDataInfo->stAEAmpData.fFrequency1;
    pData.fFrequency2 = m_pAEAmpDataInfo->stAEAmpData.fFrequency2;

    pData.fRmsBGN = m_pAEAmpDataInfo->stAEAmpData.fRmsBGN;
    pData.fPeakBGN = m_pAEAmpDataInfo->stAEAmpData.fPeakBGN;
    pData.fFrequency1BGN = m_pAEAmpDataInfo->stAEAmpData.fFrequency1BGN;
    pData.fFrequency2BGN = m_pAEAmpDataInfo->stAEAmpData.fFrequency2BGN;
    pMap->setData(&pData,8);
}

void AEAmpDataSave::addAEAmpMap(DataFile *pFile)
{
    AEAmpDataMap *pMap = new AEAmpDataMap;
    setMapHead(pMap);
    setMapInfo(pMap);
    setMapData(pMap);
    pFile->addMap(pMap);
}
#if 0
/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString AEAmpDataSave::saveData(void *pData, const QString &qsSavedPath)
{

    if(NULL == pData)
    {
        Module::logger() << "AEAmpDataSave::saveData: << NULL data input!";
        return NULL;
    }
    m_pAEAmpDataInfo = (AEAmpDataInfo*)pData;

    QDir dir(qsSavedPath);
    if(!dir.exists())
    {
        dir.mkpath(qsSavedPath);
    }

    const QString& strFileName = getFileName(m_pAEAmpDataInfo->dateTime);
    m_strAbsolutePath = qsSavedPath + "/" + strFileName;

    XMLDocument doc("AEAmplitude");

    organizeData(doc);

    bool isSuccess = saveToDataFile(AE_AMP_WITH_MAP, doc.getByteArray());
    if(isSuccess == false)
    {
        m_strAbsolutePath.clear();
    }

}
#endif
/************************************************
 * 函数名   : getStringFromData
 * 输入参数 : pDatas: 数据; uiCounts: 数据个数
 * 输出参数 : NULL
 * 返回值   : 转换后的字符串
 * 功能     : 将数据转成base64的字符串
 ************************************************/
QString AEAmpDataSave::getStringFromData( void *pDatas, UINT32 uiCounts)
{
    Q_UNUSED(pDatas);
    Q_UNUSED(uiCounts);
    return NULL;
}

/************************************************
 * 函数名   : saveExtInformation
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储可扩展信息
 ************************************************/
void AEAmpDataSave::saveExtInformation(XMLDocument& doc)
{
    Q_UNUSED(doc);
}

/************************************************
 * 函数名   : saveRawData
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储数据部分
 ************************************************/
void AEAmpDataSave::saveRawData(XMLDocument& doc)
{
    Q_UNUSED(doc);
}

/************************************************
 * 函数名   : organizeData
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 组织数据文件信息
 ************************************************/
 void AEAmpDataSave::organizeData(XMLDocument& doc)
 {
    Q_UNUSED(doc);
 }

 /************************************************
  * 函数名   : parseData
  * 输入参数 : baData: 数据
  * 输出参数 : pData: 解析到的数据
  * 返回值   : void
  * 功能     : 解析数据
  ************************************************/
 void AEAmpDataSave::parseData(const QByteArray& baData, void *pData, const QString &strFileName)
 {
    Q_UNUSED(baData);
    Q_UNUSED(pData);
    Q_UNUSED(strFileName);
 }

/************************************************
 * 函数名   : getDataTypeFolder
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件夹名
 * 功能     : 获取图谱类型文件存储最上层文件夹名
 ************************************************/
QString AEAmpDataSave::getDataTypeFolder(void)
{
    return AE_AMP_FOLDER;
}

/************************************************
 * 函数名   : getDataByPDA
 * 输入参数 : strFileName: 文件名; pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 获取结果
 * 功能     : 获取指定数据文件中的数据
 ************************************************/
INT32 AEAmpDataSave::getDataByPDA(const QString& strFileName, void *pData)
{
    DataFile *psDataFile = new DataFile;

    //step1 open data file
    bool isSuccess = psDataFile->open(strFileName);
    if(isSuccess == false)
    {
        delete psDataFile;
        return HC_FAILURE;
    }

    m_pAEAmpDataInfo = (AEAmpDataInfo*)pData;
    //step2 get displayed file head
    m_pAEAmpDataInfo->stHeadInfo.strSubstationName = psDataFile->stationName();

    AEAmpDataMap * pMap = dynamic_cast <AEAmpDataMap*>(psDataFile->dataMap(DataFileNS::SPECTRUM_CODE_AE_AMP));
    if( NULL == pMap )
    {
        delete psDataFile;
        return HC_FAILURE;
    }
    //step3 get displayed map head
    pMap->getDataType( m_pAEAmpDataInfo->stHeadInfo.eDataPrimitiveType );
    pMap->getDeviceName( m_pAEAmpDataInfo->stHeadInfo.strDeviceName );
    pMap->getDeviceNumber( m_pAEAmpDataInfo->stHeadInfo.strDeviceNumber );
    pMap->getTestPointName( m_pAEAmpDataInfo->stHeadInfo.strTestPointName );
    pMap->getTestPointNumber( m_pAEAmpDataInfo->stHeadInfo.strTestPointNumber );
    QString strDateTime;
    pMap->getMapGenerationTime( strDateTime );
    m_pAEAmpDataInfo->stHeadInfo.generationDateTime = QDateTime::fromString( strDateTime, "yyyy-mm-dd hh:mm:ss" );
    pMap->getMapProperty( m_pAEAmpDataInfo->stHeadInfo.eMapProperty );
    pMap->getTestChannelSign( m_pAEAmpDataInfo->stHeadInfo.ucTestChannelSign );
    //m_pAEAmpDataInfo->stHeadInfo.ucTestChannelSign--;
    pMap->getPDDefectLevel(m_pAEAmpDataInfo->stHeadInfo.ePDDefectLevel);
    pMap->getPDSignalTypeInfos(m_pAEAmpDataInfo->stHeadInfo.qstrPDSignalTypeInfos);

    //step4 get displayed map information
    AEMapNS::AEAmpMapInfo stMapInfo;
    pMap->getInfo(&stMapInfo);
    m_pAEAmpDataInfo->fSyncFreq = stMapInfo.fSyncFreq;
    m_pAEAmpDataInfo->sGain = stMapInfo.sGain;
    dbg_info("m_pAEAmpDataInfo->sGain is %d\n", m_pAEAmpDataInfo->sGain);
    m_pAEAmpDataInfo->eAmpUnit = stMapInfo.eAmpUnit;   // todo
    m_pAEAmpDataInfo->eTransformerType = stMapInfo.eTransformerType;

    AEMapNS::AEAmpData  m_pAeData;
    pMap->getData(&m_pAeData);

    m_pAEAmpDataInfo->stAEAmpData.fRmsBGN = m_pAeData.fRmsBGN;
    m_pAEAmpDataInfo->stAEAmpData.fPeakBGN = m_pAeData.fPeakBGN;
    m_pAEAmpDataInfo->stAEAmpData.fFrequency1BGN = m_pAeData.fFrequency1BGN;
    m_pAEAmpDataInfo->stAEAmpData.fFrequency2BGN = m_pAeData.fFrequency2BGN;
    m_pAEAmpDataInfo->stAEAmpData.fRms = m_pAeData.fRms;
    m_pAEAmpDataInfo->stAEAmpData.fPeak = m_pAeData.fPeak;
    m_pAEAmpDataInfo->stAEAmpData.fFrequency1 = m_pAeData.fFrequency1;
    m_pAEAmpDataInfo->stAEAmpData.fFrequency2 = m_pAeData.fFrequency2;

//    m_pAEAmpDataInfo->usHarmonicFrequency = (UINT16)m_pAeData.fFrequency1;

    delete psDataFile;
    return HC_SUCCESS;
}
/************************************************
 * 函数名   : getFileNameSuffix
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件后缀名
 * 功能     : 获取数据文件后缀名
 ************************************************/
QString AEAmpDataSave::getFileNameSuffix(void)
{
    return AE_AMP_FILE_NAME_SUFFIX;
}



