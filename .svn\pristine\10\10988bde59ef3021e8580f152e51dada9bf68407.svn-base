﻿#include <QPainter>
#include <QFontMetrics>
#include <QDebug>
#include "prpsdraw.h"
#include "phasepaintdata.h"
#include "prpsmap.h"

PrpsDraw::PrpsDraw()
    : PhaseChartDraw()
{
    m_ampSymbol = "=";
}

void PrpsDraw::draw(QPainter *painter, const QPalette &palette)
{
    const PrpsMap *pMap = map();
    if(!pMap)
    {
        return;
    }

    painter->save();

    drawCoordinate(painter, pMap, palette);
    drawAmpText(painter, pMap, palette);
    drawDataItems(painter, pMap);

    painter->restore();
}

void PrpsDraw::drawCoordinate(QPainter *painter, const PrpsMap *pMap, const QPalette &palette) const
{
    painter->setPen(palette.color(QPalette::Text));

    const QList<QLine> &lines = pMap->coordinateLines();

    for(int i = 0; i < lines.size(); ++i)
    {
        bool bAnt = lines[i].p1().x() != lines[i].p2().x() && lines[i].p1().y() != lines[i].p2().y();
        painter->setRenderHint(QPainter::Antialiasing, bAnt);
        painter->drawLine(lines[i]);
    }

    painter->setFont(pMap->textFont());
    const QList<QPair<QString, QRect> >& labels = pMap->labels();
    for(int i = 0; i < labels.size(); ++i)
    {
        painter->drawText(labels[i].second, Qt::AlignCenter, labels[i].first);
    }

    painter->setRenderHint(QPainter::Antialiasing, false);       

    if(!qFuzzyIsNull(threshold()))
    {
        QPen pen;
        pen.setWidth(1);
        pen.setColor(Qt::yellow);
        pen.setStyle(Qt::DashLine);
        painter->setPen(pen);
        QRect rect = pMap->getPhaseAmpCoordinateRect();
        const int yOfThresholdLine = rect.bottom() - (rect.height() - 2) * threshold();
        rect.adjust(1, 1, -1, -1);
        painter->drawLine(QLine(QPoint(rect.left(), yOfThresholdLine), QPoint(rect.right(), yOfThresholdLine)));
    }
}

void PrpsDraw::drawAmpText(QPainter *painter, const PrpsMap *pMap, const QPalette &palette)
{
    const PhasePaintData *pPaintData = paintData();
    if(!pPaintData)
    {
        return;
    }

    painter->setFont(pMap->textFont());
    QString upperText = valueToString(pPaintData->dataUpperBound());
    painter->setPen(palette.color(QPalette::Text));

    QString lowerText = valueToString(pPaintData->dataLowerBound());            

    QString maxText = QString("Max") + m_ampSymbol + valueToString(pPaintData->maxData());

    float fMaxData = pPaintData->maxData();
    float fMax = pPaintData->dataUpperBound();
    float fMin = pPaintData->dataLowerBound();

    qDebug()<<"fMaxData:"<<fMaxData;
    qDebug()<<"fMax:"<<fMax;
    qDebug()<<"fMin:"<<fMin;
    qDebug()<<"m_ampSymbol:"<<m_ampSymbol;

    if(fMaxData > fMax)
    {
        //setAmpSymbol(QString(">"));
        fMaxData = fMax;
    }
    else if(fMaxData < fMin)
    {
        //setAmpSymbol(QString("<"));
        fMaxData = fMin;
    }
    else
    {
        //setAmpSymbol(QString("="));
    }



    if( suffixType() == Phase::SUFFIX_MV )
    {
        QString strUnit;
        QString strMaxDataUnit;

        if( fMax > 1000 )
        {
            fMax = fMax / 1000;
            fMin = fMin / 1000;
            strUnit = "V";
        }
        else
        {
            strUnit = "mV";
        }

//        if( fMaxData > 1000 )
//        {
//            fMaxData = fMaxData / 1000;
//            strMaxDataUnit = "V";
//            maxText = QString("Max") + m_ampSymbol + QString::number(fMaxData, 'f', 2)  + strMaxDataUnit;
//        }
//        else
//        {
//            strMaxDataUnit = "mV";
//            maxText = QString("Max") + m_ampSymbol + QString::number(fMaxData, 'f', 1)  + strMaxDataUnit;
//        }

        if( strUnit ==  "V")
        {
            fMaxData = fMaxData / 1000;
            strMaxDataUnit = "V";
            maxText = QString("Max") + m_ampSymbol + QString::number(fMaxData, 'f', 2)  + strMaxDataUnit;
        }
        else
        {
            strMaxDataUnit = "mV";
            maxText = QString("Max") + m_ampSymbol + QString::number(fMaxData, 'f', 2)  + strMaxDataUnit;
        }

        //量程仅显示整数部分
        upperText = QString::number((int)fMax) + strUnit;
        lowerText = QString::number((int)fMin) + strUnit;

        qDebug()<<"upperText:"<<upperText;
        qDebug()<<"lowerText:"<<lowerText;

    }

    painter->drawText(upperBoundTextGeometry(pMap, upperText), Qt::AlignRight | Qt::AlignVCenter, upperText);
    painter->drawText(lowerBoundTextGeometry(pMap, lowerText), Qt::AlignRight | Qt::AlignVCenter, lowerText);
    painter->drawText(maxValueTextGeometry(pMap), Qt::AlignLeft | Qt::AlignVCenter, maxText);
}

void PrpsDraw::setAmpSymbol(QString strSymbol)
{
    m_ampSymbol = strSymbol;
}

QRect PrpsDraw::upperBoundTextGeometry(const PrpsMap *pMap, const QString &strText) const
{
    QFontMetrics ftMetrics(pMap->textFont());
    int w = ftMetrics.width(strText);
    return QRect(pMap->originPoint().x() - w - 5, pMap->coordinateRect().top(), w + 2, ftMetrics.height());
}

QRect PrpsDraw::lowerBoundTextGeometry(const PrpsMap *pMap, const QString &strText) const
{
    QFontMetrics ftMetrics(pMap->textFont());
    int w = ftMetrics.width(strText);
    return QRect(pMap->originPoint().x() - w - 5, pMap->originPoint().y() - ftMetrics.height(),
                 w + 2, ftMetrics.height());
}

QRect PrpsDraw::maxValueTextGeometry(const PrpsMap *pMap) const
{
    QFontMetrics ftMetrics(pMap->textFont());
    return QRect(pMap->originPoint().x() + 2, pMap->coordinateRect().top() - ftMetrics.height() - 2,
                 pMap->coordinateRect().width() - 20, ftMetrics.height());
}

const PrpsMap* PrpsDraw::map() const
{
    const PrpsMap *pMap = 0;
    if(PhaseChartMap::PrpsMapper2D == chartMap()->type())
    {
        pMap = static_cast<const PrpsMap *>(chartMap());
    }

    return pMap;
}

void PrpsDraw::checkAndResetImage(const PrpsMap *pMap)
{
    QRect r = pMap->boundingRect();

    if(!r.size().isValid())
    {
        qDebug("[PrpsImageDraw::checkAndResetImage] bouding rect size is valid.");
        return;
    }

    if(m_image.size() != r.size())
    {
        m_image = QImage(r.size(), QImage::Format_ARGB32);
    }

    uchar* pData = m_image.bits();
#if (QT_VERSION < QT_VERSION_CHECK(5, 10, 0))
    ::memset(pData, 0, m_image.byteCount());
#else
    ::memset(pData, 0, m_image.sizeInBytes());
#endif
}

void PrpsDraw::drawDataItems(QPainter *painter, const PrpsMap *pMap)
{
    checkAndResetImage(pMap);

    if(m_image.isNull())
    {
        return;
    }

    updateImage(pMap);
    painter->drawImage(pMap->boundingRect().topLeft(), m_image);
}

void PrpsDraw::updateImage(const PrpsMap *pMap)
{
    const PhasePaintData *pPaintData = paintData();
    if(!pPaintData)
    {
        return;
    }

    const int iMaxPeriod = pMap->rowCount();
    const int iPeriodCount = pPaintData->periodCount();
    int iStartPeriod = 0;
    int iStartRow = 0;
    if(iPeriodCount > iMaxPeriod)
    {
        iStartPeriod = iPeriodCount - iMaxPeriod;
    }
    else if(iPeriodCount < iMaxPeriod)
    {
        iStartRow = iMaxPeriod - iPeriodCount;
    }


    QLine line;
    QRgb *pData = reinterpret_cast<QRgb *>(m_image.bits());
    QRgb rgb = qRgb(255, 0, 0);
    const int columnCount = m_image.width();
    int x = 0;
    int y1 = 0, y2 = 0;
    const int bdLeft = pMap->boundingRect().left();
    const int bdTop = pMap->boundingRect().top();

    const int iOffset = phaseOffset() / (360 / pMap->columnCount());
    const float thrld = threshold();
    for(int i = iStartPeriod, iRow = iStartRow; i < iPeriodCount; ++i, ++iRow)
    {
        for(int j = 0; j < pMap->columnCount(); ++j)
        {
            if(pPaintData->percent(i, j) > thrld)
            {
                rgb = pPaintData->color(i, j).rgb();
                line = pMap->getLine(iRow, (j + iOffset) % pMap->columnCount(), pPaintData->percent(i, j));
                x = line.x1();
                y1 = line.y1() < line.y2() ? line.y1() : line.y2();
                y2 = line.y1() > line.y2() ? line.y1() : line.y2();

                int column = x - bdLeft;
                for(int k = y1; k <= y2; ++k)
                {
                    pData[(k - bdTop) * columnCount + column] = rgb;
                    pData[(k - bdTop) * columnCount + column + 1] = rgb;
                    pData[(k - bdTop) * columnCount + column + 2] = rgb;
                }
            }
        }
    }
}
