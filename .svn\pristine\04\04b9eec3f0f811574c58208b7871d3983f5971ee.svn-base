/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* ControlButton.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月7日
* 摘要：控制按钮基类定义

* 当前版本：1.0
*/

#ifndef INFRAREDCONTROLBUTTON_H
#define INFRAREDCONTROLBUTTON_H

#include <QWidget>
#include <QLabel>
#include <QIcon>
#include <QMouseEvent>
#include <QFocusEvent>
#include "DriverDataDefine.h"
#include "infraredcontrolbuttoninfo.h"
#include "popupWidget/PopupWidget.h"
#include "widgetglobal.h"
#include "pixmapLabel.h"

class WIDGET_EXPORT InfraredControlButton : public QFrame
{
    Q_OBJECT
public:
    /*************************************************
    函数名： InfraredControlButton
    输入参数:
        strTitle:标题
        strIcon: 图标路径
        parent: 父控件指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit InfraredControlButton( const QString& strTitle, const QString& strIcon, QWidget *parent = 0 );

    /*************************************************
    函数名： title
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 获取标题
    *************************************************************/
    QString title( void ) const;

    /*************************************************
    函数名： setText
    输入参数:
        strText:标题
    输出参数： NULL
    返回值： NULL
    功能： 设置标题
    *************************************************************/
    void setText( const QString& strText,const QString& strValue = QString::null );

    /*************************************************
    函数名： setIcon
    输入参数:
        strIcon: 图标路径
    输出参数： NULL
    返回值： NULL
    功能： 设置图标
    *************************************************************/
    void setIcon( const QString& strIcon );

    /*************************************************
    函数名： setID
    输入参数:
        ucID: ID
    输出参数： NULL
    返回值： NULL
    功能： 设置ID
    *************************************************************/
    void setID( UINT8 ucID );

    /*************************************************
    函数名： id
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 获取ID
    *************************************************************/
    UINT8 id( void ) const;

    /*************************************************
    函数名： setActive
    输入参数:
        bActive: 激活状态
    输出参数： NULL
    返回值： NULL
    功能： 控制按钮激活
    *************************************************************/
    void setActive( bool bActive );

    void setActiveSize(int activeSize, int noActiveSize);

    /*************************************************
    函数名： isActive
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 返回按钮的激活状态
    *************************************************************/
    bool isActive() const;

    /*******************************
     * 功能：设置按钮使能状态
     * 输入参数：
     *      bEnable：true -- 使能，false -- 不使能
     * *********************************/
    void setEnabled(bool bEnable);

    /*******************************
     * 功能：设置按钮不使能状态
     * 输入参数：
     *      bDisable：true -- 不使能，false -- 使能
     * *********************************/
    void setDisabled(bool bDisable);

    /*************************************************
    函数名： setValue
    输入参数:
        iValue: 当前值
    输出参数： NULL
    返回值： NULL
    功能： 设置值
    *************************************************************/
    virtual void setValue( int iValue );

    /*************************************************
    功能： 获取数值
    返回值：数值
    *************************************************************/
    int value() const;

    /*************************************************
    函数名： onKeyPress
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应按键按下
    *************************************************************/
    virtual void onKeyPress();

    /*************************************************
    函数名： isPopHidden
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 判断弹出窗体是否可见
    *************************************************************/
    bool isPopHidden( void );

    /*************************************************
    函数名： isPopHidden
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置弹出窗体是否可见
    *************************************************************/
    void setPopHidden( bool bHidden );

    /*************************************************
    函数名： setButtonSize
    输入参数: usWidth -- 按键宽度
             usHeight -- 按键高度
    输出参数： NULL
    返回值： NULL
    功能： 设置弹出窗体是否可见
    *************************************************************/
    void setButtonSize( UINT16 usWidth,UINT16 usHeight );

    /*************************************************
    函数名： setPopUpDirection
    输入参数: bIsUp -- true -- 向上
                      false -- 向下
    输出参数： NULL
    返回值： NULL
    功能： 设置弹出窗体的方向
    *************************************************************/
    void setPopUpDirection( bool bIsUp );

    /*************************************************
    功能： 处理鼠标释放事件
    输入参数: pEvent -- 鼠标释放事件
    *************************************************************/
    virtual void processMouseReleaseEvent(QMouseEvent* pEvent);

    void setPopupMode(PopupWidget::PopupMode enMode);

protected:
    /************************************************
     * 函数名   : mouseReleaseEvent
     * 输入参数 :
     *      event -- 事件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 鼠标事件
     ************************************************/
    void mouseReleaseEvent(QMouseEvent *event);

    /************************************************
     * 函数名   : mousePressEvent
     * 输入参数 :
     *      event -- 事件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 鼠标事件
     ************************************************/
    void mousePressEvent(QMouseEvent *event);

    /************************************************
     * 函数名   : setPopupWidget
     * 输入参数 :
     *      pWidget -- 弹出式窗体
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置弹出窗体
     ************************************************/
    void setPopupWidget( PopupWidget* pWidget );

    /*************************************************
    功能： 获取弹出窗体指针
    返回值： 弹出窗体指针
    *************************************************************/
    PopupWidget* popupWidget();

    /*************************************************
    函数名： popup
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 弹出popupWidget
    *************************************************************/
    void popup( void );


private slots:

    /************************************************
     * 功能     : popup编辑窗体提交值
     * 输入参数 :
     *      iValue -- 值
     *      strValue -- 值描述
     ************************************************/
    void onValueSubmit( int iValue, const QString& strValue );

signals:
    void sigPressed(UINT8 id);    // 按钮被点击信号，id:按钮id
    void sigPressed();            // 按钮被点击信号

    /************************************************
     * 功能     : 值发生变化
     * 输入参数 :
     *      id -- 按键ID
     *      iValue -- 值
     ************************************************/
    void sigValueChanged( int id, int iValue );

    /************************************************
     * 功能：显示模式改变信号
     * 输入参数：
     *        enMode：显示模式
     ************************************************/
    void sigPopupModeChanged(PopupWidget::PopupMode enMode);

private:

    pixmapLabel *m_pIconLabel; //菜单的icon label

    UINT8 m_ucID;//ID标识
    bool m_bActive;//激活状态
    QString m_strTitle;//描述
    QString m_strIcon;//图标路径
    PopupWidget* m_pPopupWidget;//弹出式窗体

    INT32 m_iButtonWidth;
    INT32 m_iButtonHeight;
    INT32 m_iLabelHeight;
    INT32 m_iActiveSize;

    bool m_bIsPopUpDirectionUp;
    int m_iValue;//值
};

#endif // INFRAREDCONTROLBUTTON_H

