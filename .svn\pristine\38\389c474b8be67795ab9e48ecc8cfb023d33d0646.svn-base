/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* Clock.cpp
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月17日
* 摘要：时钟控件定义

* 当前版本：1.0
*/

#include <QTime>
#include <QTimer>
#include <QTimeEdit>
#include <QDateTime>
#include <QVBoxLayout>
#include <QMainWindow>
#include <QDebug>
#include "Clock.h"

//const int CLOCK_WINDOW_WIDTH = 200;
//const int CLOCK_WINDOW_HEIGHT = 200;

const int CLOCK_WINDOW_WIDTH = 480;
const int CLOCK_WINDOW_HEIGHT = 480;
const int CLOCK_LINE = 20;

//时针三个点的坐标
static const QPoint hourHand[3] = {
    QPoint(14, 16),
    QPoint(-14, 16),
    QPoint(0, -60)
};

//分针三个点的坐标
static const QPoint minuteHand[3] = {
    QPoint(14, 16),
    QPoint(-14, 16),
    QPoint(0, -100)
};

//秒针三个点的坐标
static const QPoint secondHand[3] = {
    QPoint(8, 16),
    QPoint(-8, 16),
    QPoint(0, -160)
};

/*************************************************
函数名： Clock
输入参数:
    parent:父控件
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
Clock::Clock(QWidget *parent) :QWidget(parent)
{
    QTime time = QTime::currentTime();
    m_iHour = time.hour();
    m_iMinute = time.minute();
    m_iSecond = time.second();

    m_Painter = new QPainter;

    setFixedSize( CLOCK_WINDOW_WIDTH,CLOCK_WINDOW_HEIGHT );

    update();
}

/*************************************************
函数名： ~Clock
输入参数: NULL
输出参数： NULL
返回值： NULL
功能： 析构
*************************************************************/
Clock::~Clock( )
{
    if( m_Painter != NULL )
    {
        delete m_Painter;
        m_Painter = NULL;
    }
}

/*************************************************
函数名： drawClock
输入参数: NULL
输出参数： NULL
返回值： NULL
功能： 画时、分、秒的线条
*************************************************************/
void Clock::drawClock(void)
{

//    dbg_info("");

    m_Painter->setRenderHints(QPainter::Antialiasing);     // 打开抗锯齿
    m_Painter->translate(CLOCK_WINDOW_WIDTH / 2,CLOCK_WINDOW_HEIGHT / 2);

    m_Painter->setPen( Qt::gray );
    m_Painter->setBrush( QBrush( QColor( 216,216,216 ) ) );
    m_Painter->drawEllipse( QPoint(0,0),185,185 );

    QRadialGradient radialGrad(QPointF(0,0), 180);
    radialGrad.setColorAt(0, Qt::white);
    radialGrad.setColorAt(1, QColor(181, 210, 236));
    m_Painter->setPen( Qt::NoPen );
    m_Painter->setBrush( QBrush( radialGrad ) );
    m_Painter->drawEllipse( QPoint(0,0),180,180 );

    m_Painter->setBrush(Qt::red);
    m_Painter->setPen(Qt::red);
    m_Painter->save();    // save the x y
    float fDegree = 6.0*(float)m_iSecond;
    m_Painter->rotate(fDegree);
//    dbg_info("rotate degree of second is %f\n", fDegree);
    m_Painter->drawConvexPolygon(secondHand,3);   // 绘制一个多边形
    m_Painter->restore();

    m_Painter->setBrush(Qt::blue);
    m_Painter->setPen(Qt::blue);
    m_Painter->save();
    fDegree = 6.0*((float)m_iMinute+(float)m_iSecond/60.0);
    m_Painter->rotate(fDegree);
//    dbg_info("rotate degree of minute is %f\n", fDegree);
    m_Painter->drawConvexPolygon(minuteHand,3);   // 绘制一个多边形
    m_Painter->restore();

    m_Painter->setBrush(Qt::black);
    m_Painter->setPen(Qt::black);
    m_Painter->save();
    fDegree = 30.0*((float)m_iHour+ (float)m_iMinute/60.0);
    m_Painter->rotate(fDegree);
//    dbg_info("rotate degree of hour is %f\n", fDegree);
    m_Painter->drawConvexPolygon(hourHand,3);   // 绘制一个多边形
    m_Painter->restore();

    m_Painter->setPen( QPen( QColor( 0,128,128 ),2,Qt::SolidLine ) );
    for(int i = 0; i < 12; i ++)                // 绘制表盘刻度
    {
        m_Painter->drawLine(QPoint(0,-160),QPoint(0,-180));
        m_Painter->rotate(30);
    }
    m_Painter->setRenderHints(QPainter::Antialiasing,false);     // 关闭抗锯齿
}

/*************************************************
函数名： setTimeInfo
输入参数:
    iHour： 时
    iMinute: 分
    iSecond: 秒
输出参数： NULL
返回值： NULL
功能： 设置时间数据,根据设置的数据画出时分秒线条
*************************************************************/
void Clock::setTimeInfo(int iHour, int iMinute, int iSecond)
{
    m_iHour = iHour;
    m_iMinute = iMinute;
    m_iSecond = iSecond;

    update();
}

/*************************************************
函数名： paintEvent
输入参数:
    pEvent: 绘图事件
输出参数： NULL
返回值： NULL
功能： 绘制时钟图形事件处理函数
*************************************************************/
void Clock::paintEvent(QPaintEvent *pEvent)
{
    Q_UNUSED(pEvent)
//    dbg_info("");
    m_Painter->begin(this);
    drawClock();
    m_Painter->end();
}
