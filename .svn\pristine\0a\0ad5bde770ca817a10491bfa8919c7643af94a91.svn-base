#include "aeamptask.h"
#include "ae/AEAmpService.h"
#include "config/ConfigManager.h"
#include "ae/AEConfig.h"
#include "appconfig.h"
#include "ae/aeampdatamap.h"
#include "peripheral/peripheralservice.h"

#define AE_PROPERTY_AMP_RANGE_MIN 0;
#define AE_PROPERTY_AMP_RANGE_MAX 1000;
const UINT8 AE_PROPERTY_DATA_POINT_CNT = 4;

AEAmpTask::AEAmpTask(QObject *parent) : AbstractSpectrumTask(parent)
{
    m_pAEService = AEAmpService::instance();
}

AEAmpTask::~AEAmpTask()
{
     m_pAEService->deleteUser( m_userId );
}

void AEAmpTask::startSample()
{
    PeripheralService *pPeripherService = PeripheralService::instance();
    pPeripherService->openAEPower();

    //注册用户
    MultiServiceNS::SampleUser userInfo;
    userInfo.eSampleType = MultiServiceNS::SPECTRUM_AE_AMP;
    m_userId = m_pAEService->addUser( userInfo );
    //设置参数
    setAEAmpParameters();
    connect( m_pAEService, SIGNAL(sigData(AE::AmplitudeData,MultiServiceNS::USERID)),
             this, SLOT(onDataRead(AE::AmplitudeData,MultiServiceNS::USERID)) );
    connect( m_pAEService, SIGNAL(sigReadAEAmpDataFailed(MultiServiceNS::USERID)),
             this, SLOT(onReadDataFailed(MultiServiceNS::USERID)) );
    m_pAEService->startSample( m_userId );
    //m_pAEService->startExclusiveSample( m_userId );
}

void AEAmpTask::onDataRead(AE::AmplitudeData data, MultiServiceNS::USERID ucID )
{
    if( ucID == m_userId )
    {
        //设置数据文件里图谱数据
        m_stData = data;

        fulfillTask();
    }
}

void AEAmpTask::onReadDataFailed(MultiServiceNS::USERID  ucID)
{
    if( ucID == m_userId )
    {
        fulfillTask();
    }
}

void AEAmpTask::saveMapData()
{
    //创建图谱保存对象
    AEAmpDataMap * pMap = new AEAmpDataMap;

    //设置头部信息
    pMap->setSpectrumProperty(DataFileNS::PROPERTY_TEST);

    //设置ext信息
    fillAEAmpInfo( pMap );

    //设置数据内容
    fillAEAmpData( pMap );

    emit sigSampleFinished( pMap );
}

void AEAmpTask::fulfillTask()
{
    //停止采样
    m_pAEService->stopSample( m_userId );
    //m_pAEService->stopExclusiveSample( m_userId );

    saveMapData();

    PeripheralService *pPeripherService = PeripheralService::instance();
    pPeripherService->closeAEPower();
}

void AEAmpTask::fillAEAmpInfo(AEAmpDataMap *pMap )
{
    pMap->setDataPrimitiveType( DataFileNS::DATA_TYPE_FLOAT );
    pMap->setTestChannelSign( AE::CHANNEL_DEFAULT );

    AEMapNS::_AEAmpBinaryMapInfo mapInfo;
    if(m_eUnit == AE::UNIT_DB)
    {
        mapInfo.eAmpUnit = DataFileNS::AMP_UNIT_DB;
        mapInfo.fPeakMin = AE::AMPLITUDE_MIN_VALUES[m_eUnit][m_eGain][AE::COMPONENT_EFFECTIVE];
        mapInfo.fPeakMax = AE::AMPLITUDE_MAX_VALUES[m_eUnit][m_eGain][AE::COMPONENT_EFFECTIVE];
    }
    else
    {
        mapInfo.eAmpUnit = DataFileNS::AMP_UNIT_mV;
        mapInfo.fPeakMin = AE_PROPERTY_AMP_RANGE_MIN;
        mapInfo.fPeakMax = AE_PROPERTY_AMP_RANGE_MAX;
    }
    mapInfo.eTransformerType = AEMapNS::AE_TRANSFORMER_AIR;
    mapInfo.iDataPointNum = AE_PROPERTY_DATA_POINT_CNT;

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    UINT32 uiFreq = (Frequency) pConfig->value( APPConfig::KEY_SYS_FREQ ).toUInt();
    pConfig->endGroup();
    mapInfo.fTestFreq = uiFreq; //todo 试验频率??

    memset(mapInfo.ucaDischargeTypeProb, 0, sizeof(mapInfo.ucaDischargeTypeProb));
    //mapInfo.fTriggerThreshold = 0;
    //mapInfo.sOpenTime = 0;        //幅值图谱不需要该字段，值可以为空
    //mapInfo.sShutTime = 0;        //幅值图谱不需要该字段，值可以为空
    //mapInfo.sMaxTimeInterval = 0; //幅值图谱不需要该字段，值可以为空
    //mapInfo.cGain = 0;
	//mapInfo.eSyncSource = 0;
	//mapInfo.ucSyncState = 0;
    
    pMap->setBinaryInfo( &mapInfo );
}

void AEAmpTask::fillAEAmpData( AEAmpDataMap *pMap )
{
    AEMapNS::AEAmpBinaryData pData;
    pData.fRms = m_stData.fPeakValue;
    pData.fPeak = m_stData.fRMS;
    pData.fFrequency1 = m_stData.fFirstFreqComValue;
    pData.fFrequency2 = m_stData.fSecondFreqComValue;
    pMap->setBinaryData(&pData,AE_PROPERTY_DATA_POINT_CNT);
}

void AEAmpTask::setAEAmpParameters()
{
    //读取采样参数
    int iGroup = AE::GROUP_AE_AMPLITUDE;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_AE );
    m_eGain = (AE::GainType)pConfig->value( AE::KEY_GAIN ).toUInt();
    m_eTriggerValue = (AE::TriggerValue)pConfig->value( AE::KEY_TRIGGER_VALUE, iGroup ).toUInt();
    m_eUnit = (AE::UnitOption)pConfig->value( AE::KEY_UNIT, iGroup ).toUInt();
    m_usSpectrum = pConfig->value( AE::KEY_FREQ_COMPONENT, iGroup ).toUInt();
    pConfig->endGroup();

    //eTriggerValue = AE::TRIGGER_LEVEL_0;

    //设置采样参数
    m_pAEService->transaction();
    m_pAEService->setWorkMode( AE::MODE_AMPLITUDE );
    m_pAEService->setGain( m_eGain );
    m_pAEService->setTriggerValue( m_eTriggerValue );
    m_pAEService->setUnit( m_eUnit );
    m_pAEService->setSpectrum( m_usSpectrum );
    m_pAEService->setSyncSource( Module::SYNC_SOURCE_DEFAULT );
    m_pAEService->commit();
}
