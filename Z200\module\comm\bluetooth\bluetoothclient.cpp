#include <unistd.h>
#include <QVector>
#include <QEventLoop>
#include <QTimer>
#include <QCoreApplication>
#include <QDebug>
#include "bluetoothclient.h"
#include "datadefine.h"
#include "log/log.h"


/*************************************************
函数名： BluetoothClient)
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
BluetoothClient::BluetoothClient()
{
    m_bScan = false;
}

/*************************************************
函数名： ~BluetoothClient()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
BluetoothClient::~BluetoothClient()
{
    close();
}

/*************************************************
函数名： open()
输入参数： NULL
输出参数： NULL
返回值： 操作结果--true: 打开成功; false: 打开失败
功能： 开启本地蓝牙适配器
*************************************************************/
bool BluetoothClient::open()
{
    infoLog() << "Start running the open function.";
    if ( m_bOpen )
    {
        logWarning("BluetoothClient has already been opened.");
        return true;
    }

    m_bOpen = initDevice();

    if(m_bOpen){
        m_strDevMac.clear();
    }
    logInfo(QString("bluetooth has been opened, ret: %1.").arg(m_bOpen).toLatin1().data());
    return m_bOpen;
}

/*************************************************
函数名： close()
输入参数： NULL
输出参数： NULL
返回值： 操作结果--true: 关闭成功; false: 关闭失败
功能： 关闭本地蓝牙适配器
*************************************************************/
bool BluetoothClient::close()
{
    infoLog() << "Start running the close function.";
    if ( !m_bOpen )
    {
        logWarning("Bluetooth has already been closed.");
        return true;
    }

    bool bRes = true;

    //关闭连接端口
    if ( m_nPortHandle > 0 )
    {
        logInfo("begin to closePort...");
        closePort(m_nPortHandle);
        m_nPortHandle = 0;
        m_strDevMac.clear();
    }

    bRes = Bluetooth::close();
    logInfo(QString("bluetooth has been closed, ret: %1.").arg(bRes).toLatin1().data());
    return bRes;
}

/*************************************************
函数名： restart()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 重启蓝牙
*************************************************************/
void BluetoothClient::restart()
{
    infoLog() << "Start running the restart function.";
    close();
    open();
    return;
}

int BluetoothClient::closePort()
{
    int ret = -1;

    if (m_nPortHandle > 0)
    {
        logInfo("Closing port...");

        ret = closePort(m_nPortHandle);
        if (ret == 0)
        {
            m_nPortHandle = 0;
            m_strDevMac.clear();
            //emit sigConnectState(false);
        }
        else
        {
            logError("Failed to close port.");
        }
    }
    else
    {
        logWarning("Port handle is not valid.");
    }

    return ret;
}

/*************************************************
函数名: scanBluetoothDevice
输入参数: void
输出参数: NULL
返回值: 扫描到的设备的mac地址列表
功能: 扫描蓝牙外设
*************************************************************/
QVector<DeviceInfo> BluetoothClient::scanBluetoothDevice()
{
    infoLog() << "Start running the scanBluetoothDevice function.";
    QMutexLocker autolock(&m_mutex);
    QVector<DeviceInfo> vecRes;
    vecRes.clear();
    if (!m_bScan)
    {
        if (0 != startDeviceDiscovery(ScanTimeOut))
        {
            logError("start device discovery failed, begin to restart bluetooth...");
            restart();
            if (0 != startDeviceDiscovery(ScanTimeOut))
            {
                logError("start device discovery failed again.");
                return vecRes;
            }
        }

        m_bScan = true;
        m_destDevInfo.clear();

        if( m_pSemScan->available() > 0 )
        {
            m_pSemScan->acquire(m_pSemScan->available());
        }
        else
        {
            logDebug("m_pSemScan->available() <= 0");
        }

        logInfo("waiting for bluetooth scan result...");
        if (!m_pSemScan->tryAcquire(1, (ScanTimeOut + 3) * 1000))
        {
            logWarning("m_pSemScan->tryAcquire timeout.");
        }
        else
        {
            logWarning("m_pSemScan->tryAcquire ret true.");
        }

        logInfo("bluetooth scan finished.");
        if (m_bScan)
        {
            logDebug("begin to stop device discovery.");
            stopDeviceDiscovery();
        }

        logInfo(QString("found %1 devices.").arg(m_destDevInfo.size()).toLatin1().data());
    }
    else
    {
        logWarning("scan bluetooth device is already start.");
    }

    foreach (const DeviceInfo &dev, m_destDevInfo)
    {
        logDebug(QString("bluetooth device name: %1.").arg(dev.strName).toLatin1().data());
        vecRes.append(dev);
    }

    return vecRes;
}

/*************************************************
函数名： doConnectToDevice
输入参数： strDevMac--远端蓝牙mac地址
         isAutoConnect--是否为自动连接尝试
输出参数： NULL
返回值： 连接状态--true:成功; false:失败
功能: 执行设备连接的核心逻辑，供public方法调用
*************************************************************/
bool BluetoothClient::doConnectToDevice(const QString &strDevMac, bool isAutoConnect)
{
    infoLog() << (isAutoConnect ? "Auto connecting" : "Connecting") << " to device: " << strDevMac;
    
    // 1. 停止任何正在进行的设备扫描
    if (m_bScan)
    {
        stopDeviceDiscovery();
        usleep(1000000);
    }

    // 2. 确保在设备列表中
    QMutexLocker autolock(&m_mutex);
    if (!m_destDevInfo.contains(strDevMac))
    {
        logError(QString("m_destDevInfo has no device of: %1.").arg(strDevMac).toLatin1().data());
        return false;
    }

    // 3. 重置连接状态
    m_bConnect = false;

    // 4. 配对过程
    int nRes = pairWithRemoteDev(strDevMac);
    if (nRes == 0)
    {
        // 配对操作已启动，等待配对完成
        infoLog() << "Pairing operation started, waiting for completion...";
        if (!getPairState(strDevMac))
        {
            logError("Pairing verification failed - device may not be in pairing mode or pairing was rejected");

            // 配对失败时，尝试查询设备当前状态
            DEVM_Remote_Device_Properties_t devInfo;
            if (queryRemoteDevInfo(devInfo, strDevMac, true) == 0)
            {
                bool currentlyPaired = (devInfo.RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_CURRENTLY_PAIRED);
                infoLog() << "Device current pairing state after query: " << (currentlyPaired ? "paired" : "not paired");

                if (currentlyPaired)
                {
                    // 设备实际上已经配对，更新本地状态
                    m_destDevInfo[strDevMac].bPaired = true;
                    infoLog() << "Updated local pairing state to paired";
                }
                else
                {
                    return false;
                }
            }
            else
            {
                return false;
            }
        }
        else
        {
            infoLog() << "Pairing verification successful";
        }
    }
    else if (nRes < 0)
    {
        logError("Failed to pair with remote device");
        return false;
    }
    else
    {
        // nRes > 0 表示设备已经配对
        infoLog() << "Device already paired, proceeding with connection";
    }

    // 5. 连接过程（最多尝试2次）
    int nFailTime = 0;
    while (true)
    {
        logDebug(QString("connect time: %1.").arg(nFailTime + 1).toLatin1().data());

        // 5.1 获取服务端的端口
        m_nSevrPort = 0;
        queryRemoteDevServices(strDevMac);
        if (m_nSevrPort == 0)
        {
            logError("Unknown device or no valid service port");
            return false;
        }

        logInfo(QString("server port: %1.").arg(m_nSevrPort).toLatin1().data());

        // 5.2 尝试打开远程端口并进行连接
        if (0 == openRemotePort(strDevMac, m_nSevrPort, m_nPortHandle))
        {
            // 5.3 验证连接状态
            if (!getConnectState(strDevMac))
            {
                logError("Failed to open port");
                ++nFailTime;

                // 5.4 连接失败时的处理策略
                if (1 == nFailTime)
                {
                    // 第一次失败：尝试重新配对
                    infoLog() << "First connection attempt failed, trying to re-pair";
                    unPairRemoteDev(strDevMac);

                    // 等待一段时间确保取消配对操作完成
                    usleep(200000); // 等待200ms

                    int nRes = pairWithRemoteDev(strDevMac);
                    if (nRes == 0)
                    {
                        if (!getPairState(strDevMac))
                        {
                            logError("Re-pairing verification failed");
                            return false;
                        }
                        infoLog() << "Re-pairing successful, retrying connection";
                    }
                    else if (nRes < 0)
                    {
                        logError("Re-pairing failed");
                        return false;
                    }
                    else
                    {
                        // nRes > 0 表示已经配对，继续尝试连接
                        infoLog() << "Device already paired, retrying connection";
                    }
                }
            }
            else
            {
                // 5.5 非自动连接模式下进行连接验证
                if (!isAutoConnect)
                {
                    // 连接成功后，发送连接验证数据包
                    logInfo("Performing connection verification...");
                    
                    // 发送一个验证连接的测试数据包
                    char testData[] = {0x01, 0x02, 0x03, 0x04}; // 测试数据
                    int bytesWritten = writeData(m_nPortHandle, testData, 4);
                    
                    if (bytesWritten != 4)
                    {
                        logWarning("Connection verification failed: could not send test data");
                        ++nFailTime;
                        continue;
                    }
                    
                    // 检查连接状态
                    if (!checkConnectionState())
                    {
                        logWarning("Connection verification failed: connection lost after test data");
                        ++nFailTime;
                        continue;
                    }
                    
                    logInfo("Connection verification successful");
                }
                
                // 连接成功，退出循环
                break;
            }
        }
        else
        {
            ++nFailTime;
        }

        // 5.6 达到最大尝试次数，返回失败
        if (nFailTime == 2)
        {
            logError("Failed to open port after 2 attempts");
            return false;
        }
    }

    // 6. 保存设备MAC地址
    m_strDevMac = strDevMac;

    // 7. 设置缓冲区大小
    changeBufferSize(m_nPortHandle, RecvBuffSize, TransBuffSize);
    
    // 8. 更新连接状态
    m_bConnect = true;
    
    return true;
}

/*************************************************
函数名: connectToRemoteDev
输入参数: strDeviceMAC--远端蓝牙mac地址
输出参数: NULL
返回值: 连接状态--true:成功; false:失败
功能: 连接远端蓝牙设备
*************************************************************/
bool BluetoothClient::connectToRemoteDev(const QString &strDevMac)
{
    infoLog() << "Start running the connectToRemoteDev function. strDevMac:" << strDevMac;

    // 1. 保存MAC地址
    m_strDevMac = strDevMac;

    // 2. 轻量级状态重置，避免干扰连接过程
    infoLog() << "Performing lightweight state reset";

    // 只重置必要的状态，不调用可能干扰连接的方法
    m_bConnect = false;
    if (m_nPortHandle > 0) {
        closePort(m_nPortHandle);
        m_nPortHandle = 0;
    }

    // 3. 主动清理配对状态以避免配对冲突
    infoLog() << "Clearing pairing state to avoid conflicts";
    int unpairResult = unPairRemoteDev(strDevMac);
    if (unpairResult == 0) {
        infoLog() << "Successfully unpaired device, will re-pair during connection";
    } else {
        infoLog() << "Device was not paired or unpair failed, proceeding with connection";
    }

    // 短暂等待确保取消配对操作完成
    usleep(300000); // 等待300ms

    // 4. 等待状态重置完成
    infoLog() << "Connection state reset completed, proceeding with connection attempt";

    // 5. 调用核心连接方法，标记为非自动连接
    return doConnectToDevice(strDevMac, false);
}

bool BluetoothClient::autoConnectToRemoteDev()
{
    infoLog() << "Start running the autoConnectToRemoteDev function. strDevMac:" << m_strDevMac;
    
    // 检查是否有可连接的设备MAC地址
    if (m_strDevMac.isEmpty())
    {
        logError("No device MAC address available for auto-reconnect");
        return false;
    }
    
    // 调用核心连接方法，标记为自动连接
    return doConnectToDevice(m_strDevMac, true);
}

/*************************************************
函数名: disconnectFromRemoteDev
输入参数: strMac--远端蓝牙mac地址
输出参数: NULL
返回值: 结果状态--成功返回0
功能: 与远端蓝牙设备断开连接
*************************************************************/
int BluetoothClient::disconnectFromRemoteDev()
{
    infoLog() << "Disconnecting from remote Bluetooth device";
    
    if (!m_bConnect) {
        infoLog() << "No active connection to disconnect";
        return 0;
    }
    
    // 使用蓝牙协议断开连接但不关闭端口
    // 例如使用断开ACL连接或HCI断开命令
    int result = disconnectDeviceOnly();
    
    if (result == 0) {
        // 更新连接状态
        m_bConnect = false;
        // 保持端口处于打开状态，只是没有活动连接
        
        // 通知状态变化
        emitConnectState(false);
        infoLog() << "Successfully disconnected from device while keeping port open";
    } else {
        errorLog() << "Failed to disconnect device, error: " << result;
    }
    
    return result;
}

/*************************************************
函数名: disconnectDeviceOnly
输入参数: 无
输出参数: 无
返回值: 结果状态--成功返回0，失败返回错误码
功能: 与远端蓝牙设备断开连接但不关闭端口
*************************************************************/
int BluetoothClient::disconnectDeviceOnly()
{
    infoLog() << "Disconnecting from remote Bluetooth device without closing port";

    if (!m_bConnect)
    {
        infoLog() << "No active connection to disconnect";
        return 0;
    }

    // 调用基类实现
    int result = Bluetooth::disconnectDeviceOnly();

    if (result == 0)
    {
        // 更新连接状态但不清除端口句柄
        m_bConnect = false;

        // 通知状态变化
        emitConnectState(false);
        infoLog() << "Successfully disconnected from device while keeping port open";
    }
    else
    {
        errorLog() << "Failed to disconnect device, error: " << result;
    }
    // Initialize device after disconnection
    if (Bluetooth::initDevice())
    {
        infoLog() << "Device initialization successful";
    }
    else 
    {
        warningLog() << "Device initialization failed";
    }
    return result;
}

/*************************************************
函数名： openRemotePort
输入参数： strDevMAC--远端设备MAC；port--服务器端口；
输出参数：portHandle--读写句柄
返回值： int--成功返回0
功能： 打开端口，准备连接
*************************************************************/
int BluetoothClient::openRemotePort(const QString &strDevMAC, int port, int &portHandle)
{
    infoLog() << "Start running the openRemotePort function.strDevMAC:"<<strDevMAC<<",port:"<<port<<",portHandle:"<<portHandle;
    int res=0;
    if (!m_bInitialized)
    {
        warningLog() << "Error: Platform Manager has not been initialized.";
        return -1;
    }

    BD_ADDR_t bdAddr;
    char *cDevMac = strDevMAC.toLocal8Bit().data();
    StrToBD_ADDR(cDevMac, &bdAddr);

#ifdef Q_WS_QWS
    uint flags = 0;  // 先写死

    infoLog() << "Opening remote port: " << port;

    // 打开远程端口
    int btRes = SPPM_OpenRemotePort(bdAddr, port, flags, SPPM_Event_Callback, this, NULL);

    if (btRes > 0)
    {
        infoLog() << "SPPM_OpenRemotePort(" << port << ") Success. Port Handle: " << btRes;
        portHandle = btRes;
        res = 0;
    }
    else
    {
        warningLog() << "Error: Failed to open remote port (" << port << "). Error code: " << btRes << ", " << ERR_ConvertErrorCodeToString(btRes);
        res = -1;
    }
#else
    Q_UNUSED(bdAddr)
    Q_UNUSED(port)
    warningLog() << "Error: Unsupported platform.";
    res = -1;
#endif
    return res;
}

/*************************************************
函数名: startDeviceDiscovery
输入参数: timeout--扫描时长
输出参数: NULL
返回值: 状态--0表示启动扫描成功，否则表示失败
功能: 扫描蓝牙外设（异步接口，发送sigDeviceFound信号）
*************************************************************/
int BluetoothClient::startDeviceDiscovery(uint timeout)
{
    int res=0, btRes=0;
    infoLog() << "Start running the startDeviceDiscovery function.";
    if ( m_bInitialized )
    {
#ifdef Q_WS_QWS
        if ( (btRes = DEVM_StartDeviceDiscovery(timeout)) >= 0)
        {
            infoLog()<<"DEVM_StartDeviceDiscovery() Success:" << btRes;
            res = 0;
        }
        else
        {
            errorLog()<<"DEVM_StartDeviceDiscovery() Failure: "<< btRes<<","<< ERR_ConvertErrorCodeToString(btRes);
            res = -1;
        }
#else
        Q_UNUSED(timeout)
        Q_UNUSED(btRes)
        logWarning() << "Error: Unsupported platform.";
#endif
    }
    else
    {
        logWarning("Platform Manager has not been initialized.");
        res = -1;
    }

    return res;
}

/*************************************************
函数名： stopDeviceDiscovery
输入参数： NULL
输出参数： NULL
返回值： int--成功返回0
功能： 停止搜索设备
*************************************************************/
int BluetoothClient::stopDeviceDiscovery()
{
    // 输出调试信息
    infoLog() << "Start running the stopDeviceDiscovery function.";

    int ret_val = -1;

#ifdef Q_WS_QWS
    // 检查平台管理器初始化状态
    if (!m_bInitialized)
    {
        // 平台管理器未初始化
        logWarning("Error: Platform Manager has not been initialized.");
        return ret_val;
    }

    // 调用 DEVM_StopDeviceDiscovery() 停止设备发现
    int Result = DEVM_StopDeviceDiscovery();

    // 检查停止设备发现的结果
    if (Result >= 0)
    {
        // 输出成功信息
        infoLog() << "DEVM_StopDeviceDiscovery() Success: " << Result << ".";
        ret_val = 0;
    }
    else
    {
        // 输出失败信息
        logInfo(QString("Error: Failed to stop device discovery. Error code: %1, %2.").arg(Result).arg(ERR_ConvertErrorCodeToString(Result)));
    }
#else
    logWarning() << "Error: Unsupported platform.";
#endif

    // 返回结果值
    return ret_val;
}

/*************************************************
函数名： closePort
输入参数： port--端口；timeOut--超时时间
输出参数： NULL
返回值： int--成功返回0
功能：关闭连接
*************************************************************/
int BluetoothClient::closePort(uint portHandle, uint timeOut)
{
    infoLog() << "Start running the closePort function.portHandle:"<<portHandle;
    if (!m_bInitialized)
    {
        warningLog() << "Error: Platform Manager has not been initialized.";
        return -1;
    }
    int res=0, btRes=0;

    if ( m_bInitialized )
    {
#ifdef Q_WS_QWS
        if ((btRes = SPPM_ClosePort(portHandle, timeOut)) == 0)
        {
            logWarning("SPPM_ClosePort Success.");
            res = 0;
        }
        else
        {
            infoLog()<<"SPPM_ClosePort Failure: " << btRes<<", "<<ERR_ConvertErrorCodeToString(btRes);
            res = -1;
        }
#else
        Q_UNUSED(portHandle)
        Q_UNUSED(timeOut)
        Q_UNUSED(btRes)
#endif
    }
    else
    {
        logWarning("Platform Manager has not been initialized.");
        res = -1;
    }

    return res;
}

/*************************************************
函数名： openServicePort
输入参数： port--端口；timeOut--超时时间
输出参数： NULL
返回值： int--成功返回0
功能：打开服务端蓝牙连接端口服务
*************************************************************/
bool BluetoothClient::openServicePort()
{
    infoLog() << "Start running the openServicePort function.";

    if (!findFreeServerPort())
    {
        warningLog() << "Failed to find free server port.";
        return false;
    }

    infoLog() << "Found free server port.";

    unsigned int serverPort = 22;
    if (!registerServerPort(serverPort))
    {
        warningLog() << "Failed to register server port.";
        return false;
    }

    infoLog() << "Registered server port.";

    if (!queryServerPresent(serverPort))
    {
        warningLog() << "Failed to query server present.";
        return false;
    }

    infoLog() << "Server present.";

    return true;
}


/*************************************************
函数名： emitConnectState
输入参数： bFlag--连接状态
输出参数： NULL
返回值： NULL
功能： 发送蓝牙连接状态信号
*************************************************************/
//void BluetoothClient::emitConnectState(bool bFlag)
//{
//    m_bConnect = bFlag;
//    emit sigConnectState(bFlag);
//}

/*************************************************
函数名： updateRemoteDeviceInfos
输入参数： UpdateMask--更新标识;
         RemoteDeviceProperties--远端设备信息
输出参数： NULL
返回值： NULL
功能： 更新远端设备更新信息
*************************************************************/
void BluetoothClient::updateRemoteDeviceInfos(unsigned long UpdateMask, DEVM_Remote_Device_Properties_t *RemoteDeviceProperties)
{
    debugLog() << "Start running the updateRemoteDeviceInfos function.";

    char Buffer[64];

    if(RemoteDeviceProperties)
    {
        /* First, display any information that is not part of any update  */
        /* mask.                                                          */
        BD_ADDRToStr(RemoteDeviceProperties->BD_ADDR, Buffer);

        debugLog() << "BD_ADDR:" << Buffer;

        QString strMac = QString::fromLocal8Bit(Buffer);
        bool bFlag = false;
        if (!m_destDevInfo.contains(strMac))
        {
            DeviceInfo devInfo;
            devInfo.strMac = strMac;
            m_destDevInfo[strMac] = devInfo;
            setMac(strMac);
        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_CLASS_OF_DEVICE))
            debugLog() << "COD:" << QString("0x%02X%02X%02X").arg(RemoteDeviceProperties->ClassOfDevice.Class_of_Device0, 2, 16, QChar('0'))
                                                      .arg(RemoteDeviceProperties->ClassOfDevice.Class_of_Device1, 2, 16, QChar('0'))
                                                      .arg(RemoteDeviceProperties->ClassOfDevice.Class_of_Device2, 2, 16, QChar('0'));

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_DEVICE_NAME))
        {
            bool bFlag = (RemoteDeviceProperties->DeviceNameLength);
            debugLog() << "Device Name(" << RemoteDeviceProperties->DeviceNameLength << "):" << (bFlag ? RemoteDeviceProperties->DeviceName : "");
            QString strTmp = QString::fromLocal8Bit(RemoteDeviceProperties->DeviceName);
            debugLog() << strTmp;
            m_destDevInfo[strMac].strName = bFlag ? QString(RemoteDeviceProperties->DeviceName) : "unknown name";
        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_DEVICE_FLAGS))
            debugLog() << "Device Flags:" << QString("0x%08lX").arg(RemoteDeviceProperties->RemoteDeviceFlags);

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_RSSI))
            debugLog() << "RSSI:" << RemoteDeviceProperties->RSSI;

        if((!UpdateMask) && (RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_CURRENTLY_CONNECTED))
            debugLog() << "Trans. Power:" << RemoteDeviceProperties->TransmitPower;

        if((!UpdateMask) || ((UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_APPLICATION_DATA) && (RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_APPLICATION_DATA_VALID)))
        {
            debugLog() << "Friendly Name:" << (RemoteDeviceProperties->ApplicationData.FriendlyNameLength ? RemoteDeviceProperties->ApplicationData.FriendlyName : "");
            debugLog() << "App. Info:" << QString("%08lX").arg(RemoteDeviceProperties->ApplicationData.ApplicationInfo);
        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_PAIRING_STATE))
        {
            bFlag = (RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_CURRENTLY_PAIRED);
            debugLog() << "Paired State:" << (bFlag ? "TRUE" : "FALSE");

            m_destDevInfo[strMac].bPaired = bFlag;
        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_CONNECTION_STATE))
        {
            bFlag = (RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_CURRENTLY_CONNECTED);
            debugLog() << "Connect State:" << (bFlag ? "TRUE" : "FALSE");
            m_destDevInfo[strMac].bConnected = bFlag;

        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_ENCRYPTION_STATE))
            debugLog() << "Encrypt State:" << ((RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_LINK_CURRENTLY_ENCRYPTED) ? "TRUE" : "FALSE");

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_SNIFF_STATE))
        {
            if(RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_LINK_CURRENTLY_SNIFF_MODE)
                debugLog() << "Sniff State: TRUE (" << RemoteDeviceProperties->SniffInterval << " ms)";
            else
                debugLog() << "Sniff State: FALSE";
        }

        if((!UpdateMask) || (UpdateMask & DEVM_REMOTE_DEVICE_PROPERTIES_CHANGED_SERVICES_STATE))
            debugLog() << "Serv. Known:" << ((RemoteDeviceProperties->RemoteDeviceFlags & DEVM_REMOTE_DEVICE_FLAGS_DEVICE_SERVICES_KNOWN) ? "TRUE" : "FALSE");
    }
}

/*************************************************
函数名： getPairState
输入参数： strMac--蓝牙mac地址
输出参数： NULL
返回值： NULL
功能： 获取是否配对（阻塞）
*************************************************************/
bool BluetoothClient::getPairState(QString strMac)
{
    infoLog() << "Start running the getPairState function.strMac:"<<strMac;

    // 等待配对状态信号量可用
    int available = m_pSemPair->available();
    if (available > 0)
    {
        m_pSemPair->acquire(available);
        debugLog() << "Cleared semaphore, available value now: " << m_pSemPair->available();
    }

    // 等待一段时间，超时则返回false
    // 尝试获取信号量
    bool acquired = m_pSemPair->tryAcquire(1, PairTimeOut * 1000);
    if (!acquired)
    {
        warningLog() << "Timeout waiting for pair state.";
    }

    logDebug(QString("Get pair state for device: %1.").arg(strMac).toLatin1().data());

    // 检查目标设备信息是否存在，并返回配对状态
    if (m_destDevInfo.contains(strMac))
    {
        bool paired = m_destDevInfo[strMac].bPaired;

        // 如果配对状态为false，但我们刚刚收到了配对成功的信号，
        // 说明配对状态应该已经在emitPairState中更新了，再检查一次
        if (!paired && acquired)
        {
            infoLog() << "Pairing signal received but device state shows not paired, rechecking...";

            // 重新获取配对状态，因为emitPairState应该已经更新了
            if (m_destDevInfo.contains(strMac))
            {
                paired = m_destDevInfo[strMac].bPaired;
                if (paired)
                {
                    infoLog() << "Device pairing state found to be paired after recheck";
                }
                else
                {
                    infoLog() << "Device pairing state still shows not paired, this may indicate a problem";
                }
            }
        }

        setMac(strMac);

        logDebug(QString("Pair state for device %1: %2").arg(strMac).arg(paired ? "paired" : "not paired"));
        return paired;
    }
    else
    {
        logDebug(QString("Device %1 not found in destination device info").arg(strMac));
        return false;
    }
}

/*************************************************
函数名： getConnectState
输入参数： strMac--蓝牙mac地址
输出参数： NULL
返回值： NULL
功能： 获取是否连接（阻塞）
*************************************************************/
bool BluetoothClient::getConnectState(QString strMac)
{
    infoLog() << "Start running the getConnectState function.strMac:"<<strMac;

    if ( m_pSemConn->available() > 0 )
    {
        m_pSemConn->acquire(m_pSemConn->available());
    }

    m_pSemConn->tryAcquire(1, ConnectTimeOut * 1000);

    m_destDevInfo[strMac].bConnected = m_bConnect;

    return m_bConnect;
}

/*************************************************
函数名： checkConnectionState
输入参数： NULL
输出参数： NULL
返回值： bool--连接正常返回true，异常返回false
功能： 检查当前蓝牙连接状态
*************************************************************/
bool BluetoothClient::checkConnectionState()
{
    // 首先检查静态标志
    if (!m_bConnect)
    {
        debugLog() << "Connection state check failed: static flag indicates disconnected";
        return false;
    }
    
    // 检查端口句柄是否有效
    if (m_nPortHandle < 0)
    {
        warningLog() << "Connection state check failed: invalid port handle";
        return false;
    }
    return true;
}

void BluetoothClient::resetConnectionState(const QString &strDevMAC)
{
    infoLog() << "Resetting connection state for device: " << strDevMAC;

    // 1. 取消任何正在进行的配对操作
    int cancelResult = cancelPairWithRemoteDev(strDevMAC);
    if (cancelResult == 0) {
        infoLog() << "Cancelled ongoing pairing operations";
    }

    // 2. 强制断开设备连接
    forceDisconnectDevice(strDevMAC);

    // 3. 确保连接状态标志被重置
    m_bConnect = false;

    // 4. 清理端口句柄
    if (m_nPortHandle > 0) {
        closePort(m_nPortHandle);
        m_nPortHandle = 0;
    }

    // 5. 额外清理：断开连接后刷新状态
    disconnectFromRemoteDev();

    // 6. 短暂等待确保状态完全重置
    usleep(500000); // 等待500ms

    infoLog() << "Connection state reset completed for device: " << strDevMAC;
}

/*************************************************
函数名： connectToRemoteDevWithRetry
输入参数： strDevMac--远端蓝牙mac地址
          maxRetries--最大重试次数
输出参数： NULL
返回值： 连接状态--true:成功; false:失败
功能： 智能连接远端蓝牙设备，自动处理配对冲突
*************************************************************/
bool BluetoothClient::connectToRemoteDevWithRetry(const QString &strDevMac, int maxRetries)
{
    infoLog() << "Starting intelligent connection to device: " << strDevMac << " with max retries: " << maxRetries;

    for (int attempt = 1; attempt <= maxRetries; ++attempt)
    {
        infoLog() << "Connection attempt " << attempt << " of " << maxRetries;

        // 每次尝试前都重置连接状态
        if (attempt > 1)
        {
            infoLog() << "Resetting connection state before retry";
            resetConnectionState(strDevMac);

            // 等待更长时间确保状态完全重置
            usleep(1000000); // 等待1秒
        }

        // 尝试连接
        bool result = connectToRemoteDev(strDevMac);

        if (result)
        {
            infoLog() << "Connection successful on attempt " << attempt;
            return true;
        }

        // 连接失败，分析失败原因并采取相应策略
        logWarning(QString("Connection attempt %1 failed").arg(attempt));

        if (attempt < maxRetries)
        {
            // 根据尝试次数采用不同的恢复策略
            if (attempt == 1)
            {
                // 第一次失败：强制取消配对并重新配对
                infoLog() << "First attempt failed, forcing unpair and re-pair";
                unPairRemoteDev(strDevMac);
                usleep(500000); // 等待500ms
            }
            else if (attempt == 2)
            {
                // 第二次失败：重启蓝牙适配器
                infoLog() << "Second attempt failed, restarting Bluetooth adapter";
                restart();
                usleep(2000000); // 等待2秒让蓝牙适配器完全重启
            }

            infoLog() << "Waiting before next attempt...";
            usleep(1000000); // 每次重试前等待1秒
        }
    }

    logError(QString("Failed to connect to device %1 after %2 attempts").arg(strDevMac).arg(maxRetries));
    return false;
}

/*************************************************
函数名： connectToRemoteDevSimple
输入参数： strDevMac--远端蓝牙mac地址
输出参数： NULL
返回值： 连接状态--true:成功; false:失败
功能： 简化的连接方法，专门解决配对冲突问题
*************************************************************/
bool BluetoothClient::connectToRemoteDevSimple(const QString &strDevMac)
{
    infoLog() << "Starting simple connection to device: " << strDevMac;

    // 1. 保存MAC地址
    m_strDevMac = strDevMac;

    // 2. 基本状态重置
    m_bConnect = false;
    if (m_nPortHandle > 0) {
        closePort(m_nPortHandle);
        m_nPortHandle = 0;
    }

    // 3. 强制取消配对，确保干净的起始状态
    infoLog() << "Force unpairing to ensure clean state";
    unPairRemoteDev(strDevMac);
    usleep(500000); // 等待500ms

    // 4. 停止设备扫描
    if (m_bScan) {
        stopDeviceDiscovery();
        usleep(1000000); // 等待1秒
    }

    // 5. 确保设备在列表中
    QMutexLocker autolock(&m_mutex);
    if (!m_destDevInfo.contains(strDevMac)) {
        logError(QString("Device %1 not found in device list").arg(strDevMac));
        return false;
    }

    // 6. 开始配对
    infoLog() << "Starting pairing process";
    int pairResult = pairWithRemoteDev(strDevMac);

    if (pairResult < 0) {
        logError("Pairing failed");
        return false;
    }

    // 7. 等待配对完成，使用改进的检查方法
    bool pairSuccess = false;
    if (pairResult == 0) {
        // 需要等待配对完成
        pairSuccess = getPairState(strDevMac);
    } else {
        // 已经配对
        pairSuccess = true;
        infoLog() << "Device already paired";
    }

    if (!pairSuccess) {
        logError("Pairing verification failed");
        return false;
    }

    infoLog() << "Pairing successful, starting connection";

    // 8. 连接过程
    for (int attempt = 1; attempt <= 2; ++attempt) {
        infoLog() << "Connection attempt " << attempt;

        // 获取服务端口
        m_nSevrPort = 0;
        queryRemoteDevServices(strDevMac);
        if (m_nSevrPort == 0) {
            logError("No valid service port found");
            return false;
        }

        // 尝试打开远程端口
        if (openRemotePort(strDevMac, m_nSevrPort, m_nPortHandle) == 0) {
            // 验证连接状态
            if (getConnectState(strDevMac)) {
                // 连接成功
                m_bConnect = true;
                changeBufferSize(m_nPortHandle, RecvBuffSize, TransBuffSize);
                infoLog() << "Connection successful";
                return true;
            }
        }

        logWarning(QString("Connection attempt %1 failed").arg(attempt));

        // 第一次失败后，短暂等待再重试
        if (attempt == 1) {
            usleep(1000000); // 等待1秒
        }
    }

    logError("All connection attempts failed");
    return false;
}

/*************************************************
函数名： emitPairState
输入参数： bState--配对结果
输出参数： NULL
返回值： NULL
功能： 发送配对完成的信号并更新设备状态
*************************************************************/
void BluetoothClient::emitPairState(bool bState)
{
    infoLog() << "BluetoothClient::emitPairState called with state: " << bState;

    // 调用基类方法处理信号量和信号发送
    Bluetooth::emitPairState(bState);

    // 更新当前设备的配对状态
    if (!m_strMAC.isEmpty() && m_destDevInfo.contains(m_strMAC))
    {
        m_destDevInfo[m_strMAC].bPaired = bState;
        infoLog() << "Updated pairing state for device " << m_strMAC << " to " << (bState ? "paired" : "not paired");
    }
    else if (!m_strDevMac.isEmpty() && m_destDevInfo.contains(m_strDevMac))
    {
        m_destDevInfo[m_strDevMac].bPaired = bState;
        infoLog() << "Updated pairing state for device " << m_strDevMac << " to " << (bState ? "paired" : "not paired");
    }
    else
    {
        infoLog() << "No device MAC available to update pairing state";
    }
}
