#include "guideclientmanager.h"
#include <QMetaType>
#include <QDateTime>
#include <QtConcurrentRun>
#include <QSettings>
#include "Module.h"
#include "datadefine.h"
#include "log/log.h"
#include "infrared/guide/guide_httpdata_def.h"
#include "pda/cloud/qjason/qjson.h"
#include "infrared/guide/httpclientbean.h"
#include "global_log.h"
#include "thirdparty/guide/GuideMeasureTemp.h"
#include "infrared/infraredconfig.h"
#include "infrared/infraredservice.h"

// 处理RGB数据
//void pfnDealRGBData(const Guide::GuideRGBDataInfo &stGuideRGBDataInfo)
//{
//    return;
//}

// 处理Y16数据
//void pfnDealY16Data(const Guide::GuideY16DataInfo &stGuideY16DataInfo)
//{
//    return;
//}

/*******************************************
 * 功能：判断CPU是否为小端序
 * 返回值：
 *      bool：判断结果，true -- 小端序，false -- 大端序
 * *****************************************/
bool isCPULittleEnd()
{
    union
    {
        int iVal;
        char cVal;
    }Endian;

    Endian.iVal = 1;

    return (1 == Endian.cVal);
}

/*******************************************
 * 功能：小端序字节数组转int数据
 * 输入参数：
 *      pData：小端序的字节数组
 * 返回值：
 *      float：转换数据
 * *****************************************/
int arr2IntByLittleEnd(const char *pData)
{
    int iVal = 0;
    if(pData)
    {
        if(isCPULittleEnd())
        {
            //相同序
            memcpy(&iVal, pData, 4);
        }
        else
        {
            //相反序
            char szTemp[4] = {0};
            for(int i = 0; i < 4; ++i)
            {
                szTemp[i] = pData[3 - i];
            }

            memcpy(&iVal, szTemp, 4);
        }
    }

    return iVal;
}

/*******************************************
 * 功能：大端序字节数组转int数据
 * 输入参数：
 *      pData：大端序的字节数组
 * 返回值：
 *      int：转换数据
 * *****************************************/
int arr2IntByBigEnd(const char *pData)
{
    int iVal = 0;

    if(pData)
    {
        if(isCPULittleEnd())
        {
            //相反序
            char szTemp[4] = {0};
            for(int i = 0; i < 4; ++i)
            {
                szTemp[i] = pData[3 - i];
            }

            memcpy(&iVal, szTemp, 4);
        }
        else
        {
            //相同序
            memcpy(&iVal, pData, 4);
        }
    }

    return iVal;
}

/*******************************************
 * 功能：int数据按小端序写入字节数组
 * 输入参数：
 *      iVal：待转换的数据
 * 输出参数：
 *      qbaData：写入int数据的小端序字节数组
 * *****************************************/
void int2ArrByLittleEnd(const int iVal, QByteArray &qbaData)
{
    char szData[4] = {0};
    memcpy(szData, &iVal, sizeof(szData));

    if(isCPULittleEnd())
    {
        //相同序
        qbaData.append(szData[0]);
        qbaData.append(szData[1]);
        qbaData.append(szData[2]);
        qbaData.append(szData[3]);
    }
    else
    {
        //相反序
        qbaData.append(szData[3]);
        qbaData.append(szData[2]);
        qbaData.append(szData[1]);
        qbaData.append(szData[0]);
    }

    return;
}

/*******************************************
 * 功能：int数据按大端序写入字节数组
 * 输入参数：
 *      iVal：待转换的数据
 * 输出参数：
 *      qbaData：写入int数据的大端序字节数组
 * *****************************************/
void int2ArrByBigEnd(const int iVal, QByteArray &qbaData)
{
    char szData[4] = {0};
    memcpy(szData, &iVal, sizeof(szData));

    if(isCPULittleEnd())
    {
        //相反序
        qbaData.append(szData[3]);
        qbaData.append(szData[2]);
        qbaData.append(szData[1]);
        qbaData.append(szData[0]);
    }
    else
    {
        //相同序
        qbaData.append(szData[0]);
        qbaData.append(szData[1]);
        qbaData.append(szData[2]);
        qbaData.append(szData[3]);
    }

    return;
}

/*******************************************
 * 功能：大端序字节数组转double数据
 * 输入参数：
 *      pData：大端序的字节数组
 * 返回值：
 *      double：转换数据
 * *****************************************/
double arr2DoubleByBigEnd(const char *pData)
{
    double dVal = 0;

    if(pData)
    {
        if(isCPULittleEnd())
        {
            //相反序
            char szTemp[8] = {0};
            for(int i = 0; i < 8; ++i)
            {
                szTemp[i] = pData[7 - i];
            }

            memcpy(&dVal, szTemp, 8);
        }
        else
        {
            //相同序
            memcpy(&dVal, pData, 8);
        }
    }

    return dVal;
}

/***********************************************************
 * 功能：构造函数
 * 输入参数：
 *      parent：父指针
 * **********************************************************/
GuideClientManager::GuideClientManager(QObject *parent)
    : QObject(parent)
    , m_eNextAction(INITIAL_STATE)
    , m_bInfraredOpened(false)
    , m_qstrHttpAddr(Guide::g_strHttpSrvAddr)
    , m_qui16Port(Guide::HTTP_SRV_PORT)
    , m_handle(-1)
    , m_iTimerId(-1)
    ,m_iCloseFlag(100)
{
    qRegisterMetaType<QSharedPointer<Guide::GuideRGBDataInfo> >("QSharedPointer<Guide::GuideRGBDataInfo>");
    qRegisterMetaType<QSharedPointer<Guide::GuideY16DataInfo> >("QSharedPointer<Guide::GuideY16DataInfo>");

    m_stVideoStreamRecvInfo.qstrAddrInfo = Guide::g_strLocalAddr;
    m_stVideoStreamRecvInfo.iPackageSize = Guide::g_iPackageSize;
    m_stVideoStreamRecvInfo.iSampleInterval = /*Guide::g_iSampleInterval*/2;
    system("sysctl -w net.core.rmem_max=300242880");
    system("sysctl -w net.core.rmem_default=300242880");
    openUdpComm();

    initMeasureTempSDK();
    m_pThread = new QThread;
    m_pThread->start();

    connect(&m_connectFutureWatcher, SIGNAL(finished()), this, SLOT(onConnectInfraredDevFinished()));
}

/***********************************************************
 * 功能：析构函数
 * **********************************************************/
GuideClientManager::~GuideClientManager()
{
    if (m_pThread)
    {
        m_pThread->exit();
        m_pThread->wait(3000);
        delete m_pThread;
        m_pThread = NULL;
    }
    // Qt4.8.7 bug QFutureWacthcer的finished状态没有初始化，刚初始化未设置future的变量，isFinished()获取结果可能会是false
    if (m_connectFutureWatcher.isRunning())
    {
        m_connectFutureWatcher.waitForFinished();
    }

    if (m_disconnectFutureWatcher.isRunning())
    {
        m_disconnectFutureWatcher.waitForFinished();
    }

    deinitMeasureTempSDK();

    closeUdpComm();
}

/***********************************************************
 * 功能：获取模块单例
 * 返回值：
 *      模块单例
 * **********************************************************/
GuideClientManager* GuideClientManager::instance()
{
    static GuideClientManager objManager;

    return &objManager;
}

/***********************************************************
 * 功能：打开UDP通信
 * *********************************************************/
void GuideClientManager::openUdpComm()
{
    m_qspRgbUdpComm = QSharedPointer<UdpBean> (new (std::nothrow) UdpBean());
    if(m_qspRgbUdpComm.data())
    {
        QObject::connect(m_qspRgbUdpComm.data(), SIGNAL(sigReadyRead(qint64)), this, SLOT(onRgbDataReadyRead(qint64)));
        m_qspRgbUdpComm.data()->setLocalPortInfo(Guide::UDP_RGB_PORT);
        m_qspRgbUdpComm.data()->open();
    }

    m_qspY16UdpComm = QSharedPointer<UdpBean> (new (std::nothrow) UdpBean());
    if(m_qspY16UdpComm.data())
    {
        QObject::connect(m_qspY16UdpComm.data(), SIGNAL(sigReadyRead(qint64)), this, SLOT(onY16DataReadyRead(qint64)));
        m_qspY16UdpComm.data()->setLocalPortInfo(Guide::UDP_Y16_PORT);
        m_qspY16UdpComm.data()->open();
    }
    return;
}

/***********************************************************
 * 功能：关闭UDP通信
 * *********************************************************/
void GuideClientManager::closeUdpComm()
{
    if(m_qspRgbUdpComm.data())
    {
        QObject::disconnect(m_qspRgbUdpComm.data(), SIGNAL(sigReadyRead(qint64)), this, SLOT(onRgbDataReadyRead(qint64)));
        m_qspRgbUdpComm.data()->close();
    }

    if(m_qspY16UdpComm.data())
    {
        QObject::disconnect(m_qspY16UdpComm.data(), SIGNAL(sigReadyRead(qint64)), this, SLOT(onY16DataReadyRead(qint64)));
        m_qspY16UdpComm.data()->close();
    }
    return;
}

/***********************************************************
 * 功能：初始化测温SDK
 * *********************************************************/
void GuideClientManager::initMeasureTempSDK()
{
    // 创建测温实例
    m_handle = StartMeasure();

    // 从配置文件中读取测温曲线数据
    QSettings set(IRConfig::IR_SETTING_FILE, QSettings::IniFormat);
    set.beginGroup(IRConfig::Group_Param);
    QByteArray qbaTemperatureCurve = QByteArray::fromBase64(set.value(IRConfig::Key_TemperatureCurve).toByteArray());
    qDebug() << "TemperatureCurve: " << qbaTemperatureCurve.size();
    set.endGroup();

    // 设置测温曲线
    int iTemperatureCurveLength = qbaTemperatureCurve.size() / sizeof(INT16_T);
    if (iTemperatureCurveLength > 0)
    {
        if (GUIDEIR_OK != SetCurveData(m_handle, (INT16_T*)qbaTemperatureCurve.data(), iTemperatureCurveLength))
        {
            logWarning("SetCurveData failed.");
        }
    }
}

/***********************************************************
 * 功能：反初始化测温SDK
 * *********************************************************/
void GuideClientManager::deinitMeasureTempSDK()
{
    StopMeasure(m_handle);
    m_handle = -1;
}

/***********************************************************
 * 功能：更新测温参数
 * 输入参数：
 *      qbaMeasureParam：测温参数
 * 返回值：
 *      bool：是否成功
 * *********************************************************/
bool GuideClientManager::updateMeasureParam(QByteArray& qbaMeasureParam)
{
    if (m_qbaLastMeasureParam != qbaMeasureParam)
    {
        int iDataLength = qbaMeasureParam.size();
        if (iDataLength <= 0)
        {
            logWarning("Measure param is empty.");
            return false;
        }

        RefreshFrame(m_handle, (CHAR_T*)qbaMeasureParam.data(), iDataLength);

        m_qbaLastMeasureParam = qbaMeasureParam;
    }

    return true;
}

bool GuideClientManager::processConnectInfraredDev(Guide::GuideDevInfo &stDevInfo)
{
    logWarning("processConnectInfraredDev1");

    if (CONNECT_ACTION == m_eNextAction)
    {
        logWarning("New disconnect action");
        // 如果当前又有新的断开连接操作请求过来，就直接return，不需要重复连接再断开连接
        return true;
    }

    // 如果有断开连接操作正在进行，等待断开连接操作进行完
    if (m_disconnectFutureWatcher.isRunning())
    {
        logWarning("Wait disconnect finished1");
        m_disconnectFutureWatcher.waitForFinished();
    }
    logWarning("Wait disconnect finished2");

    bool bRet = false;

    if(!m_bInfraredOpened)
    {
        QJson objUdpInfoData;
        objUdpInfoData.add(Guide::IP, m_stVideoStreamRecvInfo.qstrAddrInfo.toLatin1());
        objUdpInfoData.add(Guide::RGB_PORT, Guide::UDP_RGB_PORT);
        objUdpInfoData.add(Guide::Y16_PORT, Guide::UDP_Y16_PORT);
        objUdpInfoData.add(Guide::PACKAGE_SIZE, m_stVideoStreamRecvInfo.iPackageSize);
        objUdpInfoData.add(Guide::SAMPLE_INTERVAL, m_stVideoStreamRecvInfo.iSampleInterval);

        QJson objReqData;
        objReqData.add(Guide::UDP_INFO, objUdpInfoData);

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::CONNECT_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                QJson objData(objRspData.value(Guide::DATA));
                stDevInfo.qstrDevSN = objData.value(Guide::DEV_SERIAL_NUM).toString();
                stDevInfo.qstrDevName = objData.value(Guide::DEV_NAME).toString();
                stDevInfo.qstrIp = objData.value(Guide::IP).toString();
                stDevInfo.qstrMac = objData.value(Guide::MAC).toString();
                stDevInfo.qstrSubNetMask = objData.value(Guide::SUB_NET_MASK).toString();
                stDevInfo.qstrGateway = objData.value(Guide::GATEWAY).toString();
                stDevInfo.qstrDns = objData.value(Guide::DNS).toString();
                stDevInfo.qstrArmVersion = objData.value(Guide::ARM_VERSION).toString();
                stDevInfo.qstrSdkVersion = objData.value(Guide::SDK_VERSION).toString();
                stDevInfo.qstrFpgaVersion = objData.value(Guide::FPGA_VERSION).toString();
                stDevInfo.qui16ArmPort = static_cast<quint16>(objData.value(Guide::ARM_PORT).toNumber());
                stDevInfo.qui16Y16Port = static_cast<quint16>(objData.value(Guide::Y16_PORT).toNumber());
                stDevInfo.qui16FpgaPort = static_cast<quint16>(objData.value(Guide::FPGA_PORT).toNumber());
                stDevInfo.iWorkMode = static_cast<int>(objData.value(Guide::WORK_MODE).toNumber());
                stDevInfo.iImgWidth = static_cast<int>(objData.value(Guide::IMG_WIDTH).toNumber());
                stDevInfo.iImgHeight = static_cast<int>(objData.value(Guide::IMG_HEIGHT).toNumber());
                stDevInfo.iVisibleImgWidth = static_cast<int>(objData.value(Guide::VISIBLEIMG_WIDTH).toNumber());
                stDevInfo.iVisibleImgHeight = static_cast<int>(objData.value(Guide::VISIBLEIMG_HEIGHT).toNumber());

                m_stGuideDevInfo = stDevInfo;

                m_bInfraredOpened = true;
                bRet = true;
                //启动监听红外转换装置连接状态定时器
                startWatchConnectTimer();
                //终止重连接红外转换装置定时器

                log_debug("infrared device connect succeed.");

                // 每次重新连接都向设备获取，并覆盖本地数据，避免下次连接了不同的镜头导致参数不一致
                QByteArray qbaCurveData;
                if (getDeviceCurveData(qbaCurveData))
                {
                    // 写入配置文件中
                    QSettings set(IRConfig::IR_SETTING_FILE, QSettings::IniFormat);
                    set.beginGroup(IRConfig::Group_Param);
                    set.setValue(IRConfig::Key_TemperatureCurve, qbaCurveData.toBase64().data());
                    set.endGroup();

                    int iTemperatureCurveLength = qbaCurveData.size() / sizeof(INT16_T);
                    if (GUIDEIR_OK != SetCurveData(m_handle, (INT16_T*)qbaCurveData.data(), iTemperatureCurveLength))
                    {
                        logWarning("SetCurveData failed.");
                    }
                }
            }
            else
            {
                logWarning("infrared device connect failed.");
            }
        }
        else
        {
            logWarning("send infrared device connect request failed.");
        }
    }
    else
    {
        bRet = true;
        logWarning("infrared device is already be opened.");
    }

    return bRet;
}

bool GuideClientManager::processDisonnectInfraredDev()
{
    logWarning("processDisonnectInfraredDev1");
    killWatchConnectTimer();
    if (DISCONNECT_ACTION == m_eNextAction)
    {
        logWarning("New connect action");
        // 如果当前又有新的连接操作请求过来，就直接return，不需要重复断开连接再连接
        return true;
    }

    // 如果有连接操作正在进行，等待连接操作进行完
    if (m_connectFutureWatcher.isRunning())
    {
        logWarning("Wait connect finished1");
        m_connectFutureWatcher.waitForFinished();
    }
    logWarning("Wait connect finished2");

    bool bRet = false;
    if(m_bInfraredOpened)
    {
        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::DISCONNECT_URL);

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                m_bInfraredOpened = false;
                bRet = true;
                log_debug("infrared device disconnect succeed.");
            }
            else
            {
                logWarning("infrared device disconnect failed.");
            }
        }
        else
        {
            Infrared::InfraredType eInfraredType =InfraredService::instance()->getInfraredType();
            if(Infrared::GUIDE == eInfraredType)
            {
                m_bInfraredOpened = false;
                logWarning("infrared device disconnect failed.");
                logWarning("send infrared device disconnect request failed.");
            }
            else
            {
                m_bInfraredOpened = false;
                bRet = true;
                log_debug("infrared device disconnect succeed.");
            }

        }
    }
    else
    {
        bRet = true;
        logWarning("infrared device is not be opened.");
    }

    return bRet;
}

/***********************************************************
 * 功能：设置本地视频流接收信息
 * 输入参数：
 *      strRecvInfo：接收信息
 * ********************************************************/
void GuideClientManager::setLocalVideoStreamRecvInfo(const Guide::VideoStreamRecvInfo &strRecvInfo)
{
    m_stVideoStreamRecvInfo = strRecvInfo;
    return;
}

/***********************************************************
 * 功能：设置Http服务信息
 * 输入参数：
 *      qstrAddr：地址
 *      qui16Port：端口
 * ********************************************************/
void GuideClientManager::setHttpServerInfo(const QString &qstrAddr, quint16 qui16Port)
{
    m_qstrHttpAddr = qstrAddr;
    m_qui16Port = qui16Port;
    return;
}

/***********************************************************
 * 功能：获取Http URL信息
 * 输入参数：
 *      qstrPartUrl：部分url信息
 * 返回值：
 *      QString：完整的url信息
 * *********************************************************/
QString GuideClientManager::getHttpUrl(const QString &qstrPartUrl)
{
    return QString("http://%1:%2%3").arg(m_qstrHttpAddr).arg(m_qui16Port).arg(qstrPartUrl);
}

/***********************************************************
 * 功能：连接红外设备
 * 输出参数：
 *      stDevInfo：高德设备信息
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::connectInfraredDev(Guide::GuideDevInfo &stDevInfo)
{
    if (DISCONNECT_ACTION == m_eNextAction)
    {
        // 如果下一步操作是断开连接，代表现在正在进行或已完成连接操作
        if (m_connectFutureWatcher.isRunning())
        {
            logWarning("Repeat connection1");
            // 如果连接操作还在进行，直接返回
            return true;
        }
        else
        {
            // 如果已完成连接操作还需要判断是否正在进行disconnect操作
            if (!m_disconnectFutureWatcher.isRunning())
            {
                // 如果的disconnect操作没有正在进行
                if (m_bInfraredOpened)
                {
                    logWarning("Connection success1");
                    // 如果连接操作已完成且成功，直接返回
                    emit sigConnectInfraredDevFinished(true);
                    return true;
                }
                else
                {
                    logWarning("Reconnection1");
                    // 如果连接操作已完成但未成功，重新连接
                    QFuture<bool> future = QtConcurrent::run(this, &GuideClientManager::processConnectInfraredDev, stDevInfo);
                    m_connectFutureWatcher.setFuture(future);
                }
            }
            else
            {
                // 如果的disconnect操作正在进行
                logWarning("Wait to connect1");
                QFuture<bool> future = QtConcurrent::run(this, &GuideClientManager::processConnectInfraredDev, stDevInfo);
                m_connectFutureWatcher.setFuture(future);
            }
        }
    }
    else
    {
        // 如果下一步操作是连接，代表现在正在进行或已完成disconnect操作，需要修改当前的下一步操作为断开连接
        m_eNextAction = DISCONNECT_ACTION;

        if (m_connectFutureWatcher.isRunning())
        {
            // 如果当前进行的是connect操作，则disconnect不会再进行，直接return
            logWarning("Repeat connection2");
            return true;
        }
        else
        {
            // 如果当前进行的是disconnect操作
            if (!m_disconnectFutureWatcher.isRunning())
            {
                if (m_bInfraredOpened)
                {
                    // 如果的disconnect操作已完成，但是仍是打开状态
                    logWarning("Connection success2");
                    emit sigConnectInfraredDevFinished(true);
                    return true;
                }
                else
                {
                    // 如果的disconnect操作已完成，红外设备连接已关闭则重新连接
                    logWarning("Reconnection2");
                    QFuture<bool> future = QtConcurrent::run(this, &GuideClientManager::processConnectInfraredDev, stDevInfo);
                    m_connectFutureWatcher.setFuture(future);
                }
            }
            else
            {
                // 如果的disconnect操作正在进行
                logWarning("Wait to connect2");
                QFuture<bool> future = QtConcurrent::run(this, &GuideClientManager::processConnectInfraredDev, stDevInfo);
                m_connectFutureWatcher.setFuture(future);
            }
        }
    }

    return true;
}

/***********************************************************
 * 功能：是否已连接红外设备
 * 返回值：
 *      bool：true -- 已连接，false -- 未连接
 * ********************************************************/
bool GuideClientManager::isConnected()
{
    return m_bInfraredOpened;
}

/***********************************************************
 * 功能：断开连接红外设备
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::insideDisconnectInfraredDev()
{
    m_bReConnected = false;
    if (CONNECT_ACTION == m_eNextAction)
    {
        // 如果下一步操作是连接，代表现在正在进行或已完成disconnect操作
        if (m_disconnectFutureWatcher.isRunning())
        {
            logWarning("Repeat disconnection1");
            // 如果disconnect操作还在进行，直接返回
            return true;
        }
        else
        {
            // 如果已完成disconnect操作还需要判断是否正在进行连接操作
            if (!m_connectFutureWatcher.isRunning())
            {
                // 如果的连接操作没有正在进行
                if (!m_bInfraredOpened)
                {
                    logWarning("Successfully disconnected1");
                    // 如果disconnect操作已完成且成功，直接返回
                    return true;
                }
                else
                {
                    logWarning("Disconnect again1");
                    // 如果disconnect操作已完成但未成功，重新disconnect
                    QFuture<bool> future = QtConcurrent::run(this, &GuideClientManager::processDisonnectInfraredDev);
                    m_disconnectFutureWatcher.setFuture(future);
                }
            }
            else
            {
                logWarning("Wait to disconnect1");
                // 如果的连接操作正在进行
                QFuture<bool> future = QtConcurrent::run(this, &GuideClientManager::processDisonnectInfraredDev);
                m_disconnectFutureWatcher.setFuture(future);
            }
        }
    }
    else
    {
        // 如果下一步操作是disconnect，代表现在正在进行或已完成连接操作，需要修改当前的下一步操作为连接
        m_eNextAction = CONNECT_ACTION;

        if (m_disconnectFutureWatcher.isRunning())
        {
            // 如果当前进行的是disconnect操作，则connect不会再进行，直接return
            logWarning("Repeat disconnection2");
            return true;
        }
        else
        {
            // 如果当前进行的是connect操作
            if (!m_connectFutureWatcher.isRunning())
            {
                if (!m_bInfraredOpened)
                {
                    // 如果的connect操作已完成，但是仍是未打开状态
                    logWarning("Successfully disconnected2");
                    return true;
                }
                else
                {
                    // 如果的connect操作已完成，红外设备连接已打开则重新断开连接
                    logWarning("Disconnect again2");
                    QFuture<bool> future = QtConcurrent::run(this, &GuideClientManager::processDisonnectInfraredDev);
                    m_disconnectFutureWatcher.setFuture(future);
                }
            }
            else
            {
                // 如果的connect操作正在进行
                logWarning("Wait to disconnect2");
                QFuture<bool> future = QtConcurrent::run(this, &GuideClientManager::processDisonnectInfraredDev);
                m_disconnectFutureWatcher.setFuture(future);
            }
        }
    }

    return true;
}

/***********************************************************
 * 功能：断开连接红外设备
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::disconnectInfraredDev()
{
    killReConnectTimer();
    return insideDisconnectInfraredDev();
}

/***********************************************************
 * 功能：获取实时温度信息
 * 输出参数：
 *      stLiveTempInfo：实时温度信息
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::getLiveTemperatureInfo(Guide::LiveTemperatureInfo &stLiveTempInfo)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::GET_LIVE_TEMPERATURE_URL);

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.getData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                QJson objData(objRspData.value(Guide::DATA));
                QJson objMaxTemp(objData.value(Guide::MAX_TEMPERATURE));
                m_stLiveTempInfo.stMaxTempInfo.dTemperatureVal = objMaxTemp.value(Guide::TEMPERATURE).toNumber();
                m_stLiveTempInfo.stMaxTempInfo.iPointX = static_cast<int>(objMaxTemp.value(Guide::POINT_X).toNumber());
                m_stLiveTempInfo.stMaxTempInfo.iPointY = static_cast<int>(objMaxTemp.value(Guide::POINT_Y).toNumber());

                QJson objMinTemp(objData.value(Guide::MIN_TEMPERATURE));
                m_stLiveTempInfo.stMinTempInfo.dTemperatureVal = objMinTemp.value(Guide::TEMPERATURE).toNumber();
                m_stLiveTempInfo.stMinTempInfo.iPointX = static_cast<int>(objMinTemp.value(Guide::POINT_X).toNumber());
                m_stLiveTempInfo.stMinTempInfo.iPointY = static_cast<int>(objMinTemp.value(Guide::POINT_Y).toNumber());

                QJson objAvgTemp(objData.value(Guide::AVG_TEMPERATURE));
                m_stLiveTempInfo.stAvgTempInfo.dTemperatureVal = objAvgTemp.value(Guide::TEMPERATURE).toNumber();
                m_stLiveTempInfo.stAvgTempInfo.iPointX = static_cast<int>(objAvgTemp.value(Guide::POINT_X).toNumber());
                m_stLiveTempInfo.stAvgTempInfo.iPointY = static_cast<int>(objAvgTemp.value(Guide::POINT_Y).toNumber());

                QJson objCenTemp(objData.value(Guide::CENTER_TEMPERATURE));
                m_stLiveTempInfo.stCenterTempInfo.dTemperatureVal = objCenTemp.value(Guide::TEMPERATURE).toNumber();
                m_stLiveTempInfo.stCenterTempInfo.iPointX = static_cast<int>(objCenTemp.value(Guide::POINT_X).toNumber());
                m_stLiveTempInfo.stCenterTempInfo.iPointY = static_cast<int>(objCenTemp.value(Guide::POINT_Y).toNumber());

                stLiveTempInfo = m_stLiveTempInfo;
                bRet = true;
            }
            else
            {
                logWarning("infrared device get live temperature failed.");
            }
        }
        else
        {
            logWarning("send infrared device get live temperature request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：获取温度参数信息
 * 输出参数：
 *      stTempParamInfo：温度参数信息
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::getTemperatureParamInfo(Guide::TemperatureParameterInfo &stTempParamInfo)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::GET_TEMPERATURE_PARAM_URL);

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.getData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                QJson objData(objRspData.value(Guide::DATA));
                stTempParamInfo.dEmissivity = objData.value(Guide::EMISSIVITY).toNumber();
                stTempParamInfo.dDistance = objData.value(Guide::DISTANCE).toNumber();
                stTempParamInfo.dHumidity = objData.value(Guide::HUMIDITY).toNumber();
                stTempParamInfo.dBGTemperature = objData.value(Guide::BACKGROUND_TEMPERATURE).toNumber();
                stTempParamInfo.iBGTempSwitch = static_cast<int>(objData.value(Guide::BACKGROUND_TEMPERATURE_SWITCH).toNumber());

                bRet = true;
            }
            else
            {
                logWarning("infrared device get temperature params failed.");
            }
        }
        else
        {
            logWarning("send infrared device get temperature params request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：设置温度参数信息
 * 输入参数：
 *      stTempParamInfo：温度参数信息
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::setTemperatureParamInfo(const Guide::TemperatureParameterInfo &stTempParamInfo)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        QJson objTempParamData;
        objTempParamData.add(Guide::EMISSIVITY, stTempParamInfo.dEmissivity);
        objTempParamData.add(Guide::DISTANCE, stTempParamInfo.dDistance);
        objTempParamData.add(Guide::HUMIDITY, stTempParamInfo.dHumidity);
        objTempParamData.add(Guide::BACKGROUND_TEMPERATURE, stTempParamInfo.dBGTemperature);
        objTempParamData.add(Guide::BACKGROUND_TEMPERATURE_SWITCH, stTempParamInfo.iBGTempSwitch);

        QJson objReqData;
        objReqData.add(Guide::TEMPERATURE_PARAMS, objTempParamData);

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::SET_TEMPERATURE_PARAM_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                bRet = true;
            }
            else
            {
                logWarning("infrared device set temperature params failed.");
            }
        }
        else
        {
            logWarning("send infrared device set temperature params request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：获取校准参数信息
 * 输出参数：
 *      stCalibrateParamInfo：校准参数信息
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::getCalibrateParamInfo(Guide::CalibrateParameterInfo &stCalibrateParamInfo)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::GET_CALIBATE_PARAM_URL);

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.getData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                QJson objData(objRspData.value(Guide::DATA));
                stCalibrateParamInfo.dParamB = objData.value(Guide::PARAM_B).toNumber();
                stCalibrateParamInfo.dParamKF = objData.value(Guide::PARAM_KF).toNumber();

                bRet = true;
            }
            else
            {
                logWarning("infrared device get calibrate params failed.");
            }
        }
        else
        {
            logWarning("send infrared device get calibrate params request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：设置校准参数信息
 * 输入参数：
 *      stCalibrateParamInfo：校准参数信息
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::setCalibrateParamInfo(const Guide::CalibrateParameterInfo &stCalibrateParamInfo)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        QJson objCalibParamData;
        objCalibParamData.add(Guide::PARAM_B, stCalibrateParamInfo.dParamB);
        objCalibParamData.add(Guide::PARAM_KF, stCalibrateParamInfo.dParamKF);

        QJson objReqData;
        objReqData.add(Guide::CALIBRATE_PARAMS, objCalibParamData);

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::SET_CALIBATE_PARAM_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                bRet = true;
            }
            else
            {
                logWarning("infrared device set calibrate params failed.");
            }
        }
        else
        {
            logWarning("send infrared device set calibrate params request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：控制实时视频
 * 输入参数：
 *      eVal：控制值
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::ctrlLiveVideo(Guide::CtrlVideoVal eVal)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        QJson objReqData;
        objReqData.add(Guide::LIVE_VIDEO_CTRL, static_cast<int>(eVal));

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_LIVE_VIDEO_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                bRet = true;
                if(Guide::CTRL_FREEZE == eVal)
                {
                    killWatchConnectTimer();
                }
                else
                {
                    startWatchConnectTimer();
                }
            }
            else
            {
                logWarning("infrared device ctrl live video failed.");
            }
        }
        else
        {
            logWarning("send infrared device ctrl live video request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：控制保存图片
 * 输入参数：
 *      eLedVal：LED开关量
 * 输出参数：
 *      stSavePictureInfo：保存图片信息
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::ctrlSavePictures(Guide::SwitchValue eLedVal, Guide::SavePicturesInfo &stSavePictureInfo)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        QJson objReqData;
        objReqData.add(Guide::LED_ON_CTRL, static_cast<int>(eLedVal));

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_SAVE_PICTURES_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                QJson objData(objRspData.value(Guide::DATA));
                QByteArray qbaInfraredData = objData.value(Guide::INFRARED_PICTURE_DATA).toString();
                QByteArray qbaVisibleData = objData.value(Guide::VISIBLE_PICTURE_DATA).toString();
                QByteArray qbaY16Data = objData.value(Guide::Y16_DATA).toString();

                m_stSavePicturesInfo.qbaInfraredPictureData = QByteArray::fromBase64(qbaInfraredData);
                m_stSavePicturesInfo.qbaVisiblePictureData = QByteArray::fromBase64(qbaVisibleData);
                m_stSavePicturesInfo.qbaY16Data = QByteArray::fromBase64(qbaY16Data);

                stSavePictureInfo = m_stSavePicturesInfo;

                bRet = true;
            }
            else
            {
                logWarning("infrared device ctrl save pictures failed.");
            }
        }
        else
        {
            logWarning("send infrared device ctrl save pictures request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：设置色带
 * 输入参数：
 *      eVal：色带
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::setPalette(Guide::PaletteVal eVal)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        QJson objReqData;
        objReqData.add(Guide::PALETTE_VALUE, static_cast<int>(eVal));

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::SET_PALETTE_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                bRet = true;
            }
            else
            {
                logWarning("infrared device set palette failed.");
            }
        }
        else
        {
            logWarning("send infrared device set palette request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：设置温度范围
 * 输入参数：
 *      stTempRangeInfo：温度范围信息
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::setTemperatureRange(const Guide::TemperatureRangeInfo &stTempRangeInfo)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        QJson objReqData;
        objReqData.add(Guide::MAX_TEMPERATURE, stTempRangeInfo.dMaxTemperature);
        objReqData.add(Guide::MIN_TEMPERATURE, stTempRangeInfo.dMinTemperature);

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::SET_TEMPERATURE_RANGE_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                bRet = true;
            }
            else
            {
                logWarning("infrared device set temperature range failed.");
            }
        }
        else
        {
            logWarning("send infrared device set temperature range request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：控制激光
 * 输入参数：
 *      eVal：控制值
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::ctrlLaser(Guide::SwitchValue eVal)
{
    bool bRet = false;
    m_iCloseFlag++;
    QJson objReqData;
    objReqData.add(Guide::LASER_ON_CTRL, static_cast<int>(eVal));

    HTTPModule::HttpRequestInfo stReqInfo;
    stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_LASER_URL);
    stReqInfo.qbaReqData = objReqData.unformattedData();

    HTTPModule::HttpRsponseInfo stRspInfo;
    HTTPModule::HttpClientBean objHttpClient;
    objHttpClient.moveToThread(m_pThread);
    if(objHttpClient.postData(stReqInfo, stRspInfo))
    {
        QJson objRspData(stRspInfo.qbaRspData);
        int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
        if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
        {
            bRet = true;

            // 写入到配置文件中
            QSettings set(IRConfig::IR_SETTING_FILE, QSettings::IniFormat);
            set.beginGroup(IRConfig::Group_Param);
            set.setValue(IRConfig::Key_LaserControl, eVal);
            set.endGroup();
        }
        else
        {
            logWarning("infrared device ctrl laser failed.");
        }
    }
    else
    {
        logWarning("send infrared device ctrl laser request failed.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：获取激光控制状态
 * 返回值：
 *      Guide::SwitchValue：状态，SW_OFF -- 关闭，SW_ON -- 打开
 * ********************************************************/
Guide::SwitchValue GuideClientManager::getLaserControlState()
{
    QSettings set(IRConfig::IR_SETTING_FILE, QSettings::IniFormat);
    set.beginGroup(IRConfig::Group_Param);
    Guide::SwitchValue eSwitchValue = static_cast<Guide::SwitchValue>(set.value(IRConfig::Key_LaserControl).toInt());
    set.endGroup();

    return eSwitchValue;
}

/***********************************************************
 * 功能：控制辅助照明
 * 输入参数：
 *      eVal：控制值
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::ctrlAuxiliaryLighting(Guide::SwitchValue eVal)
{
    bool bRet = false;
    m_iCloseFlag++;
    QJson objReqData;
    objReqData.add(Guide::LED_ON_CTRL, static_cast<int>(eVal));

    HTTPModule::HttpRequestInfo stReqInfo;
    stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_LED_URL);
    stReqInfo.qbaReqData = objReqData.unformattedData();

    HTTPModule::HttpRsponseInfo stRspInfo;
    HTTPModule::HttpClientBean objHttpClient;
    objHttpClient.moveToThread(m_pThread);
    if(objHttpClient.postData(stReqInfo, stRspInfo))
    {
        QJson objRspData(stRspInfo.qbaRspData);
        int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
        if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
        {
            bRet = true;

            // 写入到配置文件中
            QSettings set(IRConfig::IR_SETTING_FILE, QSettings::IniFormat);
            set.beginGroup(IRConfig::Group_Param);
            set.setValue(IRConfig::Key_AuxiliaryLighting, eVal);
            set.endGroup();
        }
        else
        {
            logWarning("infrared device ctrl auxiliary lighting failed.");
        }
    }
    else
    {
        logWarning("send infrared device ctrl auxiliary lighting request failed.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：获取辅助照明状态
 * 返回值：
 *      Guide::SwitchValue：状态，SW_OFF -- 关闭，SW_ON -- 打开
 * ********************************************************/
Guide::SwitchValue GuideClientManager::getAuxiliaryLightingState()
{
    QSettings set(IRConfig::IR_SETTING_FILE, QSettings::IniFormat);
    set.beginGroup(IRConfig::Group_Param);
    Guide::SwitchValue eSwitchValue = static_cast<Guide::SwitchValue>(set.value(IRConfig::Key_AuxiliaryLighting).toInt());
    set.endGroup();

    return eSwitchValue;
}

/***********************************************************
 * 功能：控制LED
 * 输入参数：
 *      eVal：控制值
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::ctrlLED(Guide::SwitchValue eVal)
{
    bool bRet = false;
    QJson objReqData;
    m_iCloseFlag++;
    objReqData.add(Guide::LED_ON_CTRL, static_cast<int>(eVal));

    HTTPModule::HttpRequestInfo stReqInfo;
    stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_LED_URL);
    stReqInfo.qbaReqData = objReqData.unformattedData();

    HTTPModule::HttpRsponseInfo stRspInfo;
    HTTPModule::HttpClientBean objHttpClient;
    objHttpClient.moveToThread(m_pThread);
    if(objHttpClient.postData(stReqInfo, stRspInfo))
    {
        QJson objRspData(stRspInfo.qbaRspData);
        int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
        if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
        {
            bRet = true;
        }
        else
        {
            logWarning("infrared device ctrl LED failed.");
        }
    }
    else
    {
        logWarning("send infrared device ctrl LED request failed.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：控制变焦
 * 输入参数：
 *      eVal：控制值
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::ctrlFocus(Guide::FocusValue eVal)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        QJson objReqData;
        objReqData.add(Guide::FOCUS_VALUE, static_cast<int>(eVal));

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_FOCUS_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                bRet = true;
            }
            else
            {
                logWarning("infrared device ctrl focus failed.");
            }
        }
        else
        {
            logWarning("send infrared device ctrl focus request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：控制缩放
 * 输入参数：
 *      stZommCtrlInfo：缩放信息
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::ctrlZoom(const Guide::ZoomCtrlInfo &stZommCtrlInfo)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        QJson objReqData;
        objReqData.add(Guide::ZOOM_VALUE, stZommCtrlInfo.eVal);
        objReqData.add(Guide::POINT_X, stZommCtrlInfo.iPointX);
        objReqData.add(Guide::POINT_Y, stZommCtrlInfo.iPointY);

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_ZOOM_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                bRet = true;
            }
            else
            {
                logWarning("infrared device ctrl zoom failed.");
            }
        }
        else
        {
            logWarning("send infrared device ctrl zoom request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：控制数据切换
 * 输入参数：
 *      eVal：数据切换模式
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::ctrlDataSwitch(Guide::CtrlDataSwitch eVal)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        QJson objReqData;
        objReqData.add(Guide::DARASWITCH_VALUE, eVal);

        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::CTRL_DATASWITCH_URL);
        stReqInfo.qbaReqData = objReqData.unformattedData();

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.postData(stReqInfo, stRspInfo))
        {
            QJson objRspData(stRspInfo.qbaRspData);
            int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
            if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
            {
                bRet = true;
            }
            else
            {
                logWarning("infrared device ctrl Data Switch failed.");
            }
        }
        else
        {
            logWarning("send infrared device ctrl Data Switch request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：Y16数据转温度数据
 * 输入参数：
 *      qvtY16Datas：Y16数据集合
 * 输出参数：
 *      qvtTemperatues：温度数据集合
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::y16Data2Temperature(const QVector<short> &qvtY16Datas, QVector<double> &qvtTemperatues)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        int iOffset = 0;
        int iLeftSize = qvtY16Datas.size();

        // 分快发送，CJSON添加成array有一定的大小限制
        while (iLeftSize > 0)
        {
            int iSendSize = iLeftSize > Guide::g_iPerDataSize ? Guide::g_iPerDataSize : iLeftSize;
            QVector<short> qvtTempY16Datas = qvtY16Datas.mid(iOffset, iSendSize);

            QJson objY16DataInfos(QJson::Array);
            for(int i = 0, iSize = qvtTempY16Datas.size(); i < iSize; ++i)
            {
                objY16DataInfos.addIntToArray(qvtTempY16Datas[i]);
            }

            QJson objReqData;
            objReqData.add(Guide::Y16_DATAS, objY16DataInfos);

            HTTPModule::HttpRequestInfo stReqInfo;
            stReqInfo.qstrUrl = getHttpUrl(Guide::GET_Y16_TO_TEMPERATURE_URL);
            stReqInfo.qbaReqData = objReqData.unformattedData();

            HTTPModule::HttpRsponseInfo stRspInfo;
            HTTPModule::HttpClientBean objHttpClient;
            if(objHttpClient.postData(stReqInfo, stRspInfo))
            {
                QJson objRspData(stRspInfo.qbaRspData);
                int iCode = static_cast<int>(objRspData.value(Guide::CODE).toNumber());
                if(Guide::HTTP_RSP_CODE_SUCCESS == iCode)
                {
                    QJson objDataInfo(objRspData.value(Guide::DATA));
                    QJson objTempInfo(objDataInfo.value(Guide::TEMPERATURES));
                    if(objTempInfo.isArray())
                    {
                        for(int i = 0, iCnt = objTempInfo.count(); i < iCnt; ++i)
                        {
                            qvtTemperatues.append(objTempInfo.at(i).toNumber());
                        }
                    }
                    bRet = true;
                }
                else
                {
                    logWarning("infrared device y16 data to temperature failed.");
                }
            }
            else
            {
                logWarning("send infrared device y16 data to temperature request failed.");
                bRet = false;
                break;
            }

            if(0 < iSendSize)
            {
                iLeftSize -= iSendSize;
                iOffset += iSendSize;

                /*if(iLeftSize > 0)
                {
                    //添加延时，避免通信层的链路数据量过大导致崩溃
                    QEventLoop eventloop;
                    QTimer::singleShot(2, &eventloop, SLOT(quit()));
                    eventloop.exec();
                }*/
            }

        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：获取设备温度曲线数据
 * 输出参数：
 *      qbaCurveData：温度曲线数据
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::getDeviceCurveData(QByteArray &qbaCurveData)
{
    bool bRet = false;
    m_iCloseFlag++;
    if(m_bInfraredOpened)
    {
        HTTPModule::HttpRequestInfo stReqInfo;
        stReqInfo.qstrUrl = getHttpUrl(Guide::GET_DEVICE_CURVE_DATA_URL);

        HTTPModule::HttpRsponseInfo stRspInfo;
        HTTPModule::HttpClientBean objHttpClient;
        objHttpClient.moveToThread(m_pThread);
        if(objHttpClient.getData(stReqInfo, stRspInfo))
        {
            if (!stRspInfo.qbaRspData.isEmpty())
            {
                qbaCurveData = stRspInfo.qbaRspData;
                bRet = true;
            }
            else
            {
                logWarning("infrared device get device curve data failed.");
            }
        }
        else
        {
            logWarning("send infrared device get device curve data request failed.");
        }
    }
    else
    {
        logWarning("infrared device is not be opened.");
    }
    m_iCloseFlag--;
    return bRet;
}

/***********************************************************
 * 功能：Y16数据转温度数据
 * 输入参数：
 *      sY16Data：Y16数据
 *      qbaMeasureParam：测温曲线
 *      stTempParamInfo：测温参数
 * 返回值：
 *      float：温度数据
 * ********************************************************/
float GuideClientManager::y16Data2Temperature(const short sY16Data, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo)
{
    float fTemp = 0.0;
    // 刷新测温参数
    if (!updateMeasureParam(qbaMeasureParam))
    {
        logWarning("updateMeasureParam failed.");
        return fTemp;
    }

    MEASURE_PARAM_INFO stMeasureParamInfo;
    stMeasureParamInfo.emiss = stTempParamInfo.dEmissivity;
    stMeasureParamInfo.distance = stTempParamInfo.dDistance;
    stMeasureParamInfo.humidity = stTempParamInfo.dHumidity;
    stMeasureParamInfo.backTemp = stTempParamInfo.dBGTemperature;
    if (GUIDEIR_ERR == GetTempByY16(m_handle, sY16Data, stMeasureParamInfo, &fTemp))
    {
        logWarning("GetTempByY16 failed.");
    }

    return fTemp;
}

/***********************************************************
 * 功能：Y16数据转温度数据
 * 输入参数：
 *      pY16Data：Y16数据集合指针
 *      qbaMeasureParam：测温曲线
 *      stTempParamInfo：测温参数
 * 输出参数：
 *      pTemperatureData：温度数据集合指针
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::y16Data2Temperature(short* pY16Data, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo, int iImageWidth, int iImageHeight, float* pTemperatureData)
{
    // 刷新测温参数
    if (!updateMeasureParam(qbaMeasureParam))
    {
        logWarning("updateMeasureParam failed.");
        return false;
    }

    MEASURE_PARAM_INFO stMeasureParamInfo;
    stMeasureParamInfo.emiss = stTempParamInfo.dEmissivity;
    stMeasureParamInfo.distance = stTempParamInfo.dDistance;
    stMeasureParamInfo.humidity = stTempParamInfo.dHumidity;
    stMeasureParamInfo.backTemp = stTempParamInfo.dBGTemperature;
    if (GUIDEIR_ERR == GetTempMatrixByY16(m_handle, pY16Data, stMeasureParamInfo, pTemperatureData, iImageWidth, iImageHeight))
    {
        logWarning("GetTempMatrixByY16 failed.");
        return false;
    }

    return true;
}

/***********************************************************
 * 功能：温度数据转Y16数据
 * 输入参数：
 *      fTemperature：温度数据
 *      qbaMeasureParam：测温曲线
 *      stTempParamInfo：测温参数
 * 返回值：
 *      short：Y16数据
 * ********************************************************/
short GuideClientManager::temperature2Y16Data(const float fTemperature, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo)
{
    short sY16Data = 0;
    // 刷新测温参数
    if (!updateMeasureParam(qbaMeasureParam))
    {
        return sY16Data;
    }

    MEASURE_PARAM_INFO stMeasureParamInfo;
    stMeasureParamInfo.emiss = stTempParamInfo.dEmissivity;
    stMeasureParamInfo.distance = stTempParamInfo.dDistance;
    stMeasureParamInfo.humidity = stTempParamInfo.dHumidity;
    stMeasureParamInfo.backTemp = stTempParamInfo.dBGTemperature;
    if (GUIDEIR_ERR == GetY16ByTemp(m_handle, fTemperature, stMeasureParamInfo, &sY16Data))
    {
        logWarning("GetY16ByTemp failed.");
    }

    return sY16Data;
}

/***********************************************************
 * 功能：温度数据转Y16数据
 * 输入参数：
 *      pTemperatureData：温度数据集合指针
 *      qbaMeasureParam：测温曲线
 *      stTempParamInfo：测温参数
 * 输出参数：
 *      pY16Data：Y16数据集合指针
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * ********************************************************/
bool GuideClientManager::temperature2Y16Data(float* pTemperatureData, QByteArray& qbaMeasureParam, const Guide::TemperatureParameterInfo& stTempParamInfo, int iImageWidth, int iImageHeight, short* pY16Data)
{
    // 刷新测温参数
    if (!updateMeasureParam(qbaMeasureParam))
    {
        logWarning("updateMeasureParam failed.");
        return false;
    }

    MEASURE_PARAM_INFO stMeasureParamInfo;
    stMeasureParamInfo.emiss = stTempParamInfo.dEmissivity;
    stMeasureParamInfo.distance = stTempParamInfo.dDistance;
    stMeasureParamInfo.humidity = stTempParamInfo.dHumidity;
    stMeasureParamInfo.backTemp = stTempParamInfo.dBGTemperature;
    if (GUIDEIR_ERR == GetY16MatrixByTemp(m_handle, pTemperatureData, stMeasureParamInfo, pY16Data, iImageWidth, iImageHeight))
    {
        logWarning("GetY16MatrixByTemp failed.");
        return false;
    }

    return true;
}

/***********************************************************
 * 功能：槽，处理RGB数据可读消息
 * 输入参数：
 *      qi64AvailableLen：数据长度
 * ********************************************************/
void GuideClientManager::onRgbDataReadyRead(qint64 qi64AvailableLen)
{
    if(m_qspRgbUdpComm.data())
    {
        QString qstrPeerAddr = "";
        quint16 qui16Port = 0;

        QByteArray qbaData = m_qspRgbUdpComm.data()->read(static_cast<unsigned int>(qi64AvailableLen), qstrPeerAddr, qui16Port);
        if(0 < qbaData.size())
        {
            int iHeadLen = Guide::UDP_HEAD_TAG.size();
            QByteArray qbaHead = qbaData.mid(0, iHeadLen);
            if(qbaHead == Guide::UDP_HEAD_TAG)
            {
                m_stRecvRgbTempDataInfo.qbaData.clear();
                m_stRecvRgbTempDataInfo.iLen = arr2IntByBigEnd(qbaData.data() + iHeadLen);
                m_stRecvRgbTempDataInfo.qbaData.append(qbaData.mid(iHeadLen + sizeof(m_stRecvRgbTempDataInfo.iLen)));
            }
            else
            {
                m_stRecvRgbTempDataInfo.qbaData.append(qbaData);
            }

            int iTailLen = Guide::UDP_TAIL_TAG.size();
            int iTotalSize = m_stRecvRgbTempDataInfo.qbaData.size();
            if(iTotalSize >= (m_stRecvRgbTempDataInfo.iLen + iTailLen))
            {
                QByteArray qbaTail = m_stRecvRgbTempDataInfo.qbaData.right(iTailLen);
                if(qbaTail == Guide::UDP_TAIL_TAG)
                {
                    // 一帧接收完毕，告诉应用层
                    QSharedPointer<Guide::GuideRGBDataInfo> qspGuideRgbDataInfo(new (std::nothrow) Guide::GuideRGBDataInfo());
                    if(qspGuideRgbDataInfo.data())
                    {
                        qspGuideRgbDataInfo.data()->iImageWidth = m_stGuideDevInfo.iImgWidth;
                        qspGuideRgbDataInfo.data()->iImageHeight = m_stGuideDevInfo.iImgHeight;
                        qspGuideRgbDataInfo.data()->qbaRGBData = m_stRecvRgbTempDataInfo.qbaData.left(iTotalSize - iTailLen);
                        //log_debug("recv rgb data size: %d", qspGuideRgbDataInfo.data()->qbaRGBData.size());
                        emit sigRgbData(qspGuideRgbDataInfo);
                    }
                }
            }
        }
    }

    return;
}

/***********************************************************
 * 功能：槽，处理Y16数据可读消息
 * 输入参数：
 *      qi64AvailableLen：数据长度
 * ********************************************************/
void GuideClientManager::onY16DataReadyRead(qint64 qi64AvailableLen)
{
    if(m_qspY16UdpComm.data())
    {
        qConnectTime = QDateTime::currentDateTime();
        QString qstrPeerAddr = "";
        quint16 qui16Port = 0;

        QByteArray qbaData = m_qspY16UdpComm.data()->read(static_cast<unsigned int>(qi64AvailableLen), qstrPeerAddr, qui16Port);
        if(0 < qbaData.size())
        {
            int iHeadLen = Guide::UDP_HEAD_TAG.size();
            QByteArray qbaHead = qbaData.mid(0, iHeadLen);
            if(qbaHead == Guide::UDP_HEAD_TAG)
            {
                //m_qi64MSecTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
                m_stRecvY16TempDataInfo.qbaData.clear();
                m_stRecvY16TempDataInfo.iLen = arr2IntByBigEnd(qbaData.data() + iHeadLen);
                m_stRecvY16TempDataInfo.qbaData.append(qbaData.mid(iHeadLen + sizeof(m_stRecvY16TempDataInfo.iLen)));
            }
            else
            {
                m_stRecvY16TempDataInfo.qbaData.append(qbaData);
            }

            // 避免程序后面启动，红外装置还在发送数据，导致接收的包不是从开头开始接收的
            if (0 == m_stRecvY16TempDataInfo.iLen)
            {
                return;
            }

            int iTailLen = Guide::UDP_TAIL_TAG.size();
            int iTotalSize = m_stRecvY16TempDataInfo.qbaData.size();
            if(iTotalSize >= (m_stRecvY16TempDataInfo.iLen + iTailLen))
            {
                QByteArray qbaTail = m_stRecvY16TempDataInfo.qbaData.right(iTailLen);
                if(qbaTail == Guide::UDP_TAIL_TAG)
                {
                    // 一帧接收完毕，告诉应用层
                    QSharedPointer<Guide::GuideY16DataInfo> qspGuideY16DataInfo(new (std::nothrow) Guide::GuideY16DataInfo());
                    if(qspGuideY16DataInfo.data())
                    {
                        //getLiveTemperatureInfo(qspGuideY16DataInfo.data()->stLiveTempInfo); // 频繁进行获取，后期是否修改为本地Y16计算最大与最小

                        qspGuideY16DataInfo.data()->iImageWidth = m_stGuideDevInfo.iImgWidth;
                        qspGuideY16DataInfo.data()->iImageHeight = m_stGuideDevInfo.iImgHeight;

                        // 温度信息
                        const static int iIntLength = sizeof(int);
                        const static int iDoubleLength = sizeof(double);
                        char* pData = m_stRecvY16TempDataInfo.qbaData.data();
                        qspGuideY16DataInfo.data()->stLiveTempInfo.stMaxTempInfo.iPointX = arr2IntByBigEnd(pData);
                        pData += iIntLength;
                        qspGuideY16DataInfo.data()->stLiveTempInfo.stMaxTempInfo.iPointY = arr2IntByBigEnd(pData);
                        pData += iIntLength;
                        qspGuideY16DataInfo.data()->stLiveTempInfo.stMaxTempInfo.dTemperatureVal = arr2DoubleByBigEnd(pData);
                        pData += iDoubleLength;

                        qspGuideY16DataInfo.data()->stLiveTempInfo.stMinTempInfo.iPointX = arr2IntByBigEnd(pData);
                        pData += iIntLength;
                        qspGuideY16DataInfo.data()->stLiveTempInfo.stMinTempInfo.iPointY = arr2IntByBigEnd(pData);
                        pData += iIntLength;
                        qspGuideY16DataInfo.data()->stLiveTempInfo.stMinTempInfo.dTemperatureVal = arr2DoubleByBigEnd(pData);
                        pData += iDoubleLength;

                        qspGuideY16DataInfo.data()->stLiveTempInfo.stAvgTempInfo.iPointX = arr2IntByBigEnd(pData);
                        pData += iIntLength;
                        qspGuideY16DataInfo.data()->stLiveTempInfo.stAvgTempInfo.iPointY = arr2IntByBigEnd(pData);
                        pData += iIntLength;
                        qspGuideY16DataInfo.data()->stLiveTempInfo.stAvgTempInfo.dTemperatureVal = arr2DoubleByBigEnd(pData);
                        pData += iDoubleLength;

                        qspGuideY16DataInfo.data()->stLiveTempInfo.stCenterTempInfo.iPointX = arr2IntByBigEnd(pData);
                        pData += iIntLength;
                        qspGuideY16DataInfo.data()->stLiveTempInfo.stCenterTempInfo.iPointY = arr2IntByBigEnd(pData);
                        pData += iIntLength;
                        qspGuideY16DataInfo.data()->stLiveTempInfo.stCenterTempInfo.dTemperatureVal = arr2DoubleByBigEnd(pData);
                        pData += iDoubleLength;

                        // 测量参数
                        int iMeasureParamDataLen = arr2IntByBigEnd(pData);
                        qspGuideY16DataInfo.data()->qbaMeasureParamData = m_stRecvY16TempDataInfo.qbaData.mid(iDoubleLength * 4 + iIntLength * 9, iMeasureParamDataLen);

                        // Y16数据
                        pData += iMeasureParamDataLen + iIntLength;
                        int iY16DataDataLen = arr2IntByBigEnd(pData);
                        qspGuideY16DataInfo.data()->qbaY16Data = m_stRecvY16TempDataInfo.qbaData.mid(iDoubleLength * 4 + iIntLength * 10 + iMeasureParamDataLen, iY16DataDataLen);
                        //log_debug("recv y16 data size: %d", qspGuideY16DataInfo.data()->qbaY16Data.size());

                        //可见光
                        pData += iY16DataDataLen + iIntLength;
                        int iVisibleDataLen = arr2IntByBigEnd(pData);
                        qspGuideY16DataInfo.data()->stVisibleDataInfo.qbaVisibleData = m_stRecvY16TempDataInfo.qbaData.mid(iDoubleLength * 4 + iIntLength * 11 + iMeasureParamDataLen + iY16DataDataLen, iVisibleDataLen);
                        qspGuideY16DataInfo.data()->stVisibleDataInfo.iImageWidth = m_stGuideDevInfo.iVisibleImgWidth;
                        qspGuideY16DataInfo.data()->stVisibleDataInfo.iImageHeight = m_stGuideDevInfo.iVisibleImgHeight;
                        m_stRecvY16TempDataInfo.iLen = 0;
                        //log_debug("-------waste %lld ms", QDateTime::currentDateTime().toMSecsSinceEpoch() - m_qi64MSecTime);

                        emit sigY16Data(qspGuideY16DataInfo);
                    }
                }
            }
        }
    }

    return;
}

void GuideClientManager::onConnectInfraredDevFinished()
{
    logWarning("onConnectInfraredDevFinished1");
    // 如果下一步操作是断开连接才会发送连接结果信号
    if (DISCONNECT_ACTION == m_eNextAction)
    {
        bool bSuccess = m_connectFutureWatcher.result();
        emit sigConnectInfraredDevFinished(bSuccess);
        logWarning("onConnectInfraredDevFinished2");
    }
    logWarning("onConnectInfraredDevFinished3");
}

/***********************************************************
 * 功能：处理RGB数据
 * 输入参数：
 *      stGuideRGBDataInfo：RGB数据信息
 * ********************************************************/
//void GuideClientManager::pfnRGBData(const Guide::GuideRGBDataInfo &stGuideRGBDataInfo)
//{
//    if(!m_bInfraredOpened)
//    {
//        logWarning("infrared device is not be opened.");
//    }

//    return;
//}

/***********************************************************
 * 功能：处理Y16数据
 * 输入参数：
 *      stGuideY16DataInfo：Y16数据信息
 * ********************************************************/
//void GuideClientManager::pfnY16Data(const Guide::GuideY16DataInfo &stGuideY16DataInfo)
//{
//    if(!m_bInfraredOpened)
//    {
//        logWarning("infrared device is not be opened.");
//    }

//    return;
//}

void GuideClientManager::startWatchConnectTimer()
{
    killReConnectTimer();
    killWatchConnectTimer();
    if( -1 == m_iTimerId )
    {
        m_iTimerId = startTimer(3000);
    }
}

void GuideClientManager::killWatchConnectTimer()
{
    if( -1 != m_iTimerId )
    {
        killTimer(m_iTimerId);
        m_iTimerId = -1;
    }
}

void GuideClientManager::startReConnectTimer()
{
    killReConnectTimer();
    if( -1 == m_iReconnectTimerId )
    {
        m_iReconnectTimerId = startTimer(500);
    }
}

void GuideClientManager::killReConnectTimer()
{
    if( -1 != m_iReconnectTimerId )
    {
        killTimer(m_iReconnectTimerId);
        m_iReconnectTimerId = -1;
    }
}

bool GuideClientManager::isWaitForClose()
{
    if(100 == m_iCloseFlag)
    {
        return false;
    }
    else
    {
        return true;
    }
}
/*************************************************
功能： 连接状态定时事件处理
输入参数:
    event -- 事件
*************************************************/
void GuideClientManager::timerEvent(QTimerEvent *e)
{
    qDebug()<<"GuideClientManager::timerEvent";
    if( e->timerId() == m_iTimerId )
    {
        if(qLastConnectTime != qConnectTime)
        {

        }
        else
        {
            emit sigConnectInfraredDevState(false);
            killWatchConnectTimer();
            startReConnectTimer();
        }
        qLastConnectTime = qConnectTime;
    }

    if( e->timerId() == m_iReconnectTimerId )
    {
        qDebug()<<"GuideClientManager::timerEvent1";
        if( !m_bInfraredOpened )
        {
            qDebug()<<"GuideClientManager::timerEvent2";
            Guide::GuideDevInfo stGuideDevInfo;
            GuideClientManager::instance()->connectInfraredDev(stGuideDevInfo);
        }
        else
        {
            GuideClientManager::instance()->insideDisconnectInfraredDev();
        }
    }
}

