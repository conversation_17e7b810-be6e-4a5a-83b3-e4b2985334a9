/*
* Copyright (c) 2016.8，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：CustomApplication.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年11月2日
* 摘要：该文件主要定义自定义QApplication类

* 当前版本：1.0
*/

#ifndef CUSTOMAPPLICATION_H
#define CUSTOMAPPLICATION_H

#include "customapplication_global.h"
#include <QApplication>

#define qCustomApp (static_cast<CustomApplication *>(QCoreApplication::instance()))

class CUSTOMAPPLICATIONSHARED_EXPORT CustomApplication : public QApplication
{
    Q_OBJECT
public:
    /****************************
    函数名： CustomApplication
    功能： 构造函数
    输入参数:argc -- 命令行参数个数
    输出参数：NULL
    返回值：NULL
    *****************************/
    explicit CustomApplication(int &argc,char **argv);

    /****************************
    函数名： notify
    功能： 消息通知，
    输入参数:QObject -- 相应控件
           QEvent -- 事件类型
    输出参数：NULL
    返回值：NULL
    *****************************/
    bool notify(QObject*obj, QEvent *e);

signals:
    /****************************
    函数名： sigPressChanged
    功能： 发送先后点击的控件
    输入参数:old -- 相应控件
           now -- 事件类型
    输出参数：NULL
    返回值：NULL
    *****************************/
    void sigPressChanged( QObject *oldObject,QObject *newObject  );
private:
    QObject *m_pPressedObject;
};

#endif // CUSTOMAPPLICATION_H
