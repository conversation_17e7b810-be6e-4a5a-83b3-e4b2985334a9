#include <QFileInfo>
#include <unistd.h>
#include "taskio.h"
#include "subtask.h"
#include "systemsetting/systemsetservice.h"
QMutex CustomAccessTaskIO::m_fileOpMutex;

CustomAccessTaskIO::CustomAccessTaskIO()
{

}

CustomAccessTaskIO::~CustomAccessTaskIO()
{

}

bool CustomAccessTaskIO::isTestPointExisted( const QVector<CustomAccessTaskNS::TestPointInfo>& vTestPoint, const QString& strId )
{
    bool bRet = false;
    for(int i = 0, iSize = vTestPoint.size(); i < iSize; ++i)
    {
        if(vTestPoint[i].s_strId == strId)
        {
            bRet = true;
            break;
        }
    }
    return bRet;
}

//获取指定任务文件的子任务信息
CustomAccessTaskNS::SubTaskInfo CustomAccessTaskIO::getSubTaskInfo( const QString& filePath )
{
    IOStateType eType = checkFileValidity( filePath );
    CustomAccessTaskNS::SubTaskInfo stSubTaskInfo;
    if( OPERATE_SUCCESS == eType )
    {
        XMLDocument doc( filePath, QIODevice::ReadOnly,CustomAccessTaskIONS::MAIN_TASK_NODE, "utf-8", false );
        if( doc.isValid() )
        {
            doc.beginElement( CustomAccessTaskIONS::ROOT_NODE );
            doc.beginElement( CustomAccessTaskIONS::MAIN_TASK_NODE );
            doc.beginElement( CustomAccessTaskIONS::SUB_TASK_NODE );
            stSubTaskInfo.s_strId   = doc.value(CustomAccessTaskIONS::ID);
            stSubTaskInfo.s_strName = doc.value(CustomAccessTaskIONS::NAME);
            stSubTaskInfo.s_eType   = (CustomAccessTaskNS::TaskType)doc.value(CustomAccessTaskIONS::TYPE).toInt();
            doc.endElement();
        }
        else
        {
            qWarning( "TaskFileIO::getSubTaskInfo open xml doc failed" );
        }
    }
    else
    {
        qWarning("TaskFileIO::getSubTaskInfo file is not valid");
    }
    return stSubTaskInfo;
}

//获取指定任务文件的主任务信息
CustomAccessTaskNS::MainTaskInfo CustomAccessTaskIO::getMainTaskInfo( const QString& filePath )
{
    IOStateType eType = checkFileValidity( filePath );
    CustomAccessTaskNS::MainTaskInfo stMainTaskInfo;
    if( OPERATE_SUCCESS == eType )
    {
        XMLDocument doc( filePath, QIODevice::ReadOnly, CustomAccessTaskIONS::MAIN_TASK_NODE, "utf-8", false );
        if( doc.isValid() )
        {
            doc.beginElement( CustomAccessTaskIONS::ROOT_NODE );
            doc.beginElement( CustomAccessTaskIONS::MAIN_TASK_NODE );
            stMainTaskInfo.s_strId = doc.value(CustomAccessTaskIONS::ID);
            stMainTaskInfo.s_strName = doc.value(CustomAccessTaskIONS::NAME);
            stMainTaskInfo.s_eType =(CustomAccessTaskNS::TaskType) doc.value(CustomAccessTaskIONS::TYPE).toUInt();
            doc.endElement();
        }
        else
        {
            qWarning( "TaskFileIO::getSubTaskInfo open xml doc failed" );
        }
    }
    else
    {
        qWarning("TaskFileIO::getSubTaskInfo file is not valid");
    }
    return stMainTaskInfo;
}

/************************************************
 * 功能: 读任务文件
 * 入参：strFilePath -- 文件路径
 * 出参：subTask -- 保存解析出来属性的子任务实例
 * 返回值：I/O错误类型
 ************************************************/
CustomAccessTaskIO::IOStateType CustomAccessTaskIO::readTaskFile( const QString &strFilePath, SubTask *pSubTask )
{
    QMutexLocker qMutexLocker(&m_fileOpMutex);  //获取文件操作权限
    IOStateType eType = checkFileValidity( strFilePath );
    if( OPERATE_SUCCESS == eType )
    {
        QVector<int> vTestpointSn;
        //解析主任务信息
        QDomDocument document;
        QFile file(strFilePath);
        document.setContent(&file);
        QDomElement root = document.documentElement();
        QDomElement verElem = root.firstChildElement( CustomAccessTaskIONS::VERSION_NODE );
        if( !verElem.isNull() )
        {
            pSubTask->m_strVersion = verElem.toElement().text();
        }
        QDomElement domElement = root.firstChildElement( CustomAccessTaskIONS::MAIN_TASK_NODE );
        pSubTask->m_sMainTaskInfo.s_strId = domElement.toElement().attribute(CustomAccessTaskIONS::ID);
        pSubTask->m_sMainTaskInfo.s_strName = domElement.toElement().attribute(CustomAccessTaskIONS::NAME);
        pSubTask->m_sMainTaskInfo.s_eType =( CustomAccessTaskNS::TaskType)domElement.toElement().attribute(CustomAccessTaskIONS::TYPE).toUInt();

        //解析子任务信息
        domElement = domElement.firstChildElement( CustomAccessTaskIONS::SUB_TASK_NODE );
        pSubTask->m_sSubTaskInfo.s_strId = domElement.toElement().attribute(CustomAccessTaskIONS::ID);
        pSubTask->m_sSubTaskInfo.s_strName = domElement.toElement().attribute(CustomAccessTaskIONS::NAME);
        pSubTask->m_sSubTaskInfo.s_eType =( CustomAccessTaskNS::TaskType)domElement.toElement().attribute(CustomAccessTaskIONS::TYPE).toUInt();


        bool bBgGapExist = false;
        CustomAccessTaskNS::GapInfo bgGapInfo;
        QStringList bgFileList;
        //解析间隔信息
        QDomNodeList gapDomList = domElement.childNodes();
        for( int iGap = 0, iGapSize = gapDomList.size(); iGap < iGapSize; ++iGap )
        {
            QDomNode gapDom = gapDomList.at( iGap );
            CustomAccessTaskNS::GapInfo gapInfo;
            gapInfo.s_strId = gapDom.toElement().attribute(CustomAccessTaskIONS::ID);
            gapInfo.s_strName = gapDom.toElement().attribute(CustomAccessTaskIONS::NAME);

            //解析测点信息
            QDomNodeList domElementList = gapDom.childNodes();
            for( int iTestData = 0, iTestSize = domElementList.size(); iTestData < iTestSize; ++iTestData )
            {
                QDomNode tempDom = domElementList.at(iTestData);
                QString strId = tempDom.toElement().attribute(CustomAccessTaskIONS::ID);
                QString strName = tempDom.toElement().attribute(CustomAccessTaskIONS::NAME);
                QString strPart = tempDom.toElement().attribute(CustomAccessTaskIONS::PART);
                QString strFileName = tempDom.toElement().attribute(CustomAccessTaskIONS::FILENAME);
                QString strBgFileList = tempDom.toElement().attribute(CustomAccessTaskIONS::BGFILENAME);
                QString strUploaded = tempDom.toElement().attribute(CustomAccessTaskIONS::FILEUPLOADED);
                QString strOrder = tempDom.toElement().attribute(CustomAccessTaskIONS::ORDER);
                CustomAccessTaskNS::DiagnoseType eDiagRet = (CustomAccessTaskNS::DiagnoseType)(tempDom.toElement().attribute(CustomAccessTaskIONS::DIAGNOSERET).toInt());

                int iOrder = -1;
                if(!strOrder.isEmpty())
                {
                    iOrder = strOrder.toInt();
                }

                if( strId.isEmpty() )//id为空的测点项为背景文件
                {
                    if( !strFileName.isEmpty() )
                    {
                        bgFileList.append( strFileName );
                    }
                }
                else
                {
                    //如果测点不存在，增加测点
                    if( !isTestPointExisted( gapInfo.s_vTestPoints, strId ) )
                    {
                        vTestpointSn.append( iOrder );

                        CustomAccessTaskNS::TestPointInfo tmpPoint;
                        tmpPoint.s_strId = strId;
                        tmpPoint.s_strName = strName;
                        tmpPoint.s_strPart = strPart;
                        tmpPoint.s_iOrder = iOrder;

                        bool bInserted = false;
                        for( int i = 0, iSize = gapInfo.s_vTestPoints.size(); i < iSize; ++i )
                        {
                            if( tmpPoint.s_iOrder < gapInfo.s_vTestPoints.at(i).s_iOrder )
                            {
                                gapInfo.s_vTestPoints.insert( i, tmpPoint );
                                bInserted = true;
                                break;
                            }
                        }
                        if( !bInserted )
                        {
                            gapInfo.s_vTestPoints.append( tmpPoint );
                        }
                    }
                    //如果测点存在，将该项作为测试项数据，添加到对应测点中
                    if( (!strFileName.isEmpty()) && (!strBgFileList.isEmpty()) )
                    {
                        QStringList tmpList = strBgFileList.split(CustomAccessTaskNS::BG_FILE_SEPARATOR);
                        for( int iTb = 0, iSize = gapInfo.s_vTestPoints.size(); iTb < iSize; ++iTb )
                        {
                            if( strId == gapInfo.s_vTestPoints.at(iTb).s_strId )
                            {
                                CustomAccessTaskNS::TestData tmpData;
                                tmpData.s_strFileName = strFileName;
                                tmpData.s_strBgFileList = tmpList;
                                tmpData.s_bUploaded = (strUploaded.toInt() > 0);//非0代表已上传
                                tmpData.eDiagRet = eDiagRet;
                                SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
                                if(SystemSet::ACCESS_PROTO_TJXJ == eProtocol)
                                {
                                    CustomAccessTaskNS::TestType eTestType = (CustomAccessTaskNS::TestType)(tempDom.toElement().attribute(CustomAccessTaskIONS::TYPE).toInt());
                                    tmpData.eTestType= eTestType;
                                    if(CustomAccessTaskNS::AEType == eTestType)
                                    {
                                        QString strPeakValue = tempDom.toElement().attribute(CustomAccessTaskIONS::PEAKVALUE);
                                        QString strFirstFreq = tempDom.toElement().attribute(CustomAccessTaskIONS::FIRSTFREQ);
                                        QString strSecFreq = tempDom.toElement().attribute(CustomAccessTaskIONS::SECONDFREQ);
                                        QString strRms = tempDom.toElement().attribute(CustomAccessTaskIONS::RMS);
                                        tmpData.fFirstFreqComValue  = strFirstFreq.toFloat();
                                        tmpData.fSecondFreqComValue = strSecFreq.toFloat();
                                        tmpData.fRMS                = strRms.toFloat();
                                        tmpData.fPeakValue          = strPeakValue.toFloat();
                                    }
                                    else
                                    {
                                        QString strMaxValue = tempDom.toElement().attribute(CustomAccessTaskIONS::MAXVALUE);
                                        tmpData.usMaxValue = strMaxValue.toInt();
                                    }
                                    QString strBgMaxValue = tempDom.toElement().attribute(CustomAccessTaskIONS::BGMAXVALUE);
                                    tmpData.fBgMaxValue   = strBgMaxValue.toFloat();
                                }
                                gapInfo.s_vTestPoints[iTb].s_vData.append( tmpData );
                                break;
                            }
                        }
                    }
                }
            }

            if( gapInfo.s_strId.isEmpty() )
            {
                bBgGapExist = true;
                bgGapInfo = gapInfo;
            }
            else
            {
                if( !gapInfo.s_vTestPoints.isEmpty() )
                {
                    bool bInserted = false;
                    for( int i = 0, iSize = pSubTask->m_vGaps.size(); i < iSize; ++i )
                    {
                        if( !pSubTask->m_vGaps.at(i).s_vTestPoints.isEmpty() )
                        {
                            if( gapInfo.s_vTestPoints.first().s_iOrder < pSubTask->m_vGaps[i].s_vTestPoints.first().s_iOrder )
                            {
                                pSubTask->m_vGaps.insert( i, gapInfo );
                                bInserted = true;
                                break;
                            }
                        }
                    }
                    if( !bInserted )
                    {
                        pSubTask->m_vGaps.append( gapInfo );
                    }
                }
            }
        }

        if( bBgGapExist )
        {
            CustomAccessTaskNS::TestPointInfo tmpPoint;
            tmpPoint.s_strId = "";
            tmpPoint.s_strName = COMMON_VIEW_CONFIG_TRANSLATE(BG_POINT);
            tmpPoint.s_strPart = "";
            tmpPoint.s_iOrder = 0;

            for( int iData = 0, iSize = bgFileList.size(); iData < iSize; ++iData )
            {
                CustomAccessTaskNS::TestData tmpData;
                tmpData.s_strFileName = bgFileList.at(iData);
                tmpData.s_strBgFileList.append( bgFileList.at(iData) );
                tmpData.eDiagRet = CustomAccessTaskNS::DiagNormal;
                tmpPoint.s_vData.append( tmpData );
            }
            bgGapInfo.s_vTestPoints.append( tmpPoint );
            bgGapInfo.m_bgHistoryFileList = bgFileList;
            bgGapInfo.m_strBGFileList = bgFileList;
            bgGapInfo.s_strName = COMMON_VIEW_CONFIG_TRANSLATE(BG_INTERVAL);

            pSubTask->m_vGaps.insert( 0, bgGapInfo );
        }
        else
        {
            if(CustomAccessTaskNS::InfraredType != pSubTask->m_sMainTaskInfo.s_eType)
            {
                bgGapInfo.s_strId = "";
                bgGapInfo.s_strName = COMMON_VIEW_CONFIG_TRANSLATE(BG_INTERVAL);

                CustomAccessTaskNS::TestPointInfo tmpPoint;
                tmpPoint.s_strId = "";
                tmpPoint.s_strName = COMMON_VIEW_CONFIG_TRANSLATE(BG_POINT);
                tmpPoint.s_strPart = "";
                tmpPoint.s_iOrder = 0;
                bgGapInfo.s_vTestPoints.append( tmpPoint );

                pSubTask->m_vGaps.insert( 0, bgGapInfo );
            }
        }
        vTestpointSn.append( 0 );

        //reset test order
        if( !vTestpointSn.isEmpty() )
        {
            qSort(vTestpointSn.begin(),vTestpointSn.end());

            for( int iGap = 0, iSize = pSubTask->m_vGaps.size(); iGap < iSize; ++iGap )
            {
                for( int iTb = 0, iTbSize = pSubTask->m_vGaps.at(iGap).s_vTestPoints.size(); iTb < iTbSize; ++iTb )
                {
                    int iSn = pSubTask->m_vGaps[iGap].s_vTestPoints[iTb].s_iOrder;
                    if( iSn >= 0 )
                    {
                        int iIndex = vTestpointSn.indexOf( iSn );
                        pSubTask->m_vGaps[iGap].s_vTestPoints[iTb].s_iOrder = iIndex;
                        if( iIndex >= 0 )
                        {
                            vTestpointSn[iIndex] = -1;
                        }
                    }
                }
            }
        }
        //get the latest test order
        int iLatestOrder = -1;
        for( int iGap = 0, iSize = pSubTask->m_vGaps.size(); iGap < iSize; ++iGap )
        {
            for( int iTb = 0, iTbSize = pSubTask->m_vGaps.at(iGap).s_vTestPoints.size(); iTb < iTbSize; ++iTb )
            {
                CustomAccessTaskNS::SubTaskTestState eState = pSubTask->m_vGaps.at(iGap).s_vTestPoints.at(iTb).pointTestState();
                if( eState == CustomAccessTaskNS::TASK_TEST )
                {
                    if( iLatestOrder < pSubTask->m_vGaps[iGap].s_vTestPoints[iTb].s_iOrder )
                    {
                        iLatestOrder = pSubTask->m_vGaps[iGap].s_vTestPoints[iTb].s_iOrder;
                    }
                }
            }
        }
        pSubTask->m_iLatestOrder = iLatestOrder;
    }
    else
    {
        qWarning("CustomAccessTaskIO::readTaskFile file is not valid");
    }
    return eType;
}

/************************************************
 * 功能: 写任务文件
 * 入参：subTask -- 子任务相关信息
 * 返回值：I/O错误类型
 ************************************************/
CustomAccessTaskIO::IOStateType CustomAccessTaskIO::writeTaskFile( SubTask* subTask, const QString &strFilePath )
{
    SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
    QMutexLocker qMutexLocker(&m_fileOpMutex);  //获取文件操作权限
    IOStateType eType = checkFileValidity( strFilePath );
    if( OPERATE_SUCCESS == eType )
    {
        XMLDocument doc( strFilePath, QIODevice::ReadWrite,CustomAccessTaskIONS::ROOT_NODE, "utf-8", false );
        if( doc.isValid() )
        {
            doc.beginElement( CustomAccessTaskIONS::VERSION_NODE );
            doc.setText( subTask->m_strVersion );
            doc.endElement();
            //更新任务信息
            doc.beginElement( CustomAccessTaskIONS::MAIN_TASK_NODE );
            doc.beginElement( CustomAccessTaskIONS::SUB_TASK_NODE );
            doc.setAttribute( CustomAccessTaskIONS::SUB_TASK_STATE, QString::number( (int)subTask->taskTestState() ) );
            doc.setAttribute( CustomAccessTaskIONS::SUB_TASK_UPLOADABLE, QString::number( subTask->isTaskUploadable() ? 1 : 0 ) );

            QList<QDomElement> gapDomList = doc.childElement( CustomAccessTaskIONS::CLEARANCE_NODE );
            bool bBgSaved = false;
            for( int iGap = 0, iSize = gapDomList.size(); iGap < iSize; ++iGap )
            {
                doc.beginElement( gapDomList.at(iGap) );
                QString strGapId = doc.attribute( CustomAccessTaskIONS::ID );
                if( strGapId.isEmpty() )
                {
                    bBgSaved = true;
                }
                doc.endElement();
            }
            if( !bBgSaved )
            {
                QDomElement gapElem = doc.addElement( CustomAccessTaskIONS::CLEARANCE_NODE );
                doc.beginElement( gapElem );
                doc.setAttribute( CustomAccessTaskIONS::ID, "" );
                doc.setAttribute( CustomAccessTaskIONS::NAME, "背景间隔" );
                doc.endElement();
                gapDomList.insert( 0, gapElem );
            }

            for( int iGap = 0, iSize = gapDomList.size(); iGap < iSize; ++iGap )
            {
                doc.beginElement( gapDomList.at(iGap) );
                QString strGapId = doc.attribute( CustomAccessTaskIONS::ID );

                //将原有测点条目全部删除，以便后继存储
                while( doc.childElement( CustomAccessTaskIONS::TEST_POINT_NODE ).size() > 0 )
                {
                    doc.removeElement( CustomAccessTaskIONS::TEST_POINT_NODE );
                }

                CustomAccessTaskNS::GapInfo gapInfo = subTask->gapInfo( strGapId );
                //存储间隔下背景文件
                /*
                for( int iBg = 0; iBg < gapInfo.m_bgHistoryFileList.size(); iBg++ )
                {
                    QDomElement elementTestData = doc.addElement( CustomAccessTaskIONS::TEST_POINT_NODE );
                    doc.beginElement( elementTestData );
                    doc.setAttribute( CustomAccessTaskIONS::ID, "" );
                    doc.setAttribute( CustomAccessTaskIONS::NAME, "" );
                    doc.setAttribute( CustomAccessTaskIONS::PART, "" );
                    doc.setAttribute( CustomAccessTaskIONS::FILENAME, gapInfo.m_bgHistoryFileList.at(iBg) );
                    doc.setAttribute( CustomAccessTaskIONS::BGFILENAME, "" );
                    doc.endElement();
                }
                */
                //存储测点条目
                for( int iTestPoint = 0, iSize = gapInfo.s_vTestPoints.size(); iTestPoint < iSize; ++iTestPoint )
                {
                    CustomAccessTaskNS::TestPointInfo tmpPoint = gapInfo.s_vTestPoints.at( iTestPoint );
                    //先保存测点描述信息
                    QDomElement elementTestData = doc.addElement( CustomAccessTaskIONS::TEST_POINT_NODE );
                    doc.beginElement( elementTestData );
                    doc.setAttribute( CustomAccessTaskIONS::ID, tmpPoint.s_strId );
                    doc.setAttribute( CustomAccessTaskIONS::NAME, tmpPoint.s_strName );
                    doc.setAttribute( CustomAccessTaskIONS::PART, tmpPoint.s_strPart );
                    doc.setAttribute( CustomAccessTaskIONS::ORDER, QString::number( tmpPoint.s_iOrder ) );
                    doc.endElement();

                    //将测点下每个测试项保存为一个测点条目
                    for( int iTestData = 0, iSize = tmpPoint.s_vData.size(); iTestData < iSize; ++iTestData )
                    {
                        QDomElement tmpElem = doc.addElement( CustomAccessTaskIONS::TEST_POINT_NODE );
                        doc.beginElement( tmpElem );
                        doc.setAttribute( CustomAccessTaskIONS::ID, tmpPoint.s_strId );
                        doc.setAttribute( CustomAccessTaskIONS::NAME, tmpPoint.s_strName );
                        doc.setAttribute( CustomAccessTaskIONS::PART, tmpPoint.s_strPart );
                        doc.setAttribute( CustomAccessTaskIONS::ORDER, QString::number( tmpPoint.s_iOrder ) );
                        doc.setAttribute( CustomAccessTaskIONS::FILENAME, tmpPoint.s_vData.at(iTestData).s_strFileName );
                        doc.setAttribute( CustomAccessTaskIONS::BGFILENAME,
                                          tmpPoint.s_vData.at(iTestData).s_strBgFileList.join(CustomAccessTaskNS::BG_FILE_SEPARATOR) );
                        doc.setAttribute(CustomAccessTaskIONS::DIAGNOSERET, QString::number(tmpPoint.s_vData.at(iTestData).eDiagRet));
                        doc.setAttribute(CustomAccessTaskIONS::TYPE,       QString::number(tmpPoint.s_vData.at(iTestData).eTestType));
                        if(SystemSet::ACCESS_PROTO_TJXJ == eProtocol)
                        {
                            if(CustomAccessTaskNS::AEType == tmpPoint.s_vData.at(iTestData).eTestType)
                            {
                                doc.setAttribute(CustomAccessTaskIONS::FIRSTFREQ,  QString::number(tmpPoint.s_vData.at(iTestData).fFirstFreqComValue));
                                doc.setAttribute(CustomAccessTaskIONS::SECONDFREQ, QString::number(tmpPoint.s_vData.at(iTestData).fSecondFreqComValue));
                                doc.setAttribute(CustomAccessTaskIONS::RMS,        QString::number(tmpPoint.s_vData.at(iTestData).fRMS));
                                doc.setAttribute(CustomAccessTaskIONS::PEAKVALUE,  QString::number(tmpPoint.s_vData.at(iTestData).fPeakValue));
                            }
                            else
                            {
                                doc.setAttribute(CustomAccessTaskIONS::MAXVALUE, QString::number(tmpPoint.s_vData.at(iTestData).usMaxValue));
                            }
                            doc.setAttribute(CustomAccessTaskIONS::BGMAXVALUE,   QString::number(tmpPoint.s_vData.at(iTestData).fBgMaxValue));
                        }
                        doc.endElement();
                    }
                }

                doc.endElement();
            }

            doc.endElement();
            doc.endElement();
            doc.save();     // 完成写入
        }
        else
        {
            qWarning("TaskFileIO::writeTaskFile open xml doc failed");
        }
    }
    else
    {
        qWarning("TaskFileIO::writeTaskFile file is not valid");
    }

#ifdef Q_OS_LINUX
    sync();
#endif

    return eType;

}


/************************************************
 * 功能: 文件是否有效
 * 入参：strFilePath -- 文件绝对路径
 * 返回值：读写错误类型
 ************************************************/
CustomAccessTaskIO::IOStateType CustomAccessTaskIO::checkFileValidity( const QString &strFilePath )
{
    CustomAccessTaskIO::IOStateType eType = OPERATE_SUCCESS;
    QFile file( strFilePath );
    if( file.exists() )                             // 判断文件是否存在
    {
        QFileInfo fileInfo( file );
        QString strSuffix = '.' + fileInfo.suffix();
        if( strSuffix != CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_SUFFIX )     // 判断文件后缀是否正确   todo
        {
            eType = FILE_TYPE_NOT_VALID;
        }
    }
    else
    {
        eType = FILE_NOT_EXIST;
    }
    return eType;
}
