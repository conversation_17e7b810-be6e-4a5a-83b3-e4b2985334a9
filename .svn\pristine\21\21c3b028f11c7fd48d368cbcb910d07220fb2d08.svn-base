/*
* Copyright (c) 2017.02，南京华乘电气科技有限公司
* All rights reserved.
*
* PulseView.cpp
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年2月15日
*       新版本重构
* 摘要：CA Pulse图谱定义

* 当前版本：1.0
*/

#include "PulseView.h"
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QtConcurrentRun>
#include <QMainWindow>
#include "ca/CAView.h"
#include "ca/CAViewConfig.h"
#include "controlButton/ControlButtonInfo.h"
#include "buttonBar/PushButtonBar.h"
#include "controlButton/CmdButton.h"
#include "controlButton/RadioButton.h"
#include "controlButton/SliderButton.h"
#include "ca/CAConfig.h"
#include "window/Window.h"
#include "chartwidget/ChartWidget.h"
#include "messageBox/msgbox.h"
#include "dataSave/DataFileInfos.h"
#include "deleteDataView/DeleteDataView.h"
#include "View.h"
#include "ca/dataSave/AccessG100PulseDataSave.h"
#include "playbackView/PlayBackView.h"
#include "pulseplaybackview.h"
#include "pulseanalysisview.h"
#include "messageBox/msgbox.h"
#include "datadefine.h"
#include "appconfig.h"
#include "global_log.h"

const int PULSE_TRIGGER_VALUE_MAX = 2000;

typedef enum _PulseButton
{
    BUTTON_NONE = -1,
    BUTTON_SAMPLE,      //采样  0
    BUTTON_CLUSTER_ANALYSIS, // 聚类分析 1
    BUTTON_TRIGGER_THREASHOLD,//触发阈值 2
    BUTTON_TRIGGER_WIDTH,//触发宽度 3
    BUTTON_SAVE_DATA,//保存数据 4
    BUTTON_GAIN,//增益 5
    BUTTON_PHASE_ALIAS,//相位偏移 6
    BUTTON_MENU,    //配置 7

    //more...
    //BUTTON_SAMPLE_RATE,//采样率 8
    BUTTON_PERCENTAGE_BEFORE_TRIG,//触发前百分比 8
    BUTTON_PULSE_ACCU_COUNT,//脉冲累积个数 9
    //BUTTON_RANGE_MAX,//量程 10
    BUTTON_LOAD_DATA,//载入数据 10
    BUTTON_DELETE_DATA,//删除数据 11
    BUTTON_RESTORE_DEFAULT,//恢复默认 12
}PulseButton;


//相位偏移
const ButtonInfo::SliderValueConfig s_CAPhaseAliasCfg =
{
    0, 360, 5
};

//采样率
const ButtonInfo::RadioValueConfig s_CASampleRateCfg =
{
    CAViewConfig::TEXT_SAMPLE_RATE_OPITIONS, sizeof(CAViewConfig::TEXT_SAMPLE_RATE_OPITIONS)/sizeof(char*)
};

//触发前采样长度占比
const ButtonInfo::SliderValueConfig s_CABeforeTrigRatioCfg =
{
    5, 50, 1
};

//脉冲累积个数
const ButtonInfo::RadioValueConfig s_CAPulseAccuCountCfg =
{
    CAViewConfig::TEXT_PULSE_ACCU_COUNT_OPITIONS, sizeof(CAViewConfig::TEXT_PULSE_ACCU_COUNT_OPITIONS)/sizeof(char*)
};

//触发幅值
const ButtonInfo::SliderValueConfig s_CATrigThreasholdCfg =
{
    0, PULSE_TRIGGER_VALUE_MAX, 1
};

//增益
const ButtonInfo::RadioValueConfig s_CAGainCfg =
{
    CAViewConfig::TEXT_GAIN_OPITIONS, sizeof(CAViewConfig::TEXT_GAIN_OPITIONS)/sizeof(char*)
};

//
const ButtonInfo::RadioValueConfig s_CATriggerWidthCfg =
{
    CAViewConfig::TEXT_TRIGGER_WIDTH_OPITIONS, sizeof(CAViewConfig::TEXT_TRIGGER_WIDTH_OPITIONS)/sizeof(char*)
};

//量程最大值  10mV~40V
const ButtonInfo::RadioValueConfig s_CARangeMaxCfg =
{
    CAViewConfig::TEXT_RANGE_MAX_OPITIONS, sizeof(CAViewConfig::TEXT_RANGE_MAX_OPITIONS)/sizeof(char*)
};

//控制按钮定义
const ButtonInfo::Info s_PulseButtonInfo[] =
{
    { BUTTON_SAMPLE, { ButtonInfo::COMMAND, CAViewConfig::TEXT_START_SAMPLE, NULL, NULL, NULL } },//
    { BUTTON_CLUSTER_ANALYSIS, { ButtonInfo::COMMAND, CAViewConfig::TEXT_ANALYZE, NULL, NULL, NULL } },//
    { BUTTON_TRIGGER_THREASHOLD, { ButtonInfo::FIXED_STEP_SLIDER, CAViewConfig::TEXT_TRIGGER_VALUE, CAViewConfig::TEXT_MV, NULL, &s_CATrigThreasholdCfg } },//
    { BUTTON_TRIGGER_WIDTH, { ButtonInfo::RADIO, CAViewConfig::TEXT_TRIGGER_WIDTH, NULL, NULL, &s_CATriggerWidthCfg } },//
    { BUTTON_SAVE_DATA, { ButtonInfo::COMMAND, CAViewConfig::TEXT_SAVE_DATA, NULL, NULL, NULL } },//
    { BUTTON_GAIN, { ButtonInfo::RADIO, CAViewConfig::TEXT_GAIN, NULL, NULL, &s_CAGainCfg  } },//
    { BUTTON_PHASE_ALIAS, { ButtonInfo::FIXED_STEP_SLIDER, CAViewConfig::TEXT_PHASE_ALIAS, CAViewConfig::TEXT_DEGREE, NULL, &s_CAPhaseAliasCfg } },//
    { BUTTON_MENU, { ButtonInfo::COMMAND, CAViewConfig::TEXT_CONFIG_MENU, NULL, NULL, NULL } }//
};

//"更多.."按钮定义
const ButtonInfo::Info s_PulseButtonInfoMore[] =
{
    //{ BUTTON_SAMPLE_RATE, { ButtonInfo::RADIO, CAViewConfig::TEXT_SAMPLE_RATE, NULL, NULL, &s_CASampleRateCfg } },//
    { BUTTON_PERCENTAGE_BEFORE_TRIG, { ButtonInfo::FIXED_STEP_SLIDER, CAViewConfig::TEXT_PERCENTAGE_BEFORE_TRIG, CAViewConfig::TEXT_PERCENTAGE, NULL, &s_CABeforeTrigRatioCfg } },//
    { BUTTON_PULSE_ACCU_COUNT, { ButtonInfo::RADIO, CAViewConfig::TEXT_PULSE_ACCU_COUNT, NULL, NULL, &s_CAPulseAccuCountCfg } },//
    //{ BUTTON_RANGE_MAX, { ButtonInfo::RADIO, CAViewConfig::TEXT_RANGE, NULL, NULL, &s_CARangeMaxCfg } },//
    { BUTTON_LOAD_DATA, { ButtonInfo::COMMAND, CAViewConfig::TEXT_LOAD_DATA, NULL, NULL, NULL } },//
    { BUTTON_DELETE_DATA, { ButtonInfo::COMMAND, CAViewConfig::TEXT_DELETE_DATA, NULL, NULL, NULL } },//
    { BUTTON_RESTORE_DEFAULT, { ButtonInfo::COMMAND, CAViewConfig::TEXT_RESTORE_DEFAULT, NULL, NULL, NULL } }//
};

const QString STATUS_LABEL_CONNECTED = "QLabel{border:none;color:green;font-size:18px;}";
const QString STATUS_LABEL_DISCONNECTED = "QLabel{border:none;color:blue;font-size:18px;}";
const QString GAIN_STATE_STYLE = "QLabel{border:none;color:red;font-size:18px;}";
const QString PULSE_NUMBER_LABEL_STYLESHEET = "QLabel{border:none;color:black;font-size:18px;}";


const float SAVE_PROGRESS_STEP = 0.1; //0.8% per half a second

const UINT16 SAVE_PROGRESS_SIZE_WIDTH = SCREEN_WIDTH * 3 / 4;
const UINT16 SAVE_PROGRESS_SIZE_HEIGHT = SCREEN_HEIGHT / 6;

const double US_PER_SEC = 1e6;
const double MHZ_PER_HZ = 1e-6;
const double HZ_PER_10KHZ = 1e4;
const QString Y_LEFT_TITLE = "f [ MHz ]";
const QString Y_LEFT_TITLE_KHZ = "f [ KHz ]";
const QString Y_LEFT_TITLE_US = "t [ us ]";
const int MHZ_TO_KHZ = 1000;


/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
PulseView::PulseView( const QString& strTitle, QWidget *parent )
    :SampleChartView( strTitle, parent )
{
    setFixedSize( Window::WIDTH, Window::HEIGHT );
    //初始化使用的数据
    initDatas();

    initSaveProgress();

    //创建图谱
    ChartWidget *pWidget = createChartLayout(parent);
    setChart( pWidget );

    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar( CAViewConfig::CONTEXT, s_PulseButtonInfo, sizeof(s_PulseButtonInfo)/sizeof(ButtonInfo::Info) );
    m_pSampleBtn = pButtonBar->button(BUTTON_SAMPLE);


    //创建更多设置栏
    createMoreConfigButtonBar(CAViewConfig::CONTEXT, s_PulseButtonInfoMore, sizeof(s_PulseButtonInfoMore)/sizeof(ButtonInfo::Info));

    setSampleRelatedBtns();

    //设置按钮数据
    setButtonBarDatas();

    //设置图谱数据
    setChartDatas();

    m_pCAService = CAService::instance();
    connectSigsWithService();
    setConnectStatusNotice();
    m_pCAService->setExpectedPulseCnt(CA::g_ausPulseAccuCount[m_ePulseAccuCnt]);

    if(!m_pCAService->hasConnectedDevice())
    {
        m_iSearchingConditionerTimerId = startTimer(CA::SEARCH_CONDITIONER_TIMER_INTERVAL_2S);
    }
}


/*************************************************
功能： 析构
*************************************************************/
PulseView::~PulseView( )
{
    bool  bSaveDataFutureRunning = m_SaveDataFuture.isRunning();
    if(bSaveDataFutureRunning)
    {
        m_SaveDataFuture.waitForFinished();
    }

    saveConfig();//存储到配置文件中

    if(m_eState == CA::STATE_SAMPLING)
    {
        m_pCAService->stopSample();
    }
    disconnect(m_pCAService, 0, this, 0);
}

void PulseView::disableSampleRelatedBtns()
{
    for(int i = 0; i < m_vecSampleRelatedBtn.size(); i ++)
    {
        PopupButton *pBtn = m_vecSampleRelatedBtn.at(i);
        pBtn->setDisabled(true);
    }
}

void PulseView::enableSampleRelatedBtns()
{
    for(int i = 0; i < m_vecSampleRelatedBtn.size(); i ++)
    {
        PopupButton *pBtn = m_vecSampleRelatedBtn.at(i);
        pBtn->setDisabled(false);
    }
}

void PulseView::setSampleRelatedBtns()
{
    m_vecSampleRelatedBtn.clear();

    PopupButton *pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_TRIGGER_WIDTH)));
    m_vecSampleRelatedBtn.append(pBtn);

    pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_GAIN)));
    m_vecSampleRelatedBtn.append(pBtn);

    pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_PHASE_ALIAS)));
    m_vecSampleRelatedBtn.append(pBtn);

    //    pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_SAMPLE_RATE)));
    //    m_vecSampleRelatedBtn.append(pBtn);

    pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_PERCENTAGE_BEFORE_TRIG)));
    m_vecSampleRelatedBtn.append(pBtn);

    pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_RESTORE_DEFAULT)));
    m_vecSampleRelatedBtn.append(pBtn);

    pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_CLUSTER_ANALYSIS)));
    m_vecSampleRelatedBtn.append(pBtn);

    pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_SAVE_DATA)));
    m_vecSampleRelatedBtn.append(pBtn);

    //    pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_RANGE_MAX)));
    //    m_vecSampleRelatedBtn.append(pBtn);

    pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_TRIGGER_THREASHOLD)));
    m_vecSampleRelatedBtn.append(pBtn);

    pBtn = ((PopupButton*)(buttonBar()->button(BUTTON_PULSE_ACCU_COUNT)));
    m_vecSampleRelatedBtn.append(pBtn);
}

void PulseView::changeRange(quint32 uiOldRange)
{
    Q_UNUSED(uiOldRange)
    m_uiRangeMax = CA::g_ausRange[m_eGain];

    if(m_uiRangeMax < CA::MV_PER_V)
    {
        m_pPrpdFast->setRangeUnitMax(CA::PRPD_RANGE_UNIT_MV, m_uiRangeMax);
    }
    else
    {
        m_pPrpdFast->setRangeUnitMax(CA::PRPD_RANGE_UNIT_V, m_uiRangeMax / CA::MV_PER_V);
    }

    if(m_uiRangeMax < m_usTrigThreashold)
    {
        m_usTrigThreashold = m_uiRangeMax;
    }

    resetTriggerValueScope();

    m_pPrpdFast->setThreshold( thresholdRatio() );
    m_pConfig->beginGroup( Module::GROUP_CA );
    m_pConfig->setValue( m_usTrigThreashold, CAConfig::KEY_PRPS_THREASHOLD );
    m_pConfig->endGroup();

    return;
}

/************************************************
 * 函数名   : onDisconnectConnection
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 槽函数， 断开连接处理(客户端连接断开/配置失败/写校准参数失败)
 ************************************************/
void PulseView::onDisconnectConnection(QString strMac)
{
    dbg_info("get disconnected signal");
    Q_UNUSED(strMac);
    m_pConnectStatusLabel->setStyleSheet( STATUS_LABEL_DISCONNECTED );
    m_pConnectStatusLabel->setText(  qApp->translate("CAView",(CAViewConfig::TEXT_CONNECTION_DISCONNECTED )));

    if(m_eState == CA::STATE_SAMPLING)
    {
        m_eState = CA::STATE_UNCONNECTED;
        enableSampleRelatedBtns();
        setSampleBtnText(m_eState);
    }
    else
    {
        m_eState = CA::STATE_UNCONNECTED;
    }

    MsgBox::information( "", QObject::trUtf8("Disconnected!") );

    m_uiSearchConditionerTimes = 0;
    if(m_iSearchingConditionerTimerId == CA::INVALID_TIMER_ID)
    {
        m_iSearchingConditionerTimerId = startTimer(CA::SEARCH_CONDITIONER_TIMER_INTERVAL_2S);
    }
}

bool PulseView::setTrigParameters()
{
    CA::TrigParam stTrigParam;
    stTrigParam.fTrigLevel = m_usTrigThreashold;
    stTrigParam.usTimeVal = CAViewConfig::TRIGGER_WIDTH_OPITIONS[m_eTriggerWidth];
    stTrigParam.eTimeUnit = CA::TIME_UNIT_US;
    stTrigParam.fPercent = m_ucPercentageBeforeTrig / 100.0;
    bool bResult = m_pCAService->setTrigParam(stTrigParam);
    return bResult;
}


bool PulseView::setAllParameters()
{
    bool bResult = false;
#ifdef _SUPPORT_SET_SYS_FREQ_
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    Module::Frequency eFreq = (Module::Frequency) pConfig->value(APPConfig::KEY_SYS_FREQ ).toInt();
    pConfig->endGroup();
    UINT32 uiPeriod = 0;


    if(eFreq == Module::FREQ_60HZ)
    {
        uiPeriod = Module::US_PER_S / (UINT32)eFreq;
    }
    else
    {
        uiPeriod = Module::US_PER_S / (UINT32)(Module::FREQ_50HZ);
    }
    bResult = m_pCAService->setSampleParam(m_eSampleRate, uiPeriod);
#else
    bResult = m_pCAService->setSampleParam(m_eSampleRate);
#endif
    if(!bResult)
    {
        dbg_warning("set sample rate&sample length failed!");
        return bResult;
    }

    bResult = m_pCAService->setGain(m_eGain);
    if(!bResult)
    {
        dbg_warning("set gain failed!");
        return bResult;
    }

    bResult = setTrigParameters();
    if(!bResult)
    {
        dbg_warning("set trig parameters failed!");
        return bResult;
    }

    return bResult;
}

/************************************************
 * 函数名   : connectSigsWithService
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : connectSigsWithService
 ************************************************/
void PulseView::connectSigsWithService()
{
    qRegisterMetaType<QList<CA::PulseData> >("QList<CA::PulseData>");
    qRegisterMetaType<CA::DataHeadInfo>("CA::DataHeadInfo");
    qRegisterMetaType<CA::GainState>("CA::GainState");
    connect( m_pCAService, SIGNAL(sigDisconnected(QString)), this, SLOT(onDisconnectConnection(QString)), Qt::QueuedConnection);
    connect( m_pCAService, SIGNAL(sigConnected(QString)), this, SLOT(onConnected(QString)), Qt::QueuedConnection);
    connect( m_pCAService, SIGNAL(sigGainState(CA::GainState)), this, SLOT(onGainState(CA::GainState)), Qt::QueuedConnection);
    connect( m_pCAService, SIGNAL(sigInvalidCalibrateCo()), this, SLOT(onInvalidCalibrateCo()), Qt::QueuedConnection);
    connect( m_pCAService, SIGNAL(sigNoCalibrateCoe()), this, SLOT(onNoCalibrateCo()), Qt::QueuedConnection);
    return;
}

/*************************************************
函数名： createPRPDChart
输入参数: parent---父控件
输出参数：NULL
返回值： NULL
功能： 创建PRPD图谱
*************************************************************/
PrpdFast *PulseView::createPRPDChart(QWidget *parent)
{
    Q_UNUSED(parent)

    //图谱 m_pPrpdFast
    m_pPrpdFast = new PrpdFast( this );
    m_pPrpdFast->setRangeUnitMax(CA::PRPD_RANGE_UNIT_MV, m_uiRangeMax);
    m_pPrpdFast->setCoordinaterColor( Qt::black );
    m_pPrpdFast->setFixedHeight( CHART_HEIGHT );
    m_pPrpdFast->setFixedWidth( CHART_WIDTH );
    return m_pPrpdFast;
}

/*************************************************
函数名： createPDSSChart
输入参数: parent---父控件
输出参数：NULL
返回值： NULL
功能： 创建PDSS图谱
*************************************************************/
FastWavePDSS *PulseView::createPDSSChart(QWidget *parent)
{
    Q_UNUSED(parent)

    //图谱 m_pFastWavePDSS
    m_pFastWavePDSS = new FastWavePDSS( this );
    m_pFastWavePDSS->setFixedHeight( CHART_HEIGHT );
    m_pFastWavePDSS->setFixedWidth( CHART_WIDTH );


    QwtText qwtTextPDSSY = m_pFastWavePDSS->axisTitle( QwtPlot::yLeft );
    QFont qFontPDSSY = qwtTextPDSSY.font();
    qFontPDSSY.setPointSize( TITLE_SIZE );
    qwtTextPDSSY.setFont( qFontPDSSY );
    m_pFastWavePDSS->setAxisTitle( QwtPlot::yLeft,qwtTextPDSSY );

    return m_pFastWavePDSS;
}

/*************************************************
函数名： createConnectStatusLabel
输入参数: parent---父控件
输出参数：NULL
返回值： NULL
功能： 创建连接状态label
*************************************************************/
QLabel *PulseView::createConnectStatusLabel(QWidget *parent)
{
    //图谱 m_pConnectStatusLabel
    m_pConnectStatusLabel = new QLabel( parent );
    m_pConnectStatusLabel->setFixedHeight( STATUS_LABEL_HEIGHT );
    m_pConnectStatusLabel->setAlignment( Qt::AlignCenter );
    return m_pConnectStatusLabel;
}

/*************************************************
函数名： createPulseNumberLabel
输入参数: parent---父控件
输出参数：NULL
返回值： NULL
功能： 创建显示脉冲数的label
*************************************************************/
QLabel *PulseView::createPulseNumberLabel(QWidget *parent)
{
    //显示脉冲数的label
    m_pPulseNumberLabel = new QLabel( parent );
    m_pPulseNumberLabel->setFixedHeight( STATUS_LABEL_HEIGHT );
    m_pPulseNumberLabel->setStyleSheet( PULSE_NUMBER_LABEL_STYLESHEET );
    m_pPulseNumberLabel->setAlignment(  Qt::AlignCenter );
    QString strPulseNumber = qApp->translate("CAView",(CAViewConfig::TEXT_PULSE_NUMBER ));
    strPulseNumber += ": "+ QString::number(m_lRawData.size());
    strPulseNumber += "/" + QString::number(CA::g_ausPulseAccuCount[m_ePulseAccuCnt]);
    m_pPulseNumberLabel->setText(  strPulseNumber);

    return m_pPulseNumberLabel;
}

/*************************************************
函数名： updatePulseNumberLabel
输入参数: parent---父控件
输出参数：NULL
返回值： NULL
功能： 更新显示脉冲数的label
*************************************************************/
void PulseView::updatePulseNumberLabel()
{
    QString strPulseNumber = qApp->translate("CAView",(CAViewConfig::TEXT_PULSE_NUMBER ));
    strPulseNumber += ": "+ QString::number(m_lRawData.size());
    strPulseNumber += "/" + QString::number(CA::g_ausPulseAccuCount[m_ePulseAccuCnt]);
    m_pPulseNumberLabel->setText(  strPulseNumber);
}

/*************************************************
函数名： createChartLayout()
输入参数: parent---父控件
输出参数：NULL
返回值： NULL
功能： 创建图谱
*************************************************************/
ChartWidget *PulseView::createChartLayout(QWidget *parent)
{
    PrpdFast *pPRPD = createPRPDChart(parent);
    FastWavePDSS *pPDSS = createPDSSChart(parent);

    QLabel *pConnectedStatusLabel = createConnectStatusLabel(parent);
    QLabel *pPulseNumberLabel = createPulseNumberLabel(parent);

    m_pYTitle = new QLabel( Y_LEFT_TITLE_US,this );
    m_pYTitle->setFixedHeight( 20 );
    QFont qFont = m_pYTitle->font();
    qFont.setPointSize( TITLE_SIZE );
    m_pYTitle->setFont( qFont );
    m_pYTitle->setAlignment( Qt::AlignLeft );

    QVBoxLayout* vContentLayout = new QVBoxLayout;
    vContentLayout->addWidget(pConnectedStatusLabel);
    vContentLayout->addSpacing(5);
    vContentLayout->addWidget( pPulseNumberLabel );
    vContentLayout->addWidget( m_pYTitle );
    vContentLayout->addWidget( pPDSS );
    vContentLayout->addWidget( pPRPD );
    vContentLayout->setContentsMargins( 0, 5, 0, 0 );
    vContentLayout->setSpacing( 0 );

    ChartWidget *pWidget = new ChartWidget;
    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->addLayout( vContentLayout );
    vLayout->setContentsMargins( 0,0,0,0 );
    vLayout->setSpacing( 0 );
    pWidget->setLayout(vLayout);

    return pWidget;
}

/*************************************************
功能： 初始化数据成员
*************************************************************/
void PulseView::initDatas()
{
    m_bCancelSave = false;
    m_dSavedRatio = 0;
    m_uiNewlyReceivedPulseCnt = 0;
    m_uiSearchConditionerTimes = 0;
    m_iSearchingConditionerTimerId = CA::INVALID_TIMER_ID;
    m_iSaveDataTimerId = CA::INVALID_TIMER_ID;
    //    m_uiRangeMax = CA::RANGE_MAX_DEFAULT;
    m_eScaleDirect = SCALE_NONE;
    m_eGainState = CA::GAIN_NORMAL;
    m_eState = CA::STATE_UNCONNECTED;
    m_eSampleRate = CA::SAMPLE_RATE_DEFAULT;
    //界面数据
    m_lRawData.clear();
    m_XDataPDSS.clear();
    m_YDataPDSS.clear();
    m_IndexPDSS.clear();
    m_prpdVector.clear();

    getSampleParameter();
}


/*************************************************
函数名： getSampleParameter
输入参数: NULL
输出参数：NULL
返回值： NULL
功能： 从配置文件读出数据,更新采样数据
*************************************************************/
void PulseView::getSampleParameter()
{
    //采样参数
    m_pConfig = ConfigManager::instance()->config();

    m_pConfig->beginGroup( Module::GROUP_CA );
    //m_eSampleRate = (CA::SampleRate)m_pConfig->value( CAConfig::KEY_SAMPLE_RATE ).toUInt();
    m_ucPercentageBeforeTrig = m_pConfig->value( CAConfig::KEY_PULSE_BEFORE_TRIG ).toUInt();
    m_iPhaseAlias = 0;
    //m_iPhaseAlias = m_pConfig->value( CAConfig::KEY_PULSE_PHASE_ALIAS ).toUInt();
    m_eTriggerWidth = (CA::TriggerWidth)m_pConfig->value( CAConfig::KEY_PULSE_TRIGWIDTH ).toUInt();
    //m_eRangeMax = (CA::RangeMax)m_pConfig->value( CAConfig::KEY_PULSE_AMP_RANGE ).toUInt();

    m_ePulseAccuCnt = (CA::PulseAccuCount)m_pConfig->value( CAConfig::KEY_PULSE_ACCU_COUNT ).toUInt();

    m_eGain = (CA::Gain)m_pConfig->value( CAConfig::KEY_GAIN ).toUInt();
    m_uiRangeMax = CA::g_ausRange[m_eGain];

    m_usTrigThreashold = m_pConfig->value( CAConfig::KEY_PULSE_TRIG_AMP ).toUInt();

    if(m_usTrigThreashold > m_uiRangeMax)
    {
        m_usTrigThreashold = m_uiRangeMax;
        m_pConfig->setValue( m_usTrigThreashold, CAConfig::KEY_PULSE_TRIG_AMP );
    }

    m_pConfig->endGroup();
}

/*************************************************
函数名： setPDSSSampleData
输入参数: NULL
输出参数：NULL
返回值： NULL
功能：set PDSS图谱数据
*************************************************************/
void PulseView::setPDSSSampleData(bool isNeedClearChart)
{
    APP_CHECK_RETURN( m_pFastWavePDSS );
    double yScaleMax = CA::sampleRateEnum2Number(m_eSampleRate) / 200.0;
    bool bIsYUpToLimit = false;
    if( yScaleMax < 1 )
    {
        bIsYUpToLimit = true;
    }


    if(isNeedClearChart == false)
    {
        QList< CA::PulseData >vecNewlyData = m_lRawData.mid(m_lRawData.size() - m_uiNewlyReceivedPulseCnt, m_uiNewlyReceivedPulseCnt);

        for( UINT16 i = 0;i < vecNewlyData.size();++i )
        {
            m_IndexPDSS.append( m_IndexPDSS.size() + i );
            m_XDataPDSS.append( vecNewlyData.at( i ).fEquivalentTime *US_PER_SEC );
            if( bIsYUpToLimit )
            {
                m_YDataPDSS.append( vecNewlyData.at( i ).fEquivalentFreq * MHZ_PER_HZ * 1000 );
            }
            else
            {
                m_YDataPDSS.append( vecNewlyData.at( i ).fEquivalentFreq * MHZ_PER_HZ );
            }
        }
    }
    else
    {
        //收到的脉冲总数超出2048时，总显示最新的2048个脉冲数据
        m_pFastWavePDSS->clearCurve();
        m_pFastWavePDSS->clear();
        m_IndexPDSS.clear();
        m_XDataPDSS.clear();
        m_YDataPDSS.clear();

        for( UINT16 i = 0;i < m_lRawData.size();++i )
        {
            m_IndexPDSS.append( i );
            m_XDataPDSS.append( m_lRawData.at( i ).fEquivalentTime *US_PER_SEC );
            if( bIsYUpToLimit )
            {
                m_YDataPDSS.append( m_lRawData.at( i ).fEquivalentFreq * MHZ_PER_HZ * 1000 );
            }
            else
            {
                m_YDataPDSS.append( m_lRawData.at( i ).fEquivalentFreq * MHZ_PER_HZ );
            }
        }
    }
    m_pFastWavePDSS->setDatas( m_IndexPDSS,m_YDataPDSS,m_XDataPDSS );


    //量程自适应
    if( (m_XDataPDSS.size() > 0) && (m_XDataPDSS.size() == m_YDataPDSS.size()) )
    {
        const float MIN_RANGE = 0.4;
        const double STEP_RADIO = 4.0;
        double dXMax = m_XDataPDSS.at(0);
        double dXMin = m_XDataPDSS.at(0);
        double dXData = 0.0f;
        double dYMax = m_YDataPDSS.at(0);
        double dYMin = m_YDataPDSS.at(0);
        double dYData = 0.0f;

        for(int i = 1; i < m_XDataPDSS.size(); i++)
        {
            dXData = m_XDataPDSS.at(i);
            if(dXMax < dXData)
            {
                dXMax = dXData;
            }

            if(dXMin > dXData)
            {
                dXMin = dXData;
            }

            dYData = m_YDataPDSS.at(i);
            if(dYMax < dYData)
            {
                dYMax = dYData;
            }

            if(dYMin > dYData)
            {
                dYMin = dYData;
            }
        }

        dXMax = qCeil(dXMax / MIN_RANGE) * MIN_RANGE;
        dXMin = qFloor(dXMin / MIN_RANGE) * MIN_RANGE;
        m_pFastWavePDSS->setScale(FastWaveBase::Y_SCALE, dXMin, dXMax, (dXMax - dXMin) / STEP_RADIO );

        dYMax = qCeil(dYMax / MIN_RANGE) * MIN_RANGE;
        dYMin = qFloor(dYMin / MIN_RANGE) * MIN_RANGE;
        m_pFastWavePDSS->setScale(FastWaveBase::X_SCALE, dYMin, dYMax, (dYMax - dYMin) / STEP_RADIO );
    }
}

/*************************************************
函数名： setPRPDSampleData
输入参数: NULL
输出参数：NULL
返回值： NULL
功能：set PRPD图谱数据
*************************************************************/
void PulseView::setPRPDSampleData()
{
    APP_CHECK_RETURN( m_pPrpdFast );
    m_prpdVector.clear();

    QList< CA::PulseData >vecNewlyData = m_lRawData.mid(m_lRawData.size() - m_uiNewlyReceivedPulseCnt, m_uiNewlyReceivedPulseCnt);

    for( UINT16 i = 0;i < vecNewlyData.size();++i )
    {
        PrpdData prpdData;
        //prpdData.fPeak needs to be ratio
        vecNewlyData[i].fMaxValue = ((float)((qint64)(vecNewlyData.at( i ).fMaxValue *100)) / 100.0);

        prpdData.fPeak = (vecNewlyData.at( i ).fMaxValue+m_uiRangeMax) / (float)(2*m_uiRangeMax);
        prpdData.fPhase = vecNewlyData.at( i ).fMaxValuePhase;
        m_prpdVector.append( prpdData );
    }

    // 由于一个一个点添加的方式效率很低, 随着时间推移, 界面越来越卡顿, 因此这里满50个点再进行添加
    //实际使用时 脉冲数据会很少,所以屏蔽该条件，否则实际脉冲数少于50时，图谱没有显示
    //if(m_prpdVector.size() >= 50)
    {
        m_pPrpdFast->setDatas( m_prpdVector );
        //m_prpdVector.clear();
    }
}

/*************************************************
函数名： setSampleData
输入参数: NULL
输出参数： NULL
返回值： NULL
功能： 设置图谱数据
*************************************************************/
void PulseView::setSampleData( bool isNeedClearChart )
{
    setPDSSSampleData(isNeedClearChart);

    setPRPDSampleData();
}

void PulseView::removePulseBelowTrigValue(QList<CA::PulseData> &lData)
{
    for(int i = 0; i < lData.size(); i++)
    {
        CA::PulseData stData = lData.at(i);
        float fMax = stData.fMaxValue;
        if(fMax < m_usTrigThreashold)
        {
            lData.removeAt(i);
        }
    }
}

void PulseView::filterPulse(QList<CA::PulseData> &lData, QList<CA::PulseData> &lRealData)
{
    float fTriggerRatio = (float)m_usTrigThreashold / (float)m_uiRangeMax;
    for(int i = 0 ; i < lData.size(); i ++)
    {
        float fPeak = lData.at( i ).fMaxValue / (float)m_uiRangeMax;
        if(fPeak >= fTriggerRatio && (fPeak > 0.0 && fPeak < 1.0))
        {
            lRealData.append(lData[i]);
        }
    }
}

/*************************************************
功能： 槽，响应service告知的图谱数据
*************************************************************/
void PulseView::onPulseData(CA::DataHeadInfo stHeadInfo, QList<CA::PulseData> lData)
{
    //dbg_info("m_eState is %d, lData.size is %d\n", m_eState, lData.size());

    if(lData.size() <= 0)
    {
        return;
    }

    m_stHeadInfo = stHeadInfo;
    int iOriginalSize = m_lRawData.size();
    m_lRawData += lData;

    UINT32 uiTotalPulseDataCnt = m_lRawData.size();
    quint32 uiExpectedPulseCnt = CA::g_ausPulseAccuCount[m_ePulseAccuCnt];
    if( uiTotalPulseDataCnt >= uiExpectedPulseCnt )
    {
        stopSample();
        dbg_info("received more than %d pulse data, save latested %d pulse data!", uiExpectedPulseCnt, uiExpectedPulseCnt);
        m_lRawData = m_lRawData.mid(0,uiExpectedPulseCnt);
        m_uiNewlyReceivedPulseCnt = m_lRawData.size() - iOriginalSize;
        setSampleData(true);
        dbg_info("final pulse count is :  %d", m_lRawData.size());
    }
    else
    {
        m_uiNewlyReceivedPulseCnt = lData.size();
        setSampleData(false);
    }

    updatePulseNumberLabel();
}

/*************************************************
功能： 设置按钮栏数据
*************************************************************/
void PulseView::setButtonBarDatas()
{
    int iTrgStep = CA::g_ausTrgStep[m_eGain];
    SliderButton *pButton = (SliderButton*)(buttonBar()->button(BUTTON_TRIGGER_THREASHOLD));
    pButton->setRange(0, m_uiRangeMax, iTrgStep);
    pButton->setValue(m_usTrigThreashold);

    ((PopupButton*)(buttonBar()->button(BUTTON_TRIGGER_WIDTH)))->setValue( m_eTriggerWidth );
    ((PopupButton*)(buttonBar()->button(BUTTON_PULSE_ACCU_COUNT)))->setValue( m_ePulseAccuCnt );
    ((PopupButton*)(buttonBar()->button(BUTTON_PHASE_ALIAS)))->setValue( m_iPhaseAlias );
    //    ((PopupButton*)(buttonBar()->button(BUTTON_SAMPLE_RATE)))->setValue( m_eSampleRate );
    ((PopupButton*)(buttonBar()->button(BUTTON_PERCENTAGE_BEFORE_TRIG)))->setValue( m_ucPercentageBeforeTrig );
    //((PopupButton*)(buttonBar()->button(BUTTON_TRIGGER_THREASHOLD)))->setValue( m_usTrigThreashold );
    ((PopupButton*)(buttonBar()->button(BUTTON_GAIN)))->setValue( m_eGain );
    //((PopupButton*)(buttonBar()->button(BUTTON_RANGE_MAX)))->setValue( m_eRangeMax );
    ((PopupButton*)(buttonBar()->button(BUTTON_TRIGGER_THREASHOLD)))->setValue( m_usTrigThreashold );
}

/*************************************************
功能： 设置表格数据
*************************************************************/
void PulseView::setChartDatas()
{
    INT32 iOldPhase = m_pPrpdFast->phaseShift();
    m_pPrpdFast->translatePhase(m_iPhaseAlias - iOldPhase);

    if(m_uiRangeMax < CA::MV_PER_V)
    {
        m_pPrpdFast->setRangeUnitMax(CA::PRPD_RANGE_UNIT_MV, m_uiRangeMax);
    }
    else
    {
        m_pPrpdFast->setRangeUnitMax(CA::PRPD_RANGE_UNIT_V, m_uiRangeMax / CA::MV_PER_V);
    }

    m_pPrpdFast->setThreshold( thresholdRatio() );

    setPDSSChartScale(m_eSampleRate);
}

/*************************************************
函数名： setPDSSChartScale
输入参数:eSampleRate---采样率
输出参数： NULL
返回值： NULL
功能： 根据采样率设置图谱量程
*************************************************************/
void PulseView::setPDSSChartScale(CA::SampleRate eSampleRate)
{
    double dMax = CAViewConfig::TRIGGER_WIDTH_OPITIONS[m_eTriggerWidth];
    m_pFastWavePDSS->setScale(FastWaveBase::Y_SCALE,0,dMax, dMax / 4.0 );

    double xScaleMax = CA::sampleRateEnum2Number(eSampleRate) / 200.0;

    //    if( xScaleMax < 1 )
    //    {
    //        xScaleMax = xScaleMax * 1000;
    //        m_pYTitle->setText( Y_LEFT_TITLE_US );
    //    }
    //    else
    //    {
    //        m_pYTitle->setText( Y_LEFT_TITLE_US );
    //    }


    m_pFastWavePDSS->setScale(FastWaveBase::X_SCALE,0,xScaleMax,xScaleMax / 4.0 );
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void PulseView::onButtonValueChanged( int id, int iValue )
{
    switch( id )
    {
    case BUTTON_PHASE_ALIAS://相位偏移
    {
        if(m_iPhaseAlias != iValue)
        {
            m_pPrpdFast->translatePhase(iValue - m_iPhaseAlias);
            m_iPhaseAlias = iValue;

            //            m_pConfig->beginGroup( Module::GROUP_CA );
            //            m_pConfig->setValue( m_iPhaseAlias, CAConfig::KEY_PULSE_PHASE_ALIAS );
            //            m_pConfig->endGroup();
        }

    }
        break;
        //    case BUTTON_SAMPLE_RATE://采样率
        //    {
        //        if(m_eSampleRate != (CA::SampleRate)iValue)
        //        {
        //            if( ( m_eSampleRate <= CA::SAMPLE_RATE_100 ) && ( iValue >= CA::SAMPLE_RATE_100 )  )
        //            {
        //                m_eScaleDirect = SCALE_LOW_TO_UP;
        //            }
        //            else if( ( m_eSampleRate > CA::SAMPLE_RATE_100 ) && ( iValue <= CA::SAMPLE_RATE_100 ) )
        //            {
        //                m_eScaleDirect = SCALE_UP_TO_LOW;
        //            }
        //            else
        //            {
        //                m_eScaleDirect = SCALE_NONE;
        //            }

        //            m_eSampleRate = (CA::SampleRate)iValue;
        //            setPDSSChartScale(m_eSampleRate);

        //            m_pConfig->beginGroup( Module::GROUP_CA );
        //            m_pConfig->setValue( m_eSampleRate, CAConfig::KEY_SAMPLE_RATE );
        //            m_pConfig->endGroup();
        //        }
        //    }
        //        break;
    case BUTTON_PERCENTAGE_BEFORE_TRIG://触发前采样长度占比
    {
        if(m_ucPercentageBeforeTrig != iValue)
        {
            m_ucPercentageBeforeTrig = iValue;

            m_pConfig->beginGroup( Module::GROUP_CA );
            m_pConfig->setValue( m_ucPercentageBeforeTrig, CAConfig::KEY_PULSE_BEFORE_TRIG );
            m_pConfig->endGroup();
        }
    }
        break;
    case BUTTON_TRIGGER_THREASHOLD://触发阈值
    {
        if(m_usTrigThreashold != iValue)
        {
            m_usTrigThreashold = iValue;

            m_pPrpdFast->setThreshold( thresholdRatio() );

            m_pConfig->beginGroup( Module::GROUP_CA );
            m_pConfig->setValue( m_usTrigThreashold, CAConfig::KEY_PULSE_TRIG_AMP );
            m_pConfig->endGroup();
        }
    }
        break;
    case BUTTON_GAIN://增益
    {
        if(m_eGain != (CA::Gain)iValue)
        {
            quint32 uiOldRange = CA::g_ausRange[m_eGain];
            m_eGain = (CA::Gain)iValue;
            m_pConfig->beginGroup( Module::GROUP_CA );
            m_pConfig->setValue( m_eGain, CAConfig::KEY_GAIN );
            m_pConfig->endGroup();

            resetPRPDDatas(uiOldRange, CA::g_ausRange[m_eGain]);
            changeRange(uiOldRange);
        }
    }
        break;
    case BUTTON_TRIGGER_WIDTH://触发宽度
    {
        if(m_eTriggerWidth != (CA::TriggerWidth)iValue)
        {
            m_eTriggerWidth = (CA::TriggerWidth)iValue;
            double dMax = CAViewConfig::TRIGGER_WIDTH_OPITIONS[m_eTriggerWidth];
            m_pFastWavePDSS->setScale(FastWaveBase::Y_SCALE,0,dMax, dMax / 4.0 );

            m_pConfig->beginGroup( Module::GROUP_CA );
            m_pConfig->setValue( m_eTriggerWidth, CAConfig::KEY_PULSE_TRIGWIDTH );
            m_pConfig->endGroup();
        }
    }
        break;
    case BUTTON_PULSE_ACCU_COUNT://脉冲累积个数
    {
        if(m_ePulseAccuCnt != (CA::PulseAccuCount)iValue)
        {
            m_ePulseAccuCnt = (CA::PulseAccuCount)iValue;
            updatePulseNumberLabel();

            m_pConfig->beginGroup( Module::GROUP_CA );
            m_pConfig->setValue( m_ePulseAccuCnt, CAConfig::KEY_PULSE_ACCU_COUNT );
            m_pConfig->endGroup();

            m_pCAService->setExpectedPulseCnt(CA::g_ausPulseAccuCount[m_ePulseAccuCnt]);
        }
    }
        break;
        //    case BUTTON_RANGE_MAX://量程最大
        //    {
        //        if(m_eRangeMax != (CA::RangeMax) iValue)
        //        {
        //            qint32 iOldRange = CA::g_RangeMax[m_eRangeMax];
        //            m_eRangeMax = (CA::RangeMax)iValue;
        //            m_uiRangeMax = CA::g_RangeMax[m_eRangeMax];

        //            if(m_uiRangeMax < CA::MV_PER_V)
        //            {
        //                m_pPrpdFast->setRangeUnitMax(CA::PRPD_RANGE_UNIT_MV, m_uiRangeMax);
        //            }
        //            else
        //            {
        //                m_pPrpdFast->setRangeUnitMax(CA::PRPD_RANGE_UNIT_V, m_uiRangeMax / CA::MV_PER_V);
        //            }

        //            if(m_uiRangeMax < m_usTrigThreashold)
        //            {
        //                m_usTrigThreashold = m_uiRangeMax;
        //            }

        //            resetTriggerValueScope();

        //            resetPRPDDatas(iOldRange, m_uiRangeMax);

        //            m_pPrpdFast->setThreshold( m_usTrigThreashold * 1.0 / m_uiRangeMax );
        //            m_pConfig->beginGroup( Module::GROUP_CA );
        //            m_pConfig->setValue( m_eRangeMax, CAConfig::KEY_PULSE_AMP_RANGE );
        //            m_pConfig->setValue( m_usTrigThreashold, CAConfig::KEY_PRPS_THREASHOLD );
        //            m_pConfig->endGroup();
        //        }
        //    }
        //        break;
    default:
        break;
    }
}

void PulseView::resetPRPDDatas(qint32 iOldRange, qint32 iNewRange)
{
    if(iOldRange == iNewRange)
    {
        return;
    }

    QVector< PrpdData > vecData = m_pPrpdFast->datas();
    int size = vecData.size();

    for(int i = 0; i < size; i ++)
    {
        float fVoltage = (vecData[i].fPeak * 2*iOldRange)-iOldRange;
        vecData[i].fPeak = (fVoltage+iNewRange) / ((float)2*iNewRange);
    }
    m_pPrpdFast->clearAccumulation();
    m_pPrpdFast->setDatas( vecData );
}

/*************************************************
函数名： resetTriggerValueScope()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 触发值范围
*************************************************************/
void PulseView::resetTriggerValueScope(void)
{
    int iTrgStep = CA::g_ausTrgStep[m_eGain];
    SliderButton *pButton = (SliderButton*)(buttonBar()->button(BUTTON_TRIGGER_THREASHOLD));
    pButton->setRange(0, m_uiRangeMax, iTrgStep);
    //获取范围和步进修改后，当前值的一个近似值，便于值修正
    m_usTrigThreashold = pButton->getApproximateVal(m_usTrigThreashold);
    pButton->setValue(m_usTrigThreashold);
    return;
}

/*************************************************
函数名： setSampleBtnText
输入参数: eState---连接状态
输出参数：NULL
返回值： NULL
功能： 根据连接状态设置采样按钮文本
*************************************************************/
void PulseView::setSampleBtnText(CA::CA_STATE eState)
{
    if(eState == CA::STATE_SAMPLING)
    {
        //change sample btn text
        m_pSampleBtn->setTitle(qApp->translate("CAView",CAViewConfig::TEXT_STOP_SAMPLE));
    }
    else
    {
        //change sample btn text
        m_pSampleBtn->setTitle(qApp->translate("CAView",CAViewConfig::TEXT_START_SAMPLE));
    }
}

/*************************************************
功能： 恢复默认
*************************************************************/
void PulseView::restoreDefault()
{

    if( MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to restore default settings?")) )
    {
        //配置文件数据恢复成默认
        m_pConfig->beginGroup( Module::GROUP_CA );
        QVector<Config::GroupKey> totalKeys;
        //totalKeys << Config::GroupKey( CAConfig::KEY_SAMPLE_RATE );
        totalKeys << Config::GroupKey( CAConfig::KEY_GAIN );
        totalKeys << Config::GroupKey( CAConfig::KEY_PULSE_BEFORE_TRIG );
        totalKeys << Config::GroupKey( CAConfig::KEY_PULSE_TRIG_AMP );
        totalKeys << Config::GroupKey( CAConfig::KEY_PULSE_PHASE_ALIAS );
        totalKeys << Config::GroupKey( CAConfig::KEY_PULSE_TRIGWIDTH );
        totalKeys << Config::GroupKey( CAConfig::KEY_PULSE_ACCU_COUNT );
        //totalKeys << Config::GroupKey( CAConfig::KEY_PULSE_AMP_RANGE );

        m_pConfig->restoreDefault( totalKeys );
        m_pConfig->endGroup();

        quint32 uiOldRange = m_uiRangeMax;

        //参数恢复成默认
        getSampleParameter();

        changeRange(uiOldRange);

        //更新按钮显示
        setButtonBarDatas();

        //更新图谱显示
        setChartDatas();

        m_pPrpdFast->clearAccumulation();
        m_pFastWavePDSS->clear();
        m_pFastWavePDSS->clearCurve();
        m_lRawData.clear();
        updatePulseNumberLabel();
    }
}

/*************************************************
功能： 聚类分析
*************************************************************/
void PulseView::clusterAnalysis()
{
    //read all calibrated parameters
    getCalibratedParameters();
    float fDiagnosisYMax;
    calculateYRangeForDiagnosis(fDiagnosisYMax);
    dbg_info("fDiagnosisYMax is %f\n", fDiagnosisYMax);

    double dPulseWidth = CAViewConfig::TRIGGER_WIDTH_OPITIONS[m_eTriggerWidth];
    PulseAnalysisView *anaView = new PulseAnalysisView(qApp->translate("CAView", CAViewConfig::TEXT_ANALYZE),
                                                       m_lRawData, m_eSampleRate,  dPulseWidth, m_uiRangeMax, fDiagnosisYMax);
    anaView->show();
}

/*************************************************
功能： 载入数据
*************************************************************/
void PulseView::loadData(void)
{
    QString filePath = DATA_STORAGE_PATH + "/" + ACCESS_G100_PULSE_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if( !infoList.isEmpty() )
    {
        PlayBackView* pView = new PlayBackView( filePath, CA_VIEW_CONFIG_TRANSLATE(CAViewConfig::TEXT_PULSE), CA_VIEW_CONFIG_TRANSLATE(CAViewConfig::TEXT_LOAD_DATA) );
        pView->addPlayback(ACCESS_G100_PULSE_FILE_NAME_SUFFIX, new PulsePlayBackView());
        connect(pView, SIGNAL(sigEnterPressed()), this, SLOT(hideMoreConfigButtonBar()));
        pView->show();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
}

/*************************************************
功能： 开始采样
*************************************************************/
bool PulseView::startSample(void)
{
    bool isSuccess = false;
    if(m_eState == CA::STATE_CONNECTED)
    {
        isSuccess  = setAllParameters();
        if(!isSuccess)
        {
            MsgBox::warning("", QObject::trUtf8("Set parameter fail, please check connection state."));
            return isSuccess;
        }

        resetGainState();
        isSuccess = m_pCAService->startSample(CA::MODE_PULSE);
        if(isSuccess)
        {
            disconnect( m_pCAService, SIGNAL(sigPulseData(CA::DataHeadInfo, QList<CA::PulseData>)), this,
                        SLOT(onPulseData(CA::DataHeadInfo, QList<CA::PulseData>)));

            connect( m_pCAService, SIGNAL(sigPulseData(CA::DataHeadInfo, QList<CA::PulseData>)), this,
                     SLOT(onPulseData(CA::DataHeadInfo, QList<CA::PulseData>)), Qt::QueuedConnection);

            m_eState = CA::STATE_SAMPLING;

            m_pFastWavePDSS->clearCurve();
            m_pPrpdFast->clearAccumulation();

            m_lRawData.clear();
            m_IndexPDSS.clear();
            m_XDataPDSS.clear();
            m_YDataPDSS.clear();

            disableSampleRelatedBtns();
            setSampleBtnText(m_eState);
            updatePulseNumberLabel();
        }
        else
        {
            MsgBox::warning("", QObject::trUtf8("Start sample fail, please check connection state."));
        }
    }

    return isSuccess;
}

/*************************************************
功能： 停止采样
*************************************************************/
bool PulseView::stopSample(void)
{
    //bool isSuccess = false;
    if(m_eState == CA::STATE_SAMPLING)
    {
        disconnect( m_pCAService, SIGNAL(sigPulseData(CA::DataHeadInfo, QList<CA::PulseData>)), this,
                    SLOT(onPulseData(CA::DataHeadInfo, QList<CA::PulseData>)));

        /*isSuccess =*/ m_pCAService->stopSample();
        if(1/*isSuccess*/) //think to be successfully
        {
            m_eState = CA::STATE_CONNECTED;

            enableSampleRelatedBtns();
            setSampleBtnText(m_eState);
        }
        else
        {
            dbg_warning("stop sample fail!\n");
        }
    }

    return true;
}

/*************************************************
功能： 删除数据
*************************************************************/
void PulseView::deleteData()
{
    QStringList nameFilters;
    nameFilters << ACCESS_G100_PULSE_FILE_NAME_SUFFIX;
    nameFilters << ".png";

    QString filePath = DATA_STORAGE_PATH + "/" + ACCESS_G100_PULSE_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if( !infoList.isEmpty() )
    {
        DeleteDataView* pView = new DeleteDataView( filePath,
                                                    CA_VIEW_CONFIG_TRANSLATE(CAViewConfig::TEXT_DELETE_DATA),
                                                    nameFilters
                                                    );
        pView->setRelatedSuffix( ACCESS_G100_PULSE_FILE_NAME_SUFFIX, QStringList(BINARY_DATA_FILE_NAME_SUFFIX) );
        pView->exec();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void PulseView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case BUTTON_SAMPLE://采样
    {
        if(!m_pCAService->hasConnectedDevice() && m_pCAService->hasCandidates())
        {
            MsgBox::warning("", QObject::trUtf8("Please pair CA Diag. processor first."));
            return;
        }
        else if(!m_pCAService->hasConnectedDevice() && !m_pCAService->hasCandidates())
        {
            MsgBox::warning("", QObject::trUtf8("No paired CA Diag. processor."));
            return;
        }
        else
        {

        }

        if(m_eState == CA::STATE_SAMPLING)
        {
            stopSample();
        }
        else if (m_eState == CA::STATE_CONNECTED)
        {
            startSample();
        }
        else
        {
            dbg_warning("wrong connection state:%d!\n", m_eState);
        }
    }
        break;
    case BUTTON_SAVE_DATA://保存数据
    {
        saveDataToFile();
    }
        break;

    case BUTTON_RESTORE_DEFAULT://恢复默认参数
    {
        restoreDefault();
    }
        break;

    case BUTTON_LOAD_DATA://载入数据
    {
        loadData();
    }
        break;
    case BUTTON_DELETE_DATA://删除数据
    {
        deleteData();
    }
        break;
    case BUTTON_CLUSTER_ANALYSIS://聚类分析
    {
        clusterAnalysis();
    }
        break;
    case BUTTON_MENU://更多
    {
        showMoreConfigButtonBar();
    }
        break;
    default:
        break;
    }
}

void PulseView::onInvalidCalibrateCo()
{
    MsgBox::warning("", QObject::trUtf8("Invalid coefficient, it will use default coefficient, please check HAS02 hardware!"));
}

void PulseView::onNoCalibrateCo()
{
    MsgBox::warning("", QObject::trUtf8("No calibrated coefficient, please calibrate HAS02 firstly!"));
}

/*************************************************
功能： 槽，响应service告知的通讯连接已连接，更新界面显示
*************************************************************/
void PulseView::onConnected(QString strMac)
{
    dbg_info("get connected signal");
    Q_UNUSED(strMac);
    m_pConnectStatusLabel->setStyleSheet(STATUS_LABEL_CONNECTED);
    m_pConnectStatusLabel->setText(qApp->translate("CAView", (CAViewConfig::TEXT_CONNECT_CONNECTED)));
    m_eState = CA::STATE_CONNECTED;

    if(m_iSearchingConditionerTimerId != CA::INVALID_TIMER_ID)
    {
        killTimer(m_iSearchingConditionerTimerId);
        m_iSearchingConditionerTimerId = CA::INVALID_TIMER_ID;
    }
}

void PulseView::getCalibratedParameters()
{
    m_stCalibrateParameters = m_pCAService->calibratedParam(m_eGain);
}

void PulseView::calculateYRangeForDiagnosis(float &fDiagnosisYMax)
{
    float fCoefficient = m_stCalibrateParameters.fCoefficient;
    float fZeroBias = m_stCalibrateParameters.fZeroBias;
    float fADBias = m_stCalibrateParameters.fADBias;

    double dMax = 0;
    double dVoltage1 = ((0 - fADBias) / (double)(CA::AD_RANGE))*2*CA::AD_SAMPLE_VOLTAGE_RANGE_MAX;
    dVoltage1 = dVoltage1 * fCoefficient + fZeroBias;
    dVoltage1 = abs(dVoltage1);

    double dVoltage2 = ((CA::AD_RANGE - fADBias) / (double)(CA::AD_RANGE))*2*CA::AD_SAMPLE_VOLTAGE_RANGE_MAX;
    dVoltage2 = dVoltage2 * fCoefficient + fZeroBias;
    dVoltage2 = abs(dVoltage2);

    dMax = dVoltage1 > dVoltage2?dVoltage1:dVoltage2;

    fDiagnosisYMax = dMax;
}

void PulseView::setPDSSMapHead(const QDateTime &stDateTime)
{
    m_stDataInfo.stPDSSMapHead.strSubstationName = "";
    m_stDataInfo.stPDSSMapHead.strSubstationNumber = "";
    m_stDataInfo.stPDSSMapHead.eCode = DataFileNS::SPECTRUM_CODE_CA_PDSS;
    m_stDataInfo.stPDSSMapHead.generationDateTime = stDateTime;//图谱生成时间
    m_stDataInfo.stPDSSMapHead.eMapProperty = DataFileNS::PROPERTY_TEST;//图谱性质
    m_stDataInfo.stPDSSMapHead.strDeviceName = "";//电力设备名称
    m_stDataInfo.stPDSSMapHead.strDeviceNumber = "";//电力设备编码
    m_stDataInfo.stPDSSMapHead.strTestPointName = "";//测点名称
    m_stDataInfo.stPDSSMapHead.strTestPointNumber = "";//测点编码
    m_stDataInfo.stPDSSMapHead.eTestLocation = DataFileNS::TEST_LOCATION_DEFAULT;
    m_stDataInfo.stPDSSMapHead.ucTestChannelSign = 1;//仪器的检测通道标识，例如：1
    m_stDataInfo.stPDSSMapHead.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;//存储数据类型
    m_stDataInfo.stPDSSMapHead.qstrRemark = m_qstrRemark;
}

void PulseView::setPulseWaveMapHead(const QDateTime &stDateTime)
{
    m_stDataInfo.stPulseWaveMapHead.strSubstationName = "";
    m_stDataInfo.stPulseWaveMapHead.strSubstationNumber = "";
    m_stDataInfo.stPulseWaveMapHead.eCode = DataFileNS::SPECTRUM_CODE_CA_PULSE_WAVE;
    m_stDataInfo.stPulseWaveMapHead.generationDateTime = stDateTime;//图谱生成时间
    m_stDataInfo.stPulseWaveMapHead.eMapProperty = DataFileNS::PROPERTY_TEST;//图谱性质
    m_stDataInfo.stPulseWaveMapHead.strDeviceName = "";//电力设备名称
    m_stDataInfo.stPulseWaveMapHead.strDeviceNumber = "";//电力设备编码
    m_stDataInfo.stPulseWaveMapHead.strTestPointName = "";//测点名称
    m_stDataInfo.stPulseWaveMapHead.strTestPointNumber = "";//测点编码
    m_stDataInfo.stPulseWaveMapHead.eTestLocation = DataFileNS::TEST_LOCATION_DEFAULT;
    m_stDataInfo.stPulseWaveMapHead.ucTestChannelSign = 1;//仪器的检测通道标识，例如：1
    m_stDataInfo.stPulseWaveMapHead.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;//存储数据类型
    m_stDataInfo.stPulseWaveMapHead.qstrRemark = m_qstrRemark;
}

void PulseView::setPulsePRPDMapHead(const QDateTime &stDateTime)
{
    m_stDataInfo.stPulsePRPDMapHead.strSubstationName = "";
    m_stDataInfo.stPulsePRPDMapHead.strSubstationNumber = "";
    m_stDataInfo.stPulsePRPDMapHead.eCode = DataFileNS::SPECTRUM_CODE_CA_PULSE_PRPD;
    m_stDataInfo.stPulsePRPDMapHead.generationDateTime = stDateTime;//图谱生成时间
    m_stDataInfo.stPulsePRPDMapHead.eMapProperty = DataFileNS::PROPERTY_TEST;//图谱性质
    m_stDataInfo.stPulsePRPDMapHead.strDeviceName = "";//电力设备名称
    m_stDataInfo.stPulsePRPDMapHead.strDeviceNumber = "";//电力设备编码
    m_stDataInfo.stPulsePRPDMapHead.strTestPointName = "";//测点名称
    m_stDataInfo.stPulsePRPDMapHead.strTestPointNumber = "";//测点编码
    m_stDataInfo.stPulsePRPDMapHead.eTestLocation = DataFileNS::TEST_LOCATION_DEFAULT;
    m_stDataInfo.stPulsePRPDMapHead.ucTestChannelSign = 1;//仪器的检测通道标识，例如：1
    m_stDataInfo.stPulsePRPDMapHead.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;//存储数据类型
    m_stDataInfo.stPulsePRPDMapHead.qstrRemark = m_qstrRemark;
}

void PulseView::setPDSSMapExtInfo()
{
    double dXMin;
    double dXMax;
    m_pFastWavePDSS->scale(FastWaveBase::X_SCALE, dXMin, dXMax );
    m_stDataInfo.stPDSSInfo.eAmpUnit = DataFileNS::AMP_UNIT_MHZ;
    m_stDataInfo.stPDSSInfo.fAmpMin = dXMin;
    m_stDataInfo.stPDSSInfo.fAmpMax = dXMax;
    m_stDataInfo.stPDSSInfo.eMapBandWidth = DataFileNS::BAND_DEFAULT;
    m_stDataInfo.stPDSSInfo.fFreqMin = 0;
    m_stDataInfo.stPDSSInfo.fFreqMax = 0;
    m_stDataInfo.stPDSSInfo.iPulseCount = m_lRawData.size();
    m_stDataInfo.stPDSSInfo.dPulseWidth = CAViewConfig::TRIGGER_WIDTH_OPITIONS[m_eTriggerWidth];

    m_pFastWavePDSS->scale(FastWaveBase::Y_SCALE, dXMin, dXMax );
    m_stDataInfo.stPDSSInfo.fPulseTimeMin = dXMin;
    m_stDataInfo.stPDSSInfo.fPulseTimeMax = dXMax;
}

void PulseView::setPulseWaveMapExtInfo()
{
    m_stDataInfo.stPulseWaveInfo.eAmpUnit = DataFileNS::AMP_UNIT_DEFAULT;
    m_stDataInfo.stPulseWaveInfo.fAmpMin = -((qint32)m_uiRangeMax);
    m_stDataInfo.stPulseWaveInfo.fAmpMax = m_uiRangeMax;
    m_stDataInfo.stPulseWaveInfo.eMapBandWidth = DataFileNS::BAND_DEFAULT;
    m_stDataInfo.stPulseWaveInfo.fFreqMin = 0;
    m_stDataInfo.stPulseWaveInfo.fFreqMax = 0;
    m_stDataInfo.stPulseWaveInfo.iPulseCount = m_lRawData.size();

    if(m_lRawData.size() > 0)
    {

        m_stDataInfo.stPulseWaveInfo.iDataCount = m_lRawData.size() * m_lRawData.at(0).listTriggerDatas.size();
    }
    else
    {
        m_stDataInfo.stPulseWaveInfo.iDataCount = 0;
    }
    m_stDataInfo.stPulseWaveInfo.usSampleRate = CA::sampleRateEnum2Number(CA::SAMPLE_RATE_DEFAULT);
    memset(m_stDataInfo.stPulseWaveInfo.ucaPdTypeProb, 0, sizeof(m_stDataInfo.stPulseWaveInfo.ucaPdTypeProb));
    m_stDataInfo.stPulseWaveInfo.eGainType = DataFileNS::GAIN_TYPE_DEFAULT;
    m_stDataInfo.stPulseWaveInfo.sGain = CA::g_ausGain[m_eGain];
}

void PulseView::setPulsePRPDMapExtInfo()
{
    m_stDataInfo.stPulsePRPDInfo.eAmpUnit = DataFileNS::AMP_UNIT_DEFAULT;
    m_stDataInfo.stPulsePRPDInfo.fAmpMin = -((qint32)m_uiRangeMax);
    m_stDataInfo.stPulsePRPDInfo.fAmpMax = m_uiRangeMax;
    m_stDataInfo.stPulsePRPDInfo.eMapBandWidth = DataFileNS::BAND_DEFAULT;
    m_stDataInfo.stPulsePRPDInfo.fFreqMin = 0;
    m_stDataInfo.stPulsePRPDInfo.fFreqMax = 0;
    m_stDataInfo.stPulsePRPDInfo.iPulseCount = m_lRawData.size();
    memset(m_stDataInfo.stPulsePRPDInfo.ucaPdTypeProb, 0, sizeof(m_stDataInfo.stPulsePRPDInfo.ucaPdTypeProb));

    getCalibratedParameters();
    float fDiagnosisYMax;
    calculateYRangeForDiagnosis(fDiagnosisYMax);
    m_stDataInfo.stPulsePRPDInfo.fDiagnosisYMax = fDiagnosisYMax;
    m_stDataInfo.stPulsePRPDInfo.usPhaseAlias = m_iPhaseAlias;
    m_stDataInfo.stPulsePRPDInfo.iPhaseIntervalCount = CA::PRPS_PHASE_CNT;
    m_stDataInfo.stPulsePRPDInfo.iQuantificationAmp = CA::PRPS_AMP_INTERVAL_CNT;
}

void PulseView::setPDSSMapData(void)
{
    int pulseCnt = m_lRawData.size();


    m_stDataInfo.vecPDSSData.clear();

    for(int i = 0; i < pulseCnt; i ++)
    {
        PDSSMapNS::PDSSMapData stData;
        stData.fEquivalentFreq = m_lRawData.at(i).fEquivalentFreq;//hz
        stData.fEquivalentTime = m_lRawData.at(i).fEquivalentTime *US_PER_SEC;//us
        m_stDataInfo.vecPDSSData.append(stData);


    }

}

void PulseView::setPulseWaveMapData(void)
{

    int pulseCnt = m_lRawData.size();
    m_stDataInfo.vecPulseWaveData.clear();
    for(int i = 0; i < pulseCnt; i ++)
    {
        int iDataPointCnt  = m_stDataInfo.stPulseWaveInfo.iDataCount / pulseCnt;
        CA::PulseData stData = m_lRawData.at(i);
        for(int j = 0; j < iDataPointCnt; j ++)
        {
            float fData = stData.listTriggerDatas.at(j);
            m_stDataInfo.vecPulseWaveData.append(fData);


        }
    }

}

void PulseView::setPulsePRPDMapData(void)
{
    int pulseCnt = m_lRawData.size();


    m_stDataInfo.vecTriggerPhaseData.clear();
    m_stDataInfo.vecPulseMaxData.clear();
    m_stDataInfo.vecPRPDColor.clear();

    for(int i = 0; i < pulseCnt; i ++)
    {
        CA::PulseData stData = m_lRawData.at(i);
        float fPhase = (stData.fMaxValuePhase + m_iPhaseAlias);
        if(fPhase >= 360.0)
        {
            fPhase -= 360.0;
        }


        m_stDataInfo.vecTriggerPhaseData.append(fPhase);

        stData.fMaxValue = ((float)((qint64)(stData.fMaxValue *100)) / 100.0);
        m_stDataInfo.vecPulseMaxData.append(stData.fMaxValue);


        float fMaxForColor = (stData.fMaxValue + m_uiRangeMax) / (float)(2*m_uiRangeMax);


        QColor color = m_pPrpdFast->getColorFromData(fMaxForColor);
        m_stDataInfo.vecPRPDColor.append(color.red());
        m_stDataInfo.vecPRPDColor.append(color.green());
        m_stDataInfo.vecPRPDColor.append(color.blue());
    }

}

/*************************************************
函数名： composeXMLDataFile
输入参数: NULL
输出参数：NULL
返回值： NULL
功能：组织xml数据
*************************************************************/
void PulseView::composeXMLDataFile(void)
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    quint8 ucFreq = pConfig->value(APPConfig::KEY_SYS_FREQ ).toUShort();
    pConfig->endGroup();
    m_stDataInfo.ucFreq = ucFreq;

    QDateTime stDateTime = QDateTime::currentDateTime();
    setPDSSMapHead(stDateTime);
    setPulseWaveMapHead(stDateTime);
    setPulsePRPDMapHead(stDateTime);

    setPDSSMapExtInfo();
    setPulseWaveMapExtInfo();
    setPulsePRPDMapExtInfo();

    setPDSSMapData();
    setPulseWaveMapData();
    setPulsePRPDMapData();
}

void PulseView::getDataFilePath(QString &strAbsolutePath)
{
    QDateTime dateTime = QDateTime::currentDateTime();
    QString strDate = dateTime.toString("yyyyMMdd");
    strAbsolutePath = DATA_STORAGE_PATH + "/" + ACCESS_G100_PULSE_FOLDER;
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(0,4);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(4,2);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(6,2);
}

/*************************************************
函数名： thread_save_data
输入参数: pstData---写到xml文件的数据
输出参数：NULL
返回值： NULL
功能： 子线程函数,写数据到xml文件
*************************************************************/
void PulseView::thread_save_data(CAPulseDataInfo *pstDataInfo)
{
    QString qsSavedPath;
    getDataFilePath(qsSavedPath);

    AccessG100PulseDataSave hDataSave;
    hDataSave.setSaveDataTime(m_SaveDataDateTime);
    m_qsDataFile = hDataSave.saveData((void*)pstDataInfo, qsSavedPath);

    QApplication::postEvent(this,new PulseSaveDataEvent(true, "save data done"));
}

/*************************************************
函数名： saveDataDone
输入参数: NULL
输出参数：NULL
返回值： NULL
功能： 数据保存结束后的处理
*************************************************************/
void PulseView::saveDataDone()
{
    bool bSuccess = false;
    if(m_qsDataFile.isEmpty() == false)
    {
        bSuccess = true;
    }

    if( !m_bCancelSave )
    {
        if(bSuccess == false)
        {
            stopRefreshSaveProcessTimer();
            m_SaveprogressDialog.close();
            QString strInfo = QObject::trUtf8("Save failure!");
            MsgBox::informationWithoutAutoAccept("", strInfo );
        }
        else
        {
            m_dSavedRatio = 1;
            QFileInfo fileInfo(m_qsDataFile);
            PushButtonBar* pBtnBar = buttonBar();
            QPoint centerPoint;
            centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
            centerPoint.setY(pBtnBar->pos().y()+pBtnBar->height()/2);

            QString strInfo = fileInfo.fileName();
            processTooLongMsgText(strInfo);
            MsgBox::informationWithoutAutoAccept("", strInfo, centerPoint);

        }
    }
    else
    {
        m_bCancelSave = false;
        QFile savedFile(m_qsDataFile);
        if(savedFile.exists())
        {
            savedFile.remove();
        }
    }
}

/*************************************************
功能： 事件处理的函数
输入参数：
        event -- 事件对象
*************************************************************/
bool PulseView::event ( QEvent * event )
{
    //写数据到xml文件的事件
    if (event->type() == static_cast<QEvent::Type>(PulseSaveDataEvent::EventId))
    {
        saveDataDone();
        return true;
    }
    else
    {
        return SampleChartView::event(event);
    }
}

void PulseView::setConnectStatusNotice()
{
    if(m_pCAService->hasConnectedDevice())
    {
        m_eState = CA::STATE_CONNECTED;
        m_pConnectStatusLabel->setStyleSheet( STATUS_LABEL_CONNECTED );
        m_pConnectStatusLabel->setText( qApp->translate("CAView",(CAViewConfig::TEXT_CONNECT_CONNECTED )));
    }
    else
    {
        m_pConnectStatusLabel->setStyleSheet( STATUS_LABEL_DISCONNECTED );
        if(m_pCAService->hasCandidates())
        {
            m_pConnectStatusLabel->setText( qApp->translate("CAView",(CAViewConfig::TEXT_WAIT_CONNECT )));
        }
        else
        {
            m_pConnectStatusLabel->setText( qApp->translate("CAView",(CAViewConfig::TEXT_SEARCHING_CA_CONDITIONER )));
        }
    }
}

/*************************************************
功能： 定时器处理函数
输入参数：e -- 定时器事件
*************************************************************/
void PulseView::timerEvent(QTimerEvent *e)
{
    if(e->timerId() == m_iSaveDataTimerId)
    {
        m_dSavedRatio += SAVE_PROGRESS_STEP;

        if(m_dSavedRatio <= 1.0)
        {
            m_SaveprogressDialog.setValue( m_dSavedRatio * 100);
        }
        else
        {
            m_dSavedRatio = 1.0;
            stopRefreshSaveProcessTimer();
            m_SaveprogressDialog.close();
        }
    }
    else if(e->timerId() == m_iSearchingConditionerTimerId)
    {
        m_uiSearchConditionerTimes ++;
        if(m_uiSearchConditionerTimes <= CA::SEARCH_CONDITIONERS_TIMES_MAX)
        {
            if(m_pCAService->hasConnectedDevice() || m_pCAService->hasCandidates())
            {
                m_uiSearchConditionerTimes = 0;
                killTimer(m_iSearchingConditionerTimerId);
                m_iSearchingConditionerTimerId = CA::INVALID_TIMER_ID;
                setConnectStatusNotice();

                if(m_pCAService->hasCandidates())
                {
                    MsgBox::information("", QObject::trUtf8("Please pair CA Diag. processor first."));
                }
            }
        }
        else
        {
            m_uiSearchConditionerTimes = 0;
            MsgBox::information("", QObject::trUtf8("No available CA Diag. processor!"));
        }
    }
    else
    {
        dbg_warning("wrong timer id!\n");
    }
}

/*************************************************
函数名： stopRefreshSaveProcessTimer()
输入参数: NULL
输出参数：NULL
返回值： NULL
功能： 关闭刷新保存进度的定时器
*************************************************************/
void PulseView::stopRefreshSaveProcessTimer( void )
{
    if( CA::INVALID_TIMER_ID != m_iSaveDataTimerId )
    {
        killTimer(m_iSaveDataTimerId);
        m_iSaveDataTimerId = CA::INVALID_TIMER_ID;
    }
}

/*************************************************
函数名： startRefreshSaveProcessTimer()
输入参数: NULL
输出参数：NULL
返回值： NULL
功能： 起动刷新保存进度的定时器
*************************************************************/
void PulseView::startRefreshSaveProcessTimer( void )
{
    stopRefreshSaveProcessTimer();
    m_iSaveDataTimerId = startTimer(REFRESH_SAVE_DATA_PROGRESS_TIMER_200MS);
}

/*************************************************
函数名： startSaveProgress
输入参数: NULL
输出参数：NULL
返回值： NULL
功能：起动保存进度条
*************************************************************/
void PulseView::startSaveProgress()
{
    m_bCancelSave = false;
    m_SaveprogressDialog.reset();
    m_SaveprogressDialog.exec();
}

void PulseView::onProgressrRejected()
{
    stopRefreshSaveProcessTimer();
    m_bCancelSave = true;
}

void PulseView::initSaveProgress()
{
    QFont font = m_SaveprogressDialog.font();
    font.setPointSize(30);
    m_SaveprogressDialog.setFont(font);

    m_SaveprogressDialog.setWindowModality(Qt::ApplicationModal);
    m_SaveprogressDialog.setWindowFlags(Qt::FramelessWindowHint|Qt::Dialog);
    m_SaveprogressDialog.setFixedSize(SAVE_PROGRESS_SIZE_WIDTH, SAVE_PROGRESS_SIZE_HEIGHT);
    m_SaveprogressDialog.setCancelButton(NULL);

    m_SaveprogressDialog.setModal(true);
    m_SaveprogressDialog.setEnabled(true);


    connect(&m_SaveprogressDialog, SIGNAL(rejected()), this, SLOT(onProgressrRejected()));
}

/*************************************************
功能： 保存数据
输入参数：NULL
返回：NULL
*************************************************************/
QString PulseView::saveDataToFile()
{
    if(m_lRawData.size() <= 0)
    {
        PushButtonBar* pBtnBar = buttonBar();
        QPoint centerPoint;
        centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
        centerPoint.setY(pBtnBar->pos().y()+pBtnBar->height()/2);
        QString strInfo = QObject::trUtf8("No data!");
        MsgBox::warning("", strInfo, centerPoint);
        return "";
    }

    m_dSavedRatio = 0.0;

    // 弹出文件备注框
    showFileCommentBox();

    composeXMLDataFile();

    startRefreshSaveProcessTimer();
    m_SaveDataDateTime = QDateTime::currentDateTime();
    m_SaveDataFuture = QtConcurrent::run(this, &PulseView::thread_save_data, &m_stDataInfo);

    //起动保存进度条
    startSaveProgress();
    return "";
}

/*************************************************
功能： 保存设置
*************************************************************/
bool PulseView::saveConfig()
{
    m_pConfig->beginGroup( Module::GROUP_CA );
    //m_pConfig->setValue( m_iPhaseAlias, CAConfig::KEY_PULSE_PHASE_ALIAS );
    //m_pConfig->setValue( m_eSampleRate, CAConfig::KEY_SAMPLE_RATE );
    m_pConfig->setValue( m_ucPercentageBeforeTrig, CAConfig::KEY_PULSE_BEFORE_TRIG );
    m_pConfig->setValue( m_usTrigThreashold, CAConfig::KEY_PULSE_TRIG_AMP );
    m_pConfig->setValue( m_eTriggerWidth, CAConfig::KEY_PULSE_TRIGWIDTH );
    //m_pConfig->setValue( m_eRangeMax, CAConfig::KEY_PULSE_AMP_RANGE );
    m_pConfig->setValue( m_eGain, CAConfig::KEY_GAIN );
    m_pConfig->setValue( m_ePulseAccuCnt, CAConfig::KEY_PULSE_ACCU_COUNT );
    m_pConfig->endGroup();
    return true;
}

/************************************************
 * 函数名   : createPngFilePath
 * 输入参数 : strAbsolutePath---截图文件的路径
 * 输出参数 : NULL
 * 返回值   : 创建路径结果
 * 功能     : 创建截图文件存储路径
 ************************************************/
bool PulseView::createPngFilePath(const QString &strAbsolutePath)
{
    bool bCreateResult = true;

    QDir dir(strAbsolutePath);
    if(!dir.exists())
    {
        bCreateResult = dir.mkpath(strAbsolutePath);
    }

    return bCreateResult;
}

/*************************************************
函数名： setPngFilePath
输入参数: NULL
输出参数：strFilePathName---图片的保存路径
返回值： NULL
功能： 设置截屏的图片保存路径
*************************************************************/
void PulseView::setPngFilePath(QString &strFilePathName)
{
    QDateTime dateTime = QDateTime::currentDateTime();
    QString strAbsolutePath = DATA_STORAGE_PATH + '/' + ACCESS_G100_PULSE_FOLDER + '/';
    QString strDate = dateTime.toString("yyyyMMdd");
    strAbsolutePath += strDate.mid(0,4);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(4,2);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(6,2);

    createPngFilePath(strAbsolutePath);

    strFilePathName = strAbsolutePath +'/'+ dateTime.toString("yyyyMMdd_hhmmss") + ".png";
}

/*************************************************
功能： 响应S键事件
输入参数：NULL
*************************************************************/
void PulseView::onSKeyPressed()
{
    stopSample();
    setSampleBtnText(m_eState);

//    QString strFilePathName = "";
//    setPngFilePath(strFilePathName);


    saveDataToFile();
    if(m_lRawData.size() <= 0)
    {
        return;
    }

//    QString strPngFile = m_qsDataFile;
//    int iPos = strPngFile.indexOf('.');
//    strPngFile = strPngFile.remove(iPos, strPngFile.length() - iPos);
//    strPngFile += ".png";

//    QString qsSavedPath;
//    getDataFilePath(qsSavedPath);
//    QString strDataFileTime = m_SaveDataDateTime.toString("yyyyMMdd_hhmmsszzz");
//    QString strPngFile = qsSavedPath + "/" + strDataFileTime + PNG_FILE_NAME_SUFFIX;

//    QPixmap pngFile = screenShot();
//    if(!pngFile.save(strPngFile, "png"))
//    {
//        qWarning()<<"PulseView::onSKeyPressed, screen shot fail!n";
//    }
}

double PulseView::thresholdRatio(void)
{
    //return m_usTrigThreashold * 1.0 / m_uiRangeMax;
    return (m_usTrigThreashold * 1.0 + m_uiRangeMax) / (m_uiRangeMax * 2);
}

/************************************************
 * 功能     : 槽函数，增益状态变化处理
 ************************************************/
void PulseView::onGainState(CA::GainState eState)
{
    if(eState == m_eGainState)
    {
        //logDebug("gain state not changed.");
        return;
    }

    if(CA::GAIN_HIGH == eState)
    {
        m_pConnectStatusLabel->setStyleSheet(GAIN_STATE_STYLE);
        m_pConnectStatusLabel->setText(QObject::trUtf8("High Gain"));
    }
    else if(CA::GAIN_LOW == eState)
    {
        if(CA::GAIN_MIN == m_eGain)
        {
            //增益为最小时，不能再提示增益偏低
            m_pConnectStatusLabel->setStyleSheet(STATUS_LABEL_CONNECTED);
            m_pConnectStatusLabel->setText(qApp->translate("CAView", (CAViewConfig::TEXT_CONNECT_CONNECTED)));
        }
        else
        {
            m_pConnectStatusLabel->setStyleSheet(GAIN_STATE_STYLE);
            m_pConnectStatusLabel->setText(QObject::trUtf8("Low Gain"));
        }
    }
    else
    {
        m_pConnectStatusLabel->setStyleSheet(STATUS_LABEL_CONNECTED);
        m_pConnectStatusLabel->setText(qApp->translate("CAView", (CAViewConfig::TEXT_CONNECT_CONNECTED)));
    }

    m_eGainState = eState;

    return;
}

void PulseView::resetGainState()
{
    m_pConnectStatusLabel->setStyleSheet(STATUS_LABEL_CONNECTED);
    m_pConnectStatusLabel->setText(qApp->translate("CAView", (CAViewConfig::TEXT_CONNECT_CONNECTED)));
    m_eGainState = CA::GAIN_NORMAL;
    return;
}
