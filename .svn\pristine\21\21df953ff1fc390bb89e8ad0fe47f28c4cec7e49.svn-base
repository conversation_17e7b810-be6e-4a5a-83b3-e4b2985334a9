#include "compressutil.h"
#include "JlCompress.h"


/*******************************************
 * 功能：压缩文件
 * 输入参数：
 *      qstrCompressedFilePath：压缩文件路径
 *      qstrlstFiles：需要压缩文件的路径集合
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * *****************************************/
bool CompressUtil::compressFiles(const QString qstrCompressedFilePath, const QStringList &qstrlstFiles)
{
    return JlCompress::compressFiles(qstrCompressedFilePath, qstrlstFiles);
}

