#include "AEPhaseChart.h"
#include <QHBoxLayout>
#include <QVBoxLayout>
#include "window/Window.h"


const int CYCLE_DEGREE = 360;  // 周期角度
#define DEFAULT_HEIGHT 380//400

/************************************************
 * 功能     ：构造函数
 * 输入参数  ：父对象指针
 ************************************************/
AEPhaseChart::AEPhaseChart(QWidget *parent) :
    ChartWidget(parent)
{
    setAttribute(Qt::WA_TransparentForMouseEvents);

    m_fPhase = 0;
    m_usYMaxValue = 1;

    createMainView();   // 创建整体布局
}

/****************************
输入参数:trigger:触发值
功能： 设置触发值
*****************************/
void AEPhaseChart::setTrigger( int iTrigger )
{
    APP_CHECK_RETURN(m_pAEChart)
    m_pAEChart->setTrigger( iTrigger );
}

/****************************
输入参数:bRunningMode:运行状态 true -- 正在运行
                            false -- 停止运行
功能： 设置运行状态
*****************************/
void AEPhaseChart::setRunningMode( bool bRunningMode )
{
    APP_CHECK_RETURN(m_pAEChartOption)
    m_pAEChartOption->setRunningMode( bRunningMode );
}

/****************************
输入参数:iPhase：移动的相位（0~360）
功能： 移动相位
*****************************/
void AEPhaseChart::translatePhase( INT32 iPhase )
{
    if( iPhase < 0 || iPhase > CYCLE_DEGREE )
    {
        return;
    }
    APP_CHECK_RETURN( m_pAEChart );
    QList<AEAbstractChart::AbstractData> lAERawData = m_pAEChart->samples();// 获得图谱显示的数据
    float fPhaseOffset = (float)( iPhase - m_fPhase ) / CYCLE_DEGREE;       // 相位偏移对数据而言，变化的大小

    for( int i = 0;i < lAERawData.size();++i )
    {
        float fPhase = lAERawData.at( i ).fXscale + fPhaseOffset;
        if( fPhase < 0 )
        {
            fPhase += 1;
        }
        else if( fPhase > 1 )
        {
            fPhase -= 1;
        }
        lAERawData[i].fXscale = fPhase;
    }
    m_pAEChart->addSamples( lAERawData,m_pAEChart->colorIndex() );       // 调用添加一组数据的接口
    m_fPhase = iPhase;
    if( m_fPhase > CYCLE_DEGREE )
    {
        m_fPhase -= CYCLE_DEGREE;
    }
    else if( m_fPhase < -CYCLE_DEGREE )
    {
        m_fPhase += CYCLE_DEGREE;                        // 对相位偏移的角度进行限制
    }
}

/*************************************************
功能： 设置增益
输入参数：
        eGain -- 增益
*************************************************/
void AEPhaseChart::setGain( AE::GainType eGain )
{
    APP_CHECK_RETURN(m_pAEChartOption)
    m_pAEChartOption->setGain( eGain );
    m_usYMaxValue = AE::Y_RANGE_VALUES[eGain];
    m_pAEChart->setYAxisScale( m_usYMaxValue );
}

/*************************************************
功能： 设置增益
输入参数：
        eGain -- 增益
*************************************************/
void AEPhaseChart::setYMax( UINT16 usMax )
{
    m_usYMaxValue = usMax;
    m_pAEChart->setYAxisScale( m_usYMaxValue );
}

/*************************************************
功能： 设置通道
输入参数：
        eChannel -- 通道
*************************************************/
void AEPhaseChart::setChannel( AE::ChannelType eChannel )
{
    APP_CHECK_RETURN(m_pAEChartOption)
    m_pAEChartOption->setChannel( eChannel );
}

/****************************
输入参数:bValid:true -- 显示
              false -- 隐藏
功能： 设置是否显示通道
*****************************/
void AEPhaseChart::setChannelValid( bool bValid )
{
    APP_CHECK_RETURN(m_pAEChartOption)
    m_pAEChartOption->setChannelValid( bValid );
}

/****************************
输入参数:eSyncState:同步状态
功能： 设置同步状态
*****************************/
void AEPhaseChart::setSyncState( Module::SyncState eSyncState )
{
    APP_CHECK_RETURN(m_pAEPhaseSync)
    m_pAEPhaseSync->setSyncState( eSyncState );
}

/****************************
输入参数:eSyncSource:同步源
功能： 设置同步源
*****************************/
void AEPhaseChart::setSyncSource( Module::SyncSource eSyncSource )
{
    APP_CHECK_RETURN(m_pAEPhaseSync)
    m_pAEPhaseSync->setSyncSource( eSyncSource );
}

/****************************
输入参数:fPhaseValue:相对相位值,fPeakValue:峰值
功能： 添加数据
*****************************/
bool AEPhaseChart::addSamples(float fPhaseValue, float fPeakValue )
{
    //APP_CHECK_RETURN( m_pAEChart );
    if( m_pAEChart == NULL )
    {
        return false;
    }

    bool bSuccess = false;
    if( fPeakValue <= m_usYMaxValue && m_usYMaxValue > 0)           // 若添加的数据大于y轴最大值，则直接返回
    {
        AE::PhaseData sAEPhaseData;
        sAEPhaseData.fPeakValue = fPeakValue;
        sAEPhaseData.fPhaseValue = fPhaseValue;
        m_lRawData.append( sAEPhaseData );          // 将数据添加进缓存

        float fPhase = fPhaseValue + m_fPhase;     // 因底层传来的数据时0~240对应0~360°
        if( fPhase < 0 )
        {
            fPhase += CYCLE_DEGREE;
        }
        else if( fPhase > CYCLE_DEGREE )
        {
            fPhase -= CYCLE_DEGREE;
        }
        AEAbstractChart::AbstractData sData;
        sData.fXscale = fPhase / CYCLE_DEGREE;
        sData.fYscale = fPeakValue / m_usYMaxValue;
        if(     sData.fXscale <= 1.0
                && sData.fXscale > NUMBER_ZERO
                && sData.fYscale <= 1.0
                && sData.fYscale > NUMBER_ZERO )
        {
            if(m_pAEChart->addSample( sData ))  // 调用图谱区域接口将数据添加进去
            {
                bSuccess = true;
            }
            else
            {
                bSuccess = false;
            }
        }
        else
        {
            bSuccess = false;
        }
    }
    else
    {
        // data out of range
        bSuccess = false;
    }

    return bSuccess;
}
bool AEPhaseChart::dataIsValid(AE::PhaseData sAEPhaseData)
{
    bool bValid;
    if( sAEPhaseData.fPeakValue <= m_usYMaxValue && m_usYMaxValue > 0)
    {
        bValid = true;
    }
    else
    {
        bValid = false;
    }

    if( bValid )
    {
        AEAbstractChart::AbstractData sData;
        sData.fXscale = sAEPhaseData.fPhaseValue  / CYCLE_DEGREE;
        sData.fYscale = sAEPhaseData.fPeakValue  / m_usYMaxValue;

        if( sData.fXscale <= 1.0  || sData.fYscale > NUMBER_ZERO )
        {
            float fX = sData.fXscale * m_pAEChart->getChartWidth();  // 当添加一组数据时的处理（有相位移动或回放等需求时需使用的接口）
            float fY = sData.fYscale  * m_pAEChart->getChartHeight();
            if( fX < NUMBER_ZERO || fY < NUMBER_ZERO )
            {
                bValid = false;
            }
            else
            {
                bValid = true;
            }
        }
        else
        {
            bValid = false;
        }

    }
    return bValid;
}
/****************************
输入参数:rawData:原始数据
功能： 添加数据
*****************************/
void AEPhaseChart::addReviewSample(const QList<AE::PhaseData> &rawData )
{
    APP_CHECK_RETURN( m_pAEChart );
    AEAbstractChart::AbstractData aeRawData;
    QList<AEAbstractChart::AbstractData> lRawData;
    if( m_usYMaxValue > 0 )
    {
        for( int i = 0;i < rawData.size();++i )
        {
            float fPhase = rawData.at( i ).fPhaseValue + m_fPhase;     // 因底层传来的数据时0~240对应0~360°
            if( fPhase < 0 )
            {
                fPhase += CYCLE_DEGREE;
            }
            else if( fPhase > CYCLE_DEGREE )
            {
                fPhase -= CYCLE_DEGREE;
            }
            aeRawData.fXscale = fPhase / CYCLE_DEGREE;
            aeRawData.fYscale = rawData.at( i ).fPeakValue / m_usYMaxValue;
            lRawData.append( aeRawData );
        }
        m_pAEChart->addSamples( lRawData );
    }
    else
    {
        // max y is wrong
    }

}

/****************************
返回值：QList<AE::PhaseData>：存放图谱原始数据
功能： 返回保存的原始数据
*****************************/
QList<AEAbstractChart::AbstractData>  const& AEPhaseChart::samples( void ) const
{
    return m_pAEChart->samples();
}
/****************************
返回值：QList<QColor>：存放图谱数据点颜色
功能： 返回保存的数据点颜色
*****************************/
QList<QColor> const& AEPhaseChart::sampleColors( void )
{
    m_lDataColors.clear();
    foreach (int index, m_pAEChart->colorIndex())
    {
        m_lDataColors.append( m_pAEChart->getColorFromIndex((quint16)index) );
    }
    return m_lDataColors;
}

/****************************
返回值：QList<int> ：存放图谱数据放电次数
功能： 返回图谱数据放电次数
*****************************/
QList<int> const& AEPhaseChart::sampleColorIndex( void )
{
    return m_pAEChart->colorIndex();
}


/****************************
输入参数:iPulseCounting:计数值
功能： 设置脉冲计数值
*****************************/
void AEPhaseChart::setPulseCounting( int iPulseCounting )
{
    APP_CHECK_RETURN(m_pAEChartOption)
    m_pAEChartOption->setPulseCounting( iPulseCounting );
}

/****************************
输入参数:bValid:true -- 显示
              false -- 隐藏
功能： 设置是否显示计数值
*****************************/
void AEPhaseChart::setPulseCountingValid( bool bValid )
{
    APP_CHECK_RETURN(m_pAEChartOption)
    m_pAEChartOption->setPulseCountingValid( bValid );
}

/****************************)
输入参数:bValid:true -- 显示
              false -- 隐藏
功能： 设置是否显示运行图标
*****************************/
void AEPhaseChart::setRunningIconValid( bool bValid )
{
    APP_CHECK_RETURN(m_pAEChartOption)
    m_pAEChartOption->setRunningIconValid( bValid );
}

/****************************
功能： 清除显示
*****************************/
void AEPhaseChart::clear( void )
{
    APP_CHECK_RETURN( m_pAEChart );
    m_lRawData.clear();
    m_pAEChart->clear();    // 清除显示
}

/****************************
函数名： createMainView( void );
输入参数:NULL
输出参数：NULL
返回值：NULL
功能： 创建界面整体布局
*****************************/
void AEPhaseChart::createMainView( void )
{
    setFixedSize( Window::WIDTH,DEFAULT_HEIGHT );
    m_pAEChart = new AEAbstractChart( AE::MODE_PHASE,this );
    m_pAEChartOption = new AEOption( this );
    m_pAEPhaseSync = new SyncWidget( this );  // 实例化图谱，通道状态信息，同步状态信息栏的对象

    m_pDiagRetLabel= new QLabel(this);
    QFont qfDiag = m_pDiagRetLabel->font();
    qfDiag.setPointSize(20);
    m_pDiagRetLabel->setFont(qfDiag);
    m_pDiagRetLabel->setFixedSize(400, 40);
    m_pDiagRetLabel->setMargin(5);
    m_pDiagRetLabel->setAlignment(Qt::AlignCenter);
    m_pDiagRetLabel->setText("");
    m_pDiagRetLabel->move(10, 70);

    QHBoxLayout *hLayout = new QHBoxLayout;
    hLayout->addWidget( m_pAEChart,12 );
    hLayout->addWidget( m_pAEPhaseSync,1);      // 图谱和同步信息栏采用水平布局，比例为12:1

    QVBoxLayout *vLayout = new QVBoxLayout( this );
    vLayout->addWidget( m_pAEChartOption,2 );
    vLayout->addLayout( hLayout,9 );            // 状态信息栏和下方图谱显示区域采用竖直布局，比例为2:9
}

/************************************************
 * 功能：设置诊断状态
 * 输入参数：
 *      stDiagDisplayInfo：诊断信息
 ************************************************/
void AEPhaseChart::setDiagRet(const DiagConfig::DiagDisplayInfo &stDiagDisplayInfo, const bool bShowDiagDisplayInfo)
{
    m_stDiagInfo = stDiagDisplayInfo;
    if(m_pDiagRetLabel && bShowDiagDisplayInfo)
    {
        m_pDiagRetLabel->setText(DiagConfig::getDiagnosisDisplayInfo(m_stDiagInfo));
    }

    return;
}

/************************************************
 * 功能：清除诊断状态
 ************************************************/
void AEPhaseChart::clearDiagRet()
{
    m_stDiagInfo.eDiagRet = DiagConfig::Diag_Unrecord;
    m_stDiagInfo.qstrPDDesInfo = "";
    m_stDiagInfo.qstrPDSignalInfos = "";

    if(m_pDiagRetLabel)
    {
        m_pDiagRetLabel->setText("");
    }

    return;
}

