#include "hfctprpspdaview.h"
#include "controlButton/ControlButtonInfo.h"
#include "buttonBar/PushButtonBar.h"
#include "controlButton/CmdButton.h"
#include "controlButton/RadioButton.h"
#include "controlButton/SliderButton.h"
#include "messageBox/msgbox.h"
#include "window/Window.h"
#include "hfct/HFCTView.h"
#include "hfct/HFCTViewConfig.h"
#include "hfct/HFCTConfig.h"
#include "appconfig.h"
#include "View.h"
#include "dataSave/DataStructures.h"
#include "pda/pdatask.h"
#include "pda/pdaservice.h"
#include "log/log.h"
#include "datafile/prps/prpsmapdefine.h"
#include "uhf/UHFConfig.h"
#include "diagnosismgr/diagnosismanager.h"
#include "datadefine.h"

const int MS_PER_MIN = 60 * 1000;  //每分钟对应的ms
const int INVALID_USER = -1;

#define PRPD_PRPS_HEIGHT 400

typedef enum _HFCTPrpsButton
{
    BUTTON_HFCT_SAMPLE = 0,//停止采样
    BUTTON_HFCT_PHASE_SHIFT,//相位偏移
    BUTTON_HFCT_GAIN,//增益
    BUTTON_ALTAS_TYPE,//图谱类型
    BUTTON_HFCT_SAVE_DATA,//保存数据
    BUTTON_ADD_TEST_DATA, //新增测试项
    BUTTON_HFCT_ACCUMULATION,//累积
    BUTTON_MORE_CONFIG,//更多配置

    BUTTON_HFCT_SYNC_SOURCE, //同步源
    BUTTON_THRESHOLD,//阈值
    BUTTON_HFCT_RESTORE_DEFAULT,//恢复默认参数
}HFCTPrpsButton;

//增益
const ButtonInfo::RadioValueConfig s_HFCTGainCfg =
{
    HFCT::GAIN_OPTIONS, HFCT::GAIN_COUNT
};

//同步
const ButtonInfo::RadioValueConfig s_HFCTSyncCfg =
{
    HFCT::TEXT_SYNC_OPTIONS, sizeof(HFCT::TEXT_SYNC_OPTIONS)/sizeof(char*)
};

//相位偏移
const ButtonInfo::SliderValueConfig s_HFCTPhaseShift =
{
    HFCT::PHASE_MIN, HFCT::PHASE_MAX, HFCT::PHASE_STEP
};


//是否累积
const ButtonInfo::RadioValueConfig s_IsAccumulationCfg =
{
    HFCT::TEXT_ACCUMULATIVE_TIME_OPTIONS, sizeof(HFCT::TEXT_ACCUMULATIVE_TIME_OPTIONS)/sizeof(char*)
};

//图谱类型
const ButtonInfo::RadioValueConfig s_AltasTypeCfg =
{
    HFCT::TEXT_ALTAS_TYPE_OPTIONS, sizeof(HFCT::TEXT_ALTAS_TYPE_OPTIONS)/sizeof(char*)
};

//阈值 单位:%
const ButtonInfo::RadioValueConfig s_ThresholdCfg =
{
    HFCT::TEXT_THRESHOLD_OPTIONS, sizeof(HFCT::TEXT_THRESHOLD_OPTIONS)/sizeof(char*)
};

//控制按钮定义
const ButtonInfo::Info s_HFCTButtonInfo[] =
{
    { BUTTON_HFCT_SAMPLE, { ButtonInfo::COMMAND, HFCT::TEXT_STOP, NULL, ":/images/sampleControl/sampleMode.png",NULL } },//采样模式
    { BUTTON_HFCT_PHASE_SHIFT, { ButtonInfo::FIXED_STEP_SLIDER, HFCT::TEXT_PHASE_ALIAS, HFCT::TEXT_DEGREE, ":/images/sampleControl/phaseShift.png", &s_HFCTPhaseShift } },//相位偏移
    { BUTTON_HFCT_GAIN, { ButtonInfo::RADIO, HFCT::TEXT_GAIN, NULL, ":/images/sampleControl/gain.png", &s_HFCTGainCfg } },//前置增益
    { BUTTON_ALTAS_TYPE, { ButtonInfo::RADIO, HFCT::TEXT_ALTAS_TYPE, NULL, NULL, &s_AltasTypeCfg } },//图谱类型
    { BUTTON_HFCT_SAVE_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL } },//保存数据
    { BUTTON_ADD_TEST_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_ADD, NULL, ":/images/sampleControl/noiseClean.png", NULL } },//新增测试项
    { BUTTON_THRESHOLD, { ButtonInfo::RADIO, HFCT::TEXT_PRPS_NOISEREDUCTION, NULL, NULL, &s_ThresholdCfg } },//阈值
    { BUTTON_MORE_CONFIG, { ButtonInfo::COMMAND, HFCT::TEXT_MORE_CONFIG, NULL, ":/images/sampleControl/moreconfig.png", NULL } },//更多配置
};

//"更多.."按钮定义
const ButtonInfo::Info s_HFCTButtonInfoMore[] =
{
    //{ BUTTON_HFCT_SYNC_SOURCE, { ButtonInfo::RADIO, HFCT::TEXT_SYNC_SOURCE, NULL, ":/images/sampleControl/syncMode.png", &s_HFCTSyncCfg } },//同步源
    { BUTTON_HFCT_ACCUMULATION, { ButtonInfo::RADIO, HFCT::TEXT_ACCUMULATIVE_TIME, NULL, ":/images/sampleControl/brandWidth.png", &s_IsAccumulationCfg } },//是否累积
    { BUTTON_HFCT_RESTORE_DEFAULT, { ButtonInfo::COMMAND, HFCT::TEXT_RESTORE_DEFAULT, NULL, ":/images/sampleControl/restoreDefault.png", NULL } },//恢复默认
};

/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
HFCTPrpsPDAView::HFCTPrpsPDAView(const QString &strTitle, const ItemTestData &stTestDataInfo, QWidget *parent) :
    HFCTPrpsViewBase(strTitle,parent)
{
    m_qsTestDataFilePath = stTestDataInfo.strFilePath;
    m_stTestData = stTestDataInfo;

    //初始化使用的数据
    initDatas();

    ChartWidget *pChart = createChart(parent);
    setChart( pChart );

    //显示上次的测试数据
    if(m_stTestData.bTested)
    {
        playBackTestedData(m_qsTestDataFilePath);
        QTimer::singleShot(START_SAMPLE_TIME_INTERVAL_2000MS, this, SLOT(onStartTest()));
    }
    else
    {
        onStartTest();
    }
}

/*************************************************
功能： 析构
*************************************************************/
HFCTPrpsPDAView::~HFCTPrpsPDAView( )
{
    stopSample();
    saveConfig(); // 存储到配置文件中
    m_qui8DataCnt = -1;
    m_vConfidences.clear();
    m_mDefect.clear();
}

/************************************************
 * 函数名   : onStartTest
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :槽函数，起动采样
 ************************************************/
void HFCTPrpsPDAView::onStartTest()
{
    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar( HFCT::CONTEXT, s_HFCTButtonInfo, sizeof(s_HFCTButtonInfo)/sizeof(ButtonInfo::Info) );
    m_pSampleBtn = pButtonBar->button(BUTTON_HFCT_SAMPLE); // 因采样按钮显示的文本需要刷新
    ((PopupButton*)(buttonBar()->button(BUTTON_ALTAS_TYPE)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_THRESHOLD)))->setPopupMode(PopupWidget::SWITCH_MODE);

    // 创建更多设置栏
    createMoreConfigButtonBar(HFCT::CONTEXT, s_HFCTButtonInfoMore, sizeof(s_HFCTButtonInfoMore)/sizeof(ButtonInfo::Info));
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_ACCUMULATION)))->setPopupMode(PopupWidget::SWITCH_MODE);

    //恢复当前的参数设置
    initParameters();
    m_eSyncState = Module::Not_Sync;

    //if the freq when playback is 60hz,then when start stample, the freq is 50 hz, this can happen in test case
    //    if(m_ucSysFreq == PrpsGlobal::FREQ_50)
    //    {
    //        m_pChart->setDisplayedPeriodCnt(PrpsGlobal::FREQ_50);
    //    }
    //    else
    //    {
    //        m_pChart->setDisplayedPeriodCnt(PrpsGlobal::FREQ_60);
    //    }

    //设置数据
    setButtonBarDatas();

    setChartDatas();

    //设置工作参数
    setWorksets();

    m_pChart->clearData();
    m_pChart->setPeriodNum(m_iSysPeriod);

    //启动采集
    startSample();
    setSampleBtnText(isSampling());

    if(m_stTestData.bIsBgn && buttonBar()->button(BUTTON_ADD_TEST_DATA)->isEnabled())
    {
        //背景测试中不允许新增
        buttonBar()->button(BUTTON_ADD_TEST_DATA)->setEnabled(false);
    }

    buttonBar()->setFocus();

    return;
}

/************************************************
 * 函数名   : initParametersByDataFile
 * 输入参数 : qsFile---数据文件存放路径
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :根据数据文件里的数据初始化采样参数
 ************************************************/
void HFCTPrpsPDAView::initParametersByDataFile(const QString &qsFile)
{
    HFCTPRPSAndPRPDDataSave  fDataSave;
    HFCTPRPSPRPDDataInfo stData;
    int value = fDataSave.getData( qsFile,&stData );

    if( HC_FAILURE == value )
    {
        initParameters();
        m_eSyncState = Module::Not_Sync;
        logError(QString("get param from file: %1 failed.").arg(qsFile).toLatin1().data());
        return;
    }

    m_eSyncSource = (Module::SyncSource)(stData.stPRPSInfo.eSyncSource -1);

    m_ucSysFreq = stData.ucFreq;
    m_eSyncState = (Module::SyncState) stData.stPRPSInfo.ucSyncState;
    m_eSpectrumState = (HFCT::SpectrumState) stData.stPRPSInfo.eDataSign;

    int iGroup = HFCT::GROUP_HFCT_PRPS;
    m_pConfig->beginGroup( Module::GROUP_HFCT );
    m_iPhaseAlias = m_pConfig->value( HFCT::KEY_PHASEALIAS ).toUInt();
    m_eGain = (HFCT::Gain)m_pConfig->value( HFCT::KEY_GAIN ).toUInt();
    m_iAccumulationTime = m_pConfig->value( HFCT::KEY_PRPS_ACCUMULATION_TIME,iGroup ).toUInt();
    m_fThresholdPercentage = m_pConfig->value( UHF::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup).toFloat();
    m_pConfig->endGroup();

    return;
}

/************************************************
 * 函数名   : playBackTestedData
 * 输入参数 : qsFile---数据文件存放路径
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 回放数据文件里的测试数据
 ************************************************/
void HFCTPrpsPDAView::playBackTestedData(const QString &qsFile)
{
    HFCTPRPSAndPRPDDataSave  fDataSave;
    HFCTPRPSPRPDDataInfo sPlayBackDataInfo;

    int value = fDataSave.getData( qsFile,&sPlayBackDataInfo );

    if( HC_FAILURE == value )
    {
        qWarning() << "HFCTPrpsPDAView::playBackTestedData :" << qsFile << " get data  failed!";
        return;
    }
    displayMap(sPlayBackDataInfo);
}

/****************************
功能： 在回放中显示
输入参数: void
*****************************/
void HFCTPrpsPDAView::displayMap(HFCTPRPSPRPDDataInfo &stInfo)
{
    m_pChart->clearData();

    //    if(stInfo.ucFreq == PrpsGlobal::FREQ_50)
    //    {
    //        m_pChart->setDisplayedPeriodCnt(PrpsGlobal::FREQ_50);
    //    }
    //    else
    //    {
    //        m_pChart->setDisplayedPeriodCnt(PrpsGlobal::FREQ_60);
    //    }

    //    /*站点名*/
    //    setStationName( stInfo.stPRPSHeadInfo.strSubstationName );

    //    /*设备名*/
    //    setDeviceName( stInfo.stPRPSHeadInfo.strDeviceName );


    /*设置频率*/
    m_pChart->setPowerFreq(PrpsGlobal::FREQ_50);


    double dMax =stInfo.stPRPSInfo.fMax;

    dbg_info("dMax is %f\n", dMax);
    /*设置最大值*/
    m_pChart->setMaxSpectrum( dMax,(PrpsGlobal::SpectrumState)stInfo.stPRPSInfo.eDataSign );


    /*设置量程*/
    m_pChart->setRangeMin( stInfo.stPRPSInfo.fAmpLowerLimit );
    m_pChart->setRangeMax( stInfo.stPRPSInfo.fAmpUpperLimit );

    m_pChart->setSync((PrpsGlobal::SyncSource) (stInfo.stPRPSInfo.eSyncSource -1), (PrpsGlobal::SyncState) stInfo.stPRPSInfo.ucSyncState);

    //是否无信号状态没有保存到数据文件，回放时，先简单默认不显示无信号
    m_pChart->setConnected(true);

    QVector<qint16> prpdData;
    prpdData.clear();
    double dPRRepeatyData;
    //int iPeriodCnt = stInfo.vecPRRepeatyData.size() / stInfo.stPRPDInfo.iPhaseIntervalCount;
    int iPeriodCnt = stInfo.stPRPDInfo.iPowerFreCycleCount;
    double dTimePerPeriod = (1000.0 / (double) stInfo.ucFreq) / 1000.0;//每周期时间 单位s

    logInfo(QString("review data period number: %1.").arg(iPeriodCnt));


    for(int i = 0; i < stInfo.stPRPDInfo.iQuantificationAmp; ++i)
    {
        for(int j = 0; j < stInfo.stPRPDInfo.iPhaseIntervalCount; ++j)
        {
            dPRRepeatyData = stInfo.vecPRRepeatyData.at(j * stInfo.stPRPDInfo.iQuantificationAmp + i);
            dPRRepeatyData = dPRRepeatyData * iPeriodCnt * dTimePerPeriod;
            prpdData.append((qint16)(dPRRepeatyData + 0.5f));
        }
    }

    m_pChart->setPRPSThreshold(stInfo.stPRPSInfo.fAnalysisThreshold);
    m_pChart->setPRPDThreshold(stInfo.stPRPDInfo.fAnalysisThreshold);

    m_pChart->setPeriodNum(iPeriodCnt);
    m_pChart->addPlayBackData( stInfo.vecPRPSData, prpdData, stInfo.stPRPDInfo.iPowerFreCycleCount );

    if(m_stTestData.bIsBgn)
    {
        m_pChart->clearDiagRet();
    }
    else
    {
        QString qstrDesInfo = DiagnosisManager::instance()->getDiagInfoByPlayback(stInfo.stPRPSHeadInfo.qstrPDSignalTypeInfos, DIAG_PRPS);

        DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
        stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stInfo.stPRPSHeadInfo.ePDDefectLevel);
        stDiagDisplayInfo.qstrPDDesInfo = qstrDesInfo;
        stDiagDisplayInfo.qstrPDSignalInfos = stInfo.stPRPSHeadInfo.qstrPDSignalTypeInfos;

        m_pChart->playbackDiagInfo(stDiagDisplayInfo);
    }
    m_pChart->setAltasType(PhaseAbstractView::PRPS_PRPD);

    return;
}

/************************************************
 * 函数名   : setTestPointInfo
 * 输入参数 : stTestPointInfo---巡检测点相关信息
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 设置显示测点信息
 ************************************************/
void HFCTPrpsPDAView::setTestPointInfo(const View::PatrolTestPointInfo &stTestPointInfo)
{
    m_stTestPointInfo = stTestPointInfo;
    m_qsStation = stTestPointInfo.strStationName;
    m_qsDevice = stTestPointInfo.strDeviceName;

    QString qstrInfo = VIEW_TRANSLATE(View::TEXT_STATION_NAME) + m_qsStation;
    m_pStationLabel->setText(getRightEllipsisInfo(m_pStationLabel->font(), width(), qstrInfo));

    qstrInfo = VIEW_TRANSLATE(View::TEXT_DEVICE) + m_qsDevice;
    m_pDeviceLabel->setText(getRightEllipsisInfo(m_pDeviceLabel->font(), width(), qstrInfo));

    qstrInfo = VIEW_TRANSLATE(View::TEXT_TESTPOINT) + stTestPointInfo.strTestPointName;
    m_pTestPointLabel->setText(getRightEllipsisInfo(m_pTestPointLabel->font(), width(), qstrInfo));
}

ChartWidget *HFCTPrpsPDAView::createChart(QWidget *parent)
{
    Q_UNUSED(parent)
    //站名
    m_pStationLabel = new QLabel;
    //设备名
    m_pDeviceLabel = new QLabel;
    //测点名
    m_pTestPointLabel = new QLabel;

    //设置style
    QFont font = m_pStationLabel->font();
    font.setPointSize(20);
    m_pStationLabel->setFont(font);
    m_pDeviceLabel->setFont(font);
    m_pTestPointLabel->setFont(font);

    QPalette labelPalette;
    QPalette bgPalette;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    SystemSet::PRPSBGColor eColor = (SystemSet::PRPSBGColor) pConfig->value( APPConfig::KEY_PRPS_BG_COLOR ).toInt();
    pConfig->endGroup();
    if(eColor == SystemSet::PRPS_BG_COLOR_GRAY)
    {
        labelPalette.setColor(QPalette::WindowText,Qt::white);
        bgPalette.setColor( QPalette::Background,QColor(128, 128, 128) );
    }
    else
    {
        labelPalette.setColor(QPalette::WindowText,Qt::black);
        bgPalette.setColor( QPalette::Background,Qt::white );
    }
    m_pStationLabel->setPalette(labelPalette);
    m_pDeviceLabel->setPalette(labelPalette);
    m_pTestPointLabel->setPalette(labelPalette);

    int iMax = 0;
    iMax = HFCT::GAIN_BASE + HFCT::GAIN_VALUES[m_eGain];

    m_pChart = new HfctPrpsUnionView( PERIOD_CNT, PERIOD_CNT, PHASE_CNT,iMax,HFCT::CHART_MIN_VALUE );


    m_pChart->setFixedSize( Window::WIDTH,/*CHART_HEIGHT*/ PRPD_PRPS_HEIGHT);
    m_pChart->setPrpdContentsMargins(0, PDA_PRPD_MARGIN, 0, PDA_PRPD_MARGIN);

    ChartWidget *pWidget = new ChartWidget;
    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->setContentsMargins(0, 0, 0, 0);
    vLayout->addWidget(m_pStationLabel);
    vLayout->addWidget(m_pDeviceLabel);
    vLayout->addWidget(m_pTestPointLabel);
    vLayout->addWidget(m_pChart);

    pWidget->setLayout(vLayout);
    pWidget->setFixedWidth(Window::WIDTH);

    setPalette(bgPalette);
    setAutoFillBackground(true);

    return pWidget;
}

/*************************************************
功能： 响应S键事件
输入参数：
        id -- 按钮ID
*************************************************************/
void HFCTPrpsPDAView::onSKeyPressed()
{
    saveTestData();
}

void HFCTPrpsPDAView::showEvent(QShowEvent *event)
{
    Q_UNUSED(event);

    return;
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void HFCTPrpsPDAView::onButtonValueChanged( int id, int iValue )
{
    switch( id )
    {
    case BUTTON_HFCT_ACCUMULATION:          // 设置累积模式
    {
        m_iAccumulationTime = iValue;

        updateAccumulativeTime();
    }
        break;
    case BUTTON_HFCT_GAIN:          // 设置增益
    {
        if( (HFCT::Gain)iValue != m_eGain )
        {
            m_eGain = (HFCT::Gain)iValue;
            setGain( m_eGain );
            m_pChart->clearData();
            m_pChart->setRangeMin( HFCT::GAIN_VALUES[m_eGain] );
            m_pChart->setRangeMax( HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE );
        }
        else
        {
            // GAIN not changed
        }
    }
        break;
    case BUTTON_HFCT_SYNC_SOURCE:   // 设置同步源
    {
        Module::SyncSource eSyncSource = (Module::SyncSource)(iValue + Module::WIRELESS_SYNC);
        if( eSyncSource != m_eSyncSource )
        {
            m_eSyncSource = eSyncSource;
            setSyncSource( m_eSyncSource );
            m_pChart->setSync( (PrpsGlobal::SyncSource)m_eSyncSource,
                               (PrpsGlobal::SyncState)m_eSyncState );
        }
        else
        {
            // syncsource not changed
        }
    }
        break;
    case BUTTON_HFCT_PHASE_SHIFT:   // 设置相位偏移
    {
        if( iValue != m_iPhaseAlias )
        {
            m_iPhaseAlias = iValue;
            m_pChart->setPhaseOffset( m_iPhaseAlias );
        }
        else
        {
            //iPhase not changed
        }
    }
        break;
    case BUTTON_ALTAS_TYPE://图谱类型
    {
        m_eAltasType = static_cast<PhaseAbstractView::AltasType>(iValue);
        updateTitle();
        m_pChart->setAltasType(m_eAltasType);
    }
        break;
    case BUTTON_THRESHOLD://阈值
    {
        m_eThresholdMode = static_cast<HFCT::ThresholdMode>(iValue);
        updateThresholdPercentage();
        m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
        m_pChart->setPRPDThresholdPercentage(m_fThresholdPercentage);
    }
        break;
    default:
        break;
    }
}

/*************************************************
功能： 弹出新增测试项窗口
*************************************************************/
void HFCTPrpsPDAView::addTestData()
{
    PDAServiceNS::TestPointType eType = PDAService::instance()->currentTask()->currentTestType()->eTestPointType;
    AddTestDataDialog *pDialog = new AddTestDataDialog(eType);
    pDialog->show();
    connect(pDialog, SIGNAL(sigAddingTestDataChanged(struct_AddingTestData&)), this, SLOT(onAddTestData(struct_AddingTestData&)));
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void HFCTPrpsPDAView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case BUTTON_MORE_CONFIG://更多设置
    {
        showMoreConfigButtonBar();
    }
        break;
    case BUTTON_ADD_TEST_DATA://新增测试项
    {
        addTestData();
    }
        break;
    case BUTTON_HFCT_SAMPLE:   // 采样、停止
    {
        if(isSampling())
        {
            stopSample();
        }
        else
        {
            m_pChart->clearData();
            startSample();
        }

        setSampleBtnText(isSampling());
    }
        break;
    case BUTTON_HFCT_RESTORE_DEFAULT:// 恢复默认
    {
        restoreDefault();
        m_pChart->clearData();
    }
        break;
    case BUTTON_HFCT_SAVE_DATA: // 保存数据
    {
        saveTestData();
    }
        break;
    default:
        break;
    }
}

/*************************************************
函数名： saveTestData
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 测试数据传给pdaservice保存
*************************************************************/
void HFCTPrpsPDAView::saveTestData()
{
    if(m_bPdaWaiting)
    {
        logInfo("saving data...");
        return;
    }
    m_bPdaWaiting = true;

    //停止采集
    stopSample();
    if(savePDAData( m_qsStation, m_qsDevice ))
    {
        delayToClose();
    }
    else
    {
        m_bPdaWaiting = false;
        PushButtonBar* pBtnBar = buttonBar();
        QPoint centerPoint;
        centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
        centerPoint.setY(pBtnBar->pos().y()+pBtnBar->height()/2);
        MsgBox::information("", QObject::trUtf8("Save failure!"), centerPoint);
        //startSample();
    }
    // 巡检里面特殊处理，因为保存成功会自动跳转，清除数据也没有意义
    // 如果保存失败的话，也不能清除数据，应该保持原样，避免用户需要重新保存原有数据
    //m_pChart->clearData();
}

/*************************************************
功能： 槽，响应读取的数据
输入参数：
        data -- 周期数据
*************************************************************/
void HFCTPrpsPDAView::onDataRead(HFCT::PRPSData data, MultiServiceNS::USERID userId)
{
    if((getUserId() != INVALID_USER) && (getUserId() == userId) && isSampling())
    {
        m_stPRPSFeateurInfo.dSpecMaxVal = m_pChart->rangeMax();
        m_stPRPSFeateurInfo.dSpecMinVal = m_pChart->rangeMin();
        m_stPRPSFeateurInfo.iPhaseNum = m_pChart->phaseCount();
        m_stPRPSFeateurInfo.iPeriodNum = m_pChart->periodCount();

        QVector<double> vData;
        for( int i = 0, iSize = data.vSpectrum.size(); i < iSize; ++i )
        {
            if(data.vSpectrum[i] < Module::ZERO)
            {
                data.vSpectrum[i] = 0;
            }
            vData.append( (double)data.vSpectrum[i] );
            m_stPRPSFeateurInfo.qvtDataIn.append(static_cast<double>(data.vSpectrum[i]));
        }

        if(HFCT::THRESHOLD_AUTO == m_eThresholdMode && DealData::denoisePRPSData(m_stPRPSFeateurInfo))
        {
            m_fThresholdPercentage = static_cast<float>(m_stPRPSFeateurInfo.dThresholdDbVal);
            m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
            m_pChart->setPRPDThresholdPercentage(m_fThresholdPercentage);
        }

        m_pChart->setData( vData );             // 将数据刷新到图谱上

        if(m_stTestData.bIsBgn)
        {
            m_pChart->clearDiagRet();
        }
        else
        {
            diagDataInfo();
        }

        if( m_vMaxValue.size() >= PERIOD_CNT )
        {
            m_vMaxValue.pop_front();            // 每推进一次，且最大值周期总数超出50周期(无论工频时50Hz还是60Hz)，则将最前一周期数据删除
        }
        m_vMaxValue.append( PRPSMaxValue( (double)data.cMaxSpectrum,data.eSpectrumState ) );

        //计算每周期最大值集合中的最大值，即当前显示数据中的最大值
        double dMaxValue = 0;
        HFCT::SpectrumState eState = m_vMaxValue.first().eSpectrumState;
        for( int i = 0, iSize = m_vMaxValue.size(); i < iSize; ++i )
        {
            if( m_vMaxValue.at( i ).cMaxValue > dMaxValue )
            {
                dMaxValue = m_vMaxValue.at( i ).cMaxValue;
                eState = m_vMaxValue.at( i ).eSpectrumState;
            }
        }

        m_usMaxValue = static_cast<quint16>(dMaxValue);
        m_eSpectrumState = eState;
        m_pChart->setMaxSpectrum( dMaxValue,( PrpsGlobal::SpectrumState )eState );
    }
    else
    {
        //illegal
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
    }
}

/*************************************************
功能： 槽，响应诊断结果
输入参数：
        qspDiagResultInfo -- 诊断结果
*************************************************************/
void HFCTPrpsPDAView::onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo)
{
    if(qspDiagResultInfo.data())
    {
        DiagConfig::DiagDisplayInfo stDiagInfo;
        stDiagInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(qspDiagResultInfo.data()->stDiagRet.defectLevel);
        stDiagInfo.qstrPDDesInfo = qspDiagResultInfo.data()->qstrPDDescription;
        stDiagInfo.qstrPDSignalInfos = qspDiagResultInfo.data()->qstrPDSignalTypeInfos;

        if(m_pChart)
        {
            m_pChart->setDiagRet(stDiagInfo);
        }
    }

    return;
}

/*************************************************
功能： 槽，响应信号状态改变
输入参数：
        eSignalState -- 信号状态
*************************************************************/
void HFCTPrpsPDAView::onSignalChanged( Module::SignalState eSignalState )
{
    if(eSignalState == Module::SIGNAL_STATE_EXIST)
    {
        m_pChart->setConnected(true);
    }
    else
    {
        m_pChart->setConnected(false);
    }
}

/*************************************************
功能： 槽，响应同步状态改变
输入参数：
        eSyncState -- 同步状态
*************************************************************/
void HFCTPrpsPDAView::onSyncStateChanged( Module::SyncState eSyncState )
{
    if( m_eSyncState != eSyncState )
    {
        m_eSyncState = eSyncState;
        m_pChart->setSync((PrpsGlobal::SyncSource)m_eSyncSource, (PrpsGlobal::SyncState)eSyncState);
    }
    return;
}

/*************************************************
功能： 初始化参数
*************************************************************/
void HFCTPrpsPDAView::initParameters()
{
    m_eAltasType = PhaseAbstractView::PRPS_PRPD;
    updateTitle();
    int iGroup = HFCT::GROUP_HFCT_PRPS;
    m_pConfig->beginGroup( Module::GROUP_HFCT );

    m_eGain = (HFCT::Gain)m_pConfig->value( HFCT::KEY_GAIN ).toUInt();
    m_iPhaseAlias = m_pConfig->value( HFCT::KEY_PHASEALIAS ).toUInt();
    m_eSyncSource = (Module::SyncSource)m_pConfig->value( HFCT::KEY_SYNC_SOURCE ).toUInt();
    m_iAccumulationTime = m_pConfig->value( HFCT::KEY_PRPS_ACCUMULATION_TIME,iGroup ).toUInt();
    m_fThresholdPercentage = m_pConfig->value( HFCT::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup).toFloat();
    m_eThresholdMode = static_cast<HFCT::ThresholdMode>(m_pConfig->value(HFCT::KEY_PRPS_THRESHOLD_MODE, iGroup).toUInt());
    m_pConfig->endGroup();

    m_pConfig->beginGroup( Module::GROUP_APP );
    m_ucSysFreq = m_pConfig->value( APPConfig::KEY_SYS_FREQ ).toUInt();
    m_pConfig->endGroup();

    m_usMaxValue = 0;
    m_eSpectrumState = HFCT::SPECTRUM_INSIDE_RANGE;
}

/*************************************************
功能： 初始化数据
*************************************************************/
void HFCTPrpsPDAView::initDatas( void )
{
    m_pConfig = ConfigManager::instance()->config();

    m_vMaxValue.clear();

    if(m_stTestData.bTested)
    {
        initParametersByDataFile(m_qsTestDataFilePath);
    }
    else
    {
        initParameters();
        m_eSyncState = Module::Not_Sync;
    }
    m_qui8DataCnt = -1;
    m_vConfidences.clear();
    m_mDefect.clear();
    return;
}

/*************************************************
功能： 设置按钮栏数据
*************************************************************/
void HFCTPrpsPDAView::setButtonBarDatas( void )
{
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_GAIN)))->setValue( m_eGain );
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_PHASE_SHIFT)))->setValue( m_iPhaseAlias );
    //((PopupButton*)(buttonBar()->button(BUTTON_HFCT_SYNC_SOURCE)))->setValue( m_eSyncSource - (int)Module::WIRELESS_SYNC );
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_ACCUMULATION)))->setValue( m_iAccumulationTime );
    ((PopupButton*)(buttonBar()->button(BUTTON_ALTAS_TYPE)))->setValue(m_eAltasType);
    ((PopupButton*)(buttonBar()->button(BUTTON_THRESHOLD)))->setValue(m_eThresholdMode);
}

/*************************************************
功能： 设置图谱数据
*************************************************************/
void HFCTPrpsPDAView::setChartDatas( void )
{
    m_pChart->setAltasType(m_eAltasType);

    m_pChart->setSync( (PrpsGlobal::SyncSource) m_eSyncSource, (PrpsGlobal::SyncState) m_eSyncState );

    /*系统频率*/
    m_pChart->setPowerFreq( PrpsGlobal::FREQ_50 );

    /*运行状态*/
    m_pChart->setRunningMode(isSampling());

    /*相位偏移*/
    m_pChart->setPhaseOffset( m_iPhaseAlias );

    /*设置量程*/
    m_pChart->setRangeMin( HFCT::GAIN_VALUES[m_eGain] );
    m_pChart->setRangeMax( HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE );

    /*累积模式*/
    updateAccumulativeTime();
    m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
    m_pChart->setPRPDThresholdPercentage(m_fThresholdPercentage);
}

/*************************************************
功能： 恢复默认
*************************************************************/
void HFCTPrpsPDAView::restoreDefault( void )
{
    if( MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to restore default settings?")) )
    {
        stopSample();
        setSampleBtnText(isSampling());

        int iGroup = HFCT::GROUP_HFCT_PRPS;

        m_pConfig->beginGroup( Module::GROUP_HFCT );
        QVector<Config::GroupKey> totalKeys;
        totalKeys << Config::GroupKey( HFCT::KEY_GAIN );
        totalKeys << Config::GroupKey( HFCT::KEY_SYNC_SOURCE );
        totalKeys << Config::GroupKey( HFCT::KEY_PHASEALIAS );
        totalKeys << Config::GroupKey( HFCT::KEY_PRPS_ACCUMULATION_TIME, iGroup );
        totalKeys << Config::GroupKey( HFCT::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup );

        m_pConfig->restoreDefault( totalKeys );
        m_pConfig->endGroup();

        //重新初始化数据
        initParameters();

        //设置数据
        setButtonBarDatas();
        setChartDatas();

        //设置工作参数
        setWorksets();
    }
}

/*************************************************
功能： 保存设置
*************************************************************/
bool HFCTPrpsPDAView::saveConfig( void )
{
    int iGroup = HFCT::GROUP_HFCT_PRPS;
    m_pConfig->beginGroup( Module::GROUP_HFCT );

    m_pConfig->setValue( m_eGain, HFCT::KEY_GAIN );
    m_pConfig->setValue( m_iPhaseAlias,HFCT::KEY_PHASEALIAS );
    m_pConfig->setValue( m_eSyncSource,HFCT::KEY_SYNC_SOURCE );
    m_pConfig->setValue( m_iAccumulationTime, HFCT::KEY_PRPS_ACCUMULATION_TIME, iGroup );
    m_pConfig->setValue( m_fThresholdPercentage, HFCT::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup );
    m_pConfig->setValue( m_eThresholdMode, HFCT::KEY_PRPS_THRESHOLD_MODE, iGroup );
    m_pConfig->endGroup();

    return true;
}
/*************************************************
功能： 设置工作参数
*************************************************************/
void HFCTPrpsPDAView::setWorksets( void )
{
    setWorkMode( HFCT::MODE_PRPS );
    setGain( m_eGain );
    setSyncSource( m_eSyncSource );
}

void HFCTPrpsPDAView::setPRPSMapHead(const QString &stationName, const QString& deviceName, HFCTPRPSPRPDDataInfo &stSavedData)
{
    Q_UNUSED( stationName )
    Q_UNUSED( deviceName )
    PDATask *pTask = PDAService::instance()->currentTask();
    if (NULL == pTask)
    {
        return;
    }

    TaskInfo stTaskInfo = pTask->taskInfo();
    stSavedData.stPRPSHeadInfo.strSubstationName = m_stTestPointInfo.strStationName;
    stSavedData.stPRPSHeadInfo.strSubstationNumber = m_stTestPointInfo.strStationID;
    stSavedData.stPRPSHeadInfo.eWeather = static_cast<DataFileNS::Weather>(stTaskInfo.stWeatherInfo.getWeatherDataSave());
    stSavedData.stPRPSHeadInfo.fTemperature = stTaskInfo.stWeatherInfo.dTemperature;
    stSavedData.stPRPSHeadInfo.ucHumidity = stTaskInfo.stWeatherInfo.dHumidity;
    stSavedData.stPRPSHeadInfo.strDeviceName = m_stTestPointInfo.strDeviceName;
    stSavedData.stPRPSHeadInfo.strDeviceNumber = m_stTestPointInfo.strDeviceID;
    stSavedData.stPRPSHeadInfo.strTestPointName = m_stTestPointInfo.strTestPointName;
    stSavedData.stPRPSHeadInfo.strTestPointNumber = m_stTestPointInfo.strTestPointID;

    stSavedData.stPRPSHeadInfo.eCode = DataFileNS::SPECTRUM_CODE_HFCT_PRPS;//图谱类型
    stSavedData.stPRPSHeadInfo.generationDateTime = QDateTime::currentDateTime();//图谱生成时间
    stSavedData.stPRPSHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;//图谱性质
    stSavedData.stPRPSHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_DEFAULT;//测点的测试部位
    stSavedData.stPRPSHeadInfo.ucTestChannelSign = 0;//仪器的检测通道标识，例如：1
    stSavedData.stPRPSHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_DOUBLE;//存储数据类型
    stSavedData.stPRPSHeadInfo.ePDDefectLevel = static_cast<DataFileNS::PDDefectLevel>(m_pChart->getPDDiagnosisRet());
    stSavedData.stPRPSHeadInfo.qstrPDSignalTypeInfos = m_pChart->getPDSignalInfos();
}

void HFCTPrpsPDAView::setPRPDMapHead(const QString &stationName, const QString& deviceName, HFCTPRPSPRPDDataInfo &stSavedData)
{
    Q_UNUSED( stationName )
    Q_UNUSED( deviceName )
    PDATask *pTask = PDAService::instance()->currentTask();
    if (NULL == pTask)
    {
        return;
    }

    TaskInfo stTaskInfo = pTask->taskInfo();
    stSavedData.stPRPDHeadInfo.strSubstationName = m_stTestPointInfo.strStationName;
    stSavedData.stPRPDHeadInfo.strSubstationNumber = m_stTestPointInfo.strStationID;
    stSavedData.stPRPDHeadInfo.eWeather = static_cast<DataFileNS::Weather>(stTaskInfo.stWeatherInfo.getWeatherDataSave());
    stSavedData.stPRPDHeadInfo.fTemperature = stTaskInfo.stWeatherInfo.dTemperature;
    stSavedData.stPRPDHeadInfo.ucHumidity = stTaskInfo.stWeatherInfo.dHumidity;
    stSavedData.stPRPDHeadInfo.strDeviceName = m_stTestPointInfo.strDeviceName;
    stSavedData.stPRPDHeadInfo.strDeviceNumber = m_stTestPointInfo.strDeviceID;
    stSavedData.stPRPDHeadInfo.strTestPointName = m_stTestPointInfo.strTestPointName;
    stSavedData.stPRPDHeadInfo.strTestPointNumber = m_stTestPointInfo.strTestPointID;

    stSavedData.stPRPDHeadInfo.eCode = DataFileNS::SPECTRUM_CODE_HFCT_PRPD;//图谱类型
    stSavedData.stPRPDHeadInfo.generationDateTime = QDateTime::currentDateTime();//图谱生成时间
    stSavedData.stPRPDHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;//图谱性质
    stSavedData.stPRPDHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_DEFAULT;//测点的测试部位
    stSavedData.stPRPDHeadInfo.ucTestChannelSign = 0;//仪器的检测通道标识，例如：1
    stSavedData.stPRPDHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_DOUBLE;//存储数据类型
    stSavedData.stPRPDHeadInfo.ePDDefectLevel = static_cast<DataFileNS::PDDefectLevel>(m_pChart->getPDDiagnosisRet());
    stSavedData.stPRPDHeadInfo.qstrPDSignalTypeInfos = m_pChart->getPDSignalInfos();
}

void HFCTPrpsPDAView::setPRPSMapInfo(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.stPRPSInfo.eAmpUnit = DataFileNS::AMP_UNIT_DB;
    stSavedData.stPRPSInfo.fAmpLowerLimit = HFCT::GAIN_VALUES[m_eGain];
    stSavedData.stPRPSInfo.fAmpUpperLimit = HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE;

    stSavedData.stPRPSInfo.eBandWidth = DataFileNS::BAND_DEFAULT;


    /*UHF::bandwidth2FreqRange(stSavedData.stPRPSInfo.fFrequencyMin, stSavedData.stPRPSInfo.fFrequencyMax, eBandWidth);*///temply

    stSavedData.stPRPSInfo.iSingleCycleInterval = SystemSetService::instance()->getPRPSSampleInterval();
    stSavedData.stPRPSInfo.iPhaseIntervalCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stSavedData.stPRPSInfo.iQuantificationAmp = 0;

    QVector< double > vecPRPSData = m_pChart->prpsData();
    stSavedData.stPRPSInfo.iPowerFreCycleCount = vecPRPSData.size() / PRPSMapNS::PHASE_INTERVAL_CNT;

    if(stSavedData.stPRPSInfo.iPowerFreCycleCount < 50)
    {
        stSavedData.stPRPSInfo.iPowerFreCycleCount = 50;
    }
    stSavedData.stPRPSInfo.uiRecordPRPSCycleCount = 0;
    memset(stSavedData.stPRPSInfo.ucaDischargeTypeProb, 0, sizeof(stSavedData.stPRPSInfo.ucaDischargeTypeProb));

    stSavedData.stPRPSInfo.eDataSign = (PRPSMapNS::MapDataSign) m_eSpectrumState;

    stSavedData.stPRPSInfo.eGainType = DataFileNS::GAIN_TYPE_DB;

    stSavedData.stPRPSInfo.sGain = -HFCT::GAIN_VALUES[m_eGain];

    stSavedData.stPRPSInfo.eSyncSource = (DataFileNS::SyncSource)(m_eSyncSource + 1);
    stSavedData.stPRPSInfo.ucSyncState = m_eSyncState;
    stSavedData.stPRPSInfo.fSyncFreq = -1;
    double dMax  = 0;
    if( vecPRPSData.size() > 0)
    {
        dMax = vecPRPSData.at(0);
        for(int i = 0; i < vecPRPSData.size(); i ++)
        {
            if(vecPRPSData.at(i) > dMax)
            {
                dMax = vecPRPSData.at(i);
            }
        }
    }
    stSavedData.stPRPSInfo.fMax = dMax;
    dbg_info("stSavedData.stPRPSInfo.fMax is %f\n", stSavedData.stPRPSInfo.fMax);
    stSavedData.stPRPSInfo.fAnalysisThreshold = m_pChart->prpsThreshold();

    return;
}

void HFCTPrpsPDAView::setPRPDMapInfo(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.stPRPDInfo.eAmpUnit = DataFileNS::AMP_UNIT_DB;
    stSavedData.stPRPDInfo.fAmpLowerLimit = HFCT::GAIN_VALUES[m_eGain];
    stSavedData.stPRPDInfo.fAmpUpperLimit = HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE;

    stSavedData.stPRPDInfo.eBandWidth = DataFileNS::BAND_DEFAULT;

    //    UHF::bandwidth2FreqRange(stSavedData.stPRPDInfo.fFrequencyMin, stSavedData.stPRPDInfo.fFrequencyMax, eBandWidth);//temply

    stSavedData.stPRPDInfo.iSingleCycleInterval = SystemSetService::instance()->getPRPSSampleInterval();
    stSavedData.stPRPDInfo.iPhaseIntervalCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stSavedData.stPRPDInfo.iQuantificationAmp = PRPSMapNS::QUANTIFICATION_AMP;

    stSavedData.stPRPDInfo.iPowerFreCycleCount = m_pChart->prpdPeriodCount();

    memset(stSavedData.stPRPDInfo.ucaDischargeTypeProb, 0, sizeof(stSavedData.stPRPDInfo.ucaDischargeTypeProb));

    stSavedData.stPRPDInfo.eDataSign = (PRPSMapNS::MapDataSign) m_eSpectrumState;

    stSavedData.stPRPDInfo.eGainType = DataFileNS::GAIN_TYPE_DB;

    stSavedData.stPRPDInfo.sGain = -HFCT::GAIN_VALUES[m_eGain];

    stSavedData.stPRPDInfo.eSyncSource = (DataFileNS::SyncSource)(m_eSyncSource + 1);

    stSavedData.stPRPDInfo.ucSyncState = m_eSyncState;

    stSavedData.stPRPDInfo.fSyncFreq = -1;

    stSavedData.stPRPDInfo.isAccumulated = m_iAccumulationTime;

    stSavedData.stPRPDInfo.ucPeriodCount = m_pChart->prpdPeriodCount();
    stSavedData.stPRPDInfo.fAnalysisThreshold = m_pChart->prpsThreshold();

    return;
}

void HFCTPrpsPDAView::setPRPDData(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.vecPRRepeatyData.clear();
    int iDataCnt =  stSavedData.stPRPDInfo.iPhaseIntervalCount * stSavedData.stPRPDInfo.iQuantificationAmp;

    UINT16 *pusPulsePRTotalCnt = new UINT16[iDataCnt];
    memset(pusPulsePRTotalCnt, 0, iDataCnt *sizeof(UINT16));
    UINT8 *pucDataColor = new UINT8[3*iDataCnt];
    UINT16 *pusDataDischargeCnt = new UINT16[iDataCnt];

    QVector< qint16 > vecPRPDData = m_pChart->prpdData();

    int iAmpAreaCnt = vecPRPDData.size() / stSavedData.stPRPDInfo.iPhaseIntervalCount;
    dbg_info("iAmpAreaCnt is %d\n", iAmpAreaCnt);
    int iPhaseShitStep = m_iPhaseAlias / (360 / stSavedData.stPRPDInfo.iPhaseIntervalCount);
    dbg_info(" iPhaseShitStep is %d\n", iPhaseShitStep);

    for(int  j = 0; j < stSavedData.stPRPDInfo.iPhaseIntervalCount; j ++)
    {
        for(int  i = 0; i< iAmpAreaCnt; i ++)
        {
            int iNewPhaseIndex = (j + iPhaseShitStep) % stSavedData.stPRPDInfo.iPhaseIntervalCount;

            qint16 sPRPD = vecPRPDData.at(i*stSavedData.stPRPDInfo.iPhaseIntervalCount + j);
            if(sPRPD <= 0)
            {
                sPRPD = 0;
            }
            pusPulsePRTotalCnt[iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + i] = sPRPD;

            QRgb color = m_pChart->prpdDataColor(i,j);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i] = qRed(color);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i+1] = qGreen(color);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i+2] = qBlue(color);
            //discharge cnt of each prpd data
            pusDataDischargeCnt[iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + i] = m_pChart->prpdDataPulseCnt(i,j);
        }
    }
    double *pdPRRepeatyProb;
    pdPRRepeatyProb = new double[iDataCnt];
    memset(pdPRRepeatyProb, 0, iDataCnt *sizeof(double));


    for(int  i = 0; i < iDataCnt; i ++)
    {
        pdPRRepeatyProb[i] = (double)pusPulsePRTotalCnt[i] / (double)stSavedData.stPRPDInfo.iPowerFreCycleCount;
        pdPRRepeatyProb[i] = pdPRRepeatyProb[i] / (20.0 / 1000.0);
        stSavedData.vecPRRepeatyData.append(pdPRRepeatyProb[i]);

        //rgb of each prpd data
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i]);
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i+1]);
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i+2]);

        //discharge cnt of each prpd data
        stSavedData.vecPRPDDataDischargeCnt.append(pusDataDischargeCnt[i]);

    }
    delete [] pdPRRepeatyProb;
    delete [] pusPulsePRTotalCnt;
    delete [] pucDataColor;
    delete [] pusDataDischargeCnt;
}

void HFCTPrpsPDAView::setPRPSData(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.vecPRPSData.clear();
    QVector< double > vecPRPSData = m_pChart->prpsData();
    int iDataPointNum = 0;
    //step5 set map data
    iDataPointNum = vecPRPSData.size();//ex 50*60
    dbg_info("iDataPointNum is %d\n", iDataPointNum);

    int iPhaseShitStep = m_iPhaseAlias / (360 / stSavedData.stPRPSInfo.iPhaseIntervalCount);
    dbg_info("iPhaseShitStep is %d\n", iPhaseShitStep);

    int iRealPeriod = iDataPointNum / stSavedData.stPRPSInfo.iPhaseIntervalCount;
    dbg_info("iRealPeriod is %d\n", iRealPeriod);
    if(iRealPeriod > 50)//todo hard code
    {
        iRealPeriod = 50;
    }

    QVector< double > vecPRPSPhaseShitedData;
    vecPRPSPhaseShitedData.clear();
    double *pdPRPSShiftedData = new double[stSavedData.stPRPSInfo.iPhaseIntervalCount * stSavedData.stPRPSInfo.iPowerFreCycleCount];
    UINT8 *pucPRPSDataColor = new UINT8[3*stSavedData.stPRPSInfo.iPhaseIntervalCount * stSavedData.stPRPSInfo.iPowerFreCycleCount];
    memset(pdPRPSShiftedData, 0, sizeof(double) * stSavedData.stPRPSInfo.iPhaseIntervalCount * stSavedData.stPRPSInfo.iPowerFreCycleCount);
    memset(pucPRPSDataColor, 0, sizeof(UINT8) * 3* stSavedData.stPRPSInfo.iPhaseIntervalCount * stSavedData.stPRPSInfo.iPowerFreCycleCount);


    int iPeriodIndex = 0;
    for( int i = 0; i < iRealPeriod; ++i )
    {
        for( int j = 0; j < stSavedData.stPRPSInfo.iPhaseIntervalCount; ++j )
        {
            QColor color = m_pChart->prpsDataColor(i,j);

            if(iRealPeriod < stSavedData.stPRPSInfo.iPowerFreCycleCount)
            {
                iPeriodIndex =  stSavedData.stPRPSInfo.iPowerFreCycleCount + i - iRealPeriod;
            }
            else
            {
                iPeriodIndex = i;
            }


            //dbg_info("%d, %d:red is %d, green is %d, blue is %d\n", i,j,color.red(),color.green(),color.blue());
            int iNewPhaseIndex = (j + iPhaseShitStep) % stSavedData.stPRPSInfo.iPhaseIntervalCount;
            pdPRPSShiftedData[iPeriodIndex*stSavedData.stPRPSInfo.iPhaseIntervalCount + iNewPhaseIndex] = vecPRPSData.at(i*stSavedData.stPRPSInfo.iPhaseIntervalCount + j);

            //rgb of each prps data
            pucPRPSDataColor[3*iPeriodIndex*stSavedData.stPRPSInfo.iPhaseIntervalCount + 3*iNewPhaseIndex] = color.red();
            pucPRPSDataColor[3*iPeriodIndex*stSavedData.stPRPSInfo.iPhaseIntervalCount + 3*iNewPhaseIndex+1] = color.green();
            pucPRPSDataColor[3*iPeriodIndex*stSavedData.stPRPSInfo.iPhaseIntervalCount + 3*iNewPhaseIndex+2] = color.blue();
        }
    }

    for( long int i = 0; i < stSavedData.stPRPSInfo.iPhaseIntervalCount * stSavedData.stPRPSInfo.iPowerFreCycleCount; ++i )
    {
        stSavedData.vecPRPSData.append(pdPRPSShiftedData[i]);

        //rgb of each prps data
        stSavedData.vecPRPSDataColor.append(pucPRPSDataColor[3*i]);
        stSavedData.vecPRPSDataColor.append(pucPRPSDataColor[3*i+1]);
        stSavedData.vecPRPSDataColor.append(pucPRPSDataColor[3*i+2]);
    }

    delete [] pdPRPSShiftedData;
    delete [] pucPRPSDataColor;
}

/************************************************
 * 函数名   : composeSavedData
 * 输入参数 : stationName---站名
 *          deviceName---设备名
 * 输出参数 : stSavedData---需要保存的数据
 * 返回值   : NULL
 * 功能     : 组织需要保存的数据
 ************************************************/
void HFCTPrpsPDAView::composeSavedData(const QString &stationName, const QString& deviceName, HFCTPRPSPRPDDataInfo &stSavedData)
{
    diagDataInfo(true);

    setPRPSMapHead(stationName, deviceName, stSavedData);

    setPRPDMapHead(stationName, deviceName, stSavedData);

    setPRPSMapInfo(stSavedData);

    setPRPDMapInfo(stSavedData);

    setPRPSData(stSavedData);

    setPRPDData(stSavedData);
}

/*************************************************
功能： 保存数据
输入参数：
        stationName -- 变电站名
        deviceName -- 设备名
返回：
    保存后的文件名
*************************************************************/
bool HFCTPrpsPDAView::savePDAData(const QString &stationName, const QString& deviceName )
{
    HFCTPRPSPRPDDataInfo stHFCTPRPSPRPDDataInfo; // 存放图谱数据用于保存的结构体
    composeSavedData(stationName, deviceName, stHFCTPRPSPRPDDataInfo);

    PDAService *pPDAService = PDAService::instance();
    APP_CHECK_RETURN_VAL(pPDAService, false);

    PDATask *pTask = pPDAService->currentTask();
    APP_CHECK_RETURN_VAL(pTask, false);
    return pTask->saveHFCTPRPSTestData(stHFCTPRPSPRPDDataInfo);
}

/*************************************************
输入参数: bIsSampling---是否正在采样的标志
功能： 设置采样按钮文本
*************************************************************/
void HFCTPrpsPDAView::setSampleBtnText( bool bIsSampling )
{
    if( bIsSampling )
    {
        m_pSampleBtn->setTitle( HFCT_VIEW_CONFIG_TRANSLATE( HFCT::TEXT_STOP ) );
    }
    else
    {
        m_pSampleBtn->setTitle( HFCT_VIEW_CONFIG_TRANSLATE( HFCT::TEXT_RUN ) );
    }
}
/************************************************
 * 函数名   : onAddTestData
 * 输入参数 :  struct_AddingTestData&
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :槽函数，将新增测试项界面的信息写入pdaTask中
 ************************************************/
void HFCTPrpsPDAView::onAddTestData( struct_AddingTestData &stAddingTestData)
{
    for(int i = 0, iSize = stAddingTestData.evType.size(); i < iSize; ++i)
    {
        ItemTestData stTestData;
        stTestData.bIsBgn = m_stTestData.bIsBgn;
        stTestData.eDataType = stAddingTestData.evType.at(i);
        stTestData.eDataUnit = m_stTestData.eDataUnit;
        stTestData.eBandWidth = m_stTestData.eBandWidth;

        PDAService::instance()->currentTask()->addTestData(stTestData);
    }
    return;
}

/*************************************************
 * 功能：诊断数据
 * 输入参数：
 *      bSave：是否为保存操作的逻辑，缺省为false
 * ***********************************************/
void HFCTPrpsPDAView::diagDataInfo(bool bSave)
{
    if(SystemSet::RT_DIAG_ON != SystemSetService::instance()->getRealtimeDiagnosisSwitch())
    {
        return;
    }

    if(bSave)
    {
        if(m_pChart->isLocalDiagnosisEnable())
        {
            PRPSDiagInfo stDiagInfo;
            stDiagInfo.qvtDataIn.clear();
            stDiagInfo.dThresholdDbVal = m_pChart->prpsThreshold();
            stDiagInfo.dSpecMaxVal = m_pChart->rangeMax();
            stDiagInfo.dSpecMinVal = m_pChart->rangeMin();

            stDiagInfo.qvtDataIn = m_pChart->prpsDiagData(stDiagInfo.iPeriodNum, stDiagInfo.iPhaseNum);

            DiagResultInfo stRetInfo;
            DiagnosisManager::instance()->diagPRPSDataDirectly(stDiagInfo, stRetInfo);

            DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
            stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stRetInfo.stDiagRet.defectLevel);
            stDiagDisplayInfo.qstrPDDesInfo = stRetInfo.qstrPDDescription;
            stDiagDisplayInfo.qstrPDSignalInfos = stRetInfo.qstrPDSignalTypeInfos;

            if(m_pChart)
            {
                m_pChart->setDiagRet(stDiagDisplayInfo);
            }
        }
    }
    else
    {
        if(m_pChart->isLocalDiagnosisEnable())
        {
            if(0 == m_qui8RealDiagInterval % View::REAL_DIAGNOSIS_INTERVAL)
            {
                PRPSDiagInfo stDiagInfo;
                stDiagInfo.qvtDataIn.clear();
                stDiagInfo.dThresholdDbVal = m_pChart->prpsThreshold();;
                stDiagInfo.dSpecMaxVal = m_pChart->rangeMax();
                stDiagInfo.dSpecMinVal = m_pChart->rangeMin();

                stDiagInfo.qvtDataIn = m_pChart->prpsDiagData(stDiagInfo.iPeriodNum, stDiagInfo.iPhaseNum);
                DiagnosisManager::instance()->diagPRPSData(stDiagInfo);
            }
            ++m_qui8RealDiagInterval;
        }
    }

    return;
}
