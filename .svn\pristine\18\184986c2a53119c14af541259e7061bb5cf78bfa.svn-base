/*
* Copyright (c) 2017.11，南京华乘电气科技有限公司
* All rights reserved.
*
* infrareddatasave.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年11月10日
* 摘要：红外数据回放

* 当前版本：1.0
*/

#ifndef GUIDEINFRAREDDATASAVE_H
#define GUIDEINFRAREDDATASAVE_H

#include "dataSave/DataSave.h"
#include "module_global.h"

class DataFile;
class DataMap;
class InfraredDataMap;

class MODULESHARED_EXPORT GuideInfraredDataSave : public DataSave
{
public:
    /*************************************************
    函数名： GuideInfraredDataSave()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    GuideInfraredDataSave();

    /*************************************************
    函数名： saveData(void *pData)
    输入参数： pDatas：数据
    输出参数： NULL
    返回值： 数据文件名
    功能： 保存数据到指定格式数据文件
    *************************************************************/
    QString saveData(void *pData);

    /*************************************************
    功能： 保存数据到指定格式数据文件
    *************************************************************/
    QString saveData(void *pData, const QString &qstrSavePath);

    /************************************************
     * 函数名   : getDataByPDA
     * 输入参数 : strFileName: 文件名; pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 获取结果
     * 功能     : 获取指定数据文件中的数据
     ************************************************/
    INT32 getDataFromFile(const QString& strFileName, void *pData);

    /********************************
     * 功能：设置图谱的头信息
     * 输入参数：
     *      stHeadInfo：头信息
     * *******************************/
    void setDataMapInfo(const DataMapHead& stHeadInfo);

    /********************************
     * 功能：获取图谱的头信息
     * *******************************/
    DataMapHead getDataMapInfo();

protected:
    /*************************************************
    函数名： getDataTypeFolder()
    输入参数： NULL
    输出参数： NULL
    返回值： 文件夹名
    功能： 获取图谱类型文件存储最上层文件夹名
    *************************************************************/
    QString getDataTypeFolder();

    /*************************************************
    函数名： getFileNameSuffix()
    输入参数： NULL
    输出参数： NULL
    返回值： 文件后缀名
    功能： 获取数据文件后缀名
    *************************************************************/
    QString getFileNameSuffix();

    /*************************************************
    函数名： getStringFromData(void *pDatas, UINT32 uiCounts)
    输入参数： pDatas：数据
              uiCounts：数据个数
    输出参数： NULL
    返回值： 转换后的字符串
    功能： 将数据转成base64的字符串
    *************************************************************/
    QString getStringFromData(void *pDatas, UINT32 uiCounts);

    /*************************************************
    函数名： saveExtInformation(XMLDocument &doc)
    输入参数： doc：XML文件
    输出参数： NULL
    返回值： NULL
    功能： 在XML文件中存储可扩展信息
    *************************************************************/
    void saveExtInformation(XMLDocument &doc);

    /*************************************************
    函数名： saveRawData(XMLDocument &doc)
    输入参数： doc：XML文件
    输出参数： NULL
    返回值： NULL
    功能： 在XML文件中存储数据部分
    *************************************************************/
    void saveRawData(XMLDocument &doc);

    /*************************************************
    函数名： parseData(const QByteArray &baData, void *pData, const QString &strFileName = "")
    输入参数： baData：数据
              strFileName：unused
    输出参数： pData：解析到的数据
    返回值： NULL
    功能： 解析数据
    *************************************************************/
    void parseData(const QByteArray &baData, void *pData, const QString &strFileName = "");

private:
    /*************************************************
    输入参数： pMap -- 图谱指针
    输出参数： NULL
    返回值： NULL
    功能： 设置图谱头信息
    *************************************************************/
    void setMapHead(DataMap *pMap);

    /*************************************************
    输入参数： pMap -- 图谱指针
    输出参数： NULL
    返回值： NULL
    功能： 设置图谱信息
    *************************************************************/
    void setMapInfo(InfraredDataMap *pMap);

    /*************************************************
    输入参数： pMap -- 图谱指针
    输出参数： NULL
    返回值： NULL
    功能： 设置图谱数据信息
    *************************************************************/
    void setMapData(InfraredDataMap *pMap);

    GuideInfraredDataInfo *m_pInfraredDataInfo;      //红外检测数据信息
    DataMapHead m_stHeadInfo;            //图谱通用的头部信息

};

#endif // GUIDEINFRAREDDATASAVE_H
