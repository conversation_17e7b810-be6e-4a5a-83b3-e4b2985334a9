#include "aeflyviewbase.h"
#include "ae/AEView.h"
#include "ae/AEViewConfig.h"
#include "window/Window.h"
#include "ae/AEConfig.h"
#include "statemonior/StateMonitor.h"

/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
AEFlyViewBase::AEFlyViewBase(const QString &strTitle, QWidget *parent) :
    SampleChartView( strTitle,parent)
{
    //初始化使用的数据
    initDatas();

    setFixedSize( Window::WIDTH, Window::HEIGHT );

    //新建图谱
    ChartWidget *pChart = createChart(parent);
    setChart( pChart );

    //业务
    AEFlyService* pService = AEFlyService::instance();
    setService(pService);
    connect( pService, SIGNAL(sigData(QVector<AE::FlyData>,MultiServiceNS::USERID)),
             this, SLOT(onDataRead(QVector<AE::FlyData>,MultiServiceNS::USERID)) );//数据读取
    connect( pService, SIGNAL(sigChannelChanged(AE::ChannelType)), this, SLOT(onChannelChanged(AE::ChannelType)) );//通道变化
    //connect( m_pService, SIGNAL(sigReadAEPulseDataFailed(MultiServiceNS::USERID)), this, SLOT(onDataReadFailed()) );//数据读取失败
    pService->start();//启动服务
}

/*************************************************
功能： 析构
*************************************************************/
AEFlyViewBase::~AEFlyViewBase( )
{
    if(AEFlyService* pService = getAEFlyService())
    {
        pService->stop();
    }
}

/*************************************************
功能： 槽，响应读取数据失败
*************************************************************/
void AEFlyViewBase::onDataReadFailed()
{
    StateMonitor::instance()->checkStmInApp();
}

/*************************************************
功能： 响应S键事件
输入参数：
        id -- 按钮ID
*************************************************************/
void AEFlyViewBase::onSKeyPressed()
{

}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void AEFlyViewBase::keyPressEvent(QKeyEvent *event)
{
    Q_UNUSED(event)
}

ChartWidget *AEFlyViewBase::createChart(QWidget *parent)
{
    Q_UNUSED(parent)
    ChartWidget *pChart = new ChartWidget;
    return pChart;
}

/*************************************************
函数名：service
输入参数: NULL
输出参数：NULL
返回值：service对象
功能：返回service对象
*************************************************************/
AEFlyService* AEFlyViewBase::getAEFlyService()
{
    if (MultiUserService* pService = service())
    {
        return dynamic_cast<AEFlyService*>(pService);
    }
    else
    {
        return NULL;
    }
}

/*************************************************
功能： 初始化数据
*************************************************************/
void AEFlyViewBase::initDatas()
{

}

