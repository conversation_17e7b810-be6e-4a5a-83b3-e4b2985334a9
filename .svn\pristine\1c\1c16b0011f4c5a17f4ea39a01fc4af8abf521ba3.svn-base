/*
* Copyright (c) 2016.3，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：SpectrumChart.cpp
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月1日
* 摘要：该文件主要定义周期图谱的相关信息

* 当前版本：1.0
*/

#ifndef SPECTRUMCHART_H
#define SPECTRUMCHART_H

#include <QWidget>
#include <QPainter>
#include <QPoint>
#include <QLine>
#include <QHash>
#include <QList>
#include <QVector>
#include "DriverDataDefine.h"
#include "datadefine.h"

class SpectrumChart : public QWidget
{
    Q_OBJECT

public:

    typedef struct _AbstractData  // 图谱保存的数据结构
    {
        float fXscale;  // 数据点占X轴的比例
        float fYscale;  // 数据点占Y轴的比例
        _AbstractData()
        {
            fXscale = 0;
            fYscale = 0;
        }
    }AbstractData;

    /****************************
    函数名： SpectrumChart( QWidget *parent )
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 构造函数
    *****************************/
    explicit SpectrumChart( QWidget *parent = 0 );

    /****************************
    函数名： ~SpectrumChart();
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 析构函数
    *****************************/
    ~SpectrumChart();

    /****************************
    函数名： addSample( float fXscale,float fYscale )
    输入参数:fXscale:数据占X轴的比例，fYscale:数据占Y轴的比例
    输出参数：NULL
    返回值：NULL
    功能： 添加数据
    *****************************/
    void addSample( const QHash< Qt::GlobalColor,QList< AbstractData > > &rawData  );


    /****************************
    函数名： samples( void )
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 返回存放的原始数据
    *****************************/
    QHash< Qt::GlobalColor,QList< AbstractData > > const& samples( void ) const;

    /****************************
    函数名： clear( void )
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能： 清除显示
    *****************************/
    void clear( void );

    /****************************
    函数名： setAlarmValue( UINT16 usAlarm )
    输入参数:usAlarm:报警值
    输出参数：NULL
    返回值：NULL
    功能： 设置红色报警值
    *****************************/
    void setAlarmValue( UINT16 usAlarm );

    /****************************
    函数名： setWarnValue( UINT16 usWarn )
    输入参数:usWarn:警告值
    输出参数：NULL
    返回值：NULL
    功能： 设置黄色警告值
    *****************************/
    void setWarnValue( UINT16 usWarn );

    /****************************
    函数名： setYAxisScale( UINT16 usY )
    输入参数:usY:量程
    输出参数：NULL
    返回值：NULL
    功能： 设置量程
    *****************************/
    void setYAxisScale( UINT16 usY );

    /****************************
    函数名： showCoordinateLines( bool bIsShow )
    输入参数:bIsShow:显示报警触发线
    输出参数：NULL
    返回值：NULL
    功能： 设置是否显示报警触发线
    *****************************/
    void showCoordinate( bool bIsShow );

protected:
    /****************************
    函数名： paintEvent(QPaintEvent *pEvent);
    输入参数:pEvent：绘图事件
    输出参数：NULL
    返回值：NULL
    功能： 重载绘图函数
    *****************************/
    void paintEvent( QPaintEvent* );

    /****************************
    函数名： resizeEvent(QResizeEvent *);
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： resize事件处理函数
    *****************************/
    void resizeEvent( QResizeEvent* );

private:
    /****************************
    函数名： dataInit( void )
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 数据初始化
    *****************************/
    void dataInit( void );

    /****************************
    函数名： drawRawData( void )
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 用于绘制AE数据
    *****************************/
    void drawRawData( void );

    /****************************
    函数名： drawCoordinate(void)
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 用于绘制AE坐标系
    *****************************/

    void drawCoordinate(void);

    /****************************
    函数名： resetParamas( void )
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能： 重置界面相关参数
    *****************************/
    void resetParams( void );

    /****************************
    函数名： makeCoordinateLines( void )
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能： 生成坐标系线段
    *****************************/
    void makeCoordinateLines( void );


private:
    QHash< Qt::GlobalColor,QList< AbstractData > > m_hRawData;          // 原始数据

    QHash< Qt::GlobalColor,QList< QLine > >     m_hCoordinateLines;  // 坐标系线段集合
    QHash< Qt::GlobalColor,QList< QRect > >     m_hCoordinateRects;  // 坐标系矩形报警颜色框集合

    QPainter    *m_pPainter;                    // 绘图设备
    bool    m_bIsShowCoordinate;                // 设置是否显示左侧报警矩形框体和对应的各报警线

    UINT16  m_usAlarm;                          // 红色报警值
    UINT16  m_usWarn;                           // 黄色警告值
    UINT16  m_usYRange;                         // 纵坐标量程

    UINT16 m_usOriginX;                         // 坐标系原点X坐标
    UINT16 m_usOriginY;                         // 坐标系原点Y坐标
    UINT16 m_usChartWidth;                      // 图谱宽度
    UINT16 m_usChartHeight;                     // 图谱高度
};

#endif // SPECTRUMCHART_H
