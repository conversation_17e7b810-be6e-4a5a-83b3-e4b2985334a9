﻿#include <QDebug>
#include "phasedatamodel.h"
#include "phasedef.h"

enum
{
    DefaultDispalyPeriodCount = 50,
    DefaultDataCountPerPeriod = 60,
    DefaultMaxRecordSize = 200
};

/************************************************
 * 函数名: PhaseDataModel
 * 输入参数:
 *          parent: 父对象指针
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 构造函数
 ************************************************/
PhaseDataModel::PhaseDataModel(int iDataCountPerPeriod, QObject *parent)
    : QObject(parent),
      m_iDataCountPerPeriod(iDataCountPerPeriod),
      m_iDisplayPeriodCount(DefaultDispalyPeriodCount),
      m_bRecording(false),
      m_currentData(iDataCountPerPeriod),
      m_dataBuffer(iDataCountPerPeriod),
      m_lastSkippedData(iDataCountPerPeriod),
      m_recordData(iDataCountPerPeriod)
{
}

/************************************************
 * 函数名: clear
 * 输入参数: void
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 清除所有数据(包括录制的数据,如果只想清除录制数据可调用clearRecordData)
 ************************************************/
void PhaseDataModel::clear()
{
    m_currentData.clear();
    m_dataBuffer.clear();
    m_lastSkippedData.clear();
    m_recordData.clear();

    emit sigDataCleared();
}

/************************************************
 * 函数名: addData
 * 输入参数:
 *        phaseData: 周期数据
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 向model中添加新数据
 ************************************************/
void PhaseDataModel::addData(const PhaseData &phaseData)
{
    if(phaseData.isEmpty())
    {
        return;
    }

    //qDebug()<<"PhaseDataModel::addData, count:"<<count;
    //qDebug()<<"PhaseDataModel::addData, phaseData.dataCountPerPeriod():"<<phaseData.dataCountPerPeriod();
    if(phaseData.dataCountPerPeriod() != m_iDataCountPerPeriod)
    {
        setDataCountPerPeriod(phaseData.dataCountPerPeriod());
    }

    m_dataBuffer.append(phaseData);
}

/************************************************
 * 函数名: setDisplayPeriodCount
 * 输入参数:
 *        iCount: 每次显示的最大周期数
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 设置每次显示的最大的周期数量
 ************************************************/
void PhaseDataModel::setDisplayPeriodCount(int iPeriodCount)
{
    PHASE_ASSERT(iPeriodCount > 0);
    if(m_iDisplayPeriodCount)
    {
        clear();
        m_iDisplayPeriodCount = iPeriodCount;
        emit sigDisplayPeriodCountChanged(iPeriodCount);
    }
}

/************************************************
 * 函数名: setDataCountPerPeriod
 * 输入参数:
 *        iDataCountPerPeriod: 每周期的点数
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 设置每周期的点数
 ************************************************/
void PhaseDataModel::setDataCountPerPeriod(int iDataCountPerPeriod)
{
    if (0 >= iDataCountPerPeriod)
    {
        iDataCountPerPeriod = DefaultDataCountPerPeriod;
    }

    if (iDataCountPerPeriod != m_iDataCountPerPeriod)
    {
        clear();
        m_iDataCountPerPeriod = iDataCountPerPeriod;
        m_dataBuffer.setDataCountPeriod(m_iDataCountPerPeriod);
        m_currentData.setDataCountPeriod(m_iDataCountPerPeriod);
        m_lastSkippedData.setDataCountPeriod(m_iDataCountPerPeriod);
        m_recordData.setDataCountPeriod(m_iDataCountPerPeriod);
        emit sigDataCountPerPeriodChanged(m_iDataCountPerPeriod);
    }
}

/************************************************
 * 函数名: setMaxRecordPeriodCount
 * 输入参数:
 *        iMaxPeriod: 最大周期数
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 设置最大录制周期数
 ************************************************/
void PhaseDataModel::setMaxRecordPeriodCount(int iMaxPeriod)
{
    PHASE_ASSERT(iMaxPeriod);
    m_iMaxRecordPeriodCount = iMaxPeriod;
}

/************************************************
 * 函数名: advance
 * 输入参数:
 *        iPeriodCount: 期望推进的周期数
 * 输出参数: NULL
 * 返回值: 时基推进的周期数
 * 功能: 实现数据更新,即推进给定周期数,如果数据不足,则返回实际推进的周期数
 ************************************************/
int PhaseDataModel::advance(int iPeriodCount)
{
    if(iPeriodCount <= 0 || m_dataBuffer.isEmpty())
    {
        return 0;
    }

    int iActStep = 0;

    if(iPeriodCount > m_dataBuffer.periodCount())
    {
        iActStep = m_dataBuffer.periodCount();
    }
    else
    {
        iActStep = iPeriodCount;
    }
#if 1
    if(iActStep > displayPeriodCount())
    {
        m_lastSkippedData = m_dataBuffer.mid(0, iActStep - displayPeriodCount());
    }
    else
    {
        m_lastSkippedData.clear();
    }

    //qDebug()<<"PhaseDataModel::advance, iActStep:"<<iActStep<<"iPeriodCount:"<<iPeriodCount;
    m_currentData.append(m_dataBuffer.takeFront(iActStep));
#endif

#if 1
    if(m_currentData.periodCount() > m_iDisplayPeriodCount)
    {
        int iOverflowCount = m_currentData.periodCount() - m_iDisplayPeriodCount;
        if(m_bRecording)
        {
            addRecordData(m_currentData.takeFront(iOverflowCount));
        }
        else
        {
            m_currentData.takeFront(iOverflowCount);
        }
    }
#endif
    emit sigAdvanced(iActStep);

    return iActStep;
}

/************************************************
 * 函数名: addRecordData
 * 输入参数:
 *          data: 周期数据
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 添加录制数据
 ************************************************/
void PhaseDataModel::addRecordData(const PhaseData &data)
{
    m_recordData.append(data);
}
