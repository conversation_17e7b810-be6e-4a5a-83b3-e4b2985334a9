/*
* Copyright (c) 2017.2，南京华乘电气科技有限公司
* All rights reserved.
*
* pmserviceprivate.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年5月18日
* 摘要：外设匹配服务模块接口定义

* 当前版本：1.0
*/

#ifndef HFCTSERVICEPRIVATE_H
#define HFCTSERVICEPRIVATE_H

#include <QObject>
#include <QThread>
#include <QMutex>
#include <QTimerEvent>
#include "datadefine.h"
#include "pmservice.h"
#include "Module.h"
#include "UHFHFCTAETEVApi.h"
#include "datadefine.h"


class PMServicePrivate : public QObject
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent -- 父Object
    *************************************************/
    PMServicePrivate( QObject* parent );

    /*************************************************
    功能： 析构函数
    *************************************************/
    ~PMServicePrivate();

    /*************************************************
    功能： 启动业务
    *************************************************/
    bool start( void );

    /*************************************************
    功能： 终止业务
    *************************************************/
    bool stop( void );

    /*************************************************
    函数名： searchConditionerList(WLDeviceAddr *pConditionerAddrList, UINT16 *pusConditionerCnt, WLDeviceType eDeviceType)
    输入参数:
        eDeviceType:调理器类型
    输出参数： pConditionerAddrList:搜索到的调理器地址列表
            pusConditionerCnt:搜索到的调理器个数
    返回值： NULL
    功能： 搜索调理器
    *************************************************************/
    bool searchConditionerList(WLDeviceAddr *pConditionerAddrList, UINT16 *pusConditionerCnt, WLDeviceType eDeviceType);

    /*************************************************
    函数名： connectConditioner(WLDeviceAddr *pstAddr, WLDeviceType eDeviceType)
    输入参数:
        pstAddr:指定的调理器地址
        eDeviceType:调理器类型
    输出参数：NULL
    返回值： NULL
    功能： 连接指定的调理器
    *************************************************************/
    bool connectConditioner(WLDeviceAddr *pstAddr, WLDeviceType eDeviceType);

    bool startScan(WLDeviceType eDeviceType);

    bool getScanList(WLDeviceAddr *pConditionerAddrList, UINT16 *pusConditionerCnt, WLDeviceType eDeviceType);

private:

    /*************************************************
    函数名： isValidConditionerType(WLDeviceType eDeviceType)
    输入参数:
        eDeviceType:调理器类型
    输出参数： NULL
    返回值： true:有效的调理器类型
            false:无效的调理器类型
    功能： 判断调理器类型是否有效
    *************************************************************/
    bool isValidConditionerType(WLDeviceType eDeviceType);

public:
    QThread *m_pThread;

};

#endif // HFCTSERVICEPRIVATE_H
