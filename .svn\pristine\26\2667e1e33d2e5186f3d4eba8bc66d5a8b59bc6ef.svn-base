/*
* Copyright (c) 2016.3，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：AEFlyPlayBackView.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年12月26日
* 摘要：该文件主要实现了超声波飞行图谱数据回放界面
*/

#ifndef AEFLYPLAYBACKVIEW_H
#define AEFLYPLAYBACKVIEW_H

#include "playbackView/PlayBackBase.h"
#include <QWidget>
#include "ae/aeflyview/AEFlyChart.h"
#include "ae/AEService.h"
#include "ae/dataSave/AEFlightDataSave.h"
#include "datafile/ae/aeflydatamap.h"
#include "datafile.h"

class AEFlyPlayBackView : public PlayBackBase
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    输入参数:
        parent -- 父窗体
    *****************************/
    explicit AEFlyPlayBackView(QWidget *parent = 0);

    ~AEFlyPlayBackView();
private:
    /****************************
    功能： 回放文件
    输入参数:
        strFileName -- 文件名
    *****************************/
    void playbackFile( const QString& strFileName );

private:
    AEFlyChart *m_pChart; // 图谱部分
    AEFlightDataInfo  m_sPlayBackDataInfo; // 存放回放数据的结构体
};

#endif // AEFLYPLAYBACKVIEW_H
