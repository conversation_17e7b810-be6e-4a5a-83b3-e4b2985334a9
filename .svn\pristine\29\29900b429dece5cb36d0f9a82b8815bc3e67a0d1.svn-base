﻿/*
* Copyright (c) 2017.08，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: phasechartdraw.h
*
* 初始版本: 1.0
* 作者: 张涛
* 创建日期: 2017年11月13日
* 摘要: 该文件定义了周期图谱的绘制类的基类

* 当前版本: 1.0
*/

#ifndef PHASECHARTDRAW_H
#define PHASECHARTDRAW_H

#include <QRect>
#include <QPalette>
#include "phasedef.h"


class PhasePaintData;
class PhaseChartMap;
class QPainter;
class QPalette;
class PhaseValueHelper;

class PhaseChartDraw
{
public:
    PhaseChartDraw();
    virtual ~PhaseChartDraw()
    { }

    // 获取/设置坐标映射器, 用以在绘制时计算坐标
    const PhaseChartMap* chartMap() const
    {
        return m_pChartMap;
    }

    void setChartMap(const PhaseChartMap *pMap)
    {
        m_pChartMap = pMap;
    }

    // 阈值
    float threshold() const
    {
        return m_fThreshold;
    }

    void setThreshold(float fThreshold)
    {
        m_fThreshold = fThreshold;
    }

    // 绘制数据类
    const PhasePaintData* paintData() const
    {
        return m_pPaintData;
    }

    void setPaintData(const PhasePaintData *pPaintData)
    {
        m_pPaintData = pPaintData;
    }

    // 相位偏移
    int phaseOffset() const
    {
        return m_iPhaseOffset;
    }

    void setPhaseOffset(int iAngle)
    {
        m_iPhaseOffset = iAngle;
    }

    // 数据辅助对象, 用以转换文字
    void setValueHelper(PhaseValueHelper *pValueHelper)
    {
        m_pValueHelper = pValueHelper;
    }

    // 封装对ValueHelper的调用
    QString valueToString(Phase::ValueType value) const;

    Phase::SuffixValue suffixType() const;

    virtual void draw(QPainter *painter, const QPalette &palette) = 0;

private:
    float m_fThreshold;
    int m_iPhaseOffset;
    const PhasePaintData *m_pPaintData;
    const PhaseChartMap *m_pChartMap;
    PhaseValueHelper *m_pValueHelper;
};

#endif // PHASECHARTDRAW_H
