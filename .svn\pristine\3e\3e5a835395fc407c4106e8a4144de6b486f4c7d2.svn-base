#include "prpsvideobutton.h"
#include <QStyle>
/************************************************
 * 功能: 构造函数
 * 入参：parent -- 父控件指针
 ************************************************/
PrpsVideoButton::PrpsVideoButton(QWidget *parent) :
    QPushButton(parent),
    m_bIsSelected( false )
{
    connect( this,SIGNAL(clicked()),this,SLOT(switchState()) );
}

/************************************************
 * 功能: 设置选中状态，通过样式表中属性选择的方式控制自动刷新
 * 入参：true -- 选中
 *       false -- 未选中
 ************************************************/
void PrpsVideoButton::setSelected( bool bSelected )
{
    if( m_bIsSelected != bSelected )
    {
        m_bIsSelected = bSelected;
        /*此处成对出现的原因是，
         * 动态根据属性值切换样式并不能立即生效（样式属性设置现存缺陷）
         * 故此处手动调用该重载函数，便于样式根据我们设定的属性完成
         * 动态切换
        */
        style()->unpolish( this );  //移除给定窗口不见的外观
        style()->polish( this );    //初始化给定窗口部件的外观
        update();               // 更新样式
    }
    else
    {
        //the same
    }
}

/************************************************
 * 功能: 获取选中状态
 * 返回值：true -- 选中
 *       false -- 未选中
 ************************************************/
bool PrpsVideoButton::isSelected( void ) const
{
    return m_bIsSelected;
}

/************************************************
 * 功能: 绑定自身点击的信号，以便切换选中状态
 ************************************************/
void PrpsVideoButton::switchState( void )
{
    setSelected( !m_bIsSelected );
}
