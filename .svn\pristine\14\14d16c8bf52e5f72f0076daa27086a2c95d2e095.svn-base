/*
* Copyright (c) 2017.12，南京华乘电气科技有限公司
* All rights reserved.
*
* AbstractSpectrumTask.h
*
* 初始版本：1.0
* 作者：陈康
* 创建日期：2017年12月11日
* 摘要：单个图谱采集任务的基类
*      用来执行实际的采集业务
*
* 当前版本：1.0
*/
#ifndef ABSTRACTSPECTRUMTASK_H
#define ABSTRACTSPECTRUMTASK_H

#include <QObject>
#include <QVector>
#include "datadefine.h"

class DataMap;
class AbstractSpectrumTask : public QObject
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent -- 父Object
    输出参数：NULL
    返回值： NULL
    *************************************************/
    explicit AbstractSpectrumTask(QObject *parent = 0);

    ~AbstractSpectrumTask();

    /*************************************************
    功能： 开始进行对应图谱的采集 异步接口
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    *************************************************/
    virtual void startSample() = 0;

signals:
    /*************************************************
    功能： 采集完成的信号
    参数:  pMap--此次采集的数据
    *************************************************/
    void sigSampleFinished( DataMap * pMap );


    void sigSampleMaps( QVector<DataMap *> vMap );

};

#endif // ABSTRACTSPECTRUMTASK_H
