﻿
#include <QApplication>
#include <QDebug>
#include <QBoxLayout>
#include <QTimer>
#include <QFocusEvent>
#include <QFontMetrics>
#include <QDesktopWidget>
#include <QListView>
#include <QStandardItemModel>
#include <QStackedLayout>
#include "datadefine.h"
#include "camatchview.h"
#include "appconfig.h"
#include "window/Window.h"
#include "View.h"
#include "titlebar/TitleBar.h"
#include "loadingView/LoadingView.h"
#include "messageBox/msgbox.h"
#include "ca/caclientsmanager.h"
#include "pmviewconfig.h"
#include "appfontmanager/appfontmanager.h"
#include "global_def.h"
#include "log/log.h"



#define NAME_WIDTH_RATIO 0.8 //宽度比例
#define NAME_HEIGHT_RATIO 0.8 //文本高度比例

CAMatchView::CAMatchView(QWidget *parent) : QWidget(parent)
  , m_bRefreshing(false)
{
    m_pUpdateFWLoading = NULL;
    //创建titlebar
    m_pTitleBar = new TitleBar(trUtf8("CA Diag. Pairing"), this);
    m_pTitleBar->setFixedHeight( 100);
    //创建subtitle
    QHBoxLayout *pSubTitleLayout = createSubTitleLayout();

    QHBoxLayout *pCurrentIDLayout = createCurrentIDLayout();
	
    //创建主窗口
    m_pView = new QListView(this);
    //m_pView->installEventFilter(this);
    //m_pView->setFocusPolicy(Qt::ClickFocus);

    m_pModel = new QStandardItemModel(this);

    QHBoxLayout *pDisconnectLayout = createDisconnectLayout();

    m_pView->setModel(m_pModel);

    m_pLoadingWidget = new LoadingView(QObject::trUtf8("Searching"), this);
    m_pStackedLayout = new QStackedLayout;
    m_pStackedLayout->addWidget(m_pLoadingWidget);
    m_pStackedLayout->addWidget(m_pView);
    m_pStackedLayout->setCurrentWidget(m_pLoadingWidget);

    //界面整体布局
    QBoxLayout *boxLayout = new QBoxLayout(QBoxLayout::TopToBottom);
    boxLayout->setSpacing(0);
    boxLayout->setContentsMargins(0, 0, 0, 0);
    boxLayout->addWidget(m_pTitleBar);
    boxLayout->addLayout(pSubTitleLayout);
    boxLayout->addLayout(pCurrentIDLayout);
    boxLayout->addLayout(m_pStackedLayout);
    boxLayout->addLayout(pDisconnectLayout);
    setLayout(boxLayout);

    //设置属性
    setWindowFlags(Qt::FramelessWindowHint);
    setFixedSize( Window::WIDTH, Window::HEIGHT );
    setAttribute(Qt::WA_DeleteOnClose, true);
    QFont font = this->font();
    font.setPointSize( 30 );
    this->setFont( font );

    m_pCAClientsManager = new CAClientsManager(this);

    autoOpenHotSpot();

    refreshConditionerList();

    m_iQueryTimerId = startTimer(QUERY_CONDITIONER_INTERVAL);
	m_iFaileCount = 0;
    connect(m_pTitleBar, SIGNAL(sigClicked()), this, SLOT(close()));
    connect(m_pView, SIGNAL(clicked(QModelIndex)), this, SLOT(connectSelectedConditioner(QModelIndex)));
    connect(m_pCAClientsManager, SIGNAL(sigDeviceDisconnected(QString)),this, SLOT(onDeviceDisconnected(QString)));
    connect(m_pCAClientsManager, SIGNAL(sigDeviceConneted(QString)), this, SLOT(onDeviceConneted(QString)));

    installEventFilter(this);
}


/*************************************************
功能： 析构
*************************************************************/
CAMatchView::~CAMatchView( )
{
    bool  bFutureRunning = m_UpdateG100WFWFuture.isRunning();
    if(bFutureRunning)
    {
        m_UpdateG100WFWFuture.waitForFinished();
    }
    APP_CHECK_FREE(m_pUpdateFWLoading);
}

/*************************************************
函数名： connectSelectedConditioner
输入参数:
    index：item在model中的下标
输出参数： NULL
返回值： NULL
功能： 当某个设备ID被点击后，根据设备的状态确认是否连接，并给出提示信息
*************************************************************/
void CAMatchView::connectSelectedConditioner(const QModelIndex& index)
{
    if(!index.isValid())
    {
        logError("list view clicked trigger index is invalid.");
        return;
    }

    QString strMac = index.data().toString();
    logInfo(QString("selected CA conditioner mac is: %1.").arg(strMac).toLatin1().data());

    if(m_pCAClientsManager->connectedDevice() == strMac)
    {
        MsgBox::information("", PM_VIEW_CONFIG_TRANSLATE(PMViewConfig::CONNECT_DEVICE_BUSY));
        return;
    }

    if(m_pCAClientsManager->connectToDevice(strMac))
    {
        refreshConditionerList(true);

        MsgBox::information("", PM_VIEW_CONFIG_TRANSLATE(PMViewConfig::CONNECT_DEVICE_SUCCESS));
    }
    else
    {
        MsgBox::information("", PM_VIEW_CONFIG_TRANSLATE(PMViewConfig::CONNECT_DEVICE_FAILED));
    }

    return;
}

/************************************************
 * 函数名   : adjustFontSize
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 设置字体大小
 ************************************************/
void CAMatchView::adjustFontSize(void)
{
    //字体
    QFont fontHeight = getReheightFont(AppFontManager::instance()->getAppCurFont(), m_pCurrentIdLabel->height() * NAME_HEIGHT_RATIO);

    QFontMetrics fm(fontHeight);
    QFont qFontSize = fontHeight;
    if(m_pCurrentIdLabel->width() * NAME_WIDTH_RATIO < fm.width( m_pCurrentIdLabel->text()))
    {
        qFontSize = getRewidthFont(AppFontManager::instance()->getAppCurFont(), m_pCurrentIdLabel->text(), m_pCurrentIdLabel->width() * NAME_WIDTH_RATIO);
    }
    m_pCurrentIdLabel->setFont(qFontSize);

    return;
}

/*************************************************
函数名： createSubTitleLayout(WLDeviceType eDeviceType)
输入参数:NULL
输出参数： NULL
返回值： 子标题栏对象：调理器名称+刷新按钮
功能： 根据调理器类型创建子标题栏
*************************************************************/
QHBoxLayout *CAMatchView::createSubTitleLayout()
{
    QLabel *pSubTitleLabel = new QLabel(PM_VIEW_CONFIG_TRANSLATE(PMViewConfig::CA_DEVICE_SELECT));
    QFont font = AppFontManager::instance()->getAppCurFont();
    font.setPixelSize(23);
    pSubTitleLabel->setFont(font);

    m_pRefreshButton = new QPushButton(QObject::trUtf8("Refresh"), this);
    //    m_pRefreshButton->installEventFilter(this);
    //    m_vAllin.append(m_pRefreshButton);
    //m_pRefreshButton->setFocusPolicy(Qt::ClickFocus);
    m_pRefreshButton->setEnabled(false);
    connect(m_pRefreshButton, SIGNAL(clicked(bool)), this, SLOT(onRefreshBtnClicked()),Qt::DirectConnection);

    QHBoxLayout *pSubTitleLayout = new QHBoxLayout;
    pSubTitleLayout->setContentsMargins(0, 0, 0, 0);
    pSubTitleLayout->addWidget(pSubTitleLabel);
    pSubTitleLayout->addWidget(m_pRefreshButton, 0, Qt::AlignRight | Qt::AlignVCenter);

    return pSubTitleLayout;
}

/*************************************************
函数名： createCurrentIDLayout()
返回值： QHBoxLayout
功能： 创建显示上次已连接调理器Layout
*************************************************************/
QHBoxLayout *CAMatchView::createCurrentIDLayout()
{
    QString qsId = QObject::trUtf8("Current ID: ");
    m_pCurrentIdLabel = new QLabel(qsId);
    m_pCurrentIdLabel->setWordWrap(true);

    QHBoxLayout *hLayout = new QHBoxLayout;
    hLayout->setContentsMargins(0, 0, 0, 0);
    hLayout->addWidget(m_pCurrentIdLabel);

    return hLayout;
}

/*************************************************
函数名： createDisconnectLayout()
返回值： QHBoxLayout
功能： 创建含有断开连接按钮的Layout
*************************************************************/
QHBoxLayout *CAMatchView::createDisconnectLayout()
{
    m_pDisconnectButton = new QPushButton(QObject::trUtf8("Disconnect"));
    QHBoxLayout *hLayout = new QHBoxLayout;
    hLayout->setContentsMargins(0, 0, 0, 0);
    hLayout->addWidget(m_pDisconnectButton);

    //    m_vAllin.append(m_pDisconnectButton);
    //    m_pDisconnectButton->installEventFilter(this);

    connect(m_pDisconnectButton, SIGNAL(clicked(bool)), this, SLOT(disconnectCurrentDevice()));

    return hLayout;
}

void CAMatchView::onDeviceDisconnected(QString strMac)
{
    Q_UNUSED(strMac);

    refreshConditionerList(true);

}

void CAMatchView::onDeviceConneted(QString strMac)
{
    Q_UNUSED(strMac);

    refreshConditionerList(true);
}

void CAMatchView::onRefreshBtnClicked()
{
    m_pRefreshButton->setEnabled(false);
    m_pDisconnectButton->setVisible(false);
    m_pStackedLayout->setCurrentWidget(m_pLoadingWidget);

    m_listDeviceMac.clear();
    m_bRefreshing = true;
    return;
}

void CAMatchView::disconnectCurrentDevice()
{
    if(m_pView->currentIndex().isValid())
    {
        QString strMac = m_pView->currentIndex().data().toString();

        if(strMac != m_pCAClientsManager->connectedDevice())
        {
            MsgBox::information("", PM_VIEW_CONFIG_TRANSLATE(PMViewConfig::UNCONNECTED_CONDITIONER));
        }
        else
        {
            if( m_pCAClientsManager->disconnectFromDevice(strMac) )
            {
                refreshConditionerList(true);
                MsgBox::information("", PM_VIEW_CONFIG_TRANSLATE(PMViewConfig::DISCONNECT_CURRENT_DEVICE_SUCCESS));
            }
            else
            {
                MsgBox::information("", PM_VIEW_CONFIG_TRANSLATE(PMViewConfig::DISCONNECT_CURRENT_DEVICE_FAILED));
            }
        }
    }
    else
    {
        MsgBox::warning("", trUtf8("Please select CA Diag. processor."));
    }
}

void CAMatchView::updateConnectState()
{
    QString strMac = m_pCAClientsManager->connectedDevice();
    if( !strMac.isEmpty() )
    {
        m_pDisconnectButton->setDisabled(false);
    }
    else
    {
        m_pDisconnectButton->setDisabled(true);
    }

    QString qsId = QObject::trUtf8("Current ID: ") + strMac;
    m_pCurrentIdLabel->setText(qsId);
}

/*************************************************
函数名： refreshConditionerList
输入参数: NULL
输出参数： NULL
返回值： NULL
功能： 重新搜索调理器，刷新列表
*************************************************************/
bool CAMatchView::refreshConditionerList(bool bStateChanged)
{
    QStringList listMac = m_pCAClientsManager->candidates();
	bool iRet = false;
    if(m_pCAClientsManager->isAnyDeviceConnected())
    {
        QString strMac = m_pCAClientsManager->connectedDevice();
        listMac.append(strMac);

        QString qsId = QObject::trUtf8("Current ID: ") + strMac;
        m_pCurrentIdLabel->setText(qsId);
    }
    else
    {
        QString qsId = QObject::trUtf8("Current ID: ");
        m_pCurrentIdLabel->setText(qsId);
    }

    if(listMac.size() > 0)
    {
        if(m_bRefreshing)
        {
            m_pRefreshButton->setEnabled(true);
            m_pDisconnectButton->setVisible(true);
            m_pStackedLayout->setCurrentWidget(m_pView);
        }

        if(listMac != m_listDeviceMac || bStateChanged)
        {
            setDeviceModel(listMac);

            if(!(m_pRefreshButton->isEnabled()))
            {
                m_pRefreshButton->setEnabled(true);
                m_pDisconnectButton->setVisible(true);
            }

            m_pStackedLayout->setCurrentWidget(m_pView);
        }
		iRet = true;
    }
    else
    {
        m_pModel->clear();

        m_pRefreshButton->setEnabled(false);
        m_pDisconnectButton->setVisible(false);
        m_pStackedLayout->setCurrentWidget(m_pLoadingWidget);
		iRet = false;
    }

    m_listDeviceMac = listMac;

    return iRet;
}

void CAMatchView::autoOpenHotSpot()
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    int iHotSpot = pConfig->value( APPConfig::KEY_HOT_SWITCH ).toInt();
    pConfig->endGroup();
    if( iHotSpot == true )//closed
    {
        bool isSuccess = View::setHotSpot(SystemSet::HOT_OPEN);
        if(isSuccess == true)
        {
            // 修改wifi开关状态，打开ap时默认关闭wifi
            ConfigInstance *pConfig = ConfigManager::instance()->config();
            pConfig->beginGroup( Module::GROUP_APP );
            pConfig->setValue( SystemSet::HOT_OPEN,APPConfig::KEY_HOT_SWITCH );
            pConfig->setValue( false,APPConfig::KEY_WIFI_SWITCH );
            pConfig->endGroup();
        }
        else
        {
            dbg_warning("auto open hot spot failed!");
        }
    }
    else
    {
        dbg_info("hot spot is already open!");
    }
}

/*************************************************
函数名： setDeviceModel
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 显示设备列表信息
*************************************************************/
void CAMatchView::setDeviceModel(const QStringList &listDeviceMac)
{
    m_pModel->clear();
    for(int i = 0; i < listDeviceMac.size(); i++)
    {
        QStandardItem *item = new QStandardItem(listDeviceMac.at(i));
        item->setTextAlignment(Qt::AlignCenter);
        item->setSizeHint(QSize(480, 80));
        item->setFlags(Qt::ItemIsSelectable | Qt::ItemIsEnabled);

        QFont ft = item->font();
        ft.setPointSize(36);
        item->setFont(ft);
        item->setForeground(QBrush(QColor(0, 158, 255)));

        QLinearGradient linearGrad(QPoint(0, 0), QPoint(0, 50));

        if(listDeviceMac.at(i) == m_pCAClientsManager->connectedDevice())
        {
            linearGrad.setColorAt(0, QColor(240, 240, 240));
            linearGrad.setColorAt(1, QColor(160, 160, 160));
            ft.setItalic(true);
            QBrush brush(linearGrad);
            item->setBackground(brush);
            item->setFont(ft);

            m_pModel->appendRow(item);
        }
        else
        {
            m_pModel->insertRow(0, item);
        }
    }
}

void CAMatchView::processUpdateG100WResult()
{
    if(m_isSuccessfullyUpdate == false)
    {
        dbg_warning("fail to chmod 755 /opt/bin-bash/expect!\n");
        MsgBox::warning("", QObject::trUtf8("Update fail!") );
    }
    else
    {
        dbg_warning("success to use expect to update G100W FW\n");
        MsgBox::informationWithoutAutoAccept("", trUtf8("Update success, please reboot HAS02."));
    }
}

/*************************************************
功能： 事件处理的函数
输入参数：
        event -- 事件对象
*************************************************************/
bool CAMatchView::event ( QEvent * event )
{

    if (event->type() == static_cast<QEvent::Type>(UpdateG100WFWEvent::EventId))
    {
        APP_CHECK_FREE(m_pUpdateFWLoading);
        processUpdateG100WResult();
        return true;
    }
    else
    {
        return QWidget::event(event);
    }
}

/*************************************************
函数名： eventFilter
输入参数:
    obj:事件所属对象
    event：事件指针
输出参数： NULL
返回值： 处理结果
功能： 实现按Esc键退出的功能
*************************************************************/
bool CAMatchView::eventFilter(QObject *obj, QEvent *event)
{
    if(QEvent::KeyPress == event->type())
    {
        QKeyEvent *e = static_cast<QKeyEvent *>(event);
        if(Qt::Key_Return == e->key() || Qt::Key_Enter == e->key())
        {
            if(obj == m_pRefreshButton)
            {
                refreshConditionerList();
            }
            else if(obj == m_pDisconnectButton)
            {
                disconnectCurrentDevice();
            }
            else
            {
                connectSelectedConditioner(m_pView->currentIndex());
            }
        }

        //        else if(Qt::Key_Left == e->key())
        //        {
        //            activePreviousWidget();
        //        }
        //        else if(Qt::Key_Right == e->key())
        //        {
        //            activeNextWidget();
        //        }
        else if( Qt::Key_Down == e->key())
        {
            if( m_pModel->hasChildren() )
            {
                QModelIndex index = m_pView->currentIndex();
                if(index.isValid())
                {
                    int iRow = index.row();
                    iRow++;
                    index = m_pModel->index(iRow, 0);
                    if(index.isValid())
                    {
                        m_pView->setCurrentIndex(index);
                    }
                }
                else
                {
                    m_pView->setCurrentIndex(m_pModel->index(0, 0));
                }
            }
        }
        else if(Qt::Key_Up == e->key())
        {
            if( m_pModel->hasChildren() )
            {
                QModelIndex index = m_pView->currentIndex();
                if(index.isValid())
                {
                    int iRow = index.row();
                    iRow--;

                    if(iRow < 0)
                    {
                        iRow = 0;
                    }

                    index = m_pModel->index(iRow, 0);
                    if(index.isValid())
                    {
                        m_pView->setCurrentIndex(index);
                    }
                }
                else
                {
                    m_pView->setCurrentIndex(m_pModel->index(m_pModel->rowCount()-1, 0));
                }
            }
        }
        else if( Qt::Key_Escape == e->key() )
        {
            close();
        }
        else if(Qt::Key_F5 == e->key())/*Key S+Down*/
        {
            queryToUpdateG100WFW();
        }
        else
        {
            //do nothing
        }
    }

    return QWidget::eventFilter(obj,event);

}

/*************************************************
函数名： threadUpdateG100WFW
输入参数: pstData---写到xml文件的数据
输出参数：NULL
返回值： NULL
功能： 子线程函数,写数据到xml文件
*************************************************************/
void CAMatchView::threadUpdateG100WFW(CAMatchView *pInstance)
{
    //新增了如下的msg box未翻译词条，故暂时屏蔽
//    QFile file ("/media/data/SavedData/update.tar.gz");
//    if(!file.exists())
//    {
//        dbg_warning("no G100W FW!\n");
//        m_isSuccessfullyUpdate = false;
        //QApplication::postEvent(this,new UpdateG100WFWEvent(true, ""));
        //return;
//    }
    Q_UNUSED(pInstance);
    QFile G100WFWFile("/media/data/SavedData/update.tar.gz");
    if(!G100WFWFile.exists())
    {
        dbg_warning("g100w fw doesn't exist in /media/data/SavedData\n");
        m_isSuccessfullyUpdate = false;
        QApplication::postEvent(this,new UpdateG100WFWEvent(true, ""));
        return;
    }

    QString strCmd;
    strCmd = "chmod 755 /opt/bin-bash/expect";
    int iResult = system(strCmd.toStdString().c_str());
    if(iResult < 0)
    {
        dbg_warning("fail to chmod 755 /opt/bin-bash/expect!\n");
        m_isSuccessfullyUpdate = false;
        QApplication::postEvent(this,new UpdateG100WFWEvent(true, ""));
        return;
    }

    strCmd = "chmod 755 /opt/bin-bash/scp_expect";
    iResult = system(strCmd.toStdString().c_str());
    if(iResult < 0)
    {
        dbg_warning("fail to chmod 755 /opt/bin-bash/scp_expect!\n");
        m_isSuccessfullyUpdate = false;
        QApplication::postEvent(this,new UpdateG100WFWEvent(true, ""));
        return;
    }

    strCmd = "cd /opt/bin-bash";
    iResult = system(strCmd.toStdString().c_str());
    if(iResult < 0)
    {
        dbg_warning("fail to cd /opt/bin-bash!\n");
        m_isSuccessfullyUpdate = false;
        QApplication::postEvent(this,new UpdateG100WFWEvent(true, ""));
        return;
    }

    strCmd = "expect /opt/bin-bash/scp_expect 192.168.1.2 /media/data/SavedData/update.tar.gz /home/<USER>/G100W/update.tar.gz";
    iResult = system(strCmd.toStdString().c_str());
    if(iResult < 0)
    {
        dbg_warning("fail to use expect to update G100W FW\n");
        m_isSuccessfullyUpdate = false;
        QApplication::postEvent(this,new UpdateG100WFWEvent(true, ""));
        return;
    }
    else
    {
        dbg_warning("success to use expect to update G100W FW\n");
        m_isSuccessfullyUpdate = true;
        QApplication::postEvent(this,new UpdateG100WFWEvent(true, ""));
        return;
    }
}

/*更新HAS02(G100W)固件*/
void CAMatchView::updateG100WFW()
{
    m_isSuccessfullyUpdate = false;
    m_UpdateG100WFWFuture = QtConcurrent::run(this, &CAMatchView::threadUpdateG100WFW, this);

#if 0
    QString strCmd;
    strCmd = "chmod 755 /opt/bin-bash/expect";
    int iResult = system(strCmd.toStdString().c_str());
    if(iResult < 0)
    {
        dbg_warning("fail to chmod 755 /opt/bin-bash/expect!\n");
        MsgBox::warning("", QObject::trUtf8("Update fail!") );
        return;
    }

    strCmd = "chmod 755 /opt/bin-bash/scp_expect";
    iResult = system(strCmd.toStdString().c_str());
    if(iResult < 0)
    {
        dbg_warning("fail to chmod 755 /opt/bin-bash/scp_expect!\n");
        MsgBox::warning("", QObject::trUtf8("Update fail!") );
        return;
    }

    strCmd = "cd /opt/bin-bash";
    iResult = system(strCmd.toStdString().c_str());
    if(iResult < 0)
    {
        dbg_warning("fail to cd /opt/bin-bash!\n");
        MsgBox::warning("", QObject::trUtf8("Update fail!") );
        return;
    }

    strCmd = "expect /opt/bin-bash/scp_expect 192.168.1.2 /media/data/SavedData/update.tar.gz /home/<USER>/G100W/update.tar.gz";
    iResult = system(strCmd.toStdString().c_str());
    if(iResult < 0)
    {
        dbg_warning("fail to use expect to update G100W FW\n");
        MsgBox::warning("", QObject::trUtf8("Update fail!") );
        return;
    }
    else
    {
        dbg_warning("success to use expect to update G100W FW\n");
        MsgBox::informationWithoutAutoAccept("", trUtf8("Update success, please reboot HAS02."));
    }
#endif
}

void CAMatchView::onStartUpdateFW()
{
    //update G100 FW
    updateG100WFW();
}

/*Key S+Down 询问是否要更新HAS02的固件*/
void CAMatchView::queryToUpdateG100WFW()
{
    MsgBox::Button eResBtn = MsgBox::question("", trUtf8("Confirm to update HAS02 firmware?") );
    if(eResBtn == MsgBox::OK)
    {
        QString strMac = m_pCAClientsManager->connectedDevice();
        if(!strMac.isEmpty())
        {
            m_pUpdateFWLoading = new LoadingView("", this);
            m_pUpdateFWLoading->setFixedSize(window()->width()/4, window()->width()/4);
            m_pUpdateFWLoading->move(window()->width()*3/8, window()->height()/2);
            m_pUpdateFWLoading->show();
            QTimer::singleShot(3000,this,SLOT(onStartUpdateFW()));
        }
        else
        {
            MsgBox::warning("", trUtf8("No HAS02 connected!") );
        }
    }
}

/*************************************************
函数名： timerEvent(QTimerEvent *pEvent)
输入参数:
输出参数：
返回值：
功能： 定时器处理函数
*************************************************************/
void CAMatchView::timerEvent(QTimerEvent *pEvent)
{
    if(pEvent->timerId() == m_iQueryTimerId)
    {
		if(!refreshConditionerList())
		{
			++m_iFaileCount;
			logInfo(QString("get conditioners failed, iFaileCount %1").arg(m_iFaileCount));
			if(m_iFaileCount == SACN_CONDITIONER_FAILED_COUNT)
			{
                MsgBox::Button eReply = MsgBox::warning("", trUtf8("CA Diag. not found!"));
				killTimer(pEvent->timerId());
			}
		}
    }
    else
    {
        killTimer(pEvent->timerId());
    }
}

/************************************************
 * 函数名   : activeNextWidget
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 将焦点往后移动
 ************************************************/
void CAMatchView::activeNextWidget( void )
{
    bool bActivate = false;
    for( int i = 0;i < m_vAllin.size();++i )
    {
        if( m_vAllin.at( i )->hasFocus() )
        {
            if( ( m_vAllin.size() - 1 ) == i )
            {
                m_vAllin.first()->setFocus();
            }
            else
            {
                m_vAllin.at( ++i )->setFocus();
            }
            bActivate = true;
            break;
        }
    }
    if( false == bActivate )
    {
        m_vAllin.first()->setFocus();
    }
}

/************************************************
 * 函数名   : activePreviousWidget
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 将焦点往前移动
 ************************************************/
void CAMatchView::activePreviousWidget( void )
{
    bool bActivate = false;
    for( int i = 0;i < m_vAllin.size();++i )
    {
        if( m_vAllin.at( i )->hasFocus() )
        {
            if( 0 == i )
            {
                m_vAllin.last()->setFocus();
            }
            else
            {
                m_vAllin.at( --i )->setFocus();
            }
            bActivate = true;
            break;
        }
    }
    if( false == bActivate )
    {
        m_vAllin.last()->setFocus();
    }
}
