/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* HFCTConfig.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2017年04月17日
* 摘要：HFCT模块的配置定义
* 当前版本：1.0
*/
#ifndef HFCTCONFIG_H
#define HFCTCONFIG_H

#include "config/ConfigManager.h"
#include "HFCT.h"
#include "module_global.h"
#include "Module.h"

namespace HFCT
{

//配置信息定义
typedef enum _ConfigInfo
{
    //组号
    GROUP_HFCT_AMPLITUDE = 0,//幅值
    GROUP_HFCT_SPECTRUM,//周期
    GROUP_HFCT_PRPS,//PRPS

    //键值
    KEY_RED_ALARM,//红色报警
    KEY_YELLOW_ALARM,//黄色报警
    KEY_SYNC_SOURCE,//同步方式
    KEY_GAIN,//增益
    KEY_PHASEALIAS,//相位偏移
    KEY_PRPS_ACCUMULATION_TIME,//累积模式
    KEY_SAMPLE_MODE,//采样模式
    KEY_PRPS_RECORD_TIME,//录屏时间
    KEY_PRPS_ANALYSIS_THRESHOLD,//分析阈值
    KEY_PRPS_THRESHOLD_MODE,//阈值模式
}ConfigInfo;

enum ThresholdMode
{
    THRESHOLD_OFF = 0,
    THRESHOLD_AUTO,
    THRESHOLD_LEVEL1,
    THRESHOLD_LEVEL2,
    THRESHOLD_MODE_MIN = THRESHOLD_OFF,
    THRESHOLD_MODE_MAX = THRESHOLD_LEVEL2,
    THRESHOLD_MODE_DEFAULT = THRESHOLD_AUTO,
};

//HFCT组的键值配置
static Config::KeyInfo KEYS_HFCT[] =
{
    { KEY_RED_ALARM, "RedAlarm", Config::NUMBER, QString::number(RED_ALARM_DEFAULT), ALARM_MIN, ALARM_MAX },
    { KEY_YELLOW_ALARM, "YellowAlarm", Config::NUMBER, QString::number(YELLOW_ALARM_DEFAULT), ALARM_MIN, ALARM_MAX },
    { KEY_SYNC_SOURCE, "SyncSource", Config::NUMBER, QString::number(SYNC_SOURCE_DEFAULT), SYNC_SOURCE_MIN, SYNC_SOURCE_MAX },
    { KEY_PHASEALIAS, "PhaseAlias", Config::NUMBER, QString::number(PHASE_DEFAULT), PHASE_MIN, PHASE_MAX },
    { KEY_GAIN, "Gain", Config::NUMBER, QString::number(GAIN_DEFAULT), GAIN_MIN, GAIN_MAX },
};

//HFCT周期组的键值配置
static Config::KeyInfo KEYS_PERIOD[] =
{
    { KEY_SAMPLE_MODE, "SampleMode", Config::NUMBER, QString::number(Module::SAMPLEMODE_DEFAULT), Module::SAMPLEMODE_MIN, Module::SAMPLEMODE_MAX },
};

//HFCT周期组的键值配置
static Config::KeyInfo KEYS_AMPLITUDE[] =
{
    { KEY_SAMPLE_MODE, "SampleMode", Config::NUMBER, QString::number(Module::SAMPLEMODE_DEFAULT), Module::SAMPLEMODE_MIN, Module::SAMPLEMODE_MAX },
};

//HFCT PRPS组的键值配置
static Config::KeyInfo KEYS_PRPS[] =
{
    { KEY_PRPS_ACCUMULATION_TIME, "AccumulationTime", Config::NUMBER, QString::number(HFCT::ACCUMULATION_DEFAULT), HFCT::ACCUMULATION_TIME_1, HFCT::ACCUMULATION_TIME_60 },
    { KEY_PRPS_RECORD_TIME, "RecordTime", Config::NUMBER, QString::number(Module::ScreenRecordTime_DEFAULT), Module::ScreenRecordTime_MIN, Module::ScreenRecordTime_MAX },
    { KEY_PRPS_ANALYSIS_THRESHOLD, "AnalysisThreshold", Config::NUMBER, QString::number(0), 0, 1 },
    { KEY_PRPS_THRESHOLD_MODE, "ThresholdMode", Config::NUMBER, QString::number(THRESHOLD_MODE_DEFAULT), THRESHOLD_MODE_MIN, THRESHOLD_MODE_MAX },
};

//HFCT组的组配置
static Config::GroupInfo GROUPS_HFCT[] =
{
    { GROUP_HFCT_AMPLITUDE, Config::NORMAL, "HFCTAmplitude", KEYS_AMPLITUDE, sizeof( KEYS_AMPLITUDE )/sizeof(Config::KeyInfo), NULL, 0 },
    { GROUP_HFCT_SPECTRUM, Config::NORMAL, "HFCTPeriod", KEYS_PERIOD, sizeof( KEYS_PERIOD )/sizeof(Config::KeyInfo), NULL, 0 },
    { GROUP_HFCT_PRPS, Config::NORMAL, "HFCTPrps", KEYS_PRPS, sizeof( KEYS_PRPS )/sizeof(Config::KeyInfo), NULL, 0 },
};

static Config::GroupInfo CONFIG =
{
    Module::GROUP_HFCT, Config::NORMAL, "HFCT", KEYS_HFCT, sizeof( KEYS_HFCT )/sizeof(Config::KeyInfo), GROUPS_HFCT, sizeof(GROUPS_HFCT)/sizeof(Config::GroupInfo)
};


}
#endif // HFCTCONFIG_H
