﻿/*
* Copyright (c) 2016.3，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：AEAbstractChart.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年12月5日
* 摘要：该文件主要实现了AE Phase和Fly的图谱实现基类
*/

#ifndef AEABSTRACTCHART_H
#define AEABSTRACTCHART_H

#include <QWidget>
#include "ae/AE.h"

class AEAbstractChart : public QWidget
{
    Q_OBJECT
public:
    typedef struct _AbstractData  // AE 飞行、相位图谱保存的数据结构
    {
//        _AbstractData(float x, float y)
//            : fXscale(x),
//              fYscale(y)
//        {
//        }
        float fXscale;  // 数据点占X轴的比例
        float fYscale;  // 数据点占Y轴的比例
        _AbstractData()
        {
            fXscale = 0;
            fYscale = 0;
        }
    }AbstractData;

    /************************************************
     * 功能     ：构造函数
     * 输入参数  ：eWorkMode -- 工作模式；父对象指针
     ************************************************/
    explicit AEAbstractChart(AE::WorkMode eWorkMode,QWidget *parent = 0);

    /****************************
    输入参数:iYMax:纵坐标最大值，最小值为0
    功能： 设置纵坐标最大值
    *****************************/
    void setYAxisScale( int iYMax );

    /****************************
    输入参数:iMin：X坐标系最小值，iMax：X坐标系最大值
    功能： 设置x坐标系范围
    *****************************/
    void setXAxisScale( int iMin, int iMax );

    /****************************
    输入参数:PhaseFlyData -- 相位/飞行图谱数据
    功能： 添加数据
    *****************************/
    bool addSample(AbstractData data);

    /****************************
    输入参数:rawData:保存原始数据的list
    功能： 当相位图谱需要移动时或实现回放功能时利用的接口
    *****************************/
    void addSamples( const QList<AbstractData> &rawData );

    /****************************
    输入参数:rawData:保存原始数据的list;
            rawIndex:保存原始数据的出现概率序号(0~255)
    功能： 当相位图谱需要移动时或实现回放功能时利用的接口
    *****************************/
    void addSamples( const QList<AbstractData> &rawData,
                     const QList<int> &colorIndex );

    /****************************
    返回值：AbstractData 原始数据
    功能： 返回存放的原始数据
    *****************************/
    const QList<AbstractData> &samples( void ) const;

    /****************************
    返回值：颜色出现的概率集合
    功能： 返回存放颜色出现概率的序号集合
    *****************************/
    const QList<int> &colorIndex( void ) const;

    /****************************
    输入参数: NULL
    功能： 清除显示
    *****************************/
    void clear( void );

    /****************************
    输入参数:trigger:触发值
    功能： 绘制触发线
    *****************************/
    void setTrigger( int iTrigger );

    /****************************
    输入参数:fYellow：黄色比例；fRed：红色比例；fBlack：黑色比例
    功能： 设置渐变的比例
    *****************************/
    void setGradient(float fYellow, float fRed, float fBlack );

    /****************************
    输入参数: usIndex：对应查表的下标
    功能： 根据index查表得到对应的rgb颜色
    *****************************/
    QColor getColorFromIndex( UINT16 usIndex );

    int getChartWidth();
    int getChartHeight();

protected:
    /****************************
    函数名： paintEvent(QPaintEvent *pEvent);
    输入参数:pEvent：绘图事件
    输出参数：NULL
    返回值：NULL
    功能： 重载绘图函数
    *****************************/
    void paintEvent( QPaintEvent* );

    /****************************
    函数名： resizeEvent(QResizeEvent *);
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： resize事件处理函数
    *****************************/
    void resizeEvent( QResizeEvent* );
private:
    /****************************
    功能： 更新全部数据
    *****************************/
    void updateAll(void);

    /****************************
    功能： 用于绘制AE数据
    *****************************/
    void drawRawData( QPainter* painter );

    /****************************
    功能： 用于绘制AE坐标系
    *****************************/
    void drawCoordinate( QPainter* painter );

    /****************************
    功能： 用于绘制正弦曲线
    *****************************/
    void drawSinusoidal(QPainter* pPainter);

    /****************************
    功能： 重置界面相关参数
    *****************************/
    void resetParams( void );

    /****************************
    功能： 生成坐标系线段
    *****************************/
    void makeCoordinateLines( void );

    /****************************
    功能： 定义存放数据颜色索引号的容器
    *****************************/
    void resizeAEVector( void );

    /****************************
    输入参数: usX:X坐标；usY:Y坐标
    功能： 根据点的位置得到对应的index
    *****************************/
    int getIndexFromData( float fX,float fY );

    /****************************
    输入参数: iIndex:颜色查表索引号；usWidth：数据点距原点的宽度；usHeight：数据点距原点的高度
    功能： 将数据添加到容器中
    *****************************/
    inline void addDataToModel( int iIndex,float fWidth,float fHeight );
private:
    QList<AbstractData>   m_lRawData;              // 保存绘制出的各种图谱数据
    QList<QLine>  m_lCoordinateLines;           // 坐标系线段集合
    QVector< QVector<int> >    m_vPointsMapVector;   // 一个以图谱区域高度和宽度为大小的二维容器，存储的数据反映该点的颜色，若出现次数高，该值不断刷新，颜色进行变化
    QHash< int,QList<QPointF> >    m_hCurrentData;    // 存放需要新绘制的图谱数据
    AE::WorkMode    m_eWorkMode;                    // 工作模式
    bool m_bShowSinusoidal; // 是否显示正弦曲线

    int  m_uiMaximum;                        // X坐标轴最大值
    int  m_uiMinimum;                        // X坐标轴最小值
    int  m_iYMaxValue;                      // Y坐标最大值
    int  m_iTrigger;                        // 触发值

    QPixmap m_AEpixmap;                         // 绘制画布
    QLinearGradient m_LinearGradient;           // 渐变色

    int m_iOriginX;                         // 坐标系原点X坐标
    int m_iOriginY;                         // 坐标系原点Y坐标
    int m_iChartWidth;                      // 图谱宽度
    int m_iChartHeight;                     // 图谱高度
    QPolygon m_sinusoidalLine; // 正弦曲线

    QList< int > m_lColorIndex;                  // 颜色出现概率的序号集合（对应chart中展示的数据）
    QList< int > m_lRawColorIndex;                  // 颜色出现概率的序号集合（对应所有的原始数据）
};

#endif // AEAbstractChart_H
