/*
* Copyright (c) 2017.01，南京华乘电气科技有限公司
* All rights reserved.
*
* PulseView.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年1月10日
*       新版本重构
* 摘要：CA Pulse图谱定义

* 当前版本：1.0
*/

#ifndef WaveTestView_H
#define WaveTestView_H
#include <QProgressDialog>
#include "widgets/sampleChartView/SampleChartView.h"
#include "ca/CA.h"
#include "ca/caservice.h"
#include "buttonBar/LabelButtonBar.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "FastWaveUT.h"
#include "dataSave/DataStructures.h"
#include "View.h"
#include "ca/dataSave/AccessG100WaveDataSave.h"
#include "pds/wavedatamap.h"
#include "controlButton/PopupButton.h"

class WaveTestView : public SampleChartView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        parent:父控件指针
    *************************************************************/
    explicit WaveTestView( const QString& strTitle, QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~WaveTestView( );

protected:
    /*************************************************
    功能： 定时器处理函数
    输入参数：
            e -- 定时器事件
    *************************************************************/
    void timerEvent(QTimerEvent *e);

signals:
    void sigDisplayWaveData(bool bStopSample);

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

private slots:
    void onInvalidCalibrateCo();

    void onNoCalibrateCo();

    /*************************************************
    功能： 槽，响应service告知的图谱数据
    *************************************************************/
    void onWaveData(CA::DataHeadInfo stHead, QList<CA::WaveData> lData);


    /*************************************************
    功能： 槽，响应service告知的通讯连接断开，更新界面显示
    *************************************************************/
    void onConnected(QString strMac);

    /************************************************
     * 函数名   : onDisconnectConnection
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数， 断开连接处理(客户端连接断开/配置失败/写校准参数失败)
     ************************************************/
    void onDisconnectConnection(QString strMac);

    /************************************************
     * 函数名   : resetGain
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数， 从校准界面返回到波形界面时，需要把波形界面的增益重新设置给g100
     ************************************************/
    void resetGain();

    void onDisplayWaveData(bool bStopSample);

private:

    /************************************************
     * 函数名   : connectSigsWithService
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : connectSigsWithService
     ************************************************/
    void connectSigsWithService();

    /*************************************************
    函数名： createChart()
    输入参数: parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 创建图谱
    *************************************************************/
    ChartWidget *createChart(QWidget *parent);


    /*************************************************
    功能： 设置表格数据
    *************************************************************/
    void setChartDatas();

    /*************************************************
    函数名： createChart()
    输入参数: parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 创建图谱
    *************************************************************/
    FastWaveUT *createUTChart(QWidget *parent);


    /*************************************************
    函数名： createChart()
    输入参数: parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 创建图谱
    *************************************************************/
    QLabel *createConnectStatusLabel(QWidget *parent);

    /*************************************************
    函数名： createFFTAverageLabel()
    输入参数: parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 创建图谱
    *************************************************************/
    QLabel *createFFTAverageLabel(QWidget *parent);

    /*************************************************
    函数名： createFFTRMSLabel()
    输入参数: parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 创建图谱
    *************************************************************/
    QLabel *createFFTRMSLabel(QWidget *parent);

    /*************************************************
    功能： 停止采样
    *************************************************************/
    void stopSample(void);

    /*************************************************
    功能： 初始化数据成员
    *************************************************************/
    void initDatas(void);

    /*************************************************
    函数名： getSampleParameter()
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能： 根据配置文件读出的数据更新采样数据
    *************************************************************/
    void getSampleParameter(void);

    /*************************************************
    功能： 设置按钮栏数据
    *************************************************************/
    void setButtonBarDatas(void);


    /*************************************************
    函数名： setChartXScale
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 根据相关参数重设图谱量程
    *************************************************************/
    void setChartXScale(void);

    /*************************************************
    函数名： setChartYScale
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 根据相关参数重设图谱量程
    *************************************************************/
    void setChartYScale(void);

    void setChartYTitle(void);

    /*************************************************
    函数名： setScaleMax(UINT32 &uixScaleMax)
    输入参数:uixScaleMax
    输出参数： NULL
    返回值： NULL
    功能：
    *************************************************************/
    void setScaleMax(UINT32 &uixScaleMax);

    /*************************************************
    函数名： updateSampleBtnText
    输入参数: eState---通讯状态
    输出参数：NULL
    返回值： NULL
    功能： 更新采样按钮的文本
    *************************************************************/
    void updateSampleBtnText(CA::CA_STATE eState);

    bool startSample();

    bool setAllParameters();

    void setSampleRelatedBtns();

    void disableSampleRelatedBtns();

    void enableSampleRelatedBtns();

    /*************************************************
    函数名： setSampleBtnText
    输入参数: eState---连接状态
    输出参数：NULL
    返回值： NULL
    功能： 根据连接状态设置采样按钮文本
    *************************************************************/
    void setSampleBtnText(CA::CA_STATE eState);

    void setConnectStatusNotice();

    void changeRange(quint32 uiOldRange, CA::Gain eNewGain);

    quint32 sampleTime2SampleLen(CA::SampleTime eSampleTime, CA::SampleRate eSampleRate);

    void pfnStartSample();

    void pfnStopSample();

    void pfnTimerSample();

    void pfnDealWaveData(CA::DataHeadInfo stHead, QList<CA::WaveData> lData);

private:
    CAService* m_pCAService;//服务模块

    ConfigInstance* m_pConfig;//配置模块

    CA::DataHeadInfo m_stHeadInfo;//数据头信息

    CA::CAConditionerInfo m_stG100Info;//G100信息

    CA::WaveSampleParameter m_stSampleParameter;//存放采样参数的结构体
    CA::CA_STATE m_eState;//和g100的通讯状态

    //界面控件
    FastWaveUT  *m_pChart;

    bool m_isSampleDone;
    QLabel *m_pYTitle;

    QLabel *m_pConnectStatusLabel;//状态栏图标
    QLabel *m_pFFTAverageLabel;
    QLabel *m_pFFTRMSLabel;
    ControlButton *m_pSampleBtn;//开始采样按钮

    //界面参数
    CA::SampleRate m_eSampleRate; //采样率
    CA::SampleTime m_eSampleTime; //采样时间
	CA::SampleLength m_eSampleLength; //采样长度
    CA::Gain m_eGain;//增益
    UINT8 m_ucBeforeTrigRatio; //触发前采样长度占比

    int m_iSearchingConditionerTimerId;

    CA::DataHeadInfo m_stHead;
    QVector<PopupButton *> m_vecSampleRelatedBtn;

    quint32 m_uiActualSampleTimes;
    quint32 m_uiSampleTimes;
    quint32 m_uiSampleInterval;
    int m_iSampleIntervalTimerID;
    quint32 m_uiSearchConditionerTimes;

    enum
    {
        CHART_HEIGHT = 380,//图谱高度
        STATUS_LABEL_HEIGHT = 20, // 状态显示标签高度
        SCALE_SIZE = 15,
        TITLE_SIZE = 15,
        REFRESH_SAVE_DATA_PROGRESS_TIMER_200_MS = 200,
        QUERY_CONNECTION_REQUEST_TIMER_500MS = 500,//查询是否有连接请求的时间间隔500ms
    };

    typedef enum _ScaleDirect
    {
        SCALE_NONE = 0,
        SCALE_UP_TO_LOW,
        SCALE_LOW_TO_UP
    }ScaleDirect;

    ScaleDirect m_eScaleDirect;
    ScaleDirect m_eXScaleDirect;

    CA::WaveSampleInterval m_eSampleInterval;
    CA::WaveSampleTimes m_eSampleTimes;
    qint32 m_iRangeMax;
    //CA::RangeMax  m_eRangeMax;

    QVector< double >  m_vXdata;
    QVector< double >  m_vYdata;

    QVector< double >  m_vXPlotData;
    QVector< double >  m_vYPlotData;
};

#endif // WaveTestView_H
