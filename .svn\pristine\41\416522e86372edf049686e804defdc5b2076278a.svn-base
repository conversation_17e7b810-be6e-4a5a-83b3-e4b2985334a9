#include "tevampspectrum.h"
#include "private/tevspectrumprivatedefine.h"
#include "common/dataspecificationutils.h"
#include "common/binaryprocesshelper.h"
#include "common/xmldocument.h"
#include "private/spectrumdatafilecommondefine.h"
#include "common/xmlprocesshelper.h"
#include <QDebug>

namespace DataSpecificationNS
{
    class TEVAmpSpectrumPrivate
    {
    public:
        TEVAmpExtInformationPrivate m_stTEVAmpExtInformationPrivate;
        TEVAmpDataPrivate m_stTEVAmpDataPrivate;
    };

    TEVAmpSpectrum::TEVAmpSpectrum()
        : Spectrum(),
          m_pTEVAmpSpectrumPrivate(new TEVAmpSpectrumPrivate)
    {
        setSpectrumTypeCode(SPECTRUM_CODE_TEV_AMP);
    }

    TEVAmpSpectrum::~TEVAmpSpectrum()
    {
    }

    /************************************************
     * 函数名   : setDataSpecificationVersion
     * 输入参数 :
       const DataSpecificationVersion eDataSpecificationVersion: 数据规范版本号
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置数据规范版本号
     ************************************************/
    void TEVAmpSpectrum::setDataSpecificationVersion(const DataSpecificationVersion eDataSpecificationVersion)
    {
        Spectrum::setDataSpecificationVersion(eDataSpecificationVersion);
        if (V_1_1_0_0 == eDataSpecificationVersion)
        {
            m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.skipSeverity();
            m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.skipDischargeSeverity();
        }
        else if (V_4_1_0_2 == eDataSpecificationVersion)
        {
            m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.skipBGFileName();
        }
    }

    /************************************************
     * 函数名   : spectrumName
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : QString
     * 功能     : 图谱名称
     ************************************************/
    QString TEVAmpSpectrum::spectrumName() const
    {
        return STR_FILE_NODE_TEV_AMP;
    }

    /************************************************
     * 函数名   : setTEVAmpExtInformation
     * 输入参数 :
       const TEVAmpExtInformation& stTEVAmpExtInformation: TEV幅值ExtInformation
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置TEV幅值ExtInformation
     ************************************************/
    void TEVAmpSpectrum::setTEVAmpExtInformation(const TEVAmpExtInformation& stTEVAmpExtInformation)
    {
        m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.ucAmpUnit = stTEVAmpExtInformation.eAmpUnit;
        m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpLowerLimit = stTEVAmpExtInformation.fAmpLowerLimit;
        m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpUpperLimit = stTEVAmpExtInformation.fAmpUpperLimit;
    }

    /************************************************
     * 函数名   : setTEVAmpData
     * 输入参数 :
       const TEVAmpData& stTEVAmpData: TEV幅值Data
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置TEV幅值Data
     ************************************************/
    void TEVAmpSpectrum::setTEVAmpData(const TEVAmpData& stTEVAmpData)
    {
        m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVAmpValue = stTEVAmpData.fTEVAmpValue;
        m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVMaxValue = stTEVAmpData.fTEVMaxValue;
        m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.iPulseCount = stTEVAmpData.iPulseCount;
        m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fSeverity = stTEVAmpData.fSeverity;
        m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.qstrBGFileName = stTEVAmpData.qstrBGFileName;
        m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.ucDischargeSeverity = stTEVAmpData.eDischargeSeverity;
    }

    /************************************************
     * 函数名   : getTEVAmpExtInformation
     * 输入参数 : NULL
     * 输出参数 :
       TEVAmpExtInformation& stTEVAmpExtInformation: TEV幅值ExtInformation
     * 返回值   : void
     * 功能     : 获取TEV幅值ExtInformation
     ************************************************/
    void TEVAmpSpectrum::getTEVAmpExtInformation(TEVAmpExtInformation& stTEVAmpExtInformation)
    {
        stTEVAmpExtInformation.eAmpUnit = static_cast<AmpUnit>(m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.ucAmpUnit);
        stTEVAmpExtInformation.fAmpLowerLimit = m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpLowerLimit;
        stTEVAmpExtInformation.fAmpUpperLimit = m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpUpperLimit;
    }

    /************************************************
     * 函数名   : getTEVAmpData
     * 输入参数 : NULL
     * 输出参数 :
       TEVAmpData& stTEVAmpData: TEV幅值Data
     * 返回值   : void
     * 功能     : 获取TEV幅值Data
     ************************************************/
    void TEVAmpSpectrum::getTEVAmpData(TEVAmpData& stTEVAmpData)
    {
        stTEVAmpData.fTEVAmpValue = m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVAmpValue;
        stTEVAmpData.fTEVMaxValue = m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVMaxValue;
        stTEVAmpData.iPulseCount = m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.iPulseCount;
        stTEVAmpData.fSeverity = m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fSeverity;
        stTEVAmpData.qstrBGFileName = m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.qstrBGFileName;
        stTEVAmpData.eDischargeSeverity = static_cast<DischargeSeverity>(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.ucDischargeSeverity);
    }

    /************************************************
     * 函数名   : saveBinarySpectrumExtInfo
     * 输入参数 :
       QDataStream& out: 输出流
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存二进制可扩展信息段
     ************************************************/
    bool TEVAmpSpectrum::saveBinarySpectrumExtInfo(QDataStream& out)
    {
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpUnit) out << m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.ucAmpUnit;
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpLowerLimit) out << m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpLowerLimit;
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpUpperLimit) out << m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpUpperLimit;

        return true;
    }

    /************************************************
     * 函数名   : saveXMLSpectrumExtInfo
     * 输入参数 :
       XMLDocument& xmlDocumentObj: xml文档对象
       QDomElement& element: dom元素
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存XML可扩展信息段
     ************************************************/
    bool TEVAmpSpectrum::saveXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj, QDomElement& element)
    {
        xmlDocumentObj.beginElement(element);
        xmlDocumentObj.beginElement(STR_SPECTRUM_NODE_EXTINFORMATION);

        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpUnit) xmlDocumentObj.setValue(TEXT_AMP_UNIT, QString::number(m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.ucAmpUnit));
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpLowerLimit) xmlDocumentObj.setValue(TEXT_AMP_MIN, QString::number(m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpLowerLimit, 'f', 1));
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpUpperLimit) xmlDocumentObj.setValue(TEXT_AMP_MAX, QString::number(m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpUpperLimit, 'f', 1));

        return true;
    }

    /************************************************
     * 函数名   : saveJSONSpectrumExtInfo
     * 输入参数 :
       rapidjson::Document::AllocatorType& alloc:
       rapidjson::Value& jsonValue: json数据
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存JSON可扩展信息段
     ************************************************/
    bool TEVAmpSpectrum::saveJSONSpectrumExtInfo(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue)
    {
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpUnit) saveJSONField(m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.ucAmpUnit, TEXT_AMP_UNIT, alloc, jsonValue);
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpLowerLimit) saveJSONField(m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpLowerLimit, TEXT_AMP_MIN, alloc, jsonValue);
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpUpperLimit) saveJSONField(m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpUpperLimit, TEXT_AMP_MAX, alloc, jsonValue);

        return true;
    }

    /************************************************
     * 函数名   : saveBinarySpectrumData
     * 输入参数 :
       QDataStream& out: 输出流
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存二进制图谱数据段
     ************************************************/
    bool TEVAmpSpectrum::saveBinarySpectrumData(QDataStream& out)
    {
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipTEVAmpValue) out << m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVAmpValue;
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipTEVMaxValue) out << m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVMaxValue;
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipPulseCount) out << m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.iPulseCount;
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipSeverity) out << m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fSeverity;
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipBGFileName) DataSpecificationUtils::convertStringToBinary(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.qstrBGFileName, TYPE_ASCII, out, BG_FILE_NAME_LENTH);
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipDischargeSeverity) out << m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.ucDischargeSeverity;

        DataSpecificationUtils::saveBinaryReservedData(SPECTRUM_BASE_LENGTH - out.device()->size(), out);

        return true;
    }

    /************************************************
     * 函数名   : saveXMLSpectrumData
     * 输入参数 :
       XMLDocument& xmlDocumentObj: xml文档对象
       QDomElement& element: dom元素
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存XML图谱数据段
     ************************************************/
    bool TEVAmpSpectrum::saveXMLSpectrumData(XMLDocument& xmlDocumentObj, QDomElement& element)
    {
        xmlDocumentObj.beginElement(element);
        xmlDocumentObj.beginElement(STR_SPECTRUM_NODE_DATA);

        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipTEVAmpValue) xmlDocumentObj.setValue(TEXT_TEV_AMP, QString::number(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVAmpValue, 'f', 1));
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipTEVMaxValue) xmlDocumentObj.setValue(TEXT_TEV_MAX, QString::number(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVMaxValue, 'f', 1));
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipPulseCount) xmlDocumentObj.setValue(TEXT_PULSE_COUNT, QString::number(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.iPulseCount));
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipSeverity) xmlDocumentObj.setValue(TEXT_SEVERITY, QString::number(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fSeverity, 'f', 1));
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipBGFileName) xmlDocumentObj.setValue(TEXT_BG_FILE_NAME, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.qstrBGFileName);
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipDischargeSeverity) xmlDocumentObj.setValue(TEXT_DISCHARGE_SEVERITY, QString::number(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.ucDischargeSeverity));

        return true;
    }

    /************************************************
     * 函数名   : saveJSONSpectrumData
     * 输入参数 :
       rapidjson::Document::AllocatorType& alloc:
       rapidjson::Value& jsonValue: json数据
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 保存JSON图谱数据段
     ************************************************/
    bool TEVAmpSpectrum::saveJSONSpectrumData(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue)
    {
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipTEVAmpValue) saveJSONField(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVAmpValue, TEXT_TEV_AMP, alloc, jsonValue);
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipTEVMaxValue) saveJSONField(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVMaxValue, TEXT_TEV_MAX, alloc, jsonValue);
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipPulseCount) saveJSONField(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.iPulseCount, TEXT_PULSE_COUNT, alloc, jsonValue);
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipSeverity) saveJSONField(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fSeverity, TEXT_SEVERITY, alloc, jsonValue);
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipBGFileName) saveJSONField(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.qstrBGFileName, TEXT_BG_FILE_NAME, alloc, jsonValue);
        if (!m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipDischargeSeverity) saveJSONField(m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.ucDischargeSeverity, TEXT_DISCHARGE_SEVERITY, alloc, jsonValue);
        return true;
    }

    /************************************************
     * 函数名   : parseBinarySpectrumExtInfo
     * 输入参数 :
       QDataStream& in: 输入流
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析二进制可扩展信息段
     ************************************************/
    bool TEVAmpSpectrum::parseBinarySpectrumExtInfo(QDataStream& in)
    {
        if (!in.device()->seek(SPECTRUM_HEADER_LENGTH))
        {
            qDebug() << "TEVAmpSpectrum::parseBinarySpectrumExtInfo failed";
            return false;
        }

        parseBinaryField(in, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpUnit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.ucAmpUnit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bHasAmpUnit);
        parseBinaryField(in, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpLowerLimit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpLowerLimit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bHasAmpLowerLimit);
        parseBinaryField(in, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bSkipAmpUpperLimit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpUpperLimit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bHasAmpUpperLimit);

        return in.status() == QDataStream::Ok;
    }

    /************************************************
     * 函数名   : parseXMLSpectrumExtInfo
     * 输入参数 :
       XMLDocument& xmlDocumentObj: xml文档对象
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析XML可扩展信息段
     ************************************************/
    bool TEVAmpSpectrum::parseXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj)
    {
        xmlDocumentObj.beginElement(spectrumName());
        xmlDocumentObj.beginElement(STR_SPECTRUM_NODE_EXTINFORMATION);

        parseXMLField(xmlDocumentObj, TEXT_AMP_UNIT, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.ucAmpUnit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bHasAmpUnit);
        parseXMLField(xmlDocumentObj, TEXT_AMP_MIN, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpLowerLimit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bHasAmpLowerLimit);
        parseXMLField(xmlDocumentObj, TEXT_AMP_MAX, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpUpperLimit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bHasAmpUpperLimit);

        xmlDocumentObj.endElement();
        xmlDocumentObj.endElement();

        return true;
    }

    /************************************************
     * 函数名   : parseJSONSpectrumExtInfo
     * 输入参数 :
       const rapidjson::Value& jsonValue: json数据
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析JSON可扩展信息段
     ************************************************/
    bool TEVAmpSpectrum::parseJSONSpectrumExtInfo(const rapidjson::Value& jsonValue)
    {
        parseJSONField(jsonValue, TEXT_AMP_UNIT, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.ucAmpUnit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bHasAmpUnit);
        parseJSONField(jsonValue, TEXT_AMP_MIN, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpLowerLimit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bHasAmpLowerLimit);
        parseJSONField(jsonValue, TEXT_AMP_MAX, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.fAmpUpperLimit, m_pTEVAmpSpectrumPrivate->m_stTEVAmpExtInformationPrivate.bHasAmpUpperLimit);

        return true;
    }

    /************************************************
     * 函数名   : parseBinarySpectrumData
     * 输入参数 :
       QDataStream& in: 输入流
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析二进制图谱数据段
     ************************************************/
    bool TEVAmpSpectrum::parseBinarySpectrumData(QDataStream& in)
    {
        if (!in.device()->seek(TEV_AMP_DATA_START_POS))
        {
            qDebug() << "TEVAmpSpectrum::parseBinarySpectrumData failed";
            return false;
        }

        parseBinaryField(in, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipTEVAmpValue, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVAmpValue, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasTEVAmpValue);
        parseBinaryField(in, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipTEVMaxValue, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVMaxValue, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasTEVMaxValue);
        parseBinaryField(in, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipPulseCount, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.iPulseCount, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasPulseCount);
        parseBinaryField(in, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipSeverity, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fSeverity, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasSeverity);
        parseBinaryField(in, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipBGFileName, DataSpecificationNS::TYPE_ASCII, DataSpecificationNS::BG_FILE_NAME_LENTH, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.qstrBGFileName, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasBGFileName);
        parseBinaryField(in, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bSkipDischargeSeverity, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.ucDischargeSeverity, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasDischargeSeverity);

        return in.status() == QDataStream::Ok;
    }

    /************************************************
     * 函数名   : parseXMLSpectrumData
     * 输入参数 :
       XMLDocument& xmlDocumentObj: xml文档对象
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析XML图谱数据段
     ************************************************/
    bool TEVAmpSpectrum::parseXMLSpectrumData(XMLDocument& xmlDocumentObj)
    {
        xmlDocumentObj.beginElement(spectrumName());
        xmlDocumentObj.beginElement(STR_SPECTRUM_NODE_DATA);

        parseXMLField(xmlDocumentObj, TEXT_TEV_AMP, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVAmpValue, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasTEVAmpValue);
        parseXMLField(xmlDocumentObj, TEXT_TEV_MAX, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVMaxValue, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasTEVMaxValue);
        parseXMLField(xmlDocumentObj, TEXT_PULSE_COUNT, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.iPulseCount, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasPulseCount);
        parseXMLField(xmlDocumentObj, TEXT_SEVERITY, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fSeverity, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasSeverity);
        parseXMLField(xmlDocumentObj, TEXT_BG_FILE_NAME, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.qstrBGFileName, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasBGFileName);
        parseXMLField(xmlDocumentObj, TEXT_DISCHARGE_SEVERITY, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.ucDischargeSeverity, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasDischargeSeverity);

        xmlDocumentObj.endElement();
        xmlDocumentObj.endElement();

        return true;
    }

    /************************************************
     * 函数名   : parseJSONSpectrumData
     * 输入参数 :
       const rapidjson::Value& jsonValue: json数据
     * 输出参数 : NULL
     * 返回值   : bool
     * 功能     : 解析JSON图谱数据段
     ************************************************/
    bool TEVAmpSpectrum::parseJSONSpectrumData(const rapidjson::Value& jsonValue)
    {
        parseJSONField(jsonValue, TEXT_TEV_AMP, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVAmpValue, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasTEVAmpValue);
        parseJSONField(jsonValue, TEXT_TEV_MAX, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fTEVMaxValue, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasTEVMaxValue);
        parseJSONField(jsonValue, TEXT_PULSE_COUNT, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.iPulseCount, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasPulseCount);
        parseJSONField(jsonValue, TEXT_SEVERITY, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.fSeverity, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasSeverity);
        parseJSONField(jsonValue, TEXT_BG_FILE_NAME, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.qstrBGFileName, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasBGFileName);
        parseJSONField(jsonValue, TEXT_DISCHARGE_SEVERITY, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.ucDischargeSeverity, m_pTEVAmpSpectrumPrivate->m_stTEVAmpDataPrivate.bHasDischargeSeverity);

        return true;
    }

}
