#include "AEFlyView.h"
#include "ae/AEView.h"
#include "ae/AEViewConfig.h"
#include "controlButton/ControlButtonInfo.h"
#include "buttonBar/PushButtonBar.h"
#include "controlButton/CmdButton.h"
#include "controlButton/RadioButton.h"
#include "controlButton/SliderButton.h"
#include "window/Window.h"
#include "rfid/RFIDScanView.h"
#include "rfid/RFIDReadView.h"
#include "playbackView/PlayBackView.h"
#include "deleteDataView/DeleteDataView.h"
#include "recordplay/RecordView.h"
#include "messageBox/msgbox.h"
#include "ae/AEConfig.h"
#include "ae/dataSave/AEFlightDataSave.h"
#include "ae/dataSave/AERecordDataSave.h"
#include "functionconfigmanager/funccfgsrvmanager.h"
#include "AEFlyPlayBackView.h"
#include "appconfig.h"
#include <datafile.h>
#include "equipmentinfo.h"
#include "appconfig.h"
#include "recordplay/playview.h"
#include "fileoper/fileoperutil.h"

const int INVALID_USER = -1;
const UINT8 MINUTE_PER_HOUR = 60;
const UINT8 SECOND_PER_MINUTE = 60;

typedef enum _AEFlyButton
{
    BUTTON_AE_SAMPLE = 0,//采样
    BUTTON_AE_GAIN,//增益
    BUTTON_AE_TRIGGER_VALUE, //触发值
    BUTTON_AE_OPEN_DOOR_TIME,//AE开门时间
    BUTTON_AE_SAVE_DATA,//保存数据
    BUTTON_AE_CLOSE_DOOR_TIME,//AE关门时间
    BUTTON_AE_RECORD,//录音启停
    BUTTON_MORE_CONFIG,//更多配置
    BUTTON_AE_TIME_INTERVAL,//时间间隔
    BUTTON_AE_VOLUME,//设置音量
    BUTTON_AE_CHANNEL,//通道
    BUTTON_AE_FILTER,//带宽模式
    BUTTON_AE_RFID_SAVE,//RFID扫描保存
    BUTTON_AE_LOAD_RECORD_DATA, //载入录音数据
    BUTTON_AE_DELETE_RECORD_DATA, //删除录音数据
    BUTTON_AE_LOAD_DATA,//载入数据
    BUTTON_AE_DELETE_DATA,//删除数据
    BUTTON_AE_RESTORE_DEFAULT,//恢复默认参数
}AEFlyButton;

//增益
const ButtonInfo::RadioValueConfig s_AEGainCfg =
{
    AE::TEXT_GAIN_OPTIONS, sizeof(AE::TEXT_GAIN_OPTIONS)/sizeof(char*)
};

//触发值
const ButtonInfo::RadioValueConfig s_AETriggerCfg =
{
    NULL, AE::TRIGGER_LEVEL_COUNT
};

//开门时间
const ButtonInfo::GroupSliderValueConfig s_OpenDoorTimeButtonInfo =
{
    AE::OPEN_DOOR_TIME, AE::OPEN_DOOR_TIME_COUNT
};

//关门时间
const ButtonInfo::GroupSliderValueConfig s_AECloseDoorButtonInfo =
{
    AE::CLOSE_DOOR_TIME, AE::CLOSE_DOOR_TIME_COUNT
};

//时间间隔
const ButtonInfo::GroupSliderValueConfig s_TimeIntervalButtonInfo =
{
    AE::TIME_INTERVAL_VALUE, AE::TIME_INTERVAL_COUNT
};

//通道
const ButtonInfo::RadioValueConfig s_AEChannel =
{
    AE::TEXT_CHANNELS, sizeof(AE::TEXT_CHANNELS)/sizeof(char*)
};

//带宽模式
const ButtonInfo::RadioValueConfig s_AEFilterCfg =
{
    AE::TEXT_FILTER_OPTIONS, sizeof(AE::TEXT_FILTER_OPTIONS)/sizeof(char*)
};


//控制按钮定义
const ButtonInfo::Info s_AEButtonInfo[] =
{
    { BUTTON_AE_SAMPLE, { ButtonInfo::COMMAND, AE::TEXT_SAMPLE, NULL, ":/images/sampleControl/sample.png", NULL } },//采样
    { BUTTON_AE_GAIN, { ButtonInfo::RADIO, AE::TEXT_GAIN, NULL, ":/images/sampleControl/gain.png", &s_AEGainCfg } },//增益
    { BUTTON_AE_TRIGGER_VALUE, { ButtonInfo::RADIO, AE::TEXT_TRIGGER_VALUE, NULL, ":/images/sampleControl/triggerValue.png", &s_AETriggerCfg } },//触发值
    { BUTTON_AE_TIME_INTERVAL, {ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_TIME_INTERVAL, AE::TEXT_MS, ":/images/sampleControl/timeInterval.png", &s_TimeIntervalButtonInfo}},//时间间隔
    { BUTTON_AE_SAVE_DATA, { ButtonInfo::COMMAND, AE::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL } },//保存数据
    { BUTTON_AE_OPEN_DOOR_TIME, { ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_OPEN_DOOR_TIME, AE::TEXT_US, ":/images/sampleControl/openDoorTime.png", &s_OpenDoorTimeButtonInfo } },//开门时间
    { BUTTON_AE_CLOSE_DOOR_TIME, { ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_CLOSE_DOOR_TIME, AE::TEXT_MS, ":/images/sampleControl/closeDoorTime.png", &s_AECloseDoorButtonInfo } },//关门时间
    { BUTTON_MORE_CONFIG, { ButtonInfo::COMMAND, AE::TEXT_MORE_CONFIG, NULL, ":/images/sampleControl/moreconfig.png", NULL } },//更多配置
};

//"更多.."按钮定义
//const ButtonInfo::Info g_AETimeIntervalBtn = {BUTTON_AE_TIME_INTERVAL, {ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_TIME_INTERVAL, AE::TEXT_MS, ":/images/sampleControl/timeInterval.png", &s_TimeIntervalButtonInfo}};//时间间隔
const ButtonInfo::Info g_AEVolumeBtn = {BUTTON_AE_VOLUME, {ButtonInfo::FIXED_STEP_SLIDER, AE::TEXT_AE_VOLUME, NULL, "", &AE::g_VolumeCfg}};//音量
const ButtonInfo::Info g_AEChannelBtn = {BUTTON_AE_CHANNEL, {ButtonInfo::RADIO, AE::TEXT_CHANNEL, NULL, "", &s_AEChannel}};//通道
const ButtonInfo::Info g_AEFilterBtn = {BUTTON_AE_FILTER, {ButtonInfo::RADIO, AE::TEXT_FILTER, NULL, "", &s_AEFilterCfg}};//带宽模式;
const ButtonInfo::Info g_AERFIDSaveBtn = {BUTTON_AE_RFID_SAVE, {ButtonInfo::COMMAND, AE::TEXT_RFID_SAVE, NULL, ":/images/sampleControl/save.png", NULL}};//RFID扫描保存
const ButtonInfo::Info g_AELoadRecordDataBtn = {BUTTON_AE_LOAD_RECORD_DATA, {ButtonInfo::COMMAND, AE::TEXT_LOAD_RECORD_DATA, NULL, ":/images/sampleControl/importData.png", NULL}};//载入数据
const ButtonInfo::Info g_AEDeleteRecordDataBtn = {BUTTON_AE_DELETE_RECORD_DATA, {ButtonInfo::COMMAND, AE::TEXT_DELETE_RECORD_DATA, NULL, ":/images/sampleControl/triggerValue.png", NULL}};//删除数据
const ButtonInfo::Info g_AELoadDataBtn = {BUTTON_AE_LOAD_DATA, {ButtonInfo::COMMAND, AE::TEXT_LOAD_DATA, NULL, ":/images/sampleControl/importData.png", NULL}};//载入数据
const ButtonInfo::Info g_AEDeleteDataBtn = {BUTTON_AE_DELETE_DATA, {ButtonInfo::COMMAND, AE::TEXT_DELETE_DATA, NULL, ":/images/sampleControl/triggerValue.png", NULL}};//删除数据
const ButtonInfo::Info g_AERecordBtn = {BUTTON_AE_RECORD, {ButtonInfo::COMMAND, AE::TEXT_START_RECORD, NULL, ":/images/sampleControl/record.png", NULL}};//录音启停
const ButtonInfo::Info g_AERestoreDataBtn = {BUTTON_AE_RESTORE_DEFAULT, {ButtonInfo::COMMAND, AE::TEXT_RESTORE_DEFAULT, NULL, ":/images/sampleControl/restoreDefault.png", NULL}};//恢复默认


/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
AEFlyView::AEFlyView(const QString &strTitle, QWidget *parent) :
    AEFlyViewBase( strTitle,parent)
{
    initBtnBarInfo();

    //初始化使用的数据
    initDatas();

    //新建图谱
    m_pChart = createChart(parent);
    setChart( m_pChart );

    //触发值列表根据增益、量纲动态变化
    resetTriggerList( m_eGain );

    //设置数据
    setButtonBarDatas();
    setChartDatas();

    //设置工作参数
    setAllWorkingSet();

    //设置音量
    RecordPlayService::instance()->setVolume(m_ucVolume);

    connect(RecordPlayService::instance(), SIGNAL(sigSysVolume(quint8)), this, SLOT(onSysVolume(quint8)));
}

/*************************************************
功能： 析构
*************************************************************/
AEFlyView::~AEFlyView( )
{
    saveConfig();//存储到配置文件中
    stopSampleService();

    if(m_pMoreBtnInfo)
    {
        delete [] m_pMoreBtnInfo;
        m_pMoreBtnInfo = NULL;
    }
}

/*****************************************
 * 功能：初始化按钮栏信息
 * **********************************************/
void AEFlyView::initBtnBarInfo()
{
    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar(AE::CONTEXT, s_AEButtonInfo, sizeof(s_AEButtonInfo) / sizeof(ButtonInfo::Info));
    m_pSampleBtn = buttonBar()->button(BUTTON_AE_SAMPLE);
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_GAIN)))->setPopupMode(PopupWidget::SWITCH_MODE);

    m_pMoreBtnInfo = NULL;
    m_qui8MoreBtnCnt = 0;

    QVector<ButtonInfo::Info> qvtMoreBtnInfos;
    qvtMoreBtnInfos.clear();

    //qvtMoreBtnInfos.push_back(g_AETimeIntervalBtn);
    qvtMoreBtnInfos.push_back(g_AEVolumeBtn);
    FuncConfigManagerNS::FunctionInfo stAEWirelessInfo;
    stAEWirelessInfo.iFuncID = FuncConfigManagerNS::AE_WIRELESS;
    stAEWirelessInfo.iParentID = FuncConfigManagerNS::AE;

    FuncConfigManagerNS::ConfigInfo stConfigInfo;
    FuncCfgSrvManager::instance()->getConfigInfo(stConfigInfo);
    int iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAEWirelessInfo);
    if(0 <= iIndex && iIndex < stConfigInfo.qvtFuncInfos.size())
    {
        if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
        {
            qvtMoreBtnInfos.push_back(g_AEChannelBtn);
        }
    }

#ifdef _R3_DEFINED_
    qvtMoreBtnInfos.push_back(g_AEFilterBtn);
#endif
    qvtMoreBtnInfos.push_back(g_AERFIDSaveBtn);
	qvtMoreBtnInfos.push_back(g_AELoadRecordDataBtn);
    qvtMoreBtnInfos.push_back(g_AEDeleteRecordDataBtn);
    qvtMoreBtnInfos.push_back(g_AELoadDataBtn);
    qvtMoreBtnInfos.push_back(g_AEDeleteDataBtn);
    qvtMoreBtnInfos.push_back(g_AERecordBtn);
    qvtMoreBtnInfos.push_back(g_AERestoreDataBtn);

    m_qui8MoreBtnCnt = static_cast<quint8>(qvtMoreBtnInfos.size());
    m_pMoreBtnInfo = new ButtonInfo::Info[m_qui8MoreBtnCnt];
    for (int i = 0; i < m_qui8MoreBtnCnt; ++i)
    {
        m_pMoreBtnInfo[i] = qvtMoreBtnInfos[i];
    }

    // 创建更多设置栏
    createMoreConfigButtonBar(AE::CONTEXT, m_pMoreBtnInfo, m_qui8MoreBtnCnt);
}

/*************************************************
功能： 初始化参数
*************************************************************/
void AEFlyView::initParameters()
{
    int iGroup = AE::GROUP_AE_FLY;
    m_pConfig = ConfigManager::instance()->config();
    m_pConfig->beginGroup( Module::GROUP_AE );

    m_eGain = (AE::GainType)m_pConfig->value( AE::KEY_GAIN ).toUInt();
    m_eFilter = (AEFilter) m_pConfig->value(AE::KEY_FILTER).toUInt();
    m_eTriggerValue = (AE::TriggerValue)m_pConfig->value( AE::KEY_TRIGGER_VALUE, iGroup ).toUInt();
    m_iTriggerValue = AE::getThreshold( AE::UNIT_DEFAULT,m_eGain,m_eTriggerValue );
    m_usOpenDoorTime = m_pConfig->value( AE::KEY_OPEN_DOOR_TIME, iGroup ).toUInt();
    m_usCloseDoorTime = m_pConfig->value( AE::KEY_CLOSE_DOOR_TIME, iGroup ).toUInt();
    m_iTimeInterval = m_pConfig->value( AE::KEY_TIME_INTERVAL, iGroup ).toUInt();
    if(m_eChannel != AE::SURFACE_MOUNT)
    {
        m_eChannel =(AE::ChannelType) m_pConfig->value( AE::KEY_CHANNEL_TYPE ).toUInt();
    }
    m_pConfig->endGroup();

    m_pConfig->beginGroup( Module::GROUP_APP );
    m_ucVolume = m_pConfig->value(APPConfig::KEY_SYSTEM_VOLUME).toUInt();
    m_pConfig->endGroup();

    m_usPulseCnt = 0;

    return;
}

AEFlyChart *AEFlyView::createChart(QWidget *parent)
{
    Q_UNUSED(parent);
    AEFlyChart *pChart = new AEFlyChart;
    return pChart;
}

/*************************************************
功能： 初始化图谱
*************************************************************/
void AEFlyView::initChart()
{
    /*暂停图谱刷新*/
    m_pChart->setRunningMode(isSampling());
    /*清空计数值*/
    m_usPulseCnt = 0;
    m_pChart->setPulseCounting(m_usPulseCnt);
    /*清空图谱*/
    m_pChart->clear();
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void AEFlyView::onButtonValueChanged( int id, int iValue )
{
    bool bIsConfigChanged = true;
    switch( id )
    {
    case BUTTON_AE_GAIN://增益
    {
        if( m_eGain != (AE::GainType)iValue )
        {
            m_eGain = (AE::GainType)iValue;
            if(AEFlyService* pService = getAEFlyService())
            {
                pService->setGain( m_eGain );
            }
            m_pChart->setGain( m_eGain );

            resetTriggerList( m_eGain );//增益变化时，重置触发值列表
            resetTriggerValue();
        }
        else
        {
            // not changed
        }

    }
        break;
    case BUTTON_AE_TRIGGER_VALUE://触发值
    {
        if( m_eTriggerValue != (AE::TriggerValue)iValue )
        {
            m_eTriggerValue = (AE::TriggerValue)iValue;
            resetTriggerValue();
        }
    }
        break;
    case BUTTON_AE_OPEN_DOOR_TIME://开门时间
    {
        if( m_usOpenDoorTime != iValue )
        {
            m_usOpenDoorTime = iValue;
            if(AEFlyService* pService = getAEFlyService())
            {
                pService->setOpenDoorTime( m_usOpenDoorTime );
            }
        }
    }
        break;
    case BUTTON_AE_CLOSE_DOOR_TIME://关门时间
    {
        if( m_usCloseDoorTime != iValue )
        {
            m_usCloseDoorTime = iValue;
            if(AEFlyService* pService = getAEFlyService())
            {
                pService->setCloseDoorTime( AE::MODE_FLY,m_usCloseDoorTime );
            }
        }
    }
        break;
    case BUTTON_AE_VOLUME://音量
    {
        if((int)m_ucVolume != iValue)
        {
            m_ucVolume = (quint8)iValue;
            RecordPlayService::instance()->setVolume(m_ucVolume);

            ConfigInstance *pConfig = ConfigManager::instance()->config();
            pConfig->beginGroup(Module::GROUP_APP);
            pConfig->setValue(iValue, APPConfig::KEY_SYSTEM_VOLUME);
            pConfig->endGroup();
        }
    }
        break;
    case BUTTON_AE_TIME_INTERVAL://时间间隔
    {
        if( m_iTimeInterval != iValue )
        {
            m_iTimeInterval = iValue;
            m_pChart->setTimeInterval( m_iTimeInterval );
            if(AEFlyService* pService = getAEFlyService())
            {
                pService->setTimeInterval( m_iTimeInterval );
            }
        }
    }
        break;
    case BUTTON_AE_CHANNEL://通道
    {
        //给底层通道的参数， 空声表贴都是同样处理的方式，但底层会返回真实采集的空声或者表贴，采集界面将其显示出来
        stopSampleService();

        if(AEFlyService* pService = getAEFlyService())
        {
            if(iValue == 1)
            {
                pService->setChannel( AE::WIRELESS );
                m_eChannel = AE::WIRELESS;
                m_pChart->setChannel( m_eChannel );
            }
            else
            {
                pService->setChannel( AE::AIR_SOUND );
                m_eChannel = AE::AIR_SOUND;
                m_pChart->setChannel( m_eChannel );
            }
        }

        startSampleService();
    }
        break;
    case BUTTON_AE_FILTER://带宽模式
    {
        m_eFilter = (AEFilter)iValue;
        if(AEFlyService* pService = getAEFlyService())
        {
            pService->setFilter(m_eFilter);
        }
    }
        break;
    default:
    {
        bIsConfigChanged = false;
    }
        return;
    }
    if( bIsConfigChanged )
    {
        stopSample();
        initChart();
    }
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void AEFlyView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case BUTTON_MORE_CONFIG://更多设置
    {
        showMoreConfigButtonBar();
    }
        break;
    case BUTTON_AE_SAMPLE://采样
    {
        if(!isSampling()) //图谱刷新暂停后，点击采样，处理；否则不处理
        {
            startSample();
            initChart();
        }
        else
        {
            stopSample();
        }
    }
        break;
    case BUTTON_AE_SAVE_DATA://保存数据
    {
        pressSaveData();
    }
        break;
	case BUTTON_AE_LOAD_RECORD_DATA://载入录音数据
    {
        bool bIsSamplingStop = false;
        if(isSampling())
        {
            stopSample();
            bIsSamplingStop = true;
        }

        loadRecordData();

        if( bIsSamplingStop )
        {
            //开始采集
            startSample();
        }
    }
        break;
    case BUTTON_AE_DELETE_RECORD_DATA://删除录音数据
    {
        deleteRecordData();
    }
        break;
    case BUTTON_AE_DELETE_DATA://删除数据
    {
        deleteData();
    }
        break;
    case BUTTON_AE_RESTORE_DEFAULT://恢复默认参数
    {
        restoreDefault();
    }
        break;
    case BUTTON_AE_RECORD://录音
    {
        //recorder();
        if(m_bRecording)
        {
            //本次为停止录音
            if(stopAERecord())
            {
                this->buttonBar()->button(BUTTON_AE_RECORD)->setTitle(AE_TRANSLATE(AE::TEXT_START_RECORD));

				bool bIsSamplingStop = false;
                if(isSampling())
                {
                    stopSample();
		            bIsSamplingStop = true;
		        }
				
				saveRecorded(m_qstrAttachPath, m_uiTotalRecordingTime);

				if( bIsSamplingStop )
		        {
                    //开始采集
                    startSample();
		        }
            }
        }
        else
        {
            //本次为开始录音
            if(startAERecord(RecordPlay::AE_FLY))
            {
                //this->buttonBar()->button(BUTTON_AE_RECORD)->setTitle(AE_TRANSLATE(AE::TEXT_STOP_RECORD));
				m_uiTotalRecordingTime = 0;
        	}
        }
    }
        break;
    case BUTTON_AE_LOAD_DATA://载入数据
    {
        bool bIsSamplingStop = false;
        if(isSampling())
        {
            stopSample();
            bIsSamplingStop = true;
        }

        loadData();

        if( bIsSamplingStop )
        {
            //开始采集
            startSample();
        }
    }
        break;
    case BUTTON_AE_RFID_SAVE://RFID扫描保存
    {
        bool bIsSamplingStop = false;
        if(isSampling())
        {
            stopSample();
            bIsSamplingStop = true;
        }

        RFIDSaveData();

        if( bIsSamplingStop )
        {
            //开始采集
            startSample();
        }
    }
        break;
    default:
        break;
    }

    return;
}

/*************************************************
功能： 开始采集
*************************************************************/
void AEFlyView::startSample()
{
    if(!isSampling())
    {
        startSampleService();
        m_pChart->setRunningMode(isSampling());
        m_pSampleBtn->setTitle(QObject::trUtf8("Stop"));
    }
}

/*************************************************
功能： 停止采集
*************************************************************/
void AEFlyView::stopSample()
{
    if(isSampling())
    {
        stopSampleService();
        m_pChart->setRunningMode(isSampling());
        m_pSampleBtn->setTitle(QObject::trUtf8("Sample"));
    }
}

/*************************************************
功能： 重新设置触发值，包括提供给service和chart两部分
*************************************************************/
void AEFlyView::resetTriggerValue( void )
{
    if(AEFlyService* pService = getAEFlyService())
    {
        pService->setTriggerValue( m_eTriggerValue );
    }
    m_iTriggerValue = AE::getThreshold( AE::UNIT_DEFAULT,m_eGain,m_eTriggerValue );
    m_pChart->setTrigger( m_iTriggerValue );
}

/*************************************************
功能： 删除数据
*************************************************************/
void AEFlyView::deleteData()
{
    QStringList nameFilters;
    nameFilters << AE_FLIGHT_FILE_NAME_SUFFIX;
    nameFilters << ".png";

    QString filePath = DATA_STORAGE_PATH + "/" + AE_FLIGHT_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if( !infoList.isEmpty() )
    {
        DeleteDataView* pView = new DeleteDataView( filePath,
                                                    AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_DELETE_DATA),
                                                    nameFilters
                                                    );

        pView->setRelatedSuffix( AE_FLIGHT_FILE_NAME_SUFFIX, QStringList(".png") );
        pView->exec();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
}

/*************************************************
功能： 槽，响应读取的数据
输入参数：
            data -- 飞行数据
            id -- 用户id
*************************************************************/
void AEFlyView::onDataRead(QVector<AE::FlyData> datas, MultiServiceNS::USERID userId)
{
    // 因为停止采样的操作是异步的，故有可能下发采集命令后仍有数据上传
    if((getUserId() != INVALID_USER) && (getUserId() == userId) && isSampling())
    {
        for(int i = 0, iSize = datas.size(); i < iSize; ++i)
        {
            UINT32 uiPulseInterval = datas.at(i).uiPulseInterval;  // 脉冲间隔时间 us
            float fPeakValue = datas.at(i).fPeakValue;
            if( fPeakValue  > m_iTriggerValue )
            {
                // 脉冲计数值超出累积上限，清零计数，暂停采集
                if( m_usPulseCnt >= PULSE_COUNT_MAX )
                {
                    stopSample();

                    m_usPulseCnt = PULSE_COUNT_MAX;
                    m_pChart->setPulseCounting(m_usPulseCnt);
                    break;
                }
                else
                {
                    if( m_pChart->addSamples( uiPulseInterval,fPeakValue ) )
                    {
                        ++m_usPulseCnt;
                        m_pChart->setPulseCounting(m_usPulseCnt);
                    }
                    m_pChart->setRunningMode(isSampling());
                }
            }

        }
    }
    else
    {
        //illegal
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
    }
}

/*************************************************
功能： 槽，响应通道变化
输入参数：
        eChannel -- 变化后的通道
*************************************************************/
void AEFlyView::onChannelChanged( AE::ChannelType eChannel )
{
    if( m_eChannel != eChannel )
    {
        m_eChannel = eChannel;
        m_pChart->setChannel( eChannel );
    }
}

/*************************************************
功能： 初始化数据
*************************************************************/
void AEFlyView::initDatas()
{
    m_pChart = NULL;

    initParameters();
    if (AEFlyService* pService = getAEFlyService())
    {
        pService->setTimeInterval(m_iTimeInterval);
    }

    MultiServiceNS::SampleUser user;
    user.eSampleType = MultiServiceNS::SPECTRUM_AE_FLY;
    addUser( user );
}

/*************************************************
功能： 设置按钮栏数据
*************************************************************/
void AEFlyView::setButtonBarDatas()
{
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_OPEN_DOOR_TIME)))->setValue( m_usOpenDoorTime );
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_GAIN)))->setValue( m_eGain );
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_CLOSE_DOOR_TIME)))->setValue( m_usCloseDoorTime );
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_TIME_INTERVAL)))->setValue( m_iTimeInterval );
    ((PopupButton*)(buttonBar()->button(BUTTON_AE_TRIGGER_VALUE)))->setValue( m_eTriggerValue );
    if(((PopupButton*)(buttonBar()->button(BUTTON_AE_FILTER))))
    {
        ((PopupButton*)(buttonBar()->button(BUTTON_AE_FILTER)))->setValue(m_eFilter);
    }

    int iChannelVal = (AE::WIRELESS == m_eChannel) ? (AE::WIRELESS - 1) : AE::AIR_SOUND;
    if(((PopupButton*)(buttonBar()->button(BUTTON_AE_CHANNEL))))
    {
        ((PopupButton*)(buttonBar()->button(BUTTON_AE_CHANNEL)))->setValue(iChannelVal);
    }

    ((PopupButton*)(buttonBar()->button(BUTTON_AE_VOLUME)))->setValue(m_ucVolume);

    return;
}

/*************************************************
功能： 设置表格数据
*************************************************************/
void AEFlyView::setChartDatas()
{
    m_pChart->setGain( m_eGain );
    m_pChart->setTimeInterval( m_iTimeInterval );
    m_pChart->setChannel( m_eChannel );
    m_pChart->setPulseCounting( m_usPulseCnt );
    m_pChart->setRunningMode(isSampling());
    m_pChart->setTrigger( m_iTriggerValue );
}

/*************************************************
功能： 载入数据
*************************************************************/
void AEFlyView::loadData()
{
    QString filePath = DATA_STORAGE_PATH + "/" + AE_FLIGHT_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if( !infoList.isEmpty() )
    {
        PlayBackView* pView = new PlayBackView( filePath, AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_FLY_CHART), AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_LOAD_DATA) );
        pView->addPlayback( AE_FLIGHT_FILE_NAME_SUFFIX, new AEFlyPlayBackView() );
        connect(pView, SIGNAL(sigEnterPressed()), this, SLOT(hideMoreConfigButtonBar()));
        pView->exec();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!") );
    }
}

/*************************************************
功能： 录音
*************************************************************/
void AEFlyView::recorder()
{
    //RecordPlayService::instance()->stopListenAE();
    //RecordPlayService::instance()->stop();
    RecordView *pRecordView = new RecordView(QObject::trUtf8("Record"), RecordPlay::AE_RECORD, RecordPlay::AE_FLY);
    connect( pRecordView, SIGNAL(sigRecorded(const QString&, const uint)), this, SLOT(onRecorded(const QString&, const uint)) );
    connect( pRecordView, SIGNAL(sigClosed()), this, SLOT(onStartListenAE()) );//关闭时恢复监听超声波
    pRecordView->show();
    return;
}

/*************************************************
功能： 槽，响应录音界面关闭后的处理（重新开始监听超声波）
*************************************************************/
void AEFlyView::onStartListenAE( )
{
    //RecordPlayService::instance()->start();
    //RecordPlayService::instance()->startListenAE();//开始监听超声波
    //RecordPlayService::instance()->setVolume(m_ucVolume);
    return;
}

/*************************************************
功能： 槽，响应录音完成
输入参数：
        strRecordFilePath -- 录音文件保存的路径
            uint -- 录音时长
*************************************************************/
void AEFlyView::onRecorded(const QString &strRecordFilePath, const uint uiRecordingTime)
{
    AERecordDataInfo sSaveDataInfo;
    sSaveDataInfo.stHeadInfo.strSubstationName = "";
    sSaveDataInfo.stHeadInfo.strDeviceName = "";
    sSaveDataInfo.stHeadInfo.generationDateTime = QDateTime::currentDateTime();
    sSaveDataInfo.stHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_UNRECORD;

    sSaveDataInfo.stHeadInfo.eCode = DataFileNS::SPECTRUM_CODE_RECORD_AE;
    sSaveDataInfo.stHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;
    sSaveDataInfo.stHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;
    sSaveDataInfo.stHeadInfo.ucTestChannelSign = (m_eChannel == AE::WIRELESS) ? 1 : 0;

    sSaveDataInfo.uiDuration = uiRecordingTime;
    sSaveDataInfo.eGainType = DataFileNS::GAIN_TYPE_AMPLIFY_RATIO;
    sSaveDataInfo.sGain =  AE::g_ausGainValues[m_eGain];
    sSaveDataInfo.eSyncSource = DataFileNS::SYNC_SOURCE_POWER;
    sSaveDataInfo.fSyncFreq = -1;
    sSaveDataInfo.eTransformerType = AE::transformChannelToDataFile(m_eChannel);
    sSaveDataInfo.uiPulseOpenTime = m_usOpenDoorTime;
    sSaveDataInfo.uiPulseShutTime = m_usCloseDoorTime;
    sSaveDataInfo.fPulseTriggerThreshold = m_iTriggerValue;

    sSaveDataInfo.strFilePath = strRecordFilePath;

    QFileInfo qfDataInfo(strRecordFilePath);
    QString qstrFileName = qfDataInfo.fileName();
    qstrFileName = qstrFileName.left(qstrFileName.lastIndexOf('.'));

    AERecordDataSave hDataSave;
    QString qstrDataFile = hDataSave.saveData(&sSaveDataInfo, qfDataInfo.absolutePath(), qstrFileName);

    QFileInfo fileInfo(qstrDataFile);
    QString strFileName = fileInfo.fileName();
    QString strInfo = qstrDataFile.isEmpty() ? QObject::trUtf8("Save failure!") : strFileName;

    processTooLongMsgText(strInfo);
    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x() + pBtnBar->width() / 2);
    centerPoint.setY(pBtnBar->pos().y() + pBtnBar->height() / 2);
    MsgBox::informationWithoutAutoAccept("", strInfo, centerPoint);
    return;
}

/*************************************************
功能： 槽，响应系统音量变化
输入参数：
        qui8Volume -- 系统音量
*************************************************************/
void AEFlyView::onSysVolume(quint8 qui8Volume)
{
    if(m_ucVolume != qui8Volume)
    {
        m_ucVolume = qui8Volume;
        ((PopupButton*)(buttonBar()->button(BUTTON_AE_VOLUME)))->setValue(m_ucVolume);
    }
}

/*************************************************
功能： 恢复默认
*************************************************************/
void AEFlyView::restoreDefault()
{
    if(MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to restore default settings?")))
    {
        stopSample();
        m_pChart->clear();

        int iGroup = AE::GROUP_AE_FLY;
        m_pConfig->beginGroup( Module::GROUP_AE );
        QVector<Config::GroupKey> totalKeys;
        totalKeys << Config::GroupKey( AE::KEY_GAIN );
        totalKeys << Config::GroupKey( AE::KEY_FILTER);
        totalKeys << Config::GroupKey( AE::KEY_TRIGGER_VALUE, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_OPEN_DOOR_TIME, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_CLOSE_DOOR_TIME, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_TIME_INTERVAL, iGroup );
        totalKeys << Config::GroupKey( AE::KEY_CHANNEL_TYPE );

        m_pConfig->restoreDefault( totalKeys );
        m_pConfig->endGroup();

        m_pConfig->beginGroup(Module::GROUP_APP);
        QVector<Config::GroupKey> qvtAppDefaultKeys;
        qvtAppDefaultKeys << Config::GroupKey(APPConfig::KEY_SYSTEM_VOLUME);
        m_pConfig->restoreDefault(qvtAppDefaultKeys);
        m_pConfig->endGroup();

        //重新初始化数据
        initParameters();

        resetTriggerList( m_eGain );
        //设置数据
        setButtonBarDatas();
        setChartDatas();

        //设置工作参数
        setAllWorkingSet();

        //设置音量
        RecordPlayService::instance()->setVolume(m_ucVolume);
    }
}

/*************************************************
功能： 保存数据
输入参数：
        stationName -- 变电站名
        deviceName -- 设备名
返回：
    保存后的文件名
*************************************************************/
QString AEFlyView::saveDataToFile()
{
    // 弹出文件备注框
    showFileCommentBox();

    AEFlightDataInfo sSaveDataInfo;
    sSaveDataInfo.stHeadInfo.generationDateTime = QDateTime::currentDateTime();
    sSaveDataInfo.stHeadInfo.strSubstationName = m_qstrStationName;
    sSaveDataInfo.stHeadInfo.strDeviceName = m_qstrDeviceName;
    sSaveDataInfo.stHeadInfo.strDeviceNumber = m_qstrDeviceNumber;
    sSaveDataInfo.stHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;
    sSaveDataInfo.stHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;
    sSaveDataInfo.stHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_UNRECORD;

    sSaveDataInfo.setUnit( AE::UNIT_DEFAULT );
    sSaveDataInfo.ePulseUnit = AEMapNS::PULSE_MIL_SECOND;
    sSaveDataInfo.stHeadInfo.ucTestChannelSign = (m_eChannel == AE::WIRELESS) ? 1 : 0;
    sSaveDataInfo.stHeadInfo.qstrRemark = m_qstrRemark;

    //sSaveDataInfo.setGain( m_eGain );
    sSaveDataInfo.eGainType =  DataFileNS::GAIN_TYPE_AMPLIFY_RATIO;
    sSaveDataInfo.sGainValue =  AE::g_ausGainValues[m_eGain];
    dbg_info("sSaveDataInfo.sGainValue is %d\n", sSaveDataInfo.sGainValue);
    sSaveDataInfo.eTransformerType = AE::transformChannelToDataFile(m_eChannel);
    sSaveDataInfo.fTrigThrhd = m_iTriggerValue;
    sSaveDataInfo.usMaxTimeInterval = m_iTimeInterval;
    sSaveDataInfo.usGatingTime = m_usOpenDoorTime;
    sSaveDataInfo.usShutTime = m_usCloseDoorTime;
    sSaveDataInfo.fSyncFreq = -1;
    sSaveDataInfo.ucSyncState = 0;

    QList<AEAbstractChart::AbstractData>  vChartDatas = m_pChart->samples();
    sSaveDataInfo.iDataPointNum = vChartDatas.size();
    AEFlightData sRawData[AEPULSEMAXNUM];
    memset( &sRawData,0,AEPULSEMAXNUM*sizeof(AEFlightData) );
    for( int i = 0;i < vChartDatas.size();++i )
    {
        sRawData[i].fInterval = vChartDatas.at( i ).fXscale * m_iTimeInterval;
        sRawData[i].fPeakValue = vChartDatas.at( i ).fYscale * AE::Y_RANGE_VALUES[m_eGain];
        if(sRawData[i].fPeakValue >= AE::Y_RANGE_VALUES[m_eGain])
        {
            sRawData[i].fPeakValue = AE::Y_RANGE_VALUES[m_eGain];
        }
    }
    memcpy( sSaveDataInfo.stAEFlightData,sRawData,sizeof(sRawData) );

    //存放图谱数据点颜色
    QList<QColor> lDataColors = m_pChart->sampleColors();
    quint8 ucaDataColor[AEPULSEMAXNUM * 3];
    memset( &ucaDataColor,0,AEPULSEMAXNUM*3*sizeof(quint8) );
    for( int i = 0;i < vChartDatas.size();++i )
    {
        ucaDataColor[3 * i] = lDataColors.at(i).red();
        ucaDataColor[3 * i + 1] = lDataColors.at(i).green();
        ucaDataColor[3 * i + 2] = lDataColors.at(i).blue();
    }
    memcpy( sSaveDataInfo.ucaAEFlyDataColor,ucaDataColor,vChartDatas.size()*3*sizeof(quint8) );

    //存放图谱数据颜色索引
    QList<int> lColorIndex = m_pChart->sampleColorIndex();
    quint16 usaColorIndex[AEPULSEMAXNUM];
    memset( &usaColorIndex,0,AEPULSEMAXNUM*sizeof(quint16) );

    //    qDebug()<<"AEFlyView::saveDataToFile, lColorIndex:"<<lColorIndex;
    //    qDebug()<<"AEFlyView::saveDataToFile, vChartDatas.size():"<<vChartDatas.size();
    //    qDebug()<<"AEFlyView::saveDataToFile, lColorIndex.size():"<<lColorIndex.size();


    for( int i = 0;i < lColorIndex.size();++i )
    {
        usaColorIndex[i] = lColorIndex.at(i);
    }
    memcpy( sSaveDataInfo.usaAEFlyColorIndex,usaColorIndex,lColorIndex.size()*sizeof(quint16) );

    AEFlightDataSave hDataSave;
    return hDataSave.saveData( &sSaveDataInfo );
}

/*************************************************
功能： 保存设置
*************************************************************/
bool AEFlyView::saveConfig()
{
    int iGroup = AE::GROUP_AE_FLY;
    m_pConfig->beginGroup( Module::GROUP_AE );

    m_pConfig->setValue( m_eGain, AE::KEY_GAIN );
    m_pConfig->setValue( m_eFilter, AE::KEY_FILTER );
    m_pConfig->setValue( m_eTriggerValue, AE::KEY_TRIGGER_VALUE, iGroup );
    m_pConfig->setValue( m_usOpenDoorTime, AE::KEY_OPEN_DOOR_TIME, iGroup );
    m_pConfig->setValue( m_usCloseDoorTime, AE::KEY_CLOSE_DOOR_TIME, iGroup );
    m_pConfig->setValue( m_iTimeInterval, AE::KEY_TIME_INTERVAL, iGroup );
    if(m_eChannel != AE::SURFACE_MOUNT)
    {
        m_pConfig->setValue( m_eChannel, AE::KEY_CHANNEL_TYPE );
    }

    m_pConfig->endGroup();

    return true;
}

/*************************************************
功能： 触发值按钮的触发值列表根据增益、量纲动态变化
输入参数：
        eGain -- 增益
*************************************************************/
void AEFlyView::resetTriggerList( AE::GainType eGain )
{
    QStringList listTrigger = AE::getTriggerList( AE::UNIT_DEFAULT, eGain );

    RadioButton* pButTrigger = (RadioButton*)( this->buttonBar()->button(BUTTON_AE_TRIGGER_VALUE) );
    if( NULL != pButTrigger )
    {
        pButTrigger->setOptionList( listTrigger );
    }
    else
    {
        qWarning() << "AEFlyView::resetTriggerList: error, button is NULL!";
    }
}

/*************************************************
功能： 设置所有工作参数
*************************************************************/
void AEFlyView::setAllWorkingSet()
{
    if (AEFlyService* pService = getAEFlyService())
    {
        pService->transaction();
        pService->setWorkMode( AE::MODE_FLY );
        pService->setGain( m_eGain );
        pService->setFilter(m_eFilter);
        pService->setTriggerValue( m_eTriggerValue );
        pService->setCloseDoorTime( AE::MODE_FLY, m_usCloseDoorTime );
        pService->setOpenDoorTime( m_usOpenDoorTime );
        pService->setUnit( AE::UNIT_DEFAULT );
        pService->setSyncSource( Module::SYNC_SOURCE_DEFAULT );
        pService->commit();

        pService->setChannel(m_eChannel);
        pService->setTimeInterval(m_iTimeInterval);
    }
    else
    {
        qWarning() << "AEFlyView::setWorkingSet: error, service handle is NULL!";
    }
}

/*************************************************
功能： 响应S键事件
输入参数：
        id -- 按钮ID
*************************************************************/
void AEFlyView::onSKeyPressed()
{
    pressSaveData();

//    bool bIsSamplingStop = false;
//    if( isSampling() )
//    {
//        stopSample();
//        bIsSamplingStop = true;
//    }

//    QString strStationName;
//    QString strDeviceName;

//    PushButtonBar* pBtnBar = buttonBar();
//    QPoint centerPoint;
//    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
//    centerPoint.setY(pBtnBar->pos().y()+pBtnBar->height()/2);

//    fastSave( "",
//              strStationName,
//              strDeviceName,
//              DATA_STORAGE_PATH + '/' + AE_FLIGHT_FOLDER,
//              centerPoint);

//    if( bIsSamplingStop )
//    {
//        startSample();
//    }
}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void AEFlyView::keyPressEvent(QKeyEvent *event)
{
    if( event->key() == Qt::Key_Escape )
    {
        if(isSampling())
        {
            stopSample();
        }
        else
        {
            SampleChartView::keyPressEvent( event );
        }
    }
    else
    {
        SampleChartView::keyPressEvent( event );
    }
}

/*************************************************
功能： 载入录音数据
*************************************************************/
void AEFlyView::loadRecordData()
{
    QString filePath = AE_RECORD_FILE_DIR_PATH_FLY;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);
    if(infoList.isEmpty())
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
    else
    {
        PlayView* pView = new PlayView(AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_LOAD_RECORD_DATA), RecordPlay::AE_FLY);
        pView->show();
    }

    return;
}

/*************************************************
功能： 删除录音数据
*************************************************************/
void AEFlyView::deleteRecordData()
{
    QStringList nameFilters;
    nameFilters << AE_AUDIO_FILE_NAME_SUFFIX;

    QString filePath = AE_RECORD_FILE_DIR_PATH_FLY;
    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);
    if(infoList.isEmpty())
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));

    }
    else
    {
        DeleteDataView* pView = new DeleteDataView(filePath,
                                                   AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_DELETE_RECORD_DATA),
                                                   nameFilters
                                                   );
        pView->setRelatedSuffix(AE_AUDIO_FILE_NAME_SUFFIX, QStringList(AE_RECORD_FILE_NAME_SURRFIX));
        pView->exec();
    }

    return;
}


/*************************************************
功能： 响应录音完成
输入参数：
        strRecordFilePath -- 录音文件保存的路径
            uint -- 录音时长
*************************************************************/
void AEFlyView::saveRecorded(const QString& strRecordFilePath, const quint32 uiRecordingTime)
{
	AERecordDataInfo sSaveDataInfo;
    sSaveDataInfo.stHeadInfo.strSubstationName = "";
    sSaveDataInfo.stHeadInfo.strDeviceName = "";
    sSaveDataInfo.stHeadInfo.generationDateTime = QDateTime::currentDateTime();
    sSaveDataInfo.stHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_UNRECORD;

    sSaveDataInfo.stHeadInfo.eCode = DataFileNS::SPECTRUM_CODE_RECORD_AE;
    sSaveDataInfo.stHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;
    sSaveDataInfo.stHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_FLOAT;
    sSaveDataInfo.stHeadInfo.ucTestChannelSign = (m_eChannel == AE::WIRELESS) ? 1 : 0;

    sSaveDataInfo.uiDuration = uiRecordingTime;
    sSaveDataInfo.eGainType = DataFileNS::GAIN_TYPE_AMPLIFY_RATIO;
    sSaveDataInfo.sGain =  AE::g_ausGainValues[m_eGain];
    sSaveDataInfo.eSyncSource = DataFileNS::SYNC_SOURCE_POWER;
    sSaveDataInfo.fSyncFreq = -1;
    sSaveDataInfo.eTransformerType = AE::transformChannelToDataFile(m_eChannel);
    sSaveDataInfo.uiPulseOpenTime = m_usOpenDoorTime;
    sSaveDataInfo.uiPulseShutTime = m_usCloseDoorTime;
    sSaveDataInfo.fPulseTriggerThreshold = m_iTriggerValue;

    sSaveDataInfo.strFilePath = strRecordFilePath;

    QFileInfo qfDataInfo(strRecordFilePath);
    QString qstrFileName = qfDataInfo.fileName();
    qstrFileName = qstrFileName.left(qstrFileName.lastIndexOf('.'));

    AERecordDataSave hDataSave;
    QString qstrDataFile = hDataSave.saveData(&sSaveDataInfo, qfDataInfo.absolutePath(), qstrFileName);

    QFileInfo fileInfo(qstrDataFile);
    QString strFileName = fileInfo.fileName();
    QString strInfo = qstrDataFile.isEmpty() ? QObject::trUtf8("Save failure!") : strFileName;

    processTooLongMsgText(strInfo);
    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x() + pBtnBar->width() / 2);
    centerPoint.setY(pBtnBar->pos().y() + pBtnBar->height() / 2);
    MsgBox::informationWithoutAutoAccept("", strInfo, centerPoint);
    return;
}

/******************************************
 * 功能：AE录音到最大录音时间，处理逻辑
 * ****************************************/
void AEFlyView::stopAERecordNextToDo()
{
    if(stopAERecord())
    {
        this->buttonBar()->button(BUTTON_AE_RECORD)->setTitle(AE_TRANSLATE(AE::TEXT_START_RECORD));
        saveRecorded(m_qstrAttachPath, m_uiTotalRecordingTime);
		m_uiTotalRecordingTime = 0;
    }
    else
    {
        logError("stop record failed.");
    }

    return;
}

/****************************************************
 * 功能：保存操作
 * **************************************************/
void AEFlyView::pressSaveData()
{
    //停止采集
    bool bIsSamplingStop = false;
    if(isSampling())
    {
        stopSample();
        bIsSamplingStop = true;
    }

    QString strDataFile = saveDataToFile();

    QFileInfo fileInfo(strDataFile);
    QString strText = fileInfo.fileName();
    showSaveInformation(strText, strDataFile);

    if( bIsSamplingStop )
    {
        //开始采集
        startSample();
    }
}

/*************************************************
功能： 槽，响应录音时长变化
输入参数：
        id -- 按钮ID
*************************************************************/
void AEFlyView::onTimerRefresh(uint uiRecordingTime)
{
	char cRecordTime[10] = {0};
	
    UINT32 uiHour = uiRecordingTime / (MINUTE_PER_HOUR * SECOND_PER_MINUTE);
    UINT32 uiMin = (uiRecordingTime % (MINUTE_PER_HOUR * SECOND_PER_MINUTE)) / SECOND_PER_MINUTE;
    UINT32 uiSec = (uiRecordingTime % (MINUTE_PER_HOUR * SECOND_PER_MINUTE)) % SECOND_PER_MINUTE;
	
    sprintf(cRecordTime, "%02d:%02d", uiMin, uiSec); //显示样式：xx:xx
	logInfo(QString("cRecordTime %1").arg(cRecordTime));
	
	this->buttonBar()->button(BUTTON_AE_RECORD)->setTitle(AE_TRANSLATE(cRecordTime));
}

