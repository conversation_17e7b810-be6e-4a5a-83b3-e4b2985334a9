/*
* Copyright (c) 2019.04，南京华乘电气科技有限公司
* All rights reserved.
*
* commonviewconfig.h
*
* 初始版本：1.0
* 作者：张浪
* 修改日期：2019年04月01日
* 当前版本：1.0
*/

#ifndef COMMONVIEWCONFIG_H
#define COMMONVIEWCONFIG_H

#include <QApplication>
#define COMMON_VIEW_CONFIG_TRANSLATE(str) qApp->translate("CommonView", (str))

namespace CustomAccessView
{
    const char* const CONTEXT = "CommonView"; // 定制接入域
    const char* const TEXT_CONNECT = QT_TRANSLATE_NOOP("CommonView", "Connect App");
    const char* const TEXT_UN_CONNECT = QT_TRANSLATE_NOOP("CommonView", "Discon");
    const char* const TEXT_COMMIT = QT_TRANSLATE_NOOP("CommonView", "Submit");
    const char* const TEXT_DELETE = QT_TRANSLATE_NOOP("CommonView", "Delete");
    const char* const TEXT_OPEN = QT_TRANSLATE_NOOP("CommonView", "Open");
    const char* const TEXT_REFRESH = QT_TRANSLATE_NOOP("CommonView", "Refresh");
    const char* const TEXT_COMPRESS = QT_TRANSLATE_NOOP_UTF8("CommonView", "Compress");
    const char* const TEXT_DOWNLOAD = QT_TRANSLATE_NOOP("CommonView", "Download");
    const char* const TEXT_UPLOAD = QT_TRANSLATE_NOOP("CommonView", "Upload");
}

#endif // COMMONVIEWCONFIG_H
