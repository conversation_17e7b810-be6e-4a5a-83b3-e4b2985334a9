/*
 * Copyright (c) 2019.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：mechanicalspectrumdefine.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/11/08
 * 摘要：mechanical图谱数据定义
 * 当前版本：1.0
 */

#pragma once

#include <QString>
#include <vector>
#include "dataspecification_def.h"

namespace DataSpecificationNS
{
    // 分合闸操作
    enum SwitchingOperation
    {
        OPER_NOT_RECORDED = 255, //未记录
        OPER_OPEN = 1,  //分闸
        OPER_CLOSE = 2, //合闸
    };

    //机械特性数据类型
    enum MechDataType
    {
        THREE_MERGE_DATA = 0,   //0 三项合一
        SPLIT_ALL_DATA,          //1 三项分开 全数据
        SPLIT_MOTOR_DATA,        //2 三项分开 电机数据
        SPLIT_COIL_DATA,         //3 三项分开 线圈数据
    };

    //操动机构类型
    enum OperatingMechanismType
    {
        OPER_MECH_ONE = 0,  //0 一台操动机构带动三项断路器
        OPER_MECH_THREE     //1 三台操动机构带三项断路器
    };

    //储能类型
    enum MechStoredEnergyType
    {
        SINGLE_MOTOR = 0,   //0 单项交/直流电机
        THREE_MOTOR         //1 三项交流电机
    };

    struct MechanicalExtInformation
    {
        AmpUnit eAmpUnit;                               //幅值单位
        float fAmpLowerLimit;                           //幅值下限
        float fAmpUpperLimit;                           //幅值上限
        qint32 iDataPoint;                              //数据点个数
        qint64 llSampleRate;                            //采样率
        SwitchingOperation eSwitchingOperation;         //分合闸操作
        qint32 iPowerFreqCycleCount;                    //工频周期数

        MechanicalExtInformation()
        {
            eAmpUnit = AMP_UNIT_DEFAULT;
            fAmpLowerLimit = 0;
            fAmpUpperLimit = 0;
            iDataPoint = 0;
            llSampleRate = 0;
            eSwitchingOperation = OPER_NOT_RECORDED;
            iPowerFreqCycleCount = 0;
        }

        MechanicalExtInformation& operator=(const MechanicalExtInformation& stMechanicalExtInformation)
        {
            this->eAmpUnit = stMechanicalExtInformation.eAmpUnit;
            this->fAmpLowerLimit = stMechanicalExtInformation.fAmpLowerLimit;
            this->fAmpUpperLimit = stMechanicalExtInformation.fAmpUpperLimit;
            this->iDataPoint = stMechanicalExtInformation.iDataPoint;
            this->llSampleRate = stMechanicalExtInformation.llSampleRate;
            this->eSwitchingOperation = stMechanicalExtInformation.eSwitchingOperation;
            this->iPowerFreqCycleCount = stMechanicalExtInformation.iPowerFreqCycleCount;
            return *this;
        }

        bool operator==(const MechanicalExtInformation& stMechanicalExtInformation) const
        {
            if (!(this->eAmpUnit == stMechanicalExtInformation.eAmpUnit)) return false;
            if (!(this->fAmpLowerLimit == stMechanicalExtInformation.fAmpLowerLimit)) return false;
            if (!(this->fAmpUpperLimit == stMechanicalExtInformation.fAmpUpperLimit)) return false;
            if (!(this->iDataPoint == stMechanicalExtInformation.iDataPoint)) return false;
            if (!(this->llSampleRate == stMechanicalExtInformation.llSampleRate)) return false;
            if (!(this->eSwitchingOperation == stMechanicalExtInformation.eSwitchingOperation)) return false;
            if (!(this->iPowerFreqCycleCount == stMechanicalExtInformation.iPowerFreqCycleCount)) return false;
            return true;
        }
    };

    struct MechanicalCoilExtInformation
    {
        MechDataType eMechDataType;//数据类型
        OperatingMechanismType eOperatingMechanismType;//操作类型
        MechStoredEnergyType eMechStoredEnergyType;//储能类型
        float fCoilUpTime; //线圈带电时刻
        float fHitTime; //铁芯撞击挚子时刻
        float fAuxSubSwitchCloseTime;//辅助开关切断时刻
        float fCoilDownTime; //线圈断电时刻
        float fCoilMaxCurrent; //线圈最大电流
        float fConversionFactor;  //转换系数

        MechanicalCoilExtInformation()
        {
            eMechDataType = THREE_MERGE_DATA;
            eOperatingMechanismType = OPER_MECH_ONE;
            eMechStoredEnergyType = SINGLE_MOTOR;
            fCoilUpTime = 0;
            fHitTime = 0;
            fAuxSubSwitchCloseTime = 0;
            fCoilDownTime = 0;
            fCoilMaxCurrent = 0;
            fConversionFactor = 0;
        }

        MechanicalCoilExtInformation& operator=(const MechanicalCoilExtInformation& stMechanicalCoilExtInformation)
        {
            this->eMechDataType = stMechanicalCoilExtInformation.eMechDataType;
            this->eOperatingMechanismType = stMechanicalCoilExtInformation.eOperatingMechanismType;
            this->eMechStoredEnergyType = stMechanicalCoilExtInformation.eMechStoredEnergyType;
            this->fCoilUpTime = stMechanicalCoilExtInformation.fCoilUpTime;
            this->fHitTime = stMechanicalCoilExtInformation.fHitTime;
            this->fAuxSubSwitchCloseTime = stMechanicalCoilExtInformation.fAuxSubSwitchCloseTime;
            this->fCoilDownTime = stMechanicalCoilExtInformation.fCoilDownTime;
            this->fCoilMaxCurrent = stMechanicalCoilExtInformation.fCoilMaxCurrent;
            this->fConversionFactor = stMechanicalCoilExtInformation.fConversionFactor;
            return *this;
        }

        bool operator==(const MechanicalCoilExtInformation& stMechanicalCoilExtInformation) const
        {
            if (!(this->eMechDataType == stMechanicalCoilExtInformation.eMechDataType)) return false;
            if (!(this->eOperatingMechanismType == stMechanicalCoilExtInformation.eOperatingMechanismType)) return false;
            if (!(this->eMechStoredEnergyType == stMechanicalCoilExtInformation.eMechStoredEnergyType)) return false;
            if (!(this->fCoilUpTime == stMechanicalCoilExtInformation.fCoilUpTime)) return false;
            if (!(this->fHitTime == stMechanicalCoilExtInformation.fHitTime)) return false;
            if (!(this->fAuxSubSwitchCloseTime == stMechanicalCoilExtInformation.fAuxSubSwitchCloseTime)) return false;
            if (!(this->fCoilDownTime == stMechanicalCoilExtInformation.fCoilDownTime)) return false;
            if (!(this->fCoilMaxCurrent == stMechanicalCoilExtInformation.fCoilMaxCurrent)) return false;
            if (!(this->fConversionFactor == stMechanicalCoilExtInformation.fConversionFactor)) return false;
            return true;
        }
    };

    struct MechanicalMotorExtInformation
    {
        MechDataType eMechDataType;
        OperatingMechanismType eOperatingMechanismType;
        MechStoredEnergyType eMechStoredEnergyType;
        float fMotorStartCurrent; //电机启动电流
        float fMotorMaxCurrent; //电机最大电流
        float fMotorStorageTime;  //电机储能时刻
        float fConversionFactor; //转换系数

        MechanicalMotorExtInformation()
        {
            eMechDataType = THREE_MERGE_DATA;
            eOperatingMechanismType = OPER_MECH_ONE;
            eMechStoredEnergyType = SINGLE_MOTOR;
            fMotorStartCurrent = 0;
            fMotorMaxCurrent = 0;
            fMotorStorageTime = 0;
            fConversionFactor = 0;
        }
        MechanicalMotorExtInformation& operator=(const MechanicalMotorExtInformation& stMechanicalMotorExtInformation)
        {
            this->eMechDataType = stMechanicalMotorExtInformation.eMechDataType;
            this->eOperatingMechanismType = stMechanicalMotorExtInformation.eOperatingMechanismType;
            this->eMechStoredEnergyType = stMechanicalMotorExtInformation.eMechStoredEnergyType;
            this->fMotorStartCurrent = stMechanicalMotorExtInformation.fMotorStartCurrent;
            this->fMotorMaxCurrent = stMechanicalMotorExtInformation.fMotorMaxCurrent;
            this->fMotorStorageTime = stMechanicalMotorExtInformation.fMotorStorageTime;
            this->fConversionFactor = stMechanicalMotorExtInformation.fConversionFactor;
            return *this;
        }

        bool operator==(const MechanicalMotorExtInformation& stMechanicalMotorExtInformation) const
        {
            if (!(this->eMechDataType == stMechanicalMotorExtInformation.eMechDataType)) return false;
            if (!(this->eOperatingMechanismType == stMechanicalMotorExtInformation.eOperatingMechanismType)) return false;
            if (!(this->eMechStoredEnergyType == stMechanicalMotorExtInformation.eMechStoredEnergyType)) return false;
            if (!(this->fMotorStartCurrent == stMechanicalMotorExtInformation.fMotorStartCurrent)) return false;
            if (!(this->fMotorMaxCurrent == stMechanicalMotorExtInformation.fMotorMaxCurrent)) return false;
            if (!(this->fMotorStorageTime == stMechanicalMotorExtInformation.fMotorStorageTime)) return false;
            if (!(this->fConversionFactor == stMechanicalMotorExtInformation.fConversionFactor)) return false;
            return true;
        }
    };

    struct MechanicalSwitchExtInformation
    {
        MechDataType eMechDataType;
        OperatingMechanismType eOperatingMechanismType;
        MechStoredEnergyType eMechStoredEnergyType;
        float fSwitchOpenTime;         //开关分时间
        float fSwitchCloseTime;    //开关合时间
        float fSwitchTwiceTime;  //开关二分时间
        float fSwitchOpenSync;//开关分同期性
        float fSwitchCloseSync;//开关合同期性
        float fSwitchTwiceSync;//开关二分同期性
        float fSwitchShotTime;  //金短时间
        float fSwitchNoCurrentTime; //无电流时间
        float fConversionFactor;//转换系数

        MechanicalSwitchExtInformation()
        {
            eMechDataType = THREE_MERGE_DATA;
            eOperatingMechanismType = OPER_MECH_ONE;
            eMechStoredEnergyType = SINGLE_MOTOR;
            fSwitchOpenTime = 0;
            fSwitchCloseTime = 0;
            fSwitchTwiceTime = 0;
            fSwitchOpenSync = 0;
            fSwitchCloseSync = 0;
            fSwitchTwiceSync = 0;
            fSwitchShotTime = 0;
            fSwitchNoCurrentTime = 0;
            fConversionFactor = 0;
        }

        MechanicalSwitchExtInformation& operator=(const MechanicalSwitchExtInformation& stMechanicalSwitchExtInformation)
        {
            this->eMechDataType = stMechanicalSwitchExtInformation.eMechDataType;
            this->eOperatingMechanismType = stMechanicalSwitchExtInformation.eOperatingMechanismType;
            this->eMechStoredEnergyType = stMechanicalSwitchExtInformation.eMechStoredEnergyType;
            this->fSwitchOpenTime = stMechanicalSwitchExtInformation.fSwitchOpenTime;
            this->fSwitchCloseTime = stMechanicalSwitchExtInformation.fSwitchCloseTime;
            this->fSwitchTwiceTime = stMechanicalSwitchExtInformation.fSwitchTwiceTime;
            this->fSwitchOpenSync = stMechanicalSwitchExtInformation.fSwitchOpenSync;
            this->fSwitchCloseSync = stMechanicalSwitchExtInformation.fSwitchCloseSync;
            this->fSwitchTwiceSync = stMechanicalSwitchExtInformation.fSwitchTwiceSync;
            this->fSwitchShotTime = stMechanicalSwitchExtInformation.fSwitchShotTime;
            this->fSwitchNoCurrentTime = stMechanicalSwitchExtInformation.fSwitchNoCurrentTime;
            this->fConversionFactor = stMechanicalSwitchExtInformation.fConversionFactor;
            return *this;
        }

        bool operator==(const MechanicalSwitchExtInformation& stMechanicalSwitchExtInformation) const
        {
            if (!(this->eMechDataType == stMechanicalSwitchExtInformation.eMechDataType)) return false;
            if (!(this->eOperatingMechanismType == stMechanicalSwitchExtInformation.eOperatingMechanismType)) return false;
            if (!(this->eMechStoredEnergyType == stMechanicalSwitchExtInformation.eMechStoredEnergyType)) return false;
            if (!(this->fSwitchOpenTime == stMechanicalSwitchExtInformation.fSwitchOpenTime)) return false;
            if (!(this->fSwitchCloseTime == stMechanicalSwitchExtInformation.fSwitchCloseTime)) return false;
            if (!(this->fSwitchTwiceTime == stMechanicalSwitchExtInformation.fSwitchTwiceTime)) return false;
            if (!(this->fSwitchOpenSync == stMechanicalSwitchExtInformation.fSwitchOpenSync)) return false;
            if (!(this->fSwitchCloseSync == stMechanicalSwitchExtInformation.fSwitchCloseSync)) return false;
            if (!(this->fSwitchTwiceSync == stMechanicalSwitchExtInformation.fSwitchTwiceSync)) return false;
            if (!(this->fSwitchShotTime == stMechanicalSwitchExtInformation.fSwitchShotTime)) return false;
            if (!(this->fSwitchNoCurrentTime == stMechanicalSwitchExtInformation.fSwitchNoCurrentTime)) return false;
            if (!(this->fConversionFactor == stMechanicalSwitchExtInformation.fConversionFactor)) return false;
            return true;
        }
    };

    struct MechanicalData
    {
        QByteArray data;

        MechanicalData() {}

        MechanicalData& operator=(const MechanicalData& stMechanicalData)
        {
            this->data = stMechanicalData.data;
            return *this;
        }

        bool operator==(const MechanicalData& stMechanicalData) const
        {
            if (!(this->data == stMechanicalData.data)) return false;
            return true;
        }
    };
}
