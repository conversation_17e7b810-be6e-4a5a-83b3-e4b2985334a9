#ifndef SCROLLTEXT_H
#define SCROLLTEXT_H

#include <QWidget>
#include <QStaticText>
#include <QTimer>


class ScrollText : public QWidget
{
    Q_OBJECT
    Q_PROPERTY(QString text READ text WRITE setText)
    Q_PROPERTY(QString separator READ separator WRITE setSeparator)

public:
    explicit ScrollText(QWidget *parent = 0);

    void setFont(const QFont& font);

public slots:
    QString text() const;
    void setText(const QString& text);

    QString separator() const;
    void setSeparator(const QString& separator);

protected:
    virtual void paintEvent(QPaintEvent *);
    virtual void resizeEvent(QResizeEvent *);

private:
    void updateText();

private slots:
    virtual void onTimeout();

private:
    QString m_qstrOriginalText;     // 原始文本
    QString m_qstrSeparator;        // 分隔符
    QStaticText m_staticText;
    int m_iOriginalTextWidth;     // 原始文本宽度
    QSize m_wholeTextSize;          // 整个文本的大小，包含分隔符
    int m_iLeftMargin;
    bool m_bScrollEnabled;
    int m_iScrollPos;
    QImage m_alphaChannel;
    QImage m_buffer;
    QTimer m_timer;
};

#endif // SCROLLTEXT_H
