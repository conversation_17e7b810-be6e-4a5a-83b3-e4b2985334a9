/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* usbSetting.cpp
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月29日
* 摘要：usb设置所在view的定义

* 当前版本：1.0
*/

#include <QApplication>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFileInfoList>
#include <QStringList>
#include <QPalette>
#include <QDebug>
#include "messageBox/msgbox.h"
#include "usbsetting.h"
#include "systemsetting/systemsetservice.h"
#include <QByteArray>
#include "window/Window.h"
#include "dataSave/DataFileInfos.h"
#include "usbmodemanager/usbmodemanager.h"
#include "customaccesstask/taskdefine.h"
#include "fileoper/fileoperutil.h"
#ifdef  Q_PROCESSOR_ARM
#include <sys/vfs.h>
#include <unistd.h>
#endif


const int TITLE_HEIGHT = 100;    // 标题栏的高度
const int DELETE_TEXT_SIZE = 15;    // 格式化存储器标签字体大小
const int CAPACITY_TEXT_SIZE = 10;    // 各显示容量标签字体大小
const int SPACING_SIZE = 50; // 标签名和容量显示内容之间的间距
const int LEFT_MARGIN = 10; // 标签名和窗口的间距
const int DELETE_LABEL_WIDTH = Window::WIDTH / 2;
const int DELETE_LABEL_HEIGHT = 100;
const QString GB_TEXT = "GB";
const QString MB_TEXT = "MB";
const QString KB_TEXT = "KB";
const QString PRRCENT_TEXT = "%";

//const QString BUTTON_STYLE = "QLabel{border-style:solid;border-width:2px;border-radius:10px;padding:2px 4px;border-color:rgb(54, 133, 203);font-size:25px;font-family:msyh}"
//                             "QLabel:focus{border:none; background: rgb( 115,198,242 );color: white;}"
//                             "QLabel:hover{border:none; background: rgb( 115,198,242 );color: white;}";
//const QString CONTENT_LABEL_STYLE = "QLabel{border:none;color:rgb(0,0,0);font-size:25px;font-family:msyh}";
//const QString CAPACITY_LABEL_STYLE = "QLabel{border:none;color:rgb(0,0,0);font-size:25px;font-family:msyh}";

const QString BUTTON_STYLE = "QLabel{border-style:solid;border-width:2px;border-radius:10px;padding:2px 4px;border-color:rgb(54, 133, 203);font-size:25px;}"
                             "QLabel:focus{border:none; background: rgb( 115,198,242 );color: white;}"
                             "QLabel:hover{border:none; background: rgb( 115,198,242 );color: white;}";
const QString CONTENT_LABEL_STYLE = "QLabel{border:none;color:rgb(0,0,0);font-size:25px;}";
const QString CAPACITY_LABEL_STYLE = "QLabel{border:none;color:rgb(0,0,0);font-size:25px;}";

/*************************************************
函数名： usbSetting(QWidget *parent)
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
usbSetting::usbSetting(QWidget *parent) :
    QFrame(parent)
{
    setWindowFlags(Qt::FramelessWindowHint);    // 设置无标题栏外框
    setFocusPolicy( Qt::StrongFocus );          // 设置焦点策略
    setFixedSize( Window::WIDTH,Window::HEIGHT ); // 固定高宽
    setAttribute( Qt::WA_DeleteOnClose );   // 设置关闭自动析构的策略

    createMainView();                   // 创建整体布局
    refreshCapacity();                  // 刷新容量显示
}

/*************************************************
函数名： createMainView(QWidget *parent)
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 创建整体布局
*************************************************************/
void usbSetting::createMainView( void )
{
    m_pTitleBar = new TitleBar( trUtf8( "Storage" ),this );
    m_pTitleBar->setFixedHeight( TITLE_HEIGHT );    // 定义标题栏，并指定高度
    connect( m_pTitleBar,SIGNAL(sigClicked()),this,SLOT(close()) );// 绑定点击标题栏退出

    QLinearGradient linearGradient( QPoint(0,0),QPoint( Window::WIDTH,Window::HEIGHT ) );
    linearGradient.setColorAt( 0,Qt::white);
    linearGradient.setColorAt( 1,QColor(181, 210, 236)); // 设置usb设置整个界面从左至右渐变

    QPalette palette = this->palette();
    palette.setBrush( QPalette::Window,QBrush( linearGradient ) );
    setAutoFillBackground( true );
    setPalette( palette );  // 设置背景色

    m_pDeleteLabel = new QLabel( trUtf8("Format Storage"),this );
    m_pDeleteLabel->setAlignment( Qt::AlignCenter );
    m_pDeleteLabel->setFixedSize( DELETE_LABEL_WIDTH,DELETE_LABEL_HEIGHT );
    m_pDeleteLabel->setStyleSheet( BUTTON_STYLE );
    m_pDeleteLabel->installEventFilter( this );     // 格式化标签初始化，设置样式

    m_pAllCapacityLabel = new QLabel( this );
    m_pAllCapacityLabel->setAlignment( Qt::AlignLeft|Qt::AlignVCenter );
    m_pAllCapacityLabel->setStyleSheet( CAPACITY_LABEL_STYLE ); // 全部容量标签

    m_pAllCapacityTitle = new QLabel( trUtf8("Shared Storage: "),this );
    m_pAllCapacityTitle->setAlignment( Qt::AlignLeft|Qt::AlignVCenter );
    m_pAllCapacityTitle->setStyleSheet( CAPACITY_LABEL_STYLE );// 全部容量标题

    QHBoxLayout *hLayoutOne = new QHBoxLayout;
    hLayoutOne->addSpacing( LEFT_MARGIN );
    hLayoutOne->addWidget( m_pAllCapacityTitle,1 );
    hLayoutOne->addSpacing( SPACING_SIZE );
    hLayoutOne->addWidget( m_pAllCapacityLabel,3 );     // 水平方向，容量显示和标题水平方向布局


    m_pUsedCapacityLabel = new QLabel( this );
    m_pUsedCapacityLabel->setAlignment( Qt::AlignLeft|Qt::AlignVCenter );
    m_pUsedCapacityLabel->setStyleSheet( CAPACITY_LABEL_STYLE );// 已用容量标签

    m_pUsedCapacityTitle = new QLabel( trUtf8("Used Storage: "),this );
    m_pUsedCapacityTitle->setAlignment( Qt::AlignLeft|Qt::AlignVCenter );
    m_pUsedCapacityTitle->setStyleSheet( CAPACITY_LABEL_STYLE );// 已用容量标题

    QHBoxLayout *hLayoutTwo = new QHBoxLayout;
    hLayoutTwo->addSpacing( LEFT_MARGIN );
    hLayoutTwo->addWidget( m_pUsedCapacityTitle,1 );
    hLayoutTwo->addSpacing( SPACING_SIZE );
    hLayoutTwo->addWidget( m_pUsedCapacityLabel,3 );    // 水平方向，容量显示和标题水平方向布局

    m_pAvailableCapacityLabel = new QLabel( this );
    m_pAvailableCapacityLabel->setAlignment( Qt::AlignLeft|Qt::AlignVCenter );
    m_pAvailableCapacityLabel->setStyleSheet( CAPACITY_LABEL_STYLE );   // 可用容量标签

    m_pAvailableCapacityTitle = new QLabel( trUtf8("Available Storage: "),this );
    m_pAvailableCapacityTitle->setAlignment( Qt::AlignLeft|Qt::AlignVCenter );
    m_pAvailableCapacityTitle->setStyleSheet( CAPACITY_LABEL_STYLE );   // 可用容量标题

    QHBoxLayout *hLayoutThree = new QHBoxLayout;
    hLayoutThree->addSpacing( LEFT_MARGIN );
    hLayoutThree->addWidget( m_pAvailableCapacityTitle,1 );
    hLayoutThree->addSpacing( SPACING_SIZE );
    hLayoutThree->addWidget( m_pAvailableCapacityLabel,3 );// 水平方向，容量显示和标题水平方向布局


    QWidget *pWidget = new QWidget;
    QHBoxLayout *deleteLayout = new QHBoxLayout;
    deleteLayout->addWidget( m_pDeleteLabel,Qt::AlignHCenter );
    pWidget->setLayout( deleteLayout );

    QVBoxLayout *vLayout = new QVBoxLayout( this ); // 整体添加竖直方向布局
    vLayout->addWidget( m_pTitleBar );
    vLayout->addLayout( hLayoutOne,1 );
    vLayout->addLayout( hLayoutTwo,1 );
    vLayout->addLayout( hLayoutThree,1 );
    vLayout->addWidget( pWidget );
    vLayout->addStretch(2);
    vLayout->setSpacing( 0 );
    vLayout->setMargin( 0 );    // 设置比例因子，边距设成0
    setLayout( vLayout );
    return;
}

/*************************************************
函数名： refreshCapacity(void)
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 格式化后更新容量显示
*************************************************************/
void usbSetting::refreshCapacity( void )
{
    APP_CHECK_RETURN(m_pAllCapacityLabel);
    APP_CHECK_RETURN(m_pUsedCapacityLabel);
    APP_CHECK_RETURN(m_pAvailableCapacityLabel);

    quint64 disk_size = 0;
    quint64 UsedCapaticity = 0;
    qint32 iPercent = 0;

    SystemSetService::instance()->getDiskUsedInfo(UsedCapaticity, disk_size, iPercent);

    m_pAllCapacityLabel->setText(QString("%1").arg(disk_size) + MB_TEXT);
    m_pUsedCapacityLabel->setText(QString("%1").arg(UsedCapaticity) + MB_TEXT );
    m_pAvailableCapacityLabel->setText(QString("%1").arg(iPercent) + PRRCENT_TEXT );
    return;
}

/*************************************************
函数名： keyPressEvent
输入参数:
    event -- 事件
输出参数：NULL
返回值： NULL
功能： 键盘事件
*************************************************************/
void usbSetting::keyPressEvent( QKeyEvent* event )
{
    if( ( event->key() == Qt::Key_Escape ) )        // esc关闭
    {
        close();
    }
    else if( ( event->key() == Qt::Key_Enter ) || ( event->key() == Qt::Key_Return ) )
    {
        messageBoxAction();
    }
    else
    {
        QFrame::keyPressEvent( event );
    }
}

/*************************************************
功能： 清除设备内部的日志文件，防止日志文件过多，设备启动失败的问题
*************************************************************/
void usbSetting::clearLogFiles()
{
    FileOperUtil::clearUnusedLogFiles();
    return;
}

/*************************************************
函数名： recursivlyRemoveDir
输入参数:
    strPath -- 要删除的目录
    bIsDelSelf -- 是否删除自身
输出参数：NULL
返回值： NULL
功能： 递归方式删除所有文件和目录
*************************************************************/
FormatResult usbSetting::recursivlyRemoveDir(QString strPath, bool bIsDelSelf)
{
    FileOperUtil::refreshToSystemDisk();

    QDir directory(strPath);
    if (!directory.exists())
    {
        return FORMAT_RESULT_ALREADY_FORMAT;
    }

    bool bRet = true;
    QStringList qstrlstFilesTemp = directory.entryList(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);
    QStringList files = directory.entryList(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Readable | QDir::Writable | QDir::Hidden);

    //总文件个数大于0，但可操作的文件个数小于0，则表示当前不可进行格式化操作
    if(qstrlstFilesTemp.size() > 0 && files.size() <= 0)
    {
        return FORMAT_RESULT_FAIL;
    }

    QList<QString>::iterator fIterator = files.begin();
    for (; fIterator != files.end(); ++fIterator)
    {
        QString filePath = QDir::convertSeparators(directory.path() + '/' + (*fIterator));
        QFileInfo fileInfo(filePath);
        if (fileInfo.isFile() || fileInfo.isSymLink())
        {
            if(!(QFile::setPermissions(filePath, QFile::ReadUser | QFile::WriteUser)))
            {
                logError("set file permissions failed.");
                bRet = false;
                break;
            }

            if(!(QFile::remove(filePath)))
            {
                logError("remove file failed.");
                bRet = false;
                break;
            }
        }
        else if (fileInfo.isDir())
        {
            if(!(filePath.compare(PACKAGE_FOLDER) == 0))
            {
                if(FORMAT_RESULT_FAIL == recursivlyRemoveDir(filePath, true))
                {
                    logError("remove directory failed.");
                    bRet = false;
                    break;
                }
            }
        }
        else
        {
            //
        }
    }

    if(bRet)
    {
        if (bIsDelSelf)
        {
            bRet = directory.rmdir(QDir::convertSeparators(directory.path()));
        }
        else
        {
            if (directory.path() == strPath)
            {
                bRet = true;
            }
            else
            {
                bRet = directory.rmdir(QDir::convertSeparators(directory.path()));
            }
        }
    }

    FileOperUtil::refreshToSystemDisk();

    return (bRet ? FORMAT_RESULT_SUCCESS : FORMAT_RESULT_FAIL);
}

/*************************************************
函数名： messageBoxAction( void )
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 格式化数据后消息提示框的处理逻辑
*************************************************************/
void usbSetting::messageBoxAction( void )
{
    MsgBox::Button eReply = MsgBox::question( "", QObject::trUtf8("To format storage or not?") );
    if( MsgBox::OK == eReply )
    {
        if(!(SystemSetService::instance()->storageOperEnable()))
        {
            logWarning("unable to operate storage space");
            MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("Format failed, the disk is being occupied."));
            return;
        }

        //清除日志信息
        clearLogFiles();

        FormatResult eResult = recursivlyRemoveDir("/media/data", false);
        logInfo(QString("format result: %1.").arg(eResult).toLatin1().data());
        if(FORMAT_RESULT_SUCCESS == eResult)
        {
            SystemSetService::instance()->storageFormatted();
            MsgBox::information( "", QObject::trUtf8("Formatted!") );
        }
        else if(FORMAT_RESULT_FAIL == eResult)
        {
            MsgBox::information( "", QObject::trUtf8("Cannot be formated!") );
        }
        else
        {
            SystemSetService::instance()->storageFormatted();
            MsgBox::information( "", QObject::trUtf8("Formatted!") );
        }

        //接入终端根路径需要存在
        QString strMainTaskRootPath = CustomAccessTaskNS::CUSTOM_ACCESS_TASK_FILE_ROOT_PATH;
        FileOperUtil::createDirectory(strMainTaskRootPath);

        refreshCapacity();
    }
    return;
}

/************************************************
 * 函数名   : eventFilter
 * 输入参数 : pObj: 面板中的控件;pEvent: 事件
 * 输出参数 : NULL
 * 返回值   : 事件处理结果
 * 功能     : 事件过滤器,处理鼠标点击事件及物理键盘按钮事件
 ************************************************/
bool usbSetting::eventFilter(QObject *pObj,QEvent *pEvent)
{
    if( m_pDeleteLabel != pObj )
    {
        return false;
    }
    if( QEvent::MouseButtonRelease == pEvent->type() )
    {
        messageBoxAction();   // 设置选中时的样式
    }
    return QFrame::eventFilter(pObj,pEvent);
}
