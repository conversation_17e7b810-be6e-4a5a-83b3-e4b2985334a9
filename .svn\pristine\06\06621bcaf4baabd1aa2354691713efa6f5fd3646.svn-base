#include "timecalibrationcontroller.h"
#include "systemsetting/systemsetservice.h"
#include "pda/cloud/qjason/qjson.h"
#include "../appserverdefine.h"
#include "appserverutils.h"
#include "globalerrprocess.h"
#include "log/log.h"

using namespace errorProcess;
using namespace AppServerNS;

TimeCalibrationController::TimeCalibrationController(QObject *parent)
    : HttpRequestHandler(parent)
{

}

TimeCalibrationController::~TimeCalibrationController()
{

}

/*****************************************
 * 功能：处理http请求函数，并作出响应
 * 输入参数：
 *      request：请求
 * 输出参数：
 *      response：响应
 * *****************************************/
void TimeCalibrationController::service(HttpRequest &request, HttpResponse &response)
{
    qint64 lCalibUtcTime = request.getParameter(TIME_CALIBRATETIME).toLongLong();
    double dTimezoneVal = request.getParameter(TIME_CALIBRATETIMEZONE).toDouble();

    QString qstrCurTimeInfo = SystemSetService::instance()->setLocalCalibrateTime(lCalibUtcTime, dTimezoneVal);
    logInfo(QString("calibrate utc time: %1, calibrate timezone: %2, current time info: %3.").arg(lCalibUtcTime).arg(dTimezoneVal).arg(qstrCurTimeInfo));

    int iRspCode = REPLY_SUCCESS_CODE;

    QJson stRspJson;
    QJson stRetJson;

    stRetJson.add(TIME_CALIBRATION, qstrCurTimeInfo.toUtf8());
	
    stRspJson.add(REPLY_CODE_KEY, iRspCode);
    stRspJson.add(REPLY_MSG_KEY, AppServerUtils::stateMsgByCode(iRspCode).toUtf8());
    stRspJson.add(REPLY_RESULT_KEY, stRetJson);

    response.write(stRspJson.unformattedData(), true);

    return;
}



