/*
 * Copyright (c) 2019.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：mechanicalspectrum.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/11/04
 * 摘要：mechanical图谱
 * 当前版本：1.0
*/

#pragma once

#include "spectrum.h"
#include "mechanicalspectrumdefine.h"

namespace DataSpecificationNS
{
    class MechanicalSpectrumPrivate;
    class DATASPECIFICATIONSHARED_EXPORT MechanicalSpectrum : public Spectrum
    {
    public:
        MechanicalSpectrum();
        ~MechanicalSpectrum();

        /************************************************
         * 函数名   : spectrumName
         * 输入参数 : NULL
         * 输出参数 : NULL
         * 返回值   : QString
         * 功能     : 图谱名称
         ************************************************/
        virtual QString spectrumName() const;

        /************************************************
         * 函数名   : setMechanicalExtInformation
         * 输入参数 :
           const MechanicalExtInformation& stMechanicalExtInformation: MechanicalExtInformation
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置MechanicalExtInformation
         ************************************************/
        void setMechanicalExtInformation(const MechanicalExtInformation& stMechanicalExtInformation);

        /************************************************
         * 函数名   : setMechanicalData
         * 输入参数 :
           const MechanicalData& stMechanicalData: MechanicalData
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置MechanicalData
         ************************************************/
        void setMechanicalData(const MechanicalData& stMechanicalData);

        /************************************************
         * 函数名   : getMechanicalExtInformation
         * 输入参数 : NULL
         * 输出参数 :
           MechanicalExtInformation& stMechanicalExtInformation: MechanicalExtInformation
         * 返回值   : void
         * 功能     : 获取MechanicalExtInformation
         ************************************************/
        void getMechanicalExtInformation(MechanicalExtInformation& stMechanicalExtInformation);

        /************************************************
         * 函数名   : setMechanicalData
         * 输入参数 : NULL
         * 输出参数 :
           MechanicalData& stMechanicalData: MechanicalData
         * 返回值   : void
         * 功能     : 获取MechanicalData
         ************************************************/
        void getMechanicalData(MechanicalData& stMechanicalData);

    protected:

        /************************************************
         * 函数名   : saveBinarySpectrumExtInfo
         * 输入参数 :
           QDataStream& out: 输出流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存二进制可扩展信息段
         ************************************************/
        virtual bool saveBinarySpectrumExtInfo(QDataStream& out);

        /************************************************
         * 函数名   : saveXMLSpectrumExtInfo
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
           QDomElement& element: dom元素
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存XML可扩展信息段
         ************************************************/
        virtual bool saveXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj, QDomElement& element);

        /************************************************
         * 函数名   : saveJSONSpectrumExtInfo
         * 输入参数 :
           rapidjson::Document::AllocatorType& alloc:
           rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存JSON可扩展信息段
         ************************************************/
        virtual bool saveJSONSpectrumExtInfo(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : saveBinarySpectrumData
         * 输入参数 :
           QDataStream& in: 输入流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存二进制图谱数据段
         ************************************************/
        virtual bool saveBinarySpectrumData(QDataStream& out);

        /************************************************
         * 函数名   : saveXMLSpectrumData
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
           QDomElement& element: dom元素
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存XML图谱数据段
         ************************************************/
        virtual bool saveXMLSpectrumData(XMLDocument& xmlDocumentObj, QDomElement& element);

        /************************************************
         * 函数名   : saveJSONSpectrumData
         * 输入参数 :
           rapidjson::Document::AllocatorType& alloc:
           rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存JSON图谱数据段
         ************************************************/
        virtual bool saveJSONSpectrumData(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : parseBinarySpectrumExtInfo
         * 输入参数 :
           QByteArray& qbaPackage: 数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析二进制可扩展信息段
         ************************************************/
        virtual bool parseBinarySpectrumExtInfo(QDataStream& in);

        /************************************************
         * 函数名   : parseXMLSpectrumExtInfo
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析XML可扩展信息段
         ************************************************/
        virtual bool parseXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj);

        /************************************************
         * 函数名   : parseJSONSpectrumExtInfo
         * 输入参数 :
           const rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析JSON可扩展信息段
         ************************************************/
        virtual bool parseJSONSpectrumExtInfo(const rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : parseBinarySpectrumData
         * 输入参数 :
           QDataStream& in: 输入流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析二进制图谱数据段
         ************************************************/
        virtual bool parseBinarySpectrumData(QDataStream& in);

        /************************************************
         * 函数名   : parseXMLSpectrumData
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析XML图谱数据段
         ************************************************/
        virtual bool parseXMLSpectrumData(XMLDocument& xmlDocumentObj);

        /************************************************
         * 函数名   : parseJSONSpectrumData
         * 输入参数 :
           const rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析JSON图谱数据段
         ************************************************/
        virtual bool parseJSONSpectrumData(const rapidjson::Value& jsonValue);

        virtual inline int dataCount();

    private:
        MechanicalSpectrumPrivate* m_pMechanicalSpectrumPrivate{ Q_NULLPTR };
    };
}
