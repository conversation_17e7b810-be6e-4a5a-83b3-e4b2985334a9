#ifndef LOCALCOMM_H
#define LOCALCOMM_H

#include <QObject>
#include <QtNetwork/QLocalSocket>
#include <QMutex>
#include <QTimer>
#include "module_global.h"
#include "localprotocol.h"

//自动重连的时间间隔
#define RECOONECT_INTERVAL 3000

class MODULESHARED_EXPORT LocalComm : public QObject
{
    Q_OBJECT
public:
    /****************************
    功能： 模块单例
    *****************************/
    static LocalComm* instance();

/******通信相关接口**********/
    /*************************************************
    函数名：
    输入参数:serverName--进程通信的服务名
    输出参数：
    返回值：
    功能：打开与进程通信的接口
    *************************************************************/
    void openComm(QString serverName);

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：关闭与进程通信的接口
    *************************************************************/
    void closeComm();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：查看与服务端是否连接
    *************************************************************/
    bool isOpened();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：获取软件基本信息
    *************************************************************/
    void initSoftwareInfo();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：获取云端基本信息
    *************************************************************/
    void initCloudInfo();

    /*更新云端软件信息
     */
    bool refreshSoftwareInfo();

    /*************************************************
    函数名：
    输入参数:messagePacket--待发送数据包
    输出参数：
    返回值：true--发送成功  false--发送失败
    功能：将指定内容封装成数据包
    *************************************************************/
    bool sendMessage(LocalMsgType eType, QString strContent = QString());

/*****相关信息存取接口*******************/
    /*************************************************
    功能：当前本地安装的软件的版本
    *************************************************************/
    QString localSoftwareVersion();

    /*************************************************
    功能：云端软件安装包的版本
    *************************************************************/
    QString cloudSoftwareVersion();

    /*************************************************
    功能：云端软件安装包的说明
    *************************************************************/
    QString cloudUpdateCaption();

    /*************************************************
    功能：软件安装包的下载进度 百分比系数
    *************************************************************/
    quint32 downloadProgress();

    /*************************************************
    功能：云端的IP
    *************************************************************/
    QString cloudIP();

    /*************************************************
    功能：设置云端的IP
    *************************************************************/
    void setCloudIP(QString strCloudIP);

    /*************************************************
    功能：云端端口号
    *************************************************************/
    QString cloudPort();

    /*************************************************
    功能：设置云端的端口号
    *************************************************************/
    void setCloudPort(QString strCloudPort);

    //与更新进程的通信是否正常
    bool isConnectedToUpdateApp();

    /*************************************************
    功能：设置update进程请求时间时区
    *************************************************************/
    void setTimezone();

    /*************************************************
    功能：设置远程升级的云地址信息
    *************************************************************/
    void setUpgCloudInfo(const QString &qstrIp, quint16 qui16Port);

signals:
    /*************************************************
    功能：软件安装包信息更新信号
    *************************************************************/
    void sigSoftwareInfoUpdated();

    /*************************************************
    功能：软件安装包下载进度更新信号
    *************************************************************/
    void sigDownloadProgressUpdated();

    /*************************************************
    功能：云传输出错信号
    *************************************************************/
    void sigCloudState(int istate);

private slots:
    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：处理解析接收的数据
    *************************************************************/
    void onReadyRead();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：处理通信接口出现的错误信息
    *************************************************************/
    void displayError(QLocalSocket::LocalSocketError socketError);

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：处理通信接口出现的错误信息
    *************************************************************/
    void onSocketConnected();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：处理通信接口出现的错误信息
    *************************************************************/
    void onSocketDisconnected();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：与服务端连接
    *************************************************************/
    void connectServer();

private:
    /*************************************************
    函数名：
    输入参数:eType--消息的类型号  strContent--消息的内容
    输出参数：
    返回值：用于发送的数据包
    功能：将指定内容封装成数据包
    *************************************************************/
    QByteArray packetMessage(LocalMsgType eType, QString strContent);

    //为防止类外部直接生成对象，将构造函数设为私有
    explicit LocalComm(QObject *parent = 0);

    static LocalComm *m_pInstance;//单例对象
    //单例对象的互斥锁
    static QMutex s_singleMutext;
    //用于析构单例对象
    class CGarbo
    {
    public:
        ~CGarbo()
        {
            if(LocalComm::m_pInstance)
            {
                delete LocalComm::m_pInstance;
            }
        }
    };
    static CGarbo s_Garbo;//单例对象

//进程通信相关参数
    QString m_serverName;   //更新服务的服务名
    QLocalSocket *m_pUpdateSocket;//与更新服务进程通信的接口
    QByteArray m_rcvBuffer; //存储尚未处理的接收数据
    bool m_bConnected;  //标识连接状态
    QTimer m_reconnectedTimer;//进行自动重连的定时器

//软件相关参数
    QString m_strCloudIP;   //云端的IP
    QString m_strCloudPort; //云端端口号
    QString m_localVersion; //当前本地安装的软件的版本
    QString m_cloudVersion; //云端软件安装包的版本
    QString m_cloudUpdateCaption;   //云端软件安装包的说明
    quint32 m_uiDownloadProgress;   //软件安装包下载进度
};

#endif // LOCALCOMM_H
