/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* ipSettingGrounp.cpp
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年4月14日
* 摘要：网络设置下输入框集合的定义

* 当前版本：1.0
*/

#include <QDebug>
#include "ipSettingGrounp.h"

const quint16 IP_EDIT_INDEX = 2; // ip控件的序号
const quint16 SUBNET_EDIT_INDEX = 3; // 掩码控件的序号
const quint16 GATEWAY_EDIT_INDEX = 4; // 网关控件的序号
const QString ORIGINAL_IP = QString( "***************" );

/*************************************************
函数名： SystemSettingGrounp(const QString& qsGrounpName,QWidget *parent)
输入参数:qsGrounpName：group的标签名
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
IpSettingGrounp::IpSettingGrounp(const QString& qsGrounpName,QWidget *parent) :
    SystemSettingGrounp(qsGrounpName,parent)
{
    m_bIsIpEdit = false;
}

/************************************************
 * 函数名   : activePreviousWidget
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 将焦点往前移动
 ************************************************/
void IpSettingGrounp::activePreviousWidget( void )
{
    bool bActivate = false;
    if( m_pSwitchMenu->switchStatus() == false )
    {
        m_pSwitchMenu->setFocus();
        return;
    }
    for( int i = 0;i < m_vAllin.size();++i )
    {
        if( m_vAllin.at( i )->hasFocus() )
        {
            if( 0 == i )
            {
                m_vAllin.last()->setFocus();
            }
            else if( IP_EDIT_INDEX == i
                     || SUBNET_EDIT_INDEX == i
                     || GATEWAY_EDIT_INDEX == i )   // IP相关设置控件，此时上下键操作被复用为参数加减
            {
                minusEditText( i );
            }
            else
            {
                m_vAllin.at( --i )->setFocus();
            }
            bActivate = true;
            break;
        }
    }
    if( false == bActivate )    // 初始状态无任何控件有焦点时的处理
    {
        m_vAllin.last()->setFocus();
    }
}

/************************************************
 * 函数名   : minusEditText
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 编辑框数据自减或切换焦点的操作
 ************************************************/
 void IpSettingGrounp::minusEditText( quint16 usIndex )
 {
     QVector<QLineEdit*> qVector = m_pLineEditGrounp->getLineEdit();// 获得各IP编辑框的指针容器
     quint16 vectorSize = m_vAllin.size() - qVector.size(); // 除各编辑框以外的控件个数
     if( m_bIsIpEdit == true && m_pSwitchMenu->switchStatus() )// 若编辑框被enter选中且控制开关已开，此时进入按键控制增减的模式
     {
         if( (usIndex - vectorSize) >= qVector.size() )// 防止容器出界
         {
             return;
         }
         MyIpLineEdit *ipLineEdit = qobject_cast<MyIpLineEdit*>(qVector.at( usIndex - vectorSize ));
         ipLineEdit->activeUp();
     }
     else
     {
         if( usIndex < 1 )  // 防止容器出界
         {
             return;
         }
         m_vAllin.at( usIndex - 1 )->setFocus();
     }
 }

/************************************************
 * 函数名   : activeNextWidget
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 将焦点往后移动
 ************************************************/
void IpSettingGrounp::activeNextWidget( void )
{
    bool bActivate = false;
    if( m_pSwitchMenu->switchStatus() == false )
    {
        m_pSwitchMenu->setFocus();
        return;
    }
    for( int i = 0;i < m_vAllin.size();++i )
    {
        if( m_vAllin.at( i )->hasFocus() )
        {
            if( IP_EDIT_INDEX == i
                || SUBNET_EDIT_INDEX == i
                || GATEWAY_EDIT_INDEX == i )// IP相关设置控件，此时上下键操作被复用为参数加减
            {
                plusEditText( i );
            }
            else
            {
                m_vAllin.at( ++i )->setFocus();
            }
            bActivate = true;
            break;
        }
    }
    if( false == bActivate )    // 初始状态无任何控件有焦点时的处理
    {
        m_vAllin.first()->setFocus();
    }
}

/************************************************
 * 函数名   : plusEditText
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 编辑框数据自加或切换焦点的操作
 ************************************************/
 void IpSettingGrounp::plusEditText( quint16 usIndex )
 {
     QVector<QLineEdit*> qVector = m_pLineEditGrounp->getLineEdit();// 获得各IP编辑框的指针容器
     quint16 vectorSize = m_vAllin.size() - qVector.size();  // 除各编辑框以外的控件个数
     if( m_bIsIpEdit == true && m_pSwitchMenu->switchStatus() ) // 若编辑框被enter选中且控制开关已开，此时进入按键控制增减的模式
     {
         if( (usIndex - vectorSize) >= qVector.size() )// 防止容器出界
         {
             return;
         }
         MyIpLineEdit *ipLineEdit = qobject_cast<MyIpLineEdit*>(qVector.at( usIndex - vectorSize ));
         ipLineEdit->activeDown();
     }
     else
     {
         if( usIndex == ( m_vAllin.size() - 1 ) )
         {
             m_vAllin.first()->setFocus();  // 选中控件为容器中最后的控件时，自动将容器中第一个控件设置焦点
         }
         else if( usIndex < ( m_vAllin.size() - 1 ) )
         {
             m_vAllin.at( usIndex + 1 )->setFocus();// 否则设置容器中下一个控件获得焦点
         }
     }
 }

/************************************************
 * 函数名   : activeEnterPress
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 模拟有enter操作的控件，被点击的效果
 ************************************************/
void IpSettingGrounp::activeEnterPress( void )
{
    if( m_pSwitchMenu->switchStatus() == false )
    {
        m_pSwitchMenu->clickSwitchMenu();
        return;
    }
    if( m_pOkLabel->hasFocus() )
    {
        onOk();
    }
    else if( m_vAllin.at( IP_EDIT_INDEX )->hasFocus()
             || m_vAllin.at( SUBNET_EDIT_INDEX )->hasFocus()
             || m_vAllin.at( GATEWAY_EDIT_INDEX )->hasFocus() )
    {
        m_bIsIpEdit = true;
    }
    else if( m_pSwitchMenu->hasFocus() )
    {
        m_pSwitchMenu->clickSwitchMenu();   // 便于父窗体直接利用事件过滤器进行控制
        if( false == m_pSwitchMenu->switchStatus() )
        {
            for( int i = 0;i < m_vAllin.size();++i )
            {
                if( m_vAllin.at( i ) != m_pSwitchMenu )
                {
                    m_vAllin.at( i )->setEnabled( false );
                }
            }
        }
        else
        {
            for( int i = 0;i < m_vAllin.size();++i )
            {
                if( m_vAllin.at( i ) != m_pSwitchMenu )
                {
                    m_vAllin.at( i )->setEnabled( true );
                }
            }
        }
    }
}

 /************************************************
  * 函数名   : activeEscape
  * 输入参数 : NULL
  * 输出参数 : NULL
  * 返回值   : NULL
  * 功能     : 改变编辑状态
  ************************************************/
  void IpSettingGrounp::activeEscape( void )
  {
      m_bIsIpEdit = false;
  }

  /************************************************
   * 函数名   : isEdited
   * 输入参数 : NULL
   * 输出参数 : NULL
   * 返回值   : NULL
   * 功能     : 判断是否正属于编辑状态
   ************************************************/
   bool IpSettingGrounp::isEdited( void )
   {
       return m_bIsIpEdit;
   }

   /************************************************
    * 函数名   : onCancel
    * 输入参数 : NULL
    * 输出参数 : NULL
    * 返回值   : NULL
    * 功能     : 和cancel按键绑定的槽函数
    ************************************************/
   void IpSettingGrounp::onCancel()
   {
       QVector<QLineEdit*> qVector = m_pLineEditGrounp->getLineEdit();
       if( qVector.isEmpty() )
       {
           return;
       }
       for( int i = 0;i < qVector.size();++i )
       {
           MyIpLineEdit *ipLineEdit = qobject_cast<MyIpLineEdit*>(qVector.at( i ));
           ipLineEdit->setText( ORIGINAL_IP );
       }
   }
