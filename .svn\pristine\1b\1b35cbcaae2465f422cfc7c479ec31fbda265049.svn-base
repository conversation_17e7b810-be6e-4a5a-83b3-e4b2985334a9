#ifndef BLUETOOTHSETSERVICE_H
#define BLUETOOTHSETSERVICE_H

#include "SystemSet.h"
#include "module_global.h"
#include "Module.h"
#include "datadefine.h"
#include "QDateTime"
#include <QTimer>
#include <QSet>

class SystemSetService;
class BluetoothClient;

class MODULESHARED_EXPORT BluetoothSetService : public QObject
{
    Q_OBJECT
public:   
    typedef struct _BluetoothInfo
    {
        QString m_strName;    //名称
        QString m_strMac;     //mac地址
        bool m_bConnected;    //是否为连接状态
        bool m_bAutoConnected;//是否自动连接
        QDateTime m_saveTime; //数据的保存时间

        _BluetoothInfo( const QString& strName = QString(), const QString& macName = QString(), bool bAuto = false )
            :m_strName(strName), m_strMac(macName), m_bAutoConnected(bAuto)
        {
            m_bConnected = false;
        }
    }BluetoothInfo;

    //蓝牙扫描模式
    typedef enum _BlueToothScanMode
    {
        SCAN_QUICK_MODE = 10,    //快速扫描模式
        SCAN_IDEL_MODE = 30,       //空闲扫描模式
    }BlueToothScanMode;

    friend class SystemSetService;

    /*************************************************
    功能： 启动蓝牙连接 异步
    该函数会自动扫描热点，并连接已记录且为自动连接的热点
    *************************************************/
    void openBluetooth();

    /*************************************************
    功能： 关闭蓝牙模块 异步
    *************************************************/
    void closeBluetooth();

    /*************************************************
    功能： 蓝牙打开状态
    返回值：true -- 已开启
          false -- 未开启
    *************************************************/
    bool isBluetoothOpened( void );

    /*************************************************
    功能： 扫描蓝牙设备列表
    参数： bStarted -- true 启动扫描 false 终止扫描
    *************************************************/
    void scanDeviceList( bool bStarted );

    /*************************************************
     * 参数：bAuto -- 是否支持自动连接
     * 功能    : 设置蓝牙是否自动连接
    *************************************************/
    void enableAutoConnected( bool bAuto );

    /*************************************************
     * 参数：strName -- 设备名
     *      strMac  -- 设备MAC
     * 功能    : 连接指定设备 异步
     * 返回值：true -- 成功
     *       false -- 失败
    *************************************************/
    void connectDevice(const QString &strName, const QString& strMac );

    /*************************************************
    功能： 获取已连接的蓝牙设备信息
    输出参数： 已连接的蓝牙设备信息（注：仅连接成功时为有效值）
    返回值：true -- 已连接
          false -- 未连接
    *************************************************/
    bool getConnectDevice( QString &devMac );

    /*************************************************
    功能： 蓝牙连接状态
    返回值：true -- 已连接
          false -- 未连接
    *************************************************/
    bool isBluetoothConnected( void );

    /*************************************************
    功能： 返回蓝牙访问客户端
    返回值：蓝牙访问客户端
    *************************************************/
    BluetoothClient * getBluetoothClient();

    //
    QVector<BluetoothInfo> scanBluetoothInfos()
    {
        return m_scanBluetoothList;
    }

signals:
    /*************************************************
    功能： 控制WiFi信号
    参数： bStart -- true代表开启 false代表关闭
    *************************************************/
    void sigControlBluetoothResult( bool bStart );

    /*************************************************
    功能： 发射WiFi扫描出的列表
    参数： wifiInfos -- 扫描出的热点列表
    *************************************************/
    void sigScanBluetoothInfos( QVector<BluetoothSetService::BluetoothInfo> wifiInfos );

    /*************************************************
    功能： 发射蓝牙连接的结果
    参数： wifiName -- 连接的热点的名称
          bResult -- 连接的结果
    *************************************************/
    void sigBluetoothConnected( QString strMac, bool bResult );

    //后台自动重连的信号
    void sigBeginToConnect( QString strName );

    /*************网络相关内部信号*****************/
    /*************************************************
        功能： 发射控制本地蓝牙开关的信号
        参数： bStart -- true代表开启 false代表关闭
        *************************************************/
    void sigControlBluetooth( bool bStart );

    /*************************************************
        功能： 发射连接蓝牙设备信号
        参数： strName -- 设备名称
                strMac -- 设备MAC
        *************************************************/
    void sigConnectBluetooth( QString strName, QString strMac );

    /*************************************************
        功能： 发射扫描蓝牙信号
        *************************************************/
    void sigScanBluetooth(  );

public slots:
    /*************************************************
    功能： 控制本地蓝牙打开关闭
    参数： bStart -- true代表开启 false代表关闭
    *************************************************/
    void onControlBluetooth( bool bStart );

    /*************************************************
    功能： 扫描远端蓝牙设备列表
    *************************************************/
    void onScanBluetoothList(  );

    /************************************************
     * 参数: strMac -- 远端蓝牙mac
     * 功能: 连接指定蓝牙设备
     ************************************************/
    void onConnectBluetooth(QString strName, QString strMac );

private slots:
    /*************************************************
    功能： 自动连接蓝牙设备
    自动连接设置为自动连接，且本次未出现连接失败的设备
    *************************************************/
    void autoConnectBluetooth(const QVector<BluetoothInfo> &bluetoothList );

    /*************************************************
    功能： 连接断开处理
    参数： bConnected--连接当前状态
    *************************************************/
    void onConnectChanged( bool bConnected );

private:
    explicit BluetoothSetService(QObject *parent = 0);

    ~BluetoothSetService();

    /*************************************************
        功能： 读取本地记录的蓝牙设备信息
        返回值：本地保存的蓝牙设备信息列表
        *************************************************/
    QVector<BluetoothInfo> getBluetoothInfoHistory();

    /*************************************************
        功能： 将蓝牙设备信息保存到本地
        参数： 待保存的蓝牙信息
        返回值： 是否保存成功
        *************************************************/
    void saveBluetoothInfo(BluetoothInfo bluetoothInfo );

    /*************************************************
        功能： 获取历史记录的蓝牙信息
        输入参数： strMac--设备的mac地址
        输出参数： bluetoothInfo--保存在本地的对应信息
        返回值： 本地是否找到对应蓝牙信息
        *************************************************/
    bool getBluetoothInfoByMac( const QString &strMac, BluetoothInfo &bluetoothInfo );

    /*****************************************************
     * 功能：设置本地蓝牙连接信息
     * ***************************************************/
    void setBTLocalInfo();

private:
    bool m_bConnectedBluetooth;          //蓝牙是否已连接
    QString m_connectDeviceMac;          //已连接的蓝牙设备MAC
    QVector<BluetoothInfo> m_localBluetoothList;      //本地保存的蓝牙设备信息列表
    QSet<QString> m_disConnectedBluetoothList;   //连接失败的蓝牙设备信息列表
    bool m_bAutoConnected;               //扫描后是否自动连接蓝牙设备

    QVector<BluetoothInfo> m_scanBluetoothList;

    bool m_bConnectting;                //处于正在连接状态

    BluetoothClient * m_backBlueClient;
};

#endif // BLUETOOTHSETSERVICE_H
