﻿/*
* Copyright (c) 2017.08，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: phasechartview.h
*
* 初始版本: 1.0
* 作者: 张涛
* 创建日期: 2017年12月5日
* 摘要: 该文件定义了周期图谱视图类,实现控制显示的图谱类型,以及对图谱的相关参数的设置

* 当前版本: 1.0
*/


#ifndef PHASECHARTVIEW_H
#define PHASECHARTVIEW_H

#include <QFrame>
#include "phasedef.h"

class PhaseDataModel;
class PrpsChart;
class PrpdChart;
class PhasePaintData;
class PhaseChartLayout;
class PhaseValueHelper;
class PrpdPointMatrix;


/*
 * 注意事项:
 * 1. 为方便使用,view类默认拥有一个水平的图谱布局器(即PhaseChartHLayout),并且此布局器有10个像素的外边距
 * 2. view类默认拥有一个采样值辅助类,用于在绘制最大值/幅值范围时,能够调用此类的接口实现将数据转换成字符串的功能,
 *    如果需要扩展,可继承PhaseValueHelper类进行自定义转换
 * 3. 如果要在图上绘制自定内容, 可继承此类并实现drawCustomContents接口, 如果需要调整图谱占用的位置,
 *    可调用PhaseChartLayout的setContentsMargins达到效果
 * 4. 如果要设置图谱文字/坐标系,以及背景色,直接通过setStyleSheet, 坐标系的颜色与文字颜色相同,采用color属性的值,
 *    背景色采用background-color属性的值, 具体可参考example的例子
 */

class PHASECHARTSHARED_EXPORT PhaseChartView : public QFrame
{
    Q_OBJECT
public:
    /************************************************
     * 函数名: PhaseChartView
     * 输入参数:
     *          parent: 父对象指针
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 构造函数
     ************************************************/
    PhaseChartView(int iDataCountPerPeriod = Phase::DefaultDataCountPerPeriod,
                   QWidget *parent = NULLPTR);

    /************************************************
     * 函数名: ~PhaseChartView
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 析构函数
     ************************************************/
    ~PhaseChartView();

    /************************************************
     * 函数名: model
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 获取视图的数据模型类
     ************************************************/
    PhaseDataModel* model() const;

    /************************************************
     * 函数名: model
     * 输入参数:
     *          pModel: 数据模型类
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置视图的数据模型类
     ************************************************/
    void setModel(PhaseDataModel *pModel);

    /************************************************
     * 函数名: isChartEnabled
     * 输入参数:
     *          eChart: 图谱类型
     * 输出参数: NULL
     * 返回值: true--使能的,即显示图谱并支持相关操作; false--图谱不使能,与eChart相关的接口调用不生效
     * 功能: 获取图谱是否使能, 默认每个图谱都不使能
     ************************************************/
    bool isChartEnabled(Phase::ChartType eChart) const;

    bool isChartVisible(Phase::ChartType eChart) const;

    /************************************************
     * 函数名: setChartEnabled
     * 输入参数:
     *          eChart: 图谱类型
     *          bool: 使能状态
     * 输出参数: NULL
     * 返回值: true--使能的,即显示图谱并支持相关操作; false--图谱不使能,与eChart相关的接口调用不生效
     * 功能: 获取图谱是否使能, 默认每个图谱都不使能
     ************************************************/
    void setChartEnabled(Phase::ChartType eChart, bool bEnabled);

    void setChartVisible(Phase::ChartType eChart, bool bVisible);

    /************************************************
     * 函数名: phaseOffset
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 相位偏移
     * 功能: 获取相位偏移值,单位为度
     ************************************************/
    int phaseOffset() const;

    /************************************************
     * 函数名: setPhaseOffset
     * 输入参数:
     *          iAngle: 相位值,单位为度
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置相位偏移值
     ************************************************/
    void setPhaseOffset(int iAngle);

    /************************************************
     * 函数名: upperBound
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 图谱幅值轴的最大坐标值
     * 功能: 获取图谱最大坐标值(prpd和prps的幅值坐标范围一样)
     ************************************************/
    Phase::ValueType upperBound() const;

    /************************************************
     * 函数名: setUpperBound
     * 输入参数:
     *          max: 图谱幅值轴的最大坐标值
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置图谱最大坐标值(prpd和prps的幅值坐标范围一样)
     ************************************************/
    void setUpperBound(Phase::ValueType max);

    /************************************************
     * 函数名: lowerBound
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 图谱幅值轴的最小坐标值
     * 功能: 获取图谱最小坐标值(prpd和prps的幅值坐标范围一样)
     ************************************************/
    float lowerBound() const;

    /************************************************
     * 函数名: setLowerBound
     * 输入参数:
     *          min: 图谱幅值轴的最小坐标值
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 获取图谱最小坐标值(prpd和prps的幅值坐标范围一样)
     ************************************************/
    void setLowerBound(Phase::ValueType min);

    /************************************************
     * 函数名: prpsThreshold
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 阈值,范围为0~1
     * 功能: 获取PRPS阈值
     ************************************************/
    float prpsThreshold() const;

    /************************************************
     * 函数名: prpdThreshold
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 阈值,范围为0~1
     * 功能: 获取PRPD阈值
     ************************************************/
    float prpdThreshold() const;

    /************************************************
     * 函数名: setPRPSThreshold
     * 输入参数:
     *          fThreshold: 阈值,范围为0~1
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置PRPS阈值
     ************************************************/
    void setPRPSThreshold(float fThreshold);

    /************************************************
     * 函数名: setPRPDThreshold
     * 输入参数:
     *          fThreshold: 阈值,范围为0~1
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置PRPD阈值
     ************************************************/
    void setPRPDThreshold(float fThreshold);

    /************************************************
     * 函数名: dataItemColorStyle
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 数据项使用的颜色
     * 功能: 设置数据项使用的颜色
     ************************************************/
    Phase::DataItemColorStyle dataItemColorStyle() const;

    /************************************************
     * 函数名: dataItemColorStyle
     * 输入参数:
     *          eStyle: 数据项颜色
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置数据项颜色
     ************************************************/
    void setDataItemColorStyle(Phase::DataItemColorStyle eStyle);

    /************************************************
     * 函数名: chartLayout
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 图谱布局器
     * 功能: 获取图谱布局器
     ************************************************/
    PhaseChartLayout* chartLayout() const;

    /************************************************
     * 函数名: setChartLayout
     * 输入参数:
     *          pLayout: 图谱布局器
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置图谱布局器
     ************************************************/
    void setChartLayout(PhaseChartLayout *pLayout);

    /************************************************
     * 函数名: textFont
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: 字体
     * 功能: 获取图谱文字使用的字体,如果不设置, 默认使用QApplication的字体
     ************************************************/
    QFont textFont() const;

    /************************************************
     * 函数名: textFont
     * 输入参数:
     *          ft: setTextFont
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置图谱文字使用的字体,如果不设置, 默认使用QApplication的字体
     ************************************************/
    void setTextFont(const QFont &ft);

    /************************************************
     * 函数名: prpdMatrix
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: PRPD图谱的点阵数据,具体含义参考PrpdPointMatrix类
     * 功能: 获取PRPD图谱的点阵数据
     ************************************************/
    PrpdPointMatrix prpdMatrix() const;

    /************************************************
     * 函数名: isAccumulative
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: true--prpd累积开启, false--不累积
     * 功能: 获取PRPD图谱的点阵数据
     ************************************************/
    bool isAccumulative() const;

    /************************************************
     * 函数名: isAccumulative
     * 输入参数:
     *          bAccumulative: true--开启prpd累积, false--不累积
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置PRPD累积是否开启
     ************************************************/
    void setAccumulative(bool bAccumulative);

    void setAccumulativeTime(int iTime);

    // 设置每秒有多少组数据
    void setDataCountPerSecond(unsigned int uiDataCountPerSecond);

    void setRecording(bool bRecording);

    /************************************************
     * 函数名: isSinusoidVisible
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: true--显示PRPD图谱得参考正弦线, false--不显示
     * 功能: 设置PRPD累积是否开启
     ************************************************/
    bool isSinusoidVisible() const;

    /************************************************
     * 函数名: setSinusoidVisible
     * 输入参数:
     *          bVisible: 是否显示PRPD图谱的正弦参考线,true--显示,false--不显示
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置是否显示PRPD图谱的正弦参考线
     ************************************************/
    void setSinusoidVisible(bool bVisible);

    /************************************************
     * 函数名: setValueHelper
     * 输入参数:
     *          pHelper: 是否显示PRPD图谱的正弦参考线,true--显示,false--不显示
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 设置是否显示PRPD图谱的正弦参考线
     ************************************************/
    void setValueHelper(PhaseValueHelper *pHelper);

    void setPRPSAmpSymbol( const QString& strSymbol );

    //设置累计信息矩阵
    void setPointMatrix( const PrpdPointMatrix *pMatrix );

    void setPRPDRenewable( bool bRenewable );


    /************************************************
     * 函数名: color
     * 输入参数:
     *          sPeriodIndex: 数据点所在的周期索引
     *          sPhaseIndex: 数据点所在的相位索引
     * 输出参数: NULL
     * 返回值: 数据点颜色
     * 功能: 获取数据点颜色
     ************************************************/
    QColor prpsDataColor(int sPeriodIndex, int sPhaseIndex);

    QColor colorForPercent(float dRatio);


    QRgb prpdDataColor(int sRowIndex, int sColumnIndex);

    int prpdDataPulseCnt(int sRowIndex, int sColumnIndex);

    void setPhaseAxisOffset(const int iPhaseAxisOffset);

    void setOriginXRatio(const int iOriginXRatio);

    void setOriginYRatio(const int iOriginYRatio);

    // 设置PRPD点大小
    void setPRPDPointSize(const int iPointSize);

public slots:
    /************************************************
     * 函数名: onAdvanced
     * 输入参数:
     *          iStep: 推进步数
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 发射让界面推进的信号
     ************************************************/
    void onAdvanced(int iStep);

    /************************************************
     * 函数名: onPeriodCountChanged
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 要显示的周期总数发生变化后对应的处理(清空界面数据元素,重置坐标系等)
     ************************************************/
    void onPeriodCountChanged(int iPeriodCount);

    /************************************************
     * 函数名: onDataCountPerPeriodChanged
     * 输入参数:
     *          iCount: 每周期数据点个数
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 每周期数据点个数改变后清空图谱并重设相应参数
     ************************************************/
    void onDataCountPerPeriodChanged(int iCount);

    /************************************************
     * 函数名: onClear
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 响应model的SigClear信号,清空界面
     ************************************************/
    void onClear();


protected:
    // 重绘
    void paintEvent(QPaintEvent *e);

    // 调整大小事件, 主要用来重新布局
    void resizeEvent(QResizeEvent *e);

    void mousePressEvent(QMouseEvent* pEvent);

    void mouseReleaseEvent(QMouseEvent* pEvent);

    void mouseMoveEvent(QMouseEvent* pEvent);

    /************************************************
     * 函数名: drawCustomContents
     * 输入参数: NULL
     * 输出参数: NULL
     * 返回值: NULL
     * 功能: 用户扩展接口,用于在界面刷新时绘制用户定制的内容, 默认为空实现
     ************************************************/
    virtual void drawCustomContents(QPainter *pPainter) const;

    // 内部实现接口
    PrpdChart* prpdChart() const
    {
        return m_pPrpdChart;
    }

    // 内部实现接口
    PrpsChart* prpsChart() const
    {
        return m_pPrpsChart;
    }

private:
    // 更新图谱的数据内容
    void updateChartContens();

    // 更新图谱参数
    void updateChartParams();

private:
    PhaseDataModel *m_pModel;
    PrpsChart *m_pPrpsChart;
    PrpdChart *m_pPrpdChart;

    PhasePaintData *m_pPaintData;

    int m_iDataCountPerPeriod;  //模型下每周期的点数
    int m_iPhaseOffset;
    float m_fPRPSThreshold;
    float m_fPRPDThreshold;

    PhaseChartLayout *m_pChartLayout;

    PhaseValueHelper *m_pValueHelper;

    friend class PhaseChartLayout;

    int m_iLastPointX;
    int m_iLastPointY;
    bool m_bPressed;
    bool m_bMouseMoving;
};

#endif // PHASECHARTVIEW_H
