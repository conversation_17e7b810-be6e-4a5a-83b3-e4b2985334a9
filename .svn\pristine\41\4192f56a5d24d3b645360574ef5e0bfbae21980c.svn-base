#include "wifisettingview.h"
#include <QScrollBar>
#include <QKeyEvent>
#include <QVBoxLayout>
#include <QNetworkInterface>
#include <QTimer>
#include <QMutexLocker>
#include "../common/hotspotlabel.h"
#include "inputwifipasswd.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "appconfig.h"
#include "messageBox/msgbox.h"
#include "systemsetting/systemsetservice.h"
#include "systemsetview/SystemViewConfig.h"
#include "log/log.h"
#include "global_log.h"

#define MAX_CAP_IP_TIME 30
/**************************************************
 *          各部分样式定义
 * ***********************************************/
const QString FRAME_STYLE = "WifiSettingView{background: rgb(211, 215, 224)}";
const QString LIST_HIDDEN_STYLE = "QListWidget{background: rgb(211, 215, 224); outline: 0px; border-radius: 15px 15px; border: none}";
const QString LIST_STYLE = "QListWidget{background: white; outline: 0px; border-radius: 15px 15px; border: none}";
const int TITLE_HEIGHT = 100;    // 标题栏的高度

/**************************************************
 *          伸缩因子，布局使用
 * ***********************************************/
//按键伸缩因子
typedef enum _StretchFactor
{
    TITLE_STRETCH = 2,//标题的伸缩因子
    SWITCH_STRETCH = 2,//按键栏的伸缩因子
    LOAD_STRETCH = 1, //搜索等待控件的伸缩因子
    LIST_STRTCH = 10, //输入框的伸缩因子
    STRTCH_NULL = 2 //空占位伸缩因子
}StretchFactor;

#define SCAN_INTERVAL 5000

#define TITLE_SWITCH_SPACING 10
#define SWITCH_HOTSPOT_SPACING 10
#define MARGIN_SIZE 20
#define ITEM_SPACING 2    // item间隙
#define ITEM_MINIUM_HEIGHT 80 //item最小高度
#define SINGLE_SHOT_INTERVAL 100 //延时扫描ap信号的间隔 100ms
const QString SCOROLLBAR_STYLE = "QScrollBar:vertical"
                                 "{"
                                 "width: 40px;"
                                 "background: rgba(0, 0, 0, 0%);"
                                 "margin: 0px, 0px, 0px, 0px;"
                                 "padding-top: 40px;"
                                 "padding-bottom: 40px;"
                                 "}"
                                 "QScrollBar::handle:vertical"
                                 "{"
                                 "width: 40px;"
                                 "background: rgba(0, 0, 0, 25%);"
                                 "border-radius: 13px;"
                                 "min-height: 20;"
                                 "}"
                                 "QScrollBar::handle:vertical:hover"
                                 "{"
                                 "width: 40px;"
                                 "background: rgba(0, 0, 0, 50%);"
                                 "border-radius: 13px;"
                                 "min-height: 20;"
                                 "}"
                                 "QScrollBar::add-line:vertical"
                                 "{"
                                 "height: 40px; width: 40px;"
                                 "subcontrol-position: bottom;"
                                 "}"
                                 "QScrollBar::sub-line:vertical"
                                 "{"
                                 "height: 40px; width: 40px;"
                                 "subcontrol-position: top;"
                                 "}"
                                 "QScrollBar::add-line:vertical:hover"
                                 "{"
                                 "height: 40px; width: 40px;"
                                 "subcontrol-position: bottom;"
                                 "}"
                                 "QScrollBar::sub-line:vertical:hover"
                                 "{"
                                 "height: 40px; width: 40px;"
                                 "subcontrol-position: top;"
                                 "}"
                                 "QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical"
                                 "{"
                                 "border-radius: 0px;"
                                 "}";

/*************************************************
入参： bWifiOn -- wifi开关状态
功能： 构造函数
*************************************************************/
WifiSettingView::WifiSettingView()
    : QFrame()
{
    m_bWifiOn = false;
    m_bViewOperating = false;
    m_qlstWifiInfos.clear();
    m_vHotspotItemDelegate.clear();
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    m_strWifiName = pConfig->value(APPConfig::KEY_WIFI_USER);
    m_strWifiPassWD = pConfig->value(APPConfig::KEY_WIFI_PWD);
    m_bWifiOn = pConfig->value(APPConfig::KEY_WIFI_SWITCH).toInt();
    pConfig->endGroup();

    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_DeleteOnClose);
    setStyleSheet(FRAME_STYLE);

    m_iLastConnectIndex = -1;
    m_iSelectedIndex = 0;
    m_iTimerId = -1;
    m_pLoading = NULL;

    // 定义标题栏，并指定高度
    m_pWifiTitle = new TitleBar(SYSTEM_INFO_TRANSLATE(SystemSet::SET_WIFI_TEXT), this);
    m_pWifiTitle->setFixedHeight(TITLE_HEIGHT);
    connect(m_pWifiTitle, SIGNAL(sigClicked()), this, SLOT(onClose()));

    m_pWifiSwitch = new SettingSwitch(m_bWifiOn, trUtf8("WiFi"), this);
    connect(m_pWifiSwitch, SIGNAL(sigSwitchStatusChanged(bool)), this, SLOT(onSwitchStatusChanged(bool)));
    m_pWifiSwitch->installEventFilter(this);

    m_pSearchFunc = new SearchFunc(this);
    m_pSearchFunc->setText(trUtf8("Select a network ..."));

    m_pListWidget = new QListWidget(this);
    m_pListWidget->setFrameShape(QListWidget::NoFrame);
    m_pListWidget->setSpacing(ITEM_SPACING);
    m_pListWidget->setContentsMargins(0, 0, 0, 0);
    m_pListWidget->verticalScrollBar()->setStyleSheet(SCOROLLBAR_STYLE);
    m_pListWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pListWidget->installEventFilter(this);

    QVBoxLayout *pContentLayout = new QVBoxLayout();
    pContentLayout->setSpacing(0);
    pContentLayout->setMargin(TITLE_SWITCH_SPACING);
    pContentLayout->addWidget(m_pWifiSwitch);

    QVBoxLayout *pSearchLayout = new QVBoxLayout();
    pSearchLayout->setSpacing(0);
    pSearchLayout->setMargin(0);
    pSearchLayout->addSpacing(SWITCH_HOTSPOT_SPACING);
    pSearchLayout->addWidget(m_pSearchFunc, LOAD_STRETCH);
    pSearchLayout->addWidget(m_pListWidget, LIST_STRTCH);

    pContentLayout->addLayout(pSearchLayout);

    QVBoxLayout *vLayout = new QVBoxLayout(this);
    vLayout->setMargin(0);
    vLayout->setSpacing(0);
    vLayout->addWidget(m_pWifiTitle, TITLE_STRETCH);
    //vLayout->addLayout(contentLayout, LIST_STRTCH + SWITCH_STRETCH + LOAD_STRETCH);
    vLayout->addLayout(pContentLayout);
    setLayout(vLayout);

    if(!m_bWifiOn)
    {
        m_pSearchFunc->hide();
        m_pListWidget->setStyleSheet(LIST_HIDDEN_STYLE);
    }
    else
    {
        start();
    }

    qRegisterMetaType<QVWifiInfoData>("QVWifiInfoData");
    connect(&m_Service, SIGNAL(sigWifiInfo(QVWifiInfoData)), this, SLOT(setWifiInfoList(QVWifiInfoData)), Qt::QueuedConnection);
    connect(&m_Service, SIGNAL(sigConnectResult(QString, QString, bool)), this, SLOT(onConnectResult(QString, QString, bool)), Qt::QueuedConnection);
    connect(this, SIGNAL(sigAddItems(QVWifiInfoData)), this, SLOT(onAddItems(QVWifiInfoData)), Qt::QueuedConnection);
    connect(SystemSetService::instance(), SIGNAL(sigWifiStateChanged(bool)), this, SLOT(onWifiStateChanged(bool)));

    //connect(this, SIGNAL(sigScanWifiSsidList()), &m_Service, SLOT(onScanWifiSsidList()), Qt::QueuedConnection);
    //connect(this, SIGNAL(sigConnectWifi(QString, QString)), &m_Service, SLOT(onConnectWifi(QString, QString)), Qt::QueuedConnection);

    m_pLoading = new SettingLoading();
    connect(m_pLoading, SIGNAL(sigClose()), this, SLOT(close()));
    m_pLoading->hide();

    m_eConnectState = CONNECT_FIRST;
    m_qlstWifiInfos = WifiInfoSettings::instance()->readWifiInfos();

}

/*************************************************
功能： 析构函数
*************************************************************/
WifiSettingView::~WifiSettingView()
{
    if(m_pLoading)
    {
        delete m_pLoading;
        m_pLoading = NULL;
    }

    WifiInfoSettings::instance()->saveWifiInfos(m_qlstWifiInfos);
}

/****************************
功能： 开始连接wifi AP
*****************************/
void WifiSettingView::start(void)
{
    m_eConnectState = CONNECT_FIRST;
    m_Service.start();

    //开始搜索
    m_pListWidget->setStyleSheet(LIST_STYLE);
    connect(m_pListWidget, SIGNAL(currentItemChanged(QListWidgetItem*, QListWidgetItem*)), this, SLOT(onItemChanged(QListWidgetItem*, QListWidgetItem*)));

    m_pSearchFunc->show();
    m_pSearchFunc->start();

    QTimer::singleShot(SINGLE_SHOT_INTERVAL, this, SLOT(onSingleShotDelay()));
    startSearchAP();

    return;
}

/****************************
功能： 退出连接wifi AP
*****************************/
void WifiSettingView::stop(void)
{
    m_Service.stop();
    m_pListWidget->setStyleSheet(LIST_HIDDEN_STYLE);
    m_pSearchFunc->stop();
    m_pSearchFunc->hide();
    stopSearchAP();
    return;
}

/****************************
功能： 开始搜索热点信号
*****************************/
void WifiSettingView::startSearchAP(void)
{
    if(-1 == m_iTimerId)
    {
        m_iTimerId = startTimer(SCAN_INTERVAL);
    }
    return;
}

/****************************
功能： 停止搜索热点信号
*****************************/
void WifiSettingView::stopSearchAP(void)
{
    if(-1 != m_iTimerId)
    {
        killTimer(m_iTimerId);
        m_iTimerId = -1;
    }
    return;
}

/****************************
功能： 搜索一次ap信号
*****************************/
void WifiSettingView::searchAP(void)
{
    logInfo("start search wifi...");
    m_Service.scanWifiList();
    return;
}

/*************************************************
功能： 添加wifi信息列表
*************************************************************/
void WifiSettingView::setWifiInfoList(const QVWifiInfoData& infoList)
{
    m_bViewOperating = true;
    if(m_bWifiOn)
    {
        logInfo("wifi has been opened.");
        QVWifiInfoData newItems;
        QVector<int> deleteItemIndex;

        newItems.clear();
        deleteItemIndex.clear();

        for(int j = 0, iSize = m_vWifiInfo.size(); j < iSize; ++j)
        {
            if(indexof(m_vWifiInfo.at(j), infoList) == -1)
            {
                deleteItemIndex << j;
            }
        }

        deleteItems(deleteItemIndex);

        if(!m_bWifiOn)
        {
            //耗时操作之后再次进行判断
            logWarning("wifi has been closed.");
            m_bViewOperating = false;
            return;
        }

        for(int i = 0, iSize = infoList.size(); i < iSize; ++i)
        {
            if(infoList.at(i).m_strWifiName.isEmpty())
            {
                continue;
            }

            if(indexof(infoList.at(i), m_vWifiInfo) == -1) // 避免出现重名wifi热点
            {
                if(indexof(infoList[i], newItems) == -1)   // 底层接口无法避免同名wifi
                {
                    newItems << infoList[i];
                }
            }
        }

        //加锁处理
        {
            QMutexLocker stWifiInfoLocker(&m_mt4WifiInfo);
            m_vWifiInfo += newItems;
        }

        onAddItems(newItems);
        //emit(sigAddItems(newItems));

        if(!m_bWifiOn)
        {
            //耗时操作之后再次进行判断
            logWarning("wifi has been closed.");
            m_bViewOperating = false;
            return;
        }

        if(CONNECT_FIRST == m_eConnectState)        // 判断当前是否连接，如果未连接则检索列表中是否有存下的上次连接的热点
        {
            int defaultIndex = -1;
            for(int i = 0, iSize = m_vWifiInfo.size(); i < iSize; ++i)
            {
                if(m_vWifiInfo.at(i).m_strWifiName == m_strWifiName)
                {
					defaultIndex = i;
                    setCurItemSelected(defaultIndex);
                    break;
                }
            }

            if(defaultIndex != -1 && !m_strWifiName.isEmpty())
            {
                //从配置文件中读取"-"需要转换为""
                if(m_strWifiPassWD == "-")
                {
                    m_strWifiPassWD = "";
                }

                connectAP(m_strWifiName, m_strWifiPassWD);
            }
        }
    }
    else
    {
        logWarning("wifi has been closed.");
    }

    m_bViewOperating = false;

    return;
}

/****************************
 * 入参：sInfo -- 待查找信息
 *      vInfos -- 供查找容器
 出参：int -- 对应下标
      -1 -- 未找到相应下标
功能： 查找符合对应信息的下标
*****************************/
int WifiSettingView::indexof(const WifiInfo& sInfo, const QVWifiInfoData& vInfos)
{
    int index = -1;
    for(int i = 0, iSize = vInfos.size(); i < iSize; ++i)
    {
        if(vInfos.at(i).m_strWifiName == sInfo.m_strWifiName)
        {
            index = 0;
            break;
        }
    }
    return index;
}

/****************************
入参： deleteItemIndex -- 需在列表中删除的热点集合
功能： 删除不存在的热点
*****************************/
void WifiSettingView::deleteItems(const QVector<int>& deleteItemIndex)
{
    disconnect(m_pListWidget, SIGNAL(currentItemChanged(QListWidgetItem*, QListWidgetItem*)), this, SLOT(onItemChanged(QListWidgetItem*, QListWidgetItem*)));
    int iVItemSize = m_vItem.size();
    QVector<SettingFrame*> tempItem;
    QVector<QListWidgetItem*> tempDelegate;
    QVWifiInfoData infoList;

    tempItem.clear();
    tempDelegate.clear();
    infoList.clear();

    for(int i = 0, iSize = deleteItemIndex.size(); i < iSize; ++i)
    {
        if(!m_bWifiOn)
        {
            logWarning("wifi has been closed.");
            return;
        }

        int cnt = deleteItemIndex.at(i);
        //加锁处理
        {
            QMutexLocker stHPDelegLocker(&m_mt4HPItemDeleg);
            if(cnt >= 0 && cnt < iVItemSize && iVItemSize == m_vHotspotItemDelegate.size())
            {
                m_vItem[cnt]->deleteLater();
                m_pListWidget->removeItemWidget(m_vHotspotItemDelegate[cnt]);
                delete m_vHotspotItemDelegate[cnt];
                tempItem.append(m_vItem[cnt]);
                tempDelegate.append(m_vHotspotItemDelegate[cnt]);

                //QCoreApplication::processEvents(QEventLoop::AllEvents); //避免界面卡顿
            }
        }
    }

    //将下标映射成对应的值,并且完成析构
    for(int j = 0, iSize = tempItem.size(); j < iSize; ++j)
    {
        if(!m_bWifiOn)
        {
            logWarning("wifi has been closed.");
            return;
        }

        //加锁处理
        {
            QMutexLocker stItemLocker(&m_mt4Item);
            int index = m_vItem.indexOf(tempItem.at(j));
            if(index != -1)
            {
                m_vItem.remove(index);
            }
        }
    }

    for(int k = 0, iSize = tempDelegate.size(); k < iSize; ++k)
    {
        if(!m_bWifiOn)
        {
            logWarning("wifi has been closed.");
            return;
        }
        //加锁处理
        {
            QMutexLocker stHPDelegLocker(&m_mt4HPItemDeleg);
            int index = m_vHotspotItemDelegate.indexOf(tempDelegate.at(k));
            if(index != -1)
            {
                m_vHotspotItemDelegate.remove(index);
            }
        }
    }

    //通过值查找对应下标在相应的容器中删除
    //加锁处理
    {
        QMutexLocker stWifiInfoLocker(&m_mt4WifiInfo);
        for(int i = 0, iSize = m_vWifiInfo.size(); i < iSize; ++i)
        {
            if(!m_bWifiOn)
            {
                logWarning("wifi has been closed.");
                return;
            }

            if(deleteItemIndex.indexOf(i) == -1)
            {
                infoList << m_vWifiInfo.at(i);
            }
        }

        m_vWifiInfo.clear();
        m_vWifiInfo = infoList;
    }

    connect(m_pListWidget, SIGNAL(currentItemChanged(QListWidgetItem*, QListWidgetItem*)), this, SLOT(onItemChanged(QListWidgetItem*, QListWidgetItem*)));
    return;
}

/****************************
入参： infoList -- 账号列表
功能： 校验账号密码的合法性
*****************************/
void WifiSettingView::onAddItems(const QVWifiInfoData& infoList)
{
    for(int i = 0, iSize = infoList.size(); i < iSize; ++i)
    {
        if(!m_bWifiOn)
        {
            logWarning("wifi has been closed.");
            break;
        }

        if(infoList.at(i).m_strWifiName.isEmpty())
        {
            continue;
        }

        QListWidgetItem* pListWidgetItem = new QListWidgetItem(m_pListWidget);
        pListWidgetItem->setSizeHint(QSize(width() - ITEM_MINIUM_HEIGHT, ITEM_MINIUM_HEIGHT));

        HotspotLabel* pHotspot = new HotspotLabel(infoList.at(i).m_strWifiName, infoList.at(i).m_bIsCipher, this);
        connect(pHotspot, SIGNAL(sigItemPressed(QString)), this, SLOT(onPressWifi(QString)));

        //加锁处理
        {
            QMutexLocker stHPDelegLocker(&m_mt4HPItemDeleg);
            m_vHotspotItemDelegate.append(pListWidgetItem);
            m_pListWidget->addItem(pListWidgetItem);
            m_pListWidget->setItemWidget(pListWidgetItem, pHotspot);
        }

        //加锁处理
        {
            QMutexLocker stItemLocker(&m_mt4Item);
            m_vItem.append(pHotspot);     // 用qlistWidgetItem作为代理，将widget添加进QListWidget中
        }

        //QCoreApplication::processEvents(QEventLoop::AllEvents); //避免界面卡顿
    }

    return;
}

/************************************************
功能：事件过滤器, 处理鼠标点击事件及物理键盘按钮事件
************************************************/
bool WifiSettingView::eventFilter(QObject *pObj, QEvent *pEvent)
{
    return QWidget::eventFilter(pObj, pEvent);
}

/*************************************************
功能： 处理按键事件
*************************************************************/
void WifiSettingView::keyPressEvent(QKeyEvent *e)
{
    switch(e->key())
    {
    case Qt::Key_Escape:
    {
        onClose();
        break;
    }
    case Qt::Key_Enter:
    case Qt::Key_Return:
    {
        if(m_iSelectedIndex >= 0 && m_iSelectedIndex < m_vItem.size())
        {
            if(!m_vWifiInfo.at(m_iSelectedIndex).m_bIsCipher)
            {
                //m_bIsCipher为false表示是加密wifi
                QString qstrName = m_vWifiInfo.at(m_iSelectedIndex).m_strWifiName;
                QString qstrPwd = "";
                if(containsWifiInfos(qstrName, qstrPwd))
                {
                    connectAP(qstrName, qstrPwd);
                }
                else
                {
                    inputWifiPassWD* pView = new inputWifiPassWD(qstrName);
                    connect(pView, SIGNAL(sigClose()), this, SLOT(onInputPassClosed()));
                    connect(pView, SIGNAL(sigWifiInfo(QString, QString)), this, SLOT(onWifiInfo(QString, QString)));
                    pView->show();
                }
            }
            else
            {
                connectAP(m_vWifiInfo.at(m_iSelectedIndex).m_strWifiName, "");
            }
        }
        break;
    }
    case Qt::Key_Up:
    {
        jumpToPreviousRow();
        break;
    }
    case Qt::Key_Down:
    {
        jumpToNextRow();
        break;
    }
    case Qt::Key_Left:
    {
        switchMenuState(true);
        break;
    }
    case Qt::Key_Right:
    {
        switchMenuState(false);
        break;
    }
    default:
    {
        break;
    }
    }

    return;
}

/*************************************************
参数： wifiName -- 用户名
功能： 接收加入的wifi名信号
*************************************************************/
void WifiSettingView::onPressWifi(const QString& wifiName)
{
    if(wifiName.isEmpty())
    {
        return;
    }

    int index = -1;
    for(int i = 0, iSize = m_vWifiInfo.size(); i < iSize; ++i)
    {
        if(wifiName == m_vWifiInfo.at(i).m_strWifiName)
        {
            index = i;
            break;
        }
    }

    if(index >= 0 && index < m_vWifiInfo.size())
    {
        m_iSelectedIndex = index;
        setCurItemSelected(m_iSelectedIndex);
        if(!m_vWifiInfo.at(m_iSelectedIndex).m_bIsCipher)
        {
            //m_bIsCipher为false表示是加密wifi
            QString qstrName = m_vWifiInfo.at(m_iSelectedIndex).m_strWifiName;
            QString qstrPwd = "";
            if(containsWifiInfos(qstrName, qstrPwd))
            {
                connectAP(qstrName, qstrPwd);
            }
            else
            {
                inputWifiPassWD* pView = new inputWifiPassWD(qstrName);
                connect(pView, SIGNAL(sigClose()), this, SLOT(onInputPassClosed()));
                connect(pView, SIGNAL(sigWifiInfo(QString, QString)), this, SLOT(onWifiInfo(QString, QString)));
                pView->show();
            }
        }
        else
        {
            connectAP(m_vWifiInfo.at(m_iSelectedIndex).m_strWifiName, "");
        }
    }

    return;
}

/****************************
功能： 接收输入密码窗口关闭时发出的信号，便于重启搜索wifi ap的定时器
*****************************/
void WifiSettingView::onInputPassClosed(void)
{
    startSearchAP();
    return;
}

/*************************************************
参数： wifiName -- 用户名
      wiifPassWD -- 密码
功能： 加入的wifi名和密码的信号
*************************************************************/
void WifiSettingView::onWifiInfo(QString wifiName, QString wifiPassWD)
{
    connectAP(wifiName, wifiPassWD);
    return;
}

/****************************
功能： 设置标签选项的选中状态，将连接上的信号打勾号，其它若有勾号的标签去掉勾号
*****************************/
void WifiSettingView::setItemSelectState(void)
{
    int iVItemSize = m_vItem.size();
    if(m_iSelectedIndex >= 0 && m_iSelectedIndex < iVItemSize)
    {
        for(int i = 0; i < iVItemSize; ++i)
        {
            HotspotLabel *p = qobject_cast<HotspotLabel *>(m_vItem[i]);
            if(i == m_iSelectedIndex)
            {
                m_iLastConnectIndex = m_iSelectedIndex;
                p->setSelectState(true);
            }
            else if(p->selectState())
            {
                p->setSelectState(false);
            }
        }
    }

    return;
}

/****************************
返回值：true -- 已连接
      false -- 未连接
功能： 是否连接上的状态
*****************************/
bool WifiSettingView::isConnected(void)
{
    bool bState = false;
    for(int i = 0, iSize = m_vItem.size(); i < iSize; ++i)
    {
        HotspotLabel* p = qobject_cast<HotspotLabel *>(m_vItem[i]);
        if(p->selectState())
        {
            bState = true;
            break;
        }
    }
    return bState;
}

/****************************
入参： name -- 账号
      passWD -- 密码
功能： 连接指定ip
*****************************/
void WifiSettingView::connectAP(const QString &name, const QString& passWD)
{
    if(SystemSetService::instance()->isFourGConnected())
    {
        //4G打开，先关闭4G网络，然后使用wifi连接
        SystemSetService::instance()->setFourGOption(SystemSet::FOUR_G_CLOSED);
        emit sigClose4G();
    }

    stopSearchAP();

    if(name.isEmpty())
    {
        return;
    }

    if(m_iLastConnectIndex >= 0 && m_iLastConnectIndex < m_vItem.size())
    {
        HotspotLabel *pLabel = qobject_cast<HotspotLabel *>(m_vItem[m_iLastConnectIndex]);
        if(pLabel)
        {
            pLabel->setSelectState(false);
            m_iLastConnectIndex = -1;
        }
    }

    m_eConnectState = CONNECT_REQUEST_SEND;
    m_Service.connectWifi(name, passWD);

    if(m_pLoading != NULL && (m_eConnectState == CONNECT_REQUEST_SEND))
    {
        m_pLoading->show();
    }
    return;
}

/************************************************
 * 参数：wifiName -- 热点名
 *      passWD -- 密码
 *      bResult -- true 加入成功
 *                 false 加入失败
 * 功能    :处理加入结果
 ************************************************/
void WifiSettingView::onConnectResult(QString wifiName, QString passWD, bool bResult)
{
    logInfo(QString("connect to wifi: %1, result: %2.").arg(wifiName).arg(bResult).toLatin1().data());
    startSearchAP();
    m_eConnectState = CONNECT_REQUEST_GET;
    if(bResult)
    {
        if(m_pLoading != NULL)
        {
            m_pLoading->setLoadingName(trUtf8("Authenticating ..."));
        }

        bool isWifiExist = false;
        for(int i = 0, iSize = m_vWifiInfo.size(); i < iSize; ++i)
        {
            if(m_vWifiInfo.at(i).m_strWifiName == wifiName)
            {
				m_iSigStrength = m_vWifiInfo.at(i).m_iSigStrength;
				m_iSelectedIndex = i;
                setItemSelectState();       // 设置连接热点和其它热点的状态
                isWifiExist = true;
                break;
            }
        }

        emit sigWifiConnected();

        if(isWifiExist)
        {
            //log_debug("%s, %s", wifiName.toLatin1().data(), passWD.toLatin1().data());
            //配置文件存在bug，存""可能会失败，于是需要将""转为"-"存
            QString qstrPwdTemp = passWD.isEmpty() ? "-" : passWD;
            ConfigInstance *pConfig = ConfigManager::instance()->config();
            pConfig->beginGroup(Module::GROUP_APP);
            pConfig->setValue(wifiName, APPConfig::KEY_WIFI_USER);
            pConfig->setValue(qstrPwdTemp, APPConfig::KEY_WIFI_PWD);
            pConfig->setValue(SystemSet::HOT_CLOSED, APPConfig::KEY_HOT_SWITCH);    //close hotspot
            pConfig->endGroup();

            m_strWifiName = wifiName;
            m_strWifiPassWD = passWD;
            //log_debug("%s, %s", wifiName.toLatin1().data(), passWD.toLatin1().data());
            addWifiInfoToList(wifiName, passWD);

            emit sigStartedWifi();
            m_iQueryCaptureIPTimerID = startTimer(1000);
            m_iCapIpTimes = 0;
        }
        else
        {
            // wifi is not exist
        }
    }
    else
    {
        if(m_pLoading)
        {
            if(m_pLoading->isVisible())
            {
                m_pLoading->hide();
            }
        }
        removeWifiInfoToList(wifiName, passWD);
        MsgBox::information("", trUtf8("Unable to connect the hotspot!"));
        // connect failed
    }

    return;
}

/************************************************
 * 输入参数 : bool：发出当前切换开关状态的信号
 * true -- 打开
 * false -- 关闭
 * 功能     : 接收切换开关状态的信号
 ************************************************/
void WifiSettingView::onSwitchStatusChanged(bool bState)
{
    if(m_bViewOperating)
    {
        logWarning("view is operating.");
        return;
    }

    m_bWifiOn = bState;
    if(!m_bWifiOn)
    {
        disconnect(m_pListWidget, SIGNAL(currentItemChanged(QListWidgetItem*, QListWidgetItem*)), this, SLOT(onItemChanged(QListWidgetItem*, QListWidgetItem*)));

        //加锁处理
        {
            QMutexLocker stWifiInfoLocker(&m_mt4WifiInfo);
            m_vWifiInfo.clear();
        }

        //加锁处理
        {
            QMutexLocker stItemLocker(&m_mt4Item);
            for(int i = 0, iSize = m_vItem.size(); i < iSize; ++i)
            {
                m_vItem[i]->deleteLater();
            }

            m_vItem.clear();
        }

        //加锁处理
        {
            QMutexLocker stHPDelegLocker(&m_mt4HPItemDeleg);
            for(int index = 0, iSize = m_vHotspotItemDelegate.size(); index < iSize; ++index)
            {
                m_pListWidget->removeItemWidget(m_vHotspotItemDelegate[index]);
                delete m_vHotspotItemDelegate[index];
            }
            m_vHotspotItemDelegate.clear();
        }

        stop();
    }
    else
    {
        start();
        emit sigStartedWifi();
    }

    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(m_bWifiOn, APPConfig::KEY_WIFI_SWITCH);
    pConfig->endGroup();
    return;
}

/****************************
功能： 单次延时处理的槽函数
*****************************/
void WifiSettingView::onSingleShotDelay(void)
{
    searchAP();
    return;
}

/****************************
输入参数:
        pCurrentItem:当前选中的item
        pPreviousItem:之前选中的item
功能： 接收item被改变的信号
*****************************/
void WifiSettingView::onItemChanged(QListWidgetItem *pCurrentItem, QListWidgetItem *pPreviousItem)
{
    int iCurrentIndex = m_vHotspotItemDelegate.indexOf(pCurrentItem);
    int iPreviousIndex = m_vHotspotItemDelegate.indexOf(pPreviousItem);
    if(iCurrentIndex != -1)
    {
        m_iSelectedIndex = iCurrentIndex;
        m_vItem.at(iCurrentIndex)->setSelected(true);
    }
    if(iPreviousIndex != -1)
    {
        m_vItem.at(iPreviousIndex)->setSelected(false);
    }
    return;
}

/****************************
函数名： jumpToNextRow
输入参数:NULL
输出参数：NULL
返回值：NULL
功能： 跳转下一行
*****************************/
void WifiSettingView::jumpToNextRow()
{
    int iSize = m_vHotspotItemDelegate.size();
    if(iSize > 0)
    {
        m_iSelectedIndex = (m_iSelectedIndex + 1) % iSize;
        setCurItemSelected(m_iSelectedIndex);
    }

    return;
}

/****************************
函数名： jumpToPreviousRow
输入参数:NULL
输出参数：NULL
返回值：NULL
功能： 跳转上一行
*****************************/
void WifiSettingView::jumpToPreviousRow()
{
    int iSize = m_vHotspotItemDelegate.size();
    if(iSize > 0)
    {
        m_iSelectedIndex = (iSize + m_iSelectedIndex - 1) % iSize;
        setCurItemSelected(m_iSelectedIndex);
    }

    return;
}

/*************************************************
功能： 设置当前item为选中状态
*************************************************************/
void WifiSettingView::setCurItemSelected(int iIndex)
{
    if(m_pListWidget && (iIndex >= 0 && iIndex < m_vHotspotItemDelegate.size()))
    {
        m_iSelectedIndex = iIndex;
        m_pListWidget->setCurrentItem(m_vHotspotItemDelegate.at(m_iSelectedIndex));
    }
    return;
}

/*************************************************
功能： 处理定时事件，定时检索wifi热点
*************************************************************/
void WifiSettingView::timerEvent(QTimerEvent *e)
{
    int iTimerId = e->timerId();
    if(iTimerId == m_iTimerId)
    {
        searchAP();
    }
    else if(iTimerId == m_iQueryCaptureIPTimerID)
    {
        if(isIPCaptured())
        {
            ConfigInstance *pConfig = ConfigManager::instance()->config();
            pConfig->beginGroup(Module::GROUP_APP);
            pConfig->setValue(SystemSet::HOT_CLOSED, APPConfig::KEY_HOT_SWITCH);
            pConfig->endGroup();

            killTimer(m_iQueryCaptureIPTimerID);
            m_iQueryCaptureIPTimerID = -1;
            if(m_pLoading)
            {
                if(m_pLoading->isVisible())
                {
                    m_pLoading->hide();
                }
            }
            MsgBox::information("", trUtf8("Access hotspot successfully!"));
        }
        else if(m_iCapIpTimes >= MAX_CAP_IP_TIME)
        {
            killTimer(m_iQueryCaptureIPTimerID);
            m_iQueryCaptureIPTimerID = -1;
            if(m_pLoading)
            {
                if(m_pLoading->isVisible())
                {
                    m_pLoading->hide();
                }
            }
            MsgBox::information("", trUtf8("Get IP info failed!"));
        }
        else
        {
            ++m_iCapIpTimes;
        }
    }

    return;
}

/*************************************************
功能： 窗口关闭事件
输入参数:
    event -- 事件
*************************************************************/
void WifiSettingView::closeEvent(QCloseEvent* event)
{
    Q_UNUSED(event);
    //stopSearchAP();
    if(-1 != m_iQueryCaptureIPTimerID)
    {
        killTimer(m_iQueryCaptureIPTimerID);
        m_iQueryCaptureIPTimerID = -1;
    }
    return;
}

bool WifiSettingView::isIPCaptured()
{
    bool isWifiUsable = false;
    QList<QNetworkInterface> list = QNetworkInterface::allInterfaces();
    for(int i = 0, iSize = list.count(); i < iSize; ++i)
    {
        QString qstrDetail = QString("dev name: %1, MAC: %2").arg(list[i].name()).arg(list[i].hardwareAddress());
        if(list[i].name().contains("wlan0"))
        {
            for(int j = 0, iCount = list[i].addressEntries().count(); j < iCount; ++j)
            {
                QString qstrIp = list[i].addressEntries()[j].ip().toString();
                qstrDetail = QString("%1, ip: %2.").arg(qstrDetail).arg(qstrIp);
                logInfo(qstrDetail.toLatin1().data());
                if(qstrIp != "0.0.0.0")
                {
                    isWifiUsable = true;
                    break;
                }
            }
        }        
    }

    return isWifiUsable;
}

/****************************
 * 入参：qstrName -- wifi的信息
 出参：qstrPwd -- 密码信息
功能： 判断保存的wifi列表中是否存在qstrName的信息，存在则给出其密码
*****************************/
bool WifiSettingView::containsWifiInfos(const QString qstrName, QString& qstrPwd)
{
    bool bRet = false;

    if(qstrName.isEmpty())
    {
        return bRet;
    }

    for(int i = 0, iSize = m_qlstWifiInfos.size(); i < iSize; ++i)
    {
        if(m_qlstWifiInfos.at(i).qstrName == qstrName)
        {
            qstrPwd = m_qlstWifiInfos.at(i).qstrPwd;
            bRet = true;
            break;
        }
    }

    return bRet;
}

/****************************
 * 入参：qstrName -- wifi名字
 *      qstrPwd -- wifi密码
功能： 更新连接成功的wifi到list中
*****************************/
void WifiSettingView::addWifiInfoToList(const QString& qstrName, const QString& qstrPwd)
{
    if(qstrName.isEmpty())
    {
        return;
    }

    bool bFind = false;
    int iIdx = 0;
    for(int i = 0, iSize = m_qlstWifiInfos.size(); i < iSize; ++i)
    {
        if(m_qlstWifiInfos.at(i).qstrName == qstrName)
        {
			iIdx = i;
            bFind = true;
            break;
        }
    }

    WifiSaveInfo stInfo;
    stInfo.qstrName = qstrName;
    stInfo.qstrPwd = qstrPwd;
	stInfo.iSigStrength = m_iSigStrength;
    if(bFind)
    {
        m_qlstWifiInfos.replace(iIdx, stInfo);
    }
    else
    {
        m_qlstWifiInfos.push_front(stInfo);
    }

	//将索引对应的热点名保存到系统中
	logInfo(QString("qstrName %1 m_iSelectedIndex %2").arg(stInfo.qstrName).arg(m_iSelectedIndex));
	SystemSetService::instance()->saveWifiSelectedIndex(m_iSelectedIndex, stInfo.iSigStrength);
	SystemSetService::instance()->saveSelectedWifiName(m_iSelectedIndex, stInfo.qstrName);

    return;
}

/****************************
 * 入参：qstrName -- wifi名字
 *      qstrPwd -- wifi密码
功能： 连接失败后清除原有的连接成功的wifi到list中
*****************************/
void WifiSettingView::removeWifiInfoToList(const QString& qstrName, const QString& qstrPwd)
{
    WifiSaveInfo stInfo;
    stInfo.qstrName = qstrName;
    stInfo.qstrPwd = qstrPwd;
    m_qlstWifiInfos.removeAll(stInfo);
    return;
}

/****************************
 * 入参：bOn -- true：打开，false：关闭
功能： 切换wifi扫描的开关
*****************************/
void WifiSettingView::switchMenuState(bool bOn)
{
    m_pWifiSwitch->setSwitchState(bOn, true);
    return;
}

/****************************
功能： 响应关闭
*****************************/
void WifiSettingView::onClose()
{
    if(m_bViewOperating)
    {
        logWarning("view is operating.");
        return;
    }

    stopSearchAP();
    disconnect(m_pListWidget, SIGNAL(currentItemChanged(QListWidgetItem*, QListWidgetItem*)), this, SLOT(onItemChanged(QListWidgetItem*, QListWidgetItem*)));
    disconnect(&m_Service, SIGNAL(sigWifiInfo(QVWifiInfoData)), this, SLOT(setWifiInfoList(QVWifiInfoData)));
    disconnect(&m_Service, SIGNAL(sigConnectResult(QString, QString, bool)), this, SLOT(onConnectResult(QString, QString, bool)));
    disconnect(SystemSetService::instance(), SIGNAL(sigWifiStateChanged(bool)), this, SLOT(onWifiStateChanged(bool)));
    close();
    return;
}

/****************************
功能： 响应wifi连接信息变化消息
*****************************/
void WifiSettingView::onWifiStateChanged(bool bConnState)
{
    if(m_bWifiOn)
    {
        //wifi关闭，需要修改界面的连接状态
        m_bViewOperating = true;

        ConfigInstance* pConfig = ConfigManager::instance()->config();
        pConfig->beginGroup(Module::GROUP_APP);
        QString qstrWifiName = pConfig->value(APPConfig::KEY_WIFI_USER);
        QString qstrWifiPwd = pConfig->value(APPConfig::KEY_WIFI_PWD);
        pConfig->endGroup();

        QMutexLocker stLocker(&m_mt4Item);
        for(int i = 0, iSize = m_vItem.size(); i < iSize; ++i)
        {
            HotspotLabel *p = qobject_cast<HotspotLabel *>(m_vItem[i]);
            if(bConnState)
            {
                if(qstrWifiName == p->getName() && !(p->selectState()))
                {
                    p->setSelectState(true);
                    m_iLastConnectIndex = i;
                }
            }
            else
            {
                if(p->selectState())
                {
                    p->setSelectState(false);
                    m_iLastConnectIndex = -1;
                }
            }
        }

        m_bViewOperating = false;
    }
    return;
}

