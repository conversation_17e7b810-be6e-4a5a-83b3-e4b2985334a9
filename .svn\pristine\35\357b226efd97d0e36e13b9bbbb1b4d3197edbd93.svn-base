#ifndef GLOBAL_DEF_H
#define GL<PERSON><PERSON>L_DEF_H

#include <QObject>
#include <QString>


typedef enum _LanguageOption_
{
    LANGUAGE_NONE = -1,
    LANGUAGE_CHINESE = 0,
    LANGUAGE_ZHTW,
    LANGUAGE_ENGLISH,
    LANGUAGE_KOREAN,
    LANGUAGE_VIETNAMESE,
    LANGUAGE_DEDE,
    LANGUAGE_FRFR,
    LANGUAGE_RURU,
    LANGUAGE_ESES,
    LANGUAGE_PTPT,
    LANGUAGE_ARSA,
    LANGUAGE_MAX,
    LANGUAGE_DEFAULT = LANGUAGE_ENGLISH
}LanguageOption;


const QString APP_DEFAULT_USER_ID = "root";
const QString APP_DEFAULT_USER_PWD = "Pd@root777";
const QString DEV_MODEL_PDSTARS = "PDS-T95";
const QString DEV_MODEL_PMDT = "PDStar";
const QString DEV_MODEL_T95DL = "T95-DL";
const QString DEV_LOGO_PATH = "/opt/bin-bash/images/logo.png";
const QString DEV_PDSTARS_LOGO_PATH = "/opt/bin-bash/images/pdstars_logo.png";
const QString DEV_PMDT_LOGO_PATH = "/opt/bin-bash/images/pmdt_logo.png";
const QString DEV_PDSTARS_DEFAULT_UPG_IP = "************";
const quint16 DEV_PDSTARS_DEFAULT_UPG_PORT = 18090;
const QString DEV_PMDT_DEFAULT_UPG_IP = "*************";
const quint16 DEV_PMDT_DEFAULT_UPG_PORT = 18090;
const QString DEV_PMDT_EN_COMPANY_NAME = "Power Monitoring and Diagnostic Technology Ltd.";
const QString DEV_PDSTARS_EN_COMPANY_NAME = "PDStars Electric Co.,Ltd.";
const QString DEV_CABEL_CUSTOM_COMPANY_NAME = QString::fromUtf8("国网北京电缆公司与华乘科技联合研制");



//QString DEV_DEFAULT_NAME = QObject::trUtf8("Intelligent Handheld Partial Discharge Detector");
//QString DEV_MANUFACTURE = QObject::trUtf8("PDStars Electric Co.,Ltd.");


#endif // GLOBAL_DEF_H
