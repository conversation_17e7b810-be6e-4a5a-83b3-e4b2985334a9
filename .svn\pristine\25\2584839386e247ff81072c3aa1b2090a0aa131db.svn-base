#include "calibratesignalwidget.h"
#include <QBoxLayout>
#include <QKeyEvent>
#include "tev/calibrate/calibrateviewdefine.h"

const QString LABEL_STYLE = "QLabel{color: black; font-size: 18px}";

const QString LABEL_SIGNAL_STYLE = "QLabel{color: black; border: 1px solid; border-color: rgb(224, 224, 224);"
                                   "border-radius: 8px; padding: 2px 4px; font-size:18px}";


CalibrateSignalWidget::CalibrateSignalWidget(const int iWidth, const int iHeight, const int iSignalCnt, QWidget *parent)
    : CalibrateBaseWidget(iWidth, iHeight, parent)
    , m_iSignalCnt(iSignalCnt)
    , m_iSignalSelectedIdx(-1)
{
    createUI();
}

CalibrateSignalWidget::~CalibrateSignalWidget()
{
}

void CalibrateSignalWidget::createUI()
{
    m_qvtqlblSignals.clear();
    m_qvtTexts.clear();

    QLabel* plblInputSig = new QLabel(CALIBRATE_VIEW_TRANSLATE(Calibrate::TEXT_INPUT));
    plblInputSig->setStyleSheet(LABEL_STYLE);
    plblInputSig->setAlignment(Qt::AlignVCenter);

    for(int i = 0; i < m_iSignalCnt; ++i)
    {
        QLabel* plblSig = new QLabel();
        plblSig->setStyleSheet(LABEL_SIGNAL_STYLE);
        plblSig->setAlignment(Qt::AlignCenter);
        plblSig->installEventFilter(this);
        QString qstrText = QString(CALIBRATE_VIEW_TRANSLATE(Calibrate::TEXT_SIGNAL)) + QString(" %1").arg(i + 1);
        m_qvtTexts.append(qstrText);

        plblSig->setText(qstrText);
        m_qvtqlblSignals.append(plblSig);
    }

    if(0 < m_iSignalCnt)
    {
        setSelectedSignalLabel(m_qvtqlblSignals[0]);
    }

    QHBoxLayout* pMainLayout = new QHBoxLayout();
    pMainLayout->setMargin(5);
    pMainLayout->setSpacing(0);
    pMainLayout->addWidget(plblInputSig);

    for(int i = 0; i < m_iSignalCnt; ++i)
    {
        pMainLayout->addWidget(m_qvtqlblSignals.at(i));
        pMainLayout->addSpacing(5);
    }

    setLayout(pMainLayout);
    return;
}

void CalibrateSignalWidget::setSelectedViewFocused()
{
    if(0 < m_iSignalCnt)
    {
        setSelectedSignalLabel(m_qvtqlblSignals[m_iSignalSelectedIdx]);
        repaint();
    }

    return;
}

void CalibrateSignalWidget::selectedPreSignal()
{
    m_iSignalSelectedIdx = (m_qvtqlblSignals.size() + m_iSignalSelectedIdx - 1) % m_qvtqlblSignals.size();
    setCalibrateSignal(m_iSignalSelectedIdx);
    return;
}

void CalibrateSignalWidget::selectedNxtSignal()
{
    m_iSignalSelectedIdx = (m_iSignalSelectedIdx + 1) % m_qvtqlblSignals.size();
    setCalibrateSignal(m_iSignalSelectedIdx);
    return;
}

void CalibrateSignalWidget::setCalibrateSignal(int iIndex)
{
    if(0 <= iIndex && iIndex < m_iSignalCnt)
    {
        m_iSignalSelectedIdx = iIndex;
        setSelectedSignalLabel(m_qvtqlblSignals[m_iSignalSelectedIdx]);
    }

    return;
}

int CalibrateSignalWidget::getCalibrateSelectedSignalIndex()
{
    return m_iSignalSelectedIdx;
}

void CalibrateSignalWidget::setSelectedSignalLabel(QObject *pObj)
{
    QLabel* plblSignal = static_cast<QLabel* >(pObj);
    for(int i = 0; i < m_iSignalCnt; ++i)
    {
        QString qstrText = m_qvtTexts[i];
        if(m_qvtqlblSignals[i] == plblSignal)
        {
            m_iSignalSelectedIdx = i;
            m_qvtqlblSignals[m_iSignalSelectedIdx]->setText(getSignalText(qstrText, true));
        }
        else
        {
            m_qvtqlblSignals[i]->setText(getSignalText(qstrText, false));
        }
    }

    //update();

    return;
}

QString CalibrateSignalWidget::getSignalText(const QString qstrText, const bool bSelected)
{
    QString qstrSignalText = "";

    QColor qColor = bSelected ? Qt::blue : Qt::black;
    qstrSignalText = QString("<font color=%1>%2</font>").arg(qColor.name()).arg(qstrText);

    return qstrSignalText;
}

bool CalibrateSignalWidget::eventFilter(QObject *pObj, QEvent *pEvent)
{
    bool bRet = false;
    int iEventType = pEvent->type();
    if(iEventType == QEvent::MouseButtonPress)
    {
        setSelectedSignalLabel(pObj);
    }
    /*else if(iEventType == QEvent::KeyPress)
    {
        QKeyEvent* pKeyEvent = static_cast<QKeyEvent*>(pEvent);
        if(Qt::Key_Up == pKeyEvent->key() || Qt::Key_Down == pKeyEvent->key())
        {
            sigKeyPressEvent(pEvent);
            bRet = true;
        }
        else
        {
            bRet = QFrame::eventFilter(pObj, pEvent);
        }
    }*/
    else
    {
        bRet = QFrame::eventFilter(pObj, pEvent);
    }

    return bRet;
}


