#-------------------------------------------------
#
# Project created by QtCreator 2016-11-21T10:36:24
#
#-------------------------------------------------

QT       -= gui

TARGET = core
TEMPLATE = lib

DEFINES += CORE_LIBRARY

SOURCES += core.cpp \
    model/HCAffair.cpp \
    model/HCCom.cpp \
    model/HCProtocol.cpp \
    model/HCService.cpp

HEADERS += core.h\
        core_global.h \
    model/HCAffair.h \
    model/HCCom.h \
    model/HCProtocol.h \
    model/HCService.h \
    model/HCStatus.h

unix {
    target.path = /usr/lib
    INSTALLS += target
}
