/*
* Copyright (c) 2019.04，南京华乘电气科技有限公司
* All rights reserved.
*
* localbusinessstandard.h
*
* 初始版本：1.0
* 作者：张浪
* 创建日期：2020年09月17日
* 摘要：T95接入终端标准传输业务，目前支持杭州华云、北京融智通、上海艾飞、西安创亦等客户的定制接入

* 当前版本：1.0
*/
#ifndef _LOCALBUSINESSSTANDARD_H_
#define _LOCALBUSINESSSTANDARD_H_

#include "localbusinessbase.h"

class LocalBusinessStandard : public LocalBusinessBase
{
    Q_OBJECT
public:

    /****************************
     * 功能：构造函数
     * 输入参数：
     *      parent：父控件
     * ***************************/
    explicit LocalBusinessStandard(QObject *parent = 0);

    /****************************
     * 功能：设置连接状态
     * 输入参数：
     *      bConnected：true -- 已连接，false -- 未连接
     * ***************************/
    void setLinkState(bool bConnected);

    /****************************
     * 功能：发送心跳
     * ****************************/
    void sendHeartbeat();

    /****************************
     * 功能：发送任务上传数据
     * 输入参数：
     *      qbaXmlData：xml结构的任务内容
     *      qbaZipFileContent：检测数据压缩文件内容
     *      qstrZipFileName：压缩文件名（只需要文件名）
     * ****************************/
    void sendUploadData(const QByteArray &qbaXmlData, const QByteArray &qbaZipFileContent, const QString &qstrZipFileName);

public slots:
    /****************************
     * 功能：解析数据
     * 输入参数：
     *      stFrameInfo：帧信息
     * ****************************/
    void onParseData_EX(const Protocol::CommFrameInfo &stFrameInfo);

protected:
    /*********************************
     * 功能：定时器处理函数
     * 输入参数：
     *      pEvent：定时事件
     * *******************************/
    void timerEvent(QTimerEvent *pEvent);

private:
    quint8 m_qui8ReqSeq;
    Protocol::CommFrameInfo m_stHBFrameInfo;

};
#endif // _LOCALBUSINESSSTANDARD_H_
