#ifndef SUBTASKVIEW_H
#define SUBTASKVIEW_H

#include "PDAUi/PDAUiView/pdalistview.h"
#include "PDAUi/PDAUiBean/pdalistchart.h"
#include "customaccesstask/taskmanager.h"
#include "systemsetting/systemsetservice.h"

class SubTaskView : public PDAListView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent:父控件指针
    *************************************************************/
    explicit SubTaskView(const QString& title, CustomAccessTaskNS::MainTaskInfo mainInfo, QWidget *parent = 0);

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~SubTaskView();

protected slots:
    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

    /*************************************************
    功能： 槽，响应条目被点击后的事件
    输入参数：
            id -- 条目序号
    *************************************************************/
    void onItemClicked( int id );

    //任务发生改变的处理
    void onTaskChanged();

    /*************************************************
    功能： 测点列表关闭
    *************************************************************/
    void onTestPointViewClosed( void );

    /*************************************************
    功能： 槽，响应******
    *************************************************************/
    void onUploadFinished();

private:
    //初始化主任务列表
    void initTaskItem();

    /*****************************************
     * 功能：初始化按钮栏信息
     * **********************************************/
    void initBtnBarInfo();

private:
    CustomAccessTaskNS::MainTaskInfo m_mainTaskInfo;
    QVector<CustomAccessTaskNS::SubTaskInfo> m_subTasks;
    QString m_strCurrentSubTaskName;
    bool m_bIsUploading;

    SystemSet::CustomAccessMode m_eAccessMode;
    SystemSet::AccessProtocol m_eAccessProtocol;
    ButtonInfo::Info *m_pBtnInfo;
    quint8 m_qui8BtnCnt;
};

#endif // SUBTASKVIEW_H
