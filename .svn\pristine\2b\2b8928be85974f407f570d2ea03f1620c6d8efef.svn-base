#include "uhfprpstestbgview.h"
#include "window/Window.h"
#include "uhf/UHFViewConfig.h"
#include "uhf/UHFConfig.h"
#include "controlButton/PopupButton.h"
#include "controlButton/RadioButton.h"
#include "messageBox/msgbox.h"
#include "appconfig.h"
#include "View.h"
#include "dataSave/DataFileInfos.h"
#include "pda/pdaservice.h"
#include "PDAUi/PDAUiView/pdaloginview.h"
#include "rfid/RFIDScanView.h"
#include "rfid/RFIDReadView.h"
#include "globalerrprocess.h"
#include "systemsetting/shutdownpower.h"
//data save
#include "equipmentinfo.h"
#include "systemsetting/systemsetservice.h"
#include "prps/prpsmapdefine.h"
#include "prps/prpddatamap.h"
#include "prps/prpsdatamap.h"
#include "mapdatafactory.h"
#include "customaccessUi/commonviewconfig.h"
#include "customaccessUi/commonitemview/commonitemlistview.h"
#include "dataspecification/dataspecification.h"
#include "dataspecification/prps/prpsspectrum.h"
#include "dataspecification/prps/prpdspectrum.h"
#include "customaccessUi/customaccessui_func.h"
#include "global_log.h"


using namespace errorProcess;

typedef enum _UHFPrpsButton_
{
    BUTTON_UHF_SAMPLE = 0,//采样

    BUTTON_UHF_SAVE_DATA,//保存数据
    BUTTON_UHF_LOAD_DATA,//载入数据

    BUTTON_MENU,//配置

    //more
    BUTTON_ALTAS_TYPE,//图谱类型
    BUTTON_THRESHOLD,//阈值
    BUTTON_UHF_FORWARD_GAIN, //前置增益
    BUTTON_UHF_PHASE_SHIFT,//相位偏移    
    BUTTON_IS_ACCUMULATION,  //是否使用累积
    BUTTON_UHF_BAND_WIDTH,//带宽
    BUTTON_UHF_SYNC_SOURCE,//同步方式
    //BUTTON_UHF_DELETE_DATA,//删除数据
}UHFPrpsButton;

//相位偏移
const ButtonInfo::SliderValueConfig s_UHFPhaseShift =
{
    UHF::PHASE_MIN, UHF::PHASE_MAX, UHF::PHASE_STEP
};

//前置增益
const ButtonInfo::RadioValueConfig s_UHFForwardGainCfg =
{
    UHF::FORWARD_GAIN_ENABLE_OPTION, sizeof(UHF::FORWARD_GAIN_ENABLE_OPTION)/sizeof(char*)
};

//同步源
const ButtonInfo::RadioValueConfig s_UHFSyncCfg =
{
    UHF::TEXT_SYNC_OPTIONS, sizeof(UHF::TEXT_SYNC_OPTIONS)/sizeof(char*)
};

//带宽
const ButtonInfo::RadioValueConfig s_UHFBandWidthCfg =
{
    UHF::BAND_WIDTH_OPTION, UHF::BAND_WIDTH_COUNT
};

//是否累积
const ButtonInfo::RadioValueConfig s_IsAccumulationCfg =
{
    UHF::TEXT_ACCUMULATIVE_TIME_OPTIONS, sizeof(UHF::TEXT_ACCUMULATIVE_TIME_OPTIONS)/sizeof(char*)
};

//图谱类型
const ButtonInfo::RadioValueConfig s_AltasTypeCfg =
{
    UHF::TEXT_ALTAS_TYPE_OPTIONS, sizeof(UHF::TEXT_ALTAS_TYPE_OPTIONS)/sizeof(char*)
};

//阈值 单位:%
const ButtonInfo::RadioValueConfig s_ThresholdCfg =
{
    UHF::TEXT_THRESHOLD_OPTIONS, sizeof(UHF::TEXT_THRESHOLD_OPTIONS)/sizeof(char*)
};

//控制按钮定义
const ButtonInfo::Info s_UHFButtonInfo[] =
{
    { BUTTON_UHF_SAMPLE, { ButtonInfo::COMMAND, UHF::TEXT_SAMPLE, NULL, ":/images/sampleControl/sample.png",NULL } },//采样
    { BUTTON_UHF_SAVE_DATA, { ButtonInfo::COMMAND, UHF::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL } },//保存数据
    { BUTTON_UHF_LOAD_DATA, { ButtonInfo::COMMAND, UHF::TEXT_LOAD_DATA, NULL, ":/images/sampleControl/importData.png",NULL } },//载入数据
    { BUTTON_MENU, { ButtonInfo::COMMAND, UHF::TEXT_MORE_CONFIG, NULL, NULL, NULL } },//配置
};

//"更多.."按钮定义
const ButtonInfo::Info s_UHFButtonInfoMore[] =
{
    { BUTTON_ALTAS_TYPE, { ButtonInfo::RADIO, UHF::TEXT_ALTAS_TYPE, NULL, NULL, &s_AltasTypeCfg } },//图谱类型
    { BUTTON_THRESHOLD, { ButtonInfo::RADIO, UHF::TEXT_PRPS_NOISEREDUCTION, NULL, NULL, &s_ThresholdCfg } },//阈值
    { BUTTON_UHF_FORWARD_GAIN, { ButtonInfo::RADIO, UHF::TEXT_FORWARD_GAIN, NULL, ":/images/sampleControl/forwardGain.png", &s_UHFForwardGainCfg } },//前置增益
    { BUTTON_UHF_PHASE_SHIFT, { ButtonInfo::FIXED_STEP_SLIDER, UHF::TEXT_PHASE_ALIAS, UHF::TEXT_DEGREE, ":/images/sampleControl/phaseShift.png", &s_UHFPhaseShift } },//相位偏移
    { BUTTON_IS_ACCUMULATION, { ButtonInfo::RADIO, UHF::TEXT_ACCUMULATIVE_TIME, NULL, ":/images/sampleControl/brandWidth.png", &s_IsAccumulationCfg } },//是否累积
    { BUTTON_UHF_BAND_WIDTH, { ButtonInfo::RADIO, UHF::TEXT_BAND_WIDTH, NULL, ":/images/sampleControl/brandWidth.png", &s_UHFBandWidthCfg } },//带宽
    //{ BUTTON_UHF_SYNC_SOURCE, { ButtonInfo::RADIO, UHF::TEXT_SYNC_SOURCE, NULL, ":/images/sampleControl/syncMode.png", &s_UHFSyncCfg } },//同步方式
    //{ BUTTON_UHF_DELETE_DATA, { ButtonInfo::COMMAND, UHF::TEXT_DELETE_DATA, NULL, ":/images/sampleControl/delete.png", NULL } },//删除数据
};

const int MS_PER_MIN = 60 * 1000;  //每分钟对应的ms
const int MOVE_PERIODS_STEP = 1; //prps每次推进的周期数
const int INVALID_USER = -1;

/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
UHFPRPSTestBGView::UHFPRPSTestBGView(const QString &strTitle, SubTask *pSubTask, const QString &strGapId, QWidget *parent) :
    UhfPRPSViewBase( strTitle,parent ),m_strGapId(strGapId)
{
    registerMaps();
    m_pSubTask = pSubTask;

    QList<QString> lStrBGFileNames;
    lStrBGFileNames = m_pSubTask->bgFileList( strGapId );
    m_pCommonItemListView = new CommonItemListView(QObject::trUtf8("Test Data List"),lStrBGFileNames);
    m_pCommonItemListView->hide();
    connect( m_pCommonItemListView, SIGNAL(sigItemSelected( qint32)), this,SLOT(onItemActivated( qint32)));
    m_testedCount = lStrBGFileNames.size();
    m_strBayName = pSubTask->gapInfo( strGapId ).s_strName;
    initData();

    ChartWidget *pWidget = createChart(parent);
    setChart(pWidget);

    PushButtonBar *pButtonBar  = createButtonbar(parent);
    m_pSampleBtn = pButtonBar->button(BUTTON_UHF_SAMPLE);

    createMoreConfigButtonBar(UHF::CONTEXT, s_UHFButtonInfoMore, sizeof(s_UHFButtonInfoMore)/sizeof(ButtonInfo::Info));
    ((PopupButton*)(buttonBar()->button(BUTTON_UHF_FORWARD_GAIN)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_ALTAS_TYPE)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_THRESHOLD)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_IS_ACCUMULATION)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_UHF_BAND_WIDTH)))->setPopupMode(PopupWidget::SWITCH_MODE);

    setButtonBarDatas();
    setChartParameters();
    setAllWorkSets();

    startSample();
    m_eState = STATE_SAMPLING;
    setSampleBtnText(m_eState);
    m_bPlayBacked = false;

    if( titleBar() != NULL )
    {
        disconnect(titleBar(), SIGNAL(sigClicked()), this, SLOT(close()));
        connect(titleBar(), SIGNAL(sigClicked()), this, SLOT(onTitleBarClicked()));
    }
}

/*************************************************
功能： 析构
*************************************************************/
UHFPRPSTestBGView::~UHFPRPSTestBGView( )
{
    saveConfig(); // 存储到配置文件中

    if(NULL != m_pCommonItemListView)
    {
        delete m_pCommonItemListView;
        m_pCommonItemListView = NULL;
    }
}

void UHFPRPSTestBGView::registerMaps()
{
    MapDataFactory::registerClass<PRPSDataMap>(XML_FILE_NODE_PRPS);//图谱根节点tag名
    MapDataFactory::registerClass<PRPDDataMap>(XML_FILE_NODE_PRPD);//图谱根节点tag名
}
/************************************************
 * 函数名   : onSignalChanged
 * 输入参数 : eState: 信号状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 槽函数，信号状态的改变
 ************************************************/
void UHFPRPSTestBGView::onSignalChanged(Module::SignalState eState)
{
    if(eState == Module::SIGNAL_STATE_EXIST)
    {
        m_pChart->setConnected(true);
    }
    else
    {
        m_pChart->setConnected(false);
    }
}

/************************************************
 * 函数名   : onSyncStateChanged
 * 输入参数 : eState: 同步状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 槽函数，同步状态的改变
 ************************************************/
void UHFPRPSTestBGView::onSyncStateChanged(Module::SyncState eState)
{
    if(m_eSyncState != eState)
    {
        m_eSyncState = eState;
        m_pChart->setSync((PrpsGlobal::SyncSource)m_eSyncSource, (PrpsGlobal::SyncState)eState);
    }
    return;
}

/************************************************
 * 函数名   : setChartParameters
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 给图谱设置参数
 ************************************************/
void UHFPRPSTestBGView::setChartParameters()
{
    m_pChart->setAltasType(m_eAltasType);

    m_pChart->setSync( (PrpsGlobal::SyncSource) m_eSyncSource, (PrpsGlobal::SyncState) m_eSyncState );

    /*系统频率*/
    m_pChart->setPowerFreq(PrpsGlobal::FREQ_50);

    /*运行状态*/
    if(m_eState == STATE_SAMPLING)
    {
        m_pChart->setRunningMode( true );
    }
    else
    {
        m_pChart->setRunningMode( false );
    }
    /*相位偏移*/
    m_pChart->setPhaseOffset( m_iPhaseAlias );


    updateAccumulativeTime();

    m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
    double dRange = m_pChart->rangeMax() - m_pChart->rangeMin();
    if (!qFuzzyIsNull(dRange))
    {
        m_pChart->setPRPDThresholdPercentage(10.0 / dRange);
    }

    return;
}

/************************************************
 * 函数名   : setAllWorkSets
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 设置所有的工作参数
 ************************************************/
void UHFPRPSTestBGView::setAllWorkSets()
{
    setWorkMode( UHF::MODE_PRPS );
    setFordwardGain( m_eForwardGain );
    setBandWidth( m_eBandWidth );
    setSyncSource( m_eSyncSource );
}

/************************************************
 * 函数名   : initData
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 初始化数据成员
 ************************************************/
void UHFPRPSTestBGView::initData()
{
    m_pSampleBtn = NULL;
    m_pLoadingWidget = NULL;

    m_eSignalState = Module::SIGNAL_STATE_NONE;// 信号状态
    m_eSyncState = Module::Not_Sync;//同步状态
    m_eState = STATE_SAMPLING;

    m_usMaxSpectrumValue = 0;
    m_vMaxValueVector.clear();
    setConfigData();
}

/*************************************************
功能： 保存设置
*************************************************************/
bool UHFPRPSTestBGView::saveConfig(void)
{
    int iGroup = UHF::GROUP_UHF_PRPS;
    m_pConfig->beginGroup( Module::GROUP_UHF );
    m_pConfig->setValue( m_eForwardGain, UHF::KEY_FORWARDGAIN );
    m_pConfig->setValue( m_eBandWidth, UHF::KEY_BANDWIDTH );
    m_pConfig->setValue( m_iPhaseAlias, UHF::KEY_PHASEALIAS );
    m_pConfig->setValue( m_eSyncSource, UHF::KEY_SYNC_SOURCE );
    m_pConfig->setValue( m_iAccumulationTime, UHF::KEY_PRPS_ACCUMULATION_TIME, iGroup );
    m_pConfig->setValue( m_fThresholdPercentage, UHF::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup );
    m_pConfig->setValue( m_eThresholdMode, UHF::KEY_PRPS_THRESHOLD_MODE, iGroup );
    m_pConfig->endGroup();

    m_pConfig->beginGroup( Module::GROUP_APP );
    m_pConfig->setValue( m_ucSysFreq, APPConfig::KEY_SYS_FREQ );
    m_pConfig->endGroup();
    return true;
}

/************************************************
 * 函数名   : setSampleBtnText
 * 输入参数 : eState: 采样状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 更新采样状态设置采样按钮显示的文本
 ************************************************/
void UHFPRPSTestBGView::setSampleBtnText(State eState)
{
    if(eState == STATE_SAMPLING)
    {
        m_pSampleBtn->setTitle(UHF_VIEW_CONFIG_TRANSLATE(UHF::TEXT_STOP));
    }
    else
    {
        m_pSampleBtn->setTitle(UHF_VIEW_CONFIG_TRANSLATE(UHF::TEXT_RUN));
    }
}

/************************************************
 * 函数名   : onDataRead
 * 输入参数 : stData: 采样数据
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 槽函数，处理收到的采样数据
 ************************************************/
void UHFPRPSTestBGView::onDataRead(UHF::PRPSData stData, MultiServiceNS::USERID userId)
{
    if((getUserId() != INVALID_USER) && (getUserId() == userId) && isSampling())
    {
        QVector<double> vRawData;
        vRawData.clear();

        m_stPRPSFeateurInfo.dSpecMaxVal = m_pChart->rangeMax();
        m_stPRPSFeateurInfo.dSpecMinVal = m_pChart->rangeMin();
        m_stPRPSFeateurInfo.iPhaseNum = m_pChart->phaseCount();
        m_stPRPSFeateurInfo.iPeriodNum = m_pChart->periodCount();

        for(int i = 0; i < SPECTTRUMNUM; ++i)
        {
            if(stData.vSpectrum[i] < Module::ZERO)
            {
                stData.vSpectrum[i] = 0;
            }

            double dData = (double) stData.vSpectrum[i];
            vRawData << dData;
            m_stPRPSFeateurInfo.qvtDataIn.append(static_cast<double>(stData.vSpectrum[i]));
        }

        if(UHF::THRESHOLD_AUTO == m_eThresholdMode && DealData::denoisePRPSData(m_stPRPSFeateurInfo))
        {
            m_fThresholdPercentage = static_cast<float>(m_stPRPSFeateurInfo.dThresholdDbVal);
            m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
            //log_debug("--------------------threshold: %f.", m_fThresholdPercentage);
        }

        m_pChart->setData(vRawData);    // 往图谱添加数据

        if(m_vMaxValueVector.size() >= m_ucSysFreq)// 若容器中数据的size超出制定个数，将最先加入的数据删除
        {
            m_vMaxValueVector.pop_front();
        }

        m_vMaxValueVector.append((double)(stData.cMaxSpectrum));  // 将每组数据中的最大值加入容器
        double dMaxValue = m_vMaxValueVector.at(0);

        for(UINT16 i = 1, iSize = m_vMaxValueVector.size(); i < iSize; ++i)
        {
            if(m_vMaxValueVector.at(i) > dMaxValue)
            {
                dMaxValue = m_vMaxValueVector.at(i);
            }
        }

        //special deal data in lu ruan
        dMaxValue = dMaxValue < 0 ? 0 : dMaxValue;
        m_usMaxSpectrumValue = (UINT16) dMaxValue;
        //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
        //m_pChart->setMaxSpectrum(dMaxValue);    // 显示最大值
    }
    else
    {
        //illegal
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
    }

    return;
}

/*************************************************
功能： 槽，响应诊断结果
输入参数：
        qspDiagResultInfo -- 诊断结果
*************************************************************/
void UHFPRPSTestBGView::onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo)
{
    Q_UNUSED(qspDiagResultInfo);
    return;
}

void UHFPRPSTestBGView::onTitleBarClicked( void )
{
   close();
}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void UHFPRPSTestBGView::keyPressEvent( QKeyEvent* event )
{
    if(event->key() == Qt::Key_Escape)
    {
         close();
    }
    else
    {
        SampleChartView::keyPressEvent( event );
    }
    return;
}

void UHFPRPSTestBGView::onItemActivated(qint32 uiItem)
{
    if(m_eOperationType == OPERATION_DELETE)
    {
        deleteSelectedFile(uiItem);
    }
    else if(m_eOperationType == OPERATION_LOAD)
    {
        //停止采样
        bool bIsSampleStopped = false;
        if(m_eState == STATE_SAMPLING)
        {
            stopSample();
            m_eState = STATE_READY;
            bIsSampleStopped = true;
        }

        if( loadSelectedFile(uiItem) )
        {
            buttonBar()->buttons()[BUTTON_UHF_SAVE_DATA]->setEnabled( false );
            m_eState = STATE_READY;
        }
        else
        {
            if( bIsSampleStopped )
            {
                startSample();
                m_eState = STATE_SAMPLING;
            }
        }
        setSampleBtnText(m_eState);
    }
    else
    {
        qWarning("UHFPRPSTestBGView::onItemActivated: error!");
    }

}
void UHFPRPSTestBGView::deleteSelectedFile(qint32 uiItem)
{
    QList<QString> lStrBGFileNames;
    lStrBGFileNames = m_pSubTask->bgFileList( m_strGapId );

    if( uiItem >= lStrBGFileNames.size())
    {
        qWarning("UHFPRPSTestBGView::deleteSelectedFile: error!");
        return;
    }

    QString strTempFile  = lStrBGFileNames.at(uiItem);

    QFile file ( strTempFile );
    if( file.exists() )
    {
         file.remove();
         lStrBGFileNames.removeAt(uiItem);
         //m_pSubTask->deleteBGFile(strTempFile );

         m_testedCount = lStrBGFileNames.size();
         m_pTestedNumLabel->setText(QObject::trUtf8("Tested Count: ") + QString::number(m_testedCount) );

         m_pCommonItemListView->clear();
         m_pCommonItemListView->addItems( lStrBGFileNames );
    }
    else
    {
        qWarning ()<<" UHFPRPSTestBGView::deleteSelectedFile: no such file: "<< strTempFile;
    }
}
bool UHFPRPSTestBGView::loadSelectedFile(qint32 uiItem)
{
    QList<QString> lStrBGFileNames;
    lStrBGFileNames = m_pSubTask->bgFileList( m_strGapId );

    bool bSuccess = false;

    if( uiItem >= lStrBGFileNames.size())
    {
        qWarning("UHFPRPSTestBGView::loadSelectedFile: error!");
        return false;
    }
    QString strTempFile  = m_pSubTask->dataSavePath() + "/" + lStrBGFileNames.at(uiItem);

    m_pCommonItemListView->hide();

    if( loadTestDataFile(strTempFile))
    {
        m_bPlayBacked = true;
        bSuccess = true;
        QString strFileName = lStrBGFileNames.at(uiItem);
        m_pLoadFileName->setText( CustomAccessTaskNS::testFileNameFromFile( strFileName ) );
    }
    else
    {
        MsgBox::warning( "", QObject::trUtf8("No file!") );
    }
    return bSuccess;
}

/*************************************************
功能： 保存图谱信息部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void UHFPRPSTestBGView::fillPRPSDataInfo(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum)
{
    pPRPSSpectrum->setStorageDataType(DataSpecificationNS::DATA_TYPE_DOUBLE);

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_UHF );
    //m_eBandWidth = (UHF::BandWidth)pConfig->value( UHF::KEY_BANDWIDTH ).toUInt();
    pConfig->endGroup();

    DataSpecificationNS::PRPSExtInformation stPRPSExtInformation;
    stPRPSExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_DB;
    stPRPSExtInformation.fAmpLowerLimit = UHF::CHART_MIN_VALUE;
    stPRPSExtInformation.fAmpUpperLimit = UHF::CHART_MAX_VALUE;

    stPRPSExtInformation.eFrequencyBand = static_cast<DataSpecificationNS::FrequencyBand>(m_eBandWidth + 1);

    UHF::bandwidth2FreqRange(stPRPSExtInformation.fFrequencyLowerLimit, stPRPSExtInformation.fFequencyUpperLimit, m_eBandWidth);

    stPRPSExtInformation.iPhaseWindowCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stPRPSExtInformation.iQuantizedAmplitude = 0;
    stPRPSExtInformation.iPowerFreqCycleCount = m_pChart->prpsData().size() / stPRPSExtInformation.iPhaseWindowCount;//总是认为数据是50*60(50Hz下)
    if(stPRPSExtInformation.iPowerFreqCycleCount < 50)
    {
        stPRPSExtInformation.iPowerFreqCycleCount = 50;
    }
    memset(stPRPSExtInformation.aucPDTypeProbability, 0, sizeof(stPRPSExtInformation.aucPDTypeProbability));

    stPRPSExtInformation.eDataJudgmentFlag = DataSpecificationNS::DATA_NORMAL;

    stPRPSExtInformation.sGain = m_eForwardGain;
    log_debug("sync source: %d.", m_eSyncSource);
    stPRPSExtInformation.eSyncSource = static_cast<DataSpecificationNS::SyncSource>(m_eSyncSource + 1);
    stPRPSExtInformation.ucSyncState = m_eSyncState;
    stPRPSExtInformation.fSyncFrequency = -1;

    pPRPSSpectrum->setPRPSExtInformation(stPRPSExtInformation);
}


/*************************************************
功能： 保存图谱信息部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void UHFPRPSTestBGView::fillPRPDDataInfo(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum)
{
    pPRPDSpectrum->setStorageDataType(DataSpecificationNS::DATA_TYPE_DOUBLE);

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_UHF );
    //m_eBandWidth = (UHF::BandWidth)pConfig->value( UHF::KEY_BANDWIDTH ).toUInt();
    pConfig->endGroup();

    DataSpecificationNS::PRPDExtInformation stPRPDExtInformation;
    stPRPDExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_DB;
    stPRPDExtInformation.fAmpLowerLimit = UHF::CHART_MIN_VALUE;
    stPRPDExtInformation.fAmpUpperLimit = UHF::CHART_MAX_VALUE;

    stPRPDExtInformation.eFrequencyBand = static_cast<DataSpecificationNS::FrequencyBand>(m_eBandWidth + 1);

    UHF::bandwidth2FreqRange(stPRPDExtInformation.fFrequencyLowerLimit, stPRPDExtInformation.fFequencyUpperLimit, m_eBandWidth);

    stPRPDExtInformation.iPhaseWindowCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stPRPDExtInformation.iQuantizedAmplitude = PRPSMapNS::QUANTIFICATION_AMP;
    stPRPDExtInformation.iPowerFreqCycleCount = m_pChart->prpdPeriodCount();

    memset(stPRPDExtInformation.aucPDTypeProbability, 0, sizeof(stPRPDExtInformation.aucPDTypeProbability));

    stPRPDExtInformation.eDataJudgmentFlag = DataSpecificationNS::DATA_NORMAL;
    stPRPDExtInformation.sGain = m_eForwardGain;

    stPRPDExtInformation.eSyncSource = static_cast<DataSpecificationNS::SyncSource>(m_eSyncSource + 1);
    stPRPDExtInformation.ucSyncState = 1;// todo
    stPRPDExtInformation.fSyncFrequency = -1;
    pPRPDSpectrum->setPRPDExtInformation(stPRPDExtInformation);
}

void UHFPRPSTestBGView::fillPRPSData(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum)
{
    QVector< double > vecPRPSData = m_pChart->prpsData();
    int iDataPointNum = 0;
    //step5 set map data
    iDataPointNum = vecPRPSData.size();//ex 50*60
    dbg_info("iDataPointNum is %d\n", iDataPointNum);

    int iPhaseShitStep = m_iPhaseAlias / (360 / PRPSMapNS::PHASE_INTERVAL_CNT);
    dbg_info("iPhaseShitStep is %d\n", iPhaseShitStep);


    int iRealPeriod = iDataPointNum / PRPSMapNS::PHASE_INTERVAL_CNT;
    dbg_info("iRealPeriod is %d\n", iRealPeriod);
    if(iRealPeriod > 50)//todo hard code
    {
        iRealPeriod = 50;
    }

    DataSpecificationNS::PRPSData stPRPSData;
    QVector<double> qvtPRPSData;
    qvtPRPSData.resize(PRPSMapNS::PHASE_INTERVAL_CNT * 50);

    if (vecPRPSData.size() > 0)
    {
        m_dMaxData = vecPRPSData[0];
    }
    else
    {
        m_dMaxData = 0;
    }

    for(int i = 0; i < iRealPeriod; ++i)
    {
        for(int iPeriodIndex = 0, j = 0; j < PRPSMapNS::PHASE_INTERVAL_CNT; ++j)
        {
            if(iRealPeriod < 50)
            {
                iPeriodIndex =  50 + i - iRealPeriod;
            }
            else
            {
                iPeriodIndex = i;
            }
            int iNewPhaseIndex = (j + iPhaseShitStep) % PRPSMapNS::PHASE_INTERVAL_CNT;

            const double& dPRPSData = vecPRPSData.at(i * PRPSMapNS::PHASE_INTERVAL_CNT + j);
            qvtPRPSData[iPeriodIndex * PRPSMapNS::PHASE_INTERVAL_CNT + iNewPhaseIndex] = dPRPSData;

            if(dPRPSData > m_dMaxData)
            {
                m_dMaxData = dPRPSData;
            }
        }
    }
    stPRPSData.qbaPDSpectrumData.resize(qvtPRPSData.size() * sizeof(double));
    memcpy(stPRPSData.qbaPDSpectrumData.data(), &qvtPRPSData[0], stPRPSData.qbaPDSpectrumData.size());
    pPRPSSpectrum->setPRPSData(stPRPSData);
}


/*************************************************
功能： 保存图谱数据部分内容到保存结构体中
输入参数: pMap -- 保存图谱数据的对象指针
输出参数：NULL
返回值： NULL
*************************************************/
void UHFPRPSTestBGView::fillPRPDData(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum)
{
    int iDataCnt =  PRPSMapNS::PHASE_INTERVAL_CNT * PRPSMapNS::QUANTIFICATION_AMP;
    QVector< qint16 > vecPRPDData = m_pChart->prpdData();

    int iAmpAreaCnt = vecPRPDData.size() / PRPSMapNS::PHASE_INTERVAL_CNT;
    dbg_info("iAmpAreaCnt is %d\n", iAmpAreaCnt);
    int iPhaseShitStep = m_iPhaseAlias / (360 / PRPSMapNS::PHASE_INTERVAL_CNT);
    dbg_info(" iPhaseShitStep is %d\n", iPhaseShitStep);

    DataSpecificationNS::PRPDData stPRPDData;
    QVector<double> qvtPRPDData;
    qvtPRPDData.resize(iDataCnt);
    for(int j = 0; j < PRPSMapNS::PHASE_INTERVAL_CNT; ++j)
    {
        for(int  i = 0; i< iAmpAreaCnt; i ++)
        {
            int iNewPhaseIndex = (j + iPhaseShitStep) % PRPSMapNS::PHASE_INTERVAL_CNT;

            qint16 sPRPD = vecPRPDData.at(i * PRPSMapNS::PHASE_INTERVAL_CNT + j);
            if(sPRPD <= 0)
            {
                sPRPD = 0;
            }
            qvtPRPDData[iNewPhaseIndex*PRPSMapNS::QUANTIFICATION_AMP + i] = sPRPD;
        }
    }

    stPRPDData.qbaPDSpectrumData.resize(qvtPRPDData.size() * sizeof(double));
    memcpy(stPRPDData.qbaPDSpectrumData.data(), &qvtPRPDData[0], stPRPDData.qbaPDSpectrumData.size());
    pPRPDSpectrum->setPRPDData(stPRPDData);
}

INT32 UHFPRPSTestBGView::quantizationAmpSection(INT32 iAmp, float fAmpLower, float fAmpUpper, INT32 iQuantizationAmpCnt)
{
    //dbg_info("iAmp is %d, fAmpLower is %f, fAmpUpper is %f, iQuantizationAmp is %d\n", iAmp, fAmpLower, fAmpUpper, iQuantizationAmp);
    float fRangePerSection = (fAmpUpper - fAmpLower) / (float)iQuantizationAmpCnt;
    int section = 0;
    float fAmp = 0;

    if(iAmp < Module::ZERO)
    {
        fAmp = 0;
    }
    else
    {
        fAmp = (float)iAmp;
    }
    section = fAmp / fRangePerSection;
    //dbg_info("section is %d\n", section);
    return section;
}


/*************************************************
功能： 保存数据
返回：
    保存结果
*************************************************************/
QString UHFPRPSTestBGView::saveTestData()
{
    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return "";
    }

    DataSpecificationNS::DataSpecification* pDataSpecification = new DataSpecificationNS::DataSpecification;//当前数据文件
    pDataSpecification->setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());
    pDataSpecification->setCompanyCode("1.1");
    pDataSpecification->setInternalVersion("1.1.0.0");

    CustomAccessTaskNS::GapInfo currentGap = m_pSubTask->gapInfo( m_strGapId );
    CustomAccessTaskNS::TestPointInfo nextTestPoint;
    for(int i = 0, iSize = currentGap.s_vTestPoints.size(); i < iSize; ++i)
    {
        if(currentGap.s_vTestPoints.at(i).s_vData.isEmpty())
        {
            nextTestPoint = currentGap.s_vTestPoints.at(i);
            break;
        }
    }

    //创建图谱保存对象
    DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum = new DataSpecificationNS::PRPSSpectrum;

    //设置头部信息
    pPRPSSpectrum->setSpectrumCharacter(DataSpecificationNS::PROPERTY_BG_NOISE);
    pPRPSSpectrum->setEquipmentName(currentGap.s_strName);
    pPRPSSpectrum->setEquipmentCode(currentGap.s_strId);
    pPRPSSpectrum->setTestPointName(nextTestPoint.s_strName);
    pPRPSSpectrum->setTestPointCode(nextTestPoint.s_strId);
    pPRPSSpectrum->setSpectrumTypeCode(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPS);

    //设置PRPS ext信息
    fillPRPSDataInfo(pPRPSSpectrum);

    //设置PRPS数据内容
    fillPRPSData(pPRPSSpectrum);

    //设置PRPD ext信息
    DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum = new DataSpecificationNS::PRPDSpectrum;

    //设置头部信息
    pPRPDSpectrum->setSpectrumCharacter(DataSpecificationNS::PROPERTY_BG_NOISE);
    pPRPDSpectrum->setEquipmentName(currentGap.s_strName);
    pPRPDSpectrum->setEquipmentCode(currentGap.s_strId);
    pPRPDSpectrum->setTestPointName(nextTestPoint.s_strName);
    pPRPDSpectrum->setTestPointCode(nextTestPoint.s_strId);
    pPRPDSpectrum->setSpectrumTypeCode(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPD);

    //设置PRPD ext信息
    fillPRPDDataInfo(pPRPDSpectrum);

    //设置PRPD数据内容
    fillPRPDData(pPRPDSpectrum);

    pDataSpecification->addSpectrum(pPRPSSpectrum);
    pDataSpecification->addSpectrum(pPRPDSpectrum);

    CustomAccessView::CustomAccessUIFunc::setDataFileHead(pDataSpecification);

    SystemSet::AccessProtocol eAccessProtocol = SystemSetService::instance()->getAccessProtocol();
    if (SystemSet::ACCESS_PROTO_JSDKY == eAccessProtocol)
    {
        m_strSavedPath = CustomAccessView::CustomAccessUIFunc::getJSBinaryDataFileSavePath(m_pSubTask->dataSavePath(), true);
    }
    else
    {
        m_strSavedPath = CustomAccessView::CustomAccessUIFunc::getBinaryDataFileSavePath(m_pSubTask->dataSavePath());
    }

    if(pDataSpecification->saveAsBinary(m_strSavedPath))
    {
        m_pSubTask->addBGFile(m_strGapId,m_strSavedPath, m_dMaxData);
        ++m_testedCount;
        m_pTestedNumLabel->setText(QObject::trUtf8("Tested Count: ") + QString::number(m_testedCount) );

    }
    else
    {
        m_strSavedPath = "";
    }

    delete pDataSpecification;
    pDataSpecification = NULL;
    return m_strSavedPath;
}

/************************************************
 * 函数名   : createChart
 * 输入参数 : parent---父控件
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 创建图谱
 ************************************************/
ChartWidget* UHFPRPSTestBGView::createChart(QWidget *parent)
{
    Q_UNUSED(parent)
    ChartWidget *pWidget = new ChartWidget;
    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->setContentsMargins(0, 0, 0, 0);
    QVBoxLayout *labelLayout = new QVBoxLayout;
    //已测次数
    m_pTestedNumLabel = new QLabel;
    m_pBayNameLabel = new QLabel;

    QPalette labelPalette;
    QPalette bgPalette;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    SystemSet::PRPSBGColor eColor = (SystemSet::PRPSBGColor) pConfig->value( APPConfig::KEY_PRPS_BG_COLOR ).toInt();
    pConfig->endGroup();
    if(eColor == SystemSet::PRPS_BG_COLOR_GRAY)
    {
        labelPalette.setColor(QPalette::WindowText,Qt::white);
        bgPalette.setColor( QPalette::Background,QColor(128, 128, 128) );
    }
    else
    {
        labelPalette.setColor(QPalette::WindowText,Qt::black);
        bgPalette.setColor( QPalette::Background,Qt::white );
    }

    //设置style
    QFont font = m_pTestedNumLabel->font();
    font.setPointSize(20);
    m_pTestedNumLabel->setFont(font);
    m_pTestedNumLabel->setText(QObject::trUtf8("Tested Count: ") + QString::number(m_testedCount) );
    m_pTestedNumLabel->setPalette(labelPalette);

    m_pBayNameLabel->setFont(font);
    m_pBayNameLabel->setText(QObject::trUtf8("Bay Name: ") + m_strBayName );
    m_pBayNameLabel->setPalette(labelPalette);

    m_pLoadFileName = new QLabel;
    m_pLoadFileName->setPalette(labelPalette);
    m_pLoadFileName->setFont(font);

    labelLayout->addWidget( m_pTestedNumLabel,Qt::AlignLeft );
    labelLayout->addWidget( m_pBayNameLabel,Qt::AlignLeft );
    labelLayout->addWidget( m_pLoadFileName,Qt::AlignLeft );

    //图谱
    m_pChart = new UhfPrpsUnionView(UHF::PRPS_PERIOD_CNT,UHF::PRPS_PERIOD_CNT, UHF::PRPS_PHASE_CNT, UHF::CHART_MAX_VALUE,UHF::CHART_MIN_VALUE);
    m_pChart->setFixedHeight( CHART_HEIGHT );
    m_pChart->setPrpdContentsMargins(0, PRPD_MARGIN, 0, PRPD_MARGIN);


    vLayout->addLayout(labelLayout);
    vLayout->addWidget(m_pChart);

    pWidget->setLayout(vLayout);
    pWidget->setFixedWidth(Window::WIDTH);

    setPalette(bgPalette);
    setAutoFillBackground(true);

    return pWidget;
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void UHFPRPSTestBGView::onButtonValueChanged( int id, int iValue )
{
    switch( id )
    {
        case BUTTON_UHF_PHASE_SHIFT://相位偏移
        {
            m_iPhaseAlias = iValue;
            m_pChart->setPhaseOffset(m_iPhaseAlias);
        }
            break;
        case BUTTON_UHF_FORWARD_GAIN://前置增益
        {
            m_eForwardGain = (UHF::ForwardGain) iValue;
            setFordwardGain(m_eForwardGain);
        }
            break;
        case BUTTON_ALTAS_TYPE://图谱类型
        {
            m_eAltasType = static_cast<PhaseAbstractView::AltasType>(iValue);
            m_pChart->setAltasType(m_eAltasType);
        }
            break;
        case BUTTON_UHF_SYNC_SOURCE://同步方式
        {
            m_eSyncSource = (Module::SyncSource) (iValue + WIRELESS_SYNC);
            setSyncSource(m_eSyncSource);
            m_pChart->setSync((PrpsGlobal::SyncSource) m_eSyncSource, (PrpsGlobal::SyncState)m_eSyncState);
        }
            break;
        case BUTTON_IS_ACCUMULATION://是否累积
        {
            m_iAccumulationTime = iValue;

            updateAccumulativeTime();
        }
        break;
        case BUTTON_UHF_BAND_WIDTH://带宽
        {
            m_eBandWidth = (UHF::BandWidth) iValue;
            setBandWidth(m_eBandWidth);
        }
            break;
        case BUTTON_THRESHOLD://阈值
        {
            m_eThresholdMode = static_cast<UHF::ThresholdMode>(iValue);
            updateThresholdPercentage();
            m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
        }
            break;
        default:
        {
            dbg_warning("wrong case: %d\n", id);
        }
            break;
    }
}

void UHFPRPSTestBGView::getFileHead(DataSpecificationNS::DataSpecification* pDataSpecification)
{
    DataSpecificationNS::SpectrumDataFileHead stSpectrumDataFileHead;
    pDataSpecification->getSpectrumDataFileHead(stSpectrumDataFileHead);
    m_pUHFPRPSPRPDDataInfo->ucFreq = stSpectrumDataFileHead.fSystemFrequency;
    m_pUHFPRPSPRPDDataInfo->stPRPSHeadInfo.strSubstationName = stSpectrumDataFileHead.qstrStationName;
    m_pUHFPRPSPRPDDataInfo->stPRPDHeadInfo.strSubstationName = stSpectrumDataFileHead.qstrStationName;
}

void UHFPRPSTestBGView::getPRPSMapHead(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum)
{
    DataSpecificationNS::SpectrumHead stSpectrumHead;
    pPRPSSpectrum->getSpectrumHead(stSpectrumHead);
    m_pUHFPRPSPRPDDataInfo->stPRPSHeadInfo.strDeviceName = stSpectrumHead.qstrEquipmentName;
    m_pUHFPRPSPRPDDataInfo->stPRPSHeadInfo.eDataPrimitiveType = static_cast<DataFileNS::DataPrimitiveType>(stSpectrumHead.eStorageDataType);
}

void UHFPRPSTestBGView::getPRPDMapHead(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum)
{
    DataSpecificationNS::SpectrumHead stSpectrumHead;
    pPRPDSpectrum->getSpectrumHead(stSpectrumHead);
    m_pUHFPRPSPRPDDataInfo->stPRPDHeadInfo.strDeviceName = stSpectrumHead.qstrEquipmentName;
    m_pUHFPRPSPRPDDataInfo->stPRPDHeadInfo.eDataPrimitiveType = static_cast<DataFileNS::DataPrimitiveType>(stSpectrumHead.eStorageDataType);
}

/****************************
功能： 获取图谱ext数据
输入参数:
    pMap -- 文件解析得到的图谱
    iDataPointCount -- 图谱数据数量
*****************************/
void UHFPRPSTestBGView::getPRPSMapInfo(const DataSpecificationNS::PRPSExtInformation* pPRPSExtInformation)
{
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.eSyncSource = static_cast<DataFileNS::SyncSource>(pPRPSExtInformation->eSyncSource);
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.ucSyncState = pPRPSExtInformation->ucSyncState;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.iPhaseIntervalCount = pPRPSExtInformation->iPhaseWindowCount;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.iQuantificationAmp = pPRPSExtInformation->iQuantizedAmplitude;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.iPowerFreCycleCount = pPRPSExtInformation->iPowerFreqCycleCount;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.eAmpUnit = static_cast<DataFileNS::AmpUnit>(pPRPSExtInformation->eAmpUnit);
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.eBandWidth = static_cast<DataFileNS::MapBandWidth>(pPRPSExtInformation->eFrequencyBand);
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.sGain = pPRPSExtInformation->sGain;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.eDataSign = static_cast<PRPSMapNS::MapDataSign>(pPRPSExtInformation->eDataJudgmentFlag);
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.fAmpLowerLimit = pPRPSExtInformation->fAmpLowerLimit;
    m_pUHFPRPSPRPDDataInfo->stPRPSInfo.fAmpUpperLimit = pPRPSExtInformation->fAmpUpperLimit;
}


/****************************
功能： 获取图谱ext数据
输入参数:
    pMap -- 文件解析得到的图谱
    iDataPointCount -- 图谱数据数量
*****************************/
void UHFPRPSTestBGView::getPRPDMapInfo(const DataSpecificationNS::PRPDExtInformation* pPRPDExtInformation)
{
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.eSyncSource = static_cast<DataFileNS::SyncSource>(pPRPDExtInformation->eSyncSource);
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.ucSyncState = pPRPDExtInformation->ucSyncState;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.iPhaseIntervalCount = pPRPDExtInformation->iPhaseWindowCount;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.iQuantificationAmp = pPRPDExtInformation->iQuantizedAmplitude;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.iPowerFreCycleCount = pPRPDExtInformation->iPowerFreqCycleCount;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.eAmpUnit = static_cast<DataFileNS::AmpUnit>(pPRPDExtInformation->eAmpUnit);
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.eBandWidth = static_cast<DataFileNS::MapBandWidth>(pPRPDExtInformation->eFrequencyBand);
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.sGain = pPRPDExtInformation->sGain;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.eDataSign = static_cast<PRPSMapNS::MapDataSign>(pPRPDExtInformation->eDataJudgmentFlag);
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.fAmpLowerLimit = pPRPDExtInformation->fAmpLowerLimit;
    m_pUHFPRPSPRPDDataInfo->stPRPDInfo.fAmpUpperLimit = pPRPDExtInformation->fAmpUpperLimit;
}


/****************************
功能： 回放文件
输入参数:
    strFileName -- 文件名
*****************************/
bool UHFPRPSTestBGView::loadTestDataFile( const QString& strFileName )
{
    qDebug()<<" UHFPRPSTestBGView::loadTeatDataFile, strFileName"<<strFileName;

    UHFPRPSPRPDDataInfo sPlayBackDataInfo;
    if(!getData(strFileName, &sPlayBackDataInfo))
    {
        qWarning() << "UHFPRPSTestBGView::loadTeatDataFile: " << strFileName << " get data  failed!";
        return false;
    }
    displayMap(sPlayBackDataInfo);

    return true;
}

bool UHFPRPSTestBGView::getData(const QString& strFileName, void *pData)
{
    qDebug()<<"UHFPRPSTestBGView::getData, strFileName: "<<strFileName;

    m_pUHFPRPSPRPDDataInfo = (UHFPRPSPRPDDataInfo*)pData;

    DataSpecificationNS::DataSpecification dataSpecification;
    dataSpecification.setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());

    //step1 open data file
    if(!dataSpecification.parseBinaryFromFile(strFileName))
    {
        return false;
    }

    //step2 get displayed file head
    getFileHead(&dataSpecification);

    DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum = dynamic_cast<DataSpecificationNS::PRPSSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPS));
    if(NULL == pPRPSSpectrum)
    {
        return false;
    }

    DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum = dynamic_cast<DataSpecificationNS::PRPDSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_UHF_PRPD));
    if(NULL == pPRPDSpectrum)
    {
        return false;
    }

    getPRPSMapHead(pPRPSSpectrum);
    getPRPDMapHead(pPRPDSpectrum);

    DataSpecificationNS::PRPSExtInformation stPRPSExtInformation;
    pPRPSSpectrum->getPRPSExtInformation(stPRPSExtInformation);
    getPRPSMapInfo(&stPRPSExtInformation);

    DataSpecificationNS::PRPDExtInformation stPRPDExtInformation;
    pPRPDSpectrum->getPRPDExtInformation(stPRPDExtInformation);
    getPRPDMapInfo(&stPRPDExtInformation);

    DataSpecificationNS::PRPSData stPRPSData;
    pPRPSSpectrum->getPRPSData(stPRPSData);
    int iDataPointNum = stPRPSExtInformation.iPhaseWindowCount * stPRPSExtInformation.iPowerFreqCycleCount;
    m_pUHFPRPSPRPDDataInfo->vecPRPSData.resize(iDataPointNum);
    memcpy(&m_pUHFPRPSPRPDDataInfo->vecPRPSData[0], stPRPSData.qbaPDSpectrumData.data(), iDataPointNum * sizeof(double));

    DataSpecificationNS::PRPDData stPRPDData;
    pPRPDSpectrum->getPRPDData(stPRPDData);
    iDataPointNum = stPRPDExtInformation.iPhaseWindowCount * stPRPDExtInformation.iQuantizedAmplitude;
    m_pUHFPRPSPRPDDataInfo->vecPRRepeatyData.resize(iDataPointNum);
    memcpy(&m_pUHFPRPSPRPDDataInfo->vecPRRepeatyData[0], stPRPDData.qbaPDSpectrumData.data(), iDataPointNum * sizeof(double));

    return true;
}

/****************************
功能： 在回放中显示
输入参数: void
*****************************/
void UHFPRPSTestBGView::displayMap(UHFPRPSPRPDDataInfo &PlayBackDataInfo)
{
    m_pChart->clearData();

    //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
    //m_pChart->setPowerFreq( (PrpsGlobal::Frequency)( PlayBackDataInfo.ucFreq ));

    //log_debug("sync source: %d, state: %d.", PlayBackDataInfo.stPRPSInfo.eSyncSource, PlayBackDataInfo.stPRPSInfo.ucSyncState);
    //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
    //m_pChart->setSync((PrpsGlobal::SyncSource)(PlayBackDataInfo.stPRPSInfo.eSyncSource - 1), (PrpsGlobal::SyncState)PlayBackDataInfo.stPRPSInfo.ucSyncState);
    m_pChart->clearSyncText();

    //是否无信号状态没有保存到数据文件，回放时，先简单默认不显示无信号
    m_pChart->setConnected(true);

    QVector<qint16> prpdData;
    prpdData.clear();
    double dPRPulseCnt;

    //NOTE 如果是累计模式，iPeriodCnt的计算不正确
    int iPeriodCnt = PlayBackDataInfo.vecPRRepeatyData.size() / PlayBackDataInfo.stPRPDInfo.iPhaseIntervalCount;

    dbg_info("iPeriodCnt is %d\n", iPeriodCnt);

    for(int i = 0; i < PlayBackDataInfo.stPRPDInfo.iQuantificationAmp; ++i)
    {
        for(int j = 0; j < PlayBackDataInfo.stPRPDInfo.iPhaseIntervalCount; ++j)
        {
            dPRPulseCnt = PlayBackDataInfo.vecPRRepeatyData.at(j * PlayBackDataInfo.stPRPDInfo.iQuantificationAmp + i);
            prpdData.append((qint16)(dPRPulseCnt));
        }
    }

    log_debug("max value: %f.", PlayBackDataInfo.stPRPSInfo.fMax);
    PlayBackDataInfo.stPRPSInfo.fMax = PlayBackDataInfo.stPRPSInfo.fMax < 0 ? 0 : PlayBackDataInfo.stPRPSInfo.fMax;
    log_debug("max value 2: %f.", PlayBackDataInfo.stPRPSInfo.fMax);
    //定制接入终端的uhf数据格式和常规uhf数据格式不一致，这里不显示
    //m_pChart->setMaxSpectrum(PlayBackDataInfo.stPRPSInfo.fMax);
    m_pChart->addPlayBackData(PlayBackDataInfo.vecPRPSData, prpdData, PlayBackDataInfo.stPRPDInfo.iPowerFreCycleCount);
    m_pChart->setPhaseOffset(0);
    return;
}
/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void UHFPRPSTestBGView::onCommandButtonPressed( int id )
{
    switch( id )
    {
        case BUTTON_UHF_SAMPLE://采样
        {
            switchSample(m_eState);
            m_pLoadFileName->clear();
        }
            break;

        case BUTTON_UHF_SAVE_DATA://保存数据
        {
			saveData();
        }
            break;
        case BUTTON_UHF_LOAD_DATA://载入数据
        {
            loadData();
        }
            break;
//        case BUTTON_UHF_DELETE_DATA://删除数据
//        {
//            deleteData();
//        }
//            break;
        case BUTTON_MENU://配置
        {
            showMoreConfigButtonBar();
        }
            break;
        default:
        {
            dbg_warning("wrong case: %d\n", id);
        }
            break;
    }
}

/************************************************
 * 函数名   : switchSample
 * 输入参数 : eState: 采样状态
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 起动、暂停采样切换
 ************************************************/
void UHFPRPSTestBGView::switchSample(State eState)
{
    dbg_info("eState is %d\n", eState);

    if( m_bPlayBacked )
    {
        setChartParameters();
        m_bPlayBacked = false;
    }
    else
    {
        //do nothing
    }

    if(eState == STATE_READY)
    {
        startSample();
        m_eState = STATE_SAMPLING;
        setSampleBtnText(m_eState);
        m_pChart->setRunningMode(true);
        buttonBar()->buttons()[BUTTON_UHF_SAVE_DATA]->setEnabled( true );
    }
    else if(eState == STATE_SAMPLING)
    {
        stopSample();
        m_eState = STATE_READY;
        setSampleBtnText(m_eState);
        m_pChart->setRunningMode(false);
    }
    else
    {
        dbg_warning("wrong state: %d\n", eState);
    }
}

/************************************************
 * 函数名   : loadData
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 载入数据
 ************************************************/
void UHFPRPSTestBGView::loadData()
{
    QList<QString> lStrBGFileNames;
    lStrBGFileNames = m_pSubTask->bgFileList(m_strGapId);
    m_pCommonItemListView->clear();

    if(lStrBGFileNames.size() <= 0)
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
        return;
    }

    QList<QString> lStrFileNames;
    QString strFileName = "";
    for(quint16 i = 0, iSize = lStrBGFileNames.size(); i < iSize; ++i)
    {
        strFileName = lStrBGFileNames[i];
        lStrFileNames.append(CustomAccessTaskNS::testFileNameFromFile(strFileName));
    }

    m_eOperationType = OPERATION_LOAD;
    m_pCommonItemListView->addItems(lStrFileNames);
    m_pCommonItemListView->show();

    return;
}

/*************************************************
功能： 删除数据
*************************************************************/
void UHFPRPSTestBGView::deleteData()
{
    m_pCommonItemListView->show();
    m_eOperationType = OPERATION_DELETE;
}

/************************************************
 * 函数名   : setConfigData
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :
 ************************************************/
void UHFPRPSTestBGView::setConfigData()
{
    m_eAltasType = PhaseAbstractView::PRPS_PRPD;

    //从配置文件读取参数
    int iGroup = UHF::GROUP_UHF_PRPS;
    m_pConfig = ConfigManager::instance()->config();
    m_pConfig->beginGroup( Module::GROUP_UHF );
    m_eForwardGain = (UHF::ForwardGain)m_pConfig->value( UHF::KEY_FORWARDGAIN ).toUInt();
    m_eBandWidth = (UHF::BandWidth)m_pConfig->value( UHF::KEY_BANDWIDTH ).toUInt();

    dbg_info("m_eBandWidth is %d\n", m_eBandWidth);

    m_iPhaseAlias = m_pConfig->value( UHF::KEY_PHASEALIAS ).toUInt();
    m_eSyncSource = (Module::SyncSource)m_pConfig->value( UHF::KEY_SYNC_SOURCE ).toUInt();
    m_iAccumulationTime = m_pConfig->value( UHF::KEY_PRPS_ACCUMULATION_TIME, iGroup).toUInt();
    m_fThresholdPercentage = m_pConfig->value( UHF::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup).toFloat();
    m_eThresholdMode = static_cast<UHF::ThresholdMode>(m_pConfig->value( UHF::KEY_PRPS_THRESHOLD_MODE, iGroup).toUInt());
    m_pConfig->endGroup();

    m_pConfig->beginGroup( Module::GROUP_APP );
    m_ucSysFreq = m_pConfig->value( APPConfig::KEY_SYS_FREQ ).toUInt();
    m_pConfig->endGroup();
}

/************************************************
 * 函数名   : setButtonBarDatas
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 设置buttonbar显示的参数
 ************************************************/
void UHFPRPSTestBGView::setButtonBarDatas()
{
    ((PopupButton*)(buttonBar()->button(BUTTON_UHF_PHASE_SHIFT)))->setValue( m_iPhaseAlias );
    ((PopupButton*)(buttonBar()->button(BUTTON_UHF_FORWARD_GAIN)))->setValue( m_eForwardGain );
    //((PopupButton*)(buttonBar()->button(BUTTON_UHF_SYNC_SOURCE)))->setValue( m_eSyncSource - (int)Module::WIRELESS_SYNC );
    ((PopupButton*)(buttonBar()->button(BUTTON_UHF_BAND_WIDTH)))->setValue( m_eBandWidth );
    ((PopupButton*)(buttonBar()->button(BUTTON_IS_ACCUMULATION)))->setValue( m_iAccumulationTime );
    ((PopupButton*)(buttonBar()->button(BUTTON_ALTAS_TYPE)))->setValue(m_eAltasType);
    ((PopupButton*)(buttonBar()->button(BUTTON_THRESHOLD)))->setValue(m_eThresholdMode);
}

/************************************************
 * 函数名   : createButtonbar
 * 输入参数 : parent---父控件
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 创建buttonbar
 ************************************************/
PushButtonBar* UHFPRPSTestBGView::createButtonbar(QWidget *parent)
{
    Q_UNUSED(parent)
    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar( UHF::CONTEXT, s_UHFButtonInfo, sizeof(s_UHFButtonInfo)/sizeof(ButtonInfo::Info) );
    return pButtonBar;
}

/*************************************************
 * 功能：保存数据
 * ***********************************************/
void UHFPRPSTestBGView::saveData()
{
    if(m_eState == STATE_SAMPLING)
    {
        //停止采集
        stopSample();
        m_eState = STATE_READY;
        setSampleBtnText(m_eState);
    }
    saveConfig();
    QString strFile = saveTestData();
    qDebug() << "UHFPRPSTestBGView::saveData";
    QFileInfo fileInfo(strFile);
    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
    centerPoint.setY(pBtnBar->pos().y()/*+pBtnBar->height()/2*/);
    if(strFile.isEmpty())
    {
        MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("Save failure!"), centerPoint);
    }
    else
    {
        if( m_pSubTask->isAutoSwitch() )
        {
            if( MsgBox::OK == MsgBox::delayQuestion("", QObject::trUtf8("Save success, auto switching."), centerPoint) )
            {
                m_pSubTask->ensureAutoSwitch( true );
                close();
            }
            else
            {
                m_pSubTask->ensureAutoSwitch( false );
                //cancel do nothing
            }
        }
        else
        {
            QString strText = fileInfo.fileName();
            processTooLongMsgText(strText);
            MsgBox::information("", strText, centerPoint);
        }
    }

    /*
    if(bNeedRestoreSample)
    {
        //开始采集
        startSample();
        m_eState = STATE_SAMPLING;
        setSampleBtnText(m_eState);
    }
    */

    return;
}

/*************************************************
功能： 响应S键事件
*************************************************************/
void UHFPRPSTestBGView::onSKeyPressed()
{
	saveData();
    return;
}

