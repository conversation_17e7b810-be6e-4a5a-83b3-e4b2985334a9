/*
 * Copyright (c) 2019.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：aeampspectrum.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/11/04
 * 摘要：AE幅值图谱
 * 当前版本：1.0
*/

#pragma once

#include "spectrum.h"
#include "aespectrumdefine.h"

namespace DataSpecificationNS
{
    class AEAmpSpectrumPrivate;
    class DATASPECIFICATIONSHARED_EXPORT AEAmpSpectrum : public Spectrum
    {
    public:
        AEAmpSpectrum();
        ~AEAmpSpectrum();

        /************************************************
         * 函数名   : setDataSpecificationVersion
         * 输入参数 :
           const DataSpecificationVersion eDataSpecificationVersion: 数据规范版本号
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置数据规范版本号
         ************************************************/
        virtual void setDataSpecificationVersion(const DataSpecificationVersion eDataSpecificationVersion);

        /************************************************
         * 函数名   : spectrumName
         * 输入参数 : NULL
         * 输出参数 : NULL
         * 返回值   : QString
         * 功能     : 图谱名称
         ************************************************/
        virtual QString spectrumName() const;

        /************************************************
         * 函数名   : setAEAmpExtInformation
         * 输入参数 :
           const AEAmpExtInformation& stAEAmpExtInformation: AE幅值ExtInformation
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置AE幅值ExtInformation
         ************************************************/
        void setAEAmpExtInformation(const AEAmpExtInformation& stAEAmpExtInformation);

        /************************************************
         * 函数名   : setAEAmpData
         * 输入参数 :
           const AEAmpData& stAEAmpData: AE幅值Data
         * 输出参数 : NULL
         * 返回值   : void
         * 功能     : 设置AE幅值Data
         ************************************************/
        void setAEAmpData(const AEAmpData& stAEAmpData);

        /************************************************
         * 函数名   : getAEAmpExtInformation
         * 输入参数 : NULL
         * 输出参数 :
           AEAmpExtInformation& stAEAmpExtInformation: AE幅值ExtInformation
         * 返回值   : void
         * 功能     : 获取AE幅值ExtInformation
         ************************************************/
        void getAEAmpExtInformation(AEAmpExtInformation& stAEAmpExtInformation);

        /************************************************
         * 函数名   : setAEAmpData
         * 输入参数 : NULL
         * 输出参数 :
           AEAmpData& stAEAmpData: AE幅值Data
         * 返回值   : void
         * 功能     : 获取AE幅值Data
         ************************************************/
        void getAEAmpData(AEAmpData& stAEAmpData);

    protected:

        /************************************************
         * 函数名   : saveBinarySpectrumExtInfo
         * 输入参数 :
           QDataStream& out: 输出流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存二进制可扩展信息段
         ************************************************/
        virtual bool saveBinarySpectrumExtInfo(QDataStream& out);

        /************************************************
         * 函数名   : saveXMLSpectrumExtInfo
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
           QDomElement& element: dom元素
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存XML可扩展信息段
         ************************************************/
        virtual bool saveXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj, QDomElement& element);

        /************************************************
         * 函数名   : saveJSONSpectrumExtInfo
         * 输入参数 :
           rapidjson::Document::AllocatorType& alloc:
           rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存JSON可扩展信息段
         ************************************************/
        virtual bool saveJSONSpectrumExtInfo(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : saveBinarySpectrumData
         * 输入参数 :
           QDataStream& out: 输出流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存二进制图谱数据段
         ************************************************/
        virtual bool saveBinarySpectrumData(QDataStream& out);

        /************************************************
         * 函数名   : saveXMLSpectrumData
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
           QDomElement& element: dom元素
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存XML图谱数据段
         ************************************************/
        virtual bool saveXMLSpectrumData(XMLDocument& xmlDocumentObj, QDomElement& element);

        /************************************************
         * 函数名   : saveJSONSpectrumData
         * 输入参数 :
           rapidjson::Document::AllocatorType& alloc:
           rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 保存JSON图谱数据段
         ************************************************/
        virtual bool saveJSONSpectrumData(rapidjson::Document::AllocatorType& alloc, rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : parseBinarySpectrumExtInfo
         * 输入参数 :
           QDataStream& in: 输入流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析二进制可扩展信息段
         ************************************************/
        virtual bool parseBinarySpectrumExtInfo(QDataStream& in);

        /************************************************
         * 函数名   : parseXMLSpectrumExtInfo
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析XML可扩展信息段
         ************************************************/
        virtual bool parseXMLSpectrumExtInfo(XMLDocument& xmlDocumentObj);

        /************************************************
         * 函数名   : parseJSONSpectrumExtInfo
         * 输入参数 :
           const rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析JSON可扩展信息段
         ************************************************/
        virtual bool parseJSONSpectrumExtInfo(const rapidjson::Value& jsonValue);

        /************************************************
         * 函数名   : parseBinarySpectrumData
         * 输入参数 :
           QDataStream& in: 输入流
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析二进制图谱数据段
         ************************************************/
        virtual bool parseBinarySpectrumData(QDataStream& in);

        /************************************************
         * 函数名   : parseXMLSpectrumData
         * 输入参数 :
           XMLDocument& xmlDocumentObj: xml文档对象
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析XML图谱数据段
         ************************************************/
        virtual bool parseXMLSpectrumData(XMLDocument& xmlDocumentObj);

        /************************************************
         * 函数名   : parseJSONSpectrumData
         * 输入参数 :
           const rapidjson::Value& jsonValue: json数据
         * 输出参数 : NULL
         * 返回值   : bool
         * 功能     : 解析JSON图谱数据段
         ************************************************/
        virtual bool parseJSONSpectrumData(const rapidjson::Value& jsonValue);

    private:
        AEAmpSpectrumPrivate* m_pAEAmpSpectrumPrivate{ Q_NULLPTR };
    };
}
