QPushButton#SliderBtn{
        border-style: none;
        border: 0px;
        color: #F0F0F0;
        padding: 0px;
        border-radius:25px;
        background: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1, stop:0 #F0F0F0, stop:1 #1077B5);
}

QPushButton#SliderBtn:hover{
        background: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1, stop:0 #5CACEE, stop:1 #4F94CD);
}

QPushButton#SliderBtn:pressed{
        background: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1, stop:0 #F0F0F0, stop:1 #1077B5);
}

QSlider::groove:vertical,QSlider::sub-page:vertical {
        background:#808080;
        width: 8px;
        border-radius: 3px;
}

QSlider::add-page:vertical {
        width: 8px;
        border-radius: 3px;
        background: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1, stop:0 #F0F0F0, stop:1 #1077B5);
}

QSlider::handle:vertical {
        height: 40px;
        margin-left: -16px;
        margin-right: -16px;
        border-radius: 20px;
        background: qradialgradient(spread: pad, cx: 0.5, cy: 0.5, radius: 0.5, fx: 0.5, fy: 0.5, stop: 0.6 #F0F0F0, stop:0.778409 #5CACEE);
}

QSlider::groove:horizontal,QSlider::add-page:horizontal {
        background:#808080;
        height: 8px;
        border-radius: 3px;
}

QSlider::sub-page:horizontal {
        height: 8px;
        border-radius: 3px;
        background: qlineargradient(spread:pad, x1:0, y1:0, x2:0, y2:1, stop:0 #F0F0F0, stop:1 #1077B5);
}

QSlider::handle:horizontal {
        width: 40px;
        height: 40px;
        margin-top: -16px;
        margin-bottom: -16px;
        border-radius: 20px;
        background: qradialgradient(spread: pad, cx: 0.5, cy: 0.5, radius: 0.5, fx: 0.5, fy: 0.5, stop: 0.6 #F0F0F0, stop:0.778409 #5CACEE);
}

