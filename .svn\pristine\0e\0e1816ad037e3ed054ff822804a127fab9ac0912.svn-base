#include <QApplication>
#include <QDebug>
#include "AEAmpPlayBackView.h"
#include "datadefine.h"
#include "ae/dataSave/AEAmpDataSave.h"
#include "window/Window.h"
#include "datafile/datafile.h"
#include "datafile/ae/aemapdefine.h"
#include "datafile/ae/aeampdatamap.h"
#include "datafile/mapdatafactory.h"
#include "diagnosismgr/diagnosismanager.h"

const UINT16 ROUTE_TITLE_WIDTH = Window::WIDTH;
const UINT16 ROUTE_TITLE_HEIGHT = 400;

/****************************
功能： 构造函数
输入参数:
    parent -- 父窗体
*****************************/
AEAmpPlayBackView::AEAmpPlayBackView( QWidget *parent )
    : PlayBackBase( parent )
{
    m_pChart = new AEAmpChart;
    setCenterWidget( m_pChart );
}

AEAmpPlayBackView::~AEAmpPlayBackView()
{

}

void AEAmpPlayBackView::playbackFile( const QString& strFileName )
{
    AEAmpDataSave  fDataSave;
    int value = fDataSave.getDataByPDA( strFileName,&m_sPlayBackDataInfo );

    if( HC_FAILURE == value )
    {
        qWarning() << "AE playback :" << strFileName << " get data content failed!" << endl;
        return;
    }
    setStationName(m_sPlayBackDataInfo.stHeadInfo.strSubstationName);
    setDeviceName(m_sPlayBackDataInfo.stHeadInfo.strDeviceName);

    m_pChart->clearNoise();
    m_pChart->clear();
    m_pChart->clearDiagRet();

    dbg_info("m_sPlayBackDataInfo.sGain is %d\n", m_sPlayBackDataInfo.sGain);
    m_pChart->setGain( m_sPlayBackDataInfo.gain() );
    m_pChart->setUnit( m_sPlayBackDataInfo.unit() );

    //更新频率成分1、频率成分2的标题
    m_pChart->setSpectrum((UINT16)m_sPlayBackDataInfo.fSyncFreq);

    m_pChart->setChannel(AE::transformChannelFromDataFile((AEMapNS::AETransformerType) m_sPlayBackDataInfo.eTransformerType));
    
    AE::AmplitudeData data;
    data.fRMS = m_sPlayBackDataInfo.stAEAmpData.fRmsBGN;
    data.fPeakValue = m_sPlayBackDataInfo.stAEAmpData.fPeakBGN;
    data.fFirstFreqComValue = m_sPlayBackDataInfo.stAEAmpData.fFrequency1BGN;
    data.fSecondFreqComValue = m_sPlayBackDataInfo.stAEAmpData.fFrequency2BGN;
    m_pChart->setNoiseData( data );

    data.fRMS = m_sPlayBackDataInfo.stAEAmpData.fRms;
    data.fPeakValue = m_sPlayBackDataInfo.stAEAmpData.fPeak;
    data.fFirstFreqComValue = m_sPlayBackDataInfo.stAEAmpData.fFrequency1;
    data.fSecondFreqComValue = m_sPlayBackDataInfo.stAEAmpData.fFrequency2;
    m_pChart->setData( data );
    QString qstrDesInfo = DiagnosisManager::instance()->getDiagInfoByPlayback(m_sPlayBackDataInfo.stHeadInfo.qstrPDSignalTypeInfos, DIAG_AE);

    DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
    stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(m_sPlayBackDataInfo.stHeadInfo.ePDDefectLevel);
    stDiagDisplayInfo.qstrPDDesInfo = qstrDesInfo;
    stDiagDisplayInfo.qstrPDSignalInfos = m_sPlayBackDataInfo.stHeadInfo.qstrPDSignalTypeInfos;
    m_pChart->playbackDiagInfo(stDiagDisplayInfo);

    dbg_info("m_sPlayBackDataInfo.stAEAmpData.fRms is %f\n", m_sPlayBackDataInfo.stAEAmpData.fRms);
    dbg_info("m_sPlayBackDataInfo.stAEAmpData.fPeak is %f\n", m_sPlayBackDataInfo.stAEAmpData.fPeak);
    dbg_info("m_sPlayBackDataInfo.stAEAmpData.fFrequency1 is %f\n", m_sPlayBackDataInfo.stAEAmpData.fFrequency1);
    dbg_info("m_sPlayBackDataInfo.stAEAmpData.fFrequency2 is %f\n", m_sPlayBackDataInfo.stAEAmpData.fFrequency2);
    dbg_info("m_sPlayBackDataInfo.stAEAmpData.fRmsBGN is %f\n", m_sPlayBackDataInfo.stAEAmpData.fRmsBGN);
    dbg_info("m_sPlayBackDataInfo.stAEAmpData.fPeakBGN is %f\n", m_sPlayBackDataInfo.stAEAmpData.fPeakBGN);
    dbg_info("m_sPlayBackDataInfo.stAEAmpData.fFrequency1BGN is %f\n", m_sPlayBackDataInfo.stAEAmpData.fFrequency1BGN);
    dbg_info("m_sPlayBackDataInfo.stAEAmpData.fFrequency2BGN is %f\n", m_sPlayBackDataInfo.stAEAmpData.fFrequency2BGN);
    return;
}
