/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* PatrolChartView.h
*
* 初始版本：1.0
* 作者：王谦
* 修改日期：2017年7月10日
* 摘要：终端各级图谱界面的基类

* 当前版本：1.0
*/

#ifndef PATROLCHARTVIEW_H
#define PATROLCHARTVIEW_H

#include <QWidget>
#include <QVBoxLayout>
#include "titlebar/TitleBar.h"
#include "chartwidget/ChartWidget.h"
#include "pushButton/PushButton.h"
#include "buttonBar/PushButtonBar.h"

class BaseChartView : public QWidget
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    输入参数:
        strTitle -- 标题
        parent -- 父窗体
    *****************************/
    explicit BaseChartView( const QString& strTitle,QWidget *parent = 0);

    /****************************
    功能： 设置图表
    输入参数:
        pChart:图表
    *****************************/
    void setChart( ChartWidget* pChart );

    /****************************
    功能： 获取图谱控件
    返回值： 图谱控件
    *****************************/
    ChartWidget* chart( void );

    /****************************
    功能： 获取标题栏控件
    返回值： 标题栏控件
    *****************************/
    TitleBar* titleBar( void );

    /*************************************************
    功能： 创建控制按钮栏
    输入参数:
        pchContext -- 国际化用的域
        pInfos -- 按钮配置信息
        iCount -- 按钮个数
        iColumnCount -- 列数
        mode -- 显示模式
        bool -- 是否支持上下键操作
            true -- 支持（默认）
            false -- 不支持
    返回值：创建的ButtonBar
    *************************************************************/
    PushButtonBar* createButtonBar(const char* const pchContext,
                                       const ButtonInfo::Info* pInfos,
                                       int iCount,
                                       bool bIsUpAndDownKeySupport = true,
                                       int iColumnCount = 4,
                                       PushButton::Mode mode = PushButton::TEXT_ONLY );
signals:
    /*************************************************
    功能： 信号，鼠标点击
    *************************************************************/
    void sigPressed();
protected:
    /*************************************************
    功能： 重载鼠标点击事件
    输入参数：
            event -- 事件
    *************************************************************/
    void mousePressEvent( QMouseEvent* event );

    /*************************************************
    功能： 键盘事件
    输入参数:
        event -- 事件
    *************************************************************/
    void keyPressEvent( QKeyEvent* event );

    /*************************************************
    功能： 响应S键事件
    *************************************************************/
    virtual void onSKeyPressed();
protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    virtual void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    virtual void onCommandButtonPressed( int id );

    /*************************************************
    功能： 槽，响应条目被点击后的事件
    输入参数：
            id -- 条目序号
    *************************************************************/
    virtual void onItemClicked( int id );
private:
    TitleBar *m_pTitleBar;  // 标题栏
    ChartWidget* m_pChart;//图表
    PushButtonBar* m_pButtonBar;//控制按钮栏
    QVBoxLayout* m_playoutChart;//图表布局
    QVBoxLayout* m_playoutButton;//按钮布局
    char* m_pchContext;//国际化用的域
};

#endif // PATROLCHARTVIEW_H
