﻿/*
* Copyright (c) 2016.12，南京华乘电气科技有限公司
* All rights reserved.
*
* AEAmpView.h
*
* 初始版本：1.0
* 作者：赵勇军
* 修改日期：2016年12月30日
*       新版本重构
* 摘要：CA界面配置信息
*       按钮配置等
* 当前版本：1.0
*/
#ifndef INFRARED_WIDGET_H
#define INFRARED_WIDGET_H

#include <QApplication>
#include "datadefine.h"

#include "infrared/infrareddatadefine.h"

#define INFRARED_CONFIG_TRANSLATE(str) qApp->translate(Infrared::CONTEXT, (str))

const quint32 ROTATE_ANGLE = 90;

namespace Infrared {
    const char* const CONTEXT = "Infrared";//域
    const char* const TEXT_TEMP_POINTS = QT_TRANSLATE_NOOP("Infrared", "Temp. Points Count");
    const char* const TEXT_UNIT_DEGREE = QT_TRANSLATE_NOOP("Infrared", "Unit(℃)");
    const char* const TEXT_FROZEN = QT_TRANSLATE_NOOP("Infrared", "Freeze");
    const char* const TEXT_RECOVER = QT_TRANSLATE_NOOP("Infrared", "Restore");
    const char* const TEXT_COLOR_TYPE = QT_TRANSLATE_NOOP("Infrared", "Palette");

    const char* const TEXT_IRON = QT_TRANSLATE_NOOP("Infrared", "Iron Red");
    const char* const TEXT_RAIN = QT_TRANSLATE_NOOP("Infrared", "Rainbow");
    const char* const TEXT_HOT_FEVER = QT_TRANSLATE_NOOP("Infrared", "Red Heat");
    const char* const TEXT_BLACK_FEVER = QT_TRANSLATE_NOOP("Infrared", "Black Heat");
    const char* const TEXT_WHITE_FEVER = QT_TRANSLATE_NOOP("Infrared", "White Heat");
    const char* const TEXT_LAVA = QT_TRANSLATE_NOOP("Infrared", "Lava");
    const char* const TEXT_MEDICAL = QT_TRANSLATE_NOOP("Infrared", "Medical");
    const char* const TEXT_HOT_IRON = QT_TRANSLATE_NOOP("Infrared", "Hot Iron");
    const char* const TEXT_INK_BROWN = QT_TRANSLATE_NOOP("Infrared", "Ink Brown");
    const char* const TEXT_MIAO_HONG = QT_TRANSLATE_NOOP("Infrared", "Miao Hong");

    const char* const TEXT_DOT = QT_TRANSLATE_NOOP("Infrared", "Point");
    const char* const TEXT_LINE = QT_TRANSLATE_NOOP("Infrared", "Line");
    const char* const TEXT_RECT = QT_TRANSLATE_NOOP("Infrared", "Rectangle");
    const char* const TEXT_CIRCLE = QT_TRANSLATE_NOOP("Infrared", "Circle");
    const char* const TEXT_ANALYSE_SHAPE = QT_TRANSLATE_NOOP("Infrared", "Analyze");
    const char* const TEXT_TEMPERATURE_CURVE = QT_TRANSLATE_NOOP("Infrared", "Line Temp.");
    const char* const TEXT_DELETE_SHAPE = QT_TRANSLATE_NOOP("Infrared", "Del Graph");
    const char* const TEXT_DELETE_SG_SHAPE = QT_TRANSLATE_NOOP("Infrared", "Delete");
    const char* const TEXT_DELETE_ALL_SHAPE = QT_TRANSLATE_NOOP("Infrared", "Delete All");
    const char* const TEXT_LAST_PAGE = QT_TRANSLATE_NOOP("Infrared", "PgUp");
    const char* const TEXT_NEXT_PAGE = QT_TRANSLATE_NOOP("Infrared", "PgDn");
    const char* const TEXT_SET = QT_TRANSLATE_NOOP("Infrared", "Setting");
    const char* const TEXT_MORE = QT_TRANSLATE_NOOP("Infrared", "More...");
    const char* const TEXT_MEASUREMENT_METHOD = QT_TRANSLATE_NOOP("Infrared", "Measurement Method");

    const char* const TEXT_SAVE_DATA = QT_TRANSLATE_NOOP("Infrared", "Save Data");
    const char* const TEXT_SAVE = QT_TRANSLATE_NOOP("Infrared", "Save");
    const char* const TEXT_LOAD_DATA = QT_TRANSLATE_NOOP("Infrared", "Load Data");
    const char* const TEXT_DELETE_DATA = QT_TRANSLATE_NOOP("Infrared", "Delete Data");

    const char* const TEXT_PLAYBACK = QT_TRANSLATE_NOOP("Infrared", "Playback");

    const char* const INFRARED_IMAGING_NAME = QT_TRANSLATE_NOOP("Infrared", "IR Image");

    const char* const TEXT_EXIT_PLAY = QT_TRANSLATE_NOOP("Infrared", "Stop Play");
    const char* const TEXT_ADD = QT_TRANSLATE_NOOP("Infrared", "Add");
    const char* const TEXT_EXIT = QT_TRANSLATE_NOOP("Infrared", "Exit");
    const char* const TEXT_RETURN = QT_TRANSLATE_NOOP("Infrared", "Return");
    const char* const TEXT_AUTO_FOCUS = QT_TRANSLATE_NOOP("Infrared", "Auto Focus");
    const char* const TEXT_CLOSE_FOCUS_FINE_TUNING = QT_TRANSLATE_NOOP("Infrared", "Close Focus Fine-Tuning");
    const char* const TEXT_FAR_FOCUS_FINE_TUNING = QT_TRANSLATE_NOOP("Infrared", "Far Focus Fine-Tuning");
    const char* const TEXT_ELECTRONIC_ZOOM = QT_TRANSLATE_NOOP("Infrared", "Electronic Zoom");
    const char* const TEXT_LASER_CONTROL = QT_TRANSLATE_NOOP("Infrared", "Laser Control");
    const char* const TEXT_ON = QT_TRANSLATE_NOOP("Infrared", "On");
    const char* const TEXT_OFF = QT_TRANSLATE_NOOP("Infrared", "Off");
    const char* const TEXT_LED_FILL_LIGHT = QT_TRANSLATE_NOOP("Infrared", "LED Fill Light");
    const char* const TEXT_DISPLAY_MODE = QT_TRANSLATE_NOOP("Infrared", "Display Mode");
    const char* const TEXT_DISPLAY_INFRARED = QT_TRANSLATE_NOOP("Infrared", "Infrared");
    const char* const TEXT_DISPLAY_PICTURE_IN_PICTURE = QT_TRANSLATE_NOOP("Infrared", "Picture in Picture");
    const char* const TEXT_DISPLAY_DIGITAL_CAMERA = QT_TRANSLATE_NOOP("Infrared", "Digital Camera");
    const char* const TEXT_AUXILIARY_LIGHTING = QT_TRANSLATE_NOOP("Infrared", "Auxiliary Lighting");


    const QString TEMPERATURE_COMPANY = "°C";

}

#endif // INFRARED_WIDGET_H
