/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* PopupButton.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2016年11月15日
* 摘要：弹出式按钮基类定义
*      1）从ControlButton派生
*      2）点击后，弹出一个配置窗体（如radio,slider等）
* 当前版本：1.0
*/
#ifndef POPUPBUTTON_H
#define POPUPBUTTON_H

#include <QWidget>
#include <QLabel>
#include <QIcon>
#include <QMouseEvent>
#include <QFocusEvent>
#include "ControlButton.h"
#include "popupWidget/PopupWidget.h"
#include "widgetglobal.h"

class WIDGET_EXPORT PopupButton : public ControlButton
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent -- 父窗体
    *************************************************************/
    explicit PopupButton( QWidget* parent = 0 );

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~PopupButton();

    /*************************************************
    功能： 设置数值
    输入参数:
        iValue: 数值
    *************************************************************/
    void setValue( int iValue );

    /*************************************************
    功能： 获取数值
    返回值：数值
    *************************************************************/
    int value() const;

    /*************************************************
    功能： 获取弹出窗体指针
    返回值： 弹出窗体指针
    *************************************************************/
    PopupWidget* popupWidget();

    /*************************************************
    功能： 设置popup控件显示格式
    输入参数:
        enMode -- 显示格式
    *************************************************************/
    void setPopupMode(PopupWidget::PopupMode enMode);

    /*************************************************
    功能： 响应键盘确认事件
    *************************************************************/
    virtual void onEnterKeyPressed();

    /*************************************************
    功能： 处理鼠标释放事件
    输入参数: pEvent -- 鼠标释放事件
    *************************************************************/
    virtual void processMouseReleaseEvent(QMouseEvent* pEvent);

signals:
    /************************************************
     * 功能     : 值发生变化
     * 输入参数 :
     *      id -- 按键ID
     *      iValue -- 值
     ************************************************/
    void sigValueChanged( int id, int iValue );

    /************************************************
     * 功能：显示模式改变信号
     * 输入参数：
     *        enMode：显示模式
     ************************************************/
    void sigPopupModeChanged(PopupWidget::PopupMode enMode);

protected:
    /************************************************
     * 功能     : 控制Popup窗体的显示
     * 输入参数 :
     *      bShow -- true :显示
     *               false :隐藏
     ************************************************/
    virtual void showPopup( bool bShow );

    /************************************************
     * 功能     : 获取弹出窗体的位置
     ************************************************/
    virtual QPoint popupPostion()const;
    /************************************************
     * 功能     : 设置弹出窗体
     *      pWidget -- 弹出窗体
     ************************************************/
    void setPopupWidget( PopupWidget* pWidget );
    /************************************************
     * 功能     : 鼠标点击事件
     * 输入参数 :
     *      event -- 事件
     ************************************************/
    void mousePressEvent( QMouseEvent *event );
    /************************************************
     * 功能     : 鼠标点击释放事件
     * 输入参数 :
     *      event -- 事件
     ************************************************/
    void mouseReleaseEvent( QMouseEvent *event );
    /*************************************************
    功能： 响应取消激活事件
    *************************************************************/
    virtual void onDisactive();

    /*************************************************
    功能： 从值获取显示的值
    *************************************************************/
    virtual QString textFromValue()const;

private slots:
    /************************************************
     * 功能     : popup编辑窗体提交值
     * 输入参数 :
     *      iValue -- 值
     *      strValue -- 值描述
     ************************************************/
    void onValueSubmit( int iValue, const QString& strValue );

    /************************************************
     * 功能     : popup窗体隐藏
     ************************************************/
    void onPopupHide();
private:
    PopupWidget* m_pPopupWidget;//弹出式窗体
    int m_iValue;//值
};

#endif // CONTROLBUTTON_H

