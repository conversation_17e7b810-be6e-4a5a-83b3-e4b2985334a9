/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* AEValueBar.h
*
* 初始版本：1.0
* 作者：邵震宇
* 修改日期：2016年11月17日
*       重构
* 摘要：AE的数值Bar

* 当前版本：1.0
*/
#ifndef VALUEBAR_H
#define VALUEBAR_H

#include <QFrame>

class AEValueBar : public QFrame
{
    Q_OBJECT

public:
    /************************************************
     * 函数名    :AEValueBar
     * 输入参数  ：名称、父对象指针
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：构造函数
     ************************************************/
    explicit AEValueBar(const QString& title, QWidget *parent = NULL);

    /************************************************
     * 函数名    :setScale
     * 输入参数  ：最小值，最大值
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置数值范围
     ************************************************/
    void setScale(double min, double max);

    /************************************************
     * 函数名    :lowerBound
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：最小值
     * 功能     ：返回当前数值范围的最小值
     ************************************************/
    double lowerBound() const;

    /************************************************
     * 函数名    :upperBound
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：最大值
     * 功能     ：返回当前数值范围的最大值
     ************************************************/
    double upperBound() const;

    /************************************************
     * 函数名    :setNoiseValue
     * 输入参数  ：
     *          value: 噪声值
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置噪声值
     ************************************************/
    void setNoiseValue(double value);

    /************************************************
     * 函数名    :noiseValue
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：读取噪声值
     ************************************************/
    double noiseValue(void) const;

    /************************************************
     * 函数名    :clearNoise
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：清除噪声值
     ************************************************/
    void clearNoise();

    /************************************************
     * 函数名    :setTitle
     * 输入参数  ：名称
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置数值栏的名称
     ************************************************/
    void setTitle(const QString& title);

    /************************************************
     * 函数名    :setValue
     * 输入参数  ：当前值
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置数值栏的当前值
     ************************************************/
    void setValue(double value);

    /************************************************
     * 函数名    :value
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：当前值
     * 功能     ：读取数值栏的当前值
     ************************************************/
    double value(void) const;

    /************************************************
     * 功能     ：清除值
     ************************************************/
    void clear();

    /************************************************
     * 函数名    :setSuffix
     * 输入参数  ：显示数值文本的后缀
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置数值文本的后缀，一般为"mV","dB"等表示单位的字符串
     ************************************************/
    void setSuffix(const QString& suffix);

    /************************************************
     * 函数名    :setSuffix
     * 输入参数  ：
     *           w: 数值条的宽度
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置数值条的宽度
     ************************************************/
    void setPipeWidth(int w);

protected:
    /************************************************
     * 函数名    :paintEvent
     * 输入参数  ：
     *          e:重绘事件指针
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：绘制整个窗口
     ************************************************/
    void paintEvent(QPaintEvent *e);

private:
    /************************************************
     * 函数名    :drawTicks
     * 输入参数  ：
     *          pPainter:绘制工具指针
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：绘制刻度
     ************************************************/
    void drawTicks(QPainter *pPainter);

    /************************************************
     * 函数名    :drawShadePanel
     * 输入参数  ：
     *          pPainter:绘制工具指针
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：绘制阴影边框
     ************************************************/
    void drawShadePanel(QPainter *pPainter);

    /************************************************
     * 函数名    :drawLiquid
     * 输入参数  ：
     *          pPainter:绘制工具指针
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：绘制数值表
     ************************************************/
    void drawLiquid(QPainter *pPainter);

    /************************************************
     * 函数名    :drawText
     * 输入参数  ：
     *          pPainter:绘制工具指针
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：绘制文本
     ************************************************/
    void drawText(QPainter *pPainter);

    /************************************************
     * 函数名    :range
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：最大值减去最小值
     * 功能     ：计算数值条的范围
     ************************************************/
    double range();

    /************************************************
     * 函数名    :liquidTotalWidth
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：数值条的宽度
     * 功能     ：计算数值条的宽度
     ************************************************/
    double liquidTotalWidth();

    /************************************************
     * 函数名    :liquidTopLeft
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：数值条坐上角的坐标
     * 功能     ：返回数值条坐上角的坐标
     ************************************************/
    QPointF liquidTopLeft();

    /************************************************
     * 函数名    :liquidWidth
     * 输入参数  ：NULL
     * 输出参数  ：NULL
     * 返回值   ：数值条的长度
     * 功能     ：返回数值条的长度
     ************************************************/
    double liquidWidth();

    /************************************************
     * 函数名    :validateValue
     * 输出参数  :dValue
     * 返回值   ：NULL
     * 功能     ：将值限制在量程内
     ************************************************/
    void validateValue(double & dValue);


private:
    double m_lowerBound;       // 范围下限
    double m_upperBound;       // 范围上限

    QString m_strTitle;        // 标题

    double m_value;            // 当前值

    QString m_strSuffix;       // 后缀

    int m_pipeWidth;         // 数值条的宽度

    bool m_hasNoise;           // 是否有噪声
    double m_noiseValue;       // 噪声值
};

#endif // VALUEBAR_H
