/*
* Copyright (c) 2017.05，南京华乘电气科技有限公司
* All rights reserved.
*
* cloudprotocol.h
*
* 初始版本：1.0
* 作者：陈康
* 创建日期：2017年05月02日
* 摘要：按照协议进行参数设置并生成对应格式的http报文
*
*

* 当前版本：1.0
*/

#ifndef CLOUDPROTOCOL_H
#define CLOUDPROTOCOL_H

#include <QByteArray>
#include <QMap>
#include <QUrl>
#include "upgradeservice.h"

namespace CPROTOCOL {
/*******************请求协议各个字段的键值*******************/
//云下载协议版本
#define PRO_VERSION_KEY "v"
//时间戳 NOTE:该字段在发送前取值
#define TIME_STAMP_KEY "timestamp"
//sha1签名
#define PRO_SIGN_KEY "sign"
//软件识别码
#define SIGNATURE_ID_KEY "signatureID"
//软件版本号
#define SW_VERSION_KEY "version"
//用户的ID
#define CLIENT_ID_KEY "client_id"
//在软件更新表中的软件路径
#define FILE_PATH_KEY "path"
//包的索引
#define INDEX_BLOCK_KEY "index"
//客户端密钥 注意：只在计算签名值时使用，不在协议中出现
#define CLIENT_SECRET_KEY "client_secret"

/*******************请求协议各个字段的默认值*******************/
//云下载协议版本的默认值
#define PRO_VERSION_VALUE "1"
//默认的client_id值
#define CLIENT_ID_VALUE "WEB"
//默认的客户端密钥的值
#define CLIENT_SECRET_VALUE "web"

/*******************应答协议各个字段的键值*******************/
//应答结果字段
#define REPLY_RESULT_KEY "result"
//安装包总的数据块大小
#define TOTAL_BLOCK_KEY "totalBlocks"
//当前数据包序号
#define BLOCK_INDEX_KEY "blockIndex"
//数据包的Md5码
#define BLOCK_MD5_KEY "Md5"
//云端软件路径
#define REMOTE_FILEPATH_KEY "filePath"
//云端软件版本
#define REMOTE_VERSION_KEY "version"
//更新说明
#define UPDATE_CAPTION_KEY "updateCaption"
}

using namespace CPROTOCOL;
class CloudProtocol
{
public:
    CloudProtocol();

    /*************************************************
    函数名：
    输入参数:
    输出参数：url--需要设置参数的url请求
    返回值：
    功能：根据设置的参数，添加请求内容
    *************************************************************/
    void addQueryItemsToUrl(QUrl &url);

    /*************************************************
    函数名：
    输入参数:strKey--请求的键值
    输出参数：
    返回值：对应的value值
    功能：根据参数的key获得对应的value
    *************************************************************/
    QString queryItemValue(QString strKey);

    /*************************************************
    函数名：
    输入参数:strKey--设置的键值 strValue--设置的值
    输出参数：
    返回值：
    功能：设置参数的value，如果key不存在则添加，如果key存在则修改对应value
    *************************************************************/
    void setQueryItem(QString strKey, QString strValue);

private:
    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：根据当前的参数值计算出对应的sha1码
    *************************************************************/
    void calculateRequest();


    QMap<QString, QString> m_queryItems;//保存需要查询的参数数据
};

#endif // CLOUDPROTOCOL_H
