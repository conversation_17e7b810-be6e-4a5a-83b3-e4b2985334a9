/*
* Copyright (c) 2017.12，南京华乘电气科技有限公司
* All rights reserved.
*
* infrareddatadefine.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年12月20日
* 摘要：InfraredWidget模块宏定义、常量、结构、枚举等

* 当前版本：1.0
*/

#ifndef INFRAREDDATADEFINE
#define INFRAREDDATADEFINE

//const int IMAGE_ORIGINAL_WIDTH  = 320;
//const int IMAGE_ORIGINAL_HEIGHT = 256;
//const int MATRIX_POINTS         = IMAGE_ORIGINAL_WIDTH * IMAGE_ORIGINAL_HEIGHT;

const int IMAGE_SCALED_WIDTH    = 600;
const int IMAGE_SCALED_HEIGHT   = 480;
//const double SCALE_TIMES        = double(IMAGE_SCALED_WIDTH) / IMAGE_ORIGINAL_WIDTH;

const float ORIGINAL_TEMPERATURE = 0.00001f;

typedef enum _InfraredButton
{
    BUTTON_INFRARED_STATUS = 0,       // 冻结/恢复  0
    BUTTON_INFRARED_COLOR_TYPE,       // 调色板  1
    BUTTON_INFRARED_ANALYSE_SHAPE,    // 分析图形  2
    BUTTON_INFRARED_DELETE_ALL_SHAPE, // 删除全部分析图形  3
    BUTTON_INFRARED_LINE_CURVE,       // 线温度分布  4
    BUTTON_INFRARED_SAVA_DATA,        // 保存数据  5
    BUTTON_INFRARED_LOAD_DATA,        // 载入数据  6
    BUTTON_INFRARED_DELETE_DATA,      // 删除数据  7
    BUTTON_INFRARED_SET_PARAM,        // 设置参数  8
    BUTTON_INFRARED_LAST_PAGE,        // 上一页(回放用)  9
    BUTTON_INFRARED_NEXT_PAGE,        // 下一页(回放用)  10
    BUTTON_INFRARED_EXIT_PLAY,        // 退出回放 11
    BUTTON_INFRARED_ADD,              // 新增测试数据
    BUTTON_INFRARED_EXIT,             // 退出
    BUTTON_INFRARED_MORE,             // 更多
    BUTTON_INFRARED_RETURN,             // 返回
}InfraredButton;

typedef enum _ObjParamType
{
    EMISSIVITY = 0,                       //辐射率
    DISTANCE,                             //测试距离
    ATMTEMP,                              //大气温度
    AMBTEMP,                              //反射温度
    RELHUM,                               //相对湿度
    REFERTEMP,                            //参考温度
    EXTOPTTEMP,                           //外部光学温度
    EXTOPTTRANSM,                         //外部光学传输率
    OBJ_PARAM_COUNT
}ObjParamType;	// 影响红外测试温度参数类型

typedef enum _ColorType
{
    COLOR_TYPE_NONE = -1,
    COLOR_TYPE_IRON = 0,                  //铁红
    COLOR_TYPE_RAIN,                      //彩虹
    COLOR_TYPE_IRON_REVERSAL,             //红热（对应高德的热铁？）
    COLOR_TYPE_BLACK_FEVER,               //黑热
    COLOR_TYPE_WHITE_FEVER,               //白热
    COLOR_TYPE_LAVA,                      //熔岩
    //COLOR_TYPE_BLUE_RED,                  //蓝红？对应高德的医疗？
    //COLOR_TYPE_HIGH_CONTRAST,             //高对比？对应高德的熔岩？
    COLOR_TYPE_COUNT
}ColorType;

typedef enum _RunningMode
{
    RUN_MODE_NONE = -1,
    RUN_MODE_AUTO = 0,                    //自动模式
    RUN_MODE_MANUAL,                      //手动模式
    RUN_MODE_COUNT
}RunningMode;

typedef enum _ShapeItemType
{
    SHAPE_NONE = -1,
    SHAPE_DOT = 0,
    SHAPE_LINE_BDIAG,   //正斜率
    SHAPE_LINE_FDIAG,   //负斜率
    SHAPE_RECTANGLE,
    SHAPE_CIRCLE,
    SHAPE_COUNT
}ShapeItemType;

typedef enum _ButtonShape
{
    BUTTON_SHAPE_NONE = 0,
    BUTTON_SHAPE_DOT,
    BUTTON_SHAPE_LINE,
    BUTTON_SHAPE_RECTANGLE,
    BUTTON_SHAPE_CIRCLE,
    BUTTON_SHAPE_MAX
}ButtonShape;

typedef enum _ElectronicZoomLevel
{
    ELECTRONIC_ZOOM_LEVEL_1 = 0,
    ELECTRONIC_ZOOM_LEVEL_2,
    ELECTRONIC_ZOOM_LEVEL_3,
    ELECTRONIC_ZOOM_LEVEL_COUNT
}ElectronicZoomLevel;

// BMP文件头（14字节）
typedef struct BITMAPFILEHEADER
{
      unsigned short bfType;              //类型（BM） 0X4d42
      unsigned int   bfSize;              //文件大小，字节数
      unsigned short bfReserved1;         //保留字1，必须为0
      unsigned short bfReserved2;         //保留字2，必须为0
      unsigned int   bfOffBits;           //位偏移，RGB点阵的开始处
}__attribute__((packed)) BITMAPFILEHEADER;

// 位图信息头（40字节）
typedef struct BITMAPINFOHEADER
{
    unsigned int   biSize;                 //本结构所占用字节数
    long           biWidth;                //位图的宽度，以像素为单位
    long           biHeight;               //位图的高度，以像素为单位
    unsigned short biPlanes;               //目标设备的级别，必须为1
    unsigned short biBitCount;             //每个像素所需的位数，必须是1(双色)、4(16色)、8(256色)、24(真彩色)之一
    unsigned int   biCompression;          //位图压缩类型，必须是0(不压缩)、1(BI_RLE8压缩类型)、2(BI_RLE4压缩类型)之一
    unsigned int   biSizeImage;            //位图的大小，以字节为单位
    long           biXPelsPerMeter;        //位图水平分辨率，每米像素数
    long           biYPelsPerMeter;        //位图垂直分辨率，每米像素数
    unsigned int   biClrUsed;              //位图实际使用的颜色表中的颜色数
    unsigned int   biClrImportant;         //位图显示过程中重要的颜色数
}__attribute__((packed)) BITMAPINFOHEADER;

typedef struct _TemperatureInfo
{
    float max;               // 最大温度
    float min;               // 最小温度
    float avg;               // 平均温度
    unsigned int max_pos_x;  // 最大温度点x坐标
    unsigned int max_pos_y;  // 最大温度点y坐标
}TemperatureInfo;

//const int BMP_DATA_SIZE = IMAGE_ORIGINAL_WIDTH * IMAGE_ORIGINAL_HEIGHT * 3 + sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER);

// 铁红（rgb）
const unsigned char iron[128][3] =
{
    {0,     0,   0},
    {0,     0,   0},
    {0,     0,  36},
    {0,     0,  51},
    {0,     0,  66},
    {0,     0,  81},
    {2,     0,  90},
    {4,     0,  99},
    {7,     0, 106},
    {11,    0, 115},
    {14,    0, 119},
    {20,    0, 123},
    {27,    0, 128},
    {33,    0, 133},
    {41,    0, 137},
    {48,    0, 140},
    {55,    0, 143},
    {61,    0, 146},
    {66,    0, 149},
    {72,    0, 150},
    {78,    0, 151},
    {84,    0, 152},
    {91,    0, 153},
    {97,    0, 155},
    {104,   0, 155},
    {110,   0, 156},
    {115,   0, 157},
    {122,   0, 157},
    {128,   0, 157},
    {134,   0, 157},
    {139,   0, 157},
    {146,   0, 156},
    {152,   0, 155},
    {157,   0, 155},
    {162,   0, 155},
    {167,   0, 154},
    {171,   0, 153},
    {175,   1, 152},
    {178,   1, 151},
    {182,   2, 149},
    {185,   4, 149},
    {188,   5, 147},
    {191,   6, 146},
    {193,   8, 144},
    {195,  11, 142},
    {198,  13, 139},
    {201,  17, 135},
    {203,  20, 132},
    {206,  23, 127},
    {208,  26, 121},
    {210,  29, 116},
    {212,  33, 111},
    {214,  37, 103},
    {217,  41,  97},
    {219,  46,  89},
    {221,  49,  78},
    {223,  53,  66},
    {224,  56,  54},
    {226,  60,  42},
    {228,  64,  30},
    {229,  68,  25},
    {231,  72,  20},
    {232,  76,  16},
    {234,  78,  12},
    {235,  82,  10},
    {236,  86,   8},
    {237,  90,   7},
    {238,  93,   5},
    {239,  96,   4},
    {240, 100,   3},
    {241, 103,   3},
    {241, 106,   2},
    {242, 109,   1},
    {243, 113,   1},
    {244, 116,   0},
    {244, 120,   0},
    {245, 125,   0},
    {246, 129,   0},
    {247, 133,   0},
    {248, 136,   0},
    {248, 139,   0},
    {249, 142,   0},
    {249, 145,   0},
    {250, 149,   0},
    {251, 154,   0},
    {252, 159,   0},
    {253, 163,   0},
    {253, 168,   0},
    {253, 172,   0},
    {254, 176,   0},
    {254, 179,   0},
    {254, 184,   0},
    {254, 187,   0},
    {254, 191,   0},
    {254, 195,   0},
    {254, 199,   0},
    {254, 202,   1},
    {254, 205,   2},
    {254, 208,   5},
    {254, 212,   9},
    {254, 216,  12},
    {255, 219,  15},
    {255, 221,  23},
    {255, 224,  32},
    {255, 227,  39},
    {255, 229,  50},
    {255, 232,  63},
    {255, 235,  75},
    {255, 238,  88},
    {255, 239, 102},
    {255, 241, 116},
    {255, 242, 134},
    {255, 244, 149},
    {255, 245, 164},
    {255, 247, 179},
    {255, 248, 192},
    {255, 249, 203},
    {255, 251, 216},
    {255, 253, 228},
    {255, 254, 239},
    {255, 255, 249},
    {255, 255, 249},
    {255, 255, 249},
    {255, 255, 249},
    {255, 255, 249},
    {255, 255, 249},
    {255, 255, 249},
    {255, 255, 249}
};

// 彩虹（rgb）
const unsigned char rain[128][3] =
{
    {0,   0,   0},
    {0,   0,   0},
    {15,   0,  15},
    {31,   0,  31},
    {47,   0,  47},
    {63,   0,  63},
    {79,   0,  79},
    {95,   0,  95},
    {111,   0, 111},
    {127,   0, 127},
    {143,   0, 143},
    {159,   0, 159},
    {175,   0, 175},
    {191,   0, 191},
    {207,   0, 207},
    {223,   0, 223},
    {239,   0, 239},
    {255,   0, 255},
    {239,   0, 250},
    {223,   0, 245},
    {207,   0, 240},
    {191,   0, 236},
    {175,   0, 231},
    {159,   0, 226},
    {143,   0, 222},
    {127,   0, 217},
    {111,   0, 212},
    {95,   0, 208},
    {79,   0, 203},
    {63,   0, 198},
    {47,   0, 194},
    {31,   0, 189},
    {15,   0, 184},
    {0,   0, 180},
    {0,  15, 184},
    {0,  31, 189},
    {0,  47, 194},
    {0,  63, 198},
    {0,  79, 203},
    {0,  95, 208},
    {0, 111, 212},
    {0, 127, 217},
    {0, 143, 222},
    {0, 159, 226},
    {0, 175, 231},
    {0, 191, 236},
    {0, 207, 240},
    {0, 223, 245},
    {0, 239, 250},
    {0, 255, 255},
    {0, 245, 239},
    {0, 236, 223},
    {0, 227, 207},
    {0, 218, 191},
    {0, 209, 175},
    {0, 200, 159},
    {0, 191, 143},
    {0, 182, 127},
    {0, 173, 111},
    {0, 164,  95},
    {0, 155,  79},
    {0, 146,  63},
    {0, 137,  47},
    {0, 128,  31},
    {0, 119,  15},
    {0, 110,   0},
    {15, 118,   0},
    {30, 127,   0},
    {45, 135,   0},
    {60, 144,   0},
    {75, 152,   0},
    {90, 161,   0},
    {105, 169,  0},
    {120, 178,  0},
    {135, 186,  0},
    {150, 195,  0},
    {165, 203,  0},
    {180, 212,  0},
    {195, 220,  0},
    {210, 229,  0},
    {225, 237,  0},
    {240, 246,  0},
    {255, 255,  0},
    {251, 240,  0},
    {248, 225,  0},
    {245, 210,  0},
    {242, 195,  0},
    {238, 180,  0},
    {235, 165,  0},
    {232, 150,  0},
    {229, 135,  0},
    {225, 120,  0},
    {222, 105,  0},
    {219,  90,  0},
    {216,  75,  0},
    {212,  60,  0},
    {209,  45,  0},
    {206,  30,  0},
    {203,  15,  0},
    {200,   0,  0},
    {202,  11,  11},
    {205,  23,  23},
    {207,  34,  34},
    {210,  46,  46},
    {212,  57,  57},
    {215,  69,  69},
    {217,  81,  81},
    {220,  92,  92},
    {222, 104, 104},
    {225, 115, 115},
    {227, 127, 127},
    {230, 139, 139},
    {232, 150, 150},
    {235, 162, 162},
    {237, 173, 173},
    {240, 185, 185},
    {242, 197, 197},
    {245, 208, 208},
    {247, 220, 220},
    {250, 231, 231},
    {252, 243, 243},
    {252, 243, 243},
    {252, 243, 243},
    {252, 243, 243},
    {252, 243, 243},
    {252, 243, 243},
    {252, 243, 243},
    {252, 243, 243}
};

// 白热（来自高德红外）
const unsigned char white_heat[256][3] =
{
    {1, 1, 1},
    {1, 1, 1},
    {2, 2, 2},
    {3, 3, 3},
    {4, 4, 4},
    {5, 5, 5},
    {6, 6, 6},
    {8, 8, 8},
    {8, 8, 8},
    {9, 9, 9},
    {10, 10, 10},
    {11, 11, 11},
    {12, 12, 12},
    {13, 13, 13},
    {15, 15, 15},
    {15, 15, 15},
    {16, 16, 16},
    {17, 17, 17},
    {18, 18, 18},
    {19, 19, 19},
    {20, 20, 20},
    {22, 22, 22},
    {22, 22, 22},
    {23, 23, 23},
    {24, 24, 24},
    {25, 25, 25},
    {26, 26, 26},
    {27, 27, 27},
    {29, 29, 29},
    {29, 29, 29},
    {30, 30, 30},
    {31, 31, 31},
    {32, 32, 32},
    {33, 33, 33},
    {34, 34, 34},
    {36, 36, 36},
    {36, 36, 36},
    {37, 37, 37},
    {38, 38, 38},
    {39, 39, 39},
    {40, 40, 40},
    {41, 41, 41},
    {43, 43, 43},
    {43, 43, 43},
    {44, 44, 44},
    {45, 45, 45},
    {46, 46, 46},
    {47, 47, 47},
    {48, 48, 48},
    {50, 50, 50},
    {50, 50, 50},
    {51, 51, 51},
    {52, 52, 52},
    {53, 53, 53},
    {54, 54, 54},
    {55, 55, 55},
    {56, 56, 56},
    {56, 56, 56},
    {58, 58, 58},
    {59, 59, 59},
    {60, 60, 60},
    {61, 61, 61},
    {62, 62, 62},
    {63, 63, 63},
    {63, 63, 63},
    {65, 65, 65},
    {66, 66, 66},
    {67, 67, 67},
    {68, 68, 68},
    {69, 69, 69},
    {70, 70, 70},
    {70, 70, 70},
    {72, 72, 72},
    {73, 73, 73},
    {74, 74, 74},
    {75, 75, 75},
    {76, 76, 76},
    {77, 77, 77},
    {77, 77, 77},
    {79, 79, 79},
    {80, 80, 80},
    {81, 81, 81},
    {82, 82, 82},
    {83, 83, 83},
    {84, 84, 84},
    {84, 84, 84},
    {86, 86, 86},
    {87, 87, 87},
    {88, 88, 88},
    {89, 89, 89},
    {90, 90, 90},
    {91, 91, 91},
    {91, 91, 91},
    {93, 93, 93},
    {94, 94, 94},
    {95, 95, 95},
    {96, 96, 96},
    {97, 97, 97},
    {98, 98, 98},
    {98, 98, 98},
    {100, 100, 100},
    {101, 101, 101},
    {102, 102, 102},
    {103, 103, 103},
    {104, 104, 104},
    {105, 105, 105},
    {105, 105, 105},
    {107, 107, 107},
    {108, 108, 108},
    {109, 109, 109},
    {110, 110, 110},
    {111, 111, 111},
    {112, 112, 112},
    {112, 112, 112},
    {113, 113, 113},
    {115, 115, 115},
    {116, 116, 116},
    {117, 117, 117},
    {118, 118, 118},
    {119, 119, 119},
    {120, 120, 120},
    {120, 120, 120},
    {122, 122, 122},
    {123, 123, 123},
    {124, 124, 124},
    {125, 125, 125},
    {126, 126, 126},
    {127, 127, 127},
    {127, 127, 127},
    {129, 129, 129},
    {130, 130, 130},
    {131, 131, 131},
    {132, 132, 132},
    {133, 133, 133},
    {134, 134, 134},
    {134, 134, 134},
    {136, 136, 136},
    {137, 137, 137},
    {138, 138, 138},
    {139, 139, 139},
    {140, 140, 140},
    {141, 141, 141},
    {141, 141, 141},
    {143, 143, 143},
    {144, 144, 144},
    {145, 145, 145},
    {146, 146, 146},
    {147, 147, 147},
    {148, 148, 148},
    {148, 148, 148},
    {150, 150, 150},
    {151, 151, 151},
    {152, 152, 152},
    {153, 153, 153},
    {154, 154, 154},
    {155, 155, 155},
    {155, 155, 155},
    {157, 157, 157},
    {158, 158, 158},
    {159, 159, 159},
    {160, 160, 160},
    {161, 161, 161},
    {162, 162, 162},
    {162, 162, 162},
    {163, 163, 163},
    {165, 165, 165},
    {166, 166, 166},
    {167, 167, 167},
    {168, 168, 168},
    {169, 169, 169},
    {169, 169, 169},
    {170, 170, 170},
    {172, 172, 172},
    {173, 173, 173},
    {174, 174, 174},
    {175, 175, 175},
    {176, 176, 176},
    {176, 176, 176},
    {177, 177, 177},
    {179, 179, 179},
    {180, 180, 180},
    {181, 181, 181},
    {182, 182, 182},
    {183, 183, 183},
    {183, 183, 183},
    {184, 184, 184},
    {186, 186, 186},
    {187, 187, 187},
    {188, 188, 188},
    {189, 189, 189},
    {190, 190, 190},
    {190, 190, 190},
    {191, 191, 191},
    {193, 193, 193},
    {194, 194, 194},
    {195, 195, 195},
    {196, 196, 196},
    {197, 197, 197},
    {197, 197, 197},
    {198, 198, 198},
    {200, 200, 200},
    {201, 201, 201},
    {202, 202, 202},
    {203, 203, 203},
    {204, 204, 204},
    {204, 204, 204},
    {205, 205, 205},
    {207, 207, 207},
    {208, 208, 208},
    {209, 209, 209},
    {210, 210, 210},
    {211, 211, 211},
    {211, 211, 211},
    {212, 212, 212},
    {214, 214, 214},
    {215, 215, 215},
    {216, 216, 216},
    {217, 217, 217},
    {218, 218, 218},
    {218, 218, 218},
    {219, 219, 219},
    {220, 220, 220},
    {222, 222, 222},
    {223, 223, 223},
    {224, 224, 224},
    {225, 225, 225},
    {225, 225, 225},
    {226, 226, 226},
    {227, 227, 227},
    {229, 229, 229},
    {230, 230, 230},
    {231, 231, 231},
    {232, 232, 232},
    {233, 233, 233},
    {233, 233, 233},
    {234, 234, 234},
    {236, 236, 236},
    {237, 237, 237},
    {238, 238, 238},
    {239, 239, 239},
    {240, 240, 240},
    {240, 240, 240},
    {241, 241, 241},
    {243, 243, 243},
    {244, 244, 244},
    {245, 245, 245},
    {246, 246, 246},
    {247, 247, 247},
    {247, 247, 247},
    {248, 248, 248},
    {250, 250, 250},
    {251, 251, 251},
    {252, 252, 252},
    {253, 253, 253},
    {254, 254, 254},
    {254, 254, 254}
};

// 黑热（来自高德红外）
const unsigned char black_heat[256][3] =
{
    {254, 254, 254},
    {254, 254, 254},
    {253, 253, 253},
    {252, 252, 252},
    {251, 251, 251},
    {250, 250, 250},
    {248, 248, 248},
    {247, 247, 247},
    {247, 247, 247},
    {246, 246, 246},
    {245, 245, 245},
    {244, 244, 244},
    {243, 243, 243},
    {241, 241, 241},
    {240, 240, 240},
    {240, 240, 240},
    {239, 239, 239},
    {238, 238, 238},
    {237, 237, 237},
    {236, 236, 236},
    {234, 234, 234},
    {233, 233, 233},
    {233, 233, 233},
    {232, 232, 232},
    {231, 231, 231},
    {230, 230, 230},
    {229, 229, 229},
    {227, 227, 227},
    {226, 226, 226},
    {225, 225, 225},
    {225, 225, 225},
    {224, 224, 224},
    {223, 223, 223},
    {222, 222, 222},
    {220, 220, 220},
    {219, 219, 219},
    {218, 218, 218},
    {218, 218, 218},
    {217, 217, 217},
    {216, 216, 216},
    {215, 215, 215},
    {214, 214, 214},
    {212, 212, 212},
    {211, 211, 211},
    {211, 211, 211},
    {210, 210, 210},
    {209, 209, 209},
    {208, 208, 208},
    {207, 207, 207},
    {205, 205, 205},
    {204, 204, 204},
    {204, 204, 204},
    {203, 203, 203},
    {202, 202, 202},
    {201, 201, 201},
    {200, 200, 200},
    {198, 198, 198},
    {197, 197, 197},
    {197, 197, 197},
    {196, 196, 196},
    {195, 195, 195},
    {194, 194, 194},
    {193, 193, 193},
    {191, 191, 191},
    {190, 190, 190},
    {190, 190, 190},
    {189, 189, 189},
    {188, 188, 188},
    {187, 187, 187},
    {186, 186, 186},
    {184, 184, 184},
    {183, 183, 183},
    {183, 183, 183},
    {182, 182, 182},
    {181, 181, 181},
    {180, 180, 180},
    {179, 179, 179},
    {177, 177, 177},
    {176, 176, 176},
    {176, 176, 176},
    {175, 175, 175},
    {174, 174, 174},
    {173, 173, 173},
    {172, 172, 172},
    {170, 170, 170},
    {169, 169, 169},
    {169, 169, 169},
    {168, 168, 168},
    {167, 167, 167},
    {166, 166, 166},
    {165, 165, 165},
    {163, 163, 163},
    {162, 162, 162},
    {162, 162, 162},
    {161, 161, 161},
    {160, 160, 160},
    {159, 159, 159},
    {158, 158, 158},
    {157, 157, 157},
    {155, 155, 155},
    {155, 155, 155},
    {154, 154, 154},
    {153, 153, 153},
    {152, 152, 152},
    {151, 151, 151},
    {150, 150, 150},
    {148, 148, 148},
    {148, 148, 148},
    {147, 147, 147},
    {146, 146, 146},
    {145, 145, 145},
    {144, 144, 144},
    {143, 143, 143},
    {141, 141, 141},
    {141, 141, 141},
    {140, 140, 140},
    {139, 139, 139},
    {138, 138, 138},
    {137, 137, 137},
    {136, 136, 136},
    {134, 134, 134},
    {134, 134, 134},
    {133, 133, 133},
    {132, 132, 132},
    {131, 131, 131},
    {130, 130, 130},
    {129, 129, 129},
    {127, 127, 127},
    {127, 127, 127},
    {126, 126, 126},
    {125, 125, 125},
    {124, 124, 124},
    {123, 123, 123},
    {122, 122, 122},
    {120, 120, 120},
    {120, 120, 120},
    {119, 119, 119},
    {118, 118, 118},
    {117, 117, 117},
    {116, 116, 116},
    {115, 115, 115},
    {113, 113, 113},
    {112, 112, 112},
    {112, 112, 112},
    {111, 111, 111},
    {110, 110, 110},
    {109, 109, 109},
    {108, 108, 108},
    {107, 107, 107},
    {105, 105, 105},
    {105, 105, 105},
    {104, 104, 104},
    {103, 103, 103},
    {102, 102, 102},
    {101, 101, 101},
    {100, 100, 100},
    {98, 98, 98},
    {98, 98, 98},
    {97, 97, 97},
    {96, 96, 96},
    {95, 95, 95},
    {94, 94, 94},
    {93, 93, 93},
    {91, 91, 91},
    {91, 91, 91},
    {90, 90, 90},
    {89, 89, 89},
    {88, 88, 88},
    {87, 87, 87},
    {86, 86, 86},
    {84, 84, 84},
    {84, 84, 84},
    {83, 83, 83},
    {82, 82, 82},
    {81, 81, 81},
    {80, 80, 80},
    {79, 79, 79},
    {77, 77, 77},
    {77, 77, 77},
    {76, 76, 76},
    {75, 75, 75},
    {74, 74, 74},
    {73, 73, 73},
    {72, 72, 72},
    {70, 70, 70},
    {70, 70, 70},
    {69, 69, 69},
    {68, 68, 68},
    {67, 67, 67},
    {66, 66, 66},
    {65, 65, 65},
    {63, 63, 63},
    {63, 63, 63},
    {62, 62, 62},
    {61, 61, 61},
    {60, 60, 60},
    {59, 59, 59},
    {58, 58, 58},
    {56, 56, 56},
    {56, 56, 56},
    {55, 55, 55},
    {54, 54, 54},
    {53, 53, 53},
    {52, 52, 52},
    {51, 51, 51},
    {50, 50, 50},
    {50, 50, 50},
    {48, 48, 48},
    {47, 47, 47},
    {46, 46, 46},
    {45, 45, 45},
    {44, 44, 44},
    {43, 43, 43},
    {43, 43, 43},
    {41, 41, 41},
    {40, 40, 40},
    {39, 39, 39},
    {38, 38, 38},
    {37, 37, 37},
    {36, 36, 36},
    {36, 36, 36},
    {34, 34, 34},
    {33, 33, 33},
    {32, 32, 32},
    {31, 31, 31},
    {30, 30, 30},
    {29, 29, 29},
    {29, 29, 29},
    {27, 27, 27},
    {26, 26, 26},
    {25, 25, 25},
    {24, 24, 24},
    {23, 23, 23},
    {22, 22, 22},
    {22, 22, 22},
    {20, 20, 20},
    {19, 19, 19},
    {18, 18, 18},
    {17, 17, 17},
    {16, 16, 16},
    {15, 15, 15},
    {15, 15, 15},
    {13, 13, 13},
    {12, 12, 12},
    {11, 11, 11},
    {10, 10, 10},
    {9, 9, 9},
    {8, 8, 8},
    {8, 8, 8},
    {6, 6, 6},
    {5, 5, 5},
    {4, 4, 4},
    {3, 3, 3},
    {2, 2, 2},
    {1, 1, 1},
    {1, 1, 1}
};

// 热铁
const unsigned char hot_metal[256][3] =
{
    {0, 50, 231},
    {2, 52, 231},
    {3, 54, 232},
    {4, 55, 231},
    {5, 56, 233},
    {8, 59, 235},
    {7, 61, 234},
    {10, 63, 236},
    {11, 65, 236},
    {12, 66, 237},
    {13, 67, 238},
    {15, 70, 238},
    {15, 72, 239},
    {17, 75, 240},
    {18, 76, 241},
    {21, 78, 243},
    {22, 80, 242},
    {23, 81, 243},
    {24, 82, 245},
    {25, 86, 245},
    {26, 87, 246},
    {28, 89, 246},
    {30, 91, 248},
    {31, 92, 249},
    {32, 93, 248},
    {33, 96, 250},
    {34, 98, 251},
    {36, 100, 252},
    {37, 101, 253},
    {40, 104, 253},
    {40, 104, 253},
    {42, 107, 255},
    {43, 108, 255},
    {44, 110, 252},
    {45, 114, 252},
    {48, 115, 249},
    {49, 116, 248},
    {50, 118, 247},
    {51, 119, 247},
    {52, 121, 244},
    {52, 124, 243},
    {56, 126, 243},
    {55, 128, 240},
    {57, 128, 238},
    {59, 131, 239},
    {61, 133, 236},
    {60, 135, 235},
    {64, 137, 235},
    {65, 139, 232},
    {65, 139, 230},
    {68, 142, 231},
    {69, 144, 228},
    {70, 145, 227},
    {71, 147, 226},
    {72, 149, 223},
    {73, 152, 223},
    {76, 153, 223},
    {77, 155, 220},
    {78, 157, 219},
    {79, 158, 218},
    {80, 160, 215},
    {80, 163, 214},
    {84, 164, 215},
    {85, 166, 214},
    {86, 168, 211},
    {87, 170, 210},
    {88, 171, 209},
    {88, 174, 206},
    {92, 176, 207},
    {92, 176, 205},
    {93, 178, 202},
    {95, 181, 202},
    {97, 182, 201},
    {96, 185, 198},
    {99, 186, 197},
    {100, 189, 198},
    {101, 189, 194},
    {104, 192, 194},
    {105, 194, 193},
    {106, 196, 190},
    {107, 197, 189},
    {108, 199, 189},
    {109, 201, 186},
    {111, 202, 185},
    {113, 205, 185},
    {114, 207, 182},
    {115, 208, 181},
    {116, 210, 181},
    {116, 213, 178},
    {119, 213, 177},
    {120, 215, 176},
    {122, 218, 174},
    {123, 220, 173},
    {125, 221, 173},
    {124, 224, 170},
    {127, 225, 169},
    {128, 226, 168},
    {130, 229, 166},
    {132, 231, 166},
    {133, 232, 165},
    {134, 234, 162},
    {135, 236, 161},
    {136, 238, 160},
    {137, 239, 159},
    {140, 242, 158},
    {141, 244, 157},
    {142, 246, 154},
    {143, 247, 153},
    {144, 249, 152},
    {145, 250, 151},
    {147, 252, 148},
    {149, 255, 149},
    {149, 253, 145},
    {151, 253, 145},
    {151, 251, 144},
    {155, 249, 142},
    {156, 249, 142},
    {155, 247, 141},
    {158, 246, 139},
    {160, 246, 137},
    {160, 244, 136},
    {162, 243, 136},
    {162, 241, 133},
    {164, 241, 133},
    {165, 240, 131},
    {168, 237, 130},
    {169, 237, 130},
    {171, 236, 128},
    {171, 234, 126},
    {173, 234, 124},
    {173, 232, 123},
    {175, 231, 121},
    {175, 229, 120},
    {178, 228, 120},
    {180, 227, 118},
    {180, 227, 118},
    {182, 225, 115},
    {184, 224, 115},
    {184, 223, 112},
    {186, 222, 112},
    {187, 221, 110},
    {188, 219, 108},
    {191, 218, 108},
    {191, 216, 105},
    {193, 215, 105},
    {194, 215, 103},
    {195, 213, 102},
    {196, 212, 102},
    {197, 211, 99},
    {199, 210, 97},
    {200, 209, 97},
    {203, 208, 97},
    {204, 206, 94},
    {204, 204, 93},
    {206, 204, 91},
    {207, 203, 91},
    {208, 201, 87},
    {211, 200, 87},
    {211, 198, 86},
    {211, 198, 84},
    {215, 196, 84},
    {215, 195, 81},
    {217, 194, 81},
    {218, 193, 81},
    {219, 192, 78},
    {220, 191, 78},
    {222, 190, 76},
    {222, 189, 73},
    {224, 186, 72},
    {226, 185, 72},
    {228, 185, 69},
    {229, 184, 69},
    {230, 182, 66},
    {230, 180, 65},
    {232, 180, 65},
    {235, 178, 63},
    {235, 178, 63},
    {235, 177, 60},
    {238, 175, 60},
    {239, 174, 57},
    {240, 173, 57},
    {242, 172, 57},
    {242, 170, 54},
    {244, 170, 54},
    {246, 167, 50},
    {248, 166, 50},
    {249, 166, 48},
    {251, 165, 48},
    {251, 164, 45},
    {252, 162, 44},
    {253, 161, 44},
    {255, 160, 42},
    {255, 158, 41},
    {255, 155, 41},
    {255, 153, 39},
    {254, 150, 37},
    {255, 148, 36},
    {254, 145, 34},
    {254, 143, 32},
    {255, 141, 31},
    {255, 138, 32},
    {255, 135, 30},
    {255, 133, 29},
    {254, 130, 26},
    {255, 128, 25},
    {254, 125, 23},
    {254, 123, 22},
    {255, 121, 20},
    {254, 117, 20},
    {255, 115, 19},
    {255, 113, 18},
    {255, 109, 16},
    {255, 108, 14},
    {255, 104, 12},
    {254, 103, 11},
    {255, 99, 11},
    {254, 98, 9},
    {255, 95, 8},
    {255, 94, 7},
    {255, 90, 5},
    {255, 89, 4},
    {255, 84, 3},
    {255, 83, 0},
    {255, 79, 0},
    {254, 78, 1},
    {252, 75, 0},
    {249, 72, 0},
    {247, 69, 0},
    {246, 68, 1},
    {244, 65, 0},
    {241, 62, 0},
    {239, 60, 0},
    {238, 58, 0},
    {237, 55, 0},
    {233, 53, 0},
    {232, 49, 0},
    {230, 48, 0},
    {229, 45, 0},
    {227, 42, 0},
    {224, 40, 0},
    {220, 38, 0},
    {216, 35, 1},
    {212, 33, 1},
    {209, 30, 0},
    {205, 28, 0},
    {203, 25, 0},
    {199, 23, 0},
    {194, 20, 0},
    {190, 17, 0},
    {188, 15, 0},
    {184, 13, 0},
    {180, 10, 1},
    {175, 7, 0},
    {172, 5, 0},
    {168, 3, 0},
    {164, 0, 1}
};

//// 蓝红 （医疗）
//const unsigned char blue_red[256][3] =
//{
//    {1, 32, 65},
//    {0, 33, 70},
//    {0, 34, 75},
//    {0, 35, 82},
//    {0, 36, 89},
//    {0, 36, 95},
//    {1, 39, 101},
//    {0, 39, 108},
//    {0, 41, 113},
//    {0, 42, 119},
//    {1, 42, 126},
//    {0, 43, 131},
//    {0, 44, 138},
//    {0, 45, 143},
//    {0, 46, 151},
//    {0, 46, 156},
//    {0, 49, 162},
//    {0, 49, 169},
//    {0, 50, 175},
//    {0, 52, 180},
//    {0, 52, 187},
//    {0, 55, 193},
//    {1, 55, 201},
//    {1, 56, 206},
//    {0, 57, 213},
//    {0, 58, 218},
//    {0, 59, 223},
//    {1, 59, 231},
//    {0, 60, 236},
//    {0, 61, 243},
//    {0, 64, 249},
//    {0, 65, 255},
//    {1, 67, 250},
//    {1, 71, 243},
//    {0, 73, 237},
//    {0, 75, 230},
//    {0, 78, 225},
//    {0, 82, 218},
//    {1, 85, 211},
//    {0, 88, 206},
//    {0, 91, 199},
//    {1, 94, 194},
//    {0, 97, 187},
//    {0, 100, 183},
//    {1, 103, 176},
//    {1, 106, 171},
//    {0, 109, 163},
//    {0, 111, 158},
//    {0, 115, 151},
//    {0, 118, 144},
//    {1, 120, 139},
//    {0, 124, 132},
//    {0, 127, 127},
//    {1, 130, 120},
//    {0, 133, 116},
//    {0, 137, 109},
//    {1, 139, 104},
//    {1, 142, 97},
//    {0, 145, 89},
//    {0, 147, 84},
//    {0, 151, 77},
//    {0, 154, 72},
//    {1, 157, 65},
//    {0, 160, 60},
//    {4, 162, 57},
//    {9, 166, 56},
//    {11, 169, 54},
//    {15, 171, 53},
//    {20, 175, 52},
//    {24, 177, 48},
//    {27, 181, 47},
//    {32, 184, 44},
//    {36, 187, 42},
//    {41, 190, 42},
//    {45, 193, 38},
//    {47, 196, 38},
//    {52, 199, 36},
//    {56, 202, 34},
//    {60, 204, 30},
//    {65, 208, 30},
//    {69, 210, 28},
//    {73, 212, 27},
//    {75, 216, 23},
//    {80, 219, 22},
//    {84, 222, 21},
//    {88, 224, 19},
//    {92, 229, 16},
//    {96, 231, 15},
//    {100, 233, 13},
//    {105, 237, 10},
//    {109, 239, 9},
//    {111, 243, 7},
//    {116, 246, 6},
//    {120, 249, 3},
//    {125, 252, 2},
//    {129, 254, 0},
//    {132, 255, 0},
//    {136, 255, 1},
//    {139, 255, 0},
//    {143, 255, 1},
//    {147, 255, 0},
//    {152, 255, 0},
//    {155, 255, 0},
//    {159, 255, 0},
//    {163, 255, 1},
//    {168, 255, 0},
//    {172, 255, 0},
//    {175, 255, 0},
//    {179, 255, 0},
//    {183, 254, 1},
//    {188, 254, 0},
//    {191, 255, 0},
//    {195, 255, 0},
//    {199, 254, 0},
//    {204, 254, 0},
//    {206, 255, 0},
//    {211, 255, 0},
//    {215, 254, 0},
//    {219, 254, 0},
//    {222, 254, 0},
//    {227, 254, 0},
//    {231, 254, 0},
//    {235, 254, 0},
//    {238, 255, 0},
//    {242, 254, 0},
//    {247, 254, 0},
//    {251, 254, 0},
//    {254, 254, 0},
//    {255, 250, 0},
//    {254, 246, 0},
//    {255, 242, 0},
//    {254, 238, 0},
//    {255, 234, 0},
//    {254, 230, 0},
//    {255, 226, 0},
//    {254, 222, 0},
//    {255, 218, 0},
//    {255, 214, 0},
//    {255, 210, 0},
//    {255, 206, 0},
//    {255, 202, 0},
//    {255, 198, 0},
//    {254, 195, 0},
//    {255, 190, 0},
//    {254, 187, 0},
//    {255, 182, 0},
//    {254, 179, 0},
//    {255, 174, 0},
//    {254, 171, 0},
//    {255, 166, 1},
//    {255, 163, 0},
//    {255, 158, 0},
//    {255, 155, 0},
//    {255, 150, 0},
//    {255, 147, 0},
//    {255, 142, 1},
//    {255, 139, 0},
//    {254, 135, 0},
//    {255, 131, 0},
//    {254, 127, 0},
//    {255, 123, 0},
//    {255, 119, 0},
//    {254, 116, 0},
//    {255, 111, 0},
//    {254, 108, 0},
//    {255, 103, 0},
//    {254, 100, 0},
//    {255, 95, 0},
//    {254, 92, 0},
//    {255, 87, 0},
//    {254, 84, 0},
//    {255, 79, 0},
//    {255, 76, 0},
//    {255, 71, 0},
//    {255, 68, 0},
//    {255, 63, 1},
//    {255, 59, 0},
//    {254, 56, 0},
//    {255, 52, 0},
//    {254, 48, 0},
//    {255, 44, 0},
//    {254, 40, 0},
//    {255, 35, 0},
//    {254, 32, 0},
//    {255, 28, 0},
//    {255, 24, 0},
//    {255, 20, 0},
//    {255, 16, 0},
//    {255, 11, 1},
//    {255, 8, 0},
//    {255, 4, 0},
//    {255, 0, 0},
//    {255, 0, 9},
//    {255, 0, 16},
//    {254, 0, 24},
//    {255, 0, 31},
//    {255, 0, 39},
//    {253, 0, 47},
//    {254, 0, 54},
//    {254, 0, 63},
//    {255, 0, 70},
//    {254, 0, 78},
//    {253, 0, 88},
//    {254, 0, 95},
//    {254, 1, 102},
//    {254, 0, 110},
//    {253, 0, 117},
//    {255, 0, 127},
//    {254, 0, 134},
//    {253, 0, 142},
//    {252, 0, 149},
//    {253, 0, 158},
//    {253, 1, 165},
//    {253, 0, 172},
//    {252, 0, 181},
//    {254, 0, 188},
//    {253, 0, 197},
//    {253, 0, 205},
//    {253, 0, 212},
//    {252, 0, 222},
//    {251, 0, 228},
//    {252, 0, 235},
//    {251, 0, 244},
//    {253, 0, 251},
//    {252, 8, 252},
//    {252, 16, 253},
//    {252, 24, 251},
//    {252, 32, 252},
//    {252, 40, 252},
//    {253, 47, 253},
//    {253, 55, 252},
//    {253, 63, 252},
//    {253, 71, 253},
//    {253, 79, 252},
//    {254, 88, 253},
//    {254, 96, 254},
//    {254, 104, 253},
//    {253, 112, 253},
//    {253, 120, 254},
//    {255, 127, 254},
//    {255, 135, 255},
//    {255, 143, 254},
//    {254, 151, 254},
//    {254, 159, 255},
//    {254, 167, 253},
//    {254, 175, 254},
//    {254, 183, 255},
//    {254, 191, 253},
//    {254, 199, 254},
//    {253, 207, 255},
//    {255, 214, 255},
//    {255, 222, 254},
//    {255, 230, 254},
//    {254, 238, 255},
//    {254, 246, 254},
//    {254, 254, 254}
//};

// 熔岩
const unsigned char lava[256][3] =
{
    {0, 0, 132},
    {0, 1, 135},
    {0, 0, 139},
    {0, 0, 143},
    {1, 0, 148},
    {0, 0, 150},
    {1, 0, 155},
    {1, 0, 159},
    {0, 0, 163},
    {0, 0, 166},
    {0, 0, 171},
    {1, 0, 176},
    {0, 0, 180},
    {0, 0, 182},
    {1, 0, 187},
    {0, 0, 191},
    {0, 0, 196},
    {0, 0, 198},
    {0, 0, 202},
    {0, 0, 207},
    {0, 0, 211},
    {0, 0, 213},
    {0, 1, 219},
    {0, 0, 223},
    {1, 0, 228},
    {1, 0, 232},
    {0, 0, 234},
    {0, 0, 239},
    {0, 0, 243},
    {0, 1, 248},
    {0, 0, 250},
    {0, 0, 254},
    {1, 3, 255},
    {0, 7, 255},
    {0, 11, 255},
    {0, 15, 255},
    {0, 19, 255},
    {0, 23, 254},
    {0, 27, 255},
    {0, 31, 255},
    {0, 35, 255},
    {0, 39, 255},
    {0, 43, 255},
    {1, 47, 254},
    {0, 51, 254},
    {1, 54, 255},
    {0, 59, 255},
    {0, 62, 255},
    {0, 67, 255},
    {0, 71, 254},
    {0, 75, 254},
    {0, 78, 255},
    {0, 83, 255},
    {0, 86, 255},
    {0, 91, 255},
    {0, 95, 254},
    {0, 99, 254},
    {0, 103, 254},
    {0, 106, 255},
    {0, 110, 255},
    {0, 114, 255},
    {0, 119, 254},
    {0, 122, 254},
    {0, 127, 254},
    {0, 130, 255},
    {0, 134, 255},
    {0, 138, 255},
    {0, 143, 253},
    {0, 146, 254},
    {0, 151, 254},
    {1, 155, 255},
    {0, 160, 255},
    {1, 163, 255},
    {0, 168, 254},
    {1, 171, 255},
    {1, 175, 255},
    {0, 179, 255},
    {1, 183, 255},
    {0, 187, 255},
    {1, 191, 254},
    {0, 195, 255},
    {1, 199, 255},
    {0, 203, 255},
    {1, 207, 255},
    {0, 211, 255},
    {1, 215, 254},
    {0, 219, 254},
    {0, 223, 255},
    {0, 227, 255},
    {0, 231, 255},
    {1, 234, 255},
    {0, 239, 254},
    {1, 243, 254},
    {0, 247, 255},
    {1, 250, 255},
    {0, 255, 255},
    {3, 254, 251},
    {7, 255, 246},
    {12, 255, 243},
    {15, 254, 239},
    {19, 255, 235},
    {23, 254, 231},
    {27, 254, 228},
    {31, 255, 223},
    {34, 254, 219},
    {39, 255, 214},
    {43, 255, 211},
    {46, 254, 207},
    {51, 255, 202},
    {55, 255, 199},
    {58, 255, 195},
    {63, 255, 190},
    {66, 254, 186},
    {72, 254, 183},
    {76, 255, 178},
    {79, 254, 174},
    {84, 254, 172},
    {88, 255, 167},
    {91, 254, 163},
    {96, 254, 160},
    {100, 255, 155},
    {103, 254, 151},
    {107, 255, 146},
    {111, 254, 142},
    {115, 254, 139},
    {119, 255, 134},
    {123, 254, 130},
    {127, 255, 127},
    {131, 255, 122},
    {134, 254, 118},
    {139, 255, 115},
    {143, 255, 111},
    {146, 255, 107},
    {151, 255, 104},
    {154, 255, 98},
    {158, 255, 95},
    {163, 255, 92},
    {167, 254, 86},
    {172, 254, 83},
    {176, 255, 80},
    {179, 254, 74},
    {184, 254, 71},
    {188, 255, 66},
    {191, 254, 62},
    {196, 255, 59},
    {199, 254, 53},
    {203, 254, 50},
    {207, 255, 48},
    {211, 254, 42},
    {215, 255, 39},
    {219, 255, 36},
    {223, 255, 30},
    {227, 255, 27},
    {231, 255, 24},
    {234, 255, 18},
    {239, 255, 15},
    {242, 254, 11},
    {246, 255, 6},
    {251, 255, 3},
    {254, 254, 0},
    {255, 250, 0},
    {254, 246, 0},
    {255, 242, 0},
    {254, 238, 0},
    {255, 234, 0},
    {254, 230, 0},
    {255, 226, 0},
    {254, 222, 0},
    {255, 218, 0},
    {255, 214, 0},
    {255, 210, 0},
    {255, 206, 0},
    {255, 202, 0},
    {255, 198, 0},
    {254, 195, 0},
    {255, 190, 0},
    {254, 187, 0},
    {255, 182, 0},
    {254, 179, 0},
    {255, 174, 0},
    {254, 171, 0},
    {255, 166, 1},
    {255, 163, 0},
    {255, 158, 0},
    {255, 155, 0},
    {255, 150, 0},
    {255, 147, 0},
    {255, 142, 1},
    {255, 139, 0},
    {254, 135, 0},
    {255, 131, 0},
    {254, 127, 0},
    {255, 123, 0},
    {255, 119, 1},
    {255, 115, 1},
    {255, 111, 0},
    {255, 107, 0},
    {255, 103, 0},
    {255, 99, 0},
    {255, 95, 0},
    {255, 91, 1},
    {255, 87, 0},
    {255, 83, 0},
    {255, 79, 0},
    {255, 75, 0},
    {255, 71, 0},
    {255, 67, 1},
    {255, 63, 1},
    {255, 59, 0},
    {255, 55, 0},
    {255, 52, 0},
    {255, 47, 0},
    {255, 43, 1},
    {255, 39, 1},
    {255, 35, 0},
    {255, 31, 0},
    {255, 28, 0},
    {255, 23, 0},
    {255, 19, 1},
    {255, 15, 1},
    {255, 11, 1},
    {255, 8, 0},
    {255, 4, 0},
    {255, 0, 0},
    {251, 0, 0},
    {248, 0, 0},
    {244, 0, 0},
    {239, 0, 1},
    {235, 0, 0},
    {232, 0, 1},
    {228, 0, 0},
    {223, 0, 0},
    {219, 1, 0},
    {215, 1, 0},
    {212, 0, 1},
    {208, 0, 0},
    {203, 0, 1},
    {199, 1, 0},
    {196, 0, 0},
    {192, 0, 1},
    {187, 1, 0},
    {183, 1, 1},
    {179, 0, 0},
    {175, 0, 0},
    {170, 0, 0},
    {166, 0, 0},
    {163, 0, 0},
    {159, 0, 0},
    {155, 0, 0},
    {150, 0, 0},
    {147, 0, 0},
    {143, 0, 0},
    {139, 0, 0},
    {134, 0, 0},
    {132, 0, 1},
    {127, 0, 0}
};

#endif // INFRAREDDATADEFINE
