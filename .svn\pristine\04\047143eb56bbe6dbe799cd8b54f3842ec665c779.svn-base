#ifndef TIME_CALIBRATION_CONTROLLER_H
#define TIME_CALIBRATION_CONTROLLER_H

#include <QObject>
#include "httpweb/httprequesthandler.h"

using namespace stefanfrings;

class TimeCalibrationController : public HttpRequestHandler
{
    Q_OBJECT
public:
    explicit TimeCalibrationController(QObject *parent = 0);
    ~TimeCalibrationController();

    /*****************************************
     * 功能：处理http请求函数，并作出响应
     * 输入参数：
     *      request：请求
     * 输出参数：
     *      response：响应
     * *****************************************/
    void service(HttpRequest &request, HttpResponse &response);

};

#endif // TIME_CALIBRATION_CONTROLLER_H
