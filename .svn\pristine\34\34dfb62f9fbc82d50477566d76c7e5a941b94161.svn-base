/*
* Copyright (c) 2017.03，南京华乘电气科技有限公司
* All rights reserved.
*
* tevampview.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年03月14日
* 摘要：TEVAmpView模块接口定义

* 当前版本：1.0
*/

#ifndef TEVAMPVIEW_H
#define TEVAMPVIEW_H

#include "tev/tevdefine.h"
#include "tev/TevAmpService.h"
#include "tevampviewbase.h"
#include "config/ConfigManager.h"
#include "widgets/histogram/HistogramChart.h"

class TEVAmpView : public TEVAmpViewBase
{
    Q_OBJECT
public:
    /*************************************************
    函数名： TEVAmpView(const QString &strTitle, QWidget *parent = 0)
    输入参数： strTitle：标题
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit TEVAmpView(const QString &strTitle, QWidget *parent = 0);

    /*************************************************
    函数名： ~TEVAmpView()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~TEVAmpView();

protected:
    /*************************************************
    函数名： onSKeyPressed()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应S键事件
    *************************************************************/
    void onSKeyPressed();

    /*************************************************
     * 功能：诊断数据
     * 输入参数：
     *      bSave：是否为保存操作的逻辑，缺省为false
     * ***********************************************/
    virtual void diagDataInfo(bool bSave = false);

    /****************************************************
     * 功能：保存操作
     * **************************************************/
    void pressSaveData();

protected slots:
    /*************************************************
    函数名： onButtonValueChanged(int id, int iValue)
    输入参数： id：按钮ID
              iValue：按钮值
    输出参数： NULL
    返回值： NULL
    功能： 响应按钮值变化事件
    *************************************************************/
    void onButtonValueChanged(int id, int iValue);

    /*************************************************
    函数名： onCommandButtonPressed(int id)
    输入参数： id：按钮ID
    输出参数： NULL
    返回值： NULL
    功能： 响应命令按钮按下事件
    *************************************************************/
    void onCommandButtonPressed(int id);

private slots:
    /*************************************************
    函数名： onDataRead(TEV::AmplitudeData data)
    输入参数： data：幅值数据
    输出参数： NULL
    返回值： NULL
    功能： 响应读取的数据
    *************************************************************/
    void onDataRead(TEV::AmplitudeData data,MultiServiceNS::USERID userId);

private:
    /*************************************************
    函数名： initDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 初始化数据
    *************************************************************/
    void initDatas();

    HistogramChart *createChart(QWidget *parent);

    /*************************************************
    函数名： setChartDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置图谱数据
    *************************************************************/
    void setChartDatas();

    /*************************************************
    函数名： setWorksets()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置工作参数
    *************************************************************/
    void setWorksets();

    /*************************************************
    函数名： saveConfig()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 保存配置信息
    *************************************************************/
    void saveConfig();

    /*************************************************
    函数名： resetAlarmScope()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 重置报警值范围
    *************************************************************/
    void resetAlarmScope();

    /*************************************************
    函数名： setButtonDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置按钮数据
    *************************************************************/
    void setButtonDatas();

    /*************************************************
    函数名： loadData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 载入数据
    *************************************************************/
    void loadData();

    /*************************************************
    函数名： deleteData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 删除数据
    *************************************************************/
    void deleteData();

    /*************************************************
    函数名： restoreDefault()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 恢复默认
    *************************************************************/
    void restoreDefault();

    void setPNGFileNamePath(QString &strPngFilePath, QString &strPngFile);

    /*************************************************
    输入参数：
        stationName：站名
        deviceName：设备名
    输出参数： NULL
    返回值： NULL
    功能：保存数据文件
    *************************************************************/
    QString saveDataToFile();

private:
    ConfigInstance *m_pConfig;
    HistogramChart *m_pChart;

    Module::SampleMode m_eSampleMode;
    UINT8 m_ucYellowAlert;
    UINT8 m_ucRedAlert;

    INT8 m_cAmpValue;
};

#endif // TEVAMPVIEW_H
