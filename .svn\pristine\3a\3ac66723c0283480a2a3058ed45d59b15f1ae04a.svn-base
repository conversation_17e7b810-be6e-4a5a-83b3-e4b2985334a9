﻿#include <QDebug>
#include "phasedata.h"

// 从原始数据构造, 主要用于给PhaseData使用,如果个数少于一个周期的数据则报错
PeriodData::PeriodData(const ValueType *pData, int size)
    : m_samples()
{
    m_samples.resize(size);
    for(int i = 0; i < size; ++i)
    {
        m_samples.data()[i] = pData[i];
    }
}

// 从原始数据构造, 主要用于给PhaseData使用,如果个数少于一个周期的数据则报错
PeriodData::PeriodData(const QVector<ValueType>& datas)
    : m_samples()
{
    m_samples = datas;
}

/************************************************
 * 函数名: PhaseData
 * 输入参数:
 *          iDataCountPerPeriod: 每周期数据点个数
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 构造函数
 ************************************************/
PhaseData::PhaseData(int iDataCountPerPeriod)
    : m_iDataCountPerPeriod(0)
{
    setDataCountPeriod(iDataCountPerPeriod);
}

/************************************************
 * 函数名: PhaseData
 * 输入参数:
 *          fData: 数据指针
 *          iSize: 数据个数
 *          iDataCountPerPeriod: 每周期数据点个数
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 构造函数
 ************************************************/
PhaseData::PhaseData(ValueType *fData, int iSize, int iDataCountPerPeriod)
    : m_iDataCountPerPeriod(iDataCountPerPeriod)
{
    PHASE_ASSERT(iDataCountPerPeriod > 0);

    int count = iSize / m_iDataCountPerPeriod;

    for(int i = 0; i < count; ++i)
    {
        m_periods.append(PeriodData(fData + i * m_iDataCountPerPeriod, m_iDataCountPerPeriod));
    }
}

/************************************************
 * 函数名: append
 * 输入参数:
 *          periodData: 一个周期的数据,如果为空则不会加入
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 添加新数据
 ************************************************/
void PhaseData::append(const PeriodData &periodData)
{
    if(!periodData.isEmpty())
    {
        PHASE_ASSERT(m_iDataCountPerPeriod == periodData.size());
    }
    else
    {
        qWarning("[PhaseData::append(const PeriodData &)] input period Data empty, cant not append.");
        return;
    }

    m_periods.append(periodData);
}

/************************************************
 * 函数名: append
 * 输入参数:
 *          rhs: 数据
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 添加新数据
 ************************************************/
void PhaseData::append(const PhaseData &rhs)
{ 
    if(!rhs.isEmpty())
    {
        PHASE_ASSERT(m_iDataCountPerPeriod == rhs.m_iDataCountPerPeriod);
    }
    else
    {
        qWarning("[PhaseData::append(const PhaseData &)] input phase Data empty, cant not append.");
        return;
    }

    m_periods.append(rhs.m_periods);
}

/************************************************
 * 函数名: setDataCountPeriod
 * 输入参数:
 *          iDataCountPerPeriod: 每周期数据点个数
 * 输出参数: NULL
 * 返回值: NULL
 * 功能: 设置每周期的数据点个数,
 *   ***注意*** 如果当前数据不为空且iDataCountPerPeriod不等于已有数据的每周期数据点个数,则会清空当前数据
 ************************************************/
void PhaseData::setDataCountPeriod(int iDataCountPerPeriod)
{
    PHASE_ASSERT(iDataCountPerPeriod > 0);
    if(m_iDataCountPerPeriod != iDataCountPerPeriod)
    {
        m_iDataCountPerPeriod = iDataCountPerPeriod;
        clear();
    }
}

PhaseData PhaseData::mid(int iPos, int iLength) const
{
    PhaseData ret(m_iDataCountPerPeriod);
    ret.m_periods = m_periods.mid(iPos, iLength);

    return ret;
}

PhaseData PhaseData::takeFront(int iLength)
{
    PhaseData ret(m_iDataCountPerPeriod);
    //qDebug() << "-----------------------------PhaseData::takeFront" << iLength << m_periods.size();
    for(int i = 0; i < iLength; ++i)
    {
        ret.m_periods.append(m_periods.takeFirst());
    }

    return ret;
}
