/*
* Copyright (c) 2017.02，南京华乘电气科技有限公司
* All rights reserved.
*
* pulsedataplaybackview.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年2月15日
*       新版本重构
* 摘要：CA Pulse回放界面定义

* 当前版本：1.0
*/

#ifndef PulseDataPlayBackView_H
#define PulseDataPlayBackView_H

#include "widgets/sampleChartView/SampleChartView.h"
#include "ca/CA.h"
#include "buttonBar/LabelButtonBar.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "PrpdFast.h"
#include "FastWavePDSS.h"
#include "ca/dataSave/AccessG100PulseDataSave.h"
#include "ca/pulse/FastCollection/FastWaveBase.h"

class PulseDataPlayBackView : public SampleChartView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        parent:父控件指针
    *************************************************************/
    explicit PulseDataPlayBackView( const QString& strTitle, QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~PulseDataPlayBackView( );


    /*************************************************
    函数名： setDatas
    输入参数: NULL
    输出参数： NULL
    返回值： NULL
    功能： 载入后，更新界面
    *************************************************************/
    void setDatas( CAPulseDataInfo *pstPlayBackData );

    /*************************************************
    函数名： setPRPDRangeMax
    输入参数:uiSampleRate---采样率，10k为单位
    输出参数： NULL
    返回值： NULL
    功能：
    *************************************************************/
    void setPRPDRangeMax(qint32 iRangeMax);

    void setDiagnosisYRange(float fRange);

    void setPDSSXScale( double min,double max,double step );

    void setPDSSYScale(double min,double max,double step);

signals:
    /****************************
     释放上按键按下信号
     ****************************/
    void sigMoveUp();

    /****************************
     释放下按键按下信号
     ****************************/
    void sigMoveDown();

protected:
    /*************************************************
    函数名： eventFilter
    输入参数:
        obj:事件所属对象
        event：事件指针
    输出参数： NULL
    返回值： 处理结果
    功能： 实现按Enter键进行确认选择的功能
    *************************************************************/
    bool eventFilter(QObject *obj, QEvent *event);

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

private:

    /*************************************************
    函数名： setRawDataForClusterAnalysis(CAPulseDataInfo *pstPlayBackData)
    输入参数: pstPlayBackData---数据文件数据
    输出参数：NULL
    返回值： NULL
    功能： 把数据文件数据转成聚类分析需要的数据结构
    *************************************************************/
    void setRawDataForClusterAnalysis(CAPulseDataInfo *pstPlayBackData);

    /*************************************************
    函数名： createPRPDChart
    输入参数: parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 创建PRPD图谱
    *************************************************************/
    PrpdFast *createPRPDChart(QWidget *parent);

    /*************************************************
    函数名： createPDSSChart
    输入参数: parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 创建PDSS图谱
    *************************************************************/
    FastWavePDSS *createPDSSChart(QWidget *parent);


    /*************************************************
    函数名： createChart
    输入参数: parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 创建图谱
    *************************************************************/
    ChartWidget * createChart(QWidget *parent);

    /*************************************************
    功能： 初始化数据成员
    *************************************************************/
    void initDatas(void);


    /*************************************************
    功能： 设置按钮栏数据
    *************************************************************/
    void setButtonBarDatas(void);

    /*************************************************
    函数名： setPDSSChartScale
    输入参数:uiSampleRate---采样率，10k为单位
    输出参数： NULL
    返回值： NULL
    功能： 设置pdss图谱的scale
    *************************************************************/
    void setPDSSChartScale(UINT32 uiSampleRate);

    /*************************************************
    函数名： setPDSSSampleData
    输入参数: pstPlayBackData---从数据文件里读取的数据
    输出参数： NULL
    返回值： NULL
    功能： 设置pdss图谱数据
    *************************************************************/
    void setPDSSSampleData(CAPulseDataInfo *pstPlayBackData);


    /*************************************************
    函数名： setPRPDSampleData
    输入参数: pstPlayBackData---从数据文件里读取的数据
    输出参数： NULL
    返回值： NULL
    功能： 设置PRPD图谱数据
    *************************************************************/
    void setPRPDSampleData(CAPulseDataInfo *pstPlayBackData);


    /*************************************************
    功能： 聚类分析
    *************************************************************/
    void clusterAnalysis();

    /*************************************************
    函数名： sampleRate2Enum( UINT32 uiSampleRate )
    输入参数:
        uiSampleRate -- 采样率， 以10k为单位
    输出参数：NULL
    返回值： NULL
    功能： 采样率数值转为枚举
    *************************************************************/
    CA::SampleRate sampleRate2Enum( UINT32 uiSampleRate );

private:
    //界面控件
    PrpdFast    *m_pPrpdFast;
    FastWavePDSS *m_pFastWavePDSS;

    QList< CA::PulseData > m_lRawData;//聚类分析用的数据
    CA::SampleRate m_eSampleRate; //采样率

    //图谱数据
    QVector< double > m_XDataPDSS;
    QVector< double > m_YDataPDSS;
    QVector< UINT16 > m_IndexPDSS;
    QVector< PrpdData > m_prpdVector;

    //界面参数
    INT32 m_iPhaseAlias; //相位偏移

    double m_YScaleMax;
    qint32 m_iRangeMax;
    float m_fDiagnosisYRange;
    PushButtonBar* m_pButtonBar;

    enum
    {
        TEXT_FONT_SIZE = 9,   // u-t,pdss图谱的字号
        STATUS_LABEL_HEIGHT = 20, // 状态显示标签高度
        CHART_HEIGHT = 210,//图谱高度
        CHART_WIDTH = 440,//图谱宽度
    };
};

#endif // PulseDataPlayBackView_H
