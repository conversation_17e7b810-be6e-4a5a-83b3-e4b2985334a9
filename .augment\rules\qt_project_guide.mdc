---
description:
globs:
alwaysApply: false
---
# Qt项目开发指南

## 项目结构
- `src/` - 源代码文件目录
- `include/` - 头文件目录
- `resources/` - 资源文件（图像、QML、UI文件等）
- `tests/` - 单元测试和集成测试
- `docs/` - 项目文档
- `libs/` - 第三方库

## 编码规范
- 使用C++11或更高版本标准
- 类名使用PascalCase（如`MainWindow`）
- 变量和函数名使用camelCase（如`buttonClick`）
- 文件名使用snake_case（如`main_window.cpp`）
- 缩进使用4个空格
- 使用智能指针代替原始指针
- 使用`#pragma once`或头文件保护

## Qt特性
- 正确使用信号和槽机制
- 利用Qt的元对象系统（使用Q_OBJECT宏）
- 使用Qt的容器类型代替STL（如`QVector`代替`std::vector`）
- 使用`QString`处理所有字符串
- 适当使用QObject父子关系管理内存

## 代码示例
```cpp
// 头文件示例
#pragma once

#include <QMainWindow>
#include <QLabel>
#include <QTimer>
#include <memory>

class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow() override;

private slots:
    void onTimerTimeout();

private:
    QLabel *m_statusLabel;
    std::unique_ptr<QTimer> m_timer;
};
```

## 自动测试
- 使用Qt Test框架进行单元测试
- 每个类应有对应的测试类
- 利用信号槽测试机制验证事件处理

## 性能考虑
- 避免在UI线程中进行耗时操作
- 使用Qt并发模块处理后台任务
- 注意QML和C++之间的性能桥接

## 跨平台兼容性
- 避免平台特定代码，使用Qt抽象
- 使用Qt提供的路径和文件处理函数
- 考虑不同屏幕尺寸和分辨率
