#include "filtersettingdlg.h"
#include <QKeyEvent>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QBitmap>
#include <QPainter>
#include "appfontmanager/appfontmanager.h"
#include "softkeyboard.h"

namespace
{
    const int g_iWidth = 400;
    const int g_iHeight = 260;
    const int g_iButtonWidth = 130;              // 按键的宽度
    const int g_iButtonSpacing = 6;          // 按键的间距
    const int g_iButtonHeight = 60;           // 按键的高度
}

const QString BUTTON_STYLE = "QPushButton:focus{border-width:2px;border-color:rgb(54, 133, 203);border-radius:10px;border-style: inset; background:rgb( 115,198,242 );}"
                             "QPushButton{border-width:2px;border-color:rgb(54, 133, 203);border-radius:10px;border-style: outset;}";

FilterSettingDlg::FilterSettingDlg(const QString& qstrKeyword, QWidget* pParent)
    : QDialog(pParent)
{
    // 设置整体显示属性
    setWindowFlags(Qt::FramelessWindowHint | Qt::Dialog);
    setWindowModality(Qt::ApplicationModal);
    //setAttribute(Qt::WA_DeleteOnClose);
    setFixedSize(g_iWidth, g_iHeight);

    QFont fontButton(AppFontManager::instance()->getAppCurFontFamily());
    fontButton.setPixelSize(24);

    m_pBtnOk = new QPushButton();
    m_pBtnOk->setText(QObject::trUtf8("OK"));
    m_pBtnOk->setStyleSheet(BUTTON_STYLE);
    m_pBtnOk->setFixedSize(g_iButtonWidth, g_iButtonHeight);
    m_pBtnOk->setFont(fontButton);

    m_pBtnCancel = new QPushButton();
    m_pBtnCancel->setText(QObject::trUtf8("Cancel"));
    m_pBtnCancel->setStyleSheet(BUTTON_STYLE);
    m_pBtnCancel->setFixedSize(g_iButtonWidth, g_iButtonHeight);
    m_pBtnCancel->setFont(fontButton);

    connect(m_pBtnOk, SIGNAL(clicked()), this, SLOT(onBtnOKPressed()));
    connect(m_pBtnCancel, SIGNAL(clicked()), this, SLOT(reject()));

    QLabel* pLabel = new QLabel(tr("Keyword"), this);
    pLabel->setFont(fontButton);
    m_pLineEdit = new QLineEdit(qstrKeyword, this);
    m_pLineEdit->setFixedHeight(100);
    m_pLineEdit->setFont(fontButton);

    QHBoxLayout* pTextLayout = new QHBoxLayout();
    pTextLayout->addWidget(pLabel);
    pTextLayout->addWidget(m_pLineEdit);

    QHBoxLayout* pBtnLayout = new QHBoxLayout();
    pBtnLayout->addWidget(m_pBtnOk);
    pBtnLayout->addSpacing(g_iButtonSpacing);
    pBtnLayout->addWidget(m_pBtnCancel);
    pBtnLayout->setAlignment(Qt::AlignRight);

    QVBoxLayout* pMainLayout = new QVBoxLayout();
    pMainLayout->addLayout(pTextLayout);
    pMainLayout->addLayout(pBtnLayout);

    //pMainLayout->setSpacing(0);
    pMainLayout->setMargin(0);

    setLayout(pMainLayout);

    SoftKeyBoard::setAttribute(KEYBOARD_MODALITY_PROPERTY, SoftKeyBoard::APPLICATION_MODAL);
}

FilterSettingDlg::~FilterSettingDlg()
{
    SoftKeyBoard::setAttribute(KEYBOARD_MODALITY_PROPERTY, SoftKeyBoard::NON_MODAL);
}

QString FilterSettingDlg::getKeyword()
{
    return m_pLineEdit->text();
}

void FilterSettingDlg::keyPressEvent(QKeyEvent* pEvent)
{
    switch(pEvent->key())
    {
    case Qt::Key_Escape://取消键
    {
        reject();
        break;
    }
    case Qt::Key_Enter:
    case Qt::Key_Return:
    {
        if(m_pBtnOk->hasFocus())
        {
            onBtnOKPressed();
        }
        else if(m_pBtnCancel->hasFocus())
        {
            reject();
        }
        else
        {
            QDialog::keyPressEvent(pEvent);
        }
        break;
    }
    default:
    {
        QDialog::keyPressEvent(pEvent);
        break;
    }
    }
}

void FilterSettingDlg::showEvent(QShowEvent* pEvent)
{
    m_pLineEdit->setFocus();
    QDialog::showEvent(pEvent);
    //this->move((Window::WIDTH - this->width()) / 2, (Window::HEIGHT - this->height()) / 2);
}

void FilterSettingDlg::paintEvent(QPaintEvent *pEvent)
{
    Q_UNUSED(pEvent);

    QBitmap bitmap(this->size());
    QPainter painter2(&bitmap);
    painter2.setPen(Qt::NoPen);
    painter2.fillRect(bitmap.rect(), Qt::white);
    painter2.setBrush(QColor(0, 0, 0));
    painter2.drawRoundedRect(rect(), 10, 10);
    setMask(bitmap);           // 绘制圆角外框

    QPainter painter(this);
    painter.setPen(Qt::NoPen);
    QLinearGradient linearGradient( QPoint(0,0),QPoint( width(),height() ) );
    linearGradient.setColorAt( 0,QColor( 130,181,238 ));
    linearGradient.setColorAt( 1,QColor( 189,217,247 )); // 设置usb设置整个界面从左至右渐变
    painter.setBrush( QBrush( linearGradient ) );
    painter.drawRect( rect() );   // 绘制整个提示框区域背景色渐变
}

void FilterSettingDlg::onBtnOKPressed()
{
    accept();
}
