#ifndef DeleteDataView_H
#define DeleteDataView_H

#include <QWidget>
#include <QVBoxLayout>
#include "filelistView/FileListView.h"
#include "titlebar/TitleBar.h"
#include "DeleteFileWidget.h"
#include "Widget.h"
#include "widgetglobal.h"

/****************************
函数名： DeleteDataView;
输入参数:NULL
输出参数：NULL
返回值：NULL
功能： 构造函数
*****************************/
class WIDGET_EXPORT DeleteDataView : public Dialog
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    输入参数:
        path -- 路径
        title -- 标题
        nameFilters -- 文件过滤符（空表示不过滤）
        parent -- 父窗体
    *****************************/
     DeleteDataView( const QString& path,
                     const QString& title,
                     const QStringList& nameFilters,
                     QWidget* parent = 0 );

     /****************************
     功能： 设置关联文件类型
     比如：删除aaa.t01,则同步删除aaa.dat，aaa.dat是aaa.t01的关联文件
     输入参数:
         strSuffixMain -- 主类型
         listRelatedSuffix -- 关联类型列表
     *****************************/
     void setRelatedSuffix( const QString& strSuffixMain, const QStringList& listRelatedSuffix );
private:
     DeleteFileWidget *m_pDeleteFileWidget;   // 文件选择窗口
     TitleBar *m_pTitleBar;           // 标题栏
     QStringList m_listNameFilters;//文件过滤器
};

#endif // DeleteDataView_H
