﻿#include <QBoxLayout>

#include "pdasiftbutton.h"


const QString BUTTON_FRAME_FOCUS_IN_STYLE = "PDASiftButton{border-style:solid;border-width:3px;border-radius:5px;border-color: rgb(207,207,207);padding:5px 3px;}"
                                            "PDASiftButton:focus{border-radius: 5px;border-width:3px; border-color: rgb( 115,198,242 );border-style: solid;}";

PDASiftButton::PDASiftButton(QWidget *parent) : QLabel(parent)
{

}

/************************************************
 * 函数名   : 鼠标事件
 * 输入参数 :
 *      event -- 事件
 ************************************************/
void PDASiftButton::mouseReleaseEvent(QMouseEvent *event)
{
    if(Qt::LeftButton == event->button())
    {
        emit clicked();
    }
    else
    {
        QLabel::mouseReleaseEvent( event );
    }
}
/************************************************
 * 函数名   : 键盘事件
 * 输入参数 :
 *      event -- 事件
 ************************************************/
 void PDASiftButton::keyPressEvent( QKeyEvent *event )
 {

    if((( Qt::Key_Return == event->key() ) || ( Qt::Key_Enter == event->key() )))
    {
        emit clicked();
    }
    else
    {
        QFrame::keyPressEvent( event );
    }

 }
 /*************************************************
    功能： 处理show事件
  *************************************************************/
 void PDASiftButton::showEvent(QShowEvent *event)
 {
     Q_UNUSED(event)
     setFocusPolicy( Qt::StrongFocus );          // 设置焦点策略
     this->setStyleSheet(BUTTON_FRAME_FOCUS_IN_STYLE);
 }

