#include <QHBoxLayout>
#include <QSpacerItem>
#include <QTextDocument>
#include <QTextCursor>
#include <QGroupBox>
#include "window/Window.h"
#include "currentdetectionviewdefine.h"
#include "currentdetectionchart.h"
#include "appfontmanager/appfontmanager.h"
#include "ammeterwidget.h"


#define DEFAULT_HEIGHT 500
#define INVALID_TIMER -1
#define DEFAULT_TIMER_INTERVAL 1000
#define RUN_FLAG_SIZE 30
#define ITEM_SPACING 15     //间距

const int g_iDataCount = 10; // 计算十组数中的最大值
const int g_iDashboardHeight = 420; // 仪表盘高度

const int g_iCurrentRangeGear10A = 10;
const int g_iCurrentRangeGear500A = 500;
const int g_iCurrentRangeGear5000A = 5000;


CurrentDetectionChart::CurrentDetectionChart(QWidget *parent)
    : ChartWidget(parent),
      m_fMaxCurrent(0.0),
      m_eSampleMode(Module::SAMPLEMODE_CONTINUOUS),
      m_iTimer(INVALID_TIMER)
{
    setFixedSize(Window::WIDTH, DEFAULT_HEIGHT);

    createLabels();
    layoutContents();

//    QTimer* timer = new QTimer();
//    connect(timer, SIGNAL(timeout()), this, SLOT(onTimeOut()));
//    timer->start(500);

    m_pGaugeArc->setPrecision(1);
    m_pGaugeArc->setCurrentUnit(AmmeterWidget::CurrentUnit_A);
}

CurrentDetectionChart::~CurrentDetectionChart()
{
}

/*************************************************
函数名： addSample(const CurrentDetection::CurrentDetectionData &data)
输入参数： data：电流数据
输出参数： NULL
返回值： NULL
功能： 添加新数据
*************************************************************/
void CurrentDetectionChart::addSample(float fCurrentValue)
{
    // 对超量程数据进行处理
    if(fCurrentValue > m_fRangeMax)
    {
        fCurrentValue = m_fRangeMax;
    }
    else if(fCurrentValue < m_fRangeMin)
    {
        fCurrentValue = m_fRangeMin;
    }

    if(Module::SAMPLEMODE_CONTINUOUS == m_eSampleMode)
    {
        setRunningStatus(true);
    }

    while (m_currentDatas.size() >= g_iDataCount)
    {
        m_currentDatas.pop_front();
    }
    m_currentDatas.append(fCurrentValue);

    // 计算最大值
    m_fMaxCurrent = m_currentDatas[0];
    for (int i = 1; i < m_currentDatas.size(); ++i)
    {
        if (m_fMaxCurrent < m_currentDatas[i])
        {
            m_fMaxCurrent = m_currentDatas[i];
        }
    }

    updateCurrentChart(fCurrentValue, m_fMaxCurrent);
}

/*************************************************
函数名： setPlayBackData(const CurrentDetection::CurrentDetectionChartData &data)
输入参数： data：电流数据
输出参数： NULL
返回值： NULL
功能： 设置回放数据
*************************************************************/
void CurrentDetectionChart::setPlayBackData(const float& fCurrentValue)
{
    // 通过电流值来确定量程范围
    if (fCurrentValue > g_iCurrentRangeGear500A)
    {
        setRange(0, g_iCurrentRangeGear5000A);
    }
    else if (fCurrentValue > g_iCurrentRangeGear10A)
    {
        setRange(0, g_iCurrentRangeGear500A);
    }
    else
    {
        setRange(0, g_iCurrentRangeGear10A);
    }

    m_currentDatas.clear();
    setSampleMode(Module::SAMPLEMODE_SINGLE);
    addSample(fCurrentValue);
}

/************************************************
 * 函数名    :setSignalStatus
 * 输入参数  ：信号状态
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：设置信号状态
 ************************************************/
void CurrentDetectionChart::setSignalStatus(const Module::SignalState eSignalState)
{
    QString qstrSignalText = "";
    if (Module::SIGNAL_STATE_NONE == eSignalState)
    {
        qstrSignalText = QString(QObject::tr("No signal"));
        setRunningStatus(false); // 无信号，运行光标不在闪烁
    }
    else // 连续模式下根据采集模式更新运行标识
    {
        if (Module::SAMPLEMODE_CONTINUOUS == m_eSampleMode)
        {
            setRunningStatus(true);
        }
        else
        {
            setRunningStatus(false);
        }
    }

    m_pSignalStatusLabel->setText(qstrSignalText);
}

/*************************************************
函数名： setRunningStatus(const bool bRunning)
输入参数： bRunning：运行标志状态
输出参数： NULL
返回值： NULL
功能： 设置运行标志状态
*************************************************************/
void CurrentDetectionChart::setRunningStatus(const bool bRunning)
{
    if (bRunning)
    {
        if (INVALID_TIMER == m_iTimer)
        {
            m_iTimer = startTimer(DEFAULT_TIMER_INTERVAL);
        }
    }
    else
    {
        if (m_iTimer != INVALID_TIMER)
        {
            killTimer(m_iTimer);
            m_iTimer = INVALID_TIMER;
        }

        m_pRunningFlagLabel->setPixmap(QPixmap(""));
    }

}

/************************************************
 * 函数名    :setSampleMode
 * 输入参数  ：eSampleMode -- 采集模式
 * 功能     ：设置采集模式
 ************************************************/
void CurrentDetectionChart::setSampleMode(Module::SampleMode eSampleMode)
{
    m_eSampleMode = eSampleMode;
    if(Module::SAMPLEMODE_SINGLE == m_eSampleMode)
    {
        setRunningStatus(false); // 单次模式运行光标不闪烁
    }
    else
    {
        setRunningStatus(true);
    }
}

/*************************************************************
 * 功能：设置电流测试类型
 * 输入参数：
 *         eCurrentRangeGear：电流单位
 * ************************************************************/
void CurrentDetectionChart::setCurrentRangeGear(CurrentDetection::CurrentRangeGear eCurrentRangeGear)
{
    if (eCurrentRangeGear != m_eCurrentRangeGear)
    {
        m_eCurrentRangeGear = eCurrentRangeGear;
        updateRange();
    }
}

/*************************************************************
 * 功能：设置范围
 * 输入参数：
 *         fRangeMin：范围最大值
 *         fRangeMax：范围最小值
 * ************************************************************/
void CurrentDetectionChart::setRange(float fRangeMin, float fRangeMax)
{
    float fRangeMinTmp = qMin(fRangeMin, fRangeMax);
    float fRangeMaxTmp = qMax(fRangeMin, fRangeMax);

    if (!qFuzzyCompare(fRangeMinTmp, m_fRangeMin) || !qFuzzyCompare(fRangeMaxTmp, m_fRangeMax))
    {
        m_fRangeMin = fRangeMinTmp;
        m_fRangeMax = fRangeMaxTmp;
        m_pGaugeArc->setRange(m_fRangeMin, m_fRangeMax);
    }
}

/************************************************
 * 功能     ：获取图谱的最大值
 * 返回值   ：图谱的最大值
 ************************************************/
float CurrentDetectionChart::getMaxData()
{
    return m_fMaxCurrent;
}

/*************************************************************
 * 功能：清空图谱
 * ************************************************************/
void CurrentDetectionChart::clear()
{
    m_currentDatas.clear();
    m_fMaxCurrent = m_fRangeMin;
    m_pMaxValueLabel->clear();
    m_pGaugeArc->setValue(m_fRangeMin);
}

/*************************************************
函数名： timerEvent(QTimerEvent *event)
输入参数： event：定时器事件
输出参数： NULL
返回值： NULL
功能： 定时器事件处理
*************************************************************/
void CurrentDetectionChart::timerEvent(QTimerEvent *event)
{
    if (event->timerId() == m_iTimer)
    {
        const QPixmap* pixmap = m_pRunningFlagLabel->pixmap();
        if (!pixmap || pixmap->isNull())
        {
            m_pRunningFlagLabel->setPixmap(QPixmap(":/images/Start_24px.png"));
        }
        else
        {
            m_pRunningFlagLabel->setPixmap(QPixmap(""));
        }
    }
}

/*************************************************
函数名： createLabels()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 创建所有Label
*************************************************************/
void CurrentDetectionChart::createLabels()
{
    m_pSignalStatusLabel = new QLabel(this);
    m_pSignalStatusLabel->setStyleSheet("color:rgb(255,0,0)");
    QFont textFont = m_pSignalStatusLabel->font();
    textFont.setPointSize(25);
    m_pSignalStatusLabel->setFont(textFont);

    m_pRunningFlagLabel = new QLabel(this);
    m_pRunningFlagLabel->setFixedSize(RUN_FLAG_SIZE, RUN_FLAG_SIZE);

    QString qstrStyle = "border: 2px solid transparent";
    m_pMaxValueTitleLabel = new QLabel(QObject::trUtf8("Max: "), this);
    m_pMaxValueTitleLabel->setAlignment(Qt::AlignVCenter | Qt::AlignRight);
    QFont font = AppFontManager::instance()->getAppCurFont();
    font.setPointSize(30);
    m_pMaxValueTitleLabel->setFont(font);

    m_pMaxValueLabel = new QLabel(this);
    font.setPointSize(45);
    m_pMaxValueLabel->setFont(font);
    m_pMaxValueTitleLabel->setAlignment(Qt::AlignVCenter | Qt::AlignLeft);
}

/*************************************************
函数名： layoutContents()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 布局内容
*************************************************************/
void CurrentDetectionChart::layoutContents()
{
    QHBoxLayout* pHBoxLayout = new QHBoxLayout();
    pHBoxLayout->setAlignment(Qt::AlignVCenter | Qt::AlignLeft);
    pHBoxLayout->addWidget(m_pSignalStatusLabel);
    pHBoxLayout->addWidget(m_pRunningFlagLabel);

    QVBoxLayout* pMainLayout = new QVBoxLayout(this);
    pMainLayout->setContentsMargins(4, 0, 4, 0);
    pMainLayout->setSpacing(10);
    pMainLayout->addLayout(pHBoxLayout);
    pMainLayout->addSpacing(ITEM_SPACING);

    // 图谱
    m_pGaugeArc = new AmmeterWidget(this);
    m_pGaugeArc->setPointerColor(QColor(100, 184, 255));
    m_pGaugeArc->setFixedWidth(Window::WIDTH);
    m_pGaugeArc->setFixedHeight(g_iDashboardHeight);

    pMainLayout->addWidget(m_pGaugeArc);

    QHBoxLayout* pMaxValueLayout = new QHBoxLayout();
    pMaxValueLayout->setContentsMargins(0, 0, 8, 0);
    pMaxValueLayout->setSpacing(0);
    pMaxValueLayout->addStretch();
    pMaxValueLayout->addWidget(m_pMaxValueTitleLabel);
    pMaxValueLayout->addWidget(m_pMaxValueLabel);

    pMainLayout->addLayout(pMaxValueLayout);

    setLayout(pMainLayout);
}

/*************************************************************
 * 功能：更新范围
 * ************************************************************/
void CurrentDetectionChart::updateRange()
{
    switch (m_eCurrentRangeGear)
    {
    case CurrentDetection::CURRENT_RANGE_GEAR_10A:
        setRange(0, g_iCurrentRangeGear10A);
        break;
    case CurrentDetection::CURRENT_RANGE_GEAR_500A:
        setRange(0, g_iCurrentRangeGear500A);
        break;
    case CurrentDetection::CURRENT_RANGE_GEAR_5000A:
        setRange(0, g_iCurrentRangeGear5000A);
        break;
    }
}

/*************************************************************
 * 功能：更新电流图表
 * ************************************************************/
void CurrentDetectionChart::updateCurrentChart(float fCurrentValue, float fMaxCurrent)
{
    float fCurrentTmp = Module::dealFloatPrecision(fCurrentValue, 1);
    m_pGaugeArc->setValue(fCurrentTmp);

    float fMaxCurrentTmp = Module::dealFloatPrecision(fMaxCurrent, 1);

    // 小于1A显示mA
    if (fMaxCurrentTmp < 1)
    {
        int iMaxCurrentTmp = fMaxCurrentTmp * 1000;
        m_pMaxValueLabel->setText(QString::number(iMaxCurrentTmp) + "mA");
    }
    else
    {
        m_pMaxValueLabel->setText(QString::number(fMaxCurrentTmp, 'f', 1) + "A");
    }
}

void CurrentDetectionChart::onTimeOut()
{
    qsrand(QTime(0,0,0).secsTo(QTime::currentTime()));

    static int iCount = 0;

    if (iCount < 10)
    {
        setRange(0, 50);
        addSample(qrand() % 50);
    }
    else if (iCount < 20)
    {
        setRange(0, 200);
        addSample(qrand() % 200);
    }
    else if (iCount < 30)
    {
        setRange(0, 500);
        addSample(qrand() % 500);
    }
    else if (iCount < 40)
    {
        setRange(0, 1000);
        addSample(qrand() % 1000);
    }
    else if (iCount < 50)
    {
        setRange(0, 5000);
        addSample(qrand() % 1000);
    }
    else
    {
        iCount = 0;
    }

    ++iCount;
}
