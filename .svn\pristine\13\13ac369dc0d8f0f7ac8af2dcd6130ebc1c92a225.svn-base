#include <QHBoxLayout>
#include <QDebug>
#include "updatefirmware.h"
#include "statemonior/StateMonitor.h"
#include "systemsetting/systemsetservice.h"
#include "model/HCStatus.h"
#include "messageBox/msgbox.h"

UpdateFirmware::UpdateFirmware(QWidget *parent)
    : QDialog(parent)
{
    setModal(true);
    setFixedSize(320, 250);
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_TranslucentBackground);

    m_pLoadingView = new LoadingView(trUtf8("Updating"));
    m_pLoadingView->setProgress(0);
    QHBoxLayout *hLayout = new QHBoxLayout;
    hLayout->addWidget(m_pLoadingView);
    setLayout(hLayout);

    connect( SystemSetService::instance(), SIGNAL(sigUpdateFwResult(qint32)),
            this, SLOT(onFwUpdateResult(qint32)) );
    connect( StateMonitor::instance(), SIGNAL(sigUpdateFirmwareProcess(qint32)),
             this, SLOT(onFwUpdateProcess(qint32)) );

    StateMonitor::instance()->startFwUpdate();
}

/*************************************************
函数名： keyPressEvent
输入参数:
    event -- 按键事件
输出参数：NULL
返回值： NULL
功能： 按键事件函数
*************************************************************/
void UpdateFirmware::keyPressEvent( QKeyEvent *event )
{
    dbg_info("[UpdateFirmware::keyPressEvent]");
    if(event->key() == Qt::Key_Escape)
    {
        dbg_info("[UpdateFirmware::keyPressEvent], Qt::Key_Escape");
        MsgBox::critical( "", trUtf8("Please exit after the firmware update is completed.") );
        return;
    }
    return QWidget::keyPressEvent(event);
}

void UpdateFirmware::paintEvent(QPaintEvent *e)
{
    Q_UNUSED(e)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    painter.setPen(Qt::NoPen);
    painter.setBrush(QBrush(QColor(255, 255, 255)));
    painter.drawRoundedRect(rect(), 10, 10);
}

/*************************************************
功能： 槽，固件更新结果的处理
参数： iResult -- 固件更新结果
*************************************************/
void UpdateFirmware::onFwUpdateResult( qint32 iResult )
{
    if(HC_SUCCESS == iResult)
    {
        m_pLoadingView->setProgress(100);
    }
    close();

    if(HC_SUCCESS == iResult)
    {
        MsgBox::information("", trUtf8("Update successfully!"));
    }
    else
    {
        MsgBox::information("", trUtf8("Update failed!"));
    }
}

/*************************************************
功能： 槽，固件更新进度的处理
出参： iProcess -- 固件更新进度
*************************************************/
void UpdateFirmware::onFwUpdateProcess( qint32 iProcess )
{
    m_pLoadingView->setProgress( iProcess );
}
