﻿/*
* Copyright (c) 2021.8，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：customaccessloginview.h
*
* 初始版本：1.0
* 作者：洪澄
* 创建日期：2021年8月3日
* 摘要：该文件定义了接入终端登录界面
* 当前版本：1.0
*/

#ifndef CUSTOMACCESSLOGINVIEW_H
#define CUSTOMACCESSLOGINVIEW_H

#include "widgets/loginview/loginviewbase.h"

class CustomAccessLoginView : public LoginViewBase
{
    Q_OBJECT
public:
    /****************************
    函数名： CustomAccessLoginView
    输入参数:
            parent:父窗口指针
    输出参数：NULL
    返回值：NULL
    功能： 构造函数
    *****************************/
    explicit CustomAccessLoginView(QWidget *parent = 0);
    ~CustomAccessLoginView();

protected:
    /****************************
    功能：登录，子类重新实现该函数
    *****************************/
    virtual void login(const QString& qstrUserName, const QString& qstrPassword);
};

#endif // CUSTOMACCESSLOGINVIEW_H
