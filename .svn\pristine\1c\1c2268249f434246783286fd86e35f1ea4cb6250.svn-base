/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
AEFlyStrategy.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年1月5日
* 摘要：AE 飞行服务模块数据和采样管理模块的基类

* 当前版本：1.0
*/

#ifndef AEFLYSTRATEGY_H
#define AEFLYSTRATEGY_H

#include "AEServiceStrategy.h"

class AEFlyStrategy : public AEServiceStrategy
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    *****************************/
    explicit AEFlyStrategy(QObject *parent = 0);

    /*************************************************
    功能： 开始采集
    *************************************************/
    void startSample();

    /*************************************************
    功能： 设置时间间隔
    输入参数:
        iTimeInterval -- 时间间隔
    返回：
        void
    *************************************************************/
    void setTimeInterval(quint32 iTimeInterval);
protected:
    /*************************************************
    功能： 定时事件处理
    输入参数:
        event -- 事件
    *************************************************/
    void timerEvent(QTimerEvent *e);
signals:
    /*************************************************
    功能： 产生的相位数据
    *************************************************/
    void sigData( const QVector< AE::FlyData > &data );

    void sigReadAEFlyDataFailed();
private:

    AEReadData m_dataRead;//读取的数据

    UINT32 m_uiReadAEFlyDataFailedTimes;

    quint32 m_iTimeInterval;

};

#endif // AEFLYSTRATEGY_H
