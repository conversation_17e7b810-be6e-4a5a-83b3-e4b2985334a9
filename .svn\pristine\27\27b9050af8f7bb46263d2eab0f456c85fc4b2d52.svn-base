#ifndef AEMAINVIEW_H
#define AEMAINVIEW_H

#include <QWidget>
#include <QStackedLayout>
#include "AEView.h"

class AEMainView : public QWidget
{
    Q_OBJECT
public:
    explicit AEMainView(QWidget *parent = NULL);
    virtual ~AEMainView();

protected:
    void keyPressEvent(QKeyEvent *pEvent);

    bool eventFilter(QObject *pObj, QEvent *pEvent);

private slots:
    void onClose();

    void onAtlasButtonClicked(AEView::Function eFunction);

private:
    QStackedLayout* m_pStackedLayout;
    QWidget *m_pBtns;
};

#endif // AEMAINVIEW_H
