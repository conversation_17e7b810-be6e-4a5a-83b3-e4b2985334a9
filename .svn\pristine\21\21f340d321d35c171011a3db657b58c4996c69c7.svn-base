/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* CmdPushButton.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月7日
* 修改日期：2016年11月15日
*       重构
* 摘要：控制按钮，点击执行控制指令

* 当前版本：1.0
*/
#include "CmdPushButton.h"

/*************************************************
功能： 构造函数
输入参数:
    mode -- 显示模式
    parent -- 父窗体
*************************************************************/
CmdPushButton::CmdPushButton( PushButton::Mode mode, QWidget* parent )
    :CmdButton( parent )
{
    PushButton* pButton = new PushButton( mode );
    pButton->setToolTipEnable(true);
    setButton( pButton );
}

void CmdPushButton::setTitle(const QString& strTitle)
{
    CmdButton::setTitle(strTitle);

    if (NULL != button())
    {
        button()->setToolTip(strTitle);
    }
}
