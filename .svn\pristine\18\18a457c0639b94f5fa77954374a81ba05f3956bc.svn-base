#include "wifiinfosettings.h"
#include <QMutexLocker>
#include <QMutex>

//static QMutex g_mtSWInfosObj;
const int iMaxRecords = 10;
const char* const WIFI_INI_FILE = "/opt/bin-bash/wifisaveinfos.ini";
const char* const WIFIS_GROUP = "wifi";
const char* const WIFIS_LIST = "wifilist";
const char* const NAME = "name";
const char* const PWD = "pwd";
const char* const SIG = "sig";
const int g_iWaitTimeout = 60000;

WifiInfoSettings::WifiInfoSettings(QObject *parent) : QObject(parent)
{
    m_pSettings = NULL;
    m_pSettings = new QSettings(WIFI_INI_FILE, QSettings::IniFormat, this);
    refreshInfos();
}

WifiInfoSettings::~WifiInfoSettings()
{

}

WifiInfoSettings* WifiInfoSettings::instance()
{
    //QMutexLocker stLocker(&g_mtSWInfosObj);
    static WifiInfoSettings obj;
    return &obj;
}

void WifiInfoSettings::refreshInfos()
{
    QList<WifiSaveInfo> qlstInfos;
    qlstInfos.clear();

    m_pSettings->beginGroup(WIFIS_GROUP);
    int iSize = m_pSettings->beginReadArray(WIFIS_LIST);
    for(int i = 0; i < iSize && i < iMaxRecords; ++i)
    {
        m_pSettings->setArrayIndex(i);
        WifiSaveInfo stInfo;
        stInfo.qstrName = m_pSettings->value(NAME).toString();
        stInfo.qstrPwd = m_pSettings->value(PWD).toString();
		stInfo.iSigStrength = m_pSettings->value(SIG).toInt();

        if(!(stInfo.qstrName.isEmpty()))
        {
            qlstInfos.append(stInfo);
        }
    }

    m_pSettings->endArray();
    m_pSettings->endGroup();

    if(m_qmt4WifiInfos.tryLock(g_iWaitTimeout))
    {
        m_qlstInfos = qlstInfos;
        m_qmt4WifiInfos.unlock();
    }

    return;
}

void WifiInfoSettings::saveWifiInfos(const QList<WifiSaveInfo>& qlstSaveWifiInfos)
{
    //写最新的iMaxRecords条记录
    m_pSettings->beginGroup(WIFIS_GROUP);
    m_pSettings->beginWriteArray(WIFIS_LIST);
    for(int i = 0, iSize = qlstSaveWifiInfos.size(); i < iSize && i < iMaxRecords; ++i)
    {
        if(!(qlstSaveWifiInfos.at(i).qstrName.isEmpty()))
        {
            m_pSettings->setArrayIndex(i);
            m_pSettings->setValue(NAME, qlstSaveWifiInfos.at(i).qstrName);
            m_pSettings->setValue(PWD, qlstSaveWifiInfos.at(i).qstrPwd);
            m_pSettings->setValue(SIG, qlstSaveWifiInfos.at(i).iSigStrength);
        }
    }
    m_pSettings->endArray();
    m_pSettings->endGroup();
    m_pSettings->sync();

    //更新到m_qlstInfos中
    refreshInfos();

    return;
}

QList<WifiSaveInfo> WifiInfoSettings::readWifiInfos()
{
    QList<WifiSaveInfo> qlstInfos;
    qlstInfos.clear();

    if(m_qmt4WifiInfos.tryLock(g_iWaitTimeout))
    {
        qlstInfos = m_qlstInfos;
        m_qmt4WifiInfos.unlock();
    }

    return qlstInfos;
}
