#include "hfctprpsview.h"
#include <QtConcurrentRun>
#include "controlButton/ControlButtonInfo.h"
#include "buttonBar/PushButtonBar.h"
#include "controlButton/CmdButton.h"
#include "controlButton/RadioButton.h"
#include "controlButton/SliderButton.h"
#include "messageBox/msgbox.h"
#include "window/Window.h"
#include "hfct/HFCTView.h"
#include "hfct/HFCTViewConfig.h"
#include "dataSave/DataFileInfos.h"
#include "hfct/HFCTConfig.h"
#include "rfid/RFIDScanView.h"
#include "rfid/RFIDReadView.h"
#include "playbackView/PlayBackView.h"
#include "deleteDataView/DeleteDataView.h"
#include "hfct/dataSave/HFCTPRPSAndPRPDDataSave.h"
#include "appconfig.h"
#include "View.h"
#include "hfctprpsplaybackview.h"
#include "hfctvideoplaybackview.h"
#include "systemsetting/shutdownpower.h"
#include "diagnosismgr/diagnosismanager.h"
#include "log/log.h"
#include "datadefine.h"
#include "fileoper/fileoperutil.h"

//data save
#include "equipmentinfo.h"
#include "prps/prpsmapdefine.h"
#include "prps/prpddatamap.h"
#include "prps/prpsdatamap.h"
#include "uhf/UHFConfig.h"


//test
#include "hfctprpspdaview.h"

const int MS_PER_MIN = 60 * 1000;  //每分钟对应的ms
const int MS_SECOND_MIN = 1000;  //每秒对应的ms
const int INVALID_USER = -1;

typedef enum _HFCTPrpsButton
{
    BUTTON_HFCT_SAMPLE = 0,//停止采样
    BUTTON_HFCT_PHASE_SHIFT,//相位偏移
    BUTTON_HFCT_GAIN,//增益
    BUTTON_ALTAS_TYPE,//图谱类型
    BUTTON_HFCT_SAVE_DATA,//保存数据
    BUTTON_RECORD,  //录屏
    BUTTON_HFCT_ACCUMULATION,//累积
    BUTTON_MORE_CONFIG,//配置

    BUTTON_HFCT_SYNC_SOURCE, //同步源
    BUTTON_RECORD_TIME,//录屏时间
    BUTTON_HFCT_RFID_SAVE,//RFID扫描保存
    BUTTON_LOAD_RECORD_DATA,//录屏回放
    BUTTON_DELETE_RECORD_DATA,//录屏删除
    BUTTON_HFCT_LOAD_DATA,//载入数据
    BUTTON_HFCT_DELETE_DATA,//删除数据
    BUTTON_THRESHOLD,//阈值
    BUTTON_HFCT_RESTORE_DEFAULT,//恢复默认参数
}HFCTPrpsButton;

//增益
const ButtonInfo::RadioValueConfig s_HFCTGainCfg =
{
    HFCT::GAIN_OPTIONS, HFCT::GAIN_COUNT
};

//同步
const ButtonInfo::RadioValueConfig s_HFCTSyncCfg =
{
    HFCT::TEXT_SYNC_OPTIONS, sizeof(HFCT::TEXT_SYNC_OPTIONS)/sizeof(char*)
};

//相位偏移
const ButtonInfo::SliderValueConfig s_HFCTPhaseShift =
{
    HFCT::PHASE_MIN, HFCT::PHASE_MAX, HFCT::PHASE_STEP
};

//录屏时间 单位：秒
const ButtonInfo::SliderValueConfig s_HFCTRecordTimeCfg =
{
    Module::ScreenRecordTime_MIN, Module::ScreenRecordTime_MAX, Module::ScreenRecordTime_STEP
};

//是否累积
const ButtonInfo::RadioValueConfig s_IsAccumulationCfg =
{
    HFCT::TEXT_ACCUMULATIVE_TIME_OPTIONS, sizeof(HFCT::TEXT_ACCUMULATIVE_TIME_OPTIONS)/sizeof(char*)
};

//图谱类型
const ButtonInfo::RadioValueConfig s_AltasTypeCfg =
{
    HFCT::TEXT_ALTAS_TYPE_OPTIONS, sizeof(HFCT::TEXT_ALTAS_TYPE_OPTIONS)/sizeof(char*)
};

//阈值 单位:%
const ButtonInfo::RadioValueConfig s_ThresholdCfg =
{
    HFCT::TEXT_THRESHOLD_OPTIONS, sizeof(HFCT::TEXT_THRESHOLD_OPTIONS)/sizeof(char*)
};

//控制按钮定义
const ButtonInfo::Info s_HFCTButtonInfo[] =
{
    { BUTTON_HFCT_SAMPLE, { ButtonInfo::COMMAND, HFCT::TEXT_STOP, NULL, ":/images/sampleControl/sampleMode.png",NULL } },//采样模式
    { BUTTON_HFCT_GAIN, { ButtonInfo::RADIO, HFCT::TEXT_GAIN, NULL, ":/images/sampleControl/gain.png", &s_HFCTGainCfg } },//前置增益
    { BUTTON_ALTAS_TYPE, { ButtonInfo::RADIO, HFCT::TEXT_ALTAS_TYPE, NULL, NULL, &s_AltasTypeCfg } },//图谱类型
    { BUTTON_THRESHOLD, { ButtonInfo::RADIO, HFCT::TEXT_PRPS_NOISEREDUCTION, NULL, NULL, &s_ThresholdCfg } },//阈值
    { BUTTON_HFCT_SAVE_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL } },//保存数据
    { BUTTON_RECORD, { ButtonInfo::COMMAND, HFCT::TEXT_RECORD, NULL, NULL, NULL } },//录屏
    { BUTTON_HFCT_ACCUMULATION, { ButtonInfo::RADIO, HFCT::TEXT_ACCUMULATIVE_TIME, NULL, ":/images/sampleControl/brandWidth.png", &s_IsAccumulationCfg } },//是否累积
    { BUTTON_MORE_CONFIG, { ButtonInfo::COMMAND, HFCT::TEXT_MORE_CONFIG, NULL, ":/images/sampleControl/moreconfig.png", NULL } },//更多配置
};

//"更多.."按钮定义
const ButtonInfo::Info s_HFCTButtonInfoMore[] =
{
    //{ BUTTON_HFCT_SYNC_SOURCE, { ButtonInfo::RADIO, HFCT::TEXT_SYNC_SOURCE, NULL, ":/images/sampleControl/syncMode.png", &s_HFCTSyncCfg } },//同步源
    { BUTTON_RECORD_TIME, { ButtonInfo::FIXED_STEP_SLIDER, HFCT::TEXT_RECORD_TIME, HFCT::TEXT_SECOND, NULL, &s_HFCTRecordTimeCfg } },//录屏时间
    { BUTTON_HFCT_PHASE_SHIFT, { ButtonInfo::FIXED_STEP_SLIDER, HFCT::TEXT_PHASE_ALIAS, HFCT::TEXT_DEGREE, ":/images/sampleControl/phaseShift.png", &s_HFCTPhaseShift } },//相位偏移
    { BUTTON_HFCT_RFID_SAVE, { ButtonInfo::COMMAND, HFCT::TEXT_RFID_SAVE, NULL, ":/images/sampleControl/save.png", NULL } },//RFID扫描保存
    { BUTTON_LOAD_RECORD_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_RECORD_PLAYBACK, NULL, NULL, NULL } },//录屏回放
    { BUTTON_DELETE_RECORD_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_RECORD_DELETE, NULL, NULL, NULL } },//录屏删除
    { BUTTON_HFCT_LOAD_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_LOAD_DATA, NULL, ":/images/sampleControl/importData.png", NULL } },//载入数据
    { BUTTON_HFCT_DELETE_DATA, { ButtonInfo::COMMAND, HFCT::TEXT_DELETE_DATA, NULL, ":/images/sampleControl/delete.png", NULL } },//删除数据
    { BUTTON_HFCT_RESTORE_DEFAULT, { ButtonInfo::COMMAND, HFCT::TEXT_RESTORE_DEFAULT, NULL, ":/images/sampleControl/restoreDefault.png", NULL } },//恢复默认
};


struct SaveHFCTPRPSVideoDataEvent : public QEvent
{
    enum {EventId = QEvent::User + View::EVENT_SAVE_HFCT_PRPS_VIDEO};

    explicit SaveHFCTPRPSVideoDataEvent(bool bSaved, const QString &strMessage)

        : QEvent(static_cast<Type>(EventId)),

          m_bSaved(bSaved), m_strMessage(strMessage) {}

    const bool m_bSaved;

    const QString m_strMessage;

};

/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
HFCTPrpsView::HFCTPrpsView(const QString &strTitle, QWidget *parent) :
    HFCTPrpsViewBase(strTitle,parent)
{
    //初始化使用的数据
    initDatas();
    //新建图谱
    ChartWidget *pChart = createChart(parent);
    setChart( pChart );

    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar( HFCT::CONTEXT, s_HFCTButtonInfo, sizeof(s_HFCTButtonInfo)/sizeof(ButtonInfo::Info) );
    m_pSampleBtn = pButtonBar->button(BUTTON_HFCT_SAMPLE); // 因采样按钮显示的文本需要刷新

    // 创建更多设置栏
    createMoreConfigButtonBar(HFCT::CONTEXT, s_HFCTButtonInfoMore, sizeof(s_HFCTButtonInfoMore)/sizeof(ButtonInfo::Info));

    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_ACCUMULATION)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_ALTAS_TYPE)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_GAIN)))->setPopupMode(PopupWidget::SWITCH_MODE);
    ((PopupButton*)(buttonBar()->button(BUTTON_THRESHOLD)))->setPopupMode(PopupWidget::SWITCH_MODE);

    //设置数据
    setButtonBarDatas();

    setChartDatas();


    //设置工作参数
    setWorksets();

    //启动采集
    startSample();
    setSampleBtnText(isSampling());
}

/*************************************************
功能： 析构
*************************************************************/
HFCTPrpsView::~HFCTPrpsView( )
{
    stopSample();

    if(m_SaveVideoFuture.isRunning())
    {
        m_SaveVideoFuture.waitForFinished();
    }
    saveConfig(); // 存储到配置文件中
    deleteRecordView();
    ShutDownPower::getInstance()->start();   // 开始定时关机
    m_qui8DataCnt = -1;
    m_vConfidences.clear();
    m_mDefect.clear();
}

ChartWidget *HFCTPrpsView::createChart(QWidget *parent)
{
    Q_UNUSED(parent)

    int iMax = 0;
    iMax = HFCT::GAIN_BASE + HFCT::GAIN_VALUES[m_eGain];
    m_pChart = new HfctPrpsUnionView( PERIOD_CNT, PERIOD_CNT, PHASE_CNT,iMax,HFCT::CHART_MIN_VALUE );
    m_pChart->setPrpdContentsMargins(0, PRPD_MARGIN, 0, PRPD_MARGIN);

    m_pChart->setFixedSize( Window::WIDTH,CHART_HEIGHT );
    m_pChart->setPeriodNum(m_iSysPeriod);

    ChartWidget *pWidget = new ChartWidget;
    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->setContentsMargins(0, 0, 0, 0);
    vLayout->addWidget(m_pChart);
    pWidget->setLayout(vLayout);
    pWidget->setFixedWidth(Window::WIDTH);

    return pWidget;
}

/*************************************************
功能： 响应S键事件
输入参数：
        id -- 按钮ID
*************************************************************/
void HFCTPrpsView::onSKeyPressed()
{
    pressSaveData();

    /*stopSample();

    QString strStationName;
    QString strDeviceName;

    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
    centerPoint.setY(pBtnBar->pos().y()+pBtnBar->height()/2);

    fastSave( "",
              strStationName,
              strDeviceName,
              DATA_STORAGE_PATH + '/' + HFCT_PRPS_PRPD_FOLDER,
              centerPoint);

    m_pChart->clearData();
    startSample();*/

    return;
}

/*************************************************
功能： 事件处理的函数
输入参数：
        event -- 事件对象
*************************************************************/
bool HFCTPrpsView::event( QEvent * event )
{
    if ( event->type() == static_cast<QEvent::Type>(SaveHFCTPRPSVideoDataEvent::EventId))
    {
        SaveHFCTPRPSVideoDataEvent *pEvent = static_cast<SaveHFCTPRPSVideoDataEvent*>(event);
        saveVideoDataDone(pEvent->m_bSaved);
        return true;
    }
    else
    {
        return SampleChartView::event(event);
    }
}

/*************************************************
功能： 保存video数据结束
入参：true -- 成功
     false -- 失败
*************************************************************/
void HFCTPrpsView::saveVideoDataDone( bool bIsSuccess )
{
    if(NULL != m_pLoadingWidget)
    {
        m_pLoadingWidget->close();
        APP_CHECK_FREE(m_pLoadingWidget);
    }

    QFileInfo fileInfo(m_strVideoFile);
    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
    centerPoint.setY(pBtnBar->pos().y()+pBtnBar->height()/2);

    QString strText;
    if(bIsSuccess == false)
    {
        strText = QObject::trUtf8("Save failure!");
    }
    else
    {
        strText = fileInfo.fileName();
    }
    processTooLongMsgText(strText);

    MsgBox::information("", strText, centerPoint);

    stopRecord();       // 停止录制
    startSample();  // 恢复采样
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void HFCTPrpsView::onButtonValueChanged( int id, int iValue )
{
    switch( id )
    {
    case BUTTON_HFCT_ACCUMULATION:          // 设置累积模式
    {
        //enableAccumulation( (bool)iValue );
        m_iAccumulationTime = iValue;
        updateAccumulativeTime();
    }
        break;
    case BUTTON_HFCT_GAIN:          // 设置增益
    {
        if( (HFCT::Gain)iValue != m_eGain )
        {
            m_eGain = (HFCT::Gain)iValue;
            setGain( m_eGain );
            m_pChart->clearData();
            m_pChart->setRangeMin( HFCT::GAIN_VALUES[m_eGain] );
            m_pChart->setRangeMax( HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE );
        }
        else
        {
            // GAIN not changed
        }
    }
        break;
    case BUTTON_ALTAS_TYPE://图谱类型
    {
        m_eAltasType = static_cast<PhaseAbstractView::AltasType>(iValue);
        updateTitle();
        m_pChart->setAltasType(m_eAltasType);
    }
        break;
    case BUTTON_HFCT_SYNC_SOURCE:   // 设置同步源
    {
        Module::SyncSource eSyncSource = (Module::SyncSource)(iValue + Module::WIRELESS_SYNC);
        if( eSyncSource != m_eSyncSource )
        {
            m_eSyncSource = eSyncSource;
            setSyncSource( m_eSyncSource );
            m_pChart->setSync( (PrpsGlobal::SyncSource)m_eSyncSource,
                               (PrpsGlobal::SyncState)m_eSyncState );
        }
        else
        {
            // syncsource not changed
        }
    }
        break;
    case BUTTON_HFCT_PHASE_SHIFT:   // 设置相位偏移
    {
        if( iValue != m_iPhaseAlias )
        {
            m_iPhaseAlias = iValue;
            m_pChart->setPhaseOffset( m_iPhaseAlias );
        }
        else
        {
            //iPhase not changed
        }
    }
        break;
    case BUTTON_RECORD_TIME:   // 设置录屏时间
    {
        if( iValue != m_ucRecordTime )
        {
            m_ucRecordTime = iValue;
        }
        else
        {
            //record time not changed
        }
    }
        break;
    case BUTTON_THRESHOLD://阈值
    {
        m_eThresholdMode = static_cast<HFCT::ThresholdMode>(iValue);
        updateThresholdPercentage();
        m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
        m_pChart->setPRPDThresholdPercentage(m_fThresholdPercentage);
    }
        break;
    default:
        break;
    }
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void HFCTPrpsView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case BUTTON_HFCT_SAMPLE:   // 采样、停止
    {
        if(isSampling())
        {
            stopSample();
        }
        else
        {
            m_pChart->clearData();
            startSample();
        }

        setSampleBtnText(isSampling());
    }
        break;
    case BUTTON_HFCT_RESTORE_DEFAULT:// 恢复默认
    {
        restoreDefault();
        m_pChart->clearData();
    }
        break;
    case BUTTON_MORE_CONFIG:    // 更多菜单
    {
        showMoreConfigButtonBar();
    }
        break;
    case BUTTON_HFCT_SAVE_DATA: // 保存数据
    {
        pressSaveData();
    }
        break;
    case BUTTON_HFCT_RFID_SAVE:// RFID扫描保存
    {
        //停止采集
        stopSample();

        RFIDSaveData();

        //开始采集
        startSample();
    }
        break;
    case BUTTON_HFCT_LOAD_DATA: // 载入数据
    {
        //stopSample();     //王谦屏蔽

        loadData();

        //startSample();
    }
        break;
    case BUTTON_HFCT_DELETE_DATA:// 删除数据
    {
        //stopSample();    //王谦屏蔽

        deleteData();

        //startSample();
    }
        break;
    case BUTTON_RECORD:// 录制
    {
        if(m_iAccumulationTime == false)
        {
            // 将按键置为累积选项
            //((RadioButton*)buttonBar()->button( BUTTON_HFCT_ACCUMULATION ))->setValue( 1 );
            m_bAccumulationChangedByRecord = true;
        }

        m_pChart->clearData();

        if(!isSampling())
        {
            startSample();
            setSampleBtnText(isSampling());
        }

        qint32 iInterval = SystemSetService::instance()->getPRPSSampleInterval();
        qint32 iPeriodAll = m_ucRecordTime * MS_SECOND_MIN / iInterval;
        startRecord( iPeriodAll,iInterval );
    }
        break;
    case BUTTON_DELETE_RECORD_DATA:// 删除录制数据
    {
        //stopSample();  //王谦屏蔽

        deleteVideoData();

        //startSample();
    }
        break;
    case BUTTON_LOAD_RECORD_DATA:// 录屏回放
    {
        stopSample();

        loadVideoData();

        startSample();
    }
        break;
    default:
        break;
    }
}

/*************************************************
功能： 槽，响应读取的数据
输入参数：
        data -- 周期数据
*************************************************************/
void HFCTPrpsView::onDataRead(HFCT::PRPSData data, MultiServiceNS::USERID userId)
{
    if((getUserId() != INVALID_USER) && (getUserId() == userId) && isSampling())
    {
        //启动录音
        if(m_bIsRecord)
        {
            m_uiRecordCount++;

            /*
              1.T95沿用T90习惯，开启录屏后5s后时间开始启动
              2.每100ms采集一组数据，丢弃前50组数据
            */
            if(m_uiRecordCount <= PERIOD_CNT)
            {
                return;
            }
        }
        QVector<double> vData;
        int iMinData = 0;
        if( ( m_eGain < HFCT::GAIN_MINUS_20 ) && ( m_eGain > HFCT::GAIN_MINUS_60 ) )
        {
            iMinData = HFCT::GAIN_VALUES[m_eGain];
        }

        m_stPRPSFeateurInfo.dSpecMaxVal = m_pChart->rangeMax();
        m_stPRPSFeateurInfo.dSpecMinVal = m_pChart->rangeMin();
        m_stPRPSFeateurInfo.iPhaseNum = m_pChart->phaseCount();
        m_stPRPSFeateurInfo.iPeriodNum = m_pChart->periodCount();

        for( int i = 0, iSize = data.vSpectrum.size(); i < iSize; ++i )
        {
            if(data.vSpectrum[i] < iMinData)
            {
                data.vSpectrum[i] = iMinData;
            }
            vData.append( (double)data.vSpectrum[i] );
            m_stPRPSFeateurInfo.qvtDataIn.append(static_cast<double>(data.vSpectrum[i]));
        }

        if(HFCT::THRESHOLD_AUTO == m_eThresholdMode && DealData::denoisePRPSData(m_stPRPSFeateurInfo))
        {
            m_fThresholdPercentage = static_cast<float>(m_stPRPSFeateurInfo.dThresholdDbVal);
            m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
            m_pChart->setPRPDThresholdPercentage(m_fThresholdPercentage);
            log_debug("--------------------threshold: %f.", m_fThresholdPercentage);
        }

        m_pChart->setData( vData );             // 将数据刷新到图谱上

        //添加本地诊断的逻辑，是否耗时，同时，录屏情况下，不存相关本地诊断标识，不进行本地诊断
        if(!m_bIsRecord)
        {
            diagDataInfo();
        }

        if( m_vMaxValue.size() >= PERIOD_CNT )
        {
            m_vMaxValue.pop_front();            // 每推进一次，且最大值周期总数超出50周期(无论工频时50Hz还是60Hz)，则将最前一周期数据删除
        }
        m_vMaxValue.append( PRPSMaxValue( (double)data.cMaxSpectrum,data.eSpectrumState ) );

        //计算每周期最大值集合中的最大值，即当前显示数据中的最大值
        double dMaxValue = 0;
        HFCT::SpectrumState eState = m_vMaxValue.first().eSpectrumState;
        for( int i = 0, iSize = m_vMaxValue.size(); i < iSize; ++i )
        {
            if( m_vMaxValue.at( i ).cMaxValue > dMaxValue )
            {
                dMaxValue = m_vMaxValue.at( i ).cMaxValue;
                eState = m_vMaxValue.at( i ).eSpectrumState;
            }
        }

        m_usMaxValue = static_cast<quint16>(dMaxValue);
        m_eSpectrumState = eState;
        m_pChart->setMaxSpectrum( dMaxValue,( PrpsGlobal::SpectrumState )eState );

        if( m_bIsRecord && ( m_pRecordView != NULL ))
        {
            // 每次添加一周期数据，更新进度为1周期，魔法数释义
            m_pRecordView->setPeriodShift( 1 );
        }
        else
        {
            //not record
        }
    }
    else
    {
        //illegal
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
    }
}

/*************************************************
功能： 槽，响应诊断结果
输入参数：
        qspDiagResultInfo -- 诊断结果
*************************************************************/
void HFCTPrpsView::onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo)
{
    if(qspDiagResultInfo.data())
    {
        DiagConfig::DiagDisplayInfo stDiagInfo;
        stDiagInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(qspDiagResultInfo.data()->stDiagRet.defectLevel);
        stDiagInfo.qstrPDDesInfo = qspDiagResultInfo.data()->qstrPDDescription;
        stDiagInfo.qstrPDSignalInfos = qspDiagResultInfo.data()->qstrPDSignalTypeInfos;

        if(m_pChart)
        {
            m_pChart->setDiagRet(stDiagInfo);
        }
    }

    return;
}

/*************************************************
功能： 槽，响应信号状态改变
输入参数：
        eSignalState -- 信号状态
*************************************************************/
void HFCTPrpsView::onSignalChanged( Module::SignalState eSignalState )
{
    if(eSignalState == Module::SIGNAL_STATE_EXIST)
    {
        m_pChart->setConnected(true);
    }
    else
    {
        m_pChart->setConnected(false);
        m_pChart->setMaxSpectrum(PrpsGlobal::SPECTRUM_UNDER_LOW); //在无信号时显示“<0dB”
    }
}

/*************************************************
功能： 槽，响应同步状态改变
输入参数：
        eSyncState -- 同步状态
*************************************************************/
void HFCTPrpsView::onSyncStateChanged( Module::SyncState eSyncState )
{
    if( m_eSyncState != eSyncState )
    {
        m_eSyncState = eSyncState;
        m_pChart->setSync((PrpsGlobal::SyncSource)m_eSyncSource, (PrpsGlobal::SyncState)eSyncState);
    }
    return;
}

/*************************************************
功能： 初始化参数
*************************************************************/
void HFCTPrpsView::initParameters()
{
    m_eAltasType = PhaseAbstractView::PRPS_PRPD;
    int iGroup = HFCT::GROUP_HFCT_PRPS;
    m_pConfig = ConfigManager::instance()->config();
    m_pConfig->beginGroup( Module::GROUP_HFCT );

    m_eGain = (HFCT::Gain)m_pConfig->value( HFCT::KEY_GAIN ).toUInt();
    m_iPhaseAlias = m_pConfig->value( HFCT::KEY_PHASEALIAS ).toUInt();
    m_eSyncSource = (Module::SyncSource)m_pConfig->value( HFCT::KEY_SYNC_SOURCE ).toUInt();
    m_iAccumulationTime = m_pConfig->value( HFCT::KEY_PRPS_ACCUMULATION_TIME, iGroup ).toUInt();
    m_ucRecordTime = m_pConfig->value( HFCT::KEY_PRPS_RECORD_TIME, iGroup ).toUInt();
    m_fThresholdPercentage = m_pConfig->value( HFCT::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup).toFloat();
    m_eThresholdMode = static_cast<HFCT::ThresholdMode>(m_pConfig->value( HFCT::KEY_PRPS_THRESHOLD_MODE, iGroup).toUInt());
    m_pConfig->endGroup();

    m_pConfig->beginGroup( Module::GROUP_APP );
    m_ucSysFreq = m_pConfig->value( APPConfig::KEY_SYS_FREQ ).toUInt();
    m_pConfig->endGroup();

    m_usMaxValue = 0;
    m_eSpectrumState = HFCT::SPECTRUM_INSIDE_RANGE;
    m_bIsRecord = false;
    m_uiRecordCount = 0;
}

/*************************************************
功能： 初始化数据
*************************************************************/
void HFCTPrpsView::initDatas( void )
{
    m_pRecordView = NULL;
    m_pLoadingWidget = NULL;
    m_vMaxValue.clear();

    m_bAccumulationChangedByRecord = false;
    m_eSyncState = Module::Not_Sync;

    initParameters();
    m_qui8DataCnt = -1;
    m_vConfidences.clear();
    m_mDefect.clear();
}

/*************************************************
功能： 设置按钮栏数据
*************************************************************/
void HFCTPrpsView::setButtonBarDatas( void )
{
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_GAIN)))->setValue( m_eGain );
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_PHASE_SHIFT)))->setValue( m_iPhaseAlias );
    //((PopupButton*)(buttonBar()->button(BUTTON_HFCT_SYNC_SOURCE)))->setValue( m_eSyncSource - (int)Module::WIRELESS_SYNC );
    ((PopupButton*)(buttonBar()->button(BUTTON_HFCT_ACCUMULATION)))->setValue(m_iAccumulationTime);
    ((PopupButton*)(buttonBar()->button(BUTTON_RECORD_TIME)))->setValue( m_ucRecordTime );
    ((PopupButton*)(buttonBar()->button(BUTTON_ALTAS_TYPE)))->setValue(m_eAltasType);
    ((PopupButton*)(buttonBar()->button(BUTTON_THRESHOLD)))->setValue(m_eThresholdMode);
}

/*************************************************
功能： 设置图谱数据
*************************************************************/
void HFCTPrpsView::setChartDatas( void )
{
    m_pChart->setAltasType(m_eAltasType);

    m_pChart->setSync( (PrpsGlobal::SyncSource) m_eSyncSource, (PrpsGlobal::SyncState) m_eSyncState );

    /*系统频率*/
    m_pChart->setPowerFreq( PrpsGlobal::FREQ_50 );

    /*运行状态*/
    m_pChart->setRunningMode(isSampling());

    /*相位偏移*/
    m_pChart->setPhaseOffset( m_iPhaseAlias );

    /*设置量程*/
    m_pChart->setRangeMin( HFCT::GAIN_VALUES[m_eGain] );
    m_pChart->setRangeMax( HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE );

    /*累积模式*/

    updateAccumulativeTime();

    m_pChart->setPRPSThresholdPercentage(m_fThresholdPercentage);
    m_pChart->setPRPDThresholdPercentage(m_fThresholdPercentage);
}

/*************************************************
功能： 恢复默认
*************************************************************/
void HFCTPrpsView::restoreDefault( void )
{
    if( MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to restore default settings?")) )
    {
        stopSample();
        setSampleBtnText(isSampling());

        int iGroup = HFCT::GROUP_HFCT_PRPS;

        m_pConfig->beginGroup( Module::GROUP_HFCT );
        QVector<Config::GroupKey> totalKeys;
        totalKeys << Config::GroupKey( HFCT::KEY_GAIN );
        totalKeys << Config::GroupKey( HFCT::KEY_SYNC_SOURCE );
        totalKeys << Config::GroupKey( HFCT::KEY_PHASEALIAS );
        totalKeys << Config::GroupKey( HFCT::KEY_PRPS_ACCUMULATION_TIME, iGroup );
        totalKeys << Config::GroupKey( HFCT::KEY_PRPS_RECORD_TIME, iGroup );
        totalKeys << Config::GroupKey( HFCT::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup );
        totalKeys << Config::GroupKey( HFCT::KEY_PRPS_THRESHOLD_MODE, iGroup );

        m_pConfig->restoreDefault( totalKeys );
        m_pConfig->endGroup();

        //重新初始化数据
        initParameters();

        //设置数据
        setButtonBarDatas();
        setChartDatas();

        //设置工作参数
        setWorksets();
    }
}

/*************************************************
功能： 设置工作参数
*************************************************************/
void HFCTPrpsView::setWorksets( void )
{
    setWorkMode( HFCT::MODE_PRPS );
    setGain( m_eGain );
    setSyncSource( m_eSyncSource );
}

/*************************************************
功能： 保存设置
*************************************************************/
bool HFCTPrpsView::saveConfig( void )
{
    int iGroup = HFCT::GROUP_HFCT_PRPS;
    m_pConfig->beginGroup( Module::GROUP_HFCT );

    m_pConfig->setValue( m_eGain, HFCT::KEY_GAIN );
    m_pConfig->setValue( m_iPhaseAlias, HFCT::KEY_PHASEALIAS );
    m_pConfig->setValue( m_eSyncSource, HFCT::KEY_SYNC_SOURCE );
    m_pConfig->setValue( m_iAccumulationTime, HFCT::KEY_PRPS_ACCUMULATION_TIME, iGroup );
    m_pConfig->setValue( m_ucRecordTime, HFCT::KEY_PRPS_RECORD_TIME, iGroup );
    m_pConfig->setValue( m_fThresholdPercentage, HFCT::KEY_PRPS_ANALYSIS_THRESHOLD, iGroup );
    m_pConfig->setValue( m_eThresholdMode, HFCT::KEY_PRPS_THRESHOLD_MODE, iGroup );
    m_pConfig->endGroup();

    return true;
}

INT32 HFCTPrpsView::quantizationAmpSection(INT32 iAmp, float fAmpLower, float fAmpUpper, INT32 iQuantizationAmp)
{
    float fRangePerSection = (fAmpUpper - fAmpLower) / (float)iQuantizationAmp;
    int section = 0;
    float fAmp = 0;

    if(iAmp > Module::ZERO)
    {
        fAmp = (iAmp - Module::ZERO);
    }
    else
    {
        fAmp = (float)iAmp;
    }

    section = fAmp / fRangePerSection;
    //dbg_info("section is %d\n", section);
    return section;
}

void HFCTPrpsView::getVideoDataFilePath(QString &strAbsolutePath)
{
    QDateTime dateTime = QDateTime::currentDateTime();
    QString strDate = dateTime.toString("yyyyMMdd");
    strAbsolutePath = DATA_STORAGE_PATH + "/" + HFCT_PRPS_RECORD_FOLDER;
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(0,4);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(4,2);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(6,2);
}

void HFCTPrpsView::getDataFilePath(QString &strAbsolutePath)
{
    QDateTime dateTime = QDateTime::currentDateTime();
    QString strDate = dateTime.toString("yyyyMMdd");
    strAbsolutePath = DATA_STORAGE_PATH + "/" + HFCT_PRPS_PRPD_FOLDER;
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(0,4);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(4,2);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(6,2);
}


void HFCTPrpsView::setPRPSMapHead(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.stPRPSHeadInfo.strSubstationName = m_qstrStationName;//站点名称
    stSavedData.stPRPSHeadInfo.strSubstationNumber = "";//站点编码
    stSavedData.stPRPSHeadInfo.eCode = DataFileNS::SPECTRUM_CODE_HFCT_PRPS;//图谱类型

    if ( 0/*m_bSaveData*/ )
    {
        //stSavedData.stPRPSHeadInfo.generationDateTime = m_qSaveTime;
    }
    else
    {
        stSavedData.stPRPSHeadInfo.generationDateTime = QDateTime::currentDateTime();//图谱生成时间
    }

    stSavedData.stPRPSHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;//图谱性质
    stSavedData.stPRPSHeadInfo.strDeviceName = m_qstrDeviceName;//电力设备名称
    stSavedData.stPRPSHeadInfo.strDeviceNumber = m_qstrDeviceNumber;//电力设备编码
    stSavedData.stPRPSHeadInfo.strTestPointName = "";//测点名称
    stSavedData.stPRPSHeadInfo.strTestPointNumber = "";//测点编码
    stSavedData.stPRPSHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_DEFAULT;//测点的测试部位
    stSavedData.stPRPSHeadInfo.ucTestChannelSign = 0;//仪器的检测通道标识，例如：1
    stSavedData.stPRPSHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_DOUBLE;//存储数据类型
    stSavedData.stPRPSHeadInfo.ePDDefectLevel = static_cast<DataFileNS::PDDefectLevel>(m_pChart->getPDDiagnosisRet());
    stSavedData.stPRPSHeadInfo.qstrPDSignalTypeInfos = m_pChart->getPDSignalInfos();
    stSavedData.stPRPSHeadInfo.qstrRemark = m_qstrRemark;
}

void HFCTPrpsView::setPRPDMapHead(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.stPRPDHeadInfo.strSubstationName = m_qstrStationName;//站点名称
    stSavedData.stPRPDHeadInfo.strSubstationNumber = "";//站点编码
    stSavedData.stPRPDHeadInfo.eCode = DataFileNS::SPECTRUM_CODE_HFCT_PRPD;//图谱类型

    if ( 0/*m_bSaveData*/ )
    {
        //stSavedData.stPRPDHeadInfo.generationDateTime = m_qSaveTime;
    }
    else
    {
        stSavedData.stPRPDHeadInfo.generationDateTime = QDateTime::currentDateTime();//图谱生成时间
    }

    stSavedData.stPRPDHeadInfo.eMapProperty = DataFileNS::PROPERTY_TEST;//图谱性质
    stSavedData.stPRPDHeadInfo.strDeviceName = m_qstrDeviceName;//电力设备名称
    stSavedData.stPRPDHeadInfo.strDeviceNumber = m_qstrDeviceNumber;//电力设备编码
    stSavedData.stPRPDHeadInfo.strTestPointName = "";//测点名称
    stSavedData.stPRPDHeadInfo.strTestPointNumber = "";//测点编码
    stSavedData.stPRPDHeadInfo.eTestLocation = DataFileNS::TEST_LOCATION_DEFAULT;//测点的测试部位
    stSavedData.stPRPDHeadInfo.ucTestChannelSign = 0;//仪器的检测通道标识，例如：1
    stSavedData.stPRPDHeadInfo.eDataPrimitiveType = DataFileNS::DATA_TYPE_DOUBLE;//存储数据类型
    stSavedData.stPRPDHeadInfo.ePDDefectLevel = static_cast<DataFileNS::PDDefectLevel>(m_pChart->getPDDiagnosisRet());
    stSavedData.stPRPDHeadInfo.qstrPDSignalTypeInfos = m_pChart->getPDSignalInfos();
    stSavedData.stPRPDHeadInfo.qstrRemark = m_qstrRemark;
}

void HFCTPrpsView::setPRPSMapVideoInfo(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.stPRPSInfo.eAmpUnit = DataFileNS::AMP_UNIT_DB;
    stSavedData.stPRPSInfo.fAmpLowerLimit = HFCT::GAIN_VALUES[m_eGain];
    stSavedData.stPRPSInfo.fAmpUpperLimit = HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE;

    stSavedData.stPRPSInfo.eBandWidth = DataFileNS::BAND_DEFAULT;


    //    UHF::bandwidth2FreqRange(stSavedData.stPRPSInfo.fFrequencyMin, stSavedData.stPRPSInfo.fFrequencyMax, eBandWidth);//temply
    stSavedData.stPRPSInfo.iSingleCycleInterval = SystemSetService::instance()->getPRPSSampleInterval();
    stSavedData.stPRPSInfo.iPhaseIntervalCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stSavedData.stPRPSInfo.iQuantificationAmp = 0;//PRPSMapNS::QUANTIFICATION_AMP;//temply

    QVector< double > vecPRPSVideoData = m_pChart->videoData();
    int iSize = vecPRPSVideoData.size();
    stSavedData.stPRPSInfo.iPowerFreCycleCount = iSize / PRPSMapNS::PHASE_INTERVAL_CNT;
    stSavedData.stPRPSInfo.uiRecordPRPSCycleCount = iSize / PRPSMapNS::PHASE_INTERVAL_CNT;
    memset(stSavedData.stPRPSInfo.ucaDischargeTypeProb, 0, sizeof(stSavedData.stPRPSInfo.ucaDischargeTypeProb));
    stSavedData.stPRPSInfo.eDataSign = (PRPSMapNS::MapDataSign) m_eSpectrumState;

    stSavedData.stPRPSInfo.eGainType = DataFileNS::GAIN_TYPE_DB;
    stSavedData.stPRPSInfo.sGain = -HFCT::GAIN_VALUES[m_eGain];

    stSavedData.stPRPSInfo.eSyncSource = (DataFileNS::SyncSource)(m_eSyncSource + 1);
    stSavedData.stPRPSInfo.ucSyncState = m_eSyncState;
    stSavedData.stPRPSInfo.fSyncFreq = -1;

    double dMax  = 0;
    if(iSize > 0)
    {
        dMax = vecPRPSVideoData[0];
        for(int i = 0; i < iSize; ++i)
        {
            dMax = (vecPRPSVideoData[i] > dMax) ? vecPRPSVideoData[i] : dMax;
        }
    }

    stSavedData.stPRPSInfo.fMax = static_cast<float>(dMax);
    stSavedData.stPRPSInfo.fAnalysisThreshold = m_pChart->prpsThreshold();
    return;
}

void HFCTPrpsView::setPRPSMapInfo(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.stPRPSInfo.eAmpUnit = DataFileNS::AMP_UNIT_DB;
    stSavedData.stPRPSInfo.fAmpLowerLimit = HFCT::GAIN_VALUES[m_eGain];
    stSavedData.stPRPSInfo.fAmpUpperLimit = HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE;

    stSavedData.stPRPSInfo.eBandWidth = DataFileNS::BAND_DEFAULT;


    /*UHF::bandwidth2FreqRange(stSavedData.stPRPSInfo.fFrequencyMin, stSavedData.stPRPSInfo.fFrequencyMax, eBandWidth);*///temply

    stSavedData.stPRPSInfo.iSingleCycleInterval = SystemSetService::instance()->getPRPSSampleInterval();
    stSavedData.stPRPSInfo.iPhaseIntervalCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stSavedData.stPRPSInfo.iQuantificationAmp = 0;

    QVector< double > vecPRPSData = m_pChart->prpsData();
    int iSize = vecPRPSData.size();
    stSavedData.stPRPSInfo.iPowerFreCycleCount = iSize / PRPSMapNS::PHASE_INTERVAL_CNT;

    if(stSavedData.stPRPSInfo.iPowerFreCycleCount < 50)
    {
        stSavedData.stPRPSInfo.iPowerFreCycleCount = 50;
    }
    stSavedData.stPRPSInfo.uiRecordPRPSCycleCount = 0;
    memset(stSavedData.stPRPSInfo.ucaDischargeTypeProb, 0, sizeof(stSavedData.stPRPSInfo.ucaDischargeTypeProb));

    stSavedData.stPRPSInfo.eDataSign = (PRPSMapNS::MapDataSign) m_eSpectrumState;

    stSavedData.stPRPSInfo.eGainType = DataFileNS::GAIN_TYPE_DB;

    stSavedData.stPRPSInfo.sGain = -HFCT::GAIN_VALUES[m_eGain];

    stSavedData.stPRPSInfo.eSyncSource = (DataFileNS::SyncSource)(m_eSyncSource + 1);
    stSavedData.stPRPSInfo.ucSyncState = m_eSyncState;
    stSavedData.stPRPSInfo.fSyncFreq = -1;

    double dMax  = 0;
    if(iSize > 0)
    {
        dMax = vecPRPSData[0];
        for(int i = 0; i < iSize; ++i)
        {
            dMax = (vecPRPSData[i] > dMax) ? vecPRPSData[i] : dMax;
        }
    }

    stSavedData.stPRPSInfo.fMax = static_cast<float>(dMax);
    stSavedData.stPRPSInfo.fAnalysisThreshold = m_pChart->prpsThreshold();

    return;
}

void HFCTPrpsView::setPRPDMapVideoInfo(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.stPRPDInfo.eAmpUnit = DataFileNS::AMP_UNIT_DB;
    stSavedData.stPRPDInfo.fAmpLowerLimit = HFCT::GAIN_VALUES[m_eGain];
    stSavedData.stPRPDInfo.fAmpUpperLimit = HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE;
    stSavedData.stPRPDInfo.eBandWidth = DataFileNS::BAND_DEFAULT;

    //    UHF::bandwidth2FreqRange(stSavedData.stPRPDInfo.fFrequencyMin, stSavedData.stPRPDInfo.fFrequencyMax, eBandWidth);//temply

    stSavedData.stPRPDInfo.iSingleCycleInterval = SystemSetService::instance()->getPRPSSampleInterval();
    stSavedData.stPRPDInfo.iPhaseIntervalCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stSavedData.stPRPDInfo.iQuantificationAmp = PRPSMapNS::QUANTIFICATION_AMP;

    //    QVector< double > vecPRPSVideoData = m_pChart->videoData();
    stSavedData.stPRPDInfo.iPowerFreCycleCount = 0;//vecPRPSVideoData.size() / stSavedData.stPRPDInfo.iPhaseIntervalCount;//temply

    memset(stSavedData.stPRPDInfo.ucaDischargeTypeProb, 0, sizeof(stSavedData.stPRPDInfo.ucaDischargeTypeProb));

    stSavedData.stPRPDInfo.eDataSign = (PRPSMapNS::MapDataSign) m_eSpectrumState;

    stSavedData.stPRPDInfo.eGainType = DataFileNS::GAIN_TYPE_DB;
    stSavedData.stPRPDInfo.sGain = -HFCT::GAIN_VALUES[m_eGain];

    stSavedData.stPRPDInfo.eSyncSource = (DataFileNS::SyncSource)(m_eSyncSource + 1);
    stSavedData.stPRPDInfo.ucSyncState = m_eSyncState;
    stSavedData.stPRPDInfo.fSyncFreq = -1;
    stSavedData.stPRPDInfo.isAccumulated = true/*m_bIsAccumulation*/;//录屏时，目前累积必须打开，这个使用m_isAccumulation，由于异步保存，会导致状态不一致，因此暂时写死为true，后续需求变更后需要相应修改

    QVector< double > vecPRPSVideoData = m_pChart->videoData();
    stSavedData.stPRPDInfo.ucPeriodCount = vecPRPSVideoData.size() / PRPSMapNS::PHASE_INTERVAL_CNT;//temply
    stSavedData.stPRPDInfo.fAnalysisThreshold = m_pChart->prpsThreshold();
}

void HFCTPrpsView::setPRPDMapInfo(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.stPRPDInfo.eAmpUnit = DataFileNS::AMP_UNIT_DB;
    stSavedData.stPRPDInfo.fAmpLowerLimit = HFCT::GAIN_VALUES[m_eGain];
    stSavedData.stPRPDInfo.fAmpUpperLimit = HFCT::GAIN_VALUES[m_eGain] + HFCT::GAIN_BASE;

    stSavedData.stPRPDInfo.eBandWidth = DataFileNS::BAND_DEFAULT;

    //    UHF::bandwidth2FreqRange(stSavedData.stPRPDInfo.fFrequencyMin, stSavedData.stPRPDInfo.fFrequencyMax, eBandWidth);//temply

    stSavedData.stPRPDInfo.iSingleCycleInterval = SystemSetService::instance()->getPRPSSampleInterval();
    stSavedData.stPRPDInfo.iPhaseIntervalCount = PRPSMapNS::PHASE_INTERVAL_CNT;
    stSavedData.stPRPDInfo.iQuantificationAmp = PRPSMapNS::QUANTIFICATION_AMP;

    stSavedData.stPRPDInfo.iPowerFreCycleCount = m_pChart->prpdPeriodCount();

    memset(stSavedData.stPRPDInfo.ucaDischargeTypeProb, 0, sizeof(stSavedData.stPRPDInfo.ucaDischargeTypeProb));

    stSavedData.stPRPDInfo.eDataSign = (PRPSMapNS::MapDataSign) m_eSpectrumState;

    stSavedData.stPRPDInfo.eGainType = DataFileNS::GAIN_TYPE_DB;

    stSavedData.stPRPDInfo.sGain = -HFCT::GAIN_VALUES[m_eGain];

    stSavedData.stPRPDInfo.eSyncSource = (DataFileNS::SyncSource)(m_eSyncSource + 1);

    stSavedData.stPRPDInfo.ucSyncState = m_eSyncState;

    stSavedData.stPRPDInfo.fSyncFreq = -1;

    stSavedData.stPRPDInfo.isAccumulated = m_iAccumulationTime;

    stSavedData.stPRPDInfo.ucPeriodCount = m_pChart->prpdPeriodCount();
    stSavedData.stPRPDInfo.fAnalysisThreshold = m_pChart->prpsThreshold();

    return;
}

void HFCTPrpsView::setPRPDData(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.vecPRRepeatyData.clear();
    int iDataCnt =  stSavedData.stPRPDInfo.iPhaseIntervalCount * stSavedData.stPRPDInfo.iQuantificationAmp;

    UINT16 *pusPulsePRTotalCnt = new UINT16[iDataCnt];
    memset(pusPulsePRTotalCnt, 0, iDataCnt *sizeof(UINT16));
    UINT8 *pucDataColor = new UINT8[3*iDataCnt];
    UINT16 *pusDataDischargeCnt = new UINT16[iDataCnt];

    QVector< qint16 > vecPRPDData = m_pChart->prpdData();

    int iAmpAreaCnt = vecPRPDData.size() / stSavedData.stPRPDInfo.iPhaseIntervalCount;
    dbg_info("iAmpAreaCnt is %d\n", iAmpAreaCnt);
    int iPhaseShitStep = m_iPhaseAlias / (360 / stSavedData.stPRPDInfo.iPhaseIntervalCount);
    dbg_info(" iPhaseShitStep is %d\n", iPhaseShitStep);

    for(int  j = 0; j < stSavedData.stPRPDInfo.iPhaseIntervalCount; j ++)
    {
        for(int  i = 0; i< iAmpAreaCnt; i ++)
        {
            int iNewPhaseIndex = (j + iPhaseShitStep) % stSavedData.stPRPDInfo.iPhaseIntervalCount;

            qint16 sPRPD = vecPRPDData.at(i*stSavedData.stPRPDInfo.iPhaseIntervalCount + j);
            if(sPRPD <= 0)
            {
                sPRPD = 0;
            }
            pusPulsePRTotalCnt[iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + i] = sPRPD;

            QRgb color = m_pChart->prpdDataColor(i,j);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i] = qRed(color);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i+1] = qGreen(color);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i+2] = qBlue(color);
            //discharge cnt of each prpd data
            pusDataDischargeCnt[iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + i] = m_pChart->prpdDataPulseCnt(i,j);
        }
    }
    double *pdPRRepeatyProb;
    pdPRRepeatyProb = new double[iDataCnt];
    memset(pdPRRepeatyProb, 0, iDataCnt *sizeof(double));


    for(int  i = 0; i < iDataCnt; i ++)
    {
        pdPRRepeatyProb[i] = (double)pusPulsePRTotalCnt[i] / (double)stSavedData.stPRPDInfo.iPowerFreCycleCount;
        pdPRRepeatyProb[i] = pdPRRepeatyProb[i] / (20.0 / 1000.0);
        stSavedData.vecPRRepeatyData.append(pdPRRepeatyProb[i]);

        //rgb of each prpd data
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i]);
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i+1]);
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i+2]);

        //discharge cnt of each prpd data
        stSavedData.vecPRPDDataDischargeCnt.append(pusDataDischargeCnt[i]);

    }
    delete [] pdPRRepeatyProb;
    delete [] pusPulsePRTotalCnt;
    delete [] pucDataColor;
    delete [] pusDataDischargeCnt;
}

void HFCTPrpsView::setPRPSData(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.vecPRPSData.clear();
    QVector< double > vecPRPSData = m_pChart->prpsData();
    int iDataPointNum = 0;
    //step5 set map data
    iDataPointNum = vecPRPSData.size();//ex 50*60
    dbg_info("iDataPointNum is %d\n", iDataPointNum);

    int iPhaseShitStep = m_iPhaseAlias / (360 / stSavedData.stPRPSInfo.iPhaseIntervalCount);
    dbg_info("iPhaseShitStep is %d\n", iPhaseShitStep);

    int iRealPeriod = iDataPointNum / stSavedData.stPRPSInfo.iPhaseIntervalCount;
    dbg_info("iRealPeriod is %d\n", iRealPeriod);
    if(iRealPeriod > 50)//todo hard code
    {
        iRealPeriod = 50;
    }

    QVector< double > vecPRPSPhaseShitedData;
    vecPRPSPhaseShitedData.clear();
    double *pdPRPSShiftedData = new double[stSavedData.stPRPSInfo.iPhaseIntervalCount * stSavedData.stPRPSInfo.iPowerFreCycleCount];
    UINT8 *pucPRPSDataColor = new UINT8[3*stSavedData.stPRPSInfo.iPhaseIntervalCount * stSavedData.stPRPSInfo.iPowerFreCycleCount];
    memset(pdPRPSShiftedData, 0, sizeof(double) * stSavedData.stPRPSInfo.iPhaseIntervalCount * stSavedData.stPRPSInfo.iPowerFreCycleCount);
    memset(pucPRPSDataColor, 0, sizeof(UINT8) * 3* stSavedData.stPRPSInfo.iPhaseIntervalCount * stSavedData.stPRPSInfo.iPowerFreCycleCount);


    int iPeriodIndex = 0;
    for( int i = 0; i < iRealPeriod; ++i )
    {
        for( int j = 0; j < stSavedData.stPRPSInfo.iPhaseIntervalCount; ++j )
        {
            QColor color = m_pChart->prpsDataColor(i,j);

            if(iRealPeriod < stSavedData.stPRPSInfo.iPowerFreCycleCount)
            {
                iPeriodIndex =  stSavedData.stPRPSInfo.iPowerFreCycleCount + i - iRealPeriod;
            }
            else
            {
                iPeriodIndex = i;
            }


            //dbg_info("%d, %d:red is %d, green is %d, blue is %d\n", i,j,color.red(),color.green(),color.blue());
            int iNewPhaseIndex = (j + iPhaseShitStep) % stSavedData.stPRPSInfo.iPhaseIntervalCount;
            pdPRPSShiftedData[iPeriodIndex*stSavedData.stPRPSInfo.iPhaseIntervalCount + iNewPhaseIndex] = vecPRPSData.at(i*stSavedData.stPRPSInfo.iPhaseIntervalCount + j);

            //rgb of each prps data
            pucPRPSDataColor[3*iPeriodIndex*stSavedData.stPRPSInfo.iPhaseIntervalCount + 3*iNewPhaseIndex] = color.red();
            pucPRPSDataColor[3*iPeriodIndex*stSavedData.stPRPSInfo.iPhaseIntervalCount + 3*iNewPhaseIndex+1] = color.green();
            pucPRPSDataColor[3*iPeriodIndex*stSavedData.stPRPSInfo.iPhaseIntervalCount + 3*iNewPhaseIndex+2] = color.blue();
        }
    }

    for( long int i = 0; i < stSavedData.stPRPSInfo.iPhaseIntervalCount * stSavedData.stPRPSInfo.iPowerFreCycleCount; ++i )
    {
        stSavedData.vecPRPSData.append(pdPRPSShiftedData[i]);

        //rgb of each prps data
        stSavedData.vecPRPSDataColor.append(pucPRPSDataColor[3*i]);
        stSavedData.vecPRPSDataColor.append(pucPRPSDataColor[3*i+1]);
        stSavedData.vecPRPSDataColor.append(pucPRPSDataColor[3*i+2]);
    }

    delete [] pdPRPSShiftedData;
    delete [] pucPRPSDataColor;
}

void HFCTPrpsView::setPRPDVideoData(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.vecPRRepeatyData.clear();
    int iDataCnt =  stSavedData.stPRPDInfo.iPhaseIntervalCount * stSavedData.stPRPDInfo.iQuantificationAmp;

    UINT16 *pusPulsePRTotalCnt = new UINT16[iDataCnt];
    memset(pusPulsePRTotalCnt, 0, iDataCnt *sizeof(UINT16));
    UINT8 *pucDataColor = new UINT8[3*iDataCnt];
    UINT16 *pusDataDischargeCnt = new UINT16[iDataCnt];

    QVector< qint16 > vecPRPDData = m_pChart->prpdData();

    int iPeriodCnt = vecPRPDData.size() / stSavedData.stPRPDInfo.iPhaseIntervalCount;
    dbg_info("iPeriodCnt is %d\n", iPeriodCnt);
    int iPhaseShitStep = m_iPhaseAlias / (360 / stSavedData.stPRPDInfo.iPhaseIntervalCount);
    dbg_info("iPhaseShitStep is %d\n", iPhaseShitStep);

    for(int  i = 0; i< iPeriodCnt; i ++)
    {
        for(int  j = 0; j < stSavedData.stPRPDInfo.iPhaseIntervalCount; j ++)
        {
            int iNewPhaseIndex = (j + iPhaseShitStep) % stSavedData.stPRPDInfo.iPhaseIntervalCount;

            qint16 sPRPD = vecPRPDData.at(i*stSavedData.stPRPDInfo.iPhaseIntervalCount + j);
            if(sPRPD <= 0)
            {
                sPRPD = 0;
            }
            pusPulsePRTotalCnt[iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + i] = sPRPD;

            QRgb color = m_pChart->prpdDataColor(i,j);
            //rgb of each prpd data
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i] = qRed(color);//vecDataColor.at(i*stSavedData.stPRPDInfo.iPhaseIntervalCount + j);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i+1] = qGreen(color);//vecDataColor.at(i*stSavedData.stPRPDInfo.iPhaseIntervalCount + j+1);
            pucDataColor[3*iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + 3*i+2] = qBlue(color);//vecDataColor.at(i*stSavedData.stPRPDInfo.iPhaseIntervalCount + j+2);
            //if(qRed(color) !=0 || qGreen(color) !=0 || qBlue(color) !=0)
            //dbg_info("%d, %d:red is %d, green is %d, blue is %d\n", i, j, qRed(color),qGreen(color),qBlue(color));

            //discharge cnt of each prpd data
            pusDataDischargeCnt[iNewPhaseIndex*stSavedData.stPRPDInfo.iQuantificationAmp + i] = m_pChart->prpdDataPulseCnt(i,j);//vecPRPDDataDischargeCnt.at(i*stSavedData.stPRPDInfo.iPhaseIntervalCount + j);
            //            if(m_pChart->prpdDataPulseCnt(i,j) > 0)
            //                dbg_info("m_pChart->prpdDataPulseCnt(%d,%d) is %d\n", i,j,m_pChart->prpdDataPulseCnt(i,j));
        }
    }
    double *pdPRRepeatyProb;
    pdPRRepeatyProb = new double[iDataCnt];
    memset(pdPRRepeatyProb, 0, iDataCnt *sizeof(double));
    quint32 uiPRPDRealPeriod = stSavedData.stPRPSInfo.uiRecordPRPSCycleCount;
    for(int  i = 0; i < iDataCnt; i ++)
    {
        pdPRRepeatyProb[i] = (double)pusPulsePRTotalCnt[i] / (double)uiPRPDRealPeriod;
        pdPRRepeatyProb[i] = pdPRRepeatyProb[i] / (20.0 / 1000.0);
        stSavedData.vecPRRepeatyData.append(pdPRRepeatyProb[i]);

        //rgb of each prpd data
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i]);
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i+1]);
        stSavedData.vecPRPDDataColor.append(pucDataColor[3*i+2]);

        //discharge cnt of each prpd data
        stSavedData.vecPRPDDataDischargeCnt.append(pusDataDischargeCnt[i]);
    }
    delete [] pdPRRepeatyProb;
    delete [] pusPulsePRTotalCnt;
    delete [] pucDataColor;
    delete [] pusDataDischargeCnt;
}

void HFCTPrpsView::setPRPSVideoData(HFCTPRPSPRPDDataInfo &stSavedData)
{
    stSavedData.vecPRPSData.clear();
    QVector< double > vecPRPSData = m_pChart->videoData();
    int iDataPointNum = 0;
    //step5 set map data
    iDataPointNum = vecPRPSData.size();//ex 50*60
    dbg_info("iDataPointNum is %d\n", iDataPointNum);//38820

    int iPhaseShitStep = m_iPhaseAlias / (360 / stSavedData.stPRPSInfo.iPhaseIntervalCount);
    dbg_info("iPhaseShitStep is %d\n", iPhaseShitStep);//0

    int iPeriod = iDataPointNum / stSavedData.stPRPSInfo.iPhaseIntervalCount;
    dbg_info("iPeriod is %d\n", iPeriod); //647

    QVector< double > vecPRPSPhaseShitedData;
    vecPRPSPhaseShitedData.clear();
    double *pdPRPSShiftedData = new double[iDataPointNum];// 3000*60
    UINT8 *pucPRPSDataColor = new UINT8[3*iDataPointNum];

    for( int i = 0; i < iPeriod; ++i )
    {
        for( int j = 0; j < stSavedData.stPRPSInfo.iPhaseIntervalCount; ++j )
        {
#if 0
            QColor color = m_pChart->prpsDataColor(i%50,j);
#else
            float fData = vecPRPSData.at(i*stSavedData.stPRPSInfo.iPhaseIntervalCount + j);
            int iMax = HFCT::GAIN_BASE + HFCT::GAIN_VALUES[m_eGain];
            float fRange = iMax - HFCT::CHART_MIN_VALUE;
            float fRatio = fData / fRange;
            QColor color = m_pChart->colorForPercent(fRatio);
#endif
            //dbg_info("%d, %d:red is %d, green is %d, blue is %d\n", i,j,color.red(),color.green(),color.blue());
            int iNewPhaseIndex = (j + iPhaseShitStep) % stSavedData.stPRPSInfo.iPhaseIntervalCount;
            pdPRPSShiftedData[i*stSavedData.stPRPSInfo.iPhaseIntervalCount + iNewPhaseIndex] = vecPRPSData.at(i*stSavedData.stPRPSInfo.iPhaseIntervalCount + j);

            //rgb of each prps data
            pucPRPSDataColor[3*i*stSavedData.stPRPSInfo.iPhaseIntervalCount + 3*iNewPhaseIndex] = color.red();
            pucPRPSDataColor[3*i*stSavedData.stPRPSInfo.iPhaseIntervalCount + 3*iNewPhaseIndex+1] = color.green();
            pucPRPSDataColor[3*i*stSavedData.stPRPSInfo.iPhaseIntervalCount + 3*iNewPhaseIndex+2] = color.blue();
        }
    }

    for( int i = 0; i < iDataPointNum; ++i )
    {
        stSavedData.vecPRPSData.append(pdPRPSShiftedData[i]);

        //rgb of each prps data
        stSavedData.vecPRPSDataColor.append(pucPRPSDataColor[3*i]);
        stSavedData.vecPRPSDataColor.append(pucPRPSDataColor[3*i+1]);
        stSavedData.vecPRPSDataColor.append(pucPRPSDataColor[3*i+2]);
    }

    delete [] pdPRPSShiftedData;
    delete [] pucPRPSDataColor;
}

/************************************************
 * 函数名   : composeSavedData
 * 输入参数 : stationName---站名
 *          deviceName---设备名
 * 输出参数 : stSavedData---需要保存的数据
 * 返回值   : NULL
 * 功能     : 组织需要保存的数据
 ************************************************/
void HFCTPrpsView::composeSavedData(HFCTPRPSPRPDDataInfo &stSavedData)
{
    diagDataInfo(true);

    setPRPSMapHead(stSavedData);

    setPRPDMapHead(stSavedData);

    setPRPSMapInfo(stSavedData);

    setPRPDMapInfo(stSavedData);

    setPRPSData(stSavedData);

    setPRPDData(stSavedData);
}

/*************************************************
功能： 保存数据
返回：
    保存结果
*************************************************************/
QString HFCTPrpsView::saveDataToFile()
{
    // 弹出文件备注框
    showFileCommentBox();

    HFCTPRPSPRPDDataInfo stHFCTPRPSPRPDDataInfo; // 存放图谱数据用于保存的结构体
    composeSavedData(stHFCTPRPSPRPDDataInfo);

    QString qsSavedPath;
    getDataFilePath(qsSavedPath);
    qDebug()<<"HFCTPrpsView::saveDataToFile, qsSavedPath:"<<qsSavedPath;

    HFCTPRPSAndPRPDDataSave cHFCTPRPSAndPRPDDataSave;
    //QString qsDataFile = cHFCTPRPSAndPRPDDataSave.saveData( (void*)&stHFCTPRPSPRPDDataInfo,qsSavedPath );

    QString qsDataFile;
    if ( 0/*m_bSaveData*/ )
    {
        //qsDataFile = cHFCTPRPSAndPRPDDataSave.saveData( m_qSaveTime, (void*)&stHFCTPRPSPRPDDataInfo,qsSavedPath );
    }
    else
    {
        qsDataFile = cHFCTPRPSAndPRPDDataSave.saveData( (void*)&stHFCTPRPSPRPDDataInfo,qsSavedPath );
    }

    //qDebug()<<"HFCTPrpsView::saveDataToFile, testDataFile:"<<qsDataFile;

    return qsDataFile;
}

/*************************************************
功能： 载入数据
*************************************************************/
void HFCTPrpsView::loadData()
{
    QString filePath = DATA_STORAGE_PATH + "/" + HFCT_PRPS_PRPD_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if( !infoList.isEmpty() )
    {
        PlayBackView* pView = new PlayBackView( filePath, HFCT_VIEW_CONFIG_TRANSLATE(HFCT::TEXT_PRPD_PRPS_SPECTRUM), HFCT_VIEW_CONFIG_TRANSLATE(HFCT::TEXT_LOAD_DATA) );
        pView->setPngBGColor(Qt::black);
        pView->addPlayback( HFCT_PRPS_PRPD_FILE_NAME_SUFFIX, new HFCTPRPSPlayBackView() );
        connect(pView, SIGNAL(sigEnterPressed()), this, SLOT(hideMoreConfigButtonBar()));
        pView->exec();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
}

/*************************************************
功能： 载入录屏数据
*************************************************************/
void HFCTPrpsView::loadVideoData()
{
    QString filePath = DATA_STORAGE_PATH + "/" + HFCT_PRPS_RECORD_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if( !infoList.isEmpty() )
    {
        PlayBackView* pView = new PlayBackView(filePath, HFCT_VIEW_CONFIG_TRANSLATE(HFCT::TEXT_RECORD_PLAYBACK), HFCT_VIEW_CONFIG_TRANSLATE(HFCT::TEXT_LOAD_DATA));
        pView->addPlayback( HFCT_PRPS_RECORD_FILE_NAME_SUFFIX, new HFCTVideoPlayBackView() );
        pView->addPlayback( HFCT_PRPS_PRPD_FILE_NAME_SUFFIX, new HFCTVideoPlayBackView() );
        connect(pView, SIGNAL(sigEnterPressed()), this, SLOT(hideMoreConfigButtonBar()));
        pView->exec();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
}

/*************************************************
功能： 删除数据
*************************************************************/
void HFCTPrpsView::deleteData()
{
    QStringList nameFilters;
    nameFilters << HFCT_PRPS_PRPD_FILE_NAME_SUFFIX;
    nameFilters << PNG_FILE_NAME_SUFFIX;

    QString filePath = DATA_STORAGE_PATH + "/" + HFCT_PRPS_PRPD_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if( !infoList.isEmpty() )
    {
        DeleteDataView* pView = new DeleteDataView( filePath,
                                                    HFCT_VIEW_CONFIG_TRANSLATE(HFCT::TEXT_DELETE_DATA),
                                                    nameFilters
                                                    );
        pView->setRelatedSuffix(HFCT_PRPS_PRPD_FILE_NAME_SUFFIX, QStringList(".png"));
        pView->exec();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
}

/*************************************************
功能： 删除prps录屏数据
*************************************************************/
void HFCTPrpsView::deleteVideoData()
{
    QStringList nameFilters;
    nameFilters << HFCT_PRPS_RECORD_FILE_NAME_SUFFIX;
    nameFilters << HFCT_PRPS_PRPD_FILE_NAME_SUFFIX;

    QString filePath = DATA_STORAGE_PATH + "/" + HFCT_PRPS_RECORD_FOLDER;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(QDir::NoDotAndDotDot | QDir::AllEntries);

    if( !infoList.isEmpty() )
    {
        DeleteDataView* pView = new DeleteDataView( filePath,
                                                    QObject::trUtf8("Delete Record"),
                                                    nameFilters
                                                    );
        pView->setRelatedSuffix("", QStringList(""));
        pView->exec();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
    }
}

/*************************************************
输入参数: bIsSampling---是否正在采样的标志
功能： 设置采样按钮文本
*************************************************************/
void HFCTPrpsView::setSampleBtnText( bool bIsSampling )
{
    if( bIsSampling )
    {
        m_pSampleBtn->setTitle( HFCT_VIEW_CONFIG_TRANSLATE( HFCT::TEXT_STOP ) );
    }
    else
    {
        m_pSampleBtn->setTitle( HFCT_VIEW_CONFIG_TRANSLATE( HFCT::TEXT_RUN ) );
    }
}

/************************************************
 * 功能: 绑定停止录制时发出的信号
 ************************************************/
void HFCTPrpsView::onRecordFinished( void )
{
    stopSample();
    //    m_pLoadingWidget = new TextLoadingView( QObject::trUtf8("Saving ...") );
    //    m_pLoadingWidget->show();
    m_pChart->model()->stopRecording();

    if(m_pChart->videoData().isEmpty())
    {
        //        m_pLoadingWidget->close();
        MsgBox::warning("", QObject::trUtf8("No data!"));
        stopRecord();       // 停止录制
        startSample();  // 恢复采样
    }
    else
    {
        m_pLoadingWidget = new TextLoadingView( QObject::trUtf8("Saving ...") );
        m_pLoadingWidget->show();
        m_SaveVideoFuture = QtConcurrent::run( this,&HFCTPrpsView::saveCrossThread );
    }

    if(m_bAccumulationChangedByRecord)
    {
        // 将按键置为累积选项
        //((RadioButton*)buttonBar()->button( BUTTON_HFCT_ACCUMULATION ))->setValue( 0 );
        m_bAccumulationChangedByRecord = false;
    }

    ShutDownPower::getInstance()->start();   // 开始定时关机
}

/*************************************************
功能： 跨线程保存
*************************************************************/
void HFCTPrpsView::saveCrossThread( void )
{
    m_strVideoFile = saveVideoData();
    bool bSuccess = !m_strVideoFile.isEmpty();     // 保存video数据

    QApplication::postEvent(this,new SaveHFCTPRPSVideoDataEvent(bSuccess, "save video data done"));
}

/************************************************
 * 函数名   : composeSavedVideoData
 * 输入参数 : stationName---站名
 *          deviceName---设备名
 * 输出参数 : stSavedData---需要保存的数据
 * 返回值   : NULL
 * 功能     : 组织需要保存的数据
 ************************************************/
void HFCTPrpsView::composeSavedVideoData(const QString &stationName, const QString& deviceName, HFCTPRPSPRPDDataInfo &stSavedData)
{
    setPRPSMapHead(stSavedData);

    setPRPDMapHead(stSavedData);

    setPRPSMapVideoInfo(stSavedData);

    setPRPDMapVideoInfo(stSavedData);

    setPRPSVideoData(stSavedData);

    setPRPDVideoData(stSavedData);
}

/*************************************************
功能： 保存video数据
返回：
    保存结果
*************************************************************/
QString HFCTPrpsView::saveVideoDataToFile(const QString &strStation, const QString &strDevice)
{
    HFCTPRPSPRPDDataInfo stHFCTPRPSPRPDDataInfo; // 存放图谱数据用于保存的结构体
    composeSavedVideoData(strStation, strDevice, stHFCTPRPSPRPDDataInfo);

    QString qsSavedPath;
    getVideoDataFilePath(qsSavedPath);
    qDebug()<<"HFCTPrpsView::saveVideoDataToFile, qsSavedPath:"<<qsSavedPath;

    HFCTPRPSAndPRPDDataSave cHFCTPRPSAndPRPDDataSave;
    cHFCTPRPSAndPRPDDataSave.setSaveVideoData(true);
    QString qsDataFile = cHFCTPRPSAndPRPDDataSave.saveData( (void*)&stHFCTPRPSPRPDDataInfo,qsSavedPath );
    qDebug()<<"HFCTPrpsView::saveVideoDataToFile, testDataFile:"<<qsDataFile;

    return qsDataFile;
}

/*************************************************
功能： 保存video数据
返回：
    保存后的文件名
*************************************************************/
QString HFCTPrpsView::saveVideoData( void )
{
    return saveVideoDataToFile("","");
}

/************************************************
 * 功能: 绑定取消录制时发出的信号
 ************************************************/
void HFCTPrpsView::onRecordCanceled( void )
{
    stopRecord();       // 停止录制
    if(m_bAccumulationChangedByRecord)
    {
        // 将按键置为累积选项
        //((RadioButton*)buttonBar()->button( BUTTON_HFCT_ACCUMULATION ))->setValue( 0 );
        m_bAccumulationChangedByRecord = false;
    }

    ShutDownPower::getInstance()->start();   // 开始定时关机
}

/*************************************************
功能： 启动录制
入参：iPeriodAll -- 录制总周期数
     iInterval -- 周期间隔
*************************************************************/
void HFCTPrpsView::startRecord( qint32 iPeriodAll,qint32 iInterval )
{
    ShutDownPower::getInstance()->stop();   // 停止定时关机
    if( m_pRecordView == NULL )
    {
        m_pRecordView = new PrpsRecordView( iPeriodAll,iInterval,this );
        connect( m_pRecordView,SIGNAL(sigRecordFinished()),this,SLOT(onRecordFinished()) );
        connect( m_pRecordView,SIGNAL(sigRecordCanceled()),this,SLOT(onRecordCanceled()) );
        m_pRecordView->setGeometry( buttonBar()->geometry() );
        m_pRecordView->show();

        m_pChart->setRecording(true);
        m_pChart->setPrpsAccumulateEnabled( true );         // prps累积使能打开
        //m_pChart->setBufferSize( iPeriodAll * PHASE_CNT );  // 设置缓存大小
    }
    else
    {
        //not valid
    }

    /*
      1.T95沿用T90习惯，开启录屏后5s后时间开始启动
      2.每100ms采集一组数据，丢弃前50组数据
    */
    //Module::mSleep(PERIOD_CNT * SystemSetService::instance()->getPRPSSampleInterval());

    m_pChart->model()->startRecording();
    m_pChart->model()->clear();            //清掉缓存，解决实际保存的数据包括了等待时间段内的数据
    m_bIsRecord = true;
}

/*************************************************
功能： 停止录制
*************************************************************/
void HFCTPrpsView::stopRecord( void )
{
    deleteRecordView();

    m_pChart->setRecording(false);
    m_pChart->model()->stopRecording();
    m_pChart->clearData();
    m_bIsRecord = false;
    m_uiRecordCount = 0;
}

/*************************************************
功能： 清除录制界面
*************************************************************/
void HFCTPrpsView::deleteRecordView( void )
{
    if( m_pRecordView != NULL )
    {
        m_pRecordView->close();
        delete m_pRecordView;
        m_pRecordView = NULL;
    }
    else
    {
        // recordView is NULL
    }
}

/*************************************************
 * 功能：诊断数据
 * 输入参数：
 *      bSave：是否为保存操作的逻辑，缺省为false
 * ***********************************************/
void HFCTPrpsView::diagDataInfo(bool bSave)
{
    if(SystemSet::RT_DIAG_ON != SystemSetService::instance()->getRealtimeDiagnosisSwitch())
    {
        return;
    }

    if(bSave)
    {
        if(m_pChart->isLocalDiagnosisEnable())
        {
            PRPSDiagInfo stDiagInfo;
            stDiagInfo.qvtDataIn.clear();
            stDiagInfo.dThresholdDbVal = m_pChart->prpsThreshold();
            stDiagInfo.dSpecMaxVal = m_pChart->rangeMax();
            stDiagInfo.dSpecMinVal = m_pChart->rangeMin();

            stDiagInfo.qvtDataIn = m_pChart->prpsDiagData(stDiagInfo.iPeriodNum, stDiagInfo.iPhaseNum);

            DiagResultInfo stRetInfo;
            DiagnosisManager::instance()->diagPRPSDataDirectly(stDiagInfo, stRetInfo);

            DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
            stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stRetInfo.stDiagRet.defectLevel);
            stDiagDisplayInfo.qstrPDDesInfo = stRetInfo.qstrPDDescription;
            stDiagDisplayInfo.qstrPDSignalInfos = stRetInfo.qstrPDSignalTypeInfos;

            if(m_pChart)
            {
                m_pChart->setDiagRet(stDiagDisplayInfo);
            }
        }
    }
    else
    {
        if(m_pChart->isLocalDiagnosisEnable())
        {
            if(0 == m_qui8RealDiagInterval % View::REAL_DIAGNOSIS_INTERVAL)
            {
                PRPSDiagInfo stDiagInfo;
                stDiagInfo.qvtDataIn.clear();
                stDiagInfo.dThresholdDbVal = m_pChart->prpsThreshold();
                stDiagInfo.dSpecMaxVal = m_pChart->rangeMax();
                stDiagInfo.dSpecMinVal = m_pChart->rangeMin();

                stDiagInfo.qvtDataIn = m_pChart->prpsDiagData(stDiagInfo.iPeriodNum, stDiagInfo.iPhaseNum);
                DiagnosisManager::instance()->diagPRPSData(stDiagInfo);
            }
            ++m_qui8RealDiagInterval;
        }
    }

    return;
}

/****************************************************
 * 功能：保存操作
 * **************************************************/
void HFCTPrpsView::pressSaveData()
{
    //停止采集
    stopSample();

    QString strFile = saveDataToFile();

    QFileInfo fileInfo(strFile);

    QString strText = fileInfo.fileName();
    QString qstrDiagRetInfo = m_pChart->getDiagRetInfo();
    if(!qstrDiagRetInfo.isEmpty())
    {
        strText += QString("\n%1").arg(qstrDiagRetInfo);
    }

    showSaveInformation(strText, strFile);

    m_pChart->clearData();
    //开始采集
    startSample();

    return;
}
