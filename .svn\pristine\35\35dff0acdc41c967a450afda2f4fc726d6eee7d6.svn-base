/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：ColorBlt.h
*
* 初始版本：1.0
* 作者：邵震宇
* 修改日期：2016年12月5日
* 摘要：定义全局使用的颜色对照表
* 当前版本：1.0
*/

#ifndef COLORBLT_H
#define COLORBLT_H

namespace ColorBlt {

const int RGB_ARRARY_WIDTH = 64;        // RGB数组的宽度
const int RGB_ARRARY_HEIGHT = 3;        // RGB数组的高度

const unsigned char colorblt[RGB_ARRARY_WIDTH][RGB_ARRARY_HEIGHT] =               // 用于绘制AE下飞行和相位两种图谱的数据颜色对照表
{
{0, 0, 0},
{23, 0, 0},
{35, 0, 0},
{47, 0, 0},
{59, 0, 0},
{70, 0, 0},
{83, 0, 0},
{94, 0, 0},
{109, 0, 0},
{124, 0, 0},
{139, 0, 0},
{153, 0, 0},
{165, 0, 0},
{178, 0, 0},
{190, 0, 0},
{202, 0, 0},
{214, 0, 0},
{225, 0, 0},
{234, 0, 0},
{243, 1, 0},
{250, 5, 0},
{253, 11, 0},
{255, 20, 0},
{255, 30, 0},
{255, 40, 0},
{255, 50, 0},
{255, 60, 0},
{255, 70, 0},
{255, 80, 0},
{255, 91, 0},
{255, 102, 0},
{255, 114, 0},
{255, 125, 0},
{255, 137, 0},
{255, 148, 0},
{255, 160, 0},
{255, 171, 0},
{255, 183, 0},
{255, 194, 0},
{255, 206, 0},
{255, 221, 0},
{255, 223, 0},
{255, 225, 0},
{255, 226, 0},
{255, 228, 0},
{255, 230, 0},
{255, 231, 0},
{255, 233, 0},
{255, 235, 0},
{255, 237, 0},
{255, 238, 1},
{255, 240, 1},
{255, 241, 1},
{255, 243, 1},
{255, 244, 2},
{255, 246, 2},
{255, 247, 3},
{255, 248, 4},
{255, 249, 5},
{255, 250, 6},
{255, 251, 7},
{255, 252, 8},
{255, 253, 10},
{255, 254, 11}


        //{0,0,0},
        //{23,0,64},
        //{35,0,64},
        //{47,0,0},
        //{59,0,0},
        //{70,0,0},
        //{83,0,0},
        //{94,0,0},
        //{109,0,0},
        //{124,0,0},
        //{139,0,0},
        //{153,0,0},
        //{165,0,0},
        //{178,0,0},
        //{190,0,0},
        //{202,0,0},
        //{214,0,0},
        //{225,0,0},
        //{234,0,0},
        //{243,1,0},
        //{250,5,0},
        //{255,11,0},
        //{255,20,0},
        //{255,30,0},
        //{255,40,0},
        //{255,50,0},
        //{255,60,0},
        //{255,70,0},
        //{255,80,0},
        //{255,91,0},
        //{255,102,0},
        //{255,114,0},
        //{255,125,0},
        //{255,137,0},
        //{255,148,0},
        //{255,160,0},
        //{255,171,0},
        //{255,183,0},
        //{255,194,0},
        //{255,206,0},
        //{255,221,0},
        //{255,223,0},
        //{255,225,0},
        //{255,226,0},
        //{255,228,0},
        //{255,230,0},
        //{255,231,0},
        //{255,233,0},
        //{255,235,0},
        //{255,237,0},
        //{255,238,1},
        //{255,240,1},
        //{255,241,1},
        //{255,243,1},
        //{255,244,2},
        //{255,246,2},
        //{255,247,3},
        //{255,248,4},
        //{255,249,5},
        //{255,250,6},
        //{255,251,7},
        //{255,252,8},
        //{255,253,10},
        //{255,254,11}
};

}

#endif // COLORBLT_H
