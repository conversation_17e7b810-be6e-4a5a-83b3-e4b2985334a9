#include "pdadownloadtaskview.h"
#include "PDAViewConfig.h"
#include "window/Window.h"
#include "pda/pdaservice.h"
#include "config/ConfigManager.h"
#include "appconfig.h"
#include "PDAUi/PDAUiBean/pdaprogressdialog.h"
#include "controlButton/PopupButton.h"
#include "pdasifttaskview/pdasifttaskview.h"
#include "messageBox/msgbox.h"

typedef enum _PDATaskButton
{
    BUTTON_SELECT_MODE = 0,// 选择模式
    BUTTON_REFRESH,//刷新  1
    BUTTON_DOWNLOAD,//下载  2
    BUTTON_SIFT,    //筛选  3
    BUTTON_ALL_SELECT,//全选  4
}PDATaskButton;

//模式
const ButtonInfo::RadioValueConfig s_ModeCfg =
{
    PDAView::TEXT_MODE_OPTIONS, sizeof(PDAView::TEXT_MODE_OPTIONS)/sizeof(char*)
};

//控制按钮定义
const ButtonInfo::Info s_PDAButtonInfo[] =
{
    //{ BUTTON_SELECT_MODE, { ButtonInfo::RADIO, PDAView::TEXT_MODE, NULL, "", &s_ModeCfg } },//按键模式
    { BUTTON_REFRESH, { ButtonInfo::COMMAND, PDAView::TEXT_REFRESH, NULL, "", NULL } },//刷新
    { BUTTON_DOWNLOAD, { ButtonInfo::COMMAND, PDAView::TEXT_DOWNLOAD, NULL, "", NULL } },//下载
    { BUTTON_SIFT, { ButtonInfo::COMMAND, PDAView::TEXT_SIFT, NULL, "", NULL } },//筛选
    { BUTTON_ALL_SELECT, { ButtonInfo::COMMAND, PDAView::TEXT_ALL_SELECT, NULL, "", NULL } },//全选
};

/*************************************************
功能： 构造函数
输入参数:
    taskInfos -- 任务概要信息
    parent:父控件指针
*************************************************************/
PDADownloadTaskView::PDADownloadTaskView(const QVector<TaskInfo>& taskInfos,QWidget *parent) :
    PDAListView(QObject::trUtf8("Task List"), parent), m_bIsGetTaskInfoing(true)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);

    connect(m_pChart, SIGNAL(sigItemPressedUnderMultil(int)), this, SLOT(onItemClickedUnderMutil(int)));
    m_pChart->setSelectionMode(PDAListChart::MULTIPLE_SELECT);

    // 初始化任务列表信息
    initTaskInfos(taskInfos);

    //创建按钮栏
    /*ListPushButtonBar *pButtonBar = */createButtonBar(PDAView::CONTEXT, s_PDAButtonInfo, sizeof(s_PDAButtonInfo)/sizeof(ButtonInfo::Info));
    //(((PopupButton*)pButtonBar->buttons().at(BUTTON_SELECT_MODE)))->setValue(0);
    //(((PopupButton*)pButtonBar->buttons().at(BUTTON_SELECT_MODE)))->setPopupMode(PopupWidget::SWITCH_MODE);

    //绑定任务下载成功的信号
    connect(PDAService::instance(), SIGNAL(sigTaskDownloaded(UINT32)), this, SLOT(onTaskDownloaded(UINT32)));

    connect(PDAService::instance(), SIGNAL(sigCmsTaskInfoChanged()), this, SLOT(onCmsTaskInfoChanged()));
    connect(PDAService::instance(), SIGNAL(sigQueryListFinished(errorProcess::ReplyCode)), this, SLOT(onQueryListFinished(errorProcess::ReplyCode)));
}

/***********************************************
 * 功能：析构函数
 * *************************************************/
PDADownloadTaskView::~PDADownloadTaskView()
{
    PDAService::instance()->stopRequest();
}

/**************************************
 * 功能：处理显示事件
 * 输入参数：
 *      pEvent：显示事件
 * ************************************/
void PDADownloadTaskView::showEvent(QShowEvent *pEvent)
{
    Q_UNUSED(pEvent);
    QTimer::singleShot(50, this, SLOT(onInitWaitingDialog()));
    return;
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void PDADownloadTaskView::onCommandButtonPressed(int id)
{
    PDAService::instance()->stopRequest();
    switch(id)
    {
    case BUTTON_REFRESH:        // 刷新
    {
        QString strInfo = QObject::trUtf8("Refreshing ...");
        MsgBox *pWaitMsgBox = new MsgBox(MsgBox::INFORMATION);
        connect(PDAService::instance(), SIGNAL(sigQueryListFinished(errorProcess::ReplyCode)), pWaitMsgBox, SLOT(accept()));
        pWaitMsgBox->setInfo("", strInfo, MsgBox::OK);
        pWaitMsgBox->setWindowModality(Qt::ApplicationModal);
        pWaitMsgBox->show();
        refresh();
        break;
    }
    case BUTTON_ALL_SELECT:
    {
        if(m_pChart->isAllItemSelected())
        {
            m_pChart->cancelAllItemSelected();  //取消全选
            buttonBar()->button(BUTTON_ALL_SELECT)->setTitle(PDA_VIEW_CONFIG_TRANSLATE(PDAView::TEXT_ALL_SELECT));
        }
        else
        {
            m_pChart->setAllItemSelected();     // 全选
            buttonBar()->button(BUTTON_ALL_SELECT)->setTitle(PDA_VIEW_CONFIG_TRANSLATE(PDAView::TEXT_ALL_SELECT_CANCEL));
        }
        break;
    }
    case BUTTON_DOWNLOAD:                   // 下载
    {
        downLoadTask();
        break;
    }
    case BUTTON_SIFT:
    {
        PDASiftTaskView* pView = new PDASiftTaskView(true);
        pView->loadDatas(PDAService::instance()->cloudTasksInfos());
        pView->show();

        connect(pView, SIGNAL(sigTaskFilterChanged(TaskFilter&)), this, SLOT(onCloudTaskFiltered(TaskFilter&)));
        connect(pView, SIGNAL(sigCloudTaskFilterCleared()), this, SLOT(onCloudTaskFilterCleared()));
        break;
    }
    default:
        break;
    }
    return;
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void PDADownloadTaskView::onButtonValueChanged( int id, int iValue )
{
    Q_UNUSED(iValue);
    switch( id )
    {
    /*case BUTTON_SELECT_MODE:
    {
        m_pChart->setSelectionMode( (PDAListChart::SelectionMode)iValue );
    }*/
        break;
    default:
        break;
    }
}

/*************************************************
功能： 处理条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void PDADownloadTaskView::onItemClicked( int id )
{
    if(id != PDAListChart::ERROR_SELECTED_INDEX)  // 是否有条目被选中
    {
        buttonBar()->button(BUTTON_ALL_SELECT)->setTitle(PDA_VIEW_CONFIG_TRANSLATE(PDAView::TEXT_ALL_SELECT));
    }
    else
    {
        logWarning("no item select");
    }
    return;
}

/*************************************************
功能： 槽，响应多选模式下，条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void PDADownloadTaskView::onItemClickedUnderMutil( int id )
{
    if(id != PDAListChart::ERROR_SELECTED_INDEX)  // 是否有条目被选中
    {
        buttonBar()->button(BUTTON_ALL_SELECT)->setTitle(PDA_VIEW_CONFIG_TRANSLATE(PDAView::TEXT_ALL_SELECT));
    }
    else
    {
        logWarning("no item select");
    }
    return;
}

/*************************************************
功能： 初始化任务信息
输入参数:
    taskInfos -- 任务概要信息
*************************************************************/
void PDADownloadTaskView::initTaskInfos(const QVector<TaskInfo>& taskInfos)
{
    QList<PDAListChart::ListItemInfo> itemInfos;
    itemInfos.clear();
    m_lItemInfos.clear();

    for(int i = 0, iSize = taskInfos.size(); i < iSize; ++i)
    {
        itemInfos << PDAListChart::ListItemInfo(taskInfos.at(i).strItemName,
                                                taskInfos.at(i).uiTestedCount,
                                                taskInfos.at(i).uiTotalCount,
                                                taskInfos.at(i).isDownloaded);
    }

    m_lItemInfos = itemInfos;
    if(m_pChart)
    {
        m_pChart->addItems(m_lItemInfos);
    }
    return;
}

/*************************************************
功能： 刷新
*************************************************************/
void PDADownloadTaskView::refresh(void)
{
    //重置全选状态
    m_pChart->cancelAllItemSelected();  //取消全选
    buttonBar()->button(BUTTON_ALL_SELECT)->setTitle(PDA_VIEW_CONFIG_TRANSLATE(PDAView::TEXT_ALL_SELECT));

    if(!m_bIsGetTaskInfoing)
    {
        m_pChart->deleteAllItem();
        ConfigInstance *pConfig = ConfigManager::instance()->config();
        pConfig->beginGroup(Module::GROUP_APP);
        QString qsUserName = pConfig->value(APPConfig::KEY_USER_NAME);
        pConfig->endGroup();

        m_bIsGetTaskInfoing = true;
        PDAService::instance()->getTasksInfoFromCloud(qsUserName);
    }

    return;
}

/*************************************************
功能： 下载任务(该接口抽象，仅为过于冗长的代码集中在一个函数内有碍观瞻)
*************************************************************/
void PDADownloadTaskView::downLoadTask(void)
{
    // 没有选中条目
    if (m_pChart->itemsIndexSelected().isEmpty())
    {
        MsgBox::warning("", QObject::trUtf8("Please choose a task!"));
        return;
    }

    QVector<UINT32> qvtTasks;
    qvtTasks.clear();

    QVector<qint32> selectItems = m_pChart->itemsIndexSelected();
    for(int i = 0, iSize = selectItems.size(); i < iSize; ++i)
    {
        qvtTasks << selectItems.at(i);
    }

    PDAProgressDialog* pDialog = new PDAProgressDialog;
    connect(PDAService::instance(), SIGNAL(sigDownloadProgress(int)), pDialog, SLOT(onProgressChanged(int)));
    connect(PDAService::instance(), SIGNAL(sigDownloadFinished(errorProcess::ReplyCode)), pDialog, SLOT(onDownloadFinished(errorProcess::ReplyCode)));

    PDAService::instance()->downloadTasks(qvtTasks);
    PDAProgressDialog::ProgressState eState = (PDAProgressDialog::ProgressState)pDialog->exec();
    if(eState == PDAProgressDialog::PROGRESS_ING)
    {
        PDAService::instance()->stopDownloadTasks();
    }
}

/*************************************************
功能：  通知任务信息列表结果已更新
*************************************************/
void PDADownloadTaskView::onCmsTaskInfoChanged()
{
    m_pChart->deleteAllItem();
    initTaskInfos(PDAService::instance()->cloudTasksInfos());
    return;
}

/*************************************************
功能：  通知任务查询操作结束
*************************************************/
void PDADownloadTaskView::onQueryListFinished(ReplyCode eCode)
{
    m_bIsGetTaskInfoing = false;
    if(eCode != REPLY_SUCCESS_CODE)
    {
        QString strMsg = errorProcess::stateMsgByCode(eCode);
        MsgBox::warning("", strMsg);
    }
    return;
}

/*************************************************
功能：  index -- cms的任务列表的序号
通知序号为index任务已经下载完毕
*************************************************/
void PDADownloadTaskView::onTaskDownloaded( UINT32 index )
{
    if(index < (quint32)m_lItemInfos.size())
    {
        m_lItemInfos[index].m_bIsTicked = true;
        m_pChart->setItemInfo(m_lItemInfos[index], index);
    }
    return;
}

/*************************************************
功能：eCode--下载结果
 通知所有需要下载的任务已经下载完毕
*************************************************/
void PDADownloadTaskView::onTaskDowndFinished( ReplyCode eCode )
{
    emit sigDownloadFinished();
    if(eCode == REPLY_SUCCESS_CODE)
    {
        MsgBox::information("", QObject::trUtf8("Download succeeded."));
    }
    else
    {
        //为了避免出现错误提示，这里统一管理
        //eCode = DOWNLOAD_TASK_ERR;
        QString strMsg = errorProcess::stateMsgByCode(eCode);
        MsgBox::warning("", strMsg);
    }
    return;
}

/*************************************************
功能：filter--过滤条件
将筛选界面返回的过滤条件传给pdaservice模块
*************************************************/
void PDADownloadTaskView::onCloudTaskFiltered(TaskFilter &filter)
{
    //调用pdaservice接口
    PDAService::instance()->setCloudTaskFilter(filter);

    QString strInfo = QObject::trUtf8("Sifting ...");
    MsgBox *pWaitMsgBox = new MsgBox(MsgBox::INFORMATION);
    connect(PDAService::instance(), SIGNAL(sigQueryListFinished(errorProcess::ReplyCode)), pWaitMsgBox, SLOT(accept()));
    pWaitMsgBox->setInfo("", strInfo, MsgBox::OK);
    pWaitMsgBox->setWindowModality(Qt::ApplicationModal);
    pWaitMsgBox->show();
    refresh();
    return;
}

/*************************************************
功能：将筛选界面返回的过滤条件清除
*************************************************/
void PDADownloadTaskView::onCloudTaskFilterCleared()
{
    //调用pdaservice接口
    PDAService::instance()->cleanCloudTaskFilter();

    QString strInfo = QObject::trUtf8("Restoring default ...");
    MsgBox *pWaitMsgBox = new MsgBox(MsgBox::INFORMATION);
    connect(PDAService::instance(), SIGNAL(sigQueryListFinished(errorProcess::ReplyCode)), pWaitMsgBox, SLOT(accept()));
    pWaitMsgBox->setInfo("", strInfo, MsgBox::OK);
    pWaitMsgBox->setWindowModality(Qt::ApplicationModal);
    pWaitMsgBox->show();
    refresh();
    return;
}

/*************************************************
功能：等待下载任务时，生成messagebox提示
*************************************************/
void PDADownloadTaskView::onInitWaitingDialog()
{
    if(this->isActiveWindow() && m_bIsGetTaskInfoing)
    {
        MsgBox* pWaitingMsgBox = new MsgBox(MsgBox::INFORMATION);
        if(pWaitingMsgBox)
        {
            pWaitingMsgBox->setWindowModality(Qt::ApplicationModal);
            pWaitingMsgBox->setInfo("", QObject::trUtf8("Getting the task list ..."), MsgBox::OK);
            connect(PDAService::instance(), SIGNAL(sigQueryListFinished(errorProcess::ReplyCode)), pWaitingMsgBox, SLOT(accept()));
            pWaitingMsgBox->show();
        }
    }

    return;
}
