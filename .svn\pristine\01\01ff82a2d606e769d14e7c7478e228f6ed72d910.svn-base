#include "zigbeeservice.h"


ZigBeeService::ZigBeeService(QObject *parent) : QObject(parent)
{

}

ZigBeeService::~ZigBeeService()
{

}

/*************************************************
功能： 获取UHF数据
输入参数：
        NULL
输出参数：
        pstUHFHFCTData -- 读取的数据
返回：
        是否读取成功
*************************************************/
int ZigBeeService::readUhfData( UHFHFCTData *pstUHFHFCTData )
{
    int iResult = 0;
#ifdef Q_OS_LINUX
    iResult = get_uhfhfct_data( pstUHFHFCTData, UHF_TERMINAL );
#endif
    return iResult;
}

/*************************************************
功能： 设置UHF滤波
输入参数：
        eFilterControl -- UHF滤波方式
输出参数：NULL
返回：
        是否设置成功
*************************************************/
int ZigBeeService::setUhfFilter( UHFFilterControl eFilterControl )
{
    int iResult = 0;
#ifdef Q_OS_LINUX
    iResult = set_uhf_filter( eFilterControl );
#endif
    return iResult;
}

/*************************************************
功能： 设置UHF增益 UHF:0dB增益、20dB增益  HFCT:0dB衰减、-20dB衰减、-40dB衰减、-60dB衰减
输入参数：
        UHFHFCTGain -- 增益枚举
输出参数：NULL
返回：
        是否设置成功
*************************************************/
int ZigBeeService::setUhfGain( UHFHFCTGain eUHFHFCTGain )
{
    int iResult = 0;
#ifdef Q_OS_LINUX
    iResult = set_uhfhfct_gain( eUHFHFCTGain,UHF_TERMINAL );
#endif
    return iResult;
}

/*************************************************
功能： 设置UHF同步源
输入参数：
        eUHFHFCTSync -- 同步源
输出参数：NULL
返回：
        是否设置成功
*************************************************/
int ZigBeeService::setUhfSyncsource( SyncSource eUHFHFCTSync )
{
    int iResult = 0;
#ifdef Q_OS_LINUX
    iResult = set_wireless_syncsource( eUHFHFCTSync, UHF_TERMINAL );
#endif
    return iResult;
}

/*************************************************
功能： 获取HFCT数据
输入参数：
        NULL
输出参数：
        pstUHFHFCTData -- 读取的数据
返回：
        是否读取成功
*************************************************/
int ZigBeeService::readHfctData( UHFHFCTData *pstUHFHFCTData )
{
    int iResult = 0;
#ifdef Q_OS_LINUX
    iResult = get_uhfhfct_data( pstUHFHFCTData, HFCT_TERMINAL );
#endif
    return iResult;
}

/*************************************************
功能： 设置HFCT增益 UHF:0dB增益、20dB增益  HFCT:0dB衰减、-20dB衰减、-40dB衰减、-60dB衰减
输入参数：
        UHFHFCTGain -- 增益枚举
输出参数：NULL
返回：
        是否设置成功
*************************************************/
int ZigBeeService::setHfctGain( UHFHFCTGain eUHFHFCTGain )
{
    int iResult = 0;
#ifdef Q_OS_LINUX
    iResult = set_uhfhfct_gain( eUHFHFCTGain, HFCT_TERMINAL ) ;
#endif
    return iResult;
}

/*************************************************
功能： 设置HFCT同步源
输入参数：
        eUHFHFCTSync -- 同步源
输出参数：NULL
返回：
        是否设置成功
*************************************************/
int ZigBeeService::setHfctSyncsource( SyncSource eUHFHFCTSync )
{
    int iResult = 0;
#ifdef Q_OS_LINUX
    iResult = set_wireless_syncsource( eUHFHFCTSync,HFCT_TERMINAL );
#endif
    return iResult;
}

/*************************************************
功能： 获取电流检测数据
输入参数：
        NULL
输出参数：
        pstCableCurrentData -- 读取的数据
返回：
        是否读取成功，0：成功，其他失败
*************************************************/
int ZigBeeService::readCurrentDetectionData(CableCurrentData* pstCableCurrentData)
{
    int iResult = 0;
#ifdef Q_OS_LINUX
    iResult = get_cable_current_data(pstCableCurrentData);
    //pstCableCurrentData->fGroundCurrent = 156.27f;
    //pstCableCurrentData->fLoadCurrent = 125.146f;
#endif
    return iResult;
}
