#include "guideinfraredplayback.h"
#include <QDebug>

// 回放页面按钮菜单
const InfraredButtonInfo s_InfraredPlayBackButtonInfos[] =
{
    { GuideInfrared::BUTTON_EXIT_PLAY,        &g_InfraredExitPlay },         // add by wujun
    { GuideInfrared::BUTTON_COLOR_TYPE,       &g_InfraredColorButtonInfoBasic, },
    { GuideInfrared::BUTTON_ANALYSE_SHAPE,    &g_InfraredAnalyseShapeBasic, },
    { GuideInfrared::BUTTON_LINE_CURVE,       &g_InfraredCurveButtonInfo, },
    { GuideInfrared::BUTTON_DELETE_ALL_SHAPE, &g_InfraredDelAllButtonInfo, },
    { GuideInfrared::BUTTON_SET_PARAM,        &g_InfraredSetParamButtonInfo, },
    { GuideInfrared::BUTTON_LAST_PAGE,        &g_InfraredLastPageButtonInfo, },
    { GuideInfrared::BUTTON_NEXT_PAGE,        &g_InfraredNextPageButtonInfo, },
};

/*************************************************
函数名： GuideInfraredPlayback(QWidget *parent)
输入参数： parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
GuideInfraredPlayback::GuideInfraredPlayback(QWidget *parent)
    :GuideInfraredViewBase(parent)
{
    InfraredControlButtonBar *pButtonBar = createButtonBar(s_InfraredPlayBackButtonInfos,
                                                           sizeof(s_InfraredPlayBackButtonInfos) / sizeof(InfraredButtonInfo));
    setButtonBar(pButtonBar);

    // 初始禁用“线温度”和“全部删除”按钮
    if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_DELETE_ALL_SHAPE))
    {
        pButton->setEnabled(false);
    }
    if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_LINE_CURVE))
    {
        pButton->setEnabled(false);
    }
}

/*************************************************
函数名： showEvent(QShowEvent *e)
输入参数： e：显示事件
输出参数： NULL
返回值： NULL
功能： 显示事件处理
*************************************************************/
void GuideInfraredPlayback::showEvent(QShowEvent *e)
{
    Q_UNUSED(e);
    m_pButtonBar->activeFirst();
    return;
}

/*************************************************
函数名： keyPressEvent(QKeyEvent *e)
输入参数： e：按键事件
输出参数： NULL
返回值： NULL
功能： 按键事件处理
*************************************************************/
void GuideInfraredPlayback::keyPressEvent(QKeyEvent *e)
{
    if (Qt::Key_Escape == e->key())
    {
        emit sigExit();
    }

    return;
}

/*************************************************************
 * 功能：更新按钮状态
 * ************************************************************/
void GuideInfraredPlayback::updateButtonState()
{
    bool bInfraredEnable = m_eGuideInfraredDisplayMode != GuideInfrared::DISPLAY_DIGITAL_CAMERA;

    if (bInfraredEnable)
    {
        m_pButtonBar->buttons(GuideInfrared::BUTTON_ANALYSE_SHAPE)->setEnabled(true);
        m_pButtonBar->buttons(GuideInfrared::BUTTON_SET_PARAM)->setEnabled(true);
        //m_pButtonBar->buttons(GuideInfrared::BUTTON_DELETE_ALL_SHAPE)->setEnabled(true);
    }
    else
    {
        // 禁用的时候要删除所有分析图形
        m_pGuideInfraredImagingView->deleteAll();
        m_pButtonBar->buttons(GuideInfrared::BUTTON_ANALYSE_SHAPE)->setEnabled(false);
        m_pButtonBar->buttons(GuideInfrared::BUTTON_DELETE_ALL_SHAPE)->setEnabled(false);
        m_pButtonBar->buttons(GuideInfrared::BUTTON_SET_PARAM)->setEnabled(false);
    }
}

/*************************************************
函数名： setData(const unsigned short *rawDataBuf, const FrameInfo &frameInfo, const Params &stParams)
输入参数： rawDataBuf：原始数据缓存
          frameInfo：帧数据信息
          stParams：红外参数信息
输出参数： NULL
返回值： NULL
功能： 设置回放数据
*************************************************************/
void GuideInfraredPlayback::setPlaybackData(const GuideInfraredDataManage::GuidePlaybackData& stGuidePlaybackData)
{
    m_pGuideInfraredImagingView->setPlaybackData(stGuidePlaybackData);
}

void GuideInfraredPlayback::playbackVisibleLightPhoto()
{
    m_pGuideInfraredImagingView->playbackVisibleLightPhoto();
}

void GuideInfraredPlayback::playbackInfraredPhoto()
{
    m_pGuideInfraredImagingView->playbackInfraredPhoto();
}

/******************************************
 * 功能：设置温度信息
 * 输入参数：
 *      stTmpInfo：温度信息
 * *********************************************/
void GuideInfraredPlayback::setTempInfo(const TemperatureInfo &stTmpInfo)
{
    m_pGuideInfraredImagingView->setTempInfo(stTmpInfo);
    return;
}

/*************************************************
输入参数： iType -- 颜色模式
输出参数： NULL
返回值： NULL
功能： 设置颜色模式
*************************************************************/
void GuideInfraredPlayback::setColorType(quint8 iType)
{
    ColorType cType = (ColorType)iType;
    if ( cType < COLOR_TYPE_IRON || cType >= COLOR_TYPE_COUNT  )
    {
        cType = COLOR_TYPE_IRON;
    }
    m_eCurrentPalette = ColorType2GuideColorType(cType);
    m_pGuideInfraredImagingView->setColorType(m_eCurrentPalette, false);
}

/*************************************************
函数名： showTitle(const QString &strFileName)
输入参数： strFileName--文件名
输出参数： NULL
返回值： NULL
功能： 显示回放文件名
*************************************************************/
void GuideInfraredPlayback::showTitle(const QString &strFileName)
{
    m_pGuideInfraredImagingView->showFileName(strFileName);
}

void GuideInfraredPlayback::deleteAllItem()
{
    m_pGuideInfraredImagingView->deleteAll(true);
}

/*************************************************
函数名： onButtonPressed(UINT8 ucID)
输入参数： ucID：按钮ID
输出参数： NULL
返回值： NULL
功能： 按钮响应处理
*************************************************************/
void GuideInfraredPlayback::onButtonPressed(UINT8 ucID)
{
    if ( m_pButtonBar->buttons(ucID) != NULL)
    {
        if ( !m_pButtonBar->buttons(ucID)->isEnabled() )
        {
            qDebug() << "----not enable.";
            return;
        }
    }

    switch (ucID)
    {
    case GuideInfrared::BUTTON_EXIT_PLAY:
    {
        emit sigExit();
    }
        break;

    case GuideInfrared::BUTTON_LAST_PAGE:
    {
        lastPage();
    }
        break;

    case GuideInfrared::BUTTON_NEXT_PAGE:
    {
        nextPage();
    }
        break;

    case GuideInfrared::BUTTON_LINE_CURVE:
    {
        m_pGuideInfraredImagingView->showLineTemperatureCurve();
    }
        break;

    default:
        GuideInfraredViewBase::onButtonPressed(ucID);
        break;
    }
}

/*************************************************
函数名： nextPage()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 下一页
*************************************************************/
void GuideInfraredPlayback::nextPage()
{
    //m_pInfraredImagingView->deleteAll(true);
    emit sigNextPageClicked();
}

/*************************************************
函数名： lastPage()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 上一页
*************************************************************/
void GuideInfraredPlayback::lastPage()
{
    //m_pInfraredImagingView->deleteAll(true);
    emit sigLastPageClicked();
}
