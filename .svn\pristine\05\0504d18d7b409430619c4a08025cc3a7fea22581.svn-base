﻿/*
* Copyright (c) 2016.1，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：aephasedatamap.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年10月24日
* 修改日期：
* 摘要：ae phase map类申明
* 当前版本：1.0
*/

#ifndef AE_PHASE_DATA_MAP_H
#define AE_PHASE_DATA_MAP_H

#include <QObject>
#include <QVector>
#include <QByteArray>

#include "datafiledefine.h"
#include "datamap.h"
#include "aemapdefine.h"
#include "datafile_global.h"

class DATAFILESHARED_EXPORT AEPhaseDataMap : public DataMap
{
public:
    /*************************************************
    功能： 构造函数
    *************************************************************/
    AEPhaseDataMap();

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~AEPhaseDataMap();

    /*************************************************
    功能： 设置图谱的ext information
    输入参数：
            pData -- 指向m图谱的ext information的指针
    *************************************************************/
    void setInfo(const AEMapNS::AEPhaseMapInfo* pMapInfo);

    /*************************************************
    功能： 读取图谱的ext information
    输出参数：
            pInfo -- 指向图谱的ext information的指针
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool getInfo(AEMapNS::AEPhaseMapInfo* pMapInfo);

    /*************************************************
    功能： 设置图谱的data
    输入参数：
            pData -- 指向图谱的data的指针
    *************************************************************/
    void setData(void* pData, void* pDataColor, void* pColorIndex, int iDataCount);

    /*************************************************
    功能： 设置图谱的data
    输入参数：
            pData -- 指向图谱的data的指针
    *************************************************************/
    void setData(void* pData, int iDataCount);

    /*************************************************
    功能： 读取图谱的data
    输出参数：
            pData -- 指向图谱的data的指针
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool getData(void* pData, void *pDataColor, void *pColorIndex, int iDataCount);

    AEMapNS::AEPhaseMapInfo ext();

    void *data();

protected:
    /*************************************************
    功能： 解析map的扩展字段
    输入参数：
            baData -- 图谱数据的xml文本内容
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool parseMapExtXML( XMLDocument * pDoc, const QString &strRootTag );

    /*************************************************
    功能： 解析map的data字段
    输出参数：
            baData -- 图谱数据的xml文本内容
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool parseMapDataXML( XMLDocument * pDoc, const QString &strRootTag );

    /*************************************************
    功能： 生成map扩展部分的xml文本
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool saveMapExtXML(XMLDocument *pDoc, QDomElement &element,bool bCrypt);

    /*************************************************
    功能： 生成map扩展部分的qbytearray文本，方便存储为二进制
    *************************************************************/
    void saveMapExtBinary( QByteArray &baPackage );

    /*************************************************
    功能： 生成map数据部分的xml文本
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool saveMapDataXML(XMLDocument *pDoc, QDomElement &element,bool bCrypt);

    /*************************************************
    功能： 生成map数据部分的qbytearray文本，方便存储为二进制
    *************************************************************/
    void saveMapDataBinary( QByteArray &baPackage );

    /*************************************************
    功能： 获取图谱根节点的标签名
    返回值:
            图谱对应的标签名
    *************************************************************/
    QString mapRootTag();

    /*************************************************
    功能： 解析map除头以外的所有字段
    输入参数：
            bytes -- 图谱数据的二进制文件内容
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    virtual bool parseMapDataBinary(const QByteArray &bytes);

private:
    AEMapNS::AEPhaseMapInfo m_stExt;
    void * m_pAeData;    //AE数据
    void * m_pAeColor;    //AE数据颜色
    void * m_pColorIndex;    //
};

#endif // AE_PHASE_DATA_MAP_H
