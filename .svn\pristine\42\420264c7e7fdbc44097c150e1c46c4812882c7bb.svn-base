﻿#include "phasepaintdata.h"
#include "phasedef.h"

inline int removeSize(int iCurrSize, int iIncrease, int maxSize)
{
    int iRes = iCurrSize + iIncrease - maxSize;
    return iRes > 0 ? iRes : 0;
}

PhasePaintData::PhasePaintData(int iMaxPeriodCount, int iDataCountPerPeriod)
    : m_fLowerBound(0),
      m_fUpperBound(100),
      m_iDataCountPerPeriod(iDataCountPerPeriod),
      m_currentData(iDataCountPerPeriod),
      m_fMaxData(0),
      m_skippedData(iDataCountPerPeriod),
      m_iLastUpdatedDataPeriodIndex(0)
{
    setMaxPeriodCount(iMaxPeriodCount);
}

void PhasePaintData::setMaxPeriodCount(int iCount)
{
    PHASE_ASSERT(iCount > 0)

    bool bUpdated = m_iMaxPeriodCount > iCount;
    m_iMaxPeriodCount = iCount;

    if(bUpdated)
    {
        updateDataInfo(0);
    }
}

void PhasePaintData::setData(const PhaseData &currentData)
{
    m_currentData = currentData;

    if(currentData.isEmpty())
    {
        clear();
    }
    else
    {
        increaseDataInfoList(currentData.periodCount());
        updateDataInfo(0);
        m_iLastUpdatedDataPeriodIndex = 0;
    }
}

void PhasePaintData::clear()
{
    m_currentData.clear();
    m_dataInfos.clear();
    m_skippedData.clear();
    m_iLastUpdatedDataPeriodIndex = 0;
    m_fMaxData = 0.0;
}

void PhasePaintData::appendData(const PhaseData &newData)
{
    if(newData.isEmpty())
    {
        return;
    }

    int iRemoveSize = removeSize(m_currentData.periodCount(), newData.periodCount(), m_iMaxPeriodCount);
    if(iRemoveSize > 0)
    {
        m_currentData.takeFront(iRemoveSize);
    }

    m_iLastUpdatedDataPeriodIndex = m_currentData.periodCount();
    m_currentData.append(newData);

    increaseDataInfoList(newData.periodCount());
    updateDataInfo(m_iLastUpdatedDataPeriodIndex);
}

void PhasePaintData::setSkippedData(const PhaseData &data)
{
    m_skippedData = data;
}

// 被跳过的数据的百分比数据
QVector<float> PhasePaintData::skippedPercents() const
{
    const int sz = m_skippedData.dataCountPerPeriod() * m_skippedData.periodCount();
    QVector<float> percents(sz);

    const Phase::ValueType range = (m_fUpperBound - m_fLowerBound);
    int index = 0;
    for(int i = 0; i < m_skippedData.periodCount(); ++i)
    {
        const Phase::ValueType *pData = m_skippedData.at(i).data();
        index = i * m_skippedData.dataCountPerPeriod();
        for(int j = 0; j < m_skippedData.dataCountPerPeriod(); ++j)
        {
            percents[index + j] = (pData[j] - m_fLowerBound) / range;
        }
    }

    return percents;
}

void PhasePaintData::clearSkippedData()
{
    m_skippedData.clear();
}

void PhasePaintData::setPhaseCount(int iPhaseCount)
{
    PHASE_ASSERT(iPhaseCount > 0);

    m_currentData.setDataCountPeriod(iPhaseCount);
}

void PhasePaintData::setColorStyle(const Phase::DataItemColorStyle eStyle)
{
    m_colorMapper.setStyle(eStyle);

    updateColor(0);
}

void PhasePaintData::updateDataInfo(int iStartPeriodIdx)
{
    const int iPeriod = m_currentData.periodCount();
    const int iPhase = phaseCount();
    const Phase::ValueType range = (m_fUpperBound - m_fLowerBound);
    for(int i = iStartPeriodIdx; i < iPeriod; ++i)
    {
        const Phase::ValueType *pData = m_currentData.at(i).data();
        DataInfo *pInfo = m_dataInfos.at(i)->data();
        for(int j = 0; j < iPhase; ++j)
        {
            pInfo[j].percent = (pData[j] - m_fLowerBound) / range;
            if(pInfo[j].percent > 1.0)
            {
                pInfo[j].percent = 1.0;
            }
            pInfo[j].colorOfPercent = m_colorMapper.colorForPercentEx(pInfo[j].percent);
        }
    }

    updateMaxData();
}

void PhasePaintData::updateColor(int iStartPeriodIdx)
{
    const int iPeriod = m_currentData.periodCount();
    const int iPhase = phaseCount();
    for(int i = iStartPeriodIdx; i < iPeriod; ++i)
    {
        DataInfo *pInfo = m_dataInfos.at(i)->data();
        for(int j = 0; j < iPhase; ++j)
        {
            pInfo[j].colorOfPercent = m_colorMapper.colorForPercentEx(pInfo[j].percent);
        }
    }
}

void PhasePaintData::updateMaxData()
{
    float max = 0;
    if(!m_currentData.isEmpty())
    {
        max = m_currentData.at(0).at(0);
    }

    const int iPeriodSz = m_currentData.periodCount();
    const int iPhaseSz = phaseCount();
    for(int i = 0; i < iPeriodSz; ++i)
    {
        const Phase::ValueType *pData = m_currentData.at(i).data();
        for(int j = 0; j < iPhaseSz; ++j)
        {
            if(pData[j] > max)
            {
                max = pData[j];
            }
        }
    }
    //对小于0的数据特殊处理
    if(max < 0)
    {
        max = 0;
    }
    m_fMaxData = max;
}

void PhasePaintData::increaseDataInfoList(int iSize)
{
    int iOverflow = m_dataInfos.size() + iSize - m_iMaxPeriodCount;

    if(iOverflow > 0)
    {
        int iDiff = maxPeriodCount() - m_dataInfos.size();
        if(iDiff > 0)
        {
            appendDataInfo(iDiff);
        }

        for(int i = 0; i < iOverflow; ++i)
        {
            m_dataInfos.push_back(m_dataInfos.front());
            m_dataInfos.pop_front();
        }
    }
    else
    {
        appendDataInfo(iSize);
    }
}

void PhasePaintData::appendDataInfo(int iSize)
{
    for(int i = 0; i < iSize; ++i)
    {
        m_dataInfos.append(QSharedPointer<QVector<DataInfo> >(new QVector<DataInfo>(phaseCount())));
    }
}

