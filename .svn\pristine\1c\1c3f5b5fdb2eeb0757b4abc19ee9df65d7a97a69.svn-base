#ifndef PRPSPLAYBACKVIEW_H
#define PRPSPLAYBACKVIEW_H

#include <QWidget>
#include "dataSave/DataStructures.h"
#include "playbackView/PlayBackBase.h"
#include "prps/prpsview/caprpsunionview.h"
#include "chartwidget/ChartWidget.h"
#include "ca/dataSave/AccessG100PRPSDataSave.h"

class PRPSPlayBackView : public PlayBackBase
{
    Q_OBJECT
public:
    /****************************
    函数名： PRPSPlayBackView;
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 构造函数
    *****************************/
    explicit PRPSPlayBackView(QWidget *parent = 0);

private:
    /*************************************************
    函数名： playbackFile(const QString& strFileName)
    输入参数:strFileName---图谱数据文件路径
    输出参数：NULL
    返回值： NULL
    功能： 回放图谱数据文件
    *************************************************************/
    void playbackFile(const QString& strFileName);

    /*************************************************
    函数名： createChart()
    输入参数: parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 创建图谱
    *************************************************************/
    ChartWidget *createChart(QWidget *parent);

    /****************************
    功能： 在回放中显示
    输入参数: void
    *****************************/
    void displayMap(AccessG100PRPSDataInfo &stInfo);

protected:
//    /*************************************************
//    函数名： onPathChanged
//    输入参数:
//        qsPath:文件列表选择的路径
//    输出参数：NULL
//    返回值： NULL
//    功能： 切换回放的数据源
//    *************************************************************/
//    void onPathChanged( const QString& qsPath, int index );

private:
    CAPrpsUnionView   *m_pChart; // 图谱部分

    AccessG100PRPSDataInfo  m_sPlayBackDataInfo; // 存放回放数据的结构体
    enum
    {
        CHART_HEIGHT = 510,//图谱高度
    };
};

#endif // PRPSPLAYBACKVIEW_H
