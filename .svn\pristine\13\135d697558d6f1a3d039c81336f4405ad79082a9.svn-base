﻿#include <QDebug>
#include <QGridLayout>
#include "ButtonBar.h"
#include "log/log.h"

//一些宏定义
#define NO_BUTTON_ACTIVE -1 //无按钮激活
/*************************************************
功能： 构造函数
输入参数:
    parent -- 父控件
*************************************************************/
ButtonBar::ButtonBar( QWidget *parent )
    : Q<PERSON><PERSON>e(parent)
    , m_iCurrentIndex(NO_BUTTON_ACTIVE)
    , m_pAttachButtonBar(NULL)
{
}

/*************************************************
功能： 添加按钮
输入参数:
    pButton -- 按钮指针
    id -- 按钮ID（注：如果为-1，则按钮用自身设置的ID）
*************************************************************/
void ButtonBar::addButton( ControlButton* pButton )
{
    if( NULL == pButton )
    {
        qWarning() << "ButtonBar::addButton: error, null button input!" ;
    }
    else
    {
        m_listButtons << pButton;

        //按钮点击
        connect( pButton, SIGNAL(sigPressed(int)), this, SLOT(onButtonPressed(int)) );
    }
}

/************************************************
 * 功能     : 获取按钮
 * 输入参数 :
 *      ucID -- 按钮的ID
 ************************************************/
ControlButton* ButtonBar::button( quint8 ucID )
{
    ControlButton* pButton = NULL;
    for(int i = 0, iCount = m_listButtons.count(); i < iCount; ++i)
    {
        if(ucID == m_listButtons[i]->id())
        {
            pButton = m_listButtons[i];
            break;
        }
    }

    //从被挂接的按钮栏中查找
    if( ( NULL == pButton ) && ( NULL != m_pAttachButtonBar ) )
    {
        pButton = m_pAttachButtonBar->button( ucID );
    }

    return pButton;
}

/************************************************
 * 功能     : 将自己挂接到另一个buttonbar
 *         例：A->attach(B)，
 *         通过B->button(ucID)，可以访问到A的按钮
 * 输入参数：
 *         pButtonBar -- 目标buttonbar
 ************************************************/
void ButtonBar::attach( ButtonBar* pButtonBar )
{
    pButtonBar->m_pAttachButtonBar = this;
}

/************************************************
 * 功能     : 解除挂接的菜单
 ************************************************/
void ButtonBar::detach( void )
{
    m_pAttachButtonBar = NULL;
}

/************************************************
 * 功能     : 获取按钮列表:waring,不可使用生成ButtonBar的枚举下标进行访问，除非枚举严格按照从0递增的顺序
 * 输入参数 :
 *         bIncludeAttach -- true：连Attach的一起返回，
 *                           false：只返回自身按钮
 * 返回值   : 按钮列表
 ************************************************/
const QList< ControlButton* > ButtonBar::buttons( bool bIncludeAttach )
{
    if( bIncludeAttach && ( NULL != m_pAttachButtonBar ) )
    {
        return m_listButtons + m_pAttachButtonBar->buttons(true);
    }
    else
    {
        return m_listButtons;
    }
}

/*************************************************
功能： 删除所有按键
*************************************************************/
void ButtonBar::removeAll( void )
{
    for(int i = 0, iCount = m_listButtons.count(); i < iCount; ++i)
    {
        delete m_listButtons[i];
        m_listButtons[i] = NULL;
    }

    m_listButtons.clear();
}

/*************************************************
功能： 删除按键
*************************************************************/
void ButtonBar::removeBtn( int index )
{
    if(m_listButtons.size() > index)
    {
        delete m_listButtons[index];
        m_listButtons[index] = NULL;

        m_listButtons.removeAt(index);
    }
    return;
}

/************************************************
* 函数名   : 键盘事件
* 输入参数 :
*      event -- 事件
************************************************/
void ButtonBar::keyPressEvent( QKeyEvent* event )
{
    int iTotalBtns = m_listButtons.count();
    if(m_iCurrentIndex >= iTotalBtns || m_iCurrentIndex < 0 || 0 == iTotalBtns)
    {
        logError(QString("error: btn cur index: %1, list btns size: %2.").arg(m_iCurrentIndex).arg(iTotalBtns));
        //以下处理避免越界后不响应按键操作
        activeAllBtns(false);
        m_iCurrentIndex = 0;
        return;
    }

    bool bHandled = true;

    //如果没有选中，且取消键，则关闭
    if(((NO_BUTTON_ACTIVE == m_iCurrentIndex) || (!buttons().at(m_iCurrentIndex)->isActive()))
            &&(Qt::Key_Escape == event->key()))
    {
        emit sigRequestClosing();
        QWidget::keyPressEvent(event);
    }
    //如果未有选中，则激活第一个
    else if(NO_BUTTON_ACTIVE == m_iCurrentIndex)
    {
        m_iCurrentIndex = 0;
        if(m_listButtons[m_iCurrentIndex]->isEnabled())
        {
            m_listButtons[m_iCurrentIndex]->setActive(true);
        }
    }
    //有选中
    else
    {
        if((Qt::Key_Return == event->key()) || (Qt::Key_Enter == event->key()))
        {
            if(m_listButtons[m_iCurrentIndex]->isEnabled())
            {
                m_listButtons[m_iCurrentIndex]->onEnterKeyPressed();
            }
        }
        else
        {
            m_listButtons[m_iCurrentIndex]->setActive( false );
            switch(event->key())
            {
            case Qt::Key_Escape://取消键
            {
                //m_iCurrentIndex = NO_BUTTON_ACTIVE;
                break;
            }
            case Qt::Key_Up:
            case Qt::Key_Left:
            {
                m_iCurrentIndex = (m_iCurrentIndex + iTotalBtns - 1) % iTotalBtns;
                if(!m_listButtons[m_iCurrentIndex]->isEnabled())
                {
                    m_iCurrentIndex = (m_iCurrentIndex + iTotalBtns - 1) % iTotalBtns;
                }

                if(m_listButtons[m_iCurrentIndex]->isEnabled())
                {
                    m_listButtons[m_iCurrentIndex]->setActive(true);
                }
                break;
            }
            case Qt::Key_Down:
            case Qt::Key_Right:
            {
                m_iCurrentIndex = (m_iCurrentIndex + 1) % iTotalBtns;
                if(!m_listButtons[m_iCurrentIndex]->isEnabled())
                {
                    m_iCurrentIndex = (m_iCurrentIndex + 1) % iTotalBtns;
                }

                if(m_listButtons[m_iCurrentIndex]->isEnabled())
                {
                    m_listButtons[m_iCurrentIndex]->setActive(true);
                }
                break;
            }
            default:
                bHandled = false;
                break;
            }
        }
    }

    if(!bHandled)
    {
        QFrame::keyPressEvent(event);
    }

    return;
}

/************************************************
 * 功能   :槽，退出激活状态
 ************************************************/
void ButtonBar::disActive()
{
    if( NO_BUTTON_ACTIVE != m_iCurrentIndex )
    {
        m_listButtons[m_iCurrentIndex]->setActive( false );
    }
}

/************************************************
 * 功能：槽，响应按钮按下
 * 输入参数：
 *         id -- 按钮ID
 ************************************************/
void ButtonBar::onButtonPressed( int id  )
{
    for(int i = 0, iCount = m_listButtons.count(); i < iCount; ++i)
    {
        if(id == m_listButtons[i]->id())
        {
            m_iCurrentIndex = i;
        }
        else
        {
            m_listButtons[i]->setActive(false);
        }
    }
    return;
}


/************************************************
 * 功能     : 获取当前索引
 * 返回值   : 当前索引
 ************************************************/
int ButtonBar::currentIndex() const
{
    return m_iCurrentIndex;
}

/************************************************
 * 功能     : 设置当前索引
 * 输入参数：
 *         index -- 当前索引
 ************************************************/
void ButtonBar::setCurrentIndex( int index )
{
    if(NO_BUTTON_ACTIVE != m_iCurrentIndex)
    {
        m_listButtons[m_iCurrentIndex]->setActive(false);
    }
    if((index >= 0) && (index < m_listButtons.count()))
    {
        m_iCurrentIndex = index;
        if(m_listButtons[m_iCurrentIndex]->isEnabled())
        {
            m_listButtons[m_iCurrentIndex]->setActive(true);
        }
    }
    return;
}

/*************************************************
功能： 显示事件
输入参数:
    event -- 事件
*************************************************************/
void ButtonBar::showEvent( QShowEvent *event )
{
    Q_UNUSED(event);
    setCurrentIndex(0);
    return;
}

/*************************************************
功能： 所有按键选中或取消选中效果
*************************************************************/
void ButtonBar::activeAllBtns(const bool bActived)
{
    for(int i = 0, iCount = m_listButtons.count(); i < iCount; ++i)
    {
        m_listButtons[i]->setActive(bActived);
    }
    return;
}

/*************************************************
功能： 设置当前激活的按钮
*************************************************************/
void ButtonBar::setCurActiveBtnID(int iBtnID)
{
    for(int i = 0, iCount = m_listButtons.count(); i < iCount; ++i)
    {
        if(iBtnID == m_listButtons[i]->id())
        {
            setCurrentIndex(i);
            break;
        }
    }

    return;
}
