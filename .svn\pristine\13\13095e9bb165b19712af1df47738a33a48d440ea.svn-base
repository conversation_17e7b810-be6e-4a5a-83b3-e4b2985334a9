#ifndef COMBOBOX_H
#define COMBOBOX_H

#include <QFrame>
#include <QComboBox>
#include <QKeyEvent>

class ComboBox : public QFrame
{
    Q_OBJECT
public:
    explicit ComboBox(QFrame *parent = 0);

    /************************************************
     * 功能     : 返回当前combobox选中的值
     ************************************************/
    int currentIndex();

    /************************************************
     * 功能     : 将包含显示信息的条目添加到combobox中
     ************************************************/
    void addItem(const QString &atext);

    /************************************************
     * 功能     : 设置combobox的选中条目
     ************************************************/
    void setCurIndex(int iIndex);


    /************************************************
     * 功能     : 清空combobox的所有条目
     ************************************************/
    void clear();


protected:
    /************************************************
     * 函数名   : focusInEvent
     * 输入参数 : pFocusEvent: 焦点事件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 获取焦点事件
     ************************************************/
    void focusInEvent(QFocusEvent *pFocusEvent);

    /************************************************
     * 函数名   : focusOutEvent
     * 输入参数 : pFocusEvent: 焦点事件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 焦点消失事件
     ************************************************/
    void focusOutEvent(QFocusEvent *pFocusEvent);

    /************************************************
     * 函数名   : 键盘事件
     * 输入参数 :
     *      event -- 事件
     ************************************************/
    void keyPressEvent( QKeyEvent* event );

signals:

public slots:

private:
    QComboBox* mpCombobox;
    bool mbisFocused; //表示整个大的控件是否得到焦点（不同于mpCombobox）
};

#endif // COMBOBOX_H
