#include <QDebug>
#include "pdadevicedetailview.h"
#include "PDAViewConfig.h"
#include "window/Window.h"
#include "pda/pdatask.h"
#include "pdapatroltypeview.h"

const QString LABEL_STYLE = "AutoScaleLabel{background:white;color:rgb(114,114,114);border:none}"
                            "AutoScaleLabel:focus{background:Blanched<PERSON>lmond;color:rgb(114,114,114);border:none}";
const QString VOLTAGE_UNIT = QString("kV");
const int TITLE_HEIGHT = 100;   // 标题栏高度


/*************************************************
功能： 构造函数
输入参数:
    parent:父控件指针
*************************************************************/
PDADeviceDetailView::PDADeviceDetailView(const ItemDevice &stDeviceInfo, QWidget *parent) :
    QWidget(parent),m_stDevInfo(stDeviceInfo)
{
    setFixedSize( Window::WIDTH,Window::HEIGHT );
    setStyleSheet( "PDADeviceDetailView{background:white}" );
    setWindowFlags( Qt::FramelessWindowHint );
    setAttribute(Qt::WA_DeleteOnClose);

    createUI();
}

/*************************************************
功能： 析构函数（保存当前任务信息->文件）
*************************************************************/
PDADeviceDetailView::~PDADeviceDetailView()
{

}

/************************************************
 * 功能     : 按键响应
 * 输入参数 :
 *      event -- 事件
 ************************************************/
void PDADeviceDetailView::keyPressEvent( QKeyEvent * event)
{
    dbg_info("");
    if( Qt::Key_Escape == event->key() )
    {
        close();
    }
}

/****************************
函数名： setDeviceInfo
输入参数:stDevInfo -- 设备详情信息
输出参数：NULL
返回值：NULL
功能： 保存设备详情信息
*****************************/
void PDADeviceDetailView::setDeviceInfo(const ItemDevice& stDevInfo)
{
    m_stDevInfo = stDevInfo;
}

/****************************
函数名： getContentFromSheet
输入参数:eLabelType -- 标签对应的类型
输出参数：NULL
返回值：NULL
功能： 根据标签类型获得相应的信息
*****************************/
QString PDADeviceDetailView::getContentFromSheet( LabelType eLabelType )
{
    QString qstrInfo = "";
    switch( eLabelType )
    {
    case DEVICE_NAME:
    {
        qstrInfo = QObject::trUtf8("Asset Name: ") + m_stDevInfo.strItemName;
        break;
    }
    case DEVICE_TYPE:
    {
        qstrInfo = QObject::trUtf8("Asset Type: ") + m_stDevInfo.strType;
        break;
    }
    case DEVICE_MODEL:
    {
        qstrInfo = QObject::trUtf8("Asset Model: ") + m_stDevInfo.strDevModel;
        break;
    }
    case DEVICE_NUM:
    {
        qstrInfo = QObject::trUtf8("Asset Number: ") + "\n" + m_stDevInfo.strDevNum; //换行，不然一行显示不下
        break;
    }
    case VOLTAGE_LEVEL:
    {
        QString strUnit = (PDAServiceNS::Voltage_V_Unit == m_stDevInfo.eVoltageUnit) ? "V" : "kV";
        QString strVoltage = QString::number(m_stDevInfo.dVoltage, 'f', 2);
        int index = strVoltage.indexOf('.');
        if(strVoltage.endsWith(".00"))
        {
            //such as 10.00
            strVoltage.remove(index, 3);
        }
        else if(strVoltage.endsWith("0"))
        {
            //such as 5.10
            strVoltage.remove(strVoltage.size() - 1, 1);
        }
        else
        {
            //such as 5.16
        }

        qstrInfo = QObject::trUtf8("Voltage Level: ") + strVoltage + strUnit;
        break;
    }
    case PRODUCE_TIME:
    {
        qstrInfo = QObject::trUtf8("Release Date: ") + m_stDevInfo.strProduceDate;
        break;
    }
    case MANUFACTURER_NAME:
    {
        qstrInfo = QObject::trUtf8("Manufacturer: ") + m_stDevInfo.strManufacturer;
        break;
    }
    case GROUP_NAME:
    {
        qstrInfo = QObject::trUtf8("Group Name: ") + QString::number(m_stDevInfo.ucGroupName);
        break;
    }
    default:
    {
        qstrInfo = "";
        break;
    }
    }
    return qstrInfo;
}

/****************************
函数名： createLabelFrame
输入参数:NULL
输出参数：NULL
返回值：NULL
功能： 创建新的标签，统一设置对其及样式并添加进布局
*****************************/
QFrame *PDADeviceDetailView::createLabelFrame(const QString &strPath, const QString &qsText )
{
    QFrame *pLabelFrame = new QFrame( this );
    QHBoxLayout *pHLabelLayout = new QHBoxLayout;

    QLabel *pTextLabel = new QLabel;
    QFont font = pTextLabel->font();
    font.setPointSize(20);
    pTextLabel->setFont(font);
    pTextLabel->setText(qsText);
    pTextLabel->setWordWrap(true);
    pTextLabel->setScaledContents(true);

    QLabel *pIconLabel = new QLabel;
    QPixmap pximap( strPath );
    pIconLabel->setPixmap(pximap);

    //pHLabelLayout->addWidget(pIconLabel);
    pHLabelLayout->addWidget(pTextLabel,Qt::AlignLeft);
    pLabelFrame->setLayout(pHLabelLayout);

    return pLabelFrame;
}

/****************************
函数名： label2IconPath
输入参数:eType---标签类型索引值
输出参数：NULL
返回值：NULL
功能： 根据标签返回标签的图片所在的路径
*****************************/
QString PDADeviceDetailView::label2IconPath(LabelType eType)
{
    switch (eType)
    {
        case DEVICE_NAME:
        {
            return QString(":/images/pdaTask/info.PNG");
        }
        case DEVICE_TYPE:
        {
            return QString(":/images/pdaTask/info.PNG");
        }
        case DEVICE_MODEL:
        {
            return QString(":/images/pdaTask/info.PNG");
        }
        case DEVICE_NUM:
        {
            return QString(":/images/pdaTask/info.PNG");
        }
        case MANUFACTURER_NAME:
        {
            return QString(":/images/pdaTask/info.PNG");
        }
        case GROUP_NAME:
        {
            return QString(":/images/pdaTask/info.PNG");
        }
        case VOLTAGE_LEVEL:
        {
            return QString(":/images/pdaTask/voltageLevel.png");
        }
        case PRODUCE_TIME:
        {
            return QString(":/images/pdaTask/factoryDate.png");
        }
        default:
        {
            return QString("");
        }
    }
}

/****************************
函数名： appendDetailLabels
输入参数:NULL
输出参数：NULL
返回值：NULL
功能： 创建详情界面的所有label添加到容器
*****************************/
void PDADeviceDetailView::appendDetailLabels()
{
    m_vecLabels.clear();

    for(int i = DEVICE_NAME; i < LABEL_TYPE_CNT; i ++)
    {
        QString qsIconPath = label2IconPath((LabelType) i);
        QString qsText = getContentFromSheet((LabelType) i);

        QFrame* plabel = createLabelFrame(qsIconPath, qsText);
        m_vecLabels.append(plabel);
    }
}

/*************************************************
功能： 创建界面
*************************************************************/
void PDADeviceDetailView::createUI()
{
    appendDetailLabels();

    TitleBar *pTitleBar = new TitleBar( trUtf8( "Device Info" ),this );
    pTitleBar->setFixedHeight( TITLE_HEIGHT );
    connect( pTitleBar,SIGNAL(sigClicked()),this,SLOT(close()) ); // 创建标题

    QVBoxLayout *vLayout = new QVBoxLayout( this );
    vLayout->setMargin( 0 );
    vLayout->addWidget( pTitleBar,Qt::AlignTop );
    for(int i = 0; i < m_vecLabels.size(); i ++)
    {
        vLayout->addWidget( m_vecLabels.at(i),Qt::AlignLeft );
    }
    vLayout->addStretch();
    vLayout->setSpacing( 0 );
    setLayout( vLayout );
}

