#include "activemanager.h"
#include <QDateTime>


#define UNLIMITED_YEAR      2099
#define UPPER_DAY           14
#define LOWER_DAY           1

ActiveManager::ActiveManager()
{
}

ActiveManager::~ActiveManager()
{
}

ActiveManager* ActiveManager::instance()
{
    static ActiveManager objManager;
    return &objManager;
}

/*****************************************************
 * 功能：根据截止日期获取激活状态
 * 输入参数：
 *      qstrShowStartDate：激活起始日期，日期格式yyyy/MM/dd
 *      qstrShowEndDate：激活截止日期，日期格式yyyy/MM/dd
 * 返回值：
 *      ActiveState：激活状态
 * ***************************************************/
ActiveState ActiveManager::getActiveStateByDate(const QString &qstrShowStartDate, const QString &qstrShowEndDate)
{
    QDate qEndDate = QDate::fromString(qstrShowEndDate, ACTIVE_SHOW_DATE_FORMAT);
    if(UNLIMITED_YEAR <= qEndDate.year())
    {
        return ACTIVE_UNLIMITED;
    }

    QDate qCurDate = QDate::currentDate();
    QString qstrCurDate = qCurDate.toString(ACTIVE_SHOW_DATE_FORMAT);

    if(qstrShowStartDate > qstrCurDate || qstrShowEndDate < qstrCurDate)
    {
        return ACTIVE_OVERDUE;
    }

    ActiveState eState = ACTIVE_FAR;
    qint64 qi64Days = qCurDate.daysTo(qEndDate);
    if(UPPER_DAY < qi64Days)
    {
        eState = ACTIVE_FAR;
    }
    else if (LOWER_DAY <= qi64Days && qi64Days <= UPPER_DAY)
    {
        eState = ACTIVE_NEAR;
    }
    else
    {
        eState = ACTIVE_OVERDUE;
    }

    return eState;
}

/*****************************************************
 * 功能：读取配置文件中的激活状态
 * 返回值：
 *      ActiveState：激活状态
 * ***************************************************/
ActiveState ActiveManager::getActiveStateByIniFile()
{
    ActiveInfo stActiveInfo;
    IniConfig::readActiveInfo(stActiveInfo);
    if(!(stActiveInfo.bActivationEffective))
    {
        return ACTIVE_UNLIMITED;
    }

    ActiveState eState = getActiveStateByDate(stActiveInfo.qstrShowStartDate, stActiveInfo.qstrShowEndDate);
    if(eState != stActiveInfo.eState)
    {
        stActiveInfo.eState = eState;
        IniConfig::writeActiveInfo(stActiveInfo);
    }

    return eState;
}

/*****************************************************
 * 功能：获取激活剩余天数
 * 返回值：
 *      int：剩余天数
 * ***************************************************/
int ActiveManager::getActiveRemainDays()
{
    ActiveInfo stActiveInfo;
    IniConfig::readActiveInfo(stActiveInfo);

    QDate qEndDate = QDate::fromString(stActiveInfo.qstrShowEndDate, ACTIVE_SHOW_DATE_FORMAT);
    QDate qCurDate = QDate::currentDate();
    return static_cast<int>(qCurDate.daysTo(qEndDate));
}
