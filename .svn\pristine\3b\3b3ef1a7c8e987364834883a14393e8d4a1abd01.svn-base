/*
* Copyright (c) 2016.07，南京华乘电气科技有限公司
* All rights reserved.
*
* autoLabel.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年7月15日
* 摘要：设置字和图片一起显示的label

* 当前版本：1.0
*/

#include <QPainter>
#include <QIcon>
#include <QEvent>
#include <QDebug>
#include "pixmapLabel.h"
#include "getrefont.h"
#include "Widget.h"
#include "appfontmanager/appfontmanager.h"

const QString PIXMAP_PATH = ":/images/sampleControl/second_menu.png";
const int FONT_SIZE = 25;
const int FONT_SIZE_NO_ACTIVE = 22;
const int TEXT_ROW_MARGIN = 7 ;   //按钮title有多行时，将行与行之间的间隙减小
#define BUTTON_HEIGHT_RATIO 0.25 //高度比例
const int BG_BLANK_WIDTH = 6; // 按钮背景图片的空白宽度
#define CONTROLBUTTON_CONTENT_TITLE_POS_RATIO 0.60 //内容中标题文字位置比例（垂直）
#define CONTROLBUTTON_CONTENT_VALUE_RATIO 0.18 //内容中值高度比例

/*************************************************
函数名： pixmapLabel(QWidget *parent)
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
pixmapLabel::pixmapLabel(const QSize &size, QWidget *parent) :
    QLabel(parent),
    m_qsOriginalSize( size ),
    m_iActiveSize( FONT_SIZE ),
    m_iNoActiveSize( FONT_SIZE_NO_ACTIVE ),
    m_bIsIncompleteDisplay(false)
{
    m_strText = QString::null;
    m_strContent = QString::null;
    m_bActive = false;
}

/*************************************************
函数名： setDrawText
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 设置显示的文本
*************************************************************/
void pixmapLabel::setDrawText(const QString& string, const QString &strValue)
{
    if( ( m_strText == string ) && ( m_strContent == strValue ) )
    {
        return;
    }
    m_strText = string;
    if( strValue != QString::null )
    {
        m_strContent = strValue;
    }
    update();
}

/*************************************************
函数名： setDrawSize
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 设置图片尺寸
*************************************************************/
void pixmapLabel::setDrawSize( const QSize& size )
{
    if( m_qsOriginalSize == size )
    {
        return;
    }
    m_qsOriginalSize = size;
    update();
}

/*************************************************
函数名： setActive
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 设置激活状态
*************************************************************/
void pixmapLabel::setActive( bool bActive )
{
    if( m_bActive == bActive )
    {
        return;
    }
    m_bActive = bActive;
    update();
}

void pixmapLabel::setActiveSize(int activeSize, int noActiveSize)
{
    if( activeSize <  noActiveSize)
    {
        return;
    }
    m_iActiveSize =  activeSize;
    m_iNoActiveSize = noActiveSize;
    update();
}

/*************************************************
功能： 返回是否显示不全
返回值:
    bool -- 是否显示不全
*************************************************************/
bool pixmapLabel::isIncompleteDisplay() const
{
    return m_bIsIncompleteDisplay;
}

/*************************************************
函数名： paintEvent
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 重绘事件
*************************************************************/
void pixmapLabel::paintEvent(QPaintEvent*e)
{
    QLabel::paintEvent(e);

    int iWidth = width() - BG_BLANK_WIDTH;
    QPainter painter(this);
    painter.save();

    QFont f;
    if (true == m_bActive)
    {
        f = QFont(AppFontManager::instance()->getAppCurFontFamily(), m_iActiveSize);
    }
    else
    {
        f = QFont(AppFontManager::instance()->getAppCurFontFamily(), m_iNoActiveSize);
    }
    QFont fontTitle = GetReFont::getReheightFont(f, height() * BUTTON_HEIGHT_RATIO);
    QFontMetrics fm(fontTitle);

    if (!m_strText.isNull())
    {
        if (m_strContent.isEmpty())//没有值内容，则只绘制按钮标题
        {
            painter.setPen(QPen(Qt::white, 1, Qt::SolidLine));
            painter.setFont(fontTitle);
            painter.drawText(QRectF(0, 0, width(), height()), fm.elidedText(m_strText, Qt::ElideRight, iWidth), Qt::AlignVCenter | Qt::AlignHCenter);

            m_bIsIncompleteDisplay = fm.width(m_strText) > iWidth;
        }
        else
        {
            int iTitleHeight = m_qsOriginalSize.height() * CONTROLBUTTON_CONTENT_TITLE_POS_RATIO; // 标题高度
            // 绘制标题
            painter.setPen(QPen(Qt::white, 2, Qt::SolidLine));
            painter.setFont(fontTitle);
            painter.drawText(QRectF(0, 0, width(), iTitleHeight), fm.elidedText(m_strText, Qt::ElideRight, iWidth), Qt::AlignVCenter | Qt::AlignHCenter);
            // 绘制值内容
            QRect rect = this->rect();
            QRect rectValue = QRect(QPoint(rect.topLeft().x(), rect.topLeft().y() + iTitleHeight - TEXT_ROW_MARGIN), QSize(rect.width(), rect.height() - iTitleHeight));
            painter.setPen(QPen(Qt::white, 1, Qt::SolidLine));
            QFont fontValue = getReheightFont(fontTitle, m_qsOriginalSize.height() * CONTROLBUTTON_CONTENT_VALUE_RATIO);
            painter.setFont(fontValue);
            QFontMetrics contentFontMetrics(fontValue);
            painter.drawText(rectValue, Qt::AlignCenter, contentFontMetrics.elidedText(m_strContent, Qt::ElideRight, iWidth));

            m_bIsIncompleteDisplay = (fm.width(m_strText) > iWidth) || (contentFontMetrics.width(m_strContent) > iWidth);
        }
    }

    painter.restore();
}
