#include "versioninfo.h"
#include <QLabel>
#include <QVBoxLayout>
#include <QStackedLayout>
#include <QKeyEvent>
#include <QDir>
#include <QDateTime>
#include "datadefine.h"
#include "messageBox/msgbox.h"
#include "config/ConfigManager.h"
#include "appconfig.h"
#include "dataSave/DataFileInfos.h"
#include "systemsetting/systemsetservice.h"
#include "titlebar/TitleBar.h"
#include "systemsetview/SystemViewConfig.h"
#include "window/Window.h"
#include "ca/caservice.h"
#include "appfontmanager/appfontmanager.h"



const QString TITLE_TEXT_STYLE = "QLabel{color:rgb(0, 0, 0, 255); border:none;}";
const QString CONDITIONER_TEXT_STYLE = "QLabel{color:rgb(0, 0, 0, 255); border:none;}";



/*************************************************
函数名： VersionInfo(const QString &title, QWidget *parent)
输入参数:
    title: view的标题
    parent:父控件指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
VersionInfo::VersionInfo(const QString &title, QWidget *parent)
    : QWidget(parent)
{
    //#ifdef Q_PROCESSOR_ARM
    setWindowFlags(Qt::FramelessWindowHint);
    setAttribute(Qt::WA_DeleteOnClose);
    setFixedSize( Window::WIDTH,Window::HEIGHT );

    QFont qFont = AppFontManager::instance()->getAppCurFont();
    qFont.setPointSize(26);


    setAutoFillBackground(true);
    QLinearGradient linearGrad(QPointF(0, 0), QPointF(0, Window::HEIGHT));
    linearGrad.setColorAt(0, Qt::white);
    linearGrad.setColorAt(1, QColor(181, 210, 236));
    QPalette pal;
    pal.setBrush(QPalette::Window, QBrush(linearGrad));
    setPalette( pal);

    QFrame *pWidget = new QFrame(this);

    //STM Version
    QString strStmVersion = "";
    SystemSetService::instance()->stm32Version(strStmVersion);
    strStmVersion = QString("STM Version: ") + strStmVersion;
    QLabel *pStmVersionLabel = new QLabel(pWidget);
    pStmVersionLabel->setStyleSheet( TITLE_TEXT_STYLE );
    pStmVersionLabel->setFont( qFont );
    pStmVersionLabel->setText(strStmVersion);

    //Zigbee Version
    QLabel *pZigbeeVersionLabel = NULL;
    WLDeviceVer stVersionData;
    memset(&stVersionData, 0, sizeof(WLDeviceVer));
    if(SystemSetService::instance()->zigbeeVersion(&stVersionData))
    {
        QByteArray qbaZigbeeVersion = QByteArray((const char*)stVersionData.caVersion);
        QString qsZigbeeVersion = QString("Zigbee Version: ") + QString(qbaZigbeeVersion);
        pZigbeeVersionLabel = new QLabel(pWidget);
        pZigbeeVersionLabel->setStyleSheet( TITLE_TEXT_STYLE );
        pZigbeeVersionLabel->setFont( qFont );
        pZigbeeVersionLabel->setText(qsZigbeeVersion);
    }

    //CA conditioner version
    QLabel *pCaConditionerVerLabel = NULL;
    CA::CAConditionerInfo stCaCondiInfo = CAService::instance()->getCAConditionerInfo();
    if(!(stCaCondiInfo.ARMSWVersion.isEmpty()))
    {
        QString qstrCaCondiVer = QString("HAS02 Version: %1").arg(stCaCondiInfo.ARMSWVersion);
        pCaConditionerVerLabel = new QLabel(pWidget);
        pCaConditionerVerLabel->setStyleSheet(TITLE_TEXT_STYLE);
        pCaConditionerVerLabel->setFont(qFont);
        pCaConditionerVerLabel->setText(qstrCaCondiVer);
    }

    QLabel *pUHFConditionerLabel = NULL;
    QLabel *pHFCTConditionerLabel = NULL;
    QLabel *pExternalSynchronizerLabel = NULL;
    QString qsLabel = "";

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );

    QString qsConditionerName = "";
    qsConditionerName = pConfig->value( APPConfig::KEY_UHF_CONDITIONER_ID );
    if(!qsConditionerName.isEmpty())
    {
        qsLabel = SYSTEM_INFO_TRANSLATE(SystemSet::UHF_CONDITIONER) + ": " + qsConditionerName;
        pUHFConditionerLabel = new QLabel(pWidget);
        pUHFConditionerLabel->setStyleSheet( CONDITIONER_TEXT_STYLE );
        pUHFConditionerLabel->setFont( qFont );
        pUHFConditionerLabel->setText(qsLabel);
    }

    //连接上的HFCT调理器version
    qsConditionerName = "";
    qsConditionerName = pConfig->value( APPConfig::KEY_HFCT_CONDITIONER_ID );
    qDebug()<<"KEY_HFCT_CONDITIONER_ID qsConditionerName:"<<qsConditionerName;

    if(!qsConditionerName.isEmpty())
    {
        qsLabel = SYSTEM_INFO_TRANSLATE(SystemSet::HFCT_CONDITIONER) + ": " + qsConditionerName;
        pHFCTConditionerLabel = new QLabel(pWidget);
        pHFCTConditionerLabel->setStyleSheet( CONDITIONER_TEXT_STYLE );
        pHFCTConditionerLabel->setFont( qFont );
        pHFCTConditionerLabel->setText(qsLabel);
    }

    //连接上的Synchronizer version
    qsConditionerName = "";
    qsConditionerName = pConfig->value( APPConfig::KEY_SYNCHRONIZER_CONDITIONER_ID );
    qDebug()<<"KEY_SYNCHRONIZER_CONDITIONER_ID qsConditionerName:"<<qsConditionerName;

    if(!qsConditionerName.isEmpty())
    {
        qsLabel = SYSTEM_INFO_TRANSLATE(SystemSet::EXTERNAL_SYNC) + ": " + qsConditionerName;
        pExternalSynchronizerLabel = new QLabel(pWidget);
        pExternalSynchronizerLabel->setStyleSheet( CONDITIONER_TEXT_STYLE );
        pExternalSynchronizerLabel->setFont( qFont );
        pExternalSynchronizerLabel->setText(qsLabel);
    }

    pConfig->endGroup();

    //set widgets to layout
    QBoxLayout *boxLayout = new QBoxLayout(QBoxLayout::TopToBottom);
    boxLayout->setAlignment(Qt::AlignCenter);

    if(NULL != pStmVersionLabel)
    {
        boxLayout->addWidget(pStmVersionLabel, 0, Qt::AlignLeft);
    }

    if(NULL != pZigbeeVersionLabel)
    {
        boxLayout->addWidget(pZigbeeVersionLabel, 0, Qt::AlignLeft);
    }

    if(NULL != pUHFConditionerLabel)
    {
        boxLayout->addWidget(pUHFConditionerLabel, 0, Qt::AlignLeft);
    }

    if(NULL != pHFCTConditionerLabel)
    {
        boxLayout->addWidget(pHFCTConditionerLabel, 0, Qt::AlignLeft);
    }

    if(NULL != pExternalSynchronizerLabel)
    {
        boxLayout->addWidget(pExternalSynchronizerLabel, 0, Qt::AlignLeft);
    }

    if(NULL != pCaConditionerVerLabel)
    {
        boxLayout->addWidget(pCaConditionerVerLabel, 0, Qt::AlignLeft);
    }

    pWidget->setLayout(boxLayout);

    //title bar
    TitleBar *pTitleBar = new TitleBar(title, this);
    connect(pTitleBar, SIGNAL(sigClicked()), this, SLOT(close()));

    QVBoxLayout *vLayout = new QVBoxLayout(this);
    vLayout->addWidget(pTitleBar, 0, Qt::AlignTop);
    vLayout->setSpacing(0);
    vLayout->setMargin(0);
    vLayout->addStretch();
    vLayout->addWidget(pWidget);
    vLayout->addStretch();

    setLayout(vLayout);
    //#endif
}

/************************************************
 * 函数名   : createDataPath
 * 输入参数 : dateTime: 时间
 * 输出参数 : NULL
 * 返回值   : 创建路径结果
 * 功能     : 创建截图文件存储路径
 ************************************************/
bool VersionInfo::createDataPath(const QString &strAbsolutePath)
{
    bool bCreateResult = true;

    QDir dir(strAbsolutePath);
    if(!dir.exists())
    {
        bCreateResult = dir.mkpath(strAbsolutePath);
    }

    return bCreateResult;
}

/*************************************************
函数名： screenShot
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 截屏
*************************************************************/
void VersionInfo::screenShot( void )
{
    QRect rect = geometry();
    QRect screenRect = QRect( mapToParent( rect .topLeft()),QSize( rect.width(),rect.height() ));
    QPixmap pixmap = QPixmap::grabWidget(this ,screenRect );


    QDateTime dateTime = QDateTime::currentDateTime();
    QString strAbsolutePath = DATA_STORAGE_PATH + "/syssettings/";
    QString strDate = dateTime.toString("yyyyMMdd");
    strAbsolutePath += strDate.mid(0,4);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(4,2);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(6,2);

    createDataPath(strAbsolutePath);

    QString strFilePathName = strAbsolutePath +'/'+ dateTime.toString("yyyyMMdd_hhmmss") + ".png";
    if(pixmap.save(strFilePathName, "png"))
    {
        MsgBox::information( "", QObject::trUtf8("Screenshot successfully!") );
    }
    else
    {
        MsgBox::warning( "", QObject::trUtf8("Screenshot failed!") );
    }

    return;
}

/*************************************************
函数名： keyPressEvent(QKeyEvent *pEvent)
输入参数:
    pEvent: key 事件
输出参数： NULL
返回值： NULL
功能： key press事件处理
*************************************************************/
void VersionInfo::keyPressEvent(QKeyEvent *pEvent)
{
    if(Qt::Key_Escape == pEvent->key())
    {
        close();
    }
    else if(Qt::Key_F1 == pEvent->key())
    {
        screenShot();
    }

    return;
}
