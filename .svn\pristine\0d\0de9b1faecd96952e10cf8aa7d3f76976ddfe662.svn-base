/*
* Copyright (c) 2016.3，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：ColorProgressBar.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年6月14日
* 摘要：该文件主要定义了自定义进度条
*/

#ifndef COLORPROGRESSBAR_H
#define COLORPROGRESSBAR_H

#include <QWidget>

class ColorProgressBar : public QWidget
{
    Q_OBJECT

public:
    /************************************************
     * 功能     ：构造函数
     * 输入参数  ：父对象指针
     ************************************************/
    ColorProgressBar(QWidget *parent = 0);

    /************************************************
     * 功能     ：设置背景颜色
     * 输入参数  ：color -- 背景颜色
     ************************************************/
    void setBarBackGroundColor( QColor color );

    /************************************************
     * 功能     ：设置进度条背景分割线颜色
     * 输入参数  ：color -- 分割线颜色颜色
     ************************************************/
    void setBarSplitLineColor( QColor color );

    /************************************************
     * 功能     ：设置进度条颜色
     * 输入参数  ：color -- 进度条颜色
     ************************************************/
    void setBarColor( QColor color );

    /************************************************
     * 功能     ：设置进度显示小数点后位数
     * 输入参数  ：iDecimal -- 位数
     ************************************************/
    void setDecimal( int iDecimal );

    /************************************************
     * 功能     ：设置进度分割线宽度
     * 输入参数  ：iWidth -- 宽度
     ************************************************/
    void setBarSplitLineWidth( int iWidth );

    /************************************************
     * 功能     ：设置是否显示背景分割线
     * 输入参数  ：color -- 分割线颜色颜色
     ************************************************/
    void setShowBarSplitLine( bool show );

public slots:
    /************************************************
     * 功能     ：设置进度值
     * 输入参数  ：value -- 进度值
     ************************************************/
    void setValue( double value);

    /************************************************
     * 功能     ：设置进度值
     * 输入参数  ：value -- 进度值
     ************************************************/
    void setValue( int value );

protected:
    /************************************************
     * 功能     ：重绘事件
     * 输入参数  ：绘制事件
     ************************************************/
    virtual void paintEvent( QPaintEvent * );
private:
    /************************************************
     * 功能     ：绘制背景色
     * 输入参数  ：p -- 绘图设备
     *           barRect -- 进度显示区域
     *           textRect -- 文本显示区域
     ************************************************/
    void drawBackGround( QPainter& p, const QRectF& barRect, const QRectF& textRect );

    /************************************************
     * 功能     ：绘制进度数据
     * 输入参数  ：p -- 绘图设备
     *           barRect -- 进度显示区域
     ************************************************/
    void drawProgress( QPainter& p, const QRectF& barRect );

    /************************************************
     * 功能     ：绘制进度显示分割线
     * 输入参数  ：p -- 绘图设备
     *           barRect -- 进度显示区域
     ************************************************/
    void drarBarSplitLine( QPainter& p, const QRectF& barRect );

    /************************************************
     * 功能     ：绘制进度文本
     * 输入参数  ：p -- 绘图设备
     *           textRect -- 文本显示区域
     ************************************************/
    void drawText( QPainter& p, const QRectF& textRect );

private:
    double m_dMin;              //最小值
    double m_dMax;              //最大值
    double m_dValue;            //数值
    QColor m_barBackGroundColor;//背景颜色
    QColor m_barSplitLineColor; //背景分割线颜色
    int m_iBarSplitLineWidth;   //背景分割线宽度
    bool m_bShowBarSplitLine;   //是否显示背景分割线
    QColor m_barColor;          //进度条颜色
    int m_iDecimal;             //进度文本小数点后位数
    int m_iXRadius;             //圆角x方向半径
    int m_iYRadius;             //圆角y方向半径
};

#endif // COLORPROGRESSBAR_H
