#include <QThread>
#include <QDebug>
#include "AEPhaseService.h"
#include "model/HCService.h"
#include "model/HCAffair.h"
#include "AEServicePrivate.h"
#include "datadefine.h"
#include "model/HCStatus.h"
#include <alsasoundApi.h>
#include <UHFHFCTAETEVApi.h>
#include <QMutex>
#include <QMutexLocker>

//static QMutex g_mtAePhaseObj;

/****************************
功能： 构造函数
*****************************/
AEPhaseService::AEPhaseService()
{

}

/****************************
功能： 析构函数
*****************************/
AEPhaseService::~AEPhaseService()
{

}

/****************************
功能： 模块单例
*****************************/
AEPhaseService* AEPhaseService::instance()
{
    //QMutexLocker stLocker(&g_mtAePhaseObj);
    static AEPhaseService service;
    return &service;
}

/*************************************************
功能： 启动独占采集
输入参数:void
输出参数：NULL
返回值： NULL
*************************************************/
bool AEPhaseService::startExclusiveSample(  MultiServiceNS::USERID userId  )
{
    bool bRet = PeripheralService::instance()->setExclusiveAEMode( PRPD );
    if( bRet )
    {
        bRet = startSample( userId );
    }

    return bRet;
}

/*************************************************
功能： 停止独占采集
输入参数:void
输出参数：NULL
返回值： NULL
*************************************************/
void AEPhaseService::stopExclusiveSample(  MultiServiceNS::USERID userId  )
{
    PeripheralService::instance()->releaseExclusiveAEMode();

    stopSample( userId );
}
