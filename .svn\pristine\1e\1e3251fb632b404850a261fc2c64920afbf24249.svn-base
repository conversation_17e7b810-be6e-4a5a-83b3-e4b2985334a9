#include "filecommentbox.h"
#include <QBitmap>
#include <QPainter>
#include <QLabel>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QToolTip>
#include "appfontmanager/appfontmanager.h"
#include "softkeyboard.h"

namespace
{
    const int g_iWidth = 400;
    const int g_iHeight = 400;
    const int g_iButtonWidth = 130;              // 按键的宽度
    const int g_iButtonSpacing = 6;          // 按键的间距
    const int g_iButtonHeight = 60;           // 按键的高度
}

const QString BUTTON_STYLE = "QPushButton:focus{border-width:2px;border-color:rgb(54, 133, 203);border-radius:10px;border-style: inset; background:rgb( 115,198,242 );}"
                             "QPushButton{border-width:2px;border-color:rgb(54, 133, 203);border-radius:10px;border-style: outset;}";

FileCommentBox::FileCommentBox(QWidget* pParent)
    : QDialog(pParent),
      m_bRotate(false)
{
    // 设置整体显示属性
    setWindowFlags(Qt::FramelessWindowHint | Qt::Dialog);
    setWindowModality(Qt::ApplicationModal);
    setFixedSize(g_iWidth, g_iHeight);

    m_textFont = QFont(AppFontManager::instance()->getAppCurFontFamily());
    m_textFont.setPixelSize(24);

    QLabel* pLabel = new QLabel(tr("Remark"), this);
    pLabel->setFont(m_textFont);
    m_pTextEdit = new QPlainTextEdit(this);
    m_pTextEdit->setFont(m_textFont);
    m_pTextEdit->installEventFilter(this);

    m_pBtnOk = new QPushButton();
    m_pBtnOk->setText(QObject::trUtf8("OK"));
    m_pBtnOk->setStyleSheet(BUTTON_STYLE);
    m_pBtnOk->setFixedSize(g_iButtonWidth, g_iButtonHeight);
    m_pBtnOk->setFont(m_textFont);

    m_pBtnCancel = new QPushButton();
    m_pBtnCancel->setText(QObject::trUtf8("Cancel"));
    m_pBtnCancel->setStyleSheet(BUTTON_STYLE);
    m_pBtnCancel->setFixedSize(g_iButtonWidth, g_iButtonHeight);
    m_pBtnCancel->setFont(m_textFont);

    connect(m_pBtnOk, SIGNAL(clicked()), this, SLOT(accept()));
    connect(m_pBtnCancel, SIGNAL(clicked()), this, SLOT(reject()));

    QHBoxLayout* pBtnLayout = new QHBoxLayout();
    pBtnLayout->addWidget(m_pBtnOk);
    pBtnLayout->addSpacing(g_iButtonSpacing);
    pBtnLayout->addWidget(m_pBtnCancel);
    pBtnLayout->setAlignment(Qt::AlignRight);

    QVBoxLayout* pMainLayout = new QVBoxLayout();
    pMainLayout->addWidget(pLabel);
    pMainLayout->addWidget(m_pTextEdit);
    pMainLayout->addLayout(pBtnLayout);
    pMainLayout->setMargin(0);

    setLayout(pMainLayout);

    SoftKeyBoard::setAttribute(KEYBOARD_MODALITY_PROPERTY, SoftKeyBoard::APPLICATION_MODAL);
}

FileCommentBox::~FileCommentBox()
{
    SoftKeyBoard::setAttribute(KEYBOARD_MODALITY_PROPERTY, SoftKeyBoard::NON_MODAL);

    if (m_bRotate)
    {
        SoftKeyBoard::setAttribute(KEYBOARD_ROTATE_PROPERTY, SoftKeyBoard::ROTATE_0_ANGLE);
        SoftKeyBoard::setAttribute(KEYBOARD_LOCATE_PROPERTY, SoftKeyBoard::LOCATE_NORMAL);
    }
//    SoftKeyBoard::resetAttribute();
//    SystemSetService::instance()->closeKeyBoard();
//    SystemSetService::instance()->initKeyBoard();
}

QString FileCommentBox::getRemark()
{
    return m_pTextEdit->toPlainText();
}


/*************************************************
功能： 设置旋转标识
输入参数:
    bRotate -- 是否旋转
*************************************************************/
void FileCommentBox::setRotate(bool bRotate)
{
    m_bRotate = bRotate;

    if (m_bRotate)
    {
        SoftKeyBoard::setAttribute(KEYBOARD_ROTATE_PROPERTY, SoftKeyBoard::ROTATE_90_ANGLE);
        SoftKeyBoard::setAttribute(KEYBOARD_LOCATE_PROPERTY, SoftKeyBoard::LOCATE_BOTTOM);
    }
}

/****************************
功能： 处理keyPress事件
输入参数:
        pEvent -- keyPress事件
*****************************/
void FileCommentBox::keyPressEvent(QKeyEvent *pEvent)
{
    switch(pEvent->key())
    {
    case Qt::Key_Escape://取消键
    {
        reject();
        break;
    }
    case Qt::Key_Enter:
    case Qt::Key_Return:
    {
        if(m_pBtnOk->hasFocus())
        {
            accept();
        }
        else if(m_pBtnCancel->hasFocus())
        {
            reject();
        }
        else
        {
            QDialog::keyPressEvent(pEvent);
        }
        break;
    }
    case Qt::Key_Up:
    case Qt::Key_Down:
    case Qt::Key_Left:
    case Qt::Key_Right:
    {
        if (m_pBtnOk == focusWidget())
        {
            m_pBtnCancel->setFocus();
        }
        else if (m_pBtnCancel == focusWidget())
        {
            m_pBtnOk->setFocus();
        }
        break;
    }
    default:
    {
        QDialog::keyPressEvent(pEvent);
        break;
    }
    }
}

/*************************************************
功能： 处理show事件
*************************************************************/
void FileCommentBox::showEvent(QShowEvent *pEvent)
{
    m_pTextEdit->setFocus();
    QDialog::showEvent(pEvent);
}

void FileCommentBox::paintEvent(QPaintEvent *pEvent)
{
    Q_UNUSED(pEvent);

    QBitmap bitmap(this->size());
    QPainter painter2(&bitmap);
    painter2.setPen(Qt::NoPen);
    painter2.fillRect(bitmap.rect(), Qt::white);
    painter2.setBrush(QColor(0, 0, 0));
    painter2.drawRoundedRect(rect(), 10, 10);
    setMask(bitmap);           // 绘制圆角外框

    QPainter painter(this);
    painter.setPen(Qt::NoPen);
    QLinearGradient linearGradient( QPoint(0,0),QPoint( width(),height() ) );
    linearGradient.setColorAt( 0,QColor( 130,181,238 ));
    linearGradient.setColorAt( 1,QColor( 189,217,247 )); // 设置usb设置整个界面从左至右渐变
    painter.setBrush( QBrush( linearGradient ) );
    painter.drawRect( rect() );   // 绘制整个提示框区域背景色渐变
}

bool FileCommentBox::eventFilter(QObject *pObject, QEvent *pEvent)
{
    if (pObject == m_pTextEdit && (pEvent->type() == QEvent::KeyPress || pEvent->type() == QEvent::InputMethod))
    {
        QString qstrText;
        if (pEvent->type() == QEvent::KeyPress)
        {
            QKeyEvent* pKeyEvent = static_cast<QKeyEvent*>(pEvent);
            int iKey = pKeyEvent->key();
            qstrText = pKeyEvent->text();
            if (iKey == Qt::Key_Return || iKey == Qt::Key_Space)
            {
                return true;
            }
        }
        else if (pEvent->type() == QEvent::InputMethod)
        {
            QInputMethodEvent* pInputMethodEvent = static_cast<QInputMethodEvent*>(pEvent);
            qstrText = pInputMethodEvent->commitString();
        }

        if (qstrText == "*" || qstrText == "|"
                || qstrText == ":" || qstrText == "\""
                || qstrText == "<" || qstrText == ">"
                || qstrText == "?" || qstrText == "."
                || qstrText == "\\" || qstrText == "/"
                || qstrText == "%")
        {
            QToolTip::setFont(m_textFont);
            QToolTip::showText(mapToGlobal(m_pTextEdit->pos()), tr("Cannot contain the following special symbols:") + "<br />" + Qt::escape("* | : \" < > ? . \\ / %"));
            return true;
        }

        // 长度不能超过32位中文字符
        QString qstrNewText = getRemark() + qstrText;
        if (qstrNewText.toLocal8Bit().length() > 64)
        {
            QToolTip::setFont(m_textFont);
            QToolTip::showText(mapToGlobal(m_pTextEdit->pos()), tr("The number of characters exceeds the limit.") + "<br />");
            return true;
        }
    }

    return QDialog::eventFilter(pObject, pEvent);
}
