/*
* Copyright (c) 2016.03，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: UHFPRPSAndPRPDDataSave.h
*
* 初始版本：1.0
* 作者：曹山
* 创建日期：2016年03月17日
* 摘要：该文件主要是定义了UHF PRPS、PRPD检测数据存储的子类
*/

#ifndef UHFPRPSANDPRPDDATASAVE_H
#define UHFPRPSANDPRPDDATASAVE_H

#include "dataSave/DataSave.h"

//data save
#include "datafile/datamap.h"
#include "datafile/datafile.h"
#include "datafile/prps/prpsdatamap.h"
#include "datafile/prps/prpddatamap.h"
#include "dataSave/DataStructures.h"

//uhf prps prpd检测数据信息
typedef struct _UHFPRPSPRPDDataInfo
{
    //file head
    quint8 ucFreq;

    //map head
    DataMapHead stPRPSHeadInfo;            //图谱通用的头部信息
    DataMapHead stPRPDHeadInfo;            //图谱通用的头部信息

    //map ext
    PRPSMapNS::PRPSMapInfo stPRPSInfo;
    PRPSMapNS::PRPDMapInfo stPRPDInfo;

    //map data
    QVector<double> vecPRPSData;
    QVector<UINT8> vecPRPSDataColor;
    /*
    xml格式下，"vecPRRepeatyData" 为单位时间脉冲重复率
    二进制下，"vecPRRepeatyData" 为脉冲次数
    */
    QVector<double> vecPRRepeatyData;
    QVector<UINT8> vecPRPDDataColor;
    QVector<UINT16> vecPRPDDataDischargeCnt;
    _UHFPRPSPRPDDataInfo()
    {
        ucFreq = 50;
        vecPRPSData.clear();
        vecPRPSDataColor.clear();
        vecPRRepeatyData.clear();
        vecPRPDDataColor.clear();
        vecPRPDDataDischargeCnt.clear();
    }
}UHFPRPSPRPDDataInfo;

class MODULESHARED_EXPORT UHFPRPSAndPRPDDataSave : public DataSave
{
public:
    /************************************************
     * 函数名   : UHFPRPSAndPRPDDataSave
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 构造函数
     ************************************************/
    UHFPRPSAndPRPDDataSave();

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData);

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData, const QString &qsSavedPath);

    QString saveData(const QDateTime &dtime, void *pData, const QString &qsSavedPath);

    INT32 getData(const QString& strFileName, void *pData);

    /**********************************************
     * 功能：设置是否保存录屏数据
     * 输入参数：
     *      bSaveVideoData：true -- 录屏数据，false -- 常规数据
     * ***********************************************/
    void setSaveVideoData(bool bSaveVideoData);

protected:
    /************************************************
     * 函数名   : getDataTypeFolder
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件夹名
     * 功能     : 获取图谱类型文件存储最上层文件夹名
     ************************************************/
    QString getDataTypeFolder(void);

    /************************************************
     * 函数名   : getFileNameSuffix
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件后缀名
     * 功能     : 获取数据文件后缀名
     ************************************************/
    QString getFileNameSuffix(void);

    /************************************************
     * 函数名   : saveHeadData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储数据头信息
     ************************************************/
    void saveHeadData(XMLDocument& doc);

    /************************************************
     * 函数名   : saveExtInformation
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储可扩展信息
     ************************************************/
    void saveExtInformation(XMLDocument& doc);

    /************************************************
     * 函数名   : saveRawData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储数据部分
     ************************************************/
    void saveRawData(XMLDocument& doc);


    /************************************************
     * 函数名   : parseData
     * 输入参数 : baData: 数据
     * 输出参数 : pData: 解析到的数据
     * 返回值   : void
     * 功能     : 解析数据
     ************************************************/
    void parseData(const QByteArray& baData, void *pData, const QString& strFileName = "");
private:
    void setPRPSMapHead(DataMap *pMap);

    void setPRPDMapHead(DataMap *pMap);


    void setPRPSMapInfo(PRPSDataMap *pMap);


    void setPRPDMapInfo(PRPDDataMap *pMap);


    void setPRPSMapData(PRPSDataMap *pMap);


    void setPRPDMapData(PRPDDataMap *pMap);


    void getDataFilePath(QString &strAbsolutePath);

    void addPRPSMap(DataFile *pFile);

    void addPRPDMap(DataFile *pFile);

    void setPlaybackInfoByFileHead(DataFile *psDataFile);
    void setPlaybackInfoByMapHead(PRPSDataMapBase *pMap);
    void setPlaybackInfoByMapInfo( PRPSDataMapBase *pMap);
    void setPlaybackInfoByMapInfo( const PRPSMapNS::PRPSMapInfo &stMapInfo);


    /****************************
    功能： 在回放中显示
    输入参数: void
    *****************************/
    void displayMap(QVector<double> &vecPRPSRawData, QVector<double> &vecPRPDRawData);

    void registerMaps();

    /****************************
    功能： 获取图谱采样数据
    输入参数:
        pMap -- 文件解析得到的图谱
        iDataPointCount -- 图谱数据数量
    *****************************/
    void setPRPSPlaybackData( PRPSDataMapBase * pMap, QVector<double> &vecPRPSRawData);


    void setPRPDPRRepeatyPlaybackData( PRPSDataMapBase * pMap, QVector<double> &vecPRRepeatyData);


    void getFileHead(DataFile *psDataFile);


    void getPRPSMapHead(DataFile *psDataFile, PRPSDataMapBase * pMap);


    void getPRPDMapHead(DataFile *psDataFile, PRPSDataMapBase * pMap);

    void getPRPSMapInfo(PRPSDataMapBase * pMap);


    void getPRPDMapInfo(PRPSDataMapBase * pMap);

    /****************************
    功能： 获取图谱ext数据
    输入参数:
        pMap -- 文件解析得到的图谱
        iDataPointCount -- 图谱数据数量
    *****************************/
    void getPRPDMapInfo( const PRPSMapNS::PRPDMapInfo &stMapInfo);

    /****************************
    功能： 获取图谱ext数据
    输入参数:
        pMap -- 文件解析得到的图谱
        iDataPointCount -- 图谱数据数量
    *****************************/
    void getPRPSMapInfo( const PRPSMapNS::PRPSMapInfo &stMapInfo);

private:
    UHFPRPSPRPDDataInfo *m_pUHFPRPSPRPDDataInfo;     //UHF PRPS 和PRPD检测数据信息
    bool m_bSaveVideoData;      //是否保存录屏数据
};

#endif // UHFPRPSANDPRPDDATASAVE_H
