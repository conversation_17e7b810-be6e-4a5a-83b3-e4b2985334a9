#ifndef PRPSLABELITEM_H
#define PRPSLABELITEM_H

/*
* Copyright (c) 2015.11，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: PrpsLabelItem.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年4月10日
* 摘要：该文件主要定义了plot各种定制显示标签的集合

* 当前版本：1.0
*/


#include "prps/prpscomponent/prpsplotitem.h"
#include <QColor>
#include <QFont>
#include <QPixmap>
#include "Widget.h"
class WIDGET_EXPORT PrpsLabelItem : public PrpsPlotItem
{
    Q_OBJECT
public:
    /************************************************
     * 功能: 构造函数
     * 入参：parent -- 父控件指针
     ************************************************/
    explicit PrpsLabelItem(QObject *parent = 0);

    /************************************************
     * 功能: 设置字体
     * 入参：font -- 字体
     ************************************************/
    void setFont( const QFont & font );

    /************************************************
     * 功能: 获得字体
     * 返回值：font -- 字体
     ************************************************/
    QFont font( void );

    /************************************************
     * 功能: 设置字体颜色
     * 入参：color -- 字体颜色
     ************************************************/
    void setFontColor( const QColor & color );

    /************************************************
     * 功能: 获得字体颜色
     * 返回值：字体颜色
     ************************************************/
    QColor fontColor( void );

    /************************************************
     * 功能: 设置文本显示
     * 入参：strText -- 待显示文本
     *      flags -- 对齐方式
     ************************************************/
    void setText( const QString& strText,int flags = Qt::AlignLeft );

    /************************************************
     * 功能: 获得显示的文本
     * 返回值：待显示文本
     ************************************************/
    QString text( void ) const;

    /************************************************
     * 功能: 设置显示的图片
     * 入参：待显示图片
     ************************************************/
    void setPixmap( const QPixmap& qPixmap );

    /************************************************
     * 功能: 获得显示的图片
     * 返回值：待显示图片
     ************************************************/
    const QPixmap& pixmap( void ) const;

    /************************************************
     * 功能: 提供各item方便绘制的接口
     * 入参：painter -- 绘图工具
     *      canvasRect -- plot的大小
     ************************************************/
    virtual void draw( QPainter *painter,
                       const QRectF &canvasRect );

    /************************************************
     * 功能: item尺寸发生改变时调用的接口
     ************************************************/
    virtual void onSizeChanged( void );
private:
    QString m_strText;  // 标签显示的文本
    QPixmap m_qPixmap;  // 标签显示的图片
    QFont m_qFont;      // 标签的字体
    QColor m_qFontColor;// 标签的字体颜色
    int m_iAlignFlags;  // 对齐方式
};

#endif // PRPSLABELITEM_H
