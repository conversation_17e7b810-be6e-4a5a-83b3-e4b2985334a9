/*
* Copyright (c) 2016.09，南京华乘电气科技有限公司
* All rights reserved.
*
* HCModel.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2016年9月9日
* 摘要：通用协议层
* 协议层根据具体协议要求，提供协议相关的业务操作接口及实现
* 当前版本：1.0
*/
#ifndef HCPROTOCOL_H
#define HCPROTOCOL_H

#include <QObject>
#include "HCCom.h"

class CORESHARED_EXPORT HCProtocol : public QObject
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent -- 父Object
    输出参数：NULL
    返回值： NULL
    *************************************************/
    explicit HCProtocol(QObject *parent = 0);

    /*************************************************
    功能： 设置通讯组件
    输入参数:
        pCom -- 通讯组件
    输出参数：NULL
    返回值： NULL
    *************************************************/
    void setCom( HCCom* pCom );
protected:
    /*************************************************
    功能： 通讯组件
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    HCCom* com( void );
protected slots:
    /*************************************************
    功能： 响应通讯组件数据准备好
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    virtual void onDataReady( void ) = 0;
private:
    HCCom* m_pCom;    //通讯组件
};

#endif // HCPROTOCOL_H
