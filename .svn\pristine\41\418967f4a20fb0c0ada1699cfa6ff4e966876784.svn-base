#ifndef HFCTPRPSDISTRIBUTENETTESTBGVIEW_H
#define HFCTPRPSDISTRIBUTENETTESTBGVIEW_H

#include <QWidget>
#include "hfct/HFCT.h"
#include "buttonBar/LabelButtonBar.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "prps/prpsview/hfctprpsunionview.h"
#include "hfct/dataSave/HFCTPRPSAndPRPDDataSave.h"
#include "hfct/hfctprpsview/hfctprpsviewbase.h"
#include "View.h"
#include "messageBox/msgbox.h"
#include "loadingView/textloadingview.h"
//data save
#include "datafile/datamap.h"
#include "datafile/prps/prpsdatamap.h"
#include "datafile/prps/prpddatamap.h"
#include "distributenetaccess/distributenetaccess_def.h"

namespace DataSpecificationNS {
    class DataSpecification;
    class PRPSSpectrum;
    class PRPDSpectrum;
    class PRPSExtInformation;
    class PRPDExtInformation;
}

class CommonItemListView;
class HFCTPRPSDistributeNetTestBGView : public HFCTPrpsViewBase
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        parent:父控件指针
    *************************************************************/
    explicit HFCTPRPSDistributeNetTestBGView(const QString &strTitle, QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~HFCTPRPSDistributeNetTestBGView( );

    /*************************************************
    功能： 设置测点信息
    *************************************************************/
    void setTestInfo(const QString& qstrTaskId, const DistributeNetAccessNS::TestpointInfo& stTestpointInfo, const DistributeNetAccessNS::RealAssetInfo& stRealAssetInfo);

signals:
    void sigAutoSwitch();

protected:
    /*************************************************
    功能： 键盘事件
    输入参数:
        event -- 事件
    *************************************************************/
    void keyPressEvent( QKeyEvent* event );

    /*************************************************
    功能： 响应S键事件
    *************************************************************/
    void onSKeyPressed();

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

    /*************************************************
    功能： 槽，响应诊断结果
    输入参数：
            qspDiagResultInfo -- 诊断结果
    *************************************************************/
    void onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo);

private:

    /************************************************
     * 函数名   : setButtonBarDatas
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置buttonbar显示的参数
     ************************************************/
    void setButtonBarDatas();

    /************************************************
     * 函数名   : createButtonbar
     * 输入参数 : parent---父控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建buttonbar
     ************************************************/
    PushButtonBar* createButtonbar(QWidget *parent);

    /************************************************
     * 函数名   : loadData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 载入数据
     ************************************************/
    void loadData();

    /************************************************
     * 函数名   : deleteData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 删除数据
     ************************************************/
    void deleteData();

    /************************************************
     * 函数名   : restoreDefault
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 恢复默认参数
     ************************************************/
    void restoreDefault();

    /************************************************
     * 函数名   : setSampleBtnText
     * 输入参数 : bIsSampling: 是否处于采样状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 更新采样状态设置采样按钮显示的文本
     ************************************************/
    void setSampleBtnText(bool bIsSampling);

    /************************************************
     * 函数名   : setConfigData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :
     ************************************************/
    void setConfigData();


    /*************************************************
    功能： 保存设置
    *************************************************************/
    bool saveConfig(void);


    /************************************************
     * 函数名   : initData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 初始化数据成员
     ************************************************/
    void initData();

    /************************************************
     * 函数名   : createChart
     * 输入参数 : parent---父控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建图谱
     ************************************************/
    ChartWidget *createChart(QWidget *parent);

    /************************************************
     * 函数名   : setChartParameters
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 给图谱设置参数
     ************************************************/
    void setChartParameters();

    /************************************************
     * 函数名   : setAllWorkSets
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置所有的工作参数
     ************************************************/
    void setAllWorkSets();

    /*************************************************
    功能： 保存图谱信息部分内容到保存结构体中
    输入参数: pMap -- 保存图谱数据的对象指针
    输出参数：NULL
    返回值： NULL
    *************************************************/
    void fillPRPSDataInfo(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum);

    /*************************************************
    功能： 保存图谱信息部分内容到保存结构体中
    输入参数: pMap -- 保存图谱数据的对象指针
    输出参数：NULL
    返回值： NULL
    *************************************************/
    void fillPRPDDataInfo(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum);

    /*************************************************
    功能： 保存图谱数据部分内容到保存结构体中
    输入参数: pMap -- 保存图谱数据的对象指针
    输出参数：NULL
    返回值： NULL
    *************************************************/
    void fillPRPSData(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum);

    /*************************************************
    功能： 保存图谱数据部分内容到保存结构体中
    输入参数: pMap -- 保存图谱数据的对象指针
    输出参数：NULL
    返回值： NULL
    *************************************************/
    void fillPRPDData(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum);

    INT32 quantizationAmpSection(INT32 iAmp, float fAmpLower, float fAmpUpper, INT32 iQuantizationAmpCnt);

    /*************************************************
    功能： 保存数据
    返回：
        保存结果
    *************************************************************/
    QString saveTestData();

    /****************************
    功能： 回放文件
    输入参数:
        strFileName -- 文件名
    *****************************/
    bool loadTestDataFile( const QString& strFileName);

    bool getData(const QString& strFileName, void *pData);

    void getFileHead(DataSpecificationNS::DataSpecification* pDataSpecification);


    void getPRPSMapHead(DataSpecificationNS::PRPSSpectrum* pPRPSSpectrum);


    void getPRPDMapHead(DataSpecificationNS::PRPDSpectrum* pPRPDSpectrum);

    /****************************
    功能： 获取图谱ext数据
    输入参数:
        pMap -- 文件解析得到的图谱
        iDataPointCount -- 图谱数据数量
    *****************************/
    void getPRPDMapInfo(const DataSpecificationNS::PRPDExtInformation* pPRPDExtInformation);

    /****************************
    功能： 获取图谱ext数据
    输入参数:
        pMap -- 文件解析得到的图谱
        iDataPointCount -- 图谱数据数量
    *****************************/
    void getPRPSMapInfo(const DataSpecificationNS::PRPSExtInformation* pPRPSExtInformation);

    /****************************
    功能： 在回放中显示
    输入参数: void
    ************************************************/
    void displayMap(HFCTPRPSPRPDDataInfo &stDataInfo, const QString& strFileName);

    void registerMaps();


    /***************************************************
     * 功能：保存数据
     * *************************************************/
    void saveData();

private slots:

    void onTitleBarClicked( void );

    /************************************************
     * 函数名   : onDataRead
     * 输入参数 : stData: 采样数据
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，处理收到的采样数据
     ************************************************/
    void onDataRead(HFCT::PRPSData data, MultiServiceNS::USERID Id);

    /************************************************
     * 函数名   : onSignalChanged
     * 输入参数 : eState: 信号状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，信号状态的改变
     ************************************************/
    void onSignalChanged(Module::SignalState eState);

    /************************************************
     * 函数名   : onSyncStateChanged
     * 输入参数 : eState: 同步状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，同步状态的改变
     ************************************************/
    void onSyncStateChanged(Module::SyncState eState);

private slots:
    void deleteSelectedFile(qint32 uiItem);
    bool loadSelectedFile(qint32 uiItem);
    void onItemActivated( qint32 );
private:
    enum OperationType
    {
        OPERATION_INVALID,
        OPERATION_DELETE,
        OPERATION_LOAD
    };
    OperationType m_eOperationType;


    QLabel *m_pBayNameLabel;
    QLabel *m_pLoadFileName;//回放文件名

    QString m_strLatestFileName;//测试数据文件路径
    CommonItemListView *m_pCommonItemListView;

    QString m_strSavedPath;
    HFCTPRPSPRPDDataInfo *m_pHFCTPRPSPRPDDataInfo;     //PRPS 和PRPD检测数据信息
    bool m_bPlayBacked;

    ConfigInstance* m_pConfig;//配置模块

    Module::SignalState m_eSignalState;// 信号状态
    Module::SyncSource m_eSyncSource;//同步方式
    Module::SyncState m_eSyncState;//同步状态
    HFCT::Gain m_eGain;//前置增益
    int m_iPhaseAlias; //相位偏移

    HFCT::PRPSData m_stData; //采样数据
    UINT16 m_usMaxSpectrumValue;// 最大值
    QVector<PRPSMaxValue> m_vMaxValueVector;  //存放最大采样数据的容器
    HFCT::SpectrumState m_eSpectrumState;//最大数据的频谱状态
    UINT8 m_ucSysFreq;//系统频率
    ControlButton *m_pSampleBtn; //采样按钮
    ControlButton *m_pSaveBtn; //保存按钮
	
    TextLoadingView *m_pLoadingWidget;
    QString m_qstrTaskId;
    DistributeNetAccessNS::RealAssetInfo m_stRealAssetInfo; // 设备信息
    DistributeNetAccessNS::TestpointInfo m_stTestpointInfo;//测点信息
};

#endif // HFCTPRPSDISTRIBUTENETTESTBGVIEW_H
