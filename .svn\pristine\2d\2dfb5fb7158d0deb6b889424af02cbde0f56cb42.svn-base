#ifndef PDATASKRUNNABLE_H
#define PDATASKRUNNABLE_H

#include <QThreadPool>
#include <QRunnable>

#include "module_global.h"

class MODULESHARED_EXPORT TaskReadRunnable : public QRunnable
{
    //智能巡检任务描述文件读取Runnable
public:
    TaskReadRunnable(int iIndex = 0);
    ~TaskReadRunnable();

    void run();

    int getCurTaskIndex();

    void setCurTaskIndex(int iIndex);

private:
    int m_iIndex;

};

class MODULESHARED_EXPORT TaskSaveRunnable : public QRunnable
{
    //智能巡检任务描述文件保存Runnable
public:
    TaskSaveRunnable();
    ~TaskSaveRunnable();

    void run();
};

#endif // PDATASKRUNNABLE_H
