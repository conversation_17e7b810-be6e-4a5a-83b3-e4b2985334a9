#include "distributenettestpointview.h"
#include "distributenetviewconfig.h"
#include "aetestview/aedistributenettestview.h"
#include "aetestview/aedistributenettestbgview.h"
#include "tevtestview/tevampdistributenettestview.h"
#include "tevtestview/tevampdistributenettestbgview.h"
#include "uhftestview/uhfprpsdistributenettestview.h"
#include "uhftestview/uhfprpsdistributenettestbgview.h"
#include "hfcttestview/hfctprpsdistributenettestview.h"
#include "hfcttestview/hfctprpsdistributenettestbgview.h"
#include "systemsetting/systemsetservice.h"
#include "PDAUi/PDAUiBean/pdalistchart.h"
#include "distributenetaccess/distributetaskmanager/distributetaskmanager.h"
#include "window/Window.h"
#include "global_log.h"

#define SINGLE_TASK 1
#define TIMER_ID_INIT -1
#define AUTO_SWITCH_TIMEOUT 1000

DistributeNetTestPointView::DistributeNetTestPointView(const QString& qstrTaskId
                                                       , const DistributeNetAccessNS::AssetInfo& stAssetInfo
                                                       , QWidget* parent)
    : PDAListView(QObject::trUtf8("Test Point List"), parent)
    , m_qstrTaskId(qstrTaskId)
    , m_stAssetInfo(stAssetInfo)
    , m_iAutoSwitchTimerId(TIMER_ID_INIT)
    , m_bAutoSwitching(false)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);
    m_eAccessMode = SystemSetService::instance()->getCustomAccessMode();

    initTaskInfos();
    connect(DistributeTaskManager::instance(), SIGNAL(sigTestPointTestStateChanged(QString)), this, SLOT(onTestPointTestStateChanged(QString)));
}

DistributeNetTestPointView::~DistributeNetTestPointView()
{
    disconnect(DistributeTaskManager::instance(), SIGNAL(sigTestPointTestStateChanged(QString)), this, SLOT(onTestPointTestStateChanged(QString)));
}

void DistributeNetTestPointView::showEvent(QShowEvent* pEvent)
{
    if(!(DistributeTaskManager::instance()->isCurTaskTestCompleted()))
    {
        PDAListView::showEvent(pEvent);
        m_iAutoSwitchTimerId = startTimer(AUTO_SWITCH_TIMEOUT);
    }

    return;
}

void DistributeNetTestPointView::closeEvent(QCloseEvent* pEvent)
{
    stopAutoSwitchTimer();
    PDAListView::closeEvent(pEvent);
}

void DistributeNetTestPointView::timerEvent(QTimerEvent* pEvent)
{
    if(pEvent->timerId() == m_iAutoSwitchTimerId)
    {
        stopAutoSwitchTimer();
        switchTestView();
    }

    return;
}

void DistributeNetTestPointView::initTaskInfos()
{
    m_pChart->deleteAllItem();

    // 添加任务信息
    QList<PDAListChart::ListItemInfo> itemInfos;
    itemInfos.clear();
    bool bTicked = false;
    int iTested = 0;

    const QVector<DistributeNetAccessNS::TestpointInfo>& qvTestpointInfos = m_stAssetInfo.qvTestpointInfos;
    for(int i = 0, iSize = qvTestpointInfos.size(); i < iSize; ++i)
    {
        iTested = DistributeNetAccessNS::TASK_TESTED == qvTestpointInfos[i].bTested ? 1 : 0;
        bTicked = DistributeNetAccessNS::TASK_TESTED == qvTestpointInfos[i].bTested;
        // -1为特殊表示，原item为已测设备和设备总数，此处标定为-1则item不再显示
        if(SystemSet::ACCESS_USB_MODE == m_eAccessMode)
        {
            itemInfos << PDAListChart::ListItemInfo(qvTestpointInfos.at(i).qstrName, iTested, 1, bTicked, PDAListItem::LABEL_MODE, qvTestpointInfos.at(i).qstrId);
        }
        else
        {
            itemInfos << PDAListChart::ListItemInfo(qvTestpointInfos.at(i).qstrName, iTested, 1, bTicked, PDAListItem::CHECK_BOX, qvTestpointInfos.at(i).qstrId);
        }
    }

    m_pChart->addItems(itemInfos);
    if(m_pChart->allItems().size() > 0)
    {
        m_pChart->setCurrentItemSelected(0);
    }

    return;
}

/*************************************************
功能： 处理条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void DistributeNetTestPointView::onItemClicked(int id)
{
    // 手动破坏自动跳转的业务流程
    stopAutoSwitchTimer();

    if(!m_bAutoSwitching)
    {
        if(0 <= id && id < m_stAssetInfo.qvTestpointInfos.size())
        {
            QString qstrTestPointId = m_stAssetInfo.qvTestpointInfos[id].qstrId;
            DistributeNetAccessNS::TestpointInfo stTestPointInfo = DistributeTaskManager::instance()->getTestPointInfo(qstrTestPointId);
            showTestView(stTestPointInfo);
        }
    }

    return;
}

void DistributeNetTestPointView::switchTestView()
{
    m_bAutoSwitching = true;

    DistributeNetAccessNS::TestpointInfo stTestPointInfo;
    if(DistributeTaskManager::instance()->getNextUntestTestPointInfo(stTestPointInfo))
    {
        if(stTestPointInfo.qstrAssetId == m_stAssetInfo.stRealAssetInfo.qstrId)
        {
            selectItem(stTestPointInfo.qstrId);
            showTestView(stTestPointInfo);
        }
        else
        {
            logWarning(QString("change test point under other asset (%1)").arg(stTestPointInfo.qstrAssetId));
            close();
            emit sigAutoClosed();
        }
    }
    else
    {
        logWarning("get test point position failed");
    }

    m_bAutoSwitching = false;

    return;
}

void DistributeNetTestPointView::showTestView(const DistributeNetAccessNS::TestpointInfo &stTestPointInfo)
{
    if(stTestPointInfo.bBgTestPoint)
    {
        switch(stTestPointInfo.eType)
        {
        case DistributeNetAccessNS::TPT_AE:
        {
            AEDistributeNetTestBGView* pView = new AEDistributeNetTestBGView(QObject::trUtf8("AE BKGD"));
            pView->setTestInfo(m_qstrTaskId, stTestPointInfo, m_stAssetInfo.stRealAssetInfo);
            pView->setBayName(QObject::trUtf8("Asset Name: ") + m_stAssetInfo.stRealAssetInfo.qstrName);
            connect(pView, SIGNAL(sigAutoSwitch()), this, SLOT(onTestViewClosed()));
            pView->show();
        }
            break;
        case DistributeNetAccessNS::TPT_TEV:   //TEV幅值
        {
            TEVAmpDistributeNetTestBGView* pView = new TEVAmpDistributeNetTestBGView(QObject::trUtf8("TEV BKGD"));
            pView->setTestInfo(m_qstrTaskId, stTestPointInfo, m_stAssetInfo.stRealAssetInfo);
            connect(pView, SIGNAL(sigAutoSwitch()), this, SLOT(onTestViewClosed()));
            pView->show();
        }
            break;
        case DistributeNetAccessNS::TPT_UHF:      //UHF PRPS/PRPD
        {
            UHFPRPSDistributeNetTestBGView* pView = new UHFPRPSDistributeNetTestBGView(QObject::trUtf8("UHF BKGD"));
            pView->setTestInfo(m_qstrTaskId, stTestPointInfo, m_stAssetInfo.stRealAssetInfo);
            connect(pView, SIGNAL(sigAutoSwitch()), this, SLOT(onTestViewClosed()));
            pView->show();
        }
            break;
        case DistributeNetAccessNS::TPT_HFCT:      //HFCT PRPS/PRPD
        {
            HFCTPRPSDistributeNetTestBGView* pView = new HFCTPRPSDistributeNetTestBGView(QObject::trUtf8("HFCT BKGD"));
            pView->setTestInfo(m_qstrTaskId, stTestPointInfo, m_stAssetInfo.stRealAssetInfo);
            connect(pView, SIGNAL(sigAutoSwitch()), this, SLOT(onTestViewClosed()));
            pView->show();
        }
            break;
        default:
        {
            qWarning("TestPointView::onItemClicked: type is not valid!");
        }
        }
    }
    else
    {
        if(DistributeTaskManager::instance()->getBGFileName(m_qstrTaskId, stTestPointInfo.eType).isEmpty())
        {
            MsgBox::warning("", QObject::trUtf8("Please complete the background test."));
        }
        else
        {
            switch(stTestPointInfo.eType)
            {
            case DistributeNetAccessNS::TPT_AE:
            {
                AEDistributeNetTestView* pView = new AEDistributeNetTestView(QObject::trUtf8("AE Detect"));
                pView->setTestInfo(m_qstrTaskId, stTestPointInfo, m_stAssetInfo.stRealAssetInfo);
                connect(pView, SIGNAL(sigAutoSwitch()), this, SLOT(onTestViewClosed()));
                pView->setTestPointName(QObject::trUtf8("Test Point Name: ") + stTestPointInfo.qstrName);
                pView->setBayName(QObject::trUtf8("Asset Name: ") + m_stAssetInfo.stRealAssetInfo.qstrName);
                pView->show();
            }
                break;
            case DistributeNetAccessNS::TPT_TEV:   //TEV幅值
            {
                TEVAmpDistributeNetTestView* pView = new TEVAmpDistributeNetTestView(QObject::trUtf8("TEV Detect"));
                pView->setTestInfo(m_qstrTaskId, stTestPointInfo, m_stAssetInfo.stRealAssetInfo);
                connect(pView, SIGNAL(sigAutoSwitch()), this, SLOT(onTestViewClosed()));
                pView->show();
            }
                break;
            case DistributeNetAccessNS::TPT_UHF:      //UHF PRPS/PRPD
            {
                UHFPRPSDistributeNetTestView* pView = new UHFPRPSDistributeNetTestView(QObject::trUtf8("UHF Detect"));
                pView->setTestInfo(m_qstrTaskId, stTestPointInfo, m_stAssetInfo.stRealAssetInfo);
                connect(pView, SIGNAL(sigAutoSwitch()), this, SLOT(onTestViewClosed()));
                pView->show();
            }
                break;
            case DistributeNetAccessNS::TPT_HFCT:      //HFCT PRPS/PRPD
            {
                HFCTPRPSDistributeNetTestView* pView = new HFCTPRPSDistributeNetTestView(QObject::trUtf8("HFCT Detect"));
                pView->setTestInfo(m_qstrTaskId, stTestPointInfo, m_stAssetInfo.stRealAssetInfo);
                connect(pView, SIGNAL(sigAutoSwitch()), this, SLOT(onTestViewClosed()));
                pView->show();
            }
                break;
            default:
            {
                qWarning("TestPointView::onItemClicked: type is not valid!");
            }
            }
        }
    }

    return;
}

void DistributeNetTestPointView::stopAutoSwitchTimer()
{
    if (TIMER_ID_INIT != m_iAutoSwitchTimerId)
    {
        killTimer(m_iAutoSwitchTimerId);
        m_iAutoSwitchTimerId = TIMER_ID_INIT;
    }

    return;
}

/*************************************************
功能： 槽，响应测量界面关闭的信号
*************************************************************/
void DistributeNetTestPointView::onTestViewClosed()
{
    DistributeNetAccessNS::TestpointInfo stTestPointInfo;
    DistributeTaskManager::instance()->getNextUntestTestPointInfo(stTestPointInfo);
    if(stTestPointInfo.qstrAssetId == m_stAssetInfo.stRealAssetInfo.qstrId)
    {
        m_iAutoSwitchTimerId = startTimer(AUTO_SWITCH_TIMEOUT);
    }
    else
    {
        stopAutoSwitchTimer();
        close();
        emit sigAutoClosed();
    }

    return;
}

void DistributeNetTestPointView::onTestPointTestStateChanged(QString qstrId)
{
    int iIndex = -1;

    DistributeNetAccessNS::TestpointInfo stTestPointInfo = DistributeTaskManager::instance()->getTestPointInfo(qstrId);
    for (int i = 0, iSize = m_stAssetInfo.qvTestpointInfos.size(); i < iSize; ++i)
    {
        if (stTestPointInfo.qstrId == m_stAssetInfo.qvTestpointInfos[i].qstrId)
        {
            iIndex = i;
            m_stAssetInfo.qvTestpointInfos[i] = stTestPointInfo;
            break;
        }
    }

    QList<PDAListChart::ListItemInfo> itemInfos = m_pChart->allItems();
    if (iIndex >= 0 && iIndex < itemInfos.size())
    {
        PDAListChart::ListItemInfo tmpInfo = itemInfos.at(iIndex);
        tmpInfo.m_iTestedCount = 1;
        tmpInfo.m_iTotalCount = 1;
        tmpInfo.m_bIsTicked = true;
        m_pChart->setItemInfo(tmpInfo, iIndex);
    }

    return;
}

void DistributeNetTestPointView::selectItem(QString qstrId)
{
    int iIndex = m_pChart->indexOfItem(qstrId);
    if(0 <= iIndex && iIndex < m_pChart->itemCount())
    {
        m_pChart->setCurrentItemSelected(static_cast<quint32>(iIndex));
    }

    return;
}

