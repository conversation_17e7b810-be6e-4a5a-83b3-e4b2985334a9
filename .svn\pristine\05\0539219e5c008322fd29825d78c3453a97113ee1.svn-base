/*
* Copyright (c) 2016.3，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：HFCTSpectrumChart.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月2日
* 摘要：该文件主要定义特高频周期图谱的相关信息

* 当前版本：1.0
*/

#ifndef HFCTSPECTRUMCHART_H
#define HFCTSPECTRUMCHART_H
#if 1
#include <QWidget>
#include <QHash>
#include <QList>
#include <QLabel>
#include <QTimerEvent>
#include <QVector>
#include "widgets/sync/SyncWidget.h"
#include "widgets/spectrumChart/PowerReferenceChart.h"
#include "widgets/spectrumChart/SpectrumChart.h"
#include "datadefine.h"
#include "chartwidget/ChartWidget.h"
#include "View.h"
#include "hfct/HFCT.h"

class HFCTPeriodChart : public ChartWidget
{
    Q_OBJECT

public:

    /****************************
    函数名： HFCTPeriodChart( QWidget *parent )
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 构造函数
    *****************************/
    explicit HFCTPeriodChart( QWidget *parent = 0 );

    /****************************
    函数名： ~HFCTPeriodChart();
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 析构函数
    *****************************/
    ~HFCTPeriodChart();

    /****************************
    函数名： addSample( float fXscale,float fYscale )
    输入参数:fXscale:数据占X轴的比例，fYscale:数据占Y轴的比例
    输出参数：NULL
    返回值：NULL
    功能： 添加数据
    *****************************/
    void addSample( const HFCT::PeriodData &rawData );

    /****************************
    函数名： samples( void )
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 返回存放的原始数据
    *****************************/
    QVector< INT8 > const& samples( void ) const;

    /************************************************
     * 函数名    :setSignalStatus
     * 输入参数  ：信号状态
     * 输出参数  ：NULL
     * 返回值   ：NULL
     * 功能     ：设置信号状态
     ************************************************/
    void setSignalStatus(Module::SignalState eSignalState);

    /************************************************
     * 函数名    :setSampleMode
     * 输入参数  ：eSampleMode -- 采集模式
     * 功能     ：设置采集模式
     ************************************************/
    void setSampleMode( Module::SampleMode eSampleMode );

    /****************************
    输入参数:eGain:增益
    功能： 设置增益(该接口设置为增益值本身，是因为保存的数据结构中增益为具体数值，为了进行统一)
    *****************************/
    void setGain( qint8 cGain );

    INT8 ampMin();


    INT8 ampMax();

    /****************************
    函数名： clear( void )
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能： 清除显示
    *****************************/
    void clear( void );

    /****************************
    函数名： setAlarmValue( UINT16 usAlarm )
    输入参数:usAlarm:报警值
    输出参数：NULL
    返回值：NULL
    功能： 设置红色报警值
    *****************************/
    void setAlarmValue( UINT16 usAlarm );

    /****************************
    函数名： setWarnValue( UINT16 usWarn )
    输入参数:usWarn:警告值
    输出参数：NULL
    返回值：NULL
    功能： 设置黄色警告值
    *****************************/
    void setWarnValue( UINT16 usWarn );

    /****************************
    函数名： setRunningStatus( bool bRunningMode )
    输入参数:bRunningMode:运行状态
    输出参数：NULL
    返回值：NULL
    功能： 设置运行状态
    *****************************/
    void setRunningStatus( bool bRunningMode );

    /****************************
    函数名： translatePhase(INT32 iPhase)
    输入参数:iPhase：移动的相位（0~ 354）
    输出参数：NULL
    返回值：NULL
    功能： 移动相位
    *****************************/
    void translatePhase( INT32 iPhase );

    /****************************
    输入参数:cMaxSpectrum:最大频谱值
           eSpectrumState -- 频谱状态
    功能： 设置最大频谱值
    *****************************/
    void setMaxSpectrum( INT8 cMaxSpectrum,HFCT::SpectrumState eSpectrumState );

    /****************************
    返回值：INT8 -- 最大频谱值
    功能： 获取最大频谱值
    *****************************/
    INT8 maxValue( void );

    /****************************
    返回值：频谱状态
    功能： 获取频谱状态
    *****************************/
    HFCT::SpectrumState spectrumState( void );

    /****************************
    函数名： setSyncState( SyncState eSyncState )
    输入参数:eSyncState:同步状态
    输出参数：NULL
    返回值：NULL
    功能： 设置同步状态
    *****************************/
    void setSyncState( Module::SyncState eSyncState );

    /****************************
    函数名： setSyncSource( SyncSource eSyncSource )
    输入参数:eSyncSource:同步源
    输出参数：NULL
    返回值：NULL
    功能： 设置同步源
    *****************************/
    void setSyncSource( Module::SyncSource eSyncSource );

protected:
    /****************************
    函数名： timerEvent( QTimerEvent *pEvent );
    输入参数:pEvent：定时事件
    输出参数：NULL
    返回值：NULL
    功能： 重载时间函数
    *****************************/
    void timerEvent( QTimerEvent *pEvent );

private:
    /****************************
    函数名： dataInit( void )
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 数据初始化
    *****************************/
    void dataInit( void );

    /****************************
    函数名： createMainView( void );
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 创建界面整体布局
    *****************************/
    void createMainView( void );

    /****************************
    函数名： getColorFromData( INT8 cData )
    输入参数: cData:数据
    输出参数：NULL
    返回值：Qt::GlobalColor：颜色
    功能： 根据数据大小获得对应的颜色
    *****************************/
    Qt::GlobalColor getColorFromData( INT8 cData );

    /****************************
    函数名： makeHFCTModel( void );
    输入参数:NULL
    输出参数：NULL
    返回值：NULL
    功能： 整理数据，set给图谱部分
    *****************************/
    void makeHFCTModel( void );

    /************************************************
     * 输入参数  ：eSampleMode -- 采集模式
     * 功能     ：根据采集模式更新运行状态
     ************************************************/
    void updateRunningStatus( Module::SampleMode eSampleMode );
private:
    Module::SampleMode m_eSampleMode;  // 采样模式
    QHash< Qt::GlobalColor,QList< SpectrumChart::AbstractData > > m_hRawData;   // 经整理后的数据
    QVector< INT8 >     m_vRawData;             // 原始数据

    UINT16  m_usAlarm;                          // 红色报警值
    UINT16  m_usWarn;                           // 黄色警告值

    HFCT::SpectrumState m_eSpectrumState;       // 频谱状态
    INT8    m_cGain;                            // 增益
    INT8    m_cMaxSpectrum;                     // 转换后的最大频谱
    bool    m_bRunningMode;                     // 运行模式，闪烁或隐藏
    bool    m_bIsConnected;                     // 不显示或“无信号！”

    INT32   m_iTimerId;                         // 定时器索引号
    INT32   m_iPhase;                           // 相位偏移

    QLabel  *m_pUpLimit;                        // 显示上限值
    QLabel  *m_pDownLimit;                      // 显示下限值
    QLabel  *m_pMaxSpectrum;                    // 显示最大频谱值
    QLabel  *m_pRunningStatus;                  // 显示运行状态
    QLabel  *m_pConnectingStatus;               // 显示连接状态

    SyncWidget *m_pSync;                        // 显示同步源的控件
    PowerReferenceChart *m_pPowerReferenceChart;// 工频参考单元
    SpectrumChart   *m_pSpectrumChart;          // 图谱基类
};
#endif
#endif // HFCTSPECTRUMCHART_H
