#ifndef SWIPELISTVIEW_H
#define SWIPELISTVIEW_H

#include <QListView>
#include "widgetglobal.h"

class WIDGET_EXPORT SwipeListView : public QListView
{
public:
    explicit SwipeListView(QWidget *parent = NULL);
    ~SwipeListView();

protected:
    void mousePressEvent(QMouseEvent* pEvent);

    void mouseReleaseEvent(QMouseEvent* pEvent);

    void mouseMoveEvent(QMouseEvent* pEvent);

private:
    int m_iLastPointY;
    bool m_bPressed;
    bool m_bMouseMoving;
    QPersistentModelIndex m_pressedIndex;
};

#endif // SWIPELISTVIEW_H
