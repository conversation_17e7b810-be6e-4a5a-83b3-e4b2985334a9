﻿/*
* Copyright (c) 2017.2，南京华乘电气科技有限公司
* All rights reserved.
*
* caprotocol.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年2月15日
* 摘要：通用协议层
* 协议层根据具体协议要求，提供协议相关的业务操作接口及实现
*
* 修改版本：1.1
* 作者：曹山
* 创建日期：2018年6月5日
* 摘要：优化通信业务流程

* 当前版本：1.1
*/

#ifndef CAPROTOCOL_H
#define CAPROTOCOL_H

#include <QObject>
#include <QReadWriteLock>
#include "CA.h"
#include "datadefine.h"

class CATcpServer;

class CAProtocol : public QObject
{
    Q_OBJECT

signals:
    void sigInvalidCalibrateCo();

    void sigNoCalibrateCoe();
    /****************************
    功能： 信号， 发送view需要的脉冲数据
    输入参数: vecData---view需要的脉冲数据
    *****************************/
    void sigPulseData(CA::DataHeadInfo stHead, QList<CA::PulseData> vecData);

    /****************************
    功能： 信号， 发送view需要的wave数据
    输入参数: vecXData---wave view需要的x坐标数据
            vecYData---wave view需要的y坐标数据
    *****************************/
    void sigWaveData(CA::DataHeadInfo stHead, QList<CA::WaveData> listWaveData);

    /****************************
    功能： 信号， 发送校准用AD数据
    输入参数: vecADData---校准用AD数据
    *****************************/
    void sigCalibrateADData(CA::DataHeadInfo stHead, QVector<UINT16> vecADData);

    /****************************
    功能： 信号， 发送prps数据
    输入参数: vecData---prps数据 单位mv 范围 0~2000mv
    *****************************/
    void sigPRPSData(CA::DataHeadInfo stHead, QVector<double> vecData);

    /****************************
    功能： 信号， 同步事件处理完毕
    输入参数: eAffair---同步事件枚举
    *****************************/
    void sigAffairDone(CA::CAAffair eAffair);

    /****************************
    功能： 信号， 通知通讯已连接
    输入参数:
    *****************************/
    void sigConnected(QString strMac);

    /****************************
    功能： 信号， 通知socket端断开连接
    输入参数:
    *****************************/
    void sigClientDisconnected(QString strMac);

    /*************************************************
    功能： 信号，通知校准无效信号
    *************************************************/
    void sigGainState(CA::GainState eState);

public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent -- 父Object
    输出参数：NULL
    返回值： NULL
    *************************************************/
    CAProtocol(QObject *parent = 0);

    /*************************************************
    功能： 析构函数
    输入参数:NULL
    输出参数：NULL
    返回值： NULL
    *************************************************/
    ~CAProtocol();

    /************************************************
     * 函数名   : hasConnectedDevice
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 查询结果  true--有; false--无
     * 功能     : 查询是否有已连接的前端
     ************************************************/
    bool hasConnectedDevice(void) const;

    /************************************************
     * 函数名   : connectedDevice
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 当前连接的CA调理器mac地址
     * 功能     : 获取当前连接的调理器
     ************************************************/
    QString connectedDevice(void) const;

    /*************************************************
    函数名： sendStartSampleCmd
    输入参数: eMode---view模式
    输出参数：NULL
    返回值： NULL
    功能：发送开始采样命令
    *************************************************************/
    void sendStartSampleCmd(CA::CAWorkMode eMode);

    /*************************************************
    功能： 发送停止采样命令
    输入参数:NULL
    *************************************************/
    void sendStopSampleCmd();

#ifdef _SUPPORT_SET_SYS_FREQ_
    /************************************************
     * 函数名   : sendSetSampleParamCmd
     * 输入参数 : eSampleRate: 采样率; usMinSampleLenTimes: 最小采样长度的倍数, 最小采样长度为1024
     *          uiSyncPeriod:内同步周期，即电网频率换算的周期，单位us
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置采样参数
     ************************************************/
    void sendSetSampleParamCmd(CA::SampleRate eSampleRate, UINT32 uiSyncPeriod, UINT16 usMinSampleLenTimes = 1);
#else
    /************************************************
     * 函数名   : sendSetSampleParamCmd
     * 输入参数 : eSampleRate: 采样率; usMinSampleLenTimes: 最小采样长度的倍数, 最小采样长度为1024
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置采样参数
     ************************************************/
    void sendSetSampleParamCmd(CA::SampleRate eSampleRate, UINT16 usMinSampleLenTimes = 1);
#endif
    /*************************************************
    功能： 设置增益
    输入参数:eGain---增益
    *************************************************/
    void sendSetGainCmd(CA::Gain eGain);

    /************************************************
     * 函数名   : sendSetTrigParamCmd
     * 输入参数 : stParam: 触发参数, 包括: 触发值、触发宽度、触发前百分比
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置触发参数
     ************************************************/
    void sendSetTrigParamCmd(const CA::TrigParam stParam);

    /************************************************
     * 函数名   : sendSetFreqBand
     * 输入参数 : eFreqBand: 频带
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置频带
     ************************************************/
    void sendSetFreqBand(CA::FreqBand eFreqBand);

    /************************************************
     * 函数名   : sendSetFIRCoefficient
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置FIR滤波器系数
     ************************************************/
    void sendSetFIRCoefficient(void);

    /*************************************************
    功能： ca校准参数写到G100
    输入参数:pstData---校准参数
    *************************************************/
    void sendSetCalibratedParamCmd(const CA::CalibratedParameter stData);

    /*************************************************
    函数名： CAConditionerInfo()
    输入参数: NULL
    输出参数：NULL
    返回值： G100信息
    功能：返回G100信息
    *************************************************************/
    CA::CAConditionerInfo CAConditionerInfo() const;

    /************************************************
     * 函数名   : calibratedParam
     * 输入参数 : eGain: 增益
     * 输出参数 : NULL
     * 返回值   : 校准参数
     * 功能     : 读取校准参数
     ************************************************/
    CA::CalibratedParameter calibratedParam(CA::Gain eGain);

    /*************************************************
    函数名： setState(CA::CA_STATE eState)
    输入参数: eState---通讯状态
    输出参数：NULL
    返回值： NULL
    功能：设置通讯状态
    *************************************************************/
    void setState(CA::CA_STATE eState);

    /************************************************
     * 函数名   : connectToActivatedDevice
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 连接到已激活的前端
     ************************************************/
    void connectToActivatedDevice(void);

    /*************************************************
    函数名： setStartSampleTimeoutState
    输入参数: bSucceed---开始采集结果
    输出参数：NULL
    返回值： NULL
    功能：设置开始采集结果
    *************************************************************/
    void setStartSampleTimeoutState(bool bSucceed);

    /*************************************************
    函数名： setWhetherOperTimeout
    输入参数: bSucceed---操作结果
    输出参数：NULL
    返回值： NULL
    功能：设置同步操作执行结果
    *************************************************************/
    void setWhetherOperTimeout(bool bSucceed);

    void setExpectedPulseCnt(quint32 uiCnt);

protected:
    /*************************************************
    函数名： timerEvent(QTimerEvent *pEvent)
    输入参数:
    输出参数：
    返回值：
    功能： 定时器处理函数
    *************************************************************/
    void timerEvent(QTimerEvent *pEvent);


signals:
    /************************************************
     * 函数名   : sigConnectToActivatedDevice
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 信号, 连接到已激活的前端
     ************************************************/
     void sigConnectToActivatedDevice(void);

private:

    typedef struct _WaveOrigData
    {
        UINT16 usADData;//ad数据
        double dTime;//时间
    }WaveOrigData;

    typedef QVector<WaveOrigData>  WaveOrigDataVector;

private:
    /************************************************
     * 函数名   : init
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 初始化
     ************************************************/
    void init(void);

    /*************************************************
    函数名：initData()
    输入参数:
    输出参数：
    返回值：
    功能： 数据成员初始化
    *************************************************************/
    void initData();

    /*************************************************
    功能： 初始化通道数据
    输入参数:NULL
    输出参数：NULL
    返回值： NUL
    *************************************************/
    void initChannelData();

    /*************************************************
    功能： 初始化前端
    输入参数:NULL
    输出参数：NULL
    返回值： NUL
    *************************************************/
    void initializeDevice();

    /****************************
    功能： 解析脉冲计数应答报文
    *****************************/
    void parseGetPulseCntReply(QByteArray &qbaPackage);

    /****************************
    功能： 解析取数据应答报文
    *****************************/
    void parseGetDataReplyPackage(QByteArray &qbaPackage);

    /****************************
    功能： 发送取脉冲计数命令
    *****************************/
    void sendGetPulseCnt();

    /****************************
    功能： 发送取脉冲数据命令
    *****************************/
    void sendGetPulseData(UINT32 uiPulseCount);

    /****************************
    功能： 发送取wave数据命令
    *****************************/
    void sendGetWaveData();

    /****************************
    功能： 发送取PRPS数据命令
    *****************************/
    void sendGetPRPSData();

    /****************************
    功能： 解析脉冲数据,提取脉冲原始数据
    输入参数: uiPulseCount---新收到的脉冲个数
            qbaPackage---数据报文(数据头+数据文件)
    输出参数: lPulseOrigData---解析后生成的原始数据
    *****************************/
    void appendPulseOrigData(UINT32 uiPulseCount, const QByteArray &qbaPackage, QList<CA::PulseData> &lPulseOrigData);

    /*************************************************
    函数名： removeWholeGetDataReplyPackage
    输入参数:qbaBlock---报文
    输出参数：
    返回值：
    功能： 请求停止采样后，不再解析数据报文，等待收据报文收完后，删除该报文
    *************************************************************/
    void removeNoNeedData(QByteArray &qbaPackage);

    /****************************
    功能： 把原始数据转为 pulse view需要的数据
    *****************************/
    void origPulseData2ViewData(QList<CA::PulseData> &lOrigData, QList<CA::PulseData> &lViewData);

//    /****************************
//    功能： 计算所有ad的平均值
//    输入参数: listADData---所有ad
//    输出参数: NULL
//    返回值:所有ad的平均值
//    *****************************/
//    double calculateADAverage(const QList<UINT16> &listADData);

    /*************************************************
    函数名： saveDataFileHeadInfo
    输入参数:qbaPackage---数据头报文
    输出参数：
    返回值：
    功能： 保存数据头信息
    *************************************************************/
    void saveDataFileHeadInfo(const QByteArray &qbaPackage);

    /*************************************************
    函数名： parsePackage
    输入参数:qbaPackage---应答报文
    输出参数：
    返回值：
    功能： 解析应答报文
    *************************************************************/
    void parsePackage(QByteArray &qbaPackage);

    /*************************************************
    函数名： processStopSampleReply
    输入参数:
    输出参数：
    返回值：
    功能： 处理停止采样应答
    *************************************************************/
    void processStopSampleReply();

    /*************************************************
    函数名： processStartSampleReply
    输入参数:qbaPackage---应答报文
    输出参数：
    返回值：
    功能： 解析起动采样应答报文
    *************************************************************/
    void processStartSampleReply(const QByteArray &qbaPackage);

    /*************************************************
    函数名： parseConfigParameterReply
    输入参数:qbaPackage---应答报文
    输出参数：
    返回值：
    功能： 解析参数配置应答报文
    *************************************************************/
    void parseConfigParameterReply(const QByteArray &qbaPackage);

    /*************************************************
    函数名： pparseInitializeG100Reply
    输入参数:qbaData---初始化G100的应答报文
    输出参数：
    返回值：
    功能： 解析初始化G100的应答报文
    *************************************************************/
    void parseInitReply(const QByteArray &qbaPackage);

    /****************************
    功能： 解析脉冲数据报文
    输入参数: qbaPackage---数据报文(数据头+数据文件)
    输出参数: listData---解析后的脉冲数据
    *****************************/
    void parsePulseData(const QByteArray &qbaPackage, QList<CA::PulseData> &listData, QList<CA::PulseData> &lViewData);

    /*************************************************
    函数名： voltage2AD
    输入参数: usVoltage: 实际电压值 单位:mv
    输出参数：
    返回值：
    功能： 实际电压值转为ad数据
    *************************************************************/
    UINT16 voltage2AD(UINT16 usVoltage);

    /*************************************************
    函数名： AD2Voltage
    输入参数: usADData---dVal
            fCoefficient---系数
            fZeroBias---零漂
            dADBias---AD偏置
    输出参数：dVal---电压值(单位：mV)
    返回值： NULL
    功能：AD值转为电压值
    *************************************************************/
    void AD2Voltage(UINT16 usADData, float fCoefficient, float fZeroBias, double dADBias, double &dVal);

    /*************************************************
    函数名： parseDeviceInfoReply
    输入参数:qbaPackage---g100设备信息应答报文
    输出参数：
    返回值：
    功能： 解析g100设备信息应答报文
    *************************************************************/
    void parseDeviceInfoReply(const QByteArray &qbaReplyData);

    /*************************************************
    函数名： parseReadCoefficientReply
    输入参数:qbaPackage---应答报文
    输出参数：
    返回值：
    功能： 解析读取校准系数的应答报文
    *************************************************************/
    void parseReadCoefficientReply(const QByteArray &qbaPackage);

    /*************************************************
    函数名： sendGetDeviceInfo()
    输入参数:
    输出参数：
    返回值：
    功能： 发送读取g100设备信息命令
    *************************************************************/
    void sendGetDeviceInfo();

    /*************************************************
    函数名： requestCalibratedParameters
    输入参数:ucType---校准系数类型
    输出参数：
    返回值：
    功能： 请求读取校准系数
    *************************************************************/
    void requestCalibratedParameters(UINT8 ucType);

    /*************************************************
    函数名： parseWriteCoefficientReply
    输入参数:qbaPackage---应答报文
    输出参数：
    返回值：
    功能： 解析写校准系数到g100的应答报文
    *************************************************************/
    void parseWriteCoefficientReply(const QByteArray &qbaPackage);

    /*************************************************
    函数名： parsePingReplykage
    输入参数:qbaPackage---应答报文
    输出参数：
    返回值：
    功能： 解析ping的应答报文
    *************************************************************/
    void parsePingReply(const QByteArray &qbaPackage);

    /*************************************************
    函数名： sendGetCalibratedParameter
    输入参数:eGain---增益档位
    输出参数：
    返回值：
    功能： 发送读取校准系数命令
    *************************************************************/
    void sendGetCalibratedParameter(CA::Gain eGain);

    /*************************************************
    函数名： affairDone
    输入参数: eAffair---处理完毕的事件
    输出参数：
    返回值：
    功能： 事件处理完毕，信号通知
    *************************************************************/
    void affairDone(CA::CAAffair eAffair);

    /*************************************************
    函数名： printPackageNoneZeroData
    输入参数:qbaData---报文
    输出参数：
    返回值：
    功能： 打印报文里非零字段
    *************************************************************/
    void printPackageNoneZeroData(const QByteArray &qbaData);

    /*************************************************
    函数名： processWaveDataFile
    输入参数:qbaPackage---数据报文(数据头+数据)
    输出参数：
    返回值：
    功能：处理wave数据报文
    *************************************************************/
    void processWaveDataFile(const QByteArray &qbaPackage);

    /*************************************************
    函数名： setDrawedWaveDataCnt
    输入参数:uiRealDataCnt---实际每个wave波形的数据点个数
    输出参数：
    返回值：每个wave波形绘制时的数据点个数
    功能：根据每条波形得到的数据点个数，转换成绘制时需要绘制的数据点个数
    *************************************************************/
    UINT32 setDrawedWaveDataCnt(UINT32 uiRealDataCnt);

    /*************************************************
    函数名： appendWaveViewADData
    输入参数:qbaPackage---数据报文(数据头+数据)
    输出参数：vecWaveOrigData---从数据报文里提取出的wave原始数据
    返回值：
    功能：从数据报文里提取出wave原始数据
    *************************************************************/
    void appendWaveViewADData(const QByteArray &qbaPackage, WaveOrigDataVector &vecWaveOrigData);

    /*************************************************
    函数名： WaveADData2ViewData
    输入参数:qbaPackage---数据报文(数据头+数据)
            dRange---采样的数据范围，目前是4000mv
            dBase---ad采样的最大ad值，目前是8192
    输出参数：vecXData---wave view需要的x坐标数据
            vecYData---wave view需要的y坐标数据
    返回值：
    功能：wave原始数据转为view需要的数据
    *************************************************************/
    void WaveADData2ViewData(WaveOrigDataVector vecWaveOrigData, QList<CA::WaveData> &listData);

    /*************************************************
    函数名： calculateExpectedPulseDataReplyLen
    输入参数:qbaBlock---报文
    输出参数：uiWantLen---期望的脉冲数据报文长度(取数据应答+数据头+数据文件)
    返回值：
    功能： 计算期望的脉冲数据报文长度(取数据应答+数据头+数据文件)
    *************************************************************/
    void calculateExpectedPulseDataReplyLen(const QByteArray &qbaPackage, UINT32 &uiWantLen);

    /*************************************************
    函数名： appendCalibrateADData
    输入参数:qbaPackage---报文
    输出参数：vecADData---存放AD数据的容器
    返回值：
    功能： 解析报文，保存AD数据
    *************************************************************/
    void appendCalibrateADData(const QByteArray &qbaPackage,QVector<UINT16> &vecADData);

    /*************************************************
    函数名： connectSignals()
    输入参数:
    输出参数：
    返回值：
    功能： 连接信号
    *************************************************************/
    void connectSignals();

    /*************************************************
    函数名： processGetDataReply
    输入参数:qbaPackage---数据报文
    输出参数：
    返回值：
    功能： 处理取数据应答报文
    *************************************************************/
    void processGetDataReply(QByteArray &qbaPackage);

    /*************************************************
    函数名： requestAllCalibratedParameters()
    输入参数:
    输出参数：
    返回值：
    功能： 请求获取所有增益档位的校准系数
    *************************************************************/
    void requestAllCalibratedParameters();

    /*************************************************
    函数名： appendPRPSADData
    输入参数:qbaReplyData---prps数据文件(数据头+数据文件)
    vecADData---prps原始数据
    返回值：
    功能： 解析prps数据文件，提取prps原始数据，添加到数据容器
    *************************************************************/
    void appendPRPSADData(const QByteArray &qbaReplyData, QVector<UINT16> &vecADData);

    /*************************************************
    函数名： prpsADData2ViewData
    输入参数:vecADData---prps原始数据
            dBase: base,默认 8192 = pow(2,13) 13为ad采样位数
            dRange:iADMax - iADMin, 默认是4000mv
    输出参数：vecPRPSViewData---view需要的prps百分比数据(相对2000mv的百分比)
    返回值：
    功能： 把prps原始数据转为view需要的百分比数据
    *************************************************************/
    void prpsADData2ViewData(const QVector<UINT16> &vecADData, QVector<double> &vecPRPSViewData);

    /*************************************************
    函数名： parsePRPSDataFile
    输入参数:qbaReplyData---prps数据文件(数据头+数据文件)
    输出参数：
    返回值：
    功能： 解析prps数据文件
    *************************************************************/
    void parsePRPSDataFile(const QByteArray &qbaReplyData);

    /*************************************************
    函数名： pingDevice
    输入参数: void
    输出参数：
    返回值：
    功能： 向前端设备发送ping指令
    *************************************************/
    void pingDevice(void);

    /*************************************************
    函数名： disconnectFromDevice
    输入参数: void
    输出参数：
    返回值：
    功能： 断开与前端的连接
    *************************************************/
    void disconnectFromDevice(void);

    /************************************************
     * 函数名   : pulseSampleLen
     * 输入参数 : usSampleRate: 采样率
     *           fTimeVal: 时间值
     *           eTimeUnit: 时间单位
     * 输出参数 : NULL
     * 返回值   : 采样长度
     * 功能     : 计算脉冲采样长度
     ************************************************/
    UINT32 pulseSampleLen(UINT32 usSampleRate, float fTimeVal, CA::TimeUnit eTimeUnit);

    /************************************************
     * 函数名   : convertToNs
     * 输入参数 : fVal: 时间值; eTimeUnit: 时间单位
     * 输出参数 : NULL
     * 返回值   : 转换后的以ns为单位的值
     * 功能     : 把时间值转换为ns值
     ************************************************/
    double convertToNs(float fVal, CA::TimeUnit eTimeUnit);

    /************************************************
     * 函数名   : samplePointsIntervalByNs
     * 输入参数 : usSampleRate: 采样率
     * 输出参数 : NULL
     * 返回值   : 采样点时间间隔
     * 功能     : 计算采样点间的时间间隔, 以ns为单位
     ************************************************/
    double samplePointsIntervalByNs(UINT32 usSampleRate);

    /************************************************
     * 函数名   : convertRealSampleLen
     * 输入参数 : uiLen: 长度
     * 输出参数 : NULL
     * 返回值   : 转换后的采样长度
     * 功能     : 转换成实际的采样长度, 实际长度应为2的n次方
     ************************************************/
    UINT32 convertRealSampleLen(UINT32 uiLen);

    /************************************************
     * 函数名   : killCheckLinkTimer
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 关闭链路检查定时器
     ************************************************/
    void killCheckLinkTimer(void);

    /************************************************
     * 函数名   : receivedPulseCount
     * 输入参数 : uiDataSize: 数据长度
     * 输出参数 : NULL
     * 返回值   : 脉冲个数
     * 功能     : 计算收到的脉冲个数
     ************************************************/
    UINT32 receivedPulseCount(UINT32 uiDataSize);

    /************************************************
     * 函数名   : isCoefficientValid
     * 功能     : 判断系数是否有效
     ************************************************/
    bool isCoefficientValid(int iSeq, double dCoefficient, double dZeroBias, double dADBias);

    /************************************************
     * 功能     : 处理增益状态
     ************************************************/
    void dealGainState();

private slots:

    /************************************************
     * 函数名   : onContinueGetPulseData
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 槽, 继续获取脉冲数据
     ************************************************/
    void onContinueGetPulseData();

    /************************************************
     * 函数名   : onContinueGetPRPSData
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 槽, 继续获取PRPS数据
     ************************************************/
    void onContinueGetPRPSData();

    /*************************************************
    函数名：onDataReady
    输入参数:
    输出参数：
    返回值：
    功能： 槽函数，处理收到的数据报文
    *************************************************************/
    void onDataReceived(qint64 llDataLen);

    /*************************************************
    函数名：onClientConneted
    输入参数:
    输出参数：
    返回值：
    功能： 槽函数，处理有连接请求
    *************************************************************/
    void onClientConneted(QString strMacAddr);

    /*************************************************
    函数名：onDisconnected
    输入参数:
    输出参数：
    返回值：
    功能： 槽函数，处理通信断开
    *************************************************************/
    void onDisconnected(QString strMacAddr);

    /************************************************
     * 函数名   : onConnectToActivatedDevice
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 槽, 连接到已激活的前端
     ************************************************/
    void onConnectToActivatedDevice(void);

private:

    typedef struct _channelData
    {
        bool isConnectDSP;//通道是否连接dsp
        UINT8 ucChannelId;//通道号
        UINT16 usSampleRate;//采样率
        UINT16 usSampleLength;//采样长度
        UINT32 uiSampleLengthBefore;//触发前采样长度
        UINT32 uiSampleLengthAfter;//触发后采样长度
        UINT16 usTriggerAmp;//触发幅值
        CA::Gain eGain;//增益
        CA::FreqBand eBand;//频带
        _channelData()
        {
            isConnectDSP = false;
            ucChannelId = 0;
            usSampleRate = CA::sampleRateEnum2Number(CA::SAMPLE_RATE_DEFAULT);
            usSampleLength = CA::sampleLengthEnum2Number(CA::SAMPLE_LENGTH_DEFAULT);
            uiSampleLengthBefore = 0;
            uiSampleLengthAfter = CA::PULSE_TOTAL_SAMPLE_LEN - uiSampleLengthBefore;
            usTriggerAmp = 0;
            eGain = CA::GAIN_DEFAULT;
            eBand = CA::BAND_DEFAULT;
        }
    }channelData;

    enum {
        GET_PULSE_DATA_CNT_MAX = 32,  //读取脉冲数据个数的最大值
        REPLY_PACKAGE_LENGTH = 1024,//应答报文长度
        MAX_CHANNEL_NUM = 18,//通道个数最大值

        GET_GAIN_0_COEFFIECINT = 0x01, //读取增益0校准参数
        GET_GAIN_MINUS20_COEFFIECINT = 0x02, //读取增益 -20db 校准参数
        GET_GAIN_MINUS40_COEFFIECINT = 0x04, //读取增益 -40db 校准参数
        GET_GAIN_MINUS60_COEFFIECINT = 0x08, //读取增益 -60db 校准参数
        GET_ALL_GAINS_COEFFIECINT = 0x0f, //读取所有档增益的校准参数

        CONFIG_COMMON = 0x01, //通用配置
        CONFIG_FILTER_COEFFICENT = 0x02, //滤波器系数配置
        CONFIG_GAIN = 0x04, //增益配置
        CONFIG_TRIGGER = 0x08, //触发配置
        CONFIG_ALL = 0x0f,//配置所有类型参数

        CMD_EXECUTE_SUCCESS = 0x01,//命令成功处理标志

        TCP_PORT_NUMBER = 30000, //tcp通讯端口号
        TRIGGER_AMP_MAX = 8191,//触发幅值最大值对应的AD值


        PRPS_DATA_PACKAGE_LEN = 8*1024,//prps数据文件长度
        PRPS_DATA_CNT = 3600,              //PRPS模式采样数据文件包含的采样数据个数


        PING_SLICE = 3000,  //ping前端时间间隔
        PING_MAX_NO_REPLY_TIMES = 3,     //ping最大允许未回复次数
        CONTINUOUS_SAMPLE_INTERVAL = 10, //连续采集等待间隔
    };

    CA::WaveSampleParameter m_stWaveParameter;//用来保存wave采样参数的数据

    QByteArray m_qbaReceivedPackage;//存放应答包

    CATcpServer *m_pTcpServer; //tcp Server

    UINT8 m_ucRequestConfigType;//参数配置类型

    UINT8 m_ucWorkedChannelIndex;//使用通道的索引

    UINT8 m_ucChannelId;//通道号

    channelData m_channelData[MAX_CHANNEL_NUM];//通道数据

    channelData m_stTmpChannelData; //暂存的数据

    volatile CA::CA_STATE m_eState;//通讯状态

    CA::DataHeadInfo m_stHeadInfo;//数据头信息

    CA::CalibratedParameter m_astCalibratedParameter[CA::GAIN_COUNT];//所有档位的校准参数

    CA::CAConditionerInfo m_stCaConditionerInfo;//g100信息

    UINT32 m_uiNodeIp;//节点IP

    UINT32 m_uiSampleSequence;//采样序列号

    UINT8 m_ucRequestCoefficientType;//请求读取的校准参数类型

    quint32 m_uiInvalidCoCnt;
    quint32 m_uiNoCalibrateGainCnt;

    volatile bool m_isCalibrateSample;//是否校准用采样

    int m_iCheckLinkTimerID; //校验链接定时器

    int m_iPingCount;  //ping计数

    volatile bool m_bConnected;  //是否已连接

    CA::CalibratedParameter m_stTmpCaliParam; //暂存的校准参数

    QReadWriteLock m_readWriteLock;

    float m_fPulseTrigLevel;//ca pulse的触发阈值, unit:mV

    volatile bool m_bStartSampleTimeout;

    volatile bool m_bOperTimeout;

    double m_dMaxUpVal;
    double m_dMaxDownVal;
    quint32 m_uiExpectedPulseCnt;
    quint32 m_uiTotalReceivedPulseCnt;
};

#endif // CAPULSEPROTOCOL_H
