/*****< ss1btpas.h >***********************************************************/
/*      Copyright 2011 - 2014 Stonestreet One.                                */
/*      All Rights Reserved.                                                  */
/*                                                                            */
/*  SS1BTPAS - Stonestreet One Bluetooth Phone Alert Status Service (GATT     */
/*             based) Type Definitions, Prototypes, and Constants.            */
/*                                                                            */
/*  Author:  <PERSON>                                                         */
/*                                                                            */
/*** MODIFICATION HISTORY *****************************************************/
/*                                                                            */
/*   mm/dd/yy  F. Lastname    Description of Modification                     */
/*   --------  -----------    ------------------------------------------------*/
/*   09/22/11  T. Cook        Initial creation.                               */
/******************************************************************************/
#ifndef __SS1BTPASH__
#define __SS1BTPASH__

#include "PASSType.h"           /* Bluetooth PASS Service Types.              */
#include "PASSAPI.h"            /* Bluetooth PASS API Prototypes/Constants.   */

#endif
