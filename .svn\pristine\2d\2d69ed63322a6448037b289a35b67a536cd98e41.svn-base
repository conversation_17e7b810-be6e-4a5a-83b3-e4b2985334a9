#include <QBoxLayout>
#include <QPushButton>
#include <QDesktopWidget>
#include "RFIDReadView.h"
#include "softkeyboard.h"
#include "appfontmanager/appfontmanager.h"
#include "log/log.h"


const QString ENSURE_BUTTON_STYLE = "QPushButton{border-style: none; border: 0px; color: #F0F0F0; padding: 2px; min-height: 20px; border-radius: 5px;"
                                    "background: qlineargradient(spread: pad, x1:0, y1:0, x2:0, y2: 1, stop: 0 #1B89CA, stop: 1 #1077B5); background-color: rgb(59, 167, 164)}";

const INT32 BUTTON_WIDTH = 150;
const INT32 BUTTON_HEIGHT = 70;
const INT32 SCAN_INFO_HEIGHT = 640;
const INT32 FONT_SIZE = 20;
const int VIEW_WIDTH = 480;
const int VIEW_HEIGHT = 760;
const int TITLE_WIDTH = 450;
const int TITLE_HEIGHT = 30;

/*************************************************
功能： 构造函数
输入参数:
    parent:父窗口指针
*************************************************************/
RFIDReadView::RFIDReadView( QWidget* parent )
    :Widget( parent )
{
    //close soft keyboard
#ifdef Q_PROCESSOR_ARM
    SoftKeyBoard::setAttribute( KEYBOARD_SHOW_PROPERTY, SoftKeyBoard::SHOW_NONE );
#endif

    setAttribute(Qt::WA_DeleteOnClose, true);
    setWindowFlags(Qt::FramelessWindowHint);
    setFixedSize(VIEW_WIDTH, VIEW_HEIGHT);
    setWindowModality(Qt::ApplicationModal);

    QFont font = AppFontManager::instance()->getAppCurFont();
    font.setPointSize(FONT_SIZE);

    //title
    m_plabelTitle = new QLabel;
    m_plabelTitle->setAlignment(Qt::AlignCenter);
    m_plabelTitle->setFont(font);
    m_plabelTitle->setFixedSize(TITLE_WIDTH, TITLE_HEIGHT);
    m_plabelTitle->setText("");

    //scan info
    m_pScanInfo = new RfidInfoWidget( RfidInfoWidget::RFID_READ );
    m_pScanInfo->setFixedHeight(SCAN_INFO_HEIGHT);

    //button ok
    m_pBtnOk = new QPushButton(QObject::trUtf8("OK"));
    m_pBtnOk->setStyleSheet(ENSURE_BUTTON_STYLE);
    m_pBtnOk->setFont(font);
    m_pBtnOk->setFixedSize(BUTTON_WIDTH, BUTTON_HEIGHT);
    connect(m_pBtnOk, SIGNAL(clicked(bool)), this, SLOT(close()));

    QBoxLayout *pLayout = new QBoxLayout(QBoxLayout::TopToBottom);
    pLayout->setAlignment(Qt::AlignCenter);
    pLayout->addWidget(m_pBtnOk, Qt::AlignCenter);

    QVBoxLayout* pmainLayout = new QVBoxLayout(this);
    pmainLayout->setAlignment(Qt::AlignCenter);
    //pmainLayout->setSpacing(0);
    pmainLayout->setMargin(0);

    pmainLayout->addWidget(m_plabelTitle);
    pmainLayout->addWidget(m_pScanInfo);
    pmainLayout->addLayout(pLayout);

    setLayout(pmainLayout);
}

//析构函数
RFIDReadView::~RFIDReadView()
{
#ifdef Q_PROCESSOR_ARM
    SoftKeyBoard::setAttribute( KEYBOARD_SHOW_PROPERTY, SoftKeyBoard::SHOW_KEY_BOARD );
#endif
}

/*************************************************
功能： 按键事件
*************************************************************/
void RFIDReadView::keyPressEvent(QKeyEvent *event)
{
    switch( event->key() )
    {
        case Qt::Key_Enter:
        case Qt::Key_Return:
        case Qt::Key_Escape:
        {
            close();
        }
            break;
        default:
            break;
    }
}

/*************************************************
功能： 设置rfid扫描结果的信息
输入参数:
    stInfo -- rfid扫描结果
*************************************************************/
void RFIDReadView::setScanInfo(const ScanInfo &stInfo)
{
    m_pScanInfo->setScanInfo( stInfo );
}

/*************************************************
功能： 设置标题
输入参数:
    strTitle -- 标题
*************************************************************/
void RFIDReadView::setTitle( const QString& strTitle )
{
    m_plabelTitle->setText( strTitle );
}

/*************************************************
功能： 显示事件
*************************************************************/
void RFIDReadView::showEvent(QShowEvent *)
{
    QDesktopWidget* pDesktop = QApplication::desktop();
    move( ( pDesktop->width() - width() ) / 2,( pDesktop->height() - height() ) / 2 );

    return;
}
