/*
* Copyright (c) 2017.11，南京华乘电气科技有限公司
* All rights reserved.
*
* infraredparameterdialog.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年11月27日
* 摘要：参数设置对话框

* 当前版本：1.0
*/

#ifndef INFRAREDPARAMETERDIALOG_H
#define INFRAREDPARAMETERDIALOG_H

#include <QDialog>
#include <QPaintEvent>
#include <QPushButton>
#include <QVBoxLayout>
#include "infraredparameteritem.h"

class InfraredParameterDialog : public QDialog
{
    Q_OBJECT
public:
    /*************************************************
    函数名： InfraredParameterDialog(QList<InfraredParameterItem *> &itemList, QWidget *parent = 0)
    输入参数： itemList：参数条目指针列表
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    InfraredParameterDialog(QList<InfraredParameterItem *> &itemList, QWidget *parent = 0);


signals:
    /*************************************************
    传递参数： valueList：参数值列表
    说明： 设置参数值信号
    *************************************************************/
    void sigInfraredParams(const QList<double> valueList);


private slots:
    /*************************************************
    函数名： onOkBtnClicked()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 确定按钮响应槽函数
    *************************************************************/
    void onOkBtnClicked();

    /*************************************************
    函数名： onCancelBtnClicked()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 取消按钮响应槽函数
    *************************************************************/
    void onCancelBtnClicked();


protected:
    /*************************************************
    函数名： paintEvent(QPaintEvent *e)
    输入参数： e：重绘事件
    输出参数： NULL
    返回值： NULL
    功能： 重绘事件处理
    *************************************************************/
    void paintEvent(QPaintEvent *e);


private:
    /*************************************************
    函数名： value(QList<double> &valueList)
    输入参数： NULL
    输出参数： valueList：参数条目数值列表
    返回值： NULL
    功能： 获取参数条目数值列表
    *************************************************************/
    void value(QList<double> &valueList);

    /*************************************************
    函数名： isLegitimacy()
    输入参数： NULL
    输出参数： NULL
    返回值： 是否合法
    功能： 合法性校验
    *************************************************************/
    bool isLegitimacy();


private:
    QList<InfraredParameterItem *> m_lItemList;
    QPushButton *m_pOkButton;
    QPushButton *m_pCancelButton;
    QVBoxLayout *m_pItemVBoxLayout;

};

#endif // INFRAREDPARAMETERDIALOG_H
