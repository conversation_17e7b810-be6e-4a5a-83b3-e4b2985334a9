/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* SwitchMenu.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月25日
* 摘要：网络设置点击开关的定义

* 当前版本：1.0
*/

#ifndef SWITCHMENU_H
#define SWITCHMENU_H

#include <QFrame>

class SwitchMenu : public QFrame
{
    Q_OBJECT
public:
    /*************************************************
    函数名： SwitchMenu(QWidget *parent)
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit SwitchMenu(QWidget *parent = 0);

    /************************************************
     * 函数名   : setIconSize
     * 输入参数 : width：宽度；height：高度
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置切换开关的大小
     ************************************************/
    void setIconSize(int width, int height);

    /************************************************
     * 函数名   : initialize
     * 输入参数 : 切换开关的图片路径
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 初始化切换开关的图片路径
     ************************************************/
    void initialize(const QString& strImageBk, const QString& strImageFore);

    /************************************************
     * 函数名   : switchStatus
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : bool：开关的状态
     * 功能     : 返回开关的状态
     ************************************************/
    bool switchStatus();

    /************************************************
     * 函数名   : clickSwitchMenu
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 按下状态切换开关
     ************************************************/
    void clickSwitchMenu();

    /************************************************
     * 输入参数 : bOn  true -- 打开
     *                false -- 关闭
     * 功能     : 按下状态切换开关
     ************************************************/
    void setSwitchState(bool bOn, bool bSendSignals = false);

protected:
    /************************************************
     * 函数名   : paintEvent
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 重载绘图事件
     ************************************************/
    virtual void paintEvent(QPaintEvent *e);

    /************************************************
     * 函数名   : mousePressEvent
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 重载鼠标点击事件
     ************************************************/
    virtual void mousePressEvent(QMouseEvent *e);

    /************************************************
     * 函数名   : mouseReleaseEvent
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 重载鼠标释放事件
     ************************************************/
    virtual void mouseReleaseEvent(QMouseEvent *e);

signals:

    /************************************************
     * 函数名   : sigSwitchStatus
     * 输入参数 : bool：发出当前切换开光状态的信号
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 发出当前切换开光状态的信号
     ************************************************/
    void sigSwitchStatus(bool bState);

private:
    QPixmap     m_pixmapBk;         // 背景图片
    QPixmap     m_pixmapFore;       // 背景前面表示当前状态的图片
    bool        m_bOn;              // 开关状态
    bool        m_bLBtnDown;        // 案件状态
    int m_iWidth;                   // 开关宽度
    int m_iHeight;                  // 开关高度
};

#endif // SWITCHMENU_H
