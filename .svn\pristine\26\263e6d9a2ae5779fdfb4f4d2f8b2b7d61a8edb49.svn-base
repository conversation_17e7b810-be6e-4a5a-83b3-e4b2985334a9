#ifndef INFRAREDDATAMAP_H
#define INFRAREDDATAMAP_H

#include <QObject>
#include "datafiledefine.h"
#include "datamap.h"
#include "infrareddefine.h"
#include "datafile_global.h"

class DATAFILESHARED_EXPORT InfraredDataMap : public DataMap
{
public:
    /*************************************************
    功能： 构造函数
    *************************************************************/
    InfraredDataMap();

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~InfraredDataMap();

    /*************************************************
    功能： 设置图谱的ext information
    输入参数：
            pData -- 指向m图谱的ext information的指针
    *************************************************************/
    void setInfo(InfraredMapNS::InfraredMapInfo* pMapInfo);

    /*************************************************
    功能： 读取图谱的ext information
    输出参数：
            pInfo -- 指向图谱的ext information的指针
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool getInfo(InfraredMapNS::InfraredMapInfo* pMapInfo);

    /*************************************************
    功能： 设置图谱的data
    输入参数：
            pData -- 指向图谱的data的指针
    *************************************************************/
    void setData(InfraredMapNS::InfraredMapData* pData);

    /*************************************************
    功能： 读取图谱的data
    输出参数：
            pData -- 指向图谱的data的指针
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool getData(InfraredMapNS::InfraredMapData* pData);

protected:
    /*************************************************
    功能： 解析map的扩展字段
    输入参数：
            baData -- 图谱数据的xml文本内容
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool parseMapExtXML( XMLDocument * pDoc, const QString &strRootTag );

    /*************************************************
    功能： 解析map的data字段
    输出参数：
            baData -- 图谱数据的xml文本内容
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool parseMapDataXML( XMLDocument * pDoc, const QString &strRootTag );

    /*************************************************
    功能： 生成map扩展部分的xml文本
    输入参数：
            baData -- 保存图谱数据的xml文本内容
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool saveMapExtXML(XMLDocument *pDoc, QDomElement &element,bool bCrypt);

    /*************************************************
    功能： 生成map扩展部分的qbytearray文本，方便存储为二进制
    *************************************************************/
    void saveMapExtBinary( QByteArray &baPackage );

    /*************************************************
    功能： 生成map数据部分的xml文本
    输入参数：
            baData -- 保存图谱数据的xml文本内容
    返回值:
            true: 成功; false:失败
    *************************************************************/
    bool saveMapDataXML(XMLDocument *pDoc, QDomElement &element, bool bCrypt);

    /*************************************************
    功能： 生成map数据部分的qbytearray文本，方便存储为二进制
    *************************************************************/
    void saveMapDataBinary( QByteArray &baPackage );

    /*************************************************
    功能： 解析map除头以外的所有字段
    输入参数：
            bytes -- 图谱数据的二进制文件内容
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    virtual bool parseMapDataBinary(const QByteArray &bytes);

    /*************************************************
    功能： 获取图谱根节点的标签名
    返回值:
            图谱对应的标签名
    *************************************************************/
    QString mapRootTag();

private:
    void clearMapData();

private:
    InfraredMapNS::InfraredMapInfo m_stExt;             //红外信息
    InfraredMapNS::InfraredMapData m_pInfraredData;     //红外数据
};

#endif // INFRAREDDATAMAP_H
