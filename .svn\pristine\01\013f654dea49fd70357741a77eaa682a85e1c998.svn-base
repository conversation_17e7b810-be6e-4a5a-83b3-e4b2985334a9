#include "tevsamplestrategy.h"

/****************************
功能： 构造函数
*****************************/
TEVSampleStrategy::TEVSampleStrategy(QObject *parent) :
    QObject(parent)
{
    m_iTimerId = -1;
    m_iSampleInterval = TEV::ReadPRPSDataInterval100MS;
}

/*************************************************
功能： 开始采集
*************************************************/
void TEVSampleStrategy::startSample()
{
    startSampleTimer();
}

/*************************************************
功能： 停止采集
*************************************************/
void TEVSampleStrategy::stopSample()
{
    stopSampleTimer();
}

/*************************************************
入参：iInterval -- 采样间隔
功能： 设置采样间隔
*************************************************/
void TEVSampleStrategy::setSampleInterval( int iInterval )
{
    m_iSampleInterval = iInterval;
}

/*************************************************
返回值：int -- 定时器id
功能： 获得定时器id
*************************************************/
int TEVSampleStrategy::timerId( void )
{
    return m_iTimerId;
}

/*************************************************
功能： 开启取数据定时器
*************************************************/
void TEVSampleStrategy::startSampleTimer( void )
{
    if( -1 == m_iTimerId )
    {
        m_iTimerId = startTimer( m_iSampleInterval );
    }
}

/*************************************************
功能： 关闭取数据定时器
*************************************************/
void TEVSampleStrategy::stopSampleTimer( void )
{
    if( -1 != m_iTimerId )
    {
        killTimer( m_iTimerId );
        m_iTimerId = -1;
    }
}
