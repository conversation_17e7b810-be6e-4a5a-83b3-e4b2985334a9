/*
* Copyright (c) 2019.4，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：AEAmpMLdiagnosis.h
*
* 初始版本：1.0
* 作者：李遥
* 创建日期：2020年9月2日
* 摘要：
*/

#ifndef AE_AMP_ML_DIAGNOSIS_H
#define AE_AMP_ML_DIAGNOSIS_H

#include "MLdiagnosisGlobal.h"
#include "AEDataDefines.h"

/*************************************************
 * 函数功能： 初始化AE幅值机器学习模型
 * 输入参数：
 *      strModelFile -- 模型文件
 * 返回参数：
 *      true：成功；false：失败
*************************************************************/
ML_DIAGNOSISSHARED_EXPORT bool initAEModelML( const char* strModelFile );

/*************************************************
 * 函数功能： 释放AE幅值机器学习模型
 * 返回参数：
 *      true：成功；false：失败
*************************************************************/
ML_DIAGNOSISSHARED_EXPORT bool freeAEModelML();

/*************************************************
 * 函数功能： AE Amp机器学习诊断
 * 输入参数：
 *      aeAmp -- ae幅值数据
 *      eGain -- 增益
 *      sThreshold -- 诊断阈值
 * 输出参数：
 *      diagResult -- 诊断结论
 * 返回参数：
 *      true：成功；false：失败
*************************************************************/
ML_DIAGNOSISSHARED_EXPORT bool diagnosisByAEAmpML(   AEAmplitude stAmpInfo,
                                                     GainLevel eGain,
                                                     AEDiagnosisThreshold sThreshold,
                                                     DiagResult* pResult  );

#endif // AE_AMP_ML_DIAGNOSIS_H
