#include "HFCTPeriodDataSave.h"
#include "datafile/datafile.h"
#include "systemsetting/systemsetservice.h"
#include "datafile/period/perioddatamap.h"
#include "uhf/UHFConfig.h"
#include "model/HCStatus.h"
#include "mapdatafactory.h"

/************************************************
 * 函数名   : HFCTPeriodDataSave
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 构造函数
 ************************************************/
HFCTPeriodDataSave::HFCTPeriodDataSave()
{
    m_pHFCTPeriodDataInfo = NULL;
    MapDataFactory::registerClass<PeriodDataMap>(XML_FILE_NODE_PERD);
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString HFCTPeriodDataSave::saveData(void *pData)
{
    if(NULL == pData)
    {
        return NULL;
    }
    m_pHFCTPeriodDataInfo = (HFCTPeriodDataInfo*)pData;
    m_strAbsolutePath = getAbsolutePath(m_pHFCTPeriodDataInfo->stHeadInfo.generationDateTime);

    return saveData( pData, m_strAbsolutePath );
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString HFCTPeriodDataSave::saveData(void *pData, const QString &qsSavedPath)
{
    QString strSavePath("");
    if(NULL == pData)
    {
        return strSavePath;
    }

    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return strSavePath;
    }

    m_pHFCTPeriodDataInfo = (HFCTPeriodDataInfo*)pData;

    QDir dir(qsSavedPath);
    if(!dir.exists())
    {
        dir.mkpath(qsSavedPath);
    }

    DataFile *pFile = new DataFile;

    setFileHeads(pFile, m_pHFCTPeriodDataInfo->stHeadInfo);

    addMap(pFile);

    bool isSuccess = pFile->save(qsSavedPath, HFCT_PERIOD_FILE_NAME_SUFFIX, strSavePath);
    qDebug()<<"saved strDataFile is:"<<strSavePath;

    delete pFile;
    if(isSuccess == false)
    {
        QFile file(strSavePath);
        file.remove();
        strSavePath = "";
    }
    //添加文件备注信息
    RemarkInfoManager::instance()->saveRemarkInfo(strSavePath, m_pHFCTPeriodDataInfo->stHeadInfo.qstrRemark);
    return strSavePath;
}

void HFCTPeriodDataSave::addMap(DataFile *pFile)
{
    PeriodDataMap *pMap = new PeriodDataMap;
    setMapHead(pMap);
    setMapInfo(pMap);
    setMapData(pMap);
    pFile->addMap(pMap);
}

void HFCTPeriodDataSave::setMapHead(DataMap *pMap)
{
    pMap->setCode(m_pHFCTPeriodDataInfo->stHeadInfo.eCode);
    pMap->setGenerationTime(m_pHFCTPeriodDataInfo->stHeadInfo.generationDateTime);
    pMap->setSpectrumProperty(m_pHFCTPeriodDataInfo->stHeadInfo.eMapProperty);

    pMap->setDeviceName(m_pHFCTPeriodDataInfo->stHeadInfo.strDeviceName);
    pMap->setDeviceNumber(m_pHFCTPeriodDataInfo->stHeadInfo.strDeviceNumber);

    pMap->setTestPointName(m_pHFCTPeriodDataInfo->stHeadInfo.strTestPointName);
    pMap->setTestPointNumber(m_pHFCTPeriodDataInfo->stHeadInfo.strTestPointNumber);

    pMap->setTestLocation(m_pHFCTPeriodDataInfo->stHeadInfo.eTestLocation);
    pMap->setTestChannelSign(m_pHFCTPeriodDataInfo->stHeadInfo.ucTestChannelSign);
    pMap->setDataPrimitiveType(m_pHFCTPeriodDataInfo->stHeadInfo.eDataPrimitiveType);
    pMap->setRemark(m_pHFCTPeriodDataInfo->stHeadInfo.qstrRemark);
}

void HFCTPeriodDataSave::setMapInfo(PeriodDataMap *pMap)
{
    PeriodMapNS::PeriodMapInfo stInfo;
    stInfo.eAmpUnit = m_pHFCTPeriodDataInfo->eAmpUnit;
    stInfo.fAmpMin = m_pHFCTPeriodDataInfo->fAmpMin;
    stInfo.fAmpMax = m_pHFCTPeriodDataInfo->fAmpMax;
    stInfo.eMapBandWidth = DataFileNS::BAND_DEFAULT;
    stInfo.ucFreqMin = m_pHFCTPeriodDataInfo->ucFreqMin;
    stInfo.ucFreqMax = m_pHFCTPeriodDataInfo->ucFreqMax;

    stInfo.iPhaseCount = m_pHFCTPeriodDataInfo->iPhaseIntervalCount;
    stInfo.fWarningVal = m_pHFCTPeriodDataInfo->ucWarning;
    stInfo.fAlarmVal = m_pHFCTPeriodDataInfo->ucAlarm;

    memcpy(stInfo.ucaPdTypeProb, m_pHFCTPeriodDataInfo->ucaDischargeTypeProb, sizeof(stInfo.ucaPdTypeProb));
    stInfo.eSyncSource = (DataFileNS::SyncSource)(m_pHFCTPeriodDataInfo->eSyncSource + 1);
    stInfo.ucSyncState = m_pHFCTPeriodDataInfo->eSyncState;

    stInfo.eDataSign = (DataFileNS::MapDataSign) m_pHFCTPeriodDataInfo->eEffectiveDataSign;
    stInfo.eGainType = m_pHFCTPeriodDataInfo->eGainType;
    stInfo.iGain = m_pHFCTPeriodDataInfo->iGain;
    stInfo.iSyncFreq = m_pHFCTPeriodDataInfo->fSyncFreq;
    pMap->setInfo(&stInfo);
}

void HFCTPeriodDataSave::setMapData(PeriodDataMap *pMap)
{
    INT16 asData [SPECTTRUMNUM * 2];
    memset( asData, 0, sizeof(INT16) * SPECTTRUMNUM * 2);

    int intvl = 360/SPECTTRUMNUM;
    for( int i = 0; i < SPECTTRUMNUM; ++i )
    {
        int iPhaseShift = (int)m_pHFCTPeriodDataInfo->fPhaseShift;
        int iPos = ( i + iPhaseShift/intvl ) % SPECTTRUMNUM;
        asData[iPos * 2] = m_pHFCTPeriodDataInfo->astSpectrum[i].sPeakValue;
        asData[iPos * 2 + 1] = iPos*intvl;
    }

    pMap->setData(asData, SPECTTRUMNUM * 2);
}

INT32 HFCTPeriodDataSave::getDataFromFile(const QString& strFileName, void *pData)
{
    DataFile *psDataFile = new DataFile;

    //step1 open data file
    bool isSuccess = psDataFile->open(strFileName);
    if(isSuccess == false)
    {
        qDebug() << "!!!!!!!!!!!!!OPen file failed";
        delete psDataFile;
        return HC_FAILURE;
    }

    m_pHFCTPeriodDataInfo = (HFCTPeriodDataInfo*)pData;
    //获取文件头信息
    m_pHFCTPeriodDataInfo->stHeadInfo.strSubstationName = psDataFile->stationName();

    PeriodDataMap * pMap = dynamic_cast <PeriodDataMap*>(psDataFile->dataMap(DataFileNS::SPECTRUM_CODE_HFCT_PERD));
    if( NULL == pMap )
    {
        delete psDataFile;
        return HC_FAILURE;
    }
    //设置头部信息
    pMap->getDataType( m_pHFCTPeriodDataInfo->stHeadInfo.eDataPrimitiveType );
    pMap->getDeviceName( m_pHFCTPeriodDataInfo->stHeadInfo.strDeviceName );
    pMap->getDeviceNumber( m_pHFCTPeriodDataInfo->stHeadInfo.strDeviceNumber );
    pMap->getTestPointName( m_pHFCTPeriodDataInfo->stHeadInfo.strTestPointName );
    pMap->getTestPointNumber( m_pHFCTPeriodDataInfo->stHeadInfo.strTestPointNumber );
    QString strDateTime;
    pMap->getMapGenerationTime( strDateTime );
    m_pHFCTPeriodDataInfo->stHeadInfo.generationDateTime = QDateTime::fromString( strDateTime, "yyyy-MM-dd hh:mm:ss" );
    pMap->getMapProperty( m_pHFCTPeriodDataInfo->stHeadInfo.eMapProperty );
    pMap->getTestChannelSign( m_pHFCTPeriodDataInfo->stHeadInfo.ucTestChannelSign );


    //设置图谱信息
    PeriodMapNS::PeriodMapInfo stMapInfo;
    pMap->getInfo(&stMapInfo);
    m_pHFCTPeriodDataInfo->iGain = stMapInfo.iGain;
    m_pHFCTPeriodDataInfo->eSyncSource = (SyncSource)(stMapInfo.eSyncSource - 1);
    m_pHFCTPeriodDataInfo->eSyncState = (SyncState)stMapInfo.ucSyncState;
    m_pHFCTPeriodDataInfo->iPhaseIntervalCount = stMapInfo.iPhaseCount;
    m_pHFCTPeriodDataInfo->ucAlarm = (UINT8)stMapInfo.fAlarmVal;
    m_pHFCTPeriodDataInfo->ucWarning = (UINT8)stMapInfo.fWarningVal;
    m_pHFCTPeriodDataInfo->eEffectiveDataSign = (EffectiveDataSign)stMapInfo.eDataSign;
    m_pHFCTPeriodDataInfo->fAmpMin = stMapInfo.fAmpMin;
    m_pHFCTPeriodDataInfo->fAmpMax = stMapInfo.fAmpMax;

    //设置数据
    INT16 asValueBuf[SPECTTRUMNUM *2] = {0};
    pMap->getData( asValueBuf, SPECTTRUMNUM *2 );

    for( int i = 0; i < SPECTTRUMNUM ; ++i )
    {
        m_pHFCTPeriodDataInfo->astSpectrum[i].sPeakValue = asValueBuf[i * 2];
    }
    m_pHFCTPeriodDataInfo->setMaxValue();

    delete psDataFile;

    return HC_SUCCESS;
}

/************************************************
 * 函数名   : saveExtInformation
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储可扩展信息
 ************************************************/
void HFCTPeriodDataSave::saveExtInformation(XMLDocument& doc)
{
    Q_UNUSED(doc);
#if 0
    //站名
    doc.setValue("SubstationName",m_pHFCTPeriodDataInfo->strSubstationName);
    //被测试设备名称
    doc.setValue("TestedDevName",m_pHFCTPeriodDataInfo->strTestedDevName);
    //采样时间
    doc.setValue("SampleTime",convertTimeToSampleTimeFormat(m_pHFCTPeriodDataInfo->dateTime));
    //同步方式
    doc.setValue("SyncMode",QString::number(m_pHFCTPeriodDataInfo->eSyncSource));
    //同步状态
    doc.setValue("SyncStatus",QString::number(m_pHFCTPeriodDataInfo->eSyncState));
    //报警值
    doc.setValue("Alarm",QString::number(m_pHFCTPeriodDataInfo->ucAlarm));
    //预警值
    doc.setValue("Warning",QString::number(m_pHFCTPeriodDataInfo->ucWarning));
    //相位偏移(来源手持设备)
    doc.setValue("PhaseShift",QString::number(m_pHFCTPeriodDataInfo->fPhaseShift));
    //相位偏移(来源上位机软件)
    doc.setValue("SoftWarePhaseShift",QString::number(m_pHFCTPeriodDataInfo->fSoftWarePhaseShift));
    //HFCT周期最大值
    doc.setValue("Max",QString::number(m_pHFCTPeriodDataInfo->cMax));
    //增益
    doc.setValue("Gain",QString::number(m_pHFCTPeriodDataInfo->fGain));
    //数据有效判定标志
    doc.setValue("EffectiveDataSign",QString::number(m_pHFCTPeriodDataInfo->eEffectiveDataSign));
#endif
}

/************************************************
 * 函数名   : saveRawData
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储数据部分
 ************************************************/
void HFCTPeriodDataSave::saveRawData(XMLDocument& doc)
{
    Q_UNUSED(doc);
#if 0
   doc.setValue("Data", getStringFromData(m_pHFCTPeriodDataInfo->acSpectrum, SPECTTRUMNUM));
#endif
}

/************************************************
 * 函数名   : parseData
 * 输入参数 : baData: 数据
 * 输出参数 : pData: 解析到的数据
 * 返回值   : void
 * 功能     : 解析数据
 ************************************************/
void HFCTPeriodDataSave::parseData(const QByteArray& baData, void *pData, const QString &strFileName)
{
    Q_UNUSED(baData);
    Q_UNUSED(pData);
    Q_UNUSED(strFileName);
#if 0
    QMutexLocker locker(&m_mutex);
    m_pHFCTPeriodDataInfo = (HFCTPeriodDataInfo*)pData;

    XMLDocument doc(baData);

    doc.beginElement("ExtInformation");
    m_pHFCTPeriodDataInfo->strSubstationName = doc.value("SubstationName");
    m_pHFCTPeriodDataInfo->strTestedDevName = doc.value("TestedDevName");
    m_pHFCTPeriodDataInfo->dateTime = convertToDateTime(doc.value("SampleTime"));
    m_pHFCTPeriodDataInfo->eSyncSource = SyncSource(doc.value("SyncMode").toInt() - 1);
    m_pHFCTPeriodDataInfo->eSyncState = SyncState(doc.value("SyncStatus").toInt());
    m_pHFCTPeriodDataInfo->ucAlarm = (UINT8)(doc.value("Alarm").toUInt());
    m_pHFCTPeriodDataInfo->ucWarning = (UINT8)(doc.value("Warning").toUInt());
    m_pHFCTPeriodDataInfo->fPhaseShift = doc.value("PhaseShift").toFloat();
    m_pHFCTPeriodDataInfo->fSoftWarePhaseShift = doc.value("SoftWarePhaseShift").toFloat();
    m_pHFCTPeriodDataInfo->cMax = (INT8)(doc.value("Max").toInt());
    m_pHFCTPeriodDataInfo->fGain = doc.value("Gain").toFloat();
    m_pHFCTPeriodDataInfo->eEffectiveDataSign = EffectiveDataSign(doc.value("EffectiveDataSign").toInt());
    doc.endElement();

    memset(m_pHFCTPeriodDataInfo->acSpectrum,0x0,sizeof(INT8)*SPECTTRUMNUM);
    QByteArray data = QByteArray::fromBase64(doc.value("Data").toLatin1());
    memcpy(m_pHFCTPeriodDataInfo->acSpectrum,(INT8*)(data.data()),data.count());
#endif
}

/************************************************
 * 函数名   : getDataTypeFolder
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件夹名
 * 功能     : 获取图谱类型文件存储最上层文件夹名
 ************************************************/
QString HFCTPeriodDataSave::getDataTypeFolder(void)
{
    return HFCT_PERIOD_FOLDER;
}

/************************************************
 * 函数名   : getFileNameSuffix
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件后缀名
 * 功能     : 获取数据文件后缀名
 ************************************************/
QString HFCTPeriodDataSave::getFileNameSuffix(void)
{
    return HFCT_PERIOD_FILE_NAME_SUFFIX;
}
