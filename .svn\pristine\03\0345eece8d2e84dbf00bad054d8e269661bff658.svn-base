/*
* Copyright (c) 2020.06，南京华乘电气科技有限公司
* All rights reserved.
*
* compressutil.h
*
* 初始版本：1.0
* 作者：张浪
* 创建日期：2020年06月02日
* 修改日期：2020年06月02日
* 修改人：张浪
* 摘要：封装了压缩文件工具类
* 当前版本：1.0
*/

#ifndef COMPRESS_UTIL_H
#define COMPRESS_UTIL_H


#include <QString>
#include <QStringList>
#include "module_global.h"



class MODULESHARED_EXPORT CompressUtil
{
public:

    /*******************************外部调用静态接口函数*******************************/

    /*******************************************
     * 功能：压缩文件
     * 输入参数：
     *      qstrCompressedFilePath：压缩文件路径
     *      qstrlstFiles：需要压缩文件的路径集合
     * 返回值：
     *      bool：操作结果，true -- 成功，false -- 失败
     * *****************************************/
    static bool compressFiles(const QString qstrCompressedFilePath, const QStringList &qstrlstFiles);

};

#endif // COMPRESS_UTIL_H
