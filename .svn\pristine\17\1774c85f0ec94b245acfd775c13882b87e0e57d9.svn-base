/*
* Copyright (c) 2017.4，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：HFCT.h
*
* 初始版本：1.0
* 作者：邵震宇
* 修改日期：2017年4月16日
* 摘要：HFCT模块相关定义
* 当前版本：1.0
*/

#ifndef HFCT_H
#define HFCT_H

#include <QVector>
#include <QByteArray>
#include "DriverDataDefine.h"

namespace HFCT
{

//增益对应具体数值
const int GAIN_VALUES[] =
{
    60,
    40,
    20,
    0,
};

typedef enum _Info
{
    PERIOD_DATA_SIZE = 60,//周期数据长度
    PRPS_PERIOD_CNT = 50//prps满屏显示的周期数
}Info;

typedef enum _SampleInfo
{
    SAMPLE_INTERVAL = 100,//100ms读取一次数据
    READ_DATA_CNT = 5,  // 读取的次数(每读取5次，发送一次数据）
    READ_DATA_FAIL_CNT = 5
}SampleInfo;

//工作模式
typedef enum _WorkMode
{
    MODE_AMPLITUDE = 0,//幅值图谱模式
    MODE_PERIOD,//周期图谱模式
    MODE_PRPS,//PRPS图谱模式
}WorkMode;

//增益
typedef enum _Gain
{
    GAIN_NONE = -1,
    GAIN_START,
    GAIN_MINUS_60 = GAIN_START,   //HFCT幅值图谱增益 -60
    GAIN_MINUS_40,   //HFCT幅值图谱增益 -40
    GAIN_MINUS_20,   //HFCT幅值图谱增益 -20
    GAIN_0,          //HFCT幅值图谱增益 0
    GAIN_MIN = GAIN_MINUS_60,
    GAIN_MAX = GAIN_0,
    GAIN_END = GAIN_0,
    GAIN_COUNT,
    GAIN_DEFAULT = GAIN_0
}Gain;

typedef enum _SingleSampleState // 单次采样状态
{
    SAMPLE_DATA_ALREADY_GET = 0,//已经获取到数据
    SAMPLE_DATA_NOT_GET  //尚未获取到数据
}SingleSampleState;

//同步源
typedef enum _SyncSource
{
    INTER_SYNC = 0, //内同步
    WIRELESS_SYNC, //电源同步
    LIGHT_SYNC, //光同步
    SYNC_SOURCE_MIN = INTER_SYNC,
    SYNC_SOURCE_MAX = LIGHT_SYNC,
    SYNC_SOURCE_COUNT = SYNC_SOURCE_MAX - SYNC_SOURCE_MIN + 1,
    SYNC_SOURCE_DEFAULT = WIRELESS_SYNC,
} SyncSource;

//频谱状态
typedef enum _SpectrumState
{
    SPECTRUM_INSIDE_RANGE = 0, //频谱范围内
    SPECTRUM_UNDER_LOW, //频谱小于范围
    SPECTRUM_ABOVE_HIGH, //频谱大于范围
}SpectrumState;

// 相位偏移
typedef enum _PhaseAlias
{
    PHASE_MIN = 0,
    PHASE_MAX = 354,
    PHASE_STEP = 6,
    PHASE_DEFAULT = 0, // 相位偏移默认值
}PhaseAlias;

// 累积模式
typedef enum _Accumulation
{
    ACCUMULATION_TIME_1 = 0,
    ACCUMULATION_TIME_5,
    ACCUMULATION_TIME_15,
    ACCUMULATION_TIME_30,
    ACCUMULATION_TIME_60,
    ACCUMULATION_DEFAULT = ACCUMULATION_TIME_5, // 累积时间默认值
}Accumulation;

//相关配置信息
typedef enum _ChartInfo
{
    GAIN_BASE = 20,       //增益基数
    CHART_MIN_VALUE = 0,  //图谱最小值
    CHART_MAX_VALUE = 80, //图谱最大值
    AMP_MAX_COLUMNS = 20, //幅值最大列数
} ChartInfo;

//报警值
typedef enum _AlarmValue
{
    ALARM_MIN = 0,
    ALARM_MAX = 80,
    ALARM_STEP = 1,
    RED_ALARM_DEFAULT = 30, // 红色报警默认值
    YELLOW_ALARM_DEFAULT = 20, // 黄色报警默认值
}AlarmValue;

//幅值图谱数据
typedef struct _AmplitudeData
{
    INT8 cMaxSpectrum; //转换后的最大频谱
    SpectrumState eSpectrumState;//频谱状态
    _AmplitudeData()
    {
        cMaxSpectrum = 0;
        eSpectrumState =SPECTRUM_UNDER_LOW;
    }
}AmplitudeData;

//周期图谱数据
typedef struct _PeriodData
{
    INT8 cMaxSpectrum; //转换后的最大频谱
    SpectrumState eSpectrumState;//频谱状态
    QVector<INT8> vSpectrum; //转换后的频谱值
    _PeriodData()
    {
        cMaxSpectrum = 0;
        eSpectrumState =SPECTRUM_UNDER_LOW;
        vSpectrum = QVector<INT8>( PERIOD_DATA_SIZE,0 );
    }
}PeriodData;

//PRPS图谱数据
typedef struct _PRPSData
{
    INT8 cMaxSpectrum; //转换后的最大频谱
    SpectrumState eSpectrumState;//频谱状态
    QVector<INT8> vSpectrum; //转换后的频谱值
    _PRPSData()
    {
        cMaxSpectrum = 0;
        eSpectrumState =SPECTRUM_UNDER_LOW;
        vSpectrum = QVector<INT8>( PERIOD_DATA_SIZE,0 );
    }
}PRPSData;

}
#endif // UHF_H

