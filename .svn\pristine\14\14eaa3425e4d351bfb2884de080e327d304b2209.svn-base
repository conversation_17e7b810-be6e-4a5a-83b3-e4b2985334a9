﻿#include "loginviewbase.h"
#include <QApplication>
#include <QBoxLayout>
#include <QKeyEvent>
#include <softkeyboard.h>
#include "datadefine.h"
#include "window/Window.h"
#include "global_def.h"
#include "View.h"
#include "config/ConfigManager.h"
#include "systemsetting/systemsetservice.h"
#include "appconfig.h"
#include "messageBox/msgbox.h"
#include "globalerrprocess.h"
#include "titlebar/TitleBar.h"
#include "log/log.h"


const int TITLE_HEIGHT = 100;
const int EDIT_WIDTH = 350;
const int EDIT_HEIGHT = 80;
const int BTN_WIDTH = 350;
const int BTN_HEIGHT = 80;
const int TEXT_MAX_LEN = 32;


const QString EDIT_STYLE = "QLineEdit{background-color:white; border:1px solid gray; border-color:rgb(224, 224, 224);"
                           "border-radius:8px; padding:2px 4px; color:rgb(114, 114, 114); font-size:25px}"
                           "QLineEdit:focus{background-color:white; border:1px solid gray; border-color:rgb(115, 198, 242);"
                           "border-radius:8px; padding:2px 4px; color:rgb(114, 114, 114); font-size:25px}";

const QString BUTTON_STYLE = "QPushButton{background-color:rgb(38, 70, 106); border:1px gray; border-color:rgb(224, 224, 224);"
                             "border-radius:8px; padding:2px 4px; color:white; font-size:25px}"
                             "QPushButton:focus{outline:none; background-color:rgb(108, 166, 205);}"
                             "QPushButton:pressed{background-color:rgb(74, 112, 139); border:1px gray; border-color:rgb(224, 224, 224);"
                             "border-radius:8px; padding:2px 4px; color:white; font-size:25px}";

const char* const TEXT_USER = QT_TRANSLATE_NOOP_UTF8("LoginViewBase", "User Name");
const char* const TEXT_PWD = QT_TRANSLATE_NOOP_UTF8("LoginViewBase", "Password");
const char* const TEXT_LOGIN = QT_TRANSLATE_NOOP_UTF8("LoginViewBase", "Login");
const char* const TEXT_LOGIN_TIPS = QT_TRANSLATE_NOOP_UTF8("LoginViewBase", "Please input user name or password.");

const QString TEXT_USER_NAME = APP_DEFAULT_USER_ID;
const QString TEXT_PASSWORD = APP_DEFAULT_USER_PWD;

#define PDA_LOGIN_TRANSLATE(str) qApp->translate("LoginViewBase", (str))


/****************************
函数名： LoginViewBase
输入参数:
        parent:父窗口指针
输出参数：NULL
返回值：NULL
功能： 构造函数
*****************************/
LoginViewBase::LoginViewBase(const QString& qstrTitle, QWidget *parent)
    : QWidget(parent)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);
    setAttribute(Qt::WA_DeleteOnClose);
    setWindowFlags(Qt::FramelessWindowHint);

    createUI(qstrTitle);
}

LoginViewBase::~LoginViewBase()
{
}

/****************************
功能： 设置用户名
*****************************/
void LoginViewBase::setUserName(const QString& qstrUserName)
{
    if (m_pUsrId)
    {
        m_pUsrId->setText(qstrUserName);
    }
}

/****************************
功能： 设置密码
*****************************/
void LoginViewBase::setPassword(const QString& qstrPassword)
{
    if (m_pPwd)
    {
        m_pPwd->setText(qstrPassword);
    }
}

/****************************
函数名： keyPressEvent
输入参数:
        event -- keyPress事件
输出参数：NULL
返回值：NULL
功能： 处理keyPress事件
*****************************/
void LoginViewBase::keyPressEvent(QKeyEvent* event)
{
    SystemSetService::instance()->closeKeyBoard();
    if(event->key() == Qt::Key_Escape)
    {
        close();
    }
    else if(event->key() == Qt::Key_Return || event->key() == Qt::Key_Enter)
    {
        onLoginClicked();
    }
    else if(event->key() == Qt::Key_Left || event->key() == Qt::Key_Up)
    {
        m_iCurIdx = (m_map4Widget.size() + m_iCurIdx - 1) % m_map4Widget.size();
        m_map4Widget[m_iCurIdx]->setFocus();
        repaint();
    }
    else if(event->key() == Qt::Key_Right || event->key() == Qt::Key_Down)
    {
        m_iCurIdx = (m_iCurIdx + 1) % m_map4Widget.size();
        m_map4Widget[m_iCurIdx]->setFocus();
        repaint();
    }
    else
    {
        QWidget::keyPressEvent(event);
    }
}

/****************************
输入参数:
        pEvent -- QShowEvent事件
功能： 处理showEvent事件
*****************************/
void LoginViewBase::showEvent(QShowEvent* pEvent)
{
    QWidget::showEvent(pEvent);

    if(m_pPwd->text().isEmpty())
    {
        m_pPwd->setEchoMode(QLineEdit::Normal);
    }
    else
    {
        m_pPwd->setEchoMode(QLineEdit::Password);
    }
}

/****************************
函数名： createUI
输入参数:NULL
输出参数：NULL
返回值：NULL
功能： 创建界面元素
*****************************/
void LoginViewBase::createUI(const QString& qstrTitle)
{
    m_pTitleBar = new TitleBar(qstrTitle, this);
    m_pTitleBar->setFixedSize(SCREEN_WIDTH, TITLE_HEIGHT);

    m_pUsrId = new QLineEdit(this);
    m_pUsrId->setFixedSize(EDIT_WIDTH, EDIT_HEIGHT);
    m_pUsrId->setStyleSheet(EDIT_STYLE);
    m_pUsrId->setPlaceholderText(PDA_LOGIN_TRANSLATE(TEXT_USER));
    m_pUsrId->setMaxLength(TEXT_MAX_LEN);
    m_map4Widget.insert(0, m_pUsrId);

    m_pPwd = new QLineEdit(this);
    m_pPwd->setFixedSize(EDIT_WIDTH, EDIT_HEIGHT);
    m_pPwd->setStyleSheet(EDIT_STYLE);
    m_pPwd->setPlaceholderText(PDA_LOGIN_TRANSLATE(TEXT_PWD));
    m_pPwd->setMaxLength(TEXT_MAX_LEN);
    m_map4Widget.insert(1, m_pPwd);

    m_pBtnLogin = new QPushButton(this);
    m_pBtnLogin->setFixedSize(BTN_WIDTH, BTN_HEIGHT);
    m_pBtnLogin->setStyleSheet(BUTTON_STYLE);
    m_pBtnLogin->setText(PDA_LOGIN_TRANSLATE(TEXT_LOGIN));
    m_pBtnLogin->setFocusPolicy(Qt::StrongFocus);
    m_pBtnLogin->setFocus();
    m_map4Widget.insert(2, m_pBtnLogin);
    m_iCurIdx = 2;

    connect(m_pTitleBar, SIGNAL(sigClicked()), this, SLOT(close()));
    connect(m_pPwd, SIGNAL(textEdited(QString)), this, SLOT(onPwdTextEdited(QString)));
    connect(m_pBtnLogin, SIGNAL(clicked(bool)), this, SLOT(onLoginClicked()));

    QVBoxLayout* pMainLayout = new QVBoxLayout(this);
    pMainLayout->setMargin(0);
    pMainLayout->setSpacing(0);

    pMainLayout->addWidget(m_pTitleBar, 0, Qt::AlignTop);

    pMainLayout->addSpacing(60);
    pMainLayout->addWidget(m_pUsrId, 0, Qt::AlignHCenter);
    pMainLayout->addSpacing(15);
    pMainLayout->addWidget(m_pPwd, 0, Qt::AlignHCenter);
    pMainLayout->addSpacing(20);
    pMainLayout->addWidget(m_pBtnLogin, 0, Qt::AlignHCenter);
    pMainLayout->addStretch();

    setLayout(pMainLayout);
}

/****************************
函数名： onLoginClicked
输入参数: NULL
输出参数：NULL
返回值：NULL
功能： 点击登录按钮处理，请求登录
*****************************/
void LoginViewBase::onLoginClicked()
{
    m_pPwd->setEchoMode(QLineEdit::Password);

    QString qstrUserName = m_pUsrId->text();
    QString qstrPassword = m_pPwd->text();

    if (qstrUserName.isEmpty() || qstrPassword.isEmpty())
    {
        MsgBox::warning("", PDA_LOGIN_TRANSLATE(TEXT_LOGIN_TIPS));
        logError("user name or password is empty.");
        return;
    }

    login(qstrUserName, qstrPassword);
}

/****************************
函数名： onLoginClicked
输入参数: qstrText
输出参数：NULL
返回值：NULL
功能： 处理密码框内容改变事件
*****************************/
void LoginViewBase::onPwdTextEdited(const QString &qstrText)
{
    Q_UNUSED(qstrText);
    m_pPwd->setEchoMode(QLineEdit::Normal);
}

