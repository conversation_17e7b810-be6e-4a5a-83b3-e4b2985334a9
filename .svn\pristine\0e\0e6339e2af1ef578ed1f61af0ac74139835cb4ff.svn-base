/*
* Copyright (c) 2016.03，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: UHFPeriodDataSave.h
*
* 初始版本：1.0
* 作者：曹山
* 创建日期：2016年03月16日
* 摘要：该文件主要是定义了UHF周期检测数据存储的子类
*/

#ifndef UHFPERIODDATASAVE_H
#define UHFPERIODDATASAVE_H

#include "dataSave/DataSave.h"
#include "period/periodmapdefine.h"

typedef struct _PeriodData
{
    INT16   sPeakValue;         //采样点数据
    INT16  sTimeValue;          //采样点时间
}PeriodData;
//UHF周期检测数据信息
typedef struct _UHFPeriodDataInfo
{
    _UHFPeriodDataInfo()
    {
        eAmpUnit = DataFileNS::AMP_UNIT_DB;
        fAmpMin = 0;
        fAmpMax = 0;
        ucFreqMin = 0;
        ucFreqMax = 0;
        eSyncSource = WIRELESS_SYNC;
        eSyncState = Synced;
        iGain = GAIN_0;
        fSyncFreq = -1;
        memset( ucaDischargeTypeProb, 0, sizeof(ucaDischargeTypeProb) );

        ucAlarm = 0;
        ucWarning = 0;
        eBandWidth = ALL_PASS_FILTER;
        fPhaseShift = 0;


        cMax = 0;
        iPhaseIntervalCount = SPECTTRUMNUM;
        memset(astSpectrum,0x0,sizeof(PeriodData)*SPECTTRUMNUM);
    }

    /************************************************
     * 功能     : 根据采样点的值计算最大周期值
     ************************************************/
    void setMaxValue()
    {
        cMax = astSpectrum[0].sPeakValue;
        for( int i = 0; i < SPECTTRUMNUM; i++ )
        {
            if( astSpectrum[i].sPeakValue > cMax )
            {
                cMax = astSpectrum[i].sPeakValue;
            }
        }
    }

    /************************************************
     * 功能     : 返回map中所要用的带宽
     ************************************************/
    DataFileNS::MapBandWidth bandWidth()
    {
        DataFileNS::MapBandWidth eMapBand = DataFileNS::BAND_DEFAULT;
        switch( eBandWidth )
        {
        case ALL_PASS_FILTER:
            eMapBand = DataFileNS::BAND_ALL_PASSED;
            break;
        case LOW_PASS_FILTER:
            eMapBand = DataFileNS::BAND_LOW_PASSED;
            break;
        case HIGH_PASS_FILTER:
            eMapBand = DataFileNS::BAND_HIGH_PASSED;
            break;
        default:
            eMapBand = DataFileNS::BAND_DEFAULT;
        }
        return eMapBand;
    }

    void setBandWidth(DataFileNS::MapBandWidth eMapBand)
    {
        switch( eMapBand )
        {
        case DataFileNS::BAND_ALL_PASSED:
            eBandWidth = ALL_PASS_FILTER;
            break;
        case DataFileNS::BAND_LOW_PASSED:
            eBandWidth = LOW_PASS_FILTER;
            break;
        case DataFileNS::BAND_HIGH_PASSED:
            eBandWidth = HIGH_PASS_FILTER;
            break;
        default:
            eBandWidth = ALL_PASS_FILTER;
        }
    }

    DataMapHead stHeadInfo;            //图谱通用的头部信息

    //ext
    DataFileNS::AmpUnit eAmpUnit;       //幅值单位
    float fAmpMin;                      //幅值下限
    float fAmpMax;                      //幅值上限
    UHFFilterControl eBandWidth;       //带宽

    quint8 ucFreqMin;                   //下限频率 Hz
    quint8 ucFreqMax;                   //上限频率
    INT32 iPhaseIntervalCount;         //相位窗数

    UINT8 ucWarning;                   //预警值，1≤ BACKGROUND≤ALARM
    UINT8 ucAlarm;                     //报警值，Warning <ALARM≤70

    quint8 ucaDischargeTypeProb[8];                 //放电类型概率

    SyncSource eSyncSource;            //同步方式,UHF目前只有2种同步方式：电源同步和光同步 从0开始
    SyncState eSyncState;              //同步状态（0：失败，1：成功）default（1）    
    EffectiveDataSign eEffectiveDataSign;
    DataFileNS::GainType eGainType;
    qint16 iGain;                       //增益
    float fSyncFreq;                   //标识测试系统频率，如果没有同步频率，则存-1
    float fPhaseShift;                 //相位偏移(来源手持设备)  步进：6度，范围0~360
//    float fSoftWarePhaseShift;         //相位偏移(来源上位机软件) 步进：6度，范围0~360

    INT8 cMax;                         //UHF周期最大值，[-1,71]
    PeriodData astSpectrum[SPECTTRUMNUM];     //60个采样点数据值
}UHFPeriodDataInfo;


class PeriodDataMap;
class DataMap;
class MODULESHARED_EXPORT UHFPeriodDataSave : public DataSave
{
public:
    /************************************************
     * 函数名   : UHFPeriodDataSave
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 构造函数
     ************************************************/
    UHFPeriodDataSave();

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData);

    /************************************************
         * 函数名   : saveData
         * 输入参数 : pDatas: 数据
         * 输出参数 : NULL
         * 返回值   : 数据文件名
         * 功能     : 保存数据到指定格式数据文件
         ************************************************/
    QString saveData(void *pData, const QString &qsSavedPath);

    /************************************************
         * 函数名   : getDataByPDA
         * 输入参数 : strFileName: 文件名; pDatas: 数据
         * 输出参数 : NULL
         * 返回值   : 获取结果
         * 功能     : 获取指定数据文件中的数据
         ************************************************/
    INT32 getDataFromFile(const QString& strFileName, void *pData);

protected:
    /************************************************
     * 函数名   : getDataTypeFolder
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件夹名
     * 功能     : 获取图谱类型文件存储最上层文件夹名
     ************************************************/
    QString getDataTypeFolder(void);

    /************************************************
     * 函数名   : getFileNameSuffix
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件后缀名
     * 功能     : 获取数据文件后缀名
     ************************************************/
    QString getFileNameSuffix(void);

    /************************************************
     * 函数名   : saveExtInformation
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储可扩展信息
     ************************************************/
    void saveExtInformation(XMLDocument& doc);

    /************************************************
     * 函数名   : saveRawData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储数据部分
     ************************************************/
    void saveRawData(XMLDocument& doc);

    /************************************************
     * 函数名   : parseData
     * 输入参数 : baData: 数据
     * 输出参数 : pData: 解析到的数据
     * 返回值   : void
     * 功能     : 解析数据
     ************************************************/
    void parseData(const QByteArray& baData, void *pData, const QString& strFileName = "");

private:
     void addMap(DataFile *pFile);
     void setMapData(PeriodDataMap *pMap);
     void setMapInfo(PeriodDataMap *pMap);
     void setMapHead(DataMap *pMap);

private:
    UHFPeriodDataInfo *m_pUHFPeriodDataInfo;     //UHF周期检测数据信息
    QMutex m_mutex;                          //互斥锁
};

#endif // UHFPERIODDATASAVE_H
