﻿#include "customaccessloginview.h"
#include "global_def.h"
#include "messageBox/msgbox.h"
#include "mobileaccessservice.h"
#include "config/ConfigManager.h"
#include "customaccessconfig.h"
#include "Module.h"
#include "log/log.h"

/****************************
函数名： CustomAccessLoginView
输入参数:
        parent:父窗口指针
输出参数：NULL
返回值：NULL
功能： 构造函数
*****************************/
CustomAccessLoginView::CustomAccessLoginView(QWidget *parent)
    : LoginViewBase(QObject::trUtf8("Login"), parent)
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_CUSTOMACCESS);
    QString qstrUserName = pConfig->value(CustomAccessConfig::KEY_USERNAME);
    QString qsPwd = pConfig->value(CustomAccessConfig::KEY_PWD);
    pConfig->endGroup();

    setUserName(qstrUserName);
    setPassword(qsPwd);
}

CustomAccessLoginView::~CustomAccessLoginView()
{
}

/****************************
功能：登录，子类重新实现该函数
*****************************/
void CustomAccessLoginView::login(const QString& qstrUserName, const QString& qstrPassword)
{
    if(MobileAccessService::instance()->loginPlatform(qstrUserName, qstrPassword))
    {
        // 从内存更新到配置文件
        ConfigInstance* pConfig = ConfigManager::instance()->config();
        pConfig->beginGroup(Module::GROUP_CUSTOMACCESS);
        pConfig->setValue(qstrUserName, CustomAccessConfig::KEY_USERNAME);
        pConfig->setValue(qstrPassword, CustomAccessConfig::KEY_PWD);
        pConfig->endGroup();
        pConfig->save();

        close();
        emit sigLogined();
    }
    else
    {
        MsgBox::warning("", QObject::trUtf8("User name or password error."));
        logError("User name or password error.");
    }
}
