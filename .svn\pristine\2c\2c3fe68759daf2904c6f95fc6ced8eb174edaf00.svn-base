#ifndef ENVIRONMENTINFO_H
#define ENVIRONMENTINFO_H


#include <QWidget>
#include <QLabel>
#include <QVBoxLayout>
#include <thirdparty/qwt/qwt_dial.h>
#include <thirdparty/qwt/qwt_dial_needle.h>
#include "chartview/ChartView.h"
#include "datadefine.h"
#include "equipmentinfo.h"
#include "Module.h"

// 环境信息项
enum EnvironmnetInfo
{
    ENVIRONMENT_TEMPERATURE = 0,   // 天气
    ENVIRONMENT_HUMIDITY,   // 温度
    ENVIRONMENT_PRESSURE       // 湿度
};

class EnvironmentInfoView : public ChartView
{
public:
    /*************************************************
    函数名： EnvironmentInfoView(const QString &qsTitle, QWidget *parent)
    输入参数:qsTitle---标题
            parent---父控件
    输出参数：NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit EnvironmentInfoView(const QString &qsTitle, QWidget *parent = NULL);

    /*************************************************
    函数名： ~EnvironmentInfoView()
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~EnvironmentInfoView();

private:
    /*************************************************
    函数名： initData(void)
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能： 数据初始化
    *************************************************************/
    void initData(void);

    /*************************************************
    函数名： createUI(void)
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能：创建布局
    *************************************************************/
    QVBoxLayout *createUI(void);

    /*************************************************
    函数名： refreshEnvironmentData(void)
    输入参数: NULL
    输出参数：NULL
    返回值： NULL
    功能： 刷新环境信息
    *************************************************************/
    void refreshEnvironmentData(void);

    /****************************
    函数名： createDial
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能： 创建表盘控件页
    *****************************/
    QwtDial *createDial(EnvironmnetInfo eEvnOpt);

protected:
    /*************************************************
    函数名： timerEvent(QTimerEvent *pEvent)
    输入参数: pEvent---定时器事件指针
    输出参数：NULL
    返回值： NULL
    功能： 定时器处理
    *************************************************************/
    void timerEvent(QTimerEvent *pEvent);

private:
    DevEnvInfo m_stEnvironmentData; //环境数据
    QLabel *m_pTemperatureLabel; //温度qlabel
    QLabel *m_pHumidityLabel;//湿度qlabel
    QLabel *m_pPressureLabel;//气压qlabel

    QwtDial *m_pTemperatureDial;
    QwtDial *m_pHumidityDial;
    QwtDial *m_pPressureDial;

    int m_iGetDataTimerId;

    enum
    {
        TIMER_INTERVAL_1_MIN = 60000, //1 second timer to get environment data
        INVALID_TIMER_ID = -1 //invalid timer id
    };
};
#endif // ENVIRONMENTINFO_H
