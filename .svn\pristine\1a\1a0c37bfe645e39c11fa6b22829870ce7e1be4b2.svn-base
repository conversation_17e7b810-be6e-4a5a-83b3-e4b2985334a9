#include "PDAViewConfig.h"
namespace PDAView
{

QString testDataNameFromType( PDAServiceNS::TestDataType eType )
{
    QString strTestData = QString::null;
    switch( eType )
    {
    case PDAServiceNS::MULTI:       //多图谱
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_MULTI);
        break;
    }
    case PDAServiceNS::HF_MULTI:       //高频多图谱
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_HF_MULTI);
        break;
    }
    case PDAServiceNS::HF_PRPD:       //高频PRPD图
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_HF_PRPD);
        break;
    }
    case PDAServiceNS::HF_PRPS:       //高频PRPS图
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_HF_PRPS);
        break;
    }
    case PDAServiceNS::HF_PULSE_WAVE:       //高频脉冲波形图
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_HF_PULSE_WAVE);
        break;
    }
    case PDAServiceNS::HFCT_AMP_DATA:       //HFCT幅值
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_HFCT_AMP);
        break;
    }
    case PDAServiceNS::HFCT_PRPSPRPD_DATA:      //HFCT PRPS和PRPD检测数据
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_HFCT_PRPSPRPD);
        break;
    }
    case PDAServiceNS::HFCT_PERIOD_DATA:        //HFCT周期检测数据
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_HFCT_PERIOD);
        break;
    }
    case PDAServiceNS::UHF_MULTI:            //UHF多图谱
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_UHF_MULTI);
        break;
    }
    case PDAServiceNS::UHF_PRPD:            //UHF PRPD图
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_UHF_PRPD);
        break;
    }
    case PDAServiceNS::UHF_PRPS:            //UHF PRPS图
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_UHF_PRPS);
        break;
    }
    case PDAServiceNS::UHF_PEAK_STATICS:            //UHF峰值统计图
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_UHF_PEAK_STATICS);
        break;
    }
    case PDAServiceNS::UHF_AMP_DATA:            //UHF幅值
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_UHF_AMP);
        break;
    }
    case PDAServiceNS::UHF_PRPSPRPD_DATA:       //UHF PRPS和PRPD检测数据
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_UHF_PRPS)/*PDA_VIEW_CONFIG_TRANSLATE(TEXT_UHF_PRPSPRPD)*/;
        break;
    }
    case PDAServiceNS::UHF_PERIOD_DATA:         //UHF周期检测数据
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_UHF_PERIOD);
        break;
    }
    case PDAServiceNS::AE_MULTI:         //AE多图谱
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_AE_MULTI);
        break;
    }
    case PDAServiceNS::AE_AMP_CHART_DATA:   //AE幅值(图谱)
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_AE_CHART_AMP);
        break;
    }
    case PDAServiceNS::AE_PHASE_DATA:       //AE相位
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_AE_PHASE);
        break;
    }
    case PDAServiceNS::AE_PILOT_DATA:       //AE飞行
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_AE_PILOT);
        break;
    }
    case PDAServiceNS::AE_WAVE_DATA:        //AE波形
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_AE_WAVE);
        break;
    }
    case PDAServiceNS::AE_AMP_DATA:         //AE幅值
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_AE_AMP);
        break;
    }
    case PDAServiceNS::TEV_MULTI:         //TEV多图谱
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_TEV_MULTI);
        break;
    }
    case PDAServiceNS::TEV_AMP_DATA:        //TEV幅值
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_TEV_AMP);
        break;
    }
    case PDAServiceNS::TEV_PULSE_DATA:        //TEV脉冲
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_TEV_PULSE);
        break;
    }
    case PDAServiceNS::TEV_PRPS_PRPD:        //TEV PRPS
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_TEV_PRPS_PRPD);
        break;
    }
    case PDAServiceNS::IMAGES:        //图片
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_IMAGES);
        break;
    }
    case PDAServiceNS::RECORDS:        //音频
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_RECORDS);
        break;
    }
    case PDAServiceNS::VIDEOS:        //视频
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_VIDEOS);
        break;
    }
    case PDAServiceNS::INFRARED_SPEC:        //Infrared
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_INFRARED);
        break;
    }
    case PDAServiceNS::COREGOUND_CURRENT:
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_CORE_GROUNDING);
        break;
    }
    case PDAServiceNS::CABLEGROUND_CURRENT:
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_CABLE_GROUNDING);
        break;
    }
    case PDAServiceNS::LEAKAGE_CURRENT:
    {
        strTestData = PDA_VIEW_CONFIG_TRANSLATE(TEXT_LEAKAGE_CURRENT);
        break;
    }
    default:
    {
        qWarning("PDAView::testDataNameFromType, testDataNameFromType wrong type.");
        break;
    }

    }

    return strTestData;
}

}
