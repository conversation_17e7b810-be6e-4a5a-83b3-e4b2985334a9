#ifndef ACCESSG100PULSEDATASAVE_H
#define ACCESSG100PULSEDATASAVE_H

#include <ca/camapdefine.h>
#include <pdss/pdssmapdefine.h>
#include <pdss/pdssdatamap.h>
#include <pulsewave/pulsewavedatamap.h>
#include <ca/pulseprpddatamap.h>
#include <pulsewave/pulsewavemapdefine.h>
#include "dataSave/DataSave.h"
#include "module_global.h"

//ca pulse检测数据信息
typedef struct _CAPulseDataInfo
{
    //file head
    quint8 ucFreq;

    //map head
    DataMapHead stPDSSMapHead;            //图谱通用的头部信息
    DataMapHead stPulseWaveMapHead;            //图谱通用的头部信息
    DataMapHead stPulsePRPDMapHead;            //图谱通用的头部信息

    //map ext
    PDSSMapNS::PDSSMapInfo stPDSSInfo;
    PulseWaveMapNS::PulseWaveMapInfo stPulseWaveInfo;
    CAMapNS::CAPulsePRPDMapInfo stPulsePRPDInfo;

    //pdss map data
    QVector<PDSSMapNS::PDSSMapData> vecPDSSData;

    //pulse wave map data
    QVector<float> vecPulseWaveData;
    QString qsBinaryFilePath;

    //pulse prpd map data
    QVector<float> vecTriggerPhaseData;
    QVector<float> vecPulseMaxData;
    QVector<quint8> vecPRPDColor;

    _CAPulseDataInfo()
    {
        ucFreq = 50;
        vecPDSSData.clear();
        vecPulseWaveData.clear();
        vecTriggerPhaseData.clear();
        vecPulseMaxData.clear();
        vecPRPDColor.clear();
        qsBinaryFilePath.clear();
    }
}CAPulseDataInfo;

class MODULESHARED_EXPORT AccessG100PulseDataSave : public DataSave
{
public:
    /************************************************
     * 函数名   : AccessG100PulseDataSave
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 构造函数
     ************************************************/
    explicit AccessG100PulseDataSave();

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData);

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData, const QString &qsSavedPath);

    INT32 getData(const QString& strFileName, void *pData);

    void getFileHead(DataFile *psDataFile);

    UINT32 fileSavedSize();

    qint64 savedFileTotalSize();

    void setSaveDataTime(QDateTime SaveDataDateTime);

protected:
    /************************************************
     * 函数名   : getDataTypeFolder
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件夹名
     * 功能     : 获取图谱类型文件存储最上层文件夹名
     ************************************************/
    QString getDataTypeFolder(void);

    /************************************************
     * 函数名   : getFileNameSuffix
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件后缀名
     * 功能     : 获取数据文件后缀名
     ************************************************/
    QString getFileNameSuffix(void);

    /************************************************
     * 函数名   : getStringFromData
     * 输入参数 : pDatas: 数据; uiCounts: 数据个数
     * 输出参数 : NULL
     * 返回值   : 转换后的字符串
     * 功能     : 将数据转成base64的字符串
     ************************************************/
    QString getStringFromData( void *pDatas, UINT32 uiCounts);

    /************************************************
     * 函数名   : saveExtInformation
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储可扩展信息
     ************************************************/
    void saveExtInformation(XMLDocument& doc);

    /************************************************
     * 函数名   : saveRawData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储数据部分
     ************************************************/
    void saveRawData(XMLDocument& doc);

    /************************************************
     * 函数名   : organizeData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 组织数据文件信息
     ************************************************/
     void organizeData(XMLDocument& doc);

     /************************************************
      * 函数名   : parseData
      * 输入参数 : baData: 数据
      * 输出参数 : pData: 解析到的数据
      * 返回值   : void
      * 功能     : 解析数据
      ************************************************/
     void parseData(const QByteArray& baData, void *pData, const QString& strFileName = "");

     /************************************************
      * 函数名   : getFileName
      * 输入参数 :  dateTime: 时间
      * 输出参数 : NULL
      * 返回值   : 文件名
      * 功能     : 从日期生成指定格式文件名
      ************************************************/
     QString getFileName(const QDateTime& dateTime);

     /************************************************
      * 函数名   : saveHeadData
      * 输入参数 : doc: XML文件
      * 输出参数 : NULL
      * 返回值   : void
      * 功能     : 在XML文件中存储数据头信息
      ************************************************/
     void saveHeadData(XMLDocument& doc);

     /************************************************
      * 函数名   : saveToDataFile
      * 输入参数 : eTestedDataType: 测试数据类型; baData: 数据
      * 输出参数 : NULL
      * 返回值   : void
      * 功能     : 保存到数据文件
      ************************************************/
     bool saveToDataFile(TestedDataType eTestedDataType, QByteArray baData);

private:
     /************************************************
      * 函数名   : getBase64FromData
      * 输入参数 : piDatas: 数据; iCounts: 数据个数
      * 输出参数 : NULL
      * 返回值   : 转换后的字符串
      * 功能     : 将数据转成base64的字符串
      ************************************************/
     QString getBase64FromData( int *piDatas, int iCounts);

     void addPulsePRPDMap(DataFile *pFile);

     void addPulseWaveMap(DataFile *pFile);

     void addPDSSMap(DataFile *pFile);

     void setPulsePRPDMapData(CAPulsePRPDDataMap *pMap);

     void setPulsePRPDMapInfo(CAPulsePRPDDataMap *pMap);

     void setPulsePRPDMapHead(CAPulsePRPDDataMap *pMap);

     void setPulseWaveMapData(PulseWaveDataMap *pMap);

     void setPulseWaveMapInfo(PulseWaveDataMap *pMap);

     void setPulseWaveMapHead(PulseWaveDataMap *pMap);

     void setPDSSMapData(PDSSDataMap *pMap);

     void setPDSSMapInfo(PDSSDataMap *pMap);

     void setPDSSMapHead(PDSSDataMap *pMap);

    void getPDSSMapHead(DataFile *psDataFile, PDSSDataMap *pMap);
    void getPDSSMapInfo(PDSSDataMap *pMap);
    void getPDSSMapInfo( const PDSSMapNS::PDSSMapInfo &stMapInfo);
    void getPDSSMapData(PDSSDataMap *pPDSSMap, CAPulseDataInfo *pstDataInfo);

    void getPulseWaveMapHead(DataFile *psDataFile, PulseWaveDataMap *pMap);
    void getPulseWaveMapInfo(PulseWaveDataMap *pMap);
    void getPulseWaveMapInfo( const PulseWaveMapNS::PulseWaveMapInfo &stMapInfo);
    void getPulseWaveMapData(PulseWaveDataMap *pPDSSMap, CAPulseDataInfo *pstDataInfo);

    void getPulsePRPDMapHead(DataFile *psDataFile, CAPulsePRPDDataMap *pMap);
    void getPulsePRPDMapInfo(CAPulsePRPDDataMap *pMap);
    void getPulsePRPDMapInfo( const CAMapNS::CAPulsePRPDMapInfo  &stMapInfo);
    void getPulsePRPDMapData(CAPulsePRPDDataMap *pPDSSMap, CAPulseDataInfo *pstDataInfo);

private:
    CAPulseDataInfo *m_pstDataInfo;

    XMLDocument m_stSavedDoc;

    UINT32 m_uiTotalFileSize;

    QString m_strBinaryFile;   //二进制文件名
    QDateTime m_SaveDataDateTime;

};

#endif // ACCESSG100PULSEDATASAVE_H
