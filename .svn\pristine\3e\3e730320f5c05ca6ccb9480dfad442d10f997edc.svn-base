#ifndef SOFTWAREINFOVIEW_H
#define SOFTWAREINFOVIEW_H

#include <QFrame>
#include <QProgressBar>
#include <QTextBrowser>
#include "datadefine.h"
#include "basechartview.h"
#include "softwareupdatechart.h"
#include "controlButton/PopupButton.h"

#define MAX_DOWNLOAD_PROGRESS 100

class SoftwareInfoView : public BaseChartView
{
    Q_OBJECT
public:
    explicit SoftwareInfoView(QWidget *parent = 0);

    ~SoftwareInfoView();

protected slots:
    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

private slots:
    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：软件安装包信息有更新的处理
    *************************************************************/
    void onSoftwareInfoUpdated();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：软件安装包下载进度有更新的处理
    *************************************************************/
    void onDownloadProgressUpdated();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：与云端通信错误的处理
    *************************************************************/
    void onCloudState( int iState );

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：刷新按钮点击事件的处理
    *************************************************************/
    void onRefreshClicked();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：下载按钮点击事件的处理
    *************************************************************/
    void onDownloadClicked();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：安装按钮点击事件的处理
    *************************************************************/
    void onInstallClicked();

private:
    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：创建界面控件并进行布局
    *************************************************************/
    void createUI();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：设置界面显示的内容
    *************************************************************/
    void setUIData();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：设置界面显示的软件基本信息
    *************************************************************/
    void setSoftwareInfo();

    /*************************************************
    函数名：
    输入参数:
    输出参数：
    返回值：
    功能：设置界面显示的下载进度信息
    *************************************************************/
    void setDownloadProgress();

private:
    SoftwareUpdateChart * m_pChart; //显示软件信息的主界面
    PushButtonBar * m_pButtonBar;
    bool m_bDownloading;//是否正在下载
    int m_downloadProgress; //下载进度
    PopupButton *m_pDownloadBtn;
};

#endif // SOFTWAREINFOVIEW_H
