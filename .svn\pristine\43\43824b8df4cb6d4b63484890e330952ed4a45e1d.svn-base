#ifndef PDASTRUCTDATADEFINE_H
#define PDASTRUCTDATADEFINE_H

#include <QObject>

enum ChartType
{
    Type_Unknown = 1,       //未知或其他图谱
    Type_Prps3D = 2,        //PRPS三维图
    Type_Scope = 3,         //示波器截图
    Type_AeAmpView = 4,     //AE幅值图
    Type_AePhaseView = 5,   //AE相位图
    Type_AePulseView = 6,   //AE脉冲图
    Type_Prpd2D = 7,        //PRPD二维图
    Type_AeWaveView = 8,        //AE波形图
    Type_AeAudioView = 9,       //AE声谱图
    Type_Prpd3D = 10,       //PRPD三维图
    Type_Infrared = 11,     //红外照片
    Type_ElecDev = 12,      //电力设备照片
    Type_AeAmpVal = 20,     //AE幅值数值
    Type_Tev = 21,          //TEV数据
};

enum PrpsDiagnoseType
{
    PRPS_NORMAL = 1,                    //normal 正常
    PRPS_CORONA = 2,                    //corona 电晕
    PRPS_FLOATING_ELECTRODE = 3,        //floating electrode 悬浮
    PRPS_VOID = 4,                      //void 气隙
    PRPS_SURFACE = 5,                   //surface 沿面
    PRPS_PARTICLE = 6,                  //particle 微粒
    PRPS_NOISE = 7,                     //noise 噪声
    PRPS_CALIBRATION = 8,               //calibration 标准测试信号
    PRPS_UNKNOWN = 9,                   //unknown 未知
    PRPS_INSUFFICIENT_DATA = 10,        //insufficient data 数据不足
    PRPS_DRILL_NOISE = 11,              //drill noise 电钻干扰
    PRPS_ENERGYSAVINGLAMPS_NOISE = 12,  //energy saving lamps noise 节能灯干扰
    PRPS_FAN_NOISE = 13,                //fan noise 风机干扰
    PRPS_IGNITION_NOISE = 14,           //ignition noise 电子打火干扰
    PRPS_INTERPHONE_NOISE = 15,         //interphone noise 对讲机干扰
    PRPS_MICROWAVESULFUR_NOISE = 16,    //microwave sulfer noise 微波硫灯干扰
    PRPS_MOTOR_NOISE = 17,              //motor noise 电机干扰
    PRPS_RADAR_NOISE = 18,              //radar noise 雷达干扰
    PRPS_SPARKLEAKDETECTOR_NOISE = 19,  //spark leak detector noise 火花检漏器干扰
    PRPS_MOBILE_NOISE = 20,             //mobile noise 手机干扰
    PRPS_PD = 21,                       //pd 局放
    PRPS_NOTPD = 22,                    //not pd 非局放
    PRPS_INSULATION = 23,               //insulation 绝缘缺陷
    PRPS_MECHANICAL_VIBRATION = 24,     //mechanical vibration 机械振动
    PRPS_LIGHT_NOISE = 25,              //light noise 灯光干扰
};

#endif // PDASTRUCTDATADEFINE_H
