/*
* Copyright (c) 2017.03，南京华乘电气科技有限公司
* All rights reserved.
*
* recordplayview.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年03月13日
* 摘要：recordplayview模块接口定义

* 当前版本：1.0
*/

#ifndef RECORD_PLAY_VIEW_H
#define RECORD_PLAY_VIEW_H

#include <QApplication>
#include "recordplayviewdefine.h"
#include "functionPanelView/FunctionPanelView.h"
#include "recordplayviewdefine.h"
#include "view_global.h"

namespace ReordPlayView
{
typedef enum _Function_  //功能
{
    RECORD = 0,  //录音
    PLAY,  //播放
    DELETE, //删除
    FUNCTION_COUNT
}Function;
}

class VIEWSHARED_EXPORT RecordPlayPanelView : public FunctionPanelView
{
    Q_OBJECT
public:
    /*************************************************
    函数名： RecordPlayPanelView(const FunctionButtonPanel::Config *pConfig, const QString &strBackgroundIcon, QWidget *parent = 0)
    输入参数： pConfig：子功能按钮配置
              strBackgroundIcon：背景图片
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit RecordPlayPanelView(const FunctionButtonPanel::Config *pConfig, const QString &strBackgroundIcon, QWidget *parent = 0);

    /*************************************************
    函数名： ~RecordPlayPanelView()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~RecordPlayPanelView();

    /*处理msgbox里文本内容过长（目前仅保存成功显示文件名时使用）*/
    void processTooLongMsgText(QString &strText);

protected:
    /*************************************************
    函数名： onButtonPressed(quint8 ucID, const FunctionButtonPanel::Config *pConfig)
    输入参数： ucID：按钮ID
              pConfig：按钮配置
    输出参数： NULL
    返回值： NULL
    功能： 响应按钮按下动作
    *************************************************************/
    void onButtonPressed(quint8 ucID, const FunctionButtonPanel::Config *pConfig);

    /*************************************************
    功能： 响应按钮按下动作
    输入参数：
            event -- 按键事件
    *************************************************************/
    void keyPressEvent( QKeyEvent *event );

private:
    /*************************************************
    功能： 删除音频文件
    *************************************************************/
    void deleteAudio();

private slots:
    /*************************************************
    功能： 槽，响应录音完成
    输入参数：
            strRecordFilePath -- 录音文件保存的路径
            uint -- 录音时长
    *************************************************************/
    void onRecorded(const QString& strRecordFilePath, const uint uiRecordingTime);

};

extern const FunctionButtonPanel::Config g_AudioFunctionConfig[ReordPlayView::FUNCTION_COUNT];

#endif // RECORD_PLAY_VIEW_H
