/*
* Copyright (c) 2016.12，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：rfidview.h
*
* 初始版本：1.0
* 作者：赵勇军
* 修改日期：2017年5月18日
* 摘要：rfid二级菜单申明
* 当前版本：1.0
*/
#ifndef RFIDVIEW_H
#define RFIDVIEW_H

#include <QApplication>
#include "functionPanelView/FunctionPanelView.h"


//国际化宏定义
namespace RFIDView
{
//功能
typedef enum _Function_
{
    RFID_READ = 0,//RFID Read
    RFID_WRITE,//RFID Write
    FUNCTION_COUNT
}Function;

}

class RFIDViewPanel : public FunctionPanelView
{
    Q_OBJECT
public:
    /************************************************
     * 功能     : 构造函数
     * 输入参数 :
     *      pConfig -- 配置
     *      strBackgroundIcon -- 背景图片
     *      parent -- 父窗体
     * 输出参数 : NULL
     * 返回值   : NULL
     ************************************************/
    explicit RFIDViewPanel( const FunctionButtonPanel::Config* pConfig,
                            const QString& strBackgroundIcon,
                            QWidget *parent = 0 );

    /************************************************
     * 功能     : 析构函数
     ************************************************/
    ~RFIDViewPanel();

protected:
    /************************************************
     * 功能     : 响应按钮按下动作
     * 注：如有子功能表，则默认打开子功能表
     * 输入参数：
     *      ucID -- 按钮ID
     *      pConfig -- 按钮配置
     ************************************************/
    void onButtonPressed( quint8 ucID, const FunctionButtonPanel::Config* pConfig );


private:
    /************************************************
     * 功能     : 创建rfid读取界面
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     ************************************************/
    void createRFIDReadView();

    /************************************************
     * 功能     : 创建RFID写入界面
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     ************************************************/
    void createRFIDWriteView();

};

extern const FunctionButtonPanel::Config g_RFIDFunctionConfig[RFIDView::FUNCTION_COUNT];

#endif // RFIDVIEW_H
