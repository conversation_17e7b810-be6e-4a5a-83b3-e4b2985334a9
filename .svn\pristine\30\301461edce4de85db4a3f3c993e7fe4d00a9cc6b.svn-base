/*
* Copyright (c) 2017.05，南京华乘电气科技有限公司
* All rights reserved.
*
* rfidwriteview.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：207年5月18日
* 摘要：RFID写入界面
* 当前版本：1.0
*/
#ifndef RFIDWRITEVIEW_H
#define RFIDWRITEVIEW_H
#include <QWidget>
#include <QStackedLayout>
#include "Widget.h"
#include "RFIDInfoWidget.h"
#include "loadingView/LoadingView.h"
//#ifdef Q_PROCESSOR_ARM
class RFIDWriteView : public QWidget
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent:父窗口指针
    *************************************************************/
    RFIDWriteView( QWidget* parent = 0 );

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~RFIDWriteView();

    /*************************************************
    功能： 保存rfid扫描结果的信息
    输入参数:
        stInfo -- rfid扫描结果
    *************************************************************/
    void setCardInfo(ReadRFIDData &stRFIDReadData);

    /*************************************************
    功能： 设置标题
    输入参数:
        strTitle -- 标题
    *************************************************************/
    void setTitle( const QString& strTitle );

protected:
    /*************************************************
    功能： 显示事件
    *************************************************************/
    void showEvent(QShowEvent *);

    /*************************************************
    功能： 关闭事件
    *************************************************************/
    void closeEvent(QCloseEvent *);

    /*************************************************
    功能： 按键事件
    *************************************************************/
    void keyPressEvent(QKeyEvent *event);

private slots:
    /*************************************************
    功能： 槽函数， 写入RFID
    输入参数:NULL
    *************************************************************/
    void writeRFIDInfo();

    /*************************************************
    功能： 槽函数， 取消写入RFID
    输入参数:NULL
    *************************************************************/
    void cancelWriteRFID();

    /*************************************************
    功能： 槽函数， 处理RFID写入结果
    输入参数:
        iResult: 0:成功；否则失败
    *************************************************************/
    void onWriteRFIDResult(int iResult);

    /*************************************************
    功能： 扫描完成, 响应sigScanRFIDResult(INT32)，以获取设备信息进行显示
    输入参数:
        iResult:扫描结果
        data:扫描数据
    *************************************************************/
    void onScanRFIDResult( int iResult, ReadRFIDData data );

private:
    /*************************************************
    功能： 把rfid信息写入rfid芯片
    输入参数:stRFIDReadData---rfid数据
    *************************************************************/
    INT32 writeRFID( ReadRFIDData &stRFIDReadData );

    /*************************************************
    功能： 读取rfid信息
    *************************************************************/
    void readRFID();

    /*************************************************
    功能： 根据错误码转成错误字符串
    输入参数:eError---错误码
    输出参数:strMsg---错误字符串
    *************************************************************/
    void errorCode2Msg(RfidInfoWidget::ErrorType eError, QString &strMsg);

private:
    RfidInfoWidget* m_pScanInfo;
    QLabel* m_plabelTitle;
    ScanService *m_pScanService;

    //LoadingView *m_pLoadingWidget;
    QWidget *m_pLayoutWidget;
    //QStackedLayout *m_pStackLayout;

    enum {
        RFID_WRITE_BTN_WIDTH  = 100,        // 按键宽度
        RFID_WRITE_BTN_HEIGHT = 70,//按键高度;
        TITLE_BAR_HEIGHT = 100 //标题栏高度
    };
};

#endif // RFIDWRITEVIEW_H
//#endif
