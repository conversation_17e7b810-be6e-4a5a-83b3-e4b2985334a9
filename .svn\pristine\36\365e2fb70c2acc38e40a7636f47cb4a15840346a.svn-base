#include "aetjtestbgview.h"
#include <QStackedWidget>
#include "ae/AEView.h"
#include "ae/AEViewConfig.h"
#include "controlButton/ControlButtonInfo.h"
#include "controlButton/CmdButton.h"
#include "controlButton/RadioButton.h"
#include "controlButton/SliderButton.h"
#include "ae/AEConfig.h"
#include "messageBox/msgbox.h"
#include "window/Window.h"
#include "aeampview/AEAmpChart.h"
#include "ae/aephaseview/AEPhaseChart.h"
#include "ae/aeflyview/AEFlyChart.h"
#include "ae/aewaveview/AeWave.h"
#include "peripheral/peripheralservice.h"
#include "functionconfigmanager/funccfgsrvmanager.h"
#include <datafile.h>
#include "appconfig.h"
#include "aebinaryfile.h"
#include "ae/AE.h"
#include "global_log.h"
#include "dataspecification/dataspecification.h"
#include "dataspecification/ae/aeampspectrum.h"
#include "dataspecification/ae/aephasespectrum.h"
#include "dataspecification/ae/aewavespectrum.h"
#include "dataspecification/ae/aepulsespectrum.h"
#include "dealdata/dealdata.h"
#include "customaccessUi/customaccessui_func.h"

const int INVALID_USER = -1;
const int AE_PULSE_CNT_MAX = 1000;
const int MAX_FLY_INTVL = 20;
const int WAVE_NUM_PER_T = 819;
const int WAVE_NUM_PER_5T_WIRELESS = 250;
const int WAVE_MAX_T = 50;

namespace aetestbgview
{

typedef enum _AEButton
{
    BUTTON_CHART_TYPE = 0,//类型
    BUTTON_START_SAMPLE,//开始采样
    BUTTON_SAVE,//保存
    BUTTON_MORE,//更多

    BUTTON_AE_GAIN,//增益
    BUTTON_AE_TRIGGER_VALUE,//触发值
    BUTTON_AE_CHANNEL,//通道
    BUTTON_AE_FILTER, //带宽模式
    BUTTON_AE_DELETE_DATA,//删除数据
    BUTTON_HISTORY_DATA,//历史数据

    //ae amp
    BUTTON_AE_SPECTRUM,//频率成分

    //ae phase
    BUTTON_AE_PHASE_ALIAS,//相移
    BUTTON_AE_CLOSE_DOOR_TIME,//关门时间

    //ae fly
    BUTTON_AE_OPEN_DOOR_TIME,//开门时间
    BUTTON_AE_TIME_INTERVAL, //时间间隔

    //ae wave
    BUTTON_AE_AMPLITUDE_RANGE,//幅值范围
    BUTTON_AE_SAMPLE_TIME,//采样时间

}AEButton;

//图谱模式
const ButtonInfo::RadioValueConfig s_AEChartTypeCfg =
{

    AE::TEXT_CHART_TYPE_OPTIONS, sizeof(AE::TEXT_CHART_TYPE_OPTIONS) / sizeof(char*)
};

const ButtonInfo::RadioValueConfig s_AEChartTypeCfg_ZJHY =
{

    AE::TEXT_CHART_TYPE_OPTIONS_ZJHY, sizeof(AE::TEXT_CHART_TYPE_OPTIONS_ZJHY) / sizeof(char*)
};

//增益
const ButtonInfo::RadioValueConfig s_AEGainCfg =
{
    AE::TEXT_GAIN_OPTIONS, sizeof(AE::TEXT_GAIN_OPTIONS)/sizeof(char*)
};

//触发值
const ButtonInfo::RadioValueConfig s_AETriggerCfg =
{
    NULL, AE::TRIGGER_LEVEL_COUNT
};

//频率成分
const ButtonInfo::SliderValueConfig s_AESpectrumCfg =
{
    AE::SPECTRUM_MIN, AE::SPECTRUM_MAX, AE::SPECTRUM_STEP
};

//通道
const ButtonInfo::RadioValueConfig s_AEChannel=
{
    AE::TEXT_CHANNELS, sizeof(AE::TEXT_CHANNELS)/sizeof(char*)
};

//带宽模式
const ButtonInfo::RadioValueConfig s_AEFilterCfg =
{
    AE::TEXT_FILTER_OPTIONS, sizeof(AE::TEXT_FILTER_OPTIONS)/sizeof(char*)
};

//相位偏移
const ButtonInfo::SliderValueConfig s_AEPhaseShift =
{
    AE::PHASE_SHIFT_MIN, AE::PHASE_SHIFT_MAX, AE::PHASE_SHIFT_STEP
};

//关门时间
const ButtonInfo::GroupSliderValueConfig s_AECloseDoorButtonInfo =
{
    AE::CLOSE_DOOR_TIME, AE::CLOSE_DOOR_TIME_COUNT
};

//同步
const ButtonInfo::RadioValueConfig s_AESync =
{
    AE::TEXT_SYNC_OPTIONS, sizeof(AE::TEXT_SYNC_OPTIONS)/sizeof(char*)
};

//开门时间
const ButtonInfo::GroupSliderValueConfig s_OpenDoorTimeButtonInfo =
{
    AE::OPEN_DOOR_TIME, AE::OPEN_DOOR_TIME_COUNT
};

//时间间隔
const ButtonInfo::GroupSliderValueConfig s_TimeIntervalButtonInfo =
{
    AE::TIME_INTERVAL_VALUE, AE::TIME_INTERVAL_COUNT
};

//采样时间
const ButtonInfo::RadioValueConfig s_SampleTime =
{
    AE::SAMPLE_TIME_OPTIONS, sizeof(AE::SAMPLE_TIME_OPTIONS)/sizeof(char*)
};

//幅值范围
const ButtonInfo::RadioValueConfig s_AEAmpRangeCfg =
{
    NULL, AE::AMP_RANGE_COUNT
};


//"更多.."按钮定义
const ButtonInfo::Info g_AEOpenDoorBtn = {BUTTON_AE_OPEN_DOOR_TIME, {ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_OPEN_DOOR_TIME, AE::TEXT_US, ":/images/sampleControl/openDoorTime.png", &s_OpenDoorTimeButtonInfo}};//开门时间
const ButtonInfo::Info g_AECloseDoorBtn = {BUTTON_AE_CLOSE_DOOR_TIME, {ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_CLOSE_DOOR_TIME, AE::TEXT_MS, ":/images/sampleControl/closeDoorTime.png", &s_AECloseDoorButtonInfo}};//关门时间
const ButtonInfo::Info g_AETimeInterval = {BUTTON_AE_TIME_INTERVAL, {ButtonInfo::FIXED_GROUP_SLIDER, AE::TEXT_TIME_INTERVAL, AE::TEXT_MS, ":/images/sampleControl/timeInterval.png", &s_TimeIntervalButtonInfo}};//时间间隔
const ButtonInfo::Info g_AESpectrumBtn = {BUTTON_AE_SPECTRUM, {ButtonInfo::FIXED_STEP_SLIDER, AE::TEXT_SPECRUM, AE::TEXT_HZ, ":/images/sampleControl/freqCompent.png", &s_AESpectrumCfg}};//频率成分
const ButtonInfo::Info g_AEPhaseAliasBtn = {BUTTON_AE_PHASE_ALIAS, {ButtonInfo::FIXED_STEP_SLIDER, AE::TEXT_PHASE_SHIFT, AE::TEXT_DEGREE, ":/images/sampleControl/phaseShift.png", &s_AEPhaseShift}};//相位偏移
const ButtonInfo::Info g_AEGainBtn = {BUTTON_AE_GAIN, {ButtonInfo::RADIO, AE::TEXT_GAIN, NULL, ":/images/sampleControl/gain.png", &s_AEGainCfg}};//增益
const ButtonInfo::Info g_AETriggerValBtn = {BUTTON_AE_TRIGGER_VALUE, {ButtonInfo::RADIO, AE::TEXT_TRIGGER_VALUE, NULL, ":/images/sampleControl/freqCompent.png", &s_AETriggerCfg}};//触发值
const ButtonInfo::Info g_AEAmpRangeBtn = {BUTTON_AE_AMPLITUDE_RANGE, {ButtonInfo::RADIO, AE::TEXT_AMP_RANGE, NULL, ":/images/sampleControl/amplitudeScope.png", &s_AEAmpRangeCfg}};//幅值范围
const ButtonInfo::Info g_AESampleTimeBtn = {BUTTON_AE_SAMPLE_TIME, {ButtonInfo::RADIO, AE::TEXT_SAMPLE_TIME, NULL, ":/images/sampleControl/sampleTime.png", &s_SampleTime}};//采样时间
const ButtonInfo::Info g_AEChannelBtn = {BUTTON_AE_CHANNEL, {ButtonInfo::RADIO, AE::TEXT_CHANNEL, NULL, "", &s_AEChannel}};//通道
const ButtonInfo::Info g_AEFilterBtn = {BUTTON_AE_FILTER, {ButtonInfo::RADIO, AE::TEXT_FILTER, NULL, "", &s_AEFilterCfg}};//带宽模式;
const ButtonInfo::Info g_AELoadDataBtn = {BUTTON_HISTORY_DATA, {ButtonInfo::COMMAND, AE::TEXT_LOAD_DATA, NULL, ":/images/sampleControl/importData.png", NULL}};//载入数据
const ButtonInfo::Info g_AEDeleteDataBtn = {BUTTON_AE_DELETE_DATA, {ButtonInfo::COMMAND, AE::TEXT_DELETE_DATA, NULL, ":/images/sampleControl/triggerValue.png", NULL}};//删除数据

}

const qint32 AE_AMP_DATA_COUNT  = 8;
const quint8 AE_AMP_SYNC_SUCCESS  = 1;
/*************************************************
功能： 构造函数
输入参数:
    strTitle:标题
    parent:父控件指针
*************************************************************/
AETJTestBGView::AETJTestBGView( const QString& strTitle, QWidget *parent )
    : AEAmpViewBase( strTitle, parent )
{
    //初始化使用的数据
    initDatas();

    //新建图谱
    m_pChart = createChart(parent);
    setChart( m_pChart );

    createButtonBarInfo();

    initBtnBarInfo();

    createMoreView(m_pButtonBar);

    //触发值列表根据增益、量纲动态变化
    resetTriggerList(m_eGain, m_eUnit);

    //设置数据
    setButtonBarDatas();

    setChartParameters();

    setWorkMode();

    //设置工作参数
    setAllWorkingSet();

    MultiServiceNS::SampleUser user;
    user.eSampleType = MultiServiceNS::SPECTRUM_AE_AMP;
    addUser(user);
    //启动采集
    startSample();
}

/*************************************************
功能： 析构
*************************************************************/
AETJTestBGView::~AETJTestBGView( )
{
    this->disconnect(this);

    saveConfig();//存储到配置文件中
    stopSample();
    if(NULL != m_pFilesItemListView)
    {
        delete m_pFilesItemListView;
        m_pFilesItemListView = NULL;
    }

    if(m_pAmpMoreBtnInfo)
    {
        delete [] m_pAmpMoreBtnInfo;
        m_pAmpMoreBtnInfo = NULL;
    }

    if(m_pFlyMoreBtnInfo)
    {
        delete [] m_pFlyMoreBtnInfo;
        m_pFlyMoreBtnInfo = NULL;
    }

    if(m_pPhaseMoreBtnInfo)
    {
        delete [] m_pPhaseMoreBtnInfo;
        m_pPhaseMoreBtnInfo = NULL;
    }

    if(m_pWaveMoreBtnInfo)
    {
        delete [] m_pWaveMoreBtnInfo;
        m_pWaveMoreBtnInfo = NULL;
    }

    if(m_pBtnBarInfo)
    {
        delete [] m_pBtnBarInfo;
        m_pBtnBarInfo = NULL;
    }

}

/*****************************************
 * 功能：创建按钮栏
 * **********************************************/
void AETJTestBGView::createButtonBarInfo()
{
    m_pButtonBar = NULL;
    m_pBtnBarInfo = NULL;

    QVector<ButtonInfo::Info> qvtBtnBarInfos;
    qvtBtnBarInfos.clear();

//    SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
//    if(SystemSet::ACCESS_PROTO_ZJHY == eProtocol || SystemSet::ACCESS_PROTO_BJRZT == eProtocol)
//    {
//        qvtBtnBarInfos.push_back({aetestbgview::BUTTON_CHART_TYPE, {ButtonInfo::RADIO, AE::TEXT_CHART_TYPE, NULL, ":/images/sampleControl/sampleMode.png", &(aetestbgview::s_AEChartTypeCfg_ZJHY)}});
//    }
//    else
//    {
//        qvtBtnBarInfos.push_back({aetestbgview::BUTTON_CHART_TYPE, {ButtonInfo::RADIO, AE::TEXT_CHART_TYPE, NULL, ":/images/sampleControl/sampleMode.png", &(aetestbgview::s_AEChartTypeCfg)}});
//    }

    qvtBtnBarInfos.push_back(aetestbgview::g_AEGainBtn);
    qvtBtnBarInfos.push_back({aetestbgview::BUTTON_START_SAMPLE, {ButtonInfo::COMMAND, AE::TEXT_START, NULL, ":/images/sampleControl/sample.png", NULL}});
    qvtBtnBarInfos.push_back({aetestbgview::BUTTON_SAVE, {ButtonInfo::COMMAND, AE::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/noiseTest.png", NULL}});
    qvtBtnBarInfos.push_back({aetestbgview::BUTTON_MORE, {ButtonInfo::COMMAND, AE::TEXT_MORE_CONFIG, NULL, ":/images/sampleControl/noiseClean.png", NULL}});

    int iBtnCnt = qvtBtnBarInfos.size();
    m_pBtnBarInfo = new ButtonInfo::Info[iBtnCnt];
    for (int i = 0; i < iBtnCnt; ++i)
    {
        m_pBtnBarInfo[i] = qvtBtnBarInfos[i];
    }

    //创建按钮栏
    m_pButtonBar = createButtonBar(AE::CONTEXT, m_pBtnBarInfo, iBtnCnt);

    m_pSampleBtn = buttonBar()->button(aetestbgview::BUTTON_START_SAMPLE);
    m_pSaveDataBtn = buttonBar()->button(aetestbgview::BUTTON_SAVE);
    m_pMoreBtn = buttonBar()->button(aetestbgview::BUTTON_MORE);
    ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_GAIN)))->setPopupMode(PopupWidget::SWITCH_MODE);

    return;
}

/*****************************************
 * 功能：初始化按钮栏信息
 * **********************************************/
void AETJTestBGView::initBtnBarInfo()
{
    m_pAmpMoreBtnInfo = NULL;
    m_qui8AmpMoreBtnCnt = 0;

    m_pFlyMoreBtnInfo = NULL;
    m_qui8FlyMoreBtnCnt = 0;

    m_pPhaseMoreBtnInfo = NULL;
    m_qui8PhaseMoreBtnCnt = 0;

    m_pWaveMoreBtnInfo = NULL;
    m_qui8WaveMoreBtnCnt = 0;

    QVector<ButtonInfo::Info> qvtAmpMoreBtnInfos;
    qvtAmpMoreBtnInfos.clear();

    QVector<ButtonInfo::Info> qvtFlyMoreBtnInfos;
    qvtFlyMoreBtnInfos.clear();

    QVector<ButtonInfo::Info> qvtPhaseMoreBtnInfos;
    qvtPhaseMoreBtnInfos.clear();

    QVector<ButtonInfo::Info> qvtWaveMoreBtnInfos;
    qvtWaveMoreBtnInfos.clear();


    qvtAmpMoreBtnInfos.push_back(aetestbgview::g_AESpectrumBtn);
    //qvtAmpMoreBtnInfos.push_back(aetestbgview::g_AEGainBtn);
    //qvtAmpMoreBtnInfos.push_back(aetestbgview::g_AETriggerValBtn);

    qvtFlyMoreBtnInfos.push_back(aetestbgview::g_AEOpenDoorBtn);
    qvtFlyMoreBtnInfos.push_back(aetestbgview::g_AECloseDoorBtn);
    //qvtFlyMoreBtnInfos.push_back(aetestbgview::g_AETimeInterval);
    qvtFlyMoreBtnInfos.push_back(aetestbgview::g_AEGainBtn);
    qvtFlyMoreBtnInfos.push_back(aetestbgview::g_AETriggerValBtn);

    qvtPhaseMoreBtnInfos.push_back(aetestbgview::g_AECloseDoorBtn);
    qvtPhaseMoreBtnInfos.push_back(aetestbgview::g_AEPhaseAliasBtn);
    qvtPhaseMoreBtnInfos.push_back(aetestbgview::g_AEGainBtn);
    qvtPhaseMoreBtnInfos.push_back(aetestbgview::g_AETriggerValBtn);

    qvtWaveMoreBtnInfos.push_back(aetestbgview::g_AEGainBtn);
    qvtWaveMoreBtnInfos.push_back(aetestbgview::g_AETriggerValBtn);
    qvtWaveMoreBtnInfos.push_back(aetestbgview::g_AEAmpRangeBtn);
    //qvtWaveMoreBtnInfos.push_back(aetestbgview::g_AESampleTimeBtn);

    FuncConfigManagerNS::FunctionInfo stAEWirelessInfo;
    stAEWirelessInfo.iFuncID = FuncConfigManagerNS::AE_WIRELESS;
    stAEWirelessInfo.iParentID = FuncConfigManagerNS::AE;

    FuncConfigManagerNS::ConfigInfo stConfigInfo;
    FuncCfgSrvManager::instance()->getConfigInfo(stConfigInfo);
    int iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAEWirelessInfo);
    if(0 <= iIndex && iIndex < stConfigInfo.qvtFuncInfos.size())
    {
        if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
        {
            qvtAmpMoreBtnInfos.push_back(aetestbgview::g_AEChannelBtn);
            qvtFlyMoreBtnInfos.push_back(aetestbgview::g_AEChannelBtn);
            qvtPhaseMoreBtnInfos.push_back(aetestbgview::g_AEChannelBtn);
            qvtWaveMoreBtnInfos.push_back(aetestbgview::g_AEChannelBtn);
        }
    }

#ifdef _R3_DEFINED_
    qvtAmpMoreBtnInfos.push_back(aetestbgview::g_AEFilterBtn);
    qvtFlyMoreBtnInfos.push_back(aetestbgview::g_AEFilterBtn);
    qvtPhaseMoreBtnInfos.push_back(aetestbgview::g_AEFilterBtn);
    qvtWaveMoreBtnInfos.push_back(aetestbgview::g_AEFilterBtn);
#endif

//    qvtAmpMoreBtnInfos.push_back(aetestbgview::g_AELoadDataBtn);
//    qvtFlyMoreBtnInfos.push_back(aetestbgview::g_AELoadDataBtn);
//    qvtPhaseMoreBtnInfos.push_back(aetestbgview::g_AELoadDataBtn);
//    qvtWaveMoreBtnInfos.push_back(aetestbgview::g_AELoadDataBtn);

    //qvtAmpMoreBtnInfos.push_back(aetestbgview::g_AEDeleteDataBtn);
    //qvtFlyMoreBtnInfos.push_back(aetestbgview::g_AEDeleteDataBtn);
    //qvtPhaseMoreBtnInfos.push_back(aetestbgview::g_AEDeleteDataBtn);
    //qvtWaveMoreBtnInfos.push_back(aetestbgview::g_AEDeleteDataBtn);

    m_qui8AmpMoreBtnCnt = static_cast<quint8>(qvtAmpMoreBtnInfos.size());
    m_pAmpMoreBtnInfo = new ButtonInfo::Info[m_qui8AmpMoreBtnCnt];
    for (int i = 0; i < m_qui8AmpMoreBtnCnt; ++i)
    {
        m_pAmpMoreBtnInfo[i] = qvtAmpMoreBtnInfos[i];
    }

    m_qui8FlyMoreBtnCnt = static_cast<quint8>(qvtFlyMoreBtnInfos.size());
    m_pFlyMoreBtnInfo = new ButtonInfo::Info[m_qui8FlyMoreBtnCnt];
    for (int i = 0; i < m_qui8FlyMoreBtnCnt; ++i)
    {
        m_pFlyMoreBtnInfo[i] = qvtFlyMoreBtnInfos[i];
    }

    m_qui8PhaseMoreBtnCnt = static_cast<quint8>(qvtPhaseMoreBtnInfos.size());
    m_pPhaseMoreBtnInfo = new ButtonInfo::Info[m_qui8PhaseMoreBtnCnt];
    for (int i = 0; i < m_qui8PhaseMoreBtnCnt; ++i)
    {
        m_pPhaseMoreBtnInfo[i] = qvtPhaseMoreBtnInfos[i];
    }

    m_qui8WaveMoreBtnCnt = static_cast<quint8>(qvtWaveMoreBtnInfos.size());
    m_pWaveMoreBtnInfo = new ButtonInfo::Info[m_qui8WaveMoreBtnCnt];
    for (int i = 0; i < m_qui8WaveMoreBtnCnt; ++i)
    {
        m_pWaveMoreBtnInfo[i] = qvtWaveMoreBtnInfos[i];
    }

    return;
}

/*************************************************
功能： 槽，响应诊断结果
输入参数：
        qspDiagResultInfo -- 诊断结果
*************************************************************/
void AETJTestBGView::onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo)
{
    Q_UNUSED(qspDiagResultInfo)
}

/*************************************************
功能： 设置子任务对象
*************************************************************/
void AETJTestBGView::setSubTask(SubTask * pSubTask, const QString &strGapId)
{
    m_pSubTask = pSubTask;
    m_strGapId = strGapId;

    AE::UnitOption eUnit = AE::UNIT_MV;
    // GIS是mV，开关柜是dB
    if(m_pSubTask->getTestTaskType() == CustomAccessTaskNS::SwitchAEType)
    {
        eUnit = AE::UNIT_DB;
    }

    if (eUnit != m_eUnit)
    {
        m_eUnit = eUnit;
        if (AEAmpService* pService = getAEAmpService())
        {
            pService->setUnit( m_eUnit );
        }
        AEAmpChart* pChart = dynamic_cast<AEAmpChart*> (m_pChartStackWidget->widget(AEView::AE_AMP));
        if (pChart)
        {
            pChart->setUnit(m_eUnit);
        }
        resetTriggerList(m_eGain, m_eUnit); // 量纲变化时，重置触发值列表
    }
}

/*************************************************
功能： 设置已测数量
*************************************************************/
void AETJTestBGView::setTestedCnt(int Cnt)
{
    m_uiTestedCnt = Cnt;
    QString strLabel = QObject::trUtf8("Tested Count: ") + QString::number(m_uiTestedCnt);
    m_pTestedCountLabel->setText(strLabel);
}
/*************************************************
功能： 设置间隔名称
*************************************************************/
void AETJTestBGView::setBayName( QString strBayName )
{
    m_pBayNameLabel->setText( strBayName );
}
/*************************************************
功能： 创建更多界面
*************************************************************/
void AETJTestBGView::createMoreView(PushButtonBar* pButtonBar)
{
    if(m_eChartType == AEView::AE_AMP)
    {
        //创建更多设置栏
        createMoreConfigButtonBar(AE::CONTEXT, m_pAmpMoreBtnInfo, m_qui8AmpMoreBtnCnt);
    }
    else if(m_eChartType == AEView::AE_PHASE)
    {
        //创建更多设置栏
        createMoreConfigButtonBar(AE::CONTEXT, m_pPhaseMoreBtnInfo, m_qui8PhaseMoreBtnCnt);
    }
    else if(m_eChartType == AEView::AE_FLY)
    {
        //创建更多设置栏
        createMoreConfigButtonBar(AE::CONTEXT, m_pFlyMoreBtnInfo, m_qui8FlyMoreBtnCnt);
    }
    else
    {
        //创建更多设置栏
        createMoreConfigButtonBar(AE::CONTEXT, m_pWaveMoreBtnInfo, m_qui8WaveMoreBtnCnt);
    }
}

/*************************************************
功能： 初始化参数
*************************************************************/
void AETJTestBGView::initParameters()
{
    m_eUnit = AE::UNIT_MV;

    int iGroup = AE::GROUP_AE_AMPLITUDE;
    m_pConfig = ConfigManager::instance()->config();
    m_pConfig->beginGroup( Module::GROUP_AE );

    m_eGain = (AE::GainType)m_pConfig->value( AE::KEY_GAIN ).toUInt();
    m_eFilter = (AEFilter) m_pConfig->value(AE::KEY_FILTER).toUInt();
    m_eTriggerValue = (AE::TriggerValue)m_pConfig->value( AE::KEY_TRIGGER_VALUE, iGroup ).toUInt();
    m_iTriggerValue = AE::getThreshold( AE::UNIT_DEFAULT,m_eGain,m_eTriggerValue );
    m_usSpectrum = m_pConfig->value( AE::KEY_FREQ_COMPONENT, iGroup ).toUInt();
    m_eChannel = (AE::ChannelType)m_pConfig->value( AE::KEY_CHANNEL_TYPE ).toUInt();
    m_pConfig->endGroup();

    iGroup = AE::GROUP_AE_FLY;
    m_pConfig->beginGroup( Module::GROUP_AE );
    m_usOpenDoorTime = m_pConfig->value( AE::KEY_OPEN_DOOR_TIME, iGroup ).toUInt();
    m_usCloseDoorTime = m_pConfig->value( AE::KEY_CLOSE_DOOR_TIME, iGroup ).toUInt();
    m_iTimeInterval = m_pConfig->value( AE::KEY_TIME_INTERVAL, iGroup ).toUInt();
    m_pConfig->endGroup();
    m_usFlyPulseCnt = 0;

    iGroup = AE::GROUP_AE_PHASE;
    m_pConfig->beginGroup( Module::GROUP_AE );
    m_iPhaseShift = m_pConfig->value( AE::KEY_PHASE_SHIFT, iGroup ).toInt();
    m_pConfig->endGroup();
    m_eSyncState = Module::Not_Sync;
    m_usAEPhasePulseCnt = 0;

    iGroup = AE::GROUP_AE_WAVE;
    m_pConfig->beginGroup( Module::GROUP_AE );
    // 业务需求，因内置 -- 单次收到数据为1T数据 无线 -- 单次250个点 对应5T数据，此处仅针对界面横坐标显示 1T or 5T
    if( AE::WIRELESS == m_eChannel )
    {
        m_eSampleTime = AE::SAMPLE_TIME_5;
    }
    else
    {
        m_eSampleTime = AE::SAMPLE_TIME_1;
    }
    m_eAmpRange = (AE::AmpRange)m_pConfig->value( AE::KEY_AMPLITUDE_SCOPE, iGroup ).toUInt();
    m_pConfig->endGroup();
    return;
}

/*************************************************
功能： 初始化数据
*************************************************************/
void AETJTestBGView::initDatas()
{
    m_eChartType = AEView::AE_AMP;
    m_uiTestedCnt = 0;
    m_eListItemProcessType = PROCESS_TYPE_INVALID;

    m_pSampleBtn = NULL;
    m_pSaveDataBtn = NULL;
    m_pMoreBtn = NULL;
    m_pTestedCountLabel = NULL;
    m_pFilesItemListView = NULL;
    m_qui32GainChangedCnt = 0;
    m_eGainStatus = AE::GAIN_STATUS_NONE;

    initParameters();
    return;
}

/*************************************************
功能： 设置表格数据
*************************************************************/
void AETJTestBGView::setChartParameters()
{
    m_iTriggerValue = AE::getThreshold( AE::UNIT_DEFAULT,m_eGain,m_eTriggerValue );

    AEAmpChart *pChart =  dynamic_cast<AEAmpChart*> (m_pChartStackWidget->widget(AEView::AE_AMP));
    pChart->setUnit( m_eUnit );
    pChart->setGain( m_eGain );
    pChart->setChannel( m_eChannel );
    pChart->setSpectrum( m_usSpectrum );
    pChart->setSampleMode( AE::SAMPLEMODE_CONTINUOUS );

    AEPhaseChart *pChart2 =  dynamic_cast<AEPhaseChart*> (m_pChartStackWidget->widget(AEView::AE_PHASE));
    pChart2->setGain( m_eGain );
    pChart2->setChannel( m_eChannel );
    pChart2->setChannelValid( true );
    pChart2->setPulseCounting( m_usAEPhasePulseCnt );
    pChart2->setRunningMode( isSampling() );
    pChart2->setTrigger( m_iTriggerValue );
    pChart2->setSyncSource( Module::WIRELESS_SYNC );
    pChart2->setSyncState( m_eSyncState );
    pChart2->translatePhase( m_iPhaseShift );

    AEFlyChart *pChart3 =  dynamic_cast<AEFlyChart*> (m_pChartStackWidget->widget(AEView::AE_FLY));
    pChart3->setGain( m_eGain );
    pChart3->setTimeInterval( m_iTimeInterval );
    pChart3->setChannel( m_eChannel );
    pChart3->setChannelValid( true );
    pChart3->setPulseCounting( m_usFlyPulseCnt );
    pChart3->setRunningMode( isSampling() );
    pChart3->setTrigger( m_iTriggerValue );

    AEWave *pChart4 =  dynamic_cast<AEWave*> (m_pChartStackWidget->widget(AEView::AE_WAVE));
    pChart4->setSampleMode( AE::SAMPLEMODE_CONTINUOUS );
    pChart4->setGain( m_eGain );
    pChart4->setAmpRange( m_eAmpRange );
    pChart4->setChannelName( m_eChannel );
    pChart4->setSynchronousMode( Module::WIRELESS_SYNC,m_eSyncState );
    // 出界防务
    /*
    if( ( (int)m_eSampleTime < AE::SAMPLE_TIME_1 ) ||
        ( (int)m_eSampleTime > AE::SAMPLE_TIME_10 ) )
    {
        m_eSampleTime = AE::SAMPLE_TIME_1;
    }
    */

    // 业务需求，因内置 -- 单次收到数据为1T数据 无线 -- 单次250个点 对应5T数据，此处仅针对界面横坐标显示 1T or 5T
    if( AE::WIRELESS == m_eChannel )
    {
        m_eSampleTime = AE::SAMPLE_TIME_5;
    }
    else
    {
        m_eSampleTime = AE::SAMPLE_TIME_1;
    }

    pChart4->setSamplingPeriod(AE::SAMPLE_TIME[m_eSampleTime]);
    pChart4->setTriggerValue(AE::getThreshold( AE::UNIT_DEFAULT, m_eGain, m_eTriggerValue));
    return;
}

/*************************************************
功能： 设置工作模式
*************************************************************/
void AETJTestBGView::setWorkMode()
{
    AEAmpService* pService = getAEAmpService();
    if(NULL != pService)
    {
        pService->transaction();
        pService->setWorkMode(AE::MODE_AMPLITUDE);
    }
    else
    {
        qWarning() << "AETJTestBGView::setWorkMode: error, service handle is NULL!";
    }
}

/*************************************************
功能： 设置所有工作参数
*************************************************************/
void AETJTestBGView::setAllWorkingSet()
{
    if(AEAmpService* pService = getAEAmpService())
    {
        pService->transaction();
        pService->setGain( m_eGain );
        pService->setFilter(m_eFilter);
        pService->setTriggerValue( m_eTriggerValue );
        pService->setCloseDoorTime( AE::MODE_FLY, m_usCloseDoorTime );
        pService->setCloseDoorTime( AE::MODE_PHASE, m_usCloseDoorTime );
        pService->setOpenDoorTime( m_usOpenDoorTime );
        pService->setUnit( m_eUnit );
        pService->setSpectrum( m_usSpectrum );
        pService->setSyncSource( Module::SYNC_SOURCE_DEFAULT );
        pService->commit();

        pService->setChannel(m_eChannel);
    }
    else
    {
        qWarning() << "AETJTestBGView::setWorkingSet: error, service handle is NULL!";
    }
}

/*************************************************
功能： 创建超声amp图谱控件
*************************************************************/
ChartWidget *AETJTestBGView::createChart(QWidget *parent)
{
    Q_UNUSED(parent);
    ChartWidget *pChart = new ChartWidget;
    QVBoxLayout *pChartLayout = new QVBoxLayout;
    pChartLayout->setSpacing( 0 );
    pChartLayout->setMargin(  0);
    QVBoxLayout *pLabelLayout = new QVBoxLayout;

    m_pTestedCountLabel = new QLabel;
    m_pBayNameLabel = new QLabel;

    QString strLabel = QObject::trUtf8("Tested Count: ") + QString::number(m_uiTestedCnt);
    QFont font = m_pTestedCountLabel->font();
    font.setPointSize(20);
    m_pTestedCountLabel->setFont(font);
    m_pTestedCountLabel->setText(strLabel);
    m_pBayNameLabel->setFont(font);

    pLabelLayout->addWidget(m_pTestedCountLabel,Qt::AlignLeft);
    pLabelLayout->addWidget(m_pBayNameLabel,Qt::AlignLeft);

    m_pLoadFileName = new QLabel;
    m_pLoadFileName->setFont(font);
    pLabelLayout->addWidget(m_pLoadFileName,Qt::AlignLeft );

    AEAmpChart *pAmpChart = new AEAmpChart;
    AEPhaseChart *pPhaseChart = new AEPhaseChart;
    AEFlyChart *pFlyChart = new AEFlyChart;
    AEWave *pWaveChart = new AEWave;

    m_pChartStackWidget = new QStackedWidget;
    m_pChartStackWidget->addWidget(pAmpChart);
    m_pChartStackWidget->addWidget(pPhaseChart);
    m_pChartStackWidget->addWidget(pFlyChart);
    m_pChartStackWidget->addWidget(pWaveChart);

    if(m_eChartType == AEView::AE_AMP)
    {
        m_pChartStackWidget->setCurrentWidget(pAmpChart);
    }
    else if(m_eChartType == AEView::AE_PHASE)
    {
        m_pChartStackWidget->setCurrentWidget(pPhaseChart);
    }
    else if(m_eChartType == AEView::AE_FLY)
    {
        m_pChartStackWidget->setCurrentWidget(pFlyChart);
    }
    else
    {
        m_pChartStackWidget->setCurrentWidget(pWaveChart);
    }

    pChartLayout->addLayout( pLabelLayout );
    pChartLayout->addWidget(m_pChartStackWidget);
    pChart->setLayout(pChartLayout);

    return pChart;
}

/*************************************************
功能： 保存设置
*************************************************************/
bool AETJTestBGView::saveConfig()
{
    int iGroup = AE::GROUP_AE_AMPLITUDE;
    m_pConfig->beginGroup( Module::GROUP_AE );
    m_pConfig->setValue( m_eGain, AE::KEY_GAIN );
    m_pConfig->setValue( m_eFilter, AE::KEY_FILTER );
    m_pConfig->setValue( m_eTriggerValue, AE::KEY_TRIGGER_VALUE, iGroup );
    m_pConfig->setValue( m_usSpectrum, AE::KEY_FREQ_COMPONENT, iGroup );
    m_pConfig->setValue( m_eChannel, AE::KEY_CHANNEL_TYPE );
    m_pConfig->endGroup();


    iGroup = AE::GROUP_AE_FLY;
    m_pConfig->beginGroup( Module::GROUP_AE );
    m_pConfig->setValue( m_usOpenDoorTime, AE::KEY_OPEN_DOOR_TIME, iGroup );
    m_pConfig->setValue( m_usCloseDoorTime, AE::KEY_CLOSE_DOOR_TIME, iGroup );
    m_pConfig->setValue( m_iTimeInterval, AE::KEY_TIME_INTERVAL, iGroup );
    m_pConfig->endGroup();


    iGroup = AE::GROUP_AE_PHASE;
    m_pConfig->beginGroup( Module::GROUP_AE );
    m_pConfig->setValue( m_iPhaseShift, AE::KEY_PHASE_SHIFT, iGroup );
    m_pConfig->endGroup();

    iGroup = AE::GROUP_AE_WAVE;
    m_pConfig->beginGroup( Module::GROUP_AE );
    m_pConfig->setValue( m_eSampleTime, AE::KEY_SAMPLE_TIME, iGroup );
    m_pConfig->setValue( m_eAmpRange, AE::KEY_AMPLITUDE_SCOPE, iGroup );
    m_pConfig->endGroup();

    return true;
}

/*************************************************
功能： 获取数据增益状态
输入参数：
        fValue -- 幅值
*************************************************************/
AE::GainStatus AETJTestBGView::getGainStatus( float fValue )
{
    float fGainLowThreshold = AE::AE_LOWGAIN_THRESHOLDS[ AE::UNIT_DEFAULT ][ m_eGain ];
    float fGainHighThreshold = AE::AE_HIGHGAIN_THRESHOLDS[ AE::UNIT_DEFAULT ][ m_eGain ];

    AE::GainStatus eGainStatus = AE::GAIN_STATUS_NONE;

    if( fValue < fGainLowThreshold)//偏低
    {
        eGainStatus = AE::GAIN_STATUS_LOW;
    }
    else if( fValue > fGainHighThreshold )//偏高
    {
        eGainStatus = AE::GAIN_STATUS_HIGH;
    }
    return eGainStatus;
}


/*************************************************
功能： 更新数据增益状态
输入参数：
        fValue -- 幅值
*************************************************************/
void AETJTestBGView::updateGainStatus( AEAmpChart *pChart, float fValue )
{
    //根据技术部要求，接入终端中不显示增益状态，而是自动切换档位
    //检查增益状态（偏高？偏低？）
    AE::GainStatus eGainStatus = getGainStatus(fValue);
#if 0
    pChart->setGainStatus(eGainStatus);//设置增益状态
#else
    if(AE::GAIN_STATUS_NONE == eGainStatus)
    {
        m_qui32GainChangedCnt = 0;
        m_eGainStatus = eGainStatus;
        pChart->setGainStatus(eGainStatus);//设置正常增益状态
    }
    else
    {
        if(m_eGainStatus == eGainStatus)
        {
            //连续增益异常，才有可能需要切换增益
            ++m_qui32GainChangedCnt;
            pChart->setGainStatus(eGainStatus);
        }
        else
        {
            m_eGainStatus = eGainStatus;
            m_qui32GainChangedCnt = 0;
        }

        if(m_qui32GainChangedCnt >= AE::AE_GAIN_CHANGED_CNT)
        {
            int iCurGainVal = (int)m_eGain;
            if(AE::GAIN_STATUS_HIGH == eGainStatus)
            {
                logInfo("high gain param...");
                --iCurGainVal;
            }
            else if(AE::GAIN_STATUS_LOW == eGainStatus)
            {
                logInfo("low gain param...");
                ++iCurGainVal;
            }
            else
            {
                //
            }

            //数据保护
            m_eGain = (AE::GainType)iCurGainVal;
            m_eGain = m_eGain > AE::GAIN_MAX ? AE::GAIN_MAX : m_eGain;
            m_eGain = m_eGain < AE::GAIN_MIN ? AE::GAIN_MIN : m_eGain;

            resetTriggerList(m_eGain, m_eUnit);//增益变化时，重置触发值列表
            resetAmpRange(m_eGain);
            setAllWorkingSet();
            ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_GAIN)))->setValue( m_eGain );

            if(PROCESS_TYPE_PLAYBACK != m_eListItemProcessType)
            {
                clearChartDatas();
                setChartParameters();
            }
        }
    }
#endif
    return;
}

/*****************************************
 * 功能：更新增益
 * **********************************************/
void AETJTestBGView::updateGainType()
{
    resetTriggerList(m_eGain, m_eUnit);//增益变化时，重置触发值列表
    resetAmpRange(m_eGain);
    setAllWorkingSet();

    if(PROCESS_TYPE_PLAYBACK != m_eListItemProcessType)
    {
        clearChartDatas();
        setChartParameters();
    }
}

/*************************************************
功能： 槽，响应通道变化
输入参数：
        eChannel -- 变化后的通道
*************************************************************/
void AETJTestBGView::onChannelChanged( AE::ChannelType eChannel )
{
    if( m_eChannel != eChannel )
    {
        m_eChannel = eChannel;
        QWidget *pWidget = m_pChartStackWidget->widget(AEView::AE_AMP);
        AEAmpChart *pChart = dynamic_cast<AEAmpChart*> (pWidget);
        pChart->setChannel( eChannel );

        pWidget = m_pChartStackWidget->widget(AEView::AE_PHASE);
        AEPhaseChart *pChart2 = dynamic_cast<AEPhaseChart*> (pWidget);
        pChart2->setChannel( eChannel );

        pWidget = m_pChartStackWidget->widget(AEView::AE_FLY);
        AEFlyChart *pChart3 = dynamic_cast<AEFlyChart*> (pWidget);
        pChart3->setChannel( eChannel );

        pWidget = m_pChartStackWidget->widget(AEView::AE_WAVE);
        AEWave *pChart4 = dynamic_cast<AEWave*> (pWidget);
        pChart4->setChannelName( eChannel );
    }
}

/*************************************************
功能： 设置按钮栏数据
*************************************************************/
void AETJTestBGView::setButtonBarDatas()
{
    //浙江华云和北京融智通接入没有AE飞行图谱
//    SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
//    if(SystemSet::ACCESS_PROTO_ZJHY == eProtocol || SystemSet::ACCESS_PROTO_BJRZT == eProtocol)
//    {
//        if(AEView::AE_WAVE == m_eChartType)
//        {
//            ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_CHART_TYPE)))->setValue( AEView::AE_FLY );
//        }
//        else
//        {
//            ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_CHART_TYPE)))->setValue(m_eChartType);
//        }
//    }
//    else
//    {
//        ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_CHART_TYPE)))->setValue( m_eChartType );
//    }

    ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_GAIN)))->setValue( m_eGain );
    //((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_TRIGGER_VALUE)))->setValue( m_eTriggerValue );

    if(((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_FILTER))))
    {
        ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_FILTER)))->setValue(m_eFilter);
    }

    int iChannelVal = (AE::WIRELESS == m_eChannel) ? (AE::WIRELESS - 1) : AE::AIR_SOUND;
    if(((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_CHANNEL))))
    {
        ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_CHANNEL)))->setValue(iChannelVal);
    }

    if(m_eChartType == AEView::AE_AMP)
    {
        ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_SPECTRUM)))->setValue( m_usSpectrum );
    }
    else if(m_eChartType == AEView::AE_PHASE)
    {
        ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_PHASE_ALIAS)))->setValue( m_iPhaseShift );
        ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_CLOSE_DOOR_TIME)))->setValue( m_usCloseDoorTime );
    }
    else if(m_eChartType == AEView::AE_FLY)
    {
        ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_OPEN_DOOR_TIME)))->setValue( m_usOpenDoorTime );
        ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_CLOSE_DOOR_TIME)))->setValue( m_usCloseDoorTime );
        //((PopupButton*)(buttonBar()->button(aelrbgview::BUTTON_AE_TIME_INTERVAL)))->setValue( m_iTimeInterval );
    }
    else
    {
        //幅值范围列表根据增益动态变化
        resetAmpRange(m_eGain);
        ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_AMPLITUDE_RANGE)))->setValue( m_eAmpRange );
        //((PopupButton*)(buttonBar()->button(aelrbgview::BUTTON_AE_SAMPLE_TIME)))->setValue( m_eSampleTime );
    }
}

/*************************************************
功能： 切换更多界面内容
*************************************************************/
void AETJTestBGView::switchMoreView()
{
    if(m_eChartType == AEView::AE_AMP)
    {
        createMoreConfigButtonBar(AE::CONTEXT, m_pAmpMoreBtnInfo, m_qui8AmpMoreBtnCnt);
        //触发值列表根据增益、量纲动态变化
        resetTriggerList(m_eGain, m_eUnit);
    }
    else if(m_eChartType == AEView::AE_PHASE)
    {
        createMoreConfigButtonBar(AE::CONTEXT, m_pPhaseMoreBtnInfo, m_qui8PhaseMoreBtnCnt);
        resetTriggerList(m_eGain, AE::UNIT_MV);
    }
    else if(m_eChartType == AEView::AE_FLY)
    {
        createMoreConfigButtonBar(AE::CONTEXT, m_pFlyMoreBtnInfo, m_qui8FlyMoreBtnCnt);
        resetTriggerList(m_eGain, AE::UNIT_MV);
    }
    else
    {
        createMoreConfigButtonBar(AE::CONTEXT, m_pWaveMoreBtnInfo,m_qui8WaveMoreBtnCnt);
        //触发值列表根据增益、量纲动态变化
        resetTriggerList(m_eGain, m_eUnit);
    }
}

/*************************************************
功能： 切换要显示的图谱
*************************************************************/
void AETJTestBGView::switchDisplayedChart()
{
    m_pChartStackWidget->setCurrentIndex(m_eChartType);

    switchMoreView();

    setButtonBarDatas();
}


/*************************************************
功能： 相移相位图谱
*************************************************************/
void AETJTestBGView::phaseShiftAEPhaseChart()
{
    QWidget *pWidget = m_pChartStackWidget->widget(AEView::AE_PHASE);
    AEPhaseChart *pChart =  dynamic_cast<AEPhaseChart*> (pWidget);
    if( pChart != NULL )
    {
        pChart->translatePhase( m_iPhaseShift );
    }
}

/*************************************************
功能： 停止采样
*************************************************************/
void AETJTestBGView::stopSample()
{
    stopSampleService();
    setSampleBtnText(isSampling());

    QWidget *pWidget = m_pChartStackWidget->widget(AEView::AE_PHASE);
    AEPhaseChart *pChart1 = dynamic_cast<AEPhaseChart*> (pWidget);
    pChart1->setRunningMode(false);

    pWidget = m_pChartStackWidget->widget(AEView::AE_FLY);
    AEFlyChart *pChart2 = dynamic_cast<AEFlyChart*> (pWidget);
    pChart2->setRunningMode(false);
}

/*************************************************
功能： 启动采样
*************************************************************/
void AETJTestBGView::startSample()
{
    startSampleService();
    enableBtnsWhenStartSample();
    setSampleBtnText(isSampling());
    clearChartDatas();
    setChartParameters();
}

/*************************************************
功能： 清除图谱的数据
*************************************************************/
void AETJTestBGView::clearChartDatas()
{
    m_usFlyPulseCnt = 0;
    m_usAEPhasePulseCnt = 0;
    m_qui32GainChangedCnt = 0;
    m_eGainStatus = AE::GAIN_STATUS_NONE;

    m_vecAmpData.clear();
    m_vecPhaseData.clear();
    m_vecWaveData.clear();
    m_vecFlyData.clear();

    QWidget *pWidget = m_pChartStackWidget->widget(AEView::AE_AMP);
    AEAmpChart *pChart1 = dynamic_cast<AEAmpChart*> (pWidget);
    pChart1->clear();

    pWidget = m_pChartStackWidget->widget(AEView::AE_PHASE);
    AEPhaseChart *pChart2 = dynamic_cast<AEPhaseChart*> (pWidget);
    pChart2->clear();


    pWidget = m_pChartStackWidget->widget(AEView::AE_FLY);
    AEFlyChart *pChart3 = dynamic_cast<AEFlyChart*> (pWidget);
    pChart3->clear();


    pWidget = m_pChartStackWidget->widget(AEView::AE_WAVE);
    AEWave *pChart4 = dynamic_cast<AEWave*> (pWidget);
    pChart4->clear();
}

/*************************************************
功能： 槽，响应读取的数据
输入参数：
        data -- 幅值数据
*************************************************************/
void AETJTestBGView::onDataRead(AE::AmplitudeData data, MultiServiceNS::USERID userId)
{
    if((getUserId() == INVALID_USER) || (getUserId() != userId) || !isSampling())
    {
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
        return;
    }

    AE::AllViewData stAllViewData;
    stAllViewData.s_ampData = data;

    AEMapNS::AEAmpBinaryData stAmpData;
    stAmpData.fPeak = stAllViewData.s_ampData.fPeakValue;
    stAmpData.fRms = stAllViewData.s_ampData.fRMS;
    stAmpData.fFrequency1 = stAllViewData.s_ampData.fFirstFreqComValue;
    stAmpData.fFrequency2 = stAllViewData.s_ampData.fSecondFreqComValue;
    AEBinaryFile::formatAmpData(m_eUnit, m_eGain, stAmpData);

    m_vecAmpData.append(stAmpData);

    for(int i = 0, iSize = stAllViewData.s_vWaveData.size(); i < iSize; ++i)
    {
        QVector< AE::WaveData > vecData = stAllViewData.s_vWaveData;
        AE::WaveData stSampleData = vecData.at(i);
        stSampleData.fWaveValue = stSampleData.fWaveValue;
        if( stSampleData.fWaveValue > AE::AE_AMPLITUDE_SCOPE[m_eGain][m_eAmpRange] )
        {
            stSampleData.fWaveValue = AE::AE_AMPLITUDE_SCOPE[m_eGain][m_eAmpRange];
        }
        m_vecWaveData.append(stSampleData);
    }

    int iWaveNum = waveNumByChannel(m_eChannel);
    if(m_vecWaveData.size() >  iWaveNum*WAVE_MAX_T)
    {
        m_vecWaveData.remove(0, iWaveNum);
    }

    setChartSampleDatas(stAllViewData);

    if(m_vecPhaseData.size() >= AE_PULSE_CNT_MAX || m_vecFlyData.size() >= AE_PULSE_CNT_MAX)
    {
        if(m_vecPhaseData.size() >= AE_PULSE_CNT_MAX)
        {
            m_vecPhaseData = m_vecPhaseData.mid(0, AE_PULSE_CNT_MAX);
        }
        if(m_vecFlyData.size() >= AE_PULSE_CNT_MAX)
        {
            m_vecFlyData = m_vecFlyData.mid(0, AE_PULSE_CNT_MAX);
        }
    }

    if(m_usAEPhasePulseCnt >= AE_PULSE_CNT_MAX && m_usFlyPulseCnt >= AE_PULSE_CNT_MAX)
    {
        stopSample();
    }
}

/*************************************************
功能： 读取到all view data触发的槽函数
*************************************************************/
void AETJTestBGView::onDataRead( AE::AllViewData stData, MultiServiceNS::USERID userId)
{
    if((getUserId() == INVALID_USER) || (getUserId() != userId) || !isSampling())
    {
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
        return;
    }

    AEMapNS::AEAmpBinaryData stAmpData;
    stAmpData.fPeak = stData.s_ampData.fPeakValue;
    stAmpData.fRms = stData.s_ampData.fRMS;
    stAmpData.fFrequency1 = stData.s_ampData.fFirstFreqComValue;
    stAmpData.fFrequency2 = stData.s_ampData.fSecondFreqComValue;
    AEBinaryFile::formatAmpData(m_eUnit, m_eGain, stAmpData);

    m_vecAmpData.append(stAmpData);

    for(int i = 0, iSize = stData.s_vWaveData.size(); i < iSize; ++i)
    {
        QVector< AE::WaveData > vecData = stData.s_vWaveData;
        AE::WaveData stSampleData = vecData.at(i);
        stSampleData.fWaveValue = stSampleData.fWaveValue;
        if( stSampleData.fWaveValue > AE::AE_AMPLITUDE_SCOPE[m_eGain][m_eAmpRange] )
        {
            stSampleData.fWaveValue = AE::AE_AMPLITUDE_SCOPE[m_eGain][m_eAmpRange];
        }
        m_vecWaveData.append(stSampleData);
    }

    int iWaveNum = waveNumByChannel(m_eChannel);
    if(m_vecWaveData.size() >  iWaveNum*WAVE_MAX_T)
    {
        m_vecWaveData.remove(0, iWaveNum);
    }

    setChartSampleDatas(stData);

    if(m_vecPhaseData.size() >= AE_PULSE_CNT_MAX || m_vecFlyData.size() >= AE_PULSE_CNT_MAX)
    {
        if(m_vecPhaseData.size() >= AE_PULSE_CNT_MAX)
        {
            m_vecPhaseData = m_vecPhaseData.mid(0, AE_PULSE_CNT_MAX);
        }
        if(m_vecFlyData.size() >= AE_PULSE_CNT_MAX)
        {
            m_vecFlyData = m_vecFlyData.mid(0, AE_PULSE_CNT_MAX);
        }
    }

    if(m_usAEPhasePulseCnt >= AE_PULSE_CNT_MAX && m_usFlyPulseCnt >= AE_PULSE_CNT_MAX)
    {
        stopSample();
    }

    return;
}

/*************************************************
功能： 设置四类图谱里显示的数据
*************************************************************/
void AETJTestBGView::setChartSampleDatas(const AE::AllViewData &stAllViewData)
{
    //set sample datas for ae amp
    AE::AmplitudeData stData;
    stData.fRMS = stAllViewData.s_ampData.fRMS;
    stData.fPeakValue = stAllViewData.s_ampData.fPeakValue;
    stData.fFirstFreqComValue = stAllViewData.s_ampData.fFirstFreqComValue;
    stData.fSecondFreqComValue = stAllViewData.s_ampData.fSecondFreqComValue;

    AEAmpChart *pChart = dynamic_cast<AEAmpChart*> (m_pChartStackWidget->widget(AEView::AE_AMP));
    if(pChart)
    {
        pChart->setData( stData );
        //updateGainStatus( pChart, stData.fPeakValue );
    }

    //set sample datas for ae phase
    float fPhaseValue = 0;
    float fPeakValue = 0;
    AEPhaseChart *pChart2 = dynamic_cast<AEPhaseChart*> (m_pChartStackWidget->widget(AEView::AE_PHASE));
    if(pChart2)
    {
        for(int i = 0, iSize = stAllViewData.s_vPhaseData.size(); i < iSize; ++i)
        {
            if(m_vecPhaseData.size() >= AE_PULSE_CNT_MAX)
            {
                break;
            }

            fPhaseValue = stAllViewData.s_vPhaseData.at(i).fPhaseValue;
            fPeakValue = stAllViewData.s_vPhaseData.at(i).fPeakValue;
            if(fPeakValue >= m_iTriggerValue)
            {
                if(pChart2->addSamples(fPhaseValue, fPeakValue))
                {
                    m_vecPhaseData.append(stAllViewData.s_vPhaseData.at(i));
                    m_usAEPhasePulseCnt = m_vecPhaseData.size();
                    pChart2->setPulseCounting(m_usAEPhasePulseCnt);
                }
            }
        }
    }

    //set sample datas for ae fly
    UINT32 uiPulseInterval = 0;
    fPeakValue = 0;
    AEFlyChart *pChart3 = dynamic_cast<AEFlyChart*> (m_pChartStackWidget->widget(AEView::AE_FLY));
    if(pChart3)
    {
        for(int i = 0, iSize = stAllViewData.s_vFlyData.size(); i < iSize; ++i)
        {
            if(m_vecFlyData.size() >= AE_PULSE_CNT_MAX)
            {
                break;
            }

            uiPulseInterval = stAllViewData.s_vFlyData.at(i).uiPulseInterval;
            fPeakValue = stAllViewData.s_vFlyData.at(i).fPeakValue;
            if(fPeakValue >= m_iTriggerValue)
            {
                if(pChart3->addSamples(uiPulseInterval, fPeakValue))
                {
                    m_vecFlyData.append(stAllViewData.s_vFlyData.at(i));
                    m_usFlyPulseCnt = m_vecFlyData.size();
                    pChart3->setPulseCounting(m_usFlyPulseCnt);
                }
            }
        }
    }

    //set sample datas for ae wave
    AEWave *pChart4 = dynamic_cast<AEWave*> (m_pChartStackWidget->widget(AEView::AE_WAVE));
    if(pChart4)
    {
        pChart4->setSamples(stAllViewData.s_vWaveData);
    }

    // 得在设置所有图谱数据只会更新，不然会导致切换增益清空图谱，其他图谱还是添加的原有增益的数据
    // 计算自动增益
    AEDealInfo stAEDealInfo;
    stAEDealInfo.fPeakValue = stData.fPeakValue;
    stAEDealInfo.eGain = m_eGain;
    stAEDealInfo.eUnit = m_eUnit;
    DealData::autoGainAEData(stAEDealInfo);
    if (stAEDealInfo.eGain != m_eGain)
    {
        ++m_qui32GainChangedCnt;
        if (m_qui32GainChangedCnt >= AE::AE_GAIN_ANTI_SHAKE_TIMES)
        {
            m_qui32GainChangedCnt = 0;
            m_eGain = stAEDealInfo.eGain;
            updateGainType();
            ((PopupButton*)(buttonBar()->button(aetestbgview::BUTTON_AE_GAIN)))->setValue(m_eGain);
        }
    }
    else
    {
        m_qui32GainChangedCnt = 0;
    }

    return;
}

/*************************************************
功能： 通过通道类型获取波形数据的个数上限
返回值：波形数据的个数上限
*************************************************************/
int AETJTestBGView::waveNumByChannel( AE::ChannelType eChannel )
{
    int iNum = 1;
    switch( eChannel )
    {
    case AE::AIR_SOUND:
    case AE::SURFACE_MOUNT:
    {
        iNum = WAVE_NUM_PER_T;
    }
        break;
    case AE::WIRELESS:
    {
        iNum = WAVE_NUM_PER_5T_WIRELESS;
    }
        break;
    default:
        break;
    }
    return iNum;
}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void AETJTestBGView::onButtonValueChanged( int id, int iValue )
{
    if( id != aetestbgview::BUTTON_CHART_TYPE )
    {
        stopSample();
    }

    switch( id )
    {
    case aetestbgview::BUTTON_CHART_TYPE://类型
    {
        if( m_eChartType != (AEView::Function)iValue )
        {
            m_eChartType = (AEView::Function)iValue;
            //浙江华云和北京融智通接入没有AE飞行图谱
            SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
            if(SystemSet::ACCESS_PROTO_ZJHY == eProtocol || SystemSet::ACCESS_PROTO_BJRZT == eProtocol)
            {
                if(AEView::AE_FLY == m_eChartType)
                {
                    m_eChartType = AEView::AE_WAVE;
                }
            }

            switchDisplayedChart();
        }
    }
        break;
    case aetestbgview::BUTTON_AE_GAIN://增益
    {
        if( m_eGain != (AE::GainType)iValue )
        {
            m_eGain = (AE::GainType)iValue;
            updateGainType();
        }
    }
        break;
    case aetestbgview::BUTTON_AE_TRIGGER_VALUE://触发值
    {
        if( m_eTriggerValue != (AE::TriggerValue)iValue )
        {
            m_eTriggerValue = (AE::TriggerValue)iValue;
            if( PROCESS_TYPE_PLAYBACK != m_eListItemProcessType )
            {
                clearChartDatas();
                setChartParameters();
            }
            setAllWorkingSet();
        }
    }
        break;
    case aetestbgview::BUTTON_AE_CHANNEL://通道
    {
        m_eChannel = (iValue == 1) ? AE::WIRELESS : AE::AIR_SOUND;
        if( PROCESS_TYPE_PLAYBACK != m_eListItemProcessType )
        {
            clearChartDatas();
            setChartParameters();
        }
        setAllWorkingSet();
    }
        break;
    case aetestbgview::BUTTON_AE_FILTER://带宽
    {
        if( m_eFilter != (AEFilter)iValue )
        {
            m_eFilter = (AEFilter)iValue;
            if( PROCESS_TYPE_PLAYBACK != m_eListItemProcessType )
            {
                clearChartDatas();
                setChartParameters();
            }
            setAllWorkingSet();
        }

    }
        break;
    case aetestbgview::BUTTON_AE_SPECTRUM://频率成分
    {
        if( m_usSpectrum != iValue )
        {
            m_usSpectrum = iValue;
            if( PROCESS_TYPE_PLAYBACK != m_eListItemProcessType )
            {
                clearChartDatas();
                setChartParameters();
            }
            setAllWorkingSet();
        }
    }
        break;
    case aetestbgview::BUTTON_AE_PHASE_ALIAS://相位偏移
    {
        if( m_iPhaseShift != iValue )
        {
            m_iPhaseShift = iValue;
            if( PROCESS_TYPE_PLAYBACK != m_eListItemProcessType )
            {
                if(m_eChartType == AEView::AE_PHASE)
                {
                    phaseShiftAEPhaseChart();
                }
            }
        }

    }
        break;
    case aetestbgview::BUTTON_AE_CLOSE_DOOR_TIME://关门时间
    {
        if( m_usCloseDoorTime != iValue )
        {
            m_usCloseDoorTime = iValue;
            if( PROCESS_TYPE_PLAYBACK != m_eListItemProcessType )
            {
                clearChartDatas();
                setChartParameters();
            }
            setAllWorkingSet();
        }
    }
        break;
    case aetestbgview::BUTTON_AE_OPEN_DOOR_TIME://开门时间
    {
        if( m_usOpenDoorTime != iValue )
        {
            m_usOpenDoorTime = iValue;
            if( PROCESS_TYPE_PLAYBACK != m_eListItemProcessType )
            {
                clearChartDatas();
                setChartParameters();
            }
            setAllWorkingSet();
        }
    }
        break;
    case aetestbgview::BUTTON_AE_TIME_INTERVAL://时间间隔
    {
        if( m_iTimeInterval != iValue )
        {
            m_iTimeInterval = iValue;
            if( PROCESS_TYPE_PLAYBACK != m_eListItemProcessType )
            {
                clearChartDatas();
                setChartParameters();
            }
            setAllWorkingSet();
        }
    }
        break;
    case aetestbgview::BUTTON_AE_AMPLITUDE_RANGE://幅值范围
    {
        if( m_eAmpRange != (AE::AmpRange)iValue )
        {
            m_eAmpRange = (AE::AmpRange)iValue;
            if( PROCESS_TYPE_PLAYBACK != m_eListItemProcessType )
            {
                clearChartDatas();
                setChartParameters();
            }
        }
    }
        break;
    case aetestbgview::BUTTON_AE_SAMPLE_TIME://采样时间
    {
        if( m_eSampleTime != (AE::SampleTime)iValue )
        {
            m_eSampleTime = (AE::SampleTime)iValue;
            if( PROCESS_TYPE_PLAYBACK != m_eListItemProcessType )
            {
                clearChartDatas();
                setChartParameters();
            }
            setAllWorkingSet();
        }
    }
        break;
    default:
        break;
    }
}

/*************************************************
功能： 设置采样按钮的文本
*************************************************************/
void AETJTestBGView::setSampleBtnText(bool isSampling)
{
    if(isSampling == false)
    {
        m_pSampleBtn->setTitle(AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_START));
    }
    else
    {
        m_pSampleBtn->setTitle(AE_VIEW_CONFIG_TRANSLATE(AE::TEXT_STOP));
    }
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void AETJTestBGView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case aetestbgview::BUTTON_START_SAMPLE://开始采样
    {
        if(!isSampling())
        {
            startSample();
        }
        else
        {
            stopSample();
        }

        //更新当前处理状态，即如果当前是回放数据，再次点击采样，更新状态
        if( PROCESS_TYPE_PLAYBACK == m_eListItemProcessType )
        {
            setChartParameters();
            m_eListItemProcessType = PROCESS_TYPE_INVALID;
        }

        m_pLoadFileName->clear();
    }
        break;

    case aetestbgview::BUTTON_SAVE://保存
    {
        stopSample();
        updateTestedCntLabel();
        saveConfig();
        saveAsBinaryDataFile();
    }
        break;
    case aetestbgview::BUTTON_MORE://更多设置
    {
        showMoreConfigButtonBar();
    }
        break;

        //        case aelrbgview::BUTTON_AE_DELETE_DATA://删除数据
        //        {
        //            deleteBinaryDataFile();
        //        }
        //            break;
    case aetestbgview::BUTTON_HISTORY_DATA://载入数据
    {
        loadData();
    }
        break;
    default:
        break;
    }
}

/*************************************************
功能： 组织二进制里的幅值ext info
*************************************************************/
void AETJTestBGView::composeAmpInfo(DataSpecificationNS::AEAmpExtInformation& stAEAmpExtInformation)
{
    if( m_eUnit > AE::UNIT_MAX || m_eUnit < AE::UNIT_MIN )
    {
        m_eUnit = AE::UNIT_MIN;
    }
    if( m_eGain > AE::GAIN_MAX || m_eGain < AE::GAIN_MIN )
    {
        m_eGain = AE::GAIN_MIN;
    }
    stAEAmpExtInformation.eAmpUnit = CustomAccessView::CustomAccessUIFunc::aeUnitOption2AmpUnit(m_eUnit);

    stAEAmpExtInformation.fAmpLowerLimit = AE::AMPLITUDE_MIN_VALUES[m_eUnit][m_eGain][AE::COMPONENT_MAX];
    stAEAmpExtInformation.fAmpUpperLimit = AE::AMPLITUDE_MAX_VALUES[m_eUnit][m_eGain][AE::COMPONENT_MAX];//AE::AE_PROPERTY_AMP_RANGE_MAX;

    stAEAmpExtInformation.eAESensorType = AE::channelType2AESensorType(m_eChannel);
    DataSpecificationNS::DataSpecificationVersion eDataSpecificationVersion = SystemSetService::instance()->getDataSpecificationVersion();
    if (DataSpecificationNS::V_4_1_0_2 == eDataSpecificationVersion)
    {
        // 公司规范8个
        stAEAmpExtInformation.iDataPoint = gs_AEAmpDataPoint;
    }
    else if (DataSpecificationNS::V_1_1_0_0 == eDataSpecificationVersion)
    {
        stAEAmpExtInformation.iDataPoint = AE_PROPERTY_DATA_POINT_CNT;
    }
    stAEAmpExtInformation.fSystemFrequency = m_usSpectrum;
    memset(stAEAmpExtInformation.aucPDTypeProbability, 0, sizeof(stAEAmpExtInformation.aucPDTypeProbability));

    if (DataSpecificationNS::V_4_1_0_2 == eDataSpecificationVersion)
    {
        // 公司规范
        stAEAmpExtInformation.fTriggerThreshold = AE::getThreshold(m_eUnit, m_eGain, m_eTriggerValue);
        stAEAmpExtInformation.sOpenTime = m_usOpenDoorTime;
        stAEAmpExtInformation.sShutTime = m_usCloseDoorTime;
        stAEAmpExtInformation.sMaxIntervalTime = AE::SAMPLE_TIME[m_eSampleTime];
        stAEAmpExtInformation.sGain = AE::g_ausGainValues[m_eGain];
        stAEAmpExtInformation.eSyncSource = DataSpecificationNS::SYNC_SOURCE_DEFAULT;
        stAEAmpExtInformation.ucSyncState = m_eSyncState;
        stAEAmpExtInformation.fSyncFrequency = -1;
    }
    else if (DataSpecificationNS::V_1_1_0_0 == eDataSpecificationVersion)
    {
        // 山东规范
        stAEAmpExtInformation.iDataPoint = AE_PROPERTY_DATA_POINT_CNT;

        stAEAmpExtInformation.eFrequencyBand = DataSpecificationNS::BAND_DEFAULT;
        stAEAmpExtInformation.fFrequencyLowerLimit = 0;//todo
        stAEAmpExtInformation.fFequencyUpperLimit = 0;//todo

        QStringList bgFileList = m_pSubTask->bgFileList( m_strGapId );
        QString strLastBGFileName = "";
        if( !bgFileList.isEmpty() )
        {
            strLastBGFileName = bgFileList.last();
        }
        stAEAmpExtInformation.qstrBGFileName = "";
    }
}

/*************************************************
功能： 组织二进制里的幅值data
*************************************************************/
void AETJTestBGView::composeAmpData(DataSpecificationNS::AEAmpData& stAEAmpData)
{
    /*
    int iDataPeriodCnt = m_vecAmpData.size();
    dbg_info("iDataPeriodCnt is %d\n", iDataPeriodCnt);
    for(int i = 0; i < iDataPeriodCnt; i ++)
    {
        AEMapNS::AEAmpBinaryData stOrigData;
        stOrigData = m_vecAmpData.at(i);
        stData.fRms +=  stOrigData.fRms;
        stData.fPeak +=  stOrigData.fPeak;
        stData.fFrequency1 +=  stOrigData.fFrequency1;
        stData.fFrequency2 +=  stOrigData.fFrequency2;
    }

    stData.fRms  = stData.fRms / (float)iDataPeriodCnt;
    stData.fPeak  = stData.fPeak / (float)iDataPeriodCnt;
    stData.fFrequency1  = stData.fFrequency1 / (float)iDataPeriodCnt;
    stData.fFrequency2  = stData.fFrequency2 / (float)iDataPeriodCnt;
    */
    if(!m_vecAmpData.isEmpty())
    {
        stAEAmpData.fSignalMax = m_vecAmpData.last().fPeak;
        stAEAmpData.fSignalRMS = m_vecAmpData.last().fRms;
        stAEAmpData.fFrequencyComponent1 = m_vecAmpData.last().fFrequency1;
        stAEAmpData.fFrequencyComponent2 = m_vecAmpData.last().fFrequency2;
    }
}

/*************************************************
功能： 组织二进制里的phase ext info
*************************************************************/
void AETJTestBGView::composePhaseInfo(DataSpecificationNS::AEPhaseExtInformation& stAEPhaseExtInformation)
{
    if (AE::UNIT_MV == m_eUnit)
    {
        stAEPhaseExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_mV;
    }
    else if (AE::UNIT_DB == m_eUnit)
    {
        stAEPhaseExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_DB;
    }

    stAEPhaseExtInformation.fAmpLowerLimit = 0;
    if( (m_eGain >= 0) && (m_eGain < 3) )
    {
        stAEPhaseExtInformation.fAmpUpperLimit = AE::Y_RANGE_VALUES[m_eGain];
    }
    else
    {
        qDebug() << "AETJTestBGView::composePhaseInfo, m_eGain : " << m_eGain;
    }
    stAEPhaseExtInformation.sGain = AE::g_ausGainValues[m_eGain];
    stAEPhaseExtInformation.fTriggerThreshold = AE::getThreshold(m_eUnit, m_eGain, m_eTriggerValue);
    stAEPhaseExtInformation.sShutTime = m_usCloseDoorTime;
    stAEPhaseExtInformation.eSyncSource = DataSpecificationNS::SYNC_SOURCE_DEFAULT;
    stAEPhaseExtInformation.ucSyncState = m_eSyncState;
    stAEPhaseExtInformation.fSyncFrequency = -1;
    stAEPhaseExtInformation.eAESensorType = static_cast<DataSpecificationNS::AESensorType>(AE::transformChannelToDataFile(m_eChannel));
    stAEPhaseExtInformation.iDataPoint = m_vecPhaseData.size();
    memset(stAEPhaseExtInformation.aucPDTypeProbability, 0, sizeof(stAEPhaseExtInformation.aucPDTypeProbability));

    //    mapInfo.s_testPointInfo.s_strDeviceName = m_pSubTask->gapInfo().s_strName;
    //    mapInfo.s_testPointInfo.s_strDeviceID = m_pSubTask->gapInfo().s_strId;
}

/*************************************************
功能： 组织二进制里的fly ext info
*************************************************************/
void AETJTestBGView::composeFlyInfo(DataSpecificationNS::AEPulseExtInformation& stAEPulseExtInformation)
{
    if (AE::UNIT_MV == m_eUnit)
    {
        stAEPulseExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_mV;
    }
    else if (AE::UNIT_DB == m_eUnit)
    {
        stAEPulseExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_DB;
    }

    stAEPulseExtInformation.fAmpLowerLimit = 0;
    if( (m_eGain >= 0) && (m_eGain < 3) )
    {
        stAEPulseExtInformation.fAmpUpperLimit = AE::Y_RANGE_VALUES[m_eGain];
    }
    else
    {
        qDebug() << "AETJTestBGView::composeFlyInfo, m_eGain : " << m_eGain;
    }
    stAEPulseExtInformation.sGain = AE::g_ausGainValues[m_eGain];
    stAEPulseExtInformation.sOpenTime = m_usOpenDoorTime;
    stAEPulseExtInformation.sShutTime = m_usCloseDoorTime;
    stAEPulseExtInformation.sMaxIntervalTime = AE::SAMPLE_TIME[m_eSampleTime];
    stAEPulseExtInformation.eSyncSource = DataSpecificationNS::SYNC_SOURCE_DEFAULT;
    stAEPulseExtInformation.ucSyncState = m_eSyncState;
    stAEPulseExtInformation.fSyncFrequency = -1;
    stAEPulseExtInformation.eAESensorType = static_cast<DataSpecificationNS::AESensorType>(AE::transformChannelToDataFile(m_eChannel));
    stAEPulseExtInformation.ePulseIntervalUnit = DataSpecificationNS::PULSE_MIL_SECOND;
    stAEPulseExtInformation.iDataPoint = m_vecFlyData.size();
    memset(stAEPulseExtInformation.aucPDTypeProbability, 0, sizeof(stAEPulseExtInformation.aucPDTypeProbability));

    //    mapInfo.s_testPointInfo.s_strDeviceName = m_pSubTask->gapInfo().s_strName;
    //    mapInfo.s_testPointInfo.s_strDeviceID = m_pSubTask->gapInfo().s_strId;
}

/*************************************************
功能： 组织二进制里的wave ext info
*************************************************************/
void AETJTestBGView::composeWaveInfo(DataSpecificationNS::AEWaveExtInformation& stAEWaveExtInformation)
{
    if (AE::UNIT_MV == m_eUnit)
    {
        stAEWaveExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_mV;
    }
    else if (AE::UNIT_DB == m_eUnit)
    {
        stAEWaveExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_DB;
    }

    stAEWaveExtInformation.fAmpLowerLimit = 0;
    stAEWaveExtInformation.fAmpUpperLimit = AE::AE_AMPLITUDE_SCOPE[m_eGain][m_eAmpRange];
    stAEWaveExtInformation.eAESensorType = static_cast<DataSpecificationNS::AESensorType>(AE::transformChannelToDataFile(m_eChannel));
    /*mapInfo.fTriggerThreshold = AE::getThreshold(m_eUnit, m_eGain, m_eTriggerValue);
    mapInfo.sGain = AE::g_ausGainValues[m_eGain];
    mapInfo.sOpenTime = m_usOpenDoorTime;
    mapInfo.sShutTime = m_usCloseDoorTime;
    mapInfo.sMaxTimeInterval = AE::SAMPLE_TIME[m_eSampleTime];
    mapInfo.eSyncSource = DataFileNS::SYNC_SOURCE_DEFAULT;
    mapInfo.ucSyncState = m_eSyncState;
    mapInfo.fSyncFreq = -1;*/
    int iWaveNum = waveNumByChannel( m_eChannel );
    stAEWaveExtInformation.iDataPoint = (m_vecWaveData.size() > iWaveNum * WAVE_MAX_T) ? (iWaveNum * WAVE_MAX_T) : m_vecWaveData.size();
    // 采样率 无线 -- 2.5k 内置 -- 40k
    if( AE::WIRELESS == m_eChannel  )
    {
        stAEWaveExtInformation.llSampleRate = 2500;
    }
    else
    {
        stAEWaveExtInformation.llSampleRate = 40960;
    }
    memset(stAEWaveExtInformation.aucPDTypeProbability, 0, sizeof(stAEWaveExtInformation.aucPDTypeProbability));

    //    mapInfo.s_testPointInfo.s_strDeviceName = m_pSubTask->gapInfo().s_strName;
    //    mapInfo.s_testPointInfo.s_strDeviceID = m_pSubTask->gapInfo().s_strId;
}

/*************************************************
功能： 更新已测试次数label
*************************************************************/
void AETJTestBGView::updateTestedCntLabel()
{
    m_uiTestedCnt ++;
    QString strLabel = QObject::trUtf8("Tested Count: ") + QString::number(m_uiTestedCnt);
    m_pTestedCountLabel->setText(strLabel);
}

/*************************************************
功能： 限制超声飞行数据幅值在量程范围内
*************************************************************/
void AETJTestBGView::limitFlyDataInRange()
{
    for(int i = 0; i < m_vecFlyData.size(); i ++)
    {
        if(m_vecFlyData.at(i).fPeakValue >= AE::Y_RANGE_VALUES[m_eGain])
        {
            m_vecFlyData[i].fPeakValue = AE::Y_RANGE_VALUES[m_eGain];
        }
    }
}

/*************************************************
功能： 保存二进制数据文件
*************************************************************/
void AETJTestBGView::saveAsBinaryDataFile()
{
    //save as binary file
    AEBinaryFile *pBinaryFile = new AEBinaryFile;
    pBinaryFile->saveFileHead();

    //save amp
    DataSpecificationNS::AEAmpExtInformation stAEAmpExtInformation;
    composeAmpInfo(stAEAmpExtInformation);
    DataSpecificationNS::AEAmpData stAEAmpData;
    composeAmpData(stAEAmpData);

    CustomAccessTaskNS::GapInfo currentGap = m_pSubTask->gapInfo( m_strGapId );
    CustomAccessTaskNS::TestPointInfo nextTestPoint;
    for( int i = 0; i < currentGap.s_vTestPoints.size(); i++ )
    {
        if( currentGap.s_vTestPoints.at(i).s_vData.isEmpty() )
        {
            nextTestPoint = currentGap.s_vTestPoints.at(i);
            break;
        }
    }
    TestPointInfo testPointInfo;
    testPointInfo.s_strDeviceName = currentGap.s_strName;
    testPointInfo.s_strDeviceID = currentGap.s_strId;
    testPointInfo.s_strTestPointName = nextTestPoint.s_strName;
    testPointInfo.s_strTestPointID = nextTestPoint.s_strId;

    pBinaryFile->addAEAmpMap(testPointInfo, stAEAmpExtInformation, stAEAmpData, true);

//    //save phase
//    DataSpecificationNS::AEPhaseExtInformation stAEPhaseExtInformation;
//    composePhaseInfo(stAEPhaseExtInformation);
//    pBinaryFile->addAEPhaseMap(testPointInfo, stAEPhaseExtInformation, m_vecPhaseData, true);

//    //浙江华云和北京融智通接入没有AE飞行图谱
//    SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
//    if(SystemSet::ACCESS_PROTO_SDLR == eProtocol)
//    {
//        //save fly
//        DataSpecificationNS::AEPulseExtInformation stAEPulseExtInformation;
//        composeFlyInfo(stAEPulseExtInformation);
//        limitFlyDataInRange();
//        pBinaryFile->addAEFlyMap(testPointInfo, stAEPulseExtInformation, m_vecFlyData, true);
//    }

//    //save wave
//    DataSpecificationNS::AEWaveExtInformation stAEWaveExtInformation;
//    composeWaveInfo(stAEWaveExtInformation);

//    int iWaveNum = waveNumByChannel( m_eChannel );
//    if( m_vecWaveData.size() > iWaveNum * WAVE_MAX_T )
//    {
//        m_vecWaveData = m_vecWaveData.mid( m_vecWaveData.size() - iWaveNum * WAVE_MAX_T );
//    }
//    pBinaryFile->addAEWaveMap(testPointInfo, stAEWaveExtInformation, m_vecWaveData, true);

    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
    centerPoint.setY(pBtnBar->pos().y()/*+pBtnBar->height()/2*/);

    //save as binary file
    if(pBinaryFile->saveAsBinary(m_pSubTask->dataSavePath(), m_strBinaryFilePath))
    {
        if (!m_strBinaryFilePath.isEmpty())
        {
            int iPos = m_strBinaryFilePath.lastIndexOf('.');
            QString qstrJpegFileFullName = m_strBinaryFilePath.left(iPos) + ".jpg";

            QPixmap pix = QPixmap::grabWidget(m_pChartStackWidget, QRect(0, 0, m_pChartStackWidget->width(), m_pChartStackWidget->height()));
            pix.save(qstrJpegFileFullName, "JPG");
        }

        m_pSubTask->addBGFile(m_strGapId, m_strBinaryFilePath, stAEAmpData.fSignalMax);
        QFileInfo fileInfo(m_strBinaryFilePath);
        if( m_pSubTask->isAutoSwitch() )
        {
            if( MsgBox::OK == MsgBox::delayQuestion("", QObject::trUtf8("Save success, auto switching."), centerPoint) )
            {
                m_pSubTask->ensureAutoSwitch( true );
                close();
            }
            else
            {
                m_pSubTask->ensureAutoSwitch( false );
                //cancel do nothing
            }
        }
        else
        {
            QString strText = fileInfo.fileName();
            processTooLongMsgText(strText);
            MsgBox::information("", strText, centerPoint);
        }

    }
    else
    {
        MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("Save failure!"), centerPoint);
    }

    //qDebug()<<"m_strBinaryFilePath:"<<m_strBinaryFilePath;
    delete pBinaryFile;
}

/*************************************************
功能： 回放历史数据
*************************************************************/
void AETJTestBGView::loadData()
{
    m_bgFileList.clear();
    m_bgFileList = m_pSubTask->bgFileList(m_strGapId);

    if(m_bgFileList.size() <= 0)
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
        return;
    }

    QList<QString> lItems;
    QString strFileName = "";
    for(quint16 i = 0, iSize = m_bgFileList.size(); i < iSize; ++i)
    {
        strFileName = m_bgFileList.at(i);
        lItems.append(CustomAccessTaskNS::testFileNameFromFile(strFileName));
    }

    if(NULL == m_pFilesItemListView)
    {
        m_pFilesItemListView = new CommonItemListView(QObject::trUtf8("Test Data List"), lItems, NULL);
        connect(m_pFilesItemListView, SIGNAL(sigItemSelected(qint32)), this, SLOT(onProcessSeletedBinaryFile(qint32)));
    }

    m_eListItemProcessType = PROCESS_TYPE_PLAYBACK;
    m_pFilesItemListView->clear();
    m_pFilesItemListView->addItems(lItems);
    m_pFilesItemListView->show();

    return;
}

/*************************************************
功能： 回放历史数据时disable相关按钮
*************************************************************/
void AETJTestBGView::disableBtnsWhenPlayback()
{
    m_pSaveDataBtn->setEnabled(false);
    //m_pMoreBtn->setEnabled(false);
}

/*************************************************
功能： 启动采样时enable被disable的相关按钮
*************************************************************/
void AETJTestBGView::enableBtnsWhenStartSample()
{
    m_pSaveDataBtn->setEnabled(true);
    //m_pMoreBtn->setEnabled(true);
}

/*************************************************
功能： 处理数据列表里选中的数据文件
*************************************************************/
void AETJTestBGView::onProcessSeletedBinaryFile(qint32 iIndex)
{
    dbg_info("iIndex is %d\n", iIndex);

    QString stData = m_bgFileList.at(iIndex);
    QString strBinaryFilePath = m_pSubTask->dataSavePath() + "/" + stData;
    if(m_eListItemProcessType == PROCESS_TYPE_PLAYBACK)
    {
        disableBtnsWhenPlayback();
        //停止采样
        stopSample();
        playbackBinaryFile(strBinaryFilePath);
        hideMoreConfigButtonBar();
    }
    else if(m_eListItemProcessType == PROCESS_TYPE_DELETE_DATA)
    {
        m_bgFileList.removeAt(iIndex);
        setTestedCnt(m_bgFileList.size());
        deleteSeletedBinaryFile(strBinaryFilePath);
        m_eListItemProcessType = PROCESS_TYPE_INVALID;
    }
    else
    {

    }

    return;
}

/*************************************************
功能： 回放幅值数据
*************************************************************/
void AETJTestBGView::playBackAmpData(DataSpecificationNS::AEAmpSpectrum* pAEAmpSpectrum)
{
    AEAmpChart *pChart = dynamic_cast<AEAmpChart*> (m_pChartStackWidget->widget(AEView::AE_AMP));
    pChart->clear();

    /*
    int iPeakMax = (int)stInfo.fPeakMax;
    qDebug() << "~~~~~~~~~~~~~~~~~~~~~~ iPeakMax : " << iPeakMax;
    switch( iPeakMax )
    {
    case 1000: //x1
    {
        pChart->setGain( AE::GAIN_X1 );
        pChart->setUnit( AE::UNIT_MV );
        pChart->setSpectrum( stInfo.fTestFreq );
    }
        break;
    case 200: //x10
    {
        pChart->setGain( AE::GAIN_X10 );
        pChart->setUnit( AE::UNIT_MV );
        pChart->setSpectrum( stInfo.fTestFreq );
    }
        break;
    case 20: //x100
    {
        pChart->setGain( AE::GAIN_X100 );
        pChart->setUnit( AE::UNIT_MV );
        pChart->setSpectrum( stInfo.fTestFreq );
    }
        break;
    default:
    {
        //set chart parameters
        pChart->setUnit( AE::UNIT_DEFAULT );
        pChart->setGain( AE::GAIN_INVALID );
        pChart->setChannel( AE::CHANNEL_INVALID );
        pChart->setSpectrum( stInfo.fTestFreq );
        pChart->setScale(stInfo.fPeakMin, stInfo.fPeakMax);
    }
        break;
    }
    */
    DataSpecificationNS::AEAmpExtInformation stAEAmpExtInformation;
    pAEAmpSpectrum->getAEAmpExtInformation(stAEAmpExtInformation);

    DataSpecificationNS::DataSpecificationVersion eDataSpecificationVersion = SystemSetService::instance()->getDataSpecificationVersion();
    AE::GainType eGain = AE::GAIN_INVALID;
    if (DataSpecificationNS::V_4_1_0_2 == eDataSpecificationVersion)
    {
        eGain = AE::gainVal2Type(stAEAmpExtInformation.sGain);
    }
    AE::UnitOption eUnitOption = CustomAccessView::CustomAccessUIFunc::ampUnit2AEUnitOption(stAEAmpExtInformation.eAmpUnit);
    //set chart parameters
    pChart->setUnit(eUnitOption);
    pChart->setGain(eGain);
    pChart->setChannel(AE::CHANNEL_INVALID);
    pChart->setSpectrum(stAEAmpExtInformation.fSystemFrequency);
    if (DataSpecificationNS::V_1_1_0_0 == eDataSpecificationVersion)
    {
        pChart->setScale(stAEAmpExtInformation.fAmpLowerLimit, stAEAmpExtInformation.fAmpUpperLimit);
    }

    DataSpecificationNS::AEAmpData stAEAmpData;
    pAEAmpSpectrum->getAEAmpData(stAEAmpData);
    //set chart data
    AE::AmplitudeData stChartData;
    stChartData.fPeakValue = stAEAmpData.fSignalMax;
    stChartData.fRMS = stAEAmpData.fSignalRMS;
    stChartData.fFirstFreqComValue = stAEAmpData.fFrequencyComponent1;
    stChartData.fSecondFreqComValue = stAEAmpData.fFrequencyComponent2;

    pChart->setData( stChartData );
}

/*************************************************
功能： 回放相位数据
*************************************************************/
void AETJTestBGView::playBackPhaseData(DataSpecificationNS::AEPhaseSpectrum* pAEPhaseSpectrum)
{
    AEPhaseChart *pChart = dynamic_cast<AEPhaseChart*> (m_pChartStackWidget->widget(AEView::AE_PHASE));
    pChart->clear();

    DataSpecificationNS::AEPhaseExtInformation stAEPhaseExtInformation;
    pAEPhaseSpectrum->getAEPhaseExtInformation(stAEPhaseExtInformation);

    pChart->setGain(AE::GAIN_INVALID);
    pChart->setYMax(stAEPhaseExtInformation.fAmpUpperLimit);
    pChart->setChannelValid(false);
    pChart->setSyncSource( Module::INVALID_SYNC_SOURCE );
    pChart->setPulseCounting(stAEPhaseExtInformation.iDataPoint);

    DataSpecificationNS::AEPhaseData stAEPhaseData;
    pAEPhaseSpectrum->getAEPhaseData(stAEPhaseData);

    const char* pData = stAEPhaseData.qbaAEPhaseData.data();

    for (quint32 i = 0; i < stAEPhaseExtInformation.iDataPoint; ++i)
    {
        float fPhaseValue;
        float fPeakValue;
        memcpy(&fPhaseValue, pData, sizeof(float));
        pData += sizeof(float);

        memcpy(&fPeakValue, pData, sizeof(float));
        pData += sizeof(float);

        pChart->addSamples( fPhaseValue,fPeakValue );
    }
}


/*************************************************
功能： 回放飞行数据
*************************************************************/
void AETJTestBGView::playBackFlyData(DataSpecificationNS::AEPulseSpectrum* pAEPulseSpectrum)
{
    AEFlyChart *pChart = dynamic_cast<AEFlyChart*> (m_pChartStackWidget->widget(AEView::AE_FLY));
    pChart->clear();

    /*
    float fTimeIntervalMax = 0;
    for(int i = 0; i < stInfo.iDataPointNum; i ++)
    {
        float fPulseInterval;
        fPulseInterval = pfData[2*i];
        if(fTimeIntervalMax < fPulseInterval)
        {
            fTimeIntervalMax = fPulseInterval;
        }
    }
    */

    DataSpecificationNS::AEPulseExtInformation stAEPulseExtInformation;
    pAEPulseSpectrum->getAEPulseExtInformation(stAEPulseExtInformation);

    pChart->setTimeInterval( MAX_FLY_INTVL );
    pChart->setGain(AE::GAIN_INVALID);
    pChart->setYMax(stAEPulseExtInformation.fAmpUpperLimit);
    pChart->setChannelValid(false);
    pChart->setPulseCounting(stAEPulseExtInformation.iDataPoint);

    DataSpecificationNS::AEPulseData stAEPulseData;
    pAEPulseSpectrum->getAEPulseData(stAEPulseData);

    const char* pData = stAEPulseData.qbaAEPulseData.data();

    for (quint32 i = 0; i < stAEPulseExtInformation.iDataPoint; ++i)
    {
        float fPulseInterval;
        float fPeakValue;
        memcpy(&fPulseInterval, pData, sizeof(float));
        pData += sizeof(float);

        memcpy(&fPeakValue, pData, sizeof(float));
        pData += sizeof(float);

        pChart->addSamples( (UINT32)(fPulseInterval*1000),fPeakValue );
    }
}

/*************************************************
功能： 超声波形图采样周期数类型转换
*************************************************************/
AE::SampleTime AETJTestBGView::intSamplePeriod2Enum(int SamplePeriod)
{
    AE::SampleTime eSamplePeriod = AE::SAMPLE_TIME_DEFAULT;
    for(int i =0 ; i < AE::SAMPLE_TIME_COUNT; i ++)
    {
        if(AE::SAMPLE_TIME[i] == SamplePeriod)
        {
            eSamplePeriod =  (AE::SampleTime) i;
            break;
        }
    }
    return eSamplePeriod;
}

/*************************************************
功能： 回放波形数据
*************************************************************/
void AETJTestBGView::playBackWaveData(DataSpecificationNS::AEWaveSpectrum* pAEWaveSpectrum)
{
    AEWave *pChart = dynamic_cast<AEWave*> (m_pChartStackWidget->widget(AEView::AE_WAVE));
    pChart->clear();

    DataSpecificationNS::AEWaveExtInformation stAEWaveExtInformation;
    pAEWaveSpectrum->getAEWaveExtInformation(stAEWaveExtInformation);

    // 无线 AE 250 - 5T 内置 1024 - 1.25T
    int iSamplePeriod = 0;
    if( AE::WIRELESS == m_eChannel )
    {
        iSamplePeriod = (int)(1.0*stAEWaveExtInformation.iDataPoint/WAVE_NUM_PER_5T_WIRELESS + 0.5) * 5;
    }
    else
    {
        iSamplePeriod = (int)(1.0*stAEWaveExtInformation.iDataPoint/WAVE_NUM_PER_T + 0.5);
    }
    pChart->setGain(AE::GAIN_INVALID);
    pChart->setChannelName(AE::CHANNEL_INVALID);
    pChart->setSamplingPeriod( iSamplePeriod );
    pChart->setSynchronousMode(Module::INVALID_SYNC_SOURCE, Module::Not_Sync);
    pChart->setYMax(stAEWaveExtInformation.fAmpUpperLimit);

    DataSpecificationNS::AEWaveData stAEWaveData;
    pAEWaveSpectrum->getAEWaveData(stAEWaveData);

    const char* pData = stAEWaveData.qbaAEWaveData.data();
    QVector< AE::WaveData > datas;
    //QVector< float > testData;
    for(int i = 0; i < /*WAVE_NUM_PER_T*/stAEWaveExtInformation.iDataPoint; ++i)
    {
        AE::WaveData stChartData;
        memcpy(&stChartData.fWaveValue, pData, sizeof(float));
        pData += sizeof(float);
        if(stChartData.fWaveValue > AE::AE_AMPLITUDE_SCOPE[m_eGain][m_eAmpRange])
        {
            stChartData.fWaveValue = AE::AE_AMPLITUDE_SCOPE[m_eGain][m_eAmpRange];
        }
        //testData.append(stChartData.fWaveValue);

        datas.append(stChartData);
    }

    pChart->setSamples(datas);
}

/*************************************************
功能： 回放指定的二进制数据文件(带完整路径的文件名)
*************************************************************/
void AETJTestBGView::playbackBinaryFile(const QString &strBinaryFilePath)
{
    qDebug()<<"AETJTestBGView::playbackBinaryFile, strBinaryFilePath:"<<strBinaryFilePath;

    DataSpecificationNS::DataSpecification dataSpecification;
    dataSpecification.setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());
    if(!dataSpecification.parseBinaryFromFile(strBinaryFilePath))
    {
        dbg_warning("parse binary file failed!\n");
        return;
    }

    int iIndex = strBinaryFilePath.lastIndexOf('/');
    QString strFileName = strBinaryFilePath.right( ( strBinaryFilePath.size() - iIndex - 1 ) );
    m_pLoadFileName->setText( CustomAccessTaskNS::testFileNameFromFile( strFileName ) );

    //file head
    DataSpecificationNS::SpectrumDataFileHead stSpectrumDataFileHead;
    dataSpecification.getSpectrumDataFileHead(stSpectrumDataFileHead);

    //ae amp
    DataSpecificationNS::AEAmpSpectrum* pAEAmpSpectrum = dynamic_cast<DataSpecificationNS::AEAmpSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_AE_AMP));
    if(NULL == pAEAmpSpectrum)
    {
        dbg_warning("parse binary file of ae amp failed!\n");
        return;
    }
    playBackAmpData(pAEAmpSpectrum);

    //ae phase
    DataSpecificationNS::AEPhaseSpectrum* pAEPhaseSpectrum = dynamic_cast<DataSpecificationNS::AEPhaseSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_AE_PHASE));
    if(NULL == pAEPhaseSpectrum)
    {
        dbg_warning("parse binary file of ae phase failed!\n");
        return;
    }

    playBackPhaseData(pAEPhaseSpectrum);

    //浙江华云和北京融智通接入没有AE飞行图谱
    SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
    if(SystemSet::ACCESS_PROTO_SDLR == eProtocol)
    {
        //ae fly
        DataSpecificationNS::AEPulseSpectrum* pAEPulseSpectrum = dynamic_cast<DataSpecificationNS::AEPulseSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_AE_PULSE));
        if(NULL == pAEPulseSpectrum)
        {
            dbg_warning("parse binary file of ae fly failed!\n");
            return;
        }
        playBackFlyData(pAEPulseSpectrum);
    }

    //ae wave
    DataSpecificationNS::AEWaveSpectrum* pAEWaveSpectrum = dynamic_cast<DataSpecificationNS::AEWaveSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_AE_WAVE));
    if(NULL == pAEWaveSpectrum)
    {
        dbg_warning("parse binary file of ae wave failed!\n");
        return;
    }
    playBackWaveData(pAEWaveSpectrum);

}


/*************************************************
功能： 删除二进制数据文件
*************************************************************/
void AETJTestBGView::deleteBinaryDataFile()
{
    m_bgFileList.clear();
    m_bgFileList = m_pSubTask->bgFileList(m_strGapId);

    if(m_bgFileList.size() <= 0)
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
        return;
    }

    QList<QString> lItems;
    for(int i = 0, iSize = m_bgFileList.size(); i < iSize; ++i)
    {
        lItems.append(CustomAccessTaskNS::testFileNameFromFile(m_bgFileList.at(i)));
    }

    if(NULL == m_pFilesItemListView)
    {
        m_pFilesItemListView = new CommonItemListView(QObject::trUtf8("Test Data List"), lItems, NULL);
        connect(m_pFilesItemListView, SIGNAL(sigItemSelected(qint32)), this, SLOT(onProcessSeletedBinaryFile(qint32)));
    }

    m_eListItemProcessType = PROCESS_TYPE_DELETE_DATA;
    m_pFilesItemListView->clear();
    m_pFilesItemListView->addItems(lItems);
    m_pFilesItemListView->show();

    return;
}

/*************************************************
功能： 删除指定的二进制数据文件(带完整路径的文件名)
*************************************************************/
void AETJTestBGView::deleteSeletedBinaryFile(const QString &strBinaryFilePath)
{
    if(removeBinaryFile(strBinaryFilePath))
    {
        QFileInfo fileInfo(strBinaryFilePath);
        //delete test data file of test point
        m_pSubTask->deleteBGFile(m_strGapId, fileInfo.fileName());

        //update list items view
        m_pFilesItemListView->clear();
        QList<QString> lItems;
        for(int i = 0, iSize = m_bgFileList.size(); i < iSize; ++i)
        {
            if(m_bgFileList.at(i) != fileInfo.fileName())
            {
                lItems.append(CustomAccessTaskNS::testFileNameFromFile(m_bgFileList.at(i)));
            }
        }
        m_pFilesItemListView->addItems(lItems);
    }
    return;
}

/*************************************************
功能： 删除二进制文件(带完整路径的文件名)
*************************************************************/
bool AETJTestBGView::removeBinaryFile(const QString &strBinaryFilePath)
{
    bool bRet = false;
    QFile file(strBinaryFilePath);
    if(file.exists())
    {
        bRet = file.remove();
    }
    log_debug("delete file ret: %d.", bRet);
    return bRet;
}

/*************************************************
功能： 触发值按钮的触发值列表根据增益、量纲动态变化
输入参数：
        eGain -- 增益
        eUnit -- 量纲
*************************************************************/
void AETJTestBGView::resetTriggerList( AE::GainType eGain, AE::UnitOption eUnit )
{
    QStringList listTrigger = AE::getTriggerList( eUnit, eGain );

    RadioButton* pButTrigger = (RadioButton*)( this->buttonBar()->button(aetestbgview::BUTTON_AE_TRIGGER_VALUE) );
    if( NULL != pButTrigger )
    {
        pButTrigger->setOptionList( listTrigger );
    }
    else
    {
        qWarning() << "AETJTestBGView::resetTriggerList: error, button is NULL!";
    }
}

/*************************************************
功能： 幅值范围按钮的列表根据增益动态变化
输入参数：
       eGain -- 增益
*************************************************************/
void AETJTestBGView::resetAmpRange(AE::GainType eGain)
{
    QStringList listAmpRange = AE::getAmpRange(eGain);

    RadioButton* pBtnAmpRange = (RadioButton*)(this->buttonBar()->button(aetestbgview::BUTTON_AE_AMPLITUDE_RANGE));
    if(NULL != pBtnAmpRange)
    {
        pBtnAmpRange->setOptionList(listAmpRange);
    }
    else
    {
        logError("amp range button is NULL.");
    }
    return;
}

/*************************************************
功能： 保存数据
输入参数：
        stationName -- 变电站名
        deviceName -- 设备名
返回：
        保存后的文件名
*************************************************************/
QString AETJTestBGView::saveDataToFile(const QString &stationName, const QString& deviceName)
{
    Q_UNUSED(stationName)
    Q_UNUSED(deviceName)
    return "";
}

/*************************************************
功能： 槽，响应同步状态变化
输入参数：
        eSyncState -- 同步状态
*************************************************************/
void AETJTestBGView::onSyncStateChanged( Module::SyncState eSyncState )
{
    if(m_eSyncState != eSyncState)
    {
        m_eSyncState = eSyncState;

        QWidget* pWidget = m_pChartStackWidget->widget(AEView::AE_PHASE);
        AEPhaseChart* pChart1 = dynamic_cast<AEPhaseChart*> (pWidget);
        pChart1->setSyncState(m_eSyncState);

        pWidget = m_pChartStackWidget->widget(AEView::AE_WAVE);
        AEWave* pChart2 = dynamic_cast<AEWave*> (pWidget);
        pChart2->setSynchronousMode(Module::SYNC_SOURCE_DEFAULT, m_eSyncState);
    }

    return;
}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void AETJTestBGView::keyPressEvent( QKeyEvent* event )
{
    if(event->key() == Qt::Key_Escape)
    {
        close();
    }
    else
    {
        SampleChartView::keyPressEvent(event);
    }
    return;
}

/*************************************************
功能： 响应S键事件
*************************************************************/
void AETJTestBGView::onSKeyPressed()
{
    stopSample();
    updateTestedCntLabel();
    saveConfig();
    saveAsBinaryDataFile();
    return;
}

