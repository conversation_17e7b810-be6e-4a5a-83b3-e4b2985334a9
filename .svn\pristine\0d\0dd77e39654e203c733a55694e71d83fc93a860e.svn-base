/*
* Copyright (c) 2017.4，南京华乘电气科技有限公司
* All rights reserved.
*
* HFCTViewConfig.h
*
* 初始版本：1.0
* 作者：邵震宇
* 修改日期：2017年4月17日
* 摘要：HFCT界面配置信息
*       按钮配置等
* 当前版本：1.0
*/


#ifndef HFCT_VIEW_CONFIG_H
#define HFCT_VIEW_CONFIG_H

#include "controlButton/ControlButtonInfo.h"
#include "HFCTView.h"
#include "hfct/HFCT.h"

#define HFCT_VIEW_CONFIG_TRANSLATE(str) qApp->translate("HFCTView", (str))

namespace HFCT
{

const char* const CONTEXT = "HFCTView";//HFCT域
const char* const TEXT_MODE = QT_TRANSLATE_NOOP("HFCTView", "Mode");
const char* const TEXT_RUN = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Start");
const char* const TEXT_STOP = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Stop");
const char* const TEXT_PAUSE = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Pause");
const char* const TEXT_SINGLE_SAMPLE = QT_TRANSLATE_NOOP("HFCTView", "Single Sample");
const char* const TEXT_SINGLE = QT_TRANSLATE_NOOP("HFCTView", "Single");
const char* const TEXT_CONTINIOUS = QT_TRANSLATE_NOOP("HFCTView", "Continuous");
const char* const TEXT_YELLOW_ALERT = QT_TRANSLATE_NOOP("HFCTView", "Warning");
const char* const TEXT_RED_ALERT = QT_TRANSLATE_NOOP("HFCTView", "High Risk");
const char* const TEXT_GAIN = QT_TRANSLATE_NOOP("HFCTView", "Gain");
const char* const TEXT_RESTORE_DEFAULT = QT_TRANSLATE_NOOP("HFCTView", "Default");
const char* const TEXT_WIRELESS_SYNC = QT_TRANSLATE_NOOP("HFCTView", "Power");
const char* const TEXT_LIGHT_SYNC = QT_TRANSLATE_NOOP("HFCTView", "Light");
const char* const TEXT_SYNC_SOURCE = QT_TRANSLATE_NOOP("HFCTView", "Sync Mode");
const char* const TEXT_MORE_CONFIG = QT_TRANSLATE_NOOP("HFCTView", "More...");
const char* const TEXT_SAVE_DATA = QT_TRANSLATE_NOOP("HFCTView", "Save Data");
const char* const TEXT_SAVE = QT_TRANSLATE_NOOP("HFCTView", "Save");
const char* const TEXT_RFID_SAVE = QT_TRANSLATE_NOOP("HFCTView", "Save RFID");
const char* const TEXT_DELETE_DATA = QT_TRANSLATE_NOOP("HFCTView", "Delete Data");
const char* const TEXT_LOAD_DATA = QT_TRANSLATE_NOOP("HFCTView", "Load Data");
const char* const TEXT_PHASE_ALIAS = QT_TRANSLATE_NOOP("HFCTView", "Phase Shift");
const char* const TEXT_ON = QT_TRANSLATE_NOOP_UTF8("HFCTView", "On");
const char* const TEXT_OFF = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Off");
const char* const TEXT_ADD = QT_TRANSLATE_NOOP("HFCTView", "Add");
const char *const TEXT_USING_ACCUMULATION  = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Accumulate");
const char* const TEXT_ACCUMULATIVE_TIME = QT_TRANSLATE_NOOP_UTF8("HFCTView","Accumulative Time");
const char* const TEXT_MORE = QT_TRANSLATE_NOOP_UTF8("HFCTView", "More...");
const char* const TEXT_PRPS_THRESHOLD = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Threshold");
const char* const TEXT_PRPS_NOISEREDUCTION = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Noise Reduction");
const char* const TEXT_ALTAS_TYPE = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Altas Type");

const char* const TEXT_DEGREE = "°";
const char* const TEXT_DB = "dB";

//prps video
const char* const TEXT_RECORD = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Record");
const char* const TEXT_RECORD_TIME = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Record Time");
const char* const TEXT_RECORD_PLAYBACK = QT_TRANSLATE_NOOP_UTF8("HFCTView","Playback");
const char* const TEXT_RECORD_DELETE = QT_TRANSLATE_NOOP_UTF8("HFCTView","Delete Record");

const char* const TEXT_AMP = QT_TRANSLATE_NOOP_UTF8("HFCTView", "View Sig Amp");
const char* const TEXT_PERIOD = QT_TRANSLATE_NOOP_UTF8("HFCTView", "View Period");
const char* const TEXT_PRPS = QT_TRANSLATE_NOOP_UTF8("HFCTView", "View PRPD&PRPS");
const char* const TEXT_AMP_TITLE = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Amplitude");
const char* const TEXT_PERIOD_TITLE = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Period");
const char* const TEXT_PRPD_PRPS_SPECTRUM = QT_TRANSLATE_NOOP_UTF8("HFCTView", "PRPD&PRPS");
const char* const TEXT_PRPS_SPECTRUM = QT_TRANSLATE_NOOP_UTF8("HFCTView", "PRPS");
const char* const TEXT_PRPD_SPECTRUM = QT_TRANSLATE_NOOP_UTF8("HFCTView", "PRPD");
const char* const TEXT_MINUTE = "min";
const char* const TEXT_SECOND = "s";

const char* const TEXT_AUTO = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Auto");
const char* const TEXT_THRESHOLD_OFF = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Off");
const char* const TEXT_THRESHOLD_LEVEL1 = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Level 1");
const char* const TEXT_THRESHOLD_LEVEL2 = QT_TRANSLATE_NOOP_UTF8("HFCTView", "Level 2");

//采集模式
const char* const TEXT_MODE_OPTIONS[] =
{
    TEXT_SINGLE,
    TEXT_CONTINIOUS,
};

//增益
const char* const GAIN_OPTIONS[] =
{
    "-60dB",
    "-40dB",
    "-20dB",
    "0dB",
};

//阈值类型
const char* const TEXT_THRESHOLD_OPTIONS[] =
{
    TEXT_THRESHOLD_OFF,
    TEXT_AUTO,
    TEXT_THRESHOLD_LEVEL1,
    TEXT_THRESHOLD_LEVEL2
};

//  累积时长
const char* const TEXT_ACCUMULATIVE_TIME_OPTIONS[] =
{
    "1s",
    "5s",
    "15s",
    "30s",
    "60s"
};

inline HFCT::Gain gainVal2Enum(UINT8 ucGain)
{
    for(int i = HFCT::GAIN_START; i <= HFCT::GAIN_END; ++i)
    {
        if(ucGain == GAIN_VALUES[i])
        {
            return (HFCT::Gain)i;
        }
    }

    return HFCT::GAIN_DEFAULT;
}

//同步选项
const char* const TEXT_SYNC_OPTIONS[] =
{
    TEXT_WIRELESS_SYNC,
    TEXT_LIGHT_SYNC,
};

//off/on选项
const char* const TEXT_ON_OFF_OPITIONS[] =
{
    TEXT_OFF, TEXT_ON
};

//图谱类型
const char* const TEXT_ALTAS_TYPE_OPTIONS[] =
{
    "PRPD+PRPS",
    "PRPS",
    "PRPD",
};

}

#endif // HFCT_VIEW_CONFIG_H
