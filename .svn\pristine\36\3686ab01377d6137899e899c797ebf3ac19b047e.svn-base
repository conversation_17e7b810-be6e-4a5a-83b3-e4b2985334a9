#include "selfinspectionmanager.h"
#include <QMutexLocker>
#include <QMetaType>
#include "selfcheck.h"
#include "log/log.h"
#include "Module.h"
#include "datadefine.h"
#include "systemsetting/systemsetservice.h"


#define SEND_RET_INTERVAL 1000

/*************************************************
 * 功能：构造函数
 * *************************************************/
SelfInspectionManager::SelfInspectionManager(QObject *parent)
    : QObject(parent)
{
    initSelfInspInfo();
    startThread();
}

/****************************************************
 * 功能：析构函数
 * ******************************************************/
SelfInspectionManager::~SelfInspectionManager()
{
    disconnect(this, SIGNAL(sigStartSelfInspect()), this, SLOT(onStartSelfInspect()));
    stopThread();
}

/****************************************************
 * 功能：获取自检管理类的实例
 * 返回值：
 *      自检管理类的实例对象
 * ******************************************************/
SelfInspectionManager* SelfInspectionManager::instance()
{
    static SelfInspectionManager objManager;
    return &objManager;
}

/****************************************************
 * 功能：获取自检模块的信息，包括自检结果
 * 返回值：
 *      设备自检模块信息集合
 * ******************************************************/
QVector<SelfInfo> SelfInspectionManager::getSelfInfo()
{
    return m_qvtSelfInfos;
}

/****************************************************
 * 功能：开始自检
 * ******************************************************/
void SelfInspectionManager::startSelfInspection(bool bCheck)
{
    //自检将导致新屏出现黑屏，此处做临时处理，等硬件解决后需要删除
    if(bCheck)
    {
        if(!m_bInspecting)
        {
            m_bInspecting = true;
            emit sigStartSelfInspect();
        }
    }
    else
    {
        Module::mSleep(1000);
        emit sigSelfInspResult(SELF_PASS);
    }

    return;
}

/****************************************************
 * 功能：停止自检
 * ******************************************************/
void SelfInspectionManager::stopSelfInspection()
{
    m_bInspecting = false;
    return;
}

/****************************************************
 * 功能：响应开始自检信号的槽
 * ******************************************************/
void SelfInspectionManager::onStartSelfInspect()
{
    bool bRet = false;
    bool bValidCheck = false;

    for(int i = 0, iSize = m_qvtSelfInfos.size(); i < iSize; ++i)
    {
        if(!m_bInspecting)
        {
            logWarning("stop self inspection external.");
            break;
        }
#ifdef Q_PROCESSOR_ARM
        switch (m_qvtSelfInfos[i].eInspModule)
        {
        case SELF_WIFI:
        {
            bValidCheck = true;
            bRet = is_wifi_selfcheck_ok();
            break;
        }
        case SELF_DISK:
        {
            bValidCheck = true;
            bRet = is_disk_selfcheck_ok();
            break;
        }
        case SELF_RTC:
        {
            bValidCheck = true;
            bRet = is_rtc_selfcheck_ok();
            break;
        }
        case SELF_RFID:
        {
            bValidCheck = true;
            bRet = is_rfid_selfcheck_ok();
            break;
        }
        case SELF_AE:
        {
            bValidCheck = true;
            bRet = is_ae_selfcheck_ok();
            break;
        }
        case SELE_TEV:
        {
            bValidCheck = true;
            bRet = is_tev_selfcheck_ok();
            break;
        }
        default:
            bValidCheck = false;
            break;
        }

        if(bValidCheck)
        {
            m_qvtSelfInfos[i].eInspState = bRet ? SELF_PASS : SELF_FAIL;
            logInfo(QString("self inspection module: %1, state: %2.").arg(m_qvtSelfInfos[i].eInspModule).arg(m_qvtSelfInfos[i].eInspState));
            emit sigSingleSelfInspResult(m_qvtSelfInfos[i].eInspModule, m_qvtSelfInfos[i].eInspState);
        }
#endif
    }

    Module::mSleep(SEND_RET_INTERVAL);
    dealSelfInspectionRet();

    return;
}

/****************************************************
 * 功能：处理（检查）所有模块自检结果
 * ******************************************************/
void SelfInspectionManager::dealSelfInspectionRet()
{
    m_eSelfState = SELF_PASS;
    for(int i = 0, iSize = m_qvtSelfInfos.size(); i < iSize; ++i)
    {
        if(SELF_FAIL == m_qvtSelfInfos[i].eInspState)
        {
            m_eSelfState = SELF_FAIL;
            break;
        }
    }

    emit sigSelfInspResult(m_eSelfState);
    m_bInspecting = false;

    SystemSetService::instance()->initLCD();//AE和TEV自检后，新屏容易黑屏，此处做临时处理，等硬件解决后需要删除这个处理

    return;
}

/****************************************************
 * 功能：初始化自检信息中
 * ******************************************************/
void SelfInspectionManager::initSelfInspInfo()
{
    m_bInspecting = false;
    m_eSelfState = SELF_IDLE;

    m_pExeThread = new QThread(this);
    this->moveToThread(m_pExeThread);

    qRegisterMetaType<SelfState>("SelfState");
    qRegisterMetaType<SelfInspModule>("SelfInspModule");

    SelfInfo stWifiInfo;
    stWifiInfo.eInspModule = SELF_WIFI;
    m_qvtSelfInfos.append(stWifiInfo);

    SelfInfo stDiskInfo;
    stDiskInfo.eInspModule = SELF_DISK;
    m_qvtSelfInfos.append(stDiskInfo);

    SelfInfo stRtcInfo;
    stRtcInfo.eInspModule = SELF_RTC;
    m_qvtSelfInfos.append(stRtcInfo);

    SelfInfo stRfidInfo;
    stRfidInfo.eInspModule = SELF_RFID;
    m_qvtSelfInfos.append(stRfidInfo);

    SelfInfo stAeInfo;
    stAeInfo.eInspModule = SELF_AE;
    m_qvtSelfInfos.append(stAeInfo);

    SelfInfo stTevInfo;
    stTevInfo.eInspModule = SELE_TEV;
    m_qvtSelfInfos.append(stTevInfo);

    connect(this, SIGNAL(sigStartSelfInspect()), this, SLOT(onStartSelfInspect()));
    return;
}

/****************************************************
 * 功能：开启线程
 * ******************************************************/
void SelfInspectionManager::startThread()
{
    QMutexLocker stLocker(&m_mt4ExeThread);
    if(m_pExeThread && !m_pExeThread->isRunning())
    {
        m_pExeThread->start();
    }
    return;
}

/****************************************************
 * 功能：停止线程
 * ******************************************************/
void SelfInspectionManager::stopThread()
{
    QMutexLocker stLocker(&m_mt4ExeThread);
    if(m_pExeThread && m_pExeThread->isRunning())
    {
        m_pExeThread->quit();
        m_pExeThread->wait(3000);
    }
    return;
}
