﻿#ifndef CALIBRATEBASEWIDGET_H
#define CALIBRATEBASEWIDGET_H

#include <QFrame>
#include <QEvent>

class CalibrateBaseWidget : public QFrame
{
    Q_OBJECT
public:
    explicit CalibrateBaseWidget(const int iWidth, const int iHeight, QWidget *parent = 0);
    ~CalibrateBaseWidget();

    virtual void setItemSelectState(bool bSelected);

    virtual bool isSelected();

    virtual void setSelectedViewFocused();

    virtual void clearSelectedViewFocused();

signals:
    void sigKeyPressEvent(QEvent *pEvent);

    void sigPressed(QObject *pObj);

protected:
    bool eventFilter(QObject *pObj, QEvent *pEvent);

protected:
    virtual void createUI();

protected:
    bool    m_bSelected;
};

#endif // CALIBRATEBASEWIDGET_H
