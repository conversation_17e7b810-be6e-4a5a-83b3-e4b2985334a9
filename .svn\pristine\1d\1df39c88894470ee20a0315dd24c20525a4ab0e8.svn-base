#include "phasetypeview.h"
#include "window/Window.h"
#include "mobileaccessservice.h"
#include "customaccesstask/taskmanager.h"
#include "customaccessUi/taskmodeview/taskmodeconfig.h"
#include "peripheral/peripheralservice.h"
#include "recordplay/RecordPlayService.h"
#include "infrared/infraredservice.h"
#include "infrared/guide/guideclientmanager.h"
#include "current/currentservice.h"
#include "customaccessUi/infraredtestview/infraredcabeltestview.h"
#include "customaccessUi/infraredtestview/guideinfraredcabeltestview.h"
#include "customaccessUi/aetestview/aecabeltestbgview.h"
#include "customaccessUi/aetestview/aecabeltestview.h"
#include "customaccessUi/uhftestview/uhfprpscabeltestbgview.h"
#include "customaccessUi/uhftestview/uhfprpscabeltestview.h"
#include "customaccessUi/hfcttestview/hfctprpscabeltestbgview.h"
#include "customaccessUi/hfcttestview/hfctprpscabeltestview.h"
#include "log/log.h"
#include "global_log.h"


#define TIMER_ID_INIT -1
#define TIMER_OUT 1000
#define AUTO_SWITCH_VIEW_DELAY 1000

/*************************************************
功能： 构造函数
输入参数:
    parent:父控件指针
*************************************************************/
PhaseTypeView::PhaseTypeView(QWidget *parent)
    : PDAListView(QObject::trUtf8("Phase Type"), parent),
      m_iAutoPhaseTimerId(TIMER_ID_INIT),
      m_bClickEnable(true),
      m_bGettingCurrentInfo(false),
      m_iCurrentTestIndex(0)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);

    // 初始化列表
    initChart();

    openPower();

    connect(CurrentService::instance(), SIGNAL(sigReadFinished(bool, const Module::CabelCurrentInfo&)), this, SLOT(onReadCurrentFinished(bool, const Module::CabelCurrentInfo&)));
}

/*************************************************
功能： 析构
*************************************************************/
PhaseTypeView::~PhaseTypeView( )
{
    killAutoPhaseTimer();
    closePower();
}

/*************************************************
功能：根据测点类型进行电源打开操作
*************************************************************/
void PhaseTypeView::openPower()
{
    if(TaskModeViewNS::PD_AE == TaskManager::instance()->getCurPDType())
    {
        PeripheralService::instance()->openAEPower();
        RecordPlayService::instance()->startListenAE();
    }
    else if(TaskModeViewNS::TYPE_INFRARED == TaskManager::instance()->getCurTestType())
    {
        Infrared::InfraredType eInfraredType = InfraredService::instance()->initInfraredType();
        if(Infrared::GUIDE == eInfraredType)
        {
            Guide::GuideDevInfo stGuideDevInfo;
            GuideClientManager::instance()->connectInfraredDev(stGuideDevInfo);
        }
        else
        {
            InfraredService::instance()->initModule();
        }
    }
}

/*************************************************
功能：根据测点类型进行电源关闭操作
*************************************************************/
void PhaseTypeView::closePower()
{
    if(TaskModeViewNS::PD_AE == TaskManager::instance()->getCurPDType())
    {
        PeripheralService::instance()->closeAEPower();
        RecordPlayService::instance()->stopListenAE();
    }
    else if(TaskModeViewNS::TYPE_INFRARED == TaskManager::instance()->getCurTestType())
    {
        Infrared::InfraredType eInfraredType = InfraredService::instance()->getInfraredType();
        if(Infrared::GUIDE == eInfraredType)
        {
            // 退出界面将激光功能关闭
            GuideClientManager::instance()->ctrlLaser(Guide::SW_OFF);

            // 退出界面将辅助照明关闭
            GuideClientManager::instance()->ctrlAuxiliaryLighting(Guide::SW_OFF);

            GuideClientManager::instance()->disconnectInfraredDev();
        }
        else
        {
            InfraredService::instance()->deinitModule();
        }
    }
}

/********************************************
 * 功能：初始化列表
 * ******************************************/
void PhaseTypeView::initChart()
{
    m_pChart->deleteAllItem();
    QList<PDAListChart::ListItemInfo> itemInfos;

    TaskModeViewNS::AssetInfo* pAssetInfo = TaskManager::instance()->getJSONCurAssetInfo();
    QString qstrCurrentAssetId = TaskManager::instance()->getJSONAssetId();
    bool bTicked = false;
    bool bTested = false;
    if(pAssetInfo->qvtPhaseTypes.isEmpty())
    {
        if(TaskModeViewNS::TYPE_CURRENT == TaskManager::instance()->getCurTestType())
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_N);
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_N), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_N));
        }

        if(TaskModeViewNS::TYPE_PD == TaskManager::instance()->getCurTestType())
        {
            bTested = TaskManager::instance()->isJSONTaskBGNTested();
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_BGN), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_BGN));
        }

        bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_A);
        itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_A), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_A));

        bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_B);
        itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_B), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_B));

        bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_C);
        itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_C), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_C));
    }
    else
    {
        if(TaskModeViewNS::TYPE_PD == TaskManager::instance()->getCurTestType())
        {
            bTested = TaskManager::instance()->isJSONTaskBGNTested();
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_BGN), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_BGN));
        }

        if(pAssetInfo->qvtPhaseTypes.contains(static_cast<int>(TaskModeViewNS::PHASE_N)))
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_N);
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_N), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_N));
        }
        else
        {
            log_debug("asset (%s) not contains phase N.", pAssetInfo->qstrId.toLatin1().data());
        }

        if(pAssetInfo->qvtPhaseTypes.contains(static_cast<int>(TaskModeViewNS::PHASE_A)))
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_A);
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_A), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_A));
        }
        else
        {
            log_debug("asset (%s) not contains phase A.", pAssetInfo->qstrId.toLatin1().data());
        }

        if(pAssetInfo->qvtPhaseTypes.contains(static_cast<int>(TaskModeViewNS::PHASE_B)))
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_B);
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_B), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_B));
        }
        else
        {
            log_debug("asset (%s) not contains phase B.", pAssetInfo->qstrId.toLatin1().data());
        }

        if(pAssetInfo->qvtPhaseTypes.contains(static_cast<int>(TaskModeViewNS::PHASE_C)))
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_C);
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_C), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_C));
        }
        else
        {
            log_debug("asset (%s) not contains phase C.", pAssetInfo->qstrId.toLatin1().data());
        }
    }

    // 设置电流值
    if(TaskModeViewNS::TYPE_CURRENT == TaskManager::instance()->getCurTestType()
            || TaskModeViewNS::TYPE_LOAD_CURRENT == TaskManager::instance()->getCurTestType())
    {
        for (int i = 0; i < itemInfos.size(); ++i)
        {
            // 未测不显示
            if (0 == itemInfos[i].m_iTotalCount)
            {
                continue;
            }

            TaskModeViewNS::PhaseType ePhaseType = static_cast<TaskModeViewNS::PhaseType>(itemInfos[i].m_strId.toInt());
            if (TaskModeViewNS::PHASE_BGN == ePhaseType)
            {
                continue;
            }

            float fGroundCurrentVal = TaskManager::instance()->getCurJSONTaskGroundCurrentVal(ePhaseType);
            itemInfos[i].m_strContent = (QString("%1A").arg(static_cast<double>(fGroundCurrentVal), 0, 'f', 1));
        }
    }

    m_pChart->addItems(itemInfos);
    m_pChart->setCurrentItemSelected(m_iCurrentTestIndex);
}

/*****************************************
 * 功能：关闭自动跳转定时器
 * **********************************************/
void PhaseTypeView::killAutoPhaseTimer()
{
    if(TIMER_ID_INIT != m_iAutoPhaseTimerId)
    {
        killTimer(m_iAutoPhaseTimerId);
        m_iAutoPhaseTimerId = TIMER_ID_INIT;
    }
}

/*****************************************
 * 功能：自动跳转检测任务
 * **********************************************/
void PhaseTypeView::setAutoPhaseTest()
{
    int iMode = MobileAccessService::instance()->getSwitchMode();
    if(SystemSet::ACCESS_AUTO_SWITCH == static_cast<SystemSet::AccessSwitchMode>(iMode))
    {
        if(TIMER_ID_INIT == m_iAutoPhaseTimerId)
        {
            m_iAutoPhaseTimerId = startTimer(AUTO_SWITCH_VIEW_DELAY);
        }
    }
}

/*****************************************
 * 功能：处理相位选择后的操作
 * **********************************************/
void PhaseTypeView::choosePhaseToDo()
{
    if(TaskModeViewNS::TYPE_CURRENT == TaskManager::instance()->getCurTestType()
            || TaskModeViewNS::TYPE_LOAD_CURRENT == TaskManager::instance()->getCurTestType())
    {
        m_bClickEnable = false;
        m_bGettingCurrentInfo = true;
        m_pChart->setEnabled(false);
        CurrentService::instance()->getCurrentInfoSync();
    }
    else
    {
        TaskModeViewNS::TestType eTestTpye = TaskManager::instance()->getCurTestType();
        TaskModeViewNS::PDType ePDType = TaskManager::instance()->getCurPDType();
        TaskModeViewNS::PhaseType ePhaseType = TaskManager::instance()->getCurPhaseType();

        switch(eTestTpye)
        {
        case TaskModeViewNS::TYPE_INFRARED:
        {
            Infrared::InfraredType eInfraredType = InfraredService::instance()->getInfraredType();
            if(Infrared::GUIDE == eInfraredType)
            {
                GuideInfraredCabelTestView* pView = new GuideInfraredCabelTestView();
                connect(pView, SIGNAL(sigTested()), this, SLOT(onTested()));
                connect(pView, SIGNAL(sigExitTest()), this, SLOT(onAutoSwitch()));
                connect(pView, SIGNAL(sigClosed()), this, SLOT(onTestViewclosed()));
                pView->show();
            }
            else
            {
                InfraredCabelTestView* pView = new InfraredCabelTestView();
                connect(pView, SIGNAL(sigTested()), this, SLOT(onTested()));
                connect(pView, SIGNAL(sigExitTest()), this, SLOT(onAutoSwitch()));
                connect(pView, SIGNAL(sigClosed()), this, SLOT(onTestViewclosed()));
                pView->show();
            }
            break;
        }
        case TaskModeViewNS::TYPE_PD:
        {
            switch(ePDType)
            {
            case TaskModeViewNS::PD_AE:
            {
                if(TaskModeViewNS::PHASE_BGN == ePhaseType)
                {
                    AECabelTestBGView *pView = new AECabelTestBGView(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_AEBGN));
                    connect(pView, SIGNAL(sigTested()), this, SLOT(onTested()));
                    connect(pView, SIGNAL(sigExitTest()), this, SLOT(onAutoSwitch()));
                    connect(pView, SIGNAL(sigClosed()), this, SLOT(onTestViewclosed()));
                    TaskModeViewNS::AssetInfo *pAssetInfo = TaskManager::instance()->getJSONCurAssetInfo();
                    if(pAssetInfo)
                    {
                        pView->setBayName(pAssetInfo->qstrName);
                    }
                    pView->show();
                }
                else
                {
                    AECabelTestView *pView = new AECabelTestView(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_AE));
                    connect(pView, SIGNAL(sigTested()), this, SLOT(onTested()));
                    connect(pView, SIGNAL(sigExitTest()), this, SLOT(onAutoSwitch()));
                    connect(pView, SIGNAL(sigClosed()), this, SLOT(onTestViewclosed()));
                    TaskModeViewNS::AssetInfo *pAssetInfo = TaskManager::instance()->getJSONCurAssetInfo();
                    if(pAssetInfo)
                    {
                        pView->setBayName(pAssetInfo->qstrName);
                    }
                    pView->show();
                }
                break;
            }
            case TaskModeViewNS::PD_UHF:
            {
                if(TaskModeViewNS::PHASE_BGN == ePhaseType)
                {
                    UHFPRPSCabelTestBGView *pView = new UHFPRPSCabelTestBGView(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_UHFBGN));
                    connect(pView, SIGNAL(sigTested()), this, SLOT(onTested()));
                    connect(pView, SIGNAL(sigExitTest()), this, SLOT(onAutoSwitch()));
                    connect(pView, SIGNAL(sigClosed()), this, SLOT(onTestViewclosed()));
                    pView->show();
                }
                else
                {
                    UHFPRPSCabelTestView *pView = new UHFPRPSCabelTestView(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_UHF));
                    connect(pView, SIGNAL(sigTested()), this, SLOT(onTested()));
                    connect(pView, SIGNAL(sigExitTest()), this, SLOT(onAutoSwitch()));
                    connect(pView, SIGNAL(sigClosed()), this, SLOT(onTestViewclosed()));
                    pView->show();
                }
                break;
            }
            case TaskModeViewNS::PD_HFCT:
            {
                if(TaskModeViewNS::PHASE_BGN == ePhaseType)
                {
                    HFCTPRPSCabelTestBGView *pView = new HFCTPRPSCabelTestBGView(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_HFCTBGN));
                    connect(pView, SIGNAL(sigTested()), this, SLOT(onTested()));
                    connect(pView, SIGNAL(sigExitTest()), this, SLOT(onAutoSwitch()));
                    connect(pView, SIGNAL(sigClosed()), this, SLOT(onTestViewclosed()));
                    pView->show();
                }
                else
                {
                    HFCTPRPSCabelTestView *pView = new HFCTPRPSCabelTestView(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_HFCT));
                    connect(pView, SIGNAL(sigTested()), this, SLOT(onTested()));
                    connect(pView, SIGNAL(sigExitTest()), this, SLOT(onAutoSwitch()));
                    connect(pView, SIGNAL(sigClosed()), this, SLOT(onTestViewclosed()));
                    pView->show();
                }
                break;
            }
            default:
                break;
            }
            break;
        }
        default:
            break;

        }
    }
}

/*************************************************
功能： 槽，响应测试界面关闭
*************************************************************/
void PhaseTypeView::onTestViewclosed()
{
    // 更新当前项的状态
    PDAListChart::ListItemInfo stListItemInfo = m_pChart->currentItemInfo();
    if (!stListItemInfo.m_strId.isEmpty())
    {
        TaskModeViewNS::PhaseType ePhaseType = static_cast<TaskModeViewNS::PhaseType>(stListItemInfo.m_strId.toInt());
        if (TaskModeViewNS::PHASE_INVALID == ePhaseType)
        {
            return;
        }

        bool bTested = false;
        QString qstrCurrentAssetId = TaskManager::instance()->getJSONAssetId();
        if (TaskModeViewNS::PHASE_BGN == ePhaseType)
        {
            bTested = TaskManager::instance()->isJSONTaskBGNTested();
        }
        else
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, ePhaseType);
        }

        m_pChart->setCurItemTested(stListItemInfo.m_strId, bTested);
    }
}

/*************************************************
功能： 槽，响应******
*************************************************************/
void PhaseTypeView::onAutoSwitch()
{
    setAutoPhaseTest();
}

/*************************************************
功能： 槽，响应******
*************************************************************/
void PhaseTypeView::onTested()
{
    TaskManager::instance()->setJSONPhaseFinished(TaskManager::instance()->getJSONAssetId(), TaskManager::instance()->getCurPhaseType());
    TaskManager::instance()->setJSONAssetTested(TaskManager::instance()->getJSONAssetId());
    TaskManager::instance()->setJSONSubTaskTested(TaskManager::instance()->getJSONSubTaskId());
    TaskManager::instance()->setJSONTaskTested(TaskManager::instance()->getJSONMainTaskId());
}

/*************************************************
功能： 槽，响应读取电流结束
*************************************************************/
void PhaseTypeView::onReadCurrentFinished(bool bSuccess, const Module::CabelCurrentInfo& stCurrentInfo)
{
    if (!m_bGettingCurrentInfo)
    {
        return;
    }

    m_bGettingCurrentInfo = false;
    m_bClickEnable = true;
    m_pChart->setEnabled(true);
    if (!bSuccess)
    {
        MsgBox::warning("", trUtf8("Read grounding current failed!"));
        return;
    }

    TaskModeViewNS::PhaseType ePhaseType = TaskManager::instance()->getCurPhaseType();
    //TaskManager::instance()->setCurJSONTaskGroundCurrentVal(ePhaseType, stCurrentInfo.fGroundCurrentVal);

    TaskManager::instance()->setJSONPhaseFinished(TaskManager::instance()->getJSONAssetId(), ePhaseType);
    TaskManager::instance()->setJSONAssetTested(TaskManager::instance()->getJSONAssetId());
    TaskManager::instance()->setJSONSubTaskTested(TaskManager::instance()->getJSONSubTaskId());
    TaskManager::instance()->setJSONTaskTested(TaskManager::instance()->getJSONMainTaskId());

    PDAListChart::ListItemInfo stListItemInfo = m_pChart->currentItemInfo();
    if (!stListItemInfo.m_strId.isEmpty())
    {
        stListItemInfo.m_strContent = QString("%1A").arg(static_cast<double>(stCurrentInfo.fGroundCurrentVal), 0, 'f', 1);
        m_pChart->setItemInfo(stListItemInfo, m_pChart->itemIndexSelected());
        m_pChart->setCurItemTested(stListItemInfo.m_strId, true);
    }

    setAutoPhaseTest();
}

/*************************************************
功能： 定时器处理函数
输入参数：
        e -- 定时事件
*************************************************************/
void PhaseTypeView::timerEvent(QTimerEvent* pEvent)
{
    m_bClickEnable = false;

    if(pEvent->timerId() == m_iAutoPhaseTimerId)
    {
        killAutoPhaseTimer();

        TaskModeViewNS::PhaseType ePhaseType = TaskManager::instance()->getUnfinishedPhaseInfo();
        if(TaskModeViewNS::PHASE_INVALID == ePhaseType)
        {
            TaskManager::instance()->setJSONSubInfoFinished(TaskManager::instance()->getJSONAssetId());

            if(MsgBox::OK == MsgBox::delayQuestion("", QObject::trUtf8("All items have been tested and will automatically jump.")))
            {
                close();
            }
        }
        else
        {
            QString qstrId = QString::number(ePhaseType);
            m_iCurrentTestIndex = m_pChart->indexOfItem(qstrId);
            m_pChart->setCurrentItemSelected(m_iCurrentTestIndex);
            Module::mSleep(100); // 界面上选中相位按钮效果
            switchTestView(m_iCurrentTestIndex);
        }
    }
    m_bClickEnable = true;
}

/****************************
功能： 处理showEvent事件
输入参数:
       event -- showEvent
*****************************/
void PhaseTypeView::showEvent(QShowEvent *event)
{
    PDAListView::showEvent(event);
    // 打开自动跳转定时器 间隔1s
    setAutoPhaseTest();
}

/*************************************************
功能： 进入测试界面
输入参数：
        id -- 条目序号
*************************************************************/
void PhaseTypeView::switchTestView(int id)
{
    PDAListChart::ListItemInfo stListItemInfo = m_pChart->currentItemInfo();
    if (stListItemInfo.m_strId.isEmpty())
    {
        return;
    }

    TaskModeViewNS::PhaseType ePhaseType = static_cast<TaskModeViewNS::PhaseType>(stListItemInfo.m_strId.toInt());

    switch(ePhaseType)
    {
    case TaskModeViewNS::PHASE_BGN:
    {
        TaskManager::instance()->setJSONTaskPhaseType(ePhaseType, "");
        choosePhaseToDo();
        break;
    }
    case TaskModeViewNS::PHASE_N:
    {
        TaskManager::instance()->setJSONTaskPhaseType(ePhaseType, TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_N));
        choosePhaseToDo();
        break;
    }
    case TaskModeViewNS::PHASE_A:
    {
        TaskManager::instance()->setJSONTaskPhaseType(ePhaseType, TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_A));
        choosePhaseToDo();
        break;
    }
    case TaskModeViewNS::PHASE_B:
    {
        TaskManager::instance()->setJSONTaskPhaseType(ePhaseType, TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_B));
        choosePhaseToDo();
        break;
    }
    case TaskModeViewNS::PHASE_C:
    {
        TaskManager::instance()->setJSONTaskPhaseType(ePhaseType, TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_C));
        choosePhaseToDo();
        break;
    }
    default:
        break;
    }
}

/*************************************************
功能： 处理条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void PhaseTypeView::onItemClicked(int id)
{
    if(!m_bClickEnable)
    {
        logWarning("unable to click.");
        return;
    }

    killAutoPhaseTimer();

    m_iCurrentTestIndex = id;
    switchTestView(id);
}


