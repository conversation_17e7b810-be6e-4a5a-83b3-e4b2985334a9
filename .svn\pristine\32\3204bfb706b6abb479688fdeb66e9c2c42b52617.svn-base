/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* UHFService.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年2月7日
* 摘要：UHF服务模块接口定义

* 当前版本：1.0
*/

#ifndef UHFSERVICE_H
#define UHFSERVICE_H
#include "model/HCService.h"
#include "UHF.h"
#include "module_global.h"
#include "Module.h"
#include "multiservice/multiuserservice.h"

class UHFServicePrivate;
class MODULESHARED_EXPORT UHFService : public MultiUserService
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    *****************************/
    UHFService();

    /****************************
    功能： 析构函数
    *****************************/
    ~UHFService();

    /*************************************************
    功能： 启动业务
    *************************************************/
    virtual bool start( void );

    /*************************************************
    功能： 启动业务
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    virtual bool isStart( void );

    /*************************************************
    功能： 终止业务
    *************************************************/
    virtual bool stop( void );

    /*************************************************
    功能： 设置工作模式
    输入参数：
            eMode -- 工作模式
    *************************************************/
    void setWorkMode( UHF::WorkMode eMode );

    /*************************************************
    功能： 设置前置增益
    输入参数：
            eGain -- 前置增益
    返回：
            true -- 成功
            false -- 失败
    *************************************************/
    bool setForwardGain( UHF::ForwardGain eGain );

    /*************************************************
    功能： 设置带宽
    输入参数：
            eBandWidth -- 带宽
    返回：
            true -- 成功
            false -- 失败
    *************************************************/
    bool setBandWidth( UHF::BandWidth eBandWidth );

    /*************************************************
    功能： 设置同步源
    输入参数：
            eSyncSource -- 同步源
    返回：
            true -- 成功
            false -- 失败
    *************************************************/
    bool setSyncSource( Module::SyncSource eSyncSource );
signals:
    /*************************************************
    功能： 信号  产生的幅值数据
    *************************************************/
    void sigData( UHF::AmplitudeData data,MultiServiceNS::USERID userId );

    /*************************************************
    功能： 信号  产生的周期数据
    *************************************************/
    void sigData( UHF::IntervalData data,MultiServiceNS::USERID userId );

    /*************************************************
    功能： 信号  产生的prps数据
    *************************************************/
    void sigData( UHF::PRPSData data,MultiServiceNS::USERID userId );

    void sigReadUHFPRPSPRPDDataFailed( MultiServiceNS::USERID userId );

    /*************************************************
    功能： 信号  数据信号状态发生变化
    *************************************************/
    void sigSignalChanged( Module::SignalState eSignalState );

    /*************************************************
    功能： 信号   同步状态变化
    *************************************************/
    void sigSyncStateChanged( Module::SyncState eSyncState );

protected:
    /*************************************************
        功能： 启动采样业务的额外处理   （一般指在子类中的额外操作）
        输入参数：
                userId -- 用户ID
        返回：
              额外处理是否成功
        *************************************************/
    virtual bool startSampleExt( MultiServiceNS::USERID userId );

    /*************************************************
        功能： 停止采样业务的额外处理   （一般指在子类中的额外操作）
        输入参数：
                userId -- 用户ID
        返回：
              额外处理是否成功
        *************************************************/
    virtual bool stopSampleExt( MultiServiceNS::USERID userId );

private slots:
    /*************************************************
    功能： 信号  产生的幅值数据
    *************************************************/
    void onData( UHF::AmplitudeData data );

    /*************************************************
    功能： 信号  产生的周期数据
    *************************************************/
    void onData( UHF::IntervalData data );

    /*************************************************
    功能： 信号  产生的PRPS数据
    *************************************************/
    void onData( UHF::PRPSData data );

    /*************************************************
    功能： 信号读取数据失败
    *************************************************/
    void onReadUHFPRPSPRPDDataFailed();

private:
    /*************************************************
    功能： 根据工作模式获取图谱类型
    输入参数：
            eWorkMode -- 工作模式
    返回值：
           图谱类型
    *************************************************/
    MultiServiceNS::SpectrumType spectrumTypeFromWorkMode( UHF::WorkMode eWorkMode );

private:
    UHFServicePrivate* d;
    friend class UHFServicePrivate;
};

#endif // UHFSERVICE_H
