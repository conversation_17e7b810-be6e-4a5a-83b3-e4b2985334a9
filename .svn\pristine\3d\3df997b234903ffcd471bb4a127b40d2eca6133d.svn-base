
#ifndef ROTATEDELETEFILEWIDGETEX_H
#define ROTATEDELETEFILEWIDGETEX_H

#include <QMessageBox>
#include <QMutex>
#include "filelistView/RotateFileListViewEx.h"

class RotateDeleteFileWidgetEx : public RotateFileListViewEx
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    输入参数:
        path -- 路径
        nameFilters -- 文件过滤符（空表示不过滤）
        parent -- 父窗体
    *****************************/
    explicit RotateDeleteFileWidgetEx(const QString& qstrPath, const QStringList& fileList, QWidget* parent = 0, bool bRotate = false);

    /****************************
    功能： 析构函数
    *****************************/
    ~RotateDeleteFileWidgetEx();

    /****************************
    功能： 设置关联文件类型
    比如：删除aaa.t01,则同步删除aaa.dat，aaa.dat是aaa.t01的关联文件
    输入参数:
        strSuffixMain -- 主类型
        listRelatedSuffix -- 关联类型列表
    *****************************/
    void setRelatedSuffix( const QString& strSuffixMain, const QStringList& listRelatedSuffix );

signals:
    /****************************
    信号: 删除当前文件
    *****************************/
    void sigDeleteCurrentFile(const QString& qstrFilePath);

private slots:
    /****************************
    功能： 槽，响应文件选中
    输入参数:
        strFileName -- 文件名
    *****************************/
    void onFileChoosed(int Id, const QStringList& fileList);
private:
    /****************************
    功能： 获取关联文件名
    输入参数:
        strFileName -- 文件名
    *****************************/
    QStringList getRelatedFiles( const QString& strFileName );

private:
    QMutex m_mt4RelatedSuffix;
    QMap<QString, QStringList> m_mapRelatedSuffix;//关联类型
    bool m_bRotate;
};

#endif // ROTATEDELETEFILEWIDGETEX_H
