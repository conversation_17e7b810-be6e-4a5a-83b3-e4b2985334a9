#ifndef PDAFILETOOL_H
#define PDAFILETOOL_H

#include "pdastructdatadefine.h"


class PdaFileTool : public QObject
{
    Q_OBJECT
public:
    explicit PdaFileTool(QObject* parent = 0);
    ~PdaFileTool();

    //根据文件路径获取图谱类型
    ChartType getChartType(const QString& qstrFilePath);

    //根据图谱类型和图谱代码得到相应的描述结果
    QString findStrInfoByType(const QString& qstrType, ChartType eType);

private:
    //获取PRPS的描述信息
    QString findPrpsInfo(const QString& qstrType);
};

#endif // PDAFILETOOL_H
