#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QFileInfo>
#include <QDomDocument>
#include <QSettings>
#include <QTest>
#include <QDebug>
#include "appfontmanager/appfontmanager.h"
#include "translation/Translation.h"

#define CLOUD_CONNECTED_STR "1"
#define CLOUD_UNCONNECTED_STR "0"
const int FONT_SIZE = 20;  // 编辑框字体大小
const int WINDOW_WIDTH = 480;
const int WINDOW_HEIGHT = 854;

MainWindow::MainWindow(QWidget *parent) :
    QMainWindow(parent),
    ui(new Ui::MainWindow)
{
    ui->setupUi(this);

    setWindowFlags(Qt::FramelessWindowHint);    // 设置无标题栏外框
    setFocusPolicy( Qt::StrongFocus );          // 设置焦点策略
    setFixedSize( WINDOW_WIDTH,WINDOW_HEIGHT );
#ifdef Q_WS_QWS
    move(0, WINDOW_HEIGHT);
#endif
    QFont font;
    font.setPointSize(FONT_SIZE);
    setFont(font);
    //layout()->setSpacing(20);
    //设置界面控件
    ui->progressBar->setRange(0, 100);
    ui->progressBar->setValue(0);

    m_pCloudAdaptor = new CloudUpdateAdaptor(this);

    connect(m_pCloudAdaptor, SIGNAL(sigInfoUpdated()), this, SLOT(onUpdateInfo()));
    connect(m_pCloudAdaptor, SIGNAL(sigDownloadProgress(quint32,quint32)), this, SLOT(onDownloadProgress(quint32,quint32)));
    connect(m_pCloudAdaptor, SIGNAL(sigNetworkError(CloudUpdateError)), this, SLOT(onCloudNetworkError(CloudUpdateError)));

    SystemManager* pSysManager = SystemManager::instance();
    qRegisterMetaType<InstallState>("InstallState");
    connect(pSysManager, SIGNAL(sigInstallProcess(quint32,quint32)), this, SLOT(onInstallProcess(quint32,quint32)));
    connect(pSysManager, SIGNAL(sigInstallError(InstallState)), this, SLOT(onInstallError(InstallState)));
    connect(pSysManager, SIGNAL(sigNeedHide()), this, SLOT(onHideWindow()));

    //建立与其它进程的通信连接
    m_localComm = new LocalCom(  );
    if(m_localComm->open())
    {
        connect(m_localComm, SIGNAL(sigQueryDownloadInfo()), m_pCloudAdaptor, SLOT(startGetSoftwareInfo()));
        connect(m_localComm, SIGNAL(sigStartDownload(QString)), m_pCloudAdaptor, SLOT(startGetFileContent()));
        connect(m_localComm, SIGNAL(sigStartInstall(QString)), this, SLOT(onInstallFW(QString)));
    }

#ifdef Q_WS_QWS
    this->hide();
#endif
}

MainWindow::~MainWindow()
{
    delete SystemManager::instance();
    delete m_pCloudAdaptor;
    delete ui;
}

void MainWindow::on_getInfoButton_clicked()
{
    m_pCloudAdaptor->startGetSoftwareInfo();
}

void MainWindow::on_getContentButton_clicked()
{
    m_pCloudAdaptor->startGetFileContent();
}

/*************************************************
功能： 槽，响应软件信息更新事件
输入参数：
*************************************************************/
void MainWindow::onUpdateInfo()
{
    qDebug()<<"MainWindow::onUpdateInfo";

    //ui->textBrowser->append(tr("获取到云端软件信息!"));
    //ui->textBrowser->append(trUtf8("Require SW info from cloud!"));
    qDebug() << SystemManager::instance()->remoteSoftwareInfo().filePath;
    qDebug() << SystemManager::instance()->remoteSoftwareInfo().version;
    qDebug() << SystemManager::instance()->remoteSoftwareInfo().updateCaption;

    quint32 savedCount = SystemManager::instance()->softwareDownloadPregress().nextPackageNo - 1;
    ui->progressBar->setValue(savedCount*100/
                              SystemManager::instance()->softwareDownloadPregress().totalCount);

    //上报软件信息
    m_localComm->sendSoftwareInfoMessage();
    m_localComm->sendProgressMessage();
    if( !SystemManager::instance()->remoteSoftwareInfo().version.isEmpty() )
    {
        m_localComm->sendCloudStateMessage(CLOUD_CONNECTED_STR);
    }
}

/*************************************************
功能： 槽，响应下载进度更新事件
输入参数：
*************************************************************/
void MainWindow::onDownloadProgress(quint32 rcvIndex, quint32 totalCount)
{
    qDebug()<<"MainWindow::onDownloadProgress";
    ui->progressBar->setValue(rcvIndex*100/totalCount);
//    if(rcvIndex == totalCount)
//    {
//        //ui->textBrowser->append(tr("安装包下载完毕!"));
//        ui->textBrowser->append(trUtf8("Finish to download FW!"));
//    }
    m_localComm->sendProgressMessage();
    m_localComm->sendCloudStateMessage(CLOUD_CONNECTED_STR);
}

/*************************************************
功能： 槽，响应云通信出错事件
输入参数：
*************************************************************/
void MainWindow::onCloudNetworkError(CloudUpdateError errorNo)
{
    Q_UNUSED(errorNo);
#if 0
    QString errorMsg;
    switch(errorNo)
    {
    case CloudTimeoutError:
        errorMsg = trUtf8("Error : CloudTimeoutError");
        break;
    case CloudReplyError:
        errorMsg = trUtf8("Error : CloudReplyError");
        break;
    case FileWriteError:
        errorMsg = trUtf8("Error : FileWriteError");
        break;
    default:
        errorMsg = trUtf8("Error : Unknown Error");
        break;
    }

    ui->textBrowser->append(errorMsg);
#endif
    m_localComm->sendCloudStateMessage(CLOUD_UNCONNECTED_STR);

}

void MainWindow::onInstallFW(QString strLanguageIndex)
{
    int iLanguage = strLanguageIndex.toInt();
    if(iLanguage >= LibLanguageFont::SET_EN)
    {
        m_eLanguage = LibLanguageFont::SET_EN;
    }
    else
    {
        m_eLanguage = (LibLanguageFont::LanuageOpition) iLanguage;
    }

    //加载国际化
    Translation::getInstance()->loadQmFile(m_eLanguage);
    Translation::getInstance()->installTranslator();

    onShowWindow();
    //ui->textBrowser->append(tr("开始安装..."));
    ui->textBrowser->append(trUtf8("Start Install..."));
    ui->progressBar->setValue(0);
    ui->recoverButton->setEnabled(false);
    SystemManager::instance()->installAllApp(SystemManager::instance()->remoteSoftwareInfo().version, false);
    ui->retranslateUi(this);
}

/*************************************************
功能： 槽，响应
输入参数：
*************************************************************/
void MainWindow::onHideWindow()
{
    hide();
    move(0, WINDOW_HEIGHT);
}

void MainWindow::onShowWindow()
{
    show();
    move( 0, 0 );
    update();
}

/*************************************************
功能： 槽，响应软件安装进度事件
输入参数：已安装的组件数 总的组件数
*************************************************************/
void MainWindow::onInstallProcess(quint32 installedCount, quint32 totalCount)
{
    onShowWindow();
    ui->progressBar->setValue(installedCount*100/totalCount);
    if(installedCount == totalCount)
    {
        //ui->textBrowser->append(tr("安装成功!"));
        ui->textBrowser->append(trUtf8("Successfully install!"));
    }
    //update();
}

/*************************************************
功能： 槽，响应安装过程出错事件
输入参数：错误码
*************************************************************/
void MainWindow::onInstallError(InstallState stateNo)
{
    QString errorMsg;
    switch(stateNo)
    {
    case FILE_CHECK_ERROR:
        //errorMsg = tr("错误:文件检查出错");
        errorMsg = trUtf8("Error:fail to check file");
        break;
    case PROCESS_OPEN_ERROR:
        //errorMsg = tr("错误:进程启动出错");
        errorMsg = trUtf8("Error:fail to launch progress");
        break;
    case PROCESS_CLOSE_ERROR:
        //errorMsg = tr("错误:进程关闭出错");
        errorMsg = trUtf8("Error:fail to close progress");
        break;
    case EXE_UPDATE_ERROR:
        //errorMsg = tr("错误:组件更新出错");
        errorMsg = trUtf8("Error:fail to update FW");
        break;
    case ROOTFS_UNMATCH_ERROR:
        //errorMsg = tr("错误:文件系统不匹配");
        errorMsg = trUtf8("Error:unmatched file system");
        break;
    case INSTALL_OK:
        break;
    default:
        //errorMsg = tr("错误:未知错误");
        errorMsg = trUtf8("Error:unknown");
        break;
    }
    ui->recoverButton->setEnabled(true);
    ui->textBrowser->append(errorMsg);
}

/*************************************************
功能： 槽，响应安装结束后重启事件
输入参数：NULL
*************************************************************/
void MainWindow::onReboot()
{
    //ui->textBrowser->append(tr("开始重启..."));
    ui->textBrowser->append(trUtf8("Restart..."));
    Translation::getInstance()->loadQmFile(m_eLanguage);
    Translation::getInstance()->installTranslator();
    qDebug()<<"MainWindow::onReboot, m_eLanguage:"<<m_eLanguage;
    QTest::qWait(1000);
#ifdef Q_WS_QWS
    //直接重启
    system("reboot");
#endif
}

void MainWindow::on_recoverButton_clicked()
{
//    ui->textBrowser->append(tr("软件版本%1,开始安装...").arg(
//                                SystemManager::instance()->localInstallVersion()));

    ui->textBrowser->append(trUtf8("SW Version%1,start install...").arg(
                                SystemManager::instance()->localInstallVersion()));
    ui->progressBar->setValue(0);
    SystemManager::instance()->installAllApp(SystemManager::instance()->localInstallVersion(), false );
}
