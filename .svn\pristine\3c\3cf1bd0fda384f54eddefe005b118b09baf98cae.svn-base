﻿/*
* Copyright (c) 2019.4，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：PRPSMLdiagnosis.h
*
* 初始版本：1.0
* 作者：李遥
* 创建日期：2020年7月21日
* 摘要：
*/

#ifndef PRPS_ML_DIAGNOSIS_H
#define PRPS_ML_DIAGNOSIS_H

#include "MLdiagnosisGlobal.h"

/*************************************************
 * 函数功能： 初始化机器学习模型
 * 输入参数：
 *      strModelFile -- 模型文件
 * 返回参数：
 *      true：成功；false：失败
*************************************************************/
ML_DIAGNOSISSHARED_EXPORT bool initPRPSModelML( const char* strModelFile );

/*************************************************
 * 函数功能： 释放prps机器学习模型
 * 返回参数：
 *      true：成功；false：失败
*************************************************************/
ML_DIAGNOSISSHARED_EXPORT bool freePRPSModelML();

/*************************************************
 * 函数功能： PRPS专家诊断
 * 输入参数：
 *      prps -- 输入数据
 *      iPhaseNum -- 输入数据的相位数
 *      iTNum -- 输入数据的周期数
 *      bAutoProcess -- 是否启用自动阈值处理和相位调整
 * 输出参数：
 *      diagResult -- 诊断结论
 * 返回参数：
 *      true：成功；false：失败
*************************************************************/
ML_DIAGNOSISSHARED_EXPORT bool diagnosisByPRPSML( double* prps, int iPhaseNum, int iTNum, bool bAutoProcess, DiagResult* diagResult );

#endif // PRPS_ML_DIAGNOSIS_H

