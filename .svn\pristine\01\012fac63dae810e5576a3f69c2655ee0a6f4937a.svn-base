/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* MsgBox.cpp
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月29日
* 摘要：消息提示框的定义

* 当前版本：1.0
*/
#include <QApplication>
#include <QVBoxLayout>
#include <QPainter>
#include <QBitmap>
#include <QHBoxLayout>
#include <QDesktopWidget>
#include <QApplication>
#include <QDebug>
#include <QTimer>
#include <softkeyboard.h>
#include "Widget.h"
#include "msgbox.h"
#include "appfontmanager/appfontmanager.h"

//MSGBOX大小
const int MSGBOX_WIDTH = 400;
const int MSGBOX_HEIGHT = 240;          // 信息提示框的高宽
//const int DISPLAYED_CHAR_CNT_PER_LINE = 15; //msgbox内容区每行显示的字符个数
//标题栏大小
const int TITLE_HEIGHT = MSGBOX_WIDTH * 0.1325;            // 标题栏的高宽
const int TITLE_FONT_HEIGHT = TITLE_HEIGHT;//标题栏字体高度
const int TITLE_WIDTH = MSGBOX_WIDTH - TITLE_HEIGHT;

//图标大小
const int ICON_SPACING = MSGBOX_WIDTH * 0.05;
const int ICON_WIDTH = TITLE_HEIGHT;
const int ICON_HEIGHT = ICON_WIDTH;

//文字大小
const int TEXT_HEIGHT = MSGBOX_HEIGHT * 0.4375;
const int TEXT_FONT_HEIGHT = TEXT_HEIGHT * 0.35;                //云诊断结果显示存在字体没有适应的情况，故在此修改参数，原数值为0.40
const int TEXT_FONT_HEIGHT_THREE_LINE = TEXT_HEIGHT * 0.2625;   //云诊断结果显示存在字体没有适应的情况，故在此修改参数，原数值为0.30

//按钮大小
const int BUTTON_WIDTH = MSGBOX_WIDTH * 0.2;              // 按键的宽度
const int BUTTON_SPACING = MSGBOX_WIDTH * 0.025;          // 按键的间距
const int BUTTON_HEIGHT = MSGBOX_HEIGHT * 0.25;           // 按键的高度
const int BUTTON_FONT_HEIGHT = BUTTON_HEIGHT * 0.6;      //按钮字体高度
const int BUTTON_BOTTOM = MSGBOX_WIDTH * 0.025;           //按钮底部


const QString BUTTON_STYLE = "QPushButton:focus{border-width:2px;border-color:rgb(54, 133, 203);border-radius:10px;border-style: inset; background:rgb( 115,198,242 );}"
                             "QPushButton{border-width:2px;border-color:rgb(54, 133, 203);border-radius:10px;border-style: outset;}";

class MsgBoxButton : public QPushButton
{
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        button -- 按钮类型
        parent -- 父窗体
    *************************************************************/
    MsgBoxButton( MsgBox::Button button, QWidget* parent = 0 )
        :QPushButton( parent )
    {
        m_button = button;
    }

    /*************************************************
    功能： 获取按钮类型
    *************************************************************/
    MsgBox::Button button() const
    {
        return m_button;
    }
private:
    MsgBox::Button m_button;
};

/*************************************************
功能： 构造函数
输入参数:
    eType -- 类型
    parent -- 父窗体
*************************************************************/
MsgBox::MsgBox(MsgBox::Type eType, QWidget *parent)
    : QDialog(parent)
{
    init(eType);
}

MsgBox::MsgBox( MsgBox::Type eType, QPoint centerPosition, QWidget *parent )
    : QDialog(parent)
    , m_centerPosition(centerPosition)
{
    init(eType);
}

MsgBox::~MsgBox()
{
    qDebug() << "MsgBox::~MsgBox: " << this;
    SoftKeyBoard::setAttribute( KEYBOARD_SHOW_PROPERTY, SoftKeyBoard::SHOW_KEY_BOARD );
}

/*************************************************
功能： 重新设定font，匹配屏幕高度、宽度等
*************************************************************/
void MsgBox::resizeTextFont()
{
//    QFont fontContent = m_plabelText->font();
//    const int iTextWidth = m_plabelText->width();

//    QFont fontOneLine = getReheightFont( fontContent, TEXT_FONT_HEIGHT );
//    QFont fontThreeLines = getReheightFont( fontContent, TEXT_FONT_HEIGHT_THREE_LINE );
//    QFontMetrics fmOneLine( fontOneLine );
//    QFontMetrics fmThreeLines( fontThreeLines );
//    QFont fontNow;

//    if( fmOneLine.width(m_plabelText->text()) >= 2 * iTextWidth)
//    {
//        if( fmThreeLines.width(m_plabelText->text()) < 2 * iTextWidth )
//        {
//            //公式做一定缩进，是因为font算宽度并不是十分精确
//            fontNow = getRewidthFont( fontContent, m_plabelText->text(), 1.9 * iTextWidth );
//        }
//        else
//        {
//            fontNow = fontThreeLines;
//        }
//    }
//    else
//    {
//        fontNow = fontOneLine;
//    }

//    fontNow.setFamily(AppFontManager::instance()->getAppCurFontFamily());

//    m_plabelText->setFont( fontNow );
}

/*************************************************
功能： 响应按钮点击
*************************************************************/
void MsgBox::onButtonClicked()
{
    MsgBoxButton* pbutton = dynamic_cast<MsgBoxButton*>(sender());

    if( NULL != pbutton )
    {
        if ( m_bRotate )
        {
            //旋转后时需要做这种特殊处理，不然出现程序卡死
            if( this->focusWidget() == (QWidget *)m_pbutOk )
            {
                accept();
            }
            else
            {
                reject();
            }
        }
        else
        {
            done( pbutton->button() );
        }

        //close();                  //No need to add close
    }
}

/*************************************************
功能： 设置信息
输入参数:
    title -- 标题
    text -- 内容
    buttons -- 显示的按钮
*************************************************************/
void MsgBox::setInfo( const QString& title,
                      const QString& text,
                      MsgBox::Buttons buttons )
{
    m_bRotate = false;

    if(title.isEmpty())
    {
        m_plabelTitle->setHidden(true);
    }
    else
    {
        m_plabelTitle->setVisible(true);
        m_plabelTitle->setText(QString("  ") + title); // 标题信息
    }

    //设置提示信息
    m_pTextBrowser->setText(text);
    //resizeTextFont();

    if(NOBUTTON == buttons)
    {
        hideButtons();
    }
    else
    {
        showButtons( buttons );
    }

    return;
}

/*************************************************
功能： 设置旋转标识
输入参数:
    bRotate -- 是否旋转
*************************************************************/
void MsgBox::setRotate(bool bRotate)
{
    m_bRotate = bRotate;
}

/*************************************************
功能： 设置焦点
输入参数:
    NULL
*************************************************************/
void MsgBox::setFocusBtn()
{
    m_pbutOk->setFocus();
}

/*************************************************
功能： 设置主动延时关闭操作
输入参数:
    iDelayTime：延时时间
*************************************************************/
void MsgBox::setDelayAcceptTime(int iDelayTime)
{
    QTimer::singleShot(iDelayTime, this, SLOT(accept()));
    return;
}

/*************************************************
功能： 获取对应的按钮
输入参数:
    eButton：按钮类型
*************************************************************/
QPushButton* MsgBox::button(MsgBox::Button eButton)
{
    if (MsgBox::OK == eButton)
    {
        return m_pbutOk;
    }
    else if (MsgBox::CANCEL == eButton)
    {
        return m_pbutCancel;
    }
    else
    {
        return  NULL;
    }
}

/*************************************************
功能： 显示按钮
输入参数：
        buttons -- 显示的按钮
*************************************************************/
void MsgBox::showButtons( Buttons buttons )
{
    m_pbutCancel->setVisible( ( MsgBox::CANCEL & buttons ) );
    m_pbutOk->setVisible( ( MsgBox::OK & buttons ) );
    m_pbutOk->setFocus();
}

/*************************************************
功能： 隐藏按钮
输入参数：NULL
*************************************************************/
void MsgBox::hideButtons()
{
    m_pbutCancel->setHidden(true);
    m_pbutOk->setHidden(true);
    return;
}

/*************************************************
功能： 设置对话框类型
输入参数:
      eType -- 类型
*************************************************************/
void MsgBox::setType( MsgBox::Type eType )
{
    switch( eType )
    {
    case MsgBox::QUESTION:
    {
        m_plabelIcon->setPixmap( QPixmap(":/images/messageBox/question.png") );
    }
        break;
    case MsgBox::INFORMATION:
    {
        m_plabelIcon->setPixmap( QPixmap(":/images/messageBox/infomation.png") );
    }
        break;
    case MsgBox::WARNING:
    {
        m_plabelIcon->setPixmap( QPixmap(":/images/messageBox/warning.png") );
    }
        break;
    case MsgBox::CRITICAL:
    {
        m_plabelIcon->setPixmap( QPixmap(":/images/messageBox/critical.png") );
    }
        break;
    default:
        break;
    }
}

/*************************************************
函数名： paintEvent
输入参数:NULL
输出参数： NULL
返回值： NULL
功能： 绘图事件
*************************************************************/
void MsgBox::paintEvent(QPaintEvent *pEvent)
{
    Q_UNUSED(pEvent);

    QBitmap bitmap(this->size());
    QPainter painter2(&bitmap);
    painter2.setPen(Qt::NoPen);
    painter2.fillRect(bitmap.rect(), Qt::white);
    painter2.setBrush(QColor(0, 0, 0));
    painter2.drawRoundedRect(rect(), 10, 10);
    setMask(bitmap);           // 绘制圆角外框

    QPainter painter(this);
    painter.setPen(Qt::NoPen);
    QLinearGradient linearGradient( QPoint(0,0),QPoint( width(),height() ) );
    linearGradient.setColorAt( 0,QColor( 130,181,238 ));
    linearGradient.setColorAt( 1,QColor( 189,217,247 )); // 设置usb设置整个界面从左至右渐变
    painter.setBrush( QBrush( linearGradient ) );
    painter.drawRect( rect() );   // 绘制整个提示框区域背景色渐变
}

void MsgBox::keyPressEvent(QKeyEvent *e)
{
    if (Qt::Key_Escape == e->key())
    {
        setResult(7);
        close();
    }
    else if (Qt::Key_Return == e->key() || Qt::Key_Enter == e->key())
    {
        if ( m_bRotate )
        {
            if( this->focusWidget() == (QWidget *)m_pbutOk )
            {
                accept();
            }
            else
            {
                reject();
            }
        }
        else
        {
            if( m_pbutOk->hasFocus() )
            {
                accept();
            }
            else
            {
                reject();
            }
        }
    }
    else
    {
        const int iKey = e->key();
        if (Qt::Key_Up == iKey || Qt::Key_Down == iKey
            || Qt::Key_Left == iKey || Qt::Key_Right == iKey)
        {
            if (m_pbutOk == focusWidget())
            {
                if (m_pbutCancel->isVisible())
                {
                    m_pbutCancel->setFocus();
                }
            }
            else if (m_pbutCancel == focusWidget())
            {
                if (m_pbutOk->isVisible())
                {
                    m_pbutOk->setFocus();
                }
            }
        }
    }
}



void MsgBox::showEvent(QShowEvent *pEvent)
{
    setFocusBtn();

    QDialog::showEvent(pEvent);

    if (m_bFisrtShow)
    {
        m_bFisrtShow = false;
        // 如果文字内容太多，高度超了需要重新调整下对话框的大小
        if (m_pTextBrowser->document()->size().height() > m_pTextBrowser->height())
        {
            m_pTextBrowser->setFixedHeight(m_pTextBrowser->document()->size().height());
            adjustSize();
            resize(width(), sizeHint().height());
        }
        int iPadding = (m_pTextBrowser->height() - m_pTextBrowser->document()->size().height()) / 2;
        // 使QTextBrowser中的文字内容居中
        m_pTextBrowser->setStyleSheet(QString("background-color: transparent; padding: %1px 0px %2px 0px").arg(iPadding).arg(iPadding));

        // 将对话框移动到指定的位置
        if (m_centerPosition.isNull())
        {
            moveCenter(this);
        }
        else
        {
            if (m_centerPosition.y() + height() / 2 > WIN_HEIGHT)
            {
                move(m_centerPosition.x() - width() / 2, WIN_HEIGHT - height());
            }
            else
            {
                move(m_centerPosition.x() - width() / 2, m_centerPosition.y() - height() / 2);
            }
        }
    }
}

void MsgBox::init(Type eType)
{
    // 避免手点到QTextBrowser的时候弹出键盘
    qDebug() << "MsgBox::init SoftKeyBoard::setAttribute( KEYBOARD_SHOW_PROPERTY, SoftKeyBoard::SHOW_NONE ): " << this;
    SoftKeyBoard::setAttribute( KEYBOARD_SHOW_PROPERTY, SoftKeyBoard::SHOW_NONE );

    m_bFisrtShow = true;
    setFixedWidth(MSGBOX_WIDTH);
    setMinimumHeight(MSGBOX_HEIGHT);
    //setFixedSize( MSGBOX_WIDTH,MSGBOX_HEIGHT );     // 初始化窗口大小

    setWindowFlags( Qt::FramelessWindowHint|Qt::Dialog );   // 去掉窗体外框
    setAttribute( Qt::WA_DeleteOnClose );                   // 设置关闭析构的属性

    //设置标题，字体加粗，自适应高度
    m_plabelTitle = new QLabel(this);
    QFont fontTitle = m_plabelTitle->font();
    fontTitle.setBold(true);
    fontTitle.setFamily( AppFontManager::instance()->getAppCurFontFamily() );
    fontTitle = getReheightFont( fontTitle, TITLE_FONT_HEIGHT );
    m_plabelTitle->setFont(fontTitle);
    m_plabelTitle->setFixedSize( TITLE_WIDTH, TITLE_HEIGHT );

    //设置提示图片，固定大小
    m_plabelIcon = new QLabel(this);
    m_plabelIcon->setFixedSize( ICON_WIDTH, ICON_HEIGHT );
    m_plabelIcon->setScaledContents(true);


    //提示信息，固定宽度，自动换行
    QFont fontText = AppFontManager::instance()->getAppCurFont();
    fontText.setPixelSize(25);

    m_pTextBrowser = new QTextBrowser(this);
    m_pTextBrowser->setFont(fontText);
    m_pTextBrowser->setFrameShape(QFrame::NoFrame);
    m_pTextBrowser->setStyleSheet("background-color: transparent;");
    m_pTextBrowser->setTextInteractionFlags(Qt::NoTextInteraction);
    m_pTextBrowser->setFocusPolicy(Qt::NoFocus);
    m_pTextBrowser->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTextBrowser->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    // ok键和cancel按键，固定大小，自适应字体
    QFont fontButton(AppFontManager::instance()->getAppCurFontFamily());
    fontButton = getReheightFont( fontButton, BUTTON_FONT_HEIGHT );

    m_pbutCancel = new MsgBoxButton( MsgBox::CANCEL, this );
    m_pbutCancel->resize( BUTTON_WIDTH, BUTTON_HEIGHT);
    m_pbutCancel->setStyleSheet( BUTTON_STYLE );
    m_pbutCancel->setFont( fontButton );
    m_pbutCancel->setText( QObject::trUtf8("Cancel") );

    m_pbutOk = new MsgBoxButton( MsgBox::OK, this );
    m_pbutOk->resize(BUTTON_WIDTH, BUTTON_HEIGHT);
    m_pbutOk->setStyleSheet( BUTTON_STYLE );
    m_pbutOk->setFont( fontButton );
    m_pbutOk->setText( QObject::trUtf8("OK") );

    // 点击信号的绑定
    QObject::connect(m_pbutOk, SIGNAL(clicked()), this, SLOT(onButtonClicked()));
    QObject::connect(m_pbutCancel, SIGNAL(clicked()), this, SLOT(onButtonClicked()));

    //布局
    QHBoxLayout* playoutTitle = new QHBoxLayout;
    playoutTitle->addWidget( m_plabelTitle );
    playoutTitle->setAlignment( Qt::AlignLeft );
    playoutTitle->setContentsMargins( 0, 0, 0, 0 );

    QHBoxLayout* playoutInfo = new QHBoxLayout;
    playoutInfo->addSpacing( ICON_SPACING );
    playoutInfo->addWidget( m_plabelIcon );
    playoutInfo->addWidget( m_pTextBrowser );

    QHBoxLayout* playoutButton = new QHBoxLayout;
    playoutButton->addWidget( m_pbutOk );
    playoutButton->addSpacing( BUTTON_SPACING );
    playoutButton->addWidget( m_pbutCancel );
    playoutButton->setAlignment( Qt::AlignRight );
    playoutButton->setContentsMargins(20,0,40,0);

    QVBoxLayout* pmainLayout = new QVBoxLayout;
    pmainLayout->addLayout( playoutTitle );
    pmainLayout->addLayout( playoutInfo );
    pmainLayout->addLayout( playoutButton );
    pmainLayout->setContentsMargins(0,0,0,BUTTON_BOTTOM);

    setLayout( pmainLayout );

    setType( eType );      //设置类型

    //moveCenter( this );
}

/*************************************************
功能： 直接生成一个对话框
输入参数:
    title -- 标题
    text -- 内容
    buttons -- 显示的按钮
    parent -- 父窗体
*************************************************************/
MsgBox::Button MsgBox::information ( const QString &title,
                                     const QString &text,
                                     QPoint centerPosition,
                                     MsgBox::Buttons buttons,
                                     QWidget * parent )
{
    MsgBox* pBox = new MsgBox( MsgBox::INFORMATION, centerPosition, parent );
    pBox->setInfo( title, text, buttons );
    QTimer::singleShot(CLOSE_TIME_INTERVAL_2000MS, pBox, SLOT(accept()));
    return (MsgBox::Button)pBox->exec();
}

/*************************************************
功能： 直接生成一个对话框
输入参数:
    title -- 标题
    text -- 内容
    buttons -- 显示的按钮
    parent -- 父窗体
*************************************************************/
MsgBox::Button MsgBox::information_EX ( const QString &title,
                                        const QString &text,
                                        const int iTimeInterval,
                                        QPoint centerPosition,
                                        MsgBox::Buttons buttons,
                                        QWidget * parent )
{
    MsgBox* pBox = new MsgBox( MsgBox::INFORMATION, centerPosition, parent );
    pBox->setInfo( title, text, buttons );
    QTimer::singleShot(iTimeInterval, pBox, SLOT(accept()));
    return (MsgBox::Button)pBox->exec();
}

/*************************************************
功能： 直接生成一个对话框(不自动关闭)
输入参数:
    title -- 标题
    text -- 内容
    buttons -- 显示的按钮
    parent -- 父窗体
*************************************************************/
MsgBox::Button MsgBox::informationWithoutAutoAccept ( const QString &title,
                                                      const QString &text,
                                                      QPoint centerPosition,
                                                      MsgBox::Buttons buttons,
                                                      QWidget * parent )
{
    MsgBox* pBox = new MsgBox( MsgBox::INFORMATION, centerPosition, parent );
    pBox->setInfo( title, text, buttons );
    return (MsgBox::Button)pBox->exec();
}

MsgBox::Button MsgBox::question( const QString &title,
                                 const QString &text,
                                 QPoint centerPosition,
                                 MsgBox::Buttons buttons,
                                 QWidget * parent )
{
    MsgBox* pBox = new MsgBox( MsgBox::QUESTION, centerPosition, parent );
    pBox->setInfo( title, text, buttons );
    return (MsgBox::Button)pBox->exec();
}

MsgBox::Button MsgBox::delayQuestion( const QString &title,
                                      const QString &text,
                                      QPoint centerPosition,
                                      MsgBox::Buttons buttons,
                                      QWidget * parent )
{
    MsgBox* pBox = new MsgBox( MsgBox::QUESTION, centerPosition, parent );
    pBox->setInfo( title, text, buttons );
    QTimer::singleShot(CLOSE_TIME_INTERVAL_2000MS, pBox, SLOT(accept()));
    return (MsgBox::Button)pBox->exec();
}

MsgBox::Button MsgBox::saveQuestion(const QString &title, const QString &text, QPoint centerPosition, Buttons buttons, QWidget *parent)
{
    MsgBox* pBox = new MsgBox( MsgBox::INFORMATION, centerPosition, parent );
    pBox->setInfo( title, text, buttons );
    pBox->button(MsgBox::CANCEL)->setText(QObject::tr("Delete"));
    return (MsgBox::Button)pBox->exec();
}

MsgBox::Button MsgBox::warning( const QString &title,
                                const QString &text,
                                QPoint centerPosition,
                                MsgBox::Buttons buttons,
                                QWidget * parent )
{
    MsgBox* pBox = new MsgBox( MsgBox::WARNING, centerPosition, parent );
    pBox->setInfo( title, text, buttons );
    return (MsgBox::Button)pBox->exec();
}

MsgBox::Button MsgBox::critical( const QString &title,
                                 const QString &text,
                                 QPoint centerPosition,
                                 MsgBox::Buttons buttons,
                                 QWidget * parent )
{
    MsgBox* pBox = new MsgBox( MsgBox::CRITICAL, centerPosition, parent );
    pBox->setInfo( title, text, buttons );
    return (MsgBox::Button)pBox->exec();
}
