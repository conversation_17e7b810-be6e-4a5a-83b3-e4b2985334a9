/*
* Copyright (c) 2019.04，南京华乘电气科技有限公司
* All rights reserved.
*
* fucconfigdefine.h
*
* 初始版本：1.0
* 作者：张浪
* 创建日期：2019年04月21日
* 修改日期：2019年04月21日
* 修改人：张浪
* 摘要：功能配置模块自定义结构数据
* 当前版本：1.0
*/

#ifndef FUNC_CONFIG_DEFINE_H
#define FUNC_CONFIG_DEFINE_H

#include <QObject>
#include <QVector>
#include "module_global.h"
#include "global_def.h"

namespace FuncConfigManagerNS
{
const int INVALID_FUNC_ID = -1;
const QString USER_ID = APP_DEFAULT_USER_ID;
const QString USER_PWD = APP_DEFAULT_USER_PWD;

typedef struct _ServerInfo_
{
    QString qstrSrvIp;
    quint16 qui16SrvPort;
    QString qstrUserID;
    QString qstrPwd;
    QString qstrToken;

    _ServerInfo_()
    {
        qstrSrvIp = "";
        qui16SrvPort = 0;
        qstrUserID = "";
        qstrPwd = "";
        qstrToken = "";
    }

}ServerInfo;

typedef struct _PresetMode_
{
    QVector<int> qvtIDs;

    _PresetMode_()
    {
        qvtIDs.clear();
    }

}PresetMode;

typedef struct _FunctionInfo_
{
    int iFuncID;
    QString qstrName;
    bool bEnable;
    int iParentID;
    QVector<int> qvtSubIDs;

    _FunctionInfo_()
    {
        iFuncID = INVALID_FUNC_ID;
        qstrName = "";
        bEnable = false;
        iParentID = INVALID_FUNC_ID;
        qvtSubIDs.clear();
    }

    bool operator==(const _FunctionInfo_ &stOther) const
    {
        return ((this->iFuncID == stOther.iFuncID) && (this->iParentID == stOther.iParentID));
    }

}FunctionInfo;

typedef struct _ConfigInfo_
{
    QString qstrAppName;
    QString qstrFirmVer;
    //QString qstrFirmUpgID;
    QVector<PresetMode> qvtPresetModes;
    QVector<FunctionInfo> qvtFuncInfos;

    _ConfigInfo_()
    {
        qstrAppName = "";
        qstrFirmVer = "";
        //qstrFirmUpgID = "";
        qvtPresetModes.clear();
        qvtFuncInfos.clear();
    }

}ConfigInfo;

enum FunctionID
{
    INVALID = -1,
    TEV = 1000,
    AE = 1010,
    UHF = 1020,
    HFCT = 1030,
    CA = 1040,
    INFRARED = 1050,
    PATROL = 1060,
    ACCESS = 1070,
    AE_WIRELESS = 1080,
    ACCESS_ZJHY = 1071,
    ACCESS_SDLR = 1072,
    ACCESS_BJRZT = 1073,
    ACCESS_SHAF = 1074,
    ACCESS_XACY = 1075,
	ACCESS_TJXJ = 1076,
    ACCESS_JSDKY = 1077,
    CURRENT = 1090,
    NETWORKSET = 1100,
    NET4GSET = 1101,
};

}

#endif // FUNC_CONFIG_DEFINE_H
