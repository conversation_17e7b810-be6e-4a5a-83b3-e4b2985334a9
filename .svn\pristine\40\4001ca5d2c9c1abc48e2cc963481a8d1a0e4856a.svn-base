/*
* Copyright (c) 2016.02，南京华乘电气科技有限公司
* All rights reserved.
*
* PlayBackTitle.cpp
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年3月17日
* 摘要：定义回放显示标题栏

* 当前版本：1.0
*/

#include <QApplication>
#include <QVBoxLayout>
#include <QFontMetrics>
#include <QVector>
#include "PlayBackTitle.h"

//const QString TITLE_TEXT_STYLE = "QLabel{background:rgb(38, 70, 90);color:rgb(255,255,255);border:1px solid red;font-size:23px;font-family:msyh}";
const QString TITLE_TEXT_STYLE = "QLabel{background:rgb(38, 70, 90);color:rgb(255,255,255);border:none;font-size:23px;}";
const quint16 PALYBACK_TITLE_WIDTH = 480;
const quint16 PALYBACK_LABEL_HEIGHT = 65;

#define MAX_LINE 2

/****************************
函数名： PlayBackTitle;
输入参数:
    stationName -- 站名
    deviceName -- 设备名
输出参数：NULL
返回值：NULL
功能： 构造函数
*****************************/
PlayBackTitle::PlayBackTitle(QWidget *parent, const QString &stationName, const QString &deviceName)
    : QWidget( parent )
    , m_qsStationName( stationName )
    , m_qsDeviceName( deviceName )
{
    /*站点名标签初始化*/
    m_pStationLabel = new QLabel( this );                   // 站名显示标签
    m_pStationLabel->setStyleSheet( TITLE_TEXT_STYLE );       // 设置样式
    m_pStationLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);// 设置文本对齐方式
    m_pStationLabel->setFixedWidth(PALYBACK_TITLE_WIDTH);
    m_pStationLabel->setMaximumHeight(PALYBACK_LABEL_HEIGHT);
    //m_pStationLabel->setWordWrap(true);
    setStationName(stationName);

    /*设备名标签初始化*/
    m_pDeviceLabel = new QLabel( this );             // 设备名显示标签
    m_pDeviceLabel->setStyleSheet( TITLE_TEXT_STYLE ); // 设置样式
    m_pDeviceLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);// 设置文本对齐方式
    m_pDeviceLabel->setFixedWidth(PALYBACK_TITLE_WIDTH);
    m_pDeviceLabel->setMaximumHeight(PALYBACK_LABEL_HEIGHT);
    //m_pDeviceLabel->setWordWrap(true);
    setDeviceName(deviceName);

    /*添加竖直布局*/
    QVBoxLayout *layout = new QVBoxLayout( this );      // 添加竖直布局
    layout->addWidget( m_pStationLabel );
    layout->addWidget( m_pDeviceLabel );
    layout->setMargin( 0 );
    layout->setSpacing( 0 );                        // 设置与边框间距和控件之间间距为0

    setLayout( layout );
}

/****************************
函数名： setStationName;
输入参数:
    stationName -- 站名
输出参数：NULL
返回值：NULL
功能： 设置站点名
*****************************/
void PlayBackTitle::setStationName( const QString& stationName )
{
    m_qsStationName = stationName;
    QString qstrTextInfo = m_qsStationName.isEmpty() ? "" : QObject::trUtf8("Substation Name: ") + m_qsStationName;
    qstrTextInfo = getDisplayTextInfo(qstrTextInfo);
    QFontMetrics qfmText(m_pStationLabel->font());
    m_pStationLabel->setText(qfmText.elidedText(qstrTextInfo, Qt::ElideRight, PALYBACK_TITLE_WIDTH));
    return;
}

/****************************
函数名： setStationName;
输入参数:
    deviceName -- 设备名
输出参数：NULL
返回值：NULL
功能： 设置设备名
*****************************/
void PlayBackTitle::setDeviceName( const QString& deviceName )
{
    m_qsDeviceName = deviceName;
    QString qstrTextInfo = m_qsDeviceName.isEmpty() ? "" : QObject::trUtf8("Asset: ") + m_qsDeviceName;
    qstrTextInfo = getDisplayTextInfo(qstrTextInfo);
    QFontMetrics qfmText(m_pDeviceLabel->font());
    m_pDeviceLabel->setText(qfmText.elidedText(qstrTextInfo, Qt::ElideRight, PALYBACK_TITLE_WIDTH));
    return;
}

/****************************
输入参数:
    qstrTextInfo -- 输入的字符串信息
输出参数：NULL
返回值：显示的字符串信息
功能： 获取显示的字符串信息
*****************************/
QString PlayBackTitle::getDisplayTextInfo(QString qstrTextInfo)
{
    if(qstrTextInfo.isEmpty())
    {
        return qstrTextInfo;
    }

    QVector<QString> qvtMultiRowText;
    qvtMultiRowText.clear();

    if(qstrTextInfo.contains('\n'))
    {
        QStringList qstrlstTextInfos = qstrTextInfo.split('\n');
        qstrTextInfo = "";
        for(int i = 0, iSize = qstrlstTextInfos.size(); i < iSize; ++i)
        {
            if(0 != i)
            {
                qstrTextInfo += " ";
            }

            if(!(qstrlstTextInfos[i].isEmpty()))
            {
                qstrTextInfo += qstrlstTextInfos[i];
            }
        }
    }

    QFontMetrics qfmText(m_pStationLabel->font());
    if(qfmText.width(qstrTextInfo) < PALYBACK_TITLE_WIDTH)
    {
        qvtMultiRowText.append(qstrTextInfo);
    }
    else
    {
        QString clipText = "";
        for(int i = 0, iSize = qstrTextInfo.size(); i < iSize; ++i)
        {
            if(qfmText.width(clipText + qstrTextInfo.at(i)) >= PALYBACK_TITLE_WIDTH)  // 判定字符串是否超出范围
            {
                qvtMultiRowText.append(clipText);
                clipText = qstrTextInfo.at(i);
            }
            else
            {
                clipText += qstrTextInfo.at(i);
            }
        }

        if(!clipText.isEmpty())                // 添加最后一组不超出宽度范围的子串
        {
            qvtMultiRowText.append(clipText);
        }
    }

    QString qstrRet = "";
    int iVtSize = qvtMultiRowText.size();
    if(iVtSize <= MAX_LINE)
    {
        for(int i = 0; i < iVtSize; ++i)
        {
            if(0 != i)
            {
                qstrRet += "\n";
            }
            qstrRet += qvtMultiRowText[i];
        }
    }
    else
    {
        for(int i = 0; i < MAX_LINE - 1; ++i)
        {
            qstrRet += qvtMultiRowText[i];
            qstrRet += "\n";
        }

        QString qstrTmpInfo = qvtMultiRowText[MAX_LINE - 1] + "              ";//add space string to display...
        qstrTmpInfo = qfmText.elidedText(qstrTmpInfo, Qt::ElideRight, PALYBACK_TITLE_WIDTH);
        qstrRet += qstrTmpInfo;
    }

    return qstrRet;
}


