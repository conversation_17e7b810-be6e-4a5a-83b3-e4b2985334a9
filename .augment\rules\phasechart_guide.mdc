---
description:
globs:
alwaysApply: false
---
# 相位图表模块指南

## 模块概述
相位图表（PhaseChart）模块提供用于显示和分析相位数据的图表组件。这个模块主要用于可视化周期性数据的相位特性，特别适用于电力系统分析。

## 主要组件
- [phasechartview.h](mdc:phasechart/src/phasechartview.h) - 相位图表视图类
- [phasechartview.cpp](mdc:phasechart/src/phasechartview.cpp) - 相位图表视图实现
- [phasechartlayout.h](mdc:phasechart/src/phasechartlayout.h) - 相位图表布局类
- [phasechartlayout.cpp](mdc:phasechart/src/phasechartlayout.cpp) - 相位图表布局实现
- [phasedatamodel.h](mdc:phasechart/src/phasedatamodel.h) - 相位数据模型
- [phasedatamodel.cpp](mdc:phasechart/src/phasedatamodel.cpp) - 相位数据模型实现

## 数据处理
- [phasedata.h](mdc:phasechart/src/phasedata.h) - 相位数据类
- [phasedata.cpp](mdc:phasechart/src/phasedata.cpp) - 相位数据实现
- [phasevaluehelper.h](mdc:phasechart/src/phasevaluehelper.h) - 相位值辅助类
- [phasevaluehelper.cpp](mdc:phasechart/src/phasevaluehelper.cpp) - 相位值辅助实现

## 核心功能
- 相位数据可视化
- 交互式缩放和平移
- 自定义图表标记
- 多数据集对比
- 导出图表为图像

## 使用方法
要使用相位图表模块，需要：

1. 包含必要的头文件
2. 创建 PhaseChartView 实例
3. 设置数据模型
4. 配置视图选项

示例：
```cpp
#include "phasechartview.h"
#include "phasedatamodel.h"

// 创建视图和模型
PhaseChartView *chartView = new PhaseChartView(this);
PhaseDataModel *dataModel = new PhaseDataModel(this);

// 加载数据
dataModel->loadData(...);

// 设置模型
chartView->setModel(dataModel);

// 配置视图
chartView->setRenderHint(QPainter::Antialiasing);
chartView->setViewportUpdateMode(QGraphicsView::FullViewportUpdate);

// 添加到布局
layout->addWidget(chartView);
```

## 自定义
相位图表提供多种自定义选项：
- 调整颜色方案
- 修改网格样式和密度
- 自定义坐标轴标签和范围
- 设置数据点样式和大小

## 示例
查看 `phasechart/demos/` 目录获取更多使用示例
