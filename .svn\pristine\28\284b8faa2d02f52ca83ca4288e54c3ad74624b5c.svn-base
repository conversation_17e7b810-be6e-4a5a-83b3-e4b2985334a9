#include "AEPhaseDataSave.h"
#include <QMutexLocker>
#include "datafile/datafile.h"
#include "datafile/ae/aephasedatamap.h"
#include "ae/AEConfig.h"
#include "model/HCStatus.h"
#include "systemsetting/systemsetservice.h"
#include "mapdatafactory.h"

/************************************************
 * 函数名   : AEPhaseDataSave
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 构造函数
 ************************************************/
AEPhaseDataSave::AEPhaseDataSave()
{
    m_pAEPhaseDataInfo = NULL;
    MapDataFactory::registerClass<AEPhaseDataMap>(XML_FILE_NODE_AE_PHASE);
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString AEPhaseDataSave::saveData(void *pData)
{
    if(NULL == pData)
    {
        return NULL;
    }
    m_pAEPhaseDataInfo = (AEPhaseDataInfo*)pData;
    m_strAbsolutePath = getAbsolutePath(m_pAEPhaseDataInfo->stHeadInfo.generationDateTime);

    return saveData( pData, m_strAbsolutePath );
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString AEPhaseDataSave::saveData(void *pData, const QString &qsSavedPath)
{
    QString strSavePath("");
    if(NULL == pData)
    {
        return strSavePath;
    }

    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return strSavePath;
    }

    m_pAEPhaseDataInfo = (AEPhaseDataInfo*)pData;

    QDir dir(qsSavedPath);
    if(!dir.exists())
    {
        dir.mkpath(qsSavedPath);
    }

    DataFile *pFile = new DataFile;

    setFileHeads(pFile, m_pAEPhaseDataInfo->stHeadInfo);

    addAEPhaseMap(pFile);

    if(!(pFile->save(qsSavedPath, AE_PHASE_FILE_NAME_SUFFIX, strSavePath)))
    {
        QFile file(strSavePath);
        file.remove();
        strSavePath = "";
    }

    delete pFile;
    pFile = NULL;
    logDebug(strSavePath);
    //添加文件备注信息
    RemarkInfoManager::instance()->saveRemarkInfo(strSavePath, m_pAEPhaseDataInfo->stHeadInfo.qstrRemark);
    return strSavePath;
}

void AEPhaseDataSave::addAEPhaseMap(DataFile *pFile)
{
    AEPhaseDataMap *pMap = new AEPhaseDataMap;
    setMapHead(pMap);
    setMapInfo(pMap);
    setMapData(pMap);
    pFile->addMap(pMap);
}

void AEPhaseDataSave::setMapHead(DataMap *pMap)
{
    pMap->setGenerationTime(m_pAEPhaseDataInfo->stHeadInfo.generationDateTime);
    pMap->setSpectrumProperty(m_pAEPhaseDataInfo->stHeadInfo.eMapProperty);
    pMap->setDeviceName(m_pAEPhaseDataInfo->stHeadInfo.strDeviceName);
    pMap->setDeviceNumber(m_pAEPhaseDataInfo->stHeadInfo.strDeviceNumber);
    pMap->setTestPointName(m_pAEPhaseDataInfo->stHeadInfo.strTestPointName);
    pMap->setTestPointNumber(m_pAEPhaseDataInfo->stHeadInfo.strTestPointNumber);
    pMap->setTestLocation(m_pAEPhaseDataInfo->stHeadInfo.eTestLocation);
    pMap->setTestChannelSign(m_pAEPhaseDataInfo->stHeadInfo.ucTestChannelSign );
    pMap->setDataPrimitiveType(m_pAEPhaseDataInfo->stHeadInfo.eDataPrimitiveType);
    pMap->setRemark(m_pAEPhaseDataInfo->stHeadInfo.qstrRemark);
}

void AEPhaseDataSave::setMapInfo(AEPhaseDataMap *pMap)
{
    AEMapNS::AEPhaseMapInfo stInfo;
    stInfo.eAmpUnit = m_pAEPhaseDataInfo->eDataUnit;
    stInfo.fAmpLowerLimit = m_pAEPhaseDataInfo->fAmpLowerLimit;//0;
    stInfo.fAmpUpperLimit = AE::Y_RANGE_VALUES[m_pAEPhaseDataInfo->gain()];
    stInfo.eTransformerType = m_pAEPhaseDataInfo->eTransformerType;//AEMapNS::AE_TRANSFORMER_UNRECORD;
    stInfo.iDataPointNum = m_pAEPhaseDataInfo->iDataPointNum;
    memcpy(stInfo.ucaDischargeTypeProb, m_pAEPhaseDataInfo->ucaDischargeTypeProb, sizeof(stInfo.ucaDischargeTypeProb));
    stInfo.fTrigAmp = m_pAEPhaseDataInfo->fTrigThrhd;
//    stInfo.usGatingTime = m_pAEPhaseDataInfo->usGatingTime;// / 1000;//us->ms TODO
    stInfo.usShutTime = m_pAEPhaseDataInfo->usShutTime *1000;//ms->us
//    stInfo.usMaxTimeInterval = m_pAEPhaseDataInfo->usMaxTimeInterval;
    stInfo.eGainType = m_pAEPhaseDataInfo->eGainType;
//    stInfo.eGainFactor = m_pAEPhaseDataInfo->eGainFactor;
    stInfo.sGain = m_pAEPhaseDataInfo->sGainValue;
    dbg_info("stInfo.sGain is %d\n", stInfo.sGain);
    stInfo.eSyncSource = (DataFileNS::SyncSource)(m_pAEPhaseDataInfo->eSyncSource + 1);//DataFileNS::SYNC_SOURCE_POWER;
    stInfo.ucSyncState = m_pAEPhaseDataInfo->ucSyncState;//0x01;
    stInfo.fSyncFreq = m_pAEPhaseDataInfo->fSyncFreq;//-1;
    stInfo.fMinValue = m_pAEPhaseDataInfo->fMinValue;
    stInfo.fMaxValue = m_pAEPhaseDataInfo->fMaxValue;
    stInfo.fAverageValue = m_pAEPhaseDataInfo->fAverageValue;
    stInfo.fDeviation = m_pAEPhaseDataInfo->fDeviation;
    pMap->setInfo(&stInfo);
}

void AEPhaseDataSave::setMapData(AEPhaseDataMap *pMap)
{
    int iDataPointNum = m_pAEPhaseDataInfo->iDataPointNum;
    dbg_info("iDataPointNum is %d\n", iDataPointNum);

    //void *pData = NULL;
    float *pfData = new float[2*iDataPointNum];
    for( int i = 0; i < iDataPointNum; ++i )
    {
		//TO CONFIRM
        pfData[2*i] = m_pAEPhaseDataInfo->stAEPhaseData[i].fPhaseValue;// + m_pAEPhaseDataInfo->fPhaseShift;
        pfData[2*i +1] = m_pAEPhaseDataInfo->stAEPhaseData[i].fPeakValue;

    }
    pMap->setData(pfData, m_pAEPhaseDataInfo->ucaAEPhaseDataColor,
                  m_pAEPhaseDataInfo->usaAEPhaseColorIndex, iDataPointNum);

    delete[] pfData;
}


INT32 AEPhaseDataSave::getDataFromFile(const QString& strFileName, void *pData)
{
    DataFile *psDataFile = new DataFile;

    //step1 open data file
    bool isSuccess = psDataFile->open(strFileName);
    if(isSuccess == false)
    {
        qDebug() << "!!!!!!!!!!!!!OPen file failed";
        delete psDataFile;
        return HC_FAILURE;
    }

    m_pAEPhaseDataInfo = (AEPhaseDataInfo*)pData;
    //获取文件头信息
    m_pAEPhaseDataInfo->stHeadInfo.strSubstationName = psDataFile->stationName();

    AEPhaseDataMap * pMap = dynamic_cast <AEPhaseDataMap*>(psDataFile->dataMap(DataFileNS::SPECTRUM_CODE_AE_PHASE));
    if( NULL == pMap )
    {
        delete psDataFile;
        return HC_FAILURE;
    }
    //设置头部信息
    pMap->getDataType( m_pAEPhaseDataInfo->stHeadInfo.eDataPrimitiveType );
    pMap->getDeviceName( m_pAEPhaseDataInfo->stHeadInfo.strDeviceName );
    pMap->getDeviceNumber( m_pAEPhaseDataInfo->stHeadInfo.strDeviceNumber );
    pMap->getTestPointName( m_pAEPhaseDataInfo->stHeadInfo.strTestPointName );
    pMap->getTestPointNumber( m_pAEPhaseDataInfo->stHeadInfo.strTestPointNumber );
    QString strDateTime;
    pMap->getMapGenerationTime( strDateTime );
    m_pAEPhaseDataInfo->stHeadInfo.generationDateTime = QDateTime::fromString( strDateTime, "yyyy-MM-dd hh:mm:ss" );
    pMap->getMapProperty( m_pAEPhaseDataInfo->stHeadInfo.eMapProperty );
    pMap->getTestChannelSign( m_pAEPhaseDataInfo->stHeadInfo.ucTestChannelSign );
    //m_pAEPhaseDataInfo->stHeadInfo.ucTestChannelSign--;

    //设置图谱信息
    AEMapNS::AEPhaseMapInfo stMapInfo;
    pMap->getInfo(&stMapInfo);
    m_pAEPhaseDataInfo->eTransformerType = stMapInfo.eTransformerType;
    m_pAEPhaseDataInfo->sGainValue = (GainValue)stMapInfo.sGain;
    m_pAEPhaseDataInfo->fTrigThrhd = stMapInfo.fTrigAmp;
//    m_pAEPhaseDataInfo->usGatingTime = stMapInfo.usGatingTime;
    m_pAEPhaseDataInfo->eSyncSource = (SyncSource)(stMapInfo.eSyncSource - 1);
    m_pAEPhaseDataInfo->ucSyncState = stMapInfo.ucSyncState;
    m_pAEPhaseDataInfo->usShutTime = stMapInfo.usShutTime / 1000;
//    m_pAEPhaseDataInfo->usMaxTimeInterval = stMapInfo.usMaxTimeInterval;
    m_pAEPhaseDataInfo->iDataPointNum = stMapInfo.iDataPointNum;

    //设置数据
    int iDataNum = m_pAEPhaseDataInfo->iDataPointNum;
    float afPhaseData [2*AEPULSEMAXNUM];     //1000个点的数值
    memset(afPhaseData, 0x0, sizeof(float)*2*AEPULSEMAXNUM);
    memset(m_pAEPhaseDataInfo->stAEPhaseData,0x0,sizeof(AEPhasePeakData)*AEPULSEMAXNUM);
	
    quint8 ucaPhaseDataColor[3*AEPULSEMAXNUM];     //数据点的颜色
    memset(ucaPhaseDataColor, 0x0, sizeof(quint8)*3*AEPULSEMAXNUM);
    memset(m_pAEPhaseDataInfo->ucaAEPhaseDataColor,0x0,sizeof(quint8)*3*AEPULSEMAXNUM);

    quint16 usaColorIndex[AEPULSEMAXNUM];     //数据点放电次数
    memset(usaColorIndex, 0x0, sizeof(quint16)*AEPULSEMAXNUM);
    memset(m_pAEPhaseDataInfo->usaAEPhaseColorIndex,0x0,sizeof(quint16)*AEPULSEMAXNUM);

    if(pMap->getData(afPhaseData, ucaPhaseDataColor, usaColorIndex, iDataNum))
    {

        for(int i = 0; i < iDataNum; i ++)
        {
            m_pAEPhaseDataInfo->stAEPhaseData[i].fPhaseValue = afPhaseData[2*i];
            m_pAEPhaseDataInfo->stAEPhaseData[i].fPeakValue = afPhaseData[2*i +1];
        }
    }
    memcpy(m_pAEPhaseDataInfo->ucaAEPhaseDataColor,ucaPhaseDataColor,sizeof(quint8)*3*iDataNum);
    memcpy(m_pAEPhaseDataInfo->usaAEPhaseColorIndex,usaColorIndex,sizeof(quint16)*iDataNum);

    delete psDataFile;

    return HC_SUCCESS;

}

/************************************************
 * 函数名   : getStringFromData
 * 输入参数 : pDatas: 数据; uiCounts: 数据个数
 * 输出参数 : NULL
 * 返回值   : 转换后的字符串
 * 功能     : 将数据转成base64的字符串
 ************************************************/
QString AEPhaseDataSave::getStringFromData( void *pDatas, UINT32 uiCounts)
{
    if(NULL == pDatas)
    {
        return NULL;
    }
    float *pfData = (float*)pDatas;

    QByteArray baDatas = QByteArray::fromRawData( (char*)pfData, uiCounts*sizeof(float)/sizeof(char) );

    QString strData = baDatas.toBase64();

    return strData;
}

/************************************************
 * 函数名   : saveExtInformation
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储可扩展信息
 ************************************************/
void AEPhaseDataSave::saveExtInformation(XMLDocument& doc)
{
    Q_UNUSED(doc);
#if 0
    //站名
    doc.setValue("SubstationName",m_pAEPhaseDataInfo->strSubstationName);
    //被测试设备名称
    doc.setValue("TestedDevName",m_pAEPhaseDataInfo->strTestedDevName);
    //采样时间
    doc.setValue("SampleTime",convertTimeToSampleTimeFormat(m_pAEPhaseDataInfo->dateTime));
    //采样率
    doc.setValue("SamplingRate",QString::number(m_pAEPhaseDataInfo->uiSamplingRate));
    //AD量程
    doc.setValue("ADRange",QString::number(m_pAEPhaseDataInfo->uiADRange));
    //AD采样位数
    doc.setValue("ADSampling",QString::number(m_pAEPhaseDataInfo->ucADSampling));
    //AD采样数据格式
    doc.setValue("ADSamplingDataFormat",QString::number(m_pAEPhaseDataInfo->eDataFormat));
    //数据单位
    doc.setValue("DataUnit",QString::number(m_pAEPhaseDataInfo->eDataUnit));
    //超声通道类型
    doc.setValue("AEChannel",QString::number(m_pAEPhaseDataInfo->eAEChannel));
    //放大器放大倍数
    doc.setValue("AmplifierAmplification",QString::number(m_pAEPhaseDataInfo->eGainValue));
    //同步方式
    doc.setValue("SyncMode",QString::number(m_pAEPhaseDataInfo->eSyncSource));
    //同步状态
    doc.setValue("SyncStatus",QString::number(m_pAEPhaseDataInfo->eSyncState));
    //触发幅值
    doc.setValue("TriggerAmplitude",QString::number(m_pAEPhaseDataInfo->fTriggerAmplitude));
    //关门时间
    doc.setValue("BlockingTime",QString::number(m_pAEPhaseDataInfo->uiBlockingTime));
    //相位偏移(来源手持设备)
    doc.setValue("PhaseShift",QString::number(m_pAEPhaseDataInfo->fPhaseShift));
    //相位偏移(来源上位机软件)
    doc.setValue("SoftWarePhaseShift",QString::number(m_pAEPhaseDataInfo->fSoftWarePhaseShift));
#endif
}

/************************************************
 * 函数名   : saveRawData
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储数据部分
 ************************************************/
void AEPhaseDataSave::saveRawData(XMLDocument& doc)
{
    Q_UNUSED(doc);
//    QMutexLocker locker(&m_mutex);
//    memset(m_afPhaseValue, 0x0, sizeof(float)*AEPULSEMAXNUM);
//    memset(m_afPeakValue, 0x0, sizeof(float)*AEPULSEMAXNUM);
//    for(int i=0; i<AEPULSEMAXNUM; i++)
//    {
//        m_afPhaseValue[i] = m_pAEPhaseDataInfo->stAEPhaseData[i].fPhaseValue;
//        m_afPeakValue[i] = m_pAEPhaseDataInfo->stAEPhaseData[i].fPeakValue;
//    }
//    doc.setValue("DataX", getStringFromData(m_afPhaseValue, AEPULSEMAXNUM));
//    doc.setValue("DataY", getStringFromData(m_afPeakValue, AEPULSEMAXNUM));
}

/************************************************
 * 函数名   : organizeData
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 组织数据文件信息
 ************************************************/
 void AEPhaseDataSave::organizeData(XMLDocument& doc)
 {
     QDomElement root = doc.documentElement();
     //Head
     QDomElement elementHead = doc.addElement("Head");
     doc.beginElement(elementHead);
     saveHeadData(doc);

     //ExtInformation
     doc.beginElement( root );
     QDomElement elementExtInformation = doc.addElement("ExtInformation");
     doc.beginElement(elementExtInformation);
     saveExtInformation(doc);

     //Data
     doc.beginElement(root);
     QDomElement elementData = doc.addElement("Data");
     doc.beginElement(elementData);
     saveRawData(doc);

     //CRC
     doc.beginElement(root);
     saveCRC(doc);
 }

 /************************************************
  * 函数名   : parseData
  * 输入参数 : baData: 数据
  * 输出参数 : pData: 解析到的数据
  * 返回值   : void
  * 功能     : 解析数据
  ************************************************/
 void AEPhaseDataSave::parseData(const QByteArray& baData, void *pData, const QString &strFileName)
 {
     Q_UNUSED(baData);
     Q_UNUSED(pData);
     Q_UNUSED(strFileName);
#if 0
     Q_UNUSED(strFileName);
     QMutexLocker locker(&m_mutex);
     m_pAEPhaseDataInfo = (AEPhaseDataInfo*)pData;

     XMLDocument doc(baData);

     doc.beginElement("ExtInformation");
     m_pAEPhaseDataInfo->strSubstationName = doc.value("SubstationName");
     m_pAEPhaseDataInfo->strTestedDevName = doc.value("TestedDevName");
     m_pAEPhaseDataInfo->dateTime = convertToDateTime(doc.value("SampleTime"));
     m_pAEPhaseDataInfo->uiSamplingRate = doc.value("SamplingRate").toUInt();
     m_pAEPhaseDataInfo->uiADRange = doc.value("ADRange").toUInt();
     m_pAEPhaseDataInfo->ucADSampling = (UINT8)(doc.value("ADSampling").toUInt());
     m_pAEPhaseDataInfo->eDataFormat = (ADSampleDataFormat)(doc.value("ADSamplingDataFormat").toInt());
     m_pAEPhaseDataInfo->eDataUnit = DataUnit(doc.value("DataUnit").toInt());
     m_pAEPhaseDataInfo->eAEChannel = AE_CHANNEL(doc.value("AEChannel").toInt());
     m_pAEPhaseDataInfo->eGainValue = GainValue(doc.value("AmplifierAmplification").toInt());
     m_pAEPhaseDataInfo->eSyncSource = SyncSource(doc.value("SyncMode").toInt() - 1);
     m_pAEPhaseDataInfo->eSyncState = SyncState(doc.value("SyncStatus").toInt());
     m_pAEPhaseDataInfo->fTriggerAmplitude = doc.value("TriggerAmplitude").toFloat();
     m_pAEPhaseDataInfo->uiBlockingTime = doc.value("BlockingTime").toUInt();
     m_pAEPhaseDataInfo->fPhaseShift = doc.value("PhaseShift").toFloat();
     m_pAEPhaseDataInfo->fSoftWarePhaseShift = doc.value("SoftWarePhaseShift").toFloat();
     doc.endElement();

     doc.beginElement("Data");
     memset(m_afPhaseValue, 0x0, sizeof(float)*AEPULSEMAXNUM);
     memset(m_afPeakValue, 0x0, sizeof(float)*AEPULSEMAXNUM);
     memset(m_pAEPhaseDataInfo->stAEPhaseData,0x0,sizeof(AEPhasePeakData)*AEPULSEMAXNUM);

     QByteArray baPhaseData = QByteArray::fromBase64(doc.value("DataX").toLatin1());
     memcpy(m_afPhaseValue,(float*)(baPhaseData.data()),baPhaseData.count());
     QByteArray baPeakData = QByteArray::fromBase64(doc.value("DataY").toLatin1());
     memcpy(m_afPeakValue,(float*)(baPeakData.data()),baPeakData.count());

     for(int i=0; i<AEPULSEMAXNUM; i++)
     {
         m_pAEPhaseDataInfo->stAEPhaseData[i].fPhaseValue = m_afPhaseValue[i];
         m_pAEPhaseDataInfo->stAEPhaseData[i].fPeakValue = m_afPeakValue[i];
     }

     doc.endElement();
#endif
 }

/************************************************
 * 函数名   : getDataTypeFolder
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件夹名
 * 功能     : 获取图谱类型文件存储最上层文件夹名
 ************************************************/
QString AEPhaseDataSave::getDataTypeFolder(void)
{
    return AE_PHASE_FOLDER;
}

/************************************************
 * 函数名   : getFileNameSuffix
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件后缀名
 * 功能     : 获取数据文件后缀名
 ************************************************/
QString AEPhaseDataSave::getFileNameSuffix(void)
{
    return AE_PHASE_FILE_NAME_SUFFIX;
}
