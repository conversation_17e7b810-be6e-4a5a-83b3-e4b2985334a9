/*
 * Copyright (c) 2019.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：dataspecificationutils.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/11/04
 * 摘要：数据规范工具类
 * 当前版本：1.0
 */

#pragma once

#include <QString>
#include <QDataStream>
#include "dataspecification_global.h"
#include "dataspecification_def.h"

namespace DataSpecificationNS
{
    class DATASPECIFICATIONSHARED_EXPORT DataSpecificationUtils
    {
    public:
        DataSpecificationUtils();
        ~DataSpecificationUtils();

        /************************************************
         * 函数名   : saveBinaryPointerData
         * 输入参数 :
           const DataSpecificationNS::StorageDataType eStorageDataType: 数据保存类型
           void* pData: 数据
           const int iDataSize: 数据大小
         * 输出参数 :
           QDataStream& out: 二进制数据流
         * 返回值   : void
         * 功能     : 保存二进制指针数据
         ************************************************/
        static void saveBinaryPointerData(QDataStream& out, const DataSpecificationNS::StorageDataType eStorageDataType, void* pData, const int iDataSize);

        /************************************************
         * 函数名   : parseBinaryPointerData
         * 输入参数 :
           QDataStream& in: 二进制数据流
           const DataSpecificationNS::StorageDataType eStorageDataType: 数据保存类型
           const int iDataSize: 数据大小
         * 输出参数 :
           QByteArray& qbaData: 数据
         * 返回值   : void
         * 功能     : 解析二进制指针数据
         ************************************************/
        static void parseBinaryPointerData(QDataStream& in, const DataSpecificationNS::StorageDataType eStorageDataType, QByteArray& qbaData, const int iDataSize);

        /************************************************
         * 函数名   : saveBinaryReservedData
         * 输入参数 :
           const int iLength: 长度
         * 输出参数 :
           QDataStream& out: 二进制数据流
         * 返回值   : void
         * 功能     : 保存二进制预留数据
         ************************************************/
        static void saveBinaryReservedData(const int iLength, QDataStream& out);

        /************************************************
         * 函数名   : convertStringToBinary
         * 输入参数 : 
           const QString& qstrData: 字符串数据
           const StringType eStringType: 字符串类型
           const quint16 usDataLength: 数据长度
         * 输出参数 :
           QDataStream& out: 二进制数据流
         * 返回值   : void
         * 功能     : 将字符串转换成二进制
         ************************************************/
        static void convertStringToBinary(const QString& qstrData, const StringType eStringType, QDataStream& out, const quint16 usDataLength);

        /************************************************
         * 函数名   : serializeDateTime
         * 输入参数 :
           const QString& qstrDateTime: 日期字符串
         * 输出参数 : NULL
         * 返回值   : qint64
         * 功能     : 序列化日期
         ************************************************/
        static qint64 serializeDateTime(const QString& qstrDateTime);

        /************************************************
         * 函数名   : calcDataBufferSize
         * 输入参数 :
           const quint32 iBufferCount: 数据个数
           const DataSpecificationNS::StorageDataType eStorageDataType: 数据保存类型
         * 输出参数 : NULL
         * 返回值   : int
         * 功能     : 计算数据缓存的占用空间大小
         ************************************************/
        static int calcDataBufferSize(const quint32 iBufferCount, const DataSpecificationNS::StorageDataType eStorageDataType);

        /************************************************
         * 函数名   : getUnicode
         * 输入参数 :
           QDataStream& in: 输入流
           const size_t: 大小
         * 输出参数 : 
           qstrDest: 输出字符串
         * 返回值   : bool
         * 功能     : 读取Unicode字符串
         ************************************************/
        static bool getUnicode(QDataStream& in, const size_t size, QString& qstrDest);

        /************************************************
         * 函数名   : getAscii
         * 输入参数 :
           QDataStream& in: 输入流
           const size_t: 大小
         * 输出参数 :
           qstrDest: 输出字符串
         * 返回值   : bool
         * 功能     : 读取Ascii字符串
         ************************************************/
        static bool getAscii(QDataStream& in, const size_t size, QString& qstrDest);

        /************************************************
         * 函数名   : getUint8Vector
         * 输入参数 :
           QDataStream& in: 输入流
           const size_t: 大小
         * 输出参数 :
           vecValue: 输出vector
         * 返回值   : bool
         * 功能     : 读取uint8类型的vector
         ************************************************/
        static bool getUint8Vector(QDataStream& in, const size_t size, std::vector<quint8>& vecValue);

        /************************************************
         * 函数名   : dischargeTypeProbabilityVector2QString
         * 输入参数 :
           const std::vector<quint8>& vecPDTypePR: 放电类型概率vector
         * 输出参数 : NULL
         * 返回值   : QString
         * 功能     : 放电类型概率vector类型转QString
         ************************************************/
        static QString dischargeTypeProbabilityVector2QString(const std::vector<quint8>& vecPDTypePR);

        static void* createDataBuffer(const quint32 iBufCnt, const StorageDataType eStorageDataType, unsigned long& iBufSize);

        static bool deleteDataBuffer(void **pBuf, const StorageDataType eStorageDataType);

        /************************************************
         * 函数名   : spectrumTypeCodeEnum2QString
         * 输入参数 :
           const SpectrumCode eSpectrumCode: 图谱类型编码
         * 输出参数 : NULL
         * 返回值   : QString
         * 功能     : 图谱类型编码枚举转QString
         ************************************************/
        static QString spectrumTypeCodeEnum2QString(const SpectrumTypeCode eSpectrumCode);
    };
}
