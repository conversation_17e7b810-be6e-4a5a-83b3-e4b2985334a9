/*
* Copyright (c) 2017.03，南京华乘电气科技有限公司
* All rights reserved.
*
* tevpulseviewbase.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年03月14日
* 摘要：TEVPulseView模块接口定义

* 当前版本：1.0
*/

#ifndef TEVPULSEVIEWBASE_H
#define TEVPULSEVIEWBASE_H

#include "tev/tevdefine.h"
#include "tev/TevPulseService.h"
#include "widgets/sampleChartView/SampleChartView.h"
#include "config/ConfigManager.h"
#include "widgets/histogram/HistogramChart.h"

class TEVPulseViewBase : public SampleChartView
{
    Q_OBJECT
public:
    /*************************************************
    函数名： TEVPulseViewBase(const QString &strTitle, QWidget *parent = 0)
    输入参数： strTitle：标题
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit TEVPulseViewBase(const QString &strTitle, QWidget *parent = 0);

    /*************************************************
    函数名： ~TEVPulseViewBase()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~TEVPulseViewBase();

protected:
    /*************************************************
    函数名：service
    输入参数: NULL
    输出参数：NULL
    返回值：service对象
    功能：返回service对象
    *************************************************************/
    TevPulseService* getTevPulseService();

    /*************************************************
    函数名： onSKeyPressed()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应S键事件
    *************************************************************/
    virtual void onSKeyPressed();

protected slots:
    /*************************************************
    函数名： onDataRead(TEV::AmplitudeData data)
    输入参数： data：幅值数据
             id:用户id
    功能： 响应读取的数据
    *************************************************************/
    virtual void onDataRead( TEV::PulseData data,MultiServiceNS::USERID userId ) = 0;

private:
    /*************************************************
    函数名： initDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 初始化数据
    *************************************************************/
    void initDatas();

    /************************************************
     * 函数名   : createChart
     * 输入参数 : parent---父控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建图谱
     ************************************************/
    virtual ChartWidget *createChart(QWidget *parent);
};

#endif // TEVPULSEVIEWBASE_H
