/*
* Copyright (c) 2017.11，南京华乘电气科技有限公司
* All rights reserved.
*
* infraredview.h
*
* 初始版本：1.0
* 作者：夏振伟
* 创建日期：2019年10月31日
* 摘要：红外展示视图

* 当前版本：1.0
*/

#ifndef INFRAREDCABELTESTVIEW_H
#define INFRAREDCABELTESTVIEW_H

#include "view_global.h"
#include "infrared/infraredservice.h"
#include "infrared/rotatedialog.h"
#include "infrared/infraredviewbase.h"
#include "customaccesstask/taskdefine.h"


class VIEWSHARED_EXPORT InfraredCabelTestView : public InfraredViewBase
{
    Q_OBJECT
public:
    /*************************************************
    函数名： InfraredView(QWidget *parent = 0)
    输入参数： parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    InfraredCabelTestView(QWidget *parent = 0);

    /*************************************************
    函数名： ~InfraredView()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~InfraredCabelTestView();

    /*************************************************
    函数名： saveData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 保存数据
    *************************************************************/
    void saveData(bool bSaveJpeg = false);

signals:
    /*************************************************
    功能： 释放红外检测信号
    *************************************************************/
    void sigTested();

    /*************************************************
    功能： 释放退出红外检测信号
    *************************************************************/
    void sigExitTest();

private slots:
    /*************************************************
    函数名： onPlaybackFinished()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应回放结束信号
    *************************************************************/
    void onPlaybackFinished();

    /*************************************************
    函数名： onData(Infrared::InfraredData stData)
    输入参数： data：红外数据
             id -- 用户
    输出参数： NULL
    返回值： NULL
    功能： 响应红外数据信号
    *************************************************************/
    void onData(QSharedPointer<Infrared::InfraredData> qspInfraredData, MultiServiceNS::USERID id);

    /*************************************************
    函数名： onReadDataFail()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应读取数据失败信号
    *************************************************************/
    void onReadDataFail();

    /*************************************************
    函数名： onInfraredInitResult(bool isSuccess)
    输入参数： isSuccess：红外初始化结果
    输出参数： NULL
    返回值： NULL
    功能： 响应红外初始化结果信号
    *************************************************************/
    void onInfraredInitResult(bool isSuccess);

    /*************************************************
    函数名： suspend()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 暂停
    *************************************************************/
    void suspend();

    void onCreateShape();

    /*************************************************
    函数名： resume()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 恢复
    *************************************************************/
    void resume();

    /*************************************************
    函数名： onStopSample()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 停止采集
    *************************************************************/
    void onStopSample();

    /*************************************************
    函数名： onRestartSample()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 重新开始采集
    *************************************************************/
    void onRestartSample();

    /*************************************************
    功能： 响应退出红外检测信号
    *************************************************************/
    void onExitInfrared();

protected:
    /*************************************************
    函数名： showEvent(QShowEvent *e)
    输入参数： e：显示事件
    输出参数： NULL
    返回值： NULL
    功能： 显示事件处理
    *************************************************************/
    void showEvent(QShowEvent *e);

    /*************************************************
    函数名： keyPressEvent(QKeyEvent *e)
    输入参数： e：按键事件
    输出参数： NULL
    返回值： NULL
    功能： 按键事件处理
    *************************************************************/
    void keyPressEvent(QKeyEvent *e);

    /*************************************************
    功能： 处理窗口关闭事件
    *************************************************************/
    void closeEvent(QCloseEvent* event);

private:
    /*************************************************
    函数名： onButtonPressed(UINT8 ucID)
    输入参数： ucID：按钮ID
    输出参数： NULL
    返回值： NULL
    功能： 按钮响应处理
    *************************************************************/
    virtual void onButtonPressed(UINT8 ucID);

    /*************************************************
    函数名： loadData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 载入数据
    *************************************************************/
    void loadData();

    /*************************************************
    函数名： onPressSKey()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 处理S键
    *************************************************************/
    void onPressSKey();

    /*************************************************
    函数名： readDataCtrl()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 控制红外数据读取
    *************************************************************/
    void readDataCtrl();

    /*************************************************
    函数名： deleteData()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 删除数据
    *************************************************************/
    void deleteData();

    /*************************************************
    函数名： disableButtons()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 禁用部分按钮
    *************************************************************/
    void disableButtons();

    /*************************************************
    函数名： enableButtons()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 使能部分按钮
    *************************************************************/
    void enableButtons();

private slots:

    /*************************************************
    函数名： showInitDialog()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 显示初始化对话框
    *************************************************************/
    void showInitDialog();

    /****************************************
     * 功能：响应诊断结果选择
     * **************************************/
    void onSelectDiagRet(CustomAccessTaskNS::DiagnoseType eDiagRet);

    /****************************************
     * 功能：创建更多设置菜单栏
     * **************************************/
    void createMoreConfigBar();

    /****************************************
     * 功能：删除当前文件槽函数
     * **************************************/
    void onDeleteCurrentFile(const QString& qstrFilePath);

private:

    /*************************************************
    函数名： onInitSuccess()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 处理初始化成功结果
    *************************************************************/
    void onInitSuccess();

    /*************************************************
    函数名： onInitFail()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 处理初始化失败结果
    *************************************************************/
    void onInitFail();

private:
    InfraredService *m_pService;
    InfraredControlButtonBar* m_pMoreButtonBars;
    QGraphicsView* m_pMoreButtonBarView;

    bool m_bIsReadData;             // 是否处于冻结状态
    bool m_bIsReadDataBeforePlayback;
    MultiServiceNS::USERID m_iUserId; // 服务模块分配的用户id，用以识别用户

    volatile bool m_bInitFlag;      //true表示正在初始化，false表示完成初始化（成功或失败）
    volatile bool m_bSave;          //true表示正在保存数据

    QString m_strFilePath; //保存路径
    QString m_strSavePicPath;  //保存的图片路径
    bool m_bSwitchNext;
    CustomAccessTaskNS::DiagnoseType m_eDiagRet;
    QStringList m_testFileList;
};

#endif // INFRAREDCABELTESTVIEW_H
