/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* ControlButton.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月7日
* 修改日期：2016年11月25日
* 作者：邵震宇
*       重构
* 摘要：控制按钮基类定义
* 当前版本：1.0
*/
#ifndef CONTROLBUTTON_H
#define CONTROLBUTTON_H
#include <QFrame>
#include <QLabel>
#include <QMouseEvent>
#include <QResizeEvent>
#include <QPaintEvent>
#include <QPainter>
#include <QVBoxLayout>
#include "ControlButtonInfo.h"
#include "widgetglobal.h"
#include "CentralButton.h"

class WIDGET_EXPORT ControlButton : public QFrame
{
    Q_OBJECT
public:
    typedef enum _Type
    {
        UNKNOWN = 0,
        CMD,//命令
        RADIO,//弹出Radio框
        SLIDER,//弹出滑块
    }Type;

public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent -- 父窗体
    *************************************************************/
    explicit ControlButton( QWidget* parent = 0 );

    /*************************************************
    功能： 设置标题
    输入参数:
        strTitle -- 标题
    *************************************************************/
    virtual void setTitle( const QString& strTitle );

    /*************************************************
    功能： 获取标题
    返回值： 标题
    *************************************************************/
    QString title( void ) const;

    /*************************************************
    功能： 设置按钮（用于实例化）
    输入参数:
        pButton -- 按钮
    *************************************************************/
    void setButton( CentralButton* pButton );

    /*************************************************
    功能： 获取实例化按钮
    返回：实例化按钮
    *************************************************************/
    CentralButton* button();

    /*************************************************
    功能： 设置数值
    输入参数:
        strContent -- 值
    *************************************************************/
    virtual void setContent( const QString& strContent );

    /*************************************************
    功能： 获取值内容
    返回值： 值内容
    *************************************************************/
    QString content( void ) const;

    /*************************************************
    功能： 设置类型
    输入参数:
        eType -- 类型
    *************************************************************/
    void setType( Type eType );

    /*************************************************
    功能： 获取类型
    返回值： 类型
    *************************************************************/
    Type type( void ) const;

    /*************************************************
    功能： 设置ID
    输入参数:
        id: ID
    *************************************************************/
    void setID( int id );

    /*************************************************
    功能： 获取ID
    返回值： ID
    *************************************************************/
    int id( void ) const;

    /*************************************************
    功能： 控制按钮激活
    输入参数:
        bActive: 激活状态
    *************************************************************/
    virtual void setActive( bool bActive );

    /*************************************************
    功能： 返回按钮的激活状态
    返回值：
        true -- 激活
        false -- 未激活
    *************************************************************/
    bool isActive() const;

    /*************************************************
    功能： 设置显示文本字体
    输入参数:
        stFont -- 字体
    *************************************************************/
    void setFont(const QFont& stFont);

    /*************************************************
    功能： 返回显示文本字体
    返回值:
        QFont -- 字体
    *************************************************************/
    QFont font();

    /*************************************************
    功能： 响应键盘确认事件
    *************************************************************/
    virtual void onEnterKeyPressed();

    /*************************************************
    功能： 设置按钮大小
    输入参数: usWidth -- 按键宽度
             usHeight -- 按键高度
    *************************************************************/
    virtual void setButtonSize( quint16 usWidth, quint16 usHeight );

    /*************************************************
    功能： 处理鼠标释放事件
    输入参数: pEvent -- 鼠标释放事件
    *************************************************************/
    virtual void processMouseReleaseEvent(QMouseEvent* pEvent);

protected:
    /*************************************************
    功能： 响应取消激活事件
    *************************************************************/
    virtual void onDisactive();
signals:
    /************************************************
     * 功能     : 信号，按键按下
     * 传递参数 :
     *      id -- 按键ID
     ************************************************/
    void sigPressed( int id );
private:
    int m_id;//ID
    Type m_eType;//类型
    bool m_bActive;//是否激活

    CentralButton* m_pButton;//按钮（用于实例化）
    QVBoxLayout* m_playout;//布局
};

#endif // CONTROLBUTTON_H

