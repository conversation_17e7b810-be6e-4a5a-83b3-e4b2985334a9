#include "querydeviceinfocontroller.h"
#include "../appserverdefine.h"
#include "appserverutils.h"
#include "systemsetting/systemsetservice.h"
#include "pda/cloud/qjason/qjson.h"
#include "pda/pda.h"
#include "globalerrprocess.h"
#include "log/log.h"

using namespace errorProcess;
using namespace AppServerNS;

const QString PROTOCOL_VERSION = "1";

QueryDeviceInfoController::QueryDeviceInfoController(QObject *parent) : HttpRequestHandler(parent)
{

}

QueryDeviceInfoController::~QueryDeviceInfoController()
{

}

void QueryDeviceInfoController::service( HttpRequest& request, HttpResponse& response )
{
    Q_UNUSED( request )
    int responseCode = REPLY_SUCCESS_CODE;
    /*
    //判断协议有效性
    long requestTime = request.getParameter( TIME_STAMP_KEY ).toLong();
    if( !AppServerUtils::isTimestampValid( requestTime ) )
    {
        responseCode = SIGNATURE_INVALID_ERR;
    }
    */

    //生成应答
    QJson respJson;//应答的Json内容

    if( REPLY_SUCCESS_CODE == responseCode )
    {
        QJson resultJson;
        SystemSet::FirmwareInfo stFirmInfo;
        SystemSetService::instance()->getFirmwareInfo(stFirmInfo);
        QString strSN = SystemSetService::instance()->getDevSerialNum();
        resultJson.add(SN_KEY, strSN.toLatin1());
        resultJson.add(DEVICE_TYPE_KEY, stFirmInfo.qstrDevModel.toLatin1());
        resultJson.add(DEVICE_SOFT_VERSION_KEY, stFirmInfo.qstrDevSwVersion.toLatin1());

        resultJson.add(PROTOCOL_VERSION_KEY, PROTOCOL_VERSION.toLatin1());
        respJson.add( REPLY_RESULT_KEY, resultJson );
    }

    respJson.add( REPLY_CODE_KEY, QString::number(responseCode).toLatin1() );
    respJson.add( REPLY_MSG_KEY, AppServerUtils::stateMsgByCode( responseCode ).toUtf8() );

    response.write(respJson.unformattedData(), true);       //解决content-length没有的问题
    return;
}
