/*****< hcicomm.h >************************************************************/
/*      Copyright 2000 - 2014 Stonestreet One.                                */
/*      All Rights Reserved.                                                  */
/*                                                                            */
/*  HCICOMM - Serial HCI Driver Implementation for Generic Bluetooth Stack    */
/*            Prototypes and Constants.                                       */
/*                                                                            */
/*  Author:  <PERSON>                                                      */
/*                                                                            */
/*** MODIFICATION HISTORY *****************************************************/
/*                                                                            */
/*   mm/dd/yy  F. Lastname    Description of Modification                     */
/*   --------  -----------    ------------------------------------------------*/
/*   01/25/01  B. Harris      Initial creation.                               */
/******************************************************************************/
#ifndef __HCICOMMH__
#define __HCICOMMH__

#include "HCICommT.h"
#include "HCITypes.h"

   /* Error Return Codes.                                               */
#define HCI_COMM_ERROR_INVALID_DRIVER_INFORMATION (-1)  /* Error that denotes */
                                                        /* that the specified */
                                                        /* Driver Information */
                                                        /* Structure is       */
                                                        /* invalid.           */

#define HCI_COMM_ERROR_INVALID_CALLBACK_INFORMATION (-2)/* Error that denotes */
                                                        /* that the specified */
                                                        /* Callback function  */
                                                        /* pointer is invalid.*/

#define HCI_COMM_ERROR_INVALID_COMM_PORT        (-3)    /* Error that denotes */
                                                        /* that the specified */
                                                        /* COM Port is        */
                                                        /* invalid.           */

#define HCI_COMM_ERROR_INVALID_BAUD_RATE        (-4)    /* Error that denotes */
                                                        /* that the specified */
                                                        /* Baud Rate is       */
                                                        /* invalid.           */

#define HCI_COMM_ERROR_INITIALIZING_COMM_PORT   (-5)    /* Error that denotes */
                                                        /* that an error      */
                                                        /* occurred during COM*/
                                                        /* Port Configuration.*/

#define HCI_COMM_ERROR_INVALID_COMM_DRIVER_ID   (-6)    /* Error that denotes */
                                                        /* Driver ID supplied */
                                                        /* as function        */
                                                        /* argument is NOT    */
                                                        /* Registered with    */
                                                        /* the Library.       */

#define HCI_COMM_ERROR_ERROR_WRITING_PORT       (-7)    /* Error that denotes */
                                                        /* that there was an  */
                                                        /* error writing the  */
                                                        /* specified Data to  */
                                                        /* the COM Port.      */

#define HCI_COMM_ERROR_UNSUPPORTED_PROTOCOL     (-8)    /* Error that denotes */
                                                        /* that the specified */
                                                        /* Protocol is NOT    */
                                                        /* valid.             */

#define HCI_COMM_ERROR_INVALID_DATA             (-9)    /* Error that denotes */
                                                        /* the specified Data */
                                                        /* to be sent was     */
                                                        /* invalid.           */

#define HCI_COMM_ERROR_UNKNOWN                  (-10)   /* Error that is not  */
                                                        /* covered by any     */
                                                        /* other Error Code.  */

#define HCI_COMM_ERROR_UNIMPLEMENTED            (-11)   /* Error that denotes */
                                                        /* the specified      */
                                                        /* function or        */
                                                        /* operation is not   */
                                                        /* implemented.       */


   /* Flag Values used with the HCI_SendCOMMPacket() function.          */
#define HCI_COMM_SEND_PACKET_ASYNCHRONOUS_FLAG  0x00000001 /* Bit Flag that   */
                                                        /* denotes that the   */
                                                        /* Data is to be      */
                                                        /* queued to be sent  */
                                                        /* (if this flag is   */
                                                        /* not specified, the */
                                                        /* function does NOT  */
                                                        /* return until ALL   */
                                                        /* Data bytes have    */
                                                        /* been sent (or an   */
                                                        /* error occurs).     */

   /* The following declared type represents the Prototype Function for */
   /* an HCI COMM Driver Receive Data Callback.  This function will be  */
   /* called whenever a complete HCI Packet has been received.  This    */
   /* function passes to the caller the HCI COMM Driver ID, the Packet  */
   /* that was received and the HCI COMM Driver Callback Parameter that */
   /* was specified when the HCI COMM Driver was Opened.  The caller is */
   /* free to use the contents of the HCI Packet ONLY in the context of */
   /* this callback.  If the caller requires the Data for a longer      */
   /* period of time, then the callback function MUST copy the data into*/
   /* another Data Buffer.  This function is guaranteed NOT to be       */
   /* invoked more than once simultaneously (i.e.  this function DOES   */
   /* NOT have be reentrant).  It should be noted that this function is */
   /* called in the Thread Context of a Thread that the User does NOT   */
   /* own.  Therefore, processing in this function should be as         */
   /* efficient as possible (this argument holds anyway because another */
   /* Packet will not be received while this function call is           */
   /* outstanding).                                                     */
typedef void (BTPSAPI *HCI_COMMDriverCallback_t)(unsigned int COMMDriverID, HCI_Packet_t *HCIPacket, unsigned long CallbackParameter);

   /* this initializes all data required by this file.  Returns: a non  */
   /* zero value if all resources were allocated and configured         */
   /* correctly, or zero otherwise.  This function MUST be called before*/
   /* any other function in this Module or the other functions will     */
   /* fail.                                                             */
int InitializeCOMMList(void);

   /* this releases all resources that are held by this module.  After  */
   /* this function is called, NO other function in this module will    */
   /* succeed until a successful call to the InitializeCOMMList() is    */
   /* completed.  This will also close any Comm ports open.             */
void CleanupCOMMList(void);

   /* The following function is responsible for actually opening the    */
   /* specified COM Port (with attributes present in the required       */
   /* COMMDriverInformation parameter).  This function also accepts the */
   /* Callback function (and parameter) to call when Data is received   */
   /* from the COM Port.  This function returns a Positive, Non-zero    */
   /* Driver ID value that can be used as input to other functions if   */
   /* the COM Port is able to be opened successfully.  If there is an   */
   /* error trying to open the COM Port (or invalid parameters) then    */
   /* this function returns a Negative return code.                     */
int OpenComm(HCI_COMMDriverInformation_t *COMMDriverInformation, HCI_COMMDriverCallback_t DriverCallback, unsigned long DriverCallbackParameter);

   /* The following function is responsible for reconfiguring the       */
   /* currently active HCI Driver.  The Input parameter to this function*/
   /* MUST have been acquired by a successful call to OpenComm().  This */
   /* function accepts the Driver ID of the driver to reconfigured,     */
   /* followed by a BOOLEAN which specifies whether or not any internal */
   /* HCI Driver state machines should be reset (for example BCSP and/or*/
   /* Packet building state machines).                                  */
int ReconfigureComm(unsigned int COMMDriverID, Boolean_t ResetStateMachines, HCI_Driver_Reconfigure_Data_t *DriverReconfigureData);

   /* The following function is responsible for Closing the specified   */
   /* COM Port and cleaning up all resources associated with the COM    */
   /* Port.  After this function is called, the specified COM Driver ID */
   /* will no longer be valid, and cannot be used as input to any of the*/
   /* other functions in this module.  Also, after this function is     */
   /* called, there will be NO Receive Data Callbacks issued.           */
void CloseComm(unsigned int COMMDriverID);

   /* The following function is provided to allow a mechanism for       */
   /* modules to force the processing of incoming COM Data outside of   */
   /* the normal Scheduled Processing.  This mechanism allows apparent  */
   /* blocking functions to be written that wait on HCI Events before   */
   /* returning.                                                        */
   /* * NOTE * This function is only applicable in device stacks that   */
   /*          are non-threaded.  This function has no effect for device*/
   /*          stacks that are operating in threaded environments.      */
void ProcessComm(unsigned int COMMDriverID);

   /* The following function is used to actually Send an HCI Packet out */
   /* the specified COM Port (specified by the COMM Driver ID).  The    */
   /* HCIPacket Parameter is the HCI Packet to send and the Flags       */
   /* argument specifies any additional processing that is required for */
   /* this Transmission.  This function returns a non-zero, non-negative*/
   /* return value if the Transmission was successful, or a negative    */
   /* return value if the data was invalid or unable to be Transmitted. */
int SendCommPacket(unsigned int COMMDriverID, HCI_Packet_t *HCIPacket, unsigned int Flags);

   /* The following function changes the settings for the specified COM */
   /* Port regarding HCI SCO Bandwidth Configuration.  This function    */
   /* does nothing in the HCI UART Driver because the specification does*/
   /* not support dynamic Bandwidth Allocation on the UART Interface at */
   /* this time.  This function returns a zero value if successful      */
   /* (which should always be the case), or a negative return value if  */
   /* there was an error (see Error Codes above).                       */
int ChangeCommSCOConfiguration(unsigned int COMMDriverID, HCI_SCOConfiguration_t SCOConfiguration);

#endif

