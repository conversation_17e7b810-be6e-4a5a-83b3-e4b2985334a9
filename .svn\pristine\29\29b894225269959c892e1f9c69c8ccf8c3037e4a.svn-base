#ifndef AEDISTRIBUTENETTESTVIEW_H
#define AEDISTRIBUTENETTESTVIEW_H

#include <QStackedWidget>
#include <QVector>
#include "SampleChartView.h"
#include "ae/AE.h"
#include "buttonBar/LabelButtonBar.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "ae/AEViewConfig.h"
#include "ae/aeallviewservice.h"
#include "ae/AEAmpService.h"
#include "customaccesstask/taskdefine.h"
#include "customaccessUi/commonitemview/commonitemlistview.h"
#include "ae/aeampview/AEAmpChart.h"
#include "dataspecification/ae/aespectrumdefine.h"
#include "diagnosismgr/diagnosismanager.h"
#include "distributenetaccess/distributenetaccess_def.h"

namespace DataSpecificationNS {
    class DataSpecification;
    class AEAmpSpectrum;
    class AEPhaseSpectrum;
    class AEWaveSpectrum;
    class AEPulseSpectrum;
}

class AEDistributeNetTestView : public SampleChartView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        parent:父控件指针
    *************************************************************/
    explicit AEDistributeNetTestView( const QString& strTitle, QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~AEDistributeNetTestView( );

    /*************************************************
    功能： 设置测点信息
    *************************************************************/
    void setTestInfo(const QString& qstrTaskId, const DistributeNetAccessNS::TestpointInfo& stTestpointInfo, const DistributeNetAccessNS::RealAssetInfo& stRealAssetInfo);

    /*************************************************
    功能： 设置间隔名称
    *************************************************************/
    void setBayName( QString strBayName );

    /*************************************************
    功能： 设置测点名称
    *************************************************************/
    void setTestPointName( QString strTestPointName );

signals:
    void sigAutoSwitch();

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );

    /*************************************************
    功能： 槽，响应诊断结果
    输入参数：
            qspDiagResultInfo -- 诊断结果
    *************************************************************/
    void onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo);

private slots:
    /*************************************************
    功能： 槽，响应通道变化
    输入参数：
            eChannel -- 变化后的通道
    *************************************************************/
    void onChannelChanged( AE::ChannelType eChannel );

protected:
    /*************************************************
    功能： 键盘事件
    输入参数:
        event -- 事件
    *************************************************************/
    void keyPressEvent( QKeyEvent* event );

    /*************************************************
    功能： 响应S键事件
    *************************************************************/
    void onSKeyPressed();

    /*************************************************
     * 功能：诊断数据
     * 输入参数：
     *      data：待诊断的数据
     *      bSave：是否为保存操作的逻辑，缺省为false
     * ***********************************************/
    virtual void diagDataInfo(const AE::AmplitudeData &data, bool bSave = false);

private:
    /*************************************************
    功能： 回放幅值数据
    *************************************************************/
    void playBackAmpData(DataSpecificationNS::AEAmpSpectrum* pAEAmpSpectrum);

    /*************************************************
    功能： 回放幅值数据
    *************************************************************/
    void playBackAmpBGData(DataSpecificationNS::AEAmpSpectrum* pAEAmpSpectrum);

    /*************************************************
    功能： 回放相位数据
    *************************************************************/
    void playBackPhaseData(DataSpecificationNS::AEPhaseSpectrum* pAEPhaseSpectrum);

    /*************************************************
    功能： 回放飞行数据
    *************************************************************/
    void playBackFlyData(DataSpecificationNS::AEPulseSpectrum* pAEPulseSpectrum);

    /*************************************************
    功能： 回放波形数据
    *************************************************************/
    void playBackWaveData(DataSpecificationNS::AEWaveSpectrum* pAEWaveSpectrum);

    /*************************************************
    功能： 切换要显示的图谱
    *************************************************************/
    void switchDisplayedChart();

    /*************************************************
    功能： 设置图谱的参数
    *************************************************************/
    void setChartParameters();

    /*************************************************
    功能： 清除图谱的数据
    *************************************************************/
    void clearChartDatas();

    /*************************************************
    功能： 设置工作模式
    *************************************************************/
    void setWorkMode();

    /*************************************************
    功能： 设置所有工作参数
    *************************************************************/
    void setAllWorkingSet();

    /*************************************************
    功能： 创建超声四个图谱控件
    *************************************************************/
    ChartWidget *createChart(QWidget *parent);

    /*************************************************
    功能： 保存设置
    *************************************************************/
    bool saveConfig();

    /*************************************************
    功能： 触发值按钮的触发值列表根据增益、量纲动态变化
    输入参数：
            eGain -- 增益
            eUnit -- 量纲
    *************************************************************/
    void resetTriggerList( AE::GainType eGain, AE::UnitOption eUnit );

    /*************************************************
    功能： 设置按钮栏数据
    *************************************************************/
    void setButtonBarDatas();

    /*************************************************
    功能： 保存数据
    输入参数：
            stationName -- 变电站名
            deviceName -- 设备名
    返回：
            保存后的文件名
    *************************************************************/
    QString saveDataToFile(const QString &stationName, const QString& deviceName);

    /*************************************************
    功能： 获取数据增益状态
    输入参数：
            fValue -- 幅值
    *************************************************************/
    AE::GainStatus getGainStatus( float fValue );

    /*************************************************
    功能： 更新数据增益状态
    输入参数：
            fValue -- 幅值
    *************************************************************/
    void updateGainStatus( AEAmpChart *pChart, float fValue );

    /*****************************************
     * 功能：更新增益
     * **********************************************/
    void updateGainType();

    /*************************************************
    功能： 判断是否满足触发条件（单次），最大值大于门槛值
    输入参数：
            data -- 采集的数据
    *************************************************************/
    bool isTriggered( const AE::AmplitudeData& data );

    /*************************************************
    功能： 初始化参数
    *************************************************************/
    void initParameters();

    /*************************************************
    功能： 初始化数据
    *************************************************************/
    void initDatas();

    /*************************************************
    功能： 相移相位图谱
    *************************************************************/
    void phaseShiftAEPhaseChart();

    /*************************************************
    功能： 设置采样按钮的文本
    *************************************************************/
    void setSampleBtnText(bool isSampling);

    /*************************************************
    功能： 保存二进制数据文件
    *************************************************************/
    void saveAsBinaryDataFile();

    /*************************************************
    功能： 删除二进制数据文件
    *************************************************************/
    void deleteBinaryDataFile();

    /*************************************************
    功能： 停止采样
    *************************************************************/
    void stopSample();

    /*************************************************
    功能： 启动采样
    *************************************************************/
    void startSample();

    /*************************************************
    功能： 切换更多界面内容
    *************************************************************/
    void switchMoreView();

    /*************************************************
    功能： 创建更多界面
    *************************************************************/
    void createMoreView(PushButtonBar* pButtonBar);

    /*************************************************
    功能： 幅值范围按钮的列表根据增益动态变化
    输入参数：
            eGain -- 增益
    *************************************************************/
    void resetAmpRange(AE::GainType eGain);

    /*************************************************
    功能： 组织二进制里的幅值ext info
    *************************************************************/
    void composeAmpInfo(DataSpecificationNS::AEAmpExtInformation& stAEAmpExtInformation);

    /*************************************************
    功能： 组织二进制里的幅值data
    *************************************************************/
    void composeAmpData(DataSpecificationNS::AEAmpData& stAEAmpData);


    /*************************************************
    功能： 组织二进制里的phase ext info
    *************************************************************/
    void composePhaseInfo(DataSpecificationNS::AEPhaseExtInformation& stAEPhaseExtInformation);

    /*************************************************
    功能： 组织二进制里的fly ext info
    *************************************************************/
    void composeFlyInfo(DataSpecificationNS::AEPulseExtInformation& stAEPulseExtInformation);

    /*************************************************
    功能： 组织二进制里的wave ext info
    *************************************************************/
    void composeWaveInfo(DataSpecificationNS::AEWaveExtInformation& stAEWaveExtInformation);

    /*************************************************
    功能： 限制超声飞行数据幅值在量程范围内
    *************************************************************/
    void limitFlyDataInRange();

    /*************************************************
    功能： 设置四类图谱里显示的数据
    *************************************************************/
    void setChartSampleDatas(const AE::AllViewData &stAllViewData);

    /*************************************************
    功能： 删除二进制文件(带完整路径的文件名)
    *************************************************************/
    bool removeBinaryFile(const QString &strBinaryFilePath);

    /*************************************************
    功能： 回放历史数据
    *************************************************************/
    void loadData();

    /*************************************************
    功能： 回放指定的二进制数据文件(带完整路径的文件名)
    *************************************************************/
    void playbackBinaryFile(const QString &strBinaryFilePath);

    /*************************************************
    功能： 删除指定的二进制数据文件(带完整路径的文件名)
    *************************************************************/
    void deleteSeletedBinaryFile(const QString &strBinaryFilePath);

    /*************************************************
    功能： 回放历史数据时disable相关按钮
    *************************************************************/
    void disableBtnsWhenPlayback();

    /*************************************************
    功能： 启动采样时enable被disable的相关按钮
    *************************************************************/
    void enableBtnsWhenStartSample();

    /*************************************************
    功能： 超声波形图采样周期数类型转换
    *************************************************************/
    AE::SampleTime intSamplePeriod2Enum(int SamplePeriod);

    void displayBG(const QString &strBinaryFilePath);

    /*************************************************
    功能： 通过通道类型获取波形数据的个数上限
    返回值：波形数据的个数上限
    *************************************************************/
    int waveNumByChannel( AE::ChannelType eChannel );

private slots:
    /*************************************************
    功能： 槽，响应读取的数据
    输入参数：
            data -- 幅值数据
    *************************************************************/
    void onDataRead(AE::AmplitudeData data, MultiServiceNS::USERID userId);

    /*************************************************
    功能： 读取到all view data触发的槽函数
    *************************************************************/
    void onDataRead( AE::AllViewData, MultiServiceNS::USERID);

    /*************************************************
    功能： 读取到all view data数据失败触发的槽函数
    *************************************************************/
    void onReadAEAllDataFailed( MultiServiceNS::USERID);

    /*************************************************
    功能： 处理数据列表里选中的数据文件
    *************************************************************/
    void onProcessSeletedBinaryFile(qint32 iIndex);

    /*************************************************
     功能： 槽，响应同步状态变化
     输入参数：
           eSyncState -- 同步状态
    *************************************************************/
    void onSyncStateChanged( Module::SyncState eSyncState );

    /*****************************************
     * 功能：创建按钮栏
     * **********************************************/
    void createButtonBarInfo();

    /*****************************************
     * 功能：初始化按钮栏信息
     * **********************************************/
    void initBtnBarInfo();

private:
    ChartWidget* m_pChart;//图谱
    ConfigInstance* m_pConfig;//配置模块
    AE::GainType m_eGain;//增益
    AEFilter m_eFilter; //带宽

    AE::UnitOption m_eUnit;//量纲类型
    quint16 m_usSpectrum;//频率成分
    AE::ChannelType m_eChannel;//通道

    AEView::Function m_eChartType;//图谱类型
    UINT16 m_usOpenDoorTime;//开门时间 us
    UINT16 m_usCloseDoorTime;//关门时间 ms
    AE::AmpRange m_eAmpRange;//幅值范围
    AE::SampleTime m_eSampleTime;//采集时间
    int m_iTimeInterval; //时间间隔

    AE::TriggerValue m_eTriggerValue;//触发值
    int m_iTriggerValue; //触发值（具体幅值）

    int m_iPhaseShift;//相位总偏移
    Module::SyncState m_eSyncState;//同步状态
    quint16 m_usFlyPulseCnt;//飞行图谱的脉冲计数
    quint16 m_usAEPhasePulseCnt;//相位图谱的脉冲计数
    ControlButton *m_pSampleBtn;//采样按钮
    ControlButton *m_pSaveDataBtn;//保存按钮
    ControlButton *m_pMoreBtn;//更多按钮

    QLabel *m_pBayNameLabel;
    QLabel *m_pTestPointNameLabel;
    QLabel *m_pLoadFileName;//回放文件名

    QString m_strBayName;

    QStackedWidget *m_pChartStackWidget;//存放四类图谱的qstack控件

    QVector<AEMapNS::AEAmpBinaryData> m_vecAmpData;//采样读取到的所有幅值数据
    QVector<AE::PhaseData> m_vecPhaseData;//采样读取到的所有相位数据
    QVector<AE::WaveData> m_vecWaveData;//采样读取到的所有波形数据
    QVector<AE::FlyData> m_vecFlyData;//采样读取到的所有飞行数据

    QString m_qstrTaskId;
    DistributeNetAccessNS::RealAssetInfo m_stRealAssetInfo; // 设备信息
    DistributeNetAccessNS::TestpointInfo m_stTestpointInfo;//测点信息
    QString m_strBinaryFilePath;//测试数据文件路径
    AE::AmplitudeData m_stChartBGData;

    AEAmpService* m_pService;

    QVector<CustomAccessTaskNS::TestData> m_vecTestData;//存放测点下已测的数据信息的容器
    CommonItemListView *m_pFilesItemListView;//测试数据列表界面对象

    //数据文件选定后的操作类型
    typedef enum _ProcessType{
        PROCESS_TYPE_INVALID = -1,
        PROCESS_TYPE_PLAYBACK = 0,//回放指定的数据文件
        PROCESS_TYPE_DELETE_DATA //删除指定的数据文件
    }ProcessType;
    ProcessType m_eListItemProcessType;//数据文件选定后的操作类型

    quint32 m_qui32GainChangedCnt;
    AE::GainStatus m_eGainStatus;
    CustomAccessTaskNS::DiagnoseType m_eDiagRet;
    QString m_qstrDiagDescription;

    ButtonInfo::Info *m_pAmpMoreBtnInfo;
    quint8 m_qui8AmpMoreBtnCnt;

    ButtonInfo::Info *m_pFlyMoreBtnInfo;
    quint8 m_qui8FlyMoreBtnCnt;

    ButtonInfo::Info *m_pPhaseMoreBtnInfo;
    quint8 m_qui8PhaseMoreBtnCnt;

    ButtonInfo::Info *m_pWaveMoreBtnInfo;
    quint8 m_qui8WaveMoreBtnCnt;

    ButtonInfo::Info *m_pBtnBarInfo;
    PushButtonBar* m_pButtonBar;
};

#endif // AEDISTRIBUTENETTESTVIEW_H
