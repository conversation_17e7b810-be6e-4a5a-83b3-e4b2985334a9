#include "SampleChartView.h"
#include <QFontMetrics>
#include "messageBox/msgbox.h"
#include "View.h"
#include "Module.h"
#include "appconfig.h"
#include "appfontmanager/appfontmanager.h"
#include "fileoper/fileoperutil.h"
#include "pda/pdaservice.h"
#include "recordplay/RecordPlayService.h"
#include "fileoper/fileoperutil.h"
#include "loadingView/loadingdialog.h"
#include "widgets/filecommentbox/filecommentbox.h"
#include "multiservice/multiuserservice.h"
#include "rfid/RFIDScanView.h"
#include "rfid/RFIDReadView.h"
#include "window/Window.h"
#include "log/log.h"
#include "global_log.h"

#define TIMER_INVALID_ID     -1
#define RECORD_MIN_INTERVAL     3000
#define RECORD_MAX_INTERVAL     10000
#define RECORD_MAX_TIME         60000 //最大录音时长60s
#define PER_SECOND 1000
const int g_iFileCommentDlgHeight = 247; // 文件备注框对话框高度
const int g_iInvalidUserId = -1;

void pfnWav2MP3(SampleChartView *pUserInfo, QString qstrWavFilePath)
{
    if(pUserInfo)
    {
        pUserInfo->wav2MP3(qstrWavFilePath);
    }

    return;
}


/****************************
功能： 构造函数
输入参数:
    strTitle -- 标题
    parent -- 父窗体
*****************************/
SampleChartView::SampleChartView(const QString& strTitle, QWidget *parent)
    : ChartView(strTitle, parent),
      m_iUserId(g_iInvalidUserId),
      m_pService(NULL),
      m_pMoreConfigView(NULL)
{
    m_bSaveData = false;
    m_bPdaWaiting = false;
    m_bRecording = false;
    m_bStopRecordEnable = false;
    m_bSampling = false;
    m_iAutoSwitchTimerId = TIMER_INVALID_ID;
    m_iAERecordMinTimerId = TIMER_INVALID_ID;
    m_iAERecordMaxTimerId = TIMER_INVALID_ID;
	m_iAERecordMaxId = TIMER_INVALID_ID;
	m_iTimerIDRefresh = TIMER_INVALID_ID;
    m_eType = RecordPlay::AE_RECORD;
	m_eAEType = RecordPlay::AE_AMP;
    m_qui8RealDiagInterval = 0;
}

/*************************************************
功能： 析构函数
*************************************************************/
SampleChartView::~SampleChartView()
{
    if (m_pMoreConfigView)
    {
        delete m_pMoreConfigView;
        m_pMoreConfigView = NULL;
    }

    if(m_iUserId != g_iInvalidUserId && NULL != m_pService)
    {
        m_pService->deleteUser(m_iUserId);
    }

    killAutoSwitchTimer();
    killAERecordMinTimer();
    killAERecordMaxTimer();
}

/*************************************************
功能： 设置service类
输入参数：
        pService -- 服务类
*************************************************************/
void SampleChartView::setService(MultiUserService* pService)
{
    if (NULL != pService)
    {
        m_pService = pService;
    }
}

MultiUserService* SampleChartView::service()
{
    return m_pService;
}

/*************************************************
功能： 增加采集用户
输入参数：
        user -- 采集用户的信息
*************************************************/
void SampleChartView::addUser(const MultiServiceNS::SampleUser& user)
{
    if (NULL != m_pService)
    {
        m_iUserId = m_pService->addUser(user);
    }
}

/*************************************************
功能： 获取采集用户id
返回值：
        MultiServiceNS::USERID -- 采集用户的信息
*************************************************/
MultiServiceNS::USERID SampleChartView::getUserId() const
{
    return m_iUserId;
}

/*************************************************
功能： 开始采集
*************************************************************/
bool SampleChartView::startSampleService()
{
    if (m_pService)
    {
        if (m_pService->startSample(m_iUserId))
        {
            m_bSampling = true;
        }
    }
    return true;
}

/*************************************************
功能： 停止采集
*************************************************************/
bool SampleChartView::stopSampleService()
{
    if (m_pService)
    {
        if (m_pService->stopSample(m_iUserId))
        {
            m_bSampling = false;
        }
    }
    return true;
}

/*************************************************************
 * 功能：是否正在采集
 * 输入参数：
 *         bool：是否正在采集
 * ************************************************************/
bool SampleChartView::isSampling()
{
    return m_bSampling;
}

/*处理msgbox里文本内容过长（目前仅保存成功显示文件名时使用）*/
void SampleChartView::processTooLongMsgText(QString &strText)
{
    int iTextChMaxPerLine = 22;
    QString strTextTmp = strText;
    QString strDisplayedText = "";
    int i = 0;

    while(strTextTmp.size() > iTextChMaxPerLine)
    {
        ++i;
        strDisplayedText += strTextTmp.mid(0, iTextChMaxPerLine);
        strDisplayedText += "<br />";//富文本种\n不起作用，<br />所有文本格式换行均起作用
        strTextTmp = strText.mid(iTextChMaxPerLine * i, strText.size() - iTextChMaxPerLine * i);
    }
    strDisplayedText += strTextTmp;

    strText = strDisplayedText;

    return;
}

/*************************************************
功能：处理云诊断返回的数据
输入参数:
    strText -- 云诊断结果
*************************************************************/
void SampleChartView::processCloudDiagnoiseMsgText(QString &strText)
{
    //LanguageOpition eLanguage  = AppFontManager::instance()->getAppCurLanguage();
    //if(eLanguage != LANGUAGE_CHINESE)
    {
        QStringList qstrlstInfos = strText.split(";");
        strText.clear();
        foreach (QString strTmp, qstrlstInfos)
        {
            if(!strTmp.isEmpty())
            {
                strTmp += ";<br />";//富文本种\n不起作用，<br />所有文本格式换行均起作用
                strText += strTmp;
            }
        }
        //solve the chinese "。", ";", ".", don't display.
        strText = strText.left(strText.lastIndexOf(";"));
        strText += QString(".");
    }
    log_debug("%s", strText.toLatin1().data());
    return;
}

void SampleChartView::setPNGFileNamePath(const QString &strDataFile, QString &strPngFilePath, QString &strPngFile)
{
    if(strDataFile.isEmpty() || strDataFile == NULL)
    {
        return;
    }
    if(!strDataFile.contains('/'))
    {
        return;
    }
    strPngFilePath = strDataFile;
    int iPos = strPngFilePath.lastIndexOf('/');

    strPngFilePath = strPngFilePath.mid(0, iPos);

    QDir dir(strPngFilePath);
    if(!dir.exists())
    {
        dir.mkpath(strPngFilePath);
    }

    strPngFile = strDataFile;
    iPos = strPngFile.lastIndexOf('.');
    strPngFile = strPngFile.mid(0, iPos);
    strPngFile += ".png";
}

/*************************************************
功能： 快速保存
输入参数：
        strTitle -- 标题
        strStationName -- 站点名
        strDeviceName -- 设备名
        strScreenShotPath -- 截屏路径
*************************************************************/
bool SampleChartView::fastSave( const QString& strTitle,
                                const QString& strStationName,
                                const QString strDeviceName,
                                const QString& strScreenShotPath,
                                const QPoint &centerPoint)
{
    Q_UNUSED(strScreenShotPath);
    m_bSaveData = true;
    //m_qSaveTime = QDateTime::currentDateTime();

    QString strDataFile = saveDataToFile(strStationName, strDeviceName);
    QString strPngFile;
    QString strPngFilePath;
    setPNGFileNamePath(strDataFile,strPngFilePath,strPngFile);

    //bool bScreenShot = saveScreenShot( strPngFilePath,strPngFile );

    m_bSaveData = false;
    bool isSuccess = false;
    QFileInfo fileInfo(strDataFile);

    if( strDataFile.isEmpty() )
    {
        MsgBox::informationWithoutAutoAccept(strTitle, QObject::trUtf8("Save failure!"), centerPoint );
        isSuccess = false;
    }
    /*else if( !bScreenShot )
    {
        MsgBox::informationWithoutAutoAccept( strTitle, QObject::trUtf8("Screenshot failed!"), centerPoint );
        isSuccess = false;
    }*/
    else
    {
        QString strText = fileInfo.fileName();
        processTooLongMsgText(strText);
        MsgBox::informationWithoutAutoAccept(strTitle, strText, centerPoint);
        isSuccess = true;
    }

    return isSuccess;
}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void SampleChartView::keyPressEvent( QKeyEvent* event )
{
    if( event->key() == View::KEY_S )
    {
        onSKeyPressed();
    }
    else
    {
        Widget::keyPressEvent( event );
    }
}

/*************************************************
功能： 窗口关闭事件
输入参数:
    event -- 事件
*************************************************************/
void SampleChartView::closeEvent( QCloseEvent* event )
{
    Q_UNUSED(event);

    PDATask *pTask = PDAService::instance()->currentTask();
    if(pTask && m_bRecording)
    {
        killAERecordMinTimer();
        killAERecordMaxTimer();
		killAERecordMax();
		killAETimer();
        m_bStopRecordEnable = true;
        stopAERecord();
        m_bRecording = false;

        //这里属于非正常停止录音，因此须将多余的录音文件删除
        FileOperUtil::deleteFile(m_qstrAttachPath);
    }

    emit sigClosed();
    m_bPdaWaiting = false;
    return;
}

/*************************************************
功能： 截屏并保存
输入参数：
        strAbsolutePath -- 路径
返回值：
        true -- 成功
        其它 -- 失败
*************************************************************/
bool SampleChartView::saveScreenShot( const QString& strAbsolutePath, const QString &strPNGFile )const
{
    if(strAbsolutePath.isEmpty() || strAbsolutePath == NULL)
    {
        return false;
    }
    if(strPNGFile.isEmpty() || strPNGFile == NULL)
    {
        return false;
    }

    bool bSucceed = false;

    QPixmap pixmap = screenShot();
#if 0
    //根据日期生成存放路径
    QDateTime dateTime = m_qSaveTime;
    if( m_qSaveTime.toString().isEmpty() )
    {
        dateTime = QDateTime::currentDateTime();
    }

    //qDebug() << "----SampleChartView::saveScreenShot " << m_qSaveTime << strPath;

    QString strAbsolutePath = strPath + '/';
    QString strDate = dateTime.toString("yyyyMMdd");
    strAbsolutePath += strDate.mid(0,4);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(4,2);
    strAbsolutePath += '/';
    strAbsolutePath += strDate.mid(6,2);
#endif
    QDir dir(strAbsolutePath);
    if(!dir.exists())
    {
        dir.mkpath( strAbsolutePath );
    }

    //strPNGFile = strAbsolutePath +'/'+ dateTime.toString("yyyyMMdd_hhmmss") + ".png";

    if( pixmap.save(strPNGFile, "png") )
    {
        bSucceed = true;
    }
    else
    {
        qWarning() << pixmap.isNull();
        qWarning() << "SampleChartView::screenShot: error, save png as " << strPNGFile << "failed!";
    }

    return bSucceed;
}

/*************************************************
功能： 响应S键事件
*************************************************************/
void SampleChartView::onSKeyPressed()
{

}

QString SampleChartView::saveDataToFile()
{
    return QString();
}

QString SampleChartView::saveDataToFile(const QString &stationName, const QString& deviceName)
{
    Q_UNUSED(stationName)
    Q_UNUSED(deviceName)

    return QString();
}

/******************************************
 * 功能：AE录音到最大录音时间，处理逻辑
 * ****************************************/
void SampleChartView::stopAERecordToDo()
{
    return;
}

/******************************************
 * 功能：AE录音到最大录音时间，处理逻辑 非pda
 * ****************************************/
void SampleChartView::stopAERecordNextToDo()
{
    return;
}

/*************************************************
功能： 延迟关闭界面，用于查看检测数据
*************************************************************/
void SampleChartView::delayToClose()
{    
    if(TIMER_INVALID_ID == m_iAutoSwitchTimerId)
    {
        m_bPdaWaiting = true;
        m_iAutoSwitchTimerId = startTimer(Module::SWITCH_TIME_INTERVAL);
        logInfo(QString("start switch timer id: %1.").arg(m_iAutoSwitchTimerId).toLatin1().data());
        //Module::mSleep(Module::SWITCH_TIME_INTERVAL);
    }
    return;
}

/*************************************************
功能： 关闭自动跳转定时器
*************************************************************/
void SampleChartView::killAutoSwitchTimer()
{
    if(TIMER_INVALID_ID != m_iAutoSwitchTimerId)
    {
        logInfo(QString("kill switch timer id: %1.").arg(m_iAutoSwitchTimerId).toLatin1().data());
        killTimer(m_iAutoSwitchTimerId);
        m_iAutoSwitchTimerId = TIMER_INVALID_ID;
    }
    m_bPdaWaiting = false;
    return;
}

/*************************************************
功能： 关闭录音最小时限定时器
*************************************************************/
void SampleChartView::killAERecordMinTimer()
{
    if(TIMER_INVALID_ID != m_iAERecordMinTimerId)
    {
        logInfo(QString("kill AE record min timer id: %1.").arg(m_iAERecordMinTimerId).toLatin1().data());
        killTimer(m_iAERecordMinTimerId);
        m_iAERecordMinTimerId = TIMER_INVALID_ID;
    }
    return;
}

/*************************************************
功能： 关闭录音最大时限定时器
*************************************************************/
void SampleChartView::killAERecordMaxTimer()
{
    if(TIMER_INVALID_ID != m_iAERecordMaxTimerId)
    {
        logInfo(QString("kill AE record max timer id: %1.").arg(m_iAERecordMaxTimerId).toLatin1().data());
        killTimer(m_iAERecordMaxTimerId);
        m_iAERecordMaxTimerId = TIMER_INVALID_ID;
    }
    return;
}

/*************************************************
功能： 关闭录音最大时限定时器
*************************************************************/
void SampleChartView::killAERecordMax()
{
    if(TIMER_INVALID_ID != m_iAERecordMaxId)
    {
        logInfo(QString("kill AE record max timer id: %1.").arg(m_iAERecordMaxId).toLatin1().data());
        killTimer(m_iAERecordMaxId);
        m_iAERecordMaxId = TIMER_INVALID_ID;
    }
    return;
}

/*************************************************
功能： 关闭录音最大时限定时器
*************************************************************/
void SampleChartView::killAETimer()
{
    if(TIMER_INVALID_ID != m_iTimerIDRefresh)
    {
        logInfo(QString("kill AE record id: %1.").arg(m_iTimerIDRefresh).toLatin1().data());
        killTimer(m_iTimerIDRefresh);
        m_iTimerIDRefresh = TIMER_INVALID_ID;
    }
    return;
}


/*************************************************
功能： 定时器处理函数
输入参数：
     event -- 定时事件
*************************************************************/
void SampleChartView::timerEvent(QTimerEvent* event)
{
    int iTimerId = event->timerId();
    if(iTimerId == m_iAutoSwitchTimerId)
    {
        killAutoSwitchTimer();
        close();
        PDAService::instance()->currentTask()->sendAutoSwitchSignal();
    }
    else if(iTimerId == m_iAERecordMinTimerId)
    {
        killAERecordMinTimer();
        m_bStopRecordEnable = true;
    }
    else if(iTimerId == m_iAERecordMaxTimerId)
    {
        killAERecordMaxTimer();
        m_bStopRecordEnable = true;
        stopAERecordToDo();
    }
	else if(iTimerId == m_iAERecordMaxId)
	{
		killAERecordMax();
        m_bStopRecordEnable = true;
        stopAERecordNextToDo();
	}
	else if(iTimerId == m_iTimerIDRefresh)
	{
		++m_uiTotalRecordingTime;
		emit sigTimerRefresh(m_uiTotalRecordingTime);
	}
    else
    {
        //invalid timer id;
    }

    return;
}

/*************************************************
功能： 根据font对单行string进行省略操作
*************************************************************/
QString SampleChartView::getRightEllipsisInfo(const QFont qFont, const int iWidth, const QString& qstrInfo)
{
    QFontMetrics qfmText(qFont);
    QString qstrTmpInfo = qfmText.elidedText(qstrInfo, Qt::ElideRight, iWidth);
    return qstrTmpInfo;
}

/**************************************************
 * 功能：设置录音类型
 * 输入参数：
 *      eType：录音类型
 * *************************************************/
void SampleChartView::setRecordType(RecordPlay::Type eType, RecordPlay::AEType eAEType)
{
    m_eType = eType;
	m_eAEType = eAEType;
    return;
}

/******************************************
 * 功能：开启录音
 * 输入参数：
 *      bPdaRecord：是否PDA的录音
 * ****************************************/
bool SampleChartView::startAERecord(bool bPdaRecord, const QString& qstrSavePath)
{
    bool bRet = false;

    if(!m_bRecording)
    {
        m_qstrAttachPath = "";  //重新开始录音，先清空之前的路径记录

        if (qstrSavePath.isEmpty())
        {
            if(bPdaRecord)
            {
                PDATask *pTask = PDAService::instance()->currentTask();
                m_qstrAttachPath = pTask ? pTask->getPdaAERecordPath() : "";
            }
            else
            {
                m_qstrAttachPath = RecordPlay::getRecordFileName(m_eType, m_eAEType);
            }
        }
        else
        {
            m_qstrAttachPath = qstrSavePath;
        }

        if(!m_qstrAttachPath.isEmpty())
        {
            //logInfo(m_qstrAttachPath);
            RecordPlayService::instance()->setRecordType(m_eType, m_eAEType);
            m_bRecording = RecordPlayService::instance()->startRecord(m_qstrAttachPath);
            if(m_bRecording)
            {
                killAERecordMinTimer();
                killAERecordMaxTimer();
                killAERecordMax();
				killAETimer();
				connect(this, SIGNAL(sigTimerRefresh(uint)), this, SLOT(onTimerRefresh(uint)));
                m_iAERecordMinTimerId = startTimer(RECORD_MIN_INTERVAL);
                m_iAERecordMaxTimerId = startTimer(RECORD_MAX_INTERVAL);
				m_iAERecordMaxId = startTimer(RECORD_MAX_TIME);
				m_iTimerIDRefresh = startTimer(PER_SECOND);
                m_bStopRecordEnable = false;
                bRet = true;
            }
            else
            {
                logError("start ae record failed.");
                FileOperUtil::deleteFile(m_qstrAttachPath);
                m_qstrAttachPath = "";
                m_bStopRecordEnable = true;
                m_bRecording = false;
            }
        }
        else
        {
            logError("record attachmentPath is empty.");
        }
    }
    else
    {
        logError("current is recording...");
    }

    return bRet;
}

/******************************************
 * 功能：停止录音
 * ****************************************/
bool SampleChartView::stopAERecord()
{
    bool bRet = false;
    if(m_bRecording)
    {
        if(m_bStopRecordEnable)
        {
            killAERecordMinTimer();
            killAERecordMaxTimer();
            killAERecordMax();
			killAETimer();
			disconnect(this, SIGNAL(sigTimerRefresh(uint)), this, SLOT(onTimerRefresh(uint)));
            RecordPlayService::instance()->stopRecord();

/* 马来西亚去除音频WAV转换MP3的需求
#ifdef _MALAYSIA_VERSION_DEFINED_
            QString qstrWavFilePath = m_qstrAttachPath;
            m_qstrAttachPath = FileOperUtil::replaceFileSurrfix(qstrWavFilePath, RECORD_FILE_NAME_MP3_SURRFIX);
            QtConcurrent::run(pfnWav2MP3, this, qstrWavFilePath);
#endif
*/
            m_bStopRecordEnable = false;
            m_bRecording = false;
            bRet = true;
        }
        else
        {
            logWarning("can not stop record temporarily.");
        }
    }
    else
    {
        logError("current is not recording...");
    }

    return bRet;
}

/*****************************************
 * 功能：wav文件转为mp3文件
 * **********************************************/
void SampleChartView::wav2MP3(QString qstrWavFilePath)
{
    //马来西亚版本将wav转换成mp3
    FileOperUtil::wav2Mp3(qstrWavFilePath, m_qstrAttachPath);
    emit sigWaitOperFinished();

    return;
}

/******************************************
 * 功能：显示保存信息提示框
 * 输入参数：
 *      qstrInfoText：信息提示框内容
 *      qstrSaveDataPath：文件保存路径
 * ****************************************/
void SampleChartView::showSaveInformation(QString& qstrInfoText, const QString& qstrSaveDataPath)
{
    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
    centerPoint.setY(pBtnBar->pos().y()+pBtnBar->height()/2);

    if (qstrSaveDataPath.isEmpty())
    {
        MsgBox::information("", QObject::trUtf8("Save failure!"), centerPoint);
    }
    else
    {
        //processTooLongMsgText(qstrInfoText);
        if (MsgBox::CANCEL == MsgBox::saveQuestion( "", qstrInfoText, centerPoint ))
        {
            if (MsgBox::OK == MsgBox::question("", QObject::trUtf8("Confirm to delete the file?") ))
            {
                FileOperUtil::deleteFile(qstrSaveDataPath);
            }
        }
    }
}

void SampleChartView::showFileCommentBox()
{
    m_qstrRemark.clear();
    bool bShowFileCommentBox = SystemSetService::instance()->isFileCommentBoxEnabled();
    if (bShowFileCommentBox)
    {
        FileCommentBox dlgFileCommentBox(this);
        PushButtonBar* pBtnBar = buttonBar();
        dlgFileCommentBox.setFixedSize(pBtnBar->width(), g_iFileCommentDlgHeight);
        dlgFileCommentBox.move(0, pBtnBar->geometry().bottom() - g_iFileCommentDlgHeight);
        if (QDialog::Accepted == dlgFileCommentBox.exec())
        {
            m_qstrRemark = dlgFileCommentBox.getRemark();
        }
    }
}

/******************************************
 * 功能：开启AE录音
 * 输入参数：
 *      bPdaRecord：是否PDA的录音
 * ****************************************/
bool SampleChartView::startAERecord(RecordPlay::AEType eAEType)
{
    bool bRet = false;

    if(!m_bRecording)
    {
        m_qstrAttachPath = "";  //重新开始录音，先清空之前的路径记录

        m_qstrAttachPath = RecordPlay::getRecordFileName(m_eType, eAEType);
		logInfo(QString("m_qstrAttachPath %1").arg(m_qstrAttachPath));

		if(!m_qstrAttachPath.isEmpty())
        {
            RecordPlayService::instance()->setRecordType(m_eType, eAEType);
            m_bRecording = RecordPlayService::instance()->startRecord(m_qstrAttachPath);
            if(m_bRecording)
            {
                killAERecordMinTimer();
                killAERecordMaxTimer();
                killAERecordMax();
				killAETimer();
                m_iAERecordMinTimerId = startTimer(RECORD_MIN_INTERVAL);
                m_iAERecordMaxTimerId = startTimer(RECORD_MAX_INTERVAL);
				ConfigInstance *pConfig = ConfigManager::instance()->config();
			    pConfig->beginGroup(Module::GROUP_APP);
				m_ui8RecordTime = pConfig->value(APPConfig::KEY_AERECORD_DURATION).toInt();
				pConfig->endGroup();
				m_iAERecordMaxId = startTimer(RECORD_MAX_TIME * m_ui8RecordTime);
				m_iTimerIDRefresh = startTimer(PER_SECOND);
				connect(this, SIGNAL(sigTimerRefresh(uint)), this, SLOT(onTimerRefresh(uint)));
                m_bStopRecordEnable = false;
                bRet = true;
            }
            else
            {
                logError("start ae record failed.");
                FileOperUtil::deleteFile(m_qstrAttachPath);
                m_qstrAttachPath = "";
                m_bStopRecordEnable = true;
                m_bRecording = false;
            }
        }
        else
        {
            logError("record attachmentPath is empty.");
        }
    }
    else
    {
        logError("current is recording...");
    }

    return bRet;
}

/******************************************
 * 功能：槽，响应录音时长变化
 * ****************************************/
void SampleChartView::onTimerRefresh(uint uiRecordingTime)
{
    Q_UNUSED(uiRecordingTime);
    return;
}

/*************************************************
功能： 显示更多配置菜单
*************************************************************/
void SampleChartView::showMoreConfigButtonBar()
{
    if (m_pMoreConfigView)
    {
        m_pMoreConfigView->setVisible(true);
    }
}

/*************************************************
功能： 隐藏更多配置菜单
*************************************************************/
void SampleChartView::hideMoreConfigButtonBar()
{
    if (m_pMoreConfigView)
    {
        m_pMoreConfigView->setVisible(false);
    }
}

/****************************************************
 * 功能：保存操作
 * **************************************************/
void SampleChartView::pressSaveData()
{
    return;
}

/*************************************************
功能： RFID扫描保存
*************************************************************/
void SampleChartView::RFIDSaveData()
{
    if(m_pMoreConfigView)
    {
        m_pMoreConfigView->hide();
    }

    RFIDScanView* pView = new RFIDScanView;

    ScanInfo scanInfo;
    ScanResult eResult = pView->scan(&scanInfo);

    // 扫描成功
    if(SCAN_SUCCEED == eResult)
    {
        QString strResult;
        m_qstrStationName = scanInfo.strStation;
        m_qstrDeviceName = scanInfo.strDeviceName;
        m_qstrDeviceNumber = scanInfo.strDeviceNumber;
        QString fileName = saveDataToFile();

        QFileInfo fileInfo(fileName);
        if((!fileName.isEmpty()) && fileInfo.exists())
        {
            strResult = fileInfo.fileName();
        }
        else
        {
            strResult = QObject::trUtf8("Save failure!");
        }

        RFIDReadView* pReadView = new RFIDReadView();
        pReadView->setScanInfo(scanInfo);
        pReadView->setTitle(strResult);
        pReadView->show();

        m_qstrStationName.clear();
        m_qstrDeviceName.clear();
        m_qstrDeviceNumber.clear();
    }
    else
    {
        logError("save rfid info failed.");
    }
}

/*************************************************
功能： 创建控更多信息按钮栏
输入参数:
    pchContext -- 国际化用的域
    pInfos -- 按钮配置信息
    iCount -- 按钮个数
    qstrTitile -- 更多界面标题名称，为空时是More...
返回值：创建的ButtonBar
*************************************************************/
LabelButtonBar* SampleChartView::createMoreConfigButtonBar(const char* const pchContext, const ButtonInfo::Info* pInfos, int iCount, QString qstrTitile)
{
    if (m_pMoreConfigView)
    {
        m_pMoreConfigView->removeAll();
        buttonBar()->detach();
        delete m_pMoreConfigView;
        m_pMoreConfigView = NULL;
    }

    m_pMoreConfigView = createLabelButtonBar(pchContext, pInfos, iCount, qstrTitile);

    if(m_pMoreConfigView)
    {
        m_pMoreConfigView->resize(Window::WIDTH, Window::HEIGHT);
        m_pMoreConfigView->attach(buttonBar()); // 做挂接
        m_pMoreConfigView->hide();
    }

    return m_pMoreConfigView;
}

/*************************************************
功能： 获取更多信息按钮栏
返回值： 更多信息按钮栏
*************************************************************/
LabelButtonBar* SampleChartView::moreConfigButtonBar()
{
    return m_pMoreConfigView;
}
