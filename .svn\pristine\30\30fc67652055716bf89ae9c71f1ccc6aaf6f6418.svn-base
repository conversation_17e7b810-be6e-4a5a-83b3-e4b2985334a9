#include <QThread>
#include <QDebug>
#include "AEFlyService.h"
#include "model/HCService.h"
#include "model/HCAffair.h"
#include "AEServicePrivate.h"
#include "datadefine.h"
#include "model/HCStatus.h"
#include <alsasoundApi.h>
#include <UHFHFCTAETEVApi.h>
#include <QMutex>
#include <QMutexLocker>

//static QMutex g_mtAeFlyObj;

/****************************
功能： 构造函数
*****************************/
AEFlyService::AEFlyService()
{
    m_vFlyData.clear();
}

/****************************
功能： 析构函数
*****************************/
AEFlyService::~AEFlyService()
{

}

/****************************
功能： 模块单例
*****************************/
AEFlyService* AEFlyService::instance()
{
    //QMutexLocker stLocker(&g_mtAeFlyObj);
    static AEFlyService service;
    return &service;
}

/*************************************************
功能： 启动独占采集
输入参数:void
输出参数：NULL
返回值： NULL
*************************************************/
bool AEFlyService::startExclusiveSample(  MultiServiceNS::USERID userId  )
{
    bool bRet = PeripheralService::instance()->setExclusiveAEMode( Pulse );
    if( bRet )
    {
        bRet = startSample( userId );
    }

    return bRet;
}

/*************************************************
功能： 停止独占采集
输入参数:void
输出参数：NULL
返回值： NULL
*************************************************/
void AEFlyService::stopExclusiveSample(  MultiServiceNS::USERID userId  )
{
    PeripheralService::instance()->releaseExclusiveAEMode();

    stopSample( userId );
}
