
#ifndef ROTATEDELETEFILEDIALOG_H
#define ROTATEDELETEFILEDIALOG_H

#include <QMessageBox>
#include <QMutex>
#include "filelistView/RotateFileListView.h"

class RotateDeleteFileWidget : public RotateFileListView
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    输入参数:
        path -- 路径
        nameFilters -- 文件过滤符（空表示不过滤）
        parent -- 父窗体
    *****************************/
    explicit RotateDeleteFileWidget( const QString& path, const QStringList& nameFilters,
                               QWidget* parent = 0, bool bRotate=false );

    /****************************
    功能： 析构函数
    *****************************/
    ~RotateDeleteFileWidget();

    /****************************
    功能： 设置关联文件类型
    比如：删除aaa.t01,则同步删除aaa.dat，aaa.dat是aaa.t01的关联文件
    输入参数:
        strSuffixMain -- 主类型
        listRelatedSuffix -- 关联类型列表
    *****************************/
    void setRelatedSuffix( const QString& strSuffixMain, const QStringList& listRelatedSuffix );

protected:
    /*************************************************
    功能： Item被点击或按下enter键时的响应
    输入参数:
            index: 索引
    *************************************************************/
    virtual void itemTriggered(const QModelIndex &index);

private slots:
    /****************************
    功能： 槽，响应文件选中
    输入参数:
        strFileName -- 文件名
    *****************************/
    void onFileChoosed( const QString& strFileName );
private:
    /****************************
    功能： 获取关联文件名
    输入参数:
        strFileName -- 文件名
    *****************************/
    QStringList getRelatedFiles( const QString& strFileName );

private:
    QString m_strPath;//路径
    QString m_strFilePath;//文件路径
    QMutex m_mt4RelatedSuffix;
    QMap<QString, QStringList> m_mapRelatedSuffix;//关联类型
    bool m_bRotate;
};

#endif // DELETEFILEDIALOG_H
