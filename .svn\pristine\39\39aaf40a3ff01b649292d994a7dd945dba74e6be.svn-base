﻿/*
* Copyright (c) 2016.1，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：datamap.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年10月24日
* 修改日期：
* 摘要：map节点部分的基类申明
* 当前版本：1.0
*/

#include <QObject>
#include <QVector>
#include <QByteArray>
#include "datafiledefine.h"
#include "datafile.h"
#include "mapdatafactory.h"
#include "./common/xmldocument.h"
#include "datamapconfig.h"
#include "crc32/crc32.h"
#include "common/dataformat.h"
#include "global_datafile_log.h"

const QString MAN_MODEL_STRING = "PDStarsT95_";
const int MAX_PRE_NUM = 32;

/*************************************************
功能： 构造函数
*************************************************************/
DataFile::DataFile(): m_pDoc(NULL)
{
    clearMaps();
}

/*************************************************
功能： 析构函数
*************************************************************/
DataFile::~DataFile()
{
    int iMapSize = m_vecMap.size();
    //qDebug() << "~DataFile " << iMapSize;
    for(int i =0 ; i < iMapSize; i ++)
    {
        DataMap *pMap = m_vecMap.at(i);
        if( NULL != pMap )
        {
            delete pMap;
        }
    }

    if( NULL != m_pDoc )
    {
        delete m_pDoc;
        m_pDoc = NULL;
    }
}

//file head

/*************************************************
功能： 设置版本号
输入参数：
        strVersion -- 版本号
*************************************************************/
void DataFile::setVersion(const QString &strVersion)
{
    m_stFileHead.strSpecificationVersion = strVersion;
}

/*************************************************
功能： 设置生成文件的时间
输入参数：
        strTime -- 生成文件的时间
*************************************************************/
void DataFile::setGenerationTime(const QString &strTime)
{
    m_stFileHead.strFileGenerationTime = strTime;
}

/*************************************************
功能： 设置国家名
输入参数：
        strCountry -- 国家名
*************************************************************/
void DataFile::setCountry(const QString &strCountry)
{
    m_stFileHead.strCountry = strCountry;
}

/*************************************************
功能： 设置省份名
输入参数：
        strProvince -- 省份名
*************************************************************/
void DataFile::setProvince(const QString &strProvince)
{
    m_stFileHead.strProvince = strProvince;
}


/*************************************************
功能： 设置城市名
输入参数：
        strCity -- 城市名
*************************************************************/
void DataFile::setCity(const QString &strCity)
{
    m_stFileHead.strCity = strCity;
}

/*************************************************
功能： 设置站点名称
输入参数：
        strName -- 站点名称
*************************************************************/
void DataFile::setStationName(const QString &strName)
{
    m_stFileHead.strStationName = strName;
}

/*************************************************
功能： 设置站点编码
输入参数：
        strCode -- 站点编码
*************************************************************/
void DataFile::setStationCode(const QString &strCode)
{
    m_stFileHead.strStationCode = strCode;
}

/*************************************************
功能： 设置天气
输入参数：
        eWeather -- 天气
*************************************************************/
void DataFile::setWeather(DataFileNS::Weather eWeather)
{
    m_stFileHead.eWeather = eWeather;
}

/*************************************************
功能： 设置环境温度
输入参数：
        fTemperature -- 环境温度,单位 摄氏度
*************************************************************/
void DataFile::setTemperature(float fTemperature)
{
    m_stFileHead.fTemperature = fTemperature;
}

/*************************************************
功能： 设置环境温度单位
输入参数：
        eUnit -- 环境温度单位
*************************************************************/
void DataFile::setTemperatureUnit(DataFileNS::TemperatureUnit eUnit)
{
    m_stFileHead.eTemperatureUnit = eUnit;
}

/*************************************************
功能： 设置环境湿度
输入参数：
        ucHumidity -- 环境湿度,单位 %
*************************************************************/
void DataFile::setHumidity(quint8 ucHumidity)
{
    m_stFileHead.ucHumidity = ucHumidity;
}

/*************************************************
功能： 设置仪器厂家
输入参数：
        strManufacturer -- 仪器厂家的名称
*************************************************************/
void DataFile::setInstrumentManufacturer(const QString &strManufacturer)
{
    m_stFileHead.strInstrumentManufacturer = strManufacturer;
}

/*************************************************
功能： 设置仪器型号
输入参数：
        strInstrumentModel -- 仪器型号的名称
*************************************************************/
void DataFile::setInstrumentModel(const QString &strInstrumentModel)
{
    m_stFileHead.strInstrumentModel = strInstrumentModel;
}

/*************************************************
功能： 设置仪器序列号
输入参数：
        strSerialNumber -- 仪器序列号字符串
*************************************************************/
void DataFile::setSerialNumber(const QString &strSerialNumber)
{
    m_stFileHead.strInstrumentSerialNumber = strSerialNumber;
}

/*************************************************
功能： 设置软件版本号
输入参数：
        strSWVersion -- 软件版本号字符串
*************************************************************/
void DataFile::setSWVersion(const QString &strSWVersion)
{
    m_stFileHead.strInstrumentVersion = strSWVersion;
}

/*************************************************
功能： 设置系统频率
输入参数：
        ucFreq -- 系统频率，单位Hz，如50Hz，填写50
*************************************************************/
void DataFile::setSystemFrequency(quint8 ucFreq)
{
    m_stFileHead.ucSystemFrequency = ucFreq;
}

bool DataFile::saveXMLFileHead(bool bCrypt)
{
    bool isSuccess = true;

    if( NULL != m_pDoc )
    {
        QDomElement root = m_pDoc->documentElement();
        m_pDoc->beginElement( root );
        m_pDoc->beginElement(XML_FILE_NODE_NAME_FILE_HEAD);

        m_pDoc->setValue(FileHeadNode::TEXT_VERSION, m_stFileHead.strSpecificationVersion);
        m_pDoc->setValue(FileHeadNode::TEXT_GENERATION_TIME, m_stFileHead.strFileGenerationTime);
        m_pDoc->setValue(FileHeadNode::TEXT_COUNTRY_NAME, m_stFileHead.strCountry);
        m_pDoc->setValue(FileHeadNode::TEXT_PROVINCE_NAME, m_stFileHead.strProvince);
        m_pDoc->setValue(FileHeadNode::TEXT_CITY_NAME, m_stFileHead.strCity);

        m_pDoc->setValue(FileHeadNode::TEXT_SUBSTATION_NAME, m_stFileHead.strStationName);
        m_pDoc->setValue(FileHeadNode::TEXT_SUBSTATION_CODE, m_stFileHead.strStationCode);
        m_pDoc->setValue(FileHeadNode::TEXT_WEATHER, QString::number(m_stFileHead.eWeather));
        m_pDoc->setValue(FileHeadNode::TEXT_TEMPERATURE_UNIT, QString::number(m_stFileHead.eTemperatureUnit));
        m_pDoc->setValue(FileHeadNode::TEXT_TEMPERATURE, QString::number(m_stFileHead.fTemperature,'f',1));
        m_pDoc->setValue(FileHeadNode::TEXT_HUMIDITY, QString::number(m_stFileHead.ucHumidity));
        m_pDoc->setValue(FileHeadNode::TEXT_MANUFACTURER, m_stFileHead.strInstrumentManufacturer);
        m_pDoc->setValue(FileHeadNode::TEXT_INSTRUMENT_MODEL, m_stFileHead.strInstrumentModel);
        m_pDoc->setValue(FileHeadNode::TEXT_INSTRUMENT_SN, m_stFileHead.strInstrumentSerialNumber);
        m_pDoc->setValue(FileHeadNode::TEXT_INSTRUMENT_VERSION, m_stFileHead.strInstrumentVersion);
        m_pDoc->setValue(FileHeadNode::TEXT_SYS_FREQUENCY, QString::number(m_stFileHead.ucSystemFrequency));

        m_stFileHead.sMapCounts = m_vecMap.size();
        m_pDoc->setValue(FileHeadNode::TEXT_SPECTRUM_COUNT, QString::number(m_stFileHead.sMapCounts));
        m_pDoc->endElement();

        isSuccess = m_pDoc->save(bCrypt);
    }
    else
    {
        isSuccess = false;
    }
    return isSuccess;
}

bool DataFile::createXMLFile(const QString &strXMLFile, bool bCrypt, DataFileNS::EncodeFormat eEncodeFormat)
{
    if( NULL != m_pDoc )
    {
        delete m_pDoc;
        m_pDoc = NULL;
    }

    if(eEncodeFormat == DataFileNS::ENCODE_UTF16)
    {
        m_pDoc = new XMLDocument(strXMLFile, QIODevice::ReadWrite, bCrypt, XML_FILE_ROOT_NODE_NAME,"utf-16");
    }
    else if(eEncodeFormat == DataFileNS::ENCODE_UTF8)
    {
        m_pDoc = new XMLDocument(strXMLFile, QIODevice::ReadWrite, bCrypt, XML_FILE_ROOT_NODE_NAME,"utf-8");
    }
    else
    {
        return false;
    }

    //XMLDocument m_doc( strXMLFile, QIODevice::ReadWrite, bCrypt, "mapDatas","utf-16" );
    QDomElement root = m_pDoc->documentElement();
    //file head
    m_pDoc->beginElement(XML_FILE_NODE_NAME_FILE_HEAD);
    m_pDoc->addElement(FileHeadNode::TEXT_VERSION);
    m_pDoc->addElement(FileHeadNode::TEXT_GENERATION_TIME);
    m_pDoc->addElement(FileHeadNode::TEXT_SUBSTATION_NAME);
    m_pDoc->addElement(FileHeadNode::TEXT_SUBSTATION_CODE);
    m_pDoc->addElement(FileHeadNode::TEXT_WEATHER);
    m_pDoc->addElement(FileHeadNode::TEXT_TEMPERATURE);
    m_pDoc->addElement(FileHeadNode::TEXT_HUMIDITY);
    m_pDoc->addElement(FileHeadNode::TEXT_MANUFACTURER);
    m_pDoc->addElement(FileHeadNode::TEXT_INSTRUMENT_MODEL);
    m_pDoc->addElement(FileHeadNode::TEXT_INSTRUMENT_SN);
    m_pDoc->addElement(FileHeadNode::TEXT_INSTRUMENT_VERSION);
    m_pDoc->addElement(FileHeadNode::TEXT_SYS_FREQUENCY);
    m_pDoc->addElement(FileHeadNode::TEXT_SPECTRUM_COUNT);

    //spectrums
    m_pDoc->beginElement( root );
    m_pDoc->addElement(XML_FILE_NODE_NAME_SPECTRUMS);

    //crc32
    m_pDoc->beginElement( root );
    m_pDoc->addElement(XML_FILE_NODE_NAME_CRC32);
    m_pDoc->endElement();
    return m_pDoc->save(bCrypt);
}

void DataFile::saveBinaryFileHead(QByteArray &baPackage)
{
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.setFloatingPointPrecision(QDataStream::SinglePrecision);

    out << qint32(0);

    DataFormat::addBinaryInfo(m_stFileHead.strSpecificationVersion, DataFileNS::TYPE_VERSION, out, 4);
    out << DataFormat::binaryTime(m_stFileHead.strFileGenerationTime);

    DataFormat::addBinaryInfo(m_stFileHead.strStationName, DataFileNS::TYPE_UNICODE, out, 128);
    DataFormat::addBinaryInfo(m_stFileHead.strStationCode, DataFileNS::TYPE_ASCII, out, 32);

    out << (quint8)m_stFileHead.eWeather;
    out << m_stFileHead.fTemperature;
    out << (qint8)m_stFileHead.ucHumidity;
    DataFormat::addBinaryInfo(m_stFileHead.strInstrumentManufacturer, DataFileNS::TYPE_UNICODE, out, 32);
    DataFormat::addBinaryInfo(m_stFileHead.strInstrumentModel, DataFileNS::TYPE_UNICODE, out, 32);
    DataFormat::addBinaryInfo(m_stFileHead.strInstrumentVersion, DataFileNS::TYPE_VERSION, out, 4);
    DataFormat::addBinaryInfo(m_stFileHead.strInstrumentSerialNumber, DataFileNS::TYPE_ASCII, out, 32);

    float fSysFreq = (float)m_stFileHead.ucSystemFrequency;
    out << fSysFreq;
}
bool DataFile::saveCRC32Xml(bool bCrypt, quint32 iCRC32)
{
    bool isSuccess = true;
    if( NULL != m_pDoc )
    {
        QDomElement root = m_pDoc->documentElement();
        m_pDoc->beginElement( root );
        m_pDoc->setValue(XML_FILE_NODE_NAME_CRC32, QString::number(iCRC32));
        m_pDoc->endElement();
        isSuccess = m_pDoc->save(bCrypt);
    }
    else
    {
        isSuccess = false;
    }
    return isSuccess;
}

void DataFile::setFileGenerationIime(const QString &strTime)
{
    setGenerationTime(strTime);

}

/*************************************************
功能： 保存数据到数据文件
输入参数：
        strFilePath -- 文件路径
        strFileExtName -- 文件后缀名
        bCrypt -- 是否加密
        eFileType -- 数据文件类型
        eEncodeFormat -- 编码格式
输出参数：
        strDataFile ---数据文件名
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool DataFile::save(const QString &strFilePath, const QString &strFileExtName, QString &strDataFile, bool bCrypt, DataFileNS::FileType eFileType, DataFileNS::EncodeFormat eEncodeFormat)
{
    Q_UNUSED(eFileType)
    //create data file
    QDir dir(strFilePath);
    if(!dir.exists())
    {
        dir.mkpath(strFilePath);
    }

//    QString strFileNameTimeFormat = "yyyyMMdd_hhmmssfff";
    QDateTime dateTime = QDateTime::currentDateTime();
//    QString strDataFileTime = dateTime.toString(strFileNameTimeFormat);
//    strDataFile = strFilePath + "/" +strDataFileTime + strFileExtName;

    qint64 lTime = dateTime.currentMSecsSinceEpoch();
    QString strDataFileTime = dateTime.fromMSecsSinceEpoch(lTime).toString("yyyyMMdd_hhmmsszzz");
    strDataFile = strFilePath + "/" +strDataFileTime + strFileExtName;
    QString strFileGenerationTime = dateTime.fromMSecsSinceEpoch(lTime).toString("yyyyMMddhhmmsszzz");
    if(!createXMLFile(strDataFile, bCrypt, eEncodeFormat))
    {
        qWarning() << "DataFile::save, createXMLFile failed!";
        return false;
    }

    setFileGenerationIime(strFileGenerationTime);

    //save file head
    if(!saveXMLFileHead(bCrypt))
    {
        qWarning() << "DataFile::save, saveXMLFileHead failed!" << endl;
        return false;
    }
    //save maps
    int iMapCnt = m_vecMap.size();
    qDebug() << "DataFile::save, iMapCnt: " << iMapCnt << endl;
    for(int i = 0; i < iMapCnt; ++i)
    {
        qDebug() << "DataFile::save, iMapCnt:" << iMapCnt << endl;
        DataMap *pMap = m_vecMap.at(i);
        pMap->saveAsXml(m_pDoc, bCrypt);
    }

    //save crc32
    quint32 iCRC32 = crc32(m_pDoc->getByteArray(), m_pDoc->getByteArray().size());

    return saveCRC32Xml(bCrypt, iCRC32);
}

/*************************************************
功能： 保存数据到数据文件
输入参数：
        strFilePath -- 文件路径
        strFileExtName -- 文件后缀名
        bCrypt -- 是否加密
        eFileType -- 数据文件类型
        eEncodeFormat -- 编码格式
        strDataFile ---数据文件名
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool DataFile::save(const QString &strFilePath, const QString &strFileExtName, const QString &strDataFile, bool bCrypt, DataFileNS::FileType eFileType, DataFileNS::EncodeFormat eEncodeFormat)
{
    Q_UNUSED(eFileType)
    Q_UNUSED(strFileExtName)

    //create data file
    QDir dir(strFilePath);
    if(!dir.exists())
    {
        dir.mkpath(strFilePath);
    }

    qDebug() << "DataFile::save, strDataFile: " << strDataFile << endl;

    if(!createXMLFile(strDataFile, bCrypt, eEncodeFormat))
    {
        qWarning() << "DataFile::save, createXMLFile failed!" << endl;
        return false;
    }
    //save file head
    if(!saveXMLFileHead(bCrypt))
    {
        qWarning() << "DataFile::save, saveXMLFileHead failed!" << endl;
        return false;
    }
    //save maps
    int iMapCnt = m_vecMap.size();
    qDebug() << "DataFile::save, iMapCnt: " << iMapCnt;
    for(int i = 0; i < iMapCnt; ++i)
    {
        qDebug() << "DataFile::save, iMapCnt:" << iMapCnt;
        DataMap *pMap = m_vecMap.at(i);
        pMap->saveAsXml(m_pDoc, bCrypt);
    }

    //save crc32
    quint32 iCRC32 = crc32(m_pDoc->getByteArray(), m_pDoc->getByteArray().size());

    return saveCRC32Xml(bCrypt, iCRC32);
}

/*************************************************
功能： 保存数据到数据文件
输入参数：
        strFilePath -- 文件路径
        strFileExtName -- 文件后缀名
        bCrypt -- 是否加密
        eFileType -- 数据文件类型
        eEncodeFormat -- 编码格式
输出参数：
        strDataFile ---数据文件名
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool DataFile::save(const QDateTime &dateTime, const QString &strFilePath, const QString &strFileExtName, QString &strDataFile, bool bCrypt, DataFileNS::FileType eFileType, DataFileNS::EncodeFormat eEncodeFormat)
{
    Q_UNUSED(eFileType)
    //create data file
    QDir dir(strFilePath);
    if(!dir.exists())
    {
        dir.mkpath(strFilePath);
    }
//    QString strFileNameTimeFormat ="yyyyMMdd_hhmmssfff";
    //QDateTime dateTime = QDateTime::currentDateTime();
//    QString strDataFileTime = dateTime.toString(strFileNameTimeFormat);
//    strDataFile = strFilePath + "/" +strDataFileTime + strFileExtName;


    qint64 lTime = QDateTime::currentMSecsSinceEpoch();
    QString strDataFileTime = dateTime.fromMSecsSinceEpoch(lTime).toString("yyyyMMdd_hhmmsszzz");
    strDataFile = strFilePath + "/" +strDataFileTime + strFileExtName;
    qDebug()<<"DataFile::save, strDataFile:"<<strDataFile;

    bool isSuccess = createXMLFile(strDataFile, bCrypt, eEncodeFormat);
    if(!isSuccess)
    {
        qWarning()<<"DataFile::save, createXMLFile failed!";
        return false;
    }

    //save file head
    isSuccess = saveXMLFileHead(bCrypt);
    if(!isSuccess)
    {
        qWarning()<<"DataFile::save, saveXMLFileHead failed!";
        return false;
    }

    //save maps
    int iMapCnt = m_vecMap.size();
    qDebug()<<"DataFile::save, iMapCnt:"<<iMapCnt;
    for(int i = 0; i < iMapCnt; i ++)
    {
        qDebug()<<"DataFile::save, iMapCnt:"<<iMapCnt;
        DataMap *pMap = m_vecMap.at(i);
        pMap->saveAsXml(m_pDoc, bCrypt);
    }

    //save crc32
    quint32 iCRC32 = crc32(m_pDoc->getByteArray(), m_pDoc->getByteArray().size());
    return saveCRC32Xml(bCrypt, iCRC32);
}

/*************************************************
功能： 获取datafile版本号
*************************************************************/
void DataFile::version(QString &strVersion)
{
    strVersion = DataFileNS::VERSION;
}

/*************************************************
功能： 保存数据到数据文件
输入参数：
        strFilePath -- 文件所在的路径 如：/test/2017/11/11
        strFileExtName -- 数据文件的后缀
输出参数：
        strDataFile ---数据文件全路径
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool DataFile::saveByBinary(const QString &strFilePath, const QString &strFileExtName, QString &strDataFile)
{
    bool bRet = true;
    do
    {
        //create data file
        QDir dir(strFilePath);
        if(!dir.exists())
        {
            if(!(dir.mkpath(strFilePath)))
            {
                bRet = false;
                log_error_datafile("create file directory failed.");
                break;
            }
        }

        QString strFileNameTimeFormat ="yyyyMMddhhmmsszzz";
        QDateTime dateTime = QDateTime::currentDateTime();
        QString strPrefix = MAN_MODEL_STRING + m_stFileHead.strInstrumentSerialNumber + "_";
        if( strPrefix.size() > MAX_PRE_NUM )
        {
            strPrefix = strPrefix.mid( 0, MAX_PRE_NUM );
        }
        QString strDataFileTime = strPrefix + dateTime.toString(strFileNameTimeFormat);
        strDataFile = strFilePath + "/" +strDataFileTime + strFileExtName;

        QByteArray baPackage;
        saveBinaryFileHead(baPackage);

        QDataStream out(&baPackage, QIODevice::ReadWrite);
        out.setByteOrder(QDataStream::LittleEndian);
        out.setFloatingPointPrecision(QDataStream::SinglePrecision);
        out.device()->seek(out.device()->size());
        int iMapCnt = m_vecMap.size();
        out << (qint16)iMapCnt;

        //caReserved
        quint8 caReserved [DataFileNS::FILE_HEAD_RESERVE];
        memset(caReserved, 0, DataFileNS::FILE_HEAD_RESERVE);

        //
        caReserved[DataFileNS::FILE_HEAD_RESERVE - 6] = 1;
        caReserved[DataFileNS::FILE_HEAD_RESERVE - 5] = 1;
        caReserved[DataFileNS::FILE_HEAD_RESERVE - 4] = 1;
        caReserved[DataFileNS::FILE_HEAD_RESERVE - 3] = 1;
        caReserved[DataFileNS::FILE_HEAD_RESERVE - 2] = 0;
        caReserved[DataFileNS::FILE_HEAD_RESERVE - 1] = 0;

        for(int i = 0; i < DataFileNS::FILE_HEAD_RESERVE; ++i)
        {
            out << (quint8)caReserved[i];
        }

        for(int i = 0; i < iMapCnt; ++i)
        {
            DataMap *pMap = m_vecMap.at(i);
            QByteArray baMapInfo;
            pMap->saveAsBinary(baMapInfo);
            baPackage.append(baMapInfo);
        }
        setBinaryFileTail(baPackage);

        QFile file(strDataFile);
        bRet = file.open(QIODevice::WriteOnly);
        if(!bRet)
        {
            bRet = false;
            log_error_datafile("open binary file failed.");
            break;
        }
        else
        {
            file.write(baPackage);
            file.close();
            bRet = true;
        }

    }while(0);

    return bRet;
}

/*************************************************
功能： 解析二进制数据文件
输入参数：
        strFilePath -- 文件全路径
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool DataFile::parseBinaryDataFile(const QString &strFilePath)
{
    QFile file( strFilePath );
    if ( !file.open( QIODevice::ReadOnly ) )
    {
        qWarning(QString("Fail to open %1").arg(strFilePath).toLocal8Bit().data());
        return false;
    }

    QByteArray baData = file.readAll();
    file.close();

    return parseBinaryData(baData);
}

/*************************************************
功能： 解析数据流
输入参数：
        bytes --- 数据流
返回值：
        成功为true，失败为false
*************************************************************/
bool DataFile::parseBinaryData(const QByteArray &bytes)
{
    if( !checkCRC32(bytes) )
    {
        qWarning("[DataFile::setBinaryData] check crc32 failed.");
        return false;
    }

    const int headerlength = DataFileNS::Binary::HeaderLength;
    const int headerFixLength = DataFileNS::Binary::HeaderPermanentLength;
    const int tailLength = DataFileNS::Binary::TailLength;

    clearMaps();

    // 获取文件总长度
    int len = 0;
    if ( bytes.size() > (int)sizeof(int) )
    {
        QDataStream in(bytes);
        in.setByteOrder(QDataStream::LittleEndian);
        in >> len;
    }

    qDebug() << "----Total file size is: " << len;

    if(len < headerFixLength || len != bytes.length())
    {
        qWarning("[DataFile::fromBinary] parse failed, bytes length is %d, file length is %d", bytes.length(), len);
        return false;
    }

    if(parseBinaryHeader(QByteArray::fromRawData(bytes.data(), headerlength)))
    {
        return parseBinaryDataMaps(QByteArray::fromRawData(bytes.data() + headerlength, bytes.length() - headerlength - tailLength));
    }

    return false;
}

/*************************************************
功能： 解析数据头
输入参数：
        bytes --- 数据流
返回值：
        成功为true，失败为false
*************************************************************/
DataFile::FileHead DataFile::fileHead()
{
    return m_stFileHead;
}


/*************************************************
功能： 解析数据头
输入参数：
        bytes --- 数据流
返回值：
        成功为true，失败为false
*************************************************************/
bool DataFile::parseBinaryHeader(const QByteArray &bytes)
{
    QDataStream in(bytes);
    in.setByteOrder(QDataStream::LittleEndian);
    // float设置4字节
    in.setFloatingPointPrecision(QDataStream::SinglePrecision);

    qint32 filelen = 0;
    quint8 version[4] = {};
    qint64 filetime;
    quint8 weatherVal = 0;
    quint8 insVer[5] = {};
    float freq = 0;
    qint16 chartNum = 0;

    DataFormat::getInt32(in, filelen);
    DataFormat::getUint8Array(in, version, 4);
    DataFormat::getInt64(in, filetime);
    DataFormat::getUnicode(in, 128 / 2, m_stFileHead.strStationName);
    DataFormat::getAscii(in, 32, m_stFileHead.strStationCode);
    DataFormat::getUInt8(in, weatherVal);
    m_stFileHead.eTemperatureUnit = DataFileNS::TEMPERATURE_UNIT_DEFAULT;
    DataFormat::getFloat(in, m_stFileHead.fTemperature);
    DataFormat::getUInt8(in, m_stFileHead.ucHumidity);
    DataFormat::getUnicode(in, 32 / 2, m_stFileHead.strInstrumentManufacturer);
    DataFormat::getUnicode(in, 32 / 2, m_stFileHead.strInstrumentModel);
    DataFormat::getUint8Array(in, insVer, 4);
    DataFormat::getAscii(in, 32, m_stFileHead.strInstrumentSerialNumber);
    DataFormat::getFloat(in, freq);
    DataFormat::getInt16(in, chartNum);

    m_stFileHead.strSpecificationVersion = QString("%1.%2.%3.%4")
            .arg(int(version[0])).arg(int(version[1])).arg(int(version[2])).arg(int(version[3]));
    m_stFileHead.strFileGenerationTime = QString::number(filetime);
    m_stFileHead.eWeather = DataFileNS::Weather(weatherVal);
    m_stFileHead.strInstrumentVersion = QString("%1.%2.%3.%4")
            .arg(int(insVer[0])).arg(int(insVer[1])).arg(int(insVer[2])).arg(int(insVer[3]));
    m_stFileHead.ucSystemFrequency = static_cast<quint8>(freq);
    m_stFileHead.sMapCounts = chartNum;

    qDebug() << m_stFileHead.strFileGenerationTime << m_stFileHead.strStationName
             << m_stFileHead.strStationCode;
    qDebug() << m_stFileHead.fTemperature << m_stFileHead.ucHumidity;
    qDebug() << m_stFileHead.strInstrumentManufacturer << m_stFileHead.strInstrumentModel;

    return in.status() == QDataStream::Ok;
}

/*************************************************
功能： 解析图谱
输入参数：
        bytes --- 数据流
返回值：
        成功为true，失败为false
*************************************************************/
bool DataFile::parseBinaryDataMaps(const QByteArray &bytes)
{
    QDataStream in(bytes);
    in.setByteOrder(QDataStream::LittleEndian);

    //clearMaps();
    int lengthToParse = bytes.length();
    const char* pData = bytes.data();

    quint8 chartType = 0;
    qint32 chartLength = 0;
    while(lengthToParse > 0)
    {
        in >> chartType;        //图谱类型编码
        in >> chartLength;      //图谱数据长度

        qDebug() << "--------chartType" << chartType << chartLength;

        if(in.status() != QDataStream::Ok)
        {
            qWarning("Error: fail to parseBinaryDataMaps.");
            return false;
        }

        QString strClassName = DataFormat::dataMapName(DataFileNS::SpectrumCode(chartType));
        qDebug() << "----test: " << strClassName;
        DataMap *pMap = dynamic_cast<DataMap*>(MapDataFactory::createObject( strClassName.toLatin1() ));

        if(pMap)
        {
            bool parseRes = pMap->parseBinaryData(QByteArray::fromRawData(pData, chartLength));

            qDebug() << "-----------------------parse result: " << parseRes;

            pData += chartLength;
            lengthToParse -= chartLength;
            in.skipRawData(chartLength - sizeof(chartLength) - sizeof(chartType));

            m_vecMap.append(pMap);
        }
        else
        {
            qWarning("Fail to create DataMap.");
            return false;
        }
    }

    return true;
}

/*************************************************
功能： 校验数据
输入参数：
        bytes --- 数据流
返回值：
        成功为true，失败为false
*************************************************************/
bool DataFile::checkCRC32(const QByteArray &bytes)
{
    quint32 iCRC32 = crc32(bytes.data(), bytes.size() - 4);
    quint32 fileCRC = 0;

    QDataStream in(bytes);
    in.setByteOrder(QDataStream::LittleEndian);
    in.skipRawData(bytes.length() - 4);

    in >> fileCRC;

    return (fileCRC == iCRC32);
}

void DataFile::clearMaps()
{
    for(int i = 0; i < m_vecMap.size(); ++i)
    {
        delete m_vecMap[i];
    }

    m_vecMap.clear();
}

void DataFile::setBinaryFileTail(QByteArray &baPackage)
{
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.setFloatingPointPrecision(QDataStream::SinglePrecision);
    qint32 iSize = out.device()->size();
    out.device()->seek(iSize);

    for(int i = 0; i < DataFileNS::DATA_FILE_TAIL_RESERVED_BYTES_CNT; i ++)
    {
        out << (quint8)(DataFileNS::INVALID_BYTE_VAL);
    }

    resetFileHeadDataLength(baPackage);

    QDataStream outWithCRC(&baPackage, QIODevice::ReadWrite);
    outWithCRC.setByteOrder(QDataStream::LittleEndian);
    qint32 iCRC32 = crc32(baPackage.constData(), baPackage.size());
    qint32 iSizeWithCRC = outWithCRC.device()->size();
    outWithCRC.device()->seek(iSizeWithCRC);
    outWithCRC << (qint32)iCRC32;
}

void DataFile::resetFileHeadDataLength(QByteArray &baPackage)
{
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.setFloatingPointPrecision(QDataStream::SinglePrecision);
    qint32 iCRC32 = 0;
    qint32 iSizeWithoutCRC = out.device()->size();
    qint32 iFileSize = iSizeWithoutCRC + sizeof(iCRC32);
    out.device()->seek(0);
    out<<(qint32)iFileSize;
}
/*************************************************
功能： 打开数据文件
输入参数：
        strDataFile ---数据文件名
        isDeCrypt -- 是否解密
        eEncodeFormat -- 编码格式
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool DataFile::open(const QString &strDataFile, bool isDeCrypt, DataFileNS::FileType eFileType,
                    DataFileNS::EncodeFormat eEncodeFormat)
{
    Q_UNUSED(eFileType)
    //parse data file, save whole file datas to m_baFileData
    //new maps by count of map in m_baFileData
    const char* pchCodecName;
    if(eEncodeFormat == DataFileNS::ENCODE_UTF8)
    {
        pchCodecName = "utf-8";
    }
    else if(eEncodeFormat == DataFileNS::ENCODE_UTF16)
    {
        pchCodecName = "utf-16";
    }
    else
    {
        qWarning("wrong ecode format when open xml file!");
        return false;
    }
    qWarning()<<"open, strDataFile:"<<strDataFile<<"isDeCrypt:"<<isDeCrypt;

    XMLDocument doc( strDataFile, QIODevice::ReadOnly,isDeCrypt, XML_FILE_ROOT_NODE_NAME, pchCodecName);

    //解析文件头部
    parseFileHead( &doc, m_stFileHead );

    doc.beginElement( XML_FILE_NODE_NAME_SPECTRUMS );
    QList<QDomElement> mapElems = doc.childElement( XML_FILE_NODE_NAME_SPECTRUM );
    qWarning("mapElems.size() is %d\n", mapElems.size());


    //遍历解析map
    for( int i = 0; i < mapElems.size(); i++ )
    {
        QDomElement elem = mapElems.at( i );
        //获取map根节点的tag，并创建对应对象实例
        QString strRootTag = elem.firstChildElement().tagName();
        doc.beginElement( mapElems.at(i));

        DataMap * tmpPMap = dynamic_cast<DataMap*>( MapDataFactory::createObject( strRootTag.toLatin1() ) );
        if( NULL != tmpPMap )
        {
            tmpPMap->setXmlData( &doc, strRootTag );
            m_vecMap.append( tmpPMap );
        }
        doc.endElement();
    }

    doc.endElement();
    return true;
}

/*************************************************
功能： 获取文件头
输入参数：
        stHead ---文件head数据
返回值:
        true: 成功 ;false:失败
*************************************************************/
bool DataFile::getFileHead(FileHead &stHead)
{
    bool bRet = true;
    stHead = m_stFileHead;
    //    if(m_stFileHead != NULL)
    //    {
    //        stHead = m_stFileHead;
    //    }
    //    else
    //    {
    //        bRet = false;
    //    }
    return bRet;
}
/*************************************************
功能： 获取版本
返回： 版本号
*************************************************************/
QString DataFile::version()
{
    return m_stFileHead.strSpecificationVersion;
}

/*************************************************
功能： 获取生成文件的时间
返回： 生成文件的时间
*************************************************************/
QString DataFile::generationTime()
{
    return m_stFileHead.strFileGenerationTime;
}

/*************************************************
功能： 获取站点名称
返回： 站点名称
*************************************************************/
QString DataFile::stationName()
{
    return m_stFileHead.strStationName;
}

/*************************************************
功能： 获取站点编码
返回： 站点编码
*************************************************************/
QString DataFile::stationCode()
{
    return m_stFileHead.strStationCode;
}

/*************************************************
功能： 获取天气信息
返回： 天气信息
*************************************************************/
quint8 DataFile::weather()
{
    return m_stFileHead.eWeather;
}

/*************************************************
功能： 获取温度信息
返回： 温度信息
*************************************************************/
float DataFile::temperature()
{
    return m_stFileHead.fTemperature;
}

/*************************************************
功能： 获取湿度信息
返回： 湿度信息
*************************************************************/
quint8 DataFile::humidity()
{
    return m_stFileHead.ucHumidity;
}

/*************************************************
功能： 获取仪器厂家
返回： 仪器厂家
*************************************************************/
QString DataFile::manufacturer()
{
    return m_stFileHead.strInstrumentManufacturer;
}

/*************************************************
功能： 获取仪器型号
返回： 仪器型号
*************************************************************/
QString DataFile::instrumentModel()
{
    return m_stFileHead.strInstrumentModel;
}

/*************************************************
功能： 获取仪器序列号
返回： 仪器序列号
*************************************************************/
QString DataFile::instrumentSN()
{
    return m_stFileHead.strInstrumentSerialNumber;
}

/*************************************************
功能： 获取仪器版本号
返回： 仪器版本号
*************************************************************/
QString DataFile::instrumentVersion()
{
    return m_stFileHead.strInstrumentModel;
}

/*************************************************
功能： 获取系统频率
返回： 系统频率 单位HZ
*************************************************************/
quint8 DataFile::sysFreq()
{
    return m_stFileHead.ucSystemFrequency;
}

/*************************************************
功能： 获取图谱数量
返回： 文件中包含的图谱数量
*************************************************************/
qint16 DataFile::spectrumCount()
{
    return m_stFileHead.sMapCounts;
}

/*************************************************
功能： 解析头部内容
输入参数：
        doc --- 对应的doc
输出参数:
        fileHead --- 解析头部的内容
返回值：
        解析成功为true，失败为false
*************************************************************/
bool DataFile::parseFileHead(XMLDocument *doc, FileHead & fileHead )
{
    bool bRet = true;

    doc->beginElement(XML_FILE_NODE_NAME_FILE_HEAD);

    fileHead.strSpecificationVersion = doc->value(FileHeadNode::TEXT_VERSION );
    fileHead.strFileGenerationTime = doc->value(FileHeadNode:: TEXT_GENERATION_TIME);
    fileHead.strStationName = doc->value(FileHeadNode:: TEXT_SUBSTATION_NAME);
    fileHead.strStationCode = doc->value(FileHeadNode:: TEXT_SUBSTATION_CODE);
    fileHead.eWeather = (DataFileNS::Weather)doc->value(FileHeadNode::TEXT_WEATHER).toInt();
    fileHead.fTemperature = doc->value(FileHeadNode:: TEXT_TEMPERATURE).toFloat();
    fileHead.ucHumidity = doc->value(FileHeadNode::TEXT_HUMIDITY).toUInt();
    fileHead.strInstrumentManufacturer = doc->value(FileHeadNode:: TEXT_MANUFACTURER);
    fileHead.strInstrumentModel = doc->value(FileHeadNode:: TEXT_INSTRUMENT_MODEL);
    fileHead.strInstrumentSerialNumber = doc->value(FileHeadNode:: TEXT_INSTRUMENT_SN);
    fileHead.strInstrumentVersion = doc->value(FileHeadNode:: TEXT_INSTRUMENT_VERSION);
    fileHead.ucSystemFrequency = doc->value(FileHeadNode:: TEXT_SYS_FREQUENCY).toUInt();
    fileHead.sMapCounts = doc->value(FileHeadNode:: TEXT_SPECTRUM_COUNT).toUInt();


    doc->endElement();
    return bRet;
}

/*************************************************
    功能： 添加图谱
    输入参数：
            pMap -- 增加的图谱
    返回值：>=0为添加的图谱在图谱列表中的位置 -1为出错
    *************************************************************/
int DataFile::addMap( DataMap * pMap )
{
    int iRet = -1;
    if( ( NULL != pMap ) && ( m_vecMap.indexOf(pMap) == -1 ) )
    {
        m_vecMap.append( pMap );
        iRet = m_vecMap.indexOf(pMap);
    }
    else
    {
        // 如果存在不更新？ issue?
        qDebug() << "map is empty or has existed!";
    }
    return iRet;
}

/*************************************************
    功能： 获取指定位置图谱的指针
    输入参数：
            uiMapPos -- 图谱在文件中的位置
    返回值：非NULL为图谱的指针 NULL为查找出错
    *************************************************************/
DataMap* DataFile::dataMap( quint32 uiMapPos )
{
    if( uiMapPos < (uint)m_vecMap.size() )
    {
        return m_vecMap[uiMapPos];
    }
    else
    {
        qDebug()<<"DataMap: map index is out of range !";
        return NULL;
    }
}

/*************************************************
    功能： 获取指定spectrum code的图谱
    输入参数：
            eCode -- 图谱spectrum code
    返回值：非NULL为图谱的指针 NULL为查找出错
    *************************************************************/
DataMap* DataFile::dataMap( DataFileNS::SpectrumCode eCode)
{
    DataMap *pMap = NULL;
    for(int i = 0; i < m_vecMap.size(); i ++)
    {
        DataFileNS::SpectrumCode eCurrentCode;
        pMap = m_vecMap[i];
        pMap->getMapType(eCurrentCode);

        if(eCurrentCode == eCode)
        {
            return pMap;
        }
    }
    return pMap;
}

/*************************************************
    功能： 获取当前文件存在的图谱头部信息列表
    返回值：当前文件存在的图谱头部信息列表
    *************************************************************/
QList<DataMap::MapHead> DataFile::mapsHeadList(  )
{
    QList<DataMap::MapHead> lmapsHead;
#if 0
    foreach( DataMap * pMap, m_vecMap )
    {
        DataMap::MapHead tempMapHead;
        if(pMap->getMapHead(tempMapHead))
        {
            lmapsHead.append(tempMapHead);
        }
        else
        {
            qDebug()<<"DataFile: get map head failed";
        }
    }
#endif
    return lmapsHead;
}


