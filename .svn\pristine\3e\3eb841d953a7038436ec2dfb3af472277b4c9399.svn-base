#include "multiuserservice.h"
#include <QDebug>
#include "global_def.h"
#include "log/log.h"
#include "global_log.h"

MultiServiceNS::USERID MultiUserService::s_uiNextID = 0;

MultiUserService::MultiUserService(QObject *parent) : HCService(parent)
{

}

MultiUserService::~MultiUserService()
{

}

/*************************************************
功能： 增加采集用户
输入参数：
        user -- 采集用户的信息
返回：
        用户的唯一标识ID
*************************************************/
MultiServiceNS::USERID MultiUserService::addUser( const MultiServiceNS::SampleUser& user )
{
    MultiServiceNS::SampleUser *pUser = new MultiServiceNS::SampleUser;
    memcpy( pUser, &user, sizeof(MultiServiceNS::SampleUser) );
    MultiServiceNS::USERID uID = createNextUserID();
    registerUser[uID] = pUser;
    return uID;
}

/*************************************************
功能： 删除采集用户
输入参数：
        userId -- 用户ID
*************************************************/
void MultiUserService::deleteUser( MultiServiceNS::USERID userId )
{
    stopSample( userId );
    MultiServiceNS::SampleUser* pUser = registerUser[userId];
    if( NULL != pUser )
    {
        delete pUser;
    }
    registerUser.remove( userId );
}

/*************************************************
功能： 开始指定用户的采集业务
输入参数：
        userId -- 用户ID
返回：
        是否启动采样成功
*************************************************/
bool MultiUserService::startSample( MultiServiceNS::USERID userId )
{
    MultiServiceNS::SampleUser tmpUser;
    bool bRet = getUserInfo( tmpUser, userId );
    if( bRet )
    {
        m_listenMutex.lock();
        listenUsers[tmpUser.eSampleType] <<  userId;
        m_listenMutex.unlock();

        bRet = startSampleExt( userId );
        logInfo("start stamp.");
    }
    else
    {
        //cann't find user
        bRet = false;
        logInfo("start stamp failed.");
    }

    return bRet;
}

/*************************************************
功能： 停止指定用户的采集业务
输入参数：
        userId -- 用户ID
返回：
        是否停止采样成功
*************************************************/
bool MultiUserService::stopSample( MultiServiceNS::USERID userId )
{
    MultiServiceNS::SampleUser tmpUser;
    bool bRet = getUserInfo( tmpUser, userId );
    if( bRet )
    {
        bRet = stopSampleExt( userId );
        m_listenMutex.lock();
        QVector<MultiServiceNS::USERID> users = listenUsers[tmpUser.eSampleType];
        int index = users.indexOf( userId );
        if( index >= 0 )
        {
            users.remove( index );
        }
        listenUsers[tmpUser.eSampleType] = users;
        m_listenMutex.unlock();
        logInfo("stop sample.");
    }
    else
    {
        //cann't find user
        bRet = false;
        logInfo("stop sample failed.");
    }
    return bRet;
}

/*************************************************
功能： 添加采集业务对应的用户ID
输入参数：
        userId -- 用户ID
返回：
        是否添加成功
*************************************************/
bool MultiUserService::addUserId( MultiServiceNS::USERID userId )
{
    MultiServiceNS::SampleUser tmpUser;
    bool bRet = getUserInfo( tmpUser, userId );
    if( bRet )
    {
        m_listenMutex.lock();
        listenUsers[tmpUser.eSampleType] <<  userId;
        m_listenMutex.unlock();
    }
    else
    {
        //cann't find user
        bRet = false;
    }

    return bRet;
}

/*************************************************
功能： 获取下一个可用的用户ID
返回：
      下一个可用的用户ID
*************************************************/
MultiServiceNS::USERID MultiUserService::createNextUserID()
{
    return (++s_uiNextID);
}

/*************************************************
功能： 获取指定类型下采集用户的集合
输入参数：
        eType -- 采集类型
返回：
        该类型下采集用户的集合
*************************************************/
QVector<MultiServiceNS::USERID> MultiUserService::sampleUsers( MultiServiceNS::SpectrumType eType )
{
    QVector<MultiServiceNS::USERID> users;
    m_listenMutex.lock();
    users = listenUsers.value( eType );
    m_listenMutex.unlock();
    return users;
}

bool MultiUserService::getUserInfo( MultiServiceNS::SampleUser & userInfo, MultiServiceNS::USERID userId )
{
    bool bFinded = false;
    m_userMutex.lock();
    MultiServiceNS::SampleUser* pUser = registerUser.value( userId );
    if( NULL != pUser )
    {
        memcpy( &userInfo, pUser, sizeof(MultiServiceNS::SampleUser) );
        bFinded = true;
    }
    else
    {
        bFinded = false;
    }
    m_userMutex.unlock();
    return bFinded;
}


bool MultiUserService::startSampleExt(MultiServiceNS::USERID userId)
{
    Q_UNUSED(userId);
    return true;
}

bool MultiUserService::stopSampleExt(MultiServiceNS::USERID userId)
{
    Q_UNUSED(userId);
    return true;
}

void MultiUserService::spectrumType2WorkMode(const MultiServiceNS::SpectrumType &eType, WorkMode &eWorkMode)
{
    switch (eType)
    {
    case MultiServiceNS::SPECTRUM_AE_AMP:
        eWorkMode = Amplitude;
        break;
    case MultiServiceNS::SPECTRUM_AE_PHASE:
        eWorkMode = PRPD;
        break;
    case MultiServiceNS::SPECTRUM_AE_FLY:
        eWorkMode = Pulse;
        break;
    case MultiServiceNS::SPECTRUM_AE_WAVE:
        eWorkMode = Waveform;
        break;
    case MultiServiceNS::SPECTRUM_AE_ALL:
        eWorkMode = AllViews;
        break;
    default:
        break;
    }
}



