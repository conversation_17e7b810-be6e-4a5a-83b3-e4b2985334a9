/*
* Copyright (c) 2016.12，南京华乘电气科技有限公司
* All rights reserved.
*
* pmviewconfig.h
*
* 初始版本：1.0
* 作者：赵勇军
* 修改日期：2017年5月18日
*       新版本重构
* 摘要：外设匹配界面配置信息
*       按钮配置等
* 当前版本：1.0
*/
#ifndef PMVIEWCONFIG_H
#define PMVIEWCONFIG_H

#include "controlButton/ControlButtonInfo.h"
#include "pmview.h"

#define PM_VIEW_CONFIG_TRANSLATE( str ) qApp->translate("PMView",(str))

namespace PMViewConfig {

const char* const CONTEXT = "PMView";//PM VIEW 域
const char* const DEVICE_NAME_SELECT[] = {QT_TRANSLATE_NOOP_UTF8("PMView","UHF Signal Processor"),
                                          QT_TRANSLATE_NOOP_UTF8("PMView","HFCT Signal Processor"),
                                          QT_TRANSLATE_NOOP_UTF8("PMView","Sync. Signal Processor"),
                                          QT_TRANSLATE_NOOP_UTF8("PMView","Receiver Pairing"),
                                          QT_TRANSLATE_NOOP_UTF8("PMView","External Pairing"),
                                          QT_TRANSLATE_NOOP_UTF8("PMView","AE Signal Processor"),
                                          QT_TRANSLATE_NOOP_UTF8("PMView","TEV Signal Processor"),
                                          QT_TRANSLATE_NOOP_UTF8("PMView","Current Signal Processor")
                                         };

const char* const CA_DEVICE_SELECT = QT_TRANSLATE_NOOP_UTF8("PMView","CA Diag. Signal Processor");
const char* const CONNECT_DEVICE_SUCCESS = QT_TRANSLATE_NOOP_UTF8("PMView","Paired!");
const char* const CONNECT_DEVICE_FAILED = QT_TRANSLATE_NOOP_UTF8("PMView","Failed pairing, try again.");
const char* const CONNECT_DEVICE_BUSY = QT_TRANSLATE_NOOP_UTF8("PMView","Already connected.");
const char* const NO_CA_DEVICE = QT_TRANSLATE_NOOP_UTF8("PMView","No paired CA Diag. processor.");
const char* const DISCONNECT_CURRENT_DEVICE_SUCCESS= QT_TRANSLATE_NOOP_UTF8("PMView","Disconnected!");
const char* const DISCONNECT_CURRENT_DEVICE_FAILED = QT_TRANSLATE_NOOP_UTF8("PMView","Disconnect fail!");
const char* const UNCONNECTED_CONDITIONER = QT_TRANSLATE_NOOP_UTF8("PMView","Signal Processor is disconnected!");
const char* const NO_DEVICE_NAME_SELECT[] = {QT_TRANSLATE_NOOP_UTF8("PMView","UHF Signal Processor not found!"),
                                             QT_TRANSLATE_NOOP_UTF8("PMView","HFCT Signal Processor not found!"),
                                             QT_TRANSLATE_NOOP_UTF8("PMView","Sync. not found!"),
                                             QT_TRANSLATE_NOOP_UTF8("PMView","Receiver not found!"),
                                             QT_TRANSLATE_NOOP_UTF8("PMView","External not found!"),
                                             QT_TRANSLATE_NOOP_UTF8("PMView","AE Signal Processor not found!"),
                                             QT_TRANSLATE_NOOP_UTF8("PMView","TEV Signal Processor not found!"),
                                             QT_TRANSLATE_NOOP_UTF8("PMView","Current Signal Processor not found!")
                                            };
const char* const CONNECT_OTHER_DEVICE = QT_TRANSLATE_NOOP_UTF8("PMView","Already connected other host.");

}

#endif // PMVIEWCONFIG_H
