/*
* Copyright (c) 2016.03，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: IntelligentPatrolDataSave.h
*
* 初始版本：1.0
* 作者：曹山
* 创建日期：2016年03月23日
* 摘要：该文件主要是定义了智能巡检类，及智能巡检相关数据结构
*/


#ifndef INTELLIGENTPATROLDATASAVE_H
#define INTELLIGENTPATROLDATASAVE_H

#include "ae/dataSave/AEAMPWithoutMap.h"
#include "tev/dataSave/TEVAmpWithoutMap.h"
#include "uhf/dataSave/UHFAmpWithoutMap.h"
#include "hfct/dataSave/HFCTAmpWithoutMap.h"
#include <QMap>

const INT8 NO_NEED_TEST = -128;  //不需测试
const INT8 NOT_TESTED = -127;    //未测试

const int MAX_TEST_POINTS = 11;  //最大测试点数

//测试点测试状态
typedef enum _TestState
{
    STATE_NOT_TESTED = 0,              //未测
    STATE_ALREADY_TESTED = 1,          //已测
    STATE_NO_NEED_TEST = 2             //不测
}TestState;

//天气
typedef enum _Weather
{
    WEATHER_NOT_TESTED = -128,     //未测试
    WEATHER_SUNSHINE = 0,          //晴天
    WEATHER_OVERCAST = 1,          //阴天
    WEATHER_RAIN = 2,              //雨天
    WEATHER_SNOW = 3,              //雪天
    WEATHER_FOGGY = 4,             //雾天
    WEATHER_THUNDERSTORM = 5,      //雷雨
    WEATHER_CLOUDY = 6,            //多云
    WEATHER_COUNT
}Weather;

//环境信息
typedef struct _EnvironmentInfo
{
    Weather eWeather;   //天气,-127未测试;1晴天;2阴天;3雨天;4雪天;5雾天;6雷雨;7多云
    INT8 cTemp;         //温度,-127未测试;温度范围为-100~100
    INT8 cHumidity;     //湿度,-127未测试;湿度度范围为0~100
}EnvironmentInfo;

//背景信息
typedef struct BackgroundInfo
{
    INT8 cAirValue;    //空气背景,-127（0x81）表示未测试;TEV有效数值范围-1~61,-1表示<0dB,61表示>60dB
    INT8 cMetalBgn1;   //金属背景1,-128~127;-127（0x81）表示未测试;TEV有效数值范围-1~61,-1表示<0dB,61表示>60dB
    INT8 cMetalBgn2;   //金属背景2
    INT8 cMetalBgn3;   //金属背景3
    INT8 cAEBgn;       //AE背景,有效数值范围-10~59dB
    INT8 cUHFBgn;      //UHF背景，[-1,71]
    INT8 cHFCTBgn;     //HFCT背景
}BackgroundInfo;

/*
 * 测试部位,幅值范围: -128~127;
 * -128(0x80)表示不测试;-127（0x81）表示未测试;
 * TEV有效数值范围-1~61,-1表示<0dB，61表示>60dB
*/
typedef struct _TestPointsValue
{
    INT8 cFrontCenterTEVAmp;  //前中幅
    INT8 cFrontBottomTEVAmp;  //前下幅
    INT8 cBackTopTEVAmp;      //后上幅
    INT8 cBackCenterTEVAmp;   //后中幅
    INT8 cBackBottomTEVAmp;   //后下幅
    INT8 cSideTopTEVAmp;      //侧上幅
    INT8 cSideCenterTEVAmp;   //侧中幅
    INT8 cSideBottomTEVAmp;   //侧下幅
    INT8 cAEAmp;              //AE幅值
    INT8 cUHFAmp;             //UHF幅值
    INT8 cHFCTAmp;            //HFCT幅值
}TestPointsValue;

//测试点
typedef enum _TestPoints
{
    FRONT_CENTER = 0,         //前中
    FRONT_BOTTOM,             //前下
    BACK_TOP,                 //后上
    BACK_CENTER,              //后中
    BACK_BOTTOM,              //后下
    SIDE_TOP,                 //侧上 5
    SIDE_CENTER,              //侧中 6
    SIDE_BOTTOM,              //侧下 7
    AE_AMPLITUDE_POINT,       //AE 8
    UHF_AMPLITUDE_POINT,      //UHF 9
    HFCT_AMPLITUDE_POINT,     //HFCT 10
    POINTS_COUNT              //总数
}TestPoints;

//测试点信息
typedef struct _TestPointsInfo
{
    QString strName;          //测试点名称
    TestPoints eTestPoints;   //测试点枚举
}TestPointsInfo;

//11个测试点信息
const TestPointsInfo s_stPointsInfo[] =
{
    {"FrontCenter", FRONT_CENTER},
    {"FrontBottom", FRONT_BOTTOM},
    {"BackTop", BACK_TOP},
    {"BackCenter", BACK_CENTER},
    {"BackBottom", BACK_BOTTOM},
    {"SideTop", SIDE_TOP},
    {"SideCenter", SIDE_CENTER},
    {"SideBottom", SIDE_BOTTOM},
    {"AE", AE_AMPLITUDE_POINT},
    {"UHF", UHF_AMPLITUDE_POINT},
    {"HFCT", HFCT_AMPLITUDE_POINT}
};

//预警、报警值
typedef struct _WarningAlarmInfo
{
    UINT8 ucTEVAlarm;         //TEV报警值，Warning <ALARM≤60
    UINT8 ucTEVWarning;       //TEV预警值，1≤ Warning<ALARM
    INT8 cAEAlarm;            //AE报警值，Warning <ALARM≤ 59
    INT8 cAEWarning;          //AE预警值，-10≤ Warning<ALARM
    UINT8 ucUHFAlarm;         //UHF报警值，1≤ Warning<ALARM
    UINT8 ucUHFWarning;       //UHF预警值，1≤BACKGROUND<ALARM
    UINT8 ucHFCTAlarm;        //HFCT报警值，Warning <ALARM≤80
    UINT8 ucHFCTWarning;      //HFCT预警值，1≤ Warning<ALARM
}WarningAlarmInfo;

//背景参数信息
typedef struct _BgParamInfo
{
    UINT8 ucAEChannel;        //超声通道类型（0x00：内置，0x01：外置，0x02：变压器无线）
    UINT8 ucAEGain;           //AE放大器放大倍数（1、10或100）
    UINT8 ucUHFBandWidth;     //UHF带宽, 0：全 1：低 2：高
    UINT8 ucUHFPregain;       //UHF前置增益，1:关 0：开
    INT8 cHFCTGain;           //HFCT增益，(0,20,40,60），显示幅值=测试数据GAIN
}BgParamInfo;

class IntelligentPatrolDataSave
{
public:
    /************************************************
     * 函数名   : IntelligentPatrolDataSave
     * 输入参数 : strFile: 文件名
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 构造函数
     ************************************************/
    IntelligentPatrolDataSave();

    /************************************************
     * 函数名   : ~IntelligentPatrolDataSave
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 析构函数
     ************************************************/
    ~IntelligentPatrolDataSave();

    /************************************************
     * 函数名   : setMissionFile
     * 输入参数 : strFile: 开关柜巡检任务文件名
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置开关柜巡检任务文件
     ************************************************/
    void setMissionFile(const QString& strFile);

    /************************************************
     * 函数名   : getTopStoragePath
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 路径名
     * 功能     : 获取开关柜巡检任务文件最上层存储路径
     ************************************************/
    const QString getTopStoragePath(void);

    /************************************************
     * 函数名   : savePointData
     * 输入参数 : strDevice: 设备名; eTestPoints: 测试点; pData: 数据, 结构体类型，包含数据值、增益、测试时间等信息
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 保存测试点数据
     ************************************************/
    void savePointData(const QString& strDevice, TestPoints eTestPoints, void *pData);

    /************************************************
     * 函数名   : setEnvironment
     * 输入参数 : stEnvironmentInfo: 环境信息
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 保存环境信息
     ************************************************/
    void saveEnvironment(const EnvironmentInfo& stEnvironmentInfo);

    /************************************************
     * 函数名   : saveBackground
     * 输入参数 : stBackgroundInfo: 背景数据
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 保存背景数据信息
     ************************************************/
    void saveBackground(const BackgroundInfo& stBackgroundInfo);

    /************************************************
     * 函数名   : saveBgParam
     * 输入参数 : stBgParamInfo: 背景参数
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 保存背景参数
     ************************************************/
    void saveBgParam(const BgParamInfo& stBgParamInfo);

    /************************************************
     * 函数名   : getWarningAlarm
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 预警、报警值信息
     * 功能     : 获取预警、报警值信息
     ************************************************/
    const WarningAlarmInfo& getWarningAlarm(void);

    /************************************************
     * 函数名   : getEnvironment
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 环境信息
     * 功能     : 获取环境信息
     ************************************************/
    EnvironmentInfo getEnvironment(void);

    /************************************************
     * 函数名   : getBackground
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 背景数据
     * 功能     : 获取背景数据
     ************************************************/
    BackgroundInfo getBackground(void);

    /************************************************
     * 函数名   : getBgParam
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 背景参数
     * 功能     : 获取背景参数
     ************************************************/
    BgParamInfo getBgParam(void);

    /************************************************
     * 函数名   : getStationName
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 变电站名
     * 功能     : 获取变电站名
     ************************************************/
    const QString& getStationName(void);

    /************************************************
     * 函数名   : getTester
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 测试人
     * 功能     : 获取测试人
     ************************************************/
    QString getTester(void);

    /************************************************
     * 函数名   : getStationName
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 电压等级
     * 功能     : 获取电压等级
     ************************************************/
    QString getVoltageDegree(void);

    /************************************************
     * 函数名   : getStationName
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 测试时间
     * 功能     : 获取测试时间
     ************************************************/
    QString getTestTime(void);

    /************************************************
     * 函数名   : getDevicesName
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 设备名列表
     * 功能     : 获取设备名列表
     ************************************************/
    const QStringList& getDevicesName(void);

    /************************************************
     * 函数名   : getPointsValue
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 测试点数据结构体
     * 功能     : 获取单个设备所有测试点数据
     ************************************************/
    TestPointsValue getPointsValue(const QString& strDevice);

    /************************************************
     * 函数名   : getPointData
     * 输入参数 : strDevice: 设备名; eTestPoints: 测试点;
     * 输出参数 : pData: 数据
     * 返回值   : 获取结果
     * 功能     : 获取指定设备里指定测试点的数据，数据是结构体类型，包含数据值、增益、测试时间等信息
     ************************************************/
    bool getPointData(const QString& strDevice, TestPoints eTestPoints, void *pData);


    void organizeHeadData();

private:
    /************************************************
     * 函数名   : readWarningAlarm
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 从测试任务文件中读取预警、报警值信息
     ************************************************/
    void readWarningAlarm(void);

    /************************************************
     * 函数名   : updateTestedItems
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 更新已经测试的测试点数量
     ************************************************/
    void updateTestedItems(void);

    /************************************************
     * 函数名   : getDescFile
     * 输入参数 : strFile: 开关柜巡检测试任务文件名
     * 输出参数 : NULL
     * 返回值   : 测试任务描述文件名
     * 功能     : 获取测试任务描述文件名, 包含绝对路径
     ************************************************/
    QString getDescFile(QString strFile);

    /************************************************
     * 函数名   : getStationInfo
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 获取变电站信息
     ************************************************/
    void getStationInfo(void);

    /************************************************
     * 函数名   : saveHead
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置xml文件中Head信息
     ************************************************/
    void setHead(void);

    /************************************************
     * 函数名   : readDevices
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 读取所有设备名
     ************************************************/
    void readDevices(void);

    /************************************************
     * 函数名   : initPointsMap
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 初始化测试点信息表
     ************************************************/
    void initPointsMap(void);

    /************************************************
     * 函数名   : readTestPointValue
     * 输入参数 : strName
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 读取测试点的幅值
     ************************************************/
    void readTestPointValue(QString strName);

    /************************************************
     * 函数名   : parsePointValue
     * 输入参数 : strData: 测试数据
     * 输出参数 : NULL
     * 返回值   : 幅值
     * 功能     : 解析测试数据
     ************************************************/
    INT8 parsePointValue(QString strData);

    /************************************************
     * 函数名   : resetPointsValue
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 重置单个设备所有测试点幅值
     ************************************************/
    void resetPointsValue(void);

    /************************************************
     * 函数名   : setPointsValue
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 设置单个设备所有测试点幅值
     ************************************************/
    void setPointsValue(void);

    /************************************************
     * 函数名   : matchDevice
     * 输入参数 : strDevice: 设备名;
     * 输出参数 : iIndex: 设备索引号
     * 返回值   : 匹配结果
     * 功能     : 匹配设备
     ************************************************/
    bool matchDevice(const QString& strDevice, int *iIndex);

    /************************************************
     * 函数名   : getPointName
     * 输入参数 : eTestPoints: 测试点;
     * 输出参数 : NULL
     * 返回值   : 测试点名
     * 功能     : 获取测试点名称
     ************************************************/
    const QString getPointName(TestPoints eTestPoints);

    /************************************************
     * 函数名   : setPointData
     * 输入参数 : eTestPoints: 测试点; pData: 数据
     * 输出参数 : NULL
     * 返回值   : 转换后的数据
     * 功能     : 设置测试点数据，将数据转换成字节流
     ************************************************/
    QByteArray setPointData(TestPoints eTestPoints, void *pData);

    /************************************************
     * 函数名   : parseData
     * 输入参数 : eTestPoints: 测试点; baData: 数据字节流
     * 输出参数 : pData: 数据
     * 返回值   : void
     * 功能     : 解析数据，将数据字节流转换成指定格式的结构体
     ************************************************/
    void parseData(TestPoints eTestPoints, const QByteArray& baData, void *pData, const QString& strFileName = "");

    /************************************************
     * 函数名   : getValueFromString
     * 输入参数 : strValue: xml节点的值
     * 输出参数 : NULL
     * 返回值   : 转换后的值
     * 功能     : 将xml节点的QString值转换成INT8的值
     ************************************************/
    INT8 getValueFromString(QString strValue);


    void saveTestPointHeadData(QDomElement testPoint, const QString &qsTestPointName);

private:
    WarningAlarmInfo m_stWarningAlarmInfo;     //预警、报警值信息
    BgParamInfo m_stBgParamInfo;               //背景参数信息
    BackgroundInfo m_stBackgroundInfo;         //背景值信息
    EnvironmentInfo m_stEnvironmentInfo;       //环境信息

    TestPointsValue m_stTestPointsValue;       //单个设备下的所有测试点值结构体

    AEAmpWithoutMap m_AEAmpWithoutMap;         //AE幅值检测数据保存对象
    TEVAmpWithoutMap m_TEVAmpWithoutMap;       //TEV幅值检测数据保存对象
    UHFAmpWithoutMap m_UHFAmpWithoutMap;       //UHF幅值检测数据保存对象
    HFCTAmpWithoutMap m_HFCTAmpWithoutMap;     //HFCT幅值检测数据保存对象

    QString m_strMissionFile;                  //开关柜巡检任务文件名
    QString m_strDescFile;                     //测试任务描述文件名
    QString m_strStation;                      //变电站名
    QStringList m_listDevices;                 //设备名列表
    UINT32 m_uiTestedItems;                    //已经测试的测试点数量

    QList<QDomElement> m_deviceElementList;    //设备节点列表
    QDomElement m_root;                        //文档根节点
    XMLDocument *m_pDoc;                       //文档

    QMap<QString,TestPoints> m_mapPointsInfo;  //测试点信息键值对
    INT8 m_acTestedValue[MAX_TEST_POINTS];     //单个设备下的所有测试点值数组

};

#endif // INTELLIGENTPATROLDATASAVE_H
