#ifndef TEVPRPSSERVICE_H
#define TEVPRPSSERVICE_H

#include <QObject>
#include <QThread>
#include "tevdefine.h"
#include "multiservice/multiuserservice.h"
#include "datadefine.h"
#include "module_global.h"

class MODULESHARED_EXPORT TevPRPSService : public MultiUserService
{
    Q_OBJECT
public:
    /****************************
    功能： 模块单例
    *****************************/
    static TevPRPSService* instance();

    /*************************************************
    函数名： start()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 启动业务
    *************************************************************/
    bool start();

    /*************************************************
    功能： 启动业务
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    bool isStart( void );

    /*************************************************
    函数名： stop()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 终止业务
    *************************************************************/
    bool stop();

    /*************************************************
    函数名： setWorksets(TEVWorkSettings &stTEVWorkSettings)
    输入参数： stTEVWorkSettings：TEV工作参数
    输出参数： NULL
    返回值： 操作结果
    功能： 设置工作参数
    *************************************************************/
    bool setWorksets(TEVWorkSettings &stTEVWorkSettings);

protected:
    /*************************************************
    功能： 启动采样业务的额外处理   （一般指在子类中的额外操作）
    输入参数：
            userId -- 用户ID
    返回：
          额外处理是否成功
    *************************************************/
    virtual bool startSampleExt( MultiServiceNS::USERID userId );

    /*************************************************
    功能： 停止采样业务的额外处理   （一般指在子类中的额外操作）
    输入参数：
            userId -- 用户ID
    返回：
          额外处理是否成功
    *************************************************/
    virtual bool stopSampleExt( MultiServiceNS::USERID userId );
signals:
    /*************************************************
    传递参数： data：脉冲数据
    说明： 脉冲数据信号
    *************************************************************/
    void sigData(TEV::PRPSData data,MultiServiceNS::USERID userId);

    void sigReadTEVPRPSDataFailed( MultiServiceNS::USERID userId );

    void sigStopSample();
    void sigStartSample();

private slots:
    void onStopSample();
    void onStartSample();

protected:
    /*************************************************
    函数名： timerEvent(QTimerEvent *event)
    输入参数： event：定时器事件
    输出参数： NULL
    返回值： NULL
    功能： 定时器事件处理
    *************************************************************/
    void timerEvent(QTimerEvent *event);

private:
    /*************************************************
    函数名： singleSample()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 单次采集
    *************************************************************/
    void singleSample();

    /*************************************************
    函数名： TevPRPSService()
    功能： 构造函数
    *************************************************************/
    explicit TevPRPSService();

    /*************************************************
    函数名： ~TevPRPSService()
    功能： 析构函数
    *************************************************************/
    ~TevPRPSService();

    /****************************
    功能： disable 拷贝
    *****************************/
    TevPRPSService( const TevPRPSService& other );

    /****************************
    功能： disable 赋值
    *****************************/
    TevPRPSService & operator = (const TevPRPSService &);
private:
    QThread *m_pThread;
    QMutex m_mutexAffair;  //事务互斥锁

    volatile bool m_bIsSampling;  //是否正在采集标识
    TEV::WorkMode m_eMode;
    int m_iTimer;
    UINT8 m_ucReadTEVPRPSDataFailedTimes;
};

#endif // TEVPRPSSERVICE_H
