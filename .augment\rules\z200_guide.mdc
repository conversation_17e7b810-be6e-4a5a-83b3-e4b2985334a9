---
description:
globs:
alwaysApply: false
---
# Z200应用程序指南

## 应用概述
Z200是项目的主应用程序，提供完整的用户界面和功能。这是一个基于Qt框架的多模块应用程序，支持多语言，并提供各种分析和监测功能。

## 主要模块
- `app/` - 应用程序入口和主窗口
- `core/` - 核心功能和基础组件
- `module/` - 功能模块实现
- `service/` - 服务层实现
- `view/` - 用户界面视图
- `widget/` - 自定义控件
- `mobileAccess/` - 移动端访问支持

## 项目文件
- [z200.pro](mdc:Z200/z200.pro) - 主项目文件，组织子项目

## 国际化
Z200支持多语言，使用Qt的翻译系统。主要语言文件：
- [language_zh_cn.ts](mdc:Z200/language_zh_cn.ts) - 简体中文
- [language_en_us.ts](mdc:Z200/language_en_us.ts) - 英文（美国）
- 其他支持的语言包括繁体中文、韩文、越南语、西班牙语、葡萄牙语、德语、法语、俄语和阿拉伯语

## 模块间依赖
- `app` 依赖 `view`、`widget` 和 `module`
- `view` 依赖 `widget` 和 `service`
- `service` 依赖 `core`
- `module` 依赖 `service` 和 `core`

## 构建顺序
构建顺序已在项目中用 `CONFIG += ordered` 指定：
1. `core`
2. `service`
3. `module`
4. `mobileAccess`
5. `widget`
6. `view`
7. `app`

## 开发指南
### 添加新功能
1. 确定功能所属模块
2. 在相应模块中创建新的类
3. 按照现有代码风格实现功能
4. 在需要的地方集成新功能

### 用户界面开发
- 使用Qt的UI设计器创建界面
- 遵循现有的界面风格和布局
- 确保所有字符串可翻译（使用tr()函数）

### 多语言支持
- 所有面向用户的字符串都应使用 `tr()` 包装
- 添加新字符串后，运行 `lupdate` 更新翻译文件
- 完成翻译后，运行 `lrelease` 生成翻译资源

### 移动支持
- 通过 `mobileAccess` 模块提供移动端功能
- 确保关键功能可通过移动接口访问
- 针对移动端优化界面布局
