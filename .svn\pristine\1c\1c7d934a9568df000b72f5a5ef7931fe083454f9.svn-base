#include "fileoperutil.h"
#include <QDebug>
#include <QProcess>
#include <QTextCodec>
#include <QString>
#include <QStringList>
#include <QIODevice>
#include <QFileInfo>
#include <QDir>
#include <QFile>
#include <QDateTime>
#include <QList>
#include "datadefine.h"
#include "log/log.h"
#ifdef  Q_PROCESSOR_ARM
#include <sys/vfs.h>
#include <unistd.h>
#endif

namespace FileOperUtil
{

#define DATA_1K 1024

bool checkFileOrDirExist(const QString qstrFileDirOrPath)
{
    refreshToSystemDisk();

    bool bRet = false;
    QFileInfo objFileInfo(qstrFileDirOrPath);
    if(objFileInfo.isFile())
    {
        bRet = objFileInfo.exists();
    }
    else if(objFileInfo.isDir())
    {
        bRet = objFileInfo.exists();
    }
    else
    {
        bRet = false;
    }

    return bRet;
}

bool createFile(const QString qstrFilePath)
{
    if(checkFileOrDirExist(qstrFilePath))
    {
        return true;
    }

    createDirectory(getFileParentFolder(qstrFilePath));
    QFile objFile(qstrFilePath);
    if(!objFile.open(QIODevice::ReadWrite))
    {
        qDebug() << "File create failed." << endl;
        return false;
    }
    objFile.close();
    refreshToSystemDisk();
    return true;
}

bool createDirectory(const QString qstrFileDir)
{
    if(checkFileOrDirExist(qstrFileDir))
    {
        return true;
    }

    QDir objDir;
    bool bRet = objDir.mkpath(qstrFileDir);
    refreshToSystemDisk();
    return bRet;
}

bool deleteFile(const QString qstrFilePath)
{
    if(!checkFileOrDirExist(qstrFilePath))
    {
        return true;
    }
    bool bRet = QFile::remove(qstrFilePath);
    refreshToSystemDisk();
    return bRet;
}

bool deleteDir(const QString qstrFileDir)
{
    if(qstrFileDir.isEmpty())
    {
        qDebug() << "File directory is empty." << endl;
        return false;
    }

    if(!checkFileOrDirExist(qstrFileDir))
    {
        qDebug() << "File directory is not exist." << endl;
        return true;
    }

    QDir objDir(qstrFileDir);
    objDir.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden | QDir::Readable | QDir::Writable);      //set filter
    QFileInfoList lstFileInfo = objDir.entryInfoList();             //get all file info
    foreach(QFileInfo objFileInfo, lstFileInfo)
    {
        if(objFileInfo.isFile() || objFileInfo.isSymLink())
        {
            QFile::setPermissions(objFileInfo.absoluteFilePath(), QFile::ReadUser | QFile::WriteUser);
            objFileInfo.dir().remove(objFileInfo.fileName());
        }
        else
        {
            deleteDir(objFileInfo.absoluteFilePath());
        }
    }

    bool bRet = objDir.rmdir(objDir.absolutePath());            //delete file folder, rmpath will remove all the path
    refreshToSystemDisk();

    return bRet;
}

bool copyOrMoveFile(const QString qstrDstPath, const QString qstrSrcPath, bool bMove)
{
    if(!checkFileOrDirExist(qstrSrcPath))
    {
        qDebug() << "Source file path is not exist." << endl;
        //return false;
        return true;        //due to accommodate with the project requirement, modify return true.
    }

    if(qstrDstPath.isEmpty())
    {
        qDebug() << "Destination directory path is empty." << endl;
        return false;
    }

    if(QString::compare(qstrDstPath, qstrSrcPath) == 0)
    {
        return true;
    }

    QString qstrDstDir = getFileParentFolder_EX(qstrDstPath);
    if(!checkFileOrDirExist(qstrDstDir))
    {
        //create the parent folder
        if(!createDirectory(qstrDstDir))
        {
            qDebug() << "create the folder (" << qstrDstDir << ") failed" << endl;
            return false;
        }
    }

    if(checkFileOrDirExist(qstrDstPath))
    {
        deleteFile(qstrDstPath);
    }

    if(bMove)
    {
        QFile objFile(qstrSrcPath);
        if(!objFile.rename(qstrDstPath))
        {
            qDebug() << "move file (" << qstrSrcPath << ") to (" << qstrDstPath << ") failed." << endl;
            return false;
        }
    }
    else
    {
        if(!(QFile::copy(qstrSrcPath, qstrDstPath)))
        {
            qDebug() << "copy file (" << qstrSrcPath << ") to (" << qstrDstPath << ") failed." << endl;
            return false;
        }
    }

    refreshToSystemDisk();

    return true;
}

bool copyOrRenameDir(const QString qstrDstDir, const QString qstrSrcDir, bool bRename)
{
    if(!checkFileOrDirExist(qstrSrcDir))
    {
        qDebug() << "Source file directory is not exist." << endl;
        return false;
    }

    if(qstrDstDir.isEmpty())
    {
        qDebug() << "Destination directory path is empty." << endl;
        return false;
    }

    if(QString::compare(qstrDstDir, qstrSrcDir) == 0)
    {
        return true;
    }

    QDir objDstDir(qstrDstDir);
    QDir objSrcDir(qstrSrcDir);

    if(bRename)
    {
        if(!objSrcDir.rename(qstrSrcDir, qstrDstDir))
        {
            qDebug() << "rename (" << qstrSrcDir << ") to (" << qstrDstDir << ") failed." << endl;
            return false;
        }
    }
    else
    {
        if(!checkFileOrDirExist(qstrDstDir))
        {
            if(!createDirectory(qstrDstDir))
            {
                qDebug() << "Create destination file directory fail." << endl;
                return false;
            }
        }

        objSrcDir.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);       //set filter
        QFileInfoList lstFileInfo = objSrcDir.entryInfoList();              //get all file info
        foreach(QFileInfo objFileInfo, lstFileInfo)
        {
            if("." == objFileInfo.fileName() || ".." == objFileInfo.fileName())
            {
                continue;
            }

            if(objFileInfo.isDir())
            {
                if(!copyDirFiles(objDstDir.filePath(objFileInfo.fileName()), objFileInfo.filePath()))
                {
                    return false;
                }
            }
            else
            {
                if(objDstDir.exists(objFileInfo.fileName()))
                {
                    objDstDir.remove(objFileInfo.fileName());
                }

                if(!QFile::copy(objFileInfo.filePath(), objDstDir.filePath(objFileInfo.fileName())))
                {
                    return false;
                }
            }
        }
    }

    refreshToSystemDisk();

    return true;
}

bool copyFileToPath(const QString qstrDstPath, const QString qstrSrcPath)
{
    return copyOrMoveFile(qstrDstPath, qstrSrcPath, false);
}

bool copyDirFiles(const QString qstrDstDir, const QString qstrSrcDir)
{
    return copyOrRenameDir(qstrDstDir, qstrSrcDir, false);
}

bool moveFileToPath(const QString qstrDstPath, const QString qstrSrcPath)
{
    return copyOrMoveFile(qstrDstPath, qstrSrcPath, true);
}

bool renameDirectory(const QString qstrDstDir, const QString qstrSrcDir)
{
    return copyOrRenameDir(qstrDstDir, qstrSrcDir, true);
}

bool readFile(const QString& qstrFilePath, QByteArray& qbaFileContent)
{
    refreshToSystemDisk();

    QFile objFile(qstrFilePath);
    if(!objFile.open(QIODevice::ReadOnly))
    {
        qDebug() << "open file failed." << endl;
        return false;
    }

    qbaFileContent.clear();
    qbaFileContent.append(objFile.readAll());
    objFile.flush();
    objFile.close();

    return true;
}

bool writeFile(const QString& qstrFilePath, const QByteArray& qbaFileContent, bool bCoverContent)
{
    if(!checkFileOrDirExist(qstrFilePath))
    {
        if(!createFile(qstrFilePath))
        {
            return false;
        }
    }

    QFile objFile(qstrFilePath);

    //set the open mode
    if(!bCoverContent)
    {
        //objFile.seek(objFile.size());
        //append the content
        if(!objFile.open(QIODevice::Append))
        {
            qDebug() << "open file failed." << endl;
            return false;
        }
    }
    else
    {
        //cover the content
        if(!objFile.open(QIODevice::ReadWrite))
        {
            qDebug() << "open file failed." << endl;
            return false;
        }

        objFile.resize(0);
    }

    bool bRet = true;
    if(-1 == objFile.write(qbaFileContent))
    {
        qDebug() << "Write to file failed." << endl;
        bRet = false;
    }

    objFile.flush();
    objFile.close();
    refreshToSystemDisk();

    return bRet;
}

qint64 getFileSize(const QString qstrFilePath)
{
    refreshToSystemDisk();

    if(!checkFileOrDirExist(qstrFilePath))
    {
        qDebug() << "File path is not exist." << endl;
        return 0;
    }

    QFile objFile(qstrFilePath);
    qint64 i64Size = 0;
    if(!(objFile.open(QIODevice::ReadOnly)))
    {
        qDebug() << "Open file fail." << endl;
        return 0;
    }

    i64Size = objFile.size();
    objFile.close();

    return i64Size;
}

qint64 getDirSize(const QString qstrDirPath)
{
    refreshToSystemDisk();

    QDir objDir(qstrDirPath);
    if(!objDir.exists())
    {
        qDebug() << "Directory is not exist." << endl;
        return 0;
    }
    quint64 qui64Size = 0;

    foreach(QFileInfo objFileInfo, objDir.entryInfoList(QDir::Files))
    {
        qui64Size += objFileInfo.size();
    }

    foreach(QString qstSubDir, objDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot))
    {
        qui64Size += getDirSize(qstrDirPath + QDir::separator() + qstSubDir);
    }

    return qui64Size;
}

QString formatFileDir(QString qstrFileDir)
{
    qstrFileDir.replace("\\", "/");
    return (qstrFileDir.endsWith("/") ? qstrFileDir : (qstrFileDir + "/"));
}

QString getFileName(QString qstrFilePath)
{
    //qDebug() << "FileOperUtil::getFileName, " << qstrFilePath << endl;
    QFileInfo objFileInfo(qstrFilePath);
    return objFileInfo.fileName();
}

QString getFileName_EX(QString qstrFilePath)
{
    qstrFilePath.replace("\\", "/");
    return qstrFilePath.right(qstrFilePath.length() - qstrFilePath.lastIndexOf("/") - 1);
}

/************************************************
 * 功能：根据文件全路径获取文件扩展名，带"."，如.jpg
 * 输入参数：
 *      qstrFilePath -- 文件全路径，文件可以是不存在的
 * 返回：
 *      QString -- 文件扩展名
 ************************************************/
QString getFileSuffix(QString qstrFilePath)
{
    QString qstrSufix = "";

    if(!qstrFilePath.isEmpty())
    {
        QFileInfo objFileInfo(qstrFilePath);
        qstrSufix = "." + objFileInfo.suffix();
    }

    return qstrSufix;
}

QString getFileParentFolder(QString qstrFilePath)
{
    QFileInfo objFileInfo(qstrFilePath);
    return objFileInfo.absolutePath();
}

QString getFileParentFolder_EX(QString qstrFilePath)
{
    qstrFilePath.replace("\\", "/");
    return qstrFilePath.left(qstrFilePath.lastIndexOf("/"));
}

/***************************************************
 * 功能：获取文件夹下的文件列表
 * 输入参数：
 *      qstrFileFolderPath：文件夹路径
 * 返回值：
 *      QStringList：文件列表
 * **************************************************/
QStringList getFileListUnderFolder(const QString qstrFileFolderPath)
{
    QStringList qslstFilePaths;
    qslstFilePaths.clear();

    do
    {
        refreshToSystemDisk();

        if(!checkFileOrDirExist(qstrFileFolderPath))
        {
            break;
        }

        QDir qdrPath(qstrFileFolderPath);
        qdrPath.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);      //set filter
        QFileInfoList lstFileInfo = qdrPath.entryInfoList();             //get all file info
        foreach(QFileInfo objFileInfo, lstFileInfo)
        {
            if(objFileInfo.isFile())
            {
                qslstFilePaths.append(objFileInfo.absoluteFilePath());
            }
            else if(objFileInfo.isDir())
            {
                qslstFilePaths.append(getFileListUnderFolder(objFileInfo.absoluteFilePath()));
            }
            else
            {
                //
            }
        }

    }while(0);

    return qslstFilePaths;
}

/************************************************
 * 功能：linux系统，刷新到磁盘系统
 * 输入参数：
 * 返回：
 ************************************************/
void refreshToSystemDisk()
{
#if defined(Q_PROCESSOR_ARM) || defined(Q_OS_LINUX)
    sync();
#endif
    return;
}

/************************************************
 * 功能：删除除今天之外的所有日志文件
 * 输入参数：
 * 返回：
 ************************************************/
void clearUnusedLogFiles()
{
    refreshToSystemDisk();

    QString qstrResFileName = QDateTime::currentDateTime().toString("yyyy-MM-dd") + ".log";
    QDir objDir(g_LogFilePath);
    objDir.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);      //set filter
    QFileInfoList lstFileInfo = objDir.entryInfoList();             //get all file info
    foreach(QFileInfo objFileInfo, lstFileInfo)
    {
        if(objFileInfo.isFile() || objFileInfo.isSymLink())
        {
            //保留今天的日志文件
            if(qstrResFileName != objFileInfo.fileName())
            {
                QFile::setPermissions(objFileInfo.absoluteFilePath(), QFile::ReadUser | QFile::WriteUser);
                objFileInfo.dir().remove(objFileInfo.fileName());
            }
        }
        else
        {
            deleteDir(objFileInfo.absoluteFilePath());
        }
    }
    refreshToSystemDisk();
    return;
}

/***************************************************
 * 功能：替换文件的后缀名
 * 输入参数：
 *      qstrFilePath：文件路径
 *      qstrNewSurrfix：新的文件后缀
 * 返回值：
 *      新的文件路径
 * *******************************************************/
QString replaceFileSurrfix(const QString &qstrFilePath, const QString &qstrNewSurrfix)
{
    QString qstrNewFilePath = qstrFilePath.left(qstrFilePath.lastIndexOf('.'));
    qstrNewFilePath += qstrNewSurrfix;
    return qstrNewFilePath;
}

/***************************************************
 * 功能：WAV文件转换为MP3文件
 * 输入参数：
 *      qstrWavFilePath：WAV文件全路径
 *      qstrMp3FilePath：MP3文件全路径
 * **************************************************/
void wav2Mp3(const QString &qstrWavFilePath, const QString &qstrMp3FilePath)
{
    QString qstrCmd = QString("lame %1 %2").arg(qstrWavFilePath).arg(qstrMp3FilePath);
    QProcess::startDetached(qstrCmd);
    return;
}

/**************************************************
 * 功能：删除空的数据文件夹
 * 输入参数：
 *      qstrFilePath：数据文件路径
 * 返回值：
 *      bool：true -- 删除成功，false -- 删除失败
 * **************************************************/
bool clearEmptyDataFolder(const QString& qstrFilePath)
{
    bool bRet = true;
    do
    {
        refreshToSystemDisk();

        QFileInfo stFileInfo(qstrFilePath);
        QDir stDayDir(stFileInfo.absolutePath());
        stDayDir.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);
        QFileInfoList lstDayFileInfo = stDayDir.entryInfoList();
        logInfo(QString("path (%1), count (%2).").arg(stDayDir.absolutePath()).arg(lstDayFileInfo.count()));
        if(lstDayFileInfo.count() > 0)
        {
            break;
        }
        /*logInfo(QString("path (%1), count (%2).").arg(stDayDir.absolutePath()).arg(stDayDir.count()));
        if(stDayDir.count() > 2)
        {
            break;
        }*/
        if(!(stDayDir.rmdir(stDayDir.absolutePath())))
        {
            logError("remove day directory failed.");
            bRet = false;
            break;
        }

        //获得上一级目录
        QFileInfo stDayTmpFile(stDayDir.absolutePath() + ".txt");
        QDir stMonthDir(stDayTmpFile.absolutePath());
        stMonthDir.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);
        QFileInfoList lstMonthFileInfo = stMonthDir.entryInfoList();
        logInfo(QString("path (%1), count (%2).").arg(stMonthDir.absolutePath()).arg(lstMonthFileInfo.count()));
        if(lstMonthFileInfo.count() > 0)
        {
            break;
        }
        /*logInfo(QString("path (%1), count (%2).").arg(stMonthDir.absolutePath()).arg(stMonthDir.count()));
        if(stMonthDir.count() > 2)
        {
            break;
        }*/
        if(!(stMonthDir.rmdir(stMonthDir.absolutePath())))
        {
            logError("remove month directory failed.");
            bRet = false;
            break;
        }

        //获得上一级目录
        QFileInfo stMonthTmpFile(stMonthDir.absolutePath() + ".txt");
        QDir stYearDir(stMonthTmpFile.absolutePath());
        stYearDir.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);
        QFileInfoList lstYearFileInfo = stYearDir.entryInfoList();
        logInfo(QString("path (%1), count (%2).").arg(stYearDir.absolutePath()).arg(lstYearFileInfo.count()));
        if(lstYearFileInfo.count() > 0)
        {
            break;
        }
        /*logInfo(QString("path (%1), count (%2).").arg(stYearDir.absolutePath()).arg(stYearDir.count()));
        if(stYearDir.count() > 2)
        {
            break;
        }*/
        if(!(stYearDir.rmdir(stYearDir.absolutePath())))
        {
            logError("remove year directory failed.");
            bRet = false;
            break;
        }

    }while(0);

    return bRet;
}

/**************************************************
 * 功能：删除空的文件夹
 * 输入参数：
 *      qstrDirPath：文件夹路径
 * 返回值：
 *      bool：true -- 删除成功，false -- 删除失败
 * **************************************************/
bool clearEmptyFolder(const QString& qstrDirPath)
{
    bool bRet = true;

    do
    {
        refreshToSystemDisk();

        if(!checkFileOrDirExist(qstrDirPath))
        {
            bRet = true;
            break;
        }

        QDir qdrPath(qstrDirPath);
        qdrPath.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);      //set filter
        QFileInfoList lstFileInfo = qdrPath.entryInfoList();             //get all file info
        foreach(QFileInfo objFileInfo, lstFileInfo)
        {
            if(objFileInfo.isDir())
            {
                QString qstrSubFilePath = objFileInfo.absoluteFilePath();
                clearEmptyFolder(qstrSubFilePath);

                QDir qdrSubPath(qstrSubFilePath);
                qdrSubPath.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);
                QFileInfoList qlstFileInfo =  qdrSubPath.entryInfoList();
                if(qlstFileInfo.count() <= 0)
                {
                    logInfo(QString("remove empty dir: %1.").arg(qstrSubFilePath));
                    qdrSubPath.rmdir(qstrSubFilePath);
                }
            }
        }

        refreshToSystemDisk();

    }while(0);

    return bRet;
}

/**************************************************
 * 功能：获取文件夹路径下第一层的文件目录信息
 * 输入参数：
 *      qstrDirPath：文件夹路径
 * 输出参数：
 *      qstrlstDirs：文件目录信息集合
 * **************************************************/
void getFirstLayerDirsUnderDirPath(const QString &qstrDirPath, QStringList &qstrlstDirs)
{
    do
    {
        refreshToSystemDisk();

        if(!checkFileOrDirExist(qstrDirPath))
        {
            break;
        }

        QDir qdrPath(qstrDirPath);
        qdrPath.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);      //set filter
        QFileInfoList lstFileInfo = qdrPath.entryInfoList();             //get all file info
        foreach(QFileInfo objFileInfo, lstFileInfo)
        {
            if(objFileInfo.isDir())
            {
                QString qstrSubFilePath = objFileInfo.absoluteFilePath();
                qstrlstDirs.append(qstrSubFilePath);
            }
        }

    }while(0);

    return;
}

/**************************************************
 * 功能：获取文件夹路径下指定文件类型的文件个数
 * 输入参数：
 *      qstrDirPath：文件夹路径
 *      qstrlstFileTypes：文件类型集合
 * 返回值：
 *      int：文件个数
 * **************************************************/
int getFileCountUnderDirPath(const QString &qstrDirPath, const QStringList &qstrlstFileTypes)
{
    int iCount = 0;

    do
    {
        refreshToSystemDisk();

        if(!checkFileOrDirExist(qstrDirPath))
        {
            break;
        }

        QDir qdrPath(qstrDirPath);
        qdrPath.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);      //set filter
        QFileInfoList lstFileInfo = qdrPath.entryInfoList(qstrlstFileTypes);

        iCount = lstFileInfo.count();

    }while(0);

    return iCount;
}

/**************************************************
 * 功能：获取文件夹路径下指定文件类型的文件路径集合
 * 输入参数：
 *      qstrDirPath：文件夹路径
 *      qstrlstFileTypes：文件类型集合
 * 返回值：
 *      QStringList：文件路径集合
 * **************************************************/
QStringList getFilePathsUnderDirPath(const QString &qstrDirPath, const QStringList &qstrlstFileTypes)
{
    QStringList qstrlstFilePaths;
    qstrlstFilePaths.clear();

    do
    {
        refreshToSystemDisk();

        if(!checkFileOrDirExist(qstrDirPath))
        {
            break;
        }

        QDir qdrPath(qstrDirPath);
        qdrPath.setFilter(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);      //set filter
        QFileInfoList lstFileInfo;
        lstFileInfo.clear();

        if(qstrlstFileTypes.isEmpty())
        {
            lstFileInfo = qdrPath.entryInfoList();
        }
        else
        {
            lstFileInfo = qdrPath.entryInfoList(qstrlstFileTypes);
        }

        foreach(QFileInfo objFileInfo, lstFileInfo)
        {
            if(objFileInfo.isFile())
            {
                qstrlstFilePaths.append(objFileInfo.filePath());
            }
            else if(objFileInfo.isDir())
            {
                qstrlstFilePaths.append(getFilePathsUnderDirPath(objFileInfo.absoluteFilePath(), qstrlstFileTypes));
            }
        }

    }while(0);

    return qstrlstFilePaths;
}

/**************************************************
 * 功能：清空文件夹路径下所有文件
 * 输入参数：
 *      qstrDirPath：文件夹路径
 * 返回值：
 *      bool：操作结果，true -- 成功，false -- 失败
 * **************************************************/
bool clearFilesUnderDirPath(const QString &qstrDirPath)
{
    bool bRet = true;

    do
    {
        refreshToSystemDisk();
        //QDir qdrPath(qstrDirPath);
        //bRet = qdrPath.removeRecursively();//这个路径也会被删除，qt 5.0 引入
        bRet = deleteDir(qstrDirPath);
        if(bRet)
        {
            bRet = createDirectory(qstrDirPath);
        }

    }while(0);

    return bRet;
}


}
