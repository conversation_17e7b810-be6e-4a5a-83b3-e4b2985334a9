/*
* Copyright (c) 2017.12，南京华乘电气科技有限公司
* All rights reserved.
*
* infrareddatamanage.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年12月11日
* 摘要：红外数据管理模块

* 当前版本：1.0
*/

#ifndef INFRAREDDATAMANAGE_H
#define INFRAREDDATAMANAGE_H

#include <QPoint>
#include <QPixmap>
#include <QObject>
#include "datadefine.h"
#include "infrareddatadefine.h"
#include "shapeitem.h"
#include "gigecamera.h"
#include "iniconfig/iniconfig.h"

class InfraredDataManage : public QObject
{
    Q_OBJECT
public:
    /*************************************************
    函数名： InfraredDataManage(ColorType type,
                               bool      bIsAutoCalcu = true,
                               float     minTemper = 0.0,
                               float     maxTemper = 100.0,
                               QObject   *parent = 0)
    输入参数： bIsAutoCalcu：是否自动计算
              minTemper：温度范围的最小值
              maxTemper：温度范围的最大值
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    InfraredDataManage(ColorType type,
                       bool      bIsAutoCalcu = true,
                       float     minTemper = 0.0,
                       float     maxTemper = 100.0,
                       QObject   *parent = 0);

    /*************************************************
    函数名： ~InfraredDataManage()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~InfraredDataManage();

    /*************************************************
    函数名： setData(const unsigned short *rawDataBuf, const FrameInfo &frameInfo, const Params &stParams)
    输入参数： rawDataBuf：原始数据缓存
              frameInfo：帧数据信息
              stParams：红外参数信息
    输出参数： NULL
    返回值： NULL
    功能： 设置红外数据
    *************************************************************/
    void setData(const unsigned short *rawDataBuf, const FrameInfo &frameInfo, const Params &stParams);

    /*************************************************
    功能： 设置红外温度数据
    输入参数： qbaTemperatureData：温度数据缓存
              stFrameInfo：帧数据信息
              stParams：红外参数信息
    *************************************************************/
    void setTemperatureData(QByteArray& qbaTemperatureData, const FrameInfo& stFrameInfo, const Params& stParams);

    /*************************************************
    函数名： setRawData(UINT8 *pucaRawData)
    输入参数： pucaRawData：原始数据缓存
    输出参数： NULL
    返回值： NULL
    功能： 设置红外原始数据
    *************************************************************/
    void setRawData(UINT8 *pucaRawData);

    /*************************************************
    函数名： setFrameInfo(FrameInfo &frameInfo)
    输入参数： frameInfo：帧数据信息
    输出参数： NULL
    返回值： NULL
    功能： 设置红外帧数据信息
    *************************************************************/
    void setFrameInfo(FrameInfo &frameInfo);

    /*************************************************
    函数名： setGigeCameraParams(Params *pstParam)
    输入参数： pstParam：红外参数指针
    输出参数： NULL
    返回值： NULL
    功能： 设置红外参数
    *************************************************************/
    void setGigeCameraParams(Params *pstParam);

    /*************************************************
    函数名： setObjectParam(ObjParamType eType, double dblVal)
    输入参数： eType：红外环境参数类型
              dblVal：红外环境参数值
    输出参数： NULL
    返回值： NULL
    功能： 设置红外环境参数
    *************************************************************/
    void setObjectParam(ObjParamType eType, double dblVal);

    /*************************************************
    函数名： getObjectParam(ObjParam *pObjParam)
    输入参数： NULL
    输出参数： pObjParam：红外环境参数指针
    返回值： NULL
    功能： 获取红外环境参数
    *************************************************************/
    void getObjectParam(ObjParam *pObjParam);

    /*************************************************
    函数名： isEmpty()
    输入参数： NULL
    输出参数： NULL
    返回值： 是否存在红外数据
    功能： 判断当前是否存在红外数据
    *************************************************************/
    bool isEmpty() const;

    /*************************************************
    功能： 清除红外数据
    *************************************************************/
    void clear();

    /*************************************************
    函数名： data(unsigned short *rawDataBuf, quint32 len, FrameInfo &frameInfo, Params &stParams)
    输入参数： len：数据长度
    输出参数： rawDataBuf：原始数据缓存
              frameInfo：帧数据信息
              stParams：红外参数信息
    返回值： NULL
    功能： 获取当前红外数据
    *************************************************************/
    void data(unsigned short *rawDataBuf, quint32 len, FrameInfo &frameInfo, Params &stParams);

    /*************************************************
    功能： 转换当前红外数据
    输入参数： qui32DataSize：数据点的个数
    输出参数： rawDataBuf：原始数据缓存
    功能： 转换当前红外数据
    *************************************************************/
    void convertData(unsigned short *rawDataBuf, quint32 qui32DataSize);

    /*************************************************
    输入参数： type：调色板类型
    输出参数： NULL
    返回值： NULL
    功能： 改变调色板类型
    *************************************************************/
    void setColorType(ColorType type);

    /*************************************************
    输入参数： NULL
    输出参数： NULL
    返回值： 调色板类型
    功能： 获取调色板类型
    *************************************************************/
    ColorType getColorType();

    /*************************************************
    函数名： setTemperatureScale(float min, float max)
    输入参数： min：最小值
              max：最大值
    输出参数： NULL
    返回值： NULL
    功能： 设置手动模式下的温度最小值/最大值
    *************************************************************/
    void setTemperatureScale(float min, float max);

    /*************************************************
    功能： 设置当前温度信息
    *************************************************************/
    void setTemperatureInfo(const TemperatureInfo &stTmpInfo);

    /*************************************************
    函数名： temperatureInfo()
    输入参数： NULL
    输出参数： NULL
    返回值： 当前温度信息
    功能： 获取当前温度信息
    *************************************************************/
    TemperatureInfo temperatureInfo();

    /*************************************************
    函数名： temperatureFromPos(const QPoint &point)
    输入参数： point：对应点
    输出参数： NULL
    返回值： NULL
    功能： 获取对应点的温度值
    *************************************************************/
    float temperatureFromPos(const QPoint &point);

    /*************************************************
    函数名： setAutoCalculate(bool bIsAutoCalcu)
    输入参数： bIsAutoCalcu：是否自动计算
    输出参数： NULL
    返回值： NULL
    功能： 设置是否自动计算温度范围
    *************************************************************/
    void setAutoCalculate(bool bIsAutoCalcu);

    /*************************************************
    函数名： generateBmpFrame()
    输入参数： NULL
    输出参数： NULL
    返回值： 生成结果
    功能： 生成BMP帧数据
    *************************************************************/
    bool generateBmpFrame();

    /*************************************************
    功能： 回放BMP帧数据
    *************************************************************/
    bool playbackBmpFrame();

    /*************************************************
    函数名： saveAsJpeg
    输入参数： 图片文件全路径
    输出参数： NULL
    返回值： 生成结果
    功能： 生成带信息的jpeg
    *************************************************************/
    bool saveAsJpeg( const QString &strJpegPath );

    /*************************************************
    函数名： rawDataToTemperature(unsigned short rawData)
    输入参数： rawData：原始数据
    输出参数： NULL
    返回值： 温度值
    功能： 将原始数据转换为温度值
    *************************************************************/
    float rawDataToTemperature(unsigned short rawData);

    /*************************************************
    函数名： temperatureToRawData(float temperature)
    输入参数： temperature：温度值
    输出参数： NULL
    返回值： 原始数据
    功能： 将温度值转换为原始数据
    *************************************************************/
    unsigned short temperatureToRawData(float temperature);

    /*************************************************
    函数名： pixmap(QPixmap &pixmap)
    输入参数： NULL
    输出参数： pixmap：红外图片
    返回值： NULL
    功能： 获取红外图片
    *************************************************************/
    void pixmap(QPixmap &pixmap);

    /*************************************************
    函数名： dataFromPos(const QPoint &pos)
    输入参数： pos：对应点
    输出参数： NULL
    返回值： 原始数据
    功能： 获取对应点的原始数据
    *************************************************************/
    unsigned short dataFromPos(const QPoint &pos);

    /*************************************************
    输入参数： NULL
    输出参数： baBmp -- 位图数据流
    返回值： NULL
    功能： 获取位图数据流
    *************************************************************/
    void getBmpBuffer( QByteArray &baBmp );

    /*************************************************
    输入参数： strJPG --
    输出参数： baBmp -- 位图数据流
    返回值： NULL
    功能： 获取位图数据流
    *************************************************************/
    bool writeExifInfo( const QString &strJPG );

    /******************************************************
     * 功能：根据校准系数修正未校准的温度值
     * 输入参数：
     *      fTempVal：未校准的温度值
     * 返回值：
     *      float：修正后的温度值
     * ****************************************************/
    float calibrateTempInfo(float fTempVal);

    /******************************************************
     * 功能：根据校准系数将修正后的温度返回成位校准的温度
     * 输入参数：
     *      fTempVal：修正后的温度值
     * 返回值：
     *      float：未校准的温度值
     * ****************************************************/
    float deCalibrateTempInfo(float fTempVal);

signals:
    /*************************************************
    传递参数： fMin：最小值
              fMax：最大值
    说明： 红外温度最大值&最小值信号
    *************************************************************/
    void sigMaxAndMinTemperature(float fMin, float fMax);

private:
    /*************************************************
    函数名： calcTemperatureInfo(const quint16 *pRawData, quint16 &max, quint16 &min)
    输入参数： pRawData：原始数据
    输出参数： max：温度最大值
              min：温度最小值
    返回值： NULL
    功能： 根据原始数据计算温度最大值&最小值
    *************************************************************/
    void calcTemperatureInfo(const quint16 *pRawData, quint16 &max, quint16 &min);

    /*************************************************
    输入参数： pRawData：原始数据
    输出参数： max：温度最大值
              min：温度最小值
    返回值： NULL
    功能： 根据原始数据计算温度最大值&最小值
    *************************************************************/
    void playbackCalcTemperatureInfo(const quint16 *pRawData, quint16 &max, quint16 &min);

    /*************************************************
    函数名： makeBmpHeader()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 生成BMP头
    *************************************************************/
    void makeBmpHeader();

    /*************************************************
    函数名： makeRGBMatrix(quint16 *pRawData)
    输入参数： pRawData：原始数据
    输出参数： NULL
    返回值： NULL
    功能： 生成RGB点阵
    *************************************************************/
    void makeRGBMatrix(quint16 *pRawData);

    /*************************************************
    输入参数： pRawData：原始数据
    输出参数： NULL
    返回值： NULL
    功能： 回放RGB点阵
    *************************************************************/
    void playbackRGBMatrix(quint16 *pRawData);

    bool write_jpeg_file( const char* filepath, const uchar *buffer, int width, int height, int quality = 90 );

    bool write_exif_info(const char* filepath , int width, int height, float dist);

private:
    unsigned short *m_pRawData;
    FrameInfo m_stFrameInfo;
    Params m_stParams;

    BITMAPFILEHEADER m_stBmpFileHead;
    BITMAPINFOHEADER m_stBmpInfoHead;
    QByteArray m_oBmpBuffer;    // 存储bmp数据流

    TemperatureInfo m_stTemperatureInfo;
    float m_fMinTemperature;
    float m_fMaxTemperature;

    ColorType m_eColorType;
    bool m_bIsAutoCalcu;

    //RGB映射表的行指针
    const quint8 (* m_pColorList)[3];
    quint16 m_uiRgbTableRow;

    quint32 m_uiFrameSize;
    InfraredCalibrateInfo m_stCalibInfo;
};

#endif // INFRAREDDATAMANAGE_H
