#ifndef LISTWIDGET_H
#define LISTWIDGET_H
#include <QFrame>
#include <QListWidget>
#include <QKeyEvent>
#include "PDAUi/PDAUiBean/pdalistitem.h"

class ListWidget : public QFrame
{
    Q_OBJECT
public:
    explicit ListWidget(QFrame *parent = 0);

    /****************************
    功能： 将包含显示内容的条目增加到listWidget中
    *****************************/
    void addInfoItem(const QString& strText, bool bIsTicked = true, bool bCover = true);

    /****************************
    功能： 将包含显示内容的条目增加到listWidget中
    *****************************/
    void addInfoItem(const QString& strText, int iItemHeight, bool bIsTicked = true, bool bCover = true);

    /****************************
    功能： 将包含显示内容的条目增加到listWidget中
    *****************************/
    void addInfoItem(const QString& strText, const QString& strInnerText, bool bIsTicked = true, bool bCover = true);

    /****************************
    功能： 清空listWidget内容
    *****************************/
    void removeAllItems();

    /****************************
    功能： 返回当前所有勾选条目的内容
    *****************************/
    QStringList getSelectedTexts();

    /****************************
    功能： 返回序号为index的条目内容
    *****************************/
    QString getItemText(quint32 index);

    /****************************
    功能： 返回选中状态序号为index的条目内容
    *****************************/
    QString getSelectedItemText(quint32 index);

    /****************************
    功能： 返回当前所有勾选条目的inner内容
    *****************************/
    QStringList getSelectedInnerTexts();

    /****************************
    功能： 返回选中状态序号为index的条目inner内容
    *****************************/
    QString getSelectedItemInnerText(quint32 index);

    /****************************
    功能： 返回序号为index的条目inner内容
    *****************************/
    QString getItemInnerText(quint32 index);

    /****************************
    功能： 返回当前勾选的条目索引
    *****************************/
    QList<quint8> getSelectedIndexes();

    /****************************
    功能： 返回当前勾选的条目索引
    *****************************/
    quint8 getSelectedIndex();

    /****************************
    功能： 设置当前高亮条目
    *****************************/
    void setCurrentItemSelected( quint32 uiIndex );

    /****************************
    功能： 设置全部选中
    *****************************/
    void setAllTicked(void);

    /*************************************************
       功能： 处理show事件
     *************************************************************/
    void showEvent(QShowEvent *event);

    /*************************************************
       功能： 处理是否响应条目单击响应事件
     *************************************************************/
    void setSingleClickedEnable(bool bEnable = true);

    /*************************************************
       功能： 处理是否响应条目点击响应事件
     *************************************************************/
    void setClickedEnable(bool bEnable = true);

    /*************************************************
       功能： 返回条目个数
     *************************************************************/
    int size() const;

    /****************************
    功能： 设置序号为iIndex的item是否被勾选
    入参： iIndex -- 选中项
          isTicked -- 是否被勾选
    *****************************/
    void setItemTicked(qint32 iIndex, bool isTicked = true);

    /****************************
    功能： 显示滑动条到选中条目的位置
    入参： iIndex -- 选中项
    *****************************/
    void showSelectedItemPos(int iIndex);

    /****************************
    功能： 设置item获得焦点状态
    入参： bFocused -- 焦点状态
    *****************************/
    void setItemFocus(bool bFocused);

    /****************************
    功能： 选中上一项
    *****************************/
    void selectPreviousItem( void );

    /****************************
    功能： 选中下一项
    *****************************/
    void selectNextItem( void );

    /****************************
    功能： 切换当前选中或不选中的样式
    *****************************/
    void switchTickState( void );

signals:
    /*************************************************
    功能： 信号，被点击时发出
    *************************************************************/
    void sigItemPressed( void );

    /*************************************************
    功能： 信号，单个条目被点击时发出
    *************************************************************/
    void sigSingleItemPressed(int iIndex);

    /*************************************************
    功能： 信号，单个条目被标识选中时发出
    *************************************************************/
    void sigSingleItemTicked(int iIndex);

protected:    /************************************************
     * 函数名   : focusInEvent
     * 输入参数 : pFocusEvent: 焦点事件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 获取焦点事件
     ************************************************/
    void focusInEvent(QFocusEvent *pFocusEvent);

    /************************************************
     * 函数名   : focusOutEvent     * 输入参数 : pFocusEvent: 焦点事件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 焦点消失事件
     ************************************************/
    void focusOutEvent(QFocusEvent *pFocusEvent);

    /************************************************
     * 函数名   : 键盘事件
     * 输入参数 :
     *      event -- 事件
     ************************************************/
    void keyPressEvent( QKeyEvent* event );

    /*************************************************
      函数名： eventFilter
      功能： 事件过滤器，获取当前点击的子控件索引，方便View设置选中的stylesheet
      *************************************************************/
    bool eventFilter(QObject *pObj, QEvent *pEvent);

private slots:
    /****************************
    功能： 槽，item被点击时触发的信号
    *****************************/
    void OnListItemChanged();

private:
    /****************************
    功能： 设置序号为iIndex的item是否获得焦点
    入参： iIndex -- 选中项
          isSelected -- 是否选中
    *****************************/
    void setItemSelected(qint32 iIndex , bool isSelected = true);

    /****************************
    功能： 参数初始化
    *****************************/
    void init( void );

    /****************************
    功能： 判断下标是否有效
    返回值：true -- 有效
           false -- 无效
    *****************************/
    bool isIndexValid( qint32 iIndex );

    /****************************
    功能： 判断enter和ok键按下
    *****************************/
    void onEnterPressed();

private:
    QStringList mlTextList ;        //存放item内容信息
    bool mbListWidgetIsFocus;      //listWidget是否得到焦点
    bool mbItemIsFocus;             //listWidget中的条目是否得到焦点
    QList<PDAListItem*> m_lItems;    // item的集合
    QListWidget *m_pListWidget;     // 提供可供滚动的框架窗体
    qint32 m_iIndexSelected;        // 选中的条目序号
    QList<QListWidgetItem*> m_lItemDelegate; // item委托的对象
    bool m_bSingleClickedEnable;//单选模式
    bool m_bClickedEnable;
};

#endif // LISTWIDGET_H
