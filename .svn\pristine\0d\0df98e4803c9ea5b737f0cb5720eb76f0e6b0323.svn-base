﻿/*
* Copyright (c) 2019.4，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：TEVdiagnosis.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2019年4月2日
* 摘要：TEV局放单条数据诊断算法
*/

#ifndef TEVDIAGNOSIS_H
#define TEVDIAGNOSIS_H
#include "PDdiagnosis.h"



//TEV诊断阈值
typedef struct _TEVDiagnosisThreshold
{
    unsigned char ucThresholdMinor;      //一般缺陷阈值
    unsigned char ucThresholdSerious;    //严重缺陷阈值
    unsigned char ucThresholdEmergency;  //危急缺陷阈值
}TEVDiagnosisThreshold;

//TEV诊断结论-马来西亚定制版本
typedef enum _TEVPulseDiagResultForMalay
{
    MALAY_INVALID_DATA = 0,
    MALAY_NORMAL = 1,
    MALAY_INTERFERENCE = 2,
    MALAY_UNKNOWN = 3,
    MALAY_INTERNAL = 4,
    MALAY_SURFACE = 5
}TEVPulseDiagResultForMalay;

/*************************************************
 * 函数功能： TEV诊断-阈值法
 * 输入参数：
 *      ucTEV -- TEV幅值
 *      sThreshold -- 诊断阈值
 * 输出参数：
 *      diagResult -- 诊断结果
 * 返回参数：
 *      void
*************************************************************/
DIAGNOSISSHARED_EXPORT void diagnosisTEVByAmplitude( unsigned char ucTEV,
                                                     TEVDiagnosisThreshold sThreshold,
                                                     DiagResult* diagResult );

/*************************************************
 * 函数功能： TEV诊断-背景差值法
 * 输入参数：
 *      ucTEV -- TEV幅值
 *      ucTEVBackground -- TEV背景值
 *      sThreshold -- 诊断阈值
 * 返回参数：
 *      诊断结果
*************************************************************/
DIAGNOSISSHARED_EXPORT void diagnosisTEVByBackgroudDiffer( unsigned char ucTEV,
                                                           unsigned char ucTEVBackground,
                                                           TEVDiagnosisThreshold sThreshold,
                                                           DiagResult* diagResult );

/*************************************************
 * 函数功能： TEV诊断-历史差值法
 * 输入参数：
 *      ucTEV -- TEV幅值
 *      ucTEVHistory -- TEV历史值
 *      sThreshold -- 诊断阈值
 * 返回参数：
 *      诊断结果
*************************************************************/
DIAGNOSISSHARED_EXPORT void diagnosisTEVByHistoryDiffer( unsigned char ucTEV,
                                                         unsigned char ucTEVHistory,
                                                         TEVDiagnosisThreshold sThreshold,
                                                         DiagResult* diagResult );

/*************************************************
 * 函数功能： TEV诊断-横向差值法
 * 输入参数：
 *      ucTEV -- TEV幅值
 *      ucTEVTransverseMin -- 其它测点TEV值最小值
 *      ucTEVTransverseMax -- 其它测点TEV值最大值
 *      sThreshold -- 诊断阈值
 * 返回参数：
 *      诊断结果
*************************************************************/
DIAGNOSISSHARED_EXPORT void diagnosisTEVByTransverseDiffer( unsigned char ucTEV,
                                                            unsigned char ucTEVTransverseMin,
                                                            unsigned char ucTEVTransverseMax,
                                                            TEVDiagnosisThreshold sThreshold,
                                                            DiagResult* diagResult );


/************************************************************
 * 函数功能： TEV脉冲诊断-马来西亚定制版本
 * 输入参数：
 *      ucTEVAmp -- TEV幅值
 *      uiPulseCnt -- TEV脉冲个数，按照2s内的脉冲计数计算
 *      sThreshold -- TEV诊断阈值
 * 输出参数：
 *      DiagResult -- 诊断结果
 * 返回值：
 *      void
*************************************************************/
DIAGNOSISSHARED_EXPORT void diagnosisTEVByPulse( unsigned char ucTEVAmp, 
                                                 unsigned int uiPulseCnt,
                                                 TEVDiagnosisThreshold sThreshold,
                                                 DiagResult* diagResult );

#endif // TEVDIAGNOSIS_H

