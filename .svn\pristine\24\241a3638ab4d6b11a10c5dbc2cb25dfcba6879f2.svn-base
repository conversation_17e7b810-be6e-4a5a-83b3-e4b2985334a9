#include <QDebug>
#include <QHBoxLayout>
#include <QScrollBar>
#include <QApplication>         // for test
#include "RotateFileListView.h"

const QString TOOL_BUTTON_STYLE = "border-radius: 2px;";
const qint32 TOOL_BUTTON_WIDTH  = 80;
const qint32 TOOL_BUTTON_HEIGHT = 48;
const QString TOOL_BUTTON_LEFT_ICON = ":/images/left_arrow.png";
const QString TOOL_BUTTON_RIGHT_ICON = ":/images/right_arrow.png";
const quint8 LINE_EDIT_HEIGHT = 60;
const QString SCOROLLBAR_STYLE = "QScrollBar:vertical{width:30px;background:rgba(0,0,0,0%);margin:0px,0px,0px,0px;padding-top:30px;padding-bottom:30px;}"
                                 "QScrollBar::handle:vertical{width:8px;background:rgba(0,0,0,25%);border-radius:4px;min-height:20;}"
                                 "QScrollBar::handle:vertical:hover{width:8px;background:rgba(0,0,0,50%);border-radius:4px;min-height:20;}"
                                 "QScrollBar::add-line:vertical{height:30px;width:30px;subcontrol-position:bottom;image: url(:/images/arrow_down_pda.png);}"
                                 "QScrollBar::sub-line:vertical{height:30px;width:30px;subcontrol-position:top;image: url(:/images/arrow_up_pda.png);}"
                                 "QScrollBar::add-line:vertical:hover{height:30px;width:30px;subcontrol-position:bottom;}"
                                 "QScrollBar::sub-line:vertical:hover{height:30px;width:30px;subcontrol-position:top;}"
                                 "QScrollBar::add-page:vertical,QScrollBar::sub-page:vertical{background:rgba(0,0,0,10%);border-radius:4px;}";
#define FILELIST_WIDTH 480
#define FILELIST_HEIGHT 854

/*************************************************
功能： 构造函数
输入参数:
    path:路径(绝对路径)
    parent:父窗口指针
*************************************************************/
RotateFileListView::RotateFileListView(const QString& path, QWidget *parent)
    : QWidget(parent), m_strListHistoryDir()
{
    resize( FILELIST_WIDTH, LINE_EDIT_HEIGHT );
    setFocusPolicy(Qt::StrongFocus);

    initialize(path);
}

/*************************************************
功能： 设置过滤条件
输入参数:
    nameFilters:文件名过滤条件
*************************************************************/
void RotateFileListView::setNameFilters( const QStringList& nameFilters )
{
    m_strListNameFilter = nameFilters;
    m_pModel->setNameFilterDisables(false);
    m_pModel->setNameFilters(m_strListNameFilter);
}

/*************************************************
功能： 处理键盘事件
输入参数:
        pEvent:键盘事件指针
*************************************************************/
void RotateFileListView::keyPressEvent(QKeyEvent *pEvent)
{
    qDebug() << "----RotateFileListView::keyPressEvent";

    switch(pEvent->key())
    {
        //case Qt::Key_Left:
        case Qt::Key_Up:
        {
            cdUp();
        }
            break;
        case Qt::Key_Escape:
        {
            if( m_pView->rootIndex() == m_pModel->index(m_pModel->rootPath())
                || NULL == m_pView->model() )
            {
                clearFocus();
                pEvent->ignore();
                emit sigEscClicked();
            }
            else
            {
                cdUp();
            }
        }
            break;
        //case Qt::Key_Right:
        case Qt::Key_Down:
        {
            qDebug() << "----Qt::Key_Right";
            cd(m_pView->currentIndex());
        }
            break;
        case Qt::Key_Return:
        case Qt::Key_Enter:
        {
            qDebug() << "-----RotateFileListView::keyPressEvent Key_Enter";
            itemTriggered( m_pView->currentIndex() );
        }
            break;
    case Qt::Key_Right:
    case Qt::Key_Left:
    {
        QApplication::sendEvent(m_pView, pEvent);
    }
        break;
        default:
            break;
    }
}

bool RotateFileListView::eventFilter(QObject *pObject, QEvent *pEvent)
{
    if (pObject == m_pView && QEvent::KeyPress == pEvent->type())
    {
        QKeyEvent* pKeyEvent = dynamic_cast<QKeyEvent*>(pEvent);
        if (NULL != pKeyEvent)
        {
            if(Qt::Key_Left == pKeyEvent->key())
            {
                QModelIndex index = m_pView->currentIndex();
                if(index.isValid())
                {
                    int iRow = index.row();
                    ++iRow;

                    if(iRow >= m_pModel->rowCount(m_pView->rootIndex()))
                    {
                        iRow = 0;
                    }

                    index = m_pModel->index(iRow, 0, m_pView->rootIndex());
                    if(index.isValid())
                    {
                        m_pView->setCurrentIndex(index);
                    }
                }
                else
                {
                    m_pView->setCurrentIndex(m_pModel->index(0, 0, m_pView->rootIndex()));
                }
                return true;
            }
            else if(Qt::Key_Right == pKeyEvent->key())
            {
                QModelIndex index = m_pView->currentIndex();
                if(index.isValid())
                {
                    int iRow = index.row();
                    --iRow;

                    if(iRow < 0)
                    {
                        iRow = m_pModel->rowCount(m_pView->rootIndex()) - 1;
                    }

                    index = m_pModel->index(iRow, 0, m_pView->rootIndex());
                    if(index.isValid())
                    {
                        m_pView->setCurrentIndex(index);
                    }
                }
                else
                {
                    m_pView->setCurrentIndex(m_pModel->index(m_pModel->rowCount(m_pView->rootIndex()) - 1, 0));
                }
                return true;
            }
        }
    }

    return QWidget::eventFilter(pObject, pEvent);
}

/*************************************************
功能： 用于在目录发生切换时设置默认的item
输入参数:
        path:目录路径
*************************************************************/
void RotateFileListView::setDefaultItem(const QString& path)
{
    updateLineEdit(path);

    QDir dir(path);
    QDir::SortFlags eSortFlags = QDir::DirsFirst | QDir::Name;
    if (m_eSortOrder == Qt::DescendingOrder)
    {
        eSortFlags |= QDir::Reversed;
    }
    QFileInfoList infoList = dir.entryInfoList( m_strListNameFilter,
                                                QDir::NoDotAndDotDot | QDir::AllEntries | QDir::AllDirs,
                                                eSortFlags);

    for(int i = 0; i < infoList.size(); ++i)
    {
        if( !infoList.at(i).isHidden() )
        {
            QModelIndex index = m_pModel->index(infoList.at(i).absoluteFilePath());
            m_pView->setCurrentIndex(index);
            if( !m_pModel->isDir(index))
            {
                QFileInfoList infoList = m_pModel->fileInfo(index).absoluteDir().entryInfoList( m_strListNameFilter, QDir::NoDotAndDotDot | QDir::AllEntries);
                QStringList absoluteFilePaths;

                foreach(QFileInfo info, infoList)
                {
                    absoluteFilePaths << info.absoluteFilePath();
                }

                int iSelectedFileIndex = absoluteFilePaths.indexOf(m_pModel->filePath(index));
                emit sigDefaultRecordPlayFile(iSelectedFileIndex, absoluteFilePaths);
            }
            break;
        }
    }
}

/*************************************************
功能： 设置默认的item
*************************************************************/
void RotateFileListView::setDefaultItem()
{
    if(!m_strListHistoryDir.isEmpty())
    {
        QString last = m_strListHistoryDir.takeLast();
        m_pView->setCurrentIndex(m_pModel->index(last));
        updateLineEdit(m_pModel->filePath(m_pView->rootIndex()));
    }
}

/*************************************************
功能： 刷新显示的路径
*************************************************************/
void RotateFileListView::updateLineEdit(const QString& path)
{
    QDir rootPath(m_pModel->rootPath());
    QString rootDir = rootPath.dirName();

    int index = path.indexOf(rootDir);
    if(-1 != index)
    {
        qint32 iStripIndex = index + rootDir.length();
        if(iStripIndex < path.length() - 1)
        {
            m_pPahtLineEdit->setText(path.mid(iStripIndex));
        }
    }
}

/*************************************************
功能： Item被点击或按下enter键时的响应
输入参数:
        index: 索引
*************************************************************/
void RotateFileListView::itemTriggered(const QModelIndex& index)
{
    if(!index.isValid())
    {
        return;
    }

    if( m_pModel->isDir(index) )
    {
        cd(index);
    }
    else
    {
        QFileInfoList infoList = m_pModel->fileInfo(index).absoluteDir().entryInfoList( m_strListNameFilter, QDir::NoDotAndDotDot | QDir::AllEntries);
        QStringList absoluteFilePaths;

        foreach(QFileInfo info, infoList)
        {
            absoluteFilePaths << info.absoluteFilePath();
        }

        int iSelectedFileIndex = absoluteFilePaths.indexOf(m_pModel->filePath(index));
        emit sigFileSelected(iSelectedFileIndex, absoluteFilePaths);
        emit sigFileSelected( m_pModel->filePath(index) );
    }
}

/*************************************************
功能： 返回上级目录
*************************************************************/
void RotateFileListView::cdUp()
{
    if(m_pView->rootIndex().isValid())
    {
        QFileInfo info(m_pModel->filePath(m_pView->rootIndex()));
        QDir dir(info.absoluteFilePath());

        if(dir != m_pModel->rootPath())
        {
            dir.cdUp();
            m_pView->setRootIndex(m_pModel->index(dir.absolutePath()));
            setDefaultItem();
        }

        return;
    }
}

/*************************************************
功能： 切换目录
输入参数:
        index：要进入的目录的item索引
*************************************************************/
void RotateFileListView::cd(const QModelIndex& index)
{
    if(index.isValid() && m_pModel->isDir(index))
    {
        m_pView->setRootIndex(index);
        m_strListHistoryDir.append(m_pModel->filePath(index));
        setDefaultItem(m_pModel->filePath(m_pView->rootIndex()));
    }
}

/*************************************************
功能： 处理返回上级目录按钮被点击的事件
*************************************************************/
void RotateFileListView::onLastBtnClicked()
{
    cdUp();
}

/*************************************************
功能： 处理进入下级目录按钮被点击的事件
*************************************************************/
void RotateFileListView::onNextBtnClicked()
{
    cd(m_pView->currentIndex());
}

/*************************************************
功能： 处理进入排序按钮被点击的事件
*************************************************************/
void RotateFileListView::onSortBtnClicked()
{
    if (m_eSortOrder == Qt::AscendingOrder)
    {
        m_eSortOrder = Qt::DescendingOrder;
        m_pSortBtn->setIcon(QIcon("://images/time_descending.png"));
    }
    else
    {
        m_eSortOrder = Qt::AscendingOrder;
        m_pSortBtn->setIcon(QIcon("://images/time_ascending.png"));
    }

    m_pModel->sort(0, m_eSortOrder);


    // 选中第一个
    QModelIndex index = m_pModel->index(0, 0, m_pView->rootIndex());
    if(index.isValid())
    {
        m_pView->setCurrentIndex(index);
    }
}

/*************************************************
功能： 响应鼠标点击事件，进入下级目录
输入参数:
       index: 被点击的Item索引
*************************************************************/
void RotateFileListView::onListViewClicked(const QModelIndex& index)
{
    if(!index.isValid())
    {
        return;
    }
    itemTriggered(index);
}

/*************************************************
功能： 初始化
*************************************************************/
void RotateFileListView::initialize(const QString &path)
{
    m_pModel = new QFileSystemModel(this);
    m_pModel->setRootPath(path);
    m_pModel->setNameFilterDisables(false);
    m_pModel->setNameFilters(m_strListNameFilter);
    m_eSortOrder = Qt::DescendingOrder;
    m_pModel->sort(0, m_eSortOrder);           //按时间降序排序

    QDir dir(path);

    m_pView = new HorListView(this);
    m_pView->setStyleSheet("font-size: 30px");
    m_pView->verticalScrollBar()->setStyleSheet( SCOROLLBAR_STYLE );
    dir = QDir(path);
    if(dir.exists())
    {
        m_pView->setModel(m_pModel);
    }
    m_pView->setRootIndex(m_pModel->index(path));
    m_pView->setSpacing(10);
    m_pView->setContentsMargins(5,0, 5, 30);
    setFocusProxy(m_pView);

//    m_pView->setFocus();
//    m_pView->setFocusPolicy(Qt::StrongFocus);
//    qDebug() << "----focus: " << QApplication::focusWidget() << this->focusWidget();

    m_pPahtLineEdit = new QLineEdit(this);
    m_pPahtLineEdit->setReadOnly(true);
    m_pPahtLineEdit->setFixedHeight(LINE_EDIT_HEIGHT);
    QFont font = m_pPahtLineEdit->font();
    font.setPointSize(32);
    m_pPahtLineEdit->setFont(font);
    m_pPahtLineEdit->setFocusPolicy(Qt::NoFocus);

    m_pLastBtn = new QToolButton(this);
    m_pNextBtn = new QToolButton(this);

    m_pLastBtn->setFixedWidth(TOOL_BUTTON_WIDTH);
    m_pNextBtn->setFixedWidth(TOOL_BUTTON_WIDTH);

    m_pLastBtn->setFocusPolicy(Qt::NoFocus);
    m_pNextBtn->setFocusPolicy(Qt::NoFocus);

    m_pLastBtn->setToolButtonStyle(Qt::ToolButtonIconOnly);
    m_pNextBtn->setToolButtonStyle(Qt::ToolButtonIconOnly);

    m_pLastBtn->setIcon(QIcon(TOOL_BUTTON_LEFT_ICON));
    m_pNextBtn->setIcon(QIcon(TOOL_BUTTON_RIGHT_ICON));
    m_pLastBtn->setStyleSheet(TOOL_BUTTON_STYLE);
    m_pNextBtn->setStyleSheet(TOOL_BUTTON_STYLE);


    m_pLastBtn->setIconSize(QSize(TOOL_BUTTON_WIDTH, TOOL_BUTTON_HEIGHT));
    m_pNextBtn->setIconSize(QSize(TOOL_BUTTON_WIDTH, TOOL_BUTTON_HEIGHT));

    m_pSortBtn = new QToolButton(this);
    m_pSortBtn->setFixedWidth(TOOL_BUTTON_WIDTH);
    m_pSortBtn->setFocusPolicy(Qt::NoFocus);
    m_pSortBtn->setToolButtonStyle(Qt::ToolButtonIconOnly);
    m_pSortBtn->setIcon(QIcon("://images/time_descending.png"));
    m_pSortBtn->setStyleSheet(TOOL_BUTTON_STYLE);
    m_pSortBtn->setIconSize(QSize(TOOL_BUTTON_WIDTH, TOOL_BUTTON_HEIGHT));

    QHBoxLayout *hLayout = new QHBoxLayout;
    hLayout->setContentsMargins(0, 0, 5, 0);
    hLayout->setSpacing(2);
    hLayout->setAlignment(Qt::AlignLeft | Qt::AlignBottom);
    hLayout->addWidget(m_pLastBtn);
    hLayout->addWidget(m_pNextBtn);
    hLayout->addWidget(m_pPahtLineEdit);
    hLayout->addWidget(m_pSortBtn);

    m_pBoxLayout = new QBoxLayout(QBoxLayout::TopToBottom);
    m_pBoxLayout->setContentsMargins(0, 0, 0, 0);
    m_pBoxLayout->setSpacing(0);
    m_pBoxLayout->addLayout(hLayout);
    m_pBoxLayout->addWidget(m_pView);

    setLayout(m_pBoxLayout);

    setDefaultItem(path);

    connect(m_pLastBtn, SIGNAL(clicked(bool)), this, SLOT(onLastBtnClicked()));
    connect(m_pNextBtn, SIGNAL(clicked(bool)), this, SLOT(onNextBtnClicked()));
    connect(m_pSortBtn, SIGNAL(clicked(bool)), this, SLOT(onSortBtnClicked()));
    connect(m_pView, SIGNAL(clicked(QModelIndex)), this, SLOT(onListViewClicked(QModelIndex)));

    m_pView->installEventFilter(this);
}
