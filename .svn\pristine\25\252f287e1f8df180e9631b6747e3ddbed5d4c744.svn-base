/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* SystemSetView.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年05月09日
* 摘要：完成系统设置界面的基类

* 当前版本：1.0
*/

#ifndef SYSTEMSETVIEW_H
#define SYSTEMSETVIEW_H

#include <QFrame>
#include "titlebar/TitleBar.h"
#include "pushButton/PushButton.h"
#include "buttonBar/ButtonBar.h"
class SystemSetView : public ButtonBar
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
        parent -- 父窗体
    *****************************/
    explicit SystemSetView(QWidget *parent = 0);

    /****************************
    功能： 界面重置
    *****************************/
    void reset( void );
protected:
    /****************************
    功能： 添加标题
    *****************************/
    void setTitle( const QString& strTitle );

    /*************************************************
    功能： 创建系统设置菜单栏
    输入参数:
        pchContext -- 国际化用的域
        pInfos -- 按钮配置信息
        iCount -- 按钮个数
    *************************************************************/
    void createButtonBar(  const char* const pchContext, const ButtonInfo::Info* pInfos, int iCount );

    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    virtual void buttonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    virtual void commandButtonPressed( int id );


private:
    /*************************************************
    功能： 添加按钮
    输入参数:
        pButton -- 按钮指针
        id -- 按钮ID（注：如果为-1，则按钮用自身设置的ID）
    *************************************************************/
    virtual void addButton( ControlButton* pButton );

//    void createVersionInfoView();

private slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    void onCommandButtonPressed( int id );
private:
    /*************************************************
    功能： 恢复默认
    *************************************************************/
    void restoreDefault( void );

    /*************************************************
    功能： 将按钮条目的高度修改成自适应高度
    *************************************************************/
    void resizeButtonSize( QVector<ControlButton*> & vButtons );

signals:
    /*************************************************
    功能： 语言发生变化时发出信号
    *************************************************************/
    void sigLanguageChanged(); //自定义的信号
private:
    TitleBar *m_pTitle;           // 标题栏
};

#endif // SYSTEMSETVIEW_H
