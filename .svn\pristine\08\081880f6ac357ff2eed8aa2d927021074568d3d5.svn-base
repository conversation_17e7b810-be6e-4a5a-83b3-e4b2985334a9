﻿/*
* Copyright (c) 2017.10，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：datafile.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年10月24日
* 修改日期：2017年10月27日
* 摘要：数据文件类申明
* 当前版本：1.0
*/

/*接口使用示例：
 1.数据生成保存
    DataFile dataFile;
    dataFile.setStationName( ... );
    ...
    AEPhaseDataMap * pAePhase = new AEPhaseDataMap;
    pAePhase->setDeviceName( ... );
    ....
    AEMapNS::AEPhaseMapInfo mapInfo;
    mapInfo.eAmpUnit = ...;
    ...
    pAePhase->setInfo( &mapInfo );
    //生成data
    double * pAePhaseData = new double[..];
    pAePhaseData[i] = ...;
    pAePhase->setData( pAePhaseData );

    dataFile->addMap( pAePhase );
    dataFile.save( ... );

 2.数据读取
    //提前对工程所使用到的map进行注册，工程中注册一次即可
    DataMapFactory::registerClass<AEPhaseDataMap>("AEPhase");//图谱根节点tag名

    DataFile dataFile;
    dataFile.open( ... );
    QList<DataMap::MapHead> headers = dataFile.mapsHeadList();
    //根据headers的内容确定自己所读取图谱的索引
    DataMap * pMap = dataFile.dataMap( iMapPos );
    AEPhaseDataMap * pAePhase = dynamic_cast<AEPhaseDataMap *>(pMap);

    pAePhase->getDeviceName(...)
    ...
    AEMapNS::AEPhaseMapInfo mapInfo;
    pAePhase->getInfo( &mapInfo );
    double * pAePhaseData = new double[..];
    pAePhase->getData( pAePhaseData );

 3.新增图谱格式的处理
    //包含data相关头文件及mapdatafactory
    //新增图谱继承 DataMap，实现其中的虚函数
    //新增图谱使用前 DataMapFactory::registerClass 注册一次即可
*/
#ifndef DATAFILE_H
#define DATAFILE_H

#include <QObject>
#include <QVector>
#include <QByteArray>
#include <QDateTime>
#include "datafiledefine.h"
#include "datamap.h"
#include "datafile_global.h"
#include "datamapconfig.h"

class XMLDocument;

class DATAFILESHARED_EXPORT DataFile : public QObject
{
public:
    //文件头
    typedef struct _FileHead
    {
        QString strSpecificationVersion;//数据格式规范版本号 版本号有4个部分，形如X.X.X.X
        QString strFileGenerationTime;//生成文件的时间 格式为YYYYMMDDhhmmssfff，例如20100818151010001
        QString strCountry;//国家名
        QString strProvince;//省份名
        QString strCity;//城市名

        QString strStationName;//站点名称
        QString strStationCode;//站点编码
        DataFileNS::Weather eWeather;//天气
        DataFileNS::TemperatureUnit eTemperatureUnit;//环境温度单位
        float fTemperature;//环境温度
        quint8 ucHumidity;//环境湿度,单位 %
        QString strInstrumentManufacturer;//仪器厂家
        QString strInstrumentModel;//仪器型号
        QString strInstrumentSerialNumber;//仪器序列号
        QString strInstrumentVersion;//仪器版本号
        quint8 ucSystemFrequency;//系统频率,单位Hz 默认50
        qint16 sMapCounts;//图谱数量
        QString strNodeName;//节点名称

        _FileHead()
        {
            strSpecificationVersion = "1.1.0.0";
            strFileGenerationTime = "";
            strCountry = "";
            strProvince = "";
            strCity = "";
            strStationName = "";
            strStationCode = "";
            eWeather = DataFileNS::WEATHER_DEFAULT;
            eTemperatureUnit = DataFileNS::TEMPERATURE_UNIT_DEFAULT;
            fTemperature = 0;
            ucHumidity = 0;
            strInstrumentManufacturer = "";
            strInstrumentModel = "";
            strInstrumentSerialNumber = "";
            strInstrumentVersion = "";
            ucSystemFrequency = 50;
            sMapCounts = 0;
            strNodeName = XML_FILE_NODE_NAME_FILE_HEAD;
        }
    }FileHead;

    /*************************************************
    功能： 构造函数
    *************************************************************/
    DataFile();

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~DataFile();

    /*************************************************
    功能： 获取datafile版本号
    *************************************************************/
    void version(QString &strVersion);

/**************文件整体操作*********************/
    /*************************************************
    功能： 保存数据到数据文件
    输入参数：
            strFilePath -- 文件所在的绝对路径 如：/test/2017/11/11
            strFileExtName -- 数据文件的后缀
            isCrypt -- 是否加密
            eFileType -- 数据文件类型
            eEncodeFormat -- 编码格式 NOTE:仅在eFileType为XML格式才生效
    输出参数：
            strDataFile ---数据文件名
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool save(const QString &strFilePath, const QString &strFileExtName, QString &strDataFile, bool bCrypt = true, DataFileNS::FileType eFileType = DataFileNS::FILE_TYPE_XML,
              DataFileNS::EncodeFormat eEncodeFormat = DataFileNS::ENCODE_UTF8);


    /*************************************************
    功能： 保存数据到数据文件
    输入参数：
            strFilePath -- 文件路径
            strFileExtName -- 文件后缀名
            bCrypt -- 是否加密
            eFileType -- 数据文件类型
            eEncodeFormat -- 编码格式
            strDataFile ---数据文件名
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool save(const QString &strFilePath, const QString &strFileExtName, const QString &strDataFile, bool bCrypt = true, DataFileNS::FileType eFileType = DataFileNS::FILE_TYPE_XML,
              DataFileNS::EncodeFormat eEncodeFormat = DataFileNS::ENCODE_UTF8);
    /*************************************************
    功能： 保存数据到数据文件
    输入参数：
            dateTime -- 文件存储的时间
            strFilePath -- 文件所在的绝对路径 如：/test/2017/11/11
            strFileExtName -- 数据文件的后缀
            isCrypt -- 是否加密
            eFileType -- 数据文件类型
            eEncodeFormat -- 编码格式 NOTE:仅在eFileType为XML格式才生效
    输出参数：
            strDataFile ---数据文件名
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    //bool save(const QDateTime &dateTime, const QString &strFilePath, const QString &strFileExtName, QString &strDataFile, bool bCrypt, DataFileNS::FileType eFileType, DataFileNS::EncodeFormat eEncodeFormat);

    bool save( const QDateTime &dateTime, const QString &strFilePath, const QString &strFileExtName, QString &strDataFile, bool bCrypt = true, DataFileNS::FileType eFileType = DataFileNS::FILE_TYPE_XML,
              DataFileNS::EncodeFormat eEncodeFormat = DataFileNS::ENCODE_UTF8);
    /*************************************************
    功能： 保存数据到数据文件
    输入参数：
            strFilePath -- 文件所在的路径 如：/test/2017/11/11
            strFileExtName -- 数据文件的后缀
    输出参数：
            strDataFile ---数据文件全路径
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool saveByBinary(const QString &strFilePath, const QString &strFileExtName, QString &strDataFile);

    /*************************************************
    功能： 解析二进制数据文件
    输入参数：
            strFilePath -- 文件全路径
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool parseBinaryDataFile( const QString &strFilePath );

    /*************************************************
    功能： 打开数据文件
    输入参数：
            strDataFile ---数据文件名
            isDeCrypt -- 是否解密
            eFileType -- 数据文件类型
            eEncodeFormat -- 编码格式 NOTE:仅在eFileType为XML格式才生效
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool open(const QString &strDataFile, bool isDeCrypt = true, DataFileNS::FileType eFileType = DataFileNS::FILE_TYPE_XML,
              DataFileNS::EncodeFormat eEncodeFormat = DataFileNS::ENCODE_UTF8);
    /*************************************************
    功能： 获取文件头
    输入参数：
            stHead ---文件head数据
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool getFileHead(FileHead &stHead);
/**************map图谱相关操作接口*********************/
    /*************************************************
    功能： 添加图谱
    输入参数：
            pMap -- 增加的图谱指针
    返回值：>=0为添加的图谱在图谱列表中的位置 -1为出错
    *************************************************************/
    int addMap( DataMap * pMap );

    /*************************************************
    功能： 获取指定位置图谱的指针
    输入参数：
            uiMapPos -- 图谱在文件中的位置
    返回值：非NULL为图谱的指针 NULL为查找出错
    *************************************************************/
    DataMap* dataMap( quint32 uiMapPos );

    /*************************************************
        功能： 获取指定spectrum code的图谱
        输入参数：
                eCode -- 图谱spectrum code
        返回值：非NULL为图谱的指针 NULL为查找出错
        *************************************************************/
    DataMap *dataMap( DataFileNS::SpectrumCode eCode );

    /*************************************************
    功能： 获取当前文件存在的图谱头部信息列表
    返回值：当前文件存在的图谱头部信息列表
    *************************************************************/
    QList<DataMap::MapHead> mapsHeadList(  );

/**************文件头部设置接口*********************/
    /*************************************************
    功能： 设置版本号
    输入参数：
            strVersion -- 版本号
    *************************************************************/
    void setVersion(const QString &strVersion);

    /*************************************************
    功能： 设置生成文件的时间
    输入参数：
            strTime -- 生成文件的时间
    *************************************************************/
    void setGenerationTime(const QString &strTime);

    /*************************************************
    功能： 设置国家名
    输入参数：
            strCountry -- 国家名
    *************************************************************/
    void setCountry(const QString &strCountry);

    /*************************************************
    功能： 设置省份名
    输入参数：
            strProvince -- 省份名
    *************************************************************/
    void setProvince(const QString &strProvince);

    /*************************************************
    功能： 设置城市名
    输入参数：
            strCity -- 城市名
    *************************************************************/
    void setCity(const QString &strCity);

    /*************************************************
    功能： 设置站点名称
    输入参数：
            strName -- 站点名称
    *************************************************************/
    void setStationName(const QString &strName);

    /*************************************************
    功能： 设置站点编码
    输入参数：
            strCode -- 站点编码
    *************************************************************/
    void setStationCode(const QString &strCode);

    /*************************************************
    功能： 设置天气
    输入参数：
            eWeather -- 天气
    *************************************************************/
    void setWeather(DataFileNS::Weather eWeather);

    /*************************************************
    功能： 设置环境温度
    输入参数：
            fTemperature -- 环境温度 单位：摄氏度 如22.1摄氏度，填写22.1
    *************************************************************/
    void setTemperature(float fTemperature);

    /*************************************************
    功能： 设置环境温度单位
    输入参数：
            eUnit -- 环境温度单位
    *************************************************************/
    void setTemperatureUnit(DataFileNS::TemperatureUnit eUnit);

    /*************************************************
    功能： 设置环境湿度
    输入参数：
            ucHumidity -- 环境湿度,单位 %百分比
    *************************************************************/
    void setHumidity(quint8 ucHumidity);

    /*************************************************
    功能： 设置仪器厂家
    输入参数：
            strManufacturer -- 仪器厂家的名称
    *************************************************************/
    void setInstrumentManufacturer(const QString &strManufacturer);

    /*************************************************
    功能： 设置仪器型号
    输入参数：
            strInstrumentModel -- 仪器型号的名称
    *************************************************************/
    void setInstrumentModel(const QString &strInstrumentModel);

    /*************************************************
    功能： 设置仪器序列号
    输入参数：
            strSerialNumber -- 仪器序列号字符串
    *************************************************************/
    void setSerialNumber(const QString &strSerialNumber);

    /*************************************************
    功能： 设置软件版本号
    输入参数：
            strSWVersion -- 软件版本号字符串
    *************************************************************/
    void setSWVersion(const QString &strSWVersion);

    /*************************************************
    功能： 设置系统频率
    输入参数：
            ucFreq -- 系统频率，单位Hz，如50Hz，填写50
    *************************************************************/
    void setSystemFrequency(quint8 ucFreq);

    //get file head nodes api
    /*************************************************
    功能： 获取版本
    返回： 版本号
    *************************************************************/
    QString version();

    /*************************************************
    功能： 获取生成文件的时间
    返回： 生成文件的时间
    *************************************************************/
    QString generationTime();

    /*************************************************
    功能： 获取站点名称
    返回： 站点名称
    *************************************************************/
    QString stationName();

    /*************************************************
    功能： 获取站点编码
    返回： 站点编码
    *************************************************************/
    QString stationCode();

    /*************************************************
    功能： 获取天气信息
    返回： 天气信息
    *************************************************************/
    quint8 weather();

    /*************************************************
    功能： 获取温度信息
    返回： 温度信息
    *************************************************************/
    float temperature();

    /*************************************************
    功能： 获取湿度信息
    返回： 湿度信息
    *************************************************************/
    quint8 humidity();

    /*************************************************
    功能： 获取仪器厂家
    返回： 仪器厂家
    *************************************************************/
    QString manufacturer();

    /*************************************************
    功能： 获取仪器型号
    返回： 仪器型号
    *************************************************************/
    QString instrumentModel();

    /*************************************************
    功能： 获取仪器序列号
    返回： 仪器序列号
    *************************************************************/
    QString instrumentSN();

    /*************************************************
    功能： 获取仪器版本号
    返回： 仪器版本号
    *************************************************************/
    QString instrumentVersion();

    /*************************************************
    功能： 获取系统频率
    返回： 系统频率 单位HZ
    *************************************************************/
    quint8 sysFreq();

    /*************************************************
    功能： 获取图谱数量
    返回： 文件中包含的图谱数量
    *************************************************************/
    qint16 spectrumCount();

    /*************************************************
    功能： 解析数据头
    输入参数：
            bytes --- 数据流
    返回值：
            成功为true，失败为false
    *************************************************************/
    DataFile::FileHead fileHead();

private:
    /*************************************************
    功能： 解析头部内容
    输入参数：
            doc --- 对应的doc
    输出参数:
            fileHead --- 解析头部的内容
    返回值：
            解析成功为true，失败为false
    *************************************************************/
    bool parseFileHead( XMLDocument * doc, FileHead & fileHead );

    /*************************************************
    功能： 创建文件的xml主体结构
    输入参数：
            strXMLFile --- xml文件的路径，包括文件名
            bCrypt --- 是否加密
            eEncodeFormat --- 编码格式 目前支持utf-8和utf-16
    返回值：
            创建成功为true，失败为false
    *************************************************************/
    bool createXMLFile(const QString &strXMLFile, bool bCrypt, DataFileNS::EncodeFormat eEncodeFormat);

    /*************************************************
    功能： 创建文件的二进制主体结构
    输入参数：
            strBineryFile --- 二进制文件的路径，包括文件名
    返回值：
            创建成功为true，失败为false
    *************************************************************/
    void saveBinaryFileHead(QByteArray &baPackage);

    /*************************************************
    功能： 保存头部内容为xml文本格式
    输入参数：
            bCrypt --- 是否加密
    返回值：
            保存成功为true，失败为false
    *************************************************************/
    bool saveXMLFileHead(bool bCrypt);

    /*************************************************
    功能： 保存crc字段
    输入参数：
            bCrypt --- 是否加密
            iCRC32 --- 计算出的crc值
    返回值：
            保存成功为true，失败为false
    *************************************************************/
    bool saveCRC32Xml(bool bCrypt, quint32 iCRC32);

    void setBinaryFileTail(QByteArray &baPackage);

    void resetFileHeadDataLength(QByteArray &baPackage);

    /*************************************************
    功能： 解析数据流
    输入参数：
            bytes --- 数据流
    返回值：
            成功为true，失败为false
    *************************************************************/
    bool parseBinaryData( const QByteArray &bytes );

    /*************************************************
    功能： 解析数据头
    输入参数：
            bytes --- 数据流
    返回值：
            成功为true，失败为false
    *************************************************************/
    bool parseBinaryHeader( const QByteArray &bytes );

    /*************************************************
    功能： 解析图谱
    输入参数：
            bytes --- 数据流
    返回值：
            成功为true，失败为false
    *************************************************************/
    bool parseBinaryDataMaps( const QByteArray &bytes );

    /*************************************************
    功能： 校验数据
    输入参数：
            bytes --- 数据流
    返回值：
            成功为true，失败为false
    *************************************************************/
    bool checkCRC32( const QByteArray &bytes );

    void clearMaps();

    void setFileGenerationIime(const QString &strTime);

private:

    FileHead m_stFileHead;
    QVector<DataMap *> m_vecMap;
    quint8 m_ucMapIndex;
    QString strRootNodeName;
//    QByteArray m_baFileData;

//    DataFileNS::MapType m_eMapType;

    XMLDocument *m_pDoc;
};

#endif // DATAFILE_H
