<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="pt_PT">
<context>
    <name>AEAbstractChart</name>
    <message>
        <location filename="view/ae/aecommonwidget/AEAbstractChart.cpp" line="14"/>
        <source>Phase[°]</source>
        <translation>Fase [°]</translation>
    </message>
    <message>
        <location filename="view/ae/aecommonwidget/AEAbstractChart.cpp" line="15"/>
        <source>Amp.[mV]</source>
        <translation>Amp.[mV]</translation>
    </message>
    <message>
        <location filename="view/ae/aecommonwidget/AEAbstractChart.cpp" line="16"/>
        <source>Time Interval[ms]</source>
        <translation>Intervalo de tempo [ms]</translation>
    </message>
    <message>
        <location filename="view/ae/aecommonwidget/AEAbstractChart.cpp" line="17"/>
        <source>Time Interval[s]</source>
        <translation>Intervalo de tempo [s]</translation>
    </message>
    <message>
        <location filename="view/ae/aecommonwidget/AEAbstractChart.cpp" line="18"/>
        <source>Time Interval[min]</source>
        <translation>Intervalo de tempo [min]</translation>
    </message>
</context>
<context>
    <name>AEAmpChart</name>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="200"/>
        <source>High Gain</source>
        <translation>Alto ganho</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="206"/>
        <source>Low Gain</source>
        <translation>Baixo ganho</translation>
    </message>
</context>
<context>
    <name>AEAtlasButtonWidget</name>
    <message>
        <location filename="view/ae/aeatlasbuttonwidget.cpp" line="46"/>
        <source>Amplitude</source>
        <translation>Amplitude</translation>
    </message>
    <message>
        <location filename="view/ae/aeatlasbuttonwidget.cpp" line="49"/>
        <source>Phase</source>
        <translation>Fase</translation>
    </message>
    <message>
        <location filename="view/ae/aeatlasbuttonwidget.cpp" line="52"/>
        <source>Fly</source>
        <translation>Fly</translation>
    </message>
    <message>
        <location filename="view/ae/aeatlasbuttonwidget.cpp" line="55"/>
        <source>Waveform</source>
        <translation>Waveform</translation>
    </message>
</context>
<context>
    <name>AEOption</name>
    <message>
        <location filename="view/ae/aecommonwidget/AEOption.cpp" line="15"/>
        <source>Pulse Count: </source>
        <translation>Pulse Count: </translation>
    </message>
    <message>
        <location filename="view/ae/aecommonwidget/AEOption.cpp" line="16"/>
        <source>Channel: </source>
        <translation>Canal: </translation>
    </message>
</context>
<context>
    <name>AEView</name>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="29"/>
        <source>Mode</source>
        <translation>Modo</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="30"/>
        <source>Single Sample</source>
        <translation>Amostra única</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="31"/>
        <source>Noise Test</source>
        <translation>Teste de ruído</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="32"/>
        <source>Record Noise</source>
        <translation>Gravar ruído</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="33"/>
        <source>Clear Noise</source>
        <translation>Limpar Noise</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="34"/>
        <source>Gain</source>
        <translation>Ganho</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="35"/>
        <source>Unit</source>
        <translation>Unidade</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="36"/>
        <source>Save Data</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="37"/>
        <source>Save</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="38"/>
        <source>More...</source>
        <translation>Mais...</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="39"/>
        <source>Trigger Value</source>
        <translation>Valor do gatilho</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="40"/>
        <source>Freq.</source>
        <translation>Freq.</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="41"/>
        <source>Delete Data</source>
        <translation>Excluir dados</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="42"/>
        <source>Channel</source>
        <translation>Canal</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="43"/>
        <source>Default</source>
        <translation>Restaurar padrão</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="44"/>
        <source>Save RFID</source>
        <translation>Salvar RFID</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="45"/>
        <source>Load Data</source>
        <translation>Carregar dados</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="46"/>
        <source>Record</source>
        <translation>Registro</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="47"/>
        <source>Add</source>
        <translation>Adicionar</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="48"/>
        <source>Bandwidth</source>
        <translation>Largura de banda</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="50"/>
        <source>Single</source>
        <translation>Solteiro</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="51"/>
        <source>Continuous</source>
        <translation>Contínuo</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="62"/>
        <source>Internal</source>
        <translation>Internal</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="63"/>
        <source>External</source>
        <translation>External</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="64"/>
        <source>Wireless</source>
        <translation>Sem fio</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="66"/>
        <source>Power Sync</source>
        <translation>Power Sync</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="67"/>
        <source>Light Sync</source>
        <translation>Sincronização de luz</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="68"/>
        <source>Sync Mode</source>
        <translation>Modo de sincronização</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="69"/>
        <source>Internal Sync</source>
        <translation>Internal Sync</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="78"/>
        <source>Sample</source>
        <translation>Amostra</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="79"/>
        <source>Gating Time</source>
        <translation>Gating Time</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="80"/>
        <source>Time Interval</source>
        <translation>Intervalo de tempo</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="81"/>
        <source>Blocking Time</source>
        <translation>Tempo de bloqueio</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="85"/>
        <source>Phase Shift</source>
        <translation>Mudança de fase</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="92"/>
        <source>Sample Time</source>
        <translation>Tempo da amostra</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="93"/>
        <source>Amp. Range</source>
        <translation>Amplitude Range</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="95"/>
        <source>Start</source>
        <translation>Começar</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="96"/>
        <source>Stop</source>
        <translation>Pare</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="97"/>
        <source>Pause</source>
        <translation>Pausar</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="99"/>
        <source>Spectrum Type</source>
        <translation>Tipo de espectro</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="100"/>
        <source>View Sig Amp</source>
        <translation>View Sig Amp</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="101"/>
        <source>View Sig Phase</source>
        <translation>View Sig Phase</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="102"/>
        <source>View Sig Fly</source>
        <translation>View Sig Fly</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="103"/>
        <source>View Sig Wave</source>
        <translation>View Sig Wave</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="104"/>
        <source>Amplitude</source>
        <translation>Amplitude</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="105"/>
        <source>Phase</source>
        <translation>Fase</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="106"/>
        <source>Fly</source>
        <translation>Fly</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="107"/>
        <source>Waveform</source>
        <translation>View Sig Wave</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="109"/>
        <source>Start Recording</source>
        <translation>Comece a gravar</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="110"/>
        <source>Stop Recording</source>
        <translation>Pare de gravar</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="111"/>
        <source>Volume</source>
        <translation>Volume</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="113"/>
        <source>Sync Lost</source>
        <translation>Sync Lost</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="115"/>
        <source>Load Record Data</source>
        <translation>Carregar dados gravados</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="116"/>
        <source>Delete Record Data</source>
        <translation>Excluir dados de gravação</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="117"/>
        <source>Others</source>
        <translation>Outros</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="235"/>
        <source>RMS</source>
        <translation>RMS</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="236"/>
        <source>Peak</source>
        <translation>Máximo</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="237"/>
        <source>Spectrum1</source>
        <translation>Componente de frequência 1</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="238"/>
        <source>Spectrum2</source>
        <translation>Componente de frequência 2</translation>
    </message>
</context>
<context>
    <name>AEWave</name>
    <message>
        <location filename="view/ae/aewaveview/AeWave.h" line="20"/>
        <source>Time</source>
        <translation>Tempo</translation>
    </message>
    <message>
        <location filename="view/ae/aewaveview/AeWave.h" line="21"/>
        <source>Amp.</source>
        <translation>Amp.</translation>
    </message>
</context>
<context>
    <name>BJCurrentDetectionChart</name>
    <message>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="19"/>
        <source>Grounding Current</source>
        <translation type="unfinished">Grounding Current</translation>
    </message>
    <message>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="20"/>
        <source>Load Current</source>
        <translation type="unfinished">Load Current</translation>
    </message>
    <message>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="21"/>
        <source>Load Current: </source>
        <translation type="unfinished">Load Current: </translation>
    </message>
    <message>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="22"/>
        <source>Grounding / Load: </source>
        <translation type="unfinished">Aterramento / Carga: </translation>
    </message>
    <message>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="23"/>
        <source>Max / Min: </source>
        <translation type="unfinished">Max / Min: </translation>
    </message>
</context>
<context>
    <name>BlueToothSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="19"/>
        <source>Bluetooth</source>
        <translation>Bluetooth</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="19"/>
        <source>Asset</source>
        <translation>Equipamento</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="186"/>
        <source>Connect to device: </source>
        <translation>Conectar ao dispositivo: </translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="211"/>
        <source>Bluetooth connect success!</source>
        <translation>Bluetooth sucesso conectar!</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="220"/>
        <source>Bluetooth connect fail!</source>
        <translation>Bluetooth se conectar falhar!</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="258"/>
        <source>Open bluetooth fail!</source>
        <translation>Falha no bluetooth aberto!</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="262"/>
        <source>Close bluetooth fail!</source>
        <translation>Falha no bluetooth próximo!</translation>
    </message>
</context>
<context>
    <name>CAMatchView</name>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="36"/>
        <source>CA Diag. Pairing</source>
        <translation>CA Diag. Emparelhamento</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="289"/>
        <source>Please select CA Diag. processor.</source>
        <translation>Por favor, selecione CA Diag. processador.</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="453"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="704"/>
        <source>Update success, please reboot HAS02.</source>
        <translation>Sucesso na atualização, reinicie o HAS02.</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="718"/>
        <source>Confirm to update HAS02 firmware?</source>
        <translation>Confirme para atualizar o firmware HAS02?</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="732"/>
        <source>No HAS02 connected!</source>
        <translation>Nenhum HAS02 conectado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="754"/>
        <source>CA Diag. not found!</source>
        <translation>CA Diag. não encontrado!</translation>
    </message>
</context>
<context>
    <name>CAView</name>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="27"/>
        <source>Sample</source>
        <translation>Amostra</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="28"/>
        <source>Stop</source>
        <translation>Pare</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="29"/>
        <source>Start Sample</source>
        <translation>Comece a gravar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="30"/>
        <source>Stop Sample</source>
        <translation>Pare de gravar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="31"/>
        <source>Zoom</source>
        <translation>Zoom</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="32"/>
        <source>Phase Shift</source>
        <translation>Mudança de fase</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="33"/>
        <source>Cluster Analyze</source>
        <translation>Cluster Analyze</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="34"/>
        <source>Pulse Review</source>
        <translation>Revisão de Pulso</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="35"/>
        <source>Save</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="36"/>
        <source>Load Data</source>
        <translation>Carregar dados</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="37"/>
        <source>Delete Data</source>
        <translation>Excluir dados</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="38"/>
        <source>More...</source>
        <translation>Mais...</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="39"/>
        <source>Sample Rate</source>
        <translation>Taxa de amostragem</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="40"/>
        <source>Sample Length</source>
        <translation>Comprimento da amostra</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="41"/>
        <source>Trigger BW</source>
        <translation>Gatilho BW</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="42"/>
        <source>Sample Interval</source>
        <translation>Intervalo de amostra</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="43"/>
        <source>Sample Times</source>
        <translation>Tempos da amostra</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="44"/>
        <source>Percentage Before Trigger</source>
        <translation>Percentage Before Trigger</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="45"/>
        <source>Pulse Accumulation Count</source>
        <translation>Contagem de Acumulação de Pulso</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="46"/>
        <source>Trigger AMP</source>
        <translation>Gatilho AMP</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="47"/>
        <source>Trigger Value</source>
        <translation>Valor do gatilho</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="48"/>
        <source>Gain</source>
        <translation>Ganho</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="49"/>
        <source>Input</source>
        <translation>Entrada</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="50"/>
        <source>Input Value</source>
        <translation>Valor de entrada</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="51"/>
        <source>Verify</source>
        <translation>Verificar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="52"/>
        <source>Start</source>
        <translation>Começar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="53"/>
        <source>Zero Drift</source>
        <translation>Zero Deriva</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="54"/>
        <source>RMS</source>
        <translation>RMS</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="55"/>
        <source>Channel</source>
        <translation>Canal</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="56"/>
        <source>Calibrate</source>
        <translation>Calibrar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="57"/>
        <source>Accumulate</source>
        <translation>Acumular</translation>
    </message>
    <message>
        <source>Accumulative Time</source>
        <translation type="obsolete">Duração Acumulativa</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="58"/>
        <source>Frequency Ref.</source>
        <translation>Frequency Ref.</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="59"/>
        <source>Default</source>
        <translation>Restaurar padrão</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="60"/>
        <source>Marker</source>
        <translation>Marcador</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="61"/>
        <source>Jump Max</source>
        <translation>Jump Max</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="62"/>
        <source>Jump Min</source>
        <translation>Jump Min</translation>
    </message>
    <message>
        <source>Noise Reduction</source>
        <translation type="obsolete">Redução de Ruído</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="64"/>
        <source>Mode</source>
        <translation>Modo</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="65"/>
        <source>Drawing</source>
        <translation>Desenhar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="66"/>
        <source>Cancel</source>
        <translation>Cancelar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="67"/>
        <source>Select</source>
        <translation>Selecione</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="68"/>
        <source>Select Color</source>
        <translation>Selecionar cor</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="69"/>
        <source>Diagnostic</source>
        <translation>Diagnóstico</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="70"/>
        <source>Draw</source>
        <translation>Desenhar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="71"/>
        <source>Move</source>
        <translation>Mover</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="72"/>
        <source>Delete</source>
        <translation>Excluir</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="73"/>
        <source>Switch Mode</source>
        <translation>Modo de comutação</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="75"/>
        <source>No data to operate.</source>
        <translation>Não há dados para operar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="76"/>
        <source>Can not be executed.</source>
        <translation>Não pode ser executado</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="78"/>
        <source>Auto</source>
        <translation>Auto</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="79"/>
        <source>Manual</source>
        <translation>Manual</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="81"/>
        <source>All</source>
        <translation>Tudo</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="82"/>
        <source>Yellow</source>
        <translation>Amarelo</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="83"/>
        <source>Light Green</source>
        <translation>Verde claro</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="84"/>
        <source>Red</source>
        <translation>Vermelho</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="85"/>
        <source>Green</source>
        <translation>Verde</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="86"/>
        <source>Blue</source>
        <translation>Azul</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="87"/>
        <source>Dark Green</source>
        <translation>Verde escuro</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="110"/>
        <source>Yes</source>
        <translation>sim</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="111"/>
        <source>No</source>
        <translation>Não</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="112"/>
        <source>On</source>
        <translation>Ligar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="113"/>
        <source>Off</source>
        <translation>Fechar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="127"/>
        <source>Pulse Count</source>
        <translation>Contagem de pulsos</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="128"/>
        <source>Disconnected</source>
        <translation>Desconectado</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="129"/>
        <source>Connected</source>
        <translation>Conectado</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="130"/>
        <source>Disconnect</source>
        <translation>Desconectar</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="131"/>
        <source>Searching CA Diag. processor ...</source>
        <translation>Pesquisando no CA Diag. processador …</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="133"/>
        <source>View Sig Pulse</source>
        <translation>View Sig Pulse</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="134"/>
        <source>View Sig Wave</source>
        <translation>View Sig Wave</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="135"/>
        <source>View PRPD&amp;PRPS</source>
        <translation>View PRPD&amp;PRPS</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="136"/>
        <source>Pulse</source>
        <translation>Pulso</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="137"/>
        <source>Waveform</source>
        <translation>View Sig Wave</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="138"/>
        <source>PRPD&amp;PRPS</source>
        <translation>PRPD&amp;PRPS</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="163"/>
        <source>Record</source>
        <translation>Registro</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="164"/>
        <source>Record Time</source>
        <translation>Tempo Recorde</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="165"/>
        <source>Range</source>
        <translation>Alcance</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="166"/>
        <source>Playback</source>
        <translation>Playback</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="167"/>
        <source>Delete Record</source>
        <translation>Excluir registro</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="174"/>
        <source>Original Ratio</source>
        <translation>Rácio original</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="175"/>
        <source>Select Pulse</source>
        <translation>Selecionar pulso</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="176"/>
        <source>Previous</source>
        <translation>Anterior</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="177"/>
        <source>Next</source>
        <translation>Próximo</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="178"/>
        <source>Continuous Sample</source>
        <translation>Amostra Contínua</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="179"/>
        <source>Single Sample</source>
        <translation>Amostra única</translation>
    </message>
    <message>
        <source>Level 1</source>
        <translation type="obsolete">Nível 1</translation>
    </message>
    <message>
        <source>Level 2</source>
        <translation type="obsolete">Nível 2</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="245"/>
        <source>Red Cluster</source>
        <translation>Cluster vermelho</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="246"/>
        <source>Yellow Cluster</source>
        <translation>Cluster Amarelo</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="247"/>
        <source>Green Cluster</source>
        <translation>Green Cluster</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="248"/>
        <source>Blue Cluster</source>
        <translation>Blue Cluster</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="249"/>
        <source>All Cluster</source>
        <translation>todos Cluster</translation>
    </message>
</context>
<context>
    <name>CalibrateView</name>
    <message>
        <location filename="view/ca/wave/calibrate/calibrateview.cpp" line="212"/>
        <source>Disconnected, exit calibration!</source>
        <translation>Desconectado, saia da calibração!</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="13"/>
        <source>Calibration</source>
        <translation>Calibração</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="14"/>
        <source>Calibration Test</source>
        <translation>Teste de Calibração</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="15"/>
        <source>Gear: </source>
        <translation>Engrenagem: </translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="16"/>
        <source>Coefficient K: </source>
        <translation>Coeficiente K: </translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="17"/>
        <source>Coefficient B: </source>
        <translation>Coeficiente B: </translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="18"/>
        <source>Input: </source>
        <translation>Entrada: </translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="19"/>
        <source>Signal</source>
        <translation>Sinal</translation>
    </message>
    <message>
        <source>Coefficient</source>
        <translation type="obsolete">Coeficiente</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="20"/>
        <source>Reference Array</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="21"/>
        <source>First set of coefficients</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="22"/>
        <source>Second set of coefficients</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="23"/>
        <source>Third set of coefficients</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="24"/>
        <source>Output Value</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="25"/>
        <source>First set of reference arrays</source>
        <oldsource>First set of reference values</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="26"/>
        <source>Second set of reference arrays</source>
        <oldsource>Second set of reference values</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="27"/>
        <source>Third set of reference arrays</source>
        <oldsource>Third set of reference values</oldsource>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="29"/>
        <source>Calibrate</source>
        <translation>Calibrar</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="30"/>
        <source>Write</source>
        <translation>Escrever</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="31"/>
        <source>Restore</source>
        <translation>Restaurar</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="32"/>
        <source>Test</source>
        <translation>Teste</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="34"/>
        <source>Calibrating</source>
        <translation>Calibrating</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="35"/>
        <source>Calibrating Succeeded</source>
        <translation>Calibração bem sucedida</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="36"/>
        <source>Calibrating Failed</source>
        <translation>Falha na calibração</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="37"/>
        <source>Not Calibrated</source>
        <translation>Não calibrado</translation>
    </message>
</context>
<context>
    <name>CalibrateWidget</name>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="13"/>
        <source>Gain</source>
        <translation>Ganho</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="14"/>
        <source>Signal1 (RMS)</source>
        <translation>Sinal1 (RMS)</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="15"/>
        <source>Signal2 (RMS)</source>
        <translation>Sinal2 (RMS)</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="16"/>
        <source>Signal3 (RMS)</source>
        <translation>Sinal3 (RMS)</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="17"/>
        <source>Signal4 (RMS)</source>
        <translation>Sinal4 (RMS)</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="19"/>
        <source>Coefficient</source>
        <translation>Coeficiente</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="20"/>
        <source>Zero Drift</source>
        <translation>Zero Deriva</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="21"/>
        <source>Calibrated Value (VPP)</source>
        <translation>Valor calibrado (VPP)</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="25"/>
        <source>ST</source>
        <translation>ST</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="26"/>
        <source>Calibrating</source>
        <translation>Calibrating</translation>
    </message>
</context>
<context>
    <name>ClockWindow</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/clock/ClockWindow.cpp" line="177"/>
        <source>Cancel</source>
        <translation>Cancelar</translation>
    </message>
</context>
<context>
    <name>CommonView</name>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="22"/>
        <source>Connect App</source>
        <translation>Connect App</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="23"/>
        <source>Discon</source>
        <translation>Desconectar</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="24"/>
        <source>Submit</source>
        <translation>Enviar</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="25"/>
        <source>Delete</source>
        <translation>Excluir</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="26"/>
        <source>Open</source>
        <translation>Abrir</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="27"/>
        <source>Refresh</source>
        <translation>Refrescar</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="28"/>
        <source>Compress</source>
        <translation>Comprimir</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="29"/>
        <source>Download</source>
        <translation>Baixar</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="30"/>
        <source>Upload</source>
        <translation>Fazer upload</translation>
    </message>
</context>
<context>
    <name>ConditionereMatchView</name>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="316"/>
        <source>UHF Pairing</source>
        <translation>Emparelhamento UHF</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="320"/>
        <source>HFCT Pairing</source>
        <translation>Emparelhamento HFCT</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="324"/>
        <source>Sync. Pairing</source>
        <translation>Emparelhamento Sync.</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="328"/>
        <source>AE Pairing</source>
        <translation>Emparelhamento AE</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="332"/>
        <source>TEV Pairing</source>
        <translation>Emparelhamento TEV</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="336"/>
        <source>Current Pairing</source>
        <translation>Emparelhamento Current</translation>
    </message>
</context>
<context>
    <name>ConnectView</name>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="49"/>
        <source>Connect Terminal</source>
        <translation>Terminal de conexão</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="56"/>
        <source>Hotspot Connection</source>
        <translation>Conexão Hotspot</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="60"/>
        <source>Bluetooth Connection</source>
        <translation>Conexão Bluetooth</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="200"/>
        <source>Confirm to connect bluetooth?</source>
        <translation>Confirme para se conectar ao Bluetooth?</translation>
    </message>
</context>
<context>
    <name>ConnectorView</name>
    <message>
        <location filename="view/customaccessUi/taskmodeview/connectorview.cpp" line="312"/>
        <source>Read load current failed!</source>
        <translation>Falha ao obter a corrente de carga!</translation>
    </message>
</context>
<context>
    <name>CurrentDetectionChart</name>
    <message>
        <source>Grounding Current</source>
        <translation type="vanished">Grounding Current</translation>
    </message>
    <message>
        <source>Load Current</source>
        <translation type="vanished">Load Current</translation>
    </message>
    <message>
        <source>Load Current: </source>
        <translation type="vanished">Load Current: </translation>
    </message>
    <message>
        <source>Grounding / Load: </source>
        <translation type="vanished">Aterramento / Carga: </translation>
    </message>
    <message>
        <source>Max / Min: </source>
        <translation type="vanished">Max / Min: </translation>
    </message>
</context>
<context>
    <name>CurrentDetectionView</name>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="24"/>
        <source>Current Detection</source>
        <translation>Detecção Atual</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="25"/>
        <source>Sample</source>
        <translation>Amostra</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="26"/>
        <source>Grounding Alarm</source>
        <translation>Alarme de Solo</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="27"/>
        <source>Phase Selection</source>
        <translation>Seleção de Fase</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="28"/>
        <source>Comprehensive Analysis</source>
        <translation>Análise abrangente</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="29"/>
        <source>Load Alarm</source>
        <translation>Alarme de Carga</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="30"/>
        <source>Delete Data</source>
        <translation>Excluir dados</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="31"/>
        <source>Save</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="32"/>
        <source>More...</source>
        <translation>Mais...</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="33"/>
        <source>Load Data</source>
        <translation>Carregar dados</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="34"/>
        <source>Start</source>
        <translation>Começar</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="35"/>
        <source>Stop</source>
        <translation>Pare</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="36"/>
        <source>Phase A</source>
        <translation>Fase A</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="37"/>
        <source>Phase B</source>
        <translation>Fase B</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="38"/>
        <source>Phase C</source>
        <translation>Fase C</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="39"/>
        <source>Phase N</source>
        <translation>Fase N</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="40"/>
        <source>Mode</source>
        <translation type="unfinished">Modo</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="41"/>
        <source>Single Sample</source>
        <translation type="unfinished">Amostra única</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="42"/>
        <source>Single</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="43"/>
        <source>Continuous</source>
        <translation type="unfinished">Contínuo</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="44"/>
        <source>Save RFID</source>
        <translation type="unfinished">Salvar RFID</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="45"/>
        <source>Range Gear</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Test Type</source>
        <translation type="obsolete">Tipo de Teste</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="46"/>
        <source>Cable Grounding</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="47"/>
        <source>Load Current</source>
        <translation type="unfinished">Load Current</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="48"/>
        <source>Core Grounding</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CurrentPhaseTypeView</name>
    <message>
        <location filename="view/customaccessUi/taskmodeview/currentphasetypeview.cpp" line="441"/>
        <source>Read current failed!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>CustomAccessTaskIO</name>
    <message>
        <location filename="mobileAccess/customaccesstask/taskio.h" line="22"/>
        <source>BG Bay</source>
        <translation>BG Bay</translation>
    </message>
    <message>
        <location filename="mobileAccess/customaccesstask/taskio.h" line="23"/>
        <source>BG Test Point</source>
        <translation>Ponto de Teste BG</translation>
    </message>
</context>
<context>
    <name>DateTimeSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/datetimesettingview.cpp" line="72"/>
        <source>Date &amp; Time</source>
        <translation>Data &amp; Hora</translation>
    </message>
</context>
<context>
    <name>DateView</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/date/DateView.cpp" line="402"/>
        <source>Cancel</source>
        <translation>Cancelar</translation>
    </message>
</context>
<context>
    <name>Desktop</name>
    <message>
        <location filename="view/Desktop.cpp" line="431"/>
        <source>Error, charge the battery and upgrade firmware later.</source>
        <translation>Erro, carregue a bateria e atualize o firmware mais tarde.</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="462"/>
        <source>TEV</source>
        <translation>TEV</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="470"/>
        <source>AE</source>
        <translation>AE</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="478"/>
        <source>UHF</source>
        <translation>UHF</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="486"/>
        <source>HFCT</source>
        <translation>HFCT</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="494"/>
        <source>CA Diag.</source>
        <translation>CA Diag.</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="499"/>
        <source>Infrared</source>
        <translation>Infrared</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="504"/>
        <source>Current</source>
        <translation>Current</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="509"/>
        <source>Patrol</source>
        <translation>Patrol</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="514"/>
        <source>C-APP</source>
        <translation>C-APP</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="523"/>
        <source>RFID</source>
        <translation>RFID</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="528"/>
        <source>Demo Video</source>
        <translation>Vídeo de demonstração</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="534"/>
        <location filename="view/Desktop.cpp" line="605"/>
        <source>Pairing</source>
        <translation>Pairing</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="536"/>
        <source>Audio</source>
        <translation>Áudio</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="537"/>
        <location filename="view/Desktop.cpp" line="606"/>
        <location filename="view/Desktop.cpp" line="1242"/>
        <source>Settings</source>
        <translation>Settings</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="562"/>
        <source>Task Mode</source>
        <translation>Modo de Tarefa</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="600"/>
        <source>Detection Mode</source>
        <translation>Modo de Detecção</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="624"/>
        <source>Self-Check failed, please go to Self-Check page for details.</source>
        <translation>Auto-verificação falhou, por favor, vá para a Auto-Check página para mais detalhes.</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="639"/>
        <source>The calibration date has expired, please apply for calibration service.</source>
        <translation>A data de calibração expirou, solicite o serviço de calibração.</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="960"/>
        <source>QR</source>
        <translation>QR</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="967"/>
        <source>Environment</source>
        <translation>Meio Ambiente</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="974"/>
        <source>GPS</source>
        <translation>GPS</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="1105"/>
        <source>Functional configuration has been modified, confirm to take effect after auto shutdown and restart manually?</source>
        <translation>A configuração funcional foi modificada. Confirme para entrar em vigor após o desligamento automático e reinicie manualmente?</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="1121"/>
        <source>Device is not in the activation date.</source>
        <translation>O dispositivo não está na data de ativação.</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="1208"/>
        <source>The device has been activated successfully.</source>
        <translation>O dispositivo foi ativado com sucesso.</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="1227"/>
        <source>Please enter the patrol function to download tasks.</source>
        <translation>Por favor, entre na função de patrulha para baixar tarefas.</translation>
    </message>
</context>
<context>
    <name>DetectionModeView</name>
    <message>
        <location filename="view/Desktop.cpp" line="576"/>
        <source>AE</source>
        <translation>AE</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="581"/>
        <source>UHF</source>
        <translation>UHF</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="586"/>
        <source>HFCT</source>
        <translation>HFCT</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="588"/>
        <source>Infrared</source>
        <translation>Infrared</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="590"/>
        <source>Current Detection</source>
        <translation>Detecção Atual</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="571"/>
        <source>TEV</source>
        <translation type="unfinished">TEV</translation>
    </message>
    <message>
        <source>Audio</source>
        <translation type="vanished">Áudio</translation>
    </message>
</context>
<context>
    <name>DeviceSelfInspectionView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="108"/>
        <source>Stop self-checking ...</source>
        <translation>A interromper o autoteste …</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="251"/>
        <source>Stop Self-Check</source>
        <translation>Stop Self-Check</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="255"/>
        <source>Start Self-Check</source>
        <translation>Start Self-Check</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="268"/>
        <source>Unknown</source>
        <translation>Desconhecido</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="271"/>
        <source>WiFi</source>
        <translation>WiFi</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="274"/>
        <source>Disk</source>
        <translation>Disco</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="277"/>
        <source>RTC</source>
        <translation>RTC</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="280"/>
        <source>RFID</source>
        <translation>RFID</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="283"/>
        <source>AE</source>
        <translation>AE</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="286"/>
        <source>TEV</source>
        <translation>TEV</translation>
    </message>
</context>
<context>
    <name>DiagConfig</name>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="28"/>
        <source>Diagnostic Result: </source>
        <translation>Resultado de Diagnóstico: </translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="29"/>
        <source>Normal</source>
        <translation>Normal</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="30"/>
        <source>Abnormal</source>
        <translation>Anormal</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="31"/>
        <source>Medium</source>
        <translation>Médio</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="32"/>
        <source>High</source>
        <translation>Alto</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="33"/>
        <source>Minor</source>
        <translation>Menor</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="34"/>
        <source>Serious</source>
        <translation>Grave</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="35"/>
        <source>Emergency</source>
        <translation>Emergência</translation>
    </message>
</context>
<context>
    <name>DistributeNetAccessNS</name>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="30"/>
        <source>Cabinet Front</source>
        <translation>Na frente do armário</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="31"/>
        <source>Cabinet Side</source>
        <translation>Lado do armário</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="32"/>
        <source>Cabinet Back</source>
        <translation>Atrás do armário</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="33"/>
        <source>TEV Detect</source>
        <translation>Detecção de TEV</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="34"/>
        <source>AE Detect</source>
        <translation>Detecção de EA</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="35"/>
        <source>UHF Detect</source>
        <translation>Detecção de UHF</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="36"/>
        <source>HFCT Detect</source>
        <translation>Detecção de HFCT</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="37"/>
        <source>BKGD Detect</source>
        <translation>BKGD Detect</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="38"/>
        <source>TEV BKGD</source>
        <translation>TEV BKGD</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="39"/>
        <source>AE BKGD</source>
        <translation>AE BKGD</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="40"/>
        <source>UHF BKGD</source>
        <translation>UHF BKGD</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="41"/>
        <source>HFCT BKGD</source>
        <translation>HFCT BKGD</translation>
    </message>
</context>
<context>
    <name>DistributeNetAddTaskView</name>
    <message>
        <location filename="view/distributenetaccess/distributenetaddtaskview.cpp" line="43"/>
        <source>Asset Information</source>
        <translation>Informações de ativos</translation>
    </message>
</context>
<context>
    <name>DistributeNetDeviceView</name>
    <message>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="219"/>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="372"/>
        <source>Comfirm to detect the %1 ?</source>
        <translation>Comfirm to detect the %1 ?</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="223"/>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="376"/>
        <source>Comfirm to detect the &quot;%1&quot; of %2 ?</source>
        <translation>Comfirm to detect the &quot;%1&quot; of %2 ?</translation>
    </message>
</context>
<context>
    <name>DistributeNetTaskView</name>
    <message>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="207"/>
        <source>No task has been chosen.</source>
        <translation>Nenhuma tarefa foi escolhida.</translation>
    </message>
</context>
<context>
    <name>DistributeNetView</name>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="13"/>
        <source>Open</source>
        <translation>Abrir</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="14"/>
        <source>New</source>
        <translation>Novo</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="15"/>
        <source>Refresh</source>
        <translation>Refrescar</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="16"/>
        <source>Delete</source>
        <translation>Excluir</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="17"/>
        <source>Sift</source>
        <translation>Peneirar</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="18"/>
        <source>Remind</source>
        <translation>Lembrar</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="19"/>
        <source>On</source>
        <translation>Ligar</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="20"/>
        <source>Off</source>
        <translation>Fechar</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="21"/>
        <source>Cabinet Front</source>
        <translation>Na frente do armário</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="22"/>
        <source>Cabinet Back</source>
        <translation>Atrás do armário</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="23"/>
        <source>Cabinet Top</source>
        <translation>Gabinete Top</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="24"/>
        <source>Cabinet Bottom</source>
        <translation>Cabinet Bottom</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="25"/>
        <source>Cabinet Left</source>
        <translation>Gabinete Esquerdo</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="26"/>
        <source>Cabinet Right</source>
        <translation>Gabinete Direito</translation>
    </message>
</context>
<context>
    <name>EnvironmentInfo</name>
    <message>
        <location filename="view/environment/environmentinfo.cpp" line="8"/>
        <source>Temperature: </source>
        <translation>Temperatura: </translation>
    </message>
    <message>
        <location filename="view/environment/environmentinfo.cpp" line="9"/>
        <source>Humidity: </source>
        <translation>Umidade: </translation>
    </message>
    <message>
        <location filename="view/environment/environmentinfo.cpp" line="10"/>
        <source>Pressure: </source>
        <translation>Pressão: </translation>
    </message>
</context>
<context>
    <name>FileCommentBox</name>
    <message>
        <location filename="view/widgets/filecommentbox/filecommentbox.cpp" line="35"/>
        <source>Remark</source>
        <translation>Observações</translation>
    </message>
    <message>
        <location filename="view/widgets/filecommentbox/filecommentbox.cpp" line="222"/>
        <source>Cannot contain the following special symbols:</source>
        <translation>Não pode conter os seguintes símbolos especiais:</translation>
    </message>
    <message>
        <location filename="view/widgets/filecommentbox/filecommentbox.cpp" line="231"/>
        <source>The number of characters exceeds the limit.</source>
        <translation>O número de caracteres excede o limite.</translation>
    </message>
</context>
<context>
    <name>FilterSettingDlg</name>
    <message>
        <location filename="view/distributenetaccess/filtersettingdlg.cpp" line="50"/>
        <source>Keyword</source>
        <translation>Palavra-chave</translation>
    </message>
</context>
<context>
    <name>GPSView</name>
    <message>
        <location filename="view/gps/gpsview.cpp" line="17"/>
        <source>Location: </source>
        <translation>Localização: </translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="18"/>
        <source>Time: </source>
        <translation>Tempo: </translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="19"/>
        <source>Satellite Num: </source>
        <translation>Número de Satélite: </translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="20"/>
        <source>Degree</source>
        <translation>Grau</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="21"/>
        <source>Minute</source>
        <translation>Minuto</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="22"/>
        <source>Hour</source>
        <translation>Hora</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="23"/>
        <source>Second</source>
        <translation>Segundo</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="24"/>
        <source>Msec</source>
        <translation>Milissegundo</translation>
    </message>
</context>
<context>
    <name>HFCTPeriodChart</name>
    <message>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="23"/>
        <source>Upper Limit: </source>
        <translation>Limite superior: </translation>
    </message>
    <message>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="24"/>
        <source>Lower Limit: </source>
        <translation>Limite inferior: </translation>
    </message>
    <message>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="25"/>
        <source>Range: </source>
        <translation>Alcance: </translation>
    </message>
    <message>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="26"/>
        <source>Max: </source>
        <translation>Máximo: </translation>
    </message>
</context>
<context>
    <name>HFCTView</name>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="29"/>
        <source>Mode</source>
        <translation>Modo</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="30"/>
        <source>Start</source>
        <translation>Começar</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="31"/>
        <source>Stop</source>
        <translation>Pare</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="32"/>
        <source>Pause</source>
        <translation>Pausar</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="33"/>
        <source>Single Sample</source>
        <translation>Amostra única</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="34"/>
        <source>Single</source>
        <translation>Solteiro</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="35"/>
        <source>Continuous</source>
        <translation>Contínuo</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="36"/>
        <source>Warning</source>
        <translation>Aviso</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="37"/>
        <source>High Risk</source>
        <translation>Alto risco</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="38"/>
        <source>Gain</source>
        <translation>Ganho</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="39"/>
        <source>Default</source>
        <translation>Restaurar padrão</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="40"/>
        <source>Power</source>
        <translation>Poder</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="41"/>
        <source>Light</source>
        <translation>Luz</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="42"/>
        <source>Sync Mode</source>
        <translation>Modo de sincronização</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="43"/>
        <location filename="view/hfct/HFCTViewConfig.h" line="55"/>
        <source>More...</source>
        <translation>Mais...</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="44"/>
        <source>Save Data</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="45"/>
        <source>Save</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="46"/>
        <source>Save RFID</source>
        <translation>Salvar RFID</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="47"/>
        <source>Delete Data</source>
        <translation>Excluir dados</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="48"/>
        <source>Load Data</source>
        <translation>Carregar dados</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="49"/>
        <source>Phase Shift</source>
        <translation>Mudança de fase</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="50"/>
        <source>On</source>
        <translation>Ligar</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="51"/>
        <location filename="view/hfct/HFCTViewConfig.h" line="81"/>
        <source>Off</source>
        <translation>Fechar</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="52"/>
        <source>Add</source>
        <translation>Adicionar</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="53"/>
        <source>Accumulate</source>
        <translation>Acumular</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="54"/>
        <source>Accumulative Time</source>
        <translation>Duração Acumulativa</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="56"/>
        <source>Threshold</source>
        <translation>Limiar</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="57"/>
        <source>Noise Reduction</source>
        <translation>Redução de Ruído</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="58"/>
        <source>Altas Type</source>
        <translation>Tipo Atlas</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="64"/>
        <source>Record</source>
        <translation>Registro</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="65"/>
        <source>Record Time</source>
        <translation>Tempo Recorde</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="66"/>
        <source>Playback</source>
        <translation>Playback</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="67"/>
        <source>Delete Record</source>
        <translation>Excluir dados de gravação</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="69"/>
        <source>View Sig Amp</source>
        <translation>View Sig Amp</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="70"/>
        <source>View Period</source>
        <translation>View Period</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="71"/>
        <source>View PRPD&amp;PRPS</source>
        <translation>View PRPD&amp;PRPS</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="72"/>
        <source>Amplitude</source>
        <translation>Amplitude</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="73"/>
        <source>Period</source>
        <translation>Period</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="74"/>
        <source>PRPD&amp;PRPS</source>
        <translation>PRPD&amp;PRPS</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="75"/>
        <source>PRPS</source>
        <translation>PRPS</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="76"/>
        <source>PRPD</source>
        <translation>PRPD</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="80"/>
        <source>Auto</source>
        <translation>Auto</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="82"/>
        <source>Level 1</source>
        <translation>Nível 1</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="83"/>
        <source>Level 2</source>
        <translation>Nível 2</translation>
    </message>
</context>
<context>
    <name>Infrared</name>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="29"/>
        <source>Temp. Points Count</source>
        <translation>Contagem de pontos de temperatura</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="30"/>
        <source>Unit(℃)</source>
        <translation>Unidade (℃)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="31"/>
        <source>Freeze</source>
        <translation>Congelar</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="32"/>
        <source>Restore</source>
        <translation>Restaurar</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="33"/>
        <source>Palette</source>
        <translation>Paleta de cores</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="35"/>
        <source>Iron Red</source>
        <translation>Iron Red</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="36"/>
        <source>Rainbow</source>
        <translation>Arco Iris</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="37"/>
        <source>Red Heat</source>
        <translation>Calor vermelho</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="38"/>
        <source>Black Heat</source>
        <translation>Calor Preto</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="39"/>
        <source>White Heat</source>
        <translation>Calor branco</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="40"/>
        <source>Lava</source>
        <translation>Lava</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="41"/>
        <source>Medical</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="42"/>
        <source>Hot Iron</source>
        <translation>Ferro quente</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="43"/>
        <source>Ink Brown</source>
        <translation>Tinta marrom</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="44"/>
        <source>Miao Hong</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="46"/>
        <source>Point</source>
        <translation>Ponto</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="47"/>
        <source>Line</source>
        <translation>Linha</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="48"/>
        <source>Rectangle</source>
        <translation>Retângulo</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="49"/>
        <source>Circle</source>
        <translation>Círculo</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="50"/>
        <source>Analyze</source>
        <translation>Analisar</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="51"/>
        <source>Line Temp.</source>
        <translation>Temperatura da linha</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="52"/>
        <source>Del Graph</source>
        <translation>Excluir gráficos</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="53"/>
        <source>Delete</source>
        <translation>Excluir</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="54"/>
        <source>Delete All</source>
        <translation>Excluir tudo</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="55"/>
        <source>PgUp</source>
        <translation>PgUp</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="56"/>
        <source>PgDn</source>
        <translation>PgDn</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="57"/>
        <source>Setting</source>
        <translation>Configuração</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="58"/>
        <source>More...</source>
        <translation>Mais...</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="59"/>
        <source>Measurement Method</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="61"/>
        <source>Save Data</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="62"/>
        <source>Save</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="63"/>
        <source>Load Data</source>
        <translation>Carregar dados</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="64"/>
        <source>Delete Data</source>
        <translation>Excluir dados</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="66"/>
        <source>Playback</source>
        <translation>Playback</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="68"/>
        <source>IR Image</source>
        <translation>IR Image</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="70"/>
        <source>Stop Play</source>
        <translation>Parar de Jogar</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="71"/>
        <source>Add</source>
        <translation>Adicionar</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="72"/>
        <source>Exit</source>
        <translation>Sair</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="73"/>
        <source>Return</source>
        <translation>Return</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="74"/>
        <source>Auto Focus</source>
        <translation>Foco Automático</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="75"/>
        <source>Close Focus Fine-Tuning</source>
        <translation>Foco próximo ajuste fino</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="76"/>
        <source>Far Focus Fine-Tuning</source>
        <translation>Far Focus Ajuste Fino</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="77"/>
        <source>Electronic Zoom</source>
        <translation>Zoom Eletrônico</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="78"/>
        <source>Laser Control</source>
        <translation>Controle de Laser</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="79"/>
        <source>On</source>
        <translation>Ligar</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="80"/>
        <source>Off</source>
        <translation>Fechar</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="81"/>
        <source>LED Fill Light</source>
        <translation>Luz de preenchimento LED</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="82"/>
        <source>Display Mode</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="83"/>
        <source>Infrared</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="84"/>
        <source>Picture in Picture</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="85"/>
        <source>Digital Camera</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="86"/>
        <source>Auxiliary Lighting</source>
        <translation>Iluminação Auxiliar</translation>
    </message>
</context>
<context>
    <name>InfraredParameterDialog</name>
    <message>
        <location filename="widget/infrared/infraredparameterdialog.cpp" line="199"/>
        <source>Empty value.</source>
        <translation>O valor não pode estar vazio.</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredparameterdialog.cpp" line="209"/>
        <source>Invalid symbol.</source>
        <translation>Símbolo inválido.</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredparameterdialog.cpp" line="220"/>
        <source>The value is out of range</source>
        <translation>O valor está fora da faixa</translation>
    </message>
</context>
<context>
    <name>InfraredWidget</name>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="29"/>
        <source>Emissivity</source>
        <translation>Emissividade</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="30"/>
        <source>Distance(m)</source>
        <translation>Distância (m)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="31"/>
        <source>Air Temp.(℃)</source>
        <translation>Temp. Do ar (℃)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="32"/>
        <source>Reflected Temp.(℃)</source>
        <translation>Temperatura refletida (℃)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="33"/>
        <source>Ext. Optical Temp.(℃)</source>
        <translation>Temperatura óptica externa (℃)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="34"/>
        <source>Ext. Optical Tx Rate</source>
        <translation>Taxa de transmissão óptica externa</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="35"/>
        <source>Relative Humidity(%)</source>
        <translation>Humidade relativa(%)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="36"/>
        <source>Reference Temp.(℃)</source>
        <translation>Temperatura de referência (℃)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="37"/>
        <source>Air Temp.(℉)</source>
        <translation>Temp. Do ar (℉)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="38"/>
        <source>Reflected Temp.(℉)</source>
        <translation>Temperatura refletida (℉)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="39"/>
        <source>Ext. Optical Temp.(℉)</source>
        <translation>Temperatura óptica externa (℉)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="40"/>
        <source>Reference Temp.(℉)</source>
        <translation>Temperatura de referência (℉)</translation>
    </message>
</context>
<context>
    <name>IntervalView</name>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="120"/>
        <source>Auto switch to next undetected point.</source>
        <translation>Mudança automática para o próximo ponto não detectado.</translation>
    </message>
</context>
<context>
    <name>LanguageSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/languagesettingview.cpp" line="78"/>
        <location filename="view/systemsetview/systemsetwidget/languagesettingview.cpp" line="249"/>
        <source>Language</source>
        <translation>Língua</translation>
    </message>
</context>
<context>
    <name>LineTemperatureCurveDialog</name>
    <message>
        <location filename="widget/infrared/linetemperaturecurvedialog.cpp" line="27"/>
        <source>Line Temperature</source>
        <translation>Temperatura da linha</translation>
    </message>
</context>
<context>
    <name>LoginViewBase</name>
    <message>
        <location filename="view/widgets/loginview/loginviewbase.cpp" line="38"/>
        <source>User Name</source>
        <translation>Nome do usuário</translation>
    </message>
    <message>
        <location filename="view/widgets/loginview/loginviewbase.cpp" line="39"/>
        <source>Password</source>
        <translation>Senha</translation>
    </message>
    <message>
        <location filename="view/widgets/loginview/loginviewbase.cpp" line="40"/>
        <source>Login</source>
        <translation>Conecte-se</translation>
    </message>
    <message>
        <location filename="view/widgets/loginview/loginviewbase.cpp" line="41"/>
        <source>Please input user name or password.</source>
        <translation>Por favor, nome de usuário de entrada ou senha.</translation>
    </message>
</context>
<context>
    <name>MainTaskView</name>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="152"/>
        <source>Reading the task list ...</source>
        <translation type="unfinished">Lendo a lista de tarefas ...</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="698"/>
        <source>Uploading the task...</source>
        <translation>Enviando a tarefa ...</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="726"/>
        <source>Downloading the task...</source>
        <translation>Baixando a tarefa ...</translation>
    </message>
</context>
<context>
    <name>MeasureSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/measuresettingview.cpp" line="58"/>
        <source>Measure</source>
        <translation>Medida</translation>
    </message>
</context>
<context>
    <name>NetSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/netsettingview.cpp" line="60"/>
        <source>Network</source>
        <translation>Rede</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/netsettingview.cpp" line="159"/>
        <source>Open 4G failed.</source>
        <translation>Falha ao abrir o 4G.</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/netsettingview.cpp" line="183"/>
        <source>Close 4G failed.</source>
        <translation>Falha ao desativar o 4G.</translation>
    </message>
</context>
<context>
    <name>NetWorkTestDialog</name>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="26"/>
        <source>Dialog</source>
        <translation>Diálogo</translation>
    </message>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="50"/>
        <source>Http Send</source>
        <translation>Envio HTTP</translation>
    </message>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="74"/>
        <source>Clear Log</source>
        <translation>Log clara</translation>
    </message>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="101"/>
        <source>Close</source>
        <translation>Fechar</translation>
    </message>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="126"/>
        <source>Tcp Send</source>
        <translation>Envio TCP</translation>
    </message>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="148"/>
        <source>this is log</source>
        <translation>isso é log</translation>
    </message>
</context>
<context>
    <name>PDACurrentTestDataView</name>
    <message>
        <source>Nonsupport test type.</source>
        <translation type="obsolete">Tipo de teste não suportado.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="516"/>
        <source>Read current failed!</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>PDADeviceDetailView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="252"/>
        <source>Device Info</source>
        <translation>Informação do dispositivo</translation>
    </message>
</context>
<context>
    <name>PDAFileView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="102"/>
        <source>All testing is completed.</source>
        <translation>Todos os testes estão concluídos.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="359"/>
        <source>Not select a device!</source>
        <translation>Nenhum equipamento selecionado!</translation>
    </message>
</context>
<context>
    <name>PDALoginView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdaloginview.cpp" line="39"/>
        <source>User Name</source>
        <translation>Nome do usuário</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdaloginview.cpp" line="40"/>
        <source>Password</source>
        <translation>Senha</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdaloginview.cpp" line="41"/>
        <source>Login</source>
        <translation>Conecte-se</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdaloginview.cpp" line="42"/>
        <source>Please input user name or password.</source>
        <translation>Por favor, nome de usuário de entrada ou senha.</translation>
    </message>
</context>
<context>
    <name>PDAPatrolTypeView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdapatroltypeview.cpp" line="18"/>
        <source>Patrol Type</source>
        <translation>Patrol Type</translation>
    </message>
</context>
<context>
    <name>PDASiftTaskView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="107"/>
        <source>Task Sift</source>
        <translation>Sift tarefa</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="114"/>
        <source>Task Platform: </source>
        <translation>Plataforma de tarefas:</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="127"/>
        <source>Task Status: </source>
        <translation>Status da tarefa:</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="139"/>
        <source>Task File Period: </source>
        <translation>Período do arquivo de tarefas: </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="155"/>
        <source>Task Plan Time Interval: </source>
        <translation>Plano Tarefa Intervalo de tempo: </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="172"/>
        <source>Transformer Substation: </source>
        <translation>Subestação do transformador:</translation>
    </message>
</context>
<context>
    <name>PDATaskView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="214"/>
        <source>No task has been chosen.</source>
        <translation>Nenhuma tarefa foi escolhida.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="548"/>
        <source>Unable to open multiple task files.</source>
        <translation>Não é possível abrir vários arquivos de tarefas.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="746"/>
        <source>Reading the task list ...</source>
        <translation>Lendo a lista de tarefas ...</translation>
    </message>
</context>
<context>
    <name>PDATestDataView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdatestdataview.cpp" line="186"/>
        <source>Nonsupport test type.</source>
        <translation>Tipo de teste não suportado.</translation>
    </message>
</context>
<context>
    <name>PDAView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="26"/>
        <source>Open</source>
        <translation>Abrir</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="27"/>
        <source>Close</source>
        <translation>Fechar</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="28"/>
        <source>Delete</source>
        <translation>Excluir</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="29"/>
        <source>Select All</source>
        <translation>Selecionar tudo</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="30"/>
        <source>Cancel</source>
        <translation>Cancelar</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="31"/>
        <source>Download</source>
        <translation>Baixar</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="32"/>
        <source>Upload</source>
        <translation>Fazer upload</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="33"/>
        <source>Refresh</source>
        <translation>Refrescar</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="34"/>
        <source>Sift</source>
        <translation>Peneirar</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="35"/>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="114"/>
        <source>RFID</source>
        <translation>RFID</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="36"/>
        <source>Details</source>
        <translation>Detalhes</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="37"/>
        <source>Low Pass PRPS</source>
        <translation>PRPS passa-baixo</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="38"/>
        <source>High Pass PRPS</source>
        <translation>High Pass PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="39"/>
        <source>All Pass PRPS</source>
        <translation>All Pass PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="40"/>
        <source>Amp. Detection</source>
        <translation>Detecção Amp</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="41"/>
        <source>Pulse Detection</source>
        <translation>Detecção de pulso</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="42"/>
        <source>Phase Detection</source>
        <translation>Detecção de fase</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="43"/>
        <source>Fly Detection</source>
        <translation>Detecção de Mosca</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="44"/>
        <source>Wave Detection</source>
        <translation>Detecção de Ondas</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="46"/>
        <source>Switchgear</source>
        <translation>Switchgear</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="47"/>
        <source>GIS</source>
        <translation>GIS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="48"/>
        <source>Transformer</source>
        <translation>Transformer</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="49"/>
        <source>Cable</source>
        <translation>Cabo</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="51"/>
        <source>Multi Spectrum</source>
        <translation>Multi Spectrum</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="52"/>
        <source>HF Multi Spectrum</source>
        <translation>HF Multi Spectrum</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="53"/>
        <source>HF PRPD Spectrum</source>
        <translation>HF PRPD Spectrum</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="54"/>
        <source>HF PRPS Spectrum</source>
        <translation>HF PRPS Spectrum</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="55"/>
        <source>PRPD&amp;PRPS</source>
        <translation>PRPD&amp;PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="56"/>
        <source>HF Pulse Wave Spectrum</source>
        <translation>HF Pulse Wave Spectrum</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="57"/>
        <source>UHF Multi Spectrum</source>
        <translation>UHF Multi Spectrum</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="58"/>
        <source>UHF PRPD</source>
        <translation>UHF PRPD</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="59"/>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="76"/>
        <source>UHF PRPS</source>
        <translation>UHF PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="60"/>
        <source>UHF Peak Statistical Chart</source>
        <translation>Gráfico estatístico de pico de UHF</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="61"/>
        <source>AE Multi Spectrum</source>
        <translation>AE Multi Spectrum</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="62"/>
        <source>TEV Multi Spectrum</source>
        <translation>TEV Multi Spectrum</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="63"/>
        <source>TEV PRPS</source>
        <translation>TEV PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="64"/>
        <source>Picture</source>
        <translation>Imagem</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="65"/>
        <source>Audio</source>
        <translation>Áudio</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="66"/>
        <source>Video</source>
        <translation>Vídeo</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="68"/>
        <source>TEV Amp.</source>
        <translation>TEV Amp.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="69"/>
        <source>TEV Pulse</source>
        <translation>TEV Pulse</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="70"/>
        <source>AE Amp.</source>
        <translation>AE Amp.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="71"/>
        <source>AE Wave</source>
        <translation>AE Wave</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="72"/>
        <source>AE Phase</source>
        <translation>AE Phase</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="73"/>
        <source>AE Amp.(Spectrum)</source>
        <translation>AE Amp.(Espectro)</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="74"/>
        <source>AE Fly</source>
        <translation>AE Fly</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="75"/>
        <source>UHF Amp.</source>
        <translation>UHF Amp.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="122"/>
        <source>Load Current</source>
        <translation type="unfinished">Load Current</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="123"/>
        <source>Cable Grounding</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="124"/>
        <source>Core Grounding</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="125"/>
        <source>Leakage Current</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="126"/>
        <source>Clamp Grounding</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="127"/>
        <source>A Phase Grounding</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="128"/>
        <source>B Phase Grounding</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="129"/>
        <source>C Phase Grounding</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="130"/>
        <source>Common Grounding</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="77"/>
        <source>UHF Period</source>
        <translation>UHF Period</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="78"/>
        <source>HFCT Amp.</source>
        <translation>HFCT Amp.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="79"/>
        <source>HFCT PRPS</source>
        <translation>HFCT PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="80"/>
        <source>HFCT Period</source>
        <translation>HFCT Period</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="81"/>
        <source>Infrared</source>
        <translation>Infravermelho</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="83"/>
        <source>Mode</source>
        <translation>Modo</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="84"/>
        <source>Single</source>
        <translation>Escolha única</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="85"/>
        <source>Multiple</source>
        <translation>Múltiple</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="87"/>
        <source>APP Platform</source>
        <translation>Plataforma de APP</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="88"/>
        <source>CMS Platform</source>
        <translation>Plataforma CMS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="89"/>
        <source>PDST Platform</source>
        <translation>Plataforma PDST</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="90"/>
        <source>PDStar Platform</source>
        <translation>Plataforma PDStar</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="91"/>
        <source>T95 Platform</source>
        <translation>Plataforma T95</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="92"/>
        <source>T90 Platform</source>
        <translation>Plataforma T90</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="93"/>
        <source>All Platform</source>
        <translation>All Platform</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="95"/>
        <source>New</source>
        <translation>Novo</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="96"/>
        <source>New Task Not Pass</source>
        <translation>Nova tarefa não aprovada</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="97"/>
        <source>New Task Passed</source>
        <translation>Nova tarefa aprovada</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="98"/>
        <source>Accepted</source>
        <translation>Aceitaram</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="99"/>
        <source>Data Uploaded</source>
        <translation>Data Uploaded</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="100"/>
        <source>Submitted</source>
        <translation>submetidas</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="101"/>
        <source>Data First Check Passed</source>
        <translation>Os dados passaram na revisão do primeiro nível</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="102"/>
        <source>Data First Check Not Pass</source>
        <translation>Dados falhou a primeira revisão nível</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="103"/>
        <source>Data Second Check Passed</source>
        <translation>Os dados passou auditoria secundário</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="104"/>
        <source>Data Second Check Not Pass</source>
        <translation>Os dados falharam na revisão secundária</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="105"/>
        <source>DOC Check Passed</source>
        <translation>DOC check Passou</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="106"/>
        <source>DOC Check Not Pass</source>
        <translation>A verificação DOC não passa</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="107"/>
        <source>Declined</source>
        <translation>Recusado</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="108"/>
        <source>Canceled</source>
        <translation>Cancelado</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="109"/>
        <source>Submit</source>
        <translation>Enviar</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="110"/>
        <source>All State</source>
        <translation>Todo o Estado</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="112"/>
        <source>Detection Mode</source>
        <translation>Modo de Detecção</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="113"/>
        <source>Auto</source>
        <translation>Auto</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="116"/>
        <source>Select Patrol Position</source>
        <translation>Select Patrol Posição</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="117"/>
        <source>Default</source>
        <translation>Predefinido</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="118"/>
        <source>Cabinet Front</source>
        <translation>Na frente do armário</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="119"/>
        <source>Cabinet Side</source>
        <translation>Lado do armário</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="120"/>
        <source>Cabinet Back</source>
        <translation>Atrás do armário</translation>
    </message>
</context>
<context>
    <name>PMView</name>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="78"/>
        <source>UHF</source>
        <translation>UHF</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="83"/>
        <source>HFCT</source>
        <translation>HFCT</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="88"/>
        <source>CA Diag.</source>
        <translation>CA Diag.</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="93"/>
        <source>AE</source>
        <translation>AE</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="98"/>
        <source>Current</source>
        <translation>Current</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="109"/>
        <source>Power Sync.</source>
        <translation>Power Sync.</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="26"/>
        <source>UHF Signal Processor</source>
        <translation>Processador de sinal UHF</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="27"/>
        <source>HFCT Signal Processor</source>
        <translation>Processador de sinal HCFT</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="28"/>
        <source>Sync. Signal Processor</source>
        <translation>Processador de sinal Sync.</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="29"/>
        <source>Receiver Pairing</source>
        <translation>Receiver Pairing</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="30"/>
        <source>External Pairing</source>
        <translation>Emparelhamento externo</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="31"/>
        <source>AE Signal Processor</source>
        <translation>Processador de sinal AE</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="32"/>
        <source>TEV Signal Processor</source>
        <translation>Processador de sinal TEV</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="33"/>
        <source>Current Signal Processor</source>
        <translation>Processador de sinal Current</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="36"/>
        <source>CA Diag. Signal Processor</source>
        <translation>CA Diag. Processador de sinal</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="37"/>
        <source>Paired!</source>
        <translation>Emparelhado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="38"/>
        <source>Failed pairing, try again.</source>
        <translation>Falha no emparelhamento, tente novamente.</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="39"/>
        <source>Already connected.</source>
        <translation>Já conectado.</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="40"/>
        <source>No paired CA Diag. processor.</source>
        <translation>Nenhum CA Diag. processador emparelhado.</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="41"/>
        <source>Disconnected!</source>
        <translation>Desconectado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="42"/>
        <source>Disconnect fail!</source>
        <translation>Desconectar falhar!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="43"/>
        <source>Signal Processor is disconnected!</source>
        <translation>Processador de sinal está desconectado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="44"/>
        <source>UHF Signal Processor not found!</source>
        <translation>Processador UHF não encontrado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="45"/>
        <source>HFCT Signal Processor not found!</source>
        <translation>Processador HFCT não encontrado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="46"/>
        <source>Sync. not found!</source>
        <translation>Sync. não encontrado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="47"/>
        <source>Receiver not found!</source>
        <translation>Receptor não encontrado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="48"/>
        <source>External not found!</source>
        <translation>Externo não encontrado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="49"/>
        <source>AE Signal Processor not found!</source>
        <translation>Processador AE não encontrado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="50"/>
        <source>TEV Signal Processor not found!</source>
        <translation>Processador TEV não encontrado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="51"/>
        <source>Current Signal Processor not found!</source>
        <translation>Processador Current não encontrado!</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="53"/>
        <source>Already connected other host.</source>
        <translation>Já está conectado outro host.</translation>
    </message>
</context>
<context>
    <name>PRPSView</name>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1157"/>
        <source>Please switch to accumulation mode.</source>
        <translation>Por favor, mude para o modo de acumulação.</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1191"/>
        <source>Please sample data before recording.</source>
        <translation>Por favor dados da amostra antes da gravação.</translation>
    </message>
</context>
<context>
    <name>PhaseTypeView</name>
    <message>
        <location filename="view/customaccessUi/taskmodeview/phasetypeview.cpp" line="443"/>
        <source>Read grounding current failed!</source>
        <translation>Leia a corrente de aterramento falhou!</translation>
    </message>
</context>
<context>
    <name>PlayView</name>
    <message>
        <location filename="view/recordplay/playview.cpp" line="245"/>
        <location filename="view/recordplay/playview.cpp" line="267"/>
        <source>Please select an audio file.</source>
        <translation>Por favor, selecione um arquivo de áudio.</translation>
    </message>
    <message>
        <location filename="view/recordplay/playview.cpp" line="677"/>
        <source>Play failed!</source>
        <translation>A reprodução falhou!</translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="332"/>
        <source>Arcing</source>
        <translation>Suspensão</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="334"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="83"/>
        <location filename="view/ca/CAViewConfig.cpp" line="31"/>
        <source>Floating Electrode</source>
        <translation>Floating Electrode</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="340"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="80"/>
        <location filename="view/ca/CAViewConfig.cpp" line="34"/>
        <source>Corona</source>
        <translation>Corona</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="345"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="86"/>
        <location filename="view/ca/CAViewConfig.cpp" line="37"/>
        <source>Void</source>
        <translation>Void</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="350"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="92"/>
        <location filename="view/ca/CAViewConfig.cpp" line="40"/>
        <source>Particle</source>
        <translation>Particle</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="358"/>
        <source>Tracking</source>
        <translation>Surface</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="362"/>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="366"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="89"/>
        <location filename="view/ca/CAViewConfig.cpp" line="43"/>
        <source>Surface</source>
        <translation>Surface</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="372"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="77"/>
        <location filename="view/ca/CAViewConfig.cpp" line="46"/>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="71"/>
        <source>Normal</source>
        <translation>Normal</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="378"/>
        <source>Interference</source>
        <translation>Interferência</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="380"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="95"/>
        <location filename="view/ca/CAViewConfig.cpp" line="57"/>
        <source>Noise</source>
        <translation>Ruído</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="386"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="101"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="152"/>
        <location filename="module/pda/taskfileio.cpp" line="3043"/>
        <location filename="view/ca/CAViewConfig.cpp" line="60"/>
        <source>Unknown</source>
        <translation>Desconhecido</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="391"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="98"/>
        <location filename="view/ca/CAView.cpp" line="117"/>
        <location filename="view/ca/wave/waveview.cpp" line="1382"/>
        <source>Calibration</source>
        <translation>Calibração</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="396"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="104"/>
        <source>Insufficient Data</source>
        <translation>Dados insuficientes</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="401"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="107"/>
        <source>Drill Noise</source>
        <translation>Drill Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="406"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="110"/>
        <source>Energy Saving Lamps Noise</source>
        <translation>Energy Saving Lamps Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="411"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="113"/>
        <source>Fan Noise</source>
        <translation>Fan Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="416"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="116"/>
        <source>Ignition Noise</source>
        <translation>Ignition Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="421"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="119"/>
        <source>Interphone Noise</source>
        <translation>Interphone Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="426"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="122"/>
        <source>Microwave Sulfer Noise</source>
        <translation>Microwave Sulfer Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="431"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="125"/>
        <source>Motor Noise</source>
        <translation>Motor Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="436"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="128"/>
        <location filename="view/ca/CAViewConfig.cpp" line="69"/>
        <source>Radar Noise</source>
        <translation>Radar Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="441"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="131"/>
        <source>Spark Leak Detector Noise</source>
        <translation>Spark Leak Detector Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="446"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="134"/>
        <location filename="view/ca/CAViewConfig.cpp" line="72"/>
        <source>Mobile Noise</source>
        <translation>Mobile Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="452"/>
        <source>Internal</source>
        <translation>PD</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="454"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="137"/>
        <source>PD</source>
        <translation>PD</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="460"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="140"/>
        <source>Not PD</source>
        <translation>Not PD</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="465"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="143"/>
        <source>Insulation</source>
        <translation>Insulation</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="470"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="146"/>
        <source>Mechanical Vibration</source>
        <translation>Mechanical Vibration</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="475"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="149"/>
        <source>Light Noise</source>
        <translation>Light Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="480"/>
        <source>Pest Repeller Noise</source>
        <translation>Pest Repeller Noise</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="495"/>
        <source>Suspected</source>
        <translation>Suspeita</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="14"/>
        <source>Please login.</source>
        <translation>Por favor faça o login.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="19"/>
        <source>Please login again.</source>
        <translation>Por favor faça login novamente.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="24"/>
        <source>Service deals error.</source>
        <translation>Erro de processamento do servidor.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="30"/>
        <location filename="view/customaccessUi/customaccessloginview.cpp" line="55"/>
        <location filename="view/systemsetview/systemsetwidget/settingloginview.cpp" line="34"/>
        <source>User name or password error.</source>
        <translation>Erro de nome de usuário ou senha.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="35"/>
        <source>Account locked.</source>
        <translation>A conta está bloqueada.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="40"/>
        <location filename="module/webserver/controller/appserverutils.cpp" line="160"/>
        <source>Service unavailable.</source>
        <translation>Serviço indisponível.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="45"/>
        <source>Signature verification error.</source>
        <translation>Erro de verificação de assinatura.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="50"/>
        <source>Request parameter error.</source>
        <translation>Erro no parâmetro de solicitação.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="55"/>
        <location filename="module/globalerrprocess.cpp" line="80"/>
        <location filename="module/globalerrprocess.cpp" line="130"/>
        <source>No operation permission.</source>
        <translation>Sem permissão de operação.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="60"/>
        <source>Request timeout.</source>
        <translation>Solicitar tempo limite.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="65"/>
        <source>Password format error.</source>
        <translation>Erro no formato da senha.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="70"/>
        <source>Password parse error.</source>
        <translation>Erro de análise de senha.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="75"/>
        <source>User name not exist.</source>
        <translation>Nome de usuário não existe.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="85"/>
        <source>User name format error.</source>
        <translation>Erro no formato do nome de usuário.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="90"/>
        <source>Device submit parameter format error.</source>
        <translation>O formato do parâmetro de informações enviado pelo dispositivo está incorreto.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="95"/>
        <source>Login info not exist.</source>
        <translation>As informações de login não existem.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="100"/>
        <source>Task is not in being download state.</source>
        <translation>A tarefa não está em um estado para download.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="105"/>
        <source>Task is not in being submit state.</source>
        <translation>Task is not in being submit state.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="110"/>
        <source>Patrol file is unmatched.</source>
        <translation>O arquivo de patrulha é incomparável.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="115"/>
        <source>Task file is not exist or record error.</source>
        <translation>O arquivo de tarefas não existe ou o registro está incorreto.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="120"/>
        <source>Task has no data to submit.</source>
        <translation>Tarefa não tem dados para enviar.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="125"/>
        <source>Task number is empty.</source>
        <translation>O número da tarefa está vazio.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="135"/>
        <location filename="module/webserver/controller/appserverutils.cpp" line="163"/>
        <source>Task is not exist.</source>
        <translation>Tarefa não existe.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="140"/>
        <source>Task has been completed, cannot be uploaded repeatedly.</source>
        <translation>Task has been completed, cannot be uploaded repeatedly.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="145"/>
        <source>Download task error.</source>
        <translation>Erro na tarefa de download.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="150"/>
        <source>Accept task error.</source>
        <translation>Falha ao aceitar a tarefa.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="155"/>
        <source>Upload task fail.</source>
        <translation>Upload task fail.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="160"/>
        <location filename="view/PDAUi/PDAUiBean/pdaprogressdialog.cpp" line="142"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="597"/>
        <source>Upload fail.</source>
        <translation>Sube fallar.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="165"/>
        <source>Undefined function.</source>
        <translation>Função indefinida.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="170"/>
        <source>No requested unzip method.</source>
        <translation>O método de descompactação solicitado não é suportado.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="175"/>
        <source>File path error.</source>
        <translation>Erro no caminho do arquivo.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="180"/>
        <source>Request task list error.</source>
        <translation>Request task list error.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="185"/>
        <source>Patrol task is not exist.</source>
        <translation>Tarefa de patrulha não existe.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="190"/>
        <location filename="module/webserver/controller/appserverutils.cpp" line="166"/>
        <source>Task is in patrolling.</source>
        <translation>Task is in patrolling.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="195"/>
        <location filename="module/webserver/controller/appserverutils.cpp" line="169"/>
        <source>File operation error.</source>
        <translation>Erro de operação do arquivo.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="200"/>
        <location filename="module/webserver/controller/appserverutils.cpp" line="172"/>
        <source>MD5 verification error.</source>
        <translation>Erro de verificação MD5.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="205"/>
        <source>Http response message is empty.</source>
        <translation>A mensagem de resposta HTTP está vazia.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="210"/>
        <source>Save task number error.</source>
        <translation>Salvar erro número de teste.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="215"/>
        <source>Get task number fail.</source>
        <translation>Falha ao obter o número da tarefa.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="220"/>
        <source>Upload test data fail.</source>
        <translation>Falha ao fazer o upload de dados de teste.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="225"/>
        <source>Save test data fail.</source>
        <translation>Falha ao salvar os dados de teste.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="230"/>
        <source>Upload task description file fail.</source>
        <translation>Upload task description file fail.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="235"/>
        <source>Upload task info error.</source>
        <translation>Upload task info error.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="240"/>
        <source>Task number unmatched.</source>
        <translation>O número da tarefa não corresponde.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="245"/>
        <source>Method unmatched.</source>
        <translation>O método não corresponde.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="250"/>
        <source>Task state unmatched.</source>
        <translation>O status da tarefa não corresponde.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="255"/>
        <source>Task serial number error.</source>
        <translation>Erro no número de série da tarefa.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="260"/>
        <source>Client verification fail.</source>
        <translation>Falha na autenticação do cliente.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="265"/>
        <source>Input parameter error.</source>
        <translation>Erro no parâmetro de entrada.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="270"/>
        <source>Task list is empty.</source>
        <translation>A lista de tarefas está vazia.</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="275"/>
        <source>Connect fail.</source>
        <translation>Falha na conexão.</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="130"/>
        <source>Unnamed</source>
        <translation>Sem nome</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="138"/>
        <source>Front</source>
        <translation>Frente</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="139"/>
        <source>Side</source>
        <translation>Lado</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="140"/>
        <source>Back</source>
        <translation>Costas</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2882"/>
        <source>Transformer</source>
        <translation>Transformer</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2887"/>
        <source>Circuit Breaker</source>
        <translation>Circuit Breaker</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2892"/>
        <source>Isolation Switcher</source>
        <translation>Isolation Switcher</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2897"/>
        <source>Knife Gate</source>
        <translation>Knife Gate</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2902"/>
        <source>Lightning Arrester</source>
        <translation>Lightning Arrester</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2907"/>
        <source>Voltage Transformer</source>
        <translation>Voltage Transformer</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2912"/>
        <source>Current Transformer</source>
        <translation>Current Transformer</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2917"/>
        <source>Bus Bar</source>
        <translation>Bus Bar</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2922"/>
        <source>Bus Coupler</source>
        <translation>Bus Coupler</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2927"/>
        <source>Switchgear</source>
        <translation>Switchgear</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2932"/>
        <source>Cable</source>
        <translation>Cabo</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2937"/>
        <source>Lightning Rod</source>
        <translation>Lightning Rod</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2942"/>
        <source>Wall Bushing</source>
        <translation>Wall Bushing</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2947"/>
        <source>Reactor</source>
        <translation>Reactor</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2952"/>
        <source>Conductor</source>
        <translation>Conductor</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2957"/>
        <source>Capacitor</source>
        <translation>Capacitor</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2962"/>
        <source>Discharge Coil</source>
        <translation>Discharge Coil</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2967"/>
        <source>Load Switcher</source>
        <translation>Load Switcher</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2972"/>
        <source>Grounding Transformer</source>
        <translation>Grounding Transformer</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2977"/>
        <source>Grounding Resistor</source>
        <translation>Grounding Resistor</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2982"/>
        <source>Grounding Grid</source>
        <translation>Grounding Grid</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2987"/>
        <source>Combined Filter</source>
        <translation>Combined Filter</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2992"/>
        <source>Insulator</source>
        <translation>Insulator</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2997"/>
        <source>Coupling Capacitor</source>
        <translation>Coupling Capacitor</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3002"/>
        <source>Power Cabinet</source>
        <translation>Power Cabinet</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3007"/>
        <source>Other Device</source>
        <translation>Outros dispositivos</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3012"/>
        <source>Power Fuse</source>
        <translation>Power Fuse</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3017"/>
        <source>Spot Transformer</source>
        <translation>Spot Transformer</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3022"/>
        <source>Arc Suppression Device</source>
        <translation>Dispositivo de supressão de arco</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3027"/>
        <source>Blocker</source>
        <translation>Blocker</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3032"/>
        <source>GIS</source>
        <translation>GIS</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3037"/>
        <source>Combined Transformer</source>
        <translation>Combined Transformer</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="154"/>
        <source>Success.</source>
        <translation>Sucesso.</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="157"/>
        <source>Verification error.</source>
        <translation>Erro de verificação.</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="175"/>
        <source>Activation code error.</source>
        <translation>Erro no código de ativação.</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="178"/>
        <source>Compress files failure error.</source>
        <translation>Erro na compactação de arquivo.</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="181"/>
        <source>Empty information error.</source>
        <translation>Erro de informações vazias.</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="184"/>
        <source>Empty test data list error.</source>
        <translation>Erro de lista de dados de teste vazio.</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="187"/>
        <source>Unknown error.</source>
        <translation>Erro desconhecido.</translation>
    </message>
    <message>
        <location filename="widget/chartview/ChartView.cpp" line="505"/>
        <source>More...</source>
        <translation>Mais...</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="329"/>
        <source>Proto</source>
        <translation>Protótipo</translation>
    </message>
    <message>
        <location filename="widget/deleteDataView/DeleteFileWidget.cpp" line="87"/>
        <location filename="widget/deleteDataView/DeleteFileWidget.cpp" line="95"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidget.cpp" line="86"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidget.cpp" line="95"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidgetEx.cpp" line="59"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidgetEx.cpp" line="68"/>
        <location filename="view/ae/aeplaybackview.cpp" line="145"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="958"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsplaybackview.cpp" line="95"/>
        <location filename="view/infrared/infraredplayback.cpp" line="219"/>
        <location filename="view/infrared/infraredview.cpp" line="632"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsplaybackview.cpp" line="98"/>
        <location filename="view/widgets/sampleChartView/SampleChartView.cpp" line="739"/>
        <source>Confirm to delete the file?</source>
        <translation>Confirme para excluir o arquivo?</translation>
    </message>
    <message>
        <location filename="widget/deleteDataView/DeleteFileWidget.cpp" line="121"/>
        <location filename="widget/deleteDataView/DeleteFileWidget.cpp" line="129"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidget.cpp" line="121"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidget.cpp" line="129"/>
        <location filename="view/distributenetaccess/distributenetaddtaskview.cpp" line="196"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="196"/>
        <source>Cannot be deleted!</source>
        <translation>Não pode ser excluído!</translation>
    </message>
    <message>
        <location filename="widget/guideinfrared/guideinfraredimagingview.cpp" line="789"/>
        <location filename="widget/infrared/infraredimagingview.cpp" line="745"/>
        <source>Substation Name</source>
        <translation>Nome da Subestação</translation>
    </message>
    <message>
        <location filename="widget/guideinfrared/guideinfraredimagingview.cpp" line="790"/>
        <location filename="widget/infrared/infraredimagingview.cpp" line="746"/>
        <source>Asset</source>
        <translation>Equipamento</translation>
    </message>
    <message>
        <location filename="widget/guideinfrared/guideinfraredimagingview.cpp" line="791"/>
        <location filename="widget/infrared/infraredimagingview.cpp" line="747"/>
        <source>Test Point</source>
        <translation>Ponto de teste</translation>
    </message>
    <message>
        <location filename="widget/infrared/colorcodedwidget.cpp" line="101"/>
        <location filename="widget/infrared/colorcodedwidget.cpp" line="197"/>
        <source>Auto</source>
        <translation>Auto</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredparameterdialog.cpp" line="52"/>
        <location filename="widget/infrared/rotateparameterdialog.cpp" line="89"/>
        <location filename="widget/messageBox/msgbox.cpp" line="504"/>
        <location filename="view/PDAUi/PDAUiBean/addtestdatadialog.cpp" line="109"/>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="133"/>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="184"/>
        <location filename="view/currentdetection/valuesettingdialog.cpp" line="81"/>
        <location filename="view/distributenetaccess/filtersettingdlg.cpp" line="36"/>
        <location filename="view/rfid/RFIDReadView.cpp" line="55"/>
        <location filename="view/systemsetview/systemsetabout/clock/ClockWindow.cpp" line="171"/>
        <location filename="view/systemsetview/systemsetabout/cloud/systemsettinggrounp.cpp" line="72"/>
        <location filename="view/systemsetview/systemsetabout/date/DateView.cpp" line="396"/>
        <location filename="view/systemsetview/systemsetabout/mobilenetwork/pppsettinggroup.cpp" line="77"/>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="53"/>
        <location filename="view/widgets/filecommentbox/filecommentbox.cpp" line="42"/>
        <source>OK</source>
        <translation>OK</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredparameterdialog.cpp" line="56"/>
        <location filename="widget/infrared/rotateparameterdialog.cpp" line="90"/>
        <location filename="widget/messageBox/msgbox.cpp" line="498"/>
        <location filename="view/PDAUi/PDAUiBean/addtestdatadialog.cpp" line="115"/>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="140"/>
        <location filename="view/currentdetection/valuesettingdialog.cpp" line="88"/>
        <location filename="view/distributenetaccess/filtersettingdlg.cpp" line="42"/>
        <location filename="view/rfid/rfidwriteview.cpp" line="76"/>
        <location filename="view/systemsetview/systemsetabout/wifisetting/inputwifipasswd.cpp" line="145"/>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="59"/>
        <location filename="view/widgets/filecommentbox/filecommentbox.cpp" line="48"/>
        <source>Cancel</source>
        <translation>Cancelar</translation>
    </message>
    <message>
        <location filename="widget/infrared/linetemperaturecurve.cpp" line="64"/>
        <source>Temp. Points Count</source>
        <translation>Contagem de pontos de temperatura</translation>
    </message>
    <message>
        <location filename="widget/infrared/linetemperaturecurve.cpp" line="67"/>
        <source>Unit(℉)</source>
        <translation>Unidade (℉)</translation>
    </message>
    <message>
        <location filename="widget/infrared/linetemperaturecurve.cpp" line="71"/>
        <source>Unit(℃)</source>
        <translation>Unidade (℃)</translation>
    </message>
    <message>
        <location filename="widget/messageBox/msgbox.cpp" line="628"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="954"/>
        <location filename="view/infrared/infraredview.cpp" line="628"/>
        <source>Delete</source>
        <translation>Excluir</translation>
    </message>
    <message>
        <location filename="widget/playbackView/PlayBackTitle.cpp" line="81"/>
        <source>Substation Name: </source>
        <translation>Nome da Subestação: </translation>
    </message>
    <message>
        <location filename="widget/playbackView/PlayBackTitle.cpp" line="99"/>
        <source>Asset: </source>
        <translation>Equipamento: </translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="114"/>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="819"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="415"/>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="106"/>
        <location filename="view/currentdetection/currentdetectionchart.cpp" line="133"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="462"/>
        <location filename="view/uhf/uhfintervalview/UHFSpectrumChart.cpp" line="520"/>
        <location filename="view/widgets/histogram/HistogramChart.cpp" line="606"/>
        <source>No signal</source>
        <translation>Sem sinal</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="835"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="429"/>
        <location filename="view/ca/prps/prpsview.cpp" line="2347"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="2080"/>
        <location filename="view/ca/wave/waveview.cpp" line="2012"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="372"/>
        <location filename="view/widgets/histogram/HistogramChart.cpp" line="132"/>
        <source>Low Gain</source>
        <translation>Baixo ganho</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="845"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="439"/>
        <location filename="view/ca/prps/prpsview.cpp" line="2334"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="2067"/>
        <location filename="view/ca/wave/waveview.cpp" line="1999"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="387"/>
        <location filename="view/widgets/histogram/HistogramChart.cpp" line="137"/>
        <source>High Gain</source>
        <translation>Alto ganho</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="918"/>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="1396"/>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="1407"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="46"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="501"/>
        <source>Power Sync</source>
        <translation>Power Sync</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="923"/>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="1412"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="506"/>
        <source>Light Sync</source>
        <translation>Sincronização de luz</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="935"/>
        <source>Sync Lost</source>
        <translation>Sync Lost</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="1400"/>
        <source>Internal Sync</source>
        <translation>Internal Sync</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="640"/>
        <source>Don&apos;t remind</source>
        <translation>Não lembra</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="1028"/>
        <location filename="view/ca/prps/prpsview.cpp" line="322"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="339"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="236"/>
        <location filename="view/ca/wave/waveview.cpp" line="296"/>
        <source>Disconnected!</source>
        <translation>Desconectado!</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdalistchart.cpp" line="66"/>
        <location filename="view/customaccessUi/commonitemview/commonitemlistview.cpp" line="87"/>
        <source>PgUp</source>
        <translation>PgUp</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdalistchart.cpp" line="73"/>
        <location filename="view/customaccessUi/commonitemview/commonitemlistview.cpp" line="95"/>
        <source>PgDn</source>
        <translation>PgDn</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdalistchart.cpp" line="875"/>
        <source>Untest</source>
        <translation>Não testado</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdalistchart.cpp" line="879"/>
        <source>Tested</source>
        <translation>Testado</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdalistchart.cpp" line="883"/>
        <source>Testing</source>
        <translation>Testando</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdaprogressdialog.cpp" line="51"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="572"/>
        <location filename="view/ae/aeflyview/aeflypdaview.cpp" line="787"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="562"/>
        <location filename="view/ae/aephaseview/aephasepdaview.cpp" line="818"/>
        <source>Stop</source>
        <translation>Pare</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdaprogressdialog.cpp" line="170"/>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="330"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="748"/>
        <source>Download succeeded.</source>
        <translation>Download bem-sucedido.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdaprogressdialog.cpp" line="147"/>
        <location filename="view/PDAUi/PDAUiBean/pdaprogressdialog.cpp" line="156"/>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="443"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="593"/>
        <source>Upload success.</source>
        <translation>O upload foi realizado com sucesso.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="78"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="167"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="218"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestview.cpp" line="185"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestbgview.cpp" line="166"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="169"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestview.cpp" line="202"/>
        <source>Asset Name: </source>
        <translation>Nome do Ativo: </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="83"/>
        <source>Asset Type: </source>
        <translation>Tipo de Ativo: </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="88"/>
        <source>Asset Model: </source>
        <translation>Modelo de Ativo: </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="93"/>
        <source>Asset Number: </source>
        <translation>Número do Ativo: </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="116"/>
        <source>Voltage Level: </source>
        <translation>Nível de tensão: </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="121"/>
        <source>Release Date: </source>
        <translation>Fabricação Data: </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="126"/>
        <source>Manufacturer: </source>
        <translation>Fabricante: </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="131"/>
        <source>Group Name: </source>
        <translation>Nome do grupo:</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="44"/>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="84"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="59"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="60"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="42"/>
        <source>Task List</source>
        <translation>Lista de tarefas</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="98"/>
        <source>Refreshing ...</source>
        <translation>Refrescar …</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="258"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="368"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="446"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="241"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="257"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="328"/>
        <source>Please choose a task!</source>
        <translation>Por favor, escolha uma tarefa!</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="351"/>
        <source>Sifting ...</source>
        <translation>Peneirando ...</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="369"/>
        <source>Restoring default ...</source>
        <translation>Restaurando padrão ...</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="390"/>
        <source>Getting the task list ...</source>
        <translation>Obtendo a lista de tarefas ...</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="43"/>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="41"/>
        <source>Asset List</source>
        <translation>Lista de ativos</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="127"/>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="402"/>
        <source>Please test background points firstly.</source>
        <translation>Por favor, realize um teste de fundo em primeiro lugar.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="327"/>
        <source>No matched device!</source>
        <translation>Nenhum dispositivo correspondente!</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="332"/>
        <source>Scan RFID failed!</source>
        <translation>RFID varredura falhou!</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdaloginview.cpp" line="180"/>
        <source>Cloud Login</source>
        <translation>Cloud Login</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdapatroltypeview.cpp" line="111"/>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="782"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="232"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="83"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="65"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="140"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="135"/>
        <source>The disk is occupied, please disconnect mobilephone or computer!</source>
        <translation>O disco está ocupado, desconecte o celular ou o computador!</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdapointlistview.cpp" line="26"/>
        <location filename="view/customaccessUi/intervalview.cpp" line="132"/>
        <location filename="view/customaccessUi/intervalview.cpp" line="359"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="24"/>
        <source>Test Point List</source>
        <translation>Lista de pontos de teste</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdapointlistview.cpp" line="193"/>
        <location filename="view/PDAUi/PDAUiView/pdapointlistview.cpp" line="204"/>
        <source>Current patrol position test completed.</source>
        <translation>Teste de posição de patrulha atual concluído.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdapointtypeview.cpp" line="15"/>
        <source>Test Point Type</source>
        <translation>Tipo de ponto de teste</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="140"/>
        <location filename="view/PDAUi/PDAUiView/pdapointtypeview.cpp" line="85"/>
        <location filename="view/PDAUi/PDAUiView/pdatestdataview.cpp" line="279"/>
        <source>Current point test completed.</source>
        <translation>Teste de ponto atual concluído.</translation>
    </message>
    <message numerus="yes">
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="145"/>
        <location filename="view/PDAUi/PDAUiView/pdapointtypeview.cpp" line="90"/>
        <location filename="view/PDAUi/PDAUiView/pdatestdataview.cpp" line="284"/>
        <source>Total: %n point(s), </source>
        <translation>
            <numerusform>Total: %n ponto, </numerusform>
            <numerusform>Total: %n pontos, </numerusform>
        </translation>
    </message>
    <message numerus="yes">
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="146"/>
        <location filename="view/PDAUi/PDAUiView/pdapointtypeview.cpp" line="91"/>
        <location filename="view/PDAUi/PDAUiView/pdatestdataview.cpp" line="285"/>
        <source>tested: %n point(s).</source>
        <translation>
            <numerusform>testado: %n ponto.</numerusform>
            <numerusform>testado: %n pontos.</numerusform>
        </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="230"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="680"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="654"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="587"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="469"/>
        <location filename="view/customaccessUi/taskmodeview/currentphasetypeview.cpp" line="238"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="562"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="565"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="723"/>
        <location filename="view/infrared/infraredview.cpp" line="489"/>
        <source>Empty data, save failure!</source>
        <oldsource>No data, Save failure!</oldsource>
        <translation type="unfinished">Os dados estão vazios, o salvamento falhou!</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="192"/>
        <source>Restore</source>
        <translation>Restaurar</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="191"/>
        <location filename="view/distributenetaccess/distributenetaddtaskview.cpp" line="190"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="187"/>
        <source>Confirm to delete?</source>
        <translation>Confirmar para excluir?</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="290"/>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="552"/>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="604"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="288"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="337"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="159"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="223"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="336"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="227"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="431"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="98"/>
        <source>No task file has been chosen.</source>
        <translation>Nenhum arquivo de tarefa foi escolhido.</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="586"/>
        <source>Reading the task ...</source>
        <translation>Lendo a tarefa ...</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="690"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="300"/>
        <source>Saving the task ...</source>
        <translation>Salvando a tarefa ...</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="25"/>
        <location filename="view/PDAUi/PDAUiView/pdatestdataview.cpp" line="29"/>
        <source>Test Item List</source>
        <translation>Lista de itens de teste</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="117"/>
        <location filename="view/ae/aecommonwidget/AEOption.cpp" line="199"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="296"/>
        <source>Channel:Internal</source>
        <translation>Channel:Internal</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="120"/>
        <location filename="view/ae/aecommonwidget/AEOption.cpp" line="202"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="299"/>
        <source>Channel:External</source>
        <translation>Channel:External</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="123"/>
        <location filename="view/ae/aecommonwidget/AEOption.cpp" line="205"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="302"/>
        <source>Wireless</source>
        <translation>Sem fio</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="149"/>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="405"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="351"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="373"/>
        <source>Trigger?</source>
        <translation>Gatilho?</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="264"/>
        <source>Noise Test</source>
        <translation>Teste de ruído</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="399"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="345"/>
        <source>Triggered</source>
        <translation>Triggered</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="312"/>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="609"/>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1377"/>
        <location filename="view/ae/aeampview/aeamppdaview.cpp" line="991"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="837"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="1217"/>
        <location filename="view/ae/aeflyview/aeflypdaview.cpp" line="768"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="857"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="1231"/>
        <location filename="view/ae/aephaseview/aephasepdaview.cpp" line="562"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="319"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="1197"/>
        <location filename="view/ae/aewaveview/aewavepdaview.cpp" line="909"/>
        <location filename="view/ca/prps/prpsview.cpp" line="864"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1471"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1714"/>
        <location filename="view/ca/prps/prpsview.cpp" line="2290"/>
        <location filename="view/ca/prps/prpsview.cpp" line="2311"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1728"/>
        <location filename="view/ca/wave/waveview.cpp" line="1320"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="1787"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestview.cpp" line="1929"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="1858"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2004"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="1965"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2102"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestbgview.cpp" line="1281"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="1381"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="1332"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="1473"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1038"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1032"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="682"/>
        <location filename="view/customaccessUi/taskmodeview/currentphasetypeview.cpp" line="360"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="849"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="940"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestbgview.cpp" line="1343"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="1497"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="1392"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="1567"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="1881"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2038"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestbgview.cpp" line="1307"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestview.cpp" line="1427"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestbgview.cpp" line="851"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="929"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestbgview.cpp" line="1368"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestview.cpp" line="1537"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1001"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="944"/>
        <location filename="view/hfct/hfctprpsview/hfctprpspdaview.cpp" line="628"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="306"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="875"/>
        <location filename="view/infrared/infraredview.cpp" line="618"/>
        <location filename="view/recordplay/recordplayview.cpp" line="126"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1573"/>
        <location filename="view/tev/tevamppdaview.cpp" line="534"/>
        <location filename="view/tev/tevpulsepdaview.cpp" line="572"/>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1259"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1842"/>
        <location filename="view/widgets/sampleChartView/SampleChartView.cpp" line="277"/>
        <location filename="view/widgets/sampleChartView/SampleChartView.cpp" line="732"/>
        <location filename="view/widgets/sampleChartView/SampleChartView.cpp" line="894"/>
        <source>Save failure!</source>
        <translation>Salve falha!</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1140"/>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1169"/>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1299"/>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1323"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="628"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="768"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="1136"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="1160"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="761"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="790"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="1152"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="1176"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="815"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="845"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="1119"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="1143"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1217"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1243"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1310"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1406"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1217"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1322"/>
        <location filename="view/ca/wave/waveview.cpp" line="1031"/>
        <location filename="view/ca/wave/waveview.cpp" line="1163"/>
        <location filename="view/currentdetection/bjcurrentdetectionview.cpp" line="209"/>
        <location filename="view/currentdetection/bjcurrentdetectionview.cpp" line="239"/>
        <location filename="view/currentdetection/currentdetectionview.cpp" line="224"/>
        <location filename="view/currentdetection/currentdetectionview.cpp" line="254"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="1804"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="2174"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestview.cpp" line="1947"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="1875"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="2260"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2021"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2497"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="1982"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="2367"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2119"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2595"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestbgview.cpp" line="529"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestbgview.cpp" line="1169"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="545"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="1266"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="528"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="1218"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="571"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="1328"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="1365"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="498"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="833"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="473"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="801"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="415"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="782"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="328"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="606"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="509"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="637"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="543"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="579"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="731"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestbgview.cpp" line="549"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestbgview.cpp" line="1229"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="576"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="1372"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="545"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="1277"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="586"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="1420"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="1457"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="1901"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="2276"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2055"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2516"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestbgview.cpp" line="504"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="530"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="566"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="715"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="418"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="735"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="406"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="736"/>
        <location filename="view/hfct/hfctamplitudeview/HFCTAmplitudeView.cpp" line="289"/>
        <location filename="view/hfct/hfctamplitudeview/HFCTAmplitudeView.cpp" line="319"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodView.cpp" line="699"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodView.cpp" line="727"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1450"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1474"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1503"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1532"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="571"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="936"/>
        <location filename="view/infrared/infraredview.cpp" line="348"/>
        <location filename="view/infrared/infraredview.cpp" line="692"/>
        <location filename="view/recordplay/recordplayview.cpp" line="90"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1331"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1363"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1715"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1750"/>
        <location filename="view/tev/tevampview.cpp" line="595"/>
        <location filename="view/tev/tevampview.cpp" line="625"/>
        <location filename="view/tev/tevpulseview.cpp" line="698"/>
        <location filename="view/tev/tevpulseview.cpp" line="728"/>
        <location filename="view/uhf/uhfamplitudeview/UHFAmplitudeView.cpp" line="308"/>
        <location filename="view/uhf/uhfamplitudeview/UHFAmplitudeView.cpp" line="338"/>
        <location filename="view/uhf/uhfintervalview/UHFIntervalView.cpp" line="755"/>
        <location filename="view/uhf/uhfintervalview/UHFIntervalView.cpp" line="783"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1598"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1630"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1992"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="2025"/>
        <source>No file!</source>
        <translation>Nenhum arquivo!</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1180"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="779"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="801"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="857"/>
        <source>Record</source>
        <translation>Registro</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1203"/>
        <location filename="view/ae/aeampview/aeamppdaview.cpp" line="1408"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="867"/>
        <location filename="view/ae/aeflyview/aeflypdaview.cpp" line="998"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="887"/>
        <location filename="view/ae/aephaseview/aephasepdaview.cpp" line="1064"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="879"/>
        <location filename="view/ae/aewaveview/aewavepdaview.cpp" line="815"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1252"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1143"/>
        <location filename="view/ca/wave/waveview.cpp" line="981"/>
        <location filename="view/hfct/hfctamplitudeview/HFCTAmplitudeView.cpp" line="592"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodView.cpp" line="520"/>
        <location filename="view/hfct/hfctprpsview/hfctprpspdaview.cpp" line="859"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="780"/>
        <location filename="view/systemsetview/systemsetwidget/routinesettingview.cpp" line="472"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1402"/>
        <location filename="view/tev/tevamppdaview.cpp" line="645"/>
        <location filename="view/tev/tevampview.cpp" line="638"/>
        <location filename="view/tev/tevpulsepdaview.cpp" line="649"/>
        <location filename="view/tev/tevpulseview.cpp" line="741"/>
        <location filename="view/uhf/uhfamplitudeview/UHFAmplitudeView.cpp" line="626"/>
        <location filename="view/uhf/uhfintervalview/UHFIntervalView.cpp" line="557"/>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1535"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1676"/>
        <source>Confirm to restore default settings?</source>
        <translation>Confirmar para restaurar as configurações padrão?</translation>
    </message>
    <message>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="585"/>
        <location filename="view/ae/aeflyview/aeflypdaview.cpp" line="806"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="575"/>
        <location filename="view/ae/aephaseview/aephasepdaview.cpp" line="837"/>
        <source>Sample</source>
        <translation>Amostra</translation>
    </message>
    <message>
        <location filename="view/ca/CAView.cpp" line="122"/>
        <source>Please connect CA Diag. processor.</source>
        <translation>Conecte o CA Diag. processador.</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.cpp" line="63"/>
        <source>Calibration Signal</source>
        <translation>Sinal de calibração</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.cpp" line="66"/>
        <source>Invalid Data</source>
        <translation>Dados inválidos</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsvideoplaybackview.cpp" line="151"/>
        <source>Loading ...</source>
        <translation>Carregando ...</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="913"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1462"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1526"/>
        <location filename="view/ca/prps/prpsview.cpp" line="2278"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1929"/>
        <location filename="view/ca/wave/waveview.cpp" line="1679"/>
        <location filename="view/ca/wave/waveview.cpp" line="1774"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1565"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1645"/>
        <source>No data!</source>
        <translation>Sem dados!</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="916"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1571"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1631"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1920"/>
        <source>Saving ...</source>
        <translation>Salvando ...</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1235"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1524"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1742"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="2017"/>
        <source>Delete Record</source>
        <translation>Excluir registro</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1326"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1232"/>
        <source>Set parameter fail, please check connection state.</source>
        <translation>parâmetro conjunto falhar, por favor, verifique o estado da conexão.</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1349"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1262"/>
        <source>Start sample fail, please check connection state.</source>
        <translation>Iniciar amostra falhar, verifique o estado da conexão.</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1425"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1751"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1339"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1833"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="941"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="1061"/>
        <location filename="view/ca/wave/waveview.cpp" line="1180"/>
        <location filename="view/ca/wave/waveview.cpp" line="1462"/>
        <source>Please pair CA Diag. processor first.</source>
        <translation>Por favor emparelhar processador CA Diag. primeiro.</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1430"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1344"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="946"/>
        <location filename="view/ca/wave/waveview.cpp" line="1185"/>
        <source>No paired CA Diag. processor.</source>
        <translation>Nenhum CA Diag. processador emparelhado.</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1557"/>
        <location filename="view/ca/pulse/pulseclusterview.cpp" line="279"/>
        <source>Signal Type: </source>
        <translation>Tipo de sinal: </translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1558"/>
        <location filename="view/ca/pulse/pulseclusterview.cpp" line="285"/>
        <source>Confidence Level: </source>
        <translation>Confidence Level: </translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1563"/>
        <source>Result</source>
        <translation>Resultado</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1664"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1405"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="993"/>
        <location filename="view/ca/wave/waveview.cpp" line="518"/>
        <source>Invalid coefficient, it will use default coefficient, please check HAS02 hardware!</source>
        <translation>Coeficiente inválido, ele usará o coeficiente padrão. Verifique o hardware HAS02!</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1669"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1410"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="998"/>
        <location filename="view/ca/wave/waveview.cpp" line="523"/>
        <source>No calibrated coefficient, please calibrate HAS02 firstly!</source>
        <translation>Nenhum coeficiente calibrado, por favor calibrar HAS02 em primeiro lugar!</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1719"/>
        <source>Save successfully!</source>
        <translation>Salvo com sucesso!</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1758"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1840"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="1068"/>
        <location filename="view/ca/wave/waveview.cpp" line="1469"/>
        <source>No available CA Diag. processor!</source>
        <translation>Não disponível CA Diag. processador!</translation>
    </message>
    <message>
        <location filename="view/ca/pulse/PulseAnalysisView.cpp" line="350"/>
        <location filename="view/ca/pulse/PulseAnalysisView.cpp" line="765"/>
        <location filename="view/ca/pulse/PulseAnalysisView.cpp" line="784"/>
        <location filename="view/ca/pulse/pulseclusterview.cpp" line="190"/>
        <source>No pulse!</source>
        <translation>No pulse!</translation>
    </message>
    <message>
        <location filename="view/ca/pulse/pulseclusterview.cpp" line="235"/>
        <source>Pulse Total Count: </source>
        <translation>Contagem total de pulso: </translation>
    </message>
    <message>
        <location filename="view/ca/pulse/pulseclusterview.cpp" line="268"/>
        <source>Result: </source>
        <translation>Resultado: </translation>
    </message>
    <message>
        <location filename="view/ca/pulse/pulsedetailview.cpp" line="286"/>
        <source>Pulse Count</source>
        <translation>Contagem de pulsos</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibrateview.cpp" line="736"/>
        <source>Invalid coefficient, please check HAS02 hardware!</source>
        <translation>Coeficiente inválido, verifique o hardware do HAS02!</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="1767"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestview.cpp" line="1909"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="1837"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="1984"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="1944"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2082"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestbgview.cpp" line="1288"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="1388"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="1338"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="1479"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1053"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1046"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="710"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="829"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="920"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestbgview.cpp" line="1350"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="1504"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="1398"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="1573"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="1859"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2017"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestbgview.cpp" line="1315"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestview.cpp" line="1435"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestbgview.cpp" line="830"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="908"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestbgview.cpp" line="1376"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestview.cpp" line="1545"/>
        <source>Save success, auto switching.</source>
        <translation>Economize com sucesso, irá saltar automaticamente.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="1818"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="2186"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="1889"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="2272"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2035"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2511"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="1996"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="2379"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2133"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2609"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestbgview.cpp" line="123"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="154"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="120"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="161"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="98"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="115"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestbgview.cpp" line="139"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="168"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="137"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="176"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="1915"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="2288"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2069"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2530"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestbgview.cpp" line="124"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestview.cpp" line="149"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestbgview.cpp" line="103"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="103"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestbgview.cpp" line="139"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestview.cpp" line="165"/>
        <source>Test Data List</source>
        <translation>Lista de dados de teste</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="440"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="664"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="1737"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="442"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="694"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="1879"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="473"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="704"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="1845"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="464"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="723"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="1978"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="491"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="786"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="842"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="527"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="838"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="896"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="316"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="481"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="558"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="340"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="509"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="616"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="510"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="837"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="889"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="546"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="889"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="942"/>
        <source>Tested Count: </source>
        <translation>Tested Count: </translation>
    </message>
    <message>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2548"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2646"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="520"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="538"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1111"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="627"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="553"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="557"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2567"/>
        <source>Delete succeeded!</source>
        <translation>Excluir com sucesso!</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="131"/>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="188"/>
        <source>Confirm to disconnect?</source>
        <translation>Confirme para desconectar?</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="241"/>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="289"/>
        <source>Access app success!</source>
        <translation>sucesso aplicativo Acesso!</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="255"/>
        <source>Please set the correct ip and port.</source>
        <translation>Por favor, defina o IP e porta correta.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="259"/>
        <source>Please confirm the server to be started.</source>
        <translation>Por favor, confirme se o serviço foi ativado.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="294"/>
        <source>Access app fail!</source>
        <translation>Falha no aplicativo de acesso!</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="328"/>
        <location filename="view/update/softwareinfoview.cpp" line="136"/>
        <source>Connecting ...</source>
        <translation>Conectando…</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/customaccessloginview.cpp" line="19"/>
        <source>Login</source>
        <translation>Iniciar sesión</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="846"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="900"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="439"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="448"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="508"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="519"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="319"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="343"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="893"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="946"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestbgview.cpp" line="176"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestbgview.cpp" line="195"/>
        <source>Bay Name: </source>
        <translation>Nome da Baía: </translation>
    </message>
    <message>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="904"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="346"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="950"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="217"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestview.cpp" line="186"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="170"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestview.cpp" line="203"/>
        <source>Test Point Name: </source>
        <translation>Nome do ponto de teste: </translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="279"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="268"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="233"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="218"/>
        <source>Device disconnected!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="290"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="279"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="229"/>
        <location filename="view/infrared/infraredview.cpp" line="190"/>
        <source>The connected infrared lens type has changed, please exit and re-enter the page.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="424"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="413"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="345"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="345"/>
        <source>Autofocus failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="432"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="421"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="353"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="353"/>
        <source>Close focus fine-tuning failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="440"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="429"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="361"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="361"/>
        <source>Far focus fine-tuning failed!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="692"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="666"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="667"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="530"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="569"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="574"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="851"/>
        <location filename="view/infrared/infraredview.cpp" line="596"/>
        <source>Saving data ...</source>
        <translation>Salvando dados ...</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="977"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="971"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="852"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="667"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="861"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="874"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="996"/>
        <location filename="view/infrared/infraredview.cpp" line="752"/>
        <source>Connecting to infrared device ...</source>
        <translation>Conectando ao dispositivo de infravermelho ...</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1165"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1143"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1038"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1001"/>
        <source>Failed to initialize parameters, do you want to exit the page?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1183"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1161"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="914"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="721"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1052"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1019"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="1055"/>
        <location filename="view/infrared/infraredview.cpp" line="811"/>
        <source>Initialization failed!</source>
        <translation>Falha na inicialização!</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1197"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1175"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1304"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1033"/>
        <source>Failed to turn off laser control!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1201"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1179"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1308"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1037"/>
        <source>Failed to turn on laser control!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1227"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1205"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1334"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1063"/>
        <source>Failed to turn off auxiliary lighting!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1231"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1209"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1338"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1067"/>
        <source>Failed to turn on auxiliary lighting!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="893"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="704"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="1034"/>
        <location filename="view/infrared/infraredview.cpp" line="790"/>
        <source>Init params failed, re-enter!</source>
        <translation>Parâmetros init falhou, entre novamente!</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="43"/>
        <source>Bay List</source>
        <translation>Bay List</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="245"/>
        <location filename="view/customaccessUi/taskmodeview/connectorview.cpp" line="249"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="304"/>
        <source>Unable to open multiple items.</source>
        <translation>Não foi possível abrir vários itens.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="249"/>
        <location filename="view/customaccessUi/taskmodeview/connectorview.cpp" line="253"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="308"/>
        <source>No item has been chosen.</source>
        <translation>Nenhum item foi escolhido.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="267"/>
        <source>Please select a bay first.</source>
        <translation>Por favor, selecione uma baía em primeiro lugar.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="297"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="195"/>
        <source>Not support to upload empty data or only background data!</source>
        <translation>Not support to upload empty data or only background data!</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="284"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="155"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="219"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="223"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="94"/>
        <source>Unable to open multiple task files.</source>
        <translation>Não é possível abrir vários arquivos de tarefas.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="341"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="364"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="253"/>
        <source>Nonsupport to upload multiple tasks.</source>
        <translation>O upload de várias tarefas não é suportado.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="451"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="246"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="333"/>
        <source>Confirm to delete the item?</source>
        <translation>Confirme para excluir o item?</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="538"/>
        <source>Subtask List</source>
        <translation>Lista subtarefa</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="669"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="782"/>
        <source>Prohibit shutdown the device during detection.</source>
        <translation>Proibir desligar o dispositivo durante a detecção.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="752"/>
        <source>Download failed.</source>
        <translation>Falha no Download.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/subtaskview.cpp" line="170"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="234"/>
        <source>Confirm to select a task.</source>
        <translation>Você deve selecionar uma tarefa.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="115"/>
        <source>All items have been tested.</source>
        <translation>Todos os itens foram testados.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="280"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="324"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="292"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="418"/>
        <source>Please complete the task first!</source>
        <translation>Conclua a tarefa primeiro!</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="332"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="427"/>
        <source>Unable to compress multiple task files.</source>
        <translation>A compactação de várias tarefas não é compatível.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="374"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="575"/>
        <source>Compress Done!</source>
        <translation>Comprimir Feito!</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/connectorview.cpp" line="130"/>
        <location filename="view/customaccessUi/taskmodeview/currentphasetypeview.cpp" line="462"/>
        <location filename="view/customaccessUi/taskmodeview/phasetypeview.cpp" line="484"/>
        <source>All items have been tested and will automatically jump.</source>
        <translation>Todos os itens foram testados e saltarão automaticamente.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/currentphasetypeview.cpp" line="34"/>
        <location filename="view/customaccessUi/taskmodeview/phasetypeview.cpp" line="33"/>
        <source>Phase Type</source>
        <translation>Tipo de Fase</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/testypeview.cpp" line="71"/>
        <source>Test Type</source>
        <translation>Tipo de Teste</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/testypeview.cpp" line="92"/>
        <source>PD Type</source>
        <translation>Tipo PD</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="67"/>
        <source>BG Test</source>
        <translation>Teste BG</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="259"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="486"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="206"/>
        <source>Please complete the background test.</source>
        <translation>Por favor, complete o teste de fundo.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="327"/>
        <source>Please select a test point.</source>
        <translation>Por favor, selecione um ponto de teste.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="344"/>
        <source>Empty test data in the point.</source>
        <translation>O ponto de teste selecionado não possui dados de teste.</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="436"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="445"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="165"/>
        <source>AE BKGD</source>
        <translation>AE BKGD</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="456"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="174"/>
        <source>TEV BKGD</source>
        <translation>TEV BKGD</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="463"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="182"/>
        <source>UHF BKGD</source>
        <translation>UHF BKGD</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="470"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="190"/>
        <source>HFCT BKGD</source>
        <translation>HFCT BKGD</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="502"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="513"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="214"/>
        <source>AE Detect</source>
        <translation>Detecção de EA</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="507"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="518"/>
        <source>Test Point: </source>
        <translation>Ponto de teste: </translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="526"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="224"/>
        <source>TEV Detect</source>
        <translation>Detecção de TEV</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="533"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="232"/>
        <source>UHF Detect</source>
        <translation>Detecção de UHF</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="540"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="240"/>
        <source>HFCT Detect</source>
        <translation>Detecção de HFCT</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetaddtaskview.cpp" line="145"/>
        <source>Create tasks successfully.</source>
        <translation>A tarefa foi criada com sucesso.</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetaddtaskview.cpp" line="150"/>
        <source>No asset has been chosen.</source>
        <translation>Nenhum ativo foi escolhido.</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="339"/>
        <source>Current task test completed.</source>
        <translation>Teste de tarefa atual concluído.</translation>
    </message>
    <message>
        <location filename="view/guideinfrared/guideinfraredplaybackview.cpp" line="186"/>
        <location filename="view/infrared/infraredplaybackview.cpp" line="141"/>
        <source>Open file failed!</source>
        <translation>Falha ao abrir arquivo!</translation>
    </message>
    <message>
        <location filename="view/guideinfrared/guideinfraredviewbase.cpp" line="349"/>
        <location filename="view/infrared/infraredviewbase.cpp" line="323"/>
        <source>Coefficient K</source>
        <translation>Coeficiente K</translation>
    </message>
    <message>
        <location filename="view/guideinfrared/guideinfraredviewbase.cpp" line="353"/>
        <location filename="view/infrared/infraredviewbase.cpp" line="327"/>
        <source>Coefficient B</source>
        <translation>Coeficiente B</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="54"/>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="377"/>
        <source>Searching</source>
        <translation>Procurando</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="185"/>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="290"/>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="27"/>
        <source>Refresh</source>
        <translation>Refrescar</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="207"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="305"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="325"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="330"/>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="350"/>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="791"/>
        <source>Current ID: </source>
        <translation>ID atual:</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="225"/>
        <source>Disconnect</source>
        <translation>Desconectar</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="448"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="671"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="680"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="689"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="698"/>
        <source>Update fail!</source>
        <translation>Falha na atualização!</translation>
    </message>
    <message>
        <location filename="view/rfid/RFIDScanView.cpp" line="35"/>
        <location filename="view/systemsetview/systemsetabout/common/scanloading.cpp" line="22"/>
        <source>Scanning ...</source>
        <translation>Digitalizando ...</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="69"/>
        <source>Write</source>
        <translation>Escrever</translation>
    </message>
    <message numerus="yes">
        <location filename="view/splash/activescreen.cpp" line="54"/>
        <source>The remaining activation time is %n day(s), please contact the supplier to re-activate.</source>
        <translation>
            <numerusform>O tempo restante de ativação é de %n dia, entre em contato com o fornecedor para reativação.</numerusform>
            <numerusform>O tempo restante de ativação é de %n dias, entre em contato com o fornecedor para reativação.</numerusform>
        </translation>
    </message>
    <message>
        <location filename="view/splash/activescreen.cpp" line="58"/>
        <source>The device is not activated, please contact the supplier to activate.</source>
        <translation>O dispositivo não está ativado, entre em contato com o fornecedor para ativar.</translation>
    </message>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="93"/>
        <source>Cable Multi-Parameter State Intelligent Diagnostic Instrument</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="99"/>
        <source>Intelligent Handheld Partial Discharge Detector</source>
        <translation>Intelligent Handheld Partial Discharge Detector</translation>
    </message>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="119"/>
        <location filename="view/systemsetview/systemsetabout/AboutInfoView.cpp" line="133"/>
        <source>Uncalibrated</source>
        <translation>Não calibrado</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/AboutInfoView.cpp" line="70"/>
        <source>Model: </source>
        <translation>Modelo: </translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/AboutInfoView.cpp" line="76"/>
        <source>PDStars Electric Co.,Ltd.</source>
        <translation>PDStars Electric Co.,Ltd.</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/AboutInfoView.cpp" line="106"/>
        <source>Serial No: </source>
        <translation>Número de série:</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/cloud/CloudSetting.cpp" line="31"/>
        <source>Cloud Settings</source>
        <translation>Configurações nuvem</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/cloud/CloudSetting.cpp" line="34"/>
        <location filename="view/update/updateSetting.cpp" line="36"/>
        <source>Cloud</source>
        <translation>Cloud</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/cloud/lineeditgrounp.cpp" line="91"/>
        <source>Address</source>
        <translation>Endereço</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/cloud/lineeditgrounp.cpp" line="96"/>
        <source>Port</source>
        <translation>Porta</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/common/settingloading.cpp" line="27"/>
        <source>Connecting</source>
        <translation>Conectando</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/mobilenetwork/ppplineeditgroup.cpp" line="91"/>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnlineeditgroup.cpp" line="86"/>
        <source>User</source>
        <translation>User</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/mobilenetwork/ppplineeditgroup.cpp" line="96"/>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnlineeditgroup.cpp" line="91"/>
        <source>PWD</source>
        <translation>PWD</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/mobilenetwork/ppplineeditgroup.cpp" line="101"/>
        <source>APN</source>
        <translation>APN</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="329"/>
        <source>To format storage or not?</source>
        <translation>Formatar cartão de memória?</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="335"/>
        <source>Format failed, the disk is being occupied.</source>
        <translation>Formato falhou, o disco é ocupado.</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="347"/>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="356"/>
        <source>Formatted!</source>
        <translation>O formato é bem sucedido!</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="351"/>
        <source>Cannot be formated!</source>
        <translation>Formato falhou!</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/versioninfo.cpp" line="242"/>
        <source>Screenshot successfully!</source>
        <translation>Captura de tela com sucesso!</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/versioninfo.cpp" line="246"/>
        <source>Screenshot failed!</source>
        <translation>Falha na captura de tela!</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnlineeditgroup.cpp" line="81"/>
        <source>Domain</source>
        <translation>Domínio</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/inputwifipasswd.cpp" line="151"/>
        <source>Connect</source>
        <translation>Conectar</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/routinesettingview.cpp" line="238"/>
        <source>USB already in R/W Mode.</source>
        <translation>USB já está no modo R/W.</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/routinesettingview.cpp" line="245"/>
        <source>Enable R/W mode for USB?</source>
        <translation>Confirmar a mudança para o modo USB de leitura e gravação?</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/settingloginview.cpp" line="19"/>
        <source>Admin Login</source>
        <translation>Admin Login</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/shortkeysettingview.cpp" line="301"/>
        <source>Confirm to take effect after auto shutdown and restart manually.</source>
        <translation>As alterações entrarão em vigor após o reinício e o dispositivo será desligado automaticamente.</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/shortkeysettingview.cpp" line="402"/>
        <source>Initialize calibration success.</source>
        <translation>Calibração de inicialização bem-sucedida.</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/updatesettingview.cpp" line="30"/>
        <source>Update</source>
        <translation>atualização</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="87"/>
        <source>Operation is not allowed while calibrating!</source>
        <translation>Durante a calibração, outras operações são proibidas!</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="498"/>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="579"/>
        <source>Please select a gear.</source>
        <translation>Por favor, selecione uma engrenagem.</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="596"/>
        <source>Write success.</source>
        <translation>Escreve com sucesso.</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="602"/>
        <source>Write failure.</source>
        <translation>Escrita falhou.</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="628"/>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="358"/>
        <source>Restoring default succeeded.</source>
        <translation>Restaurar o padrão com sucesso.</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="633"/>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="354"/>
        <source>Restoring default failed.</source>
        <translation>Falha ao restaurar o padrão.</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="689"/>
        <source>Get calibrate parameters failed.</source>
        <translation>Falha ao obter parâmetros de calibração.</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="736"/>
        <source>Calibrating, please wait.</source>
        <translation>Calibrando, por favor aguarde.</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="437"/>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="457"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="594"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="614"/>
        <source>Diagnostic unfinished, cannot exit!</source>
        <translation>O diagnóstico não está completo, por favor, não saia!</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1432"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1241"/>
        <source>Save the cloud diagnose file failed.</source>
        <translation>Falha ao salvar o arquivo de dados de diagnóstico.</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1438"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1246"/>
        <source>Diagnosing ...</source>
        <translation>Diagnosticando ...</translation>
    </message>
    <message>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="72"/>
        <source>Minor</source>
        <translation>Menor</translation>
    </message>
    <message>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="73"/>
        <source>Serious</source>
        <translation>Grave</translation>
    </message>
    <message>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="74"/>
        <source>Emergency</source>
        <translation>Emergência</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionchart.cpp" line="293"/>
        <location filename="view/widgets/histogram/HistogramChart.cpp" line="216"/>
        <location filename="view/widgets/histogram/HistogramChart.cpp" line="451"/>
        <source>Max: </source>
        <translation>Máximo: </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="44"/>
        <source>Sunshine</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="47"/>
        <source>Overcast</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="50"/>
        <source>Rainy</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="53"/>
        <source>Snowy</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="56"/>
        <source>Fog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="59"/>
        <source>Thunderstorm</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="62"/>
        <source>Cloudy</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="108"/>
        <source>Temperature</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="111"/>
        <source>Humidity</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="114"/>
        <source>Weather</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>R3TEVCalibrateView</name>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="269"/>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="302"/>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="333"/>
        <source>Operate failed!</source>
        <translation>Operação falhou!</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="31"/>
        <source>Calibration Pos 1</source>
        <translation>Posição de calibração 1</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="32"/>
        <source>Calibration Pos 2</source>
        <translation>Posição de calibração 2</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="33"/>
        <source>Calibration</source>
        <translation>Calibração</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="34"/>
        <source>More</source>
        <translation>Mais</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="35"/>
        <source>Channel</source>
        <translation>Canal</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="36"/>
        <source>Restore</source>
        <translation>Restaurar</translation>
    </message>
</context>
<context>
    <name>RFID</name>
    <message>
        <location filename="view/rfid/rfidview.cpp" line="38"/>
        <source>Read</source>
        <translation>Escrever</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidview.cpp" line="39"/>
        <source>Write</source>
        <translation>Escrever</translation>
    </message>
</context>
<context>
    <name>RFIDWriteView</name>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="57"/>
        <source>Write RFID</source>
        <translation>Escrever RFID</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="172"/>
        <location filename="view/rfid/rfidwriteview.cpp" line="261"/>
        <source>Can not write.</source>
        <translation>Impossibilitado de escrever</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="191"/>
        <source>Please input the 12-numerics test number.</source>
        <translation>Por favor insira um número de teste de 12 dígitos.</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="196"/>
        <source>Test number contains non-numeric characters.</source>
        <translation>O número do teste contém caracteres não numéricos.</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="201"/>
        <source>The length of the substation name is illegal.</source>
        <translation>O comprimento do caractere do nome da subestação não corresponde.</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="206"/>
        <source>The length of the asset name is illegal.</source>
        <translation>O comprimento do caractere do nome do dispositivo não corresponde.</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="211"/>
        <source>The length of the asset number is illegal.</source>
        <translation>O comprimento do caractere do número do dispositivo não corresponde.</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="216"/>
        <source>Asset number contains non-numeric and non-alphabetic characters.</source>
        <translation>O número do equipamento contém caracteres não numéricos e não alfabéticos.</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="221"/>
        <source>Illegal voltage level.</source>
        <translation>Illegal voltage level.</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="226"/>
        <source>Voltage level contains non-numeric characters.</source>
        <translation>O nível de tensão contém caracteres não numéricos.</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="241"/>
        <source>Confirm to write this info?</source>
        <translation>Você tem certeza de escrever esta informação?</translation>
    </message>
</context>
<context>
    <name>RecordPlayPanelView</name>
    <message>
        <location filename="view/recordplay/recordplayview.cpp" line="81"/>
        <source>Delete View</source>
        <translation>Excluir</translation>
    </message>
</context>
<context>
    <name>RecordPlayView</name>
    <message>
        <location filename="view/recordplay/recordplayview.cpp" line="31"/>
        <location filename="view/recordplay/recordplayviewdefine.h" line="31"/>
        <source>Record</source>
        <translation>Registro</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayview.cpp" line="32"/>
        <source>Playback</source>
        <translation>Playback</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayview.cpp" line="33"/>
        <source>Delete</source>
        <translation>Excluir</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="25"/>
        <source>Play</source>
        <translation>Reproduzir</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="26"/>
        <source>Previous</source>
        <translation>Anterior</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="27"/>
        <source>Next</source>
        <translation>Próximo</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="28"/>
        <source>Volume</source>
        <translation>Volume</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="32"/>
        <source>Continue</source>
        <translation>Continuar</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="33"/>
        <source>Pause</source>
        <translation>Pausar</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="34"/>
        <source>Stop</source>
        <translation>Pare</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="37"/>
        <source>Record Duration</source>
        <translation>Tempo gravação</translation>
    </message>
</context>
<context>
    <name>RecordView</name>
    <message>
        <location filename="view/recordplay/RecordView.cpp" line="356"/>
        <source>Fail to start recording, the disk is occupied.</source>
        <translation>Falha ao iniciar a gravação. O disco está ocupado.</translation>
    </message>
    <message>
        <location filename="view/recordplay/RecordView.cpp" line="517"/>
        <source>Unrecorded!</source>
        <translation>Falha na gravação!</translation>
    </message>
</context>
<context>
    <name>RoutineSettingOtherView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/routinesettingotherview.cpp" line="62"/>
        <source>Others</source>
        <translation>Outros</translation>
    </message>
</context>
<context>
    <name>RoutineSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/routinesettingview.cpp" line="108"/>
        <location filename="view/systemsetview/systemsetwidget/routinesettingview.cpp" line="594"/>
        <source>General</source>
        <translation>Geral</translation>
    </message>
</context>
<context>
    <name>SettingView</name>
    <message>
        <location filename="view/systemsetview/settingview.cpp" line="32"/>
        <source>General</source>
        <translation>Geral</translation>
    </message>
    <message>
        <location filename="view/systemsetview/settingview.cpp" line="33"/>
        <source>Measure</source>
        <translation>Medida</translation>
    </message>
    <message>
        <location filename="view/systemsetview/settingview.cpp" line="34"/>
        <source>Network</source>
        <translation>Rede</translation>
    </message>
    <message>
        <location filename="view/systemsetview/settingview.cpp" line="35"/>
        <source>Update</source>
        <translation>atualização</translation>
    </message>
</context>
<context>
    <name>ShortKeySettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/shortkeysettingview.cpp" line="428"/>
        <source>Export VPN log files success.</source>
        <translation>O arquivo de log da VPN foi exportado com sucesso.</translation>
    </message>
</context>
<context>
    <name>SoftwareInfoView</name>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="84"/>
        <source>Downloading, please wait ...</source>
        <translation>Fazendo o download, aguarde ...</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="88"/>
        <source>Downloaded!</source>
        <translation>Download bem-sucedido!</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="100"/>
        <source>Forbid installing the firmware when power off or low power, confirm?</source>
        <translation>Proibir a instalação do firmware quando desligado ou com pouca energia, confirma?</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="108"/>
        <source>Not downloaded yet, can not be installed!</source>
        <translation>Download não concluído, não é possível instalar!</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="243"/>
        <source>Connect fail</source>
        <translation>Falha na conexão</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="253"/>
        <source>Connected</source>
        <translation>Conectado</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="273"/>
        <source>Failed to launch update process</source>
        <translation>Falha ao iniciar o processo de atualização</translation>
    </message>
</context>
<context>
    <name>SoftwareUpdateChart</name>
    <message>
        <location filename="view/update/softwareupdatechart.cpp" line="32"/>
        <source>Latest Version illustration: </source>
        <translation>Versão mais recente Descrição: </translation>
    </message>
    <message>
        <location filename="view/update/softwareupdatechart.cpp" line="37"/>
        <source>Download Progress: </source>
        <translation>Progresso do download: </translation>
    </message>
    <message>
        <location filename="view/update/softwareupdatechart.cpp" line="45"/>
        <source>Connection Status: </source>
        <translation>Status da conexão: </translation>
    </message>
    <message>
        <location filename="view/update/softwareupdatechart.cpp" line="46"/>
        <source>Current Version: </source>
        <translation>Versão Atual: </translation>
    </message>
    <message>
        <location filename="view/update/softwareupdatechart.cpp" line="47"/>
        <source>Latest Version: </source>
        <translation>Última versão: </translation>
    </message>
</context>
<context>
    <name>SplashScreen</name>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="119"/>
        <source>Calibration Date: </source>
        <translation>Data de calibração: </translation>
    </message>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="133"/>
        <source>Next Calibration Date: </source>
        <translation>Próxima Calibration Data: </translation>
    </message>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="159"/>
        <source>The system is initializing, self-checking ...</source>
        <translation>O sistema está inicializando, auto-verificação …</translation>
    </message>
</context>
<context>
    <name>StatusBar</name>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="632"/>
        <source>Low battery, please exit to the desktop and charge the device.</source>
        <translation>A bateria está muito baixa, saia para a área de trabalho e carregue o dispositivo.</translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="647"/>
        <source>Low storage, please clean.</source>
        <translation>O espaço de armazenamento disponível é muito baixo, por favor, limpar o espaço de armazenamento.</translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="661"/>
        <source>Low battery, automatic shutdown 1min later, please exit to the desktop.</source>
        <translation>A bateria é muito baixo, ele será desligado automaticamente em 1 minuto, por favor saída para o desktop.</translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="683"/>
        <source>Temperature &lt; %1℃</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="686"/>
        <source>Humidity &gt; %1%RH</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="689"/>
        <source>Temperature &lt; %1℃, Humidity &gt; %2%RH</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="692"/>
        <source>%1, please pay attention to safety.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SystemSetView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/systemsetview.cpp" line="37"/>
        <source>System Settings</source>
        <translation>Configurações do sistema</translation>
    </message>
</context>
<context>
    <name>SystemViewConfig</name>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="27"/>
        <source>Cloud Settings</source>
        <translation>Configurações nuvem</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="28"/>
        <source>VPN Settings</source>
        <translation>Configurações de VPN</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="29"/>
        <source>Terminal Settings</source>
        <translation>Configurações do terminal</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="30"/>
        <source>WiFi Settings</source>
        <translation>Configurações de Wi-Fi</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="31"/>
        <source>Bluetooth Settings</source>
        <translation>Configurações de Bluetooth</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="32"/>
        <source>Upgrade</source>
        <translation>atualização</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="33"/>
        <location filename="view/systemsetview/SystemViewConfig.h" line="66"/>
        <source>Remote Upgrade</source>
        <translation>Atualização remota</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="34"/>
        <source>Storage Card</source>
        <translation>Cartão de memória</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="35"/>
        <source>Brightness</source>
        <translation>Brilho</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="36"/>
        <source>Volume</source>
        <translation>Volume</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="37"/>
        <source>Language</source>
        <translation>Língua</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="38"/>
        <source>Hotspot</source>
        <translation>Hotspot</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="39"/>
        <source>Grid Frequency</source>
        <translation>Grid Frequency</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="40"/>
        <source>Mobile Network Settings</source>
        <translation>Configurações de redes móveis</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="41"/>
        <source>Mobile Network</source>
        <translation>Redes móveis</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="42"/>
        <source>4G</source>
        <translation>4G</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="43"/>
        <source>Auto Shutdown Time</source>
        <translation>Tempo de desligamento automático</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="44"/>
        <source>Default</source>
        <translation>Restaurar padrão</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="45"/>
        <source>Upgrade Firmware</source>
        <translation>Atualização de firmware</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="46"/>
        <source>System Info</source>
        <translation>Cerca de</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="47"/>
        <source>Others</source>
        <translation>Outros</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="48"/>
        <source>Date</source>
        <translation>Data</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="49"/>
        <source>Time</source>
        <translation>Tempo</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="50"/>
        <source>Date &amp; Time</source>
        <translation>Data &amp; Hora</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="51"/>
        <source>Timezone</source>
        <translation>Fuso horário</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="55"/>
        <source>On</source>
        <translation>Ligar</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="56"/>
        <source>Off</source>
        <translation>Fechar</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="61"/>
        <source>Self-Check</source>
        <translation>Auto-Check</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="62"/>
        <source>Format Storage</source>
        <translation>Format Storage</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="63"/>
        <source>File Comment Box</source>
        <translation>Caixa de comentários do arquivo</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="65"/>
        <source>Remote Settings</source>
        <translation>Configurações remotas</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="68"/>
        <source>Model: </source>
        <translation>Modelo: </translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="69"/>
        <source>Serial No: </source>
        <translation>Número de série:</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="70"/>
        <source>Version: </source>
        <translation>Versão: </translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="71"/>
        <source>Model1: </source>
        <translation>Modelo 1: </translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="72"/>
        <source>Version Info</source>
        <translation>Informação da versão</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="73"/>
        <source>Calibration Date: </source>
        <translation>Data de calibração: </translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="74"/>
        <source>Next Calibration Date: </source>
        <translation>Próxima Calibration Data: </translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="75"/>
        <source>Activation Date: </source>
        <translation>Data de ativação:</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="76"/>
        <source>No Time Limit</source>
        <translation>Sem limite de tempo</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="77"/>
        <source>UHF</source>
        <translation>UHF</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="78"/>
        <source>HFCT</source>
        <translation>HFCT</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="79"/>
        <source>Sync.</source>
        <translation>Sync.</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="80"/>
        <source>Model2: </source>
        <translation>Modelo 2: </translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="82"/>
        <source>Software Interface</source>
        <translation>Interface de software</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="83"/>
        <source>Mass Storage</source>
        <translation>Disco removível</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="84"/>
        <source>USB Settings</source>
        <translation>Configurações USB</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="86"/>
        <source>12Hr</source>
        <translation>12Hr</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="87"/>
        <source>24Hr</source>
        <translation>24Hr</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="89"/>
        <source>Date Format</source>
        <translation>Formato de data</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="90"/>
        <source>Time Format</source>
        <translation>Formato da hora</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="91"/>
        <source>DST</source>
        <translation>DST</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="92"/>
        <source>GMT</source>
        <translation>GMT</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="93"/>
        <source>Y/M/D</source>
        <translation>Ano/Mês/Dia</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="94"/>
        <source>M/D/Y</source>
        <translation>Mês/Dia/Ano</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="95"/>
        <source>D/M/Y</source>
        <translation>Dia/Mês/Ano</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="97"/>
        <source>PRPS BG Color</source>
        <translation>PRPS BG Color</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="98"/>
        <source>Gray</source>
        <translation>cinza</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="99"/>
        <source>White</source>
        <translation>Branco</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="100"/>
        <source>Temperature Unit</source>
        <translation>Unidade de temperatura</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="104"/>
        <source>Debug Settings</source>
        <translation>Debug Setting</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="105"/>
        <source>Network Test</source>
        <translation>Teste de rede</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="106"/>
        <source>Detection Mode</source>
        <translation>Modo de Detecção</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="107"/>
        <source>Infrared Settings</source>
        <translation>Configurações de infravermelho</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="108"/>
        <source>Custom Access Mode</source>
        <translation>Modo de acesso personalizado</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="109"/>
        <source>Auto Shutdown Switch</source>
        <translation>Interruptor de desligamento automático</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="110"/>
        <source>Security Verify Switch</source>
        <translation>Interruptor de certificação de segurança</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="111"/>
        <source>Calibration Write Switch</source>
        <translation>Calibration Write Switch</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="112"/>
        <source>Calibration Interval Period</source>
        <translation>Intervalo de calibração</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="113"/>
        <source>PRPS Sample Interval</source>
        <translation>PRPS Amostra Intervalo</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="114"/>
        <source>Initialized Calibration</source>
        <translation>Inicializar calibração</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="115"/>
        <source>Export VPN Log</source>
        <translation>Exportar log da VPN</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="116"/>
        <source>LCD Reboot Switch</source>
        <translation>LCD Reboot Switch</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="117"/>
        <source>months</source>
        <translation>meses</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="118"/>
        <source>Auto Sync</source>
        <translation>Sincronização automática</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="120"/>
        <location filename="view/systemsetview/SystemViewConfig.h" line="147"/>
        <source>Auto</source>
        <translation>Auto</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="121"/>
        <source>RFID</source>
        <translation>RFID</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="125"/>
        <source>Bluetooth</source>
        <translation>Bluetooth</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="126"/>
        <source>AE Record Time</source>
        <translation>Tempo de Gravação AE</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="128"/>
        <source>Soft Keyboard Mode</source>
        <translation>Modo de teclado suave</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="129"/>
        <source>Full</source>
        <translation>Teclado completo</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="130"/>
        <source>Sudoku</source>
        <translation>Sudoku</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="132"/>
        <source>Connect</source>
        <translation>Conectado</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="133"/>
        <source>Disconnect</source>
        <translation>Desconectar</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="135"/>
        <source>Low Battery Limit</source>
        <translation>Limite de Bateria Fraca</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="138"/>
        <source>Real-time Diagnosis</source>
        <translation>Diagnóstico em tempo real</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="139"/>
        <source>PRPS Color</source>
        <translation>PRPS Color</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="140"/>
        <source>Rainbow</source>
        <translation>arco Iris</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="141"/>
        <source>Blaze</source>
        <translation>Chama</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="143"/>
        <source>Security Verify Mode</source>
        <translation>Segurança Verifique Modo</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="144"/>
        <source>None</source>
        <translation>Nenhum</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="145"/>
        <source>Query</source>
        <translation>Consulta</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="146"/>
        <source>Peer</source>
        <translation>Peer</translation>
    </message>
</context>
<context>
    <name>TEVPanelView</name>
    <message>
        <location filename="view/tev/tevview.cpp" line="145"/>
        <source>TEV Calibration</source>
        <translation>Calibração TEV</translation>
    </message>
</context>
<context>
    <name>TEVPulseChart</name>
    <message>
        <location filename="view/tev/tevpulsechart.cpp" line="56"/>
        <source>Pulse Count: </source>
        <translation>Contagem de pulsos: </translation>
    </message>
    <message>
        <location filename="view/tev/tevpulsechart.cpp" line="63"/>
        <source>Pulses/Cycle: </source>
        <translation>Pulsos / Ciclo: </translation>
    </message>
    <message>
        <location filename="view/tev/tevpulsechart.cpp" line="68"/>
        <source>Severity: </source>
        <translation>Severidade: </translation>
    </message>
</context>
<context>
    <name>TEVR3Calibrate</name>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="24"/>
        <source>Channel: </source>
        <translation>Canal: </translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="25"/>
        <source>Calibration pos 1 AMP (mV): </source>
        <translation>Posição de calibração 1 AMP (mV): </translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="26"/>
        <source>Calibration pos 2 AMP (mV): </source>
        <translation>Posição de calibração 2 AMP (mV): </translation>
    </message>
</context>
<context>
    <name>TEVView</name>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="25"/>
        <source>Mode</source>
        <translation>Modo</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="26"/>
        <source>Single</source>
        <translation>Solteiro</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="27"/>
        <source>Continuous</source>
        <translation>Contínuo</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="28"/>
        <source>Single Sample</source>
        <translation>Amostra única</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="29"/>
        <source>Screen Shot</source>
        <translation>Screenshots</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="30"/>
        <source>Test</source>
        <translation>Teste</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="31"/>
        <source>More...</source>
        <translation>Mais...</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="32"/>
        <source>Warning</source>
        <translation>Aviso</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="33"/>
        <source>High Risk</source>
        <translation>Alto risco</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="34"/>
        <source>Save</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="35"/>
        <source>Load Data</source>
        <translation>Carregar dados</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="36"/>
        <source>Delete Data</source>
        <translation>Excluir dados</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="37"/>
        <source>Default</source>
        <translation>Restaurar padrão</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="39"/>
        <source>Pulses Accumulation Period</source>
        <translation>Período de Acumulação de Pulsos</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="40"/>
        <source>Add</source>
        <translation>Adicionar</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="41"/>
        <source>Power Sync</source>
        <translation>Power Sync</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="42"/>
        <source>On</source>
        <translation>Ligar</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="43"/>
        <source>Off</source>
        <translation>Fechar</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="44"/>
        <source>Sample</source>
        <translation>Amostra</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="45"/>
        <source>Phase Shift</source>
        <translation>Mudança de fase</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="49"/>
        <source>Record</source>
        <translation>Registro</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="50"/>
        <source>Record Time</source>
        <translation>Tempo recorde</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="51"/>
        <source>Playback</source>
        <translation>Playback</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="52"/>
        <source>Delete Record</source>
        <translation>Excluir dados de gravação</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="53"/>
        <source>Accumulate</source>
        <translation>Acumular</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="54"/>
        <source>Sync Mode</source>
        <translation>Modo de sincronização</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="55"/>
        <source>Save RFID</source>
        <translation>Salvar RFID</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="56"/>
        <source>Start</source>
        <translation>Começar</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="57"/>
        <source>Stop</source>
        <translation>Pare</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="60"/>
        <source>View Sig Amp</source>
        <translation>View Sig Amp</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="61"/>
        <source>View Sig Pulse</source>
        <translation>View Sig Pulse</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="62"/>
        <source>View PRPD&amp;PRPS</source>
        <translation>View PRPD&amp;PRPS</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="63"/>
        <source>Amplitude</source>
        <translation>Amplitude</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="64"/>
        <source>Pulse</source>
        <translation>Pulso</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="65"/>
        <source>PRPD&amp;PRPS</source>
        <translation>PRPD&amp;PRPS</translation>
    </message>
</context>
<context>
    <name>TaskModeConfig</name>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="26"/>
        <source>Connect App</source>
        <translation>Connect App</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="27"/>
        <source>Discon</source>
        <translation>Desconectar</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="28"/>
        <source>Submit</source>
        <translation>Enviar</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="29"/>
        <source>Delete</source>
        <translation>Excluir</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="30"/>
        <source>Open</source>
        <translation>Abrir</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="31"/>
        <source>Refresh</source>
        <translation>Refrescar</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="32"/>
        <source>Compress</source>
        <translation>Comprimir</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="33"/>
        <source>Switch Mode</source>
        <translation>Modo de comutação</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="35"/>
        <source>Circuit</source>
        <translation>Circuito</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="36"/>
        <source>Connector</source>
        <translation>Conector</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="38"/>
        <source>Load Current</source>
        <translation>Load Current</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="39"/>
        <source>Grounding Current</source>
        <translation>Grounding Current</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="40"/>
        <source>Infrared</source>
        <translation>Infrared</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="41"/>
        <source>PD</source>
        <translation>PD</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="43"/>
        <source>Save</source>
        <translation type="unfinished">Salvar</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="45"/>
        <source>Phase N</source>
        <translation>Fase N</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="46"/>
        <source>Phase A</source>
        <translation>Fase A</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="47"/>
        <source>Phase B</source>
        <translation>Fase B</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="48"/>
        <source>Phase C</source>
        <translation>Fase C</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="51"/>
        <source>AE</source>
        <translation>AE</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="52"/>
        <source>UHF</source>
        <translation>UHF</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="53"/>
        <source>HFCT</source>
        <translation>HFCT</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="54"/>
        <source>BKGD Detect</source>
        <translation>BKGD Detect</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="55"/>
        <source>AE BKGD</source>
        <translation>AE BKGD</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="56"/>
        <source>UHF BKGD</source>
        <translation>UHF BKGD</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="57"/>
        <source>HFCT BKGD</source>
        <translation>HFCT BKGD</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="59"/>
        <source>Untest</source>
        <translation>Não testado</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="60"/>
        <source>Tested</source>
        <translation>Testado</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="62"/>
        <source>Auto</source>
        <translation>Auto</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="63"/>
        <source>Manual</source>
        <translation>Manual</translation>
    </message>
</context>
<context>
    <name>UHFSpectrumChart</name>
    <message>
        <location filename="view/uhf/uhfintervalview/UHFSpectrumChart.cpp" line="24"/>
        <source>Upper Limit: </source>
        <translation>Limite superior: </translation>
    </message>
    <message>
        <location filename="view/uhf/uhfintervalview/UHFSpectrumChart.cpp" line="25"/>
        <source>Lower limit: </source>
        <translation>Limite inferior: </translation>
    </message>
    <message>
        <location filename="view/uhf/uhfintervalview/UHFSpectrumChart.cpp" line="26"/>
        <source>Range: </source>
        <translation>Alcance: </translation>
    </message>
    <message>
        <location filename="view/uhf/uhfintervalview/UHFSpectrumChart.cpp" line="27"/>
        <source>Max: </source>
        <translation>Máximo: </translation>
    </message>
</context>
<context>
    <name>UHFView</name>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="28"/>
        <source>Mode</source>
        <translation>Modo</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="29"/>
        <source>Single Sample</source>
        <translation>Amostra única</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="30"/>
        <source>Single</source>
        <translation>Solteiro</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="31"/>
        <source>Continuous</source>
        <translation>Contínuo</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="32"/>
        <source>Warning</source>
        <translation>Aviso</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="33"/>
        <source>High Risk</source>
        <translation>Alto risco</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="36"/>
        <source>BW</source>
        <translation>BW</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="37"/>
        <source>Accumulate</source>
        <translation>Acumular</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="38"/>
        <source>Accumulative Time</source>
        <translation>Duração Acumulativa</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="39"/>
        <location filename="view/uhf/UHFViewConfig.h" line="60"/>
        <source>Gain</source>
        <translation>Ganho</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="40"/>
        <source>Default</source>
        <translation>Restaurar padrão</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="41"/>
        <source>High</source>
        <translation>Alto</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="42"/>
        <source>Low</source>
        <translation>Baixo</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="43"/>
        <source>All</source>
        <translation>Tudo</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="44"/>
        <source>High Pass</source>
        <translation>High Pass</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="45"/>
        <source>Low Pass</source>
        <translation>Low Pass</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="46"/>
        <source>All Pass</source>
        <translation>All Pass</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="47"/>
        <source>Power</source>
        <translation>Poder</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="48"/>
        <source>Light</source>
        <translation>Luz</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="49"/>
        <source>Sync Mode</source>
        <translation>Modo de sincronização</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="50"/>
        <source>Vertical Scale</source>
        <translation>Escala vertical</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="51"/>
        <location filename="view/uhf/UHFViewConfig.h" line="67"/>
        <source>More...</source>
        <translation>Mais...</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="52"/>
        <source>Save Data</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="53"/>
        <source>Save</source>
        <translation>Salvar</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="54"/>
        <source>Save RFID</source>
        <translation>Salvar RFID</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="55"/>
        <source>Delete Data</source>
        <translation>Excluir dados</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="56"/>
        <source>Load Data</source>
        <translation>Carregar dados</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="57"/>
        <source>Phase Shift</source>
        <translation>Mudança de fase</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="58"/>
        <source>Sample</source>
        <translation>Amostra</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="59"/>
        <source>Diagnostic</source>
        <translation>Diagnóstico</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="61"/>
        <source>On</source>
        <translation>Ligar</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="62"/>
        <location filename="view/uhf/UHFViewConfig.h" line="90"/>
        <source>Off</source>
        <translation>Fechar</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="63"/>
        <source>Start</source>
        <translation>Começar</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="64"/>
        <source>Stop</source>
        <translation>Pare</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="65"/>
        <source>Pause</source>
        <translation>Pausar</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="66"/>
        <source>Add</source>
        <translation>Adicionar</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="68"/>
        <source>Threshold</source>
        <translation>Limiar</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="69"/>
        <source>Noise Reduction</source>
        <translation>Redução de Ruído</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="70"/>
        <source>Altas Type</source>
        <translation>Tipo Atlas</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="73"/>
        <source>Record</source>
        <translation>Registro</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="74"/>
        <source>Record Time</source>
        <translation>Tempo recorde</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="75"/>
        <source>Playback</source>
        <translation>Playback</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="76"/>
        <source>Delete Record</source>
        <translation>Excluir dados de gravação</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="78"/>
        <source>View Sig Amp</source>
        <translation>View Sig Amp</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="79"/>
        <source>View Period</source>
        <translation>View Period</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="80"/>
        <source>View PRPD&amp;PRPS</source>
        <translation>View PRPD&amp;PRPS</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="81"/>
        <source>Amplitude</source>
        <translation>Amplitude</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="82"/>
        <source>Period</source>
        <translation>Period</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="83"/>
        <source>PRPD&amp;PRPS</source>
        <translation>PRPD&amp;PRPS</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="84"/>
        <source>PRPS</source>
        <translation>PRPS</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="85"/>
        <source>PRPD</source>
        <translation>PRPD</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="89"/>
        <source>Auto</source>
        <translation>Auto</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="91"/>
        <source>Level 1</source>
        <translation>Nível 1</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="92"/>
        <source>Level 2</source>
        <translation>Nível 2</translation>
    </message>
</context>
<context>
    <name>UhfPRPSPDAView</name>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1476"/>
        <source>Connection error.</source>
        <translation>Erro de conexão.</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1481"/>
        <source>Server connection abnormal.</source>
        <translation>Exceção de conexão do servidor.</translation>
    </message>
</context>
<context>
    <name>UhfPRPSView</name>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1286"/>
        <source>Connection error.</source>
        <translation>Erro de conexão.</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1291"/>
        <source>Server connection abnormal.</source>
        <translation>Exceção de conexão do servidor.</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1913"/>
        <source>No data!</source>
        <translation>Sem dados!</translation>
    </message>
</context>
<context>
    <name>UpdateFirmware</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/updatefirmware/updatefirmware.cpp" line="17"/>
        <source>Updating</source>
        <translation>Atualizando</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/updatefirmware/updatefirmware.cpp" line="45"/>
        <source>Please exit after the firmware update is completed.</source>
        <translation>Por favor, saia depois de completar a atualização do firmware.</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/updatefirmware/updatefirmware.cpp" line="76"/>
        <source>Update successfully!</source>
        <translation>Atualização bem-sucedida!</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/updatefirmware/updatefirmware.cpp" line="80"/>
        <source>Update failed!</source>
        <translation>Falha na atualização!</translation>
    </message>
</context>
<context>
    <name>UpdateView</name>
    <message>
        <location filename="view/update/updateviewconfig.h" line="12"/>
        <source>Refresh</source>
        <translation>Refrescar</translation>
    </message>
    <message>
        <location filename="view/update/updateviewconfig.h" line="13"/>
        <source>Download</source>
        <translation>Baixar</translation>
    </message>
    <message>
        <location filename="view/update/updateviewconfig.h" line="14"/>
        <source>Install</source>
        <translation>Instalar</translation>
    </message>
</context>
<context>
    <name>View</name>
    <message>
        <location filename="view/View.h" line="30"/>
        <source>Substation Name: </source>
        <translation>Nome da Subestação: </translation>
    </message>
    <message>
        <location filename="view/View.h" line="31"/>
        <source>Asset: </source>
        <translation>Equipamento: </translation>
    </message>
    <message>
        <location filename="view/View.h" line="32"/>
        <source>Test Point: </source>
        <translation>Ponto de teste: </translation>
    </message>
</context>
<context>
    <name>VpnSetting</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnsetting.cpp" line="83"/>
        <source>Connecting VPN server...</source>
        <translation>Conectando servidor VPN ...</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnsetting.cpp" line="130"/>
        <source>Please input VPN server domain.</source>
        <translation>Por favor, digite o nome de domínio do servidor VPN.</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnsetting.cpp" line="244"/>
        <source>Connect to VPN server success.</source>
        <translation>Conectar ao servidor VPN é bem sucedida.</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnsetting.cpp" line="248"/>
        <source>Connect to VPN server failure.</source>
        <translation>Falha ao conectar ao servidor VPN.</translation>
    </message>
</context>
<context>
    <name>WaveTestView</name>
    <message>
        <location filename="view/ca/wave/wavetestview.cpp" line="906"/>
        <source>Set parameter fail, please check connection state.</source>
        <translation>Falha ao definir parâmetros, por favor, verifique o status da conexão.</translation>
    </message>
    <message>
        <location filename="view/ca/wave/wavetestview.cpp" line="1103"/>
        <source>Start sample fail, please check connection state.</source>
        <translation>Falha ao iniciar a amostragem, por favor, verifique o status da conexão.</translation>
    </message>
</context>
<context>
    <name>WaveView</name>
    <message>
        <location filename="view/ca/wave/waveview.cpp" line="1069"/>
        <source>Set parameter fail, please check connection state.</source>
        <translation>Falha ao definir parâmetros, por favor, verifique o status da conexão.</translation>
    </message>
    <message>
        <location filename="view/ca/wave/waveview.cpp" line="1834"/>
        <source>Start sample fail, please check connection state.</source>
        <translation>Falha ao iniciar a amostragem, por favor, verifique o status da conexão.</translation>
    </message>
</context>
<context>
    <name>Weekday</name>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="33"/>
        <source>Monday</source>
        <translation>Segunda-feira</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="34"/>
        <source>Tuesday</source>
        <translation>terça</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="35"/>
        <source>Wednesday</source>
        <translation>Quarta-feira</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="36"/>
        <source>Thursday</source>
        <translation>Quinta-feira</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="37"/>
        <source>Friday</source>
        <translation>Sexta-feira</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="38"/>
        <source>Saturday</source>
        <translation>sábado</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="39"/>
        <source>Sunday</source>
        <translation>domingo</translation>
    </message>
</context>
<context>
    <name>WifiSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="128"/>
        <source>WiFi</source>
        <translation>WiFi</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="133"/>
        <source>Select a network ...</source>
        <translation>Selecione uma rede ...</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="800"/>
        <source>Authenticating ...</source>
        <translation>Autenticando ...</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="854"/>
        <source>Unable to connect the hotspot!</source>
        <translation>Não é possível conectar hotspot!</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="1033"/>
        <source>Access hotspot successfully!</source>
        <translation>Conecte o hotspot com sucesso!</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="1046"/>
        <source>Get IP info failed!</source>
        <translation>Falha ao obter informações de IP!</translation>
    </message>
</context>
<context>
    <name>inputWifiPassWD</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/inputwifipasswd.cpp" line="138"/>
        <source>Password</source>
        <translation>Senha</translation>
    </message>
</context>
<context>
    <name>rfidinfowidget</name>
    <message>
        <location filename="view/rfid/RFIDInfoWidget.cpp" line="34"/>
        <source>Substation Name (max length: 128): </source>
        <translation>Substation Name (max length: 128): </translation>
    </message>
    <message>
        <location filename="view/rfid/RFIDInfoWidget.cpp" line="35"/>
        <source>Asset Name (max length: 128): </source>
        <translation>Asset Name (max length: 128): </translation>
    </message>
    <message>
        <location filename="view/rfid/RFIDInfoWidget.cpp" line="36"/>
        <source>Asset Number (max length: 32): </source>
        <translation>Asset Number (max length: 32): </translation>
    </message>
    <message>
        <location filename="view/rfid/RFIDInfoWidget.cpp" line="37"/>
        <source>Test Number (length: 12): </source>
        <translation>Número do teste (comprimento: 12): </translation>
    </message>
    <message>
        <location filename="view/rfid/RFIDInfoWidget.cpp" line="38"/>
        <source>Voltage Level (kV): </source>
        <translation>Nível de tensão (kV): </translation>
    </message>
</context>
<context>
    <name>usbSetting</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="89"/>
        <source>Storage</source>
        <translation>Cartão de memória</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="102"/>
        <source>Format Storage</source>
        <translation>Format Storage</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="112"/>
        <source>Shared Storage: </source>
        <translation>Capacidade compartilhada: </translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="127"/>
        <source>Used Storage: </source>
        <translation>Usado Capacidade: </translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="141"/>
        <source>Available Storage: </source>
        <translation>Capacidade disponível: </translation>
    </message>
</context>
</TS>
