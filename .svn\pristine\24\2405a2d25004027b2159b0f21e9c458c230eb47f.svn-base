﻿#ifndef TEVAMPDISTRIBUTENETTESTBGVIEW_H
#define TEVAMPDISTRIBUTENETTESTBGVIEW_H

#include "tev/tevdefine.h"
#include "tev/TevPulseService.h"
#include "config/ConfigManager.h"
#include "widgets/histogram/HistogramChart.h"
#include "customaccesstask/taskdefine.h"
#include "widgets/sampleChartView/SampleChartView.h"
#include "distributenetaccess/distributenetaccess_def.h"

class CommonItemListView;
class DataMap;
class DataFile;

namespace DataSpecificationNS {
    class DataSpecification;
}
class TEVAmpDistributeNetTestBGView: public SampleChartView
{
     Q_OBJECT
public:
    /*************************************************
    函数名： TEVAmpDistributeNetTestBGView(const QString &strTitle, QWidget *parent = 0)
    输入参数： strTitle：标题
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    explicit TEVAmpDistributeNetTestBGView(const QString &strTitle, QWidget *parent = 0);

    /*************************************************
    函数名： ~TEVAmpDistributeNetTestBGView()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~TEVAmpDistributeNetTestBGView();

    /*************************************************
    功能： 设置测点信息
    *************************************************************/
    void setTestInfo(const QString& qstrTaskId, const DistributeNetAccessNS::TestpointInfo& stTestpointInfo, const DistributeNetAccessNS::RealAssetInfo& stRealAssetInfo);

signals:
    void sigAutoSwitch();

protected:
    /*************************************************
    函数名： onSKeyPressed()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 响应S键事件
    *************************************************************/
    void onSKeyPressed();

    /*************************************************
    功能： 键盘事件
    输入参数:
        event -- 事件
    *************************************************************/
    void keyPressEvent( QKeyEvent* event );

protected slots:
    /*************************************************
    函数名： onButtonValueChanged(int id, int iValue)
    输入参数： id：按钮ID
              iValue：按钮值
    输出参数： NULL
    返回值： NULL
    功能： 响应按钮值变化事件
    *************************************************************/
    void onButtonValueChanged(int id, int iValue);

    /*************************************************
    函数名： onCommandButtonPressed(int id)
    输入参数： id：按钮ID
    输出参数： NULL
    返回值： NULL
    功能： 响应命令按钮按下事件
    *************************************************************/
    void onCommandButtonPressed(int id);

private:
    /*************************************************
    函数名： initDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 初始化数据
    *************************************************************/
    void initDatas();

    /*************************************************
    函数名： setChartDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置图谱数据
    *************************************************************/
    void setChartDatas();

    /*************************************************
    函数名： setWorksets()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置工作参数
    *************************************************************/
    void setWorksets();

    /*************************************************
    函数名： saveConfig()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 保存配置信息
    *************************************************************/
    void saveConfig();

    /*************************************************
    函数名： resetAlarmScope()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 重置报警值范围
    *************************************************************/
    void resetAlarmScope();

    /*************************************************
    函数名： setButtonDatas()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 设置按钮数据
    *************************************************************/
    void setButtonDatas();

    /*************************************************
    功能： 保存数据
    输入参数：
            stationName -- 变电站名
            deviceName -- 设备名
    返回：
            保存后的文件名
    *************************************************************/
    bool saveTestData();

    /************************************************
     * 函数名   : loadData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 载入数据
     ************************************************/
    void loadData();

    /************************************************
     * 函数名   : deleteData
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 删除数据
     ************************************************/
    void deleteData();

    /************************************************
     * 函数名   : createChart
     * 输入参数 : parent---父控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建图谱
     ************************************************/
    ChartWidget* createChart(QWidget *parent);

    /************************************************
     * 函数名   : loadData
     * 输入参数 : qsFile---数据文件存放路径
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     :读取数据文件里的测试数据
     ************************************************/
    bool loadTestDataFile(const QString &qsFile, qint8 &cValue,qint8 &cMax );

    /*************************************************
    功能： 开始采集
    *************************************************************/
    void startSample();

    /*************************************************
    功能： 停止采集
    *************************************************************/
    void stopSample();

    /*************************************************
     * 功能：保存数据
     * ***********************************************/
    void saveData();

private slots:
    void deleteSelectedFile(qint32 uiItem);
    bool loadSelectedFile(qint32 uiItem);
    void onDataRead(TEV::PulseData data, MultiServiceNS::USERID userId);
    void onItemActivated( qint32 );
private:
    //已测次数
    enum OperationType
    {
        OPERATION_INVALID,
        OPERATION_DELETE,
        OPERATION_LOAD
    };
    OperationType m_eOperationType;
    QLabel *m_pBayNameLabel;

    QLabel *m_pLoadFileName;//回放文件名

    UINT8 m_ucPulseLen;
    QString m_strSavedPath;
    CommonItemListView *m_pCommonItemListView;
    ControlButton *m_pSampleBtn;
    ControlButton *m_pSaveBtn; //保存按钮
    bool m_bPlayBacked;

    ConfigInstance *m_pConfig;
    HistogramChart *m_pChart;
    Module::SampleMode m_eSampleMode;
    UINT8 m_ucYellowAlert;
    UINT8 m_ucRedAlert;
    INT8 m_cAmpValue;
    UINT32 m_uiPulseNum;  //脉冲计数
    UINT32 m_uiPerPulseNum;  //单周期脉冲数
    UINT32 m_uiPDSeverity;  //放电严重程度

    QString m_strLatestFileName;

    QString m_qsStation;
    QString m_qsDevice;

    QString m_qstrTaskId;
    DistributeNetAccessNS::RealAssetInfo m_stRealAssetInfo; // 设备信息
    DistributeNetAccessNS::TestpointInfo m_stTestpointInfo;//测点信息
};

#endif // TEVAMPDISTRIBUTENETTESTBGVIEW_H
