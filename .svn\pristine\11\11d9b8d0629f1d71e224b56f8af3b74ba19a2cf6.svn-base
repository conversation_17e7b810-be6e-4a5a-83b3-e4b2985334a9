﻿#include "logincontroller.h"
#include "functionconfigmanager/funccfgsrvmanager.h"

LoginController::LoginController(QObject *parent)
    : HttpRequestHandler(parent)
{

}

LoginController::~LoginController()
{

}

/*****************************************
 * 功能：处理http请求函数，并作出响应
 * 输入参数：
 *      request：请求
 * 输出参数：
 *      response：响应
 * *****************************************/
void LoginController::service(HttpRequest &request, HttpResponse &response)
{
    QByteArray qbaReqData = request.getBody();
    int iSpIndex = qbaReqData.indexOf('&');
    QString qstrUserId = qbaReqData.mid(0, iSpIndex);
    QString qstrPwd = qbaReqData.mid(iSpIndex + 1);

    QString qstrToken = "";
    if(!FuncCfgSrvManager::instance()->checkLoginInfo(qstrUserId, qstrPwd, qstrToken))
    {
        response.setStatus(400, "Bad Request");
    }
    else
    {
        response.setStatus(200, "200 OK");
    }

    response.write(qstrToken.toLatin1(), true);       //解决content-length没有的问题
    return;
}


