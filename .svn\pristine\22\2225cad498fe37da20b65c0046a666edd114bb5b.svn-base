#include "currentphasetypeview.h"
#include "window/Window.h"
#include "mobileaccessservice.h"
#include "customaccesstask/taskmanager.h"
#include "customaccessUi/taskmodeview/taskmodeconfig.h"
#include "customaccessUi/customaccessui_func.h"
#include "currentdetection/currentdetectionservice.h"
#include "dataspecification/currentamplitude/currentampspectrum.h"
#include "dataspecification/dataspecification.h"
#include "log/log.h"
#include "global_log.h"


#define TIMER_ID_INIT -1
#define TIMER_OUT 1000
#define AUTO_SWITCH_VIEW_DELAY 1000

const int g_iInvalidUser = -1;

enum TaskButton
{
    BUTTON_SAVE = 0, // 保存
};

// 控制按钮定义
const ButtonInfo::Info g_SaveBtn = {BUTTON_SAVE, {ButtonInfo::COMMAND, TaskModeConfig::TEXT_SAVE, NULL, "", NULL}};

/*************************************************
功能： 构造函数
输入参数:
    parent:父控件指针
*************************************************************/
CurrentPhaseTypeView::CurrentPhaseTypeView(QWidget *parent)
    : PDAListView(QObject::trUtf8("Phase Type"), parent),
      m_iUserId(g_iInvalidUser),
      m_iAutoPhaseTimerId(TIMER_ID_INIT),
      m_bSampling(false),
      m_eSignalState(Module::SIGNAL_STATE_INVALID)
{
    setFixedSize(Window::WIDTH, Window::HEIGHT);

    // 初始化列表
    initChart();

    initBtnBarInfo();

    connect(CurrentDetectionService::instance(), SIGNAL(sigData(CurrentDetection::CurrentDetectionData, MultiServiceNS::USERID)),
            this, SLOT(onDataRead(CurrentDetection::CurrentDetectionData, MultiServiceNS::USERID)));
    connect(CurrentDetectionService::instance(), SIGNAL(sigSignalChanged(Module::SignalState)), this, SLOT(onSignalChanged(Module::SignalState)));

    MultiServiceNS::SampleUser user;
    user.eSampleType = MultiServiceNS::SPECTRUM_CURRENTDETECTION;
    m_iUserId = CurrentDetectionService::instance()->addUser(user);
    CurrentDetectionService::instance()->start();
}

/*************************************************
功能： 析构
*************************************************************/
CurrentPhaseTypeView::~CurrentPhaseTypeView( )
{
    stopSample();
    if (m_iUserId != g_iInvalidUser)
    {
        CurrentDetectionService::instance()->deleteUser(m_iUserId);
    }
    CurrentDetectionService::instance()->stop();

    if(m_pBtnInfo)
    {
        delete [] m_pBtnInfo;
        m_pBtnInfo = NULL;
    }
}

/********************************************
 * 功能：初始化列表
 * ******************************************/
void CurrentPhaseTypeView::initChart()
{
    m_pChart->deleteAllItem();
    QList<PDAListChart::ListItemInfo> itemInfos;

    TaskModeViewNS::AssetInfo* pAssetInfo = TaskManager::instance()->getJSONCurAssetInfo();
    QString qstrCurrentAssetId = TaskManager::instance()->getJSONAssetId();
    bool bTicked = false;
    bool bTested = false;
    if(pAssetInfo->qvtPhaseTypes.isEmpty())
    {
        if(TaskModeViewNS::TYPE_CURRENT == TaskManager::instance()->getCurTestType())
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_N);
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_N), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_N));
        }

        bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_A);
        itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_A), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_A));

        bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_B);
        itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_B), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_B));

        bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_C);
        itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_C), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_C));
    }
    else
    {
        if(pAssetInfo->qvtPhaseTypes.contains(static_cast<int>(TaskModeViewNS::PHASE_N)))
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_N);
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_N), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_N));
        }
        else
        {
            log_debug("asset (%s) not contains phase N.", pAssetInfo->qstrId.toLatin1().data());
        }

        if(pAssetInfo->qvtPhaseTypes.contains(static_cast<int>(TaskModeViewNS::PHASE_A)))
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_A);
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_A), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_A));
        }
        else
        {
            log_debug("asset (%s) not contains phase A.", pAssetInfo->qstrId.toLatin1().data());
        }

        if(pAssetInfo->qvtPhaseTypes.contains(static_cast<int>(TaskModeViewNS::PHASE_B)))
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_B);
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_B), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_B));
        }
        else
        {
            log_debug("asset (%s) not contains phase B.", pAssetInfo->qstrId.toLatin1().data());
        }

        if(pAssetInfo->qvtPhaseTypes.contains(static_cast<int>(TaskModeViewNS::PHASE_C)))
        {
            bTested = TaskManager::instance()->isJSONPhaseFinished(qstrCurrentAssetId, TaskModeViewNS::PHASE_C);
            itemInfos << PDAListChart::ListItemInfo(TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_C), -1, bTested, bTicked, PDAListItem::CONTENT_MODE, QString::number(TaskModeViewNS::PHASE_C));
        }
        else
        {
            log_debug("asset (%s) not contains phase C.", pAssetInfo->qstrId.toLatin1().data());
        }
    }

    // 设置电流值
    if(TaskModeViewNS::TYPE_CURRENT == TaskManager::instance()->getCurTestType()
            || TaskModeViewNS::TYPE_LOAD_CURRENT == TaskManager::instance()->getCurTestType())
    {
        for (int i = 0; i < itemInfos.size(); ++i)
        {
            // 未测不显示
            if (0 == itemInfos[i].m_iTotalCount)
            {
                continue;
            }

            TaskModeViewNS::PhaseType ePhaseType = static_cast<TaskModeViewNS::PhaseType>(itemInfos[i].m_strId.toInt());
            if (TaskModeViewNS::PHASE_BGN == ePhaseType)
            {
                continue;
            }

            m_beforeTestedIndexs.insert(i);
            float fGroundCurrentVal = TaskManager::instance()->getCurJSONTaskGroundCurrentVal(ePhaseType);
            itemInfos[i].m_strContent = (QString("%1A").arg(static_cast<double>(fGroundCurrentVal), 0, 'f', 1));
        }
    }

    m_pChart->addItems(itemInfos);
    m_pChart->setCurrentItemSelected(0);
}

/*****************************************
 * 功能：初始化按钮栏信息
 * **********************************************/
void CurrentPhaseTypeView::initBtnBarInfo()
{
    m_pBtnInfo = NULL;
    m_qui8BtnCnt = 0;

    QVector<ButtonInfo::Info> qvtBtnInfos;
    qvtBtnInfos.push_back(g_SaveBtn);

    m_qui8BtnCnt = static_cast<quint8>(qvtBtnInfos.size());
    m_pBtnInfo = new ButtonInfo::Info[m_qui8BtnCnt];
    for (int i = 0; i < m_qui8BtnCnt; ++i)
    {
        m_pBtnInfo[i] = qvtBtnInfos[i];
    }

    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar(TaskModeConfig::CONTEXT, m_pBtnInfo, m_qui8BtnCnt);
}

/*****************************************
 * 功能：关闭自动跳转定时器
 * **********************************************/
void CurrentPhaseTypeView::killAutoPhaseTimer()
{
    if(TIMER_ID_INIT != m_iAutoPhaseTimerId)
    {
        killTimer(m_iAutoPhaseTimerId);
        m_iAutoPhaseTimerId = TIMER_ID_INIT;
    }
}

/*****************************************
 * 功能：自动跳转检测任务
 * **********************************************/
bool CurrentPhaseTypeView::setAutoPhaseTest()
{
    int iMode = MobileAccessService::instance()->getSwitchMode();
    if(SystemSet::ACCESS_AUTO_SWITCH == static_cast<SystemSet::AccessSwitchMode>(iMode))
    {
        if(TIMER_ID_INIT == m_iAutoPhaseTimerId)
        {
            m_iAutoPhaseTimerId = startTimer(AUTO_SWITCH_VIEW_DELAY);
        }
        return true;
    }
    else
    {
        return false;
    }
}

/*************************************************************
 * 功能：保存数据
 * ************************************************************/
void CurrentPhaseTypeView::saveData()
{
    // 如果没有测试数据
    if (m_beforeTestedIndexs.isEmpty() && m_currentTestedIndexs.isEmpty())
    {
        MsgBox::information("", QObject::trUtf8("Empty data, save failure!"));
        return;
    }

    TaskModeViewNS::AssetInfo* pAssetInfo = TaskManager::instance()->getJSONCurAssetInfo();
    APP_CHECK_RETURN(pAssetInfo);

    bool bSaveFailed = false;
    const QList<PDAListChart::ListItemInfo>& allItems = m_pChart->allItems();
    for (int i = 0; i < allItems.size(); ++i)
    {
        PDAListChart::ListItemInfo stListItemInfo = allItems[i];
        // 如果没有数据或者当前未测就不保存
        if (stListItemInfo.m_strId.isEmpty() || stListItemInfo.m_strContent.isEmpty() || !m_currentTestedIndexs.contains(i))
        {
            continue;
        }

        TaskModeViewNS::PhaseType ePhaseType = static_cast<TaskModeViewNS::PhaseType>(stListItemInfo.m_strId.toInt());
        if (TaskModeViewNS::PHASE_BGN == ePhaseType)
        {
            continue;
        }

        // 设置头部信息
        DataSpecificationNS::SpectrumHead stSpectrumHead;
        stSpectrumHead.eSpectrumTypeCode = DataSpecificationNS::SPECTRUM_CODE_CURRENT_AMPLITUDE;
        stSpectrumHead.eSpectrumCharacter = DataSpecificationNS::PROPERTY_TEST;
        stSpectrumHead.eStorageDataType = DataSpecificationNS::DATA_TYPE_FLOAT;
        stSpectrumHead.qstrEquipmentName = pAssetInfo->qstrName;
        stSpectrumHead.qstrEquipmentCode = pAssetInfo->qstrId;
        stSpectrumHead.qstrTestPointName = pAssetInfo->qstrTestPosition;
        stSpectrumHead.qstrSpectrumGenerationTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");

        DataSpecificationNS::CurrentAmpExtInformation stCurrentAmpExtInformation;
        stCurrentAmpExtInformation.ucCurrentDataCount = 1;

        DataSpecificationNS::CurrentAmpData stCurrentAmpData;
        stCurrentAmpExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_A;

        QString qstrDataName = CurrentDetection::gs_qstrCurrentAmplitude;
        QString qstrCurrent = stListItemInfo.m_strContent;
        qstrCurrent.remove('A'); // 去掉单位
        float fCurrent = qstrCurrent.toFloat();
        stCurrentAmpData.currentDatas.insert(qstrDataName, fCurrent);

        // 先设置当前测试项
        switch (ePhaseType)
        {
        case TaskModeViewNS::PHASE_N:
        {
            TaskManager::instance()->setJSONTaskPhaseType(ePhaseType, TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_N));
            break;
        }
        case TaskModeViewNS::PHASE_A:
        {
            TaskManager::instance()->setJSONTaskPhaseType(ePhaseType, TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_A));
            break;
        }
        case TaskModeViewNS::PHASE_B:
        {
            TaskManager::instance()->setJSONTaskPhaseType(ePhaseType, TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_B));
            break;
        }
        case TaskModeViewNS::PHASE_C:
        {
            TaskManager::instance()->setJSONTaskPhaseType(ePhaseType, TASKMODE_VIEW_CONFIG_TRANSLATE(TaskModeConfig::TEXT_PHASE_C));
            break;
        }
        default:
            break;
        }

        // 创建图谱文件对象
        DataSpecificationNS::DataSpecification stDataSpecification;
        CustomAccessView::CustomAccessUIFunc::setDataFileHead(&stDataSpecification);
        stDataSpecification.setDataSpecificationVersion(DataSpecificationNS::V_4_1_1_0);
        stDataSpecification.setVersion("4.1.1.0");
        stDataSpecification.setCompanyCode("");
        stDataSpecification.setInternalVersion("");

        // 创建图谱对象
        DataSpecificationNS::CurrentAmpSpectrum* pCurrentAmpSpectrum = new DataSpecificationNS::CurrentAmpSpectrum;

        pCurrentAmpSpectrum->setSpectrumHead(stSpectrumHead);

        // 设置ext信息
        pCurrentAmpSpectrum->setCurrentAmpExtInformation(stCurrentAmpExtInformation);

        // 设置数据内容
        pCurrentAmpSpectrum->setCurrentAmpData(stCurrentAmpData);

        stDataSpecification.addSpectrum(pCurrentAmpSpectrum);

        // 保存任务文件
        QString qstrSavedFilePath = CustomAccessView::CustomAccessUIFunc::getBinaryDataFileSavePath(TaskManager::instance()->getCurJSONTaskTestDataSavePath());
        if(stDataSpecification.saveAsBinary(qstrSavedFilePath))
        {
            TaskModeViewNS::GroundCurrentDataInfo stGroundCurrentDataInfo;
            stGroundCurrentDataInfo.ePhaseType = ePhaseType;
            stGroundCurrentDataInfo.dCurrentVal = fCurrent;
            stGroundCurrentDataInfo.qstrDataFileName = qstrSavedFilePath;
            TaskManager::instance()->setCurJSONTaskGroundCurrentVal(stGroundCurrentDataInfo);

            // 保存成功更新已测状态
            // 设置当前已测状态
            m_pChart->setCurItemTested(stListItemInfo.m_strId, true);
            m_beforeTestedIndexs.insert(i);
            m_currentTestedIndexs.remove(i);

            // 设置任务测试完成
            setTestFinished();
        }
        else
        {
            bSaveFailed = true;
        }
    }

    // 如果有文件保存失败就提示失败，并且不自动跳转
    if (bSaveFailed)
    {
        MsgBox::information("", QObject::trUtf8("Save failure!"));
    }
    else
    {
        // 打开自动跳转定时器 间隔1s
        setAutoPhaseTest();
    }
}

/*************************************************************
 * 功能：开始采集
 * ************************************************************/
void CurrentPhaseTypeView::startSample()
{
    if (!m_bSampling)
    {
        m_bSampling = true;
        // 开始采样
        CurrentDetectionService::instance()->startSample(m_iUserId);
    }
}

/*************************************************************
 * 功能：停止采集
 * ************************************************************/
void CurrentPhaseTypeView::stopSample()
{
    if (m_bSampling)
    {
        m_bSampling = false;
        // 开始采样
        CurrentDetectionService::instance()->stopSample(m_iUserId);
    }
}

/*************************************************
功能： 槽，响应******
*************************************************************/
void CurrentPhaseTypeView::setTestFinished()
{
    TaskManager::instance()->setJSONPhaseFinished(TaskManager::instance()->getJSONAssetId(), TaskManager::instance()->getCurPhaseType());
    TaskManager::instance()->setJSONAssetTested(TaskManager::instance()->getJSONAssetId());
    TaskManager::instance()->setJSONSubTaskTested(TaskManager::instance()->getJSONSubTaskId());
    TaskManager::instance()->setJSONTaskTested(TaskManager::instance()->getJSONMainTaskId());
}

/*************************************************
功能： 槽，响应读取电流结束
*************************************************************/
void CurrentPhaseTypeView::onDataRead(CurrentDetection::CurrentDetectionData stCurrentDetectionData, MultiServiceNS::USERID userId)
{
    if (m_bSampling && m_iUserId != g_iInvalidUser && m_iUserId == userId)
    {
        PDAListChart::ListItemInfo stListItemInfo = m_pChart->currentItemInfo();
        if (!stListItemInfo.m_strId.isEmpty())
        {
            stListItemInfo.m_strContent = QString("%1A").arg(static_cast<double>(stCurrentDetectionData.fGroundingCurrentValue), 0, 'f', 1);

            m_pChart->setItemInfo(stListItemInfo, m_pChart->itemIndexSelected());

            // 记录已测试项
            if (!m_currentTestedIndexs.contains(m_pChart->itemIndexSelected()))
            {
                m_currentTestedIndexs.insert(m_pChart->itemIndexSelected());
            }
        }
    }
}

/*************************************************
功能： 响应信号状态改变
输入参数： eSignalState：信号状态
*************************************************************/
void CurrentPhaseTypeView::onSignalChanged(Module::SignalState eSignalState)
{
    if (eSignalState != m_eSignalState)
    {
        m_eSignalState = eSignalState;

        if (Module::SIGNAL_STATE_NONE == m_eSignalState)
        {
            MsgBox::warning("", trUtf8("Read current failed!"));
        }
    }
}

/*************************************************
功能： 定时器处理函数
输入参数：
        e -- 定时事件
*************************************************************/
void CurrentPhaseTypeView::timerEvent(QTimerEvent* pEvent)
{
    if(pEvent->timerId() == m_iAutoPhaseTimerId)
    {
        killAutoPhaseTimer();

        TaskModeViewNS::PhaseType ePhaseType = TaskManager::instance()->getUnfinishedPhaseInfo();
        if(TaskModeViewNS::PHASE_INVALID == ePhaseType)
        {
            TaskManager::instance()->setJSONSubInfoFinished(TaskManager::instance()->getJSONAssetId());

            if(MsgBox::OK == MsgBox::delayQuestion("", QObject::trUtf8("All items have been tested and will automatically jump.")))
            {
                close();
            }
            else
            {
                // 开始采样
                startSample();
            }
        }
        else
        {
            QString qstrId = QString::number(ePhaseType);
            int iTestIndex = m_pChart->indexOfItem(qstrId);
            m_pChart->setCurrentItemSelected(iTestIndex);

            // 开始采样
            startSample();
        }
    }
}

/****************************
功能： 处理showEvent事件
输入参数:
       event -- showEvent
*****************************/
void CurrentPhaseTypeView::showEvent(QShowEvent *event)
{
    PDAListView::showEvent(event);

    // 打开自动跳转定时器 间隔1s
    if(!setAutoPhaseTest())
    {
        // 如果是手动跳转，需要打开采样
        startSample();
    }
}

/*************************************************
功能： 响应S键事件
*************************************************************/
void CurrentPhaseTypeView::onSKeyPressed()
{
    // 如果正好在自动跳转就关闭自动跳转
    killAutoPhaseTimer();

    // 先停止采集
    stopSample();

    // 保存数据文件
    saveData();

    // 开始采集
    startSample();
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void CurrentPhaseTypeView::onCommandButtonPressed(int id)
{
    switch (id)
    {
    case BUTTON_SAVE:
        onSKeyPressed();
        break;
    default:
        break;
    }
}

/*************************************************
功能： 处理条目被点击后的事件
输入参数：
        id -- 条目序号
*************************************************************/
void CurrentPhaseTypeView::onItemClicked(int id)
{
    // 手动点击关掉自动跳转
    killAutoPhaseTimer();

    // 如果此时刚开启界面，打断了自动跳转，导致没有开始采集，需要再次开启采集
    if (!m_bSampling)
    {
        startSample();
    }
}


