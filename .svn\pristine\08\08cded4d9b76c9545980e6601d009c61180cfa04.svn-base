/*
* Copyright (c) 2016.12，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：ConfigFileInfo.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2016年12月08日
* 摘要：该文件主要定义配置文件的组织
* 当前版本：1.0
*/
#ifndef CONFIGFILEINFO_H
#define CONFIGFILEINFO_H

#include "ae/AEConfig.h"
#include "tev/tevconfig.h"
#include "uhf/UHFConfig.h"
#include "hfct/HFCTConfig.h"
#include "ca/CAConfig.h"
#include "appconfig.h"
#include "functionsconfig.h"
#include "currentdetection/currentdetectionconfig.h"
#include "customaccessconfig.h"

#define CONFIG_FILE_CODE 0 //配置文件码

//模块配置
Config::GroupInfo* GROUP_INFO[] =
{
    &AE::CONFIG,
    &TEV::CONFIG,
	&UHF::CONFIG,
    &HFCT::CONFIG,
    &CAConfig::CONFIG,
    &APPConfig::CONFIG,
    &FunctionsConfig::CONFIG,
    &CurrentDetection::CONFIG,
    &CustomAccessConfig::CONFIG
};

#ifdef Q_PROCESSOR_ARM
const QString g_strConfigPath = "/opt/bin-bash/config/config.xml";
#else
const QString g_strConfigPath = "config/config.xml";
#endif

//循环文件配置
Config::ConfigInfo CONFIG_INFO =
{
    CONFIG_FILE_CODE, g_strConfigPath, "Station", GROUP_INFO, sizeof( GROUP_INFO )/sizeof(Config::GroupInfo*)
};

#endif // CONFIGFILEINFO_H

