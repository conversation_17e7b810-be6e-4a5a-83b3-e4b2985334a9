﻿#include "guideinfraredcabeltestview.h"
#include <QtConcurrentRun>
#include "dataSave/DataFileInfos.h"
#include "deleteDataView/RotateDeleteDataView.h"
#include "infrared/guideinfrareddatasave.h"
#include "infrared/rotatewidget.h"
#include "guideinfrared/guideinfraredplaybackview.h"
#include "config/ConfigManager.h"
#include "systemsetting/SystemSet.h"
#include "systemsetting/systemsetservice.h"
#include "statusbar/StatusBar.h"
#include "appconfig.h"
#include "log/log.h"
#include "fileoper/fileoperutil.h"
#include "infrared/guide/guideclientmanager.h"
#include "infrared/infraredplaybackview.h"
#include "dataspecification/dataspecification.h"
#include "dataspecification/infrared/infraredspectrum.h"
#include "customaccessUi/customaccessui_func.h"
#include "customaccesstask/taskmanager.h"
#include "playbackView/RotatePlayBackViewEx.h"
#include "widgets/diagretselect/diagretselectdialog.h"
#include "mobileaccessservice.h"
#include "Window.h"

namespace
{
    // 更多
    const ControlButtonInfo g_MoreButtonInfo =
    {
        BUTTON_TYPE_BASIC, Infrared::TEXT_MORE, NULL, "", NULL, 0, 0, 0, 0
    };

    // 退出
    const ControlButtonInfo g_ExitButtonInfo =
    {
        BUTTON_TYPE_BASIC, Infrared::TEXT_EXIT, NULL, NULL, NULL, 0, 0, 0, 1
    };
    // 返回
    const ControlButtonInfo g_ReturnButtonInfo =
    {
        BUTTON_TYPE_BASIC, Infrared::TEXT_RETURN, NULL, NULL, NULL, 0, 0, 0, 1
    };

    QString pfnSaveDataFun(GuideInfraredViewBase* pMain, DataSpecificationNS::InfraredSpectrum* pInfraredSpectrum, bool bSaveJpeg, QString qstrPath)
    {
        Q_UNUSED(pMain);
        Q_UNUSED(bSaveJpeg);

        DataSpecificationNS::DataSpecification stDataSpecification;//当前数据文件
        stDataSpecification.setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());

        stDataSpecification.addSpectrum(pInfraredSpectrum);

        QString qstrSavePath = CustomAccessView::CustomAccessUIFunc::getBinaryDataFileSavePath(qstrPath);

        stDataSpecification.saveAsBinary(qstrSavePath);

        return qstrSavePath;
    }

    // 采集页面按钮菜单
    const InfraredButtonInfo s_InfraredButtonInfos[] =
    {
    #ifdef DISPLAY_MODE
        { GuideInfrared::BUTTON_DISPLAY_MODE,           &g_DisplayModeButtonInfo, },
    #endif
        { GuideInfrared::BUTTON_ANALYSE_SHAPE, &g_InfraredAnalyseShapeBasic, },
        { GuideInfrared::BUTTON_SAVA_DATA, &g_SaveDataButtonInfo, },
        { GuideInfrared::BUTTON_MORE, &g_MoreButtonInfo, },
        { GuideInfrared::BUTTON_EXIT, &g_ExitButtonInfo, },
    };

    // 更多菜单
    const InfraredButtonInfo s_InfraredButtonInfosMore[] =
    {
        { GuideInfrared::BUTTON_COLOR_TYPE,       &g_InfraredColorButtonInfoBasic, },
        { GuideInfrared::BUTTON_SET_PARAM,        &g_InfraredSetParamButtonInfo, },
        { GuideInfrared::BUTTON_LOAD_DATA,        &g_LoadDataButtonInfo, },
        { GuideInfrared::BUTTON_DELETE_ALL_SHAPE, &g_InfraredDelAllButtonInfo, },
        //{ GuideInfrared::BUTTON_DELETE_DATA,      &g_DeleteDataButtonInfo, },
        { GuideInfrared::BUTTON_AUTO_FOCUS,      &g_AutoFocusButtonInfo, },
        { GuideInfrared::BUTTON_CLOSE_FOCUS_FINE_TUNING,      &g_CloseFocusFineTuningButtonInfo, },
        { GuideInfrared::BUTTON_FAR_FOCUS_FINE_TUNING,      &g_FarFocusFineTuningButtonInfo, },
//        { BUTTON_ELECTRONIC_ZOOM,      &g_ElectronicZoomButtonInfo, },
    #ifdef DISPLAY_MODE
        { GuideInfrared::BUTTON_LASER_CONTROL,      &g_LaserControlButtonInfo, },
        { GuideInfrared::BUTTON_LED_FILL_LIGHT,      &g_LEDFillLightButtonInfo, },
        { GuideInfrared::BUTTON_AUXILIARY_LIGHTING,      &g_AuxiliaryLightingButtonInfo, },
    #endif
        { GuideInfrared::BUTTON_RETURN,        &g_ReturnButtonInfo, }
    };

    const int INVALID_USER = -1;
}
/*************************************************
函数名： GuideInfraredCabelTestView(QWidget *parent)
输入参数： parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
GuideInfraredCabelTestView::GuideInfraredCabelTestView(QWidget *parent)
    : GuideInfraredViewBase(parent),
      m_bIsReadData(false),
      m_bIsReadDataBeforePlayback(false),
      m_bInitFlag(true),
      m_bSave(false),
      m_bLedFillLight(false),
      m_bSaveImage(true),
      m_bSwitchNext(false),
      m_eDiagRet(CustomAccessTaskNS::DiagNormal)
{
    qDebug() << "GuideInfraredCabelTestView::GuideInfraredView1: " << QThread::currentThreadId();
    InfraredControlButtonBar* pButtonBar = createButtonBar(s_InfraredButtonInfos, sizeof(s_InfraredButtonInfos) / sizeof(InfraredButtonInfo));
    setButtonBar(pButtonBar);

    // 创建更多菜单
    createMoreConfigBar();

#ifdef DISPLAY_MODE
    pButtonBar->buttons(GuideInfrared::BUTTON_LASER_CONTROL)->setPopupMode(PopupWidget::SWITCH_MODE);
    pButtonBar->buttons(GuideInfrared::BUTTON_LASER_CONTROL)->setValue(Guide::SW_OFF);
    pButtonBar->buttons(GuideInfrared::BUTTON_LED_FILL_LIGHT)->setPopupMode(PopupWidget::SWITCH_MODE);
    pButtonBar->buttons(GuideInfrared::BUTTON_LED_FILL_LIGHT)->setValue(m_bLedFillLight);
    pButtonBar->buttons(GuideInfrared::BUTTON_AUXILIARY_LIGHTING)->setPopupMode(PopupWidget::SWITCH_MODE);
    pButtonBar->buttons(GuideInfrared::BUTTON_AUXILIARY_LIGHTING)->setValue(Guide::SW_OFF);
    pButtonBar->buttons(GuideInfrared::BUTTON_DISPLAY_MODE)->setPopupMode(PopupWidget::SWITCH_MODE);
    pButtonBar->buttons(GuideInfrared::BUTTON_DISPLAY_MODE)->setValue(m_eGuideInfraredDisplayMode);
#endif

    disableButtons();
    // 载入数据可用
//    m_pButtonBar->buttons(GuideInfrared::BUTTON_LOAD_DATA)->setEnabled(true);
//    m_pButtonBar->buttons(GuideInfrared::BUTTON_STATUS)->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_RECOVER));
//    m_pButtonBar->buttons(GuideInfrared::BUTTON_DELETE_DATA)->setEnabled(true);

    m_pGuideInfraredImagingView->setDisplayMode(m_eGuideInfraredDisplayMode);
    //connect(m_pGuideInfraredImagingView, SIGNAL(sigCreateAnalysisShape()), this, SLOT(onStopSample()));
    connect(m_pGuideInfraredImagingView, SIGNAL(sigCreateAnalysisShape()), this, SLOT(onCreateShape()));
    connect(m_pGuideInfraredImagingView, SIGNAL(sigRestartSample()), this, SLOT(onRestartSample()));

    connect(GuideClientManager::instance(), SIGNAL(sigRgbData(QSharedPointer<Guide::GuideRGBDataInfo>)), this, SLOT(onRgbData(QSharedPointer<Guide::GuideRGBDataInfo>)));
    connect(GuideClientManager::instance(), SIGNAL(sigY16Data(QSharedPointer<Guide::GuideY16DataInfo>)), this, SLOT(onY16DataInfo(QSharedPointer<Guide::GuideY16DataInfo>)));

    connect(GuideClientManager::instance(), SIGNAL(sigConnectInfraredDevFinished(bool)), this, SLOT(onInfraredInitResult(bool)));
    connect(GuideClientManager::instance(), SIGNAL(sigConnectInfraredDevState(bool)), this, SLOT(onInfraredDevState(bool)));

    connect(InfraredService::instance(), SIGNAL(sigInfraredTypeChanged(Infrared::InfraredType)), this, SLOT(onInfraredTypeChanged(Infrared::InfraredType))); 

    connect(&m_saveDataFutureWatcher, SIGNAL(finished()), this, SLOT(onSaveDataFinished()));
    qDebug() << "GuideInfraredCabelTestView::GuideInfraredView2";

    m_qstrTestDataSavePath = TaskManager::instance()->getCurJSONTaskTestDataSavePath();
}

/*************************************************
函数名： ~GuideInfraredCabelTestView()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
GuideInfraredCabelTestView::~GuideInfraredCabelTestView()
{
    m_bIsReadData = false;

    disconnect(GuideClientManager::instance(), 0, this, 0);

#ifdef DISPLAY_MODE
    // 退出界面将激光功能关闭
    int iButtonValue = m_pButtonBar->buttons(GuideInfrared::BUTTON_LASER_CONTROL)->value();
    if (Guide::SW_ON == iButtonValue)
    {
        GuideClientManager::instance()->ctrlLaser(Guide::SW_OFF);
    }

    // 退出界面将辅助照明关闭
    iButtonValue = m_pButtonBar->buttons(GuideInfrared::BUTTON_AUXILIARY_LIGHTING)->value();
    if (Guide::SW_ON == iButtonValue)
    {
        GuideClientManager::instance()->ctrlAuxiliaryLighting(Guide::SW_OFF);
    }
#endif

    GuideClientManager::instance()->disconnectInfraredDev();
}

/*************************************************
函数名： onPlaybackFinished()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应回放结束信号
*************************************************************/
void GuideInfraredCabelTestView::onPlaybackFinished()
{
    if (m_bIsReadDataBeforePlayback)
    {
        resume();
    }
}

/*************************************************
函数名： onData(Infrared::InfraredData stData)
输入参数： data：红外数据
输出参数： NULL
返回值： NULL
功能： 响应红外数据信号
*************************************************************/
void GuideInfraredCabelTestView::onRgbData(QSharedPointer<Guide::GuideRGBDataInfo> qspInfraredData)
{
    if (m_bSave || !m_bIsReadData)
    {
        return;
    }

    if (m_pGuideInfraredImagingView)
    {
        m_pGuideInfraredImagingView->setInfraredRgbData(qspInfraredData);
    }
}

void GuideInfraredCabelTestView::onY16DataInfo(QSharedPointer<Guide::GuideY16DataInfo> qspY16DataInfo)
{
    if (m_bSave || !m_bIsReadData)
    {
        return;
    }

    if (m_pInfraredMsgBox)
    {
        // 如果断开连接又连上了把对话框关掉
        m_pInfraredMsgBox->accept();
        m_pInfraredMsgBox = NULL;
    }

    if (m_pGuideInfraredImagingView)
    {
        m_pGuideInfraredImagingView->setY16DataInfo(qspY16DataInfo);
    }
}

/*************************************************
函数名： onInfraredInitResult(bool isSuccess)
输入参数： isSuccess：红外初始化结果
输出参数： NULL
返回值： NULL
功能： 响应红外初始化结果信号
*************************************************************/
void GuideInfraredCabelTestView::onInfraredInitResult(bool bSuccess)
{
    logWarning(QString("infrared init result: %1.").arg(bSuccess).toLatin1().data());
    if (m_bInitFlag)
    {
        m_bInitFlag = false;
        emit sigInfraredInitFinished();
        if (bSuccess)
        {
            onInitSuccess();
        }
        else
        {
            onInitFail();
        }
    }
}

/*************************************************************
 * 功能：红外设备状态改变槽函数
 * 输入参数：
 *         bConnectState：连接状态
 * ************************************************************/
void GuideInfraredCabelTestView::onInfraredDevState(bool bConnectState)
{
    //qDebug() << "GuideInfraredCabelTestView::onInfraredDevState: " << QThread::currentThreadId();
    if (!bConnectState)
    {
        showMsgBox(QObject::tr("Device disconnected!"), MsgBox::WARNING);
    }
}

/*************************************************************
 * 功能：红外设备类型改变槽函数
 * ************************************************************/
void GuideInfraredCabelTestView::onInfraredTypeChanged(Infrared::InfraredType eInfraredType)
{
    if (Infrared::GUIDE != eInfraredType)
    {
        showMsgBox(QObject::tr("The connected infrared lens type has changed, please exit and re-enter the page."));
    }
}

/*************************************************
函数名： showEvent(QShowEvent *e)
输入参数： e：显示事件
输出参数： NULL
返回值： NULL
功能： 显示事件处理
*************************************************************/
void GuideInfraredCabelTestView::showEvent(QShowEvent *e)
{
    StatusBar::instance()->hide();
    GuideInfraredViewBase::showEvent(e);

    if (m_pButtonBar)
    {
        m_pButtonBar->activeFirst();
    }

    Guide::GuideDevInfo stGuideDevInfo;
    GuideClientManager::instance()->connectInfraredDev(stGuideDevInfo);

    //显示初始化对话框，通过信号的方式，直接调用有焦点问题
    //正常情况下不显示
    QTimer::singleShot(1000, this, SLOT(showInitDialog()));
}

/*************************************************
函数名： keyPressEvent(QKeyEvent *e)
输入参数： e：按键事件
输出参数： NULL
返回值： NULL
功能： 按键事件处理
*************************************************************/
void GuideInfraredCabelTestView::keyPressEvent(QKeyEvent *e)
{
    if (m_bSave)
    {
        // 正在保存不允许做其他操作
        return;
    }

    if (Qt::Key_Escape == e->key())
    {
        //logInfo("press esc key.");
        //emit sigExitInfrared();
        //onExitInfrared();

        m_bExit = true;
//        if (GuideClientManager::instance()->isWaitForClose())
//        {
//            MsgBox* pMsgBox = new MsgBox(MsgBox::INFORMATION);
//            pMsgBox->setInfo("", QObject::trUtf8("Service exiting, please wait..."), MsgBox::NOBUTTON);
//            QTimer::singleShot(10000, pMsgBox, SLOT(accept()));
//            rotateMsgBox(pMsgBox);
//        }
        close();
    }
    else if (Qt::Key_F1 == e->key())       // S键
    {
        onPressSKey();
    }
    else
    {
        GuideInfraredViewBase::keyPressEvent(e);
    }
}

/*************************************************
功能： 处理窗口关闭事件
*************************************************************/
void GuideInfraredCabelTestView::closeEvent(QCloseEvent* event)
{
    Q_UNUSED(event);
    StatusBar::instance()->show();

    if(m_bSwitchNext)
    {
        emit sigExitTest();
    }

    GuideInfraredViewBase::closeEvent(event);
    qDebug() << "GuideInfraredCabelTestView::closeEvent";
}

/*************************************************
函数名： onButtonPressed(UINT8 ucID)
输入参数： ucID：按钮ID
输出参数： NULL
返回值： NULL
功能： 按钮响应处理
*************************************************************/
void GuideInfraredCabelTestView::onButtonPressed(UINT8 ucID)
{
    if (m_bSave)
    {
        // 正在保存不允许做其他操作
        return;
    }

    if (!m_pButtonBar)
    {
        return;
    }

    if (InfraredControlButton* pButton = m_pButtonBar->buttons(ucID))
    {
        if (!pButton->isEnabled())
        {
            logError("current press button is not enable.");
            return;
        }
    }
    else
    {
        return;
    }

    switch (ucID)
    {
    case GuideInfrared::BUTTON_STATUS:
    {
        readDataCtrl();
    }
        break;
    case GuideInfrared::BUTTON_SAVA_DATA:     // 保存数据
    {
        saveData(true);
    }
        break;
    case GuideInfrared::BUTTON_LOAD_DATA:
    {
        loadData();
    }
        break;
    case GuideInfrared::BUTTON_DELETE_DATA:
    {
        deleteData();
    }
        break;
    case GuideInfrared::BUTTON_AUTO_FOCUS:
    {
        if (!GuideClientManager::instance()->ctrlFocus(Guide::FOCUS_AUTO))
        {
            showMsgBox(QObject::tr("Autofocus failed!"), MsgBox::WARNING);
        }
    }
        break;
    case GuideInfrared::BUTTON_CLOSE_FOCUS_FINE_TUNING:
    {
        if (!GuideClientManager::instance()->ctrlFocus(Guide::FOCUS_NEAR_FINE))
        {
            showMsgBox(QObject::tr("Close focus fine-tuning failed!"), MsgBox::WARNING);
        }
    }
        break;
    case GuideInfrared::BUTTON_FAR_FOCUS_FINE_TUNING:
    {
        if (!GuideClientManager::instance()->ctrlFocus(Guide::FOCUS_FAR_FINE))
        {
            showMsgBox(QObject::tr("Far focus fine-tuning failed!"), MsgBox::WARNING);
        }
    }
        break;
    case GuideInfrared::BUTTON_EXIT:     // 退出
    {
        if(m_bSave)
        {
            logInfo("saving data...");
        }
        else
        {
            close();
        }
    }
        break;
    case GuideInfrared::BUTTON_MORE:
    {
        if(m_pMoreButtonBars && m_pMoreButtonBarView)
        {
            int iHeight = Window::HEIGHT - m_pGuideInfraredImagingView->width();
            m_pMoreButtonBars->resize(iHeight, Window::WIDTH);
            m_pMoreButtonBars->activeFirst();
            m_pMoreButtonBarView->resize(Window::WIDTH, iHeight);
            m_pMoreButtonBarView->move(0, m_pGuideInfraredImagingView->width());
            m_pMoreButtonBarView->setVisible(true);
            m_pMoreButtonBarView->setFocus();
        }
    }
        break;
    case GuideInfrared::BUTTON_RETURN:
    {
        if(m_pMoreButtonBarView)
        {
            m_pMoreButtonBarView->setVisible(false);
        }
    }
        break;
    default:
        GuideInfraredViewBase::onButtonPressed(ucID);
        break;
    }
}

/*************************************************
函数名： loadData()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 载入数据
*************************************************************/
void GuideInfraredCabelTestView::loadData()
{
    m_testFileList = TaskManager::instance()->getCurrentTestFileList();

    if(m_testFileList.size() <= 0)
    {
        MsgBox* pMsgBox = new MsgBox(MsgBox::WARNING);
        pMsgBox->setInfo("", QObject::trUtf8("No file!"));
        rotateMsgBox(pMsgBox);
        return;
    }

    m_bIsReadDataBeforePlayback = m_bIsReadData;
    suspend();

    QStringList qstrTestFileList(m_testFileList);
    for (int i = 0; i < m_testFileList.size(); ++i)
    {
        qstrTestFileList[i] = CustomAccessView::CustomAccessUIFunc::getDisplayBinaryDataFileName(qstrTestFileList[i]);
    }

    QString filePath = TaskManager::instance()->getCurJSONTaskTestDataSavePath() + "/" + CustomAccessView::CustomAccessUIFunc::getBinaryDataFileNamePrefix();

    RotatePlayBackViewEx* pRotatePlayBackViewEx = new RotatePlayBackViewEx(filePath, INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_PLAYBACK), INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_LOAD_DATA), false, false, false);
    InfraredPlaybackView* pPlayBackView = new InfraredPlaybackView(InfraredPlayback::PlaybackInBJ);

    connect(pPlayBackView, SIGNAL(sigPlayNextFile()), pRotatePlayBackViewEx, SLOT(onPlayNextFile()));
    connect(pPlayBackView, SIGNAL(sigPlayLastFile()), pRotatePlayBackViewEx, SLOT(onPlayLastFile()));
    connect(pPlayBackView, SIGNAL(sigExit()), pRotatePlayBackViewEx, SLOT(onExitPlayBack()));
    connect(pPlayBackView, SIGNAL(destroyed(QObject *)), this, SLOT(onPlaybackFinished()));
    connect(pRotatePlayBackViewEx, SIGNAL(sigDeleteCurrentFile(const QString&)), this, SLOT(onDeleteCurrentFile(const QString&)));
    pRotatePlayBackViewEx->addPlayback(qstrTestFileList, pPlayBackView);
    pRotatePlayBackViewEx->show();
}

/*************************************************
函数名： suspend()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 暂停
*************************************************************/
void GuideInfraredCabelTestView::suspend()
{
    if (m_bIsReadData && m_pButtonBar)
    {
        if (m_pGuideInfraredImagingView)
        {
            m_pGuideInfraredImagingView->suspend();
        }

//        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_STATUS))
//        {
//            pButton->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_RECOVER));
//        }

        onStopSample();
        //m_pButtonBar->buttons(GuideInfrared::BUTTON_ANALYSE_SHAPE)->setEnabled(m_bIsReadData);
    }
}

void GuideInfraredCabelTestView::onCreateShape()
{
    if (m_bIsReadData && m_pButtonBar)
    {
//        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_STATUS))
//        {
//            pButton->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_RECOVER));
//        }

        onStopSample();
    }
}

/*************************************************
函数名： resume()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 恢复
*************************************************************/
void GuideInfraredCabelTestView::resume()
{
    m_bIsReadData = true;

    if (m_pGuideInfraredImagingView)
    {
        m_pGuideInfraredImagingView->resume();
    }

//    if (m_pButtonBar)
//    {
//        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_STATUS))
//        {
//            pButton->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_FROZEN));
//        }
//    }
    GuideClientManager::instance()->ctrlLiveVideo(Guide::CTRL_RESUME);
}

/*************************************************
函数名： onStopSample()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 停止采集
*************************************************************/
void GuideInfraredCabelTestView::onStopSample()
{
    m_bIsReadData = false;
    GuideClientManager::instance()->ctrlLiveVideo(Guide::CTRL_FREEZE);
}

/*************************************************
函数名： onRestartSample()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 重新开始采集
*************************************************************/
void GuideInfraredCabelTestView::onRestartSample()
{
    m_bIsReadData = true;

//    if (m_pButtonBar)
//    {
//        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_STATUS))
//        {
//            pButton->setText(INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_FROZEN));
//        }
//    }

    GuideClientManager::instance()->ctrlLiveVideo(Guide::CTRL_RESUME);
}

/*************************************************
函数名： onPressSKey()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 处理S键
*************************************************************/
void GuideInfraredCabelTestView::onPressSKey()
{
    saveData(true);
}

/*************************************************
函数名： readDataCtrl()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 控制红外数据读取
*************************************************************/
void GuideInfraredCabelTestView::readDataCtrl()
{
    if (m_bIsReadData)
    {
        suspend();
    }
    else
    {
        resume();
    }
}

/*************************************************
函数名： saveData()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 保存红外数据
*************************************************************/
void GuideInfraredCabelTestView::saveData(bool bSaveJpeg)
{
    if (NULL == m_pGuideInfraredImagingView)
    {
        return;
    }

    m_bSave = true;
    m_bSaveImage = bSaveJpeg;
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    SystemSet::TemperatureUnitOption eUnit = (SystemSet::TemperatureUnitOption) pConfig->value( APPConfig::KEY_TEMPERATURE_UNIT ).toInt();

    if (m_pGuideInfraredImagingView->isDataEmpty())
    {
        MsgBox *pMsgBox = new MsgBox(MsgBox::INFORMATION);
        pMsgBox->setInfo("", QObject::trUtf8("Empty data, save failure!"));
        rotateMsgBox(pMsgBox);
        m_bSave = false;
        return;
    }

    suspend();

    DiagRetSelectDialog *pDialog = new DiagRetSelectDialog();
    pDialog->setRotated(true);
    connect(pDialog, SIGNAL(sigSelectDiagRet(CustomAccessTaskNS::DiagnoseType)), this, SLOT(onSelectDiagRet(CustomAccessTaskNS::DiagnoseType)));
    rotateDialog(pDialog);

    showWaitingDialog(QObject::trUtf8("Saving data ..."));
    logError(QString("GuideInfraredCabelTestView::saveData start"));

#ifdef DISPLAY_MODE
    Guide::SavePicturesInfo stSavePicturesInfo;
    // 单独红外模式可见光照片需要调用拍照借口获取
    GuideClientManager::instance()->ctrlSavePictures(static_cast<Guide::SwitchValue>(m_bLedFillLight), stSavePicturesInfo);
#endif

    m_stInfraredDataInfo = TaskModeViewNS::InfraredDataInfo();
    logWarning(QString("diag ret: %1.").arg(m_eDiagRet));
    m_stInfraredDataInfo.qstrDiagResult = TaskManager::instance()->getDiagnosisDescription(m_eDiagRet);

    FrameInfo stFrameInfo;
    memset(&stFrameInfo, 0, sizeof(FrameInfo));
    m_pGuideInfraredImagingView->getFrameInfo(stFrameInfo);

    DataSpecificationNS::InfraredExtInformation stInfraredExtInformation;
    stInfraredExtInformation.iTempLatticeWidth = stFrameInfo.size_x;
    stInfraredExtInformation.iTempLatticeHeight = stFrameInfo.size_y;

    int iInfraredTemperatureDataCount = stInfraredExtInformation.iTempLatticeWidth * stInfraredExtInformation.iTempLatticeHeight;
    DataSpecificationNS::InfraredData stInfraredData;
    stInfraredData.qbaInfraredTemperatureData.resize(iInfraredTemperatureDataCount * sizeof(float));

    Params stParams;
    memset(&stParams, 0, sizeof(Params));


    // 数码相机模式不保存红外数据
    if (GuideInfrared::DISPLAY_DIGITAL_CAMERA != m_eGuideInfraredDisplayMode)
    {
        if (stFrameInfo.frame_sz > 0)
        {
#ifdef SAVE_GUIDE_DATA
            QByteArray qbaMeasureParamData;
            m_pGuideInfraredImagingView->getData((INT16*)stGuideInfraredDataInfo.pucRawData,
                                                 qbaMeasureParamData);

            stGuideInfraredDataInfo.iMeasureParamDataLength = qbaMeasureParamData.size();
            if (stGuideInfraredDataInfo.iMeasureParamDataLength > 0)
            {
                stGuideInfraredDataInfo.pMeasureParamData = new char[stGuideInfraredDataInfo.iMeasureParamDataLength];
                memcpy(stGuideInfraredDataInfo.pMeasureParamData, qbaMeasureParamData.data(), stGuideInfraredDataInfo.iMeasureParamDataLength);
            }

//                stGuideInfraredDataInfo.iTempDataLen = stFrameInfo.frame_sz;
//                stGuideInfraredDataInfo.pTemperatureData = new float[stFrameInfo.frame_sz];
//                m_pGuideInfraredImagingView->getTemperatureData(stGuideInfraredDataInfo.pTemperatureData);
#else
            m_pGuideInfraredImagingView->getTemperatureData(stInfraredData.qbaInfraredTemperatureData, stParams);
#endif

        }
    }

    Guide::TemperatureParameterInfo stTemperatureParameterInfo;
    m_pGuideInfraredImagingView->getTemperatureParameterInfo(stTemperatureParameterInfo);

    stInfraredExtInformation.fEmissivity = stTemperatureParameterInfo.dEmissivity;
    stInfraredExtInformation.fMeasureDistance = stTemperatureParameterInfo.dDistance;
    stInfraredExtInformation.cRelativeHumidity = stTemperatureParameterInfo.dHumidity;

    // 当前温度信息(max,min,avg,...)
    TemperatureInfo tmpInfo = m_pGuideInfraredImagingView->temperatureInfomation();
    if(eUnit == SystemSet::TEMPERATURE_UNIT_CENTIGRADE)//摄氏度
    {
        stInfraredExtInformation.eTemperatureUnit = DataSpecificationNS::TEMPERATURE_UNIT_CELSIUS;
        stInfraredExtInformation.fAtomTemperature = stParams.stObj.dblAtmTemp;
        stInfraredExtInformation.fReflectedTemperature = stParams.stObj.dblAmbTemp;

        m_stInfraredDataInfo.eUnit = TaskModeViewNS::UNIT_SHESHIDU;
        m_stInfraredDataInfo.dEnvTemperVal = stTemperatureParameterInfo.dBGTemperature;
        m_stInfraredDataInfo.dMinTemperVal = tmpInfo.min;
        m_stInfraredDataInfo.dMaxTemperVal = tmpInfo.max;
    }
    else        //华氏度
    {
        stInfraredExtInformation.eTemperatureUnit = DataSpecificationNS::TEMPERATURE_UNIT_FAHRENHEIT;
        double dValue = Module::Centigrade2Fahrenheit(stParams.stObj.dblAtmTemp);
        stInfraredExtInformation.fAtomTemperature = dValue;

        dValue = Module::Centigrade2Fahrenheit(stParams.stObj.dblAtmTemp);
        stInfraredExtInformation.fReflectedTemperature = dValue;

        m_stInfraredDataInfo.eUnit = TaskModeViewNS::UNIT_HUASHIDU;
        m_stInfraredDataInfo.dEnvTemperVal = Module::Centigrade2Fahrenheit(stTemperatureParameterInfo.dBGTemperature);
        m_stInfraredDataInfo.dMinTemperVal = Module::Centigrade2Fahrenheit(tmpInfo.min);
        m_stInfraredDataInfo.dMaxTemperVal = Module::Centigrade2Fahrenheit(tmpInfo.max);
    }


    DataSpecificationNS::InfraredSpectrum* pInfraredSpectrum = new DataSpecificationNS::InfraredSpectrum;
    pInfraredSpectrum->setSpectrumCharacter(DataSpecificationNS::PROPERTY_TEST);

    pInfraredSpectrum->setInfraredExtInformation(stInfraredExtInformation);
    pInfraredSpectrum->setInfraredData(stInfraredData);

    QFuture<QString> future = QtConcurrent::run(pfnSaveDataFun, this, pInfraredSpectrum, bSaveJpeg, m_qstrTestDataSavePath);
    m_saveDataFutureWatcher.setFuture(future);
}

/*************************************************
函数名： deleteData()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 删除数据
*************************************************************/
void GuideInfraredCabelTestView::deleteData()
{
    QStringList nameFilters;
    nameFilters <<  ("*" + BINARY_DATA_FILE_NAME_SUFFIX);

    QString filePath = m_qstrTestDataSavePath;

    QDir dir(filePath);
    QFileInfoList infoList = dir.entryInfoList(nameFilters);

    if (!infoList.isEmpty())
    {
        RotateDeleteDataView *pView = new RotateDeleteDataView(filePath, INFRARED_CONFIG_TRANSLATE(Infrared::TEXT_DELETE_DATA), nameFilters);
        QStringList lstSuffix;
        lstSuffix << JPG_FILE_NAME_SUFFIX;
        pView->setRelatedSuffix(BINARY_DATA_FILE_NAME_SUFFIX, lstSuffix);
        QWidget *pFocusWidget = this->focusWidget();
        if ( pFocusWidget )
        {
            pFocusWidget->clearFocus();
        }
        pView->setFocus();
        pView->exec();
        if ( pFocusWidget )
        {
            pFocusWidget->setFocus();
        }
    }
    else
    {
        MsgBox *pMsgBox = new MsgBox(MsgBox::WARNING);
        pMsgBox->setInfo("", QObject::trUtf8("No file!"));
        rotateMsgBox(pMsgBox);
    }
}

/*************************************************
函数名： disableButtons()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 禁用部分按钮
*************************************************************/
void GuideInfraredCabelTestView::disableButtons()
{
    if (!m_pButtonBar)
    {
        return;
    }

    QList<InfraredControlButton *> buttons = m_pButtonBar->buttonList();
    for (int i = 0, iSize = buttons.size(); i < iSize; ++i)
    {
        if (buttons[i])
        {
            if (!(buttons[i]->id() == GuideInfrared::BUTTON_LOAD_DATA
                  || buttons[i]->id() == GuideInfrared::BUTTON_EXIT
                  || buttons[i]->id() == GuideInfrared::BUTTON_MORE
                  || buttons[i]->id() == GuideInfrared::BUTTON_RETURN))
            {
                buttons[i]->setEnabled(false);
            }
        }
    }
}

/*************************************************
函数名： enableButtons()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 使能部分按钮
*************************************************************/
void GuideInfraredCabelTestView::enableButtons()
{
    if (!m_pButtonBar)
    {
        return;
    }

    QList<InfraredControlButton *> buttons = m_pButtonBar->buttonList();
    for (int i = 0, iSize = buttons.size(); i < iSize; ++i)
    {
        if (buttons[i])
        {
            if (!(buttons[i]->id() == GuideInfrared::BUTTON_LOAD_DATA
                  || buttons[i]->id() == GuideInfrared::BUTTON_EXIT
                  || buttons[i]->id() == GuideInfrared::BUTTON_MORE
                  || buttons[i]->id() == GuideInfrared::BUTTON_RETURN))
            {
                buttons[i]->setEnabled(true);
            }
        }
    }

    if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_DELETE_ALL_SHAPE))
    {
        pButton->setEnabled(false);
    }
}

/*************************************************************
 * 功能：更新按钮状态
 * ************************************************************/
void GuideInfraredCabelTestView::updateButtonState()
{
    if (NULL == m_pButtonBar)
    {
        return;
    }

    bool bInfraredEnable = m_eGuideInfraredDisplayMode != GuideInfrared::DISPLAY_DIGITAL_CAMERA;

    if (bInfraredEnable)
    {
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_ANALYSE_SHAPE))
        {
            pButton->setEnabled(true);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_SET_PARAM))
        {
            pButton->setEnabled(true);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_CLOSE_FOCUS_FINE_TUNING))
        {
            pButton->setEnabled(true);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_FAR_FOCUS_FINE_TUNING))
        {
            pButton->setEnabled(true);
        }
        //m_pButtonBar->buttons(GuideInfrared::BUTTON_DELETE_ALL_SHAPE)->setEnabled(true);
    }
    else
    {
        // 禁用的时候要删除所有分析图形
        if (m_pGuideInfraredImagingView)
        {
            m_pGuideInfraredImagingView->deleteAll();
        }

        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_ANALYSE_SHAPE))
        {
            pButton->setEnabled(false);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_DELETE_ALL_SHAPE))
        {
            pButton->setEnabled(false);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_SET_PARAM))
        {
            pButton->setEnabled(false);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_CLOSE_FOCUS_FINE_TUNING))
        {
            pButton->setEnabled(false);
        }
        if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_FAR_FOCUS_FINE_TUNING))
        {
            pButton->setEnabled(false);
        }
    }
}

/*************************************************
函数名： showInitDialog()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 显示初始化对话框
*************************************************************/
void GuideInfraredCabelTestView::showInitDialog()
{
    if (m_bInitFlag)
    {
        QString strInfo = QObject::trUtf8("Connecting to infrared device ...");
        MsgBox *pInitMsgBox = new MsgBox(MsgBox::INFORMATION);
        pInitMsgBox->setInfo("", strInfo, MsgBox::OK);

        RotateDialog *pInitingDialog = new RotateDialog(pInitMsgBox);
        pInitingDialog->setWindowModality(Qt::ApplicationModal);
        pInitingDialog->setAttribute(Qt::WA_DeleteOnClose);
        pInitingDialog->setFocusPolicy(Qt::StrongFocus);
        pInitingDialog->show();

        connect(this, SIGNAL(sigInfraredInitFinished()), pInitingDialog, SLOT(accept()));
    }
}

/*************************************************************
 * 功能：保存数据结束
 * ************************************************************/
void GuideInfraredCabelTestView::onSaveDataFinished()
{
    QString qstrFilePath = m_saveDataFutureWatcher.result();
    if (m_bSaveImage && !qstrFilePath.isEmpty())
    {
        m_stInfraredDataInfo.qstrDataFileName = FileOperUtil::getFileName(qstrFilePath);
        int nPos = qstrFilePath.lastIndexOf('.');
        QString qstrCommonFileName = qstrFilePath.left(nPos);
        if (GuideInfrared::DISPLAY_PICTURE_IN_PICTURE == m_eGuideInfraredDisplayMode)
        {
            // 画中画模式保存画中画截图和可见光图片
            QString qstrPicInPicFileFullName = qstrCommonFileName + "_pip" + JPG_FILE_NAME_SUFFIX;
            QPixmap pix = QPixmap::grabWidget(m_pGuideInfraredImagingView, QRect(0, 0, IMAGE_SCALED_WIDTH, IMAGE_SCALED_HEIGHT));
            if (!pix.save(qstrPicInPicFileFullName, "JPG"))
            {
                qDebug() << "----fail to save " << qstrPicInPicFileFullName;
            }
        }
        else if (GuideInfrared::DISPLAY_INFRARED == m_eGuideInfraredDisplayMode)
        {
            // 红外模式只存储红外+可见光图片
            QString strJpegFileFullName = qstrCommonFileName + JPG_FILE_NAME_SUFFIX;
            m_pGuideInfraredImagingView->saveInfraredPictureAsJpeg(strJpegFileFullName);
            m_stInfraredDataInfo.qstrPicFileName = FileOperUtil::getFileName(strJpegFileFullName);
        }

#ifdef DISPLAY_MODE
        // 都存储拍照的高清数据
        if (!stSavePicturesInfo.qbaVisiblePictureData.isEmpty())
        {
            QString strVisibleLightFileFullName = qstrCommonFileName + "_vl" + JPG_FILE_NAME_SUFFIX;
            QImage visibleLightImage = QImage::fromData(stSavePicturesInfo.qbaVisiblePictureData, "JPG");
            visibleLightImage.save(strVisibleLightFileFullName, "JPG");
        }
#endif
    }

    emit sigSaveFinished();

    logError(QString("GuideInfraredCabelTestView::saveData end"));

    MsgBox* pMsgBox = new MsgBox(MsgBox::INFORMATION);
    if(qstrFilePath.isEmpty())
    {
        pMsgBox->setInfo("", QObject::trUtf8("Save failure!"));
        rotateMsgBox(pMsgBox);
        resume();
    }
    else
    {
        // 数据写入任务中
        TaskManager::instance()->setInfraredTestDataInfo(m_stInfraredDataInfo);

        emit sigTested();

        int iMode = MobileAccessService::instance()->getSwitchMode();
        if(SystemSet::ACCESS_AUTO_SWITCH == static_cast<SystemSet::AccessSwitchMode>(iMode))
        {
            MsgBox *pMsgBox = new MsgBox(MsgBox::QUESTION);
            pMsgBox->setInfo("", QObject::trUtf8("Save success, auto switching."), (MsgBox::Buttons)(MsgBox::OK | MsgBox::CANCEL));
            pMsgBox->setRotate(true);
            pMsgBox->setDelayAcceptTime(5000);
            if(MsgBox::OK == rotateMsgBox(pMsgBox))
            {
                //emit sigExitTest();//避免当前界面还没有关闭就进入下一个红外检测界面
                m_bSwitchNext = true;
                close();
            }
            else
            {
                resume();
            }
        }
        else
        {
            QString strText = m_stInfraredDataInfo.qstrDataFileName;
            processTooLongMsgText(strText);

            MsgBox* pMsgBox = new MsgBox(MsgBox::INFORMATION);
            pMsgBox->setInfo("", strText);
            rotateMsgBox(pMsgBox);

            m_bSwitchNext = false;
            resume();
        }
    }

    m_bSave = false;
}

/****************************************
 * 功能：响应诊断结果选择
 * **************************************/
void GuideInfraredCabelTestView::onSelectDiagRet(CustomAccessTaskNS::DiagnoseType eDiagRet)
{
    m_eDiagRet = eDiagRet;
}

/****************************************
 * 功能：创建更多设置菜单栏
 * **************************************/
void GuideInfraredCabelTestView::createMoreConfigBar()
{
    m_pMoreButtonBars = createButtonBar(s_InfraredButtonInfosMore, sizeof(s_InfraredButtonInfosMore) / sizeof(InfraredButtonInfo));
    if(m_pMoreButtonBars)
    {
        m_pMoreButtonBars->attach(m_pButtonBar); // 做挂接
    }

    //创建ButtonBar部分布局
    QGraphicsScene* pMoreButtonBarScene = new QGraphicsScene(this);
    // 设置背景色
    pMoreButtonBarScene->setBackgroundBrush(QBrush(QColor(38, 70, 106)));

    m_pMoreButtonBarView = new QGraphicsView(pMoreButtonBarScene, this);
    m_pMoreButtonBarView->setContentsMargins(0, 0, 0, 0);
    m_pMoreButtonBarView->setFrameStyle(QFrame::NoFrame);
    m_pMoreButtonBarView->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pMoreButtonBarView->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pMoreButtonBarView->setResizeAnchor(QGraphicsView::AnchorViewCenter);
    m_pMoreButtonBarView->setFocusPolicy(Qt::StrongFocus);
    m_pMoreButtonBarView->rotate(VIEW_ROTATE_ANGLE);

    QGraphicsProxyWidget* pMoreButtonBarProxyWidget = pMoreButtonBarScene->addWidget(m_pMoreButtonBars);
    pMoreButtonBarProxyWidget->setFocusPolicy(Qt::StrongFocus);
    pMoreButtonBarProxyWidget->setFocus();

    pMoreButtonBarScene->setSceneRect(0, 0, m_pMoreButtonBars->width(), m_pMoreButtonBars->height());

    m_pMoreButtonBarView->setVisible(false);
}

/****************************************
 * 功能：删除当前文件槽函数
 * **************************************/
void GuideInfraredCabelTestView::onDeleteCurrentFile(const QString& qstrFilePath)
{
    QString qstrFileName = FileOperUtil::getFileName(qstrFilePath);
    if (m_testFileList.isEmpty() || !m_testFileList.contains(qstrFileName))
    {
        return;
    }

    TaskManager::instance()->deleteSpecifiedTestFile(qstrFileName);
    m_testFileList.removeOne(qstrFileName);
}

/*************************************************
函数名： onInitSuccess()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 处理初始化成功结果
*************************************************************/
void GuideInfraredCabelTestView::onInitSuccess()
{
    Guide::TemperatureParameterInfo stTemperatureParameterInfo;
    if (GuideClientManager::instance()->getTemperatureParamInfo(stTemperatureParameterInfo))
    {
        if (m_pGuideInfraredImagingView)
        {
            m_pGuideInfraredImagingView->setTemperatureParameterInfo(stTemperatureParameterInfo);
            m_pGuideInfraredImagingView->createCenterPointItem();
        }

        enableButtons();
        resume();
    }
    else
    {
        MsgBox *pMsgBox = new MsgBox(MsgBox::INFORMATION);
        pMsgBox->setInfo("", QObject::trUtf8("Failed to initialize parameters, do you want to exit the page?"), MsgBox::OK | MsgBox::CANCEL);
        if(MsgBox::OK == rotateMsgBox(pMsgBox))
        {
            //onExitInfrared();
            close();
        }
    }
}

/*************************************************
函数名： onInitFail()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 处理初始化失败结果
*************************************************************/
void GuideInfraredCabelTestView::onInitFail()
{
    showMsgBox(QObject::trUtf8("Initialization failed!"), MsgBox::WARNING);
}

/***********************************************************
 * 功能：激光控制
 * 输入参数：
 *      eSwitchValue：开或关
 * ********************************************************/
void GuideInfraredCabelTestView::ctrlLaser(Guide::SwitchValue eSwitchValue)
{
    if (!GuideClientManager::instance()->ctrlLaser(eSwitchValue))
    {
        if (Guide::SW_OFF == eSwitchValue)
        {
            showMsgBox(QObject::tr("Failed to turn off laser control!"), MsgBox::WARNING);
        }
        else
        {
            showMsgBox(QObject::tr("Failed to turn on laser control!"), MsgBox::WARNING);
        }

        // 恢复按钮状态
        if (m_pButtonBar)
        {
            if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_LASER_CONTROL))
            {
                Guide::SwitchValue eChangeValue = Guide::SW_OFF == eSwitchValue ? Guide::SW_ON : Guide::SW_OFF;
                pButton->setValue(eChangeValue);
            }
        }
    }
}

/***********************************************************
 * 功能：辅助照明控制
 * 输入参数：
 *      eSwitchValue：开或关
 * ********************************************************/
void GuideInfraredCabelTestView::ctrlAuxiliaryLighting(Guide::SwitchValue eSwitchValue)
{
    if (!GuideClientManager::instance()->ctrlAuxiliaryLighting(eSwitchValue))
    {
        if (Guide::SW_OFF == eSwitchValue)
        {
            showMsgBox(QObject::tr("Failed to turn off auxiliary lighting!"), MsgBox::WARNING);
        }
        else
        {
            showMsgBox(QObject::tr("Failed to turn on auxiliary lighting!"), MsgBox::WARNING);
        }

        // 恢复按钮状态
        if (m_pButtonBar)
        {
            if (InfraredControlButton* pButton = m_pButtonBar->buttons(GuideInfrared::BUTTON_AUXILIARY_LIGHTING))
            {
                Guide::SwitchValue eChangeValue = Guide::SW_OFF == eSwitchValue ? Guide::SW_ON : Guide::SW_OFF;
                pButton->setValue(eChangeValue);
            }
        }
    }
}

/*************************************************
函数名： onButtonValueChanged(int id, int iValue)
输入参数： id：按钮ID
          iValue：按钮值
输出参数： NULL
返回值： NULL
功能： 响应按钮值变化事件
*************************************************************/
void GuideInfraredCabelTestView::onButtonValueChanged(int id, int iValue)
{
    if (m_bSave)
    {
        // 正在保存不允许做其他操作
        return;
    }

    if (!m_pButtonBar)
    {
        return;
    }

    if (InfraredControlButton* pButton = m_pButtonBar->buttons(id))
    {
        if (!pButton->isEnabled())
        {
            logError("current press button is not enable.");
            return;
        }
    }
    else
    {
        return;
    }

    switch (id)
    {
    case GuideInfrared::BUTTON_LASER_CONTROL:
    {
        ctrlLaser(static_cast<Guide::SwitchValue>(iValue));
    }
        break;
    case GuideInfrared::BUTTON_LED_FILL_LIGHT:
    {
        m_bLedFillLight = 1 == iValue;
    }
        break;
    case GuideInfrared::BUTTON_DISPLAY_MODE:
    {
        setDisplayMode(static_cast<GuideInfrared::GuideInfraredDisplayMode>(iValue));
    }
        break;
    case GuideInfrared::BUTTON_AUXILIARY_LIGHTING:
    {
        ctrlAuxiliaryLighting(static_cast<Guide::SwitchValue>(iValue));
    }
        break;
    default:
        GuideInfraredViewBase::onButtonValueChanged(id, iValue);
        break;
    }
}
