#ifndef HFCTPRPSVIEWBASE_H
#define HFCTPRPSVIEWBASE_H

/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* HFCTPrpsView.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年4月28日
* 摘要：HFCT PRPS View定义

* 当前版本：1.0
*/

#include <QWidget>

#include "widgets/sampleChartView/SampleChartView.h"
#include "hfct/HFCT.h"
#include "hfct/HFCTPRPSService.h"
#include "buttonBar/LabelButtonBar.h"
#include "config/ConfigManager.h"
#include "diagnosismgr/diagnosismanager.h"
#include "Module.h"
#include "dealdata/dealdata.h"
#include "prps/prpsview/hfctprpsunionview.h"
#include "prps/prpsvideo/prpsrecordview.h"
#include "View.h"
#include "hfct/HFCTConfig.h"

class HFCTPrpsViewBase : public SampleChartView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        parent:父控件指针
    *************************************************************/
    explicit HFCTPrpsViewBase(const QString& strTitle, QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~HFCTPrpsViewBase( );

protected:
    /*************************************************
    函数名：service
    输入参数: NULL
    输出参数：NULL
    返回值：service对象
    功能：返回service对象
    *************************************************************/
    HFCTPRPSService* getHFCTPRPSService();

    /*************************************************
    功能： 设置工作模式
    *************************************************************/
    void setWorkMode( HFCT::WorkMode eWorkMode );

    /*************************************************
    功能： 设置增益
    入参：eGain -- 增益
    *************************************************************/
    void setGain( HFCT::Gain eGain );

    /*************************************************
    功能： 设置同步源
    入参：eSyncSource -- 同步源
    *************************************************************/
    void setSyncSource( Module::SyncSource eSyncSource );

    /*************************************************
    功能： 响应S键事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    virtual void onSKeyPressed();

    INT32 quantizationAmpSection(INT32 iAmp, float fAmpLower, float fAmpUpper, INT32 iQuantizationAmp);

    /************************************************
     * 函数名   : startSample
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 启动采样
     ************************************************/
    virtual void startSample();

    /************************************************
     * 函数名   : startSample
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 启动采样
     ************************************************/
    virtual void stopSample();

    /*************************************************
     * 功能：诊断数据
     * 输入参数：
     *      bSave：是否为保存操作的逻辑，缺省为false
     * ***********************************************/
    virtual void diagDataInfo(bool bSave = false);

    void updateAccumulativeTime();

    /*************************************************
    功能： 更新阈值百分比
    *************************************************************/
    void updateThresholdPercentage();

    /*************************************************
    功能： 更新标题
    *************************************************************/
    void updateTitle();

protected slots:
//    /*************************************************
//    功能： 槽，响应按钮值变化事件
//    输入参数：
//            id -- 按钮ID
//            iValue -- 按钮值
//    *************************************************************/
//    virtual void onButtonValueChanged( int id, int iValue ) = 0;

//    /*************************************************
//    功能： 槽，响应命令按钮按下事件
//    输入参数：
//            id -- 按钮ID
//    *************************************************************/
//    virtual void onCommandButtonPressed( int id ) = 0;

    /*************************************************
    功能： 槽，响应读取的数据
    输入参数：
            data -- 周期数据
    *************************************************************/
    virtual void onDataRead(HFCT::PRPSData data,MultiServiceNS::USERID userId) = 0;

    /*************************************************
    功能： 槽，响应信号状态改变
    输入参数：
            eSignalState -- 信号状态
    *************************************************************/
    virtual void onSignalChanged( Module::SignalState eSignalState ) = 0;

    /*************************************************
    功能： 槽，响应同步状态改变
    输入参数：
            eSyncState -- 同步状态
    *************************************************************/
    virtual void onSyncStateChanged( Module::SyncState eSyncState ) = 0;

    /*************************************************
    功能： 槽，响应诊断结果
    输入参数：
            qspDiagResultInfo -- 诊断结果
    *************************************************************/
    virtual void onDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo) = 0;

private:
    /*************************************************
    功能： 初始化数据
    *************************************************************/
    void initDatas( void );

    virtual ChartWidget *createChart(QWidget *parent);

protected:
    typedef struct _PRPSMaxValue  // PRPS图谱最大值的数据结构
    {
        INT8  cMaxValue;    // 最大值
        HFCT::SpectrumState  eSpectrumState;         //频谱状态
        _PRPSMaxValue( INT8 cMax = 0,HFCT::SpectrumState eState = HFCT::SPECTRUM_INSIDE_RANGE )
        {
            cMaxValue = cMax;
            eSpectrumState = eState;
        }
    }PRPSMaxValue;


//    HfctPrpsUnionView* m_pChart;//图谱
//    ConfigInstance* m_pConfig;//配置模块
//    LabelButtonBar* m_pMoreConfigView;//更多设置视图
//    ControlButton *m_pSampleBtn; //采样按钮
//    Module::SignalState m_eSignalState;// 信号状态

//    Module::SyncSource m_eSyncSource;//同步方式
//    Module::SyncState m_eSyncState;//同步状态
//    HFCT::Gain m_eGain;//前置增益
//    int m_iPhaseAlias; //相位偏移
//    QVector<PRPSMaxValue> m_vMaxValue;// 存放最大值的容器
//    UINT16 m_usMaxValue;//每周期数据最大值
//    HFCT::SpectrumState m_eSpectrumState;//最大数据的频谱状态
//    bool m_bIsAccumulation; //是否累积标志
//    HFCT::PRPSData m_data;//数据

//    bool m_bIsSampling; // 采集的标志
//    UINT8 m_ucSysFreq;//系统频率

//    UINT8 m_ucRecordTime;//录屏时间 Unit:Min
//    bool m_bIsRecord;    //录屏开始的标志

    enum Info
    {
        CHART_HEIGHT = 510, //UHF prps 图谱高度
        PERIOD_CNT = 50, //周期数
        PMDT_DISPLAYED_PRPS_PERIOD_CNT = 60,
        PHASE_CNT = 60, //每周期相位数
        DEFAULT_DISCHARG_RATIO = 50, //PRPD2D放电量严重程度百分比
        PRPD_MARGIN = 60,
        PDA_PRPD_MARGIN = 30,
    };

//    PrpsRecordView *m_pRecordView; // 录制界面
//    QFuture<void> m_SaveVideoFuture;
    int m_iSysPeriod;   //系统频率    
    HfctPrpsUnionView* m_pChart;//图谱
    float m_fThresholdPercentage;
    HFCT::ThresholdMode m_eThresholdMode; // 自动阈值
    PRPSFeatureDealInfo m_stPRPSFeateurInfo; // 自动阈值计算处理
    int m_iAccumulationTime; //是否累积标志
    PhaseAbstractView::AltasType m_eAltasType;
};

#endif // HFCTPRPSVIEWBASE_H
