
#ifndef ROTATEFILELISTVIEWEX_H
#define ROTATEFILELISTVIEWEX_H

#include <QWidget>
#include <QStringListModel>
#include <QDir>
#include <QBoxLayout>
#include <QKeyEvent>
#include <QDialog>
#include <QToolButton>
#include <QLineEdit>
#include "widgetglobal.h"
#include "rotatelistview.h"

class WIDGET_EXPORT RotateFileListViewEx : public QWidget
{
    Q_OBJECT

public:
    /*************************************************
    功能： 构造函数
    输入参数:
        path:路径(绝对路径)
        parent:父窗口指针
    *************************************************************/
    explicit RotateFileListViewEx( const QString& path, QWidget *parent = NULL );

    /*************************************************
    功能： 设置文件列表
    输入参数:
        fileList: 文件列表
    *************************************************************/
    void setFileList(const QStringList& fileList);

signals:
    /*************************************************
    信号： 发送用户选择的文件名
    输入参数:
        index -- 索引
        fileNameList -- 文件名列表
    *************************************************************/
    void sigFileSelected( int index , const QStringList& fileNameList );

    /*************************************************
    功能： 发送用户选择的文件名
    输入参数:
        fileName -- 文件名
    *************************************************************/
    void sigFileSelected( const QString& fileName );

    /*************************************************
    功能： esc按键按下时发射的信号
    *************************************************************/
    void sigEscClicked( void );
protected:
    /*************************************************
    功能： Item被点击或按下enter键时的响应
    输入参数:
            index: 索引
    *************************************************************/
    virtual void itemTriggered(const QModelIndex &index);

    /*************************************************
    功能： 处理键盘事件
    输入参数:
            pEvent:键盘事件指针
    *************************************************************/
    void keyPressEvent(QKeyEvent *pEvent);

    virtual bool eventFilter(QObject* pObject, QEvent* pEvent);

private slots:
    /*************************************************
    功能： 处理进入排序按钮被点击的事件
    *************************************************************/
    void onSortBtnClicked();

    /*************************************************
    功能： 响应鼠标点击事件，进入下级目录
    输入参数:
           index: 被点击的Item索引
    *************************************************************/
    void onListViewClicked(const QModelIndex& index);

private:
    /*************************************************
    功能： 初始化
    *************************************************************/
    void initialize();

protected:
    QStringListModel *m_pModel;  // 文件系统模型指针

private:
    HorListView *m_pView;          // 列表view指针

    QBoxLayout *m_pBoxLayout;    // 主布局器指针
    QLineEdit *m_pPahtLineEdit;  // 显示当前目录路径的行编辑器控件指针
    QToolButton *m_pSortBtn;     // 排序按钮指针

    Qt::SortOrder m_eSortOrder;
    QString m_qstrRootPath;
};

#endif // ROTATEFILELISTVIEWEX_H
