/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* PulseClusterView.h
*
* 初始版本：1.0
* 作者：陈康
* 创建日期：2017年4月13日
*       新版本重构
* 摘要：接入G100的脉冲分析界面

* 当前版本：1.0
*/

#include "PulseClusterView.h"
#include "CAViewConfig.h"
#include "datadefine.h"
#include "window/Window.h"
#include "pulsedetailview.h"
#include "messageBox/msgbox.h"

typedef enum _ColorClusterButton
{
    BUTTON_NONE = -1,
    BUTTON_PHASE_SHIFT = 0,//相移
    BUTTON_PULSE_WAVE//脉冲查看
}ColorClusterButton;

//相位偏移
const ButtonInfo::SliderValueConfig s_PhaseShift =
{
    CAViewConfig::PHASE_MIN, CAViewConfig::PHASE_MAX, CAViewConfig::PHASE_STEP
};

//控制按钮定义
const ButtonInfo::Info s_ClusterButtonInfo[] =
{
    { BUTTON_PHASE_SHIFT, { ButtonInfo::FIXED_STEP_SLIDER, CAViewConfig::TEXT_PHASE_ALIAS, CAViewConfig::TEXT_DEGREE, NULL, &s_PhaseShift } },//
    { BUTTON_PULSE_WAVE, { ButtonInfo::COMMAND, CAViewConfig::TEXT_PULSE_WAVE, NULL, NULL, NULL } },//
};

/*************************************************
功能： 构造函数
输入参数: strTitle: 标题
        lData: 脉冲数据
        sampRateOpition: 采样率
        parent:父窗口
*************************************************************/
PulseClusterView::PulseClusterView(const QString& strTitle,
                                     const QList<CA::PulseData> &lData,
                                     CA::SampleRate sampRateOpition,
                                   double dPulseWidth,
                                   qint32 iRangeMax,
                                   float fDiagnosisYRange,
                                     QWidget *parent)
    :ChartView(strTitle, parent),
      m_lRawData( lData ),
      m_eSampleRate( sampRateOpition ),
      m_dPulseWidth(dPulseWidth),
      m_iRangeMax(iRangeMax),
      m_fDiagnosisYRange(fDiagnosisYRange)
{
    initData();

    diagnosis(m_eSignalType, m_dbConfidence);

    createUI(this);

    setButtonBarData();
}

/*************************************************
功能： 析构函数
*************************************************************/
PulseClusterView::~PulseClusterView()
{

}

/*************************************************
功能： 槽，响应按钮值变化事件
输入参数：
        id -- 按钮ID
        iValue -- 按钮值
*************************************************************/
void PulseClusterView::onButtonValueChanged( int id, int iValue )
{
    switch( id )
    {
    case BUTTON_PHASE_SHIFT://相移
        if(m_iPhaseShift != iValue)
        {
            phaseShift(iValue - m_iPhaseShift);
            m_iPhaseShift = iValue;
        }
        break;
    default:
        break;
    }
}

void PulseClusterView::phaseShift(qint32 iDegree)
{
    m_pPrpdFast->translatePhase(iDegree);
}

/*************************************************
功能： 槽，响应命令按钮按下事件
输入参数：
        id -- 按钮ID
*************************************************************/
void PulseClusterView::onCommandButtonPressed( int id )
{
    switch( id )
    {
    case BUTTON_PULSE_WAVE://脉冲波形
        showPulseWave();
        break;
    default:
        break;
    }
}



//uiLineIndex: 0~49  uiColumnIndex :0 ~71
void PulseClusterView::calculateAmpIntervalIndex(float fValue, float fPhase, quint32 &uiLineIndex, quint32 &uiColumnIndex)
{
    float fLineStep = m_fDiagnosisYRange / (float) CA::PRPS_PERIOD_CNT;
    float fColumnStep = 360.0 / (float) CA::PRPS_PHASE_CNT;


    for(int i = 0; i < CA::PRPS_PERIOD_CNT;i ++)
    {
        if((fLineStep *i) <= fValue && (fLineStep *(i+1)) > fValue)
        {
            uiLineIndex = i;
            break;
        }
    }

    for(int i = 0; i < CA::PRPS_PHASE_CNT;i ++)
    {
        if((fColumnStep *i) <= fPhase && (fColumnStep *(i+1)) > fPhase)
        {
            uiColumnIndex = i;
            break;
        }
    }
}

void PulseClusterView::diagnosis(SignalType &eSignalType, double &dbConfidence)
{
    int iAmpNum = CA::PRPS_AMP_INTERVAL_CNT;
    int iPhaseNum = CA::PRPS_PHASE_CNT;

    double *PRPD = new double[iPhaseNum*iAmpNum];
    memset(PRPD, 0, sizeof(double) * iPhaseNum*iAmpNum);
    dbg_info(" m_lRawData.size() is %d\n",  m_lRawData.size());
    for(int i = 0; i <  m_lRawData.size(); i ++)
    {
        quint32 uiLineIndex = 0;
        quint32 uiColumnIndex = 0;

        CA::PulseData stData = m_lRawData.at(i);
        float fValue = stData.fMaxValue;
        float fPhase = stData.fMaxValuePhase;
        calculateAmpIntervalIndex(fValue, fPhase, uiLineIndex, uiColumnIndex);
        int index =  uiLineIndex  + uiColumnIndex *iAmpNum;
        PRPD[index]++;
    }

#ifdef Q_PROCESSOR_ARM
    DoulbePrpdDiag( PRPD, iPhaseNum, iAmpNum, &eSignalType, &dbConfidence );
    dbg_info("eSignalType is %d, dbConfidence is %f\n", eSignalType, dbConfidence);
#endif
    delete [] PRPD;
}

void PulseClusterView::showPulseWave()
{
    if(m_lRawData.size() > 0)
    {
        PulseDetailView *pView= new PulseDetailView(qApp->translate("CAView", CAViewConfig::TEXT_PULSE_WAVE), m_lRawData, m_eSampleRate, m_dPulseWidth, m_iRangeMax);
        pView->show();
    }
    else
    {
        MsgBox::information( "", QObject::trUtf8("No pulse!") );
    }
}

/*************************************************
功能： 设置按钮栏数据
*************************************************************/
void PulseClusterView::setButtonBarData()
{
    ((PopupButton*)(buttonBar()->button(BUTTON_PHASE_SHIFT)))->setValue( m_iPhaseShift );
}

/*************************************************
功能： 初始化成员变量
*************************************************************/
void PulseClusterView::initData()
{
    m_pPrpdFast = NULL;
    m_pPulseCntLabel = NULL;
    m_pDiagnosisLabel = NULL;
    m_iPhaseShift = 0;
}

/*************************************************
函数名： createUI
输入参数: NULL
输出参数：NULL
返回值： NULL
功能：初始化用户界面布局和显示
*************************************************************/
void PulseClusterView::createUI(QWidget *parent)
{
    setFixedSize( Window::WIDTH, Window::HEIGHT );

    //创建图谱区域
    ChartWidget *pWidget = createCharts(parent);
    setChart( pWidget );

    //创建按钮栏
    createButtonBar( CAViewConfig::CONTEXT, s_ClusterButtonInfo, sizeof(s_ClusterButtonInfo)/sizeof(ButtonInfo::Info) );
}

void PulseClusterView::setPulseCntLabelText()
{
    int totalCnt = m_lRawData.size();
    QString strLabelText =  QObject::trUtf8("Pulse Total Count: ");
    strLabelText +=  QString("%1").arg(totalCnt);
    m_pPulseCntLabel->setText(strLabelText);
}

/*************************************************
函数名： createCharts
输入参数: parent---父控件
输出参数：NULL
返回值： NULL
功能： 创建图谱显示区域
*************************************************************/
ChartWidget * PulseClusterView::createCharts(QWidget *parent)
{
    //设置图谱布局
    ChartWidget *pWidget = new ChartWidget;

    //pulse total count label
    m_pPulseCntLabel = new QLabel;
    QFont font = m_pPulseCntLabel->font();
    font.setPointSize(30);
    m_pPulseCntLabel->setFont(font);
    m_pPulseCntLabel->setAlignment(Qt::AlignCenter);

    setPulseCntLabelText();


    //diagnosis result label
    m_pDiagnosisLabel = new QLabel;
    QFont Diagnosisfont = m_pDiagnosisLabel->font();
    Diagnosisfont.setPointSize(30);
    Diagnosisfont.setBold(true);
    m_pDiagnosisLabel->setFont(Diagnosisfont);
    QString strLabelText =  QObject::trUtf8("Result: ");
    m_pDiagnosisLabel->setText(strLabelText);


    //signal type label
    m_pSignalTypeLabel = new QLabel;
    QFont SignalTypefont = m_pSignalTypeLabel->font();
    SignalTypefont.setPointSize(25);
    m_pSignalTypeLabel->setFont(SignalTypefont);
    QString strType;
    CAViewConfig::signalType2String(m_eSignalType, strType);
    QString str = QObject::trUtf8("Signal Type: ") + strType + QString(".");
    m_pSignalTypeLabel->setText(str);

    //confidence label
    m_pConfidenceLabel = new QLabel;
    m_pConfidenceLabel->setFont(SignalTypefont);
    str = QObject::trUtf8("Confidence Level: ") + QString::number(m_dbConfidence * 100.0, 'f', 2) + QString("%.");
    m_pConfidenceLabel->setText(str);

    //prpd
    //创建要显示的图谱
    PrpdFast *pPRPD = createPrpdChart(parent);
    pPRPD->setRangeUnitMax(CA::PRPD_RANGE_UNIT_MV, m_iRangeMax);
    if(m_iRangeMax < CA::MV_PER_V)
    {
        pPRPD->setRangeUnitMax(CA::PRPD_RANGE_UNIT_MV, m_iRangeMax);
    }
    else
    {
        pPRPD->setRangeUnitMax(CA::PRPD_RANGE_UNIT_V, m_iRangeMax / CA::MV_PER_V);
    }

    //set content
    QVBoxLayout* vContentLayout = new QVBoxLayout;
    vContentLayout->addWidget( m_pPulseCntLabel,Qt::AlignHCenter| Qt::AlignTop );
    vContentLayout->addWidget( pPRPD,Qt::AlignCenter );
    vContentLayout->addWidget( m_pDiagnosisLabel );
    vContentLayout->addWidget( m_pSignalTypeLabel );
    vContentLayout->addWidget( m_pConfidenceLabel );
    vContentLayout->setContentsMargins( 0,0,0,0 );
    vContentLayout->setSpacing( 0 );

    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->addLayout( vContentLayout );
    vLayout->setContentsMargins( 0,0,0,0 );
    vLayout->setSpacing( 0 );
    pWidget->setLayout(vLayout);

    return pWidget;
}


/*************************************************
功能： 创建PRPD图谱
*************************************************************/
PrpdFast *PulseClusterView::createPrpdChart(QWidget *parent)
{
    m_pPrpdFast = new PrpdFast( parent );
    m_pPrpdFast->setCoordinaterColor( Qt::black );
    m_pPrpdFast->setFixedHeight( CHART_HEIGHT );
    m_pPrpdFast->setFixedWidth( Window::WIDTH );
    m_pPrpdFast->show();
    return m_pPrpdFast;
}

///*************************************************
//功能： 设置界面的显示数据
//*************************************************************/
//void PulseClusterView::setUIData()
//{
//    setButtonBarData();

//    setChartData();
//}

/*************************************************
功能： 设置图谱数据
*************************************************************/
void PulseClusterView::setChartData()
{
    setPrpdData();
}

/*************************************************
函数名： setPrpdData
输入参数: NULL
输出参数： NULL
返回值： NULL
功能： 处理选中prpd分析数据，显示在图谱上
*************************************************************/
void PulseClusterView::setPrpdData()
{
    if(!m_lRawData.isEmpty())
    {
        APP_CHECK_RETURN( m_pPrpdFast );
        //Prpd图谱累积清零
        m_pPrpdFast->clearAccumulation();
        //获取各个点的m_maxValuePercent和m_maxValuePhase存入数组中
        QVector< PrpdData > prpdVector;
        PrpdData prpdData;
        for( UINT16 i = 0;i < m_lRawData.size();++i )
        {
            prpdData.fPeak = (m_lRawData.at( i ).fMaxValue+m_iRangeMax) / (float)(2*m_iRangeMax);
            prpdData.fPhase = m_lRawData.at( i ).fMaxValuePhase;
            prpdVector.append( prpdData );
        }
        //将该数组数据显示在图谱上
        m_pPrpdFast->setDatas( prpdVector );
    }
}

