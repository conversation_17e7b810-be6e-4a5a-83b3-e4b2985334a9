/*
 * Copyright (c) 2019.6，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：currentdetectionviewbase.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/06/27
 * 摘要：电流检测视图基类
 * 当前版本：1.0*/

#pragma once

#include "widgets/sampleChartView/SampleChartView.h"

class CurrentDetectionViewBase : public SampleChartView
{
    Q_OBJECT

public:
    explicit CurrentDetectionViewBase(const QString& qstrTitle, QWidget *parent = NULL);
    virtual ~CurrentDetectionViewBase();

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    virtual void onButtonValueChanged(int id, int iValue) = 0;

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    virtual void onCommandButtonPressed(int id) = 0;
};
