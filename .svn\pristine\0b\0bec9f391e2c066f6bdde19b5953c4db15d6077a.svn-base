/*
* Copyright (c) 2016.1，南京华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：DateTimeWidget.cpp
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2016年1月20日
* 摘要：该文件主要定义日期、时间栏的相关信息

* 当前版本：1.0
*/


#include <QApplication>
#include <QVBoxLayout>
#include <QPixmap>
#include <QPalette>
#include <QLabel>
#include <QDebug>
#include "DateTimeWidget.h"
#include "systemsetting/SystemSet.h"
#include "appfontmanager/appfontmanager.h"

//该字符串常量为设置styleSheet时所用，从左到右依次为，背景色：透明，字体颜色：偏黄色（调色板自调），边框：无，字号：**，字体：雅黑
//const QString TIME_STYLE ="QLabel{background:rgb(0,0,0,0);color:rgb(255,255,255);border:none;font-family:msyh}";
//const QString DATE_STYLE ="QLabel{background:rgb(0,0,0,0);color:rgb(255,255,255);border:none;font-family:msyh}";
const QString TIME_STYLE ="QLabel{background:rgb(0,0,0,0);color:rgb(255,255,255);border:none;}";
const QString DATE_STYLE ="QLabel{background:rgb(0,0,0,0);color:rgb(255,255,255);border:none;}";
const QString CLOCK_BACKGROUND_PATH = ":images/statusImage/clock.png";

const char* const WEEKDAY_ONE = QT_TRANSLATE_NOOP_UTF8("Weekday","Monday");
const char* const WEEKDAY_TWO = QT_TRANSLATE_NOOP_UTF8("Weekday","Tuesday");
const char* const WEEKDAY_THREE = QT_TRANSLATE_NOOP_UTF8("Weekday","Wednesday");
const char* const WEEKDAY_FOUR = QT_TRANSLATE_NOOP_UTF8("Weekday","Thursday");
const char* const WEEKDAY_FIVE = QT_TRANSLATE_NOOP_UTF8("Weekday","Friday");
const char* const WEEKDAY_SIX = QT_TRANSLATE_NOOP_UTF8("Weekday","Saturday");
const char* const WEEKDAY_SEVEN = QT_TRANSLATE_NOOP_UTF8("Weekday","Sunday");

#define DATETIMEWIDGET_1S 1000 //定时间隔
#define STR_DATE_FORMAT_Y_M_D "yyyy/MM/dd" //日期格式
#define STR_DATE_FORMAT_M_D_Y "MM/dd/yyyy" //日期格式
#define STR_DATE_FORMAT_D_M_Y "dd/MM/yyyy" //日期格式
#define TIME_FORMAT "hh:mm" //时间格式
#define PROTOTYPE_PIXEL_SIZE 60

class DateTimeWidgetPrivate : public QObject
{
public:
    typedef enum _CustomWord
    {
        TIME_DEFAULT = 0,  // 默认字样为时间
        PROTOTYPE_CUSTOM, //样机
    }CustomWord;

    /****************************
    功能： 构造函数
    输入参数:
            parent -- 父Object
    *****************************/
    explicit DateTimeWidgetPrivate( QObject *parent );

    /****************************
    功能： 设置空间像素
    输入参数:
          iDatePixel -- 日期像素
          iTimePixel -- 时间像素
    *****************************/
    void setPixcel( int iDatePixel, int iTimePixel );

    /****************************
    功能： 设置自定义字符形式，用于显示诸如样机这样的定制信息
    *****************************/
    void setCustomWord( CustomWord eCustomWord );

    void setTimeFormat(SystemSet::TimeFormat eNewFormat);

    void setDateFormat(SystemSet::DateFormat eNewFormat);

    void setIsDSTWork(bool isDSTWork);

    /****************************
    功能： 语言切换后，修改字体和文字
    *****************************/
    void retranslateUI();

private:
    /****************************
    功能： 显示当前日期时间
    输入参数:void
    *****************************/
    void showDateTime( void );

    /****************************
    函数名： 获取星期
    输入参数:
        dateTime -- 日期时间
    返回值：星期的字符串
    *****************************/
    QString getWeek( const QDateTime &dateTime );
protected:
    /****************************
    函数名： timerEvent(m_qTimerEvent *pEvent);
    输入参数:pEvent：定时事件
    输出参数：NULL
    返回值：NULL
    功能： 重载时间函数
    *****************************/
    void timerEvent(QTimerEvent *pEvent);
public:
    QLabel  *m_pLabelDate;                       // 用于显示日期
    QLabel  *m_pLabelTime;                       // 用于显示时间
    int  m_iTimerProcess;                      // 用于记录定时器的Id
    CustomWord m_eCustomWord;                    // 用于时间栏是否显示样机字样
    //DateTimeWidgetPrivate* d;
    SystemSet::DateFormat m_eDateFormat;
    SystemSet::TimeFormat m_eTimeFormat;
    bool m_isDSTWork;
};

/****************************
功能： 构造函数
输入参数:
        parent -- 父窗体
*****************************/
DateTimeWidgetPrivate::DateTimeWidgetPrivate( QObject *parent )
    : QObject( parent ), m_iTimerProcess( -1 ), m_eCustomWord( TIME_DEFAULT )
{
    m_eDateFormat = SystemSet::DATE_FORMAT_Y_M_D;
    m_eTimeFormat = SystemSet::TIME_FORMAT_24_HOUR;

    //日期
    m_pLabelDate = new QLabel();
    m_pLabelDate->setAlignment( Qt::AlignHCenter);       // 设置将文字水平居中显示
    m_pLabelDate->setStyleSheet(DATE_STYLE);             // 设置将字体、大小、颜色等参数

    //时间
    m_pLabelTime = new QLabel();
    m_pLabelTime->setAlignment(Qt::AlignHCenter);        // 设置将文字水平居中显示
    m_pLabelTime->setStyleSheet(TIME_STYLE);

    showDateTime();//显示时间

    m_iTimerProcess = startTimer( DATETIMEWIDGET_1S );
}
/****************************
功能： 显示当前日期时间
输入参数:void
*****************************/
void DateTimeWidgetPrivate::showDateTime( void )
{
    QDateTime dateTime = QDateTime::currentDateTime();          //24时制的时间
    QString strWeek = getWeek( dateTime );
    QTime time = dateTime.time();
    int hour = time.hour();
    QString strTimeSuffix = "";

    if(m_eTimeFormat == SystemSet::TIME_FORMAT_12_HOUR)
    {
        //12时制hour范围是1-12 AM/PM, 24时制hour范围是00-23
        if(0 == hour)
        {
            hour = 12;
            strTimeSuffix = "AM";
        }
        else if(12 == hour)
        {
            hour = 12;
            strTimeSuffix = "PM";
        }
        else if(12 < hour && 24 > hour)
        {
            hour -= 12;
            strTimeSuffix = "PM";
        }
        else
        {
            strTimeSuffix = "AM";
        }

        time.setHMS(hour, time.minute(), time.second());
        dateTime.setTime(time);
    }
    QString strTimeFormat;
    if(strTimeSuffix.isEmpty())
    {
        strTimeFormat = dateTime.toString(TIME_FORMAT);
    }
    else
    {
        strTimeFormat = dateTime.toString(TIME_FORMAT) + " " + strTimeSuffix;
    }

    if(m_isDSTWork)
    {
        strTimeFormat += " ";
        strTimeFormat += "DST";
    }
    if( TIME_DEFAULT == m_eCustomWord )
    {
        m_pLabelTime->setText(strTimeFormat);//显示时间
    }

    QString strDateFormat;
    if(m_eDateFormat == SystemSet::DATE_FORMAT_Y_M_D)
    {
        strDateFormat = STR_DATE_FORMAT_Y_M_D;
    }
    else if(m_eDateFormat == SystemSet::DATE_FORMAT_M_D_Y)
    {
        strDateFormat = STR_DATE_FORMAT_M_D_Y;
    }
    else
    {
        strDateFormat = STR_DATE_FORMAT_D_M_Y;
    }
    m_pLabelDate->setText( dateTime.toString( strDateFormat ) + " " +strWeek);//显示日期、星期
    return;
}

/****************************

*****************************/
void DateTimeWidgetPrivate::setTimeFormat(SystemSet::TimeFormat eNewFormat)
{
    m_eTimeFormat = eNewFormat;
}

void DateTimeWidgetPrivate::setIsDSTWork(bool isDSTWork)
{
    m_isDSTWork = isDSTWork;
}

/****************************

*****************************/
void DateTimeWidgetPrivate::setDateFormat(SystemSet::DateFormat eNewFormat)
{
    m_eDateFormat = eNewFormat;
}

/****************************
函数名： 获取星期
输入参数:
    dateTime -- 日期时间
返回值：星期的字符串
*****************************/
QString DateTimeWidgetPrivate::getWeek( const QDateTime &dateTime )
{
    switch( dateTime.date().dayOfWeek() )
    {
        case 1:
        {
            return qApp->translate("Weekday", WEEKDAY_ONE);
        }
        case 2:
        {
            return qApp->translate("Weekday", WEEKDAY_TWO);
        }
        case 3:
        {
            return qApp->translate("Weekday", WEEKDAY_THREE);
        }
        case 4:
        {
            return qApp->translate("Weekday", WEEKDAY_FOUR);
        }
        case 5:
        {
            return qApp->translate("Weekday", WEEKDAY_FIVE);
        }
        case 6:
        {
            return qApp->translate("Weekday", WEEKDAY_SIX);
        }
        case 7:
        {
            return qApp->translate("Weekday", WEEKDAY_SEVEN);
        }
        default:
            return "";
    }
}

/****************************
函数名： timerEvent(m_qTimerEvent *pEvent);
输入参数:pEvent：定时事件
输出参数：NULL
返回值：NULL
功能： 重载时间函数
*****************************/
void DateTimeWidgetPrivate::timerEvent(QTimerEvent *pEvent)
{
    if( pEvent->timerId() == m_iTimerProcess )
    {
        showDateTime();
    }
}
/****************************
功能： 设置空间像素
输入参数:
      iDatePixel -- 日期像素
      iTimePixel -- 时间像素
*****************************/
void DateTimeWidgetPrivate::setPixcel(int iDatePixel, int iTimePixel)
{
    QFont font = m_pLabelDate->font();
    font.setPixelSize(iDatePixel);
    m_pLabelDate->setFont(font);

    font = m_pLabelTime->font();
    font.setPixelSize(iTimePixel);
    m_pLabelTime->setFont(font);
    return;
}

/****************************
功能： 设置自定义字符形式，用于显示诸如样机这样的定制信息
*****************************/
void DateTimeWidgetPrivate::setCustomWord( CustomWord eCustomWord )
{
    m_eCustomWord = eCustomWord;
    if( PROTOTYPE_CUSTOM == m_eCustomWord )
    {
        QFont font = m_pLabelTime->font();
        font.setPixelSize(PROTOTYPE_PIXEL_SIZE);
        m_pLabelTime->setFont(font);
        m_pLabelTime->setText(QObject::trUtf8("Proto"));//显示时间
    }
}

/****************************
功能： 语言切换后，修改字体和文字
*****************************/
void DateTimeWidgetPrivate::retranslateUI()
{
    QFont stFont = AppFontManager::instance()->getAppCurFont();
    stFont.setPixelSize((m_pLabelDate->font()).pixelSize());
    m_pLabelDate->setFont(stFont);

    stFont.setPixelSize((m_pLabelTime->font()).pixelSize());
    m_pLabelTime->setFont(stFont);
    return;
}

/****************************
功能： 构造函数
输入参数:
        parent -- 父窗体
*****************************/
DateTimeWidget::DateTimeWidget( QWidget *parent )
    : QWidget( parent )
{
    d = new DateTimeWidgetPrivate(this);

    QVBoxLayout *vLayout = new QVBoxLayout(this);   // 添加当前窗口竖直布局
    vLayout->addWidget(d->m_pLabelTime, 3);             // 往布局中添加控件，并设置比例因子，日期和时间的大小比例为3:1
    vLayout->addSpacing(8);                         // 控件之间添加固定大小间隔
    vLayout->addWidget(d->m_pLabelDate, 1);
    vLayout->setMargin(20);                         // 设置控件和窗体之间的间距
    setLayout(vLayout);
}

/****************************
功能： 设置空间像素
输入参数:
      iDatePixel -- 日期像素
      iTimePixel -- 时间像素
*****************************/
void DateTimeWidget::setPixcel( int iDatePixel, int iTimePixel )
{
    d->setPixcel( iDatePixel, iTimePixel );
}

/****************************

*****************************/
void DateTimeWidget::setDateFormat(SystemSet::DateFormat eNewFormat)
{
    d->setDateFormat( eNewFormat );
}

/****************************

*****************************/
void DateTimeWidget::setTimeFormat(SystemSet::TimeFormat eNewFormat)
{
    d->setTimeFormat( eNewFormat );
}

void DateTimeWidget::setIsDSTWork(bool isDSTWork)
{
    d->setIsDSTWork( isDSTWork );
}

/****************************
功能： 使能样机显示
*****************************/
void DateTimeWidget::enablePrototype( void )
{
    d->setCustomWord( DateTimeWidgetPrivate::PROTOTYPE_CUSTOM );
}

/****************************
功能： 语言切换后，修改字体和文字
*****************************/
void DateTimeWidget::retranslateUI()
{
    d->retranslateUI();
    return;
}
