/*
* Copyright (c) 2017.04，南京华乘电气科技有限公司
* All rights reserved.
*
* uhfprpsviewbase.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2017年4月21日
*       新版本重构
* 摘要：UHF prps view接口和成员申明

* 当前版本：1.0
*/

#ifndef TEVPRPSVIEWBASE_H
#define TEVPRPSVIEWBASE_H

#include <QWidget>
#include "SampleChartView.h"
#include "tev/tevprpsservice.h"
#include "buttonBar/LabelButtonBar.h"
#include "config/ConfigManager.h"
#include "Module.h"
#include "prps/prpsview/uhfprpsunionview.h"
#include "dataSave/DataStructures.h"
#include "messageBox/msgbox.h"
#include "multiservice/multiservicedefine.h"

class TEVPRPSViewBase : public SampleChartView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        parent:父控件指针
    *************************************************************/
    TEVPRPSViewBase(const QString &strTitle, QWidget *parent = 0);

    /*************************************************
    功能： 析构
    *************************************************************/
    ~TEVPRPSViewBase( );

protected:
    /*************************************************
    功能： 响应S键事件
    *************************************************************/
    virtual void onSKeyPressed();

    TevPRPSService* getTevPRPSService();

    void moveMsgBox(MsgBox *pMsgBox);

    /*************************************************
    功能： 设置消息提示框信息
    入参： title -- 标题
          text -- 提示文本
    *************************************************************/
    MsgBox::Button setMessageBoxInfo( const QString &title,
                            const QString &text );

protected slots:
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    virtual void onButtonValueChanged( int id, int iValue ) = 0;

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    virtual void onCommandButtonPressed( int id ) = 0;

protected:
    /*************************************************
    功能： 设置工作模式
    *************************************************************/
    void setWorkMode( TEV::WorkMode eWorkMode );


    /*************************************************
    功能： 设置同步源
    入参：eSyncSource -- 同步源
    *************************************************************/
    void setSyncSource( Module::SyncSource eSyncSource );

    /************************************************
     * 函数名   : setChartParameters
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 给图谱设置参数
     ************************************************/
    void setChartParameters();

    /************************************************
     * 函数名   : setAllWorkSets
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 设置所有的工作参数
     ************************************************/
    void setAllWorkSets();

private slots:
    /************************************************
     * 函数名   : onDataRead
     * 输入参数 : stData: 采样数据
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，处理收到的采样数据
     ************************************************/
    virtual void onDataRead(TEV::PRPSData stData,MultiServiceNS::USERID userId) = 0;

    /************************************************
     * 函数名   : onSignalChanged
     * 输入参数 : eState: 信号状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，信号状态的改变
     ************************************************/
    virtual void onSignalChanged(Module::SignalState eState) = 0;

    /************************************************
     * 函数名   : onSyncStateChanged
     * 输入参数 : eState: 同步状态
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 槽函数，同步状态的改变
     ************************************************/
    virtual void onSyncStateChanged(Module::SyncState eState) = 0;

private:
    /*************************************************
    功能： 初始化数据
    *************************************************************/
    void initData();

    /************************************************
     * 函数名   : createChart
     * 输入参数 : parent---父控件
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 创建图谱
     ************************************************/
    virtual ChartWidget* createChart(QWidget *parent);


protected:
    enum {
        CHART_HEIGHT = 380, //TEV prps 图谱高度
        DEFAULT_DISCHARG_RATIO = 50 //PRPD2D放电量严重程度百分比
    };

    typedef enum _State
    {
        STATE_NONE = -1,    //无效状态
        STATE_READY,     //就绪状态
        STATE_SAMPLING,     //采样状态
    }State;
};

#endif // TEVPRPSVIEWBASE_H
