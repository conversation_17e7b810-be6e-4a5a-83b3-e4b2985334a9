/*
 * Copyright (c) 2019.11，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：aespectrumdefine.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/11/08
 * 摘要：ae图谱数据定义
 * 当前版本：1.0
 */

#pragma once

#include <QString>
#include <vector>
#include "dataspecification_def.h"

namespace DataSpecificationNS
{
    struct AEAmpExtInformation
    {
        AmpUnit eAmpUnit;                       //幅值单位
        float fAmpLowerLimit;                   //幅值下限
        float fAmpUpperLimit;                   //幅值上限
        AESensorType eAESensorType;             //超声传感器类型
        qint32 iDataPoint;                      //数据点个数
        float fSystemFrequency;                 //系统频率
        quint8 aucPDTypeProbability[8];         //放电类型概率
        FrequencyBand eFrequencyBand;           //频带
        float fFrequencyLowerLimit;             //下限频率
        float fFequencyUpperLimit;              //上限频率
        QString qstrBGFileName;                 //背景文件名称
        float fTriggerThreshold;                //触发幅值
        qint16 sOpenTime;                       //开门时间,单位ms
        qint16 sShutTime;                       //关门时间,单位us
        qint16 sMaxIntervalTime;                //最大间隔时间,单位ms
        qint16 sGain;                           //增益
        SyncSource eSyncSource;                 //同步源
        quint8 ucSyncState;                     //同步状态,失败：0x00 成功：0x01
        float fSyncFrequency;                   //同步频率，如果没有同步频率，则存-1
        DischargeSeverity eDischargeSeverity;                 //报警等级

        AEAmpExtInformation()
        {
            eAmpUnit = AMP_UNIT_DEFAULT;
            fAmpLowerLimit = 0;
            fAmpUpperLimit = 0;
            eAESensorType = AE_SENSOR_DEFAULT;
            iDataPoint = 0;
            fSystemFrequency = 0;
            memset(aucPDTypeProbability, 0, sizeof(aucPDTypeProbability));
            eFrequencyBand = BAND_UNRECORD;
            fFrequencyLowerLimit = 0;
            fFequencyUpperLimit = 0;
            fTriggerThreshold = 0;
            sOpenTime = 0;
            sShutTime = 0;
            sMaxIntervalTime = 0;
            sGain = 60;
            eSyncSource = SYNC_SOURCE_DEFAULT;
            ucSyncState = 0;
            fSyncFrequency = -1;
            eDischargeSeverity = DISCHARGE_SEVERITY_LEVEL_0;
        }

        AEAmpExtInformation& operator=(const AEAmpExtInformation& stAEAmpExtInformation)
        {
            this->eAmpUnit = stAEAmpExtInformation.eAmpUnit;
            this->fAmpLowerLimit = stAEAmpExtInformation.fAmpLowerLimit;
            this->fAmpUpperLimit = stAEAmpExtInformation.fAmpUpperLimit;
            this->eAESensorType = stAEAmpExtInformation.eAESensorType;
            this->iDataPoint = stAEAmpExtInformation.iDataPoint;
            this->fSystemFrequency = stAEAmpExtInformation.fSystemFrequency;
            memcpy(this->aucPDTypeProbability, stAEAmpExtInformation.aucPDTypeProbability, sizeof(stAEAmpExtInformation.aucPDTypeProbability));
            this->eFrequencyBand = stAEAmpExtInformation.eFrequencyBand;
            this->fFrequencyLowerLimit = stAEAmpExtInformation.fFrequencyLowerLimit;
            this->fFequencyUpperLimit = stAEAmpExtInformation.fFequencyUpperLimit;
            this->qstrBGFileName = stAEAmpExtInformation.qstrBGFileName;
            this->fTriggerThreshold = stAEAmpExtInformation.fTriggerThreshold;
            this->sOpenTime = stAEAmpExtInformation.sOpenTime;
            this->sShutTime = stAEAmpExtInformation.sShutTime;
            this->sMaxIntervalTime = stAEAmpExtInformation.sMaxIntervalTime;
            this->sGain = stAEAmpExtInformation.sGain;
            this->eSyncSource = stAEAmpExtInformation.eSyncSource;
            this->ucSyncState = stAEAmpExtInformation.ucSyncState;
            this->fSyncFrequency = stAEAmpExtInformation.fSyncFrequency;
            this->eDischargeSeverity = stAEAmpExtInformation.eDischargeSeverity;
            return *this;
        }

        bool operator==(const AEAmpExtInformation& stAEAmpExtInformation) const
        {
            if (!(this->eAmpUnit == stAEAmpExtInformation.eAmpUnit)) return false;
            if (!qFuzzyCompare(this->fAmpLowerLimit, stAEAmpExtInformation.fAmpLowerLimit)) return false;
            if (!qFuzzyCompare(this->fAmpUpperLimit, stAEAmpExtInformation.fAmpUpperLimit)) return false;
            if (!(this->eAESensorType == stAEAmpExtInformation.eAESensorType)) return false;
            if (!(this->iDataPoint == stAEAmpExtInformation.iDataPoint)) return false;
            if (!qFuzzyCompare(this->fSystemFrequency, stAEAmpExtInformation.fSystemFrequency)) return false;
            if (!(this->aucPDTypeProbability == stAEAmpExtInformation.aucPDTypeProbability)) return false;
            if (!(this->eFrequencyBand == stAEAmpExtInformation.eFrequencyBand)) return false;
            if (!qFuzzyCompare(this->fFrequencyLowerLimit, stAEAmpExtInformation.fFrequencyLowerLimit)) return false;
            if (!qFuzzyCompare(this->fFequencyUpperLimit, stAEAmpExtInformation.fFequencyUpperLimit)) return false;
            if (!(this->qstrBGFileName == stAEAmpExtInformation.qstrBGFileName)) return false;
            if (!qFuzzyCompare(this->fTriggerThreshold, stAEAmpExtInformation.fTriggerThreshold)) return false;
            if (!(this->sOpenTime == stAEAmpExtInformation.sOpenTime)) return false;
            if (!(this->sShutTime == stAEAmpExtInformation.sShutTime)) return false;
            if (!(this->sMaxIntervalTime == stAEAmpExtInformation.sMaxIntervalTime)) return false;
            if (!(this->sGain == stAEAmpExtInformation.sGain)) return false;
            if (!(this->eSyncSource == stAEAmpExtInformation.eSyncSource)) return false;
            if (!(this->ucSyncState == stAEAmpExtInformation.ucSyncState)) return false;
            if (!qFuzzyCompare(this->fSyncFrequency, stAEAmpExtInformation.fSyncFrequency)) return false;
            if (!(this->eDischargeSeverity == stAEAmpExtInformation.eDischargeSeverity)) return false;
            return true;
        }
    };

    struct AEAmpData
    {
        float fSignalMax;                       //信号最大值
        float fSignalRMS;                       //信号有效值
        float fFrequencyComponent1;             //频率分量1相关性
        float fFrequencyComponent2;             //频率分量2相关性
        float fBGSignalMax;                     //背景信号最大值
        float fBGSignalRMS;                     //背景信号有效值
        float fBGFrequencyComponent1;           //背景频率分量1相关性
        float fBGFrequencyComponent2;           //背景频率分量2相关性

        AEAmpData()
        {
            fSignalMax = 0;
            fSignalRMS = 0;
            fFrequencyComponent1 = 0;
            fFrequencyComponent2 = 0;
            fBGSignalMax = 0;
            fBGSignalRMS = 0;
            fBGFrequencyComponent1 = 0;
            fBGFrequencyComponent2 = 0;
        }

        AEAmpData& operator=(const AEAmpData& stAEAmpData)
        {
            this->fSignalMax = stAEAmpData.fSignalMax;
            this->fSignalRMS = stAEAmpData.fSignalRMS;
            this->fFrequencyComponent1 = stAEAmpData.fFrequencyComponent1;
            this->fFrequencyComponent2 = stAEAmpData.fFrequencyComponent2;
            this->fBGSignalMax = stAEAmpData.fBGSignalMax;
            this->fBGSignalRMS = stAEAmpData.fBGSignalRMS;
            this->fBGFrequencyComponent1 = stAEAmpData.fBGFrequencyComponent1;
            this->fBGFrequencyComponent2 = stAEAmpData.fBGFrequencyComponent2;
            return *this;
        }

        bool operator==(const AEAmpData& stAEAmpData) const
        {
            if (!qFuzzyCompare(this->fSignalMax, stAEAmpData.fSignalMax)) return false;
            if (!qFuzzyCompare(this->fSignalRMS, stAEAmpData.fSignalRMS)) return false;
            if (!qFuzzyCompare(this->fFrequencyComponent1, stAEAmpData.fFrequencyComponent1)) return false;
            if (!qFuzzyCompare(this->fFrequencyComponent2, stAEAmpData.fFrequencyComponent2)) return false;
            if (!qFuzzyCompare(this->fBGSignalMax, stAEAmpData.fBGSignalMax)) return false;
            if (!qFuzzyCompare(this->fBGSignalRMS, stAEAmpData.fBGSignalRMS)) return false;
            if (!qFuzzyCompare(this->fBGFrequencyComponent1, stAEAmpData.fBGFrequencyComponent1)) return false;
            if (!qFuzzyCompare(this->fBGFrequencyComponent2, stAEAmpData.fBGFrequencyComponent2)) return false;
            return true;
        }
    };

    struct AEPhaseExtInformation
    {
        AmpUnit eAmpUnit;                       //幅值单位
        float fAmpLowerLimit;                   //幅值下限
        float fAmpUpperLimit;                   //幅值上限
        AESensorType eAESensorType;             //超声传感器类型
        qint32 iDataPoint;                      //数据点个数
        quint8 aucPDTypeProbability[8];         //放电类型概率
        float fTriggerThreshold;                //触发幅值
        qint16 sOpenTime;                       //开门时间,单位ms
        qint16 sShutTime;                       //关门时间,单位us
        qint16 sMaxIntervalTime;                //最大间隔时间,单位ms
        qint16 sGain;                           //增益
        SyncSource eSyncSource;                 //同步源
        quint8 ucSyncState;                     //同步状态,失败：0x00 成功：0x01
        float fSyncFrequency;                   //同步频率，如果没有同步频率，则存-1
        DischargeSeverity eDischargeSeverity;                 //报警等级

        AEPhaseExtInformation()
        {
            eAmpUnit = AMP_UNIT_DEFAULT;
            fAmpLowerLimit = 0;
            fAmpUpperLimit = 0;
            eAESensorType = AE_SENSOR_DEFAULT;
            iDataPoint = 0;
            memset(aucPDTypeProbability, 0, sizeof(aucPDTypeProbability));
            fTriggerThreshold = 0;
            sOpenTime = 0;
            sShutTime = 0;
            sMaxIntervalTime = 0;
            sGain = 60;
            eSyncSource = SYNC_SOURCE_DEFAULT;
            ucSyncState = 0;
            fSyncFrequency = -1;
            eDischargeSeverity = DISCHARGE_SEVERITY_LEVEL_0;
        }

        AEPhaseExtInformation& operator=(const AEPhaseExtInformation& stAEPhaseExtInformation)
        {
            this->eAmpUnit = stAEPhaseExtInformation.eAmpUnit;
            this->fAmpLowerLimit = stAEPhaseExtInformation.fAmpLowerLimit;
            this->fAmpUpperLimit = stAEPhaseExtInformation.fAmpUpperLimit;
            this->eAESensorType = stAEPhaseExtInformation.eAESensorType;
            this->iDataPoint = stAEPhaseExtInformation.iDataPoint;
            memcpy(this->aucPDTypeProbability, stAEPhaseExtInformation.aucPDTypeProbability, sizeof(stAEPhaseExtInformation.aucPDTypeProbability));
            this->fTriggerThreshold = stAEPhaseExtInformation.fTriggerThreshold;
            this->sOpenTime = stAEPhaseExtInformation.sOpenTime;
            this->sShutTime = stAEPhaseExtInformation.sShutTime;
            this->sMaxIntervalTime = stAEPhaseExtInformation.sMaxIntervalTime;
            this->sGain = stAEPhaseExtInformation.sGain;
            this->eSyncSource = stAEPhaseExtInformation.eSyncSource;
            this->ucSyncState = stAEPhaseExtInformation.ucSyncState;
            this->fSyncFrequency = stAEPhaseExtInformation.fSyncFrequency;
            this->eDischargeSeverity = stAEPhaseExtInformation.eDischargeSeverity;
            return *this;
        }

        bool operator==(const AEPhaseExtInformation& stAEPhaseExtInformation) const
        {
            if (!(this->eAmpUnit == stAEPhaseExtInformation.eAmpUnit)) return false;
            if (!qFuzzyCompare(this->fAmpLowerLimit, stAEPhaseExtInformation.fAmpLowerLimit)) return false;
            if (!qFuzzyCompare(this->fAmpUpperLimit, stAEPhaseExtInformation.fAmpUpperLimit)) return false;
            if (!(this->eAESensorType == stAEPhaseExtInformation.eAESensorType)) return false;
            if (!(this->iDataPoint == stAEPhaseExtInformation.iDataPoint)) return false;
            if (!(this->aucPDTypeProbability == stAEPhaseExtInformation.aucPDTypeProbability)) return false;
            if (!qFuzzyCompare(this->fTriggerThreshold, stAEPhaseExtInformation.fTriggerThreshold)) return false;
            if (!(this->sOpenTime == stAEPhaseExtInformation.sOpenTime)) return false;
            if (!(this->sShutTime == stAEPhaseExtInformation.sShutTime)) return false;
            if (!(this->sMaxIntervalTime == stAEPhaseExtInformation.sMaxIntervalTime)) return false;
            if (!(this->sGain == stAEPhaseExtInformation.sGain)) return false;
            if (!(this->eSyncSource == stAEPhaseExtInformation.eSyncSource)) return false;
            if (!(this->ucSyncState == stAEPhaseExtInformation.ucSyncState)) return false;
            if (!qFuzzyCompare(this->fSyncFrequency, stAEPhaseExtInformation.fSyncFrequency)) return false;
            if (!(this->eDischargeSeverity == stAEPhaseExtInformation.eDischargeSeverity)) return false;
            return true;
        }
    };

    struct AEPhaseData
    {
        QByteArray qbaAEPhaseData;

        AEPhaseData()
        {

        }

        AEPhaseData& operator=(const AEPhaseData& stAEPhaseData)
        {
            this->qbaAEPhaseData = stAEPhaseData.qbaAEPhaseData;
            return *this;
        }

        bool operator==(const AEPhaseData& stAEPhaseData) const
        {
            if (!(this->qbaAEPhaseData == stAEPhaseData.qbaAEPhaseData)) return false;
            return true;
        }
    };


    struct AEPulseExtInformation
    {
        AmpUnit eAmpUnit;                       //幅值单位
        float fAmpLowerLimit;                   //幅值下限
        float fAmpUpperLimit;                   //幅值上限
        AESensorType eAESensorType;             //超声传感器类型
        PulseIntervalUnit ePulseIntervalUnit;   //超声时间单位
        qint32 iDataPoint;                      //数据点个数
        quint8 aucPDTypeProbability[8];         //放电类型概率
        float fTriggerThreshold;                //触发幅值
        qint16 sOpenTime;                       //开门时间,单位ms
        qint16 sShutTime;                       //关门时间,单位us
        qint16 sMaxIntervalTime;                //最大间隔时间,单位ms
        qint16 sGain;                           //增益
        SyncSource eSyncSource;                 //同步源
        quint8 ucSyncState;                     //同步状态,失败：0x00 成功：0x01
        float fSyncFrequency;                   //同步频率，如果没有同步频率，则存-1
        DischargeSeverity eDischargeSeverity;                 //报警等级

        AEPulseExtInformation()
        {
            eAmpUnit = AMP_UNIT_DEFAULT;
            fAmpLowerLimit = 0;
            fAmpUpperLimit = 0;
            eAESensorType = AE_SENSOR_DEFAULT;
            ePulseIntervalUnit = PULSE_UNIT_DEFAULT;
            iDataPoint = 0;
            memset(aucPDTypeProbability, 0, sizeof(aucPDTypeProbability));
            fTriggerThreshold = 0;
            sOpenTime = 0;
            sShutTime = 0;
            sMaxIntervalTime = 0;
            sGain = 60;
            eSyncSource = SYNC_SOURCE_DEFAULT;
            ucSyncState = 0;
            fSyncFrequency = -1;
            eDischargeSeverity = DISCHARGE_SEVERITY_LEVEL_0;
        }

        AEPulseExtInformation& operator=(const AEPulseExtInformation& stAEPulseExtInformation)
        {
            this->eAmpUnit = stAEPulseExtInformation.eAmpUnit;
            this->fAmpLowerLimit = stAEPulseExtInformation.fAmpLowerLimit;
            this->fAmpUpperLimit = stAEPulseExtInformation.fAmpUpperLimit;
            this->eAESensorType = stAEPulseExtInformation.eAESensorType;
            this->ePulseIntervalUnit = stAEPulseExtInformation.ePulseIntervalUnit;
            this->iDataPoint = stAEPulseExtInformation.iDataPoint;
            memcpy(this->aucPDTypeProbability, stAEPulseExtInformation.aucPDTypeProbability, sizeof(stAEPulseExtInformation.aucPDTypeProbability));
            this->fTriggerThreshold = stAEPulseExtInformation.fTriggerThreshold;
            this->sOpenTime = stAEPulseExtInformation.sOpenTime;
            this->sShutTime = stAEPulseExtInformation.sShutTime;
            this->sMaxIntervalTime = stAEPulseExtInformation.sMaxIntervalTime;
            this->sGain = stAEPulseExtInformation.sGain;
            this->eSyncSource = stAEPulseExtInformation.eSyncSource;
            this->ucSyncState = stAEPulseExtInformation.ucSyncState;
            this->fSyncFrequency = stAEPulseExtInformation.fSyncFrequency;
            this->eDischargeSeverity = stAEPulseExtInformation.eDischargeSeverity;
            return *this;
        }

        bool operator==(const AEPulseExtInformation& stAEPulseExtInformation) const
        {
            if (!(this->eAmpUnit == stAEPulseExtInformation.eAmpUnit)) return false;
            if (!qFuzzyCompare(this->fAmpLowerLimit, stAEPulseExtInformation.fAmpLowerLimit)) return false;
            if (!qFuzzyCompare(this->fAmpUpperLimit, stAEPulseExtInformation.fAmpUpperLimit)) return false;
            if (!(this->eAESensorType == stAEPulseExtInformation.eAESensorType)) return false;
            if (!(this->ePulseIntervalUnit == stAEPulseExtInformation.ePulseIntervalUnit)) return false;
            if (!(this->iDataPoint == stAEPulseExtInformation.iDataPoint)) return false;
            if (!(this->aucPDTypeProbability == stAEPulseExtInformation.aucPDTypeProbability)) return false;
            if (!qFuzzyCompare(this->fTriggerThreshold, stAEPulseExtInformation.fTriggerThreshold)) return false;
            if (!(this->sOpenTime == stAEPulseExtInformation.sOpenTime)) return false;
            if (!(this->sShutTime == stAEPulseExtInformation.sShutTime)) return false;
            if (!(this->sMaxIntervalTime == stAEPulseExtInformation.sMaxIntervalTime)) return false;
            if (!(this->sGain == stAEPulseExtInformation.sGain)) return false;
            if (!(this->eSyncSource == stAEPulseExtInformation.eSyncSource)) return false;
            if (!(this->ucSyncState == stAEPulseExtInformation.ucSyncState)) return false;
            if (!qFuzzyCompare(this->fSyncFrequency, stAEPulseExtInformation.fSyncFrequency)) return false;
            if (!(this->eDischargeSeverity == stAEPulseExtInformation.eDischargeSeverity)) return false;
            return true;
        }
    };

    struct AEPulseData
    {
        QByteArray qbaAEPulseData;

        AEPulseData()
        {

        }

        AEPulseData& operator=(const AEPulseData& stAEPulseData)
        {
            this->qbaAEPulseData = stAEPulseData.qbaAEPulseData;
            return *this;
        }

        bool operator==(const AEPulseData& stAEPulseData) const
        {
            if (!(this->qbaAEPulseData == stAEPulseData.qbaAEPulseData)) return false;
            return true;
        }
    };


    struct AEWaveExtInformation
    {
        AmpUnit eAmpUnit;                       //幅值单位
        float fAmpLowerLimit;                   //幅值下限
        float fAmpUpperLimit;                   //幅值上限
        AESensorType eAESensorType;             //超声传感器类型
        qint32 iDataPoint;                      //数据点个数
        qint64 llSampleRate;                    //采样率
        quint8 aucPDTypeProbability[8];         //放电类型概率
        float fTriggerThreshold;                //触发幅值
        qint16 sOpenTime;                       //开门时间,单位ms
        qint16 sShutTime;                       //关门时间,单位us
        qint16 sMaxIntervalTime;                //最大间隔时间,单位ms
        qint16 sGain;                           //增益
        SyncSource eSyncSource;                 //同步源
        quint8 ucSyncState;                     //同步状态,失败：0x00 成功：0x01
        float fSyncFrequency;                   //同步频率，如果没有同步频率，则存-1
        DischargeSeverity eDischargeSeverity;                 //报警等级

        AEWaveExtInformation()
        {
            eAmpUnit = AMP_UNIT_DEFAULT;
            fAmpLowerLimit = 0;
            fAmpUpperLimit = 0;
            eAESensorType = AE_SENSOR_DEFAULT;
            iDataPoint = 0;
            llSampleRate = 0;
            memset(aucPDTypeProbability, 0, sizeof(aucPDTypeProbability));
            fTriggerThreshold = 0;
            sOpenTime = 0;
            sShutTime = 0;
            sMaxIntervalTime = 0;
            sGain = 60;
            eSyncSource = SYNC_SOURCE_DEFAULT;
            ucSyncState = 0;
            fSyncFrequency = -1;
            eDischargeSeverity = DISCHARGE_SEVERITY_LEVEL_0;
        }

        AEWaveExtInformation& operator=(const AEWaveExtInformation& stAEWaveExtInformation)
        {
            this->eAmpUnit = stAEWaveExtInformation.eAmpUnit;
            this->fAmpLowerLimit = stAEWaveExtInformation.fAmpLowerLimit;
            this->fAmpUpperLimit = stAEWaveExtInformation.fAmpUpperLimit;
            this->eAESensorType = stAEWaveExtInformation.eAESensorType;
            this->iDataPoint = stAEWaveExtInformation.iDataPoint;
            this->llSampleRate = stAEWaveExtInformation.llSampleRate;
            memcpy(this->aucPDTypeProbability, stAEWaveExtInformation.aucPDTypeProbability, sizeof(stAEWaveExtInformation.aucPDTypeProbability));
            this->fTriggerThreshold = stAEWaveExtInformation.fTriggerThreshold;
            this->sOpenTime = stAEWaveExtInformation.sOpenTime;
            this->sShutTime = stAEWaveExtInformation.sShutTime;
            this->sMaxIntervalTime = stAEWaveExtInformation.sMaxIntervalTime;
            this->sGain = stAEWaveExtInformation.sGain;
            this->eSyncSource = stAEWaveExtInformation.eSyncSource;
            this->ucSyncState = stAEWaveExtInformation.ucSyncState;
            this->fSyncFrequency = stAEWaveExtInformation.fSyncFrequency;
            this->eDischargeSeverity = stAEWaveExtInformation.eDischargeSeverity;
            return *this;
        }

        bool operator==(const AEWaveExtInformation& stAEWaveExtInformation) const
        {
            if (!(this->eAmpUnit == stAEWaveExtInformation.eAmpUnit)) return false;
            if (!qFuzzyCompare(this->fAmpLowerLimit, stAEWaveExtInformation.fAmpLowerLimit)) return false;
            if (!qFuzzyCompare(this->fAmpUpperLimit, stAEWaveExtInformation.fAmpUpperLimit)) return false;
            if (!(this->eAESensorType == stAEWaveExtInformation.eAESensorType)) return false;
            if (!(this->iDataPoint == stAEWaveExtInformation.iDataPoint)) return false;
            if (!(this->llSampleRate == stAEWaveExtInformation.llSampleRate)) return false;
            if (!(this->aucPDTypeProbability == stAEWaveExtInformation.aucPDTypeProbability)) return false;
            if (!qFuzzyCompare(this->fTriggerThreshold, stAEWaveExtInformation.fTriggerThreshold)) return false;
            if (!(this->sOpenTime == stAEWaveExtInformation.sOpenTime)) return false;
            if (!(this->sShutTime == stAEWaveExtInformation.sShutTime)) return false;
            if (!(this->sMaxIntervalTime == stAEWaveExtInformation.sMaxIntervalTime)) return false;
            if (!(this->sGain == stAEWaveExtInformation.sGain)) return false;
            if (!(this->eSyncSource == stAEWaveExtInformation.eSyncSource)) return false;
            if (!(this->ucSyncState == stAEWaveExtInformation.ucSyncState)) return false;
            if (!qFuzzyCompare(this->fSyncFrequency, stAEWaveExtInformation.fSyncFrequency)) return false;
            if (!(this->eDischargeSeverity == stAEWaveExtInformation.eDischargeSeverity)) return false;
            return true;
        }
    };

    struct AEWaveData
    {
        QByteArray qbaAEWaveData;

        AEWaveData()
        {

        }

        AEWaveData& operator=(const AEWaveData& stAEWaveData)
        {
            this->qbaAEWaveData = stAEWaveData.qbaAEWaveData;
            return *this;
        }

        bool operator==(const AEWaveData& stAEWaveData) const
        {
            if (!(this->qbaAEWaveData == stAEWaveData.qbaAEWaveData)) return false;
            return true;
        }
    };
}
