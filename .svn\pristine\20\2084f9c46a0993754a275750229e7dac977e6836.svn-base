#将此文件放入 /usr/bin/ 目录
#!/bin/sh
# check if firmware upgrade success，this script is
# only for board Z111.
# this script is a part of updatefirmware process.
# Do NOT execute this script manurually.
# please refer to updatefirmware.c file for detailed info.
################################################################
if [ -e "/var/run/matrix-gui-2.0.pid" ];then
	    TARGET_PROGRAM_PID="/proc/$(cat /var/run/matrix-gui-2.0.pid)"
		if [ -d $TARGET_PROGRAM_PID ] && [ "$(cat $TARGET_PROGRAM_PID/comm)"x = "G1500"x ]; then
			exit 0;
		else
			exit 1;
		fi
	fi
exit 1;