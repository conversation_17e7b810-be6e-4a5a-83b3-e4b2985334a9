---
description: 
globs: 
alwaysApply: false
---
# 数据规范模块指南

## 模块概述
数据规范（DataSpecification）模块负责处理不同类型的数据格式、转换和处理。这是一个核心基础模块，为其他模块提供数据处理功能。

## 主要组件
- [dataspecification.h](mdc:dataspecification/dataspecification.h) - 数据规范主类
- [dataspecification.cpp](mdc:dataspecification/dataspecification.cpp) - 数据规范实现
- [dataspecification_def.h](mdc:dataspecification/dataspecification_def.h) - 数据规范定义
- [spectrum.h](mdc:dataspecification/spectrum.h) - 频谱处理
- [spectrum.cpp](mdc:dataspecification/spectrum.cpp) - 频谱处理实现

## 子模块
- `common/` - 通用功能
- `tev/` - TEV（瞬态对地电压）数据处理
- `ae/` - AE（声发射）数据处理
- `infrared/` - 红外数据处理
- `prps/` - PRPS（局部放电相位分辨功率谱）数据处理
- `mechanical/` - 机械数据处理
- `currentamplitude/` - 电流幅值数据处理

## 数据文件格式
数据文件格式由 [spectrumdatafiledefine.h](mdc:dataspecification/spectrumdatafiledefine.h) 定义，包括：
- 文件头格式
- 数据块结构
- 元数据定义

## 使用方法
要使用数据规范模块，需要：
1. 包含相应的头文件
2. 创建 DataSpecification 实例
3. 调用相应的方法处理数据

示例：
```cpp
#include "dataspecification.h"

// 创建实例
DataSpecification dataSpec;

// 处理数据
dataSpec.processData(...);
```

## 扩展指南
添加新的数据类型支持：
1. 在相应子目录创建新的类
2. 实现必要的接口
3. 在 dataspecification_def.h 中添加新的类型定义
4. 在 DataSpecification 类中注册新的处理器

