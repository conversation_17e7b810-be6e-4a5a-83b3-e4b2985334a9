/*
* Copyright (c) 2016.03，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: AEAmpDataSave.h
*
* 初始版本：1.0
* 作者：曹山
* 创建日期：2016年03月15日
* 摘要：该文件主要是定义了AE幅值检测数据存储的子类
*/

#ifndef AEAMPDATASAVE_H
#define AEAMPDATASAVE_H

#include "dataSave/DataSave.h"
#include "ae/AE.h"
#include "module_global.h"
#include "ae/aemapdefine.h"
#include "ae/aeampdatamap.h"
//AE幅值数据
typedef struct _AEAmpDataApp
{
    float fRms;                        //有效值，单位mV，default (0)
    float fPeak;                       //周期最大值，单位mV，default (0)
    float fFrequency1;                 //频率成分1，单位mV，default (0)
    float fFrequency2;                 //频率成分2，单位mV，default (0)

    float fRmsBGN;                     //背景噪声有效值，单位mV，default (0)
    float fPeakBGN;                    //背景噪声周期最大值，单位mV，default (0)
    float fFrequency1BGN;              //背景噪声频率成分1，单位mV，default (0)
    float fFrequency2BGN;              //背景噪声频率成分2，单位mV，default (0)   
}AEAmpDataApp;

//AE幅值检测数据信息
typedef struct _AEAmpDataInfo
{
    /************************************************
     * 功能     : 设置量纲（并完成枚举和存储类型转换）
     ************************************************/
    void setUnit( AE::UnitOption eUnit )
    {
        if( eUnit == AE::UNIT_DB )
        {
            eAmpUnit = DataFileNS::AMP_UNIT_DB;
        }
        else if( eUnit == AE::UNIT_MV )
        {
            eAmpUnit = DataFileNS::AMP_UNIT_mV;
        }
    }

    /************************************************
     * 功能     : 设置增益（并完成枚举和存储类型转换）
     ************************************************/
    void setGain( AE::GainType eGain )
    {
        if( eGain == AE::GAIN_X1 )
        {
            sGain = GAIN_SIXTY ;
        }
        else if( eGain == AE::GAIN_X10 )
        {
            sGain = GAIN_EIGHTY ;
        }
        else if( eGain == AE::GAIN_X100 )
        {
            sGain = GAIN_HUNDRED;
        }
    }

    AE::GainType gain()
    {
         AE::GainType eGain;

         if( AE::g_ausGainValues[AE::GAIN_X1]  == sGain )
         {
             eGain = AE::GAIN_X1;
         }
         else if( AE::g_ausGainValues[AE::GAIN_X10]  == sGain )
         {
             eGain = AE::GAIN_X10;
         }
         else
         {
             eGain = AE::GAIN_X100;
         }
         return eGain;
    }

    AE::UnitOption unit()
    {
        AE::UnitOption eUnit = AE::UNIT_DB;

        if( eAmpUnit == DataFileNS::AMP_UNIT_mV )
        {
            eUnit = AE::UNIT_MV;
        }
        return eUnit;
    }
    DataMapHead stHeadInfo;            //图谱通用的头部信

    DataFileNS::AmpUnit eAmpUnit; //幅值单位

    float fPeakMin;//幅值范围下限
    float fPeakMax;//幅值范围上限
    float fRMSMin;//有效值范围下限
    float fRMSMax;//有效值范围上限
    float fFreq1Min;//频率成分一范围下限
    float fFreq1Max;//频率成分一范围上限
    float fFreq2Min;//频率成分二范围下限
    float fFreq2Max;//频率成分二范围上限

    AEMapNS::AETransformerType eTransformerType;//超声传感器类型
    qint32 iDataPointNum;//数据点个数
    quint8 ucaDischargeTypeProb[8];//放电类型概率
//    float fTrigAmp;//触发幅值
//    quint16 usShutTime;//关门时间,单位us
    qint16 sGain;//增益,  x1；x10；x100
    DataFileNS::GainType eGainType; //增益种类
//    DataFileNS::GainFactor eGainFactor;//放大器放大倍数
    DataFileNS::SyncSource eSyncSource;//同步源
    quint8 ucSyncState;//同步状态,失败：0x00 成功：0x01
    float fSyncFreq;//标识测试系统频率，如果没有同步频率，则存-1

    AEAmpDataApp stAEAmpData;             //数据段
    _AEAmpDataInfo()
    {
        eAmpUnit = DataFileNS::AMP_UNIT_DEFAULT;
        fPeakMin = 0;
        fPeakMax = 0;
        fRMSMin = 0;
        fRMSMax = 0;
        fFreq1Min = 0;
        fFreq1Max = 0;
        fFreq2Min = 0;
        fFreq2Max = 0;
        eTransformerType = AEMapNS::AE_TRANSFORMER_DEFAULT;
        iDataPointNum = 8;
        memset( ucaDischargeTypeProb, 0, sizeof(ucaDischargeTypeProb) );
//        fTrigAmp = 0;
//        usShutTime = 0;

        eGainType = DataFileNS::GAIN_TYPE_DEFAULT;
        sGain = GAIN_HUNDRED;
//        eGainFactor = DataFileNS::GAIN_FACTOR_DEFAULT;
        eSyncSource = DataFileNS::SYNC_SOURCE_POWER;
        ucSyncState = 0;
        fSyncFreq = -1;
    }

}AEAmpDataInfo;

class MODULESHARED_EXPORT AEAmpDataSave : public DataSave
{
public:
    /************************************************
     * 函数名   : AEAmpDataSave
     * 输入参数 : NULL
     * 输出参数 : NULL
     * 返回值   : NULL
     * 功能     : 构造函数
     ************************************************/
    AEAmpDataSave();

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData);

    /************************************************
     * 函数名   : saveData
     * 输入参数 : pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 数据文件名
     * 功能     : 保存数据到指定格式数据文件
     ************************************************/
    QString saveData(void *pData, const QString &qsSavedPath);

    /************************************************
     * 函数名   : getDataByPDA
     * 输入参数 : strFileName: 文件名; pDatas: 数据
     * 输出参数 : NULL
     * 返回值   : 获取结果
     * 功能     : 获取指定数据文件中的数据
     ************************************************/
    INT32 getDataByPDA(const QString& strFileName, void *pData);

protected:
    /************************************************
     * 函数名   : getDataTypeFolder
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件夹名
     * 功能     : 获取图谱类型文件存储最上层文件夹名
     ************************************************/
    QString getDataTypeFolder(void);

    /************************************************
     * 函数名   : getFileNameSuffix
     * 输入参数 : void
     * 输出参数 : NULL
     * 返回值   : 文件后缀名
     * 功能     : 获取数据文件后缀名
     ************************************************/
    QString getFileNameSuffix(void);

    /************************************************
     * 函数名   : getStringFromData
     * 输入参数 : pDatas: 数据; uiCounts: 数据个数
     * 输出参数 : NULL
     * 返回值   : 转换后的字符串
     * 功能     : 将数据转成base64的字符串
     ************************************************/
    QString getStringFromData( void *pDatas, UINT32 uiCounts);

    /************************************************
     * 函数名   : saveExtInformation
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储可扩展信息
     ************************************************/
    void saveExtInformation(XMLDocument& doc);

    /************************************************
     * 函数名   : saveRawData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 在XML文件中存储数据部分
     ************************************************/
    void saveRawData(XMLDocument& doc);


    /************************************************
     * 函数名   : organizeData
     * 输入参数 : doc: XML文件
     * 输出参数 : NULL
     * 返回值   : void
     * 功能     : 组织数据文件信息
     ************************************************/
     void organizeData(XMLDocument& doc);

     /************************************************
      * 函数名   : parseData
      * 输入参数 : baData: 数据
      * 输出参数 : pData: 解析到的数据
      * 返回值   : void
      * 功能     : 解析数据
      ************************************************/
     void parseData(const QByteArray& baData, void *pData, const QString& strFileName = "");

private:
     void setMapData(AEAmpDataMap *pMap);
     void setMapInfo(AEAmpDataMap *pMap);
     void setMapHead(DataMap *pMap);
     void addAEAmpMap(DataFile *pFile);
private:
    AEAmpDataInfo *m_pAEAmpDataInfo;     //AE幅值检测数据信息
};

#endif // AEAMPDATASAVE_H
