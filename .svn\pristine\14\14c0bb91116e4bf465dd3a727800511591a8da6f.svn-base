/*
 * Copyright (c) 2020.10，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：currentampspectrumprivatedefine.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2021/10/22
 * 摘要：电流幅值图谱数据私有定义
 * 当前版本：1.0
 */

#pragma once

#include <QString>
#include <QHash>

namespace DataSpecificationNS
{
    struct CurrentAmpExtInformationPrivate
    {
        friend class CurrentAmpSpectrum;

        quint8 ucAmpUnit;
        quint8 ucCurrentDataCount;

        void skipAmpUnit() { bSkipAmpUnit = true; }
        void skipCurrentDataCount() { bSkipCurrentDataCount = true; }

        bool hasAmpUnit() const { return bHasAmpUnit; }
        bool hasCurrentDataCount() const { return bHasCurrentDataCount; }

        CurrentAmpExtInformationPrivate();
        CurrentAmpExtInformationPrivate& operator=(const CurrentAmpExtInformationPrivate& stCurrentAmpExtInformationPrivate);
        bool operator==(const CurrentAmpExtInformationPrivate& stCurrentAmpExtInformationPrivate) const;

    private:
        bool bSkipAmpUnit;
        bool bSkipCurrentDataCount;

        bool bHasAmpUnit;
        bool bHasCurrentDataCount;
    };

    struct CurrentAmpDataPrivate
    {
        friend class CurrentAmpSpectrum;

        QHash<QString, float> currentDatas;

        void skipcurrentDatas() { bSkipcurrentDatas = true; }

        bool hascurrentDatas() const { return bHascurrentDatas; }

        CurrentAmpDataPrivate();
        CurrentAmpDataPrivate& operator=(const CurrentAmpDataPrivate& stCurrentAmpDataPrivate);
        bool operator==(const CurrentAmpDataPrivate& stCurrentAmpDataPrivate) const;

    private:
        bool bSkipcurrentDatas;

        bool bHascurrentDatas;
    };
}
