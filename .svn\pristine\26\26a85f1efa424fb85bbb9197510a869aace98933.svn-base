#include "prpsunionview.h"

/************************************************
 * 功能: 构造函数
 * 入参：parent -- 父控件指针
 *     iPeriodCount -- 周期个数
 *     iPhaseCount -- 每周期相位个数
 *     dMax -- 最大值
 *     dMin -- 最小值
 ************************************************/
PrpsUnionView::PrpsUnionView(int iDisplayedPeriodCnt, qint32 iPeriodCount, qint32 iPhaseCount, double dMax, double dMin, QWidget *parent) :
    PhaseAbstractView( iDisplayedPeriodCnt, iPeriodCount,iPhaseCount,dMax,dMin,parent)
{
    m_ucStep = 1;
}

void PrpsUnionView::setAdvanceStep( quint8 ucStep)
{
    m_ucStep = ucStep;
}

/****************************
输入参数:rawData:单周期原始数据
功能： 添加数据
业务逻辑：
        图谱推动的逻辑为没添加一次数据推动一周期数据
*****************************/
void PrpsUnionView::setData( const QVector< double > &rawData )
{
    if(0 < rawData.size())
    {
        Phase::ValueType* pData = new Phase::ValueType[static_cast<size_t>(rawData.size())];
        for(int i = 0, iSize = rawData.size(); i < iSize; ++i)
        {
            pData[i] = static_cast<Phase::ValueType>(rawData[i]);
        }

        PhaseData tmpData(pData, rawData.size(), phaseCount());
        model()->addData( tmpData );
        model()->advance( m_ucStep ); // 每添加一周期数据推动一次

        refreshRecvCnt();   //更新是否可以进行本地诊断的计数

        delete[] pData;
        pData = NULL;
    }

    return;
}
