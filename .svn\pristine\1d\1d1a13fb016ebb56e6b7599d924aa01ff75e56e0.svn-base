/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* SliderPushButton.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月7日
* 修改日期：2016年11月15日
* 作者：邵震宇
*       重构
* 摘要：Push型弹出式按钮，点击后弹出滑块栏

* 当前版本：1.0
*/
#ifndef SLIDERPUSHBUTTON_H
#define SLIDERPUSHBUTTON_H
#include "controlButton/SliderButton.h"
#include "popupWidget/PushSliderPopup.h"
#include "PushButton.h"
#include "widgetglobal.h"

class WIDGET_EXPORT SliderPushButton : public SliderButton
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        iMin:最小值
        iMax:最大值
        iStep:步长
        suffix:量纲
        parent: 父控件指针
    *************************************************************/
    explicit SliderPushButton( const QString& strTitle,
                           int iMin,
                           int iMax,
                           int iStep,
                           const QString& suffix,
                           QWidget *parent = 0 );

    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle:标题
        pDataInfos -- 制定数据
        iCount -- 个数
        suffix:量纲
        parent: 父控件指针
    *************************************************************/
    explicit SliderPushButton( const QString& strTitle,
                           const int *pDataInfos,
                           int iCount,
                           const QString& suffix,
                           QWidget *parent = 0 );
    /*************************************************
    函数名： ~SliderPushButton
    输入参数:NULL
    输出参数： NULL
    返回值： NULL
    功能： 析构函数
    *************************************************************/
    ~SliderPushButton();

    /*************************************************
    功能： 设置显示格式
    输入参数:
        mode -- 显示格式
    *************************************************************/
    void setMode( PushButton::Mode mode );

    /*************************************************
    功能： 设置标题
    输入参数:
        strTitle -- 标题
    *************************************************************/
    virtual void setTitle(const QString& strTitle);

    /*************************************************
    功能： 设置数值
    输入参数:
        strContent -- 值
    *************************************************************/
    virtual void setContent(const QString& strContent);
private:
    QString m_strSuffix;//量纲
};

#endif // SLIDERCONTROLBUTTON

