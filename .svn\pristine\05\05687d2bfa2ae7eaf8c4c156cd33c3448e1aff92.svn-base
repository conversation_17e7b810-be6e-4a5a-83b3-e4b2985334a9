/*
* Copyright (c) 2016.03，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: DataFileInfos.h
*
* 初始版本：1.0
* 作者：曹山
* 创建日期：2016年03月11日
* 摘要：该文件主要是定义了各数据文件存储路径、及文件后缀名
*/

#ifndef DATAFILEINFOS
#define DATAFILEINFOS

#include <QString>

#ifdef Q_OS_WIN
const QString DATA_STORAGE_PATH = "D:/test/SaveData";//"SaveData";
#else
const QString DATA_STORAGE_PATH = "/media/data/SavedData";   //数据文件根路径
#endif
const QString RECORD_FILE_PATH = DATA_STORAGE_PATH + "/record";

//手持设备巡检数据存储根路径
const QString PDA_DATA_STORAGE_ROOT_PATH = "/media/data/SavedData/TestTask";
const QString PDA_FAST_PATROL_DATA_STORAGE_ROOT_PATH = "/media/data/SavedData/FastPatrol";
const QString PDA_TASK_TEMP_FOLDER = "temptaskfile/";
const QString PDA_TASK_TEMP_INI_FOLDER = "tempinifile/";
const QString PDA_TASK_FILE_SUFFIX = ".t13";
const QString PDA_TASK_TEMP_INI_SUFFIX = ".ini";

const QString RECORD_FILE_EXTENSION = "*.wav";

const QString MICRO_RECORD_FOLDER = "record/micro";
const QString AE_RECORD_FOLDER = "record/ae";
const QString AE_RECORD_FOLDER_AMP = "AEAmplitudeRecord";
const QString AE_RECORD_FOLDER_PHASE = "AEPhaseRecord";
const QString AE_RECORD_FOLDER_FLY = "AEFlyRecord";
const QString AE_RECORD_FOLDER_WAVE = "AEWaveRecord";
const QString MICRO_RECORD_FILE_DIR_PATH = DATA_STORAGE_PATH + QString("/%1/").arg(MICRO_RECORD_FOLDER);
const QString AE_RECORD_FILE_DIR_PATH = DATA_STORAGE_PATH + QString("/%1/").arg(AE_RECORD_FOLDER);
const QString AE_RECORD_FILE_DIR_PATH_AMP = DATA_STORAGE_PATH + QString("/%1/").arg(AE_RECORD_FOLDER_AMP);
const QString AE_RECORD_FILE_DIR_PATH_PHASE = DATA_STORAGE_PATH + QString("/%1/").arg(AE_RECORD_FOLDER_PHASE);
const QString AE_RECORD_FILE_DIR_PATH_FLY = DATA_STORAGE_PATH + QString("/%1/").arg(AE_RECORD_FOLDER_FLY);
const QString AE_RECORD_FILE_DIR_PATH_WAVE = DATA_STORAGE_PATH + QString("/%1/").arg(AE_RECORD_FOLDER_WAVE);

const QString MICRO_RECORD_FILE_NAME_SURRFIX = ".wav";     //micro.wav
const QString AE_RECORD_FILE_NAME_SURRFIX = ".wav";   //ae.wav
const QString RECORD_FILE_NAME_MP3_SURRFIX = ".mp3";

const QString FILE_DATE_FORMAT = "yyyyMMdd_hhmmss";      //文件名时间格式
const QString FILE_DATE_FORMAT_NEW = "yyyyMMdd_hhmmsszzz";      //文件名时间格式
const QString BINARY_FILE_DATE_FORMAT = "yyyyMMddhhmmsszzz"; // 二进制文件名时间格式

const QString AE_WAVE_FOLDER = "AEWave";                     //AE波形检测文件夹
const QString AE_AMP_FOLDER = "AEAmplitude";                 //AE幅值检测文件夹
const QString AE_AMP_NO_MAP_FOLDER = "AEAmplitudeNoMap";     //无图谱AE幅值检测文件夹
const QString AE_PHASE_FOLDER = "AEPhase";                   //AE相位检测文件夹
const QString AE_FLIGHT_FOLDER = "AEFlight";                 //AE飞行检测文件夹
const QString UHF_AMP_FOLDER = "UHFAmplitude";               //UHF Amplitude 检测文件夹
const QString UHF_PERIOD_FOLDER = "UHFPeriodogram";          //UHF周期检测文件夹
const QString HFCT_AMP_FOLDER = "HFCTAmplitude";             //HFCTAmplitude 检测文件夹
const QString HFCT_PERIOD_FOLDER = "HFCTPeriodogram";        //HFCT周期检测文件夹
const QString UHF_PRPS_PRPD_FOLDER = "UHFPRPSAndPRPD";       //UHF PRPS 和PRPD检测文件夹
const QString HFCT_PRPS_PRPD_FOLDER = "HFCTPRPSAndPRPD";     //HFCT PRPS 和PRPD检测文件夹
const QString INFRARED_FOLDER = "Infrared";                  //红外检测文件夹
const QString TEST_TABLE_FOLDER = "DischargeTestTable";      //局放测试表(开关柜巡检)文件夹
const QString GIS_TEST_FOLDER = "GISTest";                   //GIS检测数据文件夹
const QString TEV_DESC_FOLDER = "TEVDescription";            //开关柜巡检(局放测试表)文件夹
const QString TEV_AMP_FOLDER = "TEVAmplitude";               //TEV幅值文件夹
const QString TEV_PRPS_FOLDER = "TEVPRPS";               //TEV PRPS文件夹
const QString SCREEN_SHOT_FOLDER = "ScreenShot";               //截屏文件夹
const QString TEV_PULSE_FOLDER = "TEVPulse";                 //TEV脉冲文件夹
const QString GIS_DESC_FOLDER = "GISDescription";            //GIS巡检文件夹
const QString SUBSTATION_FOLDER = "Substation";              //站点信息文件夹
const QString PHOTO_FOLDER = "Photos";              //照片文件文件夹
const QString AUDIO_FOLDER = "Audio";              //音频文件文件夹
const QString VEDIO_FOLDER = "Vedio";              //视频文件文件夹
const QString TEST_FOLDER = "Text";              //文本文件夹
const QString ACCESS_G100_WAVE_FOLDER = "CA02Wave";        //接入G100波形数据文件夹
const QString ACCESS_G100_PRPS_FOLDER = "CA02PRPS";        //接入G100PRPS数据文件夹
const QString ACCESS_G100_PULSE_FOLDER = "CA02Pulse";      //接入G100脉冲数据文件夹
const QString PDA_TEST_DATA_FOLDER = "TestTask";      //智能巡检任务文件夹
const QString CURRENT_DETECTION_FOLDER = "CurrentDetection";            //电流检测文件夹
const QString PDA_FAST_PATROL_DATA_FOLDER = "FastPatrol";      //智能巡检任务文件夹

const QString SUBSTATION_FILE_NAME_SUFFIX = ".Station";          //站点信息数据文件后缀名
const QString DESCRIPTION_PATROL_FILE_NAME_SUFFIX = ".SWDES";    //测试任务描述文件(开关柜巡检)数据文件后缀名
const QString INFRARED_PICTURE_FILE_NAME_SUFFIX = ".jpeg";       //红外图片文件后缀名
const QString JPG_FILE_NAME_SUFFIX = ".jpg";                     //图片文件后缀名
const QString PNG_FILE_NAME_SUFFIX = ".png";                      //tev 文件后缀名
const QString AE_WAVE_FILE_NAME_SUFFIX = ".t00";                 //AE波形检测数据文件后缀名
const QString AE_AMP_FILE_NAME_SUFFIX = ".t01";                  //AE幅值检测数据文件后缀名
const QString AE_PHASE_FILE_NAME_SUFFIX = ".t02";                //AE相位检测数据文件后缀名
const QString AE_FLIGHT_FILE_NAME_SUFFIX = ".t05";               //AE飞行检测数据文件后缀名
const QString TEST_TABLE_FILE_NAME_SUFFIX = ".t03";              //局放测试表数据(开关柜巡检)文件后缀名
const QString UHF_PERIOD_FILE_NAME_SUFFIX = ".t04";              //UHF周期检测数据文件后缀名
const QString HFCT_PERIOD_FILE_NAME_SUFFIX = ".t08";             //HFCT周期检测数据文件后缀名
const QString UHF_PRPS_PRPD_FILE_NAME_SUFFIX = ".t09";           //UHF PRPS和PRPD检测数据文件后缀名
const QString TEV_PRPS_PRPD_FILE_NAME_SUFFIX = ".t10";           //TEV PRPS和PRPD检测数据文件后缀名
const QString HFCT_PRPS_PRPD_FILE_NAME_SUFFIX = ".t11";          //HFCT PRPS和PRPD检测数据文件后缀名
const QString INFRARED_FILE_NAME_SUFFIX = ".t14";                //红外检测数据文件后缀名
const QString ACCESS_G100_WAVE_FILE_NAME_SUFFIX = ".t21";        //接入G100波形数据文件后缀名
const QString ACCESS_G100_PRPS_FILE_NAME_SUFFIX = ".t22";        //接入G100PRPS数据文件后缀名
const QString ACCESS_G100_PULSE_FILE_NAME_SUFFIX = ".t23";       //接入G100脉冲数据文件后缀名
const QString TEV_AMP_FILE_NAME_SUFFIX = ".t24";                //TEV幅值数据文件后缀名
const QString TEV_PULSE_FILE_NAME_SUFFIX = ".t25";              //TEV脉冲数据文件后缀名
const QString UHF_PRPS_RECORD_FILE_NAME_SUFFIX = ".t26";        //UHF PRPS录屏数据文件后缀名
const QString TEV_PRPS_RECORD_FILE_NAME_SUFFIX = ".t33";         //TEV PRPS录屏数据文件后缀名
const QString HFCT_PRPS_RECORD_FILE_NAME_SUFFIX = ".t27";       //HFCT PRPS录屏数据文件后缀名
const QString CA_PRPS_RECORD_FILE_NAME_SUFFIX = ".t28";         //CA PRPS录屏数据文件后缀名
const QString UHF_AMP_FILE_NAME_SUFFIX = ".t29";                //UHF幅值数据文件后缀名
const QString HFCT_AMP_FILE_NAME_SUFFIX = ".t30";               //HFCT幅值数据文件后缀名
const QString AE_AUDIO_FILE_NAME_SUFFIX = ".t31";               //噪声音频数据文件后缀名
const QString BG_AUDIO_FILE_NAME_SUFFIX = ".t32";               //背景音频数据文件后缀名
const QString CURRENT_DETECTION_FILE_NAME_SUFFIX = ".t34";              //电流检测数据文件后缀名
const QString BINARY_DATA_FILE_NAME_SUFFIX = ".dat";            //二进制数据文件后缀名
const QString JSON_FILE_NAME_SUFFIX = ".json";                  //JSON数据文件后缀名

//record related
const QString UHF_PRPS_RECORD_FOLDER = "UHFPRPSAndPRPDVideo";       //UHF PRPS 和PRPD 录屏数据文件夹
const QString TEV_PRPS_RECORD_FOLDER = "TEVPRPSAndPRPDVideo";       //TEV PRPS 和PRPD 录屏数据文件夹
const QString HFCT_PRPS_RECORD_FOLDER = "HFCTPRPSAndPRPDVideo";                     //HFCT PRPS 和PRPD 录屏数据文件夹
const QString CA_PRPS_RECORD_FOLDER = "CAPRPSAndPRPDVideo";       //CA PRPS 和PRPD 录屏数据文件夹

#endif // DATAFILEINFOS

