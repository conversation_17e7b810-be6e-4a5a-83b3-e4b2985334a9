/*****< gaple.h >**************************************************************/
/*      Copyright 2010 - 2014 Stonestreet One.                                */
/*      All Rights Reserved.                                                  */
/*                                                                            */
/*  GAPLE - Stonestreet One Bluetooth Stack Generic Access Profile (GAP) Low  */
/*          Energy (LE) Implementation Type Definitions, Constants, and       */
/*          Prototypes.                                                       */
/*                                                                            */
/*  Author:  Tim <PERSON>                                                         */
/*                                                                            */
/*** MODIFICATION HISTORY *****************************************************/
/*                                                                            */
/*   mm/dd/yy  F. Lastname    Description of Modification                     */
/*   --------  -----------    ------------------------------------------------*/
/*   11/22/10  T. Cook       Initial creation.                                */
/******************************************************************************/
#ifndef __GAPLEH__
#define __GAPLEH__

   /* The following function is responsible for making sure that the    */
   /* Bluetooth Stack GAP LE Module is Initialized correctly.  This     */
   /* function *MUST* be called before ANY other Bluetooth Stack GAP LE */
   /* function can be called.  This function returns zero if the Module */
   /* was initialized correctly, or a non-zero value if there was an    */
   /* error.                                                            */
int InitializeGAPLEModule(void);

   /* The following function is responsible for instructing the         */
   /* Bluetooth Stack GAP LE Module to clean up any resources that it   */
   /* has allocated.  Once this function has completed, NO other        */
   /* Bluetooth Stack GAP LE Functions can be called until a successful */
   /* call to the InitializeGAPLEModule() function is made.  The        */
   /* parameter to this function specifies the context in which this    */
   /* function is being called.  If the specified parameter is TRUE,    */
   /* then the module will make sure that NO functions that would       */
   /* require waiting/blocking on Mutexes/Events are called.  This      */
   /* parameter would be set to TRUE if this function was called in a   */
   /* context where threads would not be allowed to run.  If this       */
   /* function is called in the context where threads are allowed to run*/
   /* then this parameter should be set to FALSE.                       */
void CleanupGAPLEModule(Boolean_t ForceCleanup);

   /* The following function is provided to allow the GAP LE Module a   */
   /* means to clean up resources for a particular Bluetooth Protocol   */
   /* Stack when the Library is being removed from the system.  The     */
   /* GAPLEContextInfo parameter is a pointer to the GAP LE Context     */
   /* Information that was created by the GAP LE Module for the         */
   /* specified Bluetooth Stack.  If the specified parameter is TRUE,   */
   /* then the module will make sure that NO functions that would       */
   /* require waiting/blocking on Mutexes/Events are called.  This      */
   /* parameter would be set to TRUE if this function was called in a   */
   /* context where threads would not be allowed to run.  If this       */
   /* function is called in the context where threads are allowed to run*/
   /* then this parameter should be set to FALSE.                       */
void CleanupGAPLEInstance(void *GAPLEContextInfo, Boolean_t ForceCleanup);

   /* The following function is responsible for initializing a GAP LE   */
   /* Context Layer for the specified Bluetooth Protocol Stack.  This   */
   /* function will allocate and initialize a GAP LE Context Information*/
   /* structure and store a pointer to this structure in the            */
   /* GAPLEContextInfo member of the BTStackInfo structure associated   */
   /* with the specified Bluetooth Stack ID.  This function returns zero*/
   /* if successful, or a non-zero value if there was an error.         */
   /* * NOTE * The final parameter IS required and specifies a location */
   /*          to hold the context information that is required for     */
   /*          the GAP Context for the specified Bluetooth protocol     */
   /*          stack (specified via the Bluetooth Stack ID parameter).  */
int GAP_LE_Initialize(unsigned int BluetoothStackID, void **GAPLEContextInfo);

   /* The following function is responsible for releasing any resources */
   /* that the GAP LE Layer associated with the Bluetooth Protocol      */
   /* Stack, specified by the Bluetooth Stack ID, has allocated.  Upon  */
   /* completion of this function, ALL GAP LE functions will fail if    */
   /* used on the specified Bluetooth Protocol Stack.                   */
void GAP_LE_Cleanup(unsigned int BluetoothStackID);

   /* The following function is a utility function that is called       */
   /* internally to disable/enable LE functionality in the stack.  This */
   /* function is primarily used with the                               */
   /* BSC_EnableFeature()/BSC_DisableFeature() functions.  This function*/
   /* returns zero if LE is idle (and can be enabled or disabled) or    */
   /* this function returns a negative return error code to signify that*/
   /* LE cannot be enabled/disabled.                                    */
   /* * NOTE * This function will return:                               */
   /*             BTPS_ERROR_ACTION_NOT_ALLOWED                         */
   /*          if LE is requested to be disabled and there is currently */
   /*          a connection active.                                     */
int GAP_LE_Enable(unsigned int BluetoothStackID, Boolean_t Enable);

#endif
