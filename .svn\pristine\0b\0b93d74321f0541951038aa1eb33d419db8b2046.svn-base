/*
* Copyright (c) 2016.08，南京华乘电气科技有限公司
* All rights reserved.
*
* RfidInfoWidget.cpp
*
* 初始版本：1.0
* 作者：张涛、王谦
* 创建日期：2016年8月11日
* 摘要：rfid读取和写入中间可输入编辑框区域

* 当前版本：1.0
*/

#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QDebug>
#include <QScrollBar>
#include <QDoubleValidator>
#include "RFIDInfoWidget.h"
#include "log/log.h"
#include "global_log.h"

const QString TEXT_EDIT_READ_STYLE = "QTextEdit{color:black;font-size:24px;}";
const QString TEXT_EDIT_WHITE_STYLE = "QTextEdit{background:rgb(0,0,0,0);border-width:1px;border-style:solid;border-color:rgb(0,0,0);font-size:24px;}";


#define TITLE_FONT_SIZE 18 //标题字体
#define VOLTAGEMAXNUM   8

const quint8 MAX_LENTH = 18; //在常规图谱rfid保存后，回放中能够正常显示的最大长度（输入中文,不考虑换行）
const char *const INFO_TITLE[] =
{
    QT_TRANSLATE_NOOP_UTF8("rfidinfowidget", "Substation Name (max length: 128): "),
    QT_TRANSLATE_NOOP_UTF8("rfidinfowidget", "Asset Name (max length: 128): "),
    QT_TRANSLATE_NOOP_UTF8("rfidinfowidget", "Asset Number (max length: 32): "),
    QT_TRANSLATE_NOOP_UTF8("rfidinfowidget", "Test Number (length: 12): "),
    QT_TRANSLATE_NOOP_UTF8("rfidinfowidget", "Voltage Level (kV): ")
};

// 各输入控件布局的比例因子
const int INFO_SCALE[] =
{
    3, 3, 2, 1, 1
};

// 各编辑控件输入字符的长度
const int INFO_MAX_LENGTH[] =
{
    STATIONNAMEMAXNUM, SWCABINETNAMEMAXNUM, SWCABINETSNMAXNUM, TESTNUMMAXNUM, VOLTAGEMAXNUM
};

const int LAYOUT_MARGIN = 5;//10;   // 边距
const int LAYOUT_SPACING = 15;  // 间距

const QString SCOROLLBAR_STYLE = "QScrollBar:vertical"
                                 "{"
                                 "width:40px;"
                                 "background:rgba(0,0,0,0%);"
                                 "margin:0px,0px,0px,0px;"
                                 "padding-top:40px;"
                                 "padding-bottom:40px;"
                                 "}"
                                 "QScrollBar::add-line:vertical"
                                 "{"
                                 "height:40px;width:40px;"
                                 "subcontrol-position:bottom;"
                                 "}"
                                 "QScrollBar::sub-line:vertical"
                                 "{"
                                 "height:40px;width:40px;"
                                 "subcontrol-position:top;"
                                 "}"
                                 "QScrollBar::add-line:vertical:hover"
                                 "{"
                                 "height:40px;width:40px;"
                                 "subcontrol-position:bottom;"
                                 "}"
                                 "QScrollBar::sub-line:vertical:hover"
                                 "{"
                                 "height:40px;width:40px;"
                                 "subcontrol-position:top;"
                                 "}"
                                 "QScrollBar::add-page:vertical,QScrollBar::sub-page:vertical"
                                 "{"
                                 "border-radius:0px;"
                                 "}";
#define RFID_TRANSLATE( str ) qApp->translate("rfidinfowidget",(str))

/*************************************************
函数名： RfidInfoWidget(RfidType eRfidType，QWidget *parent)
输入参数:
    eRfidType -- 类型
    parent --- 父控件
输出参数：NULL
返回值： NULL
功能： 构造函数
*************************************************************/
RfidInfoWidget::RfidInfoWidget( RfidType eRfidType,QWidget *parent)
    : QWidget(parent)
    , m_eRfidType(eRfidType)
{
    resize(430, 600);

    setAttribute(Qt::WA_DeleteOnClose, true);
    setWindowFlags(Qt::FramelessWindowHint);

    QVBoxLayout *mainLayout = new QVBoxLayout;
    mainLayout->setSpacing( LAYOUT_SPACING );
    mainLayout->setMargin( LAYOUT_MARGIN );

    setStyleSheet("QWidget > QLabel {font-size: 24px}");

    setFocusPolicy( Qt::StrongFocus );  // 添加布局，设置focus属性

    for(int i = 0; i < INFO_COUNT; ++i)
    {
        QLabel* pTtileLabel = new QLabel(RFID_TRANSLATE(INFO_TITLE[i]));
        QVBoxLayout *vLayout = new QVBoxLayout;
        vLayout->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        vLayout->addWidget(pTtileLabel);

        if(m_eRfidType == RFID_READ)
        {
            QTextEdit *pDataEdit = new QTextEdit;
            pDataEdit->setReadOnly(true);
            pDataEdit->setFixedHeight(80);
            pDataEdit->setStyleSheet(TEXT_EDIT_READ_STYLE);
            pDataEdit->verticalScrollBar()->setStyleSheet(SCOROLLBAR_STYLE);

            m_listDataLabels.append(pDataEdit);
            vLayout->setSpacing(0);
            vLayout->addWidget(pDataEdit);
            mainLayout->addLayout(vLayout);
        }
        else
        {
            QTextEdit *pDataEdit = new QTextEdit;
            pDataEdit->setFixedHeight(80);
            pDataEdit->setStyleSheet(TEXT_EDIT_WHITE_STYLE);
            pDataEdit->setFocusPolicy(Qt::StrongFocus);
            pDataEdit->verticalScrollBar()->setStyleSheet(SCOROLLBAR_STYLE);
            connect(pDataEdit, SIGNAL(textChanged()), this, SLOT(onTextChanged()));

            m_listDataEdits.append(pDataEdit);
            vLayout->setSpacing(0);
            vLayout->addWidget(pDataEdit);
            mainLayout->addLayout(vLayout);
        }
        m_listTtileLabels.append(pTtileLabel);
    }

    setLayout(mainLayout);

    return;
}

/*************************************************
函数名： setScanInfo
输入参数:
    stInfo -- rfid扫描结果
输出参数：NULL
返回值： NULL
功能： 添加rfid扫描结果的信息
*************************************************************/
void RfidInfoWidget::setScanInfo(const ScanInfo &stInfo)
{
    m_listDataLabels[DEVICE_NUMBER]->setText( stInfo.strDeviceNumber );
    m_listDataLabels[STATION_INFO]->setText( stInfo.strStation );
    m_listDataLabels[DEVICE_NAME]->setText( stInfo.strDeviceName );
    m_listDataLabels[TEST_NUMBER]->setText( stInfo.strTestNumber );
    m_listDataLabels[VOLTAGE_LEVEL]->setText( stInfo.strVoltageLevel );
}

/*************************************************
函数名： setCardInfo
输入参数:
    stInfo -- rfid扫描结果
输出参数：NULL
返回值： NULL
功能： 添加rfid扫描结果的信息
*************************************************************/
void RfidInfoWidget::setCardInfo(ReadRFIDData &stData)
{
    const QStringList strings = cardStringInfo(stData);
    if(m_listDataEdits.isEmpty())
    {
        return;
    }

    logInfo(strings);
    m_listDataEdits[DEVICE_NUMBER]->setText(strings.at(DEVICE_NUMBER));
    m_listDataEdits[STATION_INFO]->setText(strings.at(STATION_INFO));
    m_listDataEdits[DEVICE_NAME]->setText(strings.at(DEVICE_NAME));
    m_listDataEdits[TEST_NUMBER]->setText(strings.at(TEST_NUMBER));
    m_listDataEdits[VOLTAGE_LEVEL]->setText(strings.at(VOLTAGE_LEVEL));
    return;
}

/*************************************************
函数名： cardStringInfo
输入参数:NULL
输出参数：NULL
返回值： NULL
功能： 将rfid结构体信息转化成便于使用的qstringList
*************************************************************/
const QStringList RfidInfoWidget::cardStringInfo(const ReadRFIDData &stInfo)
{
    QStringList strings;
    strings.reserve(INFO_COUNT);

    QChar site[STATIONNAMEMAXNUM] = {0};
    for(int i = 0; i < STATIONNAMEMAXNUM; ++i)
    {
        site[i] = QChar(stInfo.usaStationName[i]);
    }
    QString qstrSiteName = QString(site).left(STATIONNAMEMAXNUM);


    QChar dev[SWCABINETNAMEMAXNUM] = {0};
    for(int i = 0; i < SWCABINETNAMEMAXNUM; ++i)
    {
        dev[i] = QChar(stInfo.usaSwitchCabinetName[i]);
    }
    QString qstrDeviceName = QString(dev).left(SWCABINETNAMEMAXNUM);

    //const QString strDeviceNumber((char*)stInfo.ucaSwitchCabinetSn);
    char szDevNum[SWCABINETSNMAXNUM] = {0};
    QString strDeviceNumber = "";
    for(int i = 0; i < SWCABINETSNMAXNUM; ++i)
    {
        //为了位数不超过SWCABINETSNMAXNUM
        szDevNum[i] = (char)(stInfo.ucaSwitchCabinetSn[i]);
        //strDeviceNumber.append((char)(stInfo.ucaSwitchCabinetSn[i]));
    }
    strDeviceNumber = QString(szDevNum).left(SWCABINETSNMAXNUM);

    QString testNumber = "";
    for(int i = 0; i < TESTNUMMAXNUM; ++i)
    {
        testNumber.append(QString::number(stInfo.ucaTestNum[i]));
    }

    double dVal = Module::dealDoublePrecision(stInfo.fVoltageClass, g_iVoltagePres);
    QString volLevel = QString::number(dVal, 'f', g_iVoltagePres);
    strings << qstrSiteName << qstrDeviceName << strDeviceNumber << testNumber << volLevel;

    return strings;
}

/*************************************************
函数名：isDigitStr
输入参数：string---校验的字符串
输出参数：NULL
返回值：true：全数字；false：非全数字
功能：校验字符串是否是全数字
*************************************************************/
bool RfidInfoWidget::isDigitStr(const QString &string)
{
    const char *pData = string.toLatin1().data();
    while(*pData && *pData >= '0' && *pData <= '9')
    {
        ++pData;
    }

    return ((*pData) ? false : true);
}

/*************************************************
函数名：checkNumberAndAlpha
输入参数：qstrInput---校验的字符串
输出参数：NULL
返回值：true：满足范围要求; false：不满足范围要求
功能：校验字符串是否满足在0-9/a-z/A-Z范围内
*************************************************************/
bool RfidInfoWidget::checkNumberAndAlpha(const QString &qstrInput)
{
    bool bRet = true;

    //利用正则表达式，可惜需要qt 5.0以上才支持
    //QString qstrExpression = "^[A-Za-z0-9]+$";
    //QRegularExpression qreExpression(qstrExpression);
    //bRet = (0 == qstrInput.indexOf(qreExpression)) ? true : false;

    const char *pData = qstrInput.toLatin1().data();
    while(*pData)
    {
        if(!((*pData >= '0' && *pData <= '9')
             || (*pData >= 'a' && *pData <= 'z')
             || (*pData >= 'A' && *pData <= 'Z')))
        {
            bRet = false;
        }
        ++pData;
    }

    return  bRet;
}

/*************************************************
函数名： cardEditInfo2RFIDReadData
输入参数:NULL
输出参数：NULL
返回值： NULL
功能： 将line edit里的信息组成底层需要的rfid数据
*************************************************************/
void RfidInfoWidget::cardEditInfo2RFIDReadData(ReadRFIDData &stRFIDReadData, ErrorType& eError)
{
    eError = ERROR_NONE;
    QStringList stringList = getCardStringInfo();

    //device number
    QString stringDeviceNumber = stringList.at( RfidInfoWidget::DEVICE_NUMBER );
    int iDeviceNumbersize = stringDeviceNumber.size(); // 将写入内容填进结构体相应位置
    if(iDeviceNumbersize > SWCABINETSNMAXNUM || iDeviceNumbersize <= 0)
    {
        //iDeviceNumbersize = SWCABINETSNMAXNUM;
        eError = ERROR_DEVICE_NUMBER_WRONG_LENGTH;
        logError(QString("device number length(%1) error, max length(%2).").arg(iDeviceNumbersize)
                 .arg(SWCABINETSNMAXNUM).toLatin1().data());
        return;
    }
    else if (!checkNumberAndAlpha(stringDeviceNumber))
    {
        eError = ERROR_DEVICE_NUMBER_NOT_DIGIT_ALPHA;
        logError(QString("device number is not digital and not alpha.").toLatin1().data());
        return;
    }
    else
    {
        for(int i = 0; i < iDeviceNumbersize; ++i)
        {
            stRFIDReadData.ucaSwitchCabinetSn[i] = stringDeviceNumber.unicode()[i].toLatin1();
        }
    }

    //station info
    QString stringStationInfo = stringList.at( RfidInfoWidget::STATION_INFO );
    int iStationInfosize = stringStationInfo.size();
    if(iStationInfosize > STATIONNAMEMAXNUM || iStationInfosize <= 0)
    {
        //iStationInfosize = STATIONNAMEMAXNUM;
        eError = ERROR_STATION_NAME_WRONG_LENGTH;
        logError(QString("station name length(%1) error, max length(%2).").arg(iStationInfosize)
                 .arg(STATIONNAMEMAXNUM).toLatin1().data());
        return;
    }

    for(int i = 0; i < iStationInfosize; ++i)
    {
        stRFIDReadData.usaStationName[i] = stringStationInfo.unicode()[i].unicode();
    }

    //device name
    QString stringDeviceName = stringList.at( RfidInfoWidget::DEVICE_NAME );
    int iDeviceNamesize = stringDeviceName.size();
    if(iDeviceNamesize > SWCABINETNAMEMAXNUM || iDeviceNamesize <= 0)
    {
        //iDeviceNamesize = SWCABINETNAMEMAXNUM;
        eError = ERROR_DEVICE_NAME_WRONG_LENGTH;
        logError(QString("device name length(%1) error, max length(%2).").arg(iDeviceNamesize)
                 .arg(SWCABINETNAMEMAXNUM).toLatin1().data());
        return;
    }

    for(int i = 0; i < iDeviceNamesize; ++i)
    {
        stRFIDReadData.usaSwitchCabinetName[i] = stringDeviceName.unicode()[i].unicode();
    }

    //test number
    QString stringTestNumber = stringList.at( RfidInfoWidget::TEST_NUMBER );
    int iTestNumbersize = stringTestNumber.size();
    if( iTestNumbersize != TESTNUMMAXNUM )
    {
        //iTestNumbersize = TESTNUMMAXNUM;
        eError = ERROR_TEST_NUMBER_WRONG_LENGTH;
        logError(QString("test number length(%1) error, max length(%2).").arg(iTestNumbersize)
                 .arg(TESTNUMMAXNUM).toLatin1().data());
        return;
    }
    else if(!isDigitStr(stringTestNumber))
    {
        //iTestNumbersize = TESTNUMMAXNUM;
        eError = ERROR_TEST_NUMBER_HAS_NONE_DIGIT;
        logError(QString("test number is not a digital.").toLatin1().data());
        return;
    }
    else
    {
        //temp修改，bywq，传递给底层的是ascii码值，并不是对应的输入字符，减去0字符对应的ascii码值
        for(int i = 0; i < iTestNumbersize; ++i)
        {
            stRFIDReadData.ucaTestNum[i] = stringTestNumber.unicode()[i].toLatin1() - '0';
        }
    }

    //voltage
    QString stringVoltage = stringList.at( RfidInfoWidget::VOLTAGE_LEVEL );
    int ivoltageNumbersize = stringVoltage.size();
    bool bOk = false;
    float fVal = stringVoltage.toFloat(&bOk);
    fVal = Module::dealFloatPrecision(fVal, g_iVoltagePres + 1);
    if(ivoltageNumbersize > VOLTAGEMAXNUM || ivoltageNumbersize <= 0)
    {
        eError = ERROR_VOLTAGE_LEVEL_NOT_VALID;
        logError(QString("voltage number length(%1) error, max length(%2).").arg(ivoltageNumbersize)
                 .arg(VOLTAGEMAXNUM).toLatin1().data());
        return;
    }
    else if(!bOk)
    {
        eError = ERROR_VOLTAGE_HAS_NONE_DIGIT;
        logError(QString("voltage number is not a digital.").toLatin1().data());
        return;
    }
    else
    {
        stRFIDReadData.fVoltageClass = fVal;
    }
    //log_debug("vol: %f, %f", fVal, stRFIDReadData.fVoltageClass);
    return;
}

/*************************************************
函数名： getCardStringInfo
输入参数:NULL
输出参数：NULL
返回值： NULL
功能： 获得rfid写入的信息
*************************************************************/
QStringList const RfidInfoWidget::getCardStringInfo( void ) const
{
    if( m_listDataEdits.isEmpty() )
    {
        return QStringList();
    }

    QStringList strings;
    strings.reserve(INFO_COUNT);

    foreach( QTextEdit* pLabel, m_listDataEdits )
    {
        strings.append( pLabel->toPlainText() );
    }

    if( strings.last().isEmpty() )
    {
        bool ok = true;
        QString qString = strings.last();
        qString.toFloat( &ok );
        if( !ok )
        {
            // 若电压等级编辑控件不为纯数字则将其置null
            strings.last() = QString::null;
        }
    }

    return strings;
}

/*************************************************
函数名： onTextChanged
输入参数:NULL
输出参数：NULL
返回值： NULL
功能： 绑定textEdit的信号，便于控制输入长度
*************************************************************/
void RfidInfoWidget::onTextChanged()
{
    QObject* pObj = sender();
    if(!pObj)
    {
        logError("signal sender is NULL.");
        return;
    }

    for(int i = 0, iSize = m_listDataEdits.size(); i < iSize; ++i)
    {
        if(pObj == m_listDataEdits.at(i))
        {
            QString textContent = m_listDataEdits.at(i)->toPlainText();
            int length = textContent.count();
            int maxLength = INFO_MAX_LENGTH[i]; // 最大字符数

            if(VOLTAGE_LEVEL == i)
            {
                //设置位数控制
                QString qstrInfo = textContent.mid(textContent.indexOf('.') + 1);
                if(textContent.contains('.') && qstrInfo.length() > g_iVoltagePres)
                {
                    textContent = textContent.left(textContent.indexOf('.') + g_iVoltagePres + 1);
                    length = textContent.count();
                    m_listDataEdits.at(i)->setText(textContent);
                    if(length <= maxLength)
                    {
                        QTextCursor textCursor = m_listDataEdits.at(i)->textCursor();
                        textCursor.setPosition(length);
                        m_listDataEdits.at(i)->setTextCursor(textCursor);   //　修改可输入控件最大字符数
                    }
                }
            }

            if(length > maxLength)
            {
                int position = m_listDataEdits.at(i)->textCursor().position();
                QTextCursor textCursor = m_listDataEdits.at(i)->textCursor();
                textContent.remove(position - (length - maxLength), length - maxLength);
                m_listDataEdits.at(i)->setText(textContent);
                textCursor.setPosition(position - (length - maxLength));
                m_listDataEdits.at(i)->setTextCursor(textCursor);   //　修改可输入控件最大字符数
            }

            break;
        }
    }

    return;
}
