/*
* Copyright (c) 2019.04，南京华乘电气科技有限公司
* All rights reserved.
*
* linklayerprotocolstandard.h
*
* 初始版本：1.0
* 作者：张浪
* 创建日期：2020年09月17日
* 摘要：T95接入终端标准链路层协议，目前支持杭州华云、北京融智通、上海艾飞、西安创亦等客户的定制接入

* 当前版本：1.0
*/
#ifndef _LINKLAYERPROTOCOLSTANDARD_H_
#define _LINKLAYERPROTOCOLSTANDARD_H_

#include "linklayerprotocolbase.h"
#include <QMutex>
#include <QMap>
#include <QVector>

class LinkLayerProtocolStandard : public LinkLayerProtocolBase
{
    Q_OBJECT
public:
    /****************************
     * 功能：构造函数
     * 输入参数：
     *      pCom：通讯组件
     *      parent：父控件
     * ****************************/
    explicit LinkLayerProtocolStandard(AbstractComm *pCom, QObject *parent = 0);

    /****************************
     * 功能：析构函数
     * ****************************/
    ~LinkLayerProtocolStandard();

public slots:
    /****************************
     * 功能：接收来自通讯组件的数据
     * 输入参数：
     *      qui64BytesAvailable：缓冲区读取数据的字节数
     * ****************************/
    void onReadyRead(qint64 qui64BytesAvailable);

    /****************************
     * 功能：发送数据内容
     * 输入参数：
     *      stFrameInfo：帧信息
     * ****************************/
    void onSendData_EX(const Protocol::CommFrameInfo &stFrameInfo);

private slots:
    /****************************
     * 功能：槽，处理接收到的数据
     * ****************************/
    void onRecvData();

private:
    /****************************
     * 功能：发送数据内容给网络
     * 输入参数：
     *      stFrameInfo：帧信息
     * 返回值：
     *      bool：true -- 发送成功，false -- 发送失败
     * ****************************/
    bool sendFrameData(Protocol::CommFrameInfo &stFrameInfo);

    /***************************
     * 功能：查找帧数据种第一次出现帧头位置
     * 输入参数：
     *      qbaData：帧数据内容
     * 返回值：
     *      int：帧头起始位置，-1表示未查找成功。
     * **************************/
    int getFrameHeadPos(const QByteArray &qbaData);

    /***************************
     * 功能：处理多帧解析和拼接逻辑
     * 输入参数：
     *      stFrameInfo：帧信息
     * **************************/
    void dealMultiFrame(Protocol::CommFrameInfo &stFrameInfo);

private:
    QMutex m_mt4RecvData;
    QByteArray m_qbaRecvData;

    QMutex m_mt4MpMutilFrames;
    QMap<int, QVector<Protocol::CommFrameInfo> > m_mp4MultiFrames;

    QString m_qstrPeerAddr;
    quint16 m_qui16Port;

};
#endif // _LINKLAYERPROTOCOLSTANDARD_H_
