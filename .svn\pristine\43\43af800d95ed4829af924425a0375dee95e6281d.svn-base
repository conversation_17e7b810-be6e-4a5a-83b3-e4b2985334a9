﻿/*
* Copyright (c) 2021.3，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：autosyancprps.h
*
* 作者：邵震宇
* 创建日期：2021年3月6日
* 摘要：prps自动同步算法
* 需求：在实验室内进行模拟测试的时候，输入的prps信号往往相位十分稳定，而此时由于采集误差、频率不一致
*       都会导致采集到的PRPS脉冲相位上不整齐，此时如果可以自动调整prps脉冲相位，使之对齐，则可以达到较好的
*       应用效果
* 原理：
* 一、名词解释
*   1、同步脉冲：每个周期PRPS中，用来做相位同步的脉冲，一般选峰值/次峰值
*       合法性判断：峰值>10且>均值的50%
*   2、同步相位：被同步的目标相位
*   3、上一周期的同步相位：上一个周期的数据中，同步脉冲所在的相位
*   4、同步宽度：本周期同步脉冲和上周期同步脉冲所在的相位区间差
*       考虑到频率差0.2Hz时，对应角差1.44度；考虑到一个相位区间差0.6度（60个相位区间），再考虑到采集误差
*       此处取相位偏差阈值=2个相位区间
* 二、同步流程
*   1、初始化同步相位
*       同步相位未初始化时，读取到的第一个同步脉冲的相位。
*   2、同步丢失
*       连续5周期，无同步脉冲或同步宽度不合法；
*       此时进入未初始化状态
*   3、每个周期
*       1）计算本周期的同步脉冲（峰值、次峰值）
*       2）判断同步宽度是否合法（峰值 or 次峰值）
*           a、合法，则进行同步
*           b、非法，则更新非法计数
*/

#ifndef AUTOSYNCPRPS_H
#define AUTOSYNCPRPS_H
#include <vector>

#define AUTO_SYNC_LIBRARY

#ifdef  WIN32
    #ifdef __cplusplus
        #if defined(AUTO_SYNC_LIBRARY)
        #  define AUTO_SYNC_EXPORT __declspec(dllexport)
        #else
        #  define AUTO_SYNC_EXPORT
        #endif
    #else
        #if defined(AUTO_SYNC_LIBRARY)
        #  define AUTO_SYNC_EXPORT __declspec(dllexport)
        #else
        #  define AUTO_SYNC_EXPORT
        #endif
    #endif
#else
    #ifdef __cplusplus
    # define AUTO_SYNC_EXPORT
    #else
    # define AUTO_SYNC_EXPORT
    #endif
#endif

/*************************************************
 * 函数功能： 自动同步PRPS
 * 输入参数：
 *      prpsPerT -- 一个周期的prps数据
 * 输出参数：
 *      isSynced -- true：被同步；false：未被同步
 * 返回参数：
 *      自动排序后的PRPS数据（单周期）
*************************************************************/
AUTO_SYNC_EXPORT std::vector<double> auto_sync_prps( const std::vector<double>& prpsPerT, bool& isSynced );

#endif // AUTOSYNCPRPS_H

