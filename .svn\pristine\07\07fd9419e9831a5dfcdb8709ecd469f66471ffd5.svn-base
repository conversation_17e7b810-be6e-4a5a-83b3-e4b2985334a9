#include "taskinteractiveio.h"
#include <QFile>
#include <QDomDocument>
#include <QVector>
#include <QTextStream>
#include <QDebug>

using namespace InteractiveIO;

TaskInteractiveIO::TaskInteractiveIO(QObject *parent) : QObject(parent)
{

}

bool TaskInteractiveIO::saveUploadTaskInfo(const UploadTaskInfo& uploadInfo, const QString& filePath)
{
    bool bRet = true;

    //创建dom结构
    QDomDocument doc;
    QDomProcessingInstruction instruction = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
    doc.appendChild(instruction);

    QDomElement root = doc.createElement(ROOT_TAG);//创建根节点
    doc.appendChild(root);//添加根节点

    //创建任务编号
    QDomElement testNumEle = doc.createElement(TEST_NUMBER_TAG);
    root.appendChild(testNumEle);
    QDomText testNumText = doc.createTextNode(uploadInfo.testNumber);
    testNumEle.appendChild(testNumText);

    //创建任务名称
    QDomElement testNameEle = doc.createElement(TEST_NAME_TAG);
    root.appendChild(testNameEle);
    QDomText testNameText = doc.createTextNode(uploadInfo.testName);
    testNameEle.appendChild(testNameText);

    //创建任务路径
    QDomElement testPathEle = doc.createElement(TASK_FILEPATH_TAG);
    root.appendChild(testPathEle);
    QDomText testPathText = doc.createTextNode(uploadInfo.taskFileUrl);
    testPathEle.appendChild(testPathText);

    //创建上传文件信息节点
    for(int i = 0; i < uploadInfo.uploadFiles.size(); i++)
    {
        QDomElement dataEle = doc.createElement(FILE_DATA_TAG);
        root.appendChild(dataEle);
        //逐个添加
        QDomElement fileNameEle = doc.createElement(FILE_NAME_TAG);//创建文件名
        dataEle.appendChild(fileNameEle);
        QDomText fileNameText = doc.createTextNode(uploadInfo.uploadFiles.at(i).originalFileName);
        fileNameEle.appendChild(fileNameText);

        QDomElement fileUrlEle = doc.createElement(FILE_URL_TAG);//创建文件url
        dataEle.appendChild(fileUrlEle);
        QDomText fileUrlText = doc.createTextNode(uploadInfo.uploadFiles.at(i).uploadedFileUrl);
        fileUrlEle.appendChild(fileUrlText);
    }

    //保存到文件
    QFile file(filePath);
    if(file.open(QFile::WriteOnly | QFile::Text | QFile::Truncate))
    {
        QTextStream out(&file);
        out.setCodec("UTF-8");
        doc.save(out, 4, QDomNode::EncodingFromTextStream);
        file.close();
    }
    else
    {
        bRet = false;
        qDebug() << "Cann't open inter file!";
    }

    return bRet;
}
