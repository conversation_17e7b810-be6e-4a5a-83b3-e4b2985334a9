/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* AEPhaseService.h
*
* 初始版本：1.0
* 作者：王谦
* 创建日期：2017年12月14日
* 摘要：为了适配接入定制接入终端，后产生的多用户的使用场景，进行的修改

* 当前版本：1.0
*/

#ifndef AEPHASESERVICE_H
#define AEPHASESERVICE_H

#include "model/HCService.h"
#include "AE.h"
#include "module_global.h"
#include "Module.h"
#include "AEService.h"

class AEServicePrivate;
class MODULESHARED_EXPORT AEPhaseService : public AEService
{
    Q_OBJECT
public:
    /****************************
    功能： 模块单例
    *****************************/
    static AEPhaseService* instance();

    /*************************************************
    功能： 启动独占采集
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    virtual bool startExclusiveSample(  MultiServiceNS::USERID userId  );

    /*************************************************
    功能： 停止独占采集
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    virtual void stopExclusiveSample(  MultiServiceNS::USERID userId  );

private:
    /****************************
    功能： 构造函数
    *****************************/
    AEPhaseService();

    /****************************
    功能： 析构函数
    *****************************/
    ~AEPhaseService();

    /****************************
    功能： disable 拷贝
    *****************************/
    AEPhaseService( const AEPhaseService& other );

    /****************************
    功能： disable 赋值
    *****************************/
    AEPhaseService & operator = (const AEPhaseService &);

};

#endif // AEPHASESERVICE_H
