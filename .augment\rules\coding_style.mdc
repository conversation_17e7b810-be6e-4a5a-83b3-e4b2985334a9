---
description:
globs:
alwaysApply: false
---
# 代码风格指南

## 命名约定
- **类名**：使用PascalCase（例如：`MainWindow`, `DataProcessor`）
- **函数名**：使用camelCase（例如：`calculateTotal()`, `processData()`）
- **变量名**：使用camelCase（例如：`userName`, `itemCount`）
- **成员变量**：使用camelCase并添加`m_`前缀（例如：`m_userName`, `m_itemCount`）
- **常量**：使用全大写和下划线（例如：`MAX_RETRIES`, `DEFAULT_TIMEOUT`）
- **枚举值**：使用PascalCase（例如：`enum class Color { Red, Green, Blue }`）
- **文件名**：使用snake_case（例如：`main_window.cpp`, `data_processor.h`）
- **命名空间**：使用小写（例如：`namespace utils { ... }`）

## 格式化规则
- 使用4个空格进行缩进，不使用制表符
- 大括号放在同一行，对齐于控制语句：
  ```cpp
  if (condition) {
      // 代码
  } else {
      // 代码
  }
  ```
- 每行最大长度为100个字符
- 运算符两侧添加空格：`a + b`而不是`a+b`
- 逗号后面加空格：`func(a, b)`而不是`func(a,b)`
- 函数名与括号之间不加空格：`func()`而不是`func ()`
- 指针和引用符号靠近类型：`int* ptr`而不是`int *ptr`

## C++风格
- 使用C++11或更高版本的特性
- 优先使用`auto`关键字进行类型推导
- 使用智能指针（`std::unique_ptr`, `std::shared_ptr`）代替原始指针
- 使用范围-based for循环：
  ```cpp
  for (const auto& item : items) {
      // 处理item
  }
  ```
- 使用列表初始化：`std::vector<int> values = {1, 2, 3};`
- 使用`nullptr`代替`NULL`或`0`
- 使用`enum class`代替传统`enum`

## Qt特定规则
- 使用Qt命名约定进行信号和槽：
  ```cpp
  signals:
      void dataChanged();
  
  private slots:
      void onButtonClicked();
  ```
- 使用Qt的属性系统：
  ```cpp
  Q_PROPERTY(QString name READ name WRITE setName NOTIFY nameChanged)
  ```
- 使用Qt的容器类（如`QVector`, `QList`, `QMap`）
- 在头文件中不使用`using namespace`
- 按照以下顺序组织类成员：
  1. Q_OBJECT 宏
  2. Q_PROPERTY 声明
  3. public 成员函数
  4. public slots
  5. signals
  6. protected 成员函数
  7. protected slots
  8. private slots
  9. private 成员函数
  10. private 成员变量

## 注释规范
- 使用Doxygen风格的注释：
  ```cpp
  /**
   * @brief 函数的简要描述
   * @param param1 参数1的描述
   * @param param2 参数2的描述
   * @return 返回值的描述
   * 
   * 详细描述（如果需要）
   */
  ```
- 对复杂逻辑添加中文注释解释
- 避免无意义的注释（如简单地重复代码）
- 保持注释与代码的同步更新

## 最佳实践
- 优先使用初始化列表而不是构造函数中的赋值
- 声明为const的函数和参数，除非需要修改
- 对类的成员变量提供访问器和修改器
- 避免使用全局变量和函数
- 避免深层嵌套（最好不超过3层）
- 确保资源被正确释放（使用RAII原则）
