/*
 * Copyright (c) 2019.6，华乘电气科技有限公司
 * All rights reserved.
 *
 * 文件名称：currentdetectionservice.h
 * 
 * 初始版本：1.0
 * 作者：洪澄
 * 创建日期：2019/06/28
 * 摘要：电流检测服务
 * 当前版本：1.0
 */

#pragma once

#include <QThread>
#include "Module.h"
#include "multiservice/multiuserservice.h"
#include "currentdetection/currentdetectiondefine.h"

class CurrentDetectionService : public MultiUserService
{
    Q_OBJECT

public:
    /****************************
    功能： 模块单例
    *****************************/
    static CurrentDetectionService* instance();

    ~CurrentDetectionService();

    /*************************************************
    函数名： start()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 启动业务
    *************************************************************/
    virtual bool start();

    /*************************************************
    功能： 启动业务
    输入参数:void
    输出参数：NULL
    返回值： NULL
    *************************************************/
    virtual bool isStart(void);

    /*************************************************
    函数名： stop()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 终止业务
    *************************************************************/
    bool stop();

signals:
    void sigStartSample();
    void sigStopSample();

    /*************************************************
    传递参数： data：电流数据
    说明： 电流数据信号
    *************************************************************/
    void sigData(CurrentDetection::CurrentDetectionData data, MultiServiceNS::USERID userId);

    /*************************************************
    功能： 信号  数据信号状态发生变化
    *************************************************/
    void sigSignalChanged(Module::SignalState eSignalState);

protected:
    /*************************************************
    功能： 启动采样业务的额外处理   （一般指在子类中的额外操作）
    输入参数：
            userId -- 用户ID
    返回：
          额外处理是否成功
    *************************************************/
    virtual bool startSampleExt(MultiServiceNS::USERID userId);

    /*************************************************
    功能： 停止采样业务的额外处理   （一般指在子类中的额外操作）
    输入参数：
            userId -- 用户ID
    返回：
          额外处理是否成功
    *************************************************/
    virtual bool stopSampleExt(MultiServiceNS::USERID userId);

    /*************************************************
    函数名： timerEvent(QTimerEvent *event)
    输入参数： event：定时器事件
    输出参数： NULL
    返回值： NULL
    功能： 定时器事件处理
    *************************************************************/
    virtual void timerEvent(QTimerEvent *event);

private:
    /*************************************************
    函数名： CurrentDetectionService()
    功能： 构造函数
    *************************************************************/
    explicit CurrentDetectionService();

    /****************************
    功能： disable 拷贝
    *****************************/
    CurrentDetectionService(const CurrentDetectionService& other);

    /****************************
    功能： disable 赋值
    *****************************/
    CurrentDetectionService & operator = (const CurrentDetectionService &);

    /*************************************************
    函数名： singleSample()
    输入参数： NULL
    输出参数： NULL
    返回值： NULL
    功能： 单次采集
    *************************************************************/
    void singleSample();

    /*************************************************
    函数名： singleSample()
    输入参数： eSignalState -- 信号状态
    输出参数： NULL
    返回值： NULL
    功能： 更新信号状态
    *************************************************/
    void updateSignalState(const Module::SignalState eSignalState);

private slots:
    void onStartSample();
    void onStopSample();

private:
    QThread *m_pThread;
    QMutex m_mutexAffair;  //事务互斥锁
    int m_iTimer;
    volatile bool m_bIsSampling;  //是否正在采集标识

    Module::SignalState m_eSignalState;//信号状态
    UINT16 m_uiReadDataFailCnt;
};
