<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="zh_CN">
<context>
    <name>AEAbstractChart</name>
    <message>
        <location filename="view/ae/aecommonwidget/AEAbstractChart.cpp" line="14"/>
        <source>Phase[°]</source>
        <translation>相位[°]</translation>
    </message>
    <message>
        <location filename="view/ae/aecommonwidget/AEAbstractChart.cpp" line="15"/>
        <source>Amp.[mV]</source>
        <translation>幅值[mV]</translation>
    </message>
    <message>
        <location filename="view/ae/aecommonwidget/AEAbstractChart.cpp" line="16"/>
        <source>Time Interval[ms]</source>
        <translation>时间间隔[ms]</translation>
    </message>
    <message>
        <location filename="view/ae/aecommonwidget/AEAbstractChart.cpp" line="17"/>
        <source>Time Interval[s]</source>
        <translation>时间间隔[s]</translation>
    </message>
    <message>
        <location filename="view/ae/aecommonwidget/AEAbstractChart.cpp" line="18"/>
        <source>Time Interval[min]</source>
        <translation>时间间隔[min]</translation>
    </message>
</context>
<context>
    <name>AEAmpChart</name>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="200"/>
        <source>High Gain</source>
        <translation>增益偏高</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="206"/>
        <source>Low Gain</source>
        <translation>增益偏低</translation>
    </message>
</context>
<context>
    <name>AEAtlasButtonWidget</name>
    <message>
        <location filename="view/ae/aeatlasbuttonwidget.cpp" line="46"/>
        <source>Amplitude</source>
        <translation>幅值</translation>
    </message>
    <message>
        <location filename="view/ae/aeatlasbuttonwidget.cpp" line="49"/>
        <source>Phase</source>
        <translation>相位</translation>
    </message>
    <message>
        <location filename="view/ae/aeatlasbuttonwidget.cpp" line="52"/>
        <source>Fly</source>
        <translation>飞行</translation>
    </message>
    <message>
        <location filename="view/ae/aeatlasbuttonwidget.cpp" line="55"/>
        <source>Waveform</source>
        <translation>波形</translation>
    </message>
</context>
<context>
    <name>AEOption</name>
    <message>
        <location filename="view/ae/aecommonwidget/AEOption.cpp" line="15"/>
        <source>Pulse Count: </source>
        <translation>脉冲个数：</translation>
    </message>
    <message>
        <location filename="view/ae/aecommonwidget/AEOption.cpp" line="16"/>
        <source>Channel: </source>
        <translation>通道：</translation>
    </message>
</context>
<context>
    <name>AEView</name>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="29"/>
        <source>Mode</source>
        <translation>模式</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="30"/>
        <source>Single Sample</source>
        <translation>单次采样</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="31"/>
        <source>Noise Test</source>
        <translation>噪声测试</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="32"/>
        <source>Record Noise</source>
        <translation>记录噪声</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="33"/>
        <source>Clear Noise</source>
        <translation>清除噪声</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="34"/>
        <source>Gain</source>
        <translation>增益</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="35"/>
        <source>Unit</source>
        <translation>单位</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="36"/>
        <source>Save Data</source>
        <translation>保存数据</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="37"/>
        <source>Save</source>
        <translation>保存</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="38"/>
        <source>More...</source>
        <translation>更多...</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="39"/>
        <source>Trigger Value</source>
        <translation>触发值</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="40"/>
        <source>Freq.</source>
        <translation>频率</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="41"/>
        <source>Delete Data</source>
        <translation>删除数据</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="42"/>
        <source>Channel</source>
        <translation>通道</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="43"/>
        <source>Default</source>
        <translation>恢复默认</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="44"/>
        <source>Save RFID</source>
        <translation>RFID保存</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="45"/>
        <source>Load Data</source>
        <translation>载入数据</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="46"/>
        <source>Record</source>
        <translation>录音</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="47"/>
        <source>Add</source>
        <translation>新增</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="48"/>
        <source>Bandwidth</source>
        <translation>带宽模式</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="50"/>
        <source>Single</source>
        <translation>单次</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="51"/>
        <source>Continuous</source>
        <translation>连续</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="62"/>
        <source>Internal</source>
        <translation>内置</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="63"/>
        <source>External</source>
        <translation>外接</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="64"/>
        <source>Wireless</source>
        <translation>无线</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="66"/>
        <source>Power Sync</source>
        <translation>电源同步</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="67"/>
        <source>Light Sync</source>
        <translation>光同步</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="68"/>
        <source>Sync Mode</source>
        <translation>同步源</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="69"/>
        <source>Internal Sync</source>
        <translation>内同步</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="78"/>
        <source>Sample</source>
        <translation>采样</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="79"/>
        <source>Gating Time</source>
        <translation>开门时间</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="80"/>
        <source>Time Interval</source>
        <translation>时间间隔</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="81"/>
        <source>Blocking Time</source>
        <translation>关门时间</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="85"/>
        <source>Phase Shift</source>
        <translation>相移</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="92"/>
        <source>Sample Time</source>
        <translation>采样时间</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="93"/>
        <source>Amp. Range</source>
        <translation>幅值范围</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="95"/>
        <source>Start</source>
        <translation>开始</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="96"/>
        <source>Stop</source>
        <translation>停止</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="97"/>
        <source>Pause</source>
        <translation>暂停</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="99"/>
        <source>Spectrum Type</source>
        <translation>图谱类型</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="100"/>
        <source>View Sig Amp</source>
        <translation>幅值图谱</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="101"/>
        <source>View Sig Phase</source>
        <translation>相位图谱</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="102"/>
        <source>View Sig Fly</source>
        <translation>飞行图谱</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="103"/>
        <source>View Sig Wave</source>
        <translation>波形图谱</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="104"/>
        <source>Amplitude</source>
        <translation>幅值图谱</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="105"/>
        <source>Phase</source>
        <translation>相位图谱</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="106"/>
        <source>Fly</source>
        <translation>飞行图谱</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="107"/>
        <source>Waveform</source>
        <translation>波形图谱</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="109"/>
        <source>Start Recording</source>
        <translation>开始录音</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="110"/>
        <source>Stop Recording</source>
        <translation>停止录音</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="111"/>
        <source>Volume</source>
        <translation>音量</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="113"/>
        <source>Sync Lost</source>
        <translation>同步丟失</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="115"/>
        <source>Load Record Data</source>
        <translation>载入录音数据</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="116"/>
        <source>Delete Record Data</source>
        <translation>删除录音数据</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="117"/>
        <source>Others</source>
        <translation>其他</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="235"/>
        <source>RMS</source>
        <translation>有效值</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="236"/>
        <source>Peak</source>
        <translation>周期最大值</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="237"/>
        <source>Spectrum1</source>
        <translation>频率成分1</translation>
    </message>
    <message>
        <location filename="view/ae/AEViewConfig.h" line="238"/>
        <source>Spectrum2</source>
        <translation>频率成分2</translation>
    </message>
</context>
<context>
    <name>AEWave</name>
    <message>
        <location filename="view/ae/aewaveview/AeWave.h" line="20"/>
        <source>Time</source>
        <translation>时间</translation>
    </message>
    <message>
        <location filename="view/ae/aewaveview/AeWave.h" line="21"/>
        <source>Amp.</source>
        <translation>幅值</translation>
    </message>
</context>
<context>
    <name>BJCurrentDetectionChart</name>
    <message>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="19"/>
        <source>Grounding Current</source>
        <translation type="unfinished">接地电流</translation>
    </message>
    <message>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="20"/>
        <source>Load Current</source>
        <translation type="unfinished">负荷电流</translation>
    </message>
    <message>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="21"/>
        <source>Load Current: </source>
        <translation type="unfinished">负荷电流：</translation>
    </message>
    <message>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="22"/>
        <source>Grounding / Load: </source>
        <translation type="unfinished">接地电流/负荷电流：</translation>
    </message>
    <message>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="23"/>
        <source>Max / Min: </source>
        <translation type="unfinished">最大值/最小值：</translation>
    </message>
</context>
<context>
    <name>BlueToothSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="19"/>
        <source>Bluetooth</source>
        <translation>蓝牙</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="19"/>
        <source>Asset</source>
        <translation>设备</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="186"/>
        <source>Connect to device: </source>
        <translation>连接设备：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="211"/>
        <source>Bluetooth connect success!</source>
        <translation>蓝牙连接成功！</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="220"/>
        <source>Bluetooth connect fail!</source>
        <translation>蓝牙连接失败！</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="258"/>
        <source>Open bluetooth fail!</source>
        <translation>打开蓝牙失败！</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="262"/>
        <source>Close bluetooth fail!</source>
        <translation>关闭蓝牙失败！</translation>
    </message>
</context>
<context>
    <name>CAMatchView</name>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="36"/>
        <source>CA Diag. Pairing</source>
        <translation>CA调理器匹配</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="289"/>
        <source>Please select CA Diag. processor.</source>
        <translation>请先选择CA调理器。</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="453"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="704"/>
        <source>Update success, please reboot HAS02.</source>
        <translation>更新成功，请重启HAS02。</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="718"/>
        <source>Confirm to update HAS02 firmware?</source>
        <translation>确定要更新HAS02的固件吗？</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="732"/>
        <source>No HAS02 connected!</source>
        <translation>没有已连接的HAS02设备！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="754"/>
        <source>CA Diag. not found!</source>
        <translation>未找到CA调理器！</translation>
    </message>
</context>
<context>
    <name>CAView</name>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="27"/>
        <source>Sample</source>
        <translation>采样</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="28"/>
        <source>Stop</source>
        <translation>停止</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="29"/>
        <source>Start Sample</source>
        <translation>开始采样</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="30"/>
        <source>Stop Sample</source>
        <translation>停止采样</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="31"/>
        <source>Zoom</source>
        <translation>缩放</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="32"/>
        <source>Phase Shift</source>
        <translation>相移</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="33"/>
        <source>Cluster Analyze</source>
        <translation>聚类分析</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="34"/>
        <source>Pulse Review</source>
        <translation>脉冲查看</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="35"/>
        <source>Save</source>
        <translation>保存数据</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="36"/>
        <source>Load Data</source>
        <translation>载入数据</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="37"/>
        <source>Delete Data</source>
        <translation>删除数据</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="38"/>
        <source>More...</source>
        <translation>更多...</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="39"/>
        <source>Sample Rate</source>
        <translation>采样率</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="40"/>
        <source>Sample Length</source>
        <translation>采样长度</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="41"/>
        <source>Trigger BW</source>
        <translation>触发宽度</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="42"/>
        <source>Sample Interval</source>
        <translation>采样间隔</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="43"/>
        <source>Sample Times</source>
        <translation>采样次数</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="44"/>
        <source>Percentage Before Trigger</source>
        <translation>触发前百分比</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="45"/>
        <source>Pulse Accumulation Count</source>
        <translation>脉冲累积个数</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="46"/>
        <source>Trigger AMP</source>
        <translation>触发幅值</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="47"/>
        <source>Trigger Value</source>
        <translation>触发值</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="48"/>
        <source>Gain</source>
        <translation>增益</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="49"/>
        <source>Input</source>
        <translation>输入</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="50"/>
        <source>Input Value</source>
        <translation>校验输入</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="51"/>
        <source>Verify</source>
        <translation>校验</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="52"/>
        <source>Start</source>
        <translation>开始</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="53"/>
        <source>Zero Drift</source>
        <translation>零漂</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="54"/>
        <source>RMS</source>
        <translation>有效值</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="55"/>
        <source>Channel</source>
        <translation>通道</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="56"/>
        <source>Calibrate</source>
        <translation>使用校准</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="57"/>
        <source>Accumulate</source>
        <translation>累积使能</translation>
    </message>
    <message>
        <source>Accumulative Time</source>
        <translation type="obsolete">累积时长</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="58"/>
        <source>Frequency Ref.</source>
        <translation>工频参考</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="59"/>
        <source>Default</source>
        <translation>恢复默认</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="60"/>
        <source>Marker</source>
        <translation>标记线</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="61"/>
        <source>Jump Max</source>
        <translation>跳转最大</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="62"/>
        <source>Jump Min</source>
        <translation>跳转最小</translation>
    </message>
    <message>
        <source>Noise Reduction</source>
        <translation type="obsolete">降噪</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="64"/>
        <source>Mode</source>
        <translation>模式</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="65"/>
        <source>Drawing</source>
        <translation>聚类绘制</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="66"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="67"/>
        <source>Select</source>
        <translation>选择</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="68"/>
        <source>Select Color</source>
        <translation>选择图簇</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="69"/>
        <source>Diagnostic</source>
        <translation>诊断</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="70"/>
        <source>Draw</source>
        <translation>绘制</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="71"/>
        <source>Move</source>
        <translation>移动</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="72"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="73"/>
        <source>Switch Mode</source>
        <translation>图谱切换</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="75"/>
        <source>No data to operate.</source>
        <translation>无数据无法执行该操作。</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="76"/>
        <source>Can not be executed.</source>
        <translation>当前模式无法执行该操作。</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="78"/>
        <source>Auto</source>
        <translation>自动</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="79"/>
        <source>Manual</source>
        <translation>手动</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="81"/>
        <source>All</source>
        <translation>全部</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="82"/>
        <source>Yellow</source>
        <translation>黄色</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="83"/>
        <source>Light Green</source>
        <translation>淡绿</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="84"/>
        <source>Red</source>
        <translation>红色</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="85"/>
        <source>Green</source>
        <translation>绿色</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="86"/>
        <source>Blue</source>
        <translation>蓝色</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="87"/>
        <source>Dark Green</source>
        <translation>深绿</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="110"/>
        <source>Yes</source>
        <translation>是</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="111"/>
        <source>No</source>
        <translation>否</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="112"/>
        <source>On</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="113"/>
        <source>Off</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="127"/>
        <source>Pulse Count</source>
        <translation>脉冲计数</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="128"/>
        <source>Disconnected</source>
        <translation>已断开</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="129"/>
        <source>Connected</source>
        <translation>已连接</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="130"/>
        <source>Disconnect</source>
        <translation>待连接</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="131"/>
        <source>Searching CA Diag. processor ...</source>
        <translation>CA调理器搜索中…</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="133"/>
        <source>View Sig Pulse</source>
        <translation>脉冲图谱</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="134"/>
        <source>View Sig Wave</source>
        <translation>波形图谱</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="135"/>
        <source>View PRPD&amp;PRPS</source>
        <translation>PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="136"/>
        <source>Pulse</source>
        <translation>脉冲图谱</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="137"/>
        <source>Waveform</source>
        <translation>波形图谱</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="138"/>
        <source>PRPD&amp;PRPS</source>
        <translation>PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="163"/>
        <source>Record</source>
        <translation>录屏</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="164"/>
        <source>Record Time</source>
        <translation>录屏时间</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="165"/>
        <source>Range</source>
        <translation>量程</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="166"/>
        <source>Playback</source>
        <translation>录屏回放</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="167"/>
        <source>Delete Record</source>
        <translation>删除录屏</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="174"/>
        <source>Original Ratio</source>
        <translation>原始比例</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="175"/>
        <source>Select Pulse</source>
        <translation>脉冲选择</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="176"/>
        <source>Previous</source>
        <translation>上一个</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="177"/>
        <source>Next</source>
        <translation>下一个</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="178"/>
        <source>Continuous Sample</source>
        <translation>连续采样</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="179"/>
        <source>Single Sample</source>
        <translation>单次采样</translation>
    </message>
    <message>
        <source>Level 1</source>
        <translation type="obsolete">等级1</translation>
    </message>
    <message>
        <source>Level 2</source>
        <translation type="obsolete">等级2</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="245"/>
        <source>Red Cluster</source>
        <translation>红色图蔟</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="246"/>
        <source>Yellow Cluster</source>
        <translation>黄色图簇</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="247"/>
        <source>Green Cluster</source>
        <translation>绿色图蔟</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="248"/>
        <source>Blue Cluster</source>
        <translation>蓝色图蔟</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.h" line="249"/>
        <source>All Cluster</source>
        <translation>全部图蔟</translation>
    </message>
</context>
<context>
    <name>CalibrateView</name>
    <message>
        <location filename="view/ca/wave/calibrate/calibrateview.cpp" line="212"/>
        <source>Disconnected, exit calibration!</source>
        <translation>连接已断开，退出校准！</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="13"/>
        <source>Calibration</source>
        <translation>校准</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="14"/>
        <source>Calibration Test</source>
        <translation>校准测试</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="15"/>
        <source>Gear: </source>
        <translation>挡位：</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="16"/>
        <source>Coefficient K: </source>
        <translation>系数K：</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="17"/>
        <source>Coefficient B: </source>
        <translation>系数B：</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="18"/>
        <source>Input: </source>
        <translation>输入：</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="19"/>
        <source>Signal</source>
        <translation>信号</translation>
    </message>
    <message>
        <source>Coefficient</source>
        <translation type="obsolete">系数</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="20"/>
        <source>Reference Array</source>
        <translation type="unfinished">参考数组</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="21"/>
        <source>First set of coefficients</source>
        <translation type="unfinished">第一组系数</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="22"/>
        <source>Second set of coefficients</source>
        <translation type="unfinished">第二组系数</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="23"/>
        <source>Third set of coefficients</source>
        <translation type="unfinished">第三组系数</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="24"/>
        <source>Output Value</source>
        <translation type="unfinished">输出值</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="25"/>
        <source>First set of reference arrays</source>
        <oldsource>First set of reference values</oldsource>
        <translation type="unfinished">第一组参考数组</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="26"/>
        <source>Second set of reference arrays</source>
        <oldsource>Second set of reference values</oldsource>
        <translation type="unfinished">第二组参考数组</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="27"/>
        <source>Third set of reference arrays</source>
        <oldsource>Third set of reference values</oldsource>
        <translation type="unfinished">第三组参考数组</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="29"/>
        <source>Calibrate</source>
        <translation>校准</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="30"/>
        <source>Write</source>
        <translation>写入</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="31"/>
        <source>Restore</source>
        <translation>恢复</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="32"/>
        <source>Test</source>
        <translation>测试</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="34"/>
        <source>Calibrating</source>
        <translation>校准中</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="35"/>
        <source>Calibrating Succeeded</source>
        <translation>校准成功</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="36"/>
        <source>Calibrating Failed</source>
        <translation>校准失败</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/calibrateviewdefine.h" line="37"/>
        <source>Not Calibrated</source>
        <translation>未校准</translation>
    </message>
</context>
<context>
    <name>CalibrateWidget</name>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="13"/>
        <source>Gain</source>
        <translation>增益</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="14"/>
        <source>Signal1 (RMS)</source>
        <translation>信号1(有效值)</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="15"/>
        <source>Signal2 (RMS)</source>
        <translation>信号2(有效值)</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="16"/>
        <source>Signal3 (RMS)</source>
        <translation>信号3(有效值)</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="17"/>
        <source>Signal4 (RMS)</source>
        <translation>信号4(有效值)</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="19"/>
        <source>Coefficient</source>
        <translation>系数</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="20"/>
        <source>Zero Drift</source>
        <translation>零漂</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="21"/>
        <source>Calibrated Value (VPP)</source>
        <translation>校准后的值(峰峰值)</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="25"/>
        <source>ST</source>
        <translation>ST</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibratewidget.cpp" line="26"/>
        <source>Calibrating</source>
        <translation>校准中</translation>
    </message>
</context>
<context>
    <name>ClockWindow</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/clock/ClockWindow.cpp" line="177"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>CommonView</name>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="22"/>
        <source>Connect App</source>
        <translation>连接终端</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="23"/>
        <source>Discon</source>
        <translation>断开</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="24"/>
        <source>Submit</source>
        <translation>提交</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="25"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="26"/>
        <source>Open</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="27"/>
        <source>Refresh</source>
        <translation>刷新</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="28"/>
        <source>Compress</source>
        <translation>压缩</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="29"/>
        <source>Download</source>
        <translation>下载</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/commonviewconfig.h" line="30"/>
        <source>Upload</source>
        <translation>上传</translation>
    </message>
</context>
<context>
    <name>ConditionereMatchView</name>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="316"/>
        <source>UHF Pairing</source>
        <translation>UHF调理器匹配</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="320"/>
        <source>HFCT Pairing</source>
        <translation>HFCT调理器匹配</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="324"/>
        <source>Sync. Pairing</source>
        <translation>同步器匹配</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="328"/>
        <source>AE Pairing</source>
        <translation>AE调理器匹配</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="332"/>
        <source>TEV Pairing</source>
        <translation>TEV调理器匹配</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="336"/>
        <source>Current Pairing</source>
        <translation>电流调理器匹配</translation>
    </message>
</context>
<context>
    <name>ConnectView</name>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="49"/>
        <source>Connect Terminal</source>
        <translation>终端接入</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="56"/>
        <source>Hotspot Connection</source>
        <translation>热点连接</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="60"/>
        <source>Bluetooth Connection</source>
        <translation>蓝牙连接</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="200"/>
        <source>Confirm to connect bluetooth?</source>
        <translation>确定要连接蓝牙？</translation>
    </message>
</context>
<context>
    <name>ConnectorView</name>
    <message>
        <location filename="view/customaccessUi/taskmodeview/connectorview.cpp" line="312"/>
        <source>Read load current failed!</source>
        <translation>获取负荷电流失败!</translation>
    </message>
</context>
<context>
    <name>CurrentDetectionChart</name>
    <message>
        <source>Grounding Current</source>
        <translation type="vanished">接地电流</translation>
    </message>
    <message>
        <source>Load Current</source>
        <translation type="vanished">负荷电流</translation>
    </message>
    <message>
        <source>Load Current: </source>
        <translation type="vanished">负荷电流：</translation>
    </message>
    <message>
        <source>Grounding / Load: </source>
        <translation type="vanished">接地电流/负荷电流：</translation>
    </message>
    <message>
        <source>Max / Min: </source>
        <translation type="vanished">最大值/最小值：</translation>
    </message>
</context>
<context>
    <name>CurrentDetectionView</name>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="24"/>
        <source>Current Detection</source>
        <translation>电流检测</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="25"/>
        <source>Sample</source>
        <translation>采样</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="26"/>
        <source>Grounding Alarm</source>
        <translation>接地报警</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="27"/>
        <source>Phase Selection</source>
        <translation>相位选择</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="28"/>
        <source>Comprehensive Analysis</source>
        <translation>综合分析</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="29"/>
        <source>Load Alarm</source>
        <translation>负荷报警</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="30"/>
        <source>Delete Data</source>
        <translation>删除数据</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="31"/>
        <source>Save</source>
        <translation>保存数据</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="32"/>
        <source>More...</source>
        <translation>更多...</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="33"/>
        <source>Load Data</source>
        <translation>载入数据</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="34"/>
        <source>Start</source>
        <translation>开始</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="35"/>
        <source>Stop</source>
        <translation>停止</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="36"/>
        <source>Phase A</source>
        <translation>A相</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="37"/>
        <source>Phase B</source>
        <translation>B相</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="38"/>
        <source>Phase C</source>
        <translation>C相</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="39"/>
        <source>Phase N</source>
        <translation>总接地</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="40"/>
        <source>Mode</source>
        <translation type="unfinished">模式</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="41"/>
        <source>Single Sample</source>
        <translation type="unfinished">单次采样</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="42"/>
        <source>Single</source>
        <translation type="unfinished">单次</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="43"/>
        <source>Continuous</source>
        <translation type="unfinished">连续</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="44"/>
        <source>Save RFID</source>
        <translation type="unfinished">RFID保存</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="45"/>
        <source>Range Gear</source>
        <translation type="unfinished">量程档位</translation>
    </message>
    <message>
        <source>Test Type</source>
        <translation type="obsolete">测试类型</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="46"/>
        <source>Cable Grounding</source>
        <translation type="unfinished">电缆接地</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="47"/>
        <source>Load Current</source>
        <translation type="unfinished">负荷电流</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionviewdefine.h" line="48"/>
        <source>Core Grounding</source>
        <translation type="unfinished">铁芯接地</translation>
    </message>
</context>
<context>
    <name>CurrentPhaseTypeView</name>
    <message>
        <location filename="view/customaccessUi/taskmodeview/currentphasetypeview.cpp" line="441"/>
        <source>Read current failed!</source>
        <translation type="unfinished">获取电流失败!</translation>
    </message>
</context>
<context>
    <name>CustomAccessTaskIO</name>
    <message>
        <location filename="mobileAccess/customaccesstask/taskio.h" line="22"/>
        <source>BG Bay</source>
        <translation>背景间隔</translation>
    </message>
    <message>
        <location filename="mobileAccess/customaccesstask/taskio.h" line="23"/>
        <source>BG Test Point</source>
        <translation>背景测点</translation>
    </message>
</context>
<context>
    <name>DateTimeSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/datetimesettingview.cpp" line="72"/>
        <source>Date &amp; Time</source>
        <translation>日期时间设置</translation>
    </message>
</context>
<context>
    <name>DateView</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/date/DateView.cpp" line="402"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
</context>
<context>
    <name>Desktop</name>
    <message>
        <location filename="view/Desktop.cpp" line="431"/>
        <source>Error, charge the battery and upgrade firmware later.</source>
        <translation>外设运行失败，请充电后执行固件更新。</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="462"/>
        <source>TEV</source>
        <translation>地电波</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="470"/>
        <source>AE</source>
        <translation>超声波</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="478"/>
        <source>UHF</source>
        <translation>特高频</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="486"/>
        <source>HFCT</source>
        <translation>高频电流</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="494"/>
        <source>CA Diag.</source>
        <translation>电流分析</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="499"/>
        <source>Infrared</source>
        <translation>红外</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="504"/>
        <source>Current</source>
        <translation>电流检测</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="509"/>
        <source>Patrol</source>
        <translation>智能巡检</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="514"/>
        <source>C-APP</source>
        <translation>接入终端</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="523"/>
        <source>RFID</source>
        <translation>RFID</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="528"/>
        <source>Demo Video</source>
        <translation>演示视频</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="534"/>
        <location filename="view/Desktop.cpp" line="605"/>
        <source>Pairing</source>
        <translation>外设匹配</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="536"/>
        <source>Audio</source>
        <translation>音频</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="537"/>
        <location filename="view/Desktop.cpp" line="606"/>
        <location filename="view/Desktop.cpp" line="1242"/>
        <source>Settings</source>
        <translation>设置</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="562"/>
        <source>Task Mode</source>
        <translation>任务模式</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="600"/>
        <source>Detection Mode</source>
        <translation>检测模式</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="624"/>
        <source>Self-Check failed, please go to Self-Check page for details.</source>
        <translation>自检失败，请进入自检页面查看详情。</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="639"/>
        <source>The calibration date has expired, please apply for calibration service.</source>
        <translation>校准日期已到，请进行校准。</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="960"/>
        <source>QR</source>
        <translation>QR</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="967"/>
        <source>Environment</source>
        <translation>环境信息</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="974"/>
        <source>GPS</source>
        <translation>GPS</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="1105"/>
        <source>Functional configuration has been modified, confirm to take effect after auto shutdown and restart manually?</source>
        <translation>功能配置已修改，确定自动关机并手动重启后生效？</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="1121"/>
        <source>Device is not in the activation date.</source>
        <translation>设备未处于激活期限内。</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="1208"/>
        <source>The device has been activated successfully.</source>
        <translation>设备已激活成功。</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="1227"/>
        <source>Please enter the patrol function to download tasks.</source>
        <translation>请进入智能巡检功能下载任务。</translation>
    </message>
</context>
<context>
    <name>DetectionModeView</name>
    <message>
        <location filename="view/Desktop.cpp" line="576"/>
        <source>AE</source>
        <translation>超声波</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="581"/>
        <source>UHF</source>
        <translation>特高频</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="586"/>
        <source>HFCT</source>
        <translation>高频电流</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="588"/>
        <source>Infrared</source>
        <translation>红外</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="590"/>
        <source>Current Detection</source>
        <translation>电流检测</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="571"/>
        <source>TEV</source>
        <translation>地电波</translation>
    </message>
    <message>
        <source>Audio</source>
        <translation type="vanished">音频</translation>
    </message>
</context>
<context>
    <name>DeviceSelfInspectionView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="108"/>
        <source>Stop self-checking ...</source>
        <translation>停止自检中…</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="251"/>
        <source>Stop Self-Check</source>
        <translation>停止自检</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="255"/>
        <source>Start Self-Check</source>
        <translation>开始自检</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="268"/>
        <source>Unknown</source>
        <translation>未知</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="271"/>
        <source>WiFi</source>
        <translation>WiFi</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="274"/>
        <source>Disk</source>
        <translation>磁盘</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="277"/>
        <source>RTC</source>
        <translation>RTC</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="280"/>
        <source>RFID</source>
        <translation>RFID</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="283"/>
        <source>AE</source>
        <translation>AE</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/deviceselfinspectionview.cpp" line="286"/>
        <source>TEV</source>
        <translation>TEV</translation>
    </message>
</context>
<context>
    <name>DiagConfig</name>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="28"/>
        <source>Diagnostic Result: </source>
        <translation>诊断结果：</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="29"/>
        <source>Normal</source>
        <translation>正常</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="30"/>
        <source>Abnormal</source>
        <translation>异常</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="31"/>
        <source>Medium</source>
        <translation>一般</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="32"/>
        <source>High</source>
        <translation>严重</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="33"/>
        <source>Minor</source>
        <translation>一般</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="34"/>
        <source>Serious</source>
        <translation>严重</translation>
    </message>
    <message>
        <location filename="widget/DiagnosisConfig.h" line="35"/>
        <source>Emergency</source>
        <translation>紧急</translation>
    </message>
</context>
<context>
    <name>DistributeNetAccessNS</name>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="30"/>
        <source>Cabinet Front</source>
        <translation>柜前</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="31"/>
        <source>Cabinet Side</source>
        <translation>柜侧</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="32"/>
        <source>Cabinet Back</source>
        <translation>柜后</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="33"/>
        <source>TEV Detect</source>
        <translation>TEV检测</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="34"/>
        <source>AE Detect</source>
        <translation>AE检测</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="35"/>
        <source>UHF Detect</source>
        <translation>UHF检测</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="36"/>
        <source>HFCT Detect</source>
        <translation>HFCT检测</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="37"/>
        <source>BKGD Detect</source>
        <translation>背景检测</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="38"/>
        <source>TEV BKGD</source>
        <translation>TEV背景检测</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="39"/>
        <source>AE BKGD</source>
        <translation>AE背景检测</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="40"/>
        <source>UHF BKGD</source>
        <translation>UHF背景检测</translation>
    </message>
    <message>
        <location filename="mobileAccess/distributenetaccess/distributenetaccess_def.h" line="41"/>
        <source>HFCT BKGD</source>
        <translation>HFCT背景检测</translation>
    </message>
</context>
<context>
    <name>DistributeNetAddTaskView</name>
    <message>
        <location filename="view/distributenetaccess/distributenetaddtaskview.cpp" line="43"/>
        <source>Asset Information</source>
        <translation>设备信息</translation>
    </message>
</context>
<context>
    <name>DistributeNetDeviceView</name>
    <message>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="219"/>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="372"/>
        <source>Comfirm to detect the %1 ?</source>
        <translation>确定要检测%1吗？</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="223"/>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="376"/>
        <source>Comfirm to detect the &quot;%1&quot; of %2 ?</source>
        <translation>确定要检测%2的“%1”位置吗？</translation>
    </message>
</context>
<context>
    <name>DistributeNetTaskView</name>
    <message>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="207"/>
        <source>No task has been chosen.</source>
        <translation>没有选中任务。</translation>
    </message>
</context>
<context>
    <name>DistributeNetView</name>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="13"/>
        <source>Open</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="14"/>
        <source>New</source>
        <translation>新建任务</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="15"/>
        <source>Refresh</source>
        <translation>刷新</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="16"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="17"/>
        <source>Sift</source>
        <translation>筛选</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="18"/>
        <source>Remind</source>
        <translation>提醒</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="19"/>
        <source>On</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="20"/>
        <source>Off</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="21"/>
        <source>Cabinet Front</source>
        <translation>柜前</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="22"/>
        <source>Cabinet Back</source>
        <translation>柜后</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="23"/>
        <source>Cabinet Top</source>
        <translation>柜顶</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="24"/>
        <source>Cabinet Bottom</source>
        <translation>柜底</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="25"/>
        <source>Cabinet Left</source>
        <translation>柜左</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetviewconfig.h" line="26"/>
        <source>Cabinet Right</source>
        <translation>柜右</translation>
    </message>
</context>
<context>
    <name>EnvironmentInfo</name>
    <message>
        <location filename="view/environment/environmentinfo.cpp" line="8"/>
        <source>Temperature: </source>
        <translation>温度：</translation>
    </message>
    <message>
        <location filename="view/environment/environmentinfo.cpp" line="9"/>
        <source>Humidity: </source>
        <translation>湿度：</translation>
    </message>
    <message>
        <location filename="view/environment/environmentinfo.cpp" line="10"/>
        <source>Pressure: </source>
        <translation>气压：</translation>
    </message>
</context>
<context>
    <name>FileCommentBox</name>
    <message>
        <location filename="view/widgets/filecommentbox/filecommentbox.cpp" line="35"/>
        <source>Remark</source>
        <translation>备注</translation>
    </message>
    <message>
        <location filename="view/widgets/filecommentbox/filecommentbox.cpp" line="222"/>
        <source>Cannot contain the following special symbols:</source>
        <translation>不能包含以下特殊符号：</translation>
    </message>
    <message>
        <location filename="view/widgets/filecommentbox/filecommentbox.cpp" line="231"/>
        <source>The number of characters exceeds the limit.</source>
        <translation>字符个数超限。</translation>
    </message>
</context>
<context>
    <name>FilterSettingDlg</name>
    <message>
        <location filename="view/distributenetaccess/filtersettingdlg.cpp" line="50"/>
        <source>Keyword</source>
        <translation>关键词</translation>
    </message>
</context>
<context>
    <name>GPSView</name>
    <message>
        <location filename="view/gps/gpsview.cpp" line="17"/>
        <source>Location: </source>
        <translation>位置：</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="18"/>
        <source>Time: </source>
        <translation>时间：</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="19"/>
        <source>Satellite Num: </source>
        <translation>卫星数量：</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="20"/>
        <source>Degree</source>
        <translation>度</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="21"/>
        <source>Minute</source>
        <translation>分</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="22"/>
        <source>Hour</source>
        <translation>时</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="23"/>
        <source>Second</source>
        <translation>秒</translation>
    </message>
    <message>
        <location filename="view/gps/gpsview.cpp" line="24"/>
        <source>Msec</source>
        <translation>毫秒</translation>
    </message>
</context>
<context>
    <name>HFCTPeriodChart</name>
    <message>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="23"/>
        <source>Upper Limit: </source>
        <translation>上限：</translation>
    </message>
    <message>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="24"/>
        <source>Lower Limit: </source>
        <translation>下限：</translation>
    </message>
    <message>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="25"/>
        <source>Range: </source>
        <translation>量程：</translation>
    </message>
    <message>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="26"/>
        <source>Max: </source>
        <translation>最大值：</translation>
    </message>
</context>
<context>
    <name>HFCTView</name>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="29"/>
        <source>Mode</source>
        <translation>模式</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="30"/>
        <source>Start</source>
        <translation>开始</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="31"/>
        <source>Stop</source>
        <translation>停止</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="32"/>
        <source>Pause</source>
        <translation>暂停</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="33"/>
        <source>Single Sample</source>
        <translation>单次采样</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="34"/>
        <source>Single</source>
        <translation>单次</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="35"/>
        <source>Continuous</source>
        <translation>连续</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="36"/>
        <source>Warning</source>
        <translation>黄色报警</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="37"/>
        <source>High Risk</source>
        <translation>红色报警</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="38"/>
        <source>Gain</source>
        <translation>增益</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="39"/>
        <source>Default</source>
        <translation>恢复默认</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="40"/>
        <source>Power</source>
        <translation>电源</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="41"/>
        <source>Light</source>
        <translation>光</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="42"/>
        <source>Sync Mode</source>
        <translation>同步源</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="43"/>
        <location filename="view/hfct/HFCTViewConfig.h" line="55"/>
        <source>More...</source>
        <translation>更多...</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="44"/>
        <source>Save Data</source>
        <translation>保存数据</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="45"/>
        <source>Save</source>
        <translation>保存</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="46"/>
        <source>Save RFID</source>
        <translation>RFID保存</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="47"/>
        <source>Delete Data</source>
        <translation>删除数据</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="48"/>
        <source>Load Data</source>
        <translation>载入数据</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="49"/>
        <source>Phase Shift</source>
        <translation>相移</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="50"/>
        <source>On</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="51"/>
        <location filename="view/hfct/HFCTViewConfig.h" line="81"/>
        <source>Off</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="52"/>
        <source>Add</source>
        <translation>新增</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="53"/>
        <source>Accumulate</source>
        <translation>累积使能</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="54"/>
        <source>Accumulative Time</source>
        <translation>累积时长</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="56"/>
        <source>Threshold</source>
        <translation>阈值</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="57"/>
        <source>Noise Reduction</source>
        <translation>降噪</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="58"/>
        <source>Altas Type</source>
        <translation>图谱类型</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="64"/>
        <source>Record</source>
        <translation>录屏</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="65"/>
        <source>Record Time</source>
        <translation>录屏时间</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="66"/>
        <source>Playback</source>
        <translation>录屏回放</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="67"/>
        <source>Delete Record</source>
        <translation>删除录屏</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="69"/>
        <source>View Sig Amp</source>
        <translation>幅值图谱</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="70"/>
        <source>View Period</source>
        <translation>周期图谱</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="71"/>
        <source>View PRPD&amp;PRPS</source>
        <translation>PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="72"/>
        <source>Amplitude</source>
        <translation>幅值图谱</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="73"/>
        <source>Period</source>
        <translation>周期图谱</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="74"/>
        <source>PRPD&amp;PRPS</source>
        <translation>PRPD&amp;PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="75"/>
        <source>PRPS</source>
        <translation>PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="76"/>
        <source>PRPD</source>
        <translation>PRPD图谱</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="80"/>
        <source>Auto</source>
        <translation>自动</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="82"/>
        <source>Level 1</source>
        <translation>等级1</translation>
    </message>
    <message>
        <location filename="view/hfct/HFCTViewConfig.h" line="83"/>
        <source>Level 2</source>
        <translation>等级2</translation>
    </message>
</context>
<context>
    <name>Infrared</name>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="29"/>
        <source>Temp. Points Count</source>
        <translation>温度点个数</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="30"/>
        <source>Unit(℃)</source>
        <translation>单位(℃)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="31"/>
        <source>Freeze</source>
        <translation>冻结</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="32"/>
        <source>Restore</source>
        <translation>恢复</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="33"/>
        <source>Palette</source>
        <translation>调色板</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="35"/>
        <source>Iron Red</source>
        <translation>铁红</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="36"/>
        <source>Rainbow</source>
        <translation>彩虹</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="37"/>
        <source>Red Heat</source>
        <translation>红热</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="38"/>
        <source>Black Heat</source>
        <translation>黑热</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="39"/>
        <source>White Heat</source>
        <translation>白热</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="40"/>
        <source>Lava</source>
        <translation>熔岩</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="41"/>
        <source>Medical</source>
        <translation>医疗</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="42"/>
        <source>Hot Iron</source>
        <translation>热铁</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="43"/>
        <source>Ink Brown</source>
        <translation>墨褐</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="44"/>
        <source>Miao Hong</source>
        <translation>描红</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="46"/>
        <source>Point</source>
        <translation>点</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="47"/>
        <source>Line</source>
        <translation>直线</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="48"/>
        <source>Rectangle</source>
        <translation>矩形</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="49"/>
        <source>Circle</source>
        <translation>圆</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="50"/>
        <source>Analyze</source>
        <translation>分析图形</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="51"/>
        <source>Line Temp.</source>
        <translation>线温度</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="52"/>
        <source>Del Graph</source>
        <translation>删除图形</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="53"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="54"/>
        <source>Delete All</source>
        <translation>全部删除</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="55"/>
        <source>PgUp</source>
        <translation>上翻页</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="56"/>
        <source>PgDn</source>
        <translation>下翻页</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="57"/>
        <source>Setting</source>
        <translation>设置</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="58"/>
        <source>More...</source>
        <translation>更多...</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="59"/>
        <source>Measurement Method</source>
        <translation type="unfinished">测量方式</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="61"/>
        <source>Save Data</source>
        <translation>保存数据</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="62"/>
        <source>Save</source>
        <translation>保存</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="63"/>
        <source>Load Data</source>
        <translation>载入数据</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="64"/>
        <source>Delete Data</source>
        <translation>删除数据</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="66"/>
        <source>Playback</source>
        <translation>数据回放</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="68"/>
        <source>IR Image</source>
        <translation>红外成像</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="70"/>
        <source>Stop Play</source>
        <translation>停止回放</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="71"/>
        <source>Add</source>
        <translation>新增</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="72"/>
        <source>Exit</source>
        <translation>退出</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="73"/>
        <source>Return</source>
        <translation>返回</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="74"/>
        <source>Auto Focus</source>
        <translation>自动对焦</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="75"/>
        <source>Close Focus Fine-Tuning</source>
        <translation>近焦微调</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="76"/>
        <source>Far Focus Fine-Tuning</source>
        <translation>远焦微调</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="77"/>
        <source>Electronic Zoom</source>
        <translation>电子变焦</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="78"/>
        <source>Laser Control</source>
        <translation>激光控制</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="79"/>
        <source>On</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="80"/>
        <source>Off</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="81"/>
        <source>LED Fill Light</source>
        <translation>LED补光</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="82"/>
        <source>Display Mode</source>
        <translation type="unfinished">展示模式</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="83"/>
        <source>Infrared</source>
        <translation type="unfinished">红外</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="84"/>
        <source>Picture in Picture</source>
        <translation type="unfinished">画中画</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="85"/>
        <source>Digital Camera</source>
        <translation type="unfinished">数码相机</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetstring.h" line="86"/>
        <source>Auxiliary Lighting</source>
        <translation>辅助照明</translation>
    </message>
</context>
<context>
    <name>InfraredParameterDialog</name>
    <message>
        <location filename="widget/infrared/infraredparameterdialog.cpp" line="199"/>
        <source>Empty value.</source>
        <translation>数值不能为空。</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredparameterdialog.cpp" line="209"/>
        <source>Invalid symbol.</source>
        <translation>非法字符。</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredparameterdialog.cpp" line="220"/>
        <source>The value is out of range</source>
        <translation>数值越界</translation>
    </message>
</context>
<context>
    <name>InfraredWidget</name>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="29"/>
        <source>Emissivity</source>
        <translation>辐射率</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="30"/>
        <source>Distance(m)</source>
        <translation>测试距离(m)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="31"/>
        <source>Air Temp.(℃)</source>
        <translation>大气温度(℃)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="32"/>
        <source>Reflected Temp.(℃)</source>
        <translation>反射温度(℃)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="33"/>
        <source>Ext. Optical Temp.(℃)</source>
        <translation>外部光学温度(℃)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="34"/>
        <source>Ext. Optical Tx Rate</source>
        <translation>外部光学传输率</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="35"/>
        <source>Relative Humidity(%)</source>
        <translation>相对湿度(%)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="36"/>
        <source>Reference Temp.(℃)</source>
        <translation>参考温度(℃)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="37"/>
        <source>Air Temp.(℉)</source>
        <translation>大气温度(℉)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="38"/>
        <source>Reflected Temp.(℉)</source>
        <translation>反射温度(℉)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="39"/>
        <source>Ext. Optical Temp.(℉)</source>
        <translation>外部光学温度(℉)</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredwidgetdefine.h" line="40"/>
        <source>Reference Temp.(℉)</source>
        <translation>参考温度(℉)</translation>
    </message>
</context>
<context>
    <name>IntervalView</name>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="120"/>
        <source>Auto switch to next undetected point.</source>
        <translation>自动进入下一个未测测点。</translation>
    </message>
</context>
<context>
    <name>LanguageSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/languagesettingview.cpp" line="78"/>
        <location filename="view/systemsetview/systemsetwidget/languagesettingview.cpp" line="249"/>
        <source>Language</source>
        <translation>语言设置</translation>
    </message>
</context>
<context>
    <name>LineTemperatureCurveDialog</name>
    <message>
        <location filename="widget/infrared/linetemperaturecurvedialog.cpp" line="27"/>
        <source>Line Temperature</source>
        <translation>线温度分布</translation>
    </message>
</context>
<context>
    <name>LoginViewBase</name>
    <message>
        <location filename="view/widgets/loginview/loginviewbase.cpp" line="38"/>
        <source>User Name</source>
        <translation>用户名</translation>
    </message>
    <message>
        <location filename="view/widgets/loginview/loginviewbase.cpp" line="39"/>
        <source>Password</source>
        <translation>密码</translation>
    </message>
    <message>
        <location filename="view/widgets/loginview/loginviewbase.cpp" line="40"/>
        <source>Login</source>
        <translation>登录</translation>
    </message>
    <message>
        <location filename="view/widgets/loginview/loginviewbase.cpp" line="41"/>
        <source>Please input user name or password.</source>
        <translation>请输入用户名或密码。</translation>
    </message>
</context>
<context>
    <name>MainTaskView</name>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="152"/>
        <source>Reading the task list ...</source>
        <translation type="unfinished">读取任务列表中…</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="698"/>
        <source>Uploading the task...</source>
        <translation>上传任务中...</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="726"/>
        <source>Downloading the task...</source>
        <translation>下载任务中...</translation>
    </message>
</context>
<context>
    <name>MeasureSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/measuresettingview.cpp" line="58"/>
        <source>Measure</source>
        <translation>测量</translation>
    </message>
</context>
<context>
    <name>NetSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/netsettingview.cpp" line="60"/>
        <source>Network</source>
        <translation>网络设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/netsettingview.cpp" line="159"/>
        <source>Open 4G failed.</source>
        <translation>打开4G失败。</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/netsettingview.cpp" line="183"/>
        <source>Close 4G failed.</source>
        <translation>关闭4G失败。</translation>
    </message>
</context>
<context>
    <name>NetWorkTestDialog</name>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="26"/>
        <source>Dialog</source>
        <translation>对话框</translation>
    </message>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="50"/>
        <source>Http Send</source>
        <translation>Http发送</translation>
    </message>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="74"/>
        <source>Clear Log</source>
        <translation>清除日志</translation>
    </message>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="101"/>
        <source>Close</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="126"/>
        <source>Tcp Send</source>
        <translation>Tcp发送</translation>
    </message>
    <message>
        <location filename="view/networktest/networktestdialog.ui" line="148"/>
        <source>this is log</source>
        <translation>这是日志信息</translation>
    </message>
</context>
<context>
    <name>PDACurrentTestDataView</name>
    <message>
        <source>Nonsupport test type.</source>
        <translation type="obsolete">不支持的测试类型。</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="516"/>
        <source>Read current failed!</source>
        <translation type="unfinished">获取电流失败!</translation>
    </message>
</context>
<context>
    <name>PDADeviceDetailView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="252"/>
        <source>Device Info</source>
        <translation>设备信息</translation>
    </message>
</context>
<context>
    <name>PDAFileView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="102"/>
        <source>All testing is completed.</source>
        <translation>所有设备已测完毕。</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="359"/>
        <source>Not select a device!</source>
        <translation>没有选择设备！</translation>
    </message>
</context>
<context>
    <name>PDALoginView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdaloginview.cpp" line="39"/>
        <source>User Name</source>
        <translation>用户名</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdaloginview.cpp" line="40"/>
        <source>Password</source>
        <translation>密码</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdaloginview.cpp" line="41"/>
        <source>Login</source>
        <translation>登录</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdaloginview.cpp" line="42"/>
        <source>Please input user name or password.</source>
        <translation>请输入用户名或密码。</translation>
    </message>
</context>
<context>
    <name>PDAPatrolTypeView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdapatroltypeview.cpp" line="18"/>
        <source>Patrol Type</source>
        <translation>巡检类型</translation>
    </message>
</context>
<context>
    <name>PDASiftTaskView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="107"/>
        <source>Task Sift</source>
        <translation>任务筛选</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="114"/>
        <source>Task Platform: </source>
        <translation>任务创建平台：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="127"/>
        <source>Task Status: </source>
        <translation>任务状态：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="139"/>
        <source>Task File Period: </source>
        <translation>任务创建时间区间：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="155"/>
        <source>Task Plan Time Interval: </source>
        <translation>任务执行时间区间：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="172"/>
        <source>Transformer Substation: </source>
        <translation>任务所属的变电站：</translation>
    </message>
</context>
<context>
    <name>PDATaskView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="214"/>
        <source>No task has been chosen.</source>
        <translation>没有选中任务。</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="548"/>
        <source>Unable to open multiple task files.</source>
        <translation>无法打开多个任务文件。</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="746"/>
        <source>Reading the task list ...</source>
        <translation>读取任务列表中…</translation>
    </message>
</context>
<context>
    <name>PDATestDataView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdatestdataview.cpp" line="186"/>
        <source>Nonsupport test type.</source>
        <translation>不支持的测试类型。</translation>
    </message>
</context>
<context>
    <name>PDAView</name>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="26"/>
        <source>Open</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="27"/>
        <source>Close</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="28"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="29"/>
        <source>Select All</source>
        <translation>全选</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="30"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="31"/>
        <source>Download</source>
        <translation>下载</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="32"/>
        <source>Upload</source>
        <translation>上传</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="33"/>
        <source>Refresh</source>
        <translation>刷新</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="34"/>
        <source>Sift</source>
        <translation>筛选</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="35"/>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="114"/>
        <source>RFID</source>
        <translation>RFID</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="36"/>
        <source>Details</source>
        <translation>详情</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="37"/>
        <source>Low Pass PRPS</source>
        <translation>低通PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="38"/>
        <source>High Pass PRPS</source>
        <translation>高通PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="39"/>
        <source>All Pass PRPS</source>
        <translation>全通PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="40"/>
        <source>Amp. Detection</source>
        <translation>幅值检测</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="41"/>
        <source>Pulse Detection</source>
        <translation>脉冲检测</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="42"/>
        <source>Phase Detection</source>
        <translation>相位检测</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="43"/>
        <source>Fly Detection</source>
        <translation>飞行检测</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="44"/>
        <source>Wave Detection</source>
        <translation>波形检测</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="46"/>
        <source>Switchgear</source>
        <translation>开关柜</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="47"/>
        <source>GIS</source>
        <translation>组合电器</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="48"/>
        <source>Transformer</source>
        <translation>变压器</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="49"/>
        <source>Cable</source>
        <translation>电力电缆</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="51"/>
        <source>Multi Spectrum</source>
        <translation>多图谱</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="52"/>
        <source>HF Multi Spectrum</source>
        <translation>高频多图谱</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="53"/>
        <source>HF PRPD Spectrum</source>
        <translation>高频PRPD图谱</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="54"/>
        <source>HF PRPS Spectrum</source>
        <translation>高频PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="55"/>
        <source>PRPD&amp;PRPS</source>
        <translation>PRPD&amp;PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="56"/>
        <source>HF Pulse Wave Spectrum</source>
        <translation>高频脉冲波形图</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="57"/>
        <source>UHF Multi Spectrum</source>
        <translation>UHF多图谱</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="58"/>
        <source>UHF PRPD</source>
        <translation>UHF PRPD</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="59"/>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="76"/>
        <source>UHF PRPS</source>
        <translation>UHF PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="60"/>
        <source>UHF Peak Statistical Chart</source>
        <translation>UHF峰值统计图</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="61"/>
        <source>AE Multi Spectrum</source>
        <translation>AE多图谱</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="62"/>
        <source>TEV Multi Spectrum</source>
        <translation>TEV多图谱</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="63"/>
        <source>TEV PRPS</source>
        <translation>TEV PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="64"/>
        <source>Picture</source>
        <translation>图片</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="65"/>
        <source>Audio</source>
        <translation>音频</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="66"/>
        <source>Video</source>
        <translation>视频</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="68"/>
        <source>TEV Amp.</source>
        <translation>TEV幅值</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="69"/>
        <source>TEV Pulse</source>
        <translation>TEV脉冲</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="70"/>
        <source>AE Amp.</source>
        <translation>AE幅值</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="71"/>
        <source>AE Wave</source>
        <translation>AE波形</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="72"/>
        <source>AE Phase</source>
        <translation>AE相位</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="73"/>
        <source>AE Amp.(Spectrum)</source>
        <translation>AE幅值(图谱)</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="74"/>
        <source>AE Fly</source>
        <translation>AE飞行</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="75"/>
        <source>UHF Amp.</source>
        <translation>UHF幅值</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="122"/>
        <source>Load Current</source>
        <translation type="unfinished">负荷电流</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="123"/>
        <source>Cable Grounding</source>
        <translation type="unfinished">电缆接地</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="124"/>
        <source>Core Grounding</source>
        <translation type="unfinished">铁芯接地</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="125"/>
        <source>Leakage Current</source>
        <translation type="unfinished">泄漏电流</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="126"/>
        <source>Clamp Grounding</source>
        <translation type="unfinished">夹件接地</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="127"/>
        <source>A Phase Grounding</source>
        <translation type="unfinished">A相接地</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="128"/>
        <source>B Phase Grounding</source>
        <translation type="unfinished">B相接地</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="129"/>
        <source>C Phase Grounding</source>
        <translation type="unfinished">C相接地</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="130"/>
        <source>Common Grounding</source>
        <translation type="unfinished">公共接地</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="77"/>
        <source>UHF Period</source>
        <translation>UHF周期</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="78"/>
        <source>HFCT Amp.</source>
        <translation>HFCT幅值</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="79"/>
        <source>HFCT PRPS</source>
        <translation>HFCT PRPS</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="80"/>
        <source>HFCT Period</source>
        <translation>HFCT周期</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="81"/>
        <source>Infrared</source>
        <translation>红外</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="83"/>
        <source>Mode</source>
        <translation>模式</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="84"/>
        <source>Single</source>
        <translation>单选</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="85"/>
        <source>Multiple</source>
        <translation>多选</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="87"/>
        <source>APP Platform</source>
        <translation>APP平台</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="88"/>
        <source>CMS Platform</source>
        <translation>CMS平台</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="89"/>
        <source>PDST Platform</source>
        <translation>PDST平台</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="90"/>
        <source>PDStar Platform</source>
        <translation>PDStar平台</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="91"/>
        <source>T95 Platform</source>
        <translation>T95平台</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="92"/>
        <source>T90 Platform</source>
        <translation>T90平台</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="93"/>
        <source>All Platform</source>
        <translation>所有平台</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="95"/>
        <source>New</source>
        <translation>新建任务</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="96"/>
        <source>New Task Not Pass</source>
        <translation>新建任务审核未通过</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="97"/>
        <source>New Task Passed</source>
        <translation>新建任务已审核</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="98"/>
        <source>Accepted</source>
        <translation>任务已接受</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="99"/>
        <source>Data Uploaded</source>
        <translation>数据已上传</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="100"/>
        <source>Submitted</source>
        <translation>任务已提交</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="101"/>
        <source>Data First Check Passed</source>
        <translation>测试数据通过一级审核</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="102"/>
        <source>Data First Check Not Pass</source>
        <translation>测试数据未通过一级审核</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="103"/>
        <source>Data Second Check Passed</source>
        <translation>测试数据通过二级审核</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="104"/>
        <source>Data Second Check Not Pass</source>
        <translation>测试数据未通过二级审核</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="105"/>
        <source>DOC Check Passed</source>
        <translation>档案审核通过</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="106"/>
        <source>DOC Check Not Pass</source>
        <translation>档案审核未通过</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="107"/>
        <source>Declined</source>
        <translation>任务被拒绝</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="108"/>
        <source>Canceled</source>
        <translation>任务被取消</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="109"/>
        <source>Submit</source>
        <translation>提交</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="110"/>
        <source>All State</source>
        <translation>所有状态</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="112"/>
        <source>Detection Mode</source>
        <translation>检测模式</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="113"/>
        <source>Auto</source>
        <translation>自动</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="116"/>
        <source>Select Patrol Position</source>
        <translation>选择巡检部位</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="117"/>
        <source>Default</source>
        <translation>默认</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="118"/>
        <source>Cabinet Front</source>
        <translation>柜前</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="119"/>
        <source>Cabinet Side</source>
        <translation>柜侧</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/PDAViewConfig.h" line="120"/>
        <source>Cabinet Back</source>
        <translation>柜后</translation>
    </message>
</context>
<context>
    <name>PMView</name>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="78"/>
        <source>UHF</source>
        <translation>特高频</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="83"/>
        <source>HFCT</source>
        <translation>高频电流</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="88"/>
        <source>CA Diag.</source>
        <translation>电流分析</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="93"/>
        <source>AE</source>
        <translation>超声波</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="98"/>
        <source>Current</source>
        <translation>电流检测</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmview.cpp" line="109"/>
        <source>Power Sync.</source>
        <translation>同步器</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="26"/>
        <source>UHF Signal Processor</source>
        <translation>特高频调理器选择</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="27"/>
        <source>HFCT Signal Processor</source>
        <translation>高频电流调理器选择</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="28"/>
        <source>Sync. Signal Processor</source>
        <translation>同步器选择</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="29"/>
        <source>Receiver Pairing</source>
        <translation>接收器选择</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="30"/>
        <source>External Pairing</source>
        <translation>外部设备选择</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="31"/>
        <source>AE Signal Processor</source>
        <translation>超声调理器选择</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="32"/>
        <source>TEV Signal Processor</source>
        <translation>暂态地调理器选择</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="33"/>
        <source>Current Signal Processor</source>
        <translation>电流调理器选择</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="36"/>
        <source>CA Diag. Signal Processor</source>
        <translation>CA调理器选择</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="37"/>
        <source>Paired!</source>
        <translation>匹配成功！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="38"/>
        <source>Failed pairing, try again.</source>
        <translation>匹配失败，请重试。</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="39"/>
        <source>Already connected.</source>
        <translation>已连接成功。</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="40"/>
        <source>No paired CA Diag. processor.</source>
        <translation>没有已匹配的CA调理器。</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="41"/>
        <source>Disconnected!</source>
        <translation>连接已断开！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="42"/>
        <source>Disconnect fail!</source>
        <translation>断开失败！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="43"/>
        <source>Signal Processor is disconnected!</source>
        <translation>未连接信号调理器！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="44"/>
        <source>UHF Signal Processor not found!</source>
        <translation>未找到UHF调理器！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="45"/>
        <source>HFCT Signal Processor not found!</source>
        <translation>未找到HFCT调理器！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="46"/>
        <source>Sync. not found!</source>
        <translation>未找到同步器！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="47"/>
        <source>Receiver not found!</source>
        <translation>未找到接收器！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="48"/>
        <source>External not found!</source>
        <translation>未找到外同步器！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="49"/>
        <source>AE Signal Processor not found!</source>
        <translation>未找到超声调理器！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="50"/>
        <source>TEV Signal Processor not found!</source>
        <translation>未找到暂态地调理器！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="51"/>
        <source>Current Signal Processor not found!</source>
        <translation>未找到电流调理器！</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/pmviewconfig.h" line="53"/>
        <source>Already connected other host.</source>
        <translation>已连接其他主机。</translation>
    </message>
</context>
<context>
    <name>PRPSView</name>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1157"/>
        <source>Please switch to accumulation mode.</source>
        <translation>请切换累积模式。</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1191"/>
        <source>Please sample data before recording.</source>
        <translation>录屏请先开始采集。</translation>
    </message>
</context>
<context>
    <name>PhaseTypeView</name>
    <message>
        <location filename="view/customaccessUi/taskmodeview/phasetypeview.cpp" line="443"/>
        <source>Read grounding current failed!</source>
        <translation>获取接地电流失败!</translation>
    </message>
</context>
<context>
    <name>PlayView</name>
    <message>
        <location filename="view/recordplay/playview.cpp" line="245"/>
        <location filename="view/recordplay/playview.cpp" line="267"/>
        <source>Please select an audio file.</source>
        <translation>请选择一个音频文件。</translation>
    </message>
    <message>
        <location filename="view/recordplay/playview.cpp" line="677"/>
        <source>Play failed!</source>
        <translation>播放失败！</translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="332"/>
        <source>Arcing</source>
        <translation>悬浮</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="334"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="83"/>
        <location filename="view/ca/CAViewConfig.cpp" line="31"/>
        <source>Floating Electrode</source>
        <translation>悬浮</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="340"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="80"/>
        <location filename="view/ca/CAViewConfig.cpp" line="34"/>
        <source>Corona</source>
        <translation>电晕</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="345"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="86"/>
        <location filename="view/ca/CAViewConfig.cpp" line="37"/>
        <source>Void</source>
        <translation>气隙</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="350"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="92"/>
        <location filename="view/ca/CAViewConfig.cpp" line="40"/>
        <source>Particle</source>
        <translation>微粒</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="358"/>
        <source>Tracking</source>
        <translation>沿面</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="362"/>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="366"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="89"/>
        <location filename="view/ca/CAViewConfig.cpp" line="43"/>
        <source>Surface</source>
        <translation>沿面</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="372"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="77"/>
        <location filename="view/ca/CAViewConfig.cpp" line="46"/>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="71"/>
        <source>Normal</source>
        <translation>正常</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="378"/>
        <source>Interference</source>
        <translation>干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="380"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="95"/>
        <location filename="view/ca/CAViewConfig.cpp" line="57"/>
        <source>Noise</source>
        <translation>噪声</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="386"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="101"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="152"/>
        <location filename="module/pda/taskfileio.cpp" line="3043"/>
        <location filename="view/ca/CAViewConfig.cpp" line="60"/>
        <source>Unknown</source>
        <translation>未知</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="391"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="98"/>
        <location filename="view/ca/CAView.cpp" line="117"/>
        <location filename="view/ca/wave/waveview.cpp" line="1382"/>
        <source>Calibration</source>
        <translation>校准</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="396"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="104"/>
        <source>Insufficient Data</source>
        <translation>数据不足</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="401"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="107"/>
        <source>Drill Noise</source>
        <translation>电钻干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="406"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="110"/>
        <source>Energy Saving Lamps Noise</source>
        <translation>节能灯干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="411"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="113"/>
        <source>Fan Noise</source>
        <translation>风机干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="416"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="116"/>
        <source>Ignition Noise</source>
        <translation>电子打火干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="421"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="119"/>
        <source>Interphone Noise</source>
        <translation>对讲机干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="426"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="122"/>
        <source>Microwave Sulfer Noise</source>
        <translation>微波硫灯干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="431"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="125"/>
        <source>Motor Noise</source>
        <translation>电机干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="436"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="128"/>
        <location filename="view/ca/CAViewConfig.cpp" line="69"/>
        <source>Radar Noise</source>
        <translation>雷达噪声</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="441"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="131"/>
        <source>Spark Leak Detector Noise</source>
        <translation>火花检漏器干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="446"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="134"/>
        <location filename="view/ca/CAViewConfig.cpp" line="72"/>
        <source>Mobile Noise</source>
        <translation>手机干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="452"/>
        <source>Internal</source>
        <translation>局放</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="454"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="137"/>
        <source>PD</source>
        <translation>局放</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="460"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="140"/>
        <source>Not PD</source>
        <translation>非局放</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="465"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="143"/>
        <source>Insulation</source>
        <translation>绝缘缺陷</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="470"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="146"/>
        <source>Mechanical Vibration</source>
        <translation>机械振动</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="475"/>
        <location filename="module/pda/pdafiletool/pdafiletool.cpp" line="149"/>
        <source>Light Noise</source>
        <translation>灯光干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="480"/>
        <source>Pest Repeller Noise</source>
        <translation>驱鼠器干扰</translation>
    </message>
    <message>
        <location filename="module/diagnosismgr/diagnosismanager.cpp" line="495"/>
        <source>Suspected</source>
        <translation>疑似</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="14"/>
        <source>Please login.</source>
        <translation>请登录。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="19"/>
        <source>Please login again.</source>
        <translation>请重新登录。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="24"/>
        <source>Service deals error.</source>
        <translation>服务端处理错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="30"/>
        <location filename="view/customaccessUi/customaccessloginview.cpp" line="55"/>
        <location filename="view/systemsetview/systemsetwidget/settingloginview.cpp" line="34"/>
        <source>User name or password error.</source>
        <translation>用户名或密码错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="35"/>
        <source>Account locked.</source>
        <translation>账户被锁定。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="40"/>
        <location filename="module/webserver/controller/appserverutils.cpp" line="160"/>
        <source>Service unavailable.</source>
        <translation>服务暂时不可用。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="45"/>
        <source>Signature verification error.</source>
        <translation>签名验证错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="50"/>
        <source>Request parameter error.</source>
        <translation>请求参数错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="55"/>
        <location filename="module/globalerrprocess.cpp" line="80"/>
        <location filename="module/globalerrprocess.cpp" line="130"/>
        <source>No operation permission.</source>
        <translation>没有操作权限。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="60"/>
        <source>Request timeout.</source>
        <translation>请求超时。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="65"/>
        <source>Password format error.</source>
        <translation>密码格式错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="70"/>
        <source>Password parse error.</source>
        <translation>密码解析错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="75"/>
        <source>User name not exist.</source>
        <translation>用户名不存在。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="85"/>
        <source>User name format error.</source>
        <translation>用户名格式错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="90"/>
        <source>Device submit parameter format error.</source>
        <translation>用户设备提交信息参数格式错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="95"/>
        <source>Login info not exist.</source>
        <translation>登录信息不存在。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="100"/>
        <source>Task is not in being download state.</source>
        <translation>任务未处于可下载状态。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="105"/>
        <source>Task is not in being submit state.</source>
        <translation>任务未处于可提交状态。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="110"/>
        <source>Patrol file is unmatched.</source>
        <translation>巡检文件不匹配。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="115"/>
        <source>Task file is not exist or record error.</source>
        <translation>任务文件不存在或记录错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="120"/>
        <source>Task has no data to submit.</source>
        <translation>任务无数据提交。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="125"/>
        <source>Task number is empty.</source>
        <translation>任务测试编号为空。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="135"/>
        <location filename="module/webserver/controller/appserverutils.cpp" line="163"/>
        <source>Task is not exist.</source>
        <translation>任务不存在。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="140"/>
        <source>Task has been completed, cannot be uploaded repeatedly.</source>
        <translation>任务已完成，不能重复上传。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="145"/>
        <source>Download task error.</source>
        <translation>下载任务出错。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="150"/>
        <source>Accept task error.</source>
        <translation>任务接受失败。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="155"/>
        <source>Upload task fail.</source>
        <translation>上传任务失败。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="160"/>
        <location filename="view/PDAUi/PDAUiBean/pdaprogressdialog.cpp" line="142"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="597"/>
        <source>Upload fail.</source>
        <translation>上传失败。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="165"/>
        <source>Undefined function.</source>
        <translation>未定义的功能。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="170"/>
        <source>No requested unzip method.</source>
        <translation>不支持所请求的解压方式。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="175"/>
        <source>File path error.</source>
        <translation>文件路径错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="180"/>
        <source>Request task list error.</source>
        <translation>请求任务列表错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="185"/>
        <source>Patrol task is not exist.</source>
        <translation>巡检任务不存在。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="190"/>
        <location filename="module/webserver/controller/appserverutils.cpp" line="166"/>
        <source>Task is in patrolling.</source>
        <translation>任务正在巡检中。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="195"/>
        <location filename="module/webserver/controller/appserverutils.cpp" line="169"/>
        <source>File operation error.</source>
        <translation>文件操作错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="200"/>
        <location filename="module/webserver/controller/appserverutils.cpp" line="172"/>
        <source>MD5 verification error.</source>
        <translation>MD5校验错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="205"/>
        <source>Http response message is empty.</source>
        <translation>Http响应报文为空。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="210"/>
        <source>Save task number error.</source>
        <translation>保存测试编号错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="215"/>
        <source>Get task number fail.</source>
        <translation>获取任务测试编号失败。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="220"/>
        <source>Upload test data fail.</source>
        <translation>上传测试数据失败。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="225"/>
        <source>Save test data fail.</source>
        <translation>保存测试数据失败。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="230"/>
        <source>Upload task description file fail.</source>
        <translation>上传任务描述文件失败。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="235"/>
        <source>Upload task info error.</source>
        <translation>上传任务信息错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="240"/>
        <source>Task number unmatched.</source>
        <translation>任务测试编号不匹配。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="245"/>
        <source>Method unmatched.</source>
        <translation>方法不匹配。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="250"/>
        <source>Task state unmatched.</source>
        <translation>任务状态不匹配。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="255"/>
        <source>Task serial number error.</source>
        <translation>任务序列号错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="260"/>
        <source>Client verification fail.</source>
        <translation>客户端验证失败。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="265"/>
        <source>Input parameter error.</source>
        <translation>输入参数错误。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="270"/>
        <source>Task list is empty.</source>
        <translation>任务列表为空。</translation>
    </message>
    <message>
        <location filename="module/globalerrprocess.cpp" line="275"/>
        <source>Connect fail.</source>
        <translation>连接失败。</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="130"/>
        <source>Unnamed</source>
        <translation>未命名</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="138"/>
        <source>Front</source>
        <translation>前</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="139"/>
        <source>Side</source>
        <translation>侧</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="140"/>
        <source>Back</source>
        <translation>后</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2882"/>
        <source>Transformer</source>
        <translation>变压器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2887"/>
        <source>Circuit Breaker</source>
        <translation>断路器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2892"/>
        <source>Isolation Switcher</source>
        <translation>隔离开关</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2897"/>
        <source>Knife Gate</source>
        <translation>刀闸</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2902"/>
        <source>Lightning Arrester</source>
        <translation>避雷器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2907"/>
        <source>Voltage Transformer</source>
        <translation>电压互感器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2912"/>
        <source>Current Transformer</source>
        <translation>电流互感器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2917"/>
        <source>Bus Bar</source>
        <translation>母线</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2922"/>
        <source>Bus Coupler</source>
        <translation>母联</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2927"/>
        <source>Switchgear</source>
        <translation>开关柜</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2932"/>
        <source>Cable</source>
        <translation>电力电缆</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2937"/>
        <source>Lightning Rod</source>
        <translation>避雷针</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2942"/>
        <source>Wall Bushing</source>
        <translation>穿墙套管</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2947"/>
        <source>Reactor</source>
        <translation>电抗器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2952"/>
        <source>Conductor</source>
        <translation>电力导线</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2957"/>
        <source>Capacitor</source>
        <translation>电力电容器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2962"/>
        <source>Discharge Coil</source>
        <translation>放电线圈</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2967"/>
        <source>Load Switcher</source>
        <translation>负荷开关</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2972"/>
        <source>Grounding Transformer</source>
        <translation>接地变</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2977"/>
        <source>Grounding Resistor</source>
        <translation>接地电阻</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2982"/>
        <source>Grounding Grid</source>
        <translation>接地网</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2987"/>
        <source>Combined Filter</source>
        <translation>结合滤波器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2992"/>
        <source>Insulator</source>
        <translation>绝缘子</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="2997"/>
        <source>Coupling Capacitor</source>
        <translation>耦合电容器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3002"/>
        <source>Power Cabinet</source>
        <translation>屏柜</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3007"/>
        <source>Other Device</source>
        <translation>其他设备</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3012"/>
        <source>Power Fuse</source>
        <translation>熔断器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3017"/>
        <source>Spot Transformer</source>
        <translation>所用变</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3022"/>
        <source>Arc Suppression Device</source>
        <translation>消弧装置</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3027"/>
        <source>Blocker</source>
        <translation>阻波器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3032"/>
        <source>GIS</source>
        <translation>组合电器</translation>
    </message>
    <message>
        <location filename="module/pda/taskfileio.cpp" line="3037"/>
        <source>Combined Transformer</source>
        <translation>组合互感器</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="154"/>
        <source>Success.</source>
        <translation>成功。</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="157"/>
        <source>Verification error.</source>
        <translation>验证错误。</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="175"/>
        <source>Activation code error.</source>
        <translation>激活码错误。</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="178"/>
        <source>Compress files failure error.</source>
        <translation>压缩文件失败错误。</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="181"/>
        <source>Empty information error.</source>
        <translation>空信息错误。</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="184"/>
        <source>Empty test data list error.</source>
        <translation>测试数据列表为空。</translation>
    </message>
    <message>
        <location filename="module/webserver/controller/appserverutils.cpp" line="187"/>
        <source>Unknown error.</source>
        <translation>未知错误。</translation>
    </message>
    <message>
        <location filename="widget/chartview/ChartView.cpp" line="505"/>
        <source>More...</source>
        <translation>更多...</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="329"/>
        <source>Proto</source>
        <translation>样机(非卖品)</translation>
    </message>
    <message>
        <location filename="widget/deleteDataView/DeleteFileWidget.cpp" line="87"/>
        <location filename="widget/deleteDataView/DeleteFileWidget.cpp" line="95"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidget.cpp" line="86"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidget.cpp" line="95"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidgetEx.cpp" line="59"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidgetEx.cpp" line="68"/>
        <location filename="view/ae/aeplaybackview.cpp" line="145"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="958"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsplaybackview.cpp" line="95"/>
        <location filename="view/infrared/infraredplayback.cpp" line="219"/>
        <location filename="view/infrared/infraredview.cpp" line="632"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsplaybackview.cpp" line="98"/>
        <location filename="view/widgets/sampleChartView/SampleChartView.cpp" line="739"/>
        <source>Confirm to delete the file?</source>
        <translation>确定要删除文件？</translation>
    </message>
    <message>
        <location filename="widget/deleteDataView/DeleteFileWidget.cpp" line="121"/>
        <location filename="widget/deleteDataView/DeleteFileWidget.cpp" line="129"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidget.cpp" line="121"/>
        <location filename="widget/deleteDataView/RotateDeleteFileWidget.cpp" line="129"/>
        <location filename="view/distributenetaccess/distributenetaddtaskview.cpp" line="196"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="196"/>
        <source>Cannot be deleted!</source>
        <translation>无法被删除！</translation>
    </message>
    <message>
        <location filename="widget/guideinfrared/guideinfraredimagingview.cpp" line="789"/>
        <location filename="widget/infrared/infraredimagingview.cpp" line="745"/>
        <source>Substation Name</source>
        <translation>站名</translation>
    </message>
    <message>
        <location filename="widget/guideinfrared/guideinfraredimagingview.cpp" line="790"/>
        <location filename="widget/infrared/infraredimagingview.cpp" line="746"/>
        <source>Asset</source>
        <translation>设备</translation>
    </message>
    <message>
        <location filename="widget/guideinfrared/guideinfraredimagingview.cpp" line="791"/>
        <location filename="widget/infrared/infraredimagingview.cpp" line="747"/>
        <source>Test Point</source>
        <translation>测点</translation>
    </message>
    <message>
        <location filename="widget/infrared/colorcodedwidget.cpp" line="101"/>
        <location filename="widget/infrared/colorcodedwidget.cpp" line="197"/>
        <source>Auto</source>
        <translation>自动</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredparameterdialog.cpp" line="52"/>
        <location filename="widget/infrared/rotateparameterdialog.cpp" line="89"/>
        <location filename="widget/messageBox/msgbox.cpp" line="504"/>
        <location filename="view/PDAUi/PDAUiBean/addtestdatadialog.cpp" line="109"/>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="133"/>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="184"/>
        <location filename="view/currentdetection/valuesettingdialog.cpp" line="81"/>
        <location filename="view/distributenetaccess/filtersettingdlg.cpp" line="36"/>
        <location filename="view/rfid/RFIDReadView.cpp" line="55"/>
        <location filename="view/systemsetview/systemsetabout/clock/ClockWindow.cpp" line="171"/>
        <location filename="view/systemsetview/systemsetabout/cloud/systemsettinggrounp.cpp" line="72"/>
        <location filename="view/systemsetview/systemsetabout/date/DateView.cpp" line="396"/>
        <location filename="view/systemsetview/systemsetabout/mobilenetwork/pppsettinggroup.cpp" line="77"/>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="53"/>
        <location filename="view/widgets/filecommentbox/filecommentbox.cpp" line="42"/>
        <source>OK</source>
        <translation>确定</translation>
    </message>
    <message>
        <location filename="widget/infrared/infraredparameterdialog.cpp" line="56"/>
        <location filename="widget/infrared/rotateparameterdialog.cpp" line="90"/>
        <location filename="widget/messageBox/msgbox.cpp" line="498"/>
        <location filename="view/PDAUi/PDAUiBean/addtestdatadialog.cpp" line="115"/>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="140"/>
        <location filename="view/currentdetection/valuesettingdialog.cpp" line="88"/>
        <location filename="view/distributenetaccess/filtersettingdlg.cpp" line="42"/>
        <location filename="view/rfid/rfidwriteview.cpp" line="76"/>
        <location filename="view/systemsetview/systemsetabout/wifisetting/inputwifipasswd.cpp" line="145"/>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="59"/>
        <location filename="view/widgets/filecommentbox/filecommentbox.cpp" line="48"/>
        <source>Cancel</source>
        <translation>取消</translation>
    </message>
    <message>
        <location filename="widget/infrared/linetemperaturecurve.cpp" line="64"/>
        <source>Temp. Points Count</source>
        <translation>温度点个数</translation>
    </message>
    <message>
        <location filename="widget/infrared/linetemperaturecurve.cpp" line="67"/>
        <source>Unit(℉)</source>
        <translation>单位(℉)</translation>
    </message>
    <message>
        <location filename="widget/infrared/linetemperaturecurve.cpp" line="71"/>
        <source>Unit(℃)</source>
        <translation>单位(℃)</translation>
    </message>
    <message>
        <location filename="widget/messageBox/msgbox.cpp" line="628"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="954"/>
        <location filename="view/infrared/infraredview.cpp" line="628"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="widget/playbackView/PlayBackTitle.cpp" line="81"/>
        <source>Substation Name: </source>
        <translation>站名：</translation>
    </message>
    <message>
        <location filename="widget/playbackView/PlayBackTitle.cpp" line="99"/>
        <source>Asset: </source>
        <translation>设备：</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="114"/>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="819"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="415"/>
        <location filename="view/currentdetection/bjcurrentdetectionchart.cpp" line="106"/>
        <location filename="view/currentdetection/currentdetectionchart.cpp" line="133"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="462"/>
        <location filename="view/uhf/uhfintervalview/UHFSpectrumChart.cpp" line="520"/>
        <location filename="view/widgets/histogram/HistogramChart.cpp" line="606"/>
        <source>No signal</source>
        <translation>无信号</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="835"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="429"/>
        <location filename="view/ca/prps/prpsview.cpp" line="2347"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="2080"/>
        <location filename="view/ca/wave/waveview.cpp" line="2012"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="372"/>
        <location filename="view/widgets/histogram/HistogramChart.cpp" line="132"/>
        <source>Low Gain</source>
        <translation>增益偏低</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="845"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="439"/>
        <location filename="view/ca/prps/prpsview.cpp" line="2334"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="2067"/>
        <location filename="view/ca/wave/waveview.cpp" line="1999"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodChart.cpp" line="387"/>
        <location filename="view/widgets/histogram/HistogramChart.cpp" line="137"/>
        <source>High Gain</source>
        <translation>增益偏高</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="918"/>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="1396"/>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="1407"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="46"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="501"/>
        <source>Power Sync</source>
        <translation>电源同步</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="923"/>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="1412"/>
        <location filename="widget/prps/prpsview/prpsabstractview.cpp" line="506"/>
        <source>Light Sync</source>
        <translation>光同步</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="935"/>
        <source>Sync Lost</source>
        <translation>同步丢失</translation>
    </message>
    <message>
        <location filename="widget/prps/prpsview/phaseabstractview.cpp" line="1400"/>
        <source>Internal Sync</source>
        <translation>内同步</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="640"/>
        <source>Don&apos;t remind</source>
        <translation>不再提醒</translation>
    </message>
    <message>
        <location filename="view/Desktop.cpp" line="1028"/>
        <location filename="view/ca/prps/prpsview.cpp" line="322"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="339"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="236"/>
        <location filename="view/ca/wave/waveview.cpp" line="296"/>
        <source>Disconnected!</source>
        <translation>已断开！</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdalistchart.cpp" line="66"/>
        <location filename="view/customaccessUi/commonitemview/commonitemlistview.cpp" line="87"/>
        <source>PgUp</source>
        <translation>上翻页</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdalistchart.cpp" line="73"/>
        <location filename="view/customaccessUi/commonitemview/commonitemlistview.cpp" line="95"/>
        <source>PgDn</source>
        <translation>下翻页</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdalistchart.cpp" line="875"/>
        <source>Untest</source>
        <translation>未测</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdalistchart.cpp" line="879"/>
        <source>Tested</source>
        <translation>已测</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdalistchart.cpp" line="883"/>
        <source>Testing</source>
        <translation>测试中</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdaprogressdialog.cpp" line="51"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="572"/>
        <location filename="view/ae/aeflyview/aeflypdaview.cpp" line="787"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="562"/>
        <location filename="view/ae/aephaseview/aephasepdaview.cpp" line="818"/>
        <source>Stop</source>
        <translation>停止</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdaprogressdialog.cpp" line="170"/>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="330"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="748"/>
        <source>Download succeeded.</source>
        <translation>下载成功。</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/pdaprogressdialog.cpp" line="147"/>
        <location filename="view/PDAUi/PDAUiBean/pdaprogressdialog.cpp" line="156"/>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="443"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="593"/>
        <source>Upload success.</source>
        <translation>上传成功。</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="78"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="167"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="218"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestview.cpp" line="185"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestbgview.cpp" line="166"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="169"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestview.cpp" line="202"/>
        <source>Asset Name: </source>
        <translation>设备名称：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="83"/>
        <source>Asset Type: </source>
        <translation>设备类型：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="88"/>
        <source>Asset Model: </source>
        <translation>设备型号：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="93"/>
        <source>Asset Number: </source>
        <translation>设备编码：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="116"/>
        <source>Voltage Level: </source>
        <translation>电压等级：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="121"/>
        <source>Release Date: </source>
        <translation>出厂日期：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="126"/>
        <source>Manufacturer: </source>
        <translation>生产厂商：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadevicedetailview.cpp" line="131"/>
        <source>Group Name: </source>
        <translation>组名：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="44"/>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="84"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="59"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="60"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="42"/>
        <source>Task List</source>
        <translation>任务列表</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="98"/>
        <source>Refreshing ...</source>
        <translation>刷新中…</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="258"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="368"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="446"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="241"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="257"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="328"/>
        <source>Please choose a task!</source>
        <translation>请选择一个任务！</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="351"/>
        <source>Sifting ...</source>
        <translation>筛选中…</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="369"/>
        <source>Restoring default ...</source>
        <translation>恢复默认中...</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdadownloadtaskview.cpp" line="390"/>
        <source>Getting the task list ...</source>
        <translation>获取任务中…</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="43"/>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="41"/>
        <source>Asset List</source>
        <translation>设备列表</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="127"/>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="402"/>
        <source>Please test background points firstly.</source>
        <translation>请先进行背景测试。</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="327"/>
        <source>No matched device!</source>
        <translation>无匹配的设备！</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdafileview.cpp" line="332"/>
        <source>Scan RFID failed!</source>
        <translation>RFID扫描失败！</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdaloginview.cpp" line="180"/>
        <source>Cloud Login</source>
        <translation>云登录</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdapatroltypeview.cpp" line="111"/>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="782"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="232"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="83"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="65"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="140"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="135"/>
        <source>The disk is occupied, please disconnect mobilephone or computer!</source>
        <translation>磁盘被占用，请断开与手机或者电脑的连接！</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdapointlistview.cpp" line="26"/>
        <location filename="view/customaccessUi/intervalview.cpp" line="132"/>
        <location filename="view/customaccessUi/intervalview.cpp" line="359"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="24"/>
        <source>Test Point List</source>
        <translation>测点列表</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdapointlistview.cpp" line="193"/>
        <location filename="view/PDAUi/PDAUiView/pdapointlistview.cpp" line="204"/>
        <source>Current patrol position test completed.</source>
        <translation>当前巡检位置已测试完毕。</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdapointtypeview.cpp" line="15"/>
        <source>Test Point Type</source>
        <translation>测点类型</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="140"/>
        <location filename="view/PDAUi/PDAUiView/pdapointtypeview.cpp" line="85"/>
        <location filename="view/PDAUi/PDAUiView/pdatestdataview.cpp" line="279"/>
        <source>Current point test completed.</source>
        <translation>当前测点类型已测试结束。</translation>
    </message>
    <message numerus="yes">
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="145"/>
        <location filename="view/PDAUi/PDAUiView/pdapointtypeview.cpp" line="90"/>
        <location filename="view/PDAUi/PDAUiView/pdatestdataview.cpp" line="284"/>
        <source>Total: %n point(s), </source>
        <translation type="unfinished">
            <numerusform>当前测点类型共：%n个测点，</numerusform>
        </translation>
    </message>
    <message numerus="yes">
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="146"/>
        <location filename="view/PDAUi/PDAUiView/pdapointtypeview.cpp" line="91"/>
        <location filename="view/PDAUi/PDAUiView/pdatestdataview.cpp" line="285"/>
        <source>tested: %n point(s).</source>
        <translation type="unfinished">
            <numerusform>已测：%n个测点。</numerusform>
        </translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="230"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="680"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="654"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="587"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="469"/>
        <location filename="view/customaccessUi/taskmodeview/currentphasetypeview.cpp" line="238"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="562"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="565"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="723"/>
        <location filename="view/infrared/infraredview.cpp" line="489"/>
        <source>Empty data, save failure!</source>
        <oldsource>No data, Save failure!</oldsource>
        <translation type="unfinished">数据为空，保存失败！</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdasifttaskview/pdasifttaskview.cpp" line="192"/>
        <source>Restore</source>
        <translation>恢复</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="191"/>
        <location filename="view/distributenetaccess/distributenetaddtaskview.cpp" line="190"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="187"/>
        <source>Confirm to delete?</source>
        <translation>确认要删除？</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="290"/>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="552"/>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="604"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="288"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="337"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="159"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="223"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="336"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="227"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="431"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="98"/>
        <source>No task file has been chosen.</source>
        <translation>未选择任务文件。</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="586"/>
        <source>Reading the task ...</source>
        <translation>读取任务信息中...</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdataskview.cpp" line="690"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="300"/>
        <source>Saving the task ...</source>
        <translation>保存任务信息中...</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="25"/>
        <location filename="view/PDAUi/PDAUiView/pdatestdataview.cpp" line="29"/>
        <source>Test Item List</source>
        <translation>测试项列表</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="117"/>
        <location filename="view/ae/aecommonwidget/AEOption.cpp" line="199"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="296"/>
        <source>Channel:Internal</source>
        <translation>通道：空声</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="120"/>
        <location filename="view/ae/aecommonwidget/AEOption.cpp" line="202"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="299"/>
        <source>Channel:External</source>
        <translation>通道：外接</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="123"/>
        <location filename="view/ae/aecommonwidget/AEOption.cpp" line="205"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="302"/>
        <source>Wireless</source>
        <translation>无线</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="149"/>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="405"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="351"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="373"/>
        <source>Trigger?</source>
        <translation>触发？</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="264"/>
        <source>Noise Test</source>
        <translation>噪声测试</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpChart.cpp" line="399"/>
        <location filename="view/ae/aewaveview/AeWave.cpp" line="345"/>
        <source>Triggered</source>
        <translation>已触发</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiView/pdacurrenttestdataview.cpp" line="312"/>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="609"/>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1377"/>
        <location filename="view/ae/aeampview/aeamppdaview.cpp" line="991"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="837"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="1217"/>
        <location filename="view/ae/aeflyview/aeflypdaview.cpp" line="768"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="857"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="1231"/>
        <location filename="view/ae/aephaseview/aephasepdaview.cpp" line="562"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="319"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="1197"/>
        <location filename="view/ae/aewaveview/aewavepdaview.cpp" line="909"/>
        <location filename="view/ca/prps/prpsview.cpp" line="864"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1471"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1714"/>
        <location filename="view/ca/prps/prpsview.cpp" line="2290"/>
        <location filename="view/ca/prps/prpsview.cpp" line="2311"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1728"/>
        <location filename="view/ca/wave/waveview.cpp" line="1320"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="1787"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestview.cpp" line="1929"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="1858"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2004"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="1965"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2102"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestbgview.cpp" line="1281"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="1381"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="1332"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="1473"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1038"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1032"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="682"/>
        <location filename="view/customaccessUi/taskmodeview/currentphasetypeview.cpp" line="360"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="849"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="940"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestbgview.cpp" line="1343"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="1497"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="1392"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="1567"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="1881"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2038"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestbgview.cpp" line="1307"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestview.cpp" line="1427"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestbgview.cpp" line="851"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="929"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestbgview.cpp" line="1368"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestview.cpp" line="1537"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1001"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="944"/>
        <location filename="view/hfct/hfctprpsview/hfctprpspdaview.cpp" line="628"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="306"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="875"/>
        <location filename="view/infrared/infraredview.cpp" line="618"/>
        <location filename="view/recordplay/recordplayview.cpp" line="126"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1573"/>
        <location filename="view/tev/tevamppdaview.cpp" line="534"/>
        <location filename="view/tev/tevpulsepdaview.cpp" line="572"/>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1259"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1842"/>
        <location filename="view/widgets/sampleChartView/SampleChartView.cpp" line="277"/>
        <location filename="view/widgets/sampleChartView/SampleChartView.cpp" line="732"/>
        <location filename="view/widgets/sampleChartView/SampleChartView.cpp" line="894"/>
        <source>Save failure!</source>
        <translation>保存失败！</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1140"/>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1169"/>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1299"/>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1323"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="628"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="768"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="1136"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="1160"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="761"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="790"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="1152"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="1176"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="815"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="845"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="1119"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="1143"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1217"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1243"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1310"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1406"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1217"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1322"/>
        <location filename="view/ca/wave/waveview.cpp" line="1031"/>
        <location filename="view/ca/wave/waveview.cpp" line="1163"/>
        <location filename="view/currentdetection/bjcurrentdetectionview.cpp" line="209"/>
        <location filename="view/currentdetection/bjcurrentdetectionview.cpp" line="239"/>
        <location filename="view/currentdetection/currentdetectionview.cpp" line="224"/>
        <location filename="view/currentdetection/currentdetectionview.cpp" line="254"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="1804"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="2174"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestview.cpp" line="1947"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="1875"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="2260"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2021"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2497"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="1982"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="2367"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2119"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2595"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestbgview.cpp" line="529"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestbgview.cpp" line="1169"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="545"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="1266"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="528"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="1218"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="571"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="1328"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="1365"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="498"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="833"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="473"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="801"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="415"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="782"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="328"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="606"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="509"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="637"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="543"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="579"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="731"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestbgview.cpp" line="549"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestbgview.cpp" line="1229"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="576"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="1372"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="545"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="1277"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="586"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="1420"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="1457"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="1901"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="2276"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2055"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2516"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestbgview.cpp" line="504"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="530"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="566"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="715"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="418"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="735"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="406"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="736"/>
        <location filename="view/hfct/hfctamplitudeview/HFCTAmplitudeView.cpp" line="289"/>
        <location filename="view/hfct/hfctamplitudeview/HFCTAmplitudeView.cpp" line="319"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodView.cpp" line="699"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodView.cpp" line="727"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1450"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1474"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1503"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1532"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="571"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="936"/>
        <location filename="view/infrared/infraredview.cpp" line="348"/>
        <location filename="view/infrared/infraredview.cpp" line="692"/>
        <location filename="view/recordplay/recordplayview.cpp" line="90"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1331"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1363"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1715"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1750"/>
        <location filename="view/tev/tevampview.cpp" line="595"/>
        <location filename="view/tev/tevampview.cpp" line="625"/>
        <location filename="view/tev/tevpulseview.cpp" line="698"/>
        <location filename="view/tev/tevpulseview.cpp" line="728"/>
        <location filename="view/uhf/uhfamplitudeview/UHFAmplitudeView.cpp" line="308"/>
        <location filename="view/uhf/uhfamplitudeview/UHFAmplitudeView.cpp" line="338"/>
        <location filename="view/uhf/uhfintervalview/UHFIntervalView.cpp" line="755"/>
        <location filename="view/uhf/uhfintervalview/UHFIntervalView.cpp" line="783"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1598"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1630"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1992"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="2025"/>
        <source>No file!</source>
        <translation>无文件！</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1180"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="779"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="801"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="857"/>
        <source>Record</source>
        <translation>录音</translation>
    </message>
    <message>
        <location filename="view/ae/aeampview/AEAmpView.cpp" line="1203"/>
        <location filename="view/ae/aeampview/aeamppdaview.cpp" line="1408"/>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="867"/>
        <location filename="view/ae/aeflyview/aeflypdaview.cpp" line="998"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="887"/>
        <location filename="view/ae/aephaseview/aephasepdaview.cpp" line="1064"/>
        <location filename="view/ae/aewaveview/AEWaveView.cpp" line="879"/>
        <location filename="view/ae/aewaveview/aewavepdaview.cpp" line="815"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1252"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1143"/>
        <location filename="view/ca/wave/waveview.cpp" line="981"/>
        <location filename="view/hfct/hfctamplitudeview/HFCTAmplitudeView.cpp" line="592"/>
        <location filename="view/hfct/hfctperiodview/HFCTPeriodView.cpp" line="520"/>
        <location filename="view/hfct/hfctprpsview/hfctprpspdaview.cpp" line="859"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="780"/>
        <location filename="view/systemsetview/systemsetwidget/routinesettingview.cpp" line="472"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1402"/>
        <location filename="view/tev/tevamppdaview.cpp" line="645"/>
        <location filename="view/tev/tevampview.cpp" line="638"/>
        <location filename="view/tev/tevpulsepdaview.cpp" line="649"/>
        <location filename="view/tev/tevpulseview.cpp" line="741"/>
        <location filename="view/uhf/uhfamplitudeview/UHFAmplitudeView.cpp" line="626"/>
        <location filename="view/uhf/uhfintervalview/UHFIntervalView.cpp" line="557"/>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1535"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1676"/>
        <source>Confirm to restore default settings?</source>
        <translation>确定要恢复默认？</translation>
    </message>
    <message>
        <location filename="view/ae/aeflyview/AEFlyView.cpp" line="585"/>
        <location filename="view/ae/aeflyview/aeflypdaview.cpp" line="806"/>
        <location filename="view/ae/aephaseview/AEPhaseView.cpp" line="575"/>
        <location filename="view/ae/aephaseview/aephasepdaview.cpp" line="837"/>
        <source>Sample</source>
        <translation>采样</translation>
    </message>
    <message>
        <location filename="view/ca/CAView.cpp" line="122"/>
        <source>Please connect CA Diag. processor.</source>
        <translation>请连接CA调理器。</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.cpp" line="63"/>
        <source>Calibration Signal</source>
        <translation>校准信号</translation>
    </message>
    <message>
        <location filename="view/ca/CAViewConfig.cpp" line="66"/>
        <source>Invalid Data</source>
        <translation>无效数据</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsvideoplaybackview.cpp" line="151"/>
        <source>Loading ...</source>
        <translation>载入中…</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="913"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1462"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1526"/>
        <location filename="view/ca/prps/prpsview.cpp" line="2278"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1929"/>
        <location filename="view/ca/wave/waveview.cpp" line="1679"/>
        <location filename="view/ca/wave/waveview.cpp" line="1774"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1565"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1645"/>
        <source>No data!</source>
        <translation>无数据！</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="916"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1571"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1631"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1920"/>
        <source>Saving ...</source>
        <translation>保存中...</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1235"/>
        <location filename="view/hfct/hfctprpsview/hfctprpsview.cpp" line="1524"/>
        <location filename="view/tev/prps/tevprpsview.cpp" line="1742"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="2017"/>
        <source>Delete Record</source>
        <translation>删除录屏</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1326"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1232"/>
        <source>Set parameter fail, please check connection state.</source>
        <translation>设置参数失败，请检查连接状态。</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1349"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1262"/>
        <source>Start sample fail, please check connection state.</source>
        <translation>开始采样失败，请检查连接状态。</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1425"/>
        <location filename="view/ca/prps/prpsview.cpp" line="1751"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1339"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1833"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="941"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="1061"/>
        <location filename="view/ca/wave/waveview.cpp" line="1180"/>
        <location filename="view/ca/wave/waveview.cpp" line="1462"/>
        <source>Please pair CA Diag. processor first.</source>
        <translation>请先匹配CA调理器。</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1430"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1344"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="946"/>
        <location filename="view/ca/wave/waveview.cpp" line="1185"/>
        <source>No paired CA Diag. processor.</source>
        <translation>无已匹配的CA调理器。</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1557"/>
        <location filename="view/ca/pulse/pulseclusterview.cpp" line="279"/>
        <source>Signal Type: </source>
        <translation>信号类型：</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1558"/>
        <location filename="view/ca/pulse/pulseclusterview.cpp" line="285"/>
        <source>Confidence Level: </source>
        <translation>置信度：</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1563"/>
        <source>Result</source>
        <translation>结果</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1664"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1405"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="993"/>
        <location filename="view/ca/wave/waveview.cpp" line="518"/>
        <source>Invalid coefficient, it will use default coefficient, please check HAS02 hardware!</source>
        <translation>系数无效，使用默认系数，请检查HAS02硬件！</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1669"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1410"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="998"/>
        <location filename="view/ca/wave/waveview.cpp" line="523"/>
        <source>No calibrated coefficient, please calibrate HAS02 firstly!</source>
        <translation>无已校准的系数，请先校准HAS02！</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1719"/>
        <source>Save successfully!</source>
        <translation>保存成功！</translation>
    </message>
    <message>
        <location filename="view/ca/prps/prpsview.cpp" line="1758"/>
        <location filename="view/ca/pulse/PulseView.cpp" line="1840"/>
        <location filename="view/ca/wave/wavetestview.cpp" line="1068"/>
        <location filename="view/ca/wave/waveview.cpp" line="1469"/>
        <source>No available CA Diag. processor!</source>
        <translation>无可用的CA调理器！</translation>
    </message>
    <message>
        <location filename="view/ca/pulse/PulseAnalysisView.cpp" line="350"/>
        <location filename="view/ca/pulse/PulseAnalysisView.cpp" line="765"/>
        <location filename="view/ca/pulse/PulseAnalysisView.cpp" line="784"/>
        <location filename="view/ca/pulse/pulseclusterview.cpp" line="190"/>
        <source>No pulse!</source>
        <translation>无脉冲！</translation>
    </message>
    <message>
        <location filename="view/ca/pulse/pulseclusterview.cpp" line="235"/>
        <source>Pulse Total Count: </source>
        <translation>脉冲总数：</translation>
    </message>
    <message>
        <location filename="view/ca/pulse/pulseclusterview.cpp" line="268"/>
        <source>Result: </source>
        <translation>结果：</translation>
    </message>
    <message>
        <location filename="view/ca/pulse/pulsedetailview.cpp" line="286"/>
        <source>Pulse Count</source>
        <translation>脉冲计数</translation>
    </message>
    <message>
        <location filename="view/ca/wave/calibrate/calibrateview.cpp" line="736"/>
        <source>Invalid coefficient, please check HAS02 hardware!</source>
        <translation>系数无效，请检查HAS02硬件！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="1767"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestview.cpp" line="1909"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="1837"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="1984"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="1944"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2082"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestbgview.cpp" line="1288"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="1388"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="1338"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="1479"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1053"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1046"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="710"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="829"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="920"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestbgview.cpp" line="1350"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="1504"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="1398"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="1573"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="1859"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2017"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestbgview.cpp" line="1315"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestview.cpp" line="1435"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestbgview.cpp" line="830"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="908"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestbgview.cpp" line="1376"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestview.cpp" line="1545"/>
        <source>Save success, auto switching.</source>
        <translation>保存成功，即将自动跳转。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="1818"/>
        <location filename="view/customaccessUi/aetestview/aecabeltestbgview.cpp" line="2186"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="1889"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="2272"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2035"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2511"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="1996"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="2379"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2133"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2609"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestbgview.cpp" line="123"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="154"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="120"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="161"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="98"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="115"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestbgview.cpp" line="139"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="168"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="137"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="176"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="1915"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestbgview.cpp" line="2288"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2069"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2530"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestbgview.cpp" line="124"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestview.cpp" line="149"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestbgview.cpp" line="103"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="103"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestbgview.cpp" line="139"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestview.cpp" line="165"/>
        <source>Test Data List</source>
        <translation>测试数据列表</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="440"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="664"/>
        <location filename="view/customaccessUi/aetestview/aetestbgview.cpp" line="1737"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="442"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="694"/>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="1879"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="473"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="704"/>
        <location filename="view/customaccessUi/aetestview/aetjtestbgview.cpp" line="1845"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="464"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="723"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="1978"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="491"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="786"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="842"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="527"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="838"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="896"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="316"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="481"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="558"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="340"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="509"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="616"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="510"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="837"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="889"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="546"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="889"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="942"/>
        <source>Tested Count: </source>
        <translation>已测次数：</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/aetestview/aetestview.cpp" line="2548"/>
        <location filename="view/customaccessUi/aetestview/aetjtestview.cpp" line="2646"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpscabeltestview.cpp" line="520"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="538"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1111"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="627"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpscabeltestview.cpp" line="553"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="557"/>
        <location filename="view/distributenetaccess/aetestview/aedistributenettestview.cpp" line="2567"/>
        <source>Delete succeeded!</source>
        <translation>删除成功！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="131"/>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="188"/>
        <source>Confirm to disconnect?</source>
        <translation>确定要断开连接？</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="241"/>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="289"/>
        <source>Access app success!</source>
        <translation>接入移动终端成功！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="255"/>
        <source>Please set the correct ip and port.</source>
        <translation>请设置正确的地址和端口。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="259"/>
        <source>Please confirm the server to be started.</source>
        <translation>请确定服务已开启。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="294"/>
        <source>Access app fail!</source>
        <translation>接入移动终端失败！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/connectview/connectview.cpp" line="328"/>
        <location filename="view/update/softwareinfoview.cpp" line="136"/>
        <source>Connecting ...</source>
        <translation>连接中…</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/customaccessloginview.cpp" line="19"/>
        <source>Login</source>
        <translation>Login</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestbgview.cpp" line="846"/>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="900"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="439"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="448"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="508"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="519"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestbgview.cpp" line="319"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="343"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestbgview.cpp" line="893"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="946"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestbgview.cpp" line="176"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestbgview.cpp" line="195"/>
        <source>Bay Name: </source>
        <translation>间隔名称：</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/hfcttestview/hfctprpstestview.cpp" line="904"/>
        <location filename="view/customaccessUi/tevtestview/tevamptestview.cpp" line="346"/>
        <location filename="view/customaccessUi/uhftestview/uhfprpstestview.cpp" line="950"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="217"/>
        <location filename="view/distributenetaccess/hfcttestview/hfctprpsdistributenettestview.cpp" line="186"/>
        <location filename="view/distributenetaccess/tevtestview/tevampdistributenettestview.cpp" line="170"/>
        <location filename="view/distributenetaccess/uhftestview/uhfprpsdistributenettestview.cpp" line="203"/>
        <source>Test Point Name: </source>
        <translation>测点名称：</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="279"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="268"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="233"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="218"/>
        <source>Device disconnected!</source>
        <translation type="unfinished">设备断开连接！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="290"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="279"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="229"/>
        <location filename="view/infrared/infraredview.cpp" line="190"/>
        <source>The connected infrared lens type has changed, please exit and re-enter the page.</source>
        <translation type="unfinished">已连接的红外镜头类型改变，请退出重新进入页面。</translation>
    </message>
    <message>
        <source>Service exiting, please wait...</source>
        <translation type="obsolete">服务退出中，请稍后…</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="424"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="413"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="345"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="345"/>
        <source>Autofocus failed!</source>
        <translation type="unfinished">自动对焦失败！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="432"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="421"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="353"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="353"/>
        <source>Close focus fine-tuning failed!</source>
        <translation type="unfinished">近焦微调失败！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="440"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="429"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="361"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="361"/>
        <source>Far focus fine-tuning failed!</source>
        <translation type="unfinished">远焦微调失败！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="692"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="666"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="667"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="530"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="569"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="574"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="851"/>
        <location filename="view/infrared/infraredview.cpp" line="596"/>
        <source>Saving data ...</source>
        <translation>保存数据中…</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="977"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="971"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="852"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="667"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="861"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="874"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="996"/>
        <location filename="view/infrared/infraredview.cpp" line="752"/>
        <source>Connecting to infrared device ...</source>
        <translation>连接红外设备中...</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1165"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1143"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1038"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1001"/>
        <source>Failed to initialize parameters, do you want to exit the page?</source>
        <translation type="unfinished">初始化参数失败，要退出页面吗？</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1183"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1161"/>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="914"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="721"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1052"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1019"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="1055"/>
        <location filename="view/infrared/infraredview.cpp" line="811"/>
        <source>Initialization failed!</source>
        <translation>初始化失败！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1197"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1175"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1304"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1033"/>
        <source>Failed to turn off laser control!</source>
        <translation type="unfinished">关闭激光控制失败！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1201"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1179"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1308"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1037"/>
        <source>Failed to turn on laser control!</source>
        <translation type="unfinished">打开激光控制失败！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1227"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1205"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1334"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1063"/>
        <source>Failed to turn off auxiliary lighting!</source>
        <translation type="unfinished">关闭辅助照明失败！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredcabeltestview.cpp" line="1231"/>
        <location filename="view/customaccessUi/infraredtestview/guideinfraredtestview.cpp" line="1209"/>
        <location filename="view/guideinfrared/guideinfraredpdaview.cpp" line="1338"/>
        <location filename="view/guideinfrared/guideinfraredview.cpp" line="1067"/>
        <source>Failed to turn on auxiliary lighting!</source>
        <translation type="unfinished">打开辅助照明失败！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/infraredtestview/infraredcabeltestview.cpp" line="893"/>
        <location filename="view/customaccessUi/infraredtestview/infraredtestview.cpp" line="704"/>
        <location filename="view/infrared/infraredpdaview.cpp" line="1034"/>
        <location filename="view/infrared/infraredview.cpp" line="790"/>
        <source>Init params failed, re-enter!</source>
        <translation>参数初始化失败，请重新进入！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="43"/>
        <source>Bay List</source>
        <translation>间隔列表</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="245"/>
        <location filename="view/customaccessUi/taskmodeview/connectorview.cpp" line="249"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="304"/>
        <source>Unable to open multiple items.</source>
        <translation>不支持打开多条目。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="249"/>
        <location filename="view/customaccessUi/taskmodeview/connectorview.cpp" line="253"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="308"/>
        <source>No item has been chosen.</source>
        <translation>未选择条目。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="267"/>
        <source>Please select a bay first.</source>
        <translation>请选择需提交的间隔。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/intervalview.cpp" line="297"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="195"/>
        <source>Not support to upload empty data or only background data!</source>
        <translation>不支持上传空数据或只有背景数据！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="284"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="155"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="219"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="223"/>
        <location filename="view/distributenetaccess/distributenettaskview.cpp" line="94"/>
        <source>Unable to open multiple task files.</source>
        <translation>不支持打开多任务文件。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="341"/>
        <location filename="view/customaccessUi/maintaskview.cpp" line="364"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="253"/>
        <source>Nonsupport to upload multiple tasks.</source>
        <translation>不支持上传多个任务。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="451"/>
        <location filename="view/customaccessUi/subtaskview.cpp" line="246"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="333"/>
        <source>Confirm to delete the item?</source>
        <translation>确定要删除条目？</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="538"/>
        <source>Subtask List</source>
        <translation>子任务列表</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="669"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="782"/>
        <source>Prohibit shutdown the device during detection.</source>
        <translation>检测过程中禁止关机。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/maintaskview.cpp" line="752"/>
        <source>Download failed.</source>
        <translation>下载失败。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/subtaskview.cpp" line="170"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="234"/>
        <source>Confirm to select a task.</source>
        <translation>确定选择一个任务。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="115"/>
        <source>All items have been tested.</source>
        <translation>所有条目已测试完毕。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="280"/>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="324"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="292"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="418"/>
        <source>Please complete the task first!</source>
        <translation>请先完成测试!</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="332"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="427"/>
        <source>Unable to compress multiple task files.</source>
        <translation>不支持压缩多个任务。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/circuitview.cpp" line="374"/>
        <location filename="view/customaccessUi/taskmodeview/taskmodeview.cpp" line="575"/>
        <source>Compress Done!</source>
        <translation>压缩完成！</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/connectorview.cpp" line="130"/>
        <location filename="view/customaccessUi/taskmodeview/currentphasetypeview.cpp" line="462"/>
        <location filename="view/customaccessUi/taskmodeview/phasetypeview.cpp" line="484"/>
        <source>All items have been tested and will automatically jump.</source>
        <translation>所有条目已测试完毕，即将自动跳转。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/currentphasetypeview.cpp" line="34"/>
        <location filename="view/customaccessUi/taskmodeview/phasetypeview.cpp" line="33"/>
        <source>Phase Type</source>
        <translation>相别选择</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/testypeview.cpp" line="71"/>
        <source>Test Type</source>
        <translation>测试类型</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/testypeview.cpp" line="92"/>
        <source>PD Type</source>
        <translation>局放类型</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="67"/>
        <source>BG Test</source>
        <translation>背景测试</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="259"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="486"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="206"/>
        <source>Please complete the background test.</source>
        <translation>请先完成背景测试。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="327"/>
        <source>Please select a test point.</source>
        <translation>请选择需提交的测点。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="344"/>
        <source>Empty test data in the point.</source>
        <translation>所选测点没有测试数据。</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="436"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="445"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="165"/>
        <source>AE BKGD</source>
        <translation>AE背景检测</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="456"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="174"/>
        <source>TEV BKGD</source>
        <translation>TEV背景检测</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="463"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="182"/>
        <source>UHF BKGD</source>
        <translation>UHF背景检测</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="470"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="190"/>
        <source>HFCT BKGD</source>
        <translation>HFCT背景检测</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="502"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="513"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="214"/>
        <source>AE Detect</source>
        <translation>AE检测</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="507"/>
        <location filename="view/customaccessUi/testpointview.cpp" line="518"/>
        <source>Test Point: </source>
        <translation>测点：</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="526"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="224"/>
        <source>TEV Detect</source>
        <translation>TEV检测</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="533"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="232"/>
        <source>UHF Detect</source>
        <translation>UHF检测</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/testpointview.cpp" line="540"/>
        <location filename="view/distributenetaccess/distributenettestpointview.cpp" line="240"/>
        <source>HFCT Detect</source>
        <translation>HFCT检测</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetaddtaskview.cpp" line="145"/>
        <source>Create tasks successfully.</source>
        <translation>创建任务成功。</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetaddtaskview.cpp" line="150"/>
        <source>No asset has been chosen.</source>
        <translation>未选择设备。</translation>
    </message>
    <message>
        <location filename="view/distributenetaccess/distributenetdeviceview.cpp" line="339"/>
        <source>Current task test completed.</source>
        <translation>当前测试任务已完成。</translation>
    </message>
    <message>
        <location filename="view/guideinfrared/guideinfraredplaybackview.cpp" line="186"/>
        <location filename="view/infrared/infraredplaybackview.cpp" line="141"/>
        <source>Open file failed!</source>
        <translation>打开文件失败！</translation>
    </message>
    <message>
        <location filename="view/guideinfrared/guideinfraredviewbase.cpp" line="349"/>
        <location filename="view/infrared/infraredviewbase.cpp" line="323"/>
        <source>Coefficient K</source>
        <translation>系数K</translation>
    </message>
    <message>
        <location filename="view/guideinfrared/guideinfraredviewbase.cpp" line="353"/>
        <location filename="view/infrared/infraredviewbase.cpp" line="327"/>
        <source>Coefficient B</source>
        <translation>系数B</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="54"/>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="377"/>
        <source>Searching</source>
        <translation>搜索中</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="185"/>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="290"/>
        <location filename="view/systemsetview/systemsetabout/bluetoothsetting/bluetoothsettingview.cpp" line="27"/>
        <source>Refresh</source>
        <translation>刷新</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="207"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="305"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="325"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="330"/>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="350"/>
        <location filename="view/peripheralmatch/conditionermatchview.cpp" line="791"/>
        <source>Current ID: </source>
        <translation>当前ID：</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="225"/>
        <source>Disconnect</source>
        <translation>断开</translation>
    </message>
    <message>
        <location filename="view/peripheralmatch/camatchview.cpp" line="448"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="671"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="680"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="689"/>
        <location filename="view/peripheralmatch/camatchview.cpp" line="698"/>
        <source>Update fail!</source>
        <translation>升级失败！</translation>
    </message>
    <message>
        <location filename="view/rfid/RFIDScanView.cpp" line="35"/>
        <location filename="view/systemsetview/systemsetabout/common/scanloading.cpp" line="22"/>
        <source>Scanning ...</source>
        <translation>扫描中…</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="69"/>
        <source>Write</source>
        <translation>写入</translation>
    </message>
    <message numerus="yes">
        <location filename="view/splash/activescreen.cpp" line="54"/>
        <source>The remaining activation time is %n day(s), please contact the supplier to re-activate.</source>
        <translation type="unfinished">
            <numerusform>剩余激活%n天，请及时联系供应商进行激活。</numerusform>
        </translation>
    </message>
    <message>
        <location filename="view/splash/activescreen.cpp" line="58"/>
        <source>The device is not activated, please contact the supplier to activate.</source>
        <translation>设备未激活，请联系供应商进行激活。</translation>
    </message>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="93"/>
        <source>Cable Multi-Parameter State Intelligent Diagnostic Instrument</source>
        <translation>电缆多参量状态智能诊断仪</translation>
    </message>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="99"/>
        <source>Intelligent Handheld Partial Discharge Detector</source>
        <translation>综合带电检测仪</translation>
    </message>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="119"/>
        <location filename="view/systemsetview/systemsetabout/AboutInfoView.cpp" line="133"/>
        <source>Uncalibrated</source>
        <translation>未校准</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/AboutInfoView.cpp" line="70"/>
        <source>Model: </source>
        <translation>型号：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/AboutInfoView.cpp" line="76"/>
        <source>PDStars Electric Co.,Ltd.</source>
        <translation>华乘电气科技股份有限公司</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/AboutInfoView.cpp" line="106"/>
        <source>Serial No: </source>
        <translation>序列号：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/cloud/CloudSetting.cpp" line="31"/>
        <source>Cloud Settings</source>
        <translation>云设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/cloud/CloudSetting.cpp" line="34"/>
        <location filename="view/update/updateSetting.cpp" line="36"/>
        <source>Cloud</source>
        <translation>云</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/cloud/lineeditgrounp.cpp" line="91"/>
        <source>Address</source>
        <translation>地址</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/cloud/lineeditgrounp.cpp" line="96"/>
        <source>Port</source>
        <translation>端口</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/common/settingloading.cpp" line="27"/>
        <source>Connecting</source>
        <translation>连接中</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/mobilenetwork/ppplineeditgroup.cpp" line="91"/>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnlineeditgroup.cpp" line="86"/>
        <source>User</source>
        <translation>用户名</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/mobilenetwork/ppplineeditgroup.cpp" line="96"/>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnlineeditgroup.cpp" line="91"/>
        <source>PWD</source>
        <translation>密码</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/mobilenetwork/ppplineeditgroup.cpp" line="101"/>
        <source>APN</source>
        <translation>APN</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="329"/>
        <source>To format storage or not?</source>
        <translation>是否格式化存储卡？</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="335"/>
        <source>Format failed, the disk is being occupied.</source>
        <translation>格式化失败，磁盘被占用。</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="347"/>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="356"/>
        <source>Formatted!</source>
        <translation>格式化成功！</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="351"/>
        <source>Cannot be formated!</source>
        <translation>格式化失败！</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/versioninfo.cpp" line="242"/>
        <source>Screenshot successfully!</source>
        <translation>截屏成功！</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/versioninfo.cpp" line="246"/>
        <source>Screenshot failed!</source>
        <translation>截屏失败！</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnlineeditgroup.cpp" line="81"/>
        <source>Domain</source>
        <translation>域名</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/inputwifipasswd.cpp" line="151"/>
        <source>Connect</source>
        <translation>连接</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/routinesettingview.cpp" line="238"/>
        <source>USB already in R/W Mode.</source>
        <translation>USB已处于读写模式。</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/routinesettingview.cpp" line="245"/>
        <source>Enable R/W mode for USB?</source>
        <translation>确认切换为USB读写模式？</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/settingloginview.cpp" line="19"/>
        <source>Admin Login</source>
        <translation>管理员登录</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/shortkeysettingview.cpp" line="301"/>
        <source>Confirm to take effect after auto shutdown and restart manually.</source>
        <translation>确定自动关机并手动重启后生效。</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/shortkeysettingview.cpp" line="402"/>
        <source>Initialize calibration success.</source>
        <translation>初始化校准成功。</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetwidget/updatesettingview.cpp" line="30"/>
        <source>Update</source>
        <translation>升级</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="87"/>
        <source>Operation is not allowed while calibrating!</source>
        <translation>校准中，禁止其他操作！</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="498"/>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="579"/>
        <source>Please select a gear.</source>
        <translation>请选择一个挡位。</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="596"/>
        <source>Write success.</source>
        <translation>写入成功。</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="602"/>
        <source>Write failure.</source>
        <translation>写入失败。</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="628"/>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="358"/>
        <source>Restoring default succeeded.</source>
        <translation>恢复默认成功。</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="633"/>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="354"/>
        <source>Restoring default failed.</source>
        <translation>恢复默认失败。</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="689"/>
        <source>Get calibrate parameters failed.</source>
        <translation>获取校准参数失败.</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r2tevcalibrateview.cpp" line="736"/>
        <source>Calibrating, please wait.</source>
        <translation>校准中，请等待。</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="437"/>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="457"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="594"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="614"/>
        <source>Diagnostic unfinished, cannot exit!</source>
        <translation>诊断未完成，请勿退出！</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1432"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1241"/>
        <source>Save the cloud diagnose file failed.</source>
        <translation>保存诊断数据文件失败。</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1438"/>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1246"/>
        <source>Diagnosing ...</source>
        <translation>诊断中….</translation>
    </message>
    <message>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="72"/>
        <source>Minor</source>
        <translation>一般</translation>
    </message>
    <message>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="73"/>
        <source>Serious</source>
        <translation>严重</translation>
    </message>
    <message>
        <location filename="view/widgets/diagretselect/diagretselectdialog.cpp" line="74"/>
        <source>Emergency</source>
        <translation>紧急</translation>
    </message>
    <message>
        <location filename="view/currentdetection/currentdetectionchart.cpp" line="293"/>
        <location filename="view/widgets/histogram/HistogramChart.cpp" line="216"/>
        <location filename="view/widgets/histogram/HistogramChart.cpp" line="451"/>
        <source>Max: </source>
        <translation>最大值：</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="44"/>
        <source>Sunshine</source>
        <translation type="unfinished">晴</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="47"/>
        <source>Overcast</source>
        <translation type="unfinished">阴</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="50"/>
        <source>Rainy</source>
        <translation type="unfinished">雨</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="53"/>
        <source>Snowy</source>
        <translation type="unfinished">雪</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="56"/>
        <source>Fog</source>
        <translation type="unfinished">雾</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="59"/>
        <source>Thunderstorm</source>
        <translation type="unfinished">雷雨</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="62"/>
        <source>Cloudy</source>
        <translation type="unfinished">多云</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="108"/>
        <source>Temperature</source>
        <translation type="unfinished">温度</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="111"/>
        <source>Humidity</source>
        <translation type="unfinished">湿度</translation>
    </message>
    <message>
        <location filename="view/PDAUi/PDAUiBean/environmentsettingdialog.cpp" line="114"/>
        <source>Weather</source>
        <translation type="unfinished">天气</translation>
    </message>
</context>
<context>
    <name>R3TEVCalibrateView</name>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="269"/>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="302"/>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="333"/>
        <source>Operate failed!</source>
        <translation>操作失败！</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="31"/>
        <source>Calibration Pos 1</source>
        <translation>校准点1</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="32"/>
        <source>Calibration Pos 2</source>
        <translation>校准点2</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="33"/>
        <source>Calibration</source>
        <translation>校准</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="34"/>
        <source>More</source>
        <translation>更多</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="35"/>
        <source>Channel</source>
        <translation>通道</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateviewdefine.h" line="36"/>
        <source>Restore</source>
        <translation>恢复</translation>
    </message>
</context>
<context>
    <name>RFID</name>
    <message>
        <location filename="view/rfid/rfidview.cpp" line="38"/>
        <source>Read</source>
        <translation>读取</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidview.cpp" line="39"/>
        <source>Write</source>
        <translation>写入</translation>
    </message>
</context>
<context>
    <name>RFIDWriteView</name>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="57"/>
        <source>Write RFID</source>
        <translation>RFID写入</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="172"/>
        <location filename="view/rfid/rfidwriteview.cpp" line="261"/>
        <source>Can not write.</source>
        <translation>无法写入。</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="191"/>
        <source>Please input the 12-numerics test number.</source>
        <translation>请输入12位长度的测试编号。</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="196"/>
        <source>Test number contains non-numeric characters.</source>
        <translation>测试编号中含有非数字字符。</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="201"/>
        <source>The length of the substation name is illegal.</source>
        <translation>站点名称字符长度不符合。</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="206"/>
        <source>The length of the asset name is illegal.</source>
        <translation>设备名称字符长度不符合。</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="211"/>
        <source>The length of the asset number is illegal.</source>
        <translation>设备编号字符长度不符合。</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="216"/>
        <source>Asset number contains non-numeric and non-alphabetic characters.</source>
        <translation>设备编号中含有非数字和非字母字符。</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="221"/>
        <source>Illegal voltage level.</source>
        <translation>电压等级不合法。</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="226"/>
        <source>Voltage level contains non-numeric characters.</source>
        <translation>电压等级中含有非数字字符。</translation>
    </message>
    <message>
        <location filename="view/rfid/rfidwriteview.cpp" line="241"/>
        <source>Confirm to write this info?</source>
        <translation>确定写入此信息？</translation>
    </message>
</context>
<context>
    <name>RecordPlayPanelView</name>
    <message>
        <location filename="view/recordplay/recordplayview.cpp" line="81"/>
        <source>Delete View</source>
        <translation>删除</translation>
    </message>
</context>
<context>
    <name>RecordPlayView</name>
    <message>
        <location filename="view/recordplay/recordplayview.cpp" line="31"/>
        <location filename="view/recordplay/recordplayviewdefine.h" line="31"/>
        <source>Record</source>
        <translation>录音</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayview.cpp" line="32"/>
        <source>Playback</source>
        <translation>播放</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayview.cpp" line="33"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="25"/>
        <source>Play</source>
        <translation>播放</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="26"/>
        <source>Previous</source>
        <translation>上一个</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="27"/>
        <source>Next</source>
        <translation>下一个</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="28"/>
        <source>Volume</source>
        <translation>音量</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="32"/>
        <source>Continue</source>
        <translation>继续</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="33"/>
        <source>Pause</source>
        <translation>暂停</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="34"/>
        <source>Stop</source>
        <translation>停止</translation>
    </message>
    <message>
        <location filename="view/recordplay/recordplayviewdefine.h" line="37"/>
        <source>Record Duration</source>
        <translation>录音时长</translation>
    </message>
</context>
<context>
    <name>RecordView</name>
    <message>
        <location filename="view/recordplay/RecordView.cpp" line="356"/>
        <source>Fail to start recording, the disk is occupied.</source>
        <translation>开始录音失败，磁盘被占用。</translation>
    </message>
    <message>
        <location filename="view/recordplay/RecordView.cpp" line="517"/>
        <source>Unrecorded!</source>
        <translation>录音失败！</translation>
    </message>
</context>
<context>
    <name>RoutineSettingOtherView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/routinesettingotherview.cpp" line="62"/>
        <source>Others</source>
        <translation>其他</translation>
    </message>
</context>
<context>
    <name>RoutineSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/routinesettingview.cpp" line="108"/>
        <location filename="view/systemsetview/systemsetwidget/routinesettingview.cpp" line="594"/>
        <source>General</source>
        <translation>常规</translation>
    </message>
</context>
<context>
    <name>SettingView</name>
    <message>
        <location filename="view/systemsetview/settingview.cpp" line="32"/>
        <source>General</source>
        <translation>常规</translation>
    </message>
    <message>
        <location filename="view/systemsetview/settingview.cpp" line="33"/>
        <source>Measure</source>
        <translation>测量</translation>
    </message>
    <message>
        <location filename="view/systemsetview/settingview.cpp" line="34"/>
        <source>Network</source>
        <translation>网络</translation>
    </message>
    <message>
        <location filename="view/systemsetview/settingview.cpp" line="35"/>
        <source>Update</source>
        <translation>升级</translation>
    </message>
</context>
<context>
    <name>ShortKeySettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/shortkeysettingview.cpp" line="428"/>
        <source>Export VPN log files success.</source>
        <translation>导出VPN日志文件成功。</translation>
    </message>
</context>
<context>
    <name>SoftwareInfoView</name>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="84"/>
        <source>Downloading, please wait ...</source>
        <translation>下载中，请等待…</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="88"/>
        <source>Downloaded!</source>
        <translation>下载成功！</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="100"/>
        <source>Forbid installing the firmware when power off or low power, confirm?</source>
        <translation>安装过程中禁止关机或电量过低，确认？</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="108"/>
        <source>Not downloaded yet, can not be installed!</source>
        <translation>未下载完毕，无法安装！</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="243"/>
        <source>Connect fail</source>
        <translation>连接失败</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="253"/>
        <source>Connected</source>
        <translation>已连接</translation>
    </message>
    <message>
        <location filename="view/update/softwareinfoview.cpp" line="273"/>
        <source>Failed to launch update process</source>
        <translation>启动升级进程失败</translation>
    </message>
</context>
<context>
    <name>SoftwareUpdateChart</name>
    <message>
        <location filename="view/update/softwareupdatechart.cpp" line="32"/>
        <source>Latest Version illustration: </source>
        <translation>最新版本说明：</translation>
    </message>
    <message>
        <location filename="view/update/softwareupdatechart.cpp" line="37"/>
        <source>Download Progress: </source>
        <translation>下载进度：</translation>
    </message>
    <message>
        <location filename="view/update/softwareupdatechart.cpp" line="45"/>
        <source>Connection Status: </source>
        <translation>连接状态：</translation>
    </message>
    <message>
        <location filename="view/update/softwareupdatechart.cpp" line="46"/>
        <source>Current Version: </source>
        <translation>当前版本：</translation>
    </message>
    <message>
        <location filename="view/update/softwareupdatechart.cpp" line="47"/>
        <source>Latest Version: </source>
        <translation>最新版本：</translation>
    </message>
</context>
<context>
    <name>SplashScreen</name>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="119"/>
        <source>Calibration Date: </source>
        <translation>校准日期：</translation>
    </message>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="133"/>
        <source>Next Calibration Date: </source>
        <translation>下次校准日期：</translation>
    </message>
    <message>
        <location filename="view/splash/splashscreen.cpp" line="159"/>
        <source>The system is initializing, self-checking ...</source>
        <translation>系统正在初始化，自检中…</translation>
    </message>
</context>
<context>
    <name>StatusBar</name>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="632"/>
        <source>Low battery, please exit to the desktop and charge the device.</source>
        <translation>电量过低，请退出到桌面并对设备进行充电。</translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="647"/>
        <source>Low storage, please clean.</source>
        <translation>可用存储空间过低，请清理存储空间。</translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="661"/>
        <source>Low battery, automatic shutdown 1min later, please exit to the desktop.</source>
        <translation>电量过低，即将在1分钟后自动关机，请退出到桌面。</translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="683"/>
        <source>Temperature &lt; %1℃</source>
        <oldsource>Temperature &lt; %1</oldsource>
        <translation type="unfinished">温度&lt;%1℃</translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="686"/>
        <source>Humidity &gt; %1%RH</source>
        <oldsource>Humidity &gt; %1</oldsource>
        <translation type="unfinished">湿度&gt;%1%RH</translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="689"/>
        <source>Temperature &lt; %1℃, Humidity &gt; %2%RH</source>
        <translation type="unfinished">温度&lt;%1℃，湿度&gt;%2%RH</translation>
    </message>
    <message>
        <location filename="widget/statusbar/StatusBar.cpp" line="692"/>
        <source>%1, please pay attention to safety.</source>
        <translation type="unfinished">%1，请注意安全。</translation>
    </message>
    <message>
        <source>Humidity &gt; %1, please pay attention to safety.</source>
        <translation type="vanished">湿度&gt;80%，请注意安全。</translation>
    </message>
</context>
<context>
    <name>SystemSetView</name>
    <message>
        <location filename="view/systemsetview/systemsetwidget/systemsetview.cpp" line="37"/>
        <source>System Settings</source>
        <translation>系统设置</translation>
    </message>
</context>
<context>
    <name>SystemViewConfig</name>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="27"/>
        <source>Cloud Settings</source>
        <translation>云设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="28"/>
        <source>VPN Settings</source>
        <translation>VPN设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="29"/>
        <source>Terminal Settings</source>
        <translation>终端设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="30"/>
        <source>WiFi Settings</source>
        <translation>WiFi设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="31"/>
        <source>Bluetooth Settings</source>
        <translation>蓝牙设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="32"/>
        <source>Upgrade</source>
        <translation>升级</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="33"/>
        <location filename="view/systemsetview/SystemViewConfig.h" line="66"/>
        <source>Remote Upgrade</source>
        <translation>远程升级</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="34"/>
        <source>Storage Card</source>
        <translation>存储卡</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="35"/>
        <source>Brightness</source>
        <translation>背光亮度</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="36"/>
        <source>Volume</source>
        <translation>音量</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="37"/>
        <source>Language</source>
        <translation>语言设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="38"/>
        <source>Hotspot</source>
        <translation>热点</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="39"/>
        <source>Grid Frequency</source>
        <translation>电网频率</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="40"/>
        <source>Mobile Network Settings</source>
        <translation>移动网络设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="41"/>
        <source>Mobile Network</source>
        <translation>移动网络</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="42"/>
        <source>4G</source>
        <translation>4G</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="43"/>
        <source>Auto Shutdown Time</source>
        <translation>自动关机时间</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="44"/>
        <source>Default</source>
        <translation>恢复默认</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="45"/>
        <source>Upgrade Firmware</source>
        <translation>固件升级</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="46"/>
        <source>System Info</source>
        <translation>关于</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="47"/>
        <source>Others</source>
        <translation>其他</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="48"/>
        <source>Date</source>
        <translation>日期</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="49"/>
        <source>Time</source>
        <translation>时间</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="50"/>
        <source>Date &amp; Time</source>
        <translation>日期时间设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="51"/>
        <source>Timezone</source>
        <translation>时区</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="55"/>
        <source>On</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="56"/>
        <source>Off</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="61"/>
        <source>Self-Check</source>
        <translation>自检</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="62"/>
        <source>Format Storage</source>
        <translation>格式化存储卡</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="63"/>
        <source>File Comment Box</source>
        <translation>文件备注框</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="65"/>
        <source>Remote Settings</source>
        <translation>远程设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="68"/>
        <source>Model: </source>
        <translation>型号：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="69"/>
        <source>Serial No: </source>
        <translation>序列号：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="70"/>
        <source>Version: </source>
        <translation>版本号：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="71"/>
        <source>Model1: </source>
        <translation>型号1：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="72"/>
        <source>Version Info</source>
        <translation>版本信息</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="73"/>
        <source>Calibration Date: </source>
        <translation>校准日期：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="74"/>
        <source>Next Calibration Date: </source>
        <translation>下次校准日期：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="75"/>
        <source>Activation Date: </source>
        <translation>激活日期：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="76"/>
        <source>No Time Limit</source>
        <translation>无期限</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="77"/>
        <source>UHF</source>
        <translation>特高频</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="78"/>
        <source>HFCT</source>
        <translation>高频电流</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="79"/>
        <source>Sync.</source>
        <translation>同步器</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="80"/>
        <source>Model2: </source>
        <translation>型号2：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="82"/>
        <source>Software Interface</source>
        <translation>专用接口</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="83"/>
        <source>Mass Storage</source>
        <translation>移动磁盘</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="84"/>
        <source>USB Settings</source>
        <translation>USB设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="86"/>
        <source>12Hr</source>
        <translation>12时制</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="87"/>
        <source>24Hr</source>
        <translation>24时制</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="89"/>
        <source>Date Format</source>
        <translation>日期格式</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="90"/>
        <source>Time Format</source>
        <translation>时间格式</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="91"/>
        <source>DST</source>
        <translation>夏令时</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="92"/>
        <source>GMT</source>
        <translation>标准时间</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="93"/>
        <source>Y/M/D</source>
        <translation>年/月/日</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="94"/>
        <source>M/D/Y</source>
        <translation>月/日/年</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="95"/>
        <source>D/M/Y</source>
        <translation>日/月/年</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="97"/>
        <source>PRPS BG Color</source>
        <translation>PRPS背景色</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="98"/>
        <source>Gray</source>
        <translation>灰色</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="99"/>
        <source>White</source>
        <translation>白色</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="100"/>
        <source>Temperature Unit</source>
        <translation>温度单位</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="104"/>
        <source>Debug Settings</source>
        <translation>快捷键调试</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="105"/>
        <source>Network Test</source>
        <translation>网络测试</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="106"/>
        <source>Detection Mode</source>
        <translation>检测模式</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="107"/>
        <source>Infrared Settings</source>
        <translation>红外设置</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="108"/>
        <source>Custom Access Mode</source>
        <translation>定制接入模式</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="109"/>
        <source>Auto Shutdown Switch</source>
        <translation>自动关机开关</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="110"/>
        <source>Security Verify Switch</source>
        <translation>安全认证开关</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="111"/>
        <source>Calibration Write Switch</source>
        <translation>校准写入开关</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="112"/>
        <source>Calibration Interval Period</source>
        <translation>校准间隔</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="113"/>
        <source>PRPS Sample Interval</source>
        <translation>PRPS采样间隔</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="114"/>
        <source>Initialized Calibration</source>
        <translation>初始化校准</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="115"/>
        <source>Export VPN Log</source>
        <translation>导出VPN日志</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="116"/>
        <source>LCD Reboot Switch</source>
        <translation>LCD重启开关</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="117"/>
        <source>months</source>
        <translation>月</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="118"/>
        <source>Auto Sync</source>
        <translation>自动同步</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="120"/>
        <location filename="view/systemsetview/SystemViewConfig.h" line="147"/>
        <source>Auto</source>
        <translation>自动认证</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="121"/>
        <source>RFID</source>
        <translation>RFID</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="125"/>
        <source>Bluetooth</source>
        <translation>蓝牙</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="126"/>
        <source>AE Record Time</source>
        <translation>超声波录音时长</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="128"/>
        <source>Soft Keyboard Mode</source>
        <translation>软键盘模式</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="129"/>
        <source>Full</source>
        <translation>全键盘</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="130"/>
        <source>Sudoku</source>
        <translation>九宫格</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="132"/>
        <source>Connect</source>
        <translation>连接</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="133"/>
        <source>Disconnect</source>
        <translation>断开</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="135"/>
        <source>Low Battery Limit</source>
        <translation>低电量下限</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="138"/>
        <source>Real-time Diagnosis</source>
        <translation>实时诊断</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="139"/>
        <source>PRPS Color</source>
        <translation>PRPS颜色</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="140"/>
        <source>Rainbow</source>
        <translation>彩虹</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="141"/>
        <source>Blaze</source>
        <translation>火焰</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="143"/>
        <source>Security Verify Mode</source>
        <translation>安全认证模式</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="144"/>
        <source>None</source>
        <translation>不认证</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="145"/>
        <source>Query</source>
        <translation>单向认证</translation>
    </message>
    <message>
        <location filename="view/systemsetview/SystemViewConfig.h" line="146"/>
        <source>Peer</source>
        <translation>双向认证</translation>
    </message>
</context>
<context>
    <name>TEVPanelView</name>
    <message>
        <location filename="view/tev/tevview.cpp" line="145"/>
        <source>TEV Calibration</source>
        <translation>TEV校准</translation>
    </message>
</context>
<context>
    <name>TEVPulseChart</name>
    <message>
        <location filename="view/tev/tevpulsechart.cpp" line="56"/>
        <source>Pulse Count: </source>
        <translation>脉冲计数：</translation>
    </message>
    <message>
        <location filename="view/tev/tevpulsechart.cpp" line="63"/>
        <source>Pulses/Cycle: </source>
        <translation>单周期脉冲数：</translation>
    </message>
    <message>
        <location filename="view/tev/tevpulsechart.cpp" line="68"/>
        <source>Severity: </source>
        <translation>放电严重程度：</translation>
    </message>
</context>
<context>
    <name>TEVR3Calibrate</name>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="24"/>
        <source>Channel: </source>
        <translation>通道：</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="25"/>
        <source>Calibration pos 1 AMP (mV): </source>
        <translation>校准点1幅值(mV)：</translation>
    </message>
    <message>
        <location filename="view/tev/calibrate/r3tevcalibrateview.cpp" line="26"/>
        <source>Calibration pos 2 AMP (mV): </source>
        <translation>校准点2幅值(mV)：</translation>
    </message>
</context>
<context>
    <name>TEVView</name>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="25"/>
        <source>Mode</source>
        <translation>模式</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="26"/>
        <source>Single</source>
        <translation>单次</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="27"/>
        <source>Continuous</source>
        <translation>连续</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="28"/>
        <source>Single Sample</source>
        <translation>单次采样</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="29"/>
        <source>Screen Shot</source>
        <translation>截屏</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="30"/>
        <source>Test</source>
        <translation>测试</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="31"/>
        <source>More...</source>
        <translation>更多...</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="32"/>
        <source>Warning</source>
        <translation>黄色报警</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="33"/>
        <source>High Risk</source>
        <translation>红色报警</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="34"/>
        <source>Save</source>
        <translation>保存数据</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="35"/>
        <source>Load Data</source>
        <translation>载入数据</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="36"/>
        <source>Delete Data</source>
        <translation>删除数据</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="37"/>
        <source>Default</source>
        <translation>恢复默认</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="39"/>
        <source>Pulses Accumulation Period</source>
        <translation>脉冲计数时长</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="40"/>
        <source>Add</source>
        <translation>新增</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="41"/>
        <source>Power Sync</source>
        <translation>电源同步</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="42"/>
        <source>On</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="43"/>
        <source>Off</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="44"/>
        <source>Sample</source>
        <translation>采样</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="45"/>
        <source>Phase Shift</source>
        <translation>相移</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="49"/>
        <source>Record</source>
        <translation>录屏</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="50"/>
        <source>Record Time</source>
        <translation>录屏时间</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="51"/>
        <source>Playback</source>
        <translation>录屏回放</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="52"/>
        <source>Delete Record</source>
        <translation>删除录屏</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="53"/>
        <source>Accumulate</source>
        <translation>累积使能</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="54"/>
        <source>Sync Mode</source>
        <translation>同步源</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="55"/>
        <source>Save RFID</source>
        <translation>RFID保存</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="56"/>
        <source>Start</source>
        <translation>开始</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="57"/>
        <source>Stop</source>
        <translation>停止</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="60"/>
        <source>View Sig Amp</source>
        <translation>幅值图谱</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="61"/>
        <source>View Sig Pulse</source>
        <translation>脉冲图谱</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="62"/>
        <source>View PRPD&amp;PRPS</source>
        <translation>PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="63"/>
        <source>Amplitude</source>
        <translation>幅值图谱</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="64"/>
        <source>Pulse</source>
        <translation>脉冲图谱</translation>
    </message>
    <message>
        <location filename="view/tev/tevviewdefine.h" line="65"/>
        <source>PRPD&amp;PRPS</source>
        <translation>PRPS图谱</translation>
    </message>
</context>
<context>
    <name>TaskModeConfig</name>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="26"/>
        <source>Connect App</source>
        <translation>连接终端</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="27"/>
        <source>Discon</source>
        <translation>断开</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="28"/>
        <source>Submit</source>
        <translation>提交</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="29"/>
        <source>Delete</source>
        <translation>删除</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="30"/>
        <source>Open</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="31"/>
        <source>Refresh</source>
        <translation>刷新</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="32"/>
        <source>Compress</source>
        <translation>压缩</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="33"/>
        <source>Switch Mode</source>
        <translation>图谱切换</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="35"/>
        <source>Circuit</source>
        <translation>线路</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="36"/>
        <source>Connector</source>
        <translation>接头</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="38"/>
        <source>Load Current</source>
        <translation>负荷电流</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="39"/>
        <source>Grounding Current</source>
        <translation>接地电流</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="40"/>
        <source>Infrared</source>
        <translation>红外</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="41"/>
        <source>PD</source>
        <translation>局放</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="43"/>
        <source>Save</source>
        <translation type="unfinished">保存</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="45"/>
        <source>Phase N</source>
        <translation>总接地</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="46"/>
        <source>Phase A</source>
        <translation>A相</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="47"/>
        <source>Phase B</source>
        <translation>B相</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="48"/>
        <source>Phase C</source>
        <translation>C相</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="51"/>
        <source>AE</source>
        <translation>超声波</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="52"/>
        <source>UHF</source>
        <translation>特高频</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="53"/>
        <source>HFCT</source>
        <translation>高频</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="54"/>
        <source>BKGD Detect</source>
        <translation>背景检测</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="55"/>
        <source>AE BKGD</source>
        <translation>超声波背景</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="56"/>
        <source>UHF BKGD</source>
        <translation>特高频背景</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="57"/>
        <source>HFCT BKGD</source>
        <translation>高频背景</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="59"/>
        <source>Untest</source>
        <translation>未测</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="60"/>
        <source>Tested</source>
        <translation>已测</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="62"/>
        <source>Auto</source>
        <translation>自动</translation>
    </message>
    <message>
        <location filename="view/customaccessUi/taskmodeview/taskmodeconfig.h" line="63"/>
        <source>Manual</source>
        <translation>手动</translation>
    </message>
</context>
<context>
    <name>UHFSpectrumChart</name>
    <message>
        <location filename="view/uhf/uhfintervalview/UHFSpectrumChart.cpp" line="24"/>
        <source>Upper Limit: </source>
        <translation>上限：</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfintervalview/UHFSpectrumChart.cpp" line="25"/>
        <source>Lower limit: </source>
        <translation>下限：</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfintervalview/UHFSpectrumChart.cpp" line="26"/>
        <source>Range: </source>
        <translation>量程：</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfintervalview/UHFSpectrumChart.cpp" line="27"/>
        <source>Max: </source>
        <translation>最大值：</translation>
    </message>
</context>
<context>
    <name>UHFView</name>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="28"/>
        <source>Mode</source>
        <translation>模式</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="29"/>
        <source>Single Sample</source>
        <translation>单次采样</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="30"/>
        <source>Single</source>
        <translation>单次</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="31"/>
        <source>Continuous</source>
        <translation>连续</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="32"/>
        <source>Warning</source>
        <translation>黄色报警</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="33"/>
        <source>High Risk</source>
        <translation>红色报警</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="36"/>
        <source>BW</source>
        <translation>带宽</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="37"/>
        <source>Accumulate</source>
        <translation>累积使能</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="38"/>
        <source>Accumulative Time</source>
        <translation>累积时长</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="39"/>
        <location filename="view/uhf/UHFViewConfig.h" line="60"/>
        <source>Gain</source>
        <translation>增益</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="40"/>
        <source>Default</source>
        <translation>恢复默认</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="41"/>
        <source>High</source>
        <translation>高</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="42"/>
        <source>Low</source>
        <translation>低</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="43"/>
        <source>All</source>
        <translation>全</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="44"/>
        <source>High Pass</source>
        <translation>高通</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="45"/>
        <source>Low Pass</source>
        <translation>低通</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="46"/>
        <source>All Pass</source>
        <translation>全通</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="47"/>
        <source>Power</source>
        <translation>电源</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="48"/>
        <source>Light</source>
        <translation>光</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="49"/>
        <source>Sync Mode</source>
        <translation>同步源</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="50"/>
        <source>Vertical Scale</source>
        <translation>垂直标度</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="51"/>
        <location filename="view/uhf/UHFViewConfig.h" line="67"/>
        <source>More...</source>
        <translation>更多...</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="52"/>
        <source>Save Data</source>
        <translation>保存数据</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="53"/>
        <source>Save</source>
        <translation>保存</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="54"/>
        <source>Save RFID</source>
        <translation>RFID保存</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="55"/>
        <source>Delete Data</source>
        <translation>删除数据</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="56"/>
        <source>Load Data</source>
        <translation>载入数据</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="57"/>
        <source>Phase Shift</source>
        <translation>相移</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="58"/>
        <source>Sample</source>
        <translation>采样</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="59"/>
        <source>Diagnostic</source>
        <translation>诊断</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="61"/>
        <source>On</source>
        <translation>打开</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="62"/>
        <location filename="view/uhf/UHFViewConfig.h" line="90"/>
        <source>Off</source>
        <translation>关闭</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="63"/>
        <source>Start</source>
        <translation>开始</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="64"/>
        <source>Stop</source>
        <translation>停止</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="65"/>
        <source>Pause</source>
        <translation>暂停</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="66"/>
        <source>Add</source>
        <translation>新增</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="68"/>
        <source>Threshold</source>
        <translation>阈值</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="69"/>
        <source>Noise Reduction</source>
        <translation>降噪</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="70"/>
        <source>Altas Type</source>
        <translation>图谱类型</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="73"/>
        <source>Record</source>
        <translation>录屏</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="74"/>
        <source>Record Time</source>
        <translation>录屏时间</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="75"/>
        <source>Playback</source>
        <translation>录屏回放</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="76"/>
        <source>Delete Record</source>
        <translation>删除录屏</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="78"/>
        <source>View Sig Amp</source>
        <translation>幅值图谱</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="79"/>
        <source>View Period</source>
        <translation>周期图谱</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="80"/>
        <source>View PRPD&amp;PRPS</source>
        <translation>PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="81"/>
        <source>Amplitude</source>
        <translation>幅值图谱</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="82"/>
        <source>Period</source>
        <translation>周期图谱</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="83"/>
        <source>PRPD&amp;PRPS</source>
        <translation>PRPD&amp;PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="84"/>
        <source>PRPS</source>
        <translation>PRPS图谱</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="85"/>
        <source>PRPD</source>
        <translation>PRPD图谱</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="89"/>
        <source>Auto</source>
        <translation>自动</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="91"/>
        <source>Level 1</source>
        <translation>等级1</translation>
    </message>
    <message>
        <location filename="view/uhf/UHFViewConfig.h" line="92"/>
        <source>Level 2</source>
        <translation>等级2</translation>
    </message>
</context>
<context>
    <name>UhfPRPSPDAView</name>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1476"/>
        <source>Connection error.</source>
        <translation>连接错误。</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpspdaview.cpp" line="1481"/>
        <source>Server connection abnormal.</source>
        <translation>服务器连接异常。</translation>
    </message>
</context>
<context>
    <name>UhfPRPSView</name>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1286"/>
        <source>Connection error.</source>
        <translation>连接错误。</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1291"/>
        <source>Server connection abnormal.</source>
        <translation>服务器连接异常。</translation>
    </message>
    <message>
        <location filename="view/uhf/uhfprpsview/uhfprpsview.cpp" line="1913"/>
        <source>No data!</source>
        <translation>无数据！</translation>
    </message>
</context>
<context>
    <name>UpdateFirmware</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/updatefirmware/updatefirmware.cpp" line="17"/>
        <source>Updating</source>
        <translation>升级中</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/updatefirmware/updatefirmware.cpp" line="45"/>
        <source>Please exit after the firmware update is completed.</source>
        <translation>请完成固件更新后退出。</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/updatefirmware/updatefirmware.cpp" line="76"/>
        <source>Update successfully!</source>
        <translation>升级成功！</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/updatefirmware/updatefirmware.cpp" line="80"/>
        <source>Update failed!</source>
        <translation>升级失败！</translation>
    </message>
</context>
<context>
    <name>UpdateView</name>
    <message>
        <location filename="view/update/updateviewconfig.h" line="12"/>
        <source>Refresh</source>
        <translation>刷新</translation>
    </message>
    <message>
        <location filename="view/update/updateviewconfig.h" line="13"/>
        <source>Download</source>
        <translation>下载</translation>
    </message>
    <message>
        <location filename="view/update/updateviewconfig.h" line="14"/>
        <source>Install</source>
        <translation>安装</translation>
    </message>
</context>
<context>
    <name>View</name>
    <message>
        <location filename="view/View.h" line="30"/>
        <source>Substation Name: </source>
        <translation>站名：</translation>
    </message>
    <message>
        <location filename="view/View.h" line="31"/>
        <source>Asset: </source>
        <translation>设备：</translation>
    </message>
    <message>
        <location filename="view/View.h" line="32"/>
        <source>Test Point: </source>
        <translation>测点：</translation>
    </message>
</context>
<context>
    <name>VpnSetting</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnsetting.cpp" line="83"/>
        <source>Connecting VPN server...</source>
        <translation>连接VPN服务中…</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnsetting.cpp" line="130"/>
        <source>Please input VPN server domain.</source>
        <translation>请输入VPN服务器域名。</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnsetting.cpp" line="244"/>
        <source>Connect to VPN server success.</source>
        <translation>连接VPN服务器成功。</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/vpnsetting/vpnsetting.cpp" line="248"/>
        <source>Connect to VPN server failure.</source>
        <translation>连接VPN服务器失败。</translation>
    </message>
</context>
<context>
    <name>WaveTestView</name>
    <message>
        <location filename="view/ca/wave/wavetestview.cpp" line="906"/>
        <source>Set parameter fail, please check connection state.</source>
        <translation>设置参数失败，请检查连接状态。</translation>
    </message>
    <message>
        <location filename="view/ca/wave/wavetestview.cpp" line="1103"/>
        <source>Start sample fail, please check connection state.</source>
        <translation>开始采样失败，请检查连接状态。</translation>
    </message>
</context>
<context>
    <name>WaveView</name>
    <message>
        <location filename="view/ca/wave/waveview.cpp" line="1069"/>
        <source>Set parameter fail, please check connection state.</source>
        <translation>设置参数失败，请检查连接状态。</translation>
    </message>
    <message>
        <location filename="view/ca/wave/waveview.cpp" line="1834"/>
        <source>Start sample fail, please check connection state.</source>
        <translation>开始采样失败，请检查连接状态。</translation>
    </message>
</context>
<context>
    <name>Weekday</name>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="33"/>
        <source>Monday</source>
        <translation>星期一</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="34"/>
        <source>Tuesday</source>
        <translation>星期二</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="35"/>
        <source>Wednesday</source>
        <translation>星期三</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="36"/>
        <source>Thursday</source>
        <translation>星期四</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="37"/>
        <source>Friday</source>
        <translation>星期五</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="38"/>
        <source>Saturday</source>
        <translation>星期六</translation>
    </message>
    <message>
        <location filename="widget/datetimeWidget/DateTimeWidget.cpp" line="39"/>
        <source>Sunday</source>
        <translation>星期日</translation>
    </message>
</context>
<context>
    <name>WifiSettingView</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="128"/>
        <source>WiFi</source>
        <translation>WiFi</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="133"/>
        <source>Select a network ...</source>
        <translation>选择网络…</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="800"/>
        <source>Authenticating ...</source>
        <translation>认证中…</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="854"/>
        <source>Unable to connect the hotspot!</source>
        <translation>无法连接热点！</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="1033"/>
        <source>Access hotspot successfully!</source>
        <translation>连接热点成功！</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/wifisettingview.cpp" line="1046"/>
        <source>Get IP info failed!</source>
        <translation>获取IP信息失败！</translation>
    </message>
</context>
<context>
    <name>inputWifiPassWD</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/wifisetting/inputwifipasswd.cpp" line="138"/>
        <source>Password</source>
        <translation>密码</translation>
    </message>
</context>
<context>
    <name>rfidinfowidget</name>
    <message>
        <location filename="view/rfid/RFIDInfoWidget.cpp" line="34"/>
        <source>Substation Name (max length: 128): </source>
        <translation>站点名称（最长：128）：</translation>
    </message>
    <message>
        <location filename="view/rfid/RFIDInfoWidget.cpp" line="35"/>
        <source>Asset Name (max length: 128): </source>
        <translation>设备名称（最长：128）：</translation>
    </message>
    <message>
        <location filename="view/rfid/RFIDInfoWidget.cpp" line="36"/>
        <source>Asset Number (max length: 32): </source>
        <translation>设备编号（最长：32）：</translation>
    </message>
    <message>
        <location filename="view/rfid/RFIDInfoWidget.cpp" line="37"/>
        <source>Test Number (length: 12): </source>
        <translation>测试编号（长度：12）：</translation>
    </message>
    <message>
        <location filename="view/rfid/RFIDInfoWidget.cpp" line="38"/>
        <source>Voltage Level (kV): </source>
        <translation>电压等级（kV）：</translation>
    </message>
</context>
<context>
    <name>usbSetting</name>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="89"/>
        <source>Storage</source>
        <translation>存储卡</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="102"/>
        <source>Format Storage</source>
        <translation>格式化存储卡</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="112"/>
        <source>Shared Storage: </source>
        <translation>共有容量：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="127"/>
        <source>Used Storage: </source>
        <translation>已用容量：</translation>
    </message>
    <message>
        <location filename="view/systemsetview/systemsetabout/storge/usbsetting.cpp" line="141"/>
        <source>Available Storage: </source>
        <translation>可用容量：</translation>
    </message>
</context>
</TS>
