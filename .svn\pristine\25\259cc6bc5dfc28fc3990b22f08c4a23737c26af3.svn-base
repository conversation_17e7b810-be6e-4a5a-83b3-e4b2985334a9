#include <QStandardItemModel>
#include "checkboxtreeview.h"

CheckBoxTreeView::CheckBoxTreeView(QWidget *parent)
    : QTreeView(parent)
{
    QStandardItemModel* pModel = new QStandardItemModel(this);
    setModel(pModel);

    connect(pModel, SIGNAL(itemChanged(QStandardItem*)), this, SLOT(onTreeItemChanged(QStandardItem*)));
}

CheckBoxTreeView::~CheckBoxTreeView()
{
}

/************************************************
 * 函数名   : checkAllChild
 * 输入参数 :
   QStandardItem* pParentItem: 父item
   bool bCheck: 是否选中
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 设置是否选中所有子项
 ************************************************/
void CheckBoxTreeView::checkAllChild(QStandardItem* pParentItem, bool bCheck)
{
    if (NULL == pParentItem)
    {
        return;
    }

    int iRowCount = pParentItem->rowCount();
    for (int i = 0; i < iRowCount; ++i)
    {
        QStandardItem* pChildItem = pParentItem->child(i);
        checkAllChild(pChildItem, bCheck);
    }

    if (pParentItem->isCheckable())
    {
        pParentItem->setCheckState(bCheck ? Qt::Checked : Qt::Unchecked);
    }
}

/************************************************
 * 函数名   : checkChildChanged
 * 输入参数 :
   QStandardItem* pChildItem: 子item
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 根据子节点的改变，更改父节点的选择情况
 ************************************************/
void CheckBoxTreeView::checkChildChanged(QStandardItem* pChildItem)
{
    if (NULL == pChildItem)
    {
        return;
    }

    QStandardItem* pParentItem = pChildItem->parent();
    if (NULL == pParentItem)
    {
        return;
    }

    Qt::CheckState eSiblingState = checkSibling(pChildItem);
    if (Qt::PartiallyChecked == eSiblingState)
    {
        if (pParentItem->isCheckable() && pParentItem->isTristate())
        {
            pParentItem->setCheckState(Qt::PartiallyChecked);
        }
    }
    else if (Qt::Checked == eSiblingState)
    {
        if (pParentItem->isCheckable())
        {
            pParentItem->setCheckState(Qt::Checked);
        }
    }
    else
    {
        if (pParentItem->isCheckable())
        {
            pParentItem->setCheckState(Qt::Unchecked);
        }
    }
    checkChildChanged(pParentItem);
}

/************************************************
 * 函数名   : checkSibling
 * 输入参数 :
   QStandardItem* pItem: item
 * 输出参数 : NULL
 * 返回值   : Qt::CheckState
 * 功能     : 测量兄弟节点的情况，如果都选中返回Qt::Checked，都不选中Qt::Unchecked,不完全选中返回Qt::PartiallyChecked
 ************************************************/
Qt::CheckState CheckBoxTreeView::checkSibling(QStandardItem* pItem)
{
    // 先通过父节点获取兄弟节点
    QStandardItem* pParentItem = pItem->parent();
    if (NULL == pParentItem)
    {
        return pItem->checkState();
    }

    int iBrotherCount = pParentItem->rowCount();
    int iCheckedCount(0), iUnCheckedCount(0);
    Qt::CheckState eState;
    for (int i = 0; i < iBrotherCount; ++i)
    {
        QStandardItem* pSiblingItem = pParentItem->child(i);
        eState = pSiblingItem->checkState();
        if (Qt::PartiallyChecked == eState)
        {
            return Qt::PartiallyChecked;
        }
        else if (Qt::Unchecked == eState)
        {
            ++iUnCheckedCount;
        }
        else
        {
            ++iCheckedCount;
        }
        if (iCheckedCount > 0 && iUnCheckedCount > 0)
        {
            return Qt::PartiallyChecked;
        }
    }
    if (iUnCheckedCount > 0)
    {
        return Qt::Unchecked;
    }
    return Qt::Checked;
}

/************************************************
 * 函数名   : onTreeItemChanged
 * 输入参数 :
   QStandardItem* pItem: 改变的item
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : item改变槽函数
 ************************************************/
void CheckBoxTreeView::onTreeItemChanged(QStandardItem* pItem)
{
    QStandardItemModel* pModel = qobject_cast<QStandardItemModel*>(model());
    if (NULL == pModel || NULL == pItem)
    {
        return;
    }

    disconnect(pModel, SIGNAL(itemChanged(QStandardItem*)), this, SLOT(onTreeItemChanged(QStandardItem*)));

    if (pItem->isCheckable())
    {
        // 如果条目是存在复选框的，那么就进行下面的操作
        Qt::CheckState state = pItem->checkState(); // 获取当前的选择状态
        if (pItem->isTristate())
        {
            // 如果条目是三态的，说明可以对子目录进行全选和全不选的设置
            if (state != Qt::PartiallyChecked)
            {
                // 当前是选中状态，需要对其子项目进行全选
                checkAllChild(pItem, state == Qt::Checked ? true : false);
            }
        }
        else
        {
            //说明是两态的，两态会对父级的三态有影响
            //判断兄弟节点的情况
            checkChildChanged(pItem);
        }
    }

    connect(pModel, SIGNAL(itemChanged(QStandardItem*)), this, SLOT(onTreeItemChanged(QStandardItem*)));

    emit sigItemCheckStateChangeded();
}
