#include "tevspectrumprivatedefine.h"

namespace DataSpecificationNS
{
    TEVAmpExtInformationPrivate::TEVAmpExtInformationPrivate()
    {
        ucAmpUnit = 6;
        bSkipAmpUnit = false;
        bHasAmpUnit = false;

        fAmpLowerLimit = 0;
        bSkipAmpLowerLimit = false;
        bHasAmpLowerLimit = false;

        fAmpUpperLimit = 0;
        bSkipAmpUpperLimit = false;
        bHasAmpUpperLimit = false;
    }

    TEVAmpExtInformationPrivate& TEVAmpExtInformationPrivate::operator=(const TEVAmpExtInformationPrivate& stTEVAmpExtInformationPrivate)
    {
        this->ucAmpUnit = stTEVAmpExtInformationPrivate.ucAmpUnit;
        this->fAmpLowerLimit = stTEVAmpExtInformationPrivate.fAmpLowerLimit;
        this->fAmpUpperLimit = stTEVAmpExtInformationPrivate.fAmpUpperLimit;
        return *this;
    }

    bool TEVAmpExtInformationPrivate::operator==(const TEVAmpExtInformationPrivate& stTEVAmpExtInformationPrivate) const
    {
        if (!(this->ucAmpUnit == stTEVAmpExtInformationPrivate.ucAmpUnit)) return false;
        if (!qFuzzyCompare(this->fAmpLowerLimit, stTEVAmpExtInformationPrivate.fAmpLowerLimit)) return false;
        if (!qFuzzyCompare(this->fAmpUpperLimit, stTEVAmpExtInformationPrivate.fAmpUpperLimit)) return false;
        return true;
    }


    TEVAmpDataPrivate::TEVAmpDataPrivate()
    {
        fTEVAmpValue = 0;
        bSkipTEVAmpValue = false;
        bHasTEVAmpValue = false;

        fTEVMaxValue = 0;
        bSkipTEVMaxValue = false;
        bHasTEVMaxValue = false;

        iPulseCount = 0;
        bSkipPulseCount = false;
        bHasPulseCount = false;

        fSeverity = 0;
        bSkipSeverity = false;
        bHasSeverity = false;

        bSkipBGFileName = false;
        bHasBGFileName = false;

        ucDischargeSeverity = 0;
        bSkipDischargeSeverity = false;
        bHasDischargeSeverity = false;
    }

    TEVAmpDataPrivate& TEVAmpDataPrivate::operator=(const TEVAmpDataPrivate& stTEVAmpDataPrivate)
    {
        this->fTEVAmpValue = stTEVAmpDataPrivate.fTEVAmpValue;
        this->fTEVMaxValue = stTEVAmpDataPrivate.fTEVMaxValue;
        this->iPulseCount = stTEVAmpDataPrivate.iPulseCount;
        this->fSeverity = stTEVAmpDataPrivate.fSeverity;
        this->qstrBGFileName = stTEVAmpDataPrivate.qstrBGFileName;
        this->ucDischargeSeverity = stTEVAmpDataPrivate.ucDischargeSeverity;
        return *this;
    }

    bool TEVAmpDataPrivate::operator==(const TEVAmpDataPrivate& stTEVAmpDataPrivate) const
    {
        if (!qFuzzyCompare(this->fTEVAmpValue, stTEVAmpDataPrivate.fTEVAmpValue)) return false;
        if (!qFuzzyCompare(this->fTEVMaxValue, stTEVAmpDataPrivate.fTEVMaxValue)) return false;
        if (!(this->iPulseCount == stTEVAmpDataPrivate.iPulseCount)) return false;
        if (!qFuzzyCompare(this->fSeverity, stTEVAmpDataPrivate.fSeverity)) return false;
        if (!(this->qstrBGFileName == stTEVAmpDataPrivate.qstrBGFileName)) return false;
        if (!(this->ucDischargeSeverity == stTEVAmpDataPrivate.ucDischargeSeverity)) return false;
        return true;
    }
}
