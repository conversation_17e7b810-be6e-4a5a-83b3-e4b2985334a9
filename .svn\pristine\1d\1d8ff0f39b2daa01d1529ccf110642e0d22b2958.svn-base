#include "../common/datamaputils.h"
#include "ae/aephasedatamap.h"
#include "../common/xmldocument.h"
#include "datamapconfig.h"
DataMapUtils::DataMapUtils(QObject *parent) : QObject(parent)
{

}

DataMapUtils::~DataMapUtils()
{

}

bool DataMapUtils::isTypeValid(DataFileNS::SpectrumCode eCode)
{
    Q_UNUSED(eCode)
    return true;
}

/*************************************************
功能： 解析图谱头部信息
输入参数：
        baData -- 保存图谱数据的xml文本内容
输出参数：
        mapHead -- 解析出的头部内容
返回值：解析是否成功
*************************************************************/
bool DataMapUtils::parseMapHead( XMLDocument* pDoc, const QString &strRootTag, DataMap::MapHead & mapHead )
{
    bool bRet = true;
    pDoc->beginElement( strRootTag );
    pDoc->beginElement( XML_FILE_NODE_HEAD );

    mapHead.eCode = (DataFileNS::SpectrumCode)pDoc->value(MapHeadNode::TEXT_SPECTRUM_CODE).toInt();

    if( DataMapUtils::isTypeValid( mapHead.eCode ) )
    {
        mapHead.strMapGenerationTime = pDoc->value(MapHeadNode::TEXT_SPECTRUM_CREATE_TIME);
        mapHead.eMapProperty = (DataFileNS::MapProperty)pDoc->value(MapHeadNode::TEXT_SPECTRUM_PROPERTY).toInt();
        mapHead.strDeviceName = pDoc->value(MapHeadNode::TEXT_EQUIPMENT_NAME);
        mapHead.strDeviceNumber = pDoc->value(MapHeadNode::TEXT_EQUIPMENT_CODE);
        mapHead.strTestPointName = pDoc->value(MapHeadNode::TEXT_TEST_POINT_NAME);
        mapHead.strTestPointNumber = pDoc->value(MapHeadNode::TEXT_TEST_POINT_CODE);
        mapHead.ucTestChannelSign = pDoc->value(MapHeadNode::TEXT_CHANNEL_INDEX).toUInt();
        mapHead.eDataPrimitiveType = (DataFileNS::DataPrimitiveType)pDoc->value(MapHeadNode::TEXT_DATA_PRIMITIVE_TYPE).toInt();
        mapHead.ePDDefectLevel = (DataFileNS::PDDefectLevel)pDoc->value(MapHeadNode::TEXT_PD_DEFECT_LEVEL).toInt();
        mapHead.qstrPDSignalTypeInfos = pDoc->value(MapHeadNode::TEXT_PD_SIGNAL_TYPE_INFO);
        mapHead.qstrRemark = pDoc->value(MapHeadNode::TEXT_REMARK);
    }
    else
    {
        bRet = false;
    }
    pDoc->endElement();
    pDoc->endElement();
    return bRet;
}

/*************************************************
功能： 解析图谱头部信息
输入参数：
        bytes -- 保存图谱数据的二进制内容
输出参数：
        mapHead -- 解析出的头部内容
        hartLength -- 图谱长度
返回值：解析是否成功
*************************************************************/
bool DataMapUtils::parseMapHead(const QByteArray &bytes, DataMap::MapHead &mapHead, qint32 &chartLength)
{
    QDataStream in(bytes);
    in.setByteOrder(QDataStream::LittleEndian);

    quint8 chartCode;
    qint64 chartTime = 0;
    quint8 chartProperty = 0;
    qint16 channelFlag = 0;
    quint8 dataType = 0;

    DataFormat::getUInt8(in, chartCode);
    DataFormat::getInt32(in, chartLength);
    DataFormat::getInt64(in, chartTime);
    DataFormat::getUInt8(in, chartProperty);
    DataFormat::getUnicode(in, 128 / 2, mapHead.strDeviceName);
    DataFormat::getAscii(in, 32, mapHead.strDeviceNumber);
    DataFormat::getUnicode(in, 128 / 2, mapHead.strTestPointName);
    DataFormat::getAscii(in, 32, mapHead.strTestPointNumber);

    DataFormat::getInt16(in, channelFlag);
    DataFormat::getUInt8(in, dataType);

    mapHead.eTestLocation = DataFileNS::TEST_LOCATION_DEFAULT;
    mapHead.eCode = DataFileNS::SpectrumCode(chartCode);
    mapHead.strMapGenerationTime = QString::number(chartTime);
    mapHead.eMapProperty = DataFileNS::MapProperty(chartProperty);
    mapHead.ucTestChannelSign = channelFlag;
    mapHead.eDataPrimitiveType = DataFileNS::DataPrimitiveType(dataType);

    return in.status() == QDataStream::Ok;
}

/*************************************************
功能： 计算数据缓存的占用空间大小
输入参数：
        iDataPointNum -- 数据点个数
        eSaveDataType -- 数据保存类型
返回值：数据缓存的占用空间大小
*************************************************************/
int DataMapUtils::calcDataBufSize( quint32 iBufCnt, DataFileNS::DataPrimitiveType eDataPrimitiveType )
{
    switch( eDataPrimitiveType )
    {
    case DataFileNS::DATA_TYPE_FLOAT:
    {
        return iBufCnt*sizeof(float);
    }
    case DataFileNS::DATA_TYPE_DOUBLE:
    {
        return iBufCnt*sizeof(double);
    }
    case DataFileNS::DATA_TYPE_INT16:
    {
        return iBufCnt*sizeof(qint16);
    }
    case DataFileNS::DATA_TYPE_UINT16:
    {
        return iBufCnt*sizeof(quint16);
    }
    case DataFileNS::DATA_TYPE_UINT8:
    {
        return iBufCnt*sizeof(quint8);
    }
    case DataFileNS::DATA_TYPE_INT8:
    {
        return iBufCnt*sizeof(qint8);
    }
    case DataFileNS::DATA_TYPE_INT32:
    {
        return iBufCnt*sizeof(qint32);
    }
    case DataFileNS::DATA_TYPE_INT64:
    {
        return iBufCnt*sizeof(qint64);
    }
    default:
        break;
    }

    return 0;
}

/*************************************************
功能： 创建的数据缓存的空间
输入参数：
        iBufCnt -- 数据个数
        eSaveDataType -- 数据保存类型
输出参数：
        iBufSize -- 创建数据的大小（count * elemsize）
返回值：创建的数据缓存的地址
*************************************************************/
void *DataMapUtils::createDataBuf( quint32 iBufCnt,
                                   DataFileNS::DataPrimitiveType eDataPrimitiveType, unsigned long & iBufSize )
{
    void *pData = NULL;

    if( iBufCnt > 0 )
    {
        switch( eDataPrimitiveType )
        {
        case DataFileNS::DATA_TYPE_FLOAT:
        {
            float *pfData = new float[iBufCnt];
            pData = (void*)pfData;
            iBufSize = sizeof(float) *iBufCnt;
        }
            break;
        case DataFileNS::DATA_TYPE_DOUBLE:
        {
            double *pdData = new double[iBufCnt];
            pData = (void*)pdData;
            iBufSize = sizeof(double) *iBufCnt;
        }
            break;

        case DataFileNS::DATA_TYPE_INT16:
        {
            short *psData = new short[iBufCnt];
            pData = (void *)psData;
            iBufSize = sizeof(short) * iBufCnt;
        }
            break;
        case DataFileNS::DATA_TYPE_UINT16:
        {
            unsigned short *psData = new unsigned short[iBufCnt];
            pData = (void *)psData;
            iBufSize = sizeof(unsigned short) * iBufCnt;
        }
            break;
        case DataFileNS::DATA_TYPE_UINT8:
        {
            quint8 *psData = new quint8[iBufCnt];
            pData = (void *)psData;
            iBufSize = sizeof(quint8) * iBufCnt;
        }
            break;
        case DataFileNS::DATA_TYPE_INT8:
        {
            qint8 *psData = new qint8[iBufCnt];
            pData = (void *)psData;
            iBufSize = sizeof(qint8) * iBufCnt;
        }
            break;
        case DataFileNS::DATA_TYPE_INT32:
        {
            qint32 *psData = new qint32[iBufCnt];
            pData = (void *)psData;
            iBufSize = sizeof(qint32) * iBufCnt;
        }
            break;
        case DataFileNS::DATA_TYPE_INT64:
        {
            qint64 *psData = new qint64[iBufCnt];
            pData = (void *)psData;
            iBufSize = sizeof(qint64) * iBufCnt;
        }
            break;
        case DataFileNS::DATA_TYPE_UNUSED:
        {
            qWarning()<<"DataMapUtils::createDataBuf: data type is unused! ";
        }
            break;
        }
    }

    return pData;
}

/*************************************************
功能： 释放数据缓存的空间
输入参数：
        pBuf -- 数据缓存的地址
        eSaveDataType -- 数据保存类型
返回值：是否释放成功
*************************************************************/
bool DataMapUtils::deleteDataBuf( void **pBuf)
{
    bool bSuccess = true;
    if( ( NULL != pBuf) && ( NULL != *pBuf ) )
    {
        delete [] *pBuf;
        *pBuf = NULL;
    }
    else
    {
        bSuccess = false;
    }
    return bSuccess;
}

void DataMapUtils::saveDataBinary(QByteArray& baPackage, DataFileNS::DataPrimitiveType eDataPrimitiveType, void* pData, int iDataSize)
{
    QDataStream out(&baPackage, QIODevice::ReadWrite);
    out.setByteOrder(QDataStream::LittleEndian);
    out.setFloatingPointPrecision(QDataStream::SinglePrecision);
    out.device()->seek(out.device()->size());

    if (NULL != pData)
    {
        switch (eDataPrimitiveType)
        {
        case DataFileNS::DATA_TYPE_INT8:
        {
            qint8 * cData = (qint8 *)pData;
            for (int i = 0; i < iDataSize; i++)
            {
                out << cData[i];
            }
        }
        break;
        case DataFileNS::DATA_TYPE_UINT8:
        {
            quint8 * ucData = (quint8 *)pData;
            for (int i = 0; i < iDataSize; i++)
            {
                out << ucData[i];
            }
        }
        break;
        case DataFileNS::DATA_TYPE_INT16:
        {
            qint16 * sData = (qint16 *)pData;
            for (int i = 0; i < iDataSize; i++)
            {
                out << sData[i];
            }
        }
        break;
        case DataFileNS::DATA_TYPE_INT32:
        {
            qint32 * iData = (qint32 *)pData;
            for (int i = 0; i < iDataSize; i++)
            {
                out << iData[i];
            }
        }
        break;
        case DataFileNS::DATA_TYPE_INT64:
        {
            qint64 * llData = (qint64 *)pData;
            for (int i = 0; i < iDataSize; i++)
            {
                out << llData[i];
            }
        }
        break;
        case DataFileNS::DATA_TYPE_FLOAT:
        {
            QDataStream::FloatingPointPrecision flag = out.floatingPointPrecision();
            out.setFloatingPointPrecision(QDataStream::SinglePrecision);
            float * fData = (float *)pData;
            for (int i = 0; i < iDataSize; i++)
            {
                out << fData[i];
            }
            out.setFloatingPointPrecision(flag);
        }
        break;
        case DataFileNS::DATA_TYPE_DOUBLE:
        {
            QDataStream::FloatingPointPrecision flag = out.floatingPointPrecision();
            out.setFloatingPointPrecision(QDataStream::DoublePrecision);
            double * dData = (double *)pData;
            for (int i = 0; i < iDataSize; i++)
            {
                out << dData[i];
            }
            out.setFloatingPointPrecision(flag);
        }
        break;
        case DataFileNS::DATA_TYPE_UINT16:
        {
            quint16 * usData = (quint16 *)pData;
            for (int i = 0; i < iDataSize; i++)
            {
                out << usData[i];
            }
        }
        break;
        default:
            break;
        }
    }
}
