#ifndef INFRAREDRADIOCONTROLBUTTON_H
#define INFRAREDRADIOCONTROLBUTTON_H

#include "infraredcontrolbutton.h"
#include "widgetglobal.h"

class WIDGET_EXPORT InfraredRadioControlButton : public InfraredControlButton
{
    Q_OBJECT
public:
    InfraredRadioControlButton(const QString& strTitle, const QStringList& listOptions = QStringList(), QWidget* parent = 0);


    /*************************************************
    功能： 设置字符串数组列表
    输入参数:
        listOptions -- 选项数组
    *************************************************************/
    void setOptionList( const QStringList& listOptions );

    /*************************************************
    功能： 设置字符串数组列表
    输入参数:
        pchContext -- 国际化用的context域
        pstrOptions -- 选项数组
        iCount -- 选项个数
    *************************************************************/
    void setOptionList( const char* pchContext, const char* const* pstrOptions, int iCount );

    void setValue(int iValue);

    /*************************************************
    功能： 处理鼠标释放事件
    输入参数: pEvent -- 鼠标释放事件
    *************************************************************/
    virtual void processMouseReleaseEvent(QMouseEvent* pEvent);

protected:
    /*************************************************
    功能： 从值获取显示的值
    *************************************************************/
    QString textFromValue()const;
private:
    QStringList m_listOptions;
    int m_iIndex;
};

#endif // INFRAREDRADIOCONTROLBUTTON_H
