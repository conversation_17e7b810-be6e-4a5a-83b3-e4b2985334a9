﻿#include "systemsetservice.h"
#include <stdlib.h>
#include <QThread>
#include <QMutex>
#include <QMutexLocker>
#include <QDateTime>
#include <QFileInfo>
#include <QFileInfoList>
#include <QCryptographicHash>
#include "gps.h"
#include "systemsettings.h"
#include "wifi.h"
#include "softkeyboard.h"
#include "config/ConfigManager.h"
#include "appconfig.h"
#include "model/HCStatus.h"
#include "Module.h"
#include "dataSave/DataFileInfos.h"
#include "update/localcomm.h"
#include "ca/catcpserver.h"
#include "ae/AEConfig.h"
#include "timezonemanager/timezonemanager.h"
#include "fileoper/fileoperutil.h"
#include "iniconfig/iniconfig.h"
#include "crcutil/crcutil.h"
#include "activemanager/activemanager.h"
#include "functionconfigmanager/funccfgsrvmanager.h"
#include "vpnaccessmanager/vpnaccessmanager.h"
#include "global_log.h"
#include "log/log.h"

#ifdef  Q_PROCESSOR_ARM
#include <sys/vfs.h>
#include <unistd.h>
#include <mobilenetwork/gprs.h>
#include <mobilenetwork/ConfigParse.h>
#include <TemHumAPI.h>
#endif


#define READ_POWER_FAILED_UP_LIMIT 3
#define SAFE_BAT   0.35                                 // 安全电量百分比
#define LOWEST_BAT   0.025
#define RECYCLE_TIME 24*3600
#define OPER_SUCC   0
#define OPER_FAIL   -1
#define WIFI_CONN_SUCC  1
#define ZERO_VAL    1e-6
#define INSTALL_PACKAGE_DIR "/media/data/upgrade"
#define OUTER_DIR "/media/data"
#define TMP_FILE_DIR "/media/data/SavedData/transfer/"

const char* const TIME_FORMAT = "yyyy-MM-dd hh:mm:ss";   //时间格式
static unsigned char SHA1_KEY[44] =
{
    0X14,0X0E,0X08,0X1C,
    0X0D,0X1F,0X00,0X00,
    0X01,0X00,0X00,0X00,
    0XFC,0X95,0X32,0X24,
    0XA7,0XCE,0X32,0X08,
    0XF4,0XB1,0X33,0X17,
    0XCC,0X36,0X58,0X0B,
    0X2A,0X2D,0XC3,0X70,
    0X20,0X21,0X22,0X23,
    0X24,0X25,0X26,0X27,
    0X28,0X29,0X2A,0X2B
};  //平台约定密码

const UINT16 KB_PER_MB = 1024;
const UINT16 B_PER_KB = 1024;
//static QMutex g_stMtSysObj;



/****************************
功能： 构造函数
*****************************/
SystemSetService::SystemSetService( )
{
    m_pbyDevSN = NULL;
    m_eHotSpotOption = SystemSet::HOT_CLOSED;
    m_eUsbMode = SystemSet::APP_USB_GADGET_NET;
    m_eDataSpecificationVersion = DataSpecificationNS::DefaultDataSpecificationVersion;

    LocalComm::instance()->openComm(SERVER_NAME);

    initFirmwareInfo(m_stFirmwareInfo);

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    m_iSysFreq = pConfig->value(APPConfig::KEY_SYS_FREQ).toInt();
    m_eRTDiagSwitch = static_cast<SystemSet::RealtimeDiagnosisSwitch>(pConfig->value(APPConfig::KEY_DIAGNOSIS_REALTIME_SWITCH).toInt());
    pConfig->endGroup();

    qRegisterMetaType<SystemSet::USBState>("SystemSet::USBState");
    connect( this, SIGNAL(sigStartFwUpdate(quint8*, quint8*)), this, SLOT(onStartFwUpdate(quint8*, quint8*)) );
    connect( this, SIGNAL(sigCreateSubService()),this, SLOT(onCreateSubService()) );
    connect( this, SIGNAL(sigSetUSBState(SystemSet::USBState)),
             this, SLOT(onSetUSBState(SystemSet::USBState)));

    connect(TimezoneManager::instance(), SIGNAL(sigTimezoneChanged()), this, SLOT(onTimezoneChanged()));

    VpnAccessManager::instance();

    m_pThread = new QThread( this );
    moveToThread( m_pThread );
    m_pThread->start();

    emit sigCreateSubService();
}

/****************************
功能： 析构函数
*****************************/
SystemSetService::~SystemSetService()
{
    IniConfig::writeFirmwareInfo(m_stFirmwareInfo);

    m_pbyDevSN = NULL;

    if( m_pThread != NULL )
    {
        if(m_pThread->isRunning())
        {
            m_pThread->quit();
            m_pThread->wait();
        }
    }
    APP_CHECK_FREE( m_pBluetoothService )
}

/****************************
功能： 模块单例
*****************************/
SystemSetService* SystemSetService::instance()
{
    //QMutexLocker stLocker(&g_stMtSysObj);
    static SystemSetService service;
    return &service;
}

/************************************************
 * 函数名   : zigbeeVersion(WLDeviceVer *pstWLDeviceVer)
 * 输入参数 : NULL
 * 输出参数 : pstWLDeviceVer--存放版本信息的数据指针
 * 返回值   : 操作是否成功
 * 功能     : 获取Zigbee版本信息
 ************************************************/
bool SystemSetService::zigbeeVersion(WLDeviceVer *pstWLDeviceVer)
{
    APP_CHECK_RETURN_VAL(pstWLDeviceVer, false);
    INT32 iSuccess = -1;
#ifdef Q_PROCESSOR_ARM
    iSuccess = get_wireless_version(pstWLDeviceVer, RECEIVER, NULL);
#endif
    return (0 == iSuccess) ? true : false;
}

/************************************************
 * 函数名   : stm32Version(QString &strVersion)
 * 输入参数 : NULL
 * 输出参数 : strVersion---返回的STM32版本号
 * 返回值   : 操作是否成功
 * 功能     : 获取STM32版本号
 ************************************************/
void SystemSetService::stm32Version(QString &strVersion)
{
#ifdef Q_PROCESSOR_ARM
    const INT8 *pcStmVersion = stm32_version();
    QByteArray qbaStmVersion = QByteArray((const char*)pcStmVersion);
    strVersion = QString(qbaStmVersion);
#endif

    return;
}

/*************************************************
功能： 设置gprs的apn信息
入参：strUser -- 用户名
     strPWD -- 密码
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::setMobileAccount(const QString &strUser, const QString &strPWD)
{
#ifdef Q_PROCESSOR_ARM
    int iRet = set_ppp_account(strUser.toLatin1().data(), strPWD.toLatin1().data());
    dbg_info("iRet is %d.", iRet);
    return ((0 == iRet) ? true : false);
#else
    return true;
#endif
}

/*************************************************
功能： 设置gprs的apn信息
入参：strAPN -- apn信息
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::setMobileAPN(const QString &strAPN)
{
#ifdef Q_PROCESSOR_ARM
    int iRet = set_ppp_apn(strAPN.toLatin1().data());
    dbg_info("iRet is %d.", iRet);
    return ((0 == iRet) ? true : false);
#else
    return true;
#endif
}

/*************************************************
功能： 重新初始化gprs服务
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::reinitMobileService()
{
#ifdef Q_PROCESSOR_ARM
    int iRet = gprs_reinit();
    dbg_info("iRet is %d.", iRet);
    return ((0 == iRet) ? true : false);
#else
    return true;
#endif
}

/*************************************************
功能： 获取gprs的apn信息
出参：strAPN -- apn信息
返回值：void
*************************************************/
void SystemSetService::getMobileAPN(QString &strAPN)
{
#ifdef Q_PROCESSOR_ARM
    char szAPN[128] = {0};
    int iRet = get_ppp_apn(szAPN);
    dbg_info("iRet is %d.", iRet);
    if(0 == iRet)
    {
        strAPN = QString(szAPN);
    }
#endif
    return;
}

/*************************************************
功能： 获取gprs的用户名和密码
出参：strUser -- 用户名
     strPWD -- 密码
返回值：void
*************************************************/
void SystemSetService::getMobileAccount(QString &strUser, QString &strPWD)
{
#ifdef Q_PROCESSOR_ARM
    char szUser[128] = {0};
    char szPWD[128] = {0};
    int iRet =  get_ppp_account(szUser, szPWD);
    dbg_info("iRet is %d.", iRet);
    if(0 == iRet)
    {
        strUser = QString(szUser);
        strPWD = QString(szPWD);
    }
#endif
    return;
}

/*************************************************
功能： 设置系统日期
入参：strDate -- 日期
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::setSystemDate( const QString& strDate )
{
    bool bSucceed = true;
#ifdef Q_PROCESSOR_ARM
    INT32 iRet = set_system_date((INT8*)strDate.toLatin1().constData());
    if(0 != iRet)
    {
        bSucceed = false;
        logError("set_system_date fail!");
    }
#endif
    return bSucceed;
}

/*************************************************
功能： 获得设备序列号
返回值：设备序列号
*************************************************/
const UINT8 *SystemSetService::deviceIdNum( void )
{
    if(NULL == m_pbyDevSN)
    {
#ifdef Q_PROCESSOR_ARM
        m_pbyDevSN = device_id_num();
        if(m_pbyDevSN)
        {
            logInfo("get dev serial number success.");
        }
        else
        {
            logError("get dev serial number fail.");
        }
#endif
    }

    return m_pbyDevSN;
}

/*************************************************
功能： 获得设备序列号
返回值：设备序列号
*************************************************/
QString SystemSetService::getDevSerialNum(void)
{
    QString qstrDevSN;
    const UINT8 *pucDeviceSN = NULL;
    pucDeviceSN = deviceIdNum();
    if(pucDeviceSN)
    {
#ifdef _DEFINE_Z200_HW_
        qstrDevSN = QString("%1%2%3%4%5%6%7%8").arg(pucDeviceSN[0]).arg(pucDeviceSN[1]).arg(pucDeviceSN[2]).arg(pucDeviceSN[3]).arg(pucDeviceSN[4]).arg(pucDeviceSN[5]).arg(pucDeviceSN[6]).arg(pucDeviceSN[7]);
#else
        QByteArray qbaSnBytes;
        qbaSnBytes.reserve(8);
        for(int i = 0; i < 8; ++i)
        {
            qbaSnBytes[i] = pucDeviceSN[i];
        }
        qstrDevSN = qbaSnBytes.toHex().toUpper();
#endif
    }

    return qstrDevSN;
}

/*************************************************
功能： 获得设备应用软件版本号
返回值：设备应用软件版本号
*************************************************/
QString SystemSetService::getDevSoftwareVersion(void)
{
    QString qstrSoftwareVersion = curSoftwareVersion();
    qstrSoftwareVersion = QString("V%1").arg(qstrSoftwareVersion);
    return qstrSoftwareVersion;
}

/*************************************************
功能： 获得设备应用软件版本号
返回值：设备应用软件版本号
*************************************************/
QString SystemSetService::curSoftwareVersion()
{
    return LocalComm::instance()->localSoftwareVersion();
}

/*************************************************
功能： 设置背光
入参： eOption -- 背光选项
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::setBackLight( SystemSet::BLOption eOption )
{
    bool bIsSuccess = false;
    INT32 iResult = -1;
#ifdef Q_PROCESSOR_ARM
    switch( eOption )
    {
    case SystemSet::BL_LEVEL_1:
    {
        iResult = set_backlight_state(BL_LV1);
    }
        break;
    case SystemSet::BL_LEVEL_2:
    {
        iResult = set_backlight_state(BL_LV2);
    }
        break;
    case SystemSet::BL_LEVEL_3:
    {
        iResult = set_backlight_state(BL_LV3);
    }
        break;
    default:
    {
        dbg_info("[backLightChange] invalid backlight state.");
    }
        break;
    }
#else
    iResult = 0;
#endif
    if(iResult == 0)
    {
        ConfigInstance *pConfig = ConfigManager::instance()->config();
        pConfig->beginGroup( Module::GROUP_APP );
        pConfig->setValue( eOption,APPConfig::KEY_BG_LIGHT  );
        pConfig->endGroup();
        bIsSuccess = true;
    }
    else
    {
        dbg_info("[backLightChange] setBL failed.");
    }
    return bIsSuccess;
}

/*************************************************
功能： 设置USB状态
入参： 开或关
*************************************************/
void SystemSetService::setUSBState(SystemSet::USBState eOption, bool bDirectCall)
{
    if(bDirectCall)
    {
        onSetUSBState(eOption);
    }
    else
    {
        emit sigSetUSBState(eOption);
    }

    return;
}

/*************************************************
功能： 设置USB模式
入参： 开或关
返回值：true -- 成功
       false -- 失败
*************************************************/
bool SystemSetService::setUSBMode(SystemSet::AppUsbMode eOption)
{
    bool bRet = true;
#ifdef Q_PROCESSOR_ARM
    int iRet = set_usb_mode((UsbMode)eOption);
    bRet = ((0 == iRet) ? true : false);
#endif

    if(bRet)
    {
        m_eUsbMode = eOption;
    }

    logInfo(QString("set usb mode: %1, ret: %2.").arg(eOption).arg(bRet));

    return bRet;
}

/*************************************************
功能： 获取USB模式
返回值：USB的模式值
*************************************************/
SystemSet::AppUsbMode SystemSetService::getUSBMode()
{
    return m_eUsbMode;
}

/*************************************************
功能： 设置usb状态
*************************************************/
void SystemSetService::onSetUSBState( SystemSet::USBState eOption )
{
    bool bIsSuccess = false;
#ifdef Q_PROCESSOR_ARM
    switch( eOption )
    {
    case SystemSet::USB_STATE_NET:
    {
        INT32 iSuccess = set_usb_mode( USB_GADGET_NET );
        ConfigInstance *pConfig = ConfigManager::instance()->config();
        pConfig->beginGroup( Module::GROUP_APP );
        if(0 == iSuccess)
        {
            qDebug() << "SystemSetService::onSetUSBState USB_GADGET_NET error code is:"
                     << iSuccess;
            pConfig->setValue( SystemSet::USB_STATE_NET,APPConfig::KEY_USB_STATE_SWITCH );
            m_eUsbMode = SystemSet::APP_USB_GADGET_NET;
            bIsSuccess = true;
            emit sigStorageMounted();
        }
        else
        {
            //pConfig->setValue( SystemSet::USB_STATE_STORGE,APPConfig::KEY_USB_STATE_SWITCH );
        }

        pConfig->endGroup();
    }
        break;
    case SystemSet::USB_STATE_STORGE:
    {
        INT32 iSuccess = set_usb_mode( USB_GADGET_MASS_STORAGE );
        ConfigInstance *pConfig = ConfigManager::instance()->config();
        pConfig->beginGroup( Module::GROUP_APP );
        qDebug() << "SystemSetService::onSetUSBState USB_GADGET_MASS_STORAGE error code is:"
                 << iSuccess;
        if(0 == iSuccess)
        {
            qDebug() << "SystemSetService::onSetUSBState USB_GADGET_MASS_STORAGE error code is:"
                     << iSuccess;
            pConfig->setValue( SystemSet::USB_STATE_STORGE,APPConfig::KEY_USB_STATE_SWITCH );
            m_eUsbMode = SystemSet::APP_USB_GADGET_MASS_STORAGE;
            bIsSuccess = true;
        }
        else
        {
            //pConfig->setValue( SystemSet::USB_STATE_NET,APPConfig::KEY_USB_STATE_SWITCH );
        }

        pConfig->endGroup();
    }
        break;
    default:
        break;
    }
#else
    Q_UNUSED( eOption )
#endif

    emit sigSetUSBResult( eOption, bIsSuccess);

    return;
}

/*************************************************
功能： 设置频率
入参： eOption -- 频率选项
*************************************************/
void SystemSetService::setFrequency( SystemSet::FreqOption eOption )
{
#ifdef Q_PROCESSOR_ARM
    Frequency eFrequency = (SystemSet::FREQ_60_HZ == eOption) ? FREQ_60 : FREQ_50;
    m_iSysFreq = eFrequency;
    INT32 iRet = set_frequency(eFrequency);
    if(0 != iRet)
    {
        logError(QString("set grid freq: %1 failed, ret: %2.").arg(eFrequency).arg(iRet).toLatin1().data());
        return;
    }
#endif

    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    Module::Frequency eFreq = Module::FREQ_DEFAULT;
    switch(eOption)
    {
    case SystemSet::FREQ_50_HZ:
    {
        eFreq = Module::FREQ_50HZ;
        break;
    }
    case SystemSet::FREQ_60_HZ:
    {
        eFreq = Module::FREQ_60HZ;
        break;
    }
    default:
        break;
    }
    pConfig->setValue(eFreq, APPConfig::KEY_SYS_FREQ);
    pConfig->endGroup();

    m_iSysFreq = eFreq;

    //modify ae freq
    pConfig->beginGroup(Module::GROUP_AE);
    pConfig->setValue(eFreq, AE::KEY_FREQ_COMPONENT, AE::GROUP_AE_AMPLITUDE);
    pConfig->endGroup();

    return;
}

/*************************************************
功能： 设置频率
返回值： 系统电网频率
*************************************************/
int SystemSetService::getFrequency()
{
    return m_iSysFreq;
}

/*************************************************
功能： 设置语言
入参： eOption -- 语言选项
*************************************************/
void SystemSetService::setLanguage( SystemSet::LanuageOption eOption )
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup( Module::GROUP_APP );
    pConfig->setValue( eOption,APPConfig::KEY_SYS_LANGAUGE );
    pConfig->endGroup();

    qDebug() << "SystemSetService::setLanguage, language sys: " << eOption << endl;

    return;
}

/*************************************************
功能： 获取语言
入参： void
返回值：语言值
*************************************************/
int SystemSetService::getLanguage()
{
    int iLangVal = 0;
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    iLangVal =  pConfig->value(APPConfig::KEY_SYS_LANGAUGE).toInt();
    pConfig->endGroup();

    return iLangVal;
}

/*************************************************
功能： 4g连接状态
返回值：true -- 已连接
      false -- 未连接
*************************************************/
bool SystemSetService::isFourGConnected( void )
{
    bool bIsConnected = false;
#ifdef Q_PROCESSOR_ARM
    bIsConnected = (0 == gprs_status() ? true : false);
#endif
    return bIsConnected;
}

/*************************************************
功能： 设置4G选项
入参： 开或关
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::setFourGOption( SystemSet::FourGOption eOption )
{
    bool bRet = false;
#ifdef Q_PROCESSOR_ARM
    INT32 iSuccess = OPER_FAIL;
    switch( eOption )
    {
    case SystemSet::FOUR_G_OPEN:
    {
        iSuccess = gprs_init();
        if(OPER_SUCC == iSuccess)
        {
            bRet = true;
            logInfo("gprs_init success.");
            /*if(isFourGConnected())
            {
                bRet = true;
                logInfo("gprs connected.");
            }
            else
            {
                logInfo("gprs disconnected.");
            }*/
        }
        else
        {
            logInfo("gprs_init fail.");
        }
        break;
    }
    case SystemSet::FOUR_G_CLOSED:
    {
        iSuccess = gprs_exit();
        if(OPER_SUCC == iSuccess)
        {
            bRet = true;
            logInfo("gprs_exit success.");
        }
        else
        {
            logInfo("gprs_exit fail.");
        }
        break;
    }
    default:
        break;
    }
#endif
    return bRet;
}

/*************************************************
功能： 设置热点选项
入参： 开或关
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::setHotSpotOption( SystemSet::HotSpotOption eOption )
{
    bool bIsSuccess = false;
#ifdef Q_PROCESSOR_ARM
    switch( eOption )
    {
    case SystemSet::HOT_OPEN:
    {
        closeWifiConnection();
        if(OPER_SUCC == wifi_init(WIFI_AP_MODE))
        {
            // 修改wifi开关状态，打开ap时默认关闭wifi
            //ConfigInstance *pConfig = ConfigManager::instance()->config();
            //pConfig->beginGroup( Module::GROUP_APP );
            //pConfig->setValue( SystemSet::HOT_OPEN,APPConfig::KEY_HOT_SWITCH );
            //pConfig->endGroup();
            CATcpServer::instance()->start();
            bIsSuccess = true;
            qDebug() << "SystemSetService::setHotSpotOption, wifi ap mode init success." << endl;
        }
        else
        {
            qDebug() << "SystemSetService::setHotSpotOption, wifi ap mode init fail." << endl;
        }

        break;
    }
    case SystemSet::HOT_CLOSED:
    {
        if(OPER_SUCC == wifi_exit(WIFI_AP_MODE))
        {
            //ConfigInstance *pConfig = ConfigManager::instance()->config();
            //pConfig->beginGroup( Module::GROUP_APP );
            //pConfig->setValue( SystemSet::HOT_CLOSED,APPConfig::KEY_HOT_SWITCH );
            //pConfig->endGroup();
            bIsSuccess = true;
            qDebug() << "SystemSetService::setHotSpotOption, wifi ap mode exit success." << endl;
            CATcpServer::instance()->stop();
        }
        else
        {
            qDebug() << "SystemSetService::setHotSpotOption, wifi ap mode exit fail." << endl;
        }

        break;
    }
    default:
    {
        break;
    }

    }
#endif

    if( bIsSuccess )
    {
        setHotSpotOptionVal(eOption);
    }

    return bIsSuccess;
}

/*************************************************
功能： 获取蓝牙设置服务实例
返回值： 蓝牙设置服务实例
*************************************************/
BluetoothSetService* SystemSetService::bluetoothSetService()
{
    return m_pBluetoothService;
}

/*************************************************
功能： 断电Z200设备
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::shutDownSystem( void )
{
    bool bSuccess = false;
#ifdef Q_PROCESSOR_ARM
    if(0 == shutdown_system())
    {
        bSuccess = true;
        logInfo("shut down system succeeded.");
    }
    else
    {
        logError("shut down system failed.");
    }
#endif
    return bSuccess;
}

/*************************************************
功能： 开关4G电源
入参：true -- 打开
     false -- 关闭
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::switchFourGPower( bool bOn )
{
    bool bIsSuccess = false;
#ifdef Q_PROCESSOR_ARM
    if( bOn )
    {
        int iResult = power_on_off_4g( 1 );
        if( iResult == 0 )
        {
            bIsSuccess = true;
        }
        else
        {
            qWarning("open 4G power failed");
        }
    }
    else
    {
        int iResult = power_on_off_4g( 0 );
        if( iResult == 0 )
        {
            bIsSuccess = true;
        }
        else
        {
            qWarning("close 4G power failed");
        }
    }
#endif
    return bIsSuccess;
}

/*************************************************
功能： 唤醒or休眠4G
入参：true -- 唤醒
     false -- 休眠
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::wakeSleepFourG( bool bWake )
{
    bool bIsSuccess = false;
#ifdef Q_PROCESSOR_ARM
    if( bWake )
    {
        int iResult = wake_sleep_4g( 1 );
        if( iResult == 0 )
        {
            bIsSuccess = true;
        }
        else
        {
            qWarning("wake 4G failed");
        }
    }
    else
    {
        int iResult = wake_sleep_4g( 0 );
        if( iResult == 0 )
        {
            bIsSuccess = true;
        }
        else
        {
            qWarning("sleep 4G failed");
        }
    }
#endif
    return bIsSuccess;
}

/*************************************************
功能： poe上电控制
入参：true -- 开启
     false -- 关闭
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::poePowerCtrl( bool bOn )
{
    bool bIsSuccess = false;
#ifdef Q_PROCESSOR_ARM
    if( bOn )
    {
        int iResult = poe_power_ctrl( 1 );
        if( iResult == 0 )
        {
            bIsSuccess = true;
        }
        else
        {
            qWarning("open poe power failed");
        }
    }
    else
    {
        int iResult = poe_power_ctrl( 0 );
        if( iResult == 0 )
        {
            bIsSuccess = true;
        }
        else
        {
            qWarning("close poe power failed");
        }
    }
#endif
    return bIsSuccess;
}

/*************************************************
功能： 关闭软键盘
*************************************************/
void SystemSetService::closeKeyBoard( void )
{
#ifdef Q_PROCESSOR_ARM
    SoftKeyBoard::close();
#endif
}

/*************************************************
功能： 初始化串口
*************************************************/
void SystemSetService::initSerialPort( void )
{
#ifdef Q_PROCESSOR_ARM
    init_uhfhfctaetev();
#endif
}

/*************************************************
功能： 关闭串口
*************************************************/
void SystemSetService::exitSerialPort( void )
{
#ifdef Q_PROCESSOR_ARM
    exit_uhfhfctaetev();
#endif
}

/*************************************************
功能： 初始化软键盘
*************************************************/
void SystemSetService::initKeyBoard( void )
{
    SystemSet::KeyboardMode eMode = getKeyBoardMode();
    SoftKeyBoard::KeyBoardType eKBType = (SystemSet::KBM_FULL == eMode) ? SoftKeyBoard::FULL_KEYBOARD : SoftKeyBoard::SUDOKU_KEYBOARD;
#ifdef Q_PROCESSOR_ARM
    SoftKeyBoard::initKeyBoard(eKBType);
    SoftKeyBoard::setAttribute(KEYBOARD_MODALITY_PROPERTY, SoftKeyBoard::NON_MODAL);
#else
    Q_UNUSED(eKBType);
#endif
    return;
}

/*************************************************
功能： stm程序是否运行在app区
返回值：true -- 是
      false -- 否
*************************************************/
bool SystemSetService::isStmInApp( void )
{
    bool bIsSuccess = false;
#ifdef Q_PROCESSOR_ARM
    if( get_stm32_inapp() == 0 )
    {
        bIsSuccess = true;
    }
    else
    {
        bIsSuccess = false;
    }
#endif
    return bIsSuccess;
}

/*************************************************
功能： stm跳转至app区
返回值：true -- 成功
      false -- 失败
*************************************************/
bool SystemSetService::stmGotoApp( void )
{
    bool bIsSuccess = false;
#ifdef Q_PROCESSOR_ARM
    if( stm32_goto_app() == 0 )
    {
        bIsSuccess = true;
    }
    else
    {
        bIsSuccess = false;
    }
#endif
    return bIsSuccess;
}


/****************************
输入参数:fPercent -- 电量比例
返回值：float -- 电量百分比
功能： 将真实电量映射成显示电量
*****************************/
float SystemSetService::batteryMappingFromRawData( float fPercent )
{
    float fBattery = LOWEST_BAT;

    if(fPercent <= SAFE_BAT)
    {
        fBattery = LOWEST_BAT;
    }
    else
    {
        fBattery = (fPercent - 1) * (1 - LOWEST_BAT) / (1 - SAFE_BAT) + 1;
    }

    return fBattery;
}

/*************************************************
功能： 获取电量百分比
返回值：电量百分比，负数表示通讯问题，不进行处理
*************************************************/
float SystemSetService::batteryPercent( void )
{
    float fBattery = 0.0;
    int iBattery = getPower();
#ifndef NEW_HARDWARE_BATTERY_ISSUE
    //该计算电量公式由上海同事提供，仅此处使用(电压百分比转为电量百分比)
    fBattery = (static_cast<float>(iBattery) * 0.042 - 3.3) / 0.9;

    //将实际电量映射成显示电量
    fBattery = batteryMappingFromRawData(fBattery);

    //异常电量处理
    //fBattery = (fBattery < 0.0f) ? 0.0f : fBattery;
    fBattery = (fBattery > 1.0f) ? 1.0f : fBattery;
    fBattery *= 100.0f; // 转换成百分比
#else
    //iBattery = (0 > iBattery) ? 0 : iBattery;
    iBattery = (100 < iBattery) ? 100 : iBattery;
    fBattery = static_cast<float>(iBattery * 1.0f);
#endif

    /*
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    int iLowerBatteryVal = pConfig->value(APPConfig::KEY_LOWER_BATTERY_VAL).toInt();
    pConfig->endGroup();

    float fBatteryTmp = fBattery;
    if(iLowerBatteryVal < 100)
    {
        fBattery = (100.0f / (100.0f - iLowerBatteryVal * 1.0f)) * fBatteryTmp - ((100.0f * iLowerBatteryVal) / (100.0f - iLowerBatteryVal * 1.0f));
    }

    logWarning(QString("current battery origion val: %1, convert val: %2.").arg(fBatteryTmp).arg(fBattery));*/

    return fBattery;
}

/*************************************************
功能： 读取电量
返回值：电量信息，负数表示通讯问题，不进行处理
*************************************************/
int SystemSetService::getPower( void )
{
    int iBattery = 0;
#ifdef Q_PROCESSOR_ARM
    for(int i = 0; i < READ_POWER_FAILED_UP_LIMIT; ++i)
    {
        iBattery = get_bat_power();
        if(iBattery > 0)
        {
            //logInfo(QString("get system battery: %1.").arg(iBattery).toLatin1().data());
            break;
        }
    }
#endif
    if(0 > iBattery)
    {
        logError(QString("get system battery error, get_bat_power ret: %1.").arg(iBattery).toLatin1().data());
    }

    return iBattery;
}

/*************************************************
功能： wifi连接状态
返回值：true -- 已连接
      false -- 未连接
*************************************************/
bool SystemSetService::isWifiConnected( void )
{
    bool bConnected = false;
#ifdef Q_PROCESSOR_ARM
    bConnected = (WIFI_CONN_SUCC == wifi_is_connected() ? true : false);
#endif
    emit sigWifiStateChanged(bConnected);
    return bConnected;
}

/*************************************************
功能： BT连接状态
返回值：true -- 已连接
      false -- 未连接
*************************************************/
bool SystemSetService::isBTConnected( void )
{
    if( NULL == m_pBluetoothService)
    {
        return false;
    }
    else
    {
        return m_pBluetoothService->isBluetoothConnected();
    }
}

/*************************************************
功能： 开始进行固件更新
*************************************************/
void SystemSetService::beginToUpdateFirmware(quint8 *pStmPercent , quint8 *pZigbeePercent)
{
    emit sigStartFwUpdate( pStmPercent, pZigbeePercent );
}

/*************************************************
功能： 槽，开始固件更新的处理
*************************************************/
void SystemSetService::onStartFwUpdate(quint8 *pStmPercent, quint8 *pZigbeePercent)
{
    APP_CHECK_RETURN(pStmPercent);
    APP_CHECK_RETURN(pZigbeePercent);

#ifdef Q_PROCESSOR_ARM
    dbg_info("ready update_conditioner\n");
    INT32 iSuccess = update_conditioner( pStmPercent );
    if(iSuccess != HC_SUCCESS)
    {
        dbg_warning("update_conditioner fail!\n");
    }

    if( iSuccess == HC_SUCCESS )
    {
        iSuccess = update_zigbee(pZigbeePercent);
        if(iSuccess != HC_SUCCESS)
        {
            dbg_warning("update_zigbee fail!\n");
        }
    }

    emit sigUpdateFwResult(iSuccess);
#endif
}
/*************************************************
功能： 创建蓝牙子服务
*************************************************/
void SystemSetService::onCreateSubService()
{
    m_pBluetoothService = new BluetoothSetService;
}

/*************************************************
功能： get current hot spot option
*************************************************/
SystemSet::HotSpotOption SystemSetService::hotSpotOption()
{
    return m_eHotSpotOption;
}

/*************************************************
功能： set hot spot option value
*************************************************/
void SystemSetService::setHotSpotOptionVal(SystemSet::HotSpotOption eOption)
{
    m_eHotSpotOption = eOption;
    return;
}

/*************************************************
功能： get current disk info
*************************************************/
bool SystemSetService::getDiskUsedInfo(quint64& llUsedSize, quint64& llAllSize, qint32& iAvailablePercent)
{
    bool bRet = false;
#ifdef Q_PROCESSOR_ARM
    sync();         //更新磁盘

    struct statfs64 fs;
    QString string = DATA_STORAGE_PATH.section("/", 0, 2);//获取磁盘名，根路径
    QByteArray ba = string.toLatin1();      //qstring转char*
    if(statfs64(ba.data(), &fs) < 0)
    {
        logError("call statfs64() failed.");
        return bRet;
    }

    llAllSize = fs.f_blocks * fs.f_bsize / (B_PER_KB * KB_PER_MB);// MB磁盘总空间大小
    double all = fs.f_blocks - fs.f_bfree + fs.f_bavail;

    iAvailablePercent = 0;
    if(qAbs(all) > ZERO_VAL)
    {
        iAvailablePercent = qRound(((double)fs.f_bavail) * 100.0 / (double)all); // 可用百分比
    }

    iAvailablePercent = (iAvailablePercent >= 100) ? 100 : iAvailablePercent;
    iAvailablePercent = (iAvailablePercent <= 0) ? 0 : iAvailablePercent;
    llUsedSize = (fs.f_blocks * fs.f_bsize - fs.f_bavail * fs.f_bsize) / (B_PER_KB * KB_PER_MB);
    llUsedSize = (llUsedSize >= llAllSize) ? 0 : llUsedSize;
    iAvailablePercent = (0 == llUsedSize) ? 100 : iAvailablePercent;
    bRet = true;
#endif
    //logInfo(QString("storage total size: %1, used: %2, available percent: %3.").arg(llAllSize).arg(llUsedSize).arg(iAvailablePercent));
    return bRet;
}

/*************************************************
功能： remove the useless files
      etc:install packages,coredump files,temp files
*************************************************/
bool SystemSetService::removeUselessFiles()
{
#ifdef Q_PROCESSOR_ARM
    sync();         //更新磁盘
#endif

    //remove coredump files
    QDir coredumpDir( OUTER_DIR );
    QFileInfoList coredumpList = coredumpDir.entryInfoList( QDir::Files | QDir::Readable | QDir::Writable );
    foreach( QFileInfo coredumpInfo, coredumpList )
    {
        if( coredumpInfo.fileName().startsWith( "core-" ) )
        {
            QFile file( coredumpInfo.absoluteFilePath() );
            file.remove();
            qDebug() << "file path : " << coredumpInfo.absoluteFilePath();
        }
    }

    //remove install packages
    QString strCurrentVersion = curSoftwareVersion();
    QDir packagesDir( INSTALL_PACKAGE_DIR );
    QFileInfoList packageList = packagesDir.entryInfoList( QDir::Dirs | QDir::NoDotAndDotDot | QDir::Readable | QDir::Writable );
    foreach( QFileInfo tmpInfo, packageList )
    {
        if( strCurrentVersion != tmpInfo.fileName() )
        {
            QDir subDir( tmpInfo.absoluteFilePath() );
            QFileInfoList fileInfos = subDir.entryInfoList( QDir::Files | QDir::Readable | QDir::Writable );
            foreach( QFileInfo fileInfo, fileInfos )
            {
                QFile file( fileInfo.absoluteFilePath() );
                file.remove();
            }
            packagesDir.rmdir( tmpInfo.fileName() );
            qDebug() << "tmpInfo.fileName() : " << tmpInfo.fileName();
        }
    }

    //remove temp files
    QDir tmpSaveDir( TMP_FILE_DIR );
    QFileInfoList allTmpFilepathList;
    QFileInfoList subDirList = tmpSaveDir.entryInfoList( QDir::Dirs | QDir::NoDotAndDotDot | QDir::Readable | QDir::Writable );
    foreach( QFileInfo subDirInfo, subDirList )
    {
        QDir subDir( subDirInfo.filePath() );
        QFileInfoList tmpInfos = subDir.entryInfoList( QDir::Files | QDir::Readable | QDir::Writable );
        allTmpFilepathList += tmpInfos;
    }
    QDateTime currentDateTime = QDateTime::currentDateTime();
    foreach( QFileInfo tmpInfo, allTmpFilepathList )
    {
        int secsTo = tmpInfo.lastModified().secsTo( currentDateTime );
        if( secsTo > RECYCLE_TIME )
        {
            QFile file( tmpInfo.absoluteFilePath() );
            file.remove();
            qDebug() << "file path : " << tmpInfo.absoluteFilePath();
        }
    }

#ifdef Q_PROCESSOR_ARM
    sync();         //更新磁盘
#endif

    return true;
}

/*************************************************
功能： 时区改变时修改系统时间
*************************************************/
void SystemSetService::onTimezoneChanged()
{
    double dLastTimezoneHour = TimezoneManager::instance()->getLastLocalTimezoneVal();
    double dCurTimezoneHour = TimezoneManager::instance()->getLocalTimezoneVal();

    qint64 lCurTime = QDateTime::currentDateTime().toMSecsSinceEpoch();
    qint64 lUtcTime = lCurTime - qint64(dLastTimezoneHour * 3600.0 * 1000.0);  //ms
    lCurTime = lUtcTime + qint64(dCurTimezoneHour * 3600.0 * 1000.0);  //ms
    QString qstrDateTime = QDateTime::fromMSecsSinceEpoch(lCurTime).toString("MMddHHmmyyyy.ss");     //format: MMddHHmmyyyy.ss

    SystemSetService::instance()->setSystemDate(qstrDateTime);
    QString qstrInfo = QString("utc: %1, format time: %2.").arg(lCurTime).arg(qstrDateTime);
    logInfo(qstrInfo.toLatin1().data());
    return;
}

/************************************
 * 功能：设置设备本地校时
 * 输入参数：
 *      qi64CalibUtcTime：utc时间，单位秒
 *      dTimezoneVal：时区值
 * 返回值：
 *      QString：当前校时后的时间信息，格式为yyyy-MM-dd hh:mm:ss
 * **********************************/
QString SystemSetService::setLocalCalibrateTime(qint64 qi64CalibUtcTime, double dTimezoneVal)
{
    //先设置时区信息，再设置时间
    //TimezoneManager::instance()->setLocalTimezoneVal(dTimezoneVal);//当时区功能不启用时，需要设置时间
    Q_UNUSED(dTimezoneVal);

    //UTC时间转换为系统具有时区时间后的补偿时间
    qint64 qi64CurTime = TimezoneManager::instance()->formatUTCTimeToLocalValMsec(qi64CalibUtcTime * 1000);//单位毫秒
    QString qstrDateTime = QDateTime::fromMSecsSinceEpoch(qi64CurTime).toString("MMddHHmmyyyy.ss");     //format: MMddHHmmyyyy.ss

    SystemSetService::instance()->setSystemDate(qstrDateTime);
    logInfo(QString("utc: %1, format time: %2.").arg(qi64CurTime).arg(qstrDateTime));

    QDateTime stPlanTime = QDateTime::fromTime_t(static_cast<uint>(qi64CurTime / 1000));           //计算值单位为s计算
    return stPlanTime.toString(TIME_FORMAT);
}

/*************************************************
功能： 记录存储卡已被格式化成功
*************************************************/
void SystemSetService::storageFormatted()
{
    emit sigStorageFormatted();
    return;
}

/*************************************************
功能：关闭wifi连接
*************************************************/
void SystemSetService::closeWifiConnection()
{
#ifdef Q_PROCESSOR_ARM
    if(OPER_SUCC == wifi_exit(WIFI_CLIENT_MODE))
    {
        logInfo("SystemSetService::setHotSpotOption, wifi station mode exit success.");
    }
    else
    {
        logInfo("SystemSetService::setHotSpotOption, wifi station mode exit fail.");
    }

    if(WIFI_CONN_SUCC == wifi_is_connected())
    {
        wifi_disconnect();
    }
#endif
    return;
}

/**************************************************
 *功能：控制模拟板上电
 *输入参数：
 *      bOpen：true -- 上电，false -- 下电
 * *************************************************/
void SystemSetService::openAnalogBoardPower(bool bOpen)
{
#ifdef Q_PROCESSOR_ARM
    quint32 qui32PowerVal = bOpen ? 1 : 0;
    analog_board_power_ctrl(qui32PowerVal);
#endif
    return;
}

/***************************************************
 * 功能：存储卡是否可操作
 * 返回值：
 *      bool：true -- 可进行操作，false -- 不可进行操作
 * *************************************************/
bool SystemSetService::storageOperEnable()
{
    bool bRet = true;
#ifdef Q_PROCESSOR_ARM
    INT8 szPath[64] = {0};
    memcpy(szPath, OUTER_DIR, strlen(OUTER_DIR));
    //bRet = (0 == check_mounted(szPath)) ? true : false;
    int iRet = check_mounted(szPath);
    if(0 != iRet)
    {
        logError(QString("check mounted return val: %1").arg(iRet));
    }

    bRet = (0 == iRet) ? true : false;
#endif

    return bRet;
}

/************************************
 * 功能：设置巡检跳转模式
 * 输入参数：
 *      eMode：巡检跳转模式
 * **********************************/
void SystemSetService::setPatrolSwitchMode(SystemSet::PatrolSwitchMode eMode)
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(static_cast<int>(eMode), APPConfig::KEY_PATROL_SWITCH_MODE);
    pConfig->endGroup();
    pConfig->save();

    return;
}

/************************************
 * 功能：获取巡检跳转模式
 * 返回值：
 *      PatrolSwitchMode：巡检跳转模式
 * **********************************/
SystemSet::PatrolSwitchMode SystemSetService::getPatrolSwitchMode()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    int iModeVal = pConfig->value(APPConfig::KEY_PATROL_SWITCH_MODE).toInt();
    pConfig->endGroup();
    return static_cast<SystemSet::PatrolSwitchMode>(iModeVal);
}

/************************************
 * 功能：设置当前定制接入终端接入模式
 * 输入参数：
 *      eMode：0 -- 有线USB直连，1 -- 蓝牙连接，2 -- wifi连接
 * **********************************/
void SystemSetService::setCustomAccessMode(SystemSet::CustomAccessMode eMode)
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(static_cast<int>(eMode), APPConfig::KEY_CUSTOM_ACCESS_MODE);
    pConfig->endGroup();

    return;
}

/************************************
 * 功能：获取当前定制接入终端接入模式
 * 返回值：
 *      CustomAccessMode：0 -- 有线USB直连，1 -- 蓝牙连接，2 -- wifi连接
 * **********************************/
SystemSet::CustomAccessMode SystemSetService::getCustomAccessMode()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    int iModeVal = pConfig->value(APPConfig::KEY_CUSTOM_ACCESS_MODE).toInt();
    pConfig->endGroup();
    return static_cast<SystemSet::CustomAccessMode>(iModeVal);
}

/************************************
 * 功能：设置设备激活码信息
 * 输入参数：
 *      qbaActiveCodeInfo：激活码信息
 * 返回值：
 *      bool：true -- 激活码设置正确，false -- 激活码设置失败
 * **********************************/
bool SystemSetService::setSysActivateCodeInfo(const QByteArray &qbaActiveCodeInfo)
{
    ActiveInfo stActiveInfo;
    IniConfig::readActiveInfo(stActiveInfo);

    //解析激活码信息 = 起始时间（yyyyMMddwwhhmmss，长度16）+ 截止时间（yyyyMMddwwhhmmss，长度16）+ 摘要信息（40字节）+ 用户信息
    int iTimeLen = 16;
    int iSha1Len = 40;
    int iUserLen = 3;

    if(qbaActiveCodeInfo.size() < (2 * iTimeLen + iSha1Len))
    {
        logError(QString("activate code info length: %1 error.").arg(qbaActiveCodeInfo.size()));
        return false;
    }

    QByteArray qbaOrigionST = qbaActiveCodeInfo.left(iTimeLen);
    QByteArray qbaOrigionET = qbaActiveCodeInfo.mid(iTimeLen, iTimeLen);
    QByteArray qbaSummary = qbaActiveCodeInfo.mid(2 * iTimeLen, iSha1Len);
    QByteArray qbaUserInfo = qbaActiveCodeInfo.mid(2 * iTimeLen + iSha1Len);

    stActiveInfo.qstrOrigionStartTime = qbaOrigionST;
    stActiveInfo.qstrOrigionEndTime = qbaOrigionET;
    stActiveInfo.qstrSummary = qbaSummary;
    stActiveInfo.qstrShowStartDate = getActiveShowTimeInfo(stActiveInfo.qstrOrigionStartTime);
    stActiveInfo.qstrShowEndDate = getActiveShowTimeInfo(stActiveInfo.qstrOrigionEndTime);

    QByteArray qbaIsShow;
    QByteArray qbaShowInfo;
    QByteArray qbaCrc32Info;
    quint32 qui32Crc32Val = 0;

    qbaIsShow.clear();
    qbaShowInfo.clear();
    qbaCrc32Info.clear();

    int iIndex = qbaUserInfo.lastIndexOf('\n');
    if(-1 != iIndex && qbaUserInfo.size() > (iIndex + 1))
    {
        qbaCrc32Info = qbaUserInfo.mid(iIndex + 1);
        qui32Crc32Val = qbaCrc32Info.toUInt();
        qbaUserInfo = qbaUserInfo.left(iIndex + 1);
        quint32 qui32Crc32CalVal = CRCUtil::getCRC32(qbaUserInfo);
        if(qui32Crc32CalVal != qui32Crc32Val)
        {
            logError(QString("activate code info crc: %1, cal crc: %2 error.").arg(qui32Crc32Val).arg(qui32Crc32CalVal));
            //return false;//平台侧使用java string通过UTF-16LE方式转出的字节数组计算CRC32值，和T95这个直接用网络接收到的字节数组计算的CRC32值不一致，这里暂时不校验
        }
    }

    if(iUserLen <= qbaUserInfo.size())
    {
        qbaIsShow = qbaUserInfo.mid(1, 1);
        stActiveInfo.bShow = qbaIsShow.toInt();
        if(stActiveInfo.bShow)
        {
            qbaShowInfo = qbaUserInfo.mid(3);
        }
    }
    else
    {
        logError(QString("activate user info length: %1 error.").arg(qbaUserInfo.size()));
        return false;
    }

    stActiveInfo.qstrFinalUserInfo = qbaShowInfo/*QString::fromUtf8(qbaShowInfo)*/;

    bool bRet = true;

    do
    {
        if(stActiveInfo.qstrShowEndDate <= stActiveInfo.qstrShowStartDate)
        {
            bRet = false;
            logError(QString("activate show end time: %1 <= show start time: %2.").arg(stActiveInfo.qstrShowEndDate).arg(stActiveInfo.qstrShowStartDate));
            break;
        }

        QString qstrCurTimeInfo = QDateTime::currentDateTime().toString(ACTIVE_SHOW_DATE_FORMAT);//激活前已经同步过正确的时间
        if(stActiveInfo.qstrShowEndDate <= qstrCurTimeInfo)
        {
            bRet = false;
            logError(QString("activate show end time: %1 <= system current time: %2.").arg(stActiveInfo.qstrShowEndDate).arg(qstrCurTimeInfo));
            break;
        }

        //校验摘要信息，SHA1(起始时间（yyyyMMddwwhhmmss，8字节）+ 截止时间（yyyyMMddwwhhmmss，8字节） + SN（8字节） + 密码（48字节）);
        //时间格式，2019082804150621即[0]=20，[1]=19，[2]=08，[3]=28，[4]=04，[5]=15，[6]=06，[7]=21
        QByteArray qbaInfoCheck;
        qbaInfoCheck.reserve(68);

        qbaOrigionST.clear();
        qbaOrigionET.clear();
        qbaOrigionST = getTimeBytesFromTimeStr(stActiveInfo.qstrOrigionStartTime);
        qbaOrigionET = getTimeBytesFromTimeStr(stActiveInfo.qstrOrigionEndTime);
        qbaInfoCheck.append(qbaOrigionST);
        qbaInfoCheck.append(qbaOrigionET);

        deviceIdNum();
        QByteArray qbaDevSn;
        qbaDevSn.reserve(8);
        if(m_pbyDevSN)
        {
            for(int i = 0; i < 8; ++i)
            {
                qbaDevSn[i] = m_pbyDevSN[7 - i];  //按照90的处理逻辑,SN顺序反过来处理
            }
        }
        qDebug() << qbaDevSn.toHex().toUpper();
        qbaInfoCheck.append(qbaDevSn);

        QByteArray qbaDevPassword;
        qbaDevPassword.reserve(44);
        for(int i = 0; i < 44; ++i)
        {
            qbaDevPassword[i] = SHA1_KEY[i];
        }
        qbaInfoCheck.append(qbaDevPassword);

        QByteArray qbaSummaryCheck = QCryptographicHash::hash(qbaInfoCheck, QCryptographicHash::Sha1);
        if(qbaSummary != qbaSummaryCheck.toHex().toUpper())
        {
            bRet = false;
            logError("check summary error.");
            break;
        }

        stActiveInfo.bActivationEffective = true;
        stActiveInfo.eState = ActiveManager::instance()->getActiveStateByDate(stActiveInfo.qstrShowStartDate, stActiveInfo.qstrShowEndDate);
        IniConfig::writeActiveInfo(stActiveInfo);   //校验通过，将激活信息保存至配置文件

        if(ACTIVE_OVERDUE != stActiveInfo.eState)
        {
            emit sigDeviceActived();
        }

    }while(0);

    return bRet;
}

/*****************************************
 * 功能：根据原始时间格式获取显示的时间格式数据
 * 输入参数：
 *      qstrActiveOrigionTimeInfo：原始激活时间格式，yyyyMMddwwhhmmss
 * 返回值：
 *      QString：显示的时间格式，yyyy/MM/dd
 * ***************************************/
QString SystemSetService::getActiveShowTimeInfo(const QString &qstrActiveOrigionTimeInfo)
{
    if(qstrActiveOrigionTimeInfo.length() < 16)
    {
        logError("origion active time info length is error.");
        return "";
    }

    bool ok = false;
    quint8 qui8YearHigh = static_cast<quint8>(qstrActiveOrigionTimeInfo.mid(2, 2).toInt(&ok, 16));
    quint8 qui8YearLow = static_cast<quint8>(qstrActiveOrigionTimeInfo.mid(0, 2).toInt(&ok, 16));
    quint8 qui8Month = static_cast<quint8>(qstrActiveOrigionTimeInfo.mid(4, 2).toInt(&ok, 16));
    quint8 qui8Day = static_cast<quint8>(qstrActiveOrigionTimeInfo.mid(6, 2).toInt(&ok, 16));

    return QString("%1%2/%3/%4").arg(qui8YearHigh, 2, 10, QChar('0')).arg(qui8YearLow, 2, 10, QChar('0')).arg(qui8Month, 2, 10, QChar('0')).arg(qui8Day, 2, 10, QChar('0'));
}

/*****************************************
 * 功能：根据原始时间格式获取8字节的时间格式
 * 输入参数：
 *      qstrTimeString：原始时间格式，yyyyMMddwwhhmmss
 * 返回值：
 *      QByteArray：8个字节的时间信息yy yy MM dd ww hh mm ss
 * ***************************************/
QByteArray SystemSetService::getTimeBytesFromTimeStr(const QString &qstrTimeString)
{
    QByteArray qbaTimeInfo;
    qbaTimeInfo.clear();

    if(qstrTimeString.length() >= 16)
    {
        bool ok = false;
        quint8 qui8YearHigh = static_cast<quint8>(qstrTimeString.mid(2, 2).toInt(&ok, 16));
        quint8 qui8YearLow = static_cast<quint8>(qstrTimeString.mid(0, 2).toInt(&ok, 16));
        quint8 qui8Month = static_cast<quint8>(qstrTimeString.mid(4, 2).toInt(&ok, 16));
        quint8 qui8Day = static_cast<quint8>(qstrTimeString.mid(6, 2).toInt(&ok, 16));
        quint8 qui8Week = 0x01;
        quint8 qui8Hour = static_cast<quint8>(qstrTimeString.mid(10, 2).toInt(&ok, 16));
        quint8 qui8Minute = static_cast<quint8>(qstrTimeString.mid(12, 2).toInt(&ok, 16));
        quint8 qui8Second = static_cast<quint8>(qstrTimeString.mid(14, 2).toInt(&ok, 16));
        qbaTimeInfo.append(qui8YearLow);
        qbaTimeInfo.append(qui8YearHigh);
        qbaTimeInfo.append(qui8Month);
        qbaTimeInfo.append(qui8Day);
        qbaTimeInfo.append(qui8Week);
        qbaTimeInfo.append(qui8Hour);
        qbaTimeInfo.append(qui8Minute);
        qbaTimeInfo.append(qui8Second);
    }

    return qbaTimeInfo;
}

/************************************
 * 功能：设备校准功能是否起作用
 * 返回值：
 *      bool：true -- 起作用，false -- 不起作用
 * **********************************/
bool SystemSetService::devCalibrateEnable()
{
    bool bEnable = true;

    do
    {
        CalibrateInfo stCalibInfo;
        IniConfig::readCalibrateInfo(stCalibInfo);
        if(stCalibInfo.qstrCalibrationDate.isEmpty())
        {
            bEnable = false;
            break;
        }

        FuncConfigManagerNS::FunctionInfo stTevInfo;
        stTevInfo.iFuncID = FuncConfigManagerNS::TEV;

        FuncConfigManagerNS::ConfigInfo stConfigInfo;
        FuncCfgSrvManager::instance()->getConfigInfo(stConfigInfo);

        int iTevIndex = stConfigInfo.qvtFuncInfos.indexOf(stTevInfo);
        int iFunctionSize = stConfigInfo.qvtFuncInfos.size();

        if(0 <= iTevIndex && iTevIndex < iFunctionSize)
        {
            bEnable = stConfigInfo.qvtFuncInfos[iTevIndex].bEnable;
        }
        else
        {
            bEnable = false;
        }

    }while (0);

    return bEnable;
}


/************************************
 * 功能：保存被选中wifi热点名的索引
 * 入口参数：index 被选中标签索引 iSigStrength 索引对应的信号强度
 *
 * **********************************/
void SystemSetService::saveWifiSelectedIndex(int index, int iSigStrength)
{
    if(index >= 0)
    {
        m_iIndex = index;
        m_iSigStrength.insert(m_iIndex, iSigStrength);
    }
}

/************************************
 * 功能：保存被选中wifi热点名
 * 入口参数：index 被选中标签索引 strWifiName wifi热点名
 *
 * **********************************/
void SystemSetService::saveSelectedWifiName(int index, QString strWifiName)
{
    if(index >= 0)
    {
        m_iIndex = index;
        m_mapWifiInfo.insert(m_iIndex, strWifiName);
    }
}

/************************************
 * 功能：获取被选中索引对应的信号强度
 * 入口参数：无
 *
 * **********************************/
int SystemSetService::readWifiSelectedSigStrength()
{
    return m_iSigStrength.value(m_iIndex);
}

/************************************
 * 功能：获取被选中索引对应的信号强度
 * 入口参数：无
 *
 * **********************************/
QString SystemSetService::readSelectedWifiName()
{
    return m_mapWifiInfo.value(m_iIndex);
}

/************************************
 * 功能：获取接入终端接入协议类型
 * 返回值：
 *      SystemSet::AccessProtocol：接入终端接入协议类型
 * **********************************/
SystemSet::AccessProtocol SystemSetService::getAccessProtocol()
{
    //默认支持一种接入协议
    SystemSet::AccessProtocol eProt = SystemSet::ACCESS_PROTO_NONE;

    FuncConfigManagerNS::FunctionInfo stAcZJHYInfo;
    stAcZJHYInfo.iFuncID = FuncConfigManagerNS::ACCESS_ZJHY;
    stAcZJHYInfo.iParentID = FuncConfigManagerNS::ACCESS;

    FuncConfigManagerNS::FunctionInfo stAcSDLRInfo;
    stAcSDLRInfo.iFuncID = FuncConfigManagerNS::ACCESS_SDLR;
    stAcSDLRInfo.iParentID = FuncConfigManagerNS::ACCESS;

    FuncConfigManagerNS::FunctionInfo stAcBJRZTInfo;
    stAcBJRZTInfo.iFuncID = FuncConfigManagerNS::ACCESS_BJRZT;
    stAcBJRZTInfo.iParentID = FuncConfigManagerNS::ACCESS;

    FuncConfigManagerNS::FunctionInfo stAcSHAFInfo;
    stAcSHAFInfo.iFuncID = FuncConfigManagerNS::ACCESS_SHAF;
    stAcSHAFInfo.iParentID = FuncConfigManagerNS::ACCESS;

    FuncConfigManagerNS::FunctionInfo stAcXACYInfo;
    stAcXACYInfo.iFuncID = FuncConfigManagerNS::ACCESS_XACY;
    stAcXACYInfo.iParentID = FuncConfigManagerNS::ACCESS;

    FuncConfigManagerNS::FunctionInfo stAcTJXJInfo;
    stAcTJXJInfo.iFuncID = FuncConfigManagerNS::ACCESS_TJXJ;
    stAcTJXJInfo.iParentID = FuncConfigManagerNS::ACCESS;

    FuncConfigManagerNS::FunctionInfo stAcJSDKYInfo;
    stAcJSDKYInfo.iFuncID = FuncConfigManagerNS::ACCESS_JSDKY;
    stAcJSDKYInfo.iParentID = FuncConfigManagerNS::ACCESS;

    do
    {
        FuncConfigManagerNS::ConfigInfo stConfigInfo;
        FuncCfgSrvManager::instance()->getConfigInfo(stConfigInfo);
        int iSize = stConfigInfo.qvtFuncInfos.size();

        int iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAcZJHYInfo);
        if(0 <= iIndex && iIndex < iSize)
        {
            if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
            {
                eProt = SystemSet::ACCESS_PROTO_ZJHY;
                break;
            }
        }

        iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAcSDLRInfo);
        if(0 <= iIndex && iIndex < iSize)
        {
            if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
            {
                eProt = SystemSet::ACCESS_PROTO_SDLR;
                break;
            }
        }

        iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAcBJRZTInfo);
        if(0 <= iIndex && iIndex < iSize)
        {
            if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
            {
                eProt = SystemSet::ACCESS_PROTO_BJRZT;
                break;
            }
        }

        iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAcSHAFInfo);
        if(0 <= iIndex && iIndex < iSize)
        {
            if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
            {
                eProt = SystemSet::ACCESS_PROTO_SHAF;
                break;
            }
        }

        iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAcXACYInfo);
        if(0 <= iIndex && iIndex < iSize)
        {
            if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
            {
                eProt = SystemSet::ACCESS_PROTO_XACY;
                break;
            }
        }

        iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAcTJXJInfo);
        if(0 <= iIndex && iIndex < iSize)
        {
            if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
            {
                eProt = SystemSet::ACCESS_PROTO_TJXJ;
                break;
            }
        }

        iIndex = stConfigInfo.qvtFuncInfos.indexOf(stAcJSDKYInfo);
        if(0 <= iIndex && iIndex < iSize)
        {
            if(stConfigInfo.qvtFuncInfos[iIndex].bEnable)
            {
                eProt = SystemSet::ACCESS_PROTO_JSDKY;
                break;
            }
        }


    }while(0);

    logInfo(QString("current access protocol: %1.").arg(eProt));

    return eProt;
}

/************************************
 * 功能：判断SSL认证是否使能状态
 * 返回值：
 *      bool：状态，true -- 使能，false -- 非使能
 * **********************************/
bool SystemSetService::isSSLVerifyEnable()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    SystemSet::SslVerifySwitch eVal = static_cast<SystemSet::SslVerifySwitch>(pConfig->value(APPConfig::KEY_SSL_SWITCH).toInt());
    pConfig->endGroup();
    return (SystemSet::SSLVERF_ON == eVal) ? true : false;
}

/************************************
 * 功能：设置SSL认证开关状态值
 * 输入参数：
 *      eVal：SSL认证开关状态值
 * **********************************/
void SystemSetService::setSSLVerifyVal(SystemSet::SslVerifySwitch eVal)
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(static_cast<int>(eVal), APPConfig::KEY_SSL_SWITCH);
    pConfig->endGroup();
    pConfig->save();

    return;
}

/************************************
 * 功能：获取SSL认证模式
 * 返回值：
 *      SystemSet::SslVerifyMode：SSL认证模式
 * **********************************/
SystemSet::SslVerifyMode SystemSetService::getSSLVerifyMode()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    int iVal = pConfig->value(APPConfig::KEY_SSL_MODE).toInt();
    pConfig->endGroup();

    return static_cast<SystemSet::SslVerifyMode>(iVal);
}

/************************************
 * 功能：设置SSL认证模式
 * 输入参数：
 *      eVal：SSL认证模式
 * **********************************/
void SystemSetService::setSSLVerifyMode(SystemSet::SslVerifyMode eVal)
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(static_cast<int>(eVal), APPConfig::KEY_SSL_MODE);
    pConfig->endGroup();
    pConfig->save();

    return;
}

/************************************
 * 功能：判断Tev校准写入是否使能状态
 * 返回值：
 *      bool：状态，true -- 使能，false -- 非使能
 * **********************************/
bool SystemSetService::isTevCalibWriteEnable()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    SystemSet::TevCalibWriteSwitch eVal = static_cast<SystemSet::TevCalibWriteSwitch>(pConfig->value(APPConfig::KEY_TEV_CALIB_WRITE_SWITCH).toInt());
    pConfig->endGroup();
    return (SystemSet::TEV_CALIBWRITE_ON == eVal) ? true : false;
}

/************************************
 * 功能：设置Tev校准写入开关状态值
 * 输入参数：
 *      eVal：Tev校准写入开关状态值
 * **********************************/
void SystemSetService::setTevCalibWriteVal(SystemSet::TevCalibWriteSwitch eVal)
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(static_cast<int>(eVal), APPConfig::KEY_TEV_CALIB_WRITE_SWITCH);
    pConfig->endGroup();
    return;
}

/************************************
 * 功能：设置软键盘模式
 * 输入参数：
 *      eMode：软键盘模式
 * **********************************/
void SystemSetService::setKeyBoardMode(SystemSet::KeyboardMode eMode)
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(static_cast<int>(eMode), APPConfig::KEY_KEYBOARD_MODE);
    pConfig->endGroup();
    pConfig->save();

    return;
}

/************************************
 * 功能：获取软键盘模式
 * 返回值：
 *      SystemSet::KeyBoardMode：软键盘模式
 * **********************************/
SystemSet::KeyboardMode SystemSetService::getKeyBoardMode()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    int iVal =  pConfig->value(APPConfig::KEY_KEYBOARD_MODE).toInt();
    pConfig->endGroup();
    return static_cast<SystemSet::KeyboardMode>(iVal);
}

/************************************************
 * 功能：获取系统设置音量
 * 返回值：
 *      quint8：系统音量
 ************************************************/
quint8 SystemSetService::getSystemVolume()
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    quint8 qui8Val = static_cast<quint8>(pConfig->value(APPConfig::KEY_SYSTEM_VOLUME).toUInt());
    pConfig->endGroup();
    return qui8Val;
}

/************************************************
 * 功能：获取AE噪声增益状态
 * 返回值：
 *      quint8：增益状态
 ************************************************/
quint8 SystemSetService::getAESoundGain()
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_AE);
    quint8 qui8Val = static_cast<quint8>(pConfig->value(AE::KEY_GAIN).toInt());
    pConfig->endGroup();
    return qui8Val;
}

/************************************************
 * 功能：获取AE通道类型
 * 返回值：
 *      quint8：AE通道类型
 ************************************************/
quint8 SystemSetService::getAEChannelType()
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_AE);
    quint8 qui8Val = static_cast<quint8>(pConfig->value(AE::KEY_CHANNEL_TYPE).toInt());
    pConfig->endGroup();
    return qui8Val;
}

/*******************************************************
 * 功能：读取固件概要信息
 * 输入参数：
 *      stFirmInfo：固件概要信息
 * **********************************************************/
void SystemSetService::readFirmwareInfo(SystemSet::FirmwareInfo &stFirmInfo)
{
    IniConfig::readFirmwareInfo(stFirmInfo);
    return;
}

/*******************************************************
 * 功能：初始化读取固件概要信息
 * 输入参数：
 *      stFirmInfo：固件概要信息
 * **********************************************************/
void SystemSetService::initFirmwareInfo(SystemSet::FirmwareInfo &stFirmInfo)
{
    readFirmwareInfo(stFirmInfo);
    if(stFirmInfo.qstrDevSwVersion.isEmpty() || stFirmInfo.qstrDevSwVersion != curSoftwareVersion())
    {
        stFirmInfo.qstrDevSwVersion = curSoftwareVersion();//软件版本
        IniConfig::writeFirmwareInfo(stFirmInfo);
    }

    if(stFirmInfo.qstrDevModel.isEmpty())
    {
        if(SystemSet::FIRM_PMDT == stFirmInfo.eFirmType)
        {
            stFirmInfo.qstrDevModel = DEV_MODEL_PMDT;
        }
        else
        {
            stFirmInfo.qstrDevModel = DEV_MODEL_PDSTARS;
        }
    }

    return;
}

/*******************************************************
 * 功能：重新读取固件概要信息
 * 输入参数：
 *      stFirmInfo：固件概要信息
 * **********************************************************/
void SystemSetService::reloadFirmwareInfo(SystemSet::FirmwareInfo &stFirmInfo)
{
    initFirmwareInfo(m_stFirmwareInfo);

    //默认logo
    if(!FileOperUtil::checkFileOrDirExist(DEV_LOGO_PATH))
    {
        QByteArray qbaLogoContent;
        qbaLogoContent.clear();
        if(SystemSet::FIRM_PMDT == m_stFirmwareInfo.eFirmType)
        {
            FileOperUtil::readFile(DEV_PMDT_LOGO_PATH, qbaLogoContent);
        }
        else
        {
            FileOperUtil::readFile(DEV_PDSTARS_LOGO_PATH, qbaLogoContent);
        }

        if(qbaLogoContent.isEmpty())
        {
            logError("logo file content is empty.");
        }
        else
        {
            FileOperUtil::writeFile(DEV_LOGO_PATH, qbaLogoContent, true);
        }
    }

    stFirmInfo = m_stFirmwareInfo;

    return;
}

/******************************************************
 * 功能：读取固件概要信息
 * 输出参数：
 *      stFirmInfo：固件概要信息
 * **********************************************************/
void SystemSetService::getFirmwareInfo(SystemSet::FirmwareInfo &stFirmInfo)
{
    stFirmInfo = m_stFirmwareInfo;
    return;
}

/******************************************************
 * 功能：读取固件类型
 * 返回值：
 *      SystemSet::FirmType：固件类型
 * **********************************************************/
SystemSet::FirmType SystemSetService::getFirmwareType()
{
    return m_stFirmwareInfo.eFirmType;
}

/******************************************************
 * 功能：获取设备类型
 * 返回值：
 *      QString：设备类型
 * **********************************************************/
QString SystemSetService::getDevModel()
{
    if(m_stFirmwareInfo.qstrDevModel.isEmpty())
    {
        if(SystemSet::FIRM_PMDT == m_stFirmwareInfo.eFirmType)
        {
            m_stFirmwareInfo.qstrDevModel = DEV_MODEL_PMDT;
        }
        else
        {
            m_stFirmwareInfo.qstrDevModel = DEV_MODEL_PDSTARS;
        }
    }

    return m_stFirmwareInfo.qstrDevModel;
}

/******************************************************
 * 功能：获取公司名称
 * 返回值：
 *      QString：公司名称
 * **********************************************************/
QString SystemSetService::getCompanyName()
{
    QString qstrCompanyName = "";

    if(SystemSet::FIRM_PMDT == m_stFirmwareInfo.eFirmType)
    {
        if(m_stFirmwareInfo.qstrEnglishCompanyName.isEmpty())
        {
            qstrCompanyName = DEV_PMDT_EN_COMPANY_NAME;
        }
        else
        {
            qstrCompanyName = m_stFirmwareInfo.qstrEnglishCompanyName;
        }
    }
    else
    {
        if(m_stFirmwareInfo.qstrChineseCompanyName.isEmpty())
        {
            qstrCompanyName = DEV_PDSTARS_EN_COMPANY_NAME;
        }
        else
        {
            qstrCompanyName = m_stFirmwareInfo.qstrChineseCompanyName;
        }
    }

    return qstrCompanyName;
}

/*******************************************************
 * 功能：写入固件概要信息
 * 输入参数：
 *      stFirmInfo：固件概要信息
 *      bNetworkSetting：是否来自于网络设置的调用
 * **********************************************************/
void SystemSetService::setFirmwareInfo(const SystemSet::FirmwareInfo &stFirmInfo, bool bNetworkSetting)
{
    m_stFirmwareInfo = stFirmInfo;
    m_stFirmwareInfo.qstrDevSwVersion = curSoftwareVersion();//软件版本
    m_stFirmwareInfo.qstrDevHwVersion = "1.0.0.1";
    stm32Version(m_stFirmwareInfo.qstrEmbeddedVersion);

    if(SystemSet::FIRM_PMDT == m_stFirmwareInfo.eFirmType)
    {
        if(m_stFirmwareInfo.qstrDevModel.isEmpty())
        {
            m_stFirmwareInfo.qstrDevModel = DEV_MODEL_PMDT;
        }

        m_stFirmwareInfo.qstrChineseCompanyName = "";//PMDT固件，没有中文公司名称

        if(m_stFirmwareInfo.qstrEnglishCompanyName.isEmpty())
        {
            m_stFirmwareInfo.qstrEnglishCompanyName = DEV_PMDT_EN_COMPANY_NAME;
        }

        LocalComm::instance()->setUpgCloudInfo(DEV_PMDT_DEFAULT_UPG_IP, DEV_PMDT_DEFAULT_UPG_PORT);

        QByteArray qbaLogoContent;
        qbaLogoContent.clear();

        if(bNetworkSetting)
        {
            if(m_stFirmwareInfo.qbaLogoPngFileData.isEmpty())
            {
                FileOperUtil::readFile(DEV_PMDT_LOGO_PATH, qbaLogoContent);
                FileOperUtil::writeFile(DEV_LOGO_PATH, qbaLogoContent, true);
            }
            else
            {
                qbaLogoContent = QByteArray::fromBase64(m_stFirmwareInfo.qbaLogoPngFileData);
                FileOperUtil::writeFile(DEV_LOGO_PATH, qbaLogoContent, true);
            }
        }

        //修改相关固件不一致的配置
        ConfigInstance* pConfig = ConfigManager::instance()->config();

        pConfig->beginGroup(Module::GROUP_AE);
        pConfig->setValue(static_cast<int>(AE::SPECTRUM_DEFAULT_60), AE::KEY_FREQ_COMPONENT, AE::GROUP_AE_AMPLITUDE);
        pConfig->endGroup();

        pConfig->beginGroup(Module::GROUP_APP);
        pConfig->setValue(static_cast<int>(Module::FREQ_60HZ), APPConfig::KEY_SYS_FREQ);
        pConfig->setValue(static_cast<int>(SystemSet::SET_EN), APPConfig::KEY_SYS_LANGAUGE);
        pConfig->setValue(static_cast<int>(SystemSet::DATE_FORMAT_M_D_Y), APPConfig::KEY_DATE_FORMAT);
        pConfig->setValue(TimezoneInfoConfig::GMT_NEG05, APPConfig::KEY_TIMEZONE);
        pConfig->endGroup();

        pConfig->save();

        TimezoneManager::instance()->setLocalTimezoneVal(TimezoneInfoConfig::GMT_NEG05, bNetworkSetting);
    }
    else
    {
        if(m_stFirmwareInfo.qstrDevModel.isEmpty())
        {
            m_stFirmwareInfo.qstrDevModel = DEV_MODEL_PDSTARS;
        }

        if(m_stFirmwareInfo.qstrChineseCompanyName.isEmpty()
                && m_stFirmwareInfo.qstrEnglishCompanyName.isEmpty())
        {
            m_stFirmwareInfo.qstrEnglishCompanyName = DEV_PDSTARS_EN_COMPANY_NAME;
        }

        LocalComm::instance()->setUpgCloudInfo(DEV_PDSTARS_DEFAULT_UPG_IP, DEV_PDSTARS_DEFAULT_UPG_PORT);

        QByteArray qbaLogoContent;
        qbaLogoContent.clear();

        if(bNetworkSetting)
        {
            if(m_stFirmwareInfo.qbaLogoPngFileData.isEmpty())
            {
                FileOperUtil::readFile(DEV_PDSTARS_LOGO_PATH, qbaLogoContent);
                FileOperUtil::writeFile(DEV_LOGO_PATH, qbaLogoContent, true);
            }
            else
            {
                qbaLogoContent = QByteArray::fromBase64(m_stFirmwareInfo.qbaLogoPngFileData);
                FileOperUtil::writeFile(DEV_LOGO_PATH, qbaLogoContent, true);
            }
        }

        //修改相关固件不一致的配置
        ConfigInstance* pConfig = ConfigManager::instance()->config();

        pConfig->beginGroup(Module::GROUP_AE);
        pConfig->setValue(static_cast<int>(AE::SPECTRUM_DEFAULT_50), AE::KEY_FREQ_COMPONENT, AE::GROUP_AE_AMPLITUDE);
        pConfig->endGroup();

        pConfig->beginGroup(Module::GROUP_APP);
        pConfig->setValue(static_cast<int>(Module::FREQ_50HZ), APPConfig::KEY_SYS_FREQ);
        pConfig->setValue(static_cast<int>(SystemSet::SET_ZH_CN), APPConfig::KEY_SYS_LANGAUGE);
        pConfig->setValue(static_cast<int>(SystemSet::DATE_FORMAT_Y_M_D), APPConfig::KEY_DATE_FORMAT);
        pConfig->setValue(TimezoneInfoConfig::GMT_POS08, APPConfig::KEY_TIMEZONE);
        pConfig->endGroup();

        pConfig->save();

        TimezoneManager::instance()->setLocalTimezoneVal(TimezoneInfoConfig::GMT_POS08, bNetworkSetting);
    }

    IniConfig::writeFirmwareInfo(m_stFirmwareInfo);//写入配置文件
    FileOperUtil::refreshToSystemDisk();

    return;
}

/******************************************************
 * 功能：读取VPN接入信息
 * 输出参数：
 *      stVpnAccessInfo：VPN接入信息
 * **********************************************************/
void SystemSetService::getVpnAccessInfo(SystemSet::VpnAccessInfo &stVpnAccessInfo)
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    stVpnAccessInfo.iMode = pConfig->value(APPConfig::KEY_VPN_ACCESS_MODE).toInt();
    stVpnAccessInfo.qstrDomainName = pConfig->value(APPConfig::KEY_VPN_ACCESS_DOMAINNAME);
    stVpnAccessInfo.qstrUserName = pConfig->value(APPConfig::KEY_VPN_ACCESS_USERNAME);
    stVpnAccessInfo.qstrPwd = pConfig->value(APPConfig::KEY_VPN_ACCESS_PWD);
    pConfig->endGroup();

    return;
}

/*******************************************************
 * 功能：写入VPN接入信息
 * 输入参数：
 *      stVpnAccessInfo：VPN接入信息
 * **********************************************************/
void SystemSetService::setVpnAccessInfo(const SystemSet::VpnAccessInfo &stVpnAccessInfo)
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(stVpnAccessInfo.iMode, APPConfig::KEY_VPN_ACCESS_MODE);
    pConfig->setValue(stVpnAccessInfo.qstrDomainName, APPConfig::KEY_VPN_ACCESS_DOMAINNAME);
    pConfig->setValue(stVpnAccessInfo.qstrUserName, APPConfig::KEY_VPN_ACCESS_USERNAME);
    pConfig->setValue(stVpnAccessInfo.qstrPwd, APPConfig::KEY_VPN_ACCESS_PWD);
    pConfig->endGroup();
    pConfig->save();

    return;
}

/*******************************************************
 * 功能：初始化远程升级信息
 * **********************************************************/
void SystemSetService::initUpgradeInfo()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    QString qstrAddr = pConfig->value(APPConfig::KEY_UPGRADE_SRV_ADDR);
    quint16 qui16Port = pConfig->value(APPConfig::KEY_UPGRADE_SRV_PORT).toUShort();
    pConfig->endGroup();

    LocalComm::instance()->setUpgCloudInfo(qstrAddr, qui16Port);

    return;
}

/*******************************************************
 * 功能：初始化LCD
 * **********************************************************/
void SystemSetService::initLCD()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    int iVal = pConfig->value(APPConfig::KEY_LCD_INIT_SWITCH).toInt();
    pConfig->endGroup();

    if(SystemSet::LCD_INTI_ON == static_cast<SystemSet::LCDInitSwitch>(iVal))
    {
        //电量小于35%时才起效
        if(35.0f > batteryPercent())
        {
            system("lcd_init 4");
            //logInfo("lcd init is executed.");
        }
    }
    else
    {
        logWarning("lcd init switch is off.");
    }

    return;
}

/*******************************************************
 * 功能： 设置数据规范版本号
 * 输入参数：
 *      DataSpecificationNS::DataSpecificationVersion：数据规范版本号
 * **********************************************************/
void SystemSetService::setDataSpecificationVersion(const DataSpecificationNS::DataSpecificationVersion eDataSpecificationVersion)
{
    m_eDataSpecificationVersion = eDataSpecificationVersion;
    return;
}

/******************************************************
 * 功能：获取数据规范版本号
 * 返回值：
 *      DataSpecificationNS::DataSpecificationVersion：数据规范版本号
 * **********************************************************/
DataSpecificationNS::DataSpecificationVersion SystemSetService::getDataSpecificationVersion()
{
    return m_eDataSpecificationVersion;
}

/******************************************************
 * 功能：设置实时诊断开关
 * 输入参数：
 *      eSwitch：开关值
 * **********************************************************/
void SystemSetService::setRealtimeDiagnosisSwitch(SystemSet::RealtimeDiagnosisSwitch eSwitch)
{
    if(eSwitch != m_eRTDiagSwitch)
    {
        m_eRTDiagSwitch = eSwitch;

        ConfigInstance* pConfig = ConfigManager::instance()->config();
        pConfig->beginGroup(Module::GROUP_APP);
        pConfig->setValue(m_eRTDiagSwitch, APPConfig::KEY_DIAGNOSIS_REALTIME_SWITCH);
        pConfig->endGroup();
        pConfig->save();
    }

    return;
}

/******************************************************
 * 功能：获取实时诊断开关
 * 输入参数：
 *      SystemSet::RealtimeDiagnosisSwitch：开关值
 * **********************************************************/
SystemSet::RealtimeDiagnosisSwitch SystemSetService::getRealtimeDiagnosisSwitch()
{
    //通过内存变量，避免频繁操作文件
    return m_eRTDiagSwitch;
}

/******************************************************
 * 功能：设置PRPS采样间隔，单位ms
 * 输入参数：
 *      iInterval：采样间隔，单位ms
 * **********************************************************/
void SystemSetService::setPRPSSampleInterval(int iInterval)
{
    log_debug("set prps sample interval: %d.", iInterval);
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(iInterval, APPConfig::KEY_PRPS_SAMPLE_INTERVAL);
    pConfig->endGroup();
    return;
}

/******************************************************
 * 功能：获取PRPS采样间隔，单位ms
 * 返回值：
 *      int：采样间隔，单位ms
 * **********************************************************/
int SystemSetService::getPRPSSampleInterval()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    int iInterval = pConfig->value(APPConfig::KEY_PRPS_SAMPLE_INTERVAL).toInt();
    pConfig->endGroup();

    // 防止异常数据
    iInterval = (iInterval < 10) ? 20 : iInterval;
    iInterval = (iInterval > 100) ? 20 : iInterval;

    log_debug("get prps sample interval: %d.", iInterval);

    return iInterval;
}

/******************************************************
 * 功能：设置PRPS自动同步
 * 输入参数：
 *      bAutoSync：是否同步
 * **********************************************************/
void SystemSetService::setPRPSAutoSync(bool bAutoSync)
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(bAutoSync, APPConfig::KEY_AUTO_SYNC);
    pConfig->endGroup();
}

/******************************************************
 * 功能：获取PRPS自动同步
 * 返回值：
 *      是否同步
 * **********************************************************/
bool SystemSetService::getPRPSAutoSync()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    bool bAutoSync = pConfig->value(APPConfig::KEY_AUTO_SYNC).toInt();
    pConfig->endGroup();

    return bAutoSync;
}

/******************************************************
 * 功能：设置文件备注框使能
 * 输入参数：
 *      bEnable：是否开启
 * **********************************************************/
void SystemSetService::setFileCommentBoxEnabled(bool bEnable)
{
    ConfigInstance *pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    pConfig->setValue(bEnable, APPConfig::KEY_FILE_COMMENT_BOX);
    pConfig->endGroup();
}

/******************************************************
 * 功能：获取文件备注框使能状态
 * 返回值：
 *      是否使能
 * **********************************************************/
bool SystemSetService::isFileCommentBoxEnabled()
{
    ConfigInstance* pConfig = ConfigManager::instance()->config();
    pConfig->beginGroup(Module::GROUP_APP);
    bool bEnable = pConfig->value(APPConfig::KEY_FILE_COMMENT_BOX).toInt();
    pConfig->endGroup();

    return bEnable;
}

/******************************************************
 * 功能：获取温湿度信息
 * 输出参数：
 *      fTemperature：温度
 *      fHumidity：湿度
 * **********************************************************/
bool SystemSetService::getTemperatureHumidityInfo(float& fTemperature, float& fHumidity)
{
    bool bResult = false;
#ifdef Q_PROCESSOR_ARM
    EnvInfo stEnvInfo;
    memset(&stEnvInfo, 0, sizeof(stEnvInfo));

    if (0 == get_enviroment_info(&stEnvInfo))
    {
        bResult = true;

        int iPrecious = 1;
        fTemperature = Module::dealFloatPrecision(stEnvInfo.fTemperature, iPrecious);
        fHumidity = Module::dealFloatPrecision(stEnvInfo.fHumidity, 0);
    }
#endif

    return bResult;
}
