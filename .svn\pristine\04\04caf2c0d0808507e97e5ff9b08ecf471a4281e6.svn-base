﻿/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* ChartView.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月7日
* 修改日期：2016年11月14日
*       重构
* 摘要：图表视图基类声明

* 当前版本：1.0
*/
#ifndef CHARTVIEW_H
#define CHARTVIEW_H
#include <QWidget>
#include "titlebar/TitleBar.h"
#include "chartwidget/ChartWidget.h"
#include "buttonBar/PushButtonBar.h"
#include "buttonBar/LabelButtonBar.h"
#include "pushButton/PushButton.h"
#include "Widget.h"

struct ChartViewPrivate;
class WIDGET_EXPORT ChartView : public Widget
{
    Q_OBJECT
public:
    /****************************
    功能： 构造函数
    输入参数:
        strTitle -- 标题
        parent -- 父窗体
    *****************************/
    explicit ChartView(const QString& strTitle, QWidget *parent = 0);

    /****************************
    功能： 析构函数
    *****************************/
    ~ChartView();

    /****************************
    功能： 设置标题
    输入参数:
        strTitle:标题
    *****************************/
    void setTitle( const QString& strTitle );

    /****************************
    功能： 获取标题
    返回值： 标题
    *****************************/
    QString title();

    /****************************
    功能： 获取标题栏
    返回值： 标题栏
    *****************************/
    TitleBar* titleBar( void );

    /****************************
    功能： 连接标题栏点击信息
    *****************************/
    void connectTitleClicked();

    /****************************
    功能： 断开连接标题栏点击信息
    *****************************/
    void disconnectTitleClicked();

    /****************************
    功能： 设置图表
    输入参数:
        pChart:图表
    *****************************/
    void setChart( ChartWidget* pChart );

    /****************************
    功能： 获取图谱控件
    返回值： 图谱控件
    *****************************/
    ChartWidget* chart( void );

    /*************************************************
    功能： 创建控制按钮栏
    输入参数:
        pchContext -- 国际化用的域
        pInfos -- 按钮配置信息
        iCount -- 按钮个数
        iColumnCount -- 列数
        mode -- 显示模式
    返回值：创建的ButtonBar
    *************************************************************/
    PushButtonBar* createButtonBar( const char* const pchContext,
                                    const ButtonInfo::Info* pInfos,
                                    int iCount,
                                    int iColumnCount = 4,
                                    PushButton::Mode mode = PushButton::TEXT_ONLY );

    /*************************************************
    功能： 更新控制按钮栏
    输入参数:
        pchContext -- 国际化用的域
        pInfos -- 按钮配置信息
        iCount -- 按钮个数
        iColumnCount -- 列数
        mode -- 显示模式
    返回值：更新后的ButtonBar
    *************************************************************/
    void updateButtonBar( const char* const pchContext, const ButtonInfo::Info* pInfos,
                          int iCount,
                          int iColumnCount = 4,
                          PushButton::Mode mode = PushButton::TEXT_ONLY);

    /*************************************************
    功能： 创建控更多信息按钮栏
    输入参数:
        pchContext -- 国际化用的域
        pInfos -- 按钮配置信息
        iCount -- 按钮个数
        qstrTitile -- 更多界面标题名称，为空时是More...
    返回值：创建的ButtonBar
    *************************************************************/
    LabelButtonBar* createLabelButtonBar(const char* const pchContext, const ButtonInfo::Info* pInfos, int iCount, QString qstrTitile = "");

    /*************************************************
    功能： 获取控制按钮栏
    返回值： 控制按钮栏
    *************************************************************/
    PushButtonBar* buttonBar( void );

    /*************************************************
    功能： 截屏
    返回值：
            截屏的图谱
    *************************************************************/
    QPixmap screenShot() const;

    /*************************************************
    功能： 收起所有弹出按键
    *************************************************************/
    void closeAllButtons();

    /*************************************************
    功能： 设置当前激活的按钮
    *************************************************************/
    void setCurActiveBtnID(int iBtnID);

    /*************************************************
     输入参数： qstrMsg -- 提示信息
     输出参数： NULL
     返回值： NULL
     功能： 显示等待对话框
     *************************************************************/
    virtual void showWaitingDialog(QString qstrMsg);

signals:
    /*************************************************
    功能： 信号，鼠标点击
    *************************************************************/
    void sigPressed();

    /*************************************************
    功能： 信号，等待操作完成
    *************************************************************/
    void sigWaitOperFinished();

public slots:

    /*************************************************
    功能： 槽，为了保证点击标题栏关闭界面时，能够收起所有弹出按键
    *************************************************************/
    void onClose( void );

protected:
    /*************************************************
    功能： 重载鼠标点击事件
    输入参数：
            event -- 事件
    *************************************************************/
    void mousePressEvent( QMouseEvent* event );
    /*************************************************
    功能： 槽，响应按钮值变化事件
    输入参数：
            id -- 按钮ID
            iValue -- 按钮值
    *************************************************************/
    virtual void onButtonValueChanged( int id, int iValue );

    /*************************************************
    功能： 槽，响应命令按钮按下事件
    输入参数：
            id -- 按钮ID
    *************************************************************/
    virtual void onCommandButtonPressed( int id );



private:
    struct ChartViewPrivate* d;
};

#endif // CHARTVIEW_H

