/*
* Copyright (c) 2016.11，南京华乘电气科技有限公司
* All rights reserved.
*
* AEServicePrivate.h
*
* 初始版本：1.0
* 作者：邵震宇
* 创建日期：2016年11月24日
* 摘要：AE服务模块接口私有定义
*      实现AE服务模块私有化的一些功能定义
* 当前版本：1.0
*/
#ifndef AESERVICEPRIVATE_H
#define AESERVICEPRIVATE_H
#include <QObject>
#include <QThread>
#include <QMutex>
#include <QTimerEvent>
#include "model/HCAffair.h"
#include "datadefine.h"
#include <UHFHFCTAETEVApi.h>
#include "AEServiceStrategy.h"
#include "AEService.h"
#include "alsasoundApi.h"
#include "../peripheral/peripheralservice.h"
//AE事务
typedef enum _AEAffair
{
    START_SAMPLE = 0,//启动采样
    STOP_SAMPLE,//停止采样
    SET_WORKSET,//设置工作参数
    SET_STRATEGY,//设置数据处理和采样控制策略
    SET_CHANNEL, //设置通道类型
    AE_AFFAIR_COUNT
}AEAffair;


#define GET_API_CHANNEL( eChannel ) ((AE_CHANNEL)(eChannel) )//将AEService的Channel类型转换成底层的Channel类型
#define GET_API_WORKMODE( eWorkMode ) ((WorkMode)(eWorkMode) )//将AEService的WorkMode类型转换成底层的WorkMode类型


class AEServicePrivate : public QObject
{
    Q_OBJECT
public:
    typedef enum _Info
    {
#ifdef Q_PROCESSOR_ARM
        READ_DATA_INTERVAL = 50,//读数据间隔为5MS
#else
        READ_DATA_INTERVAL = 100,//PC模拟间隔100MS
#endif
    }Info;

public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent -- 父Object
    *************************************************/
    AEServicePrivate( QObject* parent );

    /*************************************************
    功能： 析构函数
    *************************************************/
    ~AEServicePrivate();

    /*************************************************
    功能： 设置工作参数
    返回：
        true -- 成功
        false -- 失败
    *************************************************/
    bool setWorkSet();

    /*************************************************
    功能： 设置增益
    输入参数：
            eGain -- 增益
    *************************************************/
    void setGain( AE::GainType eGain );

    /*************************************************
    功能： 设置通道类型
    输入参数：
            eChannel -- 通道类型
    *************************************************/
    bool setChannel( AE::ChannelType eChannel );

	/*************************************************
    功能： 设置带宽
    输入参数：
            eFilter -- 带宽
    *************************************************/
    void setFilter( AEFilter eFilter );
    /*************************************************
    功能： 设置触发值
    输入参数：
            eTriggerValue -- 触发值
    *************************************************/
    void setTriggerValue( AE::TriggerValue eTriggerValue );

    /*************************************************
    功能： 设置时间间隔
    输入参数:
        iTimeInterval -- 时间间隔
    返回：
        void
    *************************************************************/
    void setTimeInterval(quint32 iTimeInterval);

    Module::SyncState syncState();

    AE::ChannelType channelType();

    void resetSoundGainWorkPara();
private slots:
    /*************************************************
    功能： 槽，处理异步事务
    输入参数:
        usAffair -- 事务
        pInfo -- 存放结果数据指针
        usID -- 当前事务的序号
    *************************************************/
    void onAffair( quint16 usAffair, void* pInfo, quint16 usID  );

    /*************************************************
    功能： 槽，处理异步事务
    输入参数:
        channel -- 通道
    *************************************************/
    void onChannelChanged(AE::ChannelType channel);

private:
    /*************************************************
    功能： 开始采集
    *************************************************/
    void startSample();

    /*************************************************
    功能： 停止采集
    *************************************************/
    void stopSample();

    /*************************************************
    入参：eMode -- 工作模式
    功能： 设置数据和采样服务策略
    *************************************************/
    void setServiceStrategy( AE::WorkMode eMode );

    /*************************************************
    功能： 重新设置升级触发门槛
    输入参数：
            eGain -- 增益
            eTriggerValue -- 触发值
    *************************************************/
    void resetThreshold( AE::GainType eGain, AE::TriggerValue eTriggerValue );

    /*************************************************
    功能： 界面增益与驱动噪声增益值转换
    输入参数：
            eGain -- 增益
            eTriggerValue -- 触发值
    *************************************************/
    AESoundGain setAEInterfaceGainToSoundGain( AE::GainType eGain);

    /*************************************************
    功能： 设置时间间隔
    输入参数：
            eGain -- 增益
            eTriggerValue -- 触发值
    *************************************************/
public://成员
    QThread* m_pThread;//执行线程
    HCAffair* m_pAffair;//事务模块
    QMutex m_mutexThread;//线程操作锁
    QMutex m_mutexWorkset;//工作参数锁

    AEWorkSetForUp m_workSet;//工作参数

    quint32 m_iTimeInterval;
    AE::TriggerValue m_eTriggerValue;//触发值
    AE::GainType m_eGain;//增益
    AE::UnitOption m_eUnit;//量纲
    AE::ChannelType m_eChannel; //通道
    AEFilter m_eFilter; //带宽模式
    AEReadData m_dataRead;//读取的数据

    bool m_bTransaction;//事务模式
    int m_iTimerReadData;//定时器ID
    AEService* p;//parent事务
    AEServiceStrategy* m_pAEServiceStrategy; // 采样和数据处理模块
};

#endif // AESERVICEPRIVATE_H

