#include "guideinfraredimagewidget.h"
#include <QPainter>
#include <QDebug>
#include <QTime>
#include <QtConcurrentRun>

GuideInfraredImageWidget::GuideInfraredImageWidget(QWidget* pParent)
    : QWidget(pParent),
      m_eCurrentPlaybackState(GuideInfrared::PlaybackInfraredPhoto),
      m_eGuideInfraredDisplayMode(GuideInfrared::DISPLAY_INFRARED)
{

}

QSize GuideInfraredImageWidget::sizeHint() const
{
    return minimumSize();
}

void GuideInfraredImageWidget::setDisplayMode(GuideInfrared::GuideInfraredDisplayMode eGuideInfraredDisplayMode)
{
    if (eGuideInfraredDisplayMode != m_eGuideInfraredDisplayMode)
    {
        m_eGuideInfraredDisplayMode = eGuideInfraredDisplayMode;
        update();
    }
}

void GuideInfraredImageWidget::setInfraredRgbPixmap(QSharedPointer<QPixmap> qspInfraredRgbPixmap)
{
//    static QTime rgbsetbackTime2;
//    static int iRgbSetbackCount2 = 0;
//    if (0 == iRgbSetbackCount2)
//    {
//        rgbsetbackTime2.restart();
//    }
//    ++iRgbSetbackCount2;
//    int iTime = rgbsetbackTime2.elapsed();
//    if (iTime >= 1000)
//    {
//        qDebug() << "----GuideInfraredImageWidget::setInfraredRgbPixmap: " << ++iRgbSetbackCount2 << " time: " << iTime;
//        iRgbSetbackCount2 = 0;
//    }
    m_qspInfraredRgbPixmap = qspInfraredRgbPixmap;
    if (GuideInfrared::DISPLAY_DIGITAL_CAMERA != m_eGuideInfraredDisplayMode && GuideInfrared::PlaybackInfraredPhoto == m_eCurrentPlaybackState)
    {
        update();
    }
}

void GuideInfraredImageWidget::setInfraredRgbImage(QSharedPointer<QImage> qspInfraredRgbImage)
{
//    static QTime rgbsetbackTime1;
//    static int iRgbSetbackCount1 = 0;
//    if (0 == iRgbSetbackCount1)
//    {
//        rgbsetbackTime1.restart();
//    }
//    ++iRgbSetbackCount1;
//    int iTime = rgbsetbackTime1.elapsed();
//    if (iTime >= 1000)
//    {
//        qDebug() << "----GuideInfraredImageWidget::setInfraredRgbImage: " << ++iRgbSetbackCount1 << " time: " << iTime;
//        iRgbSetbackCount1 = 0;
//    }
    m_qspInfraredRgbImage = qspInfraredRgbImage;
    if (GuideInfrared::DISPLAY_DIGITAL_CAMERA != m_eGuideInfraredDisplayMode && GuideInfrared::PlaybackInfraredPhoto == m_eCurrentPlaybackState)
    {
        update();
    }
}

void GuideInfraredImageWidget::setVisibleLightPixmap(QSharedPointer<QPixmap> qspVisibleLightPixmap)
{
    m_qspVisibleLightPixmap = qspVisibleLightPixmap;
    if (GuideInfrared::DISPLAY_INFRARED != m_eGuideInfraredDisplayMode)
    {
        update();
    }
}

void GuideInfraredImageWidget::playbackVisibleLightPhoto()
{
    m_eCurrentPlaybackState = GuideInfrared::PlaybackVisibleLightPhoto;
    update();
}

void GuideInfraredImageWidget::playbackInfraredPhoto()
{
    m_eCurrentPlaybackState = GuideInfrared::PlaybackInfraredPhoto;
    update();
}

void GuideInfraredImageWidget::paintEvent(QPaintEvent* pEvent)
{
//    static QTime rgbCallbackTime;
//    static int iRgbCallbackCount = 0;
//    if (0 == iRgbCallbackCount)
//    {
//        rgbCallbackTime.restart();
//    }
//    ++iRgbCallbackCount;
//    int iTime = rgbCallbackTime.elapsed();
//    if (iTime >= 1000)
//    {
//        qDebug() << "----GuideInfraredImageWidget::paintEvent: " << ++iRgbCallbackCount << " time: " << iTime;
//        iRgbCallbackCount = 0;
//    }

    //qDebug() << "GuideInfraredImageWidget::paintEvent1: " << QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    QPainter painter(this);
#if 0
    if (GuideInfrared::PlaybackInfraredPhoto == m_eCurrentPlaybackState)
    {
        {
            //QReadLocker locker(&m_lock);
            if (m_qspInfraredRgbPixmap)
            {
                //painter.setRenderHint(QPainter::SmoothPixmapTransform);

//                QPixmap pixmap = QPixmap::fromImage(*m_qspInfraredRgbImage.data());
//                //pixmap = pixmap.scaled(QSize(640, 480));
//                painter.drawPixmap(rect(), pixmap);

                painter.drawPixmap(rect(), *m_qspInfraredRgbPixmap.data());
            }
            else
            {
                painter.fillRect(rect(), QColor(0, 0, 0));
            }
        }
    }
    else
    {
        if (!m_invisiblePhotoImage.isNull())
        {
            painter.drawImage(rect(), m_invisiblePhotoImage);
        }
        else
        {
            painter.fillRect(rect(), QColor(0, 0, 0));
        }
    }
#endif

    if (GuideInfrared::DISPLAY_INFRARED == m_eGuideInfraredDisplayMode)
    {
        if (GuideInfrared::PlaybackInfraredPhoto == m_eCurrentPlaybackState)
        {
            if (m_qspInfraredRgbPixmap)
            {
                painter.drawPixmap(rect(), *m_qspInfraredRgbPixmap.data());
            }
            else
            {
                painter.fillRect(rect(), QColor(0, 0, 0));
            }
        }
        else
        {
            if (m_qspVisibleLightPixmap)
            {
                painter.drawPixmap(rect(), *m_qspVisibleLightPixmap.data());
            }
            else
            {
                painter.fillRect(rect(), QColor(0, 0, 0));
            }
        }
    }
    else if (GuideInfrared::DISPLAY_PICTURE_IN_PICTURE == m_eGuideInfraredDisplayMode)
    {
        if (m_qspVisibleLightPixmap)
        {
            painter.drawPixmap(rect(), *m_qspVisibleLightPixmap.data());
        }

        if (m_qspInfraredRgbPixmap)
        {
            painter.drawPixmap(QRect(width() / 4, height() / 4, width() / 2, height() / 2),
                               *m_qspInfraredRgbPixmap.data(),
                               QRect(m_qspInfraredRgbPixmap->width() / 4, m_qspInfraredRgbPixmap->height() / 4, m_qspInfraredRgbPixmap->width() / 2, m_qspInfraredRgbPixmap->height() / 2));
        }
    }
    else if (GuideInfrared::DISPLAY_DIGITAL_CAMERA == m_eGuideInfraredDisplayMode)
    {
        if (m_qspVisibleLightPixmap)
        {
            painter.drawPixmap(rect(), *m_qspVisibleLightPixmap.data());
        }
        else
        {
            painter.fillRect(rect(), QColor(0, 0, 0));
        }
    }
    //qDebug() << "GuideInfraredImageWidget::paintEvent2: " << QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
}
