/*
* Copyright (c) 2016.2，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：QtCrypto.h
*
* 初始版本：1.0
* 作者：
* 创建日期：2016年2月26日
* 摘要：该文件主要是定义加密模块
*
*/

#ifndef QTCRYPTO_H
#define QTCRYPTO_H
#include <QByteArray>


/************************************************
 * 函数名    :initCrypto
 * 输入参数  ：
 *      strKey -- 密钥
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：初始化加密模块函数
 ************************************************/
#ifdef Q_OS_LINUX
extern "C" void initCrypto( char* pchKey );
#else
extern "C" __declspec(dllexport) void __stdcall initCrypto( char* pchKey );
#endif


/************************************************
 * 函数名    :enCrypt
 * 输入参数  ：
 *      data -- 被加密数据
 * 输出参数  ：NULL
 * 返回值   ：
 *      加密后数据
 * 功能     :加密
 ************************************************/
extern "C" QByteArray enCrypt( const QByteArray& data );

/************************************************
 * 函数名    :deCrypt
 * 输入参数  ：
 *      data -- 被解密数据
 * 输出参数  ：NULL
 * 返回值   ：
 *      解密后数据
 * 功能     :解密
 ************************************************/
extern "C" QByteArray deCrypt( const QByteArray& data );


#endif // QTCRYPTO_H

