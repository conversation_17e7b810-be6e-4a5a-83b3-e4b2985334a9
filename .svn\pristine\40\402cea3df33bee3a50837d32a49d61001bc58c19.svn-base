﻿/*
* Copyright (c) 2017.05，南京华乘电气科技有限公司
* All rights reserved.
*
* httpbean.h
*
* 初始版本：1.0
* 作者：陈康
* 创建日期：2017年05月22日
* 摘要：与巡检云端接口访问管理与协议解析

* 当前版本：1.0
*/
#ifndef CLOUDSERVICE_H
#define CLOUDSERVICE_H

#include <QObject>
#include <QString>
#include <QByteArray>

#include "httpbean.h"
#include "pda.h"
#include "cloud/cloudprotocol.h"
#include "module_global.h"
#include "globalerrprocess.h"
using namespace errorProcess;

typedef struct _UploadCMSFileInfo_
{
    QString qstrFileName;
    QString qstrFileType;
    QString qstrType;
    QString qstrMd5Code;
    QByteArray qbaFileContent;

    _UploadCMSFileInfo_()
    {
        qstrFileName = "";
        qstrFileType = "";
        qstrType = "";
        qstrMd5Code = "";
        qbaFileContent.clear();
    }
}UploadCMSFileInfo;

class CloudService : public QObject
{
    Q_OBJECT
public:
    explicit CloudService(QObject *parent = 0);

    /*************************************************
    函数名：
    输入参数: strUser--用户名
             strPwd--密码
    输出参数：NULL
    返回值：ReplyCode
    功能：登录云服务
    *************************************************************/
    virtual ReplyCode requestLogin(const QString &strUser, const QString &strPwd);

    /*************************************************
    函数名：
    输入参数: NULL
    输出参数：NULL
    返回值：ReplyCode
    功能：校验认证
    *************************************************************/
    virtual ReplyCode checkToken();

    /*************************************************
    函数名：
    输入参数: taskInfo--任务的基本信息
             uiTimeOut--超时时间
    输出参数：baFileData---任务文件的数据
    返回值：ReplyCode
    功能：下载任务
    *************************************************************/
    virtual ReplyCode downloadTask(TaskInfo &taskInfo, QByteArray &baFileData, UINT32 uiTimeOut = 30000);

    /*************************************************
    函数名：downloadImgMediaFiles
    输入参数: strTaskFilePath--任务描述文件
    输出参数：NULL
    返回值：ReplyCode
    功能：下载任务描述文件的音频与图片文件
    *************************************************************/
    virtual ReplyCode downloadImgMediaFiles(const QString &strTaskFilePath);

    /*************************************************
    函数名：
    输入参数: taskInfo--任务基本信息
             files--任务所有要上传的数据文件
    输出参数：NULL
    返回值：是否上传成功
    功能：上传指定的测试数据文件
    *************************************************************/
    virtual ReplyCode uploadTask(const TaskInfo& taskInfo, const QList<TestFileInfo> &files, UINT32 uiTimeOut = 30000);

    /*************************************************
    函数名：
    输入参数: baData--进行云诊断的数据
    输出参数：strResult--诊断结果
    返回值： 诊断是否成功
    功能：将当前的测试数据进行cms云诊断
    *************************************************************/
    virtual bool cloudDiagnosis(const QString &qsSavedFilePath, QString& strResult, QString &strCode, UINT32 uiTimeOut = 10000);

    /*************************************************
    函数名：
    输入参数: qsTaskNum--要下载的任务编号
    返回值：ReplyCode
    功能：请求单个接受任务
    *************************************************************/
    virtual ReplyCode acceptTask(const QString &qstrInrId, UINT32 uiTimeOut = 5000);

    /*************************************************
    函数名：
    输入参数: qlTaskNumbers--要下载的任务编号
    输出参数：acceptedTasks---容器， 存放被接受任务的编号和索引
    返回值：ReplyCode
    功能：请求批量接受任务
    *************************************************************/
    virtual ReplyCode acceptTasks(const QStringList &qlInnerIds, QMap<QString, UINT32> &acceptedTasks, UINT32 uiTimeOut = 10000);

    /*************************************************
    函数名：
    输入参数: strUser--任务查询的用户名
             uiTimeOut--查询的超时时间
    输出参数：qvTaskInfoList--cms上查询的信息列表
    返回值：ReplyCode
    功能：获取任务信息
    *************************************************************/
    virtual ReplyCode getTaskLists(const QString &strUser, QVector<TaskInfo> &qvTaskInfoList, UINT32 uiTimeOut = 30000);

    /************************************************************
     * 功能：上传快速巡检的数据信息
     * 输入参数：
     *      qstrTaskDir：任务所在的文件夹
     *      qstrlstFilePaths：需要上传的所有文件集合
     *      iTimeout：网络通信超时时间
     * 返回值：
     *      执行结果
     * ***********************************************************/
    virtual ReplyCode uploadFastPatrolInfo(const QString qstrTaskDir, const QStringList &qstrlstFilePaths, int iTimeout = 30000);

    /*************************************************
    函数名：
    输入参数: NULL
    输出参数：NULL
    返回值：
    功能：停止下载
    *************************************************************/
    virtual void stopDownload();

    /*************************************************
    函数名：
    输入参数: NULL
    输出参数：NULL
    返回值：
    功能：停止上传
    *************************************************************/
    virtual void stopUpload();

    /***********************************************
     * 功能：停止网络请求
     * ***********************************************/
    virtual void stopRequest();

    /*************************************************
    函数名：
    输入参数: strID--内部标识号
    输出参数：NULL
    返回值：
    功能：获取任务编号接口
    *************************************************************/
    QUrl getTestNumberUrl(const QString& strID);

    /*************************************************
    函数名：
    输入参数: taskInfo--任务的基本信息
             uiTimeOut--超时时间
    输出参数：NULL
    返回值：ReplyCode
    功能：获取任务编号
    *************************************************************/
    ReplyCode getTestNumber(const TaskInfo &taskInfo, QString & strTestNumber, UINT32 uiTimeOut = 5000);

    /*************************************************
    函数名：
    输入参数: taskFilter--筛选参数
    输出参数：NULL
    返回值： NULL
    功能：设置云端获取任务列表的过滤项
    *************************************************************/
    void setCloudTaskFilter(const TaskFilter& taskFilter);

    /*************************************************
    功能：清空云端获取任务列表的过滤项
    *************************************************************/
    void cleanCloudTaskFilter();

    /*************************************************
    函数名：
    输入参数: qbaFileData--进行urlencode的数据
    输出参数：NULL
    返回值： 编码后的数据
    功能：对数据进行url编码
    *************************************************************/
    static QByteArray dataFile2ByteArray(const QByteArray& qbaFileData);

    /*************************************************
    函数名：readCloudConfig
    输入参数: NULL
    输出参数：NULL
    返回值：NULL
    功能：读取云配置信息(ip,端口)
    *************************************************************/
    void readCloudConfig();

    /*************************************************************************
     * 功能：获取完整的url
     * 输入参数：
     *      qstrPartUrl：部分url
     * 返回值：
     *      QString：完整的url
     * ***********************************************************************/
    QString getWholeRequestUrl(QString qstrPartUrl);

signals:
    /*************************************************
    任务上传时的进度
    *************************************************************/
    void sigUploadProcess(int iProcess);

    /*************************************************
    快速巡检任务上传时的进度
    *************************************************************/
    void sigUploadFastPatrolProcess(int iProcess);

private:
    /*************************************************
    函数名：
    输入参数: filePath--文件路径
             uiTimeout--超时时间
    输出参数：urlPath--云端存储的路径
    返回值：是否上传成功
    功能：上传完整文件
    *************************************************************/
    ReplyCode uploadWholeFile(const QString& filePath, QString& urlPath, bool bNeedDeCrypt = false, UINT32 uiTimeout = 30000);

    /*************************************************
    函数名：
    输入参数: fileContent--上传的文件内容
             uiTimeout--超时时间
    输出参数：urlPath--云端存储的路径
    返回值：ReplyCode
    功能：上传文件内容
    *************************************************************/
    ReplyCode uploadFileContent(const QByteArray& fileContent, const QString &fileName, const QString strSuffix, QString& urlPath,
                           CLOUD_PROTOCOL::FileType eType, UINT32 uiTimeout = 5000);

    //云端文件块合并
    ReplyCode mergeFileBlocks(const QStringList& urlNames, const QString& strSuffix, QString &urlPath, UINT32 uiTimeout = 10000);

    /*************************************************
    函数名：
    输入参数: qstrInrId--任务号
             urlPath--任务交互文件云端存储路径
             uiTimeout--超时时间
    输出参数：urlPath--云端存储的路径
    返回值：ReplyCode
    功能：上传任务信息
    *************************************************************/
    ReplyCode uploadTaskInfo(const QString& qstrInrId, const QString& urlPath, UINT32 uiTimeout = 10000);

    /*************************************************
    函数名：
    输入参数: queryItems--所用参数集合
    输出参数：url--需要填充的url
    返回值：
    功能：根据参数集合生成对应的URL
    *************************************************************/
    void addParamsToUrl(const QMap<QString, QString> &queryItems, QUrl &url);

    void addParamsToUrl_EX(const QMap<QString, QString> &queryItems, QUrl &url);

    /*************************************************
    函数名：
    输入参数: pageNo--获取任务列表的页号
            pageLen--一页的任务个数
    输出参数：url--需要填充的url
    返回值：
    功能：获取任务列表指令的url
    TODO 后期可能需要添加部分过滤参数
    *************************************************************/
    QUrl getTaskListsUrl(QHttpMultiPart &stMultiPart, int pageNo = 1, int pageLen = 10);


    //任务接受URL
    QUrl acceptTasksUrl(const QString &qsTaskNums, QByteArray &qbyaPostData);

    /*************************************************
    函数名：
    输入参数: fileContent--文件内容
             strSuffix--文件的后缀
             eType--文件功能代码
             qbyaPostData--post方式需要的json数据
    输出参数：url--需要填充的url
    返回值：
    功能：获取上传文件的url
    *************************************************************/
    QUrl getUploadFileUrl(const QByteArray& fileContent, const QString &strSuffix, CLOUD_PROTOCOL::FileType eType, QByteArray& qbyaPostData);

    /*************************************************
    函数名：
    输入参数: fileContent--文件内容
             strFileName--文件名
             strSuffix--文件的后缀
    输出参数： multiPart--需要填充的multiPart
    返回值：
    功能：获取上传文件信息的multiPart
    *************************************************************/
    void createUploadFileParts(const QByteArray& fileContent, const QString &strFileName, QHttpMultiPart &multiPart);

    void createUploadFileParts(const UploadCMSFileInfo &stUploadFileInfo, QHttpMultiPart &multiPart);

    /*************************************************
    函数名：
    输入参数: taskNum--任务编号
             urlPath--任务交互文件在云端的path
    输出参数：url--需要填充的url
    返回值：
    功能：获取任务上传的对应url
    *************************************************************/
    QUrl getUploadTaskUrl(const QString& taskNum, const QString& urlPath, QByteArray& qbyaPostData);

    /*************************************************
    函数名：
    输入参数: strUser--用户名
             strPwd--密码
    输出参数：NULL
    返回值：
    功能：获取登录的对应url
    *************************************************************/
    QUrl getRequestLoginUrl(const QString& strUser, const QString& strPwd);

    /*************************************************
    函数名：
    输入参数: strFileUrl-任务描述文件下载路径
    输出参数：NULL
    返回值：
    功能：下载任务文件
    *************************************************************/
    QUrl downloadFileUrl(const QString &strFileUrl, int currentBlockIndex, QHttpMultiPart &stMultiPart);

    /*************************************************
    函数名：
    输入参数: qsTaskNum--任务编号
    输出参数：NULL
    返回值：
    功能：获取下载任务的url
    *************************************************************/
    QUrl getDownloadTaskUrl(const QString &qsTaskNum, QHttpMultiPart &stMultiPart);

    /*************************************************
    函数名：
    输入参数: qsTaskNum--任务编号
    输出参数：NULL
    返回值：
    功能：获取下载任务的url
    *************************************************************/
    QUrl mergeFileUrl(const QStringList& urlNames, const QString& strSuffix, QByteArray& qbyaPostData);

    /*************************************************
    函数名：
    输入参数: baResponse--应答的内容
    输出参数：NULL
    返回值：ReplyCode
    功能：解析登录请求的应答
    *************************************************************/
    ReplyCode parseLoginResponse(const QByteArray &baResponse);

    /*************************************************
    函数名：
    输入参数: baResponse--应答的内容
    输出参数：acceptedTasks---容器， 存放被接受任务的编号和索引
    返回值：ReplyCode
    功能：解析批量接受任务的应答
    *************************************************************/
    ReplyCode parseAcceptTasksResponse(const QByteArray &baResponse, QMap<QString, UINT32> &acceptedTasks);

    /*************************************************
    函数名：
    输入参数: files--任务所有要上传的数据文件
            totalCount--文件总块数
            uiTimeOut--超时时间
    输出参数：oriUrlFileMap--数据文件名和返回path的map映射
    返回值：是否上传成功
    功能：上传测试数据文件
    *************************************************************/
    bool sendTestDataFile(const QList<TestFileInfo> &files,
                                        int totalCount, QMap<QString, QString> &oriUrlFileMap, UINT32 uiTimeOut);

    /*************************************************
    函数名：
    输入参数: taskPath--任务所有要上传的任务描述文件路径
             totalCount--文件总块数
             uiTimeOut--超时时间
    输出参数：taskUrlPath--上传任务描述文件所返回的path字段值
    返回值：是否上传成功
    功能：上传任务描述文件
    *************************************************************/
    ReplyCode sendTaskFile(const QString &taskPath, int totalCount, QString &taskUrlPath, UINT32 uiTimeOut);
    /*************************************************
    函数名：
    输入参数: baResponse--应答的内容
    输出参数：acceptedTasks---容器， 存放被接受任务的编号和索引
    返回值：ReplyCode
    功能：解析批量接受任务的应答
    *************************************************************/
    ReplyCode parseAcceptTaskResponse(const QByteArray &baResponse, QString qstrTaskNum);

    /*************************************************
    函数名：
    输入参数: baResponse--应答的内容
             expectPageNo--发送出去的页号
    输出参数：totalPage--总页数
            totalRecords--任务的总数
            taskInfos--保存获取的任务信息
    返回值：返回结果是否正常
    功能：解析请求任务列表反馈数据 解析结果添加到taskInfos中
    *************************************************************/
    ReplyCode parseQueryTaskListResponse(const QByteArray& baResponse, const int expectPageNo,
                                    int &totalPage, int &totalRecords,
                                    QVector<TaskInfo>& taskInfos);

    /*************************************************
    函数名：
    输入参数: baResponse--应答的内容
    输出参数：path--云端保存的地址
    返回值：ReplyCode
    功能：上传完整文件收到的应答解析
    *************************************************************/
    ReplyCode parseUploadWholeFileResponse(const QByteArray& baResponse, QString &path);

    /*************************************************
    函数名：
    输入参数: baResponse--应答的内容
             taskNum--发送的任务号
    输出参数：NULL
    返回值：返回结果是否正常
    功能：任务上传收到的应答解析
    *************************************************************/
    ReplyCode parseUploadTaskResponse(const QByteArray& baResponse, const QString& taskNum);

    /*************************************************
    函数名：
    输入参数: baResponse---response
    输出参数：qsFileUrl---任务文件存储在云端的url
    返回值：ReplyCode
    功能：解析下载任务应答，读出任务文件存储在云端的url
    *************************************************************/
    ReplyCode parseDownloadTaskResponse(const QByteArray &baRespone, QString &qsFileUrl);

    /*************************************************
    函数名：
    输入参数: baResult---含有result的json数据
    输出参数：totalBlockIndex---文件总块数
            currentBlockIndex---文件块索引
    返回值：ReplyCode
    功能：解析下载文件应答， 读取文件总块数和当前块索引
    *************************************************************/
    ReplyCode parseDownloadFileResponse(const QByteArray &baResult, int &totalBlockIndex, int &currentBlockIndex);

    /*************************************************
    函数名：
    输入参数: baResponse---response
    输出参数：qsFileUrl---任务文件存储在云端的url
    返回值：ReplyCode
    功能：解析下载任务应答，读出任务文件存储在云端的url
    *************************************************************/
    ReplyCode parseMergeFileResponse(const QByteArray &baRespone, QString &qsFileUrl);

    /*************************************************
    函数名：downloadFiles
    输入参数: NULL
    输出参数：stFilesPath--文件路径
    返回值：ReplyCode
    功能：下载任务描述文件的音频和图片文件
    *************************************************************/
    ReplyCode downloadFiles(ImgMediaPath &stFilesPath);

    /*************************************************
    函数名：downloadFile
    输入参数: uiTimeOut--超时时间
    输出参数：strFilePath--文件路径
    返回值：ReplyCode
    功能：下载任务描述文件的单个音频或图片文件
    *************************************************************/
    ReplyCode downloadFile(QString &strFilePath, UINT32 uiTimeOut = 10000);

    //权限认证服务接口

    /************************************************
     * 功能：权限认证服务登录接口
     * 输入参数：
     *      qstrUserNme -- 用户名
     *      qstrPwd -- 用户密码
     * 返回：
     *      ReplyCode -- 登录成功与否
     ************************************************/
    ReplyCode   loginToGetToken(const QString qstrUserNme, const QString qstrPwd);

    /************************************************
     * 功能：拼接权限认证登录url
     * 输入参数：
     *      qstrUserNme -- 用户名
     *      qstrPwd -- 用户密码
     * 返回：
     *      QUrl -- 登录url
     ************************************************/
    QUrl        getLoginGetTokenUrlAndParam(const QString qstrUserNme, const QString qstrPwd, QByteArray & byaPostData);

    /************************************************
     * 功能：解析权限登录数据接口
     * 输入参数：
     *      byaRespone -- 响应数据
     * 返回：
     *      ReplyCode -- 解析的code结果
     ************************************************/
    ReplyCode   parseLoginGetTokenResponse(const QByteArray &byaRespone);

    /************************************************
     * 功能：权限登出数据接口
     * 输入参数：
     *      无
     * 返回：
     *      无
     ************************************************/
    ReplyCode   logoutToReleaseToken();

    /************************************************
     * 功能：拼接权限登出接口url
     * 输入参数：
     *      无
     * 返回：
     *      QUrl -- 登出url
     ************************************************/
    QUrl        getLogoutUrlAndParam(QByteArray & byaPostData);

    /************************************************
     * 功能：解析权限登出数据接口
     * 输入参数：
     *      byaRespone -- 响应数据
     * 返回：
     *      ReplyCode -- 解析的code结果
     ************************************************/
    ReplyCode   parseLogoutResponse(const QByteArray &byaRespone);


protected:
    bool        m_bLogined;         //是否成功登录
    bool        m_bStopOp;          //是否停止当前操作 TODO还未使用
    bool        m_bFilterSetted;    //是否设置过滤项
    int         m_iHasSended;       //已经上传文件数
    int         m_iLoginType;

    QString     m_qstrAuthComNme;
    QString     m_qstrToken;
    QString     m_strCloudAddr;   //云端地址
    QString     m_strCloudIP;   //云端IP地址
    QString     m_strCloudPort; //云端端口号
    QString     m_authId;       //权限码

    TaskFilter  m_taskFilter;   //获取云端信息的过滤项
    HttpBean*   m_pHttpBean;    //进行http通信的组件
};

#endif // CLOUDSERVICE_H
