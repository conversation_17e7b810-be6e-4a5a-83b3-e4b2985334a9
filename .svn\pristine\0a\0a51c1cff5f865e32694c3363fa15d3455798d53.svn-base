/*
* Copyright (c) 2017.11，南京华乘电气科技有限公司
* All rights reserved.
*
* linetemperaturecurvedialog.h
*
* 初始版本：1.0
* 作者：周定宇
* 创建日期：2017年11月24日
* 摘要：线温度分布曲线对话框

* 当前版本：1.0
*/

#ifndef LINETEMPERATURECURVEDIALOG_H
#define LINETEMPERATURECURVEDIALOG_H

#include <QDialog>
#include <QGraphicsScene>

class LineTemperatureCurveDialog : public QDialog
{
    Q_OBJECT
public:
    /*************************************************
    函数名： LineTemperatureCurveDialog(const QVector<float> &datas, QWidget *parent = 0)
    输入参数： datas：温度数据
              parent：父指针
    输出参数： NULL
    返回值： NULL
    功能： 构造函数
    *************************************************************/
    LineTemperatureCurveDialog(const QVector<float> &datas, QWidget *parent = 0);


protected:
    /*************************************************
    函数名： eventFilter(QObject *obj, QEvent *e)
    输入参数： obj：事件发生的对象
              e：事件类型
    输出参数： NULL
    返回值： 操作结果，true - 直接过滤，false - 继续处理
    功能： 事件过滤器
    *************************************************************/
    bool eventFilter(QObject *obj, QEvent *e);


private:
    QGraphicsScene *m_pScene;
    QVector<float> m_datas;

};

#endif // LINETEMPERATURECURVEDIALOG_H
