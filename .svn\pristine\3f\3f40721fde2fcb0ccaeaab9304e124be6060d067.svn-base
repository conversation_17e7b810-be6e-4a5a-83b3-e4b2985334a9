#include "AEAMPWithoutMap.h"

/************************************************
 * 函数名   : AEAmpWithoutMap
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 构造函数
 ************************************************/
AEAmpWithoutMap::AEAmpWithoutMap()
{
    m_pAEAmpDataInfo = NULL;
}

/************************************************
 * 函数名   : saveData
 * 输入参数 : pDatas: 数据
 * 输出参数 : NULL
 * 返回值   : 数据文件名
 * 功能     : 保存数据到指定格式数据文件
 ************************************************/
QString AEAmpWithoutMap::saveData(void *pData)
{
    if(NULL == pData)
    {
        return NULL;
    }
    m_pAEAmpDataInfo = (AEAmpDataInfoWithoutMap*)pData;

    XMLDocument doc("AEValue");

    organizeData(doc);

    m_baData = doc.getByteArray();

    QString strData = m_baData;

    return strData;
}

/************************************************
 * 函数名   : getDataWithoutMap
 * 输入参数 : baData: xml格式数据流;
 * 输出参数 : pDatas: 数据
 * 返回值   : void
 * 功能     : 将xml格式数据提取到指定格式的结构体变量中
 ************************************************/
void AEAmpWithoutMap::getDataWithoutMap(const QByteArray& baData, void *pData)
{
    if(baData.isEmpty() || NULL == pData)
    {
        return;
    }

    parseData(baData, pData);
}

/************************************************
 * 函数名   : getDataByteArray
 * 输入参数 : void;
 * 输出参数 : NULL
 * 返回值   : xml格式数据流
 * 功能     : 获取xml格式数据流
 ************************************************/
QByteArray AEAmpWithoutMap::getDataByteArray(void)
{
    return m_baData;
}

/************************************************
 * 函数名   : getStringFromData
 * 输入参数 : pDatas: 数据; uiCounts: 数据个数
 * 输出参数 : NULL
 * 返回值   : 转换后的字符串
 * 功能     : 将数据转成base64的字符串
 ************************************************/
QString AEAmpWithoutMap::getStringFromData( void *pDatas, UINT32 uiCounts)
{
    Q_UNUSED(pDatas);
    Q_UNUSED(uiCounts);
    return NULL;
}

/************************************************
 * 函数名   : saveExtInformation
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储可扩展信息
 ************************************************/
void AEAmpWithoutMap::saveExtInformation(XMLDocument& doc)
{
    //站名
    doc.setValue("SubstationName",m_pAEAmpDataInfo->strSubstationName);
    //被测试设备名称
    doc.setValue("TestedDevName",m_pAEAmpDataInfo->strTestedDevName);
    //采样时间
    doc.setValue("SampleTime",convertTimeToSampleTimeFormat(m_pAEAmpDataInfo->dateTime));

    //超声通道类型
    doc.setValue("AEChannel",QString::number(m_pAEAmpDataInfo->eAEChannel));

    //放大器放大倍数
    doc.setValue("AmplifierAmplification",QString::number(m_pAEAmpDataInfo->eGainValue));
}

/************************************************
 * 函数名   : saveRawData
 * 输入参数 : doc: XML文件
 * 输出参数 : NULL
 * 返回值   : void
 * 功能     : 在XML文件中存储数据部分
 ************************************************/
void AEAmpWithoutMap::saveRawData(XMLDocument& doc)
{
    doc.setValue("Data", QString::number(m_pAEAmpDataInfo->cPeak));
}

/************************************************
 * 函数名   : parseData
 * 输入参数 : baData: 数据
 * 输出参数 : pData: 解析到的数据
 * 返回值   : void
 * 功能     : 解析数据
 ************************************************/
void AEAmpWithoutMap::parseData(const QByteArray& baData, void *pData, const QString &strFileName)
{
    Q_UNUSED(strFileName);
    m_pAEAmpDataInfo = (AEAmpDataInfoWithoutMap*)pData;
    XMLDocument doc(baData);
    m_pAEAmpDataInfo->cPeak = INT8(doc.value("Data").toInt());
    doc.beginElement("ExtInformation");
    m_pAEAmpDataInfo->strSubstationName = doc.value("SubstationName");
    m_pAEAmpDataInfo->strTestedDevName = doc.value("TestedDevName");
    m_pAEAmpDataInfo->dateTime = convertToDateTime(doc.value("SampleTime"));

    m_pAEAmpDataInfo->eAEChannel = (AE_CHANNEL)(doc.value("AEChannel").toInt());

    m_pAEAmpDataInfo->eGainValue = (GainValue)(doc.value("AmplifierAmplification").toInt());
}

/************************************************
 * 函数名   : getDataTypeFolder
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件夹名
 * 功能     : 获取图谱类型文件存储最上层文件夹名
 ************************************************/
QString AEAmpWithoutMap::getDataTypeFolder(void)
{
    return TEST_TABLE_FOLDER;
}

/************************************************
 * 函数名   : getFileNameSuffix
 * 输入参数 : void
 * 输出参数 : NULL
 * 返回值   : 文件后缀名
 * 功能     : 获取数据文件后缀名
 ************************************************/
QString AEAmpWithoutMap::getFileNameSuffix(void)
{
    return TEST_TABLE_FILE_NAME_SUFFIX;
}
