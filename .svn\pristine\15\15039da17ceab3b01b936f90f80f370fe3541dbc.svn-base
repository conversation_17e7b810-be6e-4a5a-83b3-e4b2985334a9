/*
* Copyright (c) 2019.10，南京华乘电气科技有限公司
* All rights reserved.
*
* distributenetaccess_def.h
*
* 初始版本：1.0
* 作者：张浪
* 修改日期：2021年01月04日
* 当前版本：1.0
* 摘要：
*
*/

#ifndef DISTRIBUTENETACCESS_DEF_H
#define DISTRIBUTENETACCESS_DEF_H

#include <QApplication>
#include <QObject>
#include <QString>
#include <QByteArray>
#include <QVector>

#define DISTRIBUTE_VIEW_CONFIG_TRANSLATE(str) qApp->translate("DistributeNetAccessNS", (str))

namespace DistributeNetAccessNS
{

const char* const CONTEXT = "DistributeNetAccessNS"; // 域
const char* const TEXT_POS_CABINET_FRONT = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "Cabinet Front");
const char* const TEXT_POS_CABINET_SIDE = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "Cabinet Side");
const char* const TEXT_POS_CABINET_BACK = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "Cabinet Back");
const char* const TEXT_TEV = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "TEV Detect");//AE
const char* const TEXT_AE = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "AE Detect");//AE
const char* const TEXT_UHF = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "UHF Detect");//UHF
const char* const TEXT_HFCT = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "HFCT Detect");//HFCT
const char* const TEXT_BGN = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "BKGD Detect");//BKGD
const char* const TEXT_TEVBGN = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "TEV BKGD");//TEV
const char* const TEXT_AEBGN = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "AE BKGD");//AE
const char* const TEXT_UHFBGN = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "UHF BKGD");//UHF
const char* const TEXT_HFCTBGN = QT_TRANSLATE_NOOP_UTF8("DistributeNetAccessNS", "HFCT BKGD");//HFCT


const char* const ARCHIVE_PATH = "/media/data/TestAccess/Archives"; // 档案路径
const char* const TASK_PATH = "/media/data/TestAccess/Tasks"; // 任务路径
const char* const COMPLETED_TASK_PATH = "/media/data/TestAccess/CompletedTasks"; // 完成任务路径
const char* const VIDEOS_PATH = "/media/data/videos"; // 视频路径

const char* const FILE_JSON_SUFFIX = ".json"; // json文件
const char* const FILE_ZIP_SUFFIX = ".zip"; // zip文件
const char* const FILE_DAT_SUFFIX = ".dat"; // dat文件
const char* const FILE_JSON_TYPE = "*.json"; // json文件类型

const int g_iLockWaitTime = 5000; // 锁等待时间

const char* const TEST_ARCHIVES = "testArchives"; // 测试档案信息集合
const char* const ARCHIVE_TYPE = "archiveType"; // 档案类型
const char* const ARCHIVE_ID = "archiveId"; // 档案id
const char* const ARCHIVE_NAME = "archiveName"; // 档案名称
const char* const MAIN_ASSETS = "mainAssets"; // 主设备信息集合
const char* const MAIN_ASSET_TYPE = "mainAssetType"; // 主设备类型
const char* const MAIN_ASSET_ID = "mainAssetId"; // 主设备名称
const char* const MAIN_ASSET_NAME = "mainAssetName"; // 主设备名称
const char* const SUB_ASSETS = "subAssets"; // 子设备信息集合
const char* const SUB_ASSET_TYPE = "subAssetType"; // 子设备类型
const char* const SUB_ASSET_ID = "subAssetId"; // 子设备名称
const char* const SUB_ASSET_NAME = "subAssetName"; // 子设备名称
const char* const SUB_ASSET_SN = "subAssetSN"; // 子设备名称测试顺序
//const char* const TEST_SN = "testSN"; // 测试顺序
const char* const TASK_ID = "taskId"; // 测试任务id
const char* const TASK_NAME = "taskName"; // 测试任务名称
const char* const TASK_TYPE = "taskType"; // 测试任务类型
const char* const CREATE_TIME = "createTime"; // 任务创建时间
const char* const TEST_TIME = "testTime"; // 任务测试时间
const char* const TEMPERATURE = "temperature"; // 温度
const char* const HUMIDITY = "humidity"; // 湿度
const char* const ASSET_INFOS = "assetInfos"; // 设备信息集合
const char* const ASSET_ID = "assetId"; // 设备id
const char* const ASSET_NAME = "assetName"; // 设备名称
const char* const ASSET_TYPE = "assetType"; // 设备类型
const char* const ASSET_SN = "assetSN"; // 测试顺序
const char* const TESTPOINTS = "testpoints"; //设备检测测点信息集合
const char* const TESTPOINT_TYPE = "testpointType"; // 测点类型
const char* const TESTPOINT_PART = "testpointPart"; // 测试部位
const char* const TESTPOINT_ID = "testpointId"; // 测点id
const char* const TESTPOINT_NAME = "testpointName"; // 测点名称
const char* const DIAGNOSIS_RESULT = "diagnosisResult"; // 测试诊断结果
const char* const FILE_NAME = "fileName"; // 数据文件名称
const char* const BGFILE_NAME = "bgFileName"; // 背景数据文件名称
const char* const TOTAL_CNT = "totalCount"; // 总的测试数
const char* const TESTED_CNT = "testedCount"; // 已测试的测试数
const char* const IS_TESTED = "isTested"; // 是否已测试标记



const char* const DATETIME_FORMAT_YYYYMMDDHHMMSSZZZ = "yyyyMMddHHmmsszzz"; // 时间格式
const char* const DATETIME_FORMAT_YYYY_MM_DD_HHMMSS_ZZZ = "yyyy-MM-dd HH:mm:ss.zzz"; // 时间格式
const char* const DATETIME_FORMAT_YYYY_MM_DD_HHMMSS = "yyyy-MM-dd HH:mm:ss"; // 时间格式



enum ArchiveType
{
    // 档案类型
    ARCHIVE_NONE = -1, // 未定义
    ARCHIVE_CIRCUIT = 0, // 线路
    ARCHIVE_SWITCHGEAR, // 开关房
};

enum AssetType
{
    // 设备类型
    ASSET_NONE = -1, // 未定义
    ASSET_TOWER = 0, // 杆塔
    ASSET_SWITCH_STATION, // 开关站
    ASSET_RING_NET_CABINET, // 环网柜
    ASSET_TRANSFORMER_IN_BOX, // 箱变内公变
    ASSET_PILLAR_TRANSFORMER, // 柱上变压器
    ASSET_DISTRIBUTE_STATION_CONTAINER, // 配电站容器
    ASSET_CABLE_DISTRIBUTE_BOX, // 电缆分接箱
    ASSET_OFFSITE_SWITCH, // 站外开关
    ASSET_LOAD_SWITCH, // 负荷开关
    ASSET_DISTRIBUTE_TRANSFORMER, // 配电变压器
    ASSET_CABLE_SECTION, // 电缆段
};

enum DefectType
{
    // 检测任务类型
    DEFECT_NONE = -1, // 未定义
    DEFECT_CIRCUIT = 0, // 线路检测
    DEFECT_SWITCHGEAR, // 开关房检测
};

enum TestpointType
{
    // 测试类型
    TPT_NONE = -1, // 未定义
    TPT_INFRARED = 1, // 红外测温
    TPT_AE, // 超声波
    TPT_TEV, // 暂态地电压
    TPT_UHF, // 特高频
    TPT_HFCT, // 高频电流
};

enum TestPosition
{
    // 测试位置
    TPP_NONE = 0, // 无特殊位置
    TPP_FRONT, // 前
    TPP_BACK, // 后
    TPP_TOP, // 上
    TPP_BOTTOM, // 下
    TPP_LEFT, // 左
    TPP_RIGHT, // 右
};

enum TestDiagnosisResult
{
    // 测试诊断结论
    TDR_NORMANL = 0, // 正常
    TDR_MINOR, // 一般
    TDR_SERIOUS, // 严重
    TDR_EMERGENCY, // 紧急
};

enum TaskTestState
{
    TASK_UNTEST = 0, // 任务未测
    TASK_TESTED, // 任务已测
    TASK_COMPLETED, // 测试已完成
};

struct RealAssetInfo
{
    // 真实（子）设备基础信息
    bool bBgTest; // 是否背景
    int iSN; // 顺序
    uint uiTotalCount; // 所有测试项总数
    uint uiTestedCount; // 该设备下已测节点个数
    AssetType eType; // 类型
    QString qstrId; // id
    QString qstrName; // 名称

    RealAssetInfo()
    {
        bBgTest = false;
        iSN = 0;
        uiTotalCount = 0;
        uiTestedCount = 0;
        eType = ASSET_NONE;
        qstrId = "";
        qstrName = "";
    }

};

struct MainAssetInfo
{
    // 主设备信息
    int iType; // 类型
    QString qstrId; // id
    QString qstrName; // 名称
    QVector<RealAssetInfo> qvtRealAssetInfos; // 真实设备信息集合

    MainAssetInfo()
    {
        iType = -1;
        qstrId = "";
        qstrName = "";
        qvtRealAssetInfos.clear();
    }

};

struct ArchiveInfo
{
    // 档案信息
    ArchiveType eType; // 类型
    QString qstrId; // id
    QString qstrName; // 名称
    QString qstrFilePath; // 文件路径
    QVector<MainAssetInfo> qvtMainAssetInfos; // 主设备信息集合

    ArchiveInfo()
    {
        eType = ARCHIVE_NONE;
        qstrId = "";
        qstrName = "";
        qstrFilePath = "";
        qvtMainAssetInfos.clear();
    }

    bool operator==(const DistributeNetAccessNS::ArchiveInfo &stOther) const
    {
        return (this->qstrId == stOther.qstrId);
    }
};

struct TestpointInfo
{
    // 测点信息
    bool bBgTestPoint; // 是否背景测点
    bool bTested; // 是否已测试的标记
    int iTestSN; // 测试顺序
    TestpointType eType; // 测点类型
    TestPosition ePos; // 测点位置
    TestDiagnosisResult eDiagRet; // 测试诊断结果
    QString qstrAssetId; // 所属设备的id
    QString qstrId; // id
    QString qstrName; // 名称
    QString qstrFileName; // 检测文件名
    QString qstrBgFileName; // 背景检测文件名

    TestpointInfo()
    {
        bBgTestPoint = false;
        bTested = false;
        iTestSN = 0;
        eType = TPT_NONE;
        ePos = TPP_NONE;
        eDiagRet = TDR_NORMANL;
        qstrAssetId = "";
        qstrId = "";
        qstrName = "";
        qstrFileName = "";
        qstrBgFileName = "";
    }

    bool operator==(const DistributeNetAccessNS::TestpointInfo &stOther) const
    {
        return (this->qstrId == stOther.qstrId);
    }

};

struct AssetInfo
{
    // 设备信息
    RealAssetInfo stRealAssetInfo; // 真实设备信息
    QVector<TestpointInfo> qvTestpointInfos; // 测点信息集合

    AssetInfo()
    {
        qvTestpointInfos.clear();
    }

};

struct TaskInfo
{
    // 任务信息
    QString qstrFilePath; // 文件路径
    QString qstrId; // id
    QString qstrName; // 名称
    QString qstrArchiveId; // 档案id
    QString qstrCreateTime; // 创建时间
    QString qstrTestTime; // 测试时间
    DefectType eType; // 检测类型
    uint uiTotalCount; // 所有测试项总数
    uint uiTestedCount; // 已测测试项数
    double dTemperature; // 温度
    double dHumidity; // 湿度
    QVector<AssetInfo> qvtAssetInfos; // 设备信息集合

    TaskInfo()
    {
        qstrFilePath = "";
        qstrId = "";
        qstrName = "";
        qstrArchiveId = "";
        qstrCreateTime = "";
        qstrTestTime = "";
        eType = DEFECT_NONE;
        uiTotalCount = 0;
        uiTestedCount = 0;
        dTemperature = 0;
        dHumidity = 0;
        qvtAssetInfos.clear();
    }

    bool operator==(const DistributeNetAccessNS::TaskInfo &stOther) const
    {
        return (this->qstrId == stOther.qstrId);
    }

};

struct DefectInfo
{
    // 检测信息
    TestDiagnosisResult eDiagRet; // 测试诊断结果
    QString qstrTaskId; // 任务id
    QString qstrAssetId; // 设备id
    QString qstrTestpointId; // 测点id
    QString qstrFileName; // 检测文件名
    QString qstrBgFileName; // 背景检测文件名
    QString qstrFilePath; // 检测文件路径
    QString qstrBgFilePath; // 背景检测文件路径

    DefectInfo()
    {
        eDiagRet = TDR_NORMANL;
        qstrTaskId = "";
        qstrAssetId = "";
        qstrTestpointId = "";
        qstrFileName = "";
        qstrBgFileName = "";
        qstrFilePath = "";
        qstrBgFilePath = "";
    }

};

struct EnvironmentInfo
{
    double dTemperature; // 温度
    double dHumidity; // 湿度

    EnvironmentInfo()
    {
        dTemperature = 0;
        dHumidity = 0;
    }

};



} // namespace DistributeNetAccessNS

#endif // DISTRIBUTENETACCESS_DEF_H
