/*
* Copyright (c) 2019.4，华乘电气科技有限公司
* All rights reserved.
*
* 文件名称：prpsFeatureCalculate.h
*
* 初始版本：1.0
* 作者：李遥
* 创建日期：2020年7月21日
* 摘要：prps数据的特征值计算
*/
#ifndef PRPS_FEATURE_CALCULATE_H
#define PRPS_FEATURE_CALCULATE_H

#include "featureGlobal.h"
#include <stdbool.h>

#define PRPS_FEATURE_NUM  (21)

/*************************************************
 * 函数功能： PRPS专家诊断
 * 输入参数：
 *      prps -- 输入数据
 *      iPhaseNum -- 输入数据的相位数
 *      iTNum -- 输入数据的周期数
 * 输出参数：
 *      pFeature -- 特征值
 *      iFeatureNum -- 特征值个数
 * 返回参数：
 *      计算是否成功true/false
*************************************************************/
FEATURE_EXPORT bool getFeatureByPRPS( double* prps, int iPhaseNum, int iTNum, double* pFeature, int* iFeatureNum );

/*****************************************************************
 * 功能：PRPS去噪
 *      输入输出的PRPS数据和阈值都是[0,1]范围。例如0.01，表示1%。
 * 输入参数：
 *      pPRPS -- PRPS数据x
 *      iPhaseNum -- 相位分区数
 *      iTNum -- 周期数
 *      bAutoThres -- 是否自动阈值
 *      dbThres -- 手动阈值
 * 输出参数：
 *      *pdbThresUse -- 实际使用的阈值
 *      ppdbPRPSOut -- 去噪后的PRPS数组
 * 返回参数：void
 ****************************************************************/
FEATURE_EXPORT void Denoise( double* pPRPS, int iPhaseNum, int iTNum, bool bAutoThres, double dbThres, double* pdbThresUse, double* pPRPSOut );

/*****************************************************************
 * 功能：自适应相位调整
 * 输入参数：
 *      pPRPS -- PRPS数据
 *      iPhaseNum -- 相位分区数
 *      iTNum -- 周期分区数
 * 输出参数：
 *      pdbPhaseShift -- 自动移动的相位，单位度
 *      pPRPSOut -- 自动调整相位后的PRPS数组
 * 返回参数：void
 ****************************************************************/
FEATURE_EXPORT void ProcessPhaseShift( double* pPRPS, int iPhaseNum, int iTNum, double* pdbPhaseShift, double* pPRPSOut );



#endif//PRPS_FEATURE_CALCULATE_H

