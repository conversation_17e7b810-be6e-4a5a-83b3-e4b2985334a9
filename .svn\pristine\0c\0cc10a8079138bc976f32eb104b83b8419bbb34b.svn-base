/*
* Copyright (c) 2016.05, 华乘电气科技有限公司
* All rights reserved.
*
* 文件名称: FastWaveUT.cpp
*
* 初始版本: 1.0
* 作者: 王谦
* 创建日期: 2016年05月6日
* 摘要: 该文件定义了百兆采集接入G100功能，u-t图谱
*/

#include <thirdparty/qwt/qwt_plot_zoomer.h>
#include <thirdparty/qwt/qwt_scale_draw.h>
#include <QDebug>
#include <QBitmap>
#include "FastWaveUT.h"
#include "../../CAView.h"
#include "../../../View.h"
#include "global_log.h"

const double XAXIS_MAX = 200;
const double XAXIS_MIN = 0;
const double XAXIS_STEP = 50;
//const double YAXIS_MAX = 2000;
//const double YAXIS_MIN = -2000;
const double YAXIS_STEP = 1000;

const QString Y_LEFT_TITLE = "U [ mv ]";
const QString Y_LEFT_TITLE_V = "U [ V ]";
const QString X_BOTTOM_TITLE = "t [ us ]";
const QString X_BOTTOM_TITLE_MS = "t [ ms ]";
const UINT16 CURVE_SIZE = 2;   // 曲线画笔尺寸
const double INTERVAL_COUNT = 600;   // 工频参考点的个数间隔
const QColor CURVE_DATA_COLOR = Qt::black; // 数据曲线的颜色
const UINT16 CURVE_DATA_SIZE = 2;       // 数据曲线的宽度
const UINT16 MOVE_EVENT_CNT = 2; // 定义当move事件累积的次数超过该值时再处理一次避免刷新过于频繁
const UINT16 INTERVAL_COUNTS = 20000;    // 工频参考周期为20ms
const QString PLOT_STYLE = "font-size:12px";

/************************************************
 * 函数名    :FastWaveUT
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：构造函数
 ************************************************/
FastWaveUT::FastWaveUT(QWidget *parent) :
    FastWaveBase(parent)
{
    dataInit();     // 初始化参数

    createUI();     // 创建界面元素

    //设置相应属性
    setStyleSheet( PLOT_STYLE );
    setXAxisTitle( X_BOTTOM_TITLE );    // 设置X轴标题
    //setYAxisTitle( Y_LEFT_TITLE );      // 设置Y轴标题

    setScale( FastWaveBase::X_SCALE,XAXIS_MIN,XAXIS_MAX,XAXIS_STEP );
    setScale( FastWaveBase::Y_SCALE,CA::CA_WAVE_AMP_LOWER_LIMIT,CA::CA_WAVE_AMP_UPPER_LIMIT,YAXIS_STEP );// 设置横、纵轴量程，间隔

    axisScaleDraw(QwtPlot::xBottom)->setTickLength(QwtScaleDiv::MinorTick, 0);
    axisScaleDraw(QwtPlot::yLeft)->setTickLength(QwtScaleDiv::MinorTick, 0);    // 设置量程分度

    setCurveStyle( QPen( CURVE_DATA_COLOR,CURVE_DATA_SIZE ) );// 设置数据的颜色，大小

    setFocusPolicy( Qt::NoFocus );

    createZoomBackButton();

    initReferenceCurve();       // 初始化工频参考数据

    //canvas()->installEventFilter( this );
    canvas()->setFocusPolicy( Qt::NoFocus );    // 将背景画布添加进事件过滤器，方便控制画布上的元素  
}

/************************************************
 * 函数名    :FastWaveUT
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：析构函数
 ************************************************/
FastWaveUT::~FastWaveUT()
{
    APP_CHECK_FREE( m_pRefrenceCurve )
    APP_CHECK_FREE( m_pMarker )
    APP_CHECK_FREE( m_pZoomer )
    APP_CHECK_FREE( m_pNodeInformation )
}

/************************************************
 * 函数名    :createUI
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：创建界面元素
************************************************/
void FastWaveUT::createUI( void )
{
    /*初始化工频参考*/
//    m_pRefrenceCurve = new QwtPlotCurve();
//    m_pRefrenceCurve->setZ(m_dMaxZ + 10);
//    m_pRefrenceCurve->setStyle(QwtPlotCurve::Lines);//直线形式
//    m_pRefrenceCurve->setPen( QPen( Qt::blue,CURVE_SIZE ) );
//    m_pRefrenceCurve->setRenderHint( QwtPlotCurve::RenderAntialiased,true );

    /*初始化标记线*/
//    m_pMarker = new autoMatker( XAXIS_MAX,XAXIS_MIN );
//    m_pMarker->setZ(m_dMaxZ + 30);
//    m_pMarker->setLinePen(Qt::red, CURVE_SIZE, Qt::DotLine);

    /*初始化交点信息*/
    m_pNodeInformation = new nodeInformation( m_dXmax,m_dXmin,m_dYmax,m_dYmin );
    m_pNodeInformation->attach( this );
    //仅仅是为了最上层显示
    m_pNodeInformation->setZ(m_dMaxZ + 30);

    //鼠标左键选择区域放大：（右键还原）
     m_pZoomer = new QwtPlotZoomer( canvas() );
     m_pZoomer->setRubberBandPen( QColor( Qt::black ) );
     m_pZoomer->setTrackerPen( QColor( Qt::black ) );
     m_pZoomer->setMousePattern(QwtEventPattern::MouseSelect2,Qt::RightButton, Qt::ControlModifier );
     m_pZoomer->setMousePattern(QwtEventPattern::MouseSelect3,Qt::RightButton );
     connect( m_pZoomer,SIGNAL(zoomed(QRectF)),this,SLOT(onZoomed(QRectF)) );
}

/************************************************
 * 函数名    :setDatas
 * 输入参数  ：xData -- x轴数据集合
 *           yData -- y轴数据集合
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：添加数据
 ************************************************/
void FastWaveUT::setDatas( const QVector< double > &xData, const QVector< double > &yData )
{
    if( xData.isEmpty() || yData.isEmpty() )
    {
        dbg_warning("FastWaveUT::setDatas Data is empty");
        return;
    }

    addSamples(xData, yData);
    m_vXdata = xData;
    m_vYdata = yData;

    adjustSizeByZoomStatus( m_eZoomedStatus );  // 根据最新的数据范围和放大状态调整界面显示尺寸
}

/************************************************
 * 函数名    :setDatas
 * 输入参数  ：xData -- x轴数据集合
 *           yData -- y轴数据集合
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：添加数据
 ************************************************/
void FastWaveUT::plotDatas( const QVector< double > &xData, const QVector< double > &yData )
{
    if( xData.isEmpty() || yData.isEmpty() )
    {
        dbg_warning("FastWaveUT::setDatas Data is empty");
        return;
    }
    //qDebug() << "FastWaveUT::plotDatas, plot data size: " << xData.size() << endl;
    addSamples(xData, yData);
    return;
}

/************************************************
 * 功能     ：更新极值
 ************************************************/
void FastWaveUT::replotLimitValue(bool bUpdate )
{
    if( bUpdate )
    {
        if( m_vXdata.isEmpty() || m_vYdata.isEmpty() ||
                ( m_vXdata.size() != m_vYdata.size() ) )
        {
            return;
        }

        APP_CHECK_RETURN( m_pNodeInformation );
        m_pNodeInformation->clear();

        double xMax = m_vXdata.first();
        double yMax = m_vYdata.first();
        double xMin = m_vXdata.first();
        double yMin = m_vYdata.first();

        /*for(int i = 0, iSize = m_vYdata.size(); i < iSize; ++i)
        {
            if( m_vYdata[i] > yMax )
            {
                yMax = m_vYdata[i];
                xMax = m_vXdata[i];
            }

            if( m_vYdata[i] < yMin )
            {
                yMin = m_vYdata[i];
                xMin = m_vXdata[i];
            }
        }*/

        for(int i = 0, iSize = m_vXdata.size(); i < iSize && m_vXdata[i] <= m_dXmax; ++i)
        {
            if(m_vYdata[i] > yMax)
            {
                yMax = m_vYdata[i];
                xMax = m_vXdata[i];
            }

            if(m_vYdata[i] < yMin)
            {
                yMin = m_vYdata[i];
                xMin = m_vXdata[i];
            }
        }

        m_pNodeInformation->appendNodeValue(xMax, yMax);
        m_pNodeInformation->appendNodeValue(xMin, yMin);
        m_pNodeInformation->setReplotAttribute(true);
        replot();
    }
    else
    {
        m_pNodeInformation->setReplotAttribute(false);
    }

    return;
}

/************************************************
 * 函数名    :updateNodeInformation
 * 输入参数  ：iMarkerIndex -- 标记线对应的数值序号
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：更新交点信息的 显示
 ************************************************/
void FastWaveUT::updateNodeInformation( INT32 iMarkerIndex )
{
    Q_UNUSED( iMarkerIndex )
    APP_CHECK_RETURN( m_pNodeInformation )
//    if( m_bMarkerEnabled && ( iMarkerIndex > 0 ) && ( iMarkerIndex < m_vXdata.size() ) )
//    {
//        m_pNodeInformation->setNodeValue( m_vXdata.at( iMarkerIndex ),
//                                          m_vYdata.at( iMarkerIndex ) ) ;
//    }
//        if( ( iMarkerIndex > 0 ) && ( iMarkerIndex < m_vXdata.size() ) )
//        {
//            m_pNodeInformation->setNodeValue( m_vXdata.at( iMarkerIndex ),
//                                              m_vYdata.at( iMarkerIndex ) ) ;
//        }
}

/************************************************
 * 函数名    :adjustSizeByZoomStatus
 * 输入参数  ：eZoomedStatus -- 当前放大的状态
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：根据放大状态调整界面尺寸，需求有提及在放大状态时获取到新的数据，界面横坐标初始状态，纵坐标保留放大状态
************************************************/
void FastWaveUT::adjustSizeByZoomStatus( ZoomedStatus eZoomedStatus )
{
    switch( eZoomedStatus )
    {
        case ZOOMED_ALREADY:
        {
            APP_CHECK_RETURN( m_pZoomer )
            QRectF baseRectF = getCanvasRect();     // 获得放大前的初始矩形范围
            QRectF zoomRectF = m_pZoomer->zoomRect();   // 获得当前放大的矩形区域范围
            // 新的数据范围为 横坐标范围恢复初始宽度 纵坐标范围保持放大后的范围
            QRectF curRectF = QRectF( QPointF( baseRectF.topLeft().x(),zoomRectF.topLeft().y() ),
                                      QSizeF( baseRectF.width(),zoomRectF.height() ));
            m_pZoomer->zoom( curRectF );    // 根据最新计算的当前显示的范围，更新页面
            //m_eZoomedStatus = ZOOMED_NONE;  // 刷新相应状态
        }
            break;
        default:
            break;
    }
}

/************************************************
 * 函数名    :xDatas
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：x轴数据
 ************************************************/
QVector< double > const& FastWaveUT::xDatas ( void ) const
{
    return m_vXdata;
}

/************************************************
 * 函数名    :yDatas
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：y轴数据
 ************************************************/
QVector< double > const& FastWaveUT::yDatas ( void ) const
{
    return m_vYdata;
}

/************************************************
 * 函数名    :setZoomEnabled
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：使能放大缩小功能
 ************************************************/
void FastWaveUT::setZoomEnabled( bool bEnbable )
{
    APP_CHECK_RETURN( m_pZoomer )
    m_pZoomer->setEnabled( bEnbable );
}

/************************************************
 * 函数名    :clearEvent
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ： 清除显示数据时，若此时标记线和数据有交点，清除显示
 ************************************************/
void FastWaveUT::clearEvent( void )
{
    APP_CHECK_RETURN( m_pNodeInformation )
    m_pNodeInformation->clear();    // 清除交点显示
    m_vXdata.clear();
    m_vYdata.clear();   // 清除原始数据
    replot();
}

/*********************************************
功能：重绘事件
输入参数： event -- 事件
*********************************************/
void FastWaveUT::paintEvent( QPaintEvent *event )
{
    FastWaveBase::paintEvent( event );
    if( m_eZoomedStatus == ZOOMED_NONE )
    {
        m_pHomeButton->hide();
    }
    else
    {
        m_pHomeButton->show();
        m_pHomeButton->move( this->canvas()->width() - m_pHomeButton->width(),
                             canvas()->height() - m_pHomeButton->height() );
    }
}

/*********************************************
功能：创建放大返回按键
*********************************************/
void FastWaveUT::createZoomBackButton( void )
{
    m_pHomeButton = new QToolButton( this->canvas() );
    m_pHomeButton->setWindowFlags(Qt::CustomizeWindowHint|Qt::FramelessWindowHint);
    QPixmap pixmap(":/images/return.png");
    QIcon icon;
    icon.addPixmap(pixmap);
    m_pHomeButton->setIcon( icon );
    m_pHomeButton->setIconSize( pixmap.size() );
    m_pHomeButton->setFixedSize( pixmap.size() );
    m_pHomeButton->setMask( pixmap.mask() );
    m_pHomeButton->move( this->canvas()->width() - m_pHomeButton->width(),
                         canvas()->height() - m_pHomeButton->height() );
    connect( m_pHomeButton, SIGNAL(pressed()), this, SLOT(onZoomBack()) );
}

void FastWaveUT::xScale( float &fMin,float &fMax )
{
    fMin = m_dXmin;
    fMax = m_dXmax;
}

/************************************************
 * 函数名    :setScale
 * 输入参数  ：min -- 最小值
 *            max -- 最大值
 *            step -- 步进
 *           eAxisScale -- 横、纵坐标轴方向
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：设置横、纵坐标轴方向的范围和步进
 ************************************************/
void FastWaveUT::setScale( AxisScale eAxisScale,double min,double max,double step )
{
    if( min > max )
    {
        return;
    }

    switch( eAxisScale )
    {
        case X_SCALE:
        {
            setXAxisScale( min,max,step );

//            APP_CHECK_RETURN( m_pMarker )
//            m_pMarker->setMarkerScale( min,max );  // 因为该图谱下标记线为竖直方向

            APP_CHECK_RETURN( m_pNodeInformation )
            m_pNodeInformation->setNodeXScale( max,min );
            m_dXmax = max;
            m_dXmin = min;

            m_bResetXCale = true;
        }
            break;
        case Y_SCALE:
        {
            setYAxisScale( min,max,step );

            APP_CHECK_RETURN( m_pNodeInformation )
            m_pNodeInformation->setNodeYScale( max,min );
            m_dYmax = max;
            m_dYmin = min;
            m_bResetXCale = true;
        }
            break;
        default:
            break;
    }

    updateZoomerBase();
}

/************************************************
 * 功能     ：设置小数点后位数
 ************************************************/
void FastWaveUT::setDotCnt( nodeInformation::DotCnt eCnt )
{
    APP_CHECK_RETURN( m_pNodeInformation )
    m_pNodeInformation->setDotCnt( eCnt );
}

/************************************************
 * 函数名    :updateZoomerBase
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：更新放大缩小控件的默认大小，即返回时默认的大小
 ************************************************/
void FastWaveUT::updateZoomerBase( void )
{
    APP_CHECK_RETURN(m_pZoomer)
    m_pZoomer->setZoomBase( getCanvasRect() );
}

/************************************************
 * 函数名    :enableMarker
 * 输入参数  ：使能标志
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：设置是否隐藏标记线
 ************************************************/
void FastWaveUT::enableMarker( bool bEnabled )
{
    Q_UNUSED( bEnabled )
//    if( bEnabled == m_bMarkerEnabled )      // 因为标记线若隐藏则无数据交点，故也应该隐藏交点图形
//    {
//        return;
//    }
//    m_bMarkerEnabled = bEnabled;
//    APP_CHECK_RETURN( m_pMarker )
//    APP_CHECK_RETURN( m_pNodeInformation )
//    if( !m_bMarkerEnabled )
//    {
//        m_pMarker->detach();
//        m_pNodeInformation->detach();
//    }
//    else
//    {
//        m_pMarker->attach( this );
//        m_pNodeInformation->attach( this );
//    }
//    replot();
}

/************************************************
 * 函数名    :enablePowerReference
 * 输入参数  ：使能标志
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：设置是否隐藏工频参考
 ************************************************/
void FastWaveUT::enablePowerReference( bool bEnabled )
{
    Q_UNUSED(bEnabled)
//    if( bEnabled == m_bRefrenceEnabled )
//    {
//        return;
//    }
//    m_bRefrenceEnabled = bEnabled;
//    APP_CHECK_RETURN( m_pRefrenceCurve )
//    if( false == m_bRefrenceEnabled )
//    {
//        m_pRefrenceCurve->detach();
//    }
//    else
//    {
//        m_pRefrenceCurve->attach( this );
//    }
//    replot();
}

/************************************************
 * 函数名    :onZoomed(slot)
 * 输入参数  ：rect:放大/缩小的区域
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：槽函数，绑定放大缩小控件，获得放大/缩小的区域
 ************************************************/
void FastWaveUT::onZoomed( const QRectF &rect )
{
    if( rect == getCanvasRect() )
    {
        return;
    }

    //judgeMarkerIsValid( rect ); // 判断标记线是否在可见范围内

    APP_CHECK_RETURN( m_pNodeInformation )
    m_pNodeInformation->setNodeXYScale( rect );
    m_eZoomedStatus = ZOOMED_ALREADY;
}

/*********************************************
功能：放大返回
*********************************************/
void FastWaveUT::onZoomBack( void )
{
    zoomNext();

    m_eZoomedStatus = ZOOMED_NONE;
}

/************************************************
 * 函数名    :judgeMarkerIsValid
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：判断标记线是否在范围内可见，有需求 需要在marker不可见时，界面能够获知该信息，将marker隐藏
************************************************/
void FastWaveUT::judgeMarkerIsValid( const QRectF& rect )
{
    Q_UNUSED( rect )
//    APP_CHECK_RETURN( m_pMarker )
//    if( m_bMarkerEnabled )
//    {
//        bool ret = m_pMarker->isMarkerValid( rect );
//        if( false == ret )
//        {
//            emit sigMarkerOutside();
//        }
//    }
//    else
//    {
//        // marker is no valid
//    }
}

/************************************************
 * 函数名    :jumpTo
 * 输入参数  ：eJumpDirection -- 跳转方向
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：跳转到最小、大值
 ************************************************/
void FastWaveUT::jumpTo( JumpDirection eJumpDirection )
{
    Q_UNUSED( eJumpDirection )
//    if( !m_bMarkerEnabled )
//    {
//        return;
//    }
//    if( m_vXdata.isEmpty() || m_vYdata.isEmpty() )
//    {
//        return;
//    }
//    int iIndex = 0;

//    switch( eJumpDirection )        // 根据跳转方向决定跳转最大值或最小值
//    {
//        case JUMP_TO_MAX:
//        {
//            iIndex = findMaximumIndex( m_vYdata );
//        }
//            break;
//        case JUMP_TO_MIN:
//        {
//            iIndex = findMinimumIndex( m_vYdata );
//        }
//            break;
//        default:
//            break;
//    }

//    if( HC_FAILURE == iIndex )
//    {
//        return;
//    }
//    m_iMarkerIndex = iIndex;
//    APP_CHECK_RETURN( m_pMarker )
//    APP_CHECK_RETURN( m_pNodeInformation )
//    m_pNodeInformation->setNodeValue( m_vXdata.at( iIndex ),m_vYdata.at( iIndex ) );// 交点信息
//    m_pMarker->setMarkerValue( m_vXdata.at( iIndex ) );// 标记线
//    replot();
}

/************************************************
 * 函数名    :findMinimumIndex
 * 输入参数  ：data -- 数据容器
 * 输出参数  ：NULL
 * 返回值   ：HC_FAILURE 失败的标志
 * 功能     ：获取容器中最小值的序号
************************************************/
int FastWaveUT::findMinimumIndex(const QVector< double > & data )
{
    if( data.isEmpty() )
    {
        return HC_FAILURE;
    }
    int iIndex = 0;
    double yMin = data.first();
    for( INT32 i = 1;i < data.size();++i)
    {
        if( yMin > data.at( i ) )
        {
            yMin = data.at( i );
            iIndex = i;
        }
    }
    return iIndex;
}

/************************************************
 * 函数名    :findMaximumIndex
 * 输入参数  ：data -- 数据容器
 * 输出参数  ：NULL
 * 返回值   ：HC_FAILURE 失败的标志
 * 功能     ：获取容器中最大值的序号
************************************************/
int FastWaveUT::findMaximumIndex(const QVector< double > & data )
{
    if( data.isEmpty() )
    {
        return HC_FAILURE;
    }
    int iIndex = 0;
    double yMax = m_vYdata.first();
    for( INT32 i = 1;i < m_vYdata.size();++i)
    {
        if( yMax < m_vYdata.at( i ) )
        {
            yMax = m_vYdata.at( i );
            iIndex = i;
        }
    }
    return iIndex;
}

/************************************************
 * 函数名    :eventFilter
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：事件过滤
 ************************************************/
bool FastWaveUT::eventFilter(QObject *pObj, QEvent *pEvent)
{
//    if( pObj == canvas() )
//    {
//        if( pEvent->type() == QEvent::MouseButtonPress)
//        {
//            QMouseEvent *e = static_cast< QMouseEvent* >(pEvent);
//            double value = globalMapToChart( e->pos() ).x();   // 相应点击的位置对应到图谱中的坐标
//            APP_CHECK_RETURN_VAL( m_pMarker,0 )
//            if( m_pMarker->press( value ) )
//            {
//                m_bIsPressed = true;
//                m_PressPoint = e->pos();
//                return true;
//            }
//            return false;
//        }
//        else if( pEvent->type() == QEvent::MouseMove )
//        {
//            if( !m_bIsPressed )
//            {
//                return false;
//            }
//            QMouseEvent *e = static_cast< QMouseEvent* >(pEvent);
//            if( m_usMoveEventCnt >= MOVE_EVENT_CNT )    // 避免多次移动时间频繁处理，刷新过于频繁
//            {
//                m_usMoveEventCnt = 0;
//                // 计算移动前后的偏移量
//                double offset = globalMapToChart( e->pos() ).x() - globalMapToChart( m_PressPoint ).x();
//                m_PressPoint = e->pos();

//                APP_CHECK_RETURN_VAL( m_pMarker,0 )
//                int ret = moveMarker( offset );     // 移动标记线，返回值标明移动标记线所到的数据序号
//                if( ret != HC_FAILURE )
//                {
//                   m_iMarkerIndex = ret;
//                   updateNodeInformation( m_iMarkerIndex );
//                }
//                replot();
//            }
//            m_usMoveEventCnt++;
//            return true;
//        }
//        else if( pEvent->type() == QEvent::MouseButtonRelease )
//        {
//            if( m_bIsPressed )
//            {
//                m_bIsPressed = false;
//                return true;
//            }
//            return false;
//        }
//    }
    return QwtPlot::eventFilter(pObj, pEvent);
}

/************************************************
 * 函数名    :globalMapToChart
 * 输入参数  ：pressPoint -- 点击的坐标
 * 输出参数  ：NULL
 * 返回值   ：转换后的坐标
 * 功能     ：将鼠标点击的坐标转换成图谱中的坐标
 ************************************************/
QPointF FastWaveUT::globalMapToChart( const QPointF &pressPoint )
{
    const QwtScaleMap xMap = canvasMap( QwtPlot::xBottom );
    const QwtScaleMap yMap = canvasMap( QwtPlot::yLeft );
    QPointF transformPoint = QwtScaleMap::invTransform( xMap, yMap, pressPoint );
    return transformPoint;
}

/************************************************
 * 函数名    :moveMarker
 * 输入参数  ：dOffset:偏移量
 * 输出参数  ：NULL
 * 返回值   ：INT32,标记线和数据线的交点的下标
 * 功能     :移动标记线，只在各数据点之间移动
 ************************************************/
INT32 FastWaveUT::moveMarker( double dOffset )
{
    Q_UNUSED( dOffset )
//    if( ( !m_bMarkerEnabled ) || ( m_vXdata.isEmpty() ) )
//    {
//        return HC_FAILURE;
//    }
//    APP_CHECK_RETURN_VAL( m_pMarker,HC_FAILURE )

//    int iAfterMoveIndex = markerIndexOffset( dOffset );
//    if( iAfterMoveIndex < 0 || iAfterMoveIndex >= m_vXdata.size() )
//    {
//        return HC_FAILURE;
//    }
//    else
//    {
//        m_pMarker->setMarkerValue( m_vXdata.at( iAfterMoveIndex ) );
//        return iAfterMoveIndex;
//    }
    return -1;
}

/************************************************
 * 函数名    :markerIndexOffset
 * 输入参数  ：dOffset -- 偏移量
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：偏移后marker的值相对应的数据下标
 ************************************************/
INT32 FastWaveUT::markerIndexOffset( double dOffset )
{
    Q_UNUSED( dOffset )
//    double markerValue = m_pMarker->markerValue();    // 当前标记线对应的值
//    double dataInterval = ( m_dXmax - m_dXmin ) / m_vXdata.size(); // 数据之间的间隔大小
//    INT32 iMarkerIndex = (markerValue - m_dXmin) / dataInterval; // marker所在位置对应的index
//    INT32 ioffsetIndex = dOffset / dataInterval;  //移动的偏移量
//    return iMarkerIndex + ioffsetIndex;
    return -1;
}

/************************************************
 * 函数名    :zoomNext
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：退回之前的缩放状态
 ************************************************/
void FastWaveUT::zoomNext( void )
{
    APP_CHECK_RETURN( m_pZoomer )
    m_pZoomer->zoom( -1 );

    // modify by wq恢复原大小时重新设置坐标特征，以期恢复坐标显示
    setScale( X_SCALE,m_dXmin,m_dXmax,( m_dXmax - m_dXmin ) / 4 );
    setScale( Y_SCALE,m_dYmin,m_dYmax,( m_dYmax - m_dYmin ) / 4 );

//    APP_CHECK_RETURN( m_pMarker )
//    m_pMarker->setMarkerScale( m_dXmin,m_dXmax );  // 因为该图谱下标记线为竖直方向

    APP_CHECK_RETURN( m_pNodeInformation )
    m_pNodeInformation->setNodeXScale( m_dXmax,m_dXmin );
    m_pNodeInformation->setNodeYScale( m_dYmax,m_dYmin );
}

/************************************************
 * 函数名    :dataInit
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：参数初始化
 ************************************************/
void FastWaveUT::dataInit( void )
{
    m_pRefrenceCurve = NULL;     // 工频参考曲线
    m_pMarker = NULL;           // 标记线
    m_pNodeInformation = NULL;  // 交点信息
    m_PressPoint = QPoint( -1,-1 );                // 记录press时的点
    m_iMarkerIndex = -1;       // 标记线对应值的序号
    m_bRefrenceEnabled = false;
    m_bMarkerEnabled = false;
    m_bIsPressed = false;

    m_dXmax = 1;
    m_dXmin = 0;
    m_dYmax = 1;
    m_dYmin = 0;
    m_usMoveEventCnt = 0;

    m_eZoomedStatus = ZOOMED_NONE;

    m_bResetXCale = false;
}

/************************************************
 * 函数名    :initReferenceCurve
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：初始化工频参考曲线
************************************************/
void FastWaveUT::initReferenceCurve( void )
{
//    APP_CHECK_RETURN( m_pRefrenceCurve )
//    QVector<double> xData;
//    QVector<double> yData;

//    const QRectF chartRect = getCanvasRect();

//    double dRange = ( chartRect.height() ) / 2;
//    for (double x = chartRect.left(); x < chartRect.right(); ++x )
//    {
//        xData.append( x );
//        yData.append( qSin(x * 2 * View::PI / INTERVAL_COUNTS ) * dRange );
//    }
//    m_pRefrenceCurve->setSamples( xData,yData );
//    if( m_bRefrenceEnabled )
//    {
//        m_pRefrenceCurve->attach( this );
//        replot();
//    }
//    else if(m_bResetXCale)
//    {
//        replot();
//    }
}

/************************************************
 * 函数名    :axisScaleChanged
 * 输入参数  ：NULL
 * 输出参数  ：NULL
 * 返回值   ：NULL
 * 功能     ：父类重新设置x，y轴最大最小值时调用，便于子类将类似工频参考等数据重新处理
 ************************************************/
 void FastWaveUT::axisScaleChanged( void )
 {
//     APP_CHECK_RETURN( m_pRefrenceCurve )
//     initReferenceCurve();
 }
