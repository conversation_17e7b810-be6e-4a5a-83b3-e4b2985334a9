#include "iniconfig.h"
#include "fileoper/fileoperutil.h"

const QString SysIniFile = "/opt/bin-bash/sysparam.ini";    //蓝牙等信息，需能够通过升级修改
const QString AppIniFile = "/opt/bin-bash/app.ini";       //校准等信息不能受升级影响
const QString HfctPrpsFile = "/opt/bin-bash/hfctprps.ini";

const QString GrpBluetooth = "bluetooth";
const QString KeySrvUuid = "srvUuid";
const QString KeySrvName = "srvName";

const QString GrpCalibration = "calibration";
const QString KeyCalibrationDate = "calibrationDate";
const QString KeyCalibIntervalMonths = "calibIntervalMonths";
const QString KeyCalibNoRemind = "calibNoRemind";

const QString GrpActiveInfo = "activeInfo";
const QString GrpHfctPrpsInfo = "hfctprps";
const QString KeyActiveOrigionST = "activeOrigStartTime";
const QString KeyActiveOrigionET = "activeQrigEndTime";
const QString KeyActiveShowST = "activeShowStartTime";
const QString KeyActiveShowET = "activeShowEndTime";
const QString KeyActiveSummary = "activeSummary";
const QString KeyActiveState = "activeState";
const QString KeyActiveEffective = "activeEffective";
const QString KeyActiveShowFinalUserInfo = "activeShowFinalUserInfo";
const QString KeyActiveFinalUserInfo = "activeFinalUserInfo";

const QString GrpTaskTmpInfo = "taskTempInfo";
const QString KeyTaskInnerId = "taskInnerId";
const QString KeyTaskTestTime = "taskTestTime";
const QString KeyTaskTotalCount = "taskTotalCount";
const QString KeyTaskTestedCount = "taskTestedCount";

const QString GrpTestDataTmpInfo = "testDataTempInfo";
const QString KeyTestDataPointType = "testDataPointType";
const QString KeyTestDataPointPos = "testDataPointPos";
const QString KeyTestDataType = "testDataType";
const QString KeyTestDataBandWidth = "testDataBandWidth";
const QString KeyTestDataUnit = "testDataUnit";
const QString KeyTestDataIndex = "testDataIndex";
const QString KeyTestDataState = "testDataState";
const QString KeyTestDataBgn = "testDataBgn";
const QString KeyTestDataPointNumber = "testDataPointNumber";
const QString KeyTestDataNumber = "testDataNumber";
const QString KeyTestDataFileName = "testDataFileName";
const QString KeyTestDataFilePath = "testDataFilePath";
const QString KeyTestDataAttachPath = "testDataAttachPath";
const QString KeyTestDataRemotePath = "testDataRemotePath";
const QString KeyTestDataRemoteAttachPath = "testDataRemoteAttachPath";

const QString GrpFirmwareInfo = "FirmwareInfo";
const QString KeyFirmType = "firmType";
const QString KeyDataFileVer = "datafileVersion";
const QString KeySoftwareVer = "softwareVersion";
const QString KeyHardwareVer = "hardwareVersion";
const QString KeyEmbeSwVer = "embeddedSwVersion";
const QString KeyDevModel = "devModel";
const QString KeyDevName = "devName";
const QString KeyChiCompName = "ChineseCompanyName";
const QString KeyEnCompName = "EnglishCompanyName";

const QString GrpInfraredCalibInfo = "InfraredCalibrateInfo";
const QString KeyCoeffK = "coefficientK";
const QString KeyCoeffB = "coefficientB";



/************************************************
 * 功能    : 检测配置文件是否存在，不存在时创建配置文件
 ************************************************/
void IniConfig::checkOrCreateIniFile()
{
    FileOperUtil::createFile(SysIniFile);
    return;
}

/************************************************
 * 输入参数 : strUuid -- 对方UUID
 *           strName -- 对方服务名
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能    : 读取蓝牙服务器的信息
 ************************************************/
void IniConfig::readBtServerInfo(QString &strUuid, QString &strName)
{
    checkOrCreateIniFile();
    QSettings sets(SysIniFile, QSettings::IniFormat);
    sets.beginGroup(GrpBluetooth);
    strUuid = sets.value(KeySrvUuid).toString();
    strName = sets.value(KeySrvName).toString();
    sets.endGroup();
    return;
}

/************************************************
 * 输入参数 : strUuid -- 对方UUID
 *           strName -- 对方服务名
 *
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能    : 写入蓝牙服务器的信息
 ************************************************/
void IniConfig::writeBtServerInfo(const QString &strUuid, const QString &strName)
{
    checkOrCreateIniFile();
    QSettings sets(SysIniFile, QSettings::IniFormat);
    sets.beginGroup(GrpBluetooth);
    sets.setValue(KeySrvUuid, strUuid);
    sets.setValue(KeySrvName, strName);
    sets.endGroup();
    sets.sync();
    return;
}

/*****************************************************
 * 功能：读取配置文件中的校准信息
 * 输出参数：
 *      stCaliInfo：校准信息
 * 返回值：
 *      true：读取成功，false：读取失败
 * *****************************************************/
bool IniConfig::readCalibrateInfo(CalibrateInfo &stCalibInfo)
{
    QSettings sets(AppIniFile, QSettings::IniFormat);
    sets.beginGroup(GrpCalibration);
    stCalibInfo.qstrCalibrationDate = sets.value(KeyCalibrationDate).toString();
    stCalibInfo.iIntervalMonths = sets.value(KeyCalibIntervalMonths).toInt();
    stCalibInfo.bNoRemind = sets.value(KeyCalibNoRemind).toBool();
    sets.endGroup();

    return ((stCalibInfo.qstrCalibrationDate.isEmpty()) ? false : true);
}

/*****************************************************
 * 功能：写入校准信息到配置文件中
 * 输入参数：
 *      stCaliInfo：校准信息
 * *****************************************************/
void IniConfig::writeCalibrateInfo(const CalibrateInfo &stCalibInfo)
{
    QSettings sets(AppIniFile, QSettings::IniFormat);
    sets.beginGroup(GrpCalibration);
    sets.setValue(KeyCalibrationDate, stCalibInfo.qstrCalibrationDate);
    sets.setValue(KeyCalibIntervalMonths, stCalibInfo.iIntervalMonths);
    sets.setValue(KeyCalibNoRemind, stCalibInfo.bNoRemind);
    sets.endGroup();
    sets.sync();

    return;
}

/*******************************************************
 * 功能：读取任务临时信息
 * 输入参数：
 *      qstrFilePath：文件路径
 * 输出参数：
 *      stTaskTmpInfo：任务临时概要信息
 * 返回值：
 *      bool：true -- 读取成功，false -- 读取失败
 * **********************************************************/
bool IniConfig::readTaskTmpInfo(const QString &qstrFilePath, TaskTmpInfo &stTaskTmpInfo)
{
    bool bRet = false;
    if(!FileOperUtil::checkFileOrDirExist(qstrFilePath))
    {
        return bRet;
    }

    QSettings sets(qstrFilePath, QSettings::IniFormat);
    sets.beginGroup(GrpTaskTmpInfo);
    stTaskTmpInfo.qstrInnerId = sets.value(KeyTaskInnerId).toString();
    stTaskTmpInfo.qi64TestTime = sets.value(KeyTaskTestTime).toLongLong();
    stTaskTmpInfo.qi32TotalTestData = sets.value(KeyTaskTotalCount).toInt();
    stTaskTmpInfo.qi32TestedTestData = sets.value(KeyTaskTestedCount).toInt();
    sets.endGroup();

    bRet = true;

    return bRet;
}

/*******************************************************
 * 功能：写入任务临时信息
 * 输入参数：
 *      qstrFilePath：文件路径
 *      stTaskTmpInfo：任务临时概要信息
 * **********************************************************/
void IniConfig::writeTaskTmpInfo(const QString &qstrFilePath, const TaskTmpInfo &stTaskTmpInfo)
{
    QSettings sets(qstrFilePath, QSettings::IniFormat);
    sets.beginGroup(GrpTaskTmpInfo);
    sets.setValue(KeyTaskInnerId, stTaskTmpInfo.qstrInnerId);
    sets.setValue(KeyTaskTestTime, stTaskTmpInfo.qi64TestTime);
    sets.setValue(KeyTaskTotalCount, stTaskTmpInfo.qi32TotalTestData);
    sets.setValue(KeyTaskTestedCount, stTaskTmpInfo.qi32TestedTestData);
    sets.endGroup();
    sets.sync();

    return;
}

/*******************************************************
 * 功能：读取测试数据临时概要信息
 * 输入参数：
 *      qstrFilePath：文件路径
 * 输出参数：
 *      stTestDataTmpInfo：测试数据临时概要信息
 * 返回值：
 *      bool：true -- 读取成功，false -- 读取失败
 * **********************************************************/
bool IniConfig::readTestDataTmpInfo(const QString &qstrFilePath, TestDataTmpInfo &stTestDataTmpInfo)
{
    bool bRet = false;
    if(!FileOperUtil::checkFileOrDirExist(qstrFilePath))
    {
        return bRet;
    }

    QSettings sets(qstrFilePath, QSettings::IniFormat);
    sets.beginGroup(GrpTestDataTmpInfo);
    stTestDataTmpInfo.qi32PointType = sets.value(KeyTestDataPointType).toInt();
    stTestDataTmpInfo.qi32PointPos = sets.value(KeyTestDataPointPos).toInt();
    stTestDataTmpInfo.qi32DataType = sets.value(KeyTestDataType).toInt();
    stTestDataTmpInfo.qi32BandWidth = sets.value(KeyTestDataBandWidth).toInt();
    stTestDataTmpInfo.qi32Unit = sets.value(KeyTestDataUnit).toInt();
    stTestDataTmpInfo.qui16Index = sets.value(KeyTestDataIndex).toString().toUShort();
    stTestDataTmpInfo.bTested = sets.value(KeyTestDataState).toBool();
    stTestDataTmpInfo.bIsBgn = sets.value(KeyTestDataBgn).toBool();
    stTestDataTmpInfo.qstrPointNumber = sets.value(KeyTestDataPointNumber).toString();
    stTestDataTmpInfo.qstrNumber = sets.value(KeyTestDataNumber).toString();
    stTestDataTmpInfo.qstrFileName = sets.value(KeyTestDataFileName).toString();
    stTestDataTmpInfo.qstrFilePath = sets.value(KeyTestDataFilePath).toString();
    stTestDataTmpInfo.qstrAttachPath = sets.value(KeyTestDataAttachPath).toString();
    stTestDataTmpInfo.qstrRemotePath = sets.value(KeyTestDataRemotePath).toString();
    stTestDataTmpInfo.qstrRemoteAttachPath = sets.value(KeyTestDataRemoteAttachPath).toString();
    sets.endGroup();

    bRet = true;

    return bRet;
}

/*******************************************************
 * 功能：写入测试数据临时概要信息
 * 输入参数：
 *      qstrFilePath：文件路径
 *      stTestDataTmpInfo：测试数据临时概要信息
 * **********************************************************/
void IniConfig::writeTestDataTmpInfo(const QString &qstrFilePath, const TestDataTmpInfo &stTestDataTmpInfo)
{
    QSettings sets(qstrFilePath, QSettings::IniFormat);
    sets.beginGroup(GrpTestDataTmpInfo);
    sets.setValue(KeyTestDataPointType, stTestDataTmpInfo.qi32PointType);
    sets.setValue(KeyTestDataPointPos, stTestDataTmpInfo.qi32PointPos);
    sets.setValue(KeyTestDataType, stTestDataTmpInfo.qi32DataType);
    sets.setValue(KeyTestDataBandWidth, stTestDataTmpInfo.qi32BandWidth);
    sets.setValue(KeyTestDataUnit, stTestDataTmpInfo.qi32Unit);
    sets.setValue(KeyTestDataIndex, stTestDataTmpInfo.qui16Index);
    sets.setValue(KeyTestDataState, stTestDataTmpInfo.bTested);
    sets.setValue(KeyTestDataBgn, stTestDataTmpInfo.bIsBgn);
    sets.setValue(KeyTestDataPointNumber, stTestDataTmpInfo.qstrPointNumber);
    sets.setValue(KeyTestDataNumber, stTestDataTmpInfo.qstrNumber);
    sets.setValue(KeyTestDataFileName, stTestDataTmpInfo.qstrFileName);
    sets.setValue(KeyTestDataFilePath, stTestDataTmpInfo.qstrFilePath);
    sets.setValue(KeyTestDataAttachPath, stTestDataTmpInfo.qstrAttachPath);
    sets.setValue(KeyTestDataRemotePath, stTestDataTmpInfo.qstrRemotePath);
    sets.setValue(KeyTestDataRemoteAttachPath, stTestDataTmpInfo.qstrRemoteAttachPath);
    sets.endGroup();
    sets.sync();

    return;
}

/*******************************************************
 * 功能：读取激活信息
 * 输入参数：
 *      stActiveInfo：激活信息
 * 返回值：
 *      bool：true -- 读取成功，false -- 读取失败
 * **********************************************************/
bool IniConfig::readActiveInfo(ActiveInfo &stActiveInfo)
{
    QSettings sets(AppIniFile, QSettings::IniFormat);
    sets.beginGroup(GrpActiveInfo);
    stActiveInfo.qstrOrigionStartTime = sets.value(KeyActiveOrigionST).toString();
    stActiveInfo.qstrOrigionEndTime = sets.value(KeyActiveOrigionET).toString();
    stActiveInfo.qstrShowStartDate = sets.value(KeyActiveShowST).toString();
    stActiveInfo.qstrShowEndDate = sets.value(KeyActiveShowET).toString();
    stActiveInfo.qstrSummary = sets.value(KeyActiveSummary).toString();
    stActiveInfo.eState = static_cast<ActiveState>(sets.value(KeyActiveState).toInt());
    stActiveInfo.bActivationEffective = sets.value(KeyActiveEffective).toBool();
    stActiveInfo.bShow = sets.value(KeyActiveShowFinalUserInfo).toBool();
    stActiveInfo.qstrFinalUserInfo = sets.value(KeyActiveFinalUserInfo).toString();
    sets.endGroup();

    return ((stActiveInfo.qstrOrigionEndTime.isEmpty()) ? false : true);
}

/*******************************************************
 * 功能：写入激活信息
 * 输入参数：
 *      stActiveInfo：激活信息
 * **********************************************************/
void IniConfig::writeActiveInfo(const ActiveInfo &stActiveInfo)
{
    QSettings sets(AppIniFile, QSettings::IniFormat);
    sets.beginGroup(GrpActiveInfo);
    sets.setValue(KeyActiveOrigionST, stActiveInfo.qstrOrigionStartTime);
    sets.setValue(KeyActiveOrigionET, stActiveInfo.qstrOrigionEndTime);
    sets.setValue(KeyActiveShowST, stActiveInfo.qstrShowStartDate);
    sets.setValue(KeyActiveShowET, stActiveInfo.qstrShowEndDate);
    sets.setValue(KeyActiveSummary, stActiveInfo.qstrSummary);
    sets.setValue(KeyActiveState, static_cast<int>(stActiveInfo.eState));
    sets.setValue(KeyActiveEffective, stActiveInfo.bActivationEffective);
    sets.setValue(KeyActiveShowFinalUserInfo, stActiveInfo.bShow);
    sets.setValue(KeyActiveFinalUserInfo, stActiveInfo.qstrFinalUserInfo);
    sets.endGroup();
    sets.sync();

    return;
}

/*******************************************************
 * 功能：读取接入终端文件的图谱状态
 * 输入参数：
 *      qstrSavedPath：文件名称
 * 返回值：
 *      HFCT::SpectrumState：文件对应的图谱状态
 * **********************************************************/
HFCT::SpectrumState IniConfig::readHfctPrpsInfo(QString qstrSavedPath)
{
    HFCT::SpectrumState eSpectrumState;
    QSettings sets(HfctPrpsFile, QSettings::IniFormat);
    sets.beginGroup(GrpHfctPrpsInfo);
    eSpectrumState = static_cast<HFCT::SpectrumState>(sets.value(qstrSavedPath).toInt());
    sets.endGroup();
    return eSpectrumState;
}


/*******************************************************
 * 功能：写入接入终端文件的图谱状态
 * 输入参数：
 *      qstrSavedPath：文件名称
 *      eSpectrumState：对应文件图谱状态
 * **********************************************************/
void IniConfig::writeHfctPrpsInfo(QString qstrSavedPath, HFCT::SpectrumState eSpectrumState)
{
    QSettings sets(HfctPrpsFile, QSettings::IniFormat);
    sets.beginGroup(GrpHfctPrpsInfo);
    sets.setValue(qstrSavedPath, static_cast<int>(eSpectrumState));
    sets.endGroup();
    sets.sync();
    return;
}

/******************************************************
 * 功能：读取固件概要信息
 * 输入参数：
 *      stFirmInfo：激活信息
 * **********************************************************/
void IniConfig::readFirmwareInfo(SystemSet::FirmwareInfo &stFirmInfo)
{
    QSettings sets(AppIniFile, QSettings::IniFormat);
    sets.beginGroup(GrpFirmwareInfo);
    stFirmInfo.eFirmType = static_cast<SystemSet::FirmType>(sets.value(KeyFirmType).toInt());
    stFirmInfo.fDataFileVersion = static_cast<float>(sets.value(KeyDataFileVer).toDouble());
    stFirmInfo.qstrDevSwVersion = sets.value(KeySoftwareVer).toString();
    stFirmInfo.qstrDevHwVersion = sets.value(KeyHardwareVer).toString();
    stFirmInfo.qstrEmbeddedVersion = sets.value(KeyEmbeSwVer).toString();
    stFirmInfo.qstrDevModel = sets.value(KeyDevModel).toString();
    stFirmInfo.qstrDevName = sets.value(KeyDevName).toString();
    stFirmInfo.qstrChineseCompanyName = sets.value(KeyChiCompName).toString();
    stFirmInfo.qstrEnglishCompanyName = sets.value(KeyEnCompName).toString();
    sets.endGroup();

    return;
}

/*******************************************************
 * 功能：写入固件概要信息
 * 输入参数：
 *      stFirmInfo：激活信息
 * **********************************************************/
void IniConfig::writeFirmwareInfo(const SystemSet::FirmwareInfo &stFirmInfo)
{
    QSettings sets(AppIniFile, QSettings::IniFormat);
    sets.beginGroup(GrpFirmwareInfo);
    sets.setValue(KeyFirmType, static_cast<int>(stFirmInfo.eFirmType));
    sets.setValue(KeyDataFileVer, static_cast<double>(stFirmInfo.fDataFileVersion));
    sets.setValue(KeySoftwareVer, stFirmInfo.qstrDevSwVersion);
    sets.setValue(KeyHardwareVer, stFirmInfo.qstrDevHwVersion);
    sets.setValue(KeyEmbeSwVer, stFirmInfo.qstrEmbeddedVersion);
    sets.setValue(KeyDevModel, stFirmInfo.qstrDevModel);
    sets.setValue(KeyDevName, stFirmInfo.qstrDevName);
    sets.setValue(KeyChiCompName, stFirmInfo.qstrChineseCompanyName);
    sets.setValue(KeyEnCompName, stFirmInfo.qstrEnglishCompanyName);
    sets.endGroup();
    sets.sync();

    return;
}

/******************************************************
 * 功能：读取红外校准系数信息
 * 输入参数：
 *      stInfo：红外校准系数信息
 * **********************************************************/
void IniConfig::readInfraredCalibrateInfo(InfraredCalibrateInfo &stInfo)
{
    QSettings sets(AppIniFile, QSettings::IniFormat);
    sets.beginGroup(GrpInfraredCalibInfo);
    stInfo.dCoeffK = sets.value(KeyCoeffK, 1.0).toDouble();
    stInfo.dCoeffB = sets.value(KeyCoeffB, 0.0).toDouble();
    sets.endGroup();

    return;
}

/******************************************************
 * 功能：写入红外校准系数信息
 * 输出参数：
 *      stInfo：红外校准系数信息
 * **********************************************************/
void IniConfig::writeInfraredCalibrateInfo(const InfraredCalibrateInfo &stInfo)
{
    QSettings sets(AppIniFile, QSettings::IniFormat);
    sets.beginGroup(GrpInfraredCalibInfo);
    sets.setValue(KeyCoeffK, stInfo.dCoeffK);
    sets.setValue(KeyCoeffB, stInfo.dCoeffB);
    sets.endGroup();
    sets.sync();

    return;
}

