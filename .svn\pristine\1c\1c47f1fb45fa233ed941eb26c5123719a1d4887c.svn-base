/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* RFIDScanView.h
*
* 初始版本：1.0
* 作者：张涛
* 创建日期：2016年3月14日
* 修改日期：2016年12月12日
* 摘要：RFID扫描界面的定义
* 当前版本：1.0
*/

#ifndef RFIDSCANVIEW_H
#define RFIDSCANVIEW_H

#include <QDialog>
#include <QLabel>
#include <QPushButton>
#include <QStackedLayout>
#include <QKeyEvent>
#include "datadefine.h"
#include "loadingView/LoadingView.h"
#include "messageBox/msgbox.h"
#include "scan/ScanService.h"

#include <rfidApi.h>


class RFIDScanView : public QDialog
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent:父窗口指针
    *************************************************************/
    explicit RFIDScanView( QWidget *parent = NULL );

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~RFIDScanView();

    /*************************************************
    功能： 扫描
    输出参数：
            psScanInfo -- 存储扫描信息
    返回：
            SCAN_SUCCEED -- 扫描成功
            其它 -- 扫描失败
    *************************************************************/
    ScanResult scan( ScanInfo* psScanInfo );

    /*************************************************
    功能： 返回底层发来的RFID信息
    输出参数：NULL
    返回：底层发来的RFID信息
    *************************************************************/
    ReadRFIDData &RFIDReadData();

protected:
    /*************************************************
    函数名： keyPressEvent
    输入参数:
        pEvent:键盘事件指针
    输出参数： NULL
    返回值： NULL
    功能： 用于实现按ESC键退出的功能
    *************************************************************/
    void keyPressEvent(QKeyEvent *pEvent);

    /*************************************************
    函数名： paintEvent
    输入参数:
        pEvent:重绘事件指针
    输出参数： NULL
    返回值： NULL
    功能： 用于实现圆角显示
    *************************************************************/
    void paintEvent(QPaintEvent *pEvent);

    /*************************************************
    功能： 定时器rfid扫描
    输入参数：
     pEvent -- 定时事件
    *************************************************************/
    void timerEvent(QTimerEvent* pEvent);

private slots:
    /*************************************************
    功能： 扫描完成, 响应控制层信号sigScanRFIDResult(INT32)，以获取设备信息进行显示
    输入参数:
        iResult:扫描结果
        data:扫描数据
    *************************************************************/
    void onScanRFIDResult( int iResult, ReadRFIDData data );    

private:
    /*************************************************
    功能： 开启rfid扫描定时器
    *************************************************************/
    void startScanTimer();

    /*************************************************
    功能： 关闭rfid扫描定时器
    *************************************************************/
    void killScanTimer();

private:
    LoadingView *m_pLoadingView;      // 加载页面
    QStackedLayout *m_pStackedMainLayout; // 栈布局器

    ScanService *m_pService;            //扫描服务模块

    ScanInfo m_sScanInfo;//扫描信息
    ReadRFIDData m_stRFIDReadData;//底层发来的RFID信息
    bool m_bScanning;
    int m_iScanTimerId;
};

#endif // SCANVIEW_H
