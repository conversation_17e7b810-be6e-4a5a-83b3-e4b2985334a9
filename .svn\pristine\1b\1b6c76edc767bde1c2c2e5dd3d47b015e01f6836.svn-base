#include "hotspotlabel.h"
#include <QDebug>
#include <QHBoxLayout>
#include <QPainter>
#include <QFontMetrics>
#include "Widget.h"
#include "appfontmanager/appfontmanager.h"

/**************************************************
 *          命令键的图标路径
 * ***********************************************/
const QString RIGHT_ARROW_ICON = ":images/right_arrow.png";
const QString TICK_ICON = ":images/appImages/APP_Ticked.png";
const QString LOCK_ICON = ":images/Wifi_Lock.png";
/**************************************************
 *          伸缩因子，布局使用
 * ***********************************************/
//按键伸缩因子
typedef enum _LabelStretch
{
    TICK_STRETCH = 1,//勾号伸缩因子
    NAME_STRTCH = 6, //wifi名伸缩因子
    CIPHER_STRTCH = 1,//加密伸缩因子
    ARROW_STRTCH = 1, //箭头伸缩因子
    STRTCH_ALL = TICK_STRETCH + NAME_STRTCH + ARROW_STRTCH + CIPHER_STRTCH
}LabelStretch;

//命令按键的伸缩因子
const int HotspotLabel::LABEL_STRETCHS[HotspotLabel::LABEL_NUM] =
{
    TICK_STRETCH,
    NAME_STRTCH,
    CIPHER_STRTCH,
    ARROW_STRTCH,
};

#define WIDTH_RATIO 0.8
#define HEIGHT_RATION 0.5
#define LOCK_WIDTH_RATIO 0.5
#define LOCK_HEIGHT_RATION 0.3
#define MARGIN_SIZE 5
#define ARROW_WIDTH 55
#define CIPHER_WIDTH 55
#define WIFI_NAME_FONT 30

const int iNameMaxLen = 100;

/*************************************************
输入参数:name -- wifi名
       parent -- 父窗体指针
       bstate -- 选中状态
功能： 构造函数
*************************************************************/
HotspotLabel::HotspotLabel(const QString &name, bool bIsCipher, QWidget *parent, bool bState) :
    SettingFrame(parent),
    m_bSelectState(bState),
    m_bCipher(bIsCipher)
{
    m_qstrName = name;
    QFont font = AppFontManager::instance()->getAppCurFont();
    font.setPointSize(WIFI_NAME_FONT);
    m_pTick = new QLabel(this);
    m_pName = new QLabel(this);
    m_pName->setFont(font);

    resizeDisplayName();

    m_pCipher = new QLabel(this);
    m_pArrow = new QLabel(this);
    m_pCipher->setMinimumWidth(CIPHER_WIDTH);
    m_pArrow->setMinimumWidth(ARROW_WIDTH);

    QHBoxLayout *hLayout = new QHBoxLayout;
    hLayout->setSpacing( 0 );
    hLayout->setMargin( MARGIN_SIZE );
    hLayout->addWidget( m_pTick, TICK_STRETCH );
    hLayout->addWidget( m_pName, NAME_STRTCH );
    hLayout->addWidget( m_pCipher, CIPHER_STRTCH );
    hLayout->addWidget( m_pArrow, ARROW_STRTCH );
    setLayout( hLayout );
}

/*************************************************
输入参数:true -- 选中
       false -- 未选中
功能： 设置是否选中
*************************************************************/
void HotspotLabel::setSelectState( bool bState )
{
    if( m_bSelectState != bState )
    {
        m_bSelectState = bState;
        if( m_bSelectState )
        {
            resizeContext( m_pTick,TICK_ICON,CONTEXT_ICON,HEIGHT_RATION,WIDTH_RATIO );
        }
        else
        {
            m_pTick->setPixmap( QPixmap() );
        }
    }
}

/*************************************************
返回值:true -- 选中
       false -- 未选中
功能： 设置是否选中
*************************************************************/
bool HotspotLabel::selectState( void )
{
    return m_bSelectState;
}

/*************************************************
参数： pLabel -- 需resize的控件
      context -- 内容（文本/icon路径）
      eContextType -- 内容类型
功能： resize标签内容
*************************************************************/
void HotspotLabel::resizeContext(QLabel* pLabel, const QString& context, ContextType eContextType , double dHeight, double dWidth)
{
    if( pLabel != NULL )
    {
        if( eContextType == CONTEXT_ICON )
        {
            QPixmap pixmap( context );

            if( !pixmap.isNull() )
            {
                QPixmap pixmapSet = pixmap.scaled( pLabel->width()*dWidth,
                                                  height()*dHeight,
                                                  Qt::IgnoreAspectRatio,
                                                  Qt::SmoothTransformation );//反锯齿
                pLabel->setPixmap( pixmapSet );
            }
        }
        else if( eContextType == CONTEXT_TEXT )
        {
#if 0
            QFont fontHeight = getReheightFont( pLabel->font(), height()*dHeight );

            QFontMetrics fm( fontHeight );
            QFont font = fontHeight;
            if( pLabel->width()*dWidth < fm.width( context ) )
            {
                font = getRewidthFont( pLabel->font(), context, pLabel->width()*dWidth );
            }
            pLabel->setFont( font );
#endif
        }
    }
}

/*************************************************
功能： resize事件，用来重定义图片和文字的大小
*************************************************************/
void HotspotLabel::resizeEvent(QResizeEvent* e)
{
    Q_UNUSED(e);
    //resizeContext( m_pName,m_pName->text(),CONTEXT_TEXT,HEIGHT_RATION,WIDTH_RATIO );
    resizeDisplayName();
    if( m_bSelectState )
    {
        resizeContext( m_pTick,TICK_ICON,CONTEXT_ICON,HEIGHT_RATION,WIDTH_RATIO );
    }

    if( !m_bCipher )
    {
        resizeContext( m_pCipher,LOCK_ICON,CONTEXT_ICON,LOCK_HEIGHT_RATION,LOCK_WIDTH_RATIO );
    }
    resizeContext( m_pArrow,RIGHT_ARROW_ICON,CONTEXT_ICON,HEIGHT_RATION,WIDTH_RATIO );
}

/*************************************************
功能： 点击事件，用来发射选中的wifiname
*************************************************************/
void HotspotLabel::mousePressEvent(QMouseEvent* e)
{
    Q_UNUSED(e);
    //emit sigItemPressed( m_pName->text() );
    emit sigItemPressed(m_qstrName);
    return;
}

/*************************************************
参数： NULL
功能： resize显示的名字，避免显示超界
*************************************************************/
void HotspotLabel::resizeDisplayName()
{
    QFontMetrics qfmName(AppFontManager::instance()->getAppCurFont());
    QString qstrName = qfmName.elidedText(m_qstrName, Qt::ElideRight, iNameMaxLen);
    m_pName->setText(qstrName);
    return;
}

/*************************************************
返回值:标签的名字
功能： 获取标签的名字
*************************************************************/
QString HotspotLabel::getName()
{
    return m_qstrName;
}
