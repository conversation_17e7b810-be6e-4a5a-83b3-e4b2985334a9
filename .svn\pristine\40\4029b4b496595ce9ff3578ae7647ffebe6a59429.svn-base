#ifndef DIAGNOSISMANAGER_H
#define DIAGNOSISMANAGER_H

#include <string.h>
#include <QObject>
#include <QVector>
#include <QThread>
#include <QSharedPointer>
#include "module_global.h"
#include "diagnosis/TEVdiagnosis.h"
#include "diagnosis/AEAmpMLdiagnosis.h"
#include "diagnosis/PRPSMLdiagnosis.h"


typedef enum _PDDiagType_
{
    DIAG_NONE = -1,
    DIAG_TEV = 0,
    DIAG_AE,
    DIAG_PRPS,
}PDDiagType;

typedef struct _DiagResultInfo_
{
    PDDiagType eType;
    DiagResult stDiagRet;
    QString qstrPDTypeInfo;
    QString qstrPDDescription;
    QString qstrPDSignalTypeInfos;

    _DiagResultInfo_()
    {
        eType = DIAG_NONE;
        stDiagRet.defectLevel = DEFECT_NONE;
        memset(stDiagRet.adbConfidences, 0, sizeof(stDiagRet.adbConfidences)/*SignalTypeNum * sizeof(double)*/);
        qstrPDTypeInfo = "";
        qstrPDDescription = "";
        qstrPDSignalTypeInfos = "";
    }

}DiagResultInfo;

typedef struct _TEVAmpDiagInfo_
{
    quint8 qui8TevAmpVal;
    TEVDiagnosisThreshold stTHInfo;

    _TEVAmpDiagInfo_()
    {
        qui8TevAmpVal = 0;
        stTHInfo.ucThresholdEmergency = 0;
        stTHInfo.ucThresholdMinor = 0;
        stTHInfo.ucThresholdSerious = 0;
    }

}TEVAmpDiagInfo;

typedef struct _TEVPulseDiagInfo_
{
    quint8 qui8TevAmpVal;
    quint32 qui32PerPulseCnt;
    quint32 qui32SysFrequency;
    TEVDiagnosisThreshold stTHInfo;

    _TEVPulseDiagInfo_()
    {
        qui8TevAmpVal = 0;
        qui32PerPulseCnt = 0;
        qui32SysFrequency = 0;
        stTHInfo.ucThresholdEmergency = 0;
        stTHInfo.ucThresholdMinor = 0;
        stTHInfo.ucThresholdSerious = 0;
    }

}TEVPulseDiagInfo;

typedef struct _AEAmpDiagInfo_
{
    AEAmplitude stAEAmpVal;
    UnitLevel eUnit;
    GainLevel eGain;
    AEDiagnosisThreshold stTHInfo;

    _AEAmpDiagInfo_()
    {
        memset(&stAEAmpVal, 0, sizeof(stAEAmpVal));
        eUnit = DIAG_UNIT_DB;
        eGain = GAIN_100;
        stTHInfo.iThresholdEmergency = 0;
        stTHInfo.iThresholdMinor = 0;
        stTHInfo.iThresholdSerious = 0;
    }

}AEAmpDiagInfo;

typedef struct _PRPSDiagInfo_
{
    int iPhaseNum;
    int iPeriodNum;
    double dThresholdDbVal;
    double dSpecMinVal;
    double dSpecMaxVal;
    QVector<double> qvtDataIn;

    _PRPSDiagInfo_()
    {
        iPhaseNum = 0;
        iPeriodNum = 0;
        dThresholdDbVal = 0;
        dSpecMinVal = 0;
        dSpecMaxVal = 0;
        qvtDataIn.clear();
    }

}PRPSDiagInfo;

typedef struct _PDSignalTypeInfo_
{
    double dConfVal;
    PDType eType;

    _PDSignalTypeInfo_()
    {
        dConfVal = 0.0;
        eType = Normal;
    }

}PDSignalTypeInfo;


class MODULESHARED_EXPORT DiagnosisManager : public QObject
{
    Q_OBJECT
public:
    //模块实例
    static DiagnosisManager* instance();

    //初始化机器学习诊断模型
    void initMLDiagModel();

    //释放机器学习诊断模型
    void freeMLDiagModel();

    //阈值法诊断TEV幅值
    void diagTEVAmpByThreshold(const TEVAmpDiagInfo &stDiagInfo, DiagResultInfo &stDiagRetInfo);

    //阈值法诊断TEV脉冲
    void diagTEVPulseByThreshold(const TEVPulseDiagInfo &stDiagInfo, DiagResultInfo &stDiagRetInfo);

    //异步方式阈值法诊断AE幅值
    void diagAEAmpByThreshold(const AEAmpDiagInfo &stDiagInfo);

    //异步方式诊断PRPS
    void diagPRPSData(const PRPSDiagInfo &stDiagInfo);

    //直接调用阈值法诊断AE幅值
    void diagAEAmpByThresholdDirectly(const AEAmpDiagInfo &stDiagInfo, DiagResultInfo &stDiagRetInfo);

    //直接调用诊断PRPS
    void diagPRPSDataDirectly(const PRPSDiagInfo &stDiagInfo, DiagResultInfo &stDiagRetInfo);

    //根据回放信息获取诊断信号类型信息
    QString getDiagInfoByPlayback(const QString &qstrPDSignalTypeInfos, PDDiagType eDiagType);

    //获取局放诊断信号类型描述信息
    QString getPDDescription(const DiagResult &stDiagRet, QString &qstrPDTypeInfo, QString &qstrPDSignalTypeInfos, PDDiagType eDiagType);

signals:
    //释放诊断结果
    void sigDiagResultInfo(QSharedPointer<DiagResultInfo> qspDiagResultInfo);

    //阈值法诊断AE幅值
    void sigDiagAEAmpByThreshold(AEAmpDiagInfo stDiagInfo);

    //诊断PRPS
    void sigDiagPRPSData(PRPSDiagInfo stDiagInfo);

private slots:
    //阈值法诊断AE幅值
    void onDiagAEAmpByThreshold(AEAmpDiagInfo stDiagInfo);

    //诊断PRPS
    void onDiagPRPSData(PRPSDiagInfo stDiagInfo);

private:
    //构造函数和析构函数
    explicit DiagnosisManager(QObject *parent = 0);
    ~DiagnosisManager();

    //阈值法诊断AE幅值
    bool pfnDiagAEAmpByThreshold(const AEAmpDiagInfo &stDiagInfo, DiagResultInfo *pDiagRetInfo);

    //诊断PRPS
    bool pfnDiagPRPSData(const PRPSDiagInfo &stDiagInfo, DiagResultInfo *pDiagRetInfo);

    //PRPS机器学习诊断
    bool pfnMLDiagPRPSData(const PRPSDiagInfo &stDiagInfo, DiagResultInfo *pDiagRetInfo);

    //获取局放诊断信号类型描述信息
    QString getPDDescription(const PDType eType, QString &qstrPDTypeInfo, PDDiagType eDiagType);

    //比较局放诊断信息
    static bool comparePDSignalTypeInfo(const PDSignalTypeInfo &stPDSignalInfo1, const PDSignalTypeInfo &stPDSignalInfo2);

private:
    QThread *m_pThread;

};

#endif // DIAGNOSISMANAGER_H
