/*
* Copyright (c) 2016.03，南京华乘电气科技有限公司
* All rights reserved.
*
* LabelButtonBar.h
*
* 初始版本：1.0
* 作者：赵勇军
* 创建日期：2016年3月7日
* 修改日期：2016年11月28日
* 作者：邵震宇
*       重构
* 摘要：控制按钮栏定义
*       1)从ButtonBar派生
*       2）竖排显示
*       3）支持LabelButton系列（RadioLabelButton,CmdLabalButton,SliderLabelButton)等
* 当前版本：1.0
*/

#ifndef LABELBUTTONBAR_H
#define LABELBUTTONBAR_H
#include "ButtonBar.h"

class QScrollArea;
class WIDGET_EXPORT LabelButtonBar : public ButtonBar
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        strTitle -- 标题
        parent -- 父控件
    *************************************************************/
    LabelButtonBar( const QString& strTitle, QWidget *parent = 0 );

    virtual ~LabelButtonBar();

    /*************************************************
    功能： 添加按钮
    输入参数:
        pButton -- 按钮指针
        id -- 按钮ID（注：如果为-1，则按钮用自身设置的ID）
    *************************************************************/
    virtual void addButton( ControlButton* pButton );

    /*************************************************
    功能： 删除所有按键
    *************************************************************/
    virtual void removeAll( void );

signals:
    /***********************************************
     * 释放更多界面关闭信息
     * **************************************************/
    void sigViewClosed();

protected:
    /*************************************************
    功能： 隐藏事件
    输入参数:
        event -- 事件
    *************************************************************/
    void hideEvent( QHideEvent *event );

    /*************************************************
     * 功能：关闭事件
     * 输入参数：
     *      pEvent：关闭事件
     * ******************************************************/
    void closeEvent(QCloseEvent *pEvent);

    /************************************************
     * 函数名   : 键盘事件
     * 输入参数 :
     *      event -- 事件
     ************************************************/
    virtual void keyPressEvent(QKeyEvent* pEvent);

    bool eventFilter(QObject* pWatched, QEvent* pEvent);

private:
    QScrollArea* m_pScrollArea;
    QVBoxLayout* m_pButtonLayout;

    int m_iLastPointY;
    bool m_bPressed;
    bool m_bMouseMoving;
    int m_iPressedIndex;
};

#endif

