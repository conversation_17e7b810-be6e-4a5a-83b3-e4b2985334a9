#-------------------------------------------------
#
# Project created by Qt<PERSON><PERSON> 2019-11-04T11:04:06
#
#-------------------------------------------------

QT       -= gui
QT += xml

GCC_VERSION = $$system("g++ -dumpversion")
lessThan(GCC_VERSION, 4.7) {
    QMAKE_CXXFLAGS += -std=c++0x
} else {
    QMAKE_CXXFLAGS += -std=c++11
}

greaterThan(QT_MAJOR_VERSION, 4): CONFIG += C++11

TARGET = dataspecification
TEMPLATE = lib

CONFIG(debug, debug|release) {
    TARGET = $$TARGET"_d_"$$QT_MAJOR_VERSION$$QT_MINOR_VERSION$$QT_PATCH_VERSION
} else {
    TARGET = $$TARGET"_r_"$$QT_MAJOR_VERSION$$QT_MINOR_VERSION$$QT_PATCH_VERSION
}

DEFINES += DATASPECIFICATION_LIBRARY

# The following define makes your compiler emit warnings if you use
# any feature of Qt which has been marked as deprecated (the exact warnings
# depend on your compiler). Please consult the documentation of the
# deprecated API in order to know how to port your code away from it.
DEFINES += QT_DEPRECATED_WARNINGS
#DEFINES += ARM

# You can also make your code fail to compile if you use deprecated APIs.
# In order to do so, uncomment the following line.
# You can also select to disable deprecated APIs only up to a certain version of Qt.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

unix {
    if(contains(DEFINES, ARM)) {
        LIBS += -L"$$PWD/lib/arm" -lCrypto
    }
    else {
        LIBS += -L"$$PWD/lib/linux" -lCrypto
    }
}

win32 {
    CONFIG(debug, debug|release) {
        LIBS += -L"$$PWD/lib/win32" -lCryptod
    } else {
        LIBS += -L"$$PWD/lib/win32" -lCrypto
    }
}

INCLUDEPATH += $$PWD/include

SOURCES += \
        ae/aeampspectrum.cpp \
        ae/aephasespectrum.cpp \
        ae/aepulsespectrum.cpp \
        ae/aewavespectrum.cpp \
        common/binaryprocesshelper.cpp \
        common/dataspecificationutils.cpp \
        common/jsonprocesshelper.cpp \
        common/xmldocument.cpp \
        common/xmlprocesshelper.cpp \
        crcutil/crcutil.cpp \
        currentamplitude/currentampspectrum.cpp \
        dataspecification.cpp \
        infrared/infraredspectrum.cpp \
        mechanical/mechanicalcoilspectrum.cpp \
        mechanical/mechanicalmotorspectrum.cpp \
        mechanical/mechanicalspectrum.cpp \
        mechanical/mechanicalswitchspectrum.cpp \
        private/aespectrumprivatedefine.cpp \
        private/currentampspectrumprivatedefine.cpp \
        private/infraredspectrumprivatedefine.cpp \
        private/mechanicalspectrumprivatedefine.cpp \
        private/prpsprpdspectrumprivatedefine.cpp \
        private/spectrumdatafileprivatedefine.cpp \
        private/spectrumprivatedefine.cpp \
        private/tevspectrumprivatedefine.cpp \
        prps/prpdspectrum.cpp \
        prps/prpsspectrum.cpp \
        spectrum.cpp \
        tev/tevampspectrum.cpp

HEADERS += \
        ae/aeampspectrum.h \
        ae/aephasespectrum.h \
        ae/aepulsespectrum.h \
        ae/aespectrumdefine.h \
        ae/aewavespectrum.h \
        common/binaryprocesshelper.h \
        common/dataspecificationutils.h \
        common/jsonprocesshelper.h \
        common/xmldocument.h \
        common/xmlprocesshelper.h \
        crcutil/crcutil.h \
        currentamplitude/currentampspectrum.h \
        currentamplitude/currentampspectrumdefine.h \
        dataspecification.h \
        dataspecification_def.h \
        dataspecification_global.h  \
        infrared/infraredspectrum.h \
        infrared/infraredspectrumdefine.h \
        mechanical/mechanicalcoilspectrum.h \
        mechanical/mechanicalmotorspectrum.h \
        mechanical/mechanicalspectrum.h \
        mechanical/mechanicalspectrumdefine.h \
        mechanical/mechanicalswitchspectrum.h \
        private/aespectrumprivatedefine.h \
        private/currentampspectrumprivatedefine.h \
        private/infraredspectrumprivatedefine.h \
        private/mechanicalspectrumprivatedefine.h \
        private/prpsprpdspectrumprivatedefine.h \
        private/spectrumdatafilecommondefine.h \
        private/spectrumdatafileprivatedefine.h \
        private/spectrumprivatedefine.h \
        private/tevspectrumprivatedefine.h \
        prps/prpdspectrum.h \
        prps/prpsprpdspectrumdefine.h \
        prps/prpsspectrum.h \
        spectrum.h \
        spectrumdatafiledefine.h \
        spectrumdefine.h \
        spectrumfactory.h \
        tev/tevampspectrum.h \
        tev/tevspectrumdefine.h

unix {
    target.path = /usr/lib
    INSTALLS += target
}
