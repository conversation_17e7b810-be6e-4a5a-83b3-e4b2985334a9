/*
* Copyright (c) 2017.11，南京华乘电气科技有限公司
* All rights reserved.
*
* pdswavedatamap.h
*
* 初始版本：1.0
* 作者：wujun
* 创建日期：2017年11月10日
* 摘要：波形数据（适用于CAWave数据、PDS-G1500波形检测数据等）
*/

#ifndef PDSWaveDATAMAP_H
#define PDSWaveDATAMAP_H

#include <QObject>
#include "datafiledefine.h"
#include "datamap.h"
#include "pdsmapdefine.h"
#include "datafile_global.h"

//ca wave
class DATAFILESHARED_EXPORT WaveDataMap : public DataMap
{
public:
    /*************************************************
    功能： 构造函数
    *************************************************************/
    WaveDataMap();

    /*************************************************
    功能： 析构函数
    *************************************************************/
    ~WaveDataMap();

    /*************************************************
    功能： 设置图谱的extinformation
    输入参数：
            pMapInfo -- 指向m图谱的extinformation的指针
    *************************************************************/
    void setInfo(PDSMapNS::PDSWaveMapInfo* pMapInfo);

    /*************************************************
    功能： 读取图谱的extinformation
    输出参数：
            pMapInfo -- 指向图谱的extinformation的指针
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool getInfo(PDSMapNS::PDSWaveMapInfo* pMapInfo);

    /*************************************************
    功能： 设置图谱的data
    输入参数：
            pData -- 指向图谱的data的指针
            iCount -- 数据个数
    *************************************************************/
    void setData(void* pData, quint32 iCount);

    /*************************************************
    功能： 读取图谱的data
    输出参数：
            pData -- 指向图谱的data的指针
            iCount -- 数据个数
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool getData(void* pData, quint32 iCount);

    void setBinaryFilePath(const QString &strFile);

protected:
    /*************************************************
    功能： 解析map的扩展字段，从xml读取信息到m_stExt中
    输入参数：
            pDoc -- xml文件指针；
            strRootTag -- 子节点名
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool parseMapExtXML( XMLDocument * pDoc, const QString &strRootTag );

    /*************************************************
    功能： 解析map的data字段
    输出参数：
            baData -- 图谱数据的xml文本内容
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool parseMapDataXML( XMLDocument * pDoc, const QString &strRootTag );

    /*************************************************
    功能： 生成map扩展部分的xml文本
    输入参数：
            baData -- 保存图谱数据的xml文本内容
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool saveMapExtXML(XMLDocument *pDoc, QDomElement &element,bool bCrypt);

    /*************************************************
    功能： 生成map数据部分的xml文本
    输入参数：
            pDoc -- xml文件指针
            element -- 节点信息
            bCrypt -- 是否加密
    返回值:
            true: 成功 ;false:失败
    *************************************************************/
    bool saveMapDataXML(XMLDocument *pDoc, QDomElement &element,bool bCrypt);

    /*************************************************
    功能： 获取图谱根节点的标签名
    返回值:
            图谱对应的标签名
    *************************************************************/
    QString mapRootTag();

private:
    PDSMapNS::PDSWaveMapInfo m_stExt;   //信息
    void* m_pData;                      //数据
    QString m_strBinaryDataFile;
};

#endif // PDSWaveDATAMAP_H
