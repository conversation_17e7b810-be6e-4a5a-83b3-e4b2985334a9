/*
* Copyright (c) 2021.10，南京华乘电气科技有限公司
* All rights reserved.
*
* pdacurrenttestdataview.h
*
* 初始版本：1.0
* 作者：洪澄
* 创建日期：2021年10月27日
* 摘要：电流类型测试项列表

* 当前版本：1.0
*/

#ifndef PDACURRENTTESTDATAVIEW_H
#define PDACURRENTTESTDATAVIEW_H

#include <QMutex>
#include "PDAUi/PDAUiView/pdalistview.h"
#include "PDAUi/PDAUiBean/pdalistchart.h"
#include "pda/pda.h"
#include "pda/pdaservice.h"
#include "multiservice/multiuserservice.h"
#include "currentdetection/currentdetectiondefine.h"

class PDACurrentTestDataView : public PDAListView
{
    Q_OBJECT
public:
    /*************************************************
    功能： 构造函数
    输入参数:
        parent:父控件指针
    *************************************************************/
    explicit PDACurrentTestDataView(QWidget *parent = 0);

    ~PDACurrentTestDataView();

protected:
    /*************************************************
    功能： 定时器处理函数
    输入参数：
            e -- 定时事件
    *************************************************************/
    void timerEvent( QTimerEvent* e );

    /****************************
    功能： 处理showEvent事件
    输入参数:
           event -- showEvent
    *****************************/
    void showEvent(QShowEvent *event);

    /*************************************************
    功能： 响应S键事件
    *************************************************************/
    virtual void onSKeyPressed();

protected slots:
    /*************************************************
    功能： 槽，响应条目被点击后的事件
    输入参数：
            strItem -- 条目名称
    *************************************************************/
    void onItemClicked( int id );

private slots:
    /*************************************************
    功能： 槽函数， 测试项列表自动跳转界面延时显示
    （为在跳转前后能够短暂回到测试项列表，避免突兀的在多个测试界面之间跳转）
    *************************************************************/
    void onAutoSwitchViewDelay();

    /*************************************************
    功能： 槽函数， 响应自动跳转状态发生变化的信号
    *************************************************************/
    void onAutoSwitched();

    /*************************************************
    功能： 响应读取的数据
    输入参数： data：电流数据
    *************************************************************/
    void onDataRead(CurrentDetection::CurrentDetectionData data, MultiServiceNS::USERID userId);

    /*************************************************
    功能： 响应信号状态改变
    输入参数： eSignalState：信号状态
    *************************************************************/
    void onSignalChanged(Module::SignalState eSignalState);

private:
    /*************************************************
    功能： 刷新列表
    输入参数:测点
    *************************************************************/
    void refreshList( const ItemTestPoint& testPoint );

    /*************************************************
    功能： 获取测试项的相关信息
    输入参数:testPoint -- 测点
    返回值：条目显示需要的测试项信息
    *************************************************************/
    QList<PDAListChart::ListItemInfo> testDataInfo( const ItemTestPoint& testPoint );

    /*************************************************
    功能： 统计测试完成状态，界面显示相应提示
    *************************************************************/
    void checkTestState( void );

    /*************************************************
    功能： 从数据文件中获取电流值
    输入参数: qstrDataFilePath -- 数据文件路径
    返回值：电流值
    *************************************************************/
    float getCurrentDataFromDataFile(const QString& qstrDataFilePath);

    /*************************************************************
     * 功能：通过巡检位置获取测试项名称
     * 输入参数：
     *         ePatrolPosition：巡检位置
     * 返回值:
     *         QString：测试项名称
     * ************************************************************/
    QString getTestDataNameFromPatrolPosition(PDAServiceNS::PatrolPosition ePatrolPosition);

    /*************************************************************
     * 功能：保存数据
     * ************************************************************/
    void saveData();

private:
    PDATask *m_pCurrentTask;//pda 当前task对象
    MultiServiceNS::USERID m_iUserId; // 服务模块分配的用户id，用以识别用户

    qint32 m_iAutoSwitchTimerId;    // 是否自动跳转的定时器id
    qint32 m_iTriggerTimerId;    // 触发后台自动跳转接口的定时器id
    int m_iTestDataPos;             // 首个未经测试的测试项位置

    bool m_bClicked;
    QMutex m_mtClick;
    QMutex m_mt4ItemInfo;   //加锁处理，避免界面崩溃
    bool m_bSaving; // 是否正在保存数据
    QSet<int> m_beforeTestedIndexs; // 记录之前已测试的索引
    QSet<int> m_currentTestedIndexs; // 记录本次已测试的索引
    Module::SignalState m_eSignalState; // 调理器信号状态
};

#endif // PDACURRENTTESTDATAVIEW_H
