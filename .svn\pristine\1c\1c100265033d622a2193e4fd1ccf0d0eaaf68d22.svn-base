﻿#include "tevamptestview.h"
#include "messageBox/msgbox.h"
#include "dataSave/DataFileInfos.h"
#include "controlButton/SliderButton.h"
#include "window/Window.h"
#include "tev/tevconfig.h"
#include "tev/tevviewdefine.h"
#include "playbackView/PlayBackView.h"
#include "dataSave/DataStructures.h"
#include "pda/pdaservice.h"
#include "equipmentinfo.h"
#include "systemsetting/systemsetservice.h"
#include "View.h"
#include "customaccesstask/taskdefine.h"
#include "customaccessUi/commonitemview/commonitemlistview.h"
#include "appconfig.h"
#include "diagnosismgr/diagnosismanager.h"
#include "dataspecification/dataspecification.h"
#include "dataspecification/tev/tevampspectrum.h"
#include "customaccessUi/customaccessui_func.h"

typedef enum _TEVAmpTestButton_
{
    BUTTON_TEV_SAMPLE,   //0
    BUTTON_TEV_SAVE_DATA,   //1
    BUTTON_TEV_LOAD_DATA,
    BUTTON_MORE_CONFIG,//更多配置  3

    //more
    BUTTON_TEV_PULSE_LEN,
    BUTTON_TEV_RED_ALERT,
    BUTTON_TEV_YELLOW_ALERT,
    BUTTON_TEV_DELETE_DATA,//删除数据

}TEVAmpTestButton;

//脉冲时长
const ButtonInfo::RadioValueConfig s_TEVPulseLen =
{
    TEV::TEXT_PULSE_LEN_OPTIONS, sizeof(TEV::TEXT_PULSE_LEN_OPTIONS) / sizeof(char*)
};

//黄色报警
const ButtonInfo::SliderValueConfig s_TEVYellowAlertCfg =
{
    TEV::YELLOW_ALERT_MIN, TEV::YELLOW_ALERT_MAX, TEV::ALERT_STEP
};

//红色报警
const ButtonInfo::SliderValueConfig s_TEVRedAlertCfg =
{
    TEV::RED_ALERT_MIN, TEV::RED_ALERT_MAX, TEV::ALERT_STEP
};

//控制按钮定义
const ButtonInfo::Info s_TEVButtonInfo[] =
{
    {BUTTON_TEV_SAMPLE, { ButtonInfo::COMMAND, TEV::TEXT_STOP, NULL, "", NULL } },//新增测试项
    {BUTTON_TEV_SAVE_DATA, {ButtonInfo::COMMAND, TEV::TEXT_SAVE_DATA, NULL, ":/images/sampleControl/save.png", NULL}},
    {BUTTON_TEV_LOAD_DATA, {ButtonInfo::COMMAND, TEV::TEXT_LOAD_DATA, NULL, ":/images/sampleControl/save.png", NULL}},
    {BUTTON_MORE_CONFIG, { ButtonInfo::COMMAND, TEV::TEXT_MORE, NULL, ":/images/sampleControl/moreconfig.png", NULL } },//更多配置
};

//"更多.."按钮定义
const ButtonInfo::Info s_TEVButtonInfoMore[] =
{
    {BUTTON_TEV_PULSE_LEN, {ButtonInfo::RADIO, TEV::TEXT_PULSE_LEN, NULL, ":/images/sampleControl/pulseCountTimeLength.png", &s_TEVPulseLen}},
    {BUTTON_TEV_RED_ALERT, {ButtonInfo::FIXED_STEP_SLIDER, TEV::TEXT_RED_ALERT, NULL, ":/images/sampleControl/alert.png", &s_TEVRedAlertCfg}},
    {BUTTON_TEV_YELLOW_ALERT, {ButtonInfo::FIXED_STEP_SLIDER, TEV::TEXT_YELLOW_ALERT, NULL, ":/images/sampleControl/alert.png", &s_TEVYellowAlertCfg}},
    { BUTTON_TEV_DELETE_DATA, { ButtonInfo::COMMAND, TEV::TEXT_DELETE_DATA, NULL, "", NULL } },//删除数据
};

const INT32 TEV_CHART_HEIGHT = 485;// + 125;
const int INVALID_USER = -1;

/*************************************************
函数名： TEVAmpTestView(const QString &strTitle, QWidget *parent)
输入参数： strTitle：标题
          parent：父指针
输出参数： NULL
返回值： NULL
功能： 构造函数
*************************************************************/
TEVAmpTestView::TEVAmpTestView(const QString &strTitle, SubTask *pSubTask, const QString &strGapId, QString const& strID, QWidget *parent)
    :SampleChartView(strTitle, parent),m_strGapId( strGapId ),m_cAmpValue(0)
{
    //temp wq
    setFixedSize( Window::WIDTH,Window::HEIGHT );

    TevPulseService* pService = TevPulseService::instance();
    setService(pService);

    m_strID = strID;
    m_pSubTask = pSubTask;

    m_testedCount = 0;
    m_strBayName = "";
    m_strTestPointName = "";
    for(quint16 i=0; i < pSubTask->testPoints( strGapId ).size(); i++)
    {
        if( strID == pSubTask->testPoints( strGapId ).at(i).s_strId )
        {
            m_vecTestData = pSubTask->testPoints( strGapId ).at(i).s_vData;
            m_testedCount = m_vecTestData.size();
            m_strBayName = pSubTask->gapInfo( strGapId ).s_strName;
            m_sTestPoint = pSubTask->testPoints( strGapId ).at(i);
            m_strTestPointName = pSubTask->testPoints( strGapId ).at(i).s_strName;
        }
    }
    QList<QString> lStrFileNames;
    for( quint16 i=0; i< m_vecTestData.size(); i++)
    {
        lStrFileNames.append( m_vecTestData.at(i).s_strFileName);
    }
    m_pCommonItemListView = new CommonItemListView(QObject::trUtf8("Test Data List"),lStrFileNames);
    m_pCommonItemListView->hide();
    connect(m_pCommonItemListView, SIGNAL(sigItemSelected( qint32)), this, SLOT(onItemActivated( qint32)));

    //初始化数据
    initDatas();

    //新建图谱
    ChartWidget *pChartWidget = createChart(parent);
    setChart(pChartWidget);

    //创建按钮栏
    PushButtonBar* pButtonBar = createButtonBar(TEV::CONTEXT, s_TEVButtonInfo, sizeof(s_TEVButtonInfo) / sizeof(ButtonInfo::Info));
    m_pSampleBtn = buttonBar()->button(BUTTON_TEV_SAMPLE);

    // 创建更多设置栏
    createMoreConfigButtonBar(TEV::CONTEXT, s_TEVButtonInfoMore, sizeof(s_TEVButtonInfoMore)/sizeof(ButtonInfo::Info));

    //重置报警值范围
    resetAlarmScope();

    //设置按钮数据
    setButtonDatas();

    //设置图谱数据
    setChartDatas();

    //设置工作参数
    setWorksets();

    //显示上次的测试数据
    connect(pService, SIGNAL(sigData(TEV::PulseData,MultiServiceNS::USERID)),
            this, SLOT(onDataRead(TEV::PulseData,MultiServiceNS::USERID)));

    m_bPlayBacked = false;
    startSample();
    m_pChart->clear();
}

/*************************************************
函数名： ~TEVAmpView()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 析构函数
*************************************************************/
TEVAmpTestView::~TEVAmpTestView()
{
    saveConfig();
    stopSampleService();

    if(NULL != m_pCommonItemListView)
    {
        delete m_pCommonItemListView;
        m_pCommonItemListView = NULL;
    }
}



/************************************************
 * 函数名   : loadTeatDataFile
 * 输入参数 : qsFile---数据文件存放路径
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     :读取数据文件里的测试数据
 ************************************************/
bool TEVAmpTestView::loadTestDataFile(const QString &qsFile, qint8 &cValue, qint8 &cMax)
{
    bool isSuccess = true;
    DataSpecificationNS::DataSpecification dataSpecification;
    dataSpecification.setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());
    //step1 open data file
    dataSpecification.parseBinaryFromFile(qsFile);

    DataSpecificationNS::TEVAmpSpectrum* pTEVAmpSpectrum = dynamic_cast<DataSpecificationNS::TEVAmpSpectrum*>(dataSpecification.spectrum(DataSpecificationNS::SPECTRUM_CODE_TEV_AMP));
    if(NULL == pTEVAmpSpectrum)
    {
        isSuccess = false;
        return isSuccess;
    }

    //设置图谱数据
    DataSpecificationNS::TEVAmpData stTEVAmpData;
    pTEVAmpSpectrum->getTEVAmpData(stTEVAmpData);

    cValue = (INT8)(Module::dealFloatPrecision(stTEVAmpData.fTEVAmpValue, 0))/*(stTEVAmpData.fTEVAmp+0.5f)*/;
    cMax = (INT8)(Module::dealFloatPrecision(stTEVAmpData.fTEVMaxValue, 0))/*(stTEVAmpData.fTEVMax+0.5f)*/;
    return isSuccess;
}

/*************************************************
函数名： initDatas()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 初始化数据
*************************************************************/
void TEVAmpTestView::initDatas()
{
    m_pConfig = ConfigManager::instance()->config();
    m_pConfig->beginGroup(Module::GROUP_TEV);
    m_ucPulseLen = m_pConfig->value(TEV::KEY_PULSE_LEN, TEV::GROUP_TEV_PULSE).toUInt();
    m_ucYellowAlert = m_pConfig->value(TEV::KEY_YELLOW_ALERT).toUInt();
    m_ucRedAlert = m_pConfig->value(TEV::KEY_RED_ALERT).toUInt();
    m_pConfig->endGroup();

    MultiServiceNS::SampleUser user;
    user.eSampleType = MultiServiceNS::SPECTRUM_TEV_PULSE;
    addUser( user );
    m_eOperationType = OPERATION_INVALID;
}

/*************************************************
函数名： setChartDatas()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 设置图谱数据
*************************************************************/
void TEVAmpTestView::setChartDatas()
{
    m_eDiagRet = CustomAccessTaskNS::DiagNormal;
    m_pChart->setAlarm(m_ucYellowAlert, m_ucRedAlert);
    m_pChart->setRunningStatus(isSampling());
}

/*************************************************
函数名： saveConfig()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 保存配置信息
*************************************************************/
void TEVAmpTestView::saveConfig()
{
    m_pConfig->beginGroup(Module::GROUP_TEV);
    m_pConfig->setValue(m_ucYellowAlert, TEV::KEY_YELLOW_ALERT);
    m_pConfig->setValue(m_ucRedAlert, TEV::KEY_RED_ALERT);
    m_pConfig->setValue(m_ucPulseLen, TEV::KEY_PULSE_LEN, TEV::GROUP_TEV_PULSE);
    m_pConfig->endGroup();
}

/*************************************************
函数名： onDataRead(TEV::AmplitudeData data)
输入参数： data：幅值数据
输出参数： NULL
返回值： NULL
功能： 响应读取的数据
*************************************************************/
void TEVAmpTestView::onDataRead(TEV::PulseData data, MultiServiceNS::USERID userId)
{
    if((getUserId() != INVALID_USER) && (getUserId() == userId) && isSampling())
    {
        m_pChart->addSample(data.cAmpValue);

        m_cAmpValue = data.cAmpValue;
        m_uiPulseNum = data.uiPulseNum;
        m_uiPerPulseNum = data.uiPerPulseNum;
        m_uiPDSeverity = data.uiPDSeverity;

        //边界处理
        m_cAmpValue = (TEV::cMinAmpValue > m_cAmpValue) ? TEV::cMinAmpValue : m_cAmpValue;
        m_cAmpValue = (TEV::cMaxAmpValue < m_cAmpValue) ? TEV::cMaxAmpValue : m_cAmpValue;

        diagDataInfo();
    }
    else
    {
        //illegal
        logError(QString("m_iUserId = %1, userId = %2, isSampling = %3").arg(getUserId()).arg(userId).arg(isSampling()));
    }
}

/*************************************************
函数名： setWorksets()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 设置工作参数
*************************************************************/
void TEVAmpTestView::setWorksets()
{
    TEVWorkSettings stTEVWorkSettings;
    stTEVWorkSettings.ucWorkMode = (UINT8)TEV::MODE_PULSE;
    stTEVWorkSettings.ucTime = m_ucPulseLen;
    stTEVWorkSettings.ucBackGround = m_ucYellowAlert;
    stTEVWorkSettings.ucAlarm = m_ucRedAlert;

    if (MultiUserService* pService = service())
    {
        TevPulseService* pTevPulseService =  dynamic_cast<TevPulseService*>(pService);
        if (pTevPulseService)
        {
            pTevPulseService->setWorksets(stTEVWorkSettings);
        }
    }
    m_eDiagRet = CustomAccessTaskNS::DiagNormal;
    return;
}

/************************************************
 * 函数名   : createChart
 * 输入参数 : parent---父控件
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 创建图谱
 ************************************************/
ChartWidget* TEVAmpTestView::createChart(QWidget *parent)
{
    Q_UNUSED(parent)
    ChartWidget *pWidget = new ChartWidget;
    QVBoxLayout *vLayout = new QVBoxLayout;
    vLayout->setContentsMargins(0, 0, 0, 0);

    //已测次数
    m_pTestedNumLabel = new QLabel;
    m_pBayNameLabel = new QLabel;
    m_pTestPointNameLabel = new QLabel;

    //设置style
    QFont font = m_pTestedNumLabel->font();
    font.setPointSize(20);

    m_pTestedNumLabel->setFont(font);
    m_pTestedNumLabel->setText(QObject::trUtf8("Tested Count: ") + QString::number(m_testedCount) );

    m_pBayNameLabel->setFont(font);
    m_pBayNameLabel->setText(QObject::trUtf8("Bay Name: ") + m_strBayName );

    m_pTestPointNameLabel->setFont(font);
    m_pTestPointNameLabel->setText(QObject::trUtf8("Test Point Name: ") + m_strTestPointName );

    m_pLoadFileName = new QLabel;
    m_pLoadFileName->setFont(font);

    //图谱
    m_pChart = new HistogramChart(TEV::AMP_MAX_COLUMNS, TEV::CHART_MIN_VALUE, TEV::CHART_MAX_VALUE, TEV_CHART_HEIGHT);

    QVBoxLayout *pTestInfoLayout = new QVBoxLayout;
    pTestInfoLayout->addWidget(m_pTestedNumLabel,Qt::AlignLeft);
    pTestInfoLayout->addWidget(m_pBayNameLabel,Qt::AlignLeft);
    pTestInfoLayout->addWidget(m_pTestPointNameLabel,Qt::AlignLeft);
    pTestInfoLayout->addWidget(m_pLoadFileName,Qt::AlignLeft);

    //    QRect pos(m_pChart->pos().x(),m_pChart->pos().y(), width(),35);
    //    pTestInfoLayout->setGeometry(pos);

    //vLayout->addWidget(m_pStationLabel);
    vLayout->addLayout(pTestInfoLayout);
    //    vLayout->addWidget(m_pTestPointLabel);
    vLayout->addWidget(m_pChart);
    pWidget->setLayout(vLayout);
    pWidget->setFixedWidth(Window::WIDTH);

    return pWidget;
}

/*************************************************
函数名： resetAlarmScope()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 重置报警值范围
*************************************************************/
void TEVAmpTestView::resetAlarmScope()
{
    SliderButton *pYellowAlarmButton = (SliderButton*)(buttonBar()->button(BUTTON_TEV_YELLOW_ALERT));
    pYellowAlarmButton->setRange(TEV::YELLOW_ALERT_MIN, m_ucRedAlert - 1, TEV::ALERT_STEP);
    pYellowAlarmButton->setValue(m_ucYellowAlert);

    SliderButton *pRedAlarmButton = (SliderButton*)(buttonBar()->button(BUTTON_TEV_RED_ALERT));
    pRedAlarmButton->setRange(m_ucYellowAlert + 1, TEV::RED_ALERT_MAX, TEV::ALERT_STEP);
    pRedAlarmButton->setValue(m_ucRedAlert);
}

/*************************************************
函数名： setButtonDatas()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 设置按钮数据
*************************************************************/
void TEVAmpTestView::setButtonDatas()
{
    ((PopupButton*)(buttonBar()->button(BUTTON_TEV_PULSE_LEN)))->setValue(m_ucPulseLen - 1);
    ((PopupButton*)(buttonBar()->button(BUTTON_TEV_YELLOW_ALERT)))->setValue(m_ucYellowAlert);
    ((PopupButton*)(buttonBar()->button(BUTTON_TEV_RED_ALERT)))->setValue(m_ucRedAlert);
}

/*************************************************
函数名： onSKeyPressed()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应S键事件
*************************************************************/
//void TEVAmpTestView::onSKeyPressed()
//{
//    bool bSuccess = saveTestData();

//    QFileInfo fileInfo(m_strSavedPath);
//    PushButtonBar* pBtnBar = buttonBar();
//    QPoint centerPoint;
//    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
//    centerPoint.setY(pBtnBar->pos().y()/*+pBtnBar->height()/2*/);

//    QString qstrFileName = CustomAccessTaskNS::testFileNameFromFile(fileInfo.fileName());
//    QString strInfo = bSuccess ? qstrFileName : QObject::trUtf8("Save failure!");
//    MsgBox::information( "", strInfo, centerPoint );
//    return;
//}

/*************************************************
功能： 保存数据
输入参数：
        stationName -- 变电站名
        deviceName -- 设备名
返回：
        保存后的文件名
*************************************************************/
bool TEVAmpTestView::saveTestData( )
{
    bool bSuccess = false;

    if(!(SystemSetService::instance()->storageOperEnable()))
    {
        logWarning("unable to operate storage space");
        return bSuccess;
    }

    diagDataInfo(true);

    DataSpecificationNS::DataSpecification* pDataSpecification = new DataSpecificationNS::DataSpecification;//当前数据文件
    pDataSpecification->setDataSpecificationVersion(SystemSetService::instance()->getDataSpecificationVersion());
    pDataSpecification->setCompanyCode("1.1");
    pDataSpecification->setInternalVersion("1.1.0.0");
    //创建图谱保存对象
    DataSpecificationNS::TEVAmpSpectrum* pTEVAmpSpectrum = new DataSpecificationNS::TEVAmpSpectrum;

    //设置头部信息
    pTEVAmpSpectrum->setSpectrumCharacter(DataSpecificationNS::PROPERTY_TEST);

    //设置ext信息
    pTEVAmpSpectrum->setStorageDataType(DataSpecificationNS::DATA_TYPE_FLOAT);
    pTEVAmpSpectrum->setDetectionChannelIdentification(TEV::TEST_CHANNEL_ID);

    DataSpecificationNS::TEVAmpExtInformation stTEVAmpExtInformation;
    stTEVAmpExtInformation.eAmpUnit = DataSpecificationNS::AMP_UNIT_DB;
    stTEVAmpExtInformation.fAmpLowerLimit = TEV::CHART_MIN_VALUE;
    stTEVAmpExtInformation.fAmpUpperLimit = TEV::CHART_MAX_VALUE;

    pTEVAmpSpectrum->setTEVAmpExtInformation(stTEVAmpExtInformation);

    pTEVAmpSpectrum->setEquipmentName(m_pSubTask->gapInfo(m_strGapId).s_strName);
    pTEVAmpSpectrum->setEquipmentCode(m_pSubTask->gapInfo(m_strGapId).s_strId);
    pTEVAmpSpectrum->setTestPointName(m_sTestPoint.s_strName);
    pTEVAmpSpectrum->setTestPointCode(m_sTestPoint.s_strId);

    //设置数据内容
    DataSpecificationNS::TEVAmpData stTEVAmpData;
    stTEVAmpData.fTEVAmpValue = m_cAmpValue;
    stTEVAmpData.fTEVMaxValue = m_pChart->getMaxData();//最大值没有对应的数据
    stTEVAmpData.iPulseCount = m_uiPulseNum;
    QString strFileName;
    if( !m_pSubTask->getLatestBGFile(m_strGapId,strFileName))
    {
        qWarning("TEVAmpTestView::saveTestData, getLatestBGFile failed!");
    }
    stTEVAmpData.qstrBGFileName = QFileInfo(strFileName).fileName();
    pTEVAmpSpectrum->setTEVAmpData(stTEVAmpData);

    if(-1 != pDataSpecification->addSpectrum(pTEVAmpSpectrum))
    {
        CustomAccessView::CustomAccessUIFunc::setDataFileHead(pDataSpecification);

        SystemSet::AccessProtocol eAccessProtocol = SystemSetService::instance()->getAccessProtocol();
        if (SystemSet::ACCESS_PROTO_JSDKY == eAccessProtocol)
        {
            m_strSavedPath = CustomAccessView::CustomAccessUIFunc::getJSBinaryDataFileSavePath(m_pSubTask->dataSavePath(), false);
        }
        else
        {
            m_strSavedPath = CustomAccessView::CustomAccessUIFunc::getBinaryDataFileSavePath(m_pSubTask->dataSavePath());
        }

        if(pDataSpecification->saveAsBinary(m_strSavedPath))
        {
            CustomAccessTaskNS::TestData stDataInfo;
            stDataInfo.eTestType = CustomAccessTaskNS::TEVType;
            stDataInfo.usMaxValue = stTEVAmpData.fTEVMaxValue;

            m_pSubTask->addTestPointDatFile(m_strGapId, m_strID, m_strSavedPath, stDataInfo, m_eDiagRet);
            ++m_testedCount;
            m_pTestedNumLabel->setText(QObject::trUtf8("Tested Count: ") + QString::number(m_testedCount) );
            bSuccess = true;
        }
        else
        {
            bSuccess = false;
        }
    }
    delete pDataSpecification;
    pDataSpecification = NULL;
    return bSuccess;
}

/************************************************
 * 函数名   : loadData
 * 输入参数 : NULL
 * 输出参数 : NULL
 * 返回值   : NULL
 * 功能     : 载入数据
 ************************************************/
void TEVAmpTestView::loadData()
{
    m_vecTestData.clear();
    for(quint16 i=0, iSize = m_pSubTask->testPoints(m_strGapId).size(); i < iSize; ++i)
    {
        if( m_strID == m_pSubTask->testPoints(m_strGapId).at(i).s_strId )
        {
            m_vecTestData = m_pSubTask->testPoints(m_strGapId).at(i).s_vData;
            m_testedCount = m_vecTestData.size();
        }
    }

    if(m_vecTestData.size() <= 0)
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
        return;
    }

    QList<QString> lStrFileNames;
    QString strFileName = "";
    for(quint16 i = 0, iSize = m_vecTestData.size(); i < iSize; ++i)
    {
        strFileName = m_vecTestData.at(i).s_strFileName;
        lStrFileNames.append(CustomAccessTaskNS::testFileNameFromFile(strFileName));
    }

    m_eOperationType = OPERATION_LOAD;
    m_pCommonItemListView->clear();
    m_pCommonItemListView->addItems(lStrFileNames);
    m_pCommonItemListView->show();

    return;
}
/*************************************************
功能： 删除数据
*************************************************************/
void TEVAmpTestView::deleteData()
{
    m_vecTestData.clear();
    for(quint16 i = 0, iSize = m_pSubTask->testPoints(m_strGapId).size(); i < iSize; ++i)
    {
        if( m_strID == m_pSubTask->testPoints(m_strGapId).at(i).s_strId )
        {
            m_vecTestData = m_pSubTask->testPoints(m_strGapId).at(i).s_vData;
            m_testedCount = m_vecTestData.size();
        }
    }

    if(m_vecTestData.size() <= 0)
    {
        MsgBox::warning("", QObject::trUtf8("No file!"));
        return;
    }

    QList<QString> lItems;
    QString strFileName = "";
    for(quint16 i = 0, iSize = m_vecTestData.size(); i < iSize; ++i)
    {
        strFileName = m_vecTestData.at(i).s_strFileName;
        lItems.append(CustomAccessTaskNS::testFileNameFromFile(strFileName));
    }

    m_eOperationType = OPERATION_DELETE;
    m_pCommonItemListView->clear();
    m_pCommonItemListView->addItems(lItems);
    m_pCommonItemListView->show();

    return;
}

void TEVAmpTestView::deleteSelectedFile(qint32 uiItem)
{
    if( uiItem >= m_vecTestData.size())
    {
        qWarning("TEVAmpTestView::deleteSelectedFile: error!");
        return;
    }

    QString strTempFile  = m_pSubTask->dataSavePath() + "/" + m_vecTestData.at(uiItem).s_strFileName;
    QFile file ( strTempFile );
    if( file.exists() )
    {
        file.remove();
        m_pSubTask->deleteTestDatFile( m_strGapId, m_strID, m_vecTestData.at( uiItem ).s_strFileName );

        m_vecTestData.remove(uiItem);
        m_testedCount = m_vecTestData.size();
        m_pTestedNumLabel->setText(QObject::trUtf8("Tested Count: ") + QString::number(m_testedCount) );
        m_pCommonItemListView->clear();

        QList<QString> lStrFileNames;
        QString strFileName = "";
        for(quint16 i = 0, iSize = m_vecTestData.size(); i < iSize; ++i)
        {
            strFileName = m_vecTestData.at( i ).s_strFileName;
            lStrFileNames.append( CustomAccessTaskNS::testFileNameFromFile( strFileName ) );
        }
        m_pCommonItemListView->addItems( lStrFileNames );
        MsgBox::information("", QObject::trUtf8("Delete succeeded!"));
    }
    else
    {
        qWarning ()<<" TEVAmpTestView::deleteSelectedFile: no such file: "<< strTempFile;
    }
    return;
}

void TEVAmpTestView::onItemActivated(qint32 uiItem)
{
    if(m_eOperationType == OPERATION_DELETE)
    {
        deleteSelectedFile(uiItem);
    }
    else if(m_eOperationType == OPERATION_LOAD)
    {

        //停止采样
        bool bIsSampleStopped = false;
        if(isSampling())
        {
            stopSample();
            bIsSampleStopped = true;
        }
        if( loadSelectedFile(uiItem) )
        {
            buttonBar()->buttons()[BUTTON_TEV_SAVE_DATA]->setEnabled( false );
        }
        else
        {
            if( bIsSampleStopped )
            {
                startSample();
            }
        }
    }
    else
    {
        qWarning("TEVAmpTestView::onItemActivated: error!");
    }
    return;
}
bool TEVAmpTestView::loadSelectedFile(qint32 uiItem)
{
    if( uiItem >= m_vecTestData.size())
    {
        qWarning("TEVAmpTestView::deleteSelectedFile: error!");
        return false;
    }

    bool bSuccess = false;

    QString strSelectedFile;
    strSelectedFile = m_pSubTask->dataSavePath() + "/" + m_vecTestData.at( uiItem ).s_strFileName;
    INT8 cTestedVal;
    INT8 cMax;

    m_pCommonItemListView->hide();

    if(loadTestDataFile(strSelectedFile, cTestedVal, cMax))
    {
        dbg_info("cTestedVal: %d, cMax: %d.", cTestedVal, cMax);

        stopSample();

        m_bPlayBacked = true;
        m_pChart->clear();
        m_pChart->clearDiagRet();
        m_pChart->addSample(cMax);
        m_pChart->addSample(cTestedVal);
        m_pChart->setRunningStatus(false);

        // 山东版本回放不诊断（山东数据规范TEV回放无法支撑诊断且山东无诊断需求）
        SystemSet::AccessProtocol eProtocol = SystemSetService::instance()->getAccessProtocol();
        if (SystemSet::ACCESS_PROTO_SDLR != eProtocol)
        {
            TEVAmpDiagInfo stDiagInfo;
            stDiagInfo.qui8TevAmpVal = cTestedVal;
            stDiagInfo.stTHInfo.ucThresholdMinor = m_ucYellowAlert;
            stDiagInfo.stTHInfo.ucThresholdSerious = m_ucYellowAlert + 15;
            stDiagInfo.stTHInfo.ucThresholdEmergency = m_ucYellowAlert + 30;

            DiagResultInfo stDiagRetInfo;

            DiagnosisManager::instance()->diagTEVAmpByThreshold(stDiagInfo, stDiagRetInfo);

            DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
            stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stDiagRetInfo.stDiagRet.defectLevel);
            stDiagDisplayInfo.qstrPDDesInfo = stDiagRetInfo.qstrPDDescription;
            stDiagDisplayInfo.qstrPDSignalInfos = stDiagRetInfo.qstrPDSignalTypeInfos;

            if(m_pChart)
            {
                m_pChart->playbackDiagInfo(stDiagDisplayInfo);
            }
        }

        bSuccess = true;
        QString strFileName = m_vecTestData.at( uiItem ).s_strFileName;
        m_pLoadFileName->setText( CustomAccessTaskNS::testFileNameFromFile( strFileName ) );
    }
    else
    {
        MsgBox::warning( "", QObject::trUtf8("No file!") );
    }

    return bSuccess;
}
/*************************************************
功能： 开始采集
*************************************************************/
void TEVAmpTestView::startSample()
{
    if(!isSampling())
    {
        m_eOperationType = OPERATION_INVALID;
        startSampleService();
        m_pSampleBtn->setTitle(TEV_VIEW_CONFIG_TRANSLATE(TEV::TEXT_STOP));
        m_pChart->setRunningStatus(isSampling());
    }
}

/*************************************************
功能： 停止采集
*************************************************************/
void TEVAmpTestView::stopSample()
{
    if(isSampling())
    {
        stopSampleService();
        m_pSampleBtn->setTitle(TEV_VIEW_CONFIG_TRANSLATE(TEV::TEXT_START));
        m_pChart->setRunningStatus(isSampling());
    }
}

/*************************************************
函数名： onButtonValueChanged(int id, int iValue)
输入参数： id：按钮ID
          iValue：按钮值
输出参数： NULL
返回值： NULL
功能： 响应按钮值变化事件
*************************************************************/
void TEVAmpTestView::onButtonValueChanged(int id, int iValue)
{
    switch (id)
    {
    case BUTTON_TEV_PULSE_LEN:
    {
        int iSize = sizeof(TEV::TEV_PULSE_LEN) / sizeof(int);
        iValue = (iValue < 0) ? 0 : iValue;
        iValue = (iValue >= iSize) ? iSize : iValue;
        int iLen = TEV::TEV_PULSE_LEN[iValue];
        if (iLen != m_ucPulseLen)
        {
            stopSample();
            m_ucPulseLen = iLen;
            setWorksets();
        }
        break;
    }
    case BUTTON_TEV_YELLOW_ALERT:
    {
        if (iValue != m_ucYellowAlert)
        {
            stopSample();
            m_ucYellowAlert = iValue;
            resetAlarmScope();
            m_pChart->setAlarm(m_ucYellowAlert, m_ucRedAlert);
            setWorksets();
        }
        break;
    }
    case BUTTON_TEV_RED_ALERT:
    {
        if (iValue != m_ucRedAlert)
        {
            stopSample();
            m_ucRedAlert = iValue;
            resetAlarmScope();
            m_pChart->setAlarm(m_ucYellowAlert, m_ucRedAlert);
            setWorksets();
        }
        break;
    }
    default:
        break;
    }
    return;
}
/*************************************************
函数名： onCommandButtonPressed(int id)
输入参数： id：按钮ID
输出参数： NULL
返回值： NULL
功能： 响应命令按钮按下事件
*************************************************************/
void TEVAmpTestView::onCommandButtonPressed(int id)
{
    switch (id)
    {
    case BUTTON_TEV_SAMPLE://采样
    {
        if( m_bPlayBacked )
        {
            setChartDatas();
            m_bPlayBacked = false;
            m_pChart->clear(); // 避免正常刷新时显示回放数据
        }
        else
        {
            //do nothing
        }
        if(!isSampling()) //图谱刷新暂停后，点击采样，处理；否则不处理
        {
            startSample();
            buttonBar()->buttons()[BUTTON_TEV_SAVE_DATA]->setEnabled( true );
        }
        else
        {
            stopSample();
        }

        m_pLoadFileName->clear();
    }
        break;
    case BUTTON_MORE_CONFIG://更多设置
    {
        showMoreConfigButtonBar();
    }
        break;
    case BUTTON_TEV_SAVE_DATA://保存数据
    {
        saveData();
    }
        break;
    case BUTTON_TEV_LOAD_DATA://载入数据
    {
        loadData();
    }
        break;
    case BUTTON_TEV_DELETE_DATA://删除数据
    {
        deleteData();
    }
        break;

    default:
        break;
    }
}

/*************************************************
功能： 键盘事件
输入参数:
    event -- 事件
*************************************************************/
void TEVAmpTestView::keyPressEvent( QKeyEvent* event )
{
    if(event->key() == Qt::Key_Escape)
    {
        close();
    }
    else
    {
        SampleChartView::keyPressEvent(event);
    }
    return;
}

/*************************************************
 * 功能：保存数据
 * ***********************************************/
void TEVAmpTestView::saveData()
{
    saveConfig();

    //m_pService->stopSample( m_iUserId );
    stopSample();

    bool bSuccess = saveTestData();

    QFileInfo fileInfo(m_strSavedPath);
    PushButtonBar* pBtnBar = buttonBar();
    QPoint centerPoint;
    centerPoint.setX(pBtnBar->pos().x()+pBtnBar->width()/2);
    centerPoint.setY(pBtnBar->pos().y()/*+pBtnBar->height()/2*/);

    if( bSuccess )
    {
        if( m_pSubTask->isAutoSwitch() )
        {
            if( MsgBox::OK == MsgBox::delayQuestion("", QObject::trUtf8("Save success, auto switching."), centerPoint) )
                {
                    m_pSubTask->ensureAutoSwitch( true );
                    close();
                }
                else
                {
                    m_pSubTask->ensureAutoSwitch( false );
                //cancel do nothing
            }
        }
        else
        {
            QString strText = fileInfo.fileName();
            processTooLongMsgText(strText);
            MsgBox::information("", strText, centerPoint);
        }
    }
    else
    {
        MsgBox::informationWithoutAutoAccept("", QObject::trUtf8("Save failure!"), centerPoint);
    }

    //m_pService->startSample( m_iUserId );
    return;
}

/*************************************************
函数名： onSKeyPressed()
输入参数： NULL
输出参数： NULL
返回值： NULL
功能： 响应S键事件
*************************************************************/
void TEVAmpTestView::onSKeyPressed()
{
    saveData();
    return;
}

/*************************************************
 * 功能：诊断数据
 * 输入参数：
 *      bSave：是否为保存操作的逻辑，缺省为false
 * ***********************************************/
void TEVAmpTestView::diagDataInfo(bool bSave)
{
    // 接入终端诊断数据不受外部开关影响，常开
    if(bSave)
    {
        TEVAmpDiagInfo stDiagInfo;
        stDiagInfo.qui8TevAmpVal = static_cast<quint8>(m_cAmpValue);
        stDiagInfo.stTHInfo.ucThresholdMinor = m_ucYellowAlert;
        stDiagInfo.stTHInfo.ucThresholdSerious = m_ucYellowAlert + 15;
        stDiagInfo.stTHInfo.ucThresholdEmergency = m_ucYellowAlert + 30;

        DiagResultInfo stDiagRetInfo;

        DiagnosisManager::instance()->diagTEVAmpByThreshold(stDiagInfo, stDiagRetInfo);
        m_eDiagRet = (CustomAccessTaskNS::DiagnoseType)(stDiagRetInfo.stDiagRet.defectLevel);

        DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
        stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stDiagRetInfo.stDiagRet.defectLevel);
        stDiagDisplayInfo.qstrPDDesInfo = stDiagRetInfo.qstrPDDescription;
        stDiagDisplayInfo.qstrPDSignalInfos = stDiagRetInfo.qstrPDSignalTypeInfos;

        if(m_pChart)
        {
            m_pChart->setDiagRet(stDiagDisplayInfo);
        }
    }
    else
    {
        TEVAmpDiagInfo stDiagInfo;
        stDiagInfo.qui8TevAmpVal = static_cast<quint8>(m_cAmpValue);
        stDiagInfo.stTHInfo.ucThresholdMinor = m_ucYellowAlert;
        stDiagInfo.stTHInfo.ucThresholdSerious = m_ucYellowAlert + 15;
        stDiagInfo.stTHInfo.ucThresholdEmergency = m_ucYellowAlert + 30;

        DiagResultInfo stDiagRetInfo;

        DiagnosisManager::instance()->diagTEVAmpByThreshold(stDiagInfo, stDiagRetInfo);
        m_eDiagRet = (CustomAccessTaskNS::DiagnoseType)(stDiagRetInfo.stDiagRet.defectLevel);
        DiagConfig::DiagDisplayInfo stDiagDisplayInfo;
        stDiagDisplayInfo.eDiagRet = static_cast<DiagConfig::DiagnoseRet>(stDiagRetInfo.stDiagRet.defectLevel);
        stDiagDisplayInfo.qstrPDDesInfo = stDiagRetInfo.qstrPDDescription;
        stDiagDisplayInfo.qstrPDSignalInfos = stDiagRetInfo.qstrPDSignalTypeInfos;

        if(m_pChart)
        {
            m_pChart->setDiagRet(stDiagDisplayInfo);
        }
    }

    return;
}
